# South African Payment Gateway Integration Setup Guide

This guide covers the setup and configuration of multiple South African payment gateways for your Coco Milk Kids e-commerce store.

## Overview

The payment system supports the following South African payment gateways:

1. **PayFast** - South Africa's leading payment gateway
2. **Ozow** - Instant EFT payments
3. **SnapScan** - QR code payments (coming soon)
4. **Yoco** - Card payments and POS integration (coming soon)
5. **PayU** - International and local card processing (coming soon)

## Prerequisites

1. **Business Registration**: Valid South African business registration
2. **VAT Registration**: VAT number (if applicable)
3. **Bank Account**: South African business bank account
4. **SSL Certificate**: Valid SSL certificate for your domain

## Step 1: PayFast Setup

### 1.1 Create PayFast Account

1. Visit [PayFast.co.za](https://www.payfast.co.za)
2. Sign up for a merchant account
3. Complete business verification process
4. Wait for account approval (usually 1-3 business days)

### 1.2 Get PayFast Credentials

1. Log into your PayFast merchant dashboard
2. Go to **Settings > Integration**
3. Note down your:
   - Merchant ID
   - Merchant Key
   - Create a secure passphrase (minimum 10 characters)

### 1.3 Configure PayFast Settings

1. Set your **Return URL**: `https://yourdomain.com/payment/success`
2. Set your **Cancel URL**: `https://yourdomain.com/payment/cancelled`
3. Set your **Notify URL**: `https://yourdomain.com/api/webhooks/payfast`
4. Enable **Email Confirmations**
5. Set **Currency** to ZAR

## Step 2: Ozow Setup

### 2.1 Create Ozow Account

1. Visit [Ozow.com](https://www.ozow.com)
2. Apply for a merchant account
3. Complete the application process
4. Provide required business documentation

### 2.2 Get Ozow Credentials

1. Log into your Ozow merchant portal
2. Navigate to **Integration > API Keys**
3. Note down your:
   - API Key
   - Private Key
   - Site Code

### 2.3 Configure Ozow Settings

1. Set **Success URL**: `https://yourdomain.com/payment/success`
2. Set **Error URL**: `https://yourdomain.com/payment/cancelled`
3. Set **Notify URL**: `https://yourdomain.com/api/webhooks/ozow`
4. Enable **Real-time notifications**

## Step 3: Environment Configuration

### 3.1 Update Environment Variables

Add the following to your `.env.local` file:

```env
# PayFast Configuration
PAYFAST_MERCHANT_ID=your_merchant_id
PAYFAST_MERCHANT_KEY=your_merchant_key
PAYFAST_PASSPHRASE=your_secure_passphrase

# Ozow Configuration
OZOW_API_KEY=your_api_key
OZOW_PRIVATE_KEY=your_private_key
OZOW_SITE_CODE=your_site_code

# Payment Security
PAYMENT_ENCRYPTION_KEY=your_32_character_encryption_key

# South African Business Details
SA_VAT_NUMBER=4123456789
SA_BUSINESS_REGISTRATION=2024/123456/07
```

### 3.2 Generate Encryption Key

Generate a secure 32-character encryption key:

```bash
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

## Step 4: Test the Integration

### 4.1 Test PayFast

```bash
curl -X POST http://localhost:3000/api/payments \
  -H "Content-Type: application/json" \
  -d '{
    "amount": {
      "amount": 100.00,
      "currency": "ZAR"
    },
    "customer": {
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "phone": "0821234567"
    },
    "items": [{
      "id": "1",
      "name": "Test Product",
      "quantity": 1,
      "unitPrice": 100.00,
      "totalPrice": 100.00
    }],
    "metadata": {
      "orderId": "TEST001",
      "source": "web"
    },
    "preferredGateway": "payfast"
  }'
```

### 4.2 Test Ozow

```bash
curl -X POST http://localhost:3000/api/payments \
  -H "Content-Type: application/json" \
  -d '{
    "amount": {
      "amount": 50.00,
      "currency": "ZAR"
    },
    "customer": {
      "email": "<EMAIL>",
      "firstName": "Jane",
      "lastName": "Smith"
    },
    "items": [{
      "id": "2",
      "name": "Test Product 2",
      "quantity": 1,
      "unitPrice": 50.00,
      "totalPrice": 50.00
    }],
    "metadata": {
      "orderId": "TEST002",
      "source": "web"
    },
    "preferredGateway": "ozow",
    "preferredMethod": "instant_eft"
  }'
```

## Step 5: Webhook Configuration

### 5.1 PayFast Webhooks

PayFast will send POST requests to your notify URL with the following data:

- `m_payment_id`: Your payment reference
- `pf_payment_id`: PayFast transaction ID
- `payment_status`: COMPLETE, FAILED, or CANCELLED
- `amount_gross`: Payment amount
- `signature`: Security signature

### 5.2 Ozow Webhooks

Ozow will send POST requests to your notify URL with JSON data:

- `TransactionId`: Ozow transaction ID
- `TransactionReference`: Your payment reference
- `Status`: Complete, Error, Cancelled, or PendingInvestigation
- `Amount`: Payment amount
- `HashCheck`: Security hash

## Step 6: Production Deployment

### 6.1 SSL Certificate

Ensure your production domain has a valid SSL certificate:

```bash
# Test SSL certificate
curl -I https://yourdomain.com
```

### 6.2 Update Gateway Settings

1. **PayFast**: Switch to production mode in merchant dashboard
2. **Ozow**: Request production credentials from Ozow support
3. **Environment**: Set `NODE_ENV=production`

### 6.3 Update URLs

Update all webhook and return URLs to use your production domain:

- Return URL: `https://yourdomain.com/payment/success`
- Cancel URL: `https://yourdomain.com/payment/cancelled`
- PayFast Notify: `https://yourdomain.com/api/webhooks/payfast`
- Ozow Notify: `https://yourdomain.com/api/webhooks/ozow`

## Step 7: Security Considerations

### 7.1 PCI DSS Compliance

- Never store card details on your server
- Use HTTPS for all payment-related communications
- Implement proper access controls
- Regular security audits

### 7.2 POPIA Compliance

- Obtain consent for data processing
- Implement data retention policies
- Provide data access and deletion rights
- Maintain audit trails

### 7.3 Rate Limiting

The system includes built-in rate limiting:

- 10 payment requests per minute per IP
- 100 API requests per 15 minutes per IP
- Automatic blocking of suspicious activity

## Step 8: Monitoring and Logging

### 8.1 Payment Logs

Logs are stored in the `logs/` directory:

- `payment-combined.log`: All payment activities
- `payment-errors.log`: Error logs only
- `payment-audit-YYYY-MM-DD.log`: Daily audit trails

### 8.2 Monitoring Endpoints

- Payment status: `GET /api/payments/status/{transactionId}`
- Available methods: `GET /api/payments`
- Health check: `GET /api/health`

## Troubleshooting

### Common Issues

1. **Invalid Signature Errors**
   - Check passphrase/private key configuration
   - Verify webhook URL is accessible
   - Ensure HTTPS is properly configured

2. **Payment Failures**
   - Check gateway credentials
   - Verify business account status
   - Review transaction limits

3. **Webhook Not Received**
   - Test webhook URL accessibility
   - Check firewall settings
   - Verify SSL certificate

### Debug Mode

Enable debug logging:

```env
NODE_ENV=development
DEBUG=true
```

### Support Contacts

- **PayFast**: <EMAIL>
- **Ozow**: <EMAIL>
- **Technical Issues**: Check logs and error messages

## Next Steps

1. **Additional Gateways**: Implement SnapScan, Yoco, and PayU
2. **Fraud Detection**: Add fraud detection rules
3. **Reporting**: Implement payment analytics dashboard
4. **Mobile Payments**: Add mobile-specific payment methods
5. **Subscriptions**: Implement recurring payment support

## Compliance Checklist

- [ ] Business registration verified
- [ ] VAT registration (if applicable)
- [ ] SSL certificate installed
- [ ] Payment gateway accounts approved
- [ ] Webhook URLs configured
- [ ] Test transactions completed
- [ ] Production credentials configured
- [ ] Security measures implemented
- [ ] POPIA compliance documented
- [ ] Monitoring and logging enabled

The payment system is now ready for production use with comprehensive South African payment gateway support!
