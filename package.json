{"name": "coco-milk-kids", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3090", "build": "next build", "start": "next start --port 3090", "lint": "next lint", "setup": "bash scripts/setup-project.sh", "setup:appwrite": "tsx scripts/setup-appwrite.ts", "seed": "tsx scripts/seed-minimal.ts && tsx scripts/add-sample-products.ts", "seed:basic": "tsx scripts/seed-database.ts", "seed:minimal": "tsx scripts/seed-minimal.ts", "seed:products": "tsx scripts/add-sample-products.ts", "seed:orders": "tsx scripts/seed-orders.ts", "seed:pages": "tsx scripts/seed-pages.ts", "seed:all": "tsx scripts/seeders/seed-all.ts", "seed:users": "tsx scripts/seeders/seed-users.ts", "seed:catalog": "tsx scripts/seeders/seed-products.ts", "seed:inventory": "tsx scripts/seeders/seed-inventory.ts", "seed:orders-new": "tsx scripts/seeders/seed-orders.ts", "seed:reviews": "tsx scripts/seeders/seed-reviews.ts", "create-admin": "tsx scripts/create-admin-user.ts", "init-admin": "node scripts/init-admin.js", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate"}, "dependencies": {"@ai-sdk/openai": "latest", "@ai-sdk/react": "^1.2.12", "@codemirror/autocomplete": "^6.18.6", "@codemirror/commands": "^6.8.1", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/lang-php": "^6.0.1", "@codemirror/lang-python": "^6.2.1", "@codemirror/lang-sql": "^6.8.0", "@codemirror/lang-xml": "^6.1.0", "@codemirror/language": "^6.11.0", "@codemirror/legacy-modes": "^6.5.1", "@codemirror/lint": "^6.8.5", "@codemirror/search": "^6.5.11", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.8", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@faker-js/faker": "^9.8.0", "@hookform/resolvers": "^3.9.1", "@nivo/bar": "^0.99.0", "@nivo/calendar": "^0.99.0", "@nivo/core": "^0.99.0", "@nivo/heatmap": "^0.99.0", "@nivo/line": "^0.99.0", "@nivo/pie": "^0.99.0", "@nivo/treemap": "^0.99.0", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@types/react-grid-layout": "^1.3.5", "@types/react-resizable": "^3.0.8", "@uiw/codemirror-theme-vscode": "^4.23.12", "@uiw/react-codemirror": "^4.23.12", "@woocommerce/woocommerce-rest-api": "^1.0.1", "ai": "latest", "appwrite": "16.0.2", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "cors": "^2.8.5", "crypto-js": "^4.2.0", "date-fns": "4.1.0", "dotenv": "^16.4.7", "embla-carousel-react": "8.5.1", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "framer-motion": "^12.12.1", "helmet": "^8.1.0", "input-otp": "1.4.1", "isomorphic-dompurify": "^2.25.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.454.0", "moment-timezone": "^0.6.0", "next": "15.2.4", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "nextjs-toploader": "^3.8.16", "node-appwrite": "15.0.0", "nodemailer": "^7.0.3", "rate-limiter-flexible": "^7.1.1", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-grid-layout": "^1.5.1", "react-hook-form": "^7.54.1", "react-resizable": "^3.0.5", "react-resizable-panels": "^2.1.7", "reactflow": "^11.11.4", "recharts": "2.15.0", "sonner": "^1.7.1", "supports-color": "^10.0.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.6", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.1", "zustand": "^5.0.5"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.18", "@types/crypto-js": "^4.2.2", "@types/express-rate-limit": "^6.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "postcss": "^8", "prisma": "^5.22.0", "tailwindcss": "^3.4.17", "tsx": "^4.19.2", "typescript": "^5"}}