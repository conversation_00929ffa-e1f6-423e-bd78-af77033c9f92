"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { She<PERSON>, She<PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Search, Menu, ShoppingBag, User, Heart } from "lucide-react"
import { useCart } from "@/hooks/use-cart"
import { NavigationSidebar } from "@/components/navigation-sidebar"
import { CartDrawer } from "@/components/storefront/cart/cart-drawer"
import { SearchDialog } from "@/components/search-dialog"
import { CurrencySelector } from "@/components/currency-selector"
import CocoMilkLogo from "@/components/ui/coco-milk-logo"

export default function Header() {
  const pathname = usePathname()
  const { itemCount } = useCart()
  const [isCartOpen, setIsCartOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)

  // Mock user data - replace with actual auth state
  const user = {
    name: "John Doe",
    email: "<EMAIL>",
    avatar: "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"
  }

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <header
      className={cn(
        "sticky top-0 z-50 w-full transition-all duration-200 border-b border-gray-100",
        scrolled ? "bg-white/95 backdrop-blur-sm" : "bg-white",
      )}
    >
      <div className="container flex h-16 items-center px-4 md:px-6">
        <Sheet open={isMobileNavOpen} onOpenChange={setIsMobileNavOpen}>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden hover:bg-gray-100 transition-colors"
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle navigation menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-80">
            <div className="h-full">
              <NavigationSidebar
                user={user}
                onSignOut={() => {
                  console.log("Sign out")
                  setIsMobileNavOpen(false)
                }}
              />
            </div>
          </SheetContent>
        </Sheet>

        <Link href="/" className="mr-4 flex items-center space-x-2">
         <image
          src={"@/assets/coco-logo-hori.svg"}
          alt="Coco Milk Kids"
          width={40}
          height={40}
          className="object-contain"
        />
          <span className="hidden md:inline font-bold text-lg">Coco Milk Kids</span>
        </Link>

        <nav className="hidden md:flex items-center space-x-8 text-sm font-normal">
          <Link
            href="/"
            className={cn(
              "transition-colors hover:text-gray-600 py-1",
              pathname === "/" ? "text-black" : "text-gray-700",
            )}
          >
            Home
          </Link>
          <Link
            href="/products"
            className={cn(
              "transition-colors hover:text-gray-600 py-1",
              pathname === "/products" || pathname.startsWith("/products/") ? "text-black" : "text-gray-700",
            )}
          >
            Shop
          </Link>
          <Link
            href="/collections/summer"
            className={cn(
              "transition-colors hover:text-gray-600 py-1",
              pathname === "/collections/summer" ? "text-black" : "text-gray-700",
            )}
          >
            Summer
          </Link>
          <Link
            href="/collections/sale"
            className={cn(
              "transition-colors hover:text-gray-600 py-1",
              pathname === "/collections/sale" ? "text-black" : "text-gray-700",
            )}
          >
            Sale
          </Link>
          {process.env.NODE_ENV === 'development' && (
            <Link
              href="/admin/page-builder"
              className={cn(
                "transition-colors hover:text-gray-600 py-1 text-xs bg-blue-100 px-2 rounded",
                pathname === "/admin/page-builder" ? "text-blue-800" : "text-blue-600",
              )}
            >
              Page Builder
            </Link>
          )}
        </nav>

        <div className="ml-auto flex items-center space-x-4">
          <CurrencySelector />

          <div className="hidden md:flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsSearchOpen(true)}
              className="hover:bg-gray-100 transition-colors"
            >
              <Search className="h-5 w-5" />
              <span className="sr-only">Search</span>
            </Button>

            <Button
              variant="ghost"
              size="icon"
              asChild
              className="hover:bg-gray-100 transition-colors"
            >
              <Link href="/wishlist">
                <Heart className="h-5 w-5" />
                <span className="sr-only">Wishlist</span>
              </Link>
            </Button>

            <Button
              variant="ghost"
              size="icon"
              asChild
              className="hover:bg-gray-100 transition-colors"
            >
              <Link href="/account/dashboard">
                <User className="h-5 w-5" />
                <span className="sr-only">Account</span>
              </Link>
            </Button>
          </div>

          <Button
            variant="ghost"
            size="icon"
            className="relative hover:bg-gray-100 transition-colors"
            onClick={() => setIsCartOpen(true)}
          >
            <ShoppingBag className="h-5 w-5" />
            {itemCount > 0 && (
              <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-[#6C1411] text-[10px] font-medium text-white">
                {itemCount}
              </span>
            )}
            <span className="sr-only">Shopping cart</span>
          </Button>

          {/* Mobile search button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsSearchOpen(true)}
            className="md:hidden hover:bg-gray-100 transition-colors"
          >
            <Search className="h-5 w-5" />
            <span className="sr-only">Search</span>
          </Button>
        </div>
      </div>

      <CartDrawer open={isCartOpen} onOpenChange={setIsCartOpen} />
      <SearchDialog open={isSearchOpen} onOpenChange={setIsSearchOpen} />
    </header>
  )
}
