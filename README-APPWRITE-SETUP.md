# 🚀 Appwrite Media Library Setup

This guide will help you set up the complete Appwrite infrastructure for your media library system using our automated setup tools.

## 📋 Prerequisites

1. **Appwrite Account**: Sign up at [cloud.appwrite.io](https://cloud.appwrite.io)
2. **Project Created**: Create a new project in your Appwrite console
3. **API Key Generated**: Create an API key with required permissions

## 🔧 Quick Setup

### Method 1: Automated Script (Recommended)

1. **Install dependencies**:
   ```bash
   pnpm install
   ```

2. **Configure environment variables** in `.env.local`:
   ```env
   # Appwrite Configuration
   NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
   NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id_here
   APPWRITE_API_KEY=your_api_key_here
   
   # Media Library Configuration (optional - defaults provided)
   NEXT_PUBLIC_APPWRITE_MEDIA_BUCKET_ID=media
   NEXT_PUBLIC_APPWRITE_MEDIA_COLLECTION_ID=media-metadata
   ```

3. **Run the setup script**:
   ```bash
   pnpm setup:appwrite
   ```

   Or with options:
   ```bash
   # Force recreate existing infrastructure
   pnpm setup:appwrite --force
   
   # Verbose output
   pnpm setup:appwrite --verbose
   
   # Show help
   pnpm setup:appwrite --help
   ```

### Method 2: Web Interface

1. Start your development server:
   ```bash
   pnpm dev
   ```

2. Navigate to your admin panel and look for the "Appwrite Setup" section

3. Click "Run Setup" to automatically configure your infrastructure

### Method 3: API Endpoint

You can also trigger setup via API:

```bash
# Check setup status
curl http://localhost:3090/api/appwrite/setup

# Run setup
curl -X POST http://localhost:3090/api/appwrite/setup
```

## 🔑 API Key Permissions

Your Appwrite API key needs the following scopes:

### Required Permissions:
- ✅ `files.read` - Read files from storage
- ✅ `files.write` - Upload and delete files
- ✅ `buckets.read` - Read bucket information
- ✅ `buckets.write` - Create and modify buckets
- ✅ `databases.read` - Read database information
- ✅ `databases.write` - Create and modify databases
- ✅ `collections.read` - Read collection information
- ✅ `collections.write` - Create and modify collections

### How to Create API Key:

1. Go to your Appwrite console
2. Navigate to **Settings** → **API Keys**
3. Click **Create API Key**
4. Name it "Media Library Setup"
5. Select the required scopes above
6. Copy the generated key to your `.env.local`

## 📁 What Gets Created

The setup process creates the following infrastructure:

### 🗄️ Storage Bucket: `media`
- **Purpose**: Store all media files (images, videos, documents)
- **Max File Size**: 50MB
- **Allowed Types**: Images, videos, audio, documents, archives, code files
- **Security**: File-level permissions enabled
- **Features**: Compression, encryption, antivirus scanning

### 🗃️ Database: `main` (or your configured database)
- **Purpose**: Store application data and metadata

### 📊 Collection: `media-metadata`
- **Purpose**: Store additional metadata for media files
- **Attributes**:
  - `fileId` (string) - Reference to storage file
  - `alt` (string) - Alt text for accessibility
  - `title` (string) - Display title
  - `description` (string) - File description
  - `tags` (array) - Searchable tags
  - `folder` (string) - Organization folder
  - `width` (integer) - Image width
  - `height` (integer) - Image height
  - `mimeType` (string) - File MIME type
  - `fileSize` (integer) - File size in bytes

## 🔍 Verification

After setup, verify everything is working:

1. **Check the setup status**:
   ```bash
   pnpm setup:appwrite
   ```

2. **Test file upload**:
   - Navigate to your page builder
   - Try uploading a file using the media field
   - Verify the file appears in your Appwrite storage

3. **Check Appwrite console**:
   - Verify the bucket exists in Storage
   - Verify the database and collection exist

## 🛠️ Troubleshooting

### Common Issues:

#### ❌ "Invalid API key or insufficient permissions"
- **Solution**: Check your API key and ensure it has all required scopes
- **Check**: API key is correctly set in `.env.local`

#### ❌ "Project not found"
- **Solution**: Verify your `NEXT_PUBLIC_APPWRITE_PROJECT_ID`
- **Check**: Project ID matches your Appwrite console

#### ❌ "CORS errors in browser"
- **Solution**: Add your domain to Appwrite platform settings
- **Steps**: Settings → Platforms → Add Web Platform → Add your domain

#### ❌ "Bucket already exists"
- **Solution**: Use `--force` flag to recreate
- **Command**: `pnpm setup:appwrite --force`

#### ❌ "Connection refused"
- **Solution**: Check your Appwrite endpoint URL
- **Check**: Endpoint is accessible and correct

### Debug Mode:

Enable verbose logging:
```bash
pnpm setup:appwrite --verbose
```

## 🔄 Updating Configuration

To update your bucket or collection settings:

1. **Modify the configuration** in `lib/appwrite/server.ts`
2. **Run setup with force flag**:
   ```bash
   pnpm setup:appwrite --force
   ```

## 🏗️ Manual Setup (Advanced)

If you prefer manual setup, follow the detailed guide in `docs/appwrite-media-setup.md`.

## 📈 Performance Optimization

After setup, consider these optimizations:

1. **Enable CDN** in your Appwrite project settings
2. **Configure caching** for better performance
3. **Set up image transformations** for responsive images
4. **Implement lazy loading** in your components

## 🔒 Security Best Practices

1. **Use environment variables** for all sensitive data
2. **Set appropriate file permissions** based on your use case
3. **Enable antivirus scanning** (included in setup)
4. **Implement rate limiting** for uploads
5. **Validate file types** on both client and server

## 🚀 Next Steps

Once setup is complete:

1. **Start developing**: Your media library is ready to use
2. **Customize fields**: Modify field configurations as needed
3. **Add more file types**: Update allowed extensions if needed
4. **Implement workflows**: Add custom upload processing
5. **Monitor usage**: Check storage usage in Appwrite console

## 📞 Support

If you encounter issues:

1. **Check the logs**: Setup script provides detailed error messages
2. **Verify environment**: Ensure all variables are set correctly
3. **Check permissions**: API key must have all required scopes
4. **Review documentation**: See `docs/appwrite-media-setup.md` for details

## 🎉 Success!

Once setup is complete, you'll have a professional-grade media library system that rivals WordPress and other major platforms!

Your media library includes:
- ✅ Drag & drop file uploads
- ✅ Advanced file organization
- ✅ Search and filtering
- ✅ Image previews and transformations
- ✅ Secure file storage
- ✅ Metadata management
- ✅ Responsive design
- ✅ Production-ready performance
