# Admin Login Setup Guide

This guide will help you set up and fix the admin login system for the Coco Milk Kids e-commerce platform.

## Quick Setup

### 1. Environment Variables

Make sure you have the following environment variables set in your `.env` file:

```bash
# Admin Authentication
JWT_SECRET=your_jwt_secret_for_admin_auth_here_32_chars_min

# Database
DATABASE_URL="your_database_connection_string"
```

### 2. Database Setup

Run the database migrations to create the admin tables:

```bash
# Generate Prisma client
npm run db:generate

# Push database schema
npm run db:push

# Or run migrations (if you have migration files)
npx prisma migrate dev
```

### 3. Initialize Admin User

Run the admin initialization script:

```bash
# Using npm script
npm run init-admin

# Or directly
node scripts/init-admin.js
```

This will create:
- Default admin role with full permissions
- Default admin user with credentials:
  - **Email:** <EMAIL>
  - **Password:** admin123

## Accessing Admin Panel

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Navigate to the admin login page:
   ```
   http://localhost:3090/admin/login
   ```

3. Use the default credentials:
   - **Email:** <EMAIL>
   - **Password:** admin123

4. After successful login, you'll be redirected to:
   ```
   http://localhost:3090/admin
   ```

## Troubleshooting

### Common Issues

#### 1. "Invalid credentials" error
- Make sure you've run the admin initialization script
- Check that the database is properly connected
- Verify the email and password are correct

#### 2. "Database connection error"
- Check your `DATABASE_URL` in `.env`
- Make sure your database server is running
- Run `npx prisma migrate dev` to ensure tables exist

#### 3. "JWT_SECRET not found"
- Add `JWT_SECRET` to your `.env` file
- Use a strong, random string (at least 32 characters)

#### 4. Rate limiting errors
- Wait 15 minutes and try again
- Check if you're making too many login attempts

### Manual Admin User Creation

If the script doesn't work, you can create an admin user manually via the API:

```bash
# Create admin user (development only)
curl -X PUT http://localhost:3090/api/admin/auth/login \
  -H "Content-Type: application/json"
```

### Reset Admin Password

To reset the admin password, you can:

1. Use the admin panel (if you have access)
2. Run the initialization script again (it will skip if user exists)
3. Manually update the database

## Security Notes

### Development
- Default credentials are for development only
- Change the password immediately after first login

### Production
- Use strong, unique passwords
- Set a secure `JWT_SECRET` environment variable
- Enable HTTPS for all admin access
- Consider implementing two-factor authentication
- Regularly review admin user permissions
- Monitor admin activity logs

## Admin Features

Once logged in, you'll have access to:

- **Dashboard** - Overview of store metrics
- **Products** - Manage product catalog
- **Orders** - Process and manage orders
- **Customers** - Customer management
- **Inventory** - Stock management
- **Analytics** - Sales and performance reports
- **Settings** - Store configuration
- **Page Builder** - Create custom pages
- **Content Management** - Manage blog posts and content

## API Endpoints

The admin authentication system provides these endpoints:

- `POST /api/admin/auth/login` - Admin login
- `GET /api/admin/auth/me` - Get current admin user
- `POST /api/admin/auth/logout` - Admin logout
- `PUT /api/admin/auth/login` - Create default admin user (dev only)

## Support

If you encounter issues:

1. Check the browser console for errors
2. Check the server logs
3. Verify your environment variables
4. Ensure database connectivity
5. Try the manual setup steps above

For additional help, refer to the main project documentation or contact the development team.
