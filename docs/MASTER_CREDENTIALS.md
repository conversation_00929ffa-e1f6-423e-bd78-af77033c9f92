# 🔐 HARDCODED MASTER CREDENTIALS

## Emergency Access Credentials

The Coco Milk Kids admin system includes hardcoded master credentials for emergency access and system recovery.

### 👑 Master Administrator Access

```
Email:    <EMAIL>
Password: CocoMaster2024!
```

## 🚨 CRITICAL SECURITY WARNINGS

### ⚠️ PRODUCTION SECURITY RISKS
- **NEVER use these credentials in production**
- **ALWAYS change or disable in production environments**
- **These credentials bypass all database security**
- **Full system access with ALL permissions**

### 🔒 Security Features
- **Permissions**: `['*']` (All permissions)
- **Roles**: `['master', 'admin', 'super-admin']`
- **Database**: Bypasses database authentication
- **Session**: Creates JWT token without database session
- **Rate Limiting**: Subject to same rate limits as regular users

## 🎯 Use Cases

### ✅ Appropriate Use
- **Emergency access** when database is down
- **System recovery** after database corruption
- **Initial setup** before database is configured
- **Development and testing** environments
- **Debugging authentication issues**

### ❌ Inappropriate Use
- **Production environments**
- **Regular administrative tasks**
- **When database is functioning normally**
- **Shared or public systems**

## 🛠 Implementation Details

### Authentication Flow
1. User enters master credentials
2. System checks for exact email/password match
3. Bypasses database lookup entirely
4. Creates JWT token with hardcoded user data
5. Returns success with full permissions

### Code Location
- **Login API**: `/app/api/admin/auth/login/route.ts` (lines 37-68)
- **Auth Check**: `/app/api/admin/auth/me/route.ts` (lines 23-38)
- **User ID**: `master-hardcoded`

### Token Structure
```json
{
  "userId": "master-hardcoded",
  "email": "<EMAIL>",
  "roles": ["master", "admin", "super-admin"],
  "permissions": ["*"]
}
```

## 🔧 Configuration

### Environment Variables
- Uses same `JWT_SECRET` as regular authentication
- No additional configuration required
- Always enabled (cannot be disabled via config)

### Permissions
The hardcoded master has ALL permissions including:
- System administration
- User management
- Product management
- Order management
- Financial operations
- Content management
- Database operations
- Security settings

## 🛡️ Security Recommendations

### Development Environment
1. ✅ Use for initial setup and testing
2. ✅ Document access in team procedures
3. ✅ Monitor usage in logs
4. ✅ Rotate credentials periodically

### Staging Environment
1. ⚠️ Use only for emergency access
2. ⚠️ Monitor all usage
3. ⚠️ Consider disabling if not needed
4. ⚠️ Use strong, unique passwords

### Production Environment
1. 🚨 **DISABLE OR CHANGE IMMEDIATELY**
2. 🚨 **Use environment-specific credentials**
3. 🚨 **Implement additional security layers**
4. 🚨 **Monitor and alert on usage**

## 🔄 Disabling Hardcoded Access

### Option 1: Comment Out Code
```javascript
// Comment out the hardcoded check in login route
/*
if (email.toLowerCase() === MASTER_EMAIL && password === MASTER_PASSWORD) {
  // ... hardcoded login logic
}
*/
```

### Option 2: Environment Variable Control
Add environment variable check:
```javascript
if (process.env.ENABLE_MASTER_ACCESS === 'true' && 
    email.toLowerCase() === MASTER_EMAIL && 
    password === MASTER_PASSWORD) {
  // ... hardcoded login logic
}
```

### Option 3: Production Check
```javascript
if (process.env.NODE_ENV !== 'production' && 
    email.toLowerCase() === MASTER_EMAIL && 
    password === MASTER_PASSWORD) {
  // ... hardcoded login logic
}
```

## 📊 Monitoring and Auditing

### Log Monitoring
- Monitor for `master-hardcoded` user ID in logs
- Set up alerts for hardcoded master usage
- Track all actions performed by master user

### Security Audits
- Regular review of hardcoded access usage
- Verify production environments have disabled access
- Document all emergency access incidents

## 🆘 Emergency Procedures

### Database Down Scenario
1. Access admin panel with master credentials
2. Check system status and logs
3. Perform necessary recovery operations
4. Document incident and actions taken

### Lost Admin Access
1. Use master credentials to access system
2. Reset or create new admin accounts
3. Verify database connectivity
4. Restore normal authentication flow

## 📞 Support Information

### Contact Information
- **Development Team**: [<EMAIL>]
- **System Administrator**: [<EMAIL>]
- **Emergency Contact**: [<EMAIL>]

### Documentation
- **Admin Setup Guide**: `docs/ADMIN_SETUP.md`
- **Security Procedures**: `docs/SECURITY.md`
- **Emergency Procedures**: `docs/EMERGENCY.md`

---

**⚠️ REMEMBER: These credentials provide FULL SYSTEM ACCESS. Use responsibly and secure appropriately for your environment.**
