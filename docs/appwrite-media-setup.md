# Appwrite Media Library Setup Guide

This guide will help you set up the Appwrite-powered media library for your page builder system.

## Prerequisites

1. **Appwrite Account**: Sign up at [cloud.appwrite.io](https://cloud.appwrite.io) or set up a self-hosted instance
2. **Project Created**: Create a new project in your Appwrite console

## Step 1: Create Storage Bucket

1. Navigate to **Storage** in your Appwrite console
2. Click **Create Bucket**
3. Configure the bucket:
   - **Bucket ID**: `media` (or update `NEXT_PUBLIC_APPWRITE_MEDIA_BUCKET_ID` in your .env)
   - **Name**: Media Library
   - **Permissions**: Configure based on your needs
   - **File Security**: Enable if you want file-level permissions
   - **Antivirus**: Enable for security (recommended)
   - **Encryption**: Enable for sensitive content

### Recommended Bucket Settings

```json
{
  "bucketId": "media",
  "name": "Media Library",
  "permissions": [
    "read(\"any\")",
    "create(\"users\")",
    "update(\"users\")",
    "delete(\"users\")"
  ],
  "fileSecurity": true,
  "enabled": true,
  "maximumFileSize": ********,
  "allowedFileExtensions": [
    "jpg", "jpeg", "png", "gif", "webp", "svg",
    "mp4", "webm", "ogg", "avi", "mov",
    "mp3", "wav", "ogg", "aac", "flac",
    "pdf", "doc", "docx", "txt", "rtf"
  ],
  "compression": "gzip",
  "encryption": true,
  "antivirus": true
}
```

## Step 2: Configure Environment Variables

Update your `.env.local` file with your Appwrite configuration:

```env
# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id_here
APPWRITE_API_KEY=your_api_key_here

# Media Library Configuration
NEXT_PUBLIC_APPWRITE_MEDIA_BUCKET_ID=media
NEXT_PUBLIC_APPWRITE_MEDIA_COLLECTION_ID=media-metadata
```

### Getting Your Configuration Values

1. **Project ID**: Found in your project settings
2. **API Key**: Create a new API key in Settings > API Keys with the following scopes:
   - `files.read`
   - `files.write`
   - `buckets.read`

## Step 3: Install Dependencies

The media library uses the Appwrite Web SDK:

```bash
pnpm add appwrite
```

## Step 4: Optional - Database Collection for Metadata

If you want to store additional metadata (tags, descriptions, etc.), create a database collection:

1. Navigate to **Databases** in your Appwrite console
2. Create a new database or use an existing one
3. Create a collection with ID: `media-metadata`

### Collection Schema

```json
{
  "collectionId": "media-metadata",
  "name": "Media Metadata",
  "permissions": [
    "read(\"any\")",
    "create(\"users\")",
    "update(\"users\")",
    "delete(\"users\")"
  ],
  "attributes": [
    {
      "key": "fileId",
      "type": "string",
      "size": 255,
      "required": true
    },
    {
      "key": "alt",
      "type": "string",
      "size": 255,
      "required": false
    },
    {
      "key": "title",
      "type": "string",
      "size": 255,
      "required": false
    },
    {
      "key": "description",
      "type": "string",
      "size": 1000,
      "required": false
    },
    {
      "key": "tags",
      "type": "string",
      "array": true,
      "required": false
    },
    {
      "key": "folder",
      "type": "string",
      "size": 255,
      "required": false
    }
  ],
  "indexes": [
    {
      "key": "fileId_index",
      "type": "key",
      "attributes": ["fileId"]
    },
    {
      "key": "tags_index",
      "type": "fulltext",
      "attributes": ["tags"]
    }
  ]
}
```

## Step 5: Security Configuration

### File Permissions

Configure file permissions based on your use case:

**Public Files (recommended for most media)**:
```javascript
[
  Permission.read(Role.any()),
  Permission.create(Role.users()),
  Permission.update(Role.users()),
  Permission.delete(Role.users())
]
```

**Private Files**:
```javascript
[
  Permission.read(Role.user("USER_ID")),
  Permission.create(Role.user("USER_ID")),
  Permission.update(Role.user("USER_ID")),
  Permission.delete(Role.user("USER_ID"))
]
```

### CORS Configuration

If you're running into CORS issues, add your domain to the platform settings:

1. Go to **Settings** > **Platforms**
2. Add a new **Web Platform**
3. Set your domain (e.g., `http://localhost:3000` for development)

## Step 6: Usage Examples

### Basic Upload

```typescript
import { mediaLibrary } from '@/lib/appwrite/media'

// Upload a file
const file = new File(['content'], 'example.jpg', { type: 'image/jpeg' })
const uploadedFile = await mediaLibrary.uploadFile({
  file,
  alt: 'Example image',
  title: 'My Example',
  tags: ['example', 'demo']
})
```

### Get Files with Filters

```typescript
// Get all images
const { files } = await mediaLibrary.getFiles({
  type: 'image',
  search: 'landscape'
})

// Get files from specific folder
const { files } = await mediaLibrary.getFiles({
  folder: 'products'
})
```

### Delete File

```typescript
await mediaLibrary.deleteFile('file_id_here')
```

## Step 7: Testing

1. Start your development server
2. Navigate to your page builder
3. Try uploading a file using the media field
4. Verify the file appears in your Appwrite storage bucket

## Troubleshooting

### Common Issues

1. **CORS Errors**: Make sure your domain is added to the platform settings
2. **Permission Denied**: Check your API key scopes and file permissions
3. **File Size Limits**: Adjust the `maximumFileSize` in your bucket settings
4. **File Type Restrictions**: Update `allowedFileExtensions` in your bucket

### Debug Mode

Enable debug logging by setting:

```env
NEXT_PUBLIC_APPWRITE_DEBUG=true
```

## Advanced Configuration

### Custom File Processing

You can extend the media library to include custom file processing:

```typescript
// Custom image resizing
const processImage = async (file: File) => {
  // Your image processing logic
  return processedFile
}

// Upload with processing
const uploadedFile = await mediaLibrary.uploadFile({
  file: await processImage(originalFile),
  // ... other options
})
```

### Folder Organization

Organize files into folders:

```typescript
await mediaLibrary.uploadFile({
  file,
  folder: 'products/category-name',
  // ... other options
})
```

### Bulk Operations

```typescript
// Upload multiple files
const files = [file1, file2, file3]
const uploadPromises = files.map(file => 
  mediaLibrary.uploadFile({ file })
)
const uploadedFiles = await Promise.all(uploadPromises)
```

## Security Best Practices

1. **Use file validation** on both client and server
2. **Implement virus scanning** (Appwrite provides this)
3. **Set appropriate file size limits**
4. **Use signed URLs** for sensitive content
5. **Implement rate limiting** for uploads
6. **Validate file types** server-side

## Performance Optimization

1. **Enable compression** in bucket settings
2. **Use CDN** for file delivery
3. **Implement lazy loading** for media grids
4. **Cache file metadata** when possible
5. **Use appropriate image formats** (WebP, AVIF)

This setup provides a robust, WordPress-like media library experience powered by Appwrite's infrastructure.
