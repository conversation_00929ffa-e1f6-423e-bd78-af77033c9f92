# Layout Builder System Architecture

## Overview
The Layout Builder extends the existing Page Builder system to provide comprehensive layout management for headers, footers, sidebars, navigation, and overall page structure.

## Core Components

### 1. Layout Builder Engine
```typescript
// Layout structure definition
interface LayoutStructure {
  id: string
  name: string
  type: 'site' | 'page' | 'post-type' | 'template'
  structure: {
    header: LayoutSection
    main: LayoutSection
    sidebar?: LayoutSection
    footer: LayoutSection
  }
  responsive: ResponsiveSettings
  conditions: LayoutConditions
}

interface LayoutSection {
  id: string
  type: 'header' | 'main' | 'sidebar' | 'footer'
  blocks: LayoutBlock[]
  styling: SectionStyling
  responsive: ResponsiveSettings
}

interface LayoutBlock {
  id: string
  type: 'navigation' | 'logo' | 'search' | 'content' | 'widget' | 'custom'
  configuration: BlockConfiguration
  content: BlockContent
  styling: BlockStyling
  responsive: ResponsiveSettings
}
```

### 2. Layout Types

#### Site-Wide Layouts
- **Global Header/Footer**: Consistent across all pages
- **Navigation Systems**: Main menu, breadcrumbs, mobile nav
- **Brand Elements**: Logo, contact info, social links

#### Page-Specific Layouts
- **Landing Pages**: Hero sections, call-to-actions
- **Blog Layouts**: Article structure, author info, related posts
- **E-commerce**: Product grids, filters, checkout flow
- **Portfolio**: Gallery layouts, project showcases

#### Post Type Layouts
- **Blog Posts**: Article layout with sidebar, author box
- **Portfolio Items**: Image galleries, project details
- **Products**: Product images, specifications, reviews
- **Testimonials**: Customer info, rating displays

### 3. Layout Builder Interface

#### Visual Editor
- **Drag-and-drop** layout sections
- **Real-time preview** with device switching
- **Grid system** with responsive breakpoints
- **Component library** of pre-built elements

#### Layout Hierarchy
```
Site Layout (Global)
├── Header Layout
│   ├── Logo Block
│   ├── Navigation Block
│   └── Search Block
├── Main Content Area
│   └── [Page Builder Content]
├── Sidebar Layout (Optional)
│   ├── Widget Blocks
│   └── Custom Blocks
└── Footer Layout
    ├── Links Block
    ├── Social Block
    └── Copyright Block
```

## Integration with Existing System

### 1. Universal Renderer Enhancement
```typescript
// Enhanced renderer with layout support
interface RenderContext {
  layout: LayoutStructure
  content: PageContent
  postType?: PostType
  metadata: SEOMetadata
}

export async function UniversalRenderer({ 
  pathname, 
  searchParams,
  layoutId?: string 
}: UniversalRendererProps) {
  // 1. Resolve content (existing)
  const resolution = await RouteResolver.resolve(pathname)
  
  // 2. Resolve layout (new)
  const layout = await LayoutResolver.resolve(resolution, layoutId)
  
  // 3. Render with layout structure
  return (
    <LayoutRenderer layout={layout}>
      <ContentRenderer resolution={resolution} />
    </LayoutRenderer>
  )
}
```

### 2. Database Schema Extension
```sql
-- Layout tables
CREATE TABLE layouts (
  id VARCHAR PRIMARY KEY,
  name VARCHAR NOT NULL,
  type VARCHAR NOT NULL, -- 'site', 'page', 'post-type', 'template'
  structure JSON NOT NULL,
  conditions JSON,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE layout_assignments (
  id VARCHAR PRIMARY KEY,
  layout_id VARCHAR REFERENCES layouts(id),
  target_type VARCHAR NOT NULL, -- 'global', 'page', 'post-type', 'specific'
  target_id VARCHAR, -- page_id, post_type_name, or null for global
  priority INTEGER DEFAULT 0,
  conditions JSON
);
```

## Layout Builder Features

### 1. Pre-built Layout Templates
- **E-commerce Store**: Header with cart, product grids, footer
- **Blog/Magazine**: Header with menu, sidebar, article layout
- **Portfolio**: Minimal header, full-width galleries, contact footer
- **Corporate**: Professional header, content sections, contact info
- **Landing Page**: Hero header, conversion sections, minimal footer

### 2. Responsive Design System
- **Breakpoint Management**: Mobile, tablet, desktop, large screens
- **Flexible Grids**: CSS Grid and Flexbox integration
- **Component Scaling**: Automatic sizing and spacing
- **Mobile-First**: Progressive enhancement approach

### 3. Dynamic Content Areas
- **Content Slots**: Designated areas for Page Builder content
- **Widget Areas**: Sidebar and footer widget zones
- **Navigation Menus**: Dynamic menu generation
- **Breadcrumbs**: Automatic navigation trails

### 4. Advanced Features
- **Conditional Layouts**: Show/hide based on user, device, content
- **A/B Testing**: Multiple layout variants
- **Performance Optimization**: Layout caching and lazy loading
- **SEO Integration**: Structured data in layout elements

## Implementation Phases

### Phase 1: Core Layout Engine (Week 1-2)
- Layout data models and database schema
- Basic layout resolver and renderer
- Simple header/footer management

### Phase 2: Visual Builder Interface (Week 3-4)
- Drag-and-drop layout editor
- Component library and styling options
- Real-time preview system

### Phase 3: Advanced Features (Week 5-6)
- Responsive design controls
- Conditional layout logic
- Template system and presets

### Phase 4: Integration & Polish (Week 7-8)
- Performance optimization
- SEO enhancements
- Testing and documentation

## Benefits for Your E-commerce System

### 1. Brand Consistency
- Unified header with logo and navigation
- Consistent footer across all pages
- Standardized product page layouts

### 2. User Experience
- Intuitive navigation structure
- Mobile-optimized layouts
- Fast-loading, cached layouts

### 3. Content Management
- Easy layout updates without developer
- A/B testing for conversion optimization
- Seasonal layout changes (holidays, sales)

### 4. Developer Efficiency
- Reusable layout components
- Separation of layout and content concerns
- Easier maintenance and updates

## Next Steps

1. **Review and approve** this architecture
2. **Create database migrations** for layout tables
3. **Implement core layout engine** with basic functionality
4. **Build visual layout editor** interface
5. **Integrate with existing Page Builder** system
6. **Create default layout templates** for common use cases
7. **Add responsive design controls**
8. **Implement performance optimizations**

The Layout Builder will transform your system from a content management platform into a complete website building solution, giving users full control over both content and presentation while maintaining the professional, production-ready architecture you've already built.
