# Page Builder Conversion Plan

## Overview
Systematic conversion of all static pages in the Next.js app directory to use the Page Builder system while maintaining the existing selfi.co.za design aesthetic and Zara-style visual elements.

## Phase 1: Analysis of Existing Pages

### Static Pages Identified:
1. **Home Page** (`/`) - Complex with multiple sections
2. **About Page** (`/about`) - Story, values, mission sections
3. **Contact Page** (`/contact`) - Form + contact info
4. **FAQ Page** (`/faq`) - Accordion-style Q&A
5. **Brand Page** (`/brand`) - Logo showcase and brand assets
6. **Help Page** (`/help`) - Help topics grid
7. **Privacy Page** (`/privacy`) - Legal content sections
8. **Terms Page** (`/terms`) - Legal content sections
9. **Shipping Page** (`/shipping`) - Shipping info cards
10. **Stores Page** (`/stores`) - Store locator with search
11. **Newsletter Page** (`/newsletter`) - Subscription form + benefits

### Common Design Patterns Identified:
- **Zara-style minimalism** with clean typography
- **Card-based layouts** for information sections
- **Grid systems** (2x2, 3-column, asymmetric)
- **Hero sections** with background images
- **Contact information blocks** with consistent styling
- **Form components** with validation
- **Icon + text combinations** for features/benefits
- **Accordion components** for FAQ content
- **Search functionality** for stores
- **Social proof sections** with testimonials

## Phase 2: Custom Block Development

### New Blocks Required:

#### 1. **Story Section Block**
- **Purpose**: About page story content with image
- **Features**: Text content + image, responsive layout
- **Configuration**: Title, content paragraphs, image, layout direction

#### 2. **Values Grid Block**
- **Purpose**: Display company values with icons
- **Features**: Icon + title + description grid
- **Configuration**: Number of columns, icon selection, content

#### 3. **Mission Statement Block**
- **Purpose**: Highlighted mission/vision content
- **Features**: Centered text with background
- **Configuration**: Title, content, background style

#### 4. **Contact Info Block**
- **Purpose**: Contact details with icons
- **Features**: Email, phone, address with icons
- **Configuration**: Contact details, icon styles

#### 5. **Contact Form Block**
- **Purpose**: Contact form with validation
- **Features**: Form fields, subject selection, validation
- **Configuration**: Form fields, email endpoint, styling

#### 6. **FAQ Accordion Block**
- **Purpose**: Expandable Q&A sections
- **Features**: Accordion interface, search
- **Configuration**: FAQ items, styling, search enable

#### 7. **Brand Assets Block**
- **Purpose**: Logo and brand asset showcase
- **Features**: Logo variants, color palettes, usage guidelines
- **Configuration**: Asset uploads, descriptions

#### 8. **Help Topics Grid Block**
- **Purpose**: Help topic navigation
- **Features**: Icon + title + description grid with links
- **Configuration**: Topics, icons, links, popularity flags

#### 9. **Legal Content Block**
- **Purpose**: Structured legal content
- **Features**: Sectioned content with headings
- **Configuration**: Sections, content, contact info

#### 10. **Shipping Info Cards Block**
- **Purpose**: Shipping options and policies
- **Features**: Card layout with icons and details
- **Configuration**: Shipping options, pricing, policies

#### 11. **Store Locator Block**
- **Purpose**: Store finder with search
- **Features**: Search, store cards, map integration
- **Configuration**: Store data, search settings, map API

#### 12. **Newsletter Benefits Block**
- **Purpose**: Newsletter signup benefits
- **Features**: Benefit cards with icons
- **Configuration**: Benefits list, icons, styling

#### 13. **Newsletter Form Block**
- **Purpose**: Newsletter subscription form
- **Features**: Email input, preferences, validation
- **Configuration**: Form fields, preferences, styling

#### 14. **Social Proof Block**
- **Purpose**: Testimonials and social proof
- **Features**: Customer quotes, ratings
- **Configuration**: Testimonials, styling, layout

#### 15. **Featured Categories Block**
- **Purpose**: Zara-style 2x2 category grid
- **Features**: Image overlays, hover effects
- **Configuration**: Categories, images, links

#### 16. **Editorial Grid Block**
- **Purpose**: Zara-style asymmetric content grid
- **Features**: Large + small image layout
- **Configuration**: Images, content, layout ratios

#### 17. **Special Offers Banner Block**
- **Purpose**: Promotional banner with CTA
- **Features**: Dark background, minimal text, CTA
- **Configuration**: Offer text, CTA, background

## Phase 3: Implementation Strategy

### Step 1: Create Block Components (Week 1)
1. Implement all 17 custom blocks
2. Add to block registry
3. Create configuration schemas
4. Test in Page Builder editor

### Step 2: Convert Simple Pages (Week 2)
1. **About Page** - Story + Values + Mission blocks
2. **Brand Page** - Brand Assets block
3. **Privacy/Terms Pages** - Legal Content blocks
4. **Help Page** - Help Topics Grid block

### Step 3: Convert Complex Pages (Week 3)
1. **Contact Page** - Contact Info + Contact Form blocks
2. **FAQ Page** - FAQ Accordion block
3. **Shipping Page** - Shipping Info Cards block
4. **Newsletter Page** - Newsletter Benefits + Form blocks

### Step 4: Convert Advanced Pages (Week 4)
1. **Stores Page** - Store Locator block
2. **Home Page** - Multiple blocks integration
3. Testing and refinement

### Step 5: Testing & Optimization (Week 5)
1. Cross-browser testing
2. Mobile responsiveness
3. Performance optimization
4. SEO verification

## Phase 4: Migration Process

### For Each Page:
1. **Create Page Builder page** in admin interface
2. **Configure blocks** to match original design
3. **Test functionality** (forms, links, interactions)
4. **Verify SEO** (meta tags, structured data)
5. **Update routing** to use Page Builder system
6. **Archive static page** (keep as backup)

## Phase 5: Quality Assurance

### Design Consistency Checklist:
- [ ] Zara-style minimalism maintained
- [ ] selfi.co.za color scheme preserved
- [ ] Typography consistency (Montserrat font)
- [ ] Responsive design working
- [ ] Hover effects and animations
- [ ] Brand guidelines followed

### Functionality Checklist:
- [ ] Forms working with validation
- [ ] Links and navigation functional
- [ ] Search functionality operational
- [ ] Mobile experience optimized
- [ ] Loading performance acceptable
- [ ] SEO metadata correct

## Success Metrics
- All 11 static pages converted to Page Builder
- Design consistency maintained (100%)
- Functionality preserved (100%)
- Performance maintained or improved
- SEO rankings preserved
- Admin can easily edit content

## Timeline: 5 Weeks Total
- **Week 1**: Block development
- **Week 2**: Simple page conversion
- **Week 3**: Complex page conversion  
- **Week 4**: Advanced page conversion
- **Week 5**: Testing & optimization

This systematic approach ensures all pages are converted while maintaining design consistency and functionality.
