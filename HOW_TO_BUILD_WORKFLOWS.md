# 🔧 How to Build Workflows - Complete Guide

## ✅ **YES! You Can Build Workflows with This System**

The workflow system is **fully functional** and provides multiple ways to create custom workflows:

1. **🎨 Visual Workflow Builder** - Drag-and-drop interface
2. **📋 Template-Based Creation** - Start from pre-built templates
3. **💻 Programmatic Creation** - Build workflows with code
4. **🔄 API-Based Creation** - Create via REST API

---

## 🎨 **Method 1: Visual Workflow Builder**

### **Access the Builder**
Navigate to: **`/admin/workflows/builder`**

### **Step-by-Step Process**

1. **Configure Workflow Settings**
   - Name your workflow
   - Add description
   - Select category (Customer, Order, Inventory, Marketing, Custom)
   - Choose trigger type (Event, Schedule, Manual)

2. **Set Up Triggers**
   - **Event Triggers**: `customer.registered`, `order.paid`, `cart.abandoned`, etc.
   - **Scheduled Triggers**: Cron expressions (`0 9 * * 1` = Every Monday 9 AM)
   - **Manual Triggers**: Triggered manually by admins

3. **Add Workflow Steps**
   - **Action Steps**: Send emails, update database, call APIs
   - **Notification Steps**: Email, SMS, push notifications
   - **Condition Steps**: Check values, branch logic
   - **Delay Steps**: Wait for specific time periods
   - **Integration Steps**: Connect to external services

4. **Configure Each Step**
   - Set step name and description
   - Configure step-specific settings
   - Set retry limits and dependencies
   - Add conditional logic

5. **Test and Save**
   - Test workflow with sample data
   - Save and activate workflow

---

## 📋 **Method 2: Template-Based Creation**

### **Using Pre-Built Templates**

```typescript
// Access via Admin Interface
// Navigate to /admin/workflows
// Click "Create Workflow" → Select Template

// Available Templates:
// - Customer Welcome Series
// - Cart Abandonment Recovery
// - Order Fulfillment
// - Low Stock Alerts
// - Birthday Campaigns
// - Return Processing
// - Inventory Audits
```

### **Customize Templates**

```typescript
// Create from template with customizations
const workflow = await workflowService.createWorkflowFromTemplate(
  'customer-welcome-series',
  {
    name: 'My Custom Welcome Series',
    description: 'Customized welcome flow for VIP customers',
    tags: ['vip', 'welcome', 'custom'],
    // Modify steps, timing, content, etc.
  }
)
```

---

## 💻 **Method 3: Programmatic Creation**

### **Create Custom Workflow with Code**

```typescript
import { WorkflowService } from '@/lib/workflows/workflow-service'
import { WorkflowDefinition } from '@/lib/workflows/types'

const workflowService = new WorkflowService()

// Define your custom workflow
const customWorkflow: Omit<WorkflowDefinition, 'id' | 'createdAt' | 'updatedAt'> = {
  name: 'VIP Customer Upgrade',
  description: 'Upgrade customers to VIP after 5 purchases',
  version: '1.0.0',
  category: 'customer',
  
  // Trigger configuration
  trigger: {
    type: 'event',
    event: 'order.completed',
    conditions: [
      {
        field: 'customer.orderCount',
        operator: 'equals',
        value: 5
      }
    ],
    config: {}
  },
  
  // Workflow steps
  steps: [
    {
      id: 'upgrade-customer',
      name: 'Upgrade to VIP Status',
      description: 'Update customer status to VIP',
      type: 'action',
      status: 'pending',
      config: {
        actionType: 'update_customer',
        customerId: '{{customer.id}}',
        updates: {
          status: 'vip',
          discountPercentage: 15
        }
      },
      retryCount: 0,
      maxRetries: 3
    },
    {
      id: 'send-vip-email',
      name: 'Send VIP Welcome Email',
      description: 'Congratulate new VIP customer',
      type: 'notification',
      status: 'pending',
      config: {
        type: 'email',
        template: 'vip-welcome',
        recipient: '{{customer.email}}',
        data: {
          customerName: '{{customer.firstName}}',
          vipPerks: ['Free shipping', 'Early access', 'Priority support']
        }
      },
      dependencies: ['upgrade-customer'],
      retryCount: 0,
      maxRetries: 3
    }
  ],
  
  isActive: true,
  createdBy: 'admin',
  tags: ['vip', 'customer-loyalty'],
  metadata: {
    estimatedDuration: '5 minutes',
    businessImpact: 'Increases customer loyalty'
  }
}

// Create the workflow
const createdWorkflow = await workflowService.createWorkflow(customWorkflow)
console.log('Workflow created:', createdWorkflow.id)
```

---

## 🔄 **Method 4: API-Based Creation**

### **Create Workflow via REST API**

```bash
# Create new workflow
curl -X POST http://localhost:3090/api/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Custom API Workflow",
    "description": "Created via API",
    "category": "custom",
    "trigger": {
      "type": "event",
      "event": "customer.registered"
    },
    "steps": [
      {
        "id": "welcome-email",
        "name": "Send Welcome Email",
        "type": "notification",
        "config": {
          "type": "email",
          "template": "welcome",
          "recipient": "{{customer.email}}"
        }
      }
    ],
    "isActive": true,
    "tags": ["api", "welcome"]
  }'
```

### **Create from Template via API**

```bash
# Create from template
curl -X POST http://localhost:3090/api/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "fromTemplate": true,
    "templateId": "customer-welcome-series",
    "name": "My Custom Welcome",
    "createdBy": "api-user"
  }'
```

---

## 🎯 **Practical Examples**

### **Example 1: Flash Sale Workflow**

```typescript
const flashSaleWorkflow = {
  name: 'Flash Sale Campaign',
  description: '24-hour flash sale with countdown',
  category: 'marketing',
  trigger: {
    type: 'manual', // Triggered manually by marketing team
    config: {}
  },
  steps: [
    {
      id: 'create-sale',
      name: 'Create Flash Sale',
      type: 'action',
      config: {
        actionType: 'api_call',
        endpoint: '/api/e-commerce/sales',
        method: 'POST',
        data: {
          name: 'Flash Sale',
          discount: 40,
          duration: 86400000 // 24 hours
        }
      }
    },
    {
      id: 'send-announcement',
      name: 'Send Flash Sale Email',
      type: 'notification',
      config: {
        type: 'email',
        template: 'flash-sale',
        recipient: 'all_customers',
        data: {
          discount: '40%',
          endTime: '{{date.add(24, "hours")}}'
        }
      },
      dependencies: ['create-sale']
    },
    {
      id: 'wait-12-hours',
      name: 'Wait 12 Hours',
      type: 'delay',
      config: {
        delay: 43200000 // 12 hours
      },
      dependencies: ['send-announcement']
    },
    {
      id: 'send-reminder',
      name: 'Send Final Hours Reminder',
      type: 'notification',
      config: {
        type: 'email',
        template: 'flash-sale-reminder',
        recipient: 'customers_no_purchase',
        data: {
          hoursLeft: 12,
          urgency: 'FINAL HOURS!'
        }
      },
      dependencies: ['wait-12-hours']
    }
  ]
}
```

### **Example 2: Abandoned Cart Recovery**

```typescript
const cartRecoveryWorkflow = {
  name: 'Smart Cart Recovery',
  description: 'Progressive cart abandonment recovery',
  category: 'customer',
  trigger: {
    type: 'event',
    event: 'cart.abandoned',
    conditions: [
      {
        field: 'cart.value',
        operator: 'greater_than',
        value: 100 // Only for carts over R100
      }
    ]
  },
  steps: [
    {
      id: 'wait-1-hour',
      name: 'Wait 1 Hour',
      type: 'delay',
      config: { delay: 3600000 }
    },
    {
      id: 'send-reminder-1',
      name: 'Send First Reminder',
      type: 'notification',
      config: {
        type: 'email',
        template: 'cart-reminder-1',
        recipient: '{{customer.email}}',
        data: {
          cartItems: '{{cart.items}}',
          cartTotal: '{{cart.total}}',
          discount: '5%'
        }
      },
      dependencies: ['wait-1-hour']
    },
    {
      id: 'wait-24-hours',
      name: 'Wait 24 Hours',
      type: 'delay',
      config: { delay: 86400000 },
      dependencies: ['send-reminder-1']
    },
    {
      id: 'send-reminder-2',
      name: 'Send Second Reminder',
      type: 'notification',
      config: {
        type: 'email',
        template: 'cart-reminder-2',
        recipient: '{{customer.email}}',
        data: {
          cartItems: '{{cart.items}}',
          discount: '10%',
          urgency: 'Limited time offer!'
        }
      },
      dependencies: ['wait-24-hours']
    }
  ]
}
```

---

## 🔧 **Step Types and Configurations**

### **Action Steps**
```typescript
{
  type: 'action',
  config: {
    actionType: 'update_customer' | 'send_email' | 'api_call' | 'create_task',
    // Specific configuration based on action type
  }
}
```

### **Notification Steps**
```typescript
{
  type: 'notification',
  config: {
    type: 'email' | 'sms' | 'push',
    template: 'template-name',
    recipient: '{{customer.email}}' | 'all_customers',
    data: { /* template variables */ }
  }
}
```

### **Condition Steps**
```typescript
{
  type: 'condition',
  config: {
    conditions: [
      {
        field: 'order.total',
        operator: 'greater_than',
        value: 500
      }
    ]
  }
}
```

### **Delay Steps**
```typescript
{
  type: 'delay',
  config: {
    delay: 3600000 // milliseconds (1 hour)
  }
}
```

---

## 🚀 **Getting Started**

1. **Access the Builder**: Go to `/admin/workflows/builder`
2. **Start with Templates**: Use existing templates as starting points
3. **Experiment**: Create simple workflows first
4. **Test Thoroughly**: Use the test functionality
5. **Monitor Performance**: Check analytics and execution logs

---

## 📊 **Workflow Management**

- **View All Workflows**: `/admin/workflows`
- **Monitor Executions**: `/admin/workflows/executions`
- **Analytics**: Built into each workflow
- **Templates**: Browse available templates
- **API Documentation**: Complete REST API for all operations

---

**🎉 The workflow system is production-ready and fully functional! Start building your custom workflows today!**
