/**
 * Product Reviews & Ratings Seeder
 * Creates realistic product reviews using existing users and products
 */

import { PrismaClient } from '@prisma/client'
import { faker } from '@faker-js/faker'

const prisma = new PrismaClient()

// Review templates for kids clothing
const reviewTemplates = {
  positive: [
    "Absolutely love this {product}! The quality is amazing and my {child} looks adorable in it.",
    "Great {product}! Fits perfectly and the material is so soft. Highly recommend!",
    "Beautiful {product}! The colors are vibrant and it washes well. Will definitely buy again.",
    "Perfect {product} for my {child}. Good quality and great value for money.",
    "Lovely {product}! My {child} loves wearing it and it's very comfortable.",
    "Excellent quality {product}. True to size and arrived quickly. Very happy!",
    "Amazing {product}! The fabric is so soft and the design is beautiful.",
    "Great purchase! This {product} is well-made and my {child} loves it."
  ],
  neutral: [
    "Nice {product}. Good quality but the sizing runs a bit small.",
    "Decent {product}. The material is okay but could be softer.",
    "Good {product} overall. The color is slightly different from the picture.",
    "Average {product}. It's fine but nothing special. Does the job.",
    "Okay {product}. The quality is decent for the price.",
    "Fair {product}. It's comfortable but the design could be better."
  ],
  negative: [
    "Disappointed with this {product}. The quality is not as expected.",
    "Not happy with this {product}. It shrunk after the first wash.",
    "Poor quality {product}. The stitching came undone after a few wears.",
    "Wouldn't recommend this {product}. The material feels cheap.",
    "Not worth the money. This {product} doesn't look like the picture.",
    "Bad experience with this {product}. It arrived damaged."
  ]
}

const childReferences = ['little one', 'daughter', 'son', 'toddler', 'baby', 'child', 'kid']

const reviewerNames = [
  'Sarah M.', 'Jennifer L.', 'Michelle K.', 'Lisa R.', 'Amanda T.',
  'Nicole P.', 'Rachel S.', 'Jessica W.', 'Ashley B.', 'Stephanie C.',
  'Nomsa M.', 'Thandiwe N.', 'Lerato K.', 'Palesa S.', 'Zanele D.',
  'Precious T.', 'Naledi M.', 'Kgomotso P.', 'Boitumelo L.', 'Refilwe S.'
]

function generateReviewContent(rating: number, productTitle: string) {
  let templates: string[]
  
  if (rating >= 4) {
    templates = reviewTemplates.positive
  } else if (rating >= 3) {
    templates = reviewTemplates.neutral
  } else {
    templates = reviewTemplates.negative
  }
  
  const template = faker.helpers.arrayElement(templates)
  const child = faker.helpers.arrayElement(childReferences)
  const productType = productTitle.split(' ').pop()?.toLowerCase() || 'item'
  
  return template
    .replace(/{product}/g, productType)
    .replace(/{child}/g, child)
}

function generateRatingDistribution() {
  // Realistic rating distribution (mostly positive)
  const weights = [2, 3, 8, 35, 52] // 1-star to 5-star weights
  const random = Math.random() * 100
  let cumulative = 0
  
  for (let i = 0; i < weights.length; i++) {
    cumulative += weights[i]
    if (random <= cumulative) {
      return i + 1
    }
  }
  
  return 5 // fallback
}

export async function seedReviews(reviewsPerProduct: number = 5) {
  try {
    console.log(`⭐ Creating product reviews...`)
    
    // Get existing data
    const users = await prisma.user.findMany({
      where: { isActive: true }
    })
    
    const products = await prisma.product.findMany({
      where: { status: 'active' },
      include: { reviews: true }
    })
    
    if (users.length === 0) {
      throw new Error('No users found. Please run the user seeder first.')
    }
    
    if (products.length === 0) {
      throw new Error('No products found. Please run the product seeder first.')
    }
    
    console.log(`📝 Creating reviews for ${products.length} products...`)
    
    const reviews = []
    
    for (const product of products) {
      // Skip if product already has reviews
      if (product.reviews.length > 0) {
        continue
      }
      
      const numReviews = faker.number.int({ min: 1, max: reviewsPerProduct })
      const reviewUsers = faker.helpers.arrayElements(users, numReviews)
      
      for (let i = 0; i < numReviews; i++) {
        const user = reviewUsers[i]
        const rating = generateRatingDistribution()
        const content = generateReviewContent(rating, product.title)
        const reviewerName = faker.helpers.arrayElement(reviewerNames)
        
        // Check if user already reviewed this product
        const existingReview = await prisma.productReview.findFirst({
          where: {
            productId: product.id,
            userId: user.id
          }
        })
        
        if (!existingReview) {
          const review = await prisma.productReview.create({
            data: {
              productId: product.id,
              userId: user.id,
              rating,
              title: rating >= 4 ? 'Great product!' : rating >= 3 ? 'Good product' : 'Could be better',
              content,
              reviewerName,
              reviewerEmail: user.email,
              isVerifiedPurchase: Math.random() < 0.7, // 70% verified purchases
              isApproved: Math.random() < 0.95, // 95% approved
              isHelpful: Math.random() < 0.3,
              helpfulCount: faker.number.int({ min: 0, max: 15 }),
              unhelpfulCount: faker.number.int({ min: 0, max: 3 }),
              status: 'published',
              createdAt: faker.date.past({ years: 1 }),
              updatedAt: faker.date.recent({ days: 30 })
            }
          })
          
          reviews.push(review)
        }
      }
    }
    
    // Update product review statistics
    console.log('📊 Updating product review statistics...')
    
    for (const product of products) {
      const productReviews = await prisma.productReview.findMany({
        where: {
          productId: product.id,
          status: 'published'
        }
      })
      
      if (productReviews.length > 0) {
        const totalRating = productReviews.reduce((sum, review) => sum + review.rating, 0)
        const averageRating = totalRating / productReviews.length
        
        // Calculate rating distribution
        const ratingDistribution = [1, 2, 3, 4, 5].map(rating => 
          productReviews.filter(review => review.rating === rating).length
        )
        
        await prisma.product.update({
          where: { id: product.id },
          data: {
            reviewCount: productReviews.length,
            averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
            ratingDistribution,
            lastReviewAt: productReviews[productReviews.length - 1]?.createdAt
          }
        })
      }
    }
    
    console.log(`🎉 Successfully created ${reviews.length} product reviews`)
    console.log(`📊 Review statistics:`)
    
    const ratingCounts = reviews.reduce((acc, review) => {
      acc[review.rating] = (acc[review.rating] || 0) + 1
      return acc
    }, {} as Record<number, number>)
    
    Object.entries(ratingCounts).forEach(([rating, count]) => {
      console.log(`   - ${rating} stars: ${count} reviews`)
    })
    
    const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
    console.log(`   - Average rating: ${averageRating.toFixed(1)} stars`)
    console.log(`   - Verified purchases: ${reviews.filter(r => r.isVerifiedPurchase).length}`)
    
    return reviews
    
  } catch (error) {
    console.error('❌ Error seeding reviews:', error)
    throw error
  }
}

// Run the seeder if called directly
if (require.main === module) {
  const reviewsPerProduct = process.argv[2] ? parseInt(process.argv[2]) : 5
  
  seedReviews(reviewsPerProduct)
    .then(() => {
      console.log('🎉 Review seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Review seeding failed:', error)
      process.exit(1)
    })
    .finally(() => {
      prisma.$disconnect()
    })
}
