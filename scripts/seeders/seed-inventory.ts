/**
 * Inventory Management Seeder
 * Creates inventory locations, items, and movements using existing products
 */

import { PrismaClient } from '@prisma/client'
import { faker } from '@faker-js/faker'

const prisma = new PrismaClient()

// South African warehouse locations
const warehouseLocations = [
  {
    name: 'Cape Town Main Warehouse',
    code: 'CPT-MAIN',
    type: 'warehouse',
    address: {
      line1: '123 Industrial Road',
      line2: 'Montague Gardens',
      city: 'Cape Town',
      province: 'Western Cape',
      country: 'South Africa',
      postalCode: '7441'
    },
    contactName: '<PERSON>',
    contactEmail: '<EMAIL>',
    contactPhone: '+27 21 555 0123'
  },
  {
    name: 'Johannesburg Distribution Center',
    code: 'JHB-DC',
    type: 'warehouse',
    address: {
      line1: '456 Logistics Avenue',
      line2: 'Kempton Park',
      city: 'Johannesburg',
      province: 'Gauteng',
      country: 'South Africa',
      postalCode: '1619'
    },
    contactName: '<PERSON>',
    contactEmail: '<EMAIL>',
    contactPhone: '+27 11 555 0456'
  },
  {
    name: 'Durban Coastal Store',
    code: 'DBN-STORE',
    type: 'store',
    address: {
      line1: '789 Marine Parade',
      line2: 'South Beach',
      city: 'Durban',
      province: 'KwaZulu-Natal',
      country: 'South Africa',
      postalCode: '4001'
    },
    contactName: 'Priya Patel',
    contactEmail: '<EMAIL>',
    contactPhone: '+27 31 555 0789'
  },
  {
    name: 'Online Fulfillment Center',
    code: 'ONLINE-FC',
    type: 'warehouse',
    address: {
      line1: '321 E-commerce Hub',
      line2: 'Midrand',
      city: 'Johannesburg',
      province: 'Gauteng',
      country: 'South Africa',
      postalCode: '1685'
    },
    contactName: 'David Mthembu',
    contactEmail: '<EMAIL>',
    contactPhone: '+27 11 555 0321'
  }
]

const movementTypes = [
  { type: 'purchase', direction: 'in', reason: 'Stock purchase from supplier' },
  { type: 'sale', direction: 'out', reason: 'Product sold to customer' },
  { type: 'adjustment', direction: 'in', reason: 'Inventory count adjustment - increase' },
  { type: 'adjustment', direction: 'out', reason: 'Inventory count adjustment - decrease' },
  { type: 'transfer', direction: 'out', reason: 'Transfer to another location' },
  { type: 'transfer', direction: 'in', reason: 'Transfer from another location' },
  { type: 'return', direction: 'in', reason: 'Customer return' },
  { type: 'damage', direction: 'out', reason: 'Damaged goods write-off' },
  { type: 'theft', direction: 'out', reason: 'Inventory shrinkage - theft' }
]

export async function seedInventory() {
  try {
    console.log('🏭 Setting up inventory management system...')
    
    // Create inventory locations
    console.log('📍 Creating inventory locations...')
    const locations = []
    for (const locationData of warehouseLocations) {
      const existingLocation = await prisma.inventoryLocation.findUnique({
        where: { code: locationData.code }
      })
      
      if (!existingLocation) {
        const location = await prisma.inventoryLocation.create({
          data: {
            ...locationData,
            isActive: true,
            isPrimary: locationData.code === 'CPT-MAIN',
            allowsInventory: true,
            allowsFulfillment: true,
            maxCapacity: faker.number.int({ min: 5000, max: 20000 }),
            currentUtilization: faker.number.int({ min: 1000, max: 8000 }),
            operatingHours: {
              monday: { open: '08:00', close: '17:00' },
              tuesday: { open: '08:00', close: '17:00' },
              wednesday: { open: '08:00', close: '17:00' },
              thursday: { open: '08:00', close: '17:00' },
              friday: { open: '08:00', close: '17:00' },
              saturday: { open: '09:00', close: '13:00' },
              sunday: { closed: true }
            }
          }
        })
        locations.push(location)
      } else {
        locations.push(existingLocation)
      }
    }
    
    // Get existing products and variants
    console.log('📦 Getting existing products...')
    const products = await prisma.product.findMany({
      where: { status: 'active' },
      include: { variants: true }
    })
    
    if (products.length === 0) {
      throw new Error('No products found. Please run the product seeder first.')
    }
    
    console.log(`📊 Creating inventory items for ${products.length} products...`)
    
    const inventoryItems = []
    const movements = []
    
    // Create inventory items for each product variant at each location
    for (const product of products) {
      for (const variant of product.variants) {
        for (const location of locations) {
          const sku = variant.sku || `${product.handle}-${variant.id}`.toUpperCase()
          
          // Check if inventory item already exists
          const existingItem = await prisma.inventoryItem.findUnique({
            where: { sku }
          })
          
          if (!existingItem) {
            const quantity = faker.number.int({ min: 0, max: 150 })
            const reservedQuantity = Math.min(quantity, faker.number.int({ min: 0, max: 20 }))
            const availableQuantity = quantity - reservedQuantity
            
            const inventoryItem = await prisma.inventoryItem.create({
              data: {
                productId: product.id,
                variantId: variant.id,
                sku,
                name: `${product.title} - ${variant.title}`,
                description: product.description?.substring(0, 200),
                quantity,
                reservedQuantity,
                availableQuantity,
                committedQuantity: 0,
                lowStockThreshold: faker.number.int({ min: 5, max: 20 }),
                outOfStockThreshold: 0,
                reorderPoint: faker.number.int({ min: 10, max: 30 }),
                reorderQuantity: faker.number.int({ min: 50, max: 200 }),
                costPrice: Number(variant.costPerItem) || Number(product.costPerItem) || 0,
                averageCost: Number(variant.costPerItem) || Number(product.costPerItem) || 0,
                lastCostPrice: Number(variant.costPerItem) || Number(product.costPerItem) || 0,
                currency: 'ZAR',
                locationId: location.id,
                binLocation: `${faker.string.alpha({ length: 1, casing: 'upper' })}${faker.number.int({ min: 1, max: 20 })}-${faker.number.int({ min: 1, max: 10 })}`,
                zone: faker.helpers.arrayElement(['A', 'B', 'C', 'D']),
                aisle: faker.number.int({ min: 1, max: 20 }).toString(),
                shelf: faker.number.int({ min: 1, max: 5 }).toString(),
                trackQuantity: true,
                allowBackorders: Math.random() < 0.3,
                continueSellingWhenOutOfStock: Math.random() < 0.2,
                status: 'active',
                lastStockUpdate: faker.date.recent({ days: 30 }),
                weight: Number(variant.weight) || Number(product.weight) || 0,
                weightUnit: variant.weightUnit || product.weightUnit || 'kg',
                tags: product.tags || []
              }
            })
            
            inventoryItems.push(inventoryItem)
            
            // Create initial stock movement (purchase)
            if (quantity > 0) {
              const movement = await prisma.inventoryMovement.create({
                data: {
                  inventoryItemId: inventoryItem.id,
                  locationId: location.id,
                  type: 'purchase',
                  direction: 'in',
                  quantity,
                  unitCost: inventoryItem.costPrice,
                  totalCost: inventoryItem.costPrice * quantity,
                  currency: 'ZAR',
                  referenceType: 'purchase_order',
                  referenceNumber: `PO-${faker.string.alphanumeric({ length: 8 }).toUpperCase()}`,
                  reason: 'Initial stock purchase',
                  userName: 'System',
                  notes: 'Initial inventory setup',
                  createdAt: faker.date.past({ days: 90 })
                }
              })
              movements.push(movement)
            }
            
            // Create some random movements for realistic history
            const movementCount = faker.number.int({ min: 2, max: 8 })
            for (let i = 0; i < movementCount; i++) {
              const movementType = faker.helpers.arrayElement(movementTypes)
              const movementQuantity = faker.number.int({ min: 1, max: 20 })
              
              const movement = await prisma.inventoryMovement.create({
                data: {
                  inventoryItemId: inventoryItem.id,
                  locationId: location.id,
                  type: movementType.type,
                  direction: movementType.direction,
                  quantity: movementQuantity,
                  unitCost: movementType.direction === 'in' ? inventoryItem.costPrice : null,
                  totalCost: movementType.direction === 'in' ? inventoryItem.costPrice * movementQuantity : null,
                  currency: 'ZAR',
                  referenceType: movementType.type === 'sale' ? 'order' : movementType.type,
                  referenceNumber: `${movementType.type.toUpperCase()}-${faker.string.alphanumeric({ length: 6 }).toUpperCase()}`,
                  reason: movementType.reason,
                  userName: faker.helpers.arrayElement(['John Smith', 'Sarah Johnson', 'Mike Wilson', 'Lisa Brown']),
                  notes: Math.random() < 0.3 ? faker.lorem.sentence() : null,
                  createdAt: faker.date.past({ days: 60 })
                }
              })
              movements.push(movement)
            }
          }
        }
      }
    }
    
    console.log(`🎉 Successfully created inventory system:`)
    console.log(`   - Locations: ${locations.length}`)
    console.log(`   - Inventory items: ${inventoryItems.length}`)
    console.log(`   - Stock movements: ${movements.length}`)
    console.log(`   - Total inventory value: R${inventoryItems.reduce((sum, item) => sum + (Number(item.costPrice) * item.quantity), 0).toLocaleString()}`)
    
    // Create some inventory alerts for low stock
    console.log('⚠️ Creating inventory alerts...')
    const lowStockItems = inventoryItems.filter(item => item.quantity <= item.lowStockThreshold)
    
    for (const item of lowStockItems.slice(0, 10)) { // Limit to 10 alerts
      await prisma.inventoryAlert.create({
        data: {
          inventoryItemId: item.id,
          locationId: item.locationId,
          type: 'low_stock',
          severity: item.quantity === 0 ? 'critical' : 'warning',
          title: `Low Stock Alert: ${item.name}`,
          message: `Stock level (${item.quantity}) is below threshold (${item.lowStockThreshold})`,
          isActive: true,
          triggeredAt: faker.date.recent({ days: 7 })
        }
      })
    }
    
    console.log(`📢 Created ${Math.min(lowStockItems.length, 10)} inventory alerts`)
    
    return { locations, inventoryItems, movements }
    
  } catch (error) {
    console.error('❌ Error seeding inventory:', error)
    throw error
  }
}

// Run the seeder if called directly
if (require.main === module) {
  seedInventory()
    .then(() => {
      console.log('🎉 Inventory seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Inventory seeding failed:', error)
      process.exit(1)
    })
    .finally(() => {
      prisma.$disconnect()
    })
}
