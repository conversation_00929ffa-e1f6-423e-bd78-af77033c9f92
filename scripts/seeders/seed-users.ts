/**
 * User & Customer Seeder
 * Creates realistic users with South African data and customer profiles
 */

import { PrismaClient } from '@prisma/client'
import { faker } from '@faker-js/faker'

const prisma = new PrismaClient()

// South African specific data
const southAfricanCities = [
  'Cape Town', 'Johannesburg', 'Durban', 'Pretoria', 'Port Elizabeth',
  'Bloemfontein', 'East London', 'Pietermaritzburg', 'Kimberley', 'Polokwane',
  'Nelspruit', 'George', 'Rustenburg', 'Potchefstroom', 'Witbank'
]

const southAfricanProvinces = [
  'Western Cape', 'Gauteng', 'KwaZulu-Natal', 'Eastern Cape', 'Free State',
  'Limpopo', 'Mpumalanga', 'North West', 'Northern Cape'
]

const southAfricanNames = {
  firstNames: [
    '<PERSON>hab<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>e', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON>', '<PERSON><PERSON>', 'Ra<PERSON>ed', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>'
  ],
  last<PERSON><PERSON>s: [
    '<PERSON><PERSON>bu', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>u', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>i',
    '<PERSON> der <PERSON>r<PERSON>', '<PERSON>a', '<PERSON><PERSON>ius', 'Du Plessis', 'Steyn', 'Fourie', 'Nel', 'Venter',
    'Patel', 'Reddy', 'Naidoo', 'Pillay', 'Maharaj', 'Singh', 'Govind', 'Ramesh',
    'Adams', 'Williams', 'Johnson', 'Brown', 'Davis', 'Miller', 'Wilson', 'Moore'
  ]
}

const loyaltyTiers = ['bronze', 'silver', 'gold', 'platinum', 'diamond']

function generateSouthAfricanName() {
  return {
    firstName: faker.helpers.arrayElement(southAfricanNames.firstNames),
    lastName: faker.helpers.arrayElement(southAfricanNames.lastNames)
  }
}

function generateSouthAfricanPhone() {
  const prefixes = ['082', '083', '084', '072', '073', '074', '076', '078', '079']
  const prefix = faker.helpers.arrayElement(prefixes)
  const number = faker.string.numeric(7)
  return `+27 ${prefix} ${number.slice(0, 3)} ${number.slice(3)}`
}

function generateUserAddress() {
  return {
    firstName: '',
    lastName: '',
    company: Math.random() < 0.2 ? faker.company.name() : null,
    address1: faker.location.streetAddress(),
    address2: Math.random() < 0.3 ? faker.location.secondaryAddress() : null,
    city: faker.helpers.arrayElement(southAfricanCities),
    province: faker.helpers.arrayElement(southAfricanProvinces),
    country: 'South Africa',
    postalCode: faker.string.numeric(4),
    phone: generateSouthAfricanPhone(),
    isDefault: true,
    type: 'both',
    label: 'Home'
  }
}

export async function seedUsers(count: number = 100) {
  try {
    console.log(`👥 Creating ${count} users with customer profiles...`)
    
    const users = []
    const customers = []
    
    for (let i = 0; i < count; i++) {
      const { firstName, lastName } = generateSouthAfricanName()
      const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}${i}@example.com`
      const phone = generateSouthAfricanPhone()
      
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email }
      })
      
      if (!existingUser) {
        const customerSince = faker.date.past({ years: 3 })
        const orderCount = faker.number.int({ min: 0, max: 25 })
        const totalSpent = orderCount > 0 ? faker.number.float({ min: 100, max: 15000, fractionDigits: 2 }) : 0
        const averageOrderValue = orderCount > 0 ? totalSpent / orderCount : 0
        
        // Create user
        const user = await prisma.user.create({
          data: {
            email,
            firstName,
            lastName,
            displayName: `${firstName} ${lastName}`,
            phone,
            dateOfBirth: Math.random() < 0.7 ? faker.date.birthdate({ min: 18, max: 65, mode: 'age' }) : null,
            gender: faker.helpers.arrayElement(['male', 'female', 'other', 'prefer_not_to_say']),
            emailVerified: Math.random() < 0.8,
            phoneVerified: Math.random() < 0.6,
            lastLoginAt: Math.random() < 0.7 ? faker.date.recent({ days: 30 }) : null,
            acceptsMarketing: Math.random() < 0.4,
            preferredLanguage: faker.helpers.arrayElement(['en', 'af', 'zu', 'xh']),
            preferredCurrency: 'ZAR',
            timezone: 'Africa/Johannesburg',
            avatar: Math.random() < 0.3 ? faker.image.avatar() : null,
            bio: Math.random() < 0.2 ? faker.lorem.sentence() : null,
            isActive: Math.random() < 0.95,
            isBlocked: Math.random() < 0.02,
            customerSince: orderCount > 0 ? customerSince : null,
            totalSpent,
            orderCount,
            averageOrderValue,
            lastOrderAt: orderCount > 0 ? faker.date.recent({ days: 90 }) : null,
            loyaltyPoints: Math.random() < 0.6 ? faker.number.int({ min: 0, max: 5000 }) : null,
            loyaltyTier: totalSpent > 5000 ? faker.helpers.arrayElement(loyaltyTiers) : null,
            tags: Math.random() < 0.3 ? faker.helpers.arrayElements(['vip', 'frequent-buyer', 'newsletter', 'social-media'], { min: 1, max: 2 }) : [],
            notes: Math.random() < 0.1 ? faker.lorem.sentence() : null
          }
        })
        
        // Create user address
        const addressData = generateUserAddress()
        addressData.firstName = firstName
        addressData.lastName = lastName
        
        await prisma.userAddress.create({
          data: {
            ...addressData,
            userId: user.id
          }
        })
        
        // Create corresponding customer record
        const customer = await prisma.customer.create({
          data: {
            email,
            firstName,
            lastName,
            phone,
            dateOfBirth: user.dateOfBirth?.toISOString().split('T')[0] || null,
            preferences: {
              marketing: user.acceptsMarketing,
              language: user.preferredLanguage,
              currency: user.preferredCurrency,
              notifications: {
                email: Math.random() < 0.7,
                sms: Math.random() < 0.4,
                push: Math.random() < 0.5
              }
            }
          }
        })
        
        // Create customer address
        await prisma.customerAddress.create({
          data: {
            customerId: customer.id,
            firstName,
            lastName,
            company: addressData.company,
            address1: addressData.address1,
            address2: addressData.address2,
            city: addressData.city,
            province: addressData.province,
            country: addressData.country,
            postalCode: addressData.postalCode,
            phone: addressData.phone,
            isDefault: true,
            type: 'both'
          }
        })
        
        users.push(user)
        customers.push(customer)
        
        if ((i + 1) % 20 === 0) {
          console.log(`✅ Created ${i + 1}/${count} users`)
        }
      }
    }
    
    console.log(`🎉 Successfully created ${users.length} users and ${customers.length} customers`)
    console.log(`📊 User statistics:`)
    console.log(`   - Active users: ${users.filter(u => u.isActive).length}`)
    console.log(`   - Email verified: ${users.filter(u => u.emailVerified).length}`)
    console.log(`   - Customers with orders: ${users.filter(u => u.orderCount > 0).length}`)
    console.log(`   - VIP customers: ${users.filter(u => u.totalSpent > 5000).length}`)
    
    return { users, customers }
    
  } catch (error) {
    console.error('❌ Error seeding users:', error)
    throw error
  }
}

// Run the seeder if called directly
if (require.main === module) {
  const count = process.argv[2] ? parseInt(process.argv[2]) : 100
  
  seedUsers(count)
    .then(() => {
      console.log('🎉 User seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 User seeding failed:', error)
      process.exit(1)
    })
    .finally(() => {
      prisma.$disconnect()
    })
}
