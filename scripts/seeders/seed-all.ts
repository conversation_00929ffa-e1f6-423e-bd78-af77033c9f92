/**
 * Master Seeder Orchestrator
 * Runs all seeders in the correct order to create a complete e-commerce dataset
 */

import { PrismaClient } from '@prisma/client'
import { seedUsers } from './seed-users'
import { seedProducts } from './seed-products'
import { seedInventory } from './seed-inventory'
import { seedOrders } from './seed-orders'
import { seedReviews } from './seed-reviews'

const prisma = new PrismaClient()

interface SeedingOptions {
  users?: number
  products?: number
  orders?: number
  reviewsPerProduct?: number
  skipExisting?: boolean
  verbose?: boolean
}

const defaultOptions: SeedingOptions = {
  users: 100,
  products: 50,
  orders: 75,
  reviewsPerProduct: 5,
  skipExisting: true,
  verbose: true
}

async function checkExistingData() {
  const [userCount, productCount, orderCount, reviewCount] = await Promise.all([
    prisma.user.count(),
    prisma.product.count(),
    prisma.order.count(),
    prisma.productReview.count()
  ])
  
  return { userCount, productCount, orderCount, reviewCount }
}

async function seedDatabase(options: SeedingOptions = {}) {
  const config = { ...defaultOptions, ...options }
  
  try {
    console.log('🚀 Starting comprehensive database seeding...')
    console.log('=' .repeat(60))
    
    if (config.verbose) {
      console.log('📋 Seeding Configuration:')
      console.log(`   - Users: ${config.users}`)
      console.log(`   - Products: ${config.products}`)
      console.log(`   - Orders: ${config.orders}`)
      console.log(`   - Reviews per product: ${config.reviewsPerProduct}`)
      console.log(`   - Skip existing: ${config.skipExisting}`)
      console.log('')
    }
    
    // Check existing data
    const existingData = await checkExistingData()
    
    if (config.verbose) {
      console.log('📊 Current Database State:')
      console.log(`   - Users: ${existingData.userCount}`)
      console.log(`   - Products: ${existingData.productCount}`)
      console.log(`   - Orders: ${existingData.orderCount}`)
      console.log(`   - Reviews: ${existingData.reviewCount}`)
      console.log('')
    }
    
    const results: any = {}
    
    // 1. Seed Users & Customers
    console.log('👥 STEP 1: Creating Users & Customers')
    console.log('-'.repeat(40))
    
    if (config.skipExisting && existingData.userCount >= config.users!) {
      console.log(`⏭️  Skipping users - already have ${existingData.userCount} users`)
      results.users = { users: [], customers: [] }
    } else {
      const usersToCreate = Math.max(0, config.users! - existingData.userCount)
      results.users = await seedUsers(usersToCreate)
    }
    
    console.log('')
    
    // 2. Seed Products & Categories
    console.log('🛍️  STEP 2: Creating Products & Categories')
    console.log('-'.repeat(40))
    
    if (config.skipExisting && existingData.productCount >= config.products!) {
      console.log(`⏭️  Skipping products - already have ${existingData.productCount} products`)
      results.products = { products: [], categories: [] }
    } else {
      const productsToCreate = Math.max(0, config.products! - existingData.productCount)
      results.products = await seedProducts(productsToCreate)
    }
    
    console.log('')
    
    // 3. Seed Inventory
    console.log('📦 STEP 3: Setting up Inventory Management')
    console.log('-'.repeat(40))
    
    const inventoryItems = await prisma.inventoryItem.count()
    if (config.skipExisting && inventoryItems > 0) {
      console.log(`⏭️  Skipping inventory - already have ${inventoryItems} inventory items`)
      results.inventory = { locations: [], inventoryItems: [], movements: [] }
    } else {
      results.inventory = await seedInventory()
    }
    
    console.log('')
    
    // 4. Seed Orders
    console.log('🛒 STEP 4: Creating Orders')
    console.log('-'.repeat(40))
    
    if (config.skipExisting && existingData.orderCount >= config.orders!) {
      console.log(`⏭️  Skipping orders - already have ${existingData.orderCount} orders`)
      results.orders = []
    } else {
      const ordersToCreate = Math.max(0, config.orders! - existingData.orderCount)
      results.orders = await seedOrders(ordersToCreate)
    }
    
    console.log('')
    
    // 5. Seed Reviews
    console.log('⭐ STEP 5: Creating Product Reviews')
    console.log('-'.repeat(40))
    
    const productsWithReviews = await prisma.product.count({
      where: { reviewCount: { gt: 0 } }
    })
    
    if (config.skipExisting && productsWithReviews > 0) {
      console.log(`⏭️  Skipping reviews - already have reviews on ${productsWithReviews} products`)
      results.reviews = []
    } else {
      results.reviews = await seedReviews(config.reviewsPerProduct!)
    }
    
    console.log('')
    console.log('🎉 DATABASE SEEDING COMPLETED!')
    console.log('=' .repeat(60))
    
    // Final statistics
    const finalData = await checkExistingData()
    
    console.log('📊 Final Database Statistics:')
    console.log(`   - Users: ${finalData.userCount}`)
    console.log(`   - Products: ${finalData.productCount}`)
    console.log(`   - Orders: ${finalData.orderCount}`)
    console.log(`   - Reviews: ${finalData.reviewCount}`)
    
    // Calculate totals
    const totalOrderValue = await prisma.order.aggregate({
      _sum: { total: true },
      where: { paymentStatus: 'paid' }
    })
    
    const averageOrderValue = await prisma.order.aggregate({
      _avg: { total: true }
    })
    
    const inventoryValue = await prisma.inventoryItem.aggregate({
      _sum: { quantity: true }
    })
    
    console.log('')
    console.log('💰 Business Metrics:')
    console.log(`   - Total Revenue: R${(totalOrderValue._sum.total || 0).toLocaleString()}`)
    console.log(`   - Average Order Value: R${(averageOrderValue._avg.total || 0).toFixed(2)}`)
    console.log(`   - Total Inventory Items: ${(inventoryValue._sum.quantity || 0).toLocaleString()}`)
    
    const activeProducts = await prisma.product.count({ where: { status: 'active' } })
    const averageRating = await prisma.productReview.aggregate({
      _avg: { rating: true }
    })
    
    console.log(`   - Active Products: ${activeProducts}`)
    console.log(`   - Average Product Rating: ${(averageRating._avg.rating || 0).toFixed(1)} stars`)
    
    console.log('')
    console.log('✅ Your e-commerce database is now fully populated!')
    console.log('🌐 You can now test all features of your application.')
    
    return results
    
  } catch (error) {
    console.error('❌ Database seeding failed:', error)
    throw error
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2)
  const options: SeedingOptions = {}
  
  // Parse command line arguments
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i]?.replace('--', '')
    const value = args[i + 1]
    
    switch (key) {
      case 'users':
        options.users = parseInt(value) || 100
        break
      case 'products':
        options.products = parseInt(value) || 50
        break
      case 'orders':
        options.orders = parseInt(value) || 75
        break
      case 'reviews':
        options.reviewsPerProduct = parseInt(value) || 5
        break
      case 'force':
        options.skipExisting = false
        break
      case 'quiet':
        options.verbose = false
        break
    }
  }
  
  // Show help
  if (args.includes('--help') || args.includes('-h')) {
    console.log('🌱 Database Seeder')
    console.log('')
    console.log('Usage: pnpm seed:all [options]')
    console.log('')
    console.log('Options:')
    console.log('  --users <number>     Number of users to create (default: 100)')
    console.log('  --products <number>  Number of products to create (default: 50)')
    console.log('  --orders <number>    Number of orders to create (default: 75)')
    console.log('  --reviews <number>   Reviews per product (default: 5)')
    console.log('  --force              Force recreation of existing data')
    console.log('  --quiet              Reduce output verbosity')
    console.log('  --help, -h           Show this help message')
    console.log('')
    console.log('Examples:')
    console.log('  pnpm seed:all')
    console.log('  pnpm seed:all --users 200 --products 100')
    console.log('  pnpm seed:all --force --quiet')
    process.exit(0)
  }
  
  seedDatabase(options)
    .then(() => {
      console.log('🎉 All seeding completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Seeding failed:', error)
      process.exit(1)
    })
    .finally(() => {
      prisma.$disconnect()
    })
}
