/**
 * Enhanced Order Seeder
 * Creates realistic orders using existing users and products
 */

import { PrismaClient } from '@prisma/client'
import { faker } from '@faker-js/faker'

const prisma = new PrismaClient()

// Order status distributions for realistic data
const orderStatusDistribution = [
  { status: 'pending', weight: 5 },
  { status: 'confirmed', weight: 15 },
  { status: 'processing', weight: 25 },
  { status: 'shipped', weight: 30 },
  { status: 'delivered', weight: 20 },
  { status: 'cancelled', weight: 3 },
  { status: 'refunded', weight: 2 }
]

const paymentStatusDistribution = [
  { status: 'pending', weight: 8 },
  { status: 'paid', weight: 80 },
  { status: 'failed', weight: 7 },
  { status: 'refunded', weight: 3 },
  { status: 'partially_refunded', weight: 2 }
]

function getWeightedRandom<T extends { weight: number }>(items: T[]): T {
  const totalWeight = items.reduce((sum, item) => sum + item.weight, 0)
  let random = Math.random() * totalWeight
  
  for (const item of items) {
    random -= item.weight
    if (random <= 0) return item
  }
  
  return items[0] // fallback
}

function generateOrderNumber(): string {
  const today = new Date()
  const year = today.getFullYear().toString().slice(-2)
  const month = (today.getMonth() + 1).toString().padStart(2, '0')
  const day = today.getDate().toString().padStart(2, '0')
  const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0')
  
  return `ORD${year}${month}${day}${random}`
}

function generateOrderDates(status: string) {
  const now = new Date()
  const createdAt = faker.date.past({ years: 0.25 }) // About 90 days
  
  let confirmedAt = null
  let processedAt = null
  let shippedAt = null
  let deliveredAt = null
  let cancelledAt = null
  
  if (['confirmed', 'processing', 'shipped', 'delivered'].includes(status)) {
    confirmedAt = faker.date.between({ from: createdAt, to: new Date(createdAt.getTime() + 24 * 60 * 60 * 1000) })
  }
  
  if (['processing', 'shipped', 'delivered'].includes(status)) {
    processedAt = faker.date.between({ from: confirmedAt!, to: new Date(confirmedAt!.getTime() + 48 * 60 * 60 * 1000) })
  }
  
  if (['shipped', 'delivered'].includes(status)) {
    shippedAt = faker.date.between({ from: processedAt!, to: new Date(processedAt!.getTime() + 72 * 60 * 60 * 1000) })
  }
  
  if (status === 'delivered') {
    deliveredAt = faker.date.between({ from: shippedAt!, to: new Date(shippedAt!.getTime() + 168 * 60 * 60 * 1000) })
  }
  
  if (status === 'cancelled') {
    cancelledAt = faker.date.between({ from: createdAt, to: now })
  }
  
  return { createdAt, confirmedAt, processedAt, shippedAt, deliveredAt, cancelledAt }
}

async function createOrderItems(products: any[], orderValue: 'low' | 'medium' | 'high') {
  const itemCounts = {
    low: { min: 1, max: 2 },
    medium: { min: 2, max: 4 },
    high: { min: 3, max: 6 }
  }
  
  const count = faker.number.int(itemCounts[orderValue])
  const selectedProducts = faker.helpers.arrayElements(products, count)
  
  let subtotal = 0
  const orderItems = []
  
  for (const product of selectedProducts) {
    // Get a random variant for this product
    const variants = await prisma.productVariant.findMany({
      where: { productId: product.id }
    })
    
    const variant = variants.length > 0 ? faker.helpers.arrayElement(variants) : null
    const quantity = faker.number.int({ min: 1, max: 3 })
    const unitPrice = Number(variant?.price || product.price)
    const totalPrice = unitPrice * quantity
    subtotal += totalPrice
    
    orderItems.push({
      productId: product.id,
      variantId: variant?.id,
      quantity,
      unitPrice,
      totalPrice,
      currency: 'ZAR',
      productTitle: product.title,
      productSlug: product.slug,
      productImage: null, // Will be populated from product images if available
      variantTitle: variant?.title,
      sku: variant?.sku,
      fulfillmentStatus: 'unfulfilled',
      fulfillableQuantity: quantity,
      fulfilledQuantity: 0,
      returnableQuantity: quantity,
      refundableQuantity: quantity,
      requiresShipping: true,
      isTaxable: true
    })
  }
  
  return { orderItems, subtotal }
}

function calculateOrderTotals(subtotal: number) {
  const totalTax = subtotal * 0.15 // 15% VAT
  const totalShipping = subtotal > 500 ? 0 : 65 // Free shipping over R500
  const total = subtotal + totalTax + totalShipping
  
  return { totalTax, totalShipping, total }
}

export async function seedOrders(count: number = 50) {
  try {
    console.log(`🛒 Creating ${count} sample orders...`)
    
    // Get existing data
    const users = await prisma.user.findMany({
      where: {
        isActive: true,
        customerSince: { not: null }
      }
    })
    
    const products = await prisma.product.findMany({
      where: { status: 'active' }
    })
    
    if (users.length === 0) {
      throw new Error('No users found. Please run the user seeder first.')
    }
    
    if (products.length === 0) {
      throw new Error('No products found. Please run the product seeder first.')
    }
    
    const orders = []
    
    for (let i = 0; i < count; i++) {
      const user = faker.helpers.arrayElement(users)
      const orderStatus = getWeightedRandom(orderStatusDistribution)
      const paymentStatus = getWeightedRandom(paymentStatusDistribution)
      
      // Determine order value category
      const orderValueType = faker.helpers.arrayElement(['low', 'medium', 'high'])
      
      const { orderItems, subtotal } = await createOrderItems(products, orderValueType)
      const { totalTax, totalShipping, total } = calculateOrderTotals(subtotal)
      
      const dates = generateOrderDates(orderStatus.status)
      const orderNumber = generateOrderNumber()
      
      // Check if order already exists
      const existingOrder = await prisma.order.findFirst({
        where: { orderNumber }
      })
      
      if (!existingOrder) {
        const order = await prisma.order.create({
          data: {
            orderNumber,
            userId: user.id,
            customerEmail: user.email!,
            customerFirstName: user.firstName!,
            customerLastName: user.lastName!,
            customerPhone: user.phone!,
            billingAddress: {
              firstName: user.firstName!,
              lastName: user.lastName!,
              address1: faker.location.streetAddress(),
              city: faker.location.city(),
              province: faker.location.state(),
              country: 'South Africa',
              postalCode: faker.location.zipCode(),
              phone: user.phone!
            },
            shippingAddress: {
              firstName: user.firstName!,
              lastName: user.lastName!,
              address1: faker.location.streetAddress(),
              city: faker.location.city(),
              province: faker.location.state(),
              country: 'South Africa',
              postalCode: faker.location.zipCode(),
              phone: user.phone!
            },
            itemCount: orderItems.length,
            subtotal,
            totalTax,
            totalShipping,
            total,
            currency: 'ZAR',
            paymentStatus: paymentStatus.status,
            fulfillmentStatus: orderStatus.status === 'delivered' ? 'fulfilled' : 'unfulfilled',
            status: orderStatus.status,
            financialStatus: paymentStatus.status === 'paid' ? 'paid' : 'pending',
            source: faker.helpers.arrayElement(['web', 'mobile', 'admin', 'phone']),
            customerNote: Math.random() < 0.3 ? faker.lorem.sentence() : null,
            internalNotes: Math.random() < 0.2 ? [faker.lorem.sentence()] : [],
            tags: Math.random() < 0.4 ? faker.helpers.arrayElements(['urgent', 'vip', 'wholesale', 'gift', 'bulk'], { min: 1, max: 2 }) : [],
            createdAt: dates.createdAt,
            confirmedAt: dates.confirmedAt,
            processedAt: dates.processedAt,
            shippedAt: dates.shippedAt,
            deliveredAt: dates.deliveredAt,
            cancelledAt: dates.cancelledAt,
            items: {
              create: orderItems
            }
          }
        })
        
        orders.push(order)
        
        if ((i + 1) % 10 === 0) {
          console.log(`✅ Created ${i + 1}/${count} orders`)
        }
      }
    }
    
    console.log(`🎉 Successfully created ${orders.length} orders`)
    console.log(`💰 Total order value: ${orders.reduce((sum, order) => sum + Number(order.total), 0).toLocaleString('en-ZA', { style: 'currency', currency: 'ZAR' })}`)
    console.log(`📊 Status distribution:`)
    
    const statusCounts = orders.reduce((acc, order) => {
      acc[order.status] = (acc[order.status] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`   - ${status}: ${count}`)
    })
    
    return orders
    
  } catch (error) {
    console.error('❌ Error seeding orders:', error)
    throw error
  }
}

// Run the seeder if called directly
if (require.main === module) {
  const count = process.argv[2] ? parseInt(process.argv[2]) : 50
  
  seedOrders(count)
    .then(() => {
      console.log('🎉 Order seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Order seeding failed:', error)
      process.exit(1)
    })
    .finally(() => {
      prisma.$disconnect()
    })
}
