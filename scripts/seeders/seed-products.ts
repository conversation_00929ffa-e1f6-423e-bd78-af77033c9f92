/**
 * Product Catalog Seeder
 * Creates comprehensive product catalog with variants, images, and categories
 */

import { PrismaClient } from '@prisma/client'
import { faker } from '@faker-js/faker'

const prisma = new PrismaClient()

// Kids clothing specific data
const productCategories = [
  { name: 'Baby Clothing', slug: 'baby-clothing', description: 'Comfortable clothing for babies 0-24 months' },
  { name: 'Toddler Clothing', slug: 'toddler-clothing', description: 'Stylish clothes for toddlers 2-4 years' },
  { name: 'Kids Clothing', slug: 'kids-clothing', description: 'Trendy clothing for kids 5-12 years' },
  { name: 'Accessories', slug: 'accessories', description: 'Hats, bags, and other accessories' },
  { name: 'Shoes', slug: 'shoes', description: 'Comfortable and stylish footwear' },
  { name: 'Sleepwear', slug: 'sleepwear', description: 'Cozy pajamas and sleepwear' },
  { name: 'Outerwear', slug: 'outerwear', description: 'Jackets, coats, and warm clothing' }
]

const productTypes = [
  'T-Shirts', 'Dresses', 'Pants', 'Shorts', 'Skirts', 'Hoodies', 'Sweaters',
  'Jackets', 'Pajamas', 'Underwear', 'Socks', 'Shoes', 'Hats', 'Bags'
]

const vendors = [
  'Coco Kids', 'Little Stars', 'Tiny Tots', 'Mini Fashion', 'Baby Bliss',
  'Kiddie Couture', 'Sweet Dreams', 'Playful Prints', 'Comfort Kids'
]

const colors = [
  'Red', 'Blue', 'Green', 'Yellow', 'Pink', 'Purple', 'Orange', 'Black',
  'White', 'Gray', 'Navy', 'Mint', 'Coral', 'Lavender', 'Turquoise'
]

const sizes = {
  baby: ['Newborn', '0-3M', '3-6M', '6-9M', '9-12M', '12-18M', '18-24M'],
  toddler: ['2T', '3T', '4T'],
  kids: ['XS', 'S', 'M', 'L', 'XL'],
  shoes: ['4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '1', '2', '3']
}

function generateProductTitle(type: string, category: string) {
  const adjectives = ['Cute', 'Adorable', 'Comfy', 'Stylish', 'Cozy', 'Fun', 'Trendy', 'Sweet', 'Soft', 'Colorful']
  const adjective = faker.helpers.arrayElement(adjectives)
  
  if (category.includes('Baby')) {
    return `${adjective} Baby ${type}`
  } else if (category.includes('Toddler')) {
    return `${adjective} Toddler ${type}`
  } else {
    return `${adjective} Kids ${type}`
  }
}

function generateProductDescription(title: string, type: string) {
  const features = [
    'Made from 100% organic cotton',
    'Machine washable',
    'Hypoallergenic materials',
    'Comfortable fit',
    'Durable construction',
    'Fade-resistant colors',
    'Easy care instructions',
    'Soft and breathable fabric'
  ]
  
  const selectedFeatures = faker.helpers.arrayElements(features, { min: 3, max: 5 })
  
  return `${title} perfect for everyday wear. Features: ${selectedFeatures.join(', ')}. Available in multiple colors and sizes.`
}

function getSizesForCategory(category: string) {
  if (category.includes('Baby')) return sizes.baby
  if (category.includes('Toddler')) return sizes.toddler
  if (category.includes('Shoes')) return sizes.shoes
  return sizes.kids
}

function generateProductImages(productSlug: string, colorCount: number) {
  const images = []
  for (let i = 0; i < Math.min(colorCount + 1, 4); i++) {
    images.push({
      url: `https://images.unsplash.com/photo-${faker.string.numeric(10)}?w=800&h=800&fit=crop`,
      altText: `${productSlug} image ${i + 1}`,
      position: i,
      width: 800,
      height: 800
    })
  }
  return images
}

export async function seedProducts(count: number = 50) {
  try {
    console.log(`🛍️ Creating ${count} products with variants...`)
    
    // First ensure categories exist
    console.log('📂 Creating product categories...')
    const categories = []
    for (const categoryData of productCategories) {
      const existingCategory = await prisma.productCategory.findUnique({
        where: { slug: categoryData.slug }
      })
      
      if (!existingCategory) {
        const category = await prisma.productCategory.create({
          data: {
            ...categoryData,
            isVisible: true,
            position: categories.length
          }
        })
        categories.push(category)
      } else {
        categories.push(existingCategory)
      }
    }
    
    const products = []
    
    for (let i = 0; i < count; i++) {
      const productType = faker.helpers.arrayElement(productTypes)
      const category = faker.helpers.arrayElement(categories)
      const vendor = faker.helpers.arrayElement(vendors)
      const title = generateProductTitle(productType, category.name)
      const slug = `${title.toLowerCase().replace(/[^a-z0-9]+/g, '-')}-${i}`
      const handle = slug
      
      // Check if product already exists
      const existingProduct = await prisma.product.findUnique({
        where: { slug }
      })
      
      if (!existingProduct) {
        const basePrice = faker.number.float({ min: 89, max: 599, fractionDigits: 2 })
        const compareAtPrice = Math.random() < 0.3 ? basePrice * 1.2 : null
        const costPerItem = basePrice * 0.6
        
        const product = await prisma.product.create({
          data: {
            title,
            slug,
            handle,
            description: generateProductDescription(title, productType),
            vendor,
            productType,
            status: faker.helpers.arrayElement(['active', 'active', 'active', 'draft']), // 75% active
            publishedAt: Math.random() < 0.8 ? faker.date.past({ years: 1 }) : null,
            price: basePrice,
            compareAtPrice,
            costPerItem,
            trackQuantity: true,
            continueSellingWhenOutOfStock: Math.random() < 0.2,
            requiresShipping: productType !== 'Digital',
            isTaxable: true,
            weight: faker.number.float({ min: 0.1, max: 2.0, fractionDigits: 2 }),
            weightUnit: 'kg',
            seoTitle: `${title} - Premium Kids Clothing | Coco Milk Kids`,
            seoDescription: `Shop ${title.toLowerCase()} at Coco Milk Kids. High-quality, comfortable clothing for children.`,
            seoKeywords: [title.toLowerCase(), productType.toLowerCase(), 'kids clothing', 'children wear']
          }
        })
        
        // Create product category relation
        await prisma.productCategoryRelation.create({
          data: {
            productId: product.id,
            categoryId: category.id
          }
        })
        
        // Create product variants
        const availableColors = faker.helpers.arrayElements(colors, { min: 2, max: 4 })
        const availableSizes = getSizesForCategory(category.name)
        const selectedSizes = faker.helpers.arrayElements(availableSizes, { min: 3, max: 6 })
        
        // Create color option
        const colorOption = await prisma.productOption.create({
          data: {
            productId: product.id,
            name: 'Color',
            position: 1,
            values: availableColors
          }
        })
        
        // Create size option
        const sizeOption = await prisma.productOption.create({
          data: {
            productId: product.id,
            name: 'Size',
            position: 2,
            values: selectedSizes
          }
        })
        
        // Create variants for each color/size combination
        let variantPosition = 1
        for (const color of availableColors) {
          for (const size of selectedSizes) {
            const variantPrice = basePrice + faker.number.float({ min: -20, max: 50, fractionDigits: 2 })
            const sku = `${product.handle.toUpperCase()}-${color.toUpperCase()}-${size.replace(/[^A-Z0-9]/g, '')}`
            
            const variant = await prisma.productVariant.create({
              data: {
                productId: product.id,
                title: `${color} / ${size}`,
                price: Math.max(variantPrice, 50), // Minimum price
                compareAtPrice: compareAtPrice ? compareAtPrice + (variantPrice - basePrice) : null,
                costPerItem: costPerItem + (variantPrice - basePrice) * 0.6,
                sku,
                barcode: faker.string.numeric(12),
                position: variantPosition++,
                weight: faker.number.float({ min: 0.1, max: 2.0, fractionDigits: 2 }),
                weightUnit: 'kg',
                requiresShipping: true,
                taxable: true,
                inventoryQuantity: faker.number.int({ min: 0, max: 100 }),
                inventoryPolicy: 'deny',
                fulfillmentService: 'manual',
                inventoryManagement: true,
                metafields: {
                  color: color.toLowerCase(),
                  size: size.toLowerCase()
                }
              }
            })

            // Create variant options
            await prisma.productVariantOption.create({
              data: {
                variantId: variant.id,
                name: 'Color',
                value: color
              }
            })

            await prisma.productVariantOption.create({
              data: {
                variantId: variant.id,
                name: 'Size',
                value: size
              }
            })
          }
        }
        
        // Create product images
        const imageData = generateProductImages(product.slug, availableColors.length)
        for (const image of imageData) {
          await prisma.productImage.create({
            data: {
              productId: product.id,
              ...image
            }
          })
        }
        
        products.push(product)
        
        if ((i + 1) % 10 === 0) {
          console.log(`✅ Created ${i + 1}/${count} products`)
        }
      }
    }
    
    console.log(`🎉 Successfully created ${products.length} products`)
    console.log(`📊 Product statistics:`)
    console.log(`   - Active products: ${products.filter(p => p.status === 'active').length}`)
    console.log(`   - Categories: ${categories.length}`)
    console.log(`   - Average price: R${(products.reduce((sum, p) => sum + Number(p.price), 0) / products.length).toFixed(2)}`)
    
    return { products, categories }
    
  } catch (error) {
    console.error('❌ Error seeding products:', error)
    throw error
  }
}

// Run the seeder if called directly
if (require.main === module) {
  const count = process.argv[2] ? parseInt(process.argv[2]) : 50
  
  seedProducts(count)
    .then(() => {
      console.log('🎉 Product seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Product seeding failed:', error)
      process.exit(1)
    })
    .finally(() => {
      prisma.$disconnect()
    })
}
