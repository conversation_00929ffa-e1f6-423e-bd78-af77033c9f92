#!/usr/bin/env tsx

import { seedPageBuilderPages } from '../lib/seeders/page-builder-seeder-simple'

async function main() {
  try {
    console.log('🚀 Starting Page Builder seeding process...')
    console.log('⚠️  This will clear existing pages and create new ones.')
    
    await seedPageBuilderPages()
    
    console.log('🎉 Page Builder seeding completed successfully!')
    console.log('')
    console.log('📄 Created pages:')
    console.log('  • Home (/)')
    console.log('  • About (/about)')
    console.log('  • Contact (/contact)')
    console.log('  • FAQ (/faq)')
    console.log('  • Brand (/brand)')
    console.log('  • Help (/help)')
    console.log('  • Privacy Policy (/privacy)')
    console.log('  • Terms of Service (/terms)')
    console.log('  • Shipping (/shipping)')
    console.log('  • Stores (/stores)')
    console.log('  • Newsletter (/newsletter)')
    console.log('')
    console.log('🎯 Next steps:')
    console.log('  1. Visit /admin/page-builder to manage pages')
    console.log('  2. Test the pages in your browser')
    console.log('  3. Customize content as needed')
    console.log('  4. Update navigation menus to use new pages')
    
    process.exit(0)
  } catch (error) {
    console.error('❌ Error during seeding:', error)
    process.exit(1)
  }
}

main()
