#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function seedMinimal() {
  console.log('🌱 Starting minimal database seeding...')

  try {
    // 1. Create basic admin user if it doesn't exist
    console.log('👨‍💼 Creating admin user...')
    
    const adminUser = await prisma.adminUser.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        firstName: 'System',
        lastName: 'Administrator',
        displayName: 'System Admin',
        passwordHash: await bcrypt.hash('admin123!', 12),
        isActive: true,
        isEmailVerified: true
      }
    })

    // 2. Create basic inventory location
    console.log('📍 Creating inventory location...')
    
    const warehouse = await prisma.inventoryLocation.upsert({
      where: { code: 'WH-CPT-001' },
      update: {},
      create: {
        name: 'Main Warehouse - Cape Town',
        code: 'WH-CPT-001',
        type: 'warehouse',
        address: {
          street: '123 Industrial Road, Montague Gardens',
          city: 'Cape Town',
          province: 'Western Cape',
          postalCode: '7441',
          country: 'South Africa'
        },
        contactName: 'Warehouse Manager',
        contactEmail: '<EMAIL>',
        contactPhone: '+27 21 555 0123',
        isActive: true,
        isPrimary: true,
        allowsInventory: true,
        allowsFulfillment: true,
        maxCapacity: 10000,
        currentUtilization: 0
      }
    })

    // 3. Create basic product category
    console.log('📂 Creating product category...')
    
    const category = await prisma.productCategory.upsert({
      where: { slug: 'kids-clothing' },
      update: {},
      create: {
        name: 'Kids Clothing',
        slug: 'kids-clothing',
        description: 'Quality clothing for children',
        isVisible: true,
        position: 1,
        seoTitle: 'Kids Clothing | Coco Milk Kids',
        seoDescription: 'Shop quality kids clothing at Coco Milk Kids.'
      }
    })

    // 4. Create basic product collection
    console.log('📚 Creating product collection...')
    
    const collection = await prisma.productCollection.upsert({
      where: { slug: 'featured' },
      update: {},
      create: {
        title: 'Featured Products',
        slug: 'featured',
        description: 'Our featured collection of kids clothing',
        sortOrder: 'manual',
        isVisible: true,
        seoTitle: 'Featured Products | Coco Milk Kids',
        seoDescription: 'Discover our featured kids clothing collection.'
      }
    })

    // 5. Create basic site settings
    console.log('⚙️ Creating site settings...')
    
    const existingSettings = await prisma.siteSettings.findFirst()
    
    if (!existingSettings) {
      await prisma.siteSettings.create({
        data: {
          siteName: 'Coco Milk Kids',
          siteDescription: 'Premium South African kids clothing with comfort, style, and quality.',
          siteUrl: 'https://cocomilkkids.com',
          logoUrl: '/images/logo.svg',
          faviconUrl: '/images/favicon.ico',
          socialMedia: {
            facebook: 'https://facebook.com/cocomilkkids',
            instagram: 'https://instagram.com/cocomilkkids',
            twitter: 'https://twitter.com/cocomilkkids'
          },
          seo: {
            defaultTitle: 'Coco Milk Kids - Premium South African Kids Clothing',
            defaultDescription: 'Shop premium kids clothing at Coco Milk Kids. Quality, comfort, and style for South African children.',
            keywords: ['kids clothing', 'south africa', 'children fashion', 'organic cotton', 'baby clothes']
          },
          ecommerce: {
            currency: 'ZAR',
            taxRate: 0.15,
            freeShippingThreshold: 500,
            shippingRate: 65
          }
        }
      })
    }

    console.log('✅ Minimal database seeding completed successfully!')
    console.log(`Created:`)
    console.log(`- 1 admin user (<EMAIL>)`)
    console.log(`- 1 inventory location`)
    console.log(`- 1 product category`)
    console.log(`- 1 product collection`)
    console.log(`- Site settings configuration`)
    console.log('')
    console.log('🔗 You can now:')
    console.log('   • Login as admin: <EMAIL> / admin123!')
    console.log('   • Start adding products through the admin interface')
    console.log('   • Run the full seeder: pnpm seed:basic')

  } catch (error) {
    console.error('❌ Error seeding database:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seeder
if (require.main === module) {
  seedMinimal()
    .then(() => {
      console.log('🎉 Minimal seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Minimal seeding failed:', error)
      process.exit(1)
    })
}

export { seedMinimal }
