import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedFrontendTestData() {
  console.log('🌱 Seeding frontend test data...')

  try {
    // Create default post type first
    await prisma.postType.upsert({
      where: { name: 'post' },
      update: {},
      create: {
        name: 'post',
        label: 'Post',
        labelPlural: 'Posts',
        description: 'Default blog posts',
        isPublic: true,
        isHierarchical: false,
        hasArchive: true,
        supportsTitle: true,
        supportsContent: true,
        supportsExcerpt: true,
        supportsThumbnail: true,
        supportsComments: true,
        supportsRevisions: true,
        supportsPageBuilder: false,
        menuPosition: 5,
        taxonomies: ['category', 'tag'],
        templates: ['single-post', 'post-grid'],
        isSystem: true,
        isActive: true
      }
    })

    // Create test post types
    await prisma.postType.upsert({
      where: { name: 'portfolio' },
      update: {},
      create: {
        name: 'portfolio',
        label: 'Portfolio',
        labelPlural: 'Portfolio Items',
        description: 'Showcase of our work and projects',
        isPublic: true,
        isHierarchical: false,
        hasArchive: true,
        supportsTitle: true,
        supportsContent: true,
        supportsExcerpt: true,
        supportsThumbnail: true,
        supportsComments: false,
        supportsRevisions: true,
        supportsPageBuilder: true,
        menuPosition: 20,
        taxonomies: ['category', 'tag'],
        templates: ['portfolio-single', 'portfolio-grid'],
        isSystem: false,
        isActive: true
      }
    })

    await prisma.postType.upsert({
      where: { name: 'testimonial' },
      update: {},
      create: {
        name: 'testimonial',
        label: 'Testimonial',
        labelPlural: 'Testimonials',
        description: 'Customer testimonials and reviews',
        isPublic: true,
        isHierarchical: false,
        hasArchive: true,
        supportsTitle: true,
        supportsContent: true,
        supportsExcerpt: true,
        supportsThumbnail: true,
        supportsComments: false,
        supportsRevisions: true,
        supportsPageBuilder: false,
        menuPosition: 25,
        taxonomies: ['category'],
        templates: ['testimonial-single'],
        isSystem: false,
        isActive: true
      }
    })

    // Create taxonomies
    const categoryTaxonomy = await prisma.taxonomy.upsert({
      where: { name: 'category' },
      update: {},
      create: {
        name: 'category',
        label: 'Category',
        labelPlural: 'Categories',
        description: 'Content categories',
        isHierarchical: true,
        isPublic: true,
        isSystem: true,
        isActive: true
      }
    })

    await prisma.taxonomy.upsert({
      where: { name: 'tag' },
      update: {},
      create: {
        name: 'tag',
        label: 'Tag',
        labelPlural: 'Tags',
        description: 'Content tags',
        isHierarchical: false,
        isPublic: true,
        isSystem: true,
        isActive: true
      }
    })

    // Create taxonomy terms
    const webDesignCategory = await prisma.taxonomyTerm.upsert({
      where: {
        taxonomyId_slug: {
          taxonomyId: categoryTaxonomy.id,
          slug: 'web-design'
        }
      },
      update: {},
      create: {
        name: 'Web Design',
        slug: 'web-design',
        description: 'Web design projects and articles',
        taxonomyId: categoryTaxonomy.id
      }
    })

    const ecommerceCategory = await prisma.taxonomyTerm.upsert({
      where: {
        taxonomyId_slug: {
          taxonomyId: categoryTaxonomy.id,
          slug: 'ecommerce'
        }
      },
      update: {},
      create: {
        name: 'E-commerce',
        slug: 'ecommerce',
        description: 'E-commerce related content',
        taxonomyId: categoryTaxonomy.id
      }
    })

    // Create test pages
    const aboutPage = await prisma.page.upsert({
      where: { slug: 'about-us-test' },
      update: {},
      create: {
        title: 'About Us - Test Page',
        slug: 'about-us-test',
        description: 'Learn more about our company and mission',
        status: 'published',
        type: 'page',
        seoTitle: 'About Us | Coco Milk Kids',
        seoDescription: 'Discover the story behind Coco Milk Kids and our commitment to quality children\'s clothing.',
        seoKeywords: ['about us', 'company', 'mission', 'children clothing'],
        isHomePage: false,
        isLandingPage: false,
        allowComments: false,
        publishedAt: new Date(),
        createdBy: 'system',
        updatedBy: 'system'
      }
    })

    // Create page blocks for the about page
    await prisma.pageBlock.createMany({
      data: [
        {
          pageId: aboutPage.id,
          blockType: 'hero',
          position: 1,
          isVisible: true,
          configuration: {
            layout: 'centered',
            height: 'medium'
          },
          content: {
            title: 'About Coco Milk Kids',
            subtitle: 'Premium children\'s clothing for growing adventures',
            description: 'We believe every child deserves clothing that\'s as adventurous as they are.',
            buttonText: 'Our Story',
            buttonLink: '#story'
          },
          styling: {
            backgroundColor: '#f8f9fa',
            textColor: '#333333',
            titleSize: 'large'
          }
        },
        {
          pageId: aboutPage.id,
          blockType: 'text',
          position: 2,
          isVisible: true,
          configuration: {
            columns: 1,
            alignment: 'left'
          },
          content: {
            title: 'Our Story',
            content: '<p>Founded in 2020, Coco Milk Kids started with a simple mission: to create beautiful, comfortable, and sustainable clothing for children. We understand that kids need clothes that can keep up with their active lifestyles while looking great.</p><p>Our team of designers and parents work together to create pieces that are both stylish and practical, using only the finest materials that are gentle on sensitive skin.</p>'
          },
          styling: {
            padding: '40px 0',
            maxWidth: '800px'
          }
        }
      ]
    })

    // Create test blog posts
    const blogPost1 = await prisma.post.upsert({
      where: { slug: 'choosing-right-school-uniform' },
      update: {},
      create: {
        title: 'Choosing the Right School Uniform for Your Child',
        slug: 'choosing-right-school-uniform',
        content: '<p>Selecting the perfect school uniform is more than just following dress codes. It\'s about ensuring your child feels comfortable and confident throughout their school day.</p><h2>Key Considerations</h2><p>When choosing school uniforms, consider fabric quality, fit, durability, and ease of care. Our school uniform collection is designed with these factors in mind.</p>',
        contentHtml: '<p>Selecting the perfect school uniform is more than just following dress codes. It\'s about ensuring your child feels comfortable and confident throughout their school day.</p><h2>Key Considerations</h2><p>When choosing school uniforms, consider fabric quality, fit, durability, and ease of care. Our school uniform collection is designed with these factors in mind.</p>',
        excerpt: 'A comprehensive guide to selecting the perfect school uniform that combines comfort, style, and durability for your child.',
        status: 'published',
        postType: 'post',
        featuredImage: '/images/blog/school-uniform-guide.jpg',
        featuredImageAlt: 'Children wearing school uniforms',
        seoTitle: 'School Uniform Guide | Coco Milk Kids',
        seoDescription: 'Expert tips for choosing the perfect school uniform. Quality, comfort, and style for your child\'s education journey.',
        seoKeywords: ['school uniform', 'children clothing', 'school wear', 'uniform guide'],
        viewCount: 245,
        shareCount: 12,
        likeCount: 18,
        commentCount: 5,
        allowComments: true,
        publishedAt: new Date('2024-01-15'),
        authorName: 'Sarah Johnson',
        authorEmail: '<EMAIL>'
      }
    })

    // Create portfolio items
    const portfolioItem1 = await prisma.post.upsert({
      where: { slug: 'summer-collection-2024' },
      update: {},
      create: {
        title: 'Summer Collection 2024 - Bright & Breezy',
        slug: 'summer-collection-2024',
        content: '<p>Our Summer 2024 collection captures the essence of childhood joy with vibrant colors and breathable fabrics perfect for warm weather adventures.</p><h2>Design Philosophy</h2><p>Each piece in this collection was designed with active children in mind, featuring moisture-wicking fabrics and reinforced seams for durability.</p>',
        contentHtml: '<p>Our Summer 2024 collection captures the essence of childhood joy with vibrant colors and breathable fabrics perfect for warm weather adventures.</p><h2>Design Philosophy</h2><p>Each piece in this collection was designed with active children in mind, featuring moisture-wicking fabrics and reinforced seams for durability.</p>',
        excerpt: 'A vibrant summer collection featuring breathable fabrics and playful designs perfect for active children.',
        status: 'published',
        postType: 'portfolio',
        featuredImage: '/images/portfolio/summer-2024-hero.jpg',
        featuredImageAlt: 'Summer 2024 collection showcase',
        seoTitle: 'Summer Collection 2024 | Coco Milk Kids Portfolio',
        seoDescription: 'Explore our vibrant Summer 2024 collection featuring breathable fabrics and playful designs for active children.',
        seoKeywords: ['summer collection', 'children fashion', 'portfolio', '2024 collection'],
        customFields: {
          client: 'Coco Milk Kids',
          projectUrl: '/collections/summer',
          technologies: ['Sustainable Cotton', 'Moisture-Wicking', 'UV Protection'],
          gallery: [
            '/images/portfolio/summer-2024-1.jpg',
            '/images/portfolio/summer-2024-2.jpg',
            '/images/portfolio/summer-2024-3.jpg'
          ]
        },
        viewCount: 156,
        shareCount: 8,
        likeCount: 24,
        allowComments: true,
        publishedAt: new Date('2024-02-01'),
        authorName: 'Design Team',
        authorEmail: '<EMAIL>'
      }
    })

    // Create testimonials
    await prisma.post.upsert({
      where: { slug: 'amazing-quality-sarah-m' },
      update: {},
      create: {
        title: 'Amazing Quality and Comfort',
        slug: 'amazing-quality-sarah-m',
        content: '<p>I\'ve been shopping at Coco Milk Kids for over two years now, and I\'m consistently impressed with the quality and durability of their clothing. My daughter loves the comfortable fit and stylish designs.</p>',
        contentHtml: '<p>I\'ve been shopping at Coco Milk Kids for over two years now, and I\'m consistently impressed with the quality and durability of their clothing. My daughter loves the comfortable fit and stylish designs.</p>',
        excerpt: 'Consistently impressed with the quality and durability. My daughter loves the comfortable fit and stylish designs.',
        status: 'published',
        postType: 'testimonial',
        featuredImage: '/images/testimonials/sarah-m.jpg',
        featuredImageAlt: 'Sarah M. customer photo',
        customFields: {
          rating: 5,
          company: 'Parent',
          position: 'Mother of 2'
        },
        viewCount: 89,
        allowComments: false,
        publishedAt: new Date('2024-01-20'),
        authorName: 'Sarah M.'
      }
    })

    // Create taxonomy term relationships
    await prisma.postTaxonomyTerm.createMany({
      data: [
        {
          postId: portfolioItem1.id,
          termId: webDesignCategory.id
        },
        {
          postId: blogPost1.id,
          termId: ecommerceCategory.id
        }
      ]
    })

    console.log('✅ Frontend test data seeded successfully!')
    console.log(`
📄 Test Pages Created:
- About Us Test: /about-us-test

📝 Test Blog Posts Created:
- School Uniform Guide: /choosing-right-school-uniform

🎨 Test Portfolio Items Created:
- Summer Collection 2024: /portfolio/summer-collection-2024

💬 Test Testimonials Created:
- Customer Review: /testimonial/amazing-quality-sarah-m

📂 Test Archives Available:
- Portfolio Archive: /portfolio
- Testimonial Archive: /testimonial

🔍 SEO Routes:
- Sitemap: /sitemap.xml
- Robots: /robots.txt
    `)

  } catch (error) {
    console.error('❌ Error seeding frontend test data:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  seedFrontendTestData()
    .catch((error) => {
      console.error(error)
      process.exit(1)
    })
}

export default seedFrontendTestData
