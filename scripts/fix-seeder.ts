#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to modify the comprehensive seeder to use upsert operations
 * This makes the seeder safe to run multiple times and work with existing data
 */

import fs from 'fs'
import path from 'path'

const SEEDER_PATH = path.join(__dirname, 'seed-database.ts')

function fixSeeder() {
  console.log('🔧 Fixing comprehensive seeder to use upsert operations...')
  
  let content = fs.readFileSync(SEEDER_PATH, 'utf8')
  
  // Fix subcategories to use upsert
  content = content.replace(
    /prisma\.productCategory\.create\({[\s\S]*?data: \{([\s\S]*?)\}[\s\S]*?\}\)/g,
    (match, dataContent) => {
      // Extract slug from the data content
      const slugMatch = dataContent.match(/slug: ['"`]([^'"`]+)['"`]/)
      if (slugMatch) {
        const slug = slugMatch[1]
        return `prisma.productCategory.upsert({
        where: { slug: '${slug}' },
        update: {},
        create: {${dataContent}}
      })`
      }
      return match
    }
  )
  
  // Fix collections to use upsert
  content = content.replace(
    /prisma\.productCollection\.create\({[\s\S]*?data: \{([\s\S]*?)\}[\s\S]*?\}\)/g,
    (match, dataContent) => {
      const slugMatch = dataContent.match(/slug: ['"`]([^'"`]+)['"`]/)
      if (slugMatch) {
        const slug = slugMatch[1]
        return `prisma.productCollection.upsert({
        where: { slug: '${slug}' },
        update: {},
        create: {${dataContent}}
      })`
      }
      return match
    }
  )
  
  // Fix product tags to use upsert
  content = content.replace(
    /prisma\.productTag\.create\({[\s\S]*?data: \{([\s\S]*?)\}[\s\S]*?\}\)/g,
    (match, dataContent) => {
      const slugMatch = dataContent.match(/slug: ['"`]([^'"`]+)['"`]/)
      if (slugMatch) {
        const slug = slugMatch[1]
        return `prisma.productTag.upsert({
        where: { slug: '${slug}' },
        update: {},
        create: {${dataContent}}
      })`
      }
      return match
    }
  )
  
  // Fix product attributes to use upsert
  content = content.replace(
    /prisma\.productAttribute\.create\({[\s\S]*?data: \{([\s\S]*?)\}[\s\S]*?\}\)/g,
    (match, dataContent) => {
      const slugMatch = dataContent.match(/slug: ['"`]([^'"`]+)['"`]/)
      if (slugMatch) {
        const slug = slugMatch[1]
        return `prisma.productAttribute.upsert({
        where: { slug: '${slug}' },
        update: {},
        create: {${dataContent}}
      })`
      }
      return match
    }
  )
  
  // Fix users to use upsert
  content = content.replace(
    /prisma\.user\.create\({[\s\S]*?data: \{([\s\S]*?)\}[\s\S]*?\}\)/g,
    (match, dataContent) => {
      const emailMatch = dataContent.match(/email: ['"`]([^'"`]+)['"`]/)
      if (emailMatch) {
        const email = emailMatch[1]
        return `prisma.user.upsert({
        where: { email: '${email}' },
        update: {},
        create: {${dataContent}}
      })`
      }
      return match
    }
  )
  
  // Fix products to use upsert
  content = content.replace(
    /prisma\.product\.create\({[\s\S]*?data: \{([\s\S]*?)\}[\s\S]*?\}\)/g,
    (match, dataContent) => {
      const slugMatch = dataContent.match(/slug: ['"`]([^'"`]+)['"`]/)
      if (slugMatch) {
        const slug = slugMatch[1]
        return `prisma.product.upsert({
        where: { slug: '${slug}' },
        update: {},
        create: {${dataContent}}
      })`
      }
      return match
    }
  )
  
  // Fix coupons to use upsert
  content = content.replace(
    /prisma\.coupon\.create\({[\s\S]*?data: \{([\s\S]*?)\}[\s\S]*?\}\)/g,
    (match, dataContent) => {
      const codeMatch = dataContent.match(/code: ['"`]([^'"`]+)['"`]/)
      if (codeMatch) {
        const code = codeMatch[1]
        return `prisma.coupon.upsert({
        where: { code: '${code}' },
        update: {},
        create: {${dataContent}}
      })`
      }
      return match
    }
  )
  
  // Fix newsletter subscribers to use upsert
  content = content.replace(
    /prisma\.newsletterSubscriber\.create\({[\s\S]*?data: \{([\s\S]*?)\}[\s\S]*?\}\)/g,
    (match, dataContent) => {
      const emailMatch = dataContent.match(/email: ['"`]([^'"`]+)['"`]/)
      if (emailMatch) {
        const email = emailMatch[1]
        return `prisma.newsletterSubscriber.upsert({
        where: { email: '${email}' },
        update: {},
        create: {${dataContent}}
      })`
      }
      return match
    }
  )
  
  // Fix site settings to use upsert with findFirst check
  content = content.replace(
    /await prisma\.siteSettings\.create\({[\s\S]*?data: \{([\s\S]*?)\}[\s\S]*?\}\)/g,
    (match, dataContent) => {
      return `const existingSettings = await prisma.siteSettings.findFirst()
    if (!existingSettings) {
      await prisma.siteSettings.create({
        data: {${dataContent}}
      })
    }`
    }
  )
  
  // Add skip logic for existing relationships
  content = content.replace(
    /await prisma\.userRole\.create\({[\s\S]*?data: \{([\s\S]*?)\}[\s\S]*?\}\)/g,
    (match, dataContent) => {
      return `const existingUserRole = await prisma.userRole.findFirst({
        where: { userId: user.id, roleId: roles[3].id }
      })
      if (!existingUserRole) {
        await prisma.userRole.create({
          data: {${dataContent}}
        })
      }`
    }
  )
  
  // Skip existing product variants, inventory items, etc.
  content = content.replace(
    /await prisma\.productVariant\.create\({[\s\S]*?data: \{([\s\S]*?)\}[\s\S]*?\}\)/g,
    (match, dataContent) => {
      return `const existingVariant = await prisma.productVariant.findFirst({
        where: { sku: variantInfo.sku }
      })
      if (!existingVariant) {
        const variant = await prisma.productVariant.create({
          data: {${dataContent}}
        })`
    }
  )
  
  content = content.replace(
    /await prisma\.inventoryItem\.create\({[\s\S]*?data: \{([\s\S]*?)\}[\s\S]*?\}\)/g,
    (match, dataContent) => {
      return `const existingInventory = await prisma.inventoryItem.findFirst({
        where: { sku: variantInfo.sku }
      })
      if (!existingInventory) {
        await prisma.inventoryItem.create({
          data: {${dataContent}}
        })
      }`
    }
  )
  
  // Skip existing orders
  content = content.replace(
    /const order = await prisma\.order\.create\({[\s\S]*?data: \{([\s\S]*?)\}[\s\S]*?\}\)/g,
    (match, dataContent) => {
      return `const existingOrder = await prisma.order.findFirst({
        where: { orderNumber: \`ORD-\${Date.now()}-\${i.toString().padStart(3, '0')}\` }
      })
      if (!existingOrder) {
        const order = await prisma.order.create({
          data: {${dataContent}}
        })
        sampleOrders.push(order)
      }`
    }
  )
  
  // Write the fixed content back
  fs.writeFileSync(SEEDER_PATH, content)
  
  console.log('✅ Seeder has been fixed to use upsert operations!')
  console.log('🔄 The seeder can now be run multiple times safely.')
  console.log('📊 It will skip existing data and only create new records.')
}

if (require.main === module) {
  try {
    fixSeeder()
    console.log('🎉 Seeder fix completed successfully!')
  } catch (error) {
    console.error('❌ Error fixing seeder:', error)
    process.exit(1)
  }
}

export { fixSeeder }
