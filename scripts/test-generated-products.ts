#!/usr/bin/env tsx

/**
 * Test Generated Products
 * 
 * This script tests the generated products to ensure they work correctly
 * and displays useful statistics about the product catalog.
 */

import { 
  generatedProducts, 
  getGeneratedProductStats,
  getGeneratedNewProducts,
  getGeneratedFeaturedProducts,
  getGeneratedSaleProducts,
  getGeneratedClothingProducts,
  searchGeneratedProducts
} from '../lib/seeders/data/generated-products'

function testGeneratedProducts() {
  console.log('🧪 Testing Generated Products...\n')
  
  // Basic tests
  console.log('📊 Basic Statistics:')
  console.log('=' .repeat(40))
  
  const stats = getGeneratedProductStats()
  console.log(`Total Products: ${stats.total}`)
  console.log(`Categories: ${stats.categories}`)
  console.log(`Subcategories: ${stats.subcategories}`)
  console.log(`Price Range: R${stats.priceRange.min} - R${stats.priceRange.max}`)
  console.log(`Average Price: R${stats.averagePrice.toFixed(2)}`)
  console.log(`Total Stock: ${stats.totalStock} items`)
  
  console.log('\n🏷️ Product Flags:')
  console.log(`New Products: ${stats.newProducts}`)
  console.log(`Featured Products: ${stats.featuredProducts}`)
  console.log(`Sale Products: ${stats.saleProducts}`)
  
  console.log('\n📦 By Category:')
  console.log(`Clothing: ${stats.byCategory.clothing}`)
  console.log(`Footwear: ${stats.byCategory.footwear}`)
  console.log(`Accessories: ${stats.byCategory.accessories}`)
  
  console.log('\n👥 By Gender:')
  console.log(`Boys: ${stats.byGender.boys}`)
  console.log(`Girls: ${stats.byGender.girls}`)
  console.log(`Unisex: ${stats.byGender.unisex}`)
  
  console.log('\n🌍 By Season:')
  console.log(`Spring: ${stats.bySeason.spring}`)
  console.log(`Summer: ${stats.bySeason.summer}`)
  console.log(`Autumn: ${stats.bySeason.autumn}`)
  console.log(`Winter: ${stats.bySeason.winter}`)
  console.log(`All Season: ${stats.bySeason.allSeason}`)
  
  // Test helper functions
  console.log('\n🔍 Testing Helper Functions:')
  console.log('=' .repeat(40))
  
  const newProducts = getGeneratedNewProducts()
  console.log(`✅ New Products Function: ${newProducts.length} products`)
  
  const featuredProducts = getGeneratedFeaturedProducts()
  console.log(`✅ Featured Products Function: ${featuredProducts.length} products`)
  
  const saleProducts = getGeneratedSaleProducts()
  console.log(`✅ Sale Products Function: ${saleProducts.length} products`)
  
  const clothingProducts = getGeneratedClothingProducts()
  console.log(`✅ Clothing Products Function: ${clothingProducts.length} products`)
  
  // Test search functionality
  const searchResults = searchGeneratedProducts('playful')
  console.log(`✅ Search Function: ${searchResults.length} results for "playful"`)
  
  // Display sample products
  console.log('\n🛍️ Sample Products:')
  console.log('=' .repeat(40))
  
  generatedProducts.slice(0, 3).forEach((product, index) => {
    console.log(`\n${index + 1}. ${product.name}`)
    console.log(`   ID: ${product.id}`)
    console.log(`   Price: R${product.price}`)
    console.log(`   Category: ${product.category} > ${product.subcategory}`)
    console.log(`   Gender: ${product.gender} | Age: ${product.ageGroup} | Season: ${product.season}`)
    console.log(`   Stock: ${product.stock} items`)
    console.log(`   Variants: ${product.variants.length} (${product.sizes.length} sizes, ${product.colors.length} colors)`)
    console.log(`   Tags: ${product.tags.slice(0, 3).join(', ')}${product.tags.length > 3 ? '...' : ''}`)
    console.log(`   New: ${product.isNew ? '✅' : '❌'} | Featured: ${product.isFeatured ? '✅' : '❌'} | Sale: ${product.isOnSale ? '✅' : '❌'}`)
  })
  
  // Validate data integrity
  console.log('\n🔍 Data Integrity Checks:')
  console.log('=' .repeat(40))
  
  let issues = 0
  
  generatedProducts.forEach(product => {
    // Check required fields
    if (!product.id || !product.name || !product.slug) {
      console.log(`❌ Missing required fields: ${product.id || 'NO_ID'}`)
      issues++
    }
    
    // Check price validity
    if (product.price <= 0) {
      console.log(`❌ Invalid price: ${product.name} - R${product.price}`)
      issues++
    }
    
    // Check stock consistency
    const calculatedStock = product.variants.reduce((sum, v) => sum + v.stock, 0)
    if (calculatedStock !== product.stock) {
      console.log(`❌ Stock mismatch: ${product.name} - Expected: ${product.stock}, Calculated: ${calculatedStock}`)
      issues++
    }
    
    // Check variants have valid data
    product.variants.forEach(variant => {
      if (!variant.size || !variant.color || !variant.sku) {
        console.log(`❌ Invalid variant: ${product.name} - ${variant.id}`)
        issues++
      }
    })
    
    // Check image paths
    if (!product.images || product.images.length === 0) {
      console.log(`❌ No images: ${product.name}`)
      issues++
    }
  })
  
  if (issues === 0) {
    console.log('✅ All data integrity checks passed!')
  } else {
    console.log(`❌ Found ${issues} data integrity issues`)
  }
  
  // Summary
  console.log('\n🎉 Test Summary:')
  console.log('=' .repeat(40))
  console.log(`✅ Generated ${generatedProducts.length} products successfully`)
  console.log(`✅ All helper functions working correctly`)
  console.log(`✅ Search functionality operational`)
  console.log(`${issues === 0 ? '✅' : '❌'} Data integrity: ${issues === 0 ? 'PASSED' : `${issues} issues found`}`)
  
  console.log('\n🚀 Products are ready for use in the application!')
}

// Run the tests
testGeneratedProducts()
