# Prisma Import Standardization Scripts

This directory contains scripts to automatically standardize all Prisma imports across the codebase to use the centralized `@/lib/prisma` library.

## 🎯 Purpose

The scripts solve the problem of inconsistent Prisma imports throughout the codebase by:

- ✅ Replacing all `@/lib/ecommerce/config/database` imports with `@/lib/prisma`
- ✅ Converting `db` references to `prisma`
- ✅ Updating `getPrismaClient()` calls to direct `prisma` usage
- ✅ Handling complex import statements with multiple imports
- ✅ Creating backups before making changes
- ✅ Providing detailed progress reporting

## 📁 Available Scripts

### 1. `fix-prisma-imports.sh` (Recommended)
**Bash script with comprehensive error handling and user interaction**

```bash
# Run the script
./scripts/fix-prisma-imports.sh

# Or make it executable first
chmod +x scripts/fix-prisma-imports.sh
./scripts/fix-prisma-imports.sh
```

**Features:**
- ✅ Interactive prompts
- ✅ Automatic backups with timestamps
- ✅ Colored output for better readability
- ✅ Optional build verification
- ✅ Comprehensive error handling
- ✅ Progress reporting

### 2. `fix-prisma-imports.js`
**Node.js script for programmatic usage**

```bash
# Run with Node.js
node scripts/fix-prisma-imports.js

# Or with npm/pnpm
npm run fix-prisma-imports
pnpm fix-prisma-imports
```

### 3. `fix-prisma-imports.ts`
**TypeScript version with enhanced type safety**

```bash
# Run with tsx (if installed)
npx tsx scripts/fix-prisma-imports.ts

# Or compile and run
npx tsc scripts/fix-prisma-imports.ts
node scripts/fix-prisma-imports.js
```

## 🔧 What Gets Updated

### Import Statements
```typescript
// Before
import { prisma } from '@/lib/ecommerce/config/database'
import { db } from '@/lib/ecommerce/config/database'
import { getPrismaClient } from '@/lib/ecommerce/config/database'

// After
import { prisma } from '@/lib/prisma'
import { prisma } from '@/lib/prisma'
import { prisma } from '@/lib/prisma'
```

### Usage Patterns
```typescript
// Before
const client = getPrismaClient()
await db.user.findMany()
const { db, withTransaction } = require('@/lib/ecommerce/config/database')

// After
const client = prisma
await prisma.user.findMany()
const { prisma, withTransaction } = require('@/lib/prisma')
```

### Complex Imports
```typescript
// Before
import { prisma, db, withTransaction, connectDatabase } from '@/lib/ecommerce/config/database'

// After
import { prisma, withTransaction, connectDatabase } from '@/lib/prisma'
```

## 📂 Files Processed

The scripts automatically scan and update files in:

- `app/` - Next.js app directory
- `lib/` - Library files
- `components/` - React components
- `hooks/` - Custom React hooks
- `scripts/` - Utility scripts

### Specific Files Updated

**E-commerce Services:**
- `lib/ecommerce/services/newsletter-service.ts`
- `lib/ecommerce/services/payment-service.ts`
- `lib/ecommerce/services/inventory-service.ts`
- `lib/ecommerce/services/coupon-service.ts`
- `lib/ecommerce/services/order-service.ts`
- `lib/ecommerce/services/analytics-service.ts`
- `lib/ecommerce/services/product-variant-service.ts`
- `lib/ecommerce/services/shipping-service.ts`
- `lib/ecommerce/services/email-service.ts`

**Routing & Site Settings:**
- `lib/routing/route-resolver.ts`
- `lib/site-settings/site-settings-service.ts`

**API Routes:**
- `app/api/admin/site-settings/route.ts`
- `app/api/admin/site-settings/homepage/route.ts`
- `app/api/site-settings/homepage/route.ts`
- `app/api/pages/check-slug/route.ts`

**Page Components:**
- `app/dynamic-page/[slug]/page.tsx`
- `app/[...slug]/page.tsx`

## 🛡️ Safety Features

### Automatic Backups
```bash
# Backups are created with timestamps
backup_20241201_143022/
├── newsletter-service.ts.backup
├── payment-service.ts.backup
├── route-resolver.ts.backup
└── ...
```

### Dry Run Mode (JavaScript/TypeScript versions)
```javascript
// Add this flag to test without making changes
const DRY_RUN = true
```

### Rollback Instructions
```bash
# If something goes wrong, restore from backups
cp backup_*/filename.backup original/path/filename

# Or restore all files
for file in backup_*/*.backup; do
  original_path=$(echo $file | sed 's|backup_[0-9_]*||' | sed 's|\.backup||')
  cp "$file" "$original_path"
done
```

## 📊 Output Example

```bash
🔧 Starting Prisma Import Standardization...

📁 Scanning directories: app, lib, components, hooks, scripts
🔍 Looking for file extensions: .ts, .tsx, .js, .jsx
❌ Excluding: lib/prisma.ts, lib/ecommerce/config/database.ts, node_modules, .next, .git

📄 Found 45 files to scan

✅ Updated lib/ecommerce/services/newsletter-service.ts
✅ Updated lib/ecommerce/services/payment-service.ts
✅ Updated lib/routing/route-resolver.ts
⏭️  Skipped app/page.tsx (no changes needed)

============================================================
📊 PROCESSING SUMMARY
============================================================
📄 Total files scanned: 45
✅ Files updated: 12
❌ Files with errors: 0
⏭️  Files skipped (no changes needed): 33

📝 Updated files:
   - lib/ecommerce/services/newsletter-service.ts
   - lib/ecommerce/services/payment-service.ts
   - lib/routing/route-resolver.ts
   - ...

🎉 Prisma import standardization complete!

📋 Next steps:
   1. Run: pnpm build
   2. Test your application
   3. Run: npx prisma generate (if needed)
   4. Commit your changes
```

## 🚀 Quick Start

### Option 1: Run the Bash Script (Recommended)
```bash
# Make executable and run
chmod +x scripts/fix-prisma-imports.sh
./scripts/fix-prisma-imports.sh
```

### Option 2: Add to package.json
```json
{
  "scripts": {
    "fix-prisma-imports": "node scripts/fix-prisma-imports.js",
    "fix-prisma-imports:ts": "npx tsx scripts/fix-prisma-imports.ts"
  }
}
```

Then run:
```bash
pnpm fix-prisma-imports
```

## 🔍 Verification

After running the script:

1. **Build the project:**
   ```bash
   pnpm build
   ```

2. **Check for any remaining old imports:**
   ```bash
   grep -r "@/lib/ecommerce/config/database" app/ lib/ components/ hooks/
   ```

3. **Test key functionality:**
   ```bash
   # Test database connection
   pnpm dev
   # Navigate to pages that use database
   ```

4. **Run Prisma commands:**
   ```bash
   npx prisma generate
   npx prisma db push
   ```

## 🐛 Troubleshooting

### Common Issues

**1. Permission Denied**
```bash
chmod +x scripts/fix-prisma-imports.sh
```

**2. Script Not Found**
```bash
# Make sure you're in the project root
ls scripts/fix-prisma-imports.sh
```

**3. Build Errors After Running**
```bash
# Check for syntax errors in updated files
pnpm build

# Restore from backup if needed
cp backup_*/problematic-file.backup path/to/problematic-file
```

**4. TypeScript Errors**
```bash
# Regenerate Prisma client
npx prisma generate

# Check TypeScript
npx tsc --noEmit
```

### Manual Fixes

If the script misses any files, manually update them:

```typescript
// Find remaining old imports
grep -r "@/lib/ecommerce/config/database" .

// Replace manually
sed -i 's|@/lib/ecommerce/config/database|@/lib/prisma|g' filename.ts
```

## 📋 Post-Script Checklist

- [ ] All files build successfully
- [ ] Database connections work
- [ ] API routes respond correctly
- [ ] Page rendering works
- [ ] No TypeScript errors
- [ ] Tests pass (if applicable)
- [ ] Commit changes to version control
- [ ] Remove backup directory (optional)

## 🎯 Benefits

After running these scripts, you'll have:

- ✅ **Consistent imports** across the entire codebase
- ✅ **Single source of truth** for Prisma configuration
- ✅ **Better maintainability** with centralized database setup
- ✅ **Improved developer experience** with unified imports
- ✅ **Easier debugging** with consistent patterns
- ✅ **Future-proof architecture** for scaling

The centralized `@/lib/prisma` library provides better performance monitoring, connection management, and development utilities while maintaining backward compatibility.
