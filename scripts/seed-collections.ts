// Script to seed default collections
// Run with: npx tsx scripts/seed-collections.ts

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

const defaultCollections = [
  {
    title: 'Featured Products',
    slug: 'featured-products',
    description: 'Our handpicked selection of the best products',
    sortOrder: 'manual',
    isVisible: true,
    seoTitle: 'Featured Products - Best Sellers',
    seoDescription: 'Discover our most popular and highly-rated products'
  },
  {
    title: 'New Arrivals',
    slug: 'new-arrivals',
    description: 'Latest products added to our store',
    sortOrder: 'created',
    isVisible: true,
    seoTitle: 'New Arrivals - Latest Products',
    seoDescription: 'Check out the newest additions to our product catalog'
  },
  {
    title: 'Best Sellers',
    slug: 'best-sellers',
    description: 'Our most popular products',
    sortOrder: 'best-selling',
    isVisible: true,
    seoTitle: 'Best Sellers - Most Popular Products',
    seoDescription: 'Shop our best-selling and most popular items'
  },
  {
    title: 'Sale Items',
    slug: 'sale-items',
    description: 'Products on sale with special discounts',
    sortOrder: 'price-asc',
    isVisible: true,
    seoTitle: 'Sale Items - Special Offers',
    seoDescription: 'Great deals and discounts on selected products'
  },
  {
    title: 'Premium Collection',
    slug: 'premium-collection',
    description: 'High-quality premium products',
    sortOrder: 'price-desc',
    isVisible: true,
    seoTitle: 'Premium Collection - Luxury Products',
    seoDescription: 'Explore our premium and luxury product range'
  }
]

async function seedCollections() {
  console.log('🌱 Seeding collections...')

  try {
    for (const collection of defaultCollections) {
      // Check if collection already exists
      const existing = await prisma.productCollection.findUnique({
        where: { slug: collection.slug }
      })

      if (existing) {
        console.log(`⏭️  Collection "${collection.title}" already exists, skipping...`)
        continue
      }

      // Create collection
      const created = await prisma.productCollection.create({
        data: collection
      })

      console.log(`✅ Created collection: ${created.title}`)
    }

    console.log('🎉 Collections seeding completed!')
  } catch (error) {
    console.error('❌ Error seeding collections:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seeding function
if (require.main === module) {
  seedCollections()
    .then(() => {
      console.log('✨ Seeding finished successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Seeding failed:', error)
      process.exit(1)
    })
}

export { seedCollections }