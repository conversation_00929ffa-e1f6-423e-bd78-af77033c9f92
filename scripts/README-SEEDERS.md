# Database Seeders Documentation

## Overview

This document describes the comprehensive database seeders for the Coco Milk Kids e-commerce platform. The seeders populate the database with realistic, production-ready sample data that respects all foreign key constraints and relationships.

## Main Seeder: `seed-database.ts`

### Purpose
Creates a complete, realistic dataset for development, testing, and demo purposes with South African context and ZAR currency.

### What Gets Seeded

#### 1. **User Management System**
- **Permissions**: 8 core permissions (create/edit/delete products, manage orders, users, pages, analytics)
- **Roles**: 4 user roles (Super Admin, Admin, Manager, Customer) with hierarchical permissions
- **Admin System**: 3 admin roles and 2 admin users with secure password hashing
- **Users**: 5 sample users with realistic South African names, addresses, and purchase history

#### 2. **Inventory Management**
- **Locations**: 3 inventory locations (Cape Town warehouse, Johannesburg DC, Durban store)
- **Inventory Items**: Complete inventory tracking for all product variants

#### 3. **Product Catalog**
- **Categories**: Hierarchical category structure with main categories and subcategories
  - Boys Clothing (T-Shirts, Shorts, Jeans)
  - Girls Clothing (Dresses, Tops, Skirts)
  - Baby Clothing (Onesies, Sets)
  - School Uniforms
  - Accessories
- **Collections**: 7 themed collections (New Arrivals, Best Sellers, Summer, Heritage Day, etc.)
- **Tags**: 10 product tags (Organic, Cotton, Comfortable, Durable, etc.)
- **Attributes**: 5 product attributes (Size, Color, Material, Care Instructions, Season)

#### 4. **Products with South African Theme**
- **Safari Adventure Boys T-Shirt**: Organic cotton with wildlife prints
- **Springbok Rugby Shorts**: Heritage-inspired sports wear
- **Protea Princess Dress**: Featuring South Africa's national flower
- **Table Mountain Sunset Top**: Cape Town-inspired artistic design
- **Little Lion Onesie Set**: Adorable baby clothing with African theme

Each product includes:
- Multiple variants (size/color combinations)
- Realistic pricing in ZAR
- SEO-optimized descriptions
- Product images
- Inventory tracking
- Attribute values

#### 5. **E-commerce Data**
- **Orders**: 8 sample orders with realistic order items, pricing, and tax calculations
- **Coupons**: 3 active coupons (Welcome discount, Summer sale, Free shipping)
- **Site Settings**: Complete site configuration with South African context

#### 6. **Marketing & Communication**
- **Newsletter Subscribers**: 25 sample subscribers with preferences

### South African Context

The seeder includes authentic South African elements:
- **Cities**: Cape Town, Johannesburg, Durban, Pretoria, etc.
- **Provinces**: All 9 South African provinces
- **Postal Codes**: Realistic South African postal codes
- **Currency**: ZAR (South African Rand) throughout
- **Tax**: 15% VAT calculation
- **Cultural Elements**: Springbok, Protea, Table Mountain, Safari themes
- **Names**: Realistic South African names (Thabo, Nomsa, Pieter, Fatima, etc.)

### Technical Features

#### Data Integrity
- **Foreign Key Compliance**: All relationships properly maintained
- **Dependency Order**: Seeding follows correct dependency chain
- **Referential Integrity**: No orphaned records
- **Unique Constraints**: All unique fields properly handled

#### Production-Ready Data
- **No Placeholders**: All data is realistic and meaningful
- **Proper Validation**: Data respects all model validations
- **SEO Optimized**: Products include proper SEO fields
- **Complete Records**: All required fields populated

#### Performance Optimized
- **Batch Operations**: Uses Promise.all for parallel creation where possible
- **Efficient Queries**: Minimizes database round trips
- **Clean Slate**: Comprehensive cleanup before seeding

## Usage

### Running the Seeder

```bash
# Run the main comprehensive seeder
pnpm seed

# Or run directly with tsx
tsx scripts/seed-database.ts
```

### Environment Requirements

- **Development Only**: Automatic cleanup only runs in non-production environments
- **Database**: PostgreSQL database configured
- **Dependencies**: @faker-js/faker, bcryptjs, @prisma/client

### Safety Features

- **Environment Check**: Cleanup only runs when `NODE_ENV !== 'production'`
- **Error Handling**: Comprehensive error catching and reporting
- **Transaction Safety**: Proper cleanup on failure
- **Connection Management**: Automatic Prisma client disconnection

## Data Relationships

### User Hierarchy
```
AdminUsers -> AdminRoles -> Permissions
Users -> UserRoles -> Roles -> Permissions
Users -> UserAddresses
```

### Product Structure
```
Products -> ProductCategories (hierarchical)
Products -> ProductCollections
Products -> ProductTags
Products -> ProductAttributes -> ProductAttributeValues
Products -> ProductVariants -> ProductVariantOptions
Products -> ProductImages
Products -> InventoryItems -> InventoryLocations
```

### Order Flow
```
Users -> Orders -> OrderItems -> Products/Variants
Orders -> Payments
Orders -> Coupons -> CouponUsage
```

## Customization

### Adding More Products
1. Extend the `productData` array in `seed-database.ts`
2. Follow the existing structure with South African themes
3. Ensure proper category/collection assignments
4. Include realistic variants and inventory

### Modifying Categories
1. Update the category creation sections
2. Maintain hierarchical relationships
3. Update product assignments accordingly

### Changing Locations
1. Modify the `inventoryLocations` array
2. Update South African city/province data
3. Ensure inventory items reference correct locations

## Troubleshooting

### Common Issues
1. **Foreign Key Errors**: Check dependency order in cleanup and creation
2. **Unique Constraint Violations**: Ensure unique fields (emails, slugs) are unique
3. **Missing Required Fields**: Verify all required model fields are populated

### Debugging
- Enable Prisma query logging
- Check console output for detailed progress
- Verify database state after partial runs

## Future Enhancements

### Planned Additions
- Page Builder content seeding
- Blog posts and categories
- Product reviews and ratings
- Workflow system data
- Theme configurations
- Layout builder templates

### Extensibility
The seeder is designed to be easily extended with additional models and relationships while maintaining data integrity and South African context.
