#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client'
import { faker } from '@faker-js/faker'

const prisma = new PrismaClient()

async function addSampleProducts() {
  console.log('👕 Adding sample products for dashboard testing...')

  try {
    // Get existing category and collection
    const category = await prisma.productCategory.findFirst({
      where: { slug: 'kids-clothing' }
    })
    
    const collection = await prisma.productCollection.findFirst({
      where: { slug: 'featured' }
    })
    
    const warehouse = await prisma.inventoryLocation.findFirst({
      where: { code: 'WH-CPT-001' }
    })

    if (!category || !collection || !warehouse) {
      console.error('❌ Required data not found. Please run minimal seeder first.')
      return
    }

    // Sample products data
    const productsData = [
      {
        title: 'Safari Adventure Boys T-Shirt',
        description: 'Comfortable 100% organic cotton t-shirt featuring South African wildlife prints.',
        price: 189.99,
        compareAtPrice: 229.99,
        costPerItem: 95.00,
        sku: 'SAF-BOY-TSH-001',
        inventory: 50
      },
      {
        title: 'Protea Princess Dress',
        description: 'Elegant dress featuring South Africa\'s national flower, the Protea.',
        price: 349.99,
        compareAtPrice: 429.99,
        costPerItem: 175.00,
        sku: 'PRO-GIR-DRE-001',
        inventory: 30
      },
      {
        title: 'Little Lion Onesie Set',
        description: 'Adorable 3-piece onesie set featuring cute lion designs.',
        price: 299.99,
        compareAtPrice: 359.99,
        costPerItem: 150.00,
        sku: 'LIO-BAB-ONE-001',
        inventory: 25
      },
      {
        title: 'Springbok Rugby Shorts',
        description: 'Durable rugby-style shorts inspired by the Springboks.',
        price: 249.99,
        compareAtPrice: 299.99,
        costPerItem: 125.00,
        sku: 'SPR-BOY-SHO-001',
        inventory: 40
      },
      {
        title: 'Table Mountain Sunset Top',
        description: 'Beautiful girls\' top featuring an artistic print of Cape Town\'s iconic Table Mountain.',
        price: 199.99,
        compareAtPrice: 249.99,
        costPerItem: 100.00,
        sku: 'TBL-GIR-TOP-001',
        inventory: 35
      }
    ]

    const products = []

    for (const productData of productsData) {
      // Check if product already exists
      const existingProduct = await prisma.product.findFirst({
        where: { slug: productData.title.toLowerCase().replace(/\s+/g, '-').replace(/'/g, '') }
      })

      if (existingProduct) {
        console.log(`⏭️  Product "${productData.title}" already exists, skipping...`)
        products.push(existingProduct)
        continue
      }

      const product = await prisma.product.create({
        data: {
          title: productData.title,
          slug: productData.title.toLowerCase().replace(/\s+/g, '-').replace(/'/g, ''),
          description: productData.description,
          vendor: 'Coco Milk Kids',
          productType: 'Clothing',
          handle: productData.title.toLowerCase().replace(/\s+/g, '-').replace(/'/g, ''),
          status: 'active',
          publishedAt: new Date(),
          price: productData.price,
          compareAtPrice: productData.compareAtPrice,
          costPerItem: productData.costPerItem,
          currency: 'ZAR',
          trackQuantity: true,
          inventoryQuantity: productData.inventory,
          weight: 0.2,
          weightUnit: 'kg',
          seoTitle: `${productData.title} | Coco Milk Kids`,
          seoDescription: productData.description.substring(0, 160),
          isVisible: true,
          isAvailable: true,
          availableForSale: true,
          images: {
            create: [
              {
                url: `/images/products/${productData.sku.toLowerCase()}-front.jpg`,
                altText: `${productData.title} - Front View`,
                position: 1
              },
              {
                url: `/images/products/${productData.sku.toLowerCase()}-back.jpg`,
                altText: `${productData.title} - Back View`,
                position: 2
              }
            ]
          },
          categories: {
            create: {
              categoryId: category.id
            }
          },
          collections: {
            create: {
              collectionId: collection.id,
              position: products.length
            }
          }
        }
      })

      // Create inventory item
      await prisma.inventoryItem.create({
        data: {
          productId: product.id,
          sku: productData.sku,
          name: product.title,
          quantity: productData.inventory,
          availableQuantity: productData.inventory,
          costPrice: productData.costPerItem,
          averageCost: productData.costPerItem,
          currency: 'ZAR',
          locationId: warehouse.id,
          trackQuantity: true,
          lowStockThreshold: 5,
          reorderPoint: 10,
          reorderQuantity: 25
        }
      })

      products.push(product)
      console.log(`✅ Created product: ${product.title}`)
    }

    // Create some sample users
    console.log('👥 Creating sample users...')
    const users = []
    
    for (let i = 0; i < 3; i++) {
      const email = faker.internet.email()
      const existingUser = await prisma.user.findFirst({ where: { email } })
      
      if (!existingUser) {
        const user = await prisma.user.create({
          data: {
            email,
            firstName: faker.person.firstName(),
            lastName: faker.person.lastName(),
            displayName: faker.person.fullName(),
            phone: `+27 ${faker.string.numeric(2)} ${faker.string.numeric(3)} ${faker.string.numeric(4)}`,
            emailVerified: true,
            acceptsMarketing: faker.datatype.boolean(),
            preferredCurrency: 'ZAR',
            customerSince: faker.date.past({ years: 2 }),
            totalSpent: faker.number.float({ min: 100, max: 2000, fractionDigits: 2 }),
            orderCount: faker.number.int({ min: 1, max: 10 }),
            loyaltyPoints: faker.number.int({ min: 10, max: 500 }),
            loyaltyTier: faker.helpers.arrayElement(['Bronze', 'Silver', 'Gold']),
            isActive: true
          }
        })
        users.push(user)
        console.log(`✅ Created user: ${user.email}`)
      }
    }

    // Create some sample orders
    console.log('🛒 Creating sample orders...')
    
    for (let i = 0; i < 5; i++) {
      const user = users[faker.number.int({ min: 0, max: users.length - 1 })]
      const orderProducts = faker.helpers.arrayElements(products, { min: 1, max: 3 })
      
      let subtotal = 0
      const orderItems = []
      
      for (const product of orderProducts) {
        const quantity = faker.number.int({ min: 1, max: 3 })
        const unitPrice = Number(product.price)
        const totalPrice = unitPrice * quantity
        subtotal += totalPrice
        
        orderItems.push({
          productId: product.id,
          quantity,
          unitPrice,
          totalPrice,
          currency: 'ZAR',
          productTitle: product.title,
          productSlug: product.slug,
          fulfillableQuantity: quantity,
          returnableQuantity: quantity,
          refundableQuantity: quantity
        })
      }
      
      const totalTax = subtotal * 0.15 // 15% VAT
      const totalShipping = subtotal > 500 ? 0 : 65 // Free shipping over R500
      const total = subtotal + totalTax + totalShipping
      
      const orderNumber = `ORD-${Date.now()}-${i.toString().padStart(3, '0')}`
      
      const existingOrder = await prisma.order.findFirst({
        where: { orderNumber }
      })
      
      if (!existingOrder && user) {
        const order = await prisma.order.create({
          data: {
            orderNumber,
            userId: user.id,
            customerEmail: user.email!,
            customerFirstName: user.firstName!,
            customerLastName: user.lastName!,
            customerPhone: user.phone!,
            billingAddress: {
              firstName: user.firstName!,
              lastName: user.lastName!,
              address1: faker.location.streetAddress(),
              city: 'Cape Town',
              province: 'Western Cape',
              country: 'South Africa',
              postalCode: '8001',
              phone: user.phone!
            },
            shippingAddress: {
              firstName: user.firstName!,
              lastName: user.lastName!,
              address1: faker.location.streetAddress(),
              city: 'Cape Town',
              province: 'Western Cape',
              country: 'South Africa',
              postalCode: '8001',
              phone: user.phone!
            },
            itemCount: orderItems.length,
            subtotal,
            totalTax,
            totalShipping,
            total,
            currency: 'ZAR',
            paymentStatus: faker.helpers.arrayElement(['paid', 'pending', 'authorized']),
            fulfillmentStatus: faker.helpers.arrayElement(['unfulfilled', 'fulfilled', 'shipped']),
            status: faker.helpers.arrayElement(['confirmed', 'processing', 'shipped', 'delivered']),
            financialStatus: faker.helpers.arrayElement(['paid', 'pending', 'authorized']),
            confirmedAt: faker.date.recent({ days: 30 }),
            source: 'web',
            items: {
              create: orderItems
            }
          }
        })
        console.log(`✅ Created order: ${order.orderNumber}`)
      }
    }

    console.log('✅ Sample products and data added successfully!')
    console.log(`Created:`)
    console.log(`- ${products.length} sample products`)
    console.log(`- ${users.length} sample users`)
    console.log(`- Up to 5 sample orders`)
    console.log('')
    console.log('🎯 You can now test all dashboard features:')
    console.log('   • Product management')
    console.log('   • Inventory tracking')
    console.log('   • Order management')
    console.log('   • Customer management')
    console.log('   • Analytics and reporting')

  } catch (error) {
    console.error('❌ Error adding sample products:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
if (require.main === module) {
  addSampleProducts()
    .then(() => {
      console.log('🎉 Sample products added successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Failed to add sample products:', error)
      process.exit(1)
    })
}

export { addSampleProducts }
