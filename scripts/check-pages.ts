#!/usr/bin/env tsx

import { prisma } from '@/lib/prisma'

/**
 * <PERSON><PERSON><PERSON> to check existing pages and their status
 */
async function main() {
  console.log('🔍 Checking existing pages...')
  
  try {
    const pages = await prisma.page.findMany({
      select: {
        id: true,
        title: true,
        slug: true,
        status: true,
        type: true,
        isHomePage: true,
        _count: {
          select: {
            blocks: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    console.log(`\n📄 Found ${pages.length} pages:`)
    pages.forEach((page, index) => {
      console.log(`${index + 1}. ${page.title}`)
      console.log(`   Slug: ${page.slug}`)
      console.log(`   Status: ${page.status}`)
      console.log(`   Type: ${page.type}`)
      console.log(`   Is Homepage: ${page.isHomePage}`)
      console.log(`   Blocks: ${page._count.blocks}`)
      console.log(`   ID: ${page.id}`)
      console.log('')
    })

    // Check site settings
    const siteSettings = await prisma.siteSettings.findFirst()
    console.log('🏠 Site Settings:')
    console.log(`   Homepage ID: ${siteSettings?.homepageId || 'Not set'}`)
    
    if (siteSettings?.homepageId) {
      const homepage = await prisma.page.findUnique({
        where: { id: siteSettings.homepageId },
        select: { title: true, slug: true, status: true }
      })
      console.log(`   Homepage: ${homepage?.title} (${homepage?.slug}) - ${homepage?.status}`)
    }

  } catch (error) {
    console.error('❌ Error checking pages:', error)
    process.exit(1)
  }
}

// Run the script
main()
