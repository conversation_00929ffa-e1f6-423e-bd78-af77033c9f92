#!/usr/bin/env tsx

/**
 * Comprehensive Database Seeder Runner
 * 
 * This script runs the comprehensive database seeder with proper error handling,
 * logging, and environment checks.
 */

import { seedDatabase } from './seed-database'

async function runSeeder() {
  console.log('🚀 Starting Comprehensive Database Seeder...')
  console.log('=' .repeat(60))
  
  // Environment check
  if (process.env.NODE_ENV === 'production') {
    console.warn('⚠️  WARNING: Running in production environment!')
    console.warn('⚠️  Data cleanup will be skipped for safety.')
  }
  
  // Database URL check
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is not set!')
    console.error('Please configure your database connection string.')
    process.exit(1)
  }
  
  const startTime = Date.now()
  
  try {
    await seedDatabase()
    
    const endTime = Date.now()
    const duration = ((endTime - startTime) / 1000).toFixed(2)
    
    console.log('=' .repeat(60))
    console.log(`🎉 Seeding completed successfully in ${duration} seconds!`)
    console.log('')
    console.log('📊 Database is now populated with:')
    console.log('   • User management system with roles and permissions')
    console.log('   • Admin users and roles')
    console.log('   • Inventory locations and management')
    console.log('   • Product catalog with South African themes')
    console.log('   • Sample orders and e-commerce data')
    console.log('   • Coupons and marketing data')
    console.log('   • Site configuration')
    console.log('')
    console.log('🔗 You can now:')
    console.log('   • Start the development server: pnpm dev')
    console.log('   • View the database: pnpm db:studio')
    console.log('   • Login as admin: <EMAIL> / admin123!')
    console.log('   • Login as manager: <EMAIL> / manager123!')
    console.log('')
    
  } catch (error) {
    const endTime = Date.now()
    const duration = ((endTime - startTime) / 1000).toFixed(2)
    
    console.log('=' .repeat(60))
    console.error(`💥 Seeding failed after ${duration} seconds!`)
    console.error('')
    
    if (error instanceof Error) {
      console.error('Error Details:')
      console.error(`  Message: ${error.message}`)
      if (error.stack) {
        console.error(`  Stack: ${error.stack}`)
      }
    } else {
      console.error('Unknown error:', error)
    }
    
    console.error('')
    console.error('🔧 Troubleshooting:')
    console.error('   • Check your database connection')
    console.error('   • Ensure Prisma schema is up to date: pnpm db:push')
    console.error('   • Verify all dependencies are installed: pnpm install')
    console.error('   • Check the logs above for specific error details')
    console.error('')
    
    process.exit(1)
  }
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('\n⚠️  Seeding interrupted by user')
  process.exit(1)
})

process.on('SIGTERM', () => {
  console.log('\n⚠️  Seeding terminated')
  process.exit(1)
})

// Run the seeder
runSeeder()
