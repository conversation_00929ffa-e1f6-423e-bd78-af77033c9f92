#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🧪 Testing Prisma Import Standardization...\n')

// Test files that should now use @/lib/prisma
const testFiles = [
  'lib/ecommerce/services/newsletter-service.ts',
  'lib/ecommerce/services/payment-service.ts',
  'lib/ecommerce/services/inventory-service.ts',
  'lib/ecommerce/services/coupon-service.ts',
  'lib/ecommerce/services/order-service.ts',
  'lib/routing/route-resolver.ts',
  'lib/site-settings/site-settings-service.ts',
  'app/api/admin/site-settings/route.ts',
  'app/api/admin/site-settings/homepage/route.ts',
  'app/api/site-settings/homepage/route.ts',
  'app/api/pages/check-slug/route.ts',
  'app/dynamic-page/[slug]/page.tsx',
  'app/api/e-commerce/pages/validate-slug/route.ts',
  'app/api/e-commerce/pages/[id]/route.ts',
  'app/api/e-commerce/pages/route.ts',
  'app/frontend/preview/[slug]/page.tsx'
]

let passCount = 0
let failCount = 0
const results = []

console.log('📋 Testing files for correct Prisma imports...\n')

testFiles.forEach(filePath => {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filePath}`)
    results.push({ file: filePath, status: 'not_found', issue: 'File does not exist' })
    return
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8')
    
    // Check for old imports
    const hasOldImport = content.includes("@/lib/ecommerce/config/database")
    const hasNewImport = content.includes("@/lib/prisma")
    
    if (hasOldImport) {
      console.log(`❌ ${filePath} - Still has old import`)
      results.push({ file: filePath, status: 'fail', issue: 'Contains old import' })
      failCount++
    } else if (hasNewImport) {
      console.log(`✅ ${filePath} - Using new import`)
      results.push({ file: filePath, status: 'pass', issue: null })
      passCount++
    } else {
      console.log(`ℹ️  ${filePath} - No Prisma import found`)
      results.push({ file: filePath, status: 'no_import', issue: 'No Prisma import detected' })
    }
  } catch (error) {
    console.log(`❌ ${filePath} - Error reading file: ${error.message}`)
    results.push({ file: filePath, status: 'error', issue: error.message })
    failCount++
  }
})

console.log('\n' + '='.repeat(60))
console.log('📊 TEST RESULTS SUMMARY')
console.log('='.repeat(60))
console.log(`✅ Files with correct imports: ${passCount}`)
console.log(`❌ Files with issues: ${failCount}`)
console.log(`📄 Total files tested: ${testFiles.length}`)

// Show detailed results
const failedFiles = results.filter(r => r.status === 'fail' || r.status === 'error')
if (failedFiles.length > 0) {
  console.log('\n❌ Files that need attention:')
  failedFiles.forEach(({ file, issue }) => {
    console.log(`   - ${file}: ${issue}`)
  })
}

const passedFiles = results.filter(r => r.status === 'pass')
if (passedFiles.length > 0) {
  console.log('\n✅ Files with correct imports:')
  passedFiles.forEach(({ file }) => {
    console.log(`   - ${file}`)
  })
}

// Test the centralized Prisma library
console.log('\n🔧 Testing centralized Prisma library...')

try {
  if (fs.existsSync('lib/prisma.ts')) {
    const prismaContent = fs.readFileSync('lib/prisma.ts', 'utf8')
    
    // Check for key exports
    const hasExport = prismaContent.includes('export const prisma')
    const hasUtilities = prismaContent.includes('connectDatabase') && 
                        prismaContent.includes('disconnectDatabase') &&
                        prismaContent.includes('healthCheck')
    
    if (hasExport && hasUtilities) {
      console.log('✅ Centralized Prisma library is properly configured')
    } else {
      console.log('⚠️  Centralized Prisma library may be incomplete')
    }
  } else {
    console.log('❌ Centralized Prisma library not found at lib/prisma.ts')
    failCount++
  }
} catch (error) {
  console.log(`❌ Error testing Prisma library: ${error.message}`)
  failCount++
}

// Final result
console.log('\n' + '='.repeat(60))
if (failCount === 0) {
  console.log('🎉 ALL TESTS PASSED! Prisma imports are standardized.')
  console.log('\n📋 Next steps:')
  console.log('   1. Run: pnpm build (to verify compilation)')
  console.log('   2. Test your application')
  console.log('   3. Commit your changes')
  process.exit(0)
} else {
  console.log('❌ SOME TESTS FAILED! Please fix the issues above.')
  console.log('\n🔧 To fix remaining issues:')
  console.log('   1. Manually update files with old imports')
  console.log('   2. Run this test again')
  console.log('   3. Run: pnpm build')
  process.exit(1)
}
