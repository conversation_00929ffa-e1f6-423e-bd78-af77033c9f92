import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedLayoutBuilder() {
  console.log('🎨 Seeding Layout Builder system...')

  try {
    // Create default navigation menu
    const mainMenu = await prisma.navigationMenu.upsert({
      where: { slug: 'main-menu' },
      update: {},
      create: {
        name: 'Main Menu',
        slug: 'main-menu',
        description: 'Primary site navigation',
        items: [
          {
            id: 'home',
            text: 'Home',
            url: '/',
            target: '_self',
            position: 1,
            isVisible: true,
            children: []
          },
          {
            id: 'shop',
            text: 'Shop',
            url: '/shop',
            target: '_self',
            position: 2,
            isVisible: true,
            children: [
              {
                id: 'boys',
                text: 'Boys',
                url: '/shop/boys',
                target: '_self',
                position: 1,
                isVisible: true,
                children: []
              },
              {
                id: 'girls',
                text: 'Girls',
                url: '/shop/girls',
                target: '_self',
                position: 2,
                isVisible: true,
                children: []
              },
              {
                id: 'accessories',
                text: 'Accessories',
                url: '/shop/accessories',
                target: '_self',
                position: 3,
                isVisible: true,
                children: []
              }
            ]
          },
          {
            id: 'about',
            text: 'About',
            url: '/about',
            target: '_self',
            position: 3,
            isVisible: true,
            children: []
          },
          {
            id: 'contact',
            text: 'Contact',
            url: '/contact',
            target: '_self',
            position: 4,
            isVisible: true,
            children: []
          }
        ],
        settings: {
          style: 'horizontal',
          mobileStyle: 'hamburger',
          showIcons: false,
          showArrows: true,
          maxDepth: 2
        },
        isActive: true
      }
    })

    // Create footer menu
    const footerMenu = await prisma.navigationMenu.upsert({
      where: { slug: 'footer-menu' },
      update: {},
      create: {
        name: 'Footer Menu',
        slug: 'footer-menu',
        description: 'Footer navigation links',
        items: [
          {
            id: 'privacy',
            text: 'Privacy Policy',
            url: '/privacy',
            target: '_self',
            position: 1,
            isVisible: true,
            children: []
          },
          {
            id: 'terms',
            text: 'Terms of Service',
            url: '/terms',
            target: '_self',
            position: 2,
            isVisible: true,
            children: []
          },
          {
            id: 'shipping',
            text: 'Shipping Info',
            url: '/shipping',
            target: '_self',
            position: 3,
            isVisible: true,
            children: []
          },
          {
            id: 'returns',
            text: 'Returns',
            url: '/returns',
            target: '_self',
            position: 4,
            isVisible: true,
            children: []
          }
        ],
        settings: {
          style: 'horizontal',
          mobileStyle: 'vertical',
          showIcons: false,
          showArrows: false,
          maxDepth: 1
        },
        isActive: true
      }
    })

    // Create sidebar widget area
    const sidebarWidgets = await prisma.widgetArea.upsert({
      where: { slug: 'sidebar-primary' },
      update: {},
      create: {
        name: 'Primary Sidebar',
        slug: 'sidebar-primary',
        description: 'Main sidebar widget area',
        widgets: [
          {
            id: 'recent-posts',
            type: 'recent-posts',
            title: 'Recent Posts',
            content: {
              count: 5,
              showDate: true,
              showExcerpt: false
            },
            settings: {},
            position: 1,
            isVisible: true
          },
          {
            id: 'categories',
            type: 'categories',
            title: 'Categories',
            content: {
              showCount: true,
              hierarchical: true
            },
            settings: {},
            position: 2,
            isVisible: true
          }
        ],
        settings: {
          layout: 'vertical',
          spacing: {
            top: '1rem',
            right: '0',
            bottom: '1rem',
            left: '0'
          }
        },
        isActive: true
      }
    })

    // Create E-commerce Store Layout
    const ecommerceLayout = await prisma.layout.upsert({
      where: { id: 'ecommerce-store-layout' },
      update: {},
      create: {
        id: 'ecommerce-store-layout',
        name: 'E-commerce Store Layout',
        description: 'Complete e-commerce layout with header, navigation, cart, and footer',
        type: 'site',
        category: 'ecommerce',
        structure: {
          header: {
            id: 'header-ecommerce',
            type: 'header',
            name: 'E-commerce Header',
            position: 1,
            blocks: [
              {
                id: 'logo-ecommerce',
                type: 'logo',
                name: 'Store Logo',
                position: 1,
                configuration: { size: 'medium', alignment: 'left' },
                content: { text: 'Coco Milk Kids', image: '/logo.png' },
                styling: {},
                responsive: {},
                conditions: {},
                isVisible: true
              },
              {
                id: 'search-ecommerce',
                type: 'search',
                name: 'Product Search',
                position: 2,
                configuration: { size: 'medium', alignment: 'center' },
                content: { placeholder: 'Search products...' },
                styling: {},
                responsive: {},
                conditions: {},
                isVisible: true
              },
              {
                id: 'nav-ecommerce',
                type: 'navigation',
                name: 'Main Navigation',
                position: 3,
                configuration: { style: 'horizontal' },
                content: { menu: 'main-menu' },
                styling: {},
                responsive: {},
                conditions: {},
                isVisible: true
              },
              {
                id: 'cart-ecommerce',
                type: 'cart',
                name: 'Shopping Cart',
                position: 4,
                configuration: { size: 'medium', alignment: 'right' },
                content: {},
                styling: {},
                responsive: {},
                conditions: {},
                isVisible: true
              }
            ],
            configuration: { 
              layout: 'flex', 
              alignment: 'justify',
              spacing: { top: '1rem', right: '2rem', bottom: '1rem', left: '2rem' }
            },
            styling: { 
              background: { type: 'color', color: '#ffffff' },
              border: { width: '0 0 1px 0', style: 'solid', color: '#e5e5e5', radius: '0' }
            },
            responsive: {},
            isVisible: true
          },
          main: {
            id: 'main-ecommerce',
            type: 'main',
            name: 'Main Content',
            position: 2,
            blocks: [
              {
                id: 'content-ecommerce',
                type: 'content',
                name: 'Page Content',
                position: 1,
                configuration: {},
                content: {},
                styling: {},
                responsive: {},
                conditions: {},
                isVisible: true
              }
            ],
            configuration: { 
              layout: 'block',
              spacing: { top: '2rem', right: '2rem', bottom: '2rem', left: '2rem' }
            },
            styling: {},
            responsive: {},
            isVisible: true
          },
          footer: {
            id: 'footer-ecommerce',
            type: 'footer',
            name: 'E-commerce Footer',
            position: 3,
            blocks: [
              {
                id: 'links-footer',
                type: 'links',
                name: 'Footer Links',
                position: 1,
                configuration: { alignment: 'center' },
                content: { menu: 'footer-menu' },
                styling: {},
                responsive: {},
                conditions: {},
                isVisible: true
              },
              {
                id: 'social-footer',
                type: 'social',
                name: 'Social Links',
                position: 2,
                configuration: { alignment: 'center' },
                content: {
                  links: [
                    { id: 'facebook', text: 'Facebook', url: 'https://facebook.com', target: '_blank', icon: '📘' },
                    { id: 'instagram', text: 'Instagram', url: 'https://instagram.com', target: '_blank', icon: '📷' },
                    { id: 'twitter', text: 'Twitter', url: 'https://twitter.com', target: '_blank', icon: '🐦' }
                  ]
                },
                styling: {},
                responsive: {},
                conditions: {},
                isVisible: true
              },
              {
                id: 'copyright-footer',
                type: 'copyright',
                name: 'Copyright',
                position: 3,
                configuration: { alignment: 'center' },
                content: { text: '© 2024 Coco Milk Kids. All rights reserved.' },
                styling: {},
                responsive: {},
                conditions: {},
                isVisible: true
              }
            ],
            configuration: { 
              layout: 'flex', 
              alignment: 'center',
              spacing: { top: '2rem', right: '2rem', bottom: '2rem', left: '2rem' }
            },
            styling: { 
              background: { type: 'color', color: '#f8f9fa' },
              border: { width: '1px 0 0 0', style: 'solid', color: '#e5e5e5', radius: '0' }
            },
            responsive: {},
            isVisible: true
          }
        },
        styling: {
          theme: 'ecommerce',
          colorScheme: 'light',
          typography: {
            fontFamily: 'Inter, sans-serif',
            fontSize: '16px',
            fontWeight: '400',
            lineHeight: '1.5',
            letterSpacing: '0',
            textAlign: 'left',
            textTransform: 'none'
          },
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          colors: {
            primary: '#ffcc00',
            secondary: '#333333',
            accent: '#ff6b6b',
            text: '#333333',
            background: '#ffffff',
            border: '#e5e5e5'
          }
        },
        responsive: {
          mobile: { 
            display: 'block', 
            width: '100%', 
            height: 'auto', 
            spacing: { top: 0, right: 16, bottom: 0, left: 16 }, 
            typography: { fontSize: '14px' } 
          },
          tablet: { 
            display: 'block', 
            width: '100%', 
            height: 'auto', 
            spacing: { top: 0, right: 24, bottom: 0, left: 24 }, 
            typography: { fontSize: '15px' } 
          },
          desktop: { 
            display: 'block', 
            width: '100%', 
            height: 'auto', 
            spacing: { top: 0, right: 32, bottom: 0, left: 32 }, 
            typography: { fontSize: '16px' } 
          },
          large: { 
            display: 'block', 
            width: '100%', 
            height: 'auto', 
            spacing: { top: 0, right: 40, bottom: 0, left: 40 }, 
            typography: { fontSize: '16px' } 
          }
        },
        conditions: {},
        isTemplate: true,
        isSystem: false,
        isActive: true,
        usageCount: 0,
        tags: ['ecommerce', 'store', 'shopping']
      }
    })

    // Create global assignment for e-commerce layout
    await prisma.layoutAssignment.upsert({
      where: { id: 'global-ecommerce-assignment' },
      update: {},
      create: {
        id: 'global-ecommerce-assignment',
        layoutId: ecommerceLayout.id,
        targetType: 'global',
        priority: 10, // Higher priority than default
        conditions: {},
        isActive: true
      }
    })

    console.log('✅ Layout Builder seeded successfully!')
    console.log(`
🎨 Layout Builder Components Created:
- E-commerce Store Layout: Complete layout with header, navigation, cart, footer
- Main Navigation Menu: Home, Shop (with submenu), About, Contact
- Footer Navigation Menu: Privacy, Terms, Shipping, Returns
- Primary Sidebar: Recent posts and categories widgets
- Social Media Links: Facebook, Instagram, Twitter

🔧 Layout Features:
- Responsive design for mobile, tablet, desktop
- Flexible block system for headers and footers
- Navigation with dropdown support
- Shopping cart integration
- Widget areas for dynamic content
- SEO-optimized structure

🚀 Next Steps:
1. Visit any page to see the new layout in action
2. Access /admin/layout-builder to customize layouts
3. Create additional layout templates for different page types
4. Configure navigation menus and widget areas
    `)

  } catch (error) {
    console.error('❌ Error seeding Layout Builder:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  seedLayoutBuilder()
    .catch((error) => {
      console.error(error)
      process.exit(1)
    })
}

export default seedLayoutBuilder
