#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client'
import { ThemeGenerator } from '@/lib/theme-generator/theme-generator'
import { ThemeGeneratorOptions } from '@/lib/theme-generator/types'

const prisma = new PrismaClient()

async function seedThemes() {
  console.log('🎨 Seeding themes...')

  try {
    // Clean existing themes (in development only)
    if (process.env.NODE_ENV !== 'production') {
      console.log('🧹 Cleaning existing themes...')
      await prisma.themeApplication.deleteMany()
      await prisma.theme.deleteMany()
    }

    // Default theme presets
    const themePresets: Array<{
      name: string
      description: string
      category: string
      isDefault?: boolean
      options: ThemeGeneratorOptions
    }> = [
      {
        name: 'Coco Milk Default',
        description: 'The default theme for Coco Milk Kids store',
        category: 'business',
        isDefault: true,
        options: {
          baseColor: '#3b82f6',
          style: 'modern',
          contrast: 'medium',
          saturation: 'normal',
          borderRadius: 'rounded',
          fontPairing: 'modern',
          spacing: 'normal'
        }
      },
      {
        name: 'Elegant Purple',
        description: 'Sophisticated purple theme with elegant styling',
        category: 'elegant',
        options: {
          baseColor: '#8b5cf6',
          style: 'elegant',
          contrast: 'medium',
          saturation: 'normal',
          borderRadius: 'rounded',
          fontPairing: 'classic',
          spacing: 'spacious'
        }
      },
      {
        name: 'Bold Orange',
        description: 'Vibrant and energetic orange theme',
        category: 'bold',
        options: {
          baseColor: '#f97316',
          style: 'bold',
          contrast: 'high',
          saturation: 'vibrant',
          borderRadius: 'rounded',
          fontPairing: 'modern',
          spacing: 'compact'
        }
      },
      {
        name: 'Minimal Gray',
        description: 'Clean and minimal gray theme',
        category: 'minimal',
        options: {
          baseColor: '#6b7280',
          style: 'minimal',
          contrast: 'low',
          saturation: 'muted',
          borderRadius: 'sharp',
          fontPairing: 'modern',
          spacing: 'normal'
        }
      },
      {
        name: 'Creative Pink',
        description: 'Playful and creative pink theme',
        category: 'creative',
        options: {
          baseColor: '#ec4899',
          style: 'playful',
          contrast: 'medium',
          saturation: 'vibrant',
          borderRadius: 'pill',
          fontPairing: 'creative',
          spacing: 'spacious'
        }
      },
      {
        name: 'Professional Navy',
        description: 'Professional navy blue theme for business',
        category: 'business',
        options: {
          baseColor: '#1e40af',
          style: 'professional',
          contrast: 'medium',
          saturation: 'normal',
          borderRadius: 'rounded',
          fontPairing: 'technical',
          spacing: 'normal'
        }
      },
      {
        name: 'Modern Green',
        description: 'Fresh and modern green theme',
        category: 'modern',
        options: {
          baseColor: '#10b981',
          style: 'modern',
          contrast: 'medium',
          saturation: 'normal',
          borderRadius: 'rounded',
          fontPairing: 'modern',
          spacing: 'normal'
        }
      },
      {
        name: 'Classic Red',
        description: 'Timeless red theme with classic styling',
        category: 'classic',
        options: {
          baseColor: '#dc2626',
          style: 'elegant',
          contrast: 'medium',
          saturation: 'normal',
          borderRadius: 'rounded',
          fontPairing: 'classic',
          spacing: 'normal'
        }
      }
    ]

    const createdThemes = []

    for (const preset of themePresets) {
      console.log(`🎨 Creating theme: ${preset.name}`)
      
      // Generate theme configuration
      const themeConfig = ThemeGenerator.generateTheme(preset.options)
      
      // Override generated metadata with preset data
      themeConfig.name = preset.name
      themeConfig.description = preset.description
      themeConfig.metadata.category = preset.category as any
      themeConfig.metadata.isDefault = preset.isDefault || false
      themeConfig.metadata.isActive = preset.isDefault || false
      themeConfig.author = 'Coco Milk Kids'

      // Save theme to database
      const savedTheme = await prisma.theme.create({
        data: {
          id: themeConfig.id,
          name: themeConfig.name,
          description: themeConfig.description,
          version: themeConfig.version,
          author: themeConfig.author,
          category: themeConfig.metadata.category,
          tags: themeConfig.metadata.tags,
          preview: themeConfig.metadata.preview,
          config: JSON.stringify(themeConfig),
          isDefault: themeConfig.metadata.isDefault,
          isActive: themeConfig.metadata.isActive,
          createdBy: 'system',
          updatedBy: 'system'
        }
      })

      createdThemes.push(savedTheme)
      console.log(`✅ Created theme: ${savedTheme.name} (${savedTheme.id})`)
    }

    // Apply default theme globally
    const defaultTheme = createdThemes.find(t => t.isDefault)
    if (defaultTheme) {
      console.log(`🌐 Applying default theme globally: ${defaultTheme.name}`)
      
      await prisma.themeApplication.create({
        data: {
          scope: 'global',
          themeId: defaultTheme.id,
          appliedBy: 'system',
          isActive: true
        }
      })
    }

    console.log('✅ Theme seeding completed successfully!')
    console.log(`Created ${createdThemes.length} themes:`)
    createdThemes.forEach(theme => {
      console.log(`  - ${theme.name} (${theme.category})${theme.isDefault ? ' [DEFAULT]' : ''}${theme.isActive ? ' [ACTIVE]' : ''}`)
    })

  } catch (error) {
    console.error('❌ Error seeding themes:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seeder
if (require.main === module) {
  seedThemes()
    .then(() => {
      console.log('🎉 Theme seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Theme seeding failed:', error)
      process.exit(1)
    })
}

export { seedThemes }
