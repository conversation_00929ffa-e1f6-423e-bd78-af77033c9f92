#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client'
import { WorkflowService } from '@/lib/workflows/workflow-service'
import { customerWorkflowTemplates } from '@/lib/workflows/templates/customer-workflows'
import { orderWorkflowTemplates } from '@/lib/workflows/templates/order-workflows'
import { inventoryWorkflowTemplates } from '@/lib/workflows/templates/inventory-workflows'

const prisma = new PrismaClient()
const workflowService = new WorkflowService(prisma)

async function seedWorkflows() {
  console.log('🔄 Seeding workflows...')

  try {
    // Clean existing workflows (in development only)
    if (process.env.NODE_ENV !== 'production') {
      console.log('🧹 Cleaning existing workflows...')
      await prisma.workflowExecution.deleteMany()
      await prisma.workflow.deleteMany()
    }

    const createdWorkflows = []

    // Seed customer workflows
    console.log('👥 Creating customer workflows...')
    for (const template of customerWorkflowTemplates) {
      console.log(`  Creating: ${template.name}`)
      
      const workflow = await workflowService.createWorkflowFromTemplate(template.id, {
        createdBy: 'system',
        isActive: true
      })
      
      createdWorkflows.push(workflow)
      console.log(`  ✅ Created: ${workflow.name} (${workflow.id})`)
    }

    // Seed order workflows
    console.log('📦 Creating order workflows...')
    for (const template of orderWorkflowTemplates) {
      console.log(`  Creating: ${template.name}`)
      
      const workflow = await workflowService.createWorkflowFromTemplate(template.id, {
        createdBy: 'system',
        isActive: true
      })
      
      createdWorkflows.push(workflow)
      console.log(`  ✅ Created: ${workflow.name} (${workflow.id})`)
    }

    // Seed inventory workflows
    console.log('📊 Creating inventory workflows...')
    for (const template of inventoryWorkflowTemplates) {
      console.log(`  Creating: ${template.name}`)
      
      const workflow = await workflowService.createWorkflowFromTemplate(template.id, {
        createdBy: 'system',
        isActive: true
      })
      
      createdWorkflows.push(workflow)
      console.log(`  ✅ Created: ${workflow.name} (${workflow.id})`)
    }

    console.log('✅ Workflow seeding completed successfully!')
    console.log(`Created ${createdWorkflows.length} workflows:`)
    
    // Group by category
    const byCategory = createdWorkflows.reduce((acc, workflow) => {
      if (!acc[workflow.category]) acc[workflow.category] = []
      acc[workflow.category].push(workflow)
      return acc
    }, {} as Record<string, any[]>)

    Object.entries(byCategory).forEach(([category, workflows]) => {
      console.log(`\n📂 ${category.toUpperCase()} (${workflows.length} workflows):`)
      workflows.forEach(workflow => {
        console.log(`  - ${workflow.name}${workflow.isActive ? ' [ACTIVE]' : ''}`)
      })
    })

    // Create some sample workflow executions for demonstration
    console.log('\n🎯 Creating sample workflow executions...')
    
    const sampleExecutions = [
      {
        eventType: 'customer.registered' as const,
        eventData: {
          customer: {
            id: 'sample-customer-1',
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe'
          }
        }
      },
      {
        eventType: 'order.paid' as const,
        eventData: {
          order: {
            id: 'sample-order-1',
            orderNumber: 'ORD-001',
            customerEmail: '<EMAIL>',
            customerFirstName: 'Jane',
            total: 299.99,
            items: [
              { id: 'item-1', name: 'Kids T-Shirt', quantity: 2, price: 149.99 }
            ],
            paymentStatus: 'paid'
          }
        }
      },
      {
        eventType: 'inventory.low_stock' as const,
        eventData: {
          product: {
            id: 'product-1',
            name: 'Kids Jeans',
            sku: 'KJ-001',
            currentStock: 5,
            reorderPoint: 10,
            autoReorder: true,
            supplierId: 'supplier-1'
          }
        }
      }
    ]

    for (const sample of sampleExecutions) {
      try {
        const executionIds = await workflowService.triggerWorkflow(
          sample.eventType,
          sample.eventData,
          { userId: 'system', source: 'seeder' }
        )
        
        if (executionIds.length > 0) {
          console.log(`  ✅ Triggered ${executionIds.length} workflow(s) for ${sample.eventType}`)
        }
      } catch (error) {
        console.log(`  ⚠️  Could not trigger workflow for ${sample.eventType}: ${error}`)
      }
    }

    console.log('\n🎉 Workflow seeding completed!')

  } catch (error) {
    console.error('❌ Error seeding workflows:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seeder
if (require.main === module) {
  seedWorkflows()
    .then(() => {
      console.log('🎉 Workflow seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Workflow seeding failed:', error)
      process.exit(1)
    })
}

export { seedWorkflows }
