#!/usr/bin/env tsx

/**
 * Product Seeder Runner
 * 
 * This script runs the product seeder and saves the generated products
 * to a new file that can be used throughout the application.
 */

import { writeFileSync } from 'fs'
import { join } from 'path'
import { seedProducts } from '../lib/seeders/product-seeder'

async function runSeeder() {
  console.log('🚀 Starting Coco Milk Kids Product Seeder...\n')
  
  try {
    // Generate products from images
    const products = seedProducts()
    
    console.log('\n📝 Writing products to file...')
    
    // Create the generated products file content
    const fileContent = `/**
 * Generated Products for Coco Milk Kids
 * 
 * This file was automatically generated by the product seeder.
 * It contains products based on the available images and their metadata.
 * 
 * Generated on: ${new Date().toISOString()}
 * Total products: ${products.length}
 */

import { Product, ProductVariant } from './product-seed'

export const generatedProducts: Product[] = ${JSON.stringify(products, null, 2)}

// Helper functions for working with generated product data
export const getGeneratedProductsByCategory = (category: string) => {
  return generatedProducts.filter(product => product.category === category)
}

export const getGeneratedProductsBySubcategory = (subcategory: string) => {
  return generatedProducts.filter(product => product.subcategory === subcategory)
}

export const getGeneratedProductsByGender = (gender: Product['gender']) => {
  return generatedProducts.filter(product => product.gender === gender || product.gender === 'unisex')
}

export const getGeneratedProductsBySeason = (season: Product['season']) => {
  return generatedProducts.filter(product => product.season === season || product.season === 'all-season')
}

export const getGeneratedNewProducts = () => {
  return generatedProducts.filter(product => product.isNew)
}

export const getGeneratedFeaturedProducts = () => {
  return generatedProducts.filter(product => product.isFeatured)
}

export const getGeneratedSaleProducts = () => {
  return generatedProducts.filter(product => product.isOnSale)
}

export const getGeneratedProductById = (id: string) => {
  return generatedProducts.find(product => product.id === id)
}

export const getGeneratedProductBySlug = (slug: string) => {
  return generatedProducts.find(product => product.slug === slug)
}

export const searchGeneratedProducts = (query: string) => {
  const lowercaseQuery = query.toLowerCase()
  return generatedProducts.filter(product =>
    product.name.toLowerCase().includes(lowercaseQuery) ||
    product.description.toLowerCase().includes(lowercaseQuery) ||
    product.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
    product.category.toLowerCase().includes(lowercaseQuery) ||
    product.subcategory.toLowerCase().includes(lowercaseQuery)
  )
}

// Product statistics
export const getGeneratedProductStats = () => {
  return {
    total: generatedProducts.length,
    categories: [...new Set(generatedProducts.map(p => p.category))].length,
    subcategories: [...new Set(generatedProducts.map(p => p.subcategory))].length,
    newProducts: getGeneratedNewProducts().length,
    featuredProducts: getGeneratedFeaturedProducts().length,
    saleProducts: getGeneratedSaleProducts().length,
    averagePrice: generatedProducts.reduce((sum, p) => sum + p.price, 0) / generatedProducts.length,
    priceRange: {
      min: Math.min(...generatedProducts.map(p => p.price)),
      max: Math.max(...generatedProducts.map(p => p.price))
    },
    totalStock: generatedProducts.reduce((sum, p) => sum + p.stock, 0),
    byCategory: {
      clothing: getGeneratedProductsByCategory('clothing').length,
      footwear: getGeneratedProductsByCategory('footwear').length,
      accessories: getGeneratedProductsByCategory('accessories').length
    },
    byGender: {
      boys: getGeneratedProductsByGender('boys').length,
      girls: getGeneratedProductsByGender('girls').length,
      unisex: getGeneratedProductsByGender('unisex').length
    },
    bySeason: {
      spring: getGeneratedProductsBySeason('spring').length,
      summer: getGeneratedProductsBySeason('summer').length,
      autumn: getGeneratedProductsBySeason('autumn').length,
      winter: getGeneratedProductsBySeason('winter').length,
      allSeason: getGeneratedProductsBySeason('all-season').length
    }
  }
}

// Quick access functions
export const getGeneratedClothingProducts = () => getGeneratedProductsByCategory('clothing')
export const getGeneratedFootwearProducts = () => getGeneratedProductsByCategory('footwear')
export const getGeneratedAccessoryProducts = () => getGeneratedProductsByCategory('accessories')
export const getGeneratedBoysProducts = () => getGeneratedProductsByGender('boys')
export const getGeneratedGirlsProducts = () => getGeneratedProductsByGender('girls')
export const getGeneratedUnisexProducts = () => getGeneratedProductsByGender('unisex')
`

    // Write to file
    const outputPath = join(process.cwd(), 'lib/data/generated-products.ts')
    writeFileSync(outputPath, fileContent, 'utf8')
    
    console.log(`✅ Products written to: ${outputPath}`)
    
    // Display summary statistics
    console.log('\n📊 Generation Summary:')
    console.log('=' .repeat(50))
    
    const stats = {
      total: products.length,
      categories: [...new Set(products.map(p => p.category))],
      subcategories: [...new Set(products.map(p => p.subcategory))],
      newProducts: products.filter(p => p.isNew).length,
      featuredProducts: products.filter(p => p.isFeatured).length,
      saleProducts: products.filter(p => p.isOnSale).length,
      averagePrice: products.reduce((sum, p) => sum + p.price, 0) / products.length,
      priceRange: {
        min: Math.min(...products.map(p => p.price)),
        max: Math.max(...products.map(p => p.price))
      },
      totalStock: products.reduce((sum, p) => sum + p.stock, 0),
      byCategory: {
        clothing: products.filter(p => p.category === 'clothing').length,
        footwear: products.filter(p => p.category === 'footwear').length,
        accessories: products.filter(p => p.category === 'accessories').length
      },
      byGender: {
        boys: products.filter(p => p.gender === 'boys').length,
        girls: products.filter(p => p.gender === 'girls').length,
        unisex: products.filter(p => p.gender === 'unisex').length
      }
    }
    
    console.log(`📦 Total Products: ${stats.total}`)
    console.log(`🏷️ Categories: ${stats.categories.join(', ')} (${stats.categories.length})`)
    console.log(`📂 Subcategories: ${stats.subcategories.length}`)
    console.log(`💰 Price Range: R${stats.priceRange.min} - R${stats.priceRange.max}`)
    console.log(`💵 Average Price: R${stats.averagePrice.toFixed(2)}`)
    console.log(`📦 Total Stock: ${stats.totalStock} items`)
    console.log(`🆕 New Products: ${stats.newProducts}`)
    console.log(`⭐ Featured Products: ${stats.featuredProducts}`)
    console.log(`🏷️ Sale Products: ${stats.saleProducts}`)
    
    console.log('\n📊 By Category:')
    console.log(`  👕 Clothing: ${stats.byCategory.clothing}`)
    console.log(`  👟 Footwear: ${stats.byCategory.footwear}`)
    console.log(`  🎒 Accessories: ${stats.byCategory.accessories}`)
    
    console.log('\n👥 By Gender:')
    console.log(`  👦 Boys: ${stats.byGender.boys}`)
    console.log(`  👧 Girls: ${stats.byGender.girls}`)
    console.log(`  👶 Unisex: ${stats.byGender.unisex}`)
    
    console.log('\n🎉 Product seeding completed successfully!')
    console.log(`📁 Generated file: lib/data/generated-products.ts`)
    
  } catch (error) {
    console.error('❌ Error running seeder:', error)
    process.exit(1)
  }
}

// Run the seeder
runSeeder()
