#!/bin/bash

# Prisma Import Standardization Script
# This script updates all files to use the centralized @/lib/prisma import

set -e

echo "🔧 Starting Prisma Import Standardization..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

print_info "Project root detected"

# Create backup directory
BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
print_info "Creating backup in $BACKUP_DIR..."
mkdir -p "$BACKUP_DIR"

# Files to update with their old and new import patterns
declare -A FILES_TO_UPDATE=(
    ["lib/ecommerce/services/newsletter-service.ts"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["lib/ecommerce/services/payment-service.ts"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["lib/ecommerce/services/inventory-service.ts"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["lib/ecommerce/services/coupon-service.ts"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["lib/ecommerce/services/order-service.ts"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["lib/ecommerce/services/analytics-service.ts"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["lib/ecommerce/services/product-variant-service.ts"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["lib/ecommerce/services/shipping-service.ts"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["lib/ecommerce/services/email-service.ts"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["lib/routing/route-resolver.ts"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["lib/site-settings/site-settings-service.ts"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["app/api/admin/site-settings/route.ts"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["app/api/admin/site-settings/homepage/route.ts"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["app/api/site-settings/homepage/route.ts"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["app/api/pages/check-slug/route.ts"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["app/dynamic-page/[slug]/page.tsx"]="@/lib/ecommerce/config/database|@/lib/prisma"
    ["app/[...slug]/page.tsx"]="@/lib/ecommerce/config/database|@/lib/prisma"
)

# Function to update a single file
update_file() {
    local file="$1"
    local old_import="$2"
    local new_import="$3"
    
    if [ -f "$file" ]; then
        # Create backup
        cp "$file" "$BACKUP_DIR/$(basename "$file").backup"
        
        # Update the import
        if grep -q "$old_import" "$file"; then
            sed -i.tmp "s|from '$old_import'|from '$new_import'|g" "$file"
            sed -i.tmp "s|from \"$old_import\"|from \"$new_import\"|g" "$file"
            rm -f "$file.tmp"
            print_status "Updated $file"
            return 0
        else
            print_info "No changes needed for $file"
            return 1
        fi
    else
        print_warning "File not found: $file"
        return 1
    fi
}

# Update all files
updated_count=0
total_files=${#FILES_TO_UPDATE[@]}

print_info "Processing $total_files files..."
echo ""

for file in "${!FILES_TO_UPDATE[@]}"; do
    IFS='|' read -r old_import new_import <<< "${FILES_TO_UPDATE[$file]}"
    
    if update_file "$file" "$old_import" "$new_import"; then
        ((updated_count++))
    fi
done

echo ""
print_info "Processing additional patterns..."

# Find and update any remaining files with old imports
find_and_update() {
    local pattern="$1"
    local replacement="$2"
    local description="$3"
    
    print_info "Searching for $description..."
    
    # Find files containing the pattern
    files=$(grep -r -l "$pattern" app/ lib/ components/ hooks/ 2>/dev/null || true)
    
    if [ -n "$files" ]; then
        echo "$files" | while read -r file; do
            if [ -f "$file" ] && [[ "$file" != *"node_modules"* ]] && [[ "$file" != *".git"* ]]; then
                # Create backup if not already backed up
                backup_file="$BACKUP_DIR/$(basename "$file").backup"
                if [ ! -f "$backup_file" ]; then
                    cp "$file" "$backup_file"
                fi
                
                # Apply replacement
                sed -i.tmp "$replacement" "$file"
                rm -f "$file.tmp"
                print_status "Updated $file ($description)"
                ((updated_count++))
            fi
        done
    fi
}

# Update various import patterns
find_and_update "from '@/lib/ecommerce/config/database'" "s|from '@/lib/ecommerce/config/database'|from '@/lib/prisma'|g" "old database imports"
find_and_update 'from "@/lib/ecommerce/config/database"' 's|from "@/lib/ecommerce/config/database"|from "@/lib/prisma"|g' "old database imports (double quotes)"

# Update db references to prisma
find_and_update "{ db }" "s|{ db }|{ prisma }|g" "db destructuring"
find_and_update "db\." "s|db\.|prisma.|g" "db usage"

# Update getPrismaClient references
find_and_update "getPrismaClient()" "s|getPrismaClient()|prisma|g" "getPrismaClient calls"
find_and_update "{ getPrismaClient }" "s|{ getPrismaClient }|{ prisma }|g" "getPrismaClient imports"

echo ""
print_info "Summary:"
echo "  📄 Total files processed: $total_files"
echo "  ✅ Files updated: $updated_count"
echo "  📁 Backups created in: $BACKUP_DIR"

echo ""
print_info "Next steps:"
echo "  1. Run: pnpm build"
echo "  2. Test your application"
echo "  3. Run: npx prisma generate (if needed)"
echo "  4. If everything works, you can remove the backup directory"
echo "  5. Commit your changes"

echo ""
print_status "Prisma import standardization complete! 🎉"

# Optional: Run build to check for errors
read -p "Would you like to run 'pnpm build' to check for errors? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "Running build..."
    if pnpm build; then
        print_status "Build successful!"
    else
        print_error "Build failed. Please check the errors above."
        print_info "You can restore from backups in $BACKUP_DIR if needed."
        exit 1
    fi
fi
