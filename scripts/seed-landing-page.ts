#!/usr/bin/env tsx

import { seedLandingPage } from '@/lib/seeders/landing-page-seeder'

/**
 * <PERSON>ript to seed the dynamic landing page
 * This will create a page builder version of the current hardcoded landing page
 * and set it as the homepage
 */
async function main() {
  console.log('🚀 Starting landing page seeding...')
  
  try {
    await seedLandingPage()
    console.log('✅ Landing page seeding completed successfully!')
    process.exit(0)
  } catch (error) {
    console.error('❌ Landing page seeding failed:', error)
    process.exit(1)
  }
}

// Run the script
main()
