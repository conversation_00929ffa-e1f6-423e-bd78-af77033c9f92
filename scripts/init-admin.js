#!/usr/bin/env node

/**
 * Admin User Initialization Script
 * 
 * This script initializes the default admin user for the Coco Milk Kids admin system.
 * Run this script after setting up your database and environment variables.
 * 
 * Usage:
 *   node scripts/init-admin.js
 *   
 * Or via npm:
 *   npm run init-admin
 */

const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function initializeAdmin() {
  try {
    console.log('🚀 Initializing Coco Milk Kids Admin System...\n')

    // Check if admin user already exists
    console.log('📋 Checking for existing admin users...')
    const existingAdmin = await prisma.adminUser.findFirst()
    
    if (existingAdmin) {
      console.log('✅ Admin user already exists!')
      console.log(`   Email: ${existingAdmin.email}`)
      console.log(`   Name: ${existingAdmin.displayName}`)
      console.log('\n💡 If you need to reset the admin password, please use the admin panel or contact support.')
      return
    }

    console.log('📝 No admin user found. Creating default admin user...\n')

    // Create default admin role
    console.log('🔐 Creating admin role...')
    const adminRole = await prisma.adminRole.upsert({
      where: { name: 'admin' },
      update: {},
      create: {
        name: 'admin',
        description: 'Full administrative access to Coco Milk Kids system',
        permissions: [
          // Product management
          'products.read',
          'products.write',
          'products.delete',
          'products.manage',
          
          // Order management
          'orders.read',
          'orders.write',
          'orders.delete',
          'orders.manage',
          'orders.fulfill',
          'orders.refund',
          
          // Customer management
          'customers.read',
          'customers.write',
          'customers.delete',
          'customers.manage',
          
          // Inventory management
          'inventory.read',
          'inventory.write',
          'inventory.manage',
          
          // Analytics and reports
          'analytics.read',
          'reports.read',
          'reports.generate',
          
          // System settings
          'settings.read',
          'settings.write',
          'settings.manage',
          
          // User management
          'users.read',
          'users.write',
          'users.delete',
          'users.manage',
          
          // Content management
          'content.read',
          'content.write',
          'content.delete',
          'content.manage',
          
          // Page builder
          'pages.read',
          'pages.write',
          'pages.delete',
          'pages.manage',
          
          // System administration
          'system.manage',
          'logs.read',
          'backup.manage'
        ],
        isSystemRole: true
      }
    })
    console.log('✅ Admin role created successfully!')

    // Create default admin user
    console.log('👤 Creating default admin user...')
    const defaultPassword = 'admin123'
    const hashedPassword = await bcrypt.hash(defaultPassword, 12)
    
    await prisma.adminUser.create({
      data: {
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        displayName: 'Coco Milk Kids Admin',
        passwordHash: hashedPassword,
        isActive: true,
        isEmailVerified: true,
        timezone: 'Africa/Johannesburg',
        locale: 'en',
        roles: {
          create: {
            roleId: adminRole.id
          }
        }
      }
    })

    console.log('✅ Default admin user created successfully!\n')

    // Display login credentials
    console.log('🎉 Admin system initialization complete!\n')
    console.log('📋 Login Credentials:')
    console.log('   URL: http://localhost:3000/admin')
    console.log('   Email: <EMAIL>')
    console.log('   Password: admin123')
    console.log('\n⚠️  IMPORTANT SECURITY NOTES:')
    console.log('   1. Change the default password immediately after first login')
    console.log('   2. Use a strong, unique password for production')
    console.log('   3. Enable two-factor authentication if available')
    console.log('   4. Regularly review admin user permissions')
    console.log('\n🔒 For production deployment:')
    console.log('   - Set a strong JWT_SECRET in your environment variables')
    console.log('   - Use HTTPS for all admin access')
    console.log('   - Implement proper backup procedures')
    console.log('   - Monitor admin activity logs')

  } catch (error) {
    console.error('❌ Error initializing admin system:', error)
    
    if (error.code === 'P2002') {
      console.log('\n💡 This error usually means the admin user already exists.')
      console.log('   Try logging in with: <EMAIL> / admin123')
    } else if (error.code === 'P1001') {
      console.log('\n💡 Database connection error. Please check:')
      console.log('   1. Database is running')
      console.log('   2. DATABASE_URL is correctly set in .env')
      console.log('   3. Database migrations have been run: npx prisma migrate dev')
    } else {
      console.log('\n💡 Please check:')
      console.log('   1. Database is properly configured and running')
      console.log('   2. Prisma migrations are up to date: npx prisma migrate dev')
      console.log('   3. Environment variables are properly set')
    }
    
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the initialization
if (require.main === module) {
  initializeAdmin()
}

module.exports = { initializeAdmin }
