#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client'
import { WorkflowService } from '@/lib/workflows/workflow-service'
import { vipCustomerWorkflow } from '@/lib/workflows/examples/custom-workflow-examples'

const prisma = new PrismaClient()
const workflowService = new WorkflowService(prisma)

async function testWorkflowSystem() {
  console.log('🧪 Testing Workflow System...')

  try {
    // Initialize the service
    await workflowService.initialize()

    // Test 1: List existing workflows
    console.log('\n📋 Test 1: List Existing Workflows')
    const { workflows, total } = await workflowService.listWorkflows()
    console.log(`✅ Found ${total} workflows in database`)
    workflows.slice(0, 3).forEach(workflow => {
      console.log(`  - ${workflow.name} (${workflow.category}) - ${workflow.isActive ? 'Active' : 'Inactive'}`)
    })

    // Test 2: Get available templates
    console.log('\n📚 Test 2: Available Templates')
    const templates = workflowService.getTemplates()
    console.log(`✅ Found ${templates.length} workflow templates`)
    templates.slice(0, 5).forEach(template => {
      console.log(`  - ${template.name} (${template.category})`)
    })

    // Test 3: Create workflow from template
    console.log('\n🏗️ Test 3: Create Workflow from Template')
    const newWorkflow = await workflowService.createWorkflowFromTemplate(
      'customer-welcome-series',
      {
        name: 'Test Welcome Series',
        description: 'Test workflow created by system test',
        createdBy: 'test-system',
        tags: ['test', 'welcome']
      }
    )
    console.log(`✅ Created workflow: ${newWorkflow.name} (ID: ${newWorkflow.id})`)

    // Test 4: Create custom workflow
    console.log('\n🎨 Test 4: Create Custom Workflow')
    const customWorkflow = await workflowService.createWorkflow({
      name: 'Test Custom Workflow',
      description: 'A simple test workflow',
      version: '1.0.0',
      category: 'custom',
      trigger: {
        type: 'event',
        event: 'customer.registered',
        config: {}
      },
      steps: [
        {
          id: 'test-step-1',
          name: 'Test Notification',
          description: 'Send a test notification',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'test-template',
            recipient: '{{customer.email}}',
            data: {
              message: 'Welcome to our test!'
            }
          },
          retryCount: 0,
          maxRetries: 3
        }
      ],
      isActive: true,
      createdBy: 'test-system',
      tags: ['test', 'custom'],
      metadata: {
        testWorkflow: true,
        createdAt: new Date().toISOString()
      }
    })
    console.log(`✅ Created custom workflow: ${customWorkflow.name} (ID: ${customWorkflow.id})`)

    // Test 5: Trigger a workflow
    console.log('\n🚀 Test 5: Trigger Workflow')
    const executionIds = await workflowService.triggerWorkflow(
      'customer.registered',
      {
        customer: {
          id: 'test-customer-123',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User'
        }
      },
      {
        userId: 'test-system',
        source: 'system-test'
      }
    )
    console.log(`✅ Triggered ${executionIds.length} workflow execution(s)`)
    executionIds.forEach(id => {
      console.log(`  - Execution ID: ${id}`)
    })

    // Test 6: Get workflow analytics
    console.log('\n📊 Test 6: Workflow Analytics')
    if (workflows.length > 0) {
      const analytics = await workflowService.getWorkflowAnalytics(workflows[0].id)
      if (analytics) {
        console.log(`✅ Analytics for "${workflows[0].name}":`)
        console.log(`  - Total Executions: ${analytics.executions}`)
        console.log(`  - Success Rate: ${analytics.successRate.toFixed(1)}%`)
        console.log(`  - Average Duration: ${analytics.averageDuration}ms`)
      } else {
        console.log(`ℹ️ No analytics available for workflow`)
      }
    }

    // Test 7: List executions
    console.log('\n📈 Test 7: List Workflow Executions')
    const { executions, total: executionTotal } = await workflowService.listExecutions({ limit: 5 })
    console.log(`✅ Found ${executionTotal} total executions`)
    executions.forEach(execution => {
      console.log(`  - ${execution.id}: ${execution.status} (${execution.workflowId})`)
    })

    // Test 8: Update workflow
    console.log('\n✏️ Test 8: Update Workflow')
    const updatedWorkflow = await workflowService.updateWorkflow(customWorkflow.id, {
      description: 'Updated test workflow description',
      tags: ['test', 'custom', 'updated']
    })
    if (updatedWorkflow) {
      console.log(`✅ Updated workflow: ${updatedWorkflow.name}`)
      console.log(`  - New description: ${updatedWorkflow.description}`)
      console.log(`  - Tags: ${updatedWorkflow.tags.join(', ')}`)
    }

    // Test 9: Create workflow from example
    console.log('\n🌟 Test 9: Create VIP Customer Workflow Example')
    const vipWorkflow = await workflowService.createWorkflow({
      ...vipCustomerWorkflow,
      name: 'Test VIP Customer Workflow',
      createdBy: 'test-system',
      tags: ['test', 'vip', 'example']
    })
    console.log(`✅ Created VIP workflow: ${vipWorkflow.name}`)
    console.log(`  - Steps: ${vipWorkflow.steps.length}`)
    console.log(`  - Category: ${vipWorkflow.category}`)

    // Cleanup test workflows
    console.log('\n🧹 Cleanup: Removing Test Workflows')
    const testWorkflows = [newWorkflow.id, customWorkflow.id, vipWorkflow.id]
    for (const workflowId of testWorkflows) {
      const deleted = await workflowService.deleteWorkflow(workflowId)
      if (deleted) {
        console.log(`✅ Deleted test workflow: ${workflowId}`)
      }
    }

    console.log('\n🎉 All Tests Completed Successfully!')
    console.log('\n📋 Test Summary:')
    console.log('✅ Workflow listing works')
    console.log('✅ Template system works')
    console.log('✅ Workflow creation from templates works')
    console.log('✅ Custom workflow creation works')
    console.log('✅ Workflow triggering works')
    console.log('✅ Analytics system works')
    console.log('✅ Execution monitoring works')
    console.log('✅ Workflow updates work')
    console.log('✅ Example workflows work')
    console.log('✅ Workflow deletion works')

    console.log('\n🚀 The Workflow System is FULLY FUNCTIONAL!')

  } catch (error) {
    console.error('❌ Test failed:', error)
    throw error
  } finally {
    await workflowService.shutdown()
    await prisma.$disconnect()
  }
}

// Run the test
if (require.main === module) {
  testWorkflowSystem()
    .then(() => {
      console.log('\n✅ Workflow system test completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 Workflow system test failed:', error)
      process.exit(1)
    })
}

export { testWorkflowSystem }
