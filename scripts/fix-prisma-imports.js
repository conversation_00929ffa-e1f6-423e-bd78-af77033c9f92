#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🔧 Starting Prisma Import Standardization Script...\n')

// Configuration
const OLD_IMPORTS = [
  "import { prisma } from '@/lib/ecommerce/config/database'",
  "import { prisma } from '@/lib/ecommerce/config/database';",
  "import { db } from '@/lib/ecommerce/config/database'",
  "import { db } from '@/lib/ecommerce/config/database';",
  "import { getPrismaClient } from '@/lib/ecommerce/config/database'",
  "import { getPrismaClient } from '@/lib/ecommerce/config/database';",
  "from '@/lib/ecommerce/config/database'",
]

const NEW_IMPORT = "import { prisma } from '@/lib/prisma'"

// Files and directories to process
const DIRECTORIES_TO_SCAN = [
  'app',
  'lib',
  'components',
  'hooks',
  'scripts'
]

const FILE_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx']

// Files to exclude from processing
const EXCLUDE_FILES = [
  'lib/prisma.ts', // Our new centralized file
  'lib/ecommerce/config/database.ts', // Keep the original for reference
  'node_modules',
  '.next',
  '.git',
  'dist',
  'build'
]

/**
 * Recursively find all files with specified extensions
 */
function findFiles(dir, extensions = FILE_EXTENSIONS) {
  let results = []
  
  if (!fs.existsSync(dir)) {
    return results
  }

  const files = fs.readdirSync(dir)
  
  for (const file of files) {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    // Skip excluded files/directories
    if (EXCLUDE_FILES.some(exclude => filePath.includes(exclude))) {
      continue
    }
    
    if (stat.isDirectory()) {
      results = results.concat(findFiles(filePath, extensions))
    } else if (extensions.some(ext => file.endsWith(ext))) {
      results.push(filePath)
    }
  }
  
  return results
}

/**
 * Check if file contains any of the old import patterns
 */
function hasOldImports(content) {
  return OLD_IMPORTS.some(oldImport => content.includes(oldImport))
}

/**
 * Replace old imports with new standardized import
 */
function replaceImports(content) {
  let updatedContent = content
  let hasChanges = false
  
  // Replace each old import pattern
  OLD_IMPORTS.forEach(oldImport => {
    if (updatedContent.includes(oldImport)) {
      // Handle different import variations
      if (oldImport.includes('{ db }')) {
        // Replace db with prisma
        updatedContent = updatedContent.replace(
          /import\s*{\s*db\s*}\s*from\s*['"]@\/lib\/ecommerce\/config\/database['"]/g,
          NEW_IMPORT
        )
        // Replace db usage with prisma in the content
        updatedContent = updatedContent.replace(/\bdb\./g, 'prisma.')
        updatedContent = updatedContent.replace(/\bdb\b/g, 'prisma')
      } else if (oldImport.includes('{ getPrismaClient }')) {
        // Replace getPrismaClient with direct prisma import
        updatedContent = updatedContent.replace(
          /import\s*{\s*getPrismaClient\s*}\s*from\s*['"]@\/lib\/ecommerce\/config\/database['"]/g,
          NEW_IMPORT
        )
        // Replace getPrismaClient() calls with prisma
        updatedContent = updatedContent.replace(/getPrismaClient\(\)/g, 'prisma')
      } else {
        // Standard prisma import replacement
        updatedContent = updatedContent.replace(oldImport, NEW_IMPORT)
      }
      hasChanges = true
    }
  })
  
  // Handle complex import statements with multiple imports
  updatedContent = updatedContent.replace(
    /import\s*{\s*([^}]*prisma[^}]*)\s*}\s*from\s*['"]@\/lib\/ecommerce\/config\/database['"]/g,
    (match, imports) => {
      // Extract individual imports
      const importList = imports.split(',').map(imp => imp.trim())
      const otherImports = importList.filter(imp => 
        !imp.includes('prisma') && 
        !imp.includes('db') && 
        !imp.includes('getPrismaClient')
      )
      
      let result = NEW_IMPORT
      if (otherImports.length > 0) {
        result += `\nimport { ${otherImports.join(', ')} } from '@/lib/ecommerce/config/database'`
      }
      
      hasChanges = true
      return result
    }
  )
  
  return { content: updatedContent, hasChanges }
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    
    if (!hasOldImports(content)) {
      return { processed: false, error: null }
    }
    
    const { content: updatedContent, hasChanges } = replaceImports(content)
    
    if (hasChanges) {
      fs.writeFileSync(filePath, updatedContent, 'utf8')
      return { processed: true, error: null }
    }
    
    return { processed: false, error: null }
  } catch (error) {
    return { processed: false, error: error.message }
  }
}

/**
 * Main execution function
 */
function main() {
  console.log('📁 Scanning directories:', DIRECTORIES_TO_SCAN.join(', '))
  console.log('🔍 Looking for file extensions:', FILE_EXTENSIONS.join(', '))
  console.log('❌ Excluding:', EXCLUDE_FILES.join(', '))
  console.log()
  
  // Find all files to process
  let allFiles = []
  DIRECTORIES_TO_SCAN.forEach(dir => {
    if (fs.existsSync(dir)) {
      allFiles = allFiles.concat(findFiles(dir))
    }
  })
  
  console.log(`📄 Found ${allFiles.length} files to scan\n`)
  
  // Process each file
  let processedCount = 0
  let errorCount = 0
  const processedFiles = []
  const errorFiles = []
  
  allFiles.forEach(filePath => {
    const result = processFile(filePath)
    
    if (result.error) {
      errorCount++
      errorFiles.push({ file: filePath, error: result.error })
      console.log(`❌ Error processing ${filePath}: ${result.error}`)
    } else if (result.processed) {
      processedCount++
      processedFiles.push(filePath)
      console.log(`✅ Updated ${filePath}`)
    }
  })
  
  // Summary
  console.log('\n' + '='.repeat(60))
  console.log('📊 PROCESSING SUMMARY')
  console.log('='.repeat(60))
  console.log(`📄 Total files scanned: ${allFiles.length}`)
  console.log(`✅ Files updated: ${processedCount}`)
  console.log(`❌ Files with errors: ${errorCount}`)
  console.log(`⏭️  Files skipped (no changes needed): ${allFiles.length - processedCount - errorCount}`)
  
  if (processedFiles.length > 0) {
    console.log('\n📝 Updated files:')
    processedFiles.forEach(file => console.log(`   - ${file}`))
  }
  
  if (errorFiles.length > 0) {
    console.log('\n❌ Files with errors:')
    errorFiles.forEach(({ file, error }) => console.log(`   - ${file}: ${error}`))
  }
  
  // Additional fixes
  console.log('\n🔧 Applying additional fixes...')
  
  // Update ecommerce index.ts to export from new prisma location
  const ecommerceIndexPath = 'lib/ecommerce/index.ts'
  if (fs.existsSync(ecommerceIndexPath)) {
    try {
      let content = fs.readFileSync(ecommerceIndexPath, 'utf8')
      const oldExport = "import { prisma, db, withTransaction, isHealthy, connectDatabase, disconnectDatabase } from './config/database'"
      const newExport = "import { prisma, withTransaction, healthCheck as isHealthy, connectDatabase, disconnectDatabase } from '@/lib/prisma'"
      
      if (content.includes(oldExport)) {
        content = content.replace(oldExport, newExport)
        content = content.replace(
          "export { prisma, db, withTransaction, isHealthy, connectDatabase, disconnectDatabase } from './config/database'",
          "export { prisma, withTransaction, healthCheck as isHealthy, connectDatabase, disconnectDatabase } from '@/lib/prisma'"
        )
        fs.writeFileSync(ecommerceIndexPath, content, 'utf8')
        console.log(`✅ Updated ${ecommerceIndexPath}`)
      }
    } catch (error) {
      console.log(`❌ Error updating ${ecommerceIndexPath}: ${error.message}`)
    }
  }
  
  console.log('\n🎉 Prisma import standardization complete!')
  console.log('\n📋 Next steps:')
  console.log('   1. Run: npm run build (or pnpm build)')
  console.log('   2. Test your application')
  console.log('   3. Run: npx prisma generate (if needed)')
  console.log('   4. Commit your changes')
  
  if (errorCount > 0) {
    console.log('\n⚠️  Please review and manually fix the files with errors.')
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = { findFiles, hasOldImports, replaceImports, processFile }
