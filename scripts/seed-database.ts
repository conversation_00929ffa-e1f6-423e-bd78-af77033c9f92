#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client'
import { faker } from '@faker-js/faker'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

// South African specific data
const southAfricanCities = [
  'Cape Town', 'Johannesburg', 'Durban', 'Pretoria', 'Port Elizabeth',
  'Bloemfontein', 'East London', 'Pietermaritzburg', 'Kimberley', 'Polokwane'
]

const southAfricanProvinces = [
  'Western Cape', 'Gauteng', 'KwaZulu-Natal', 'Eastern Cape', 'Free State',
  'Limpopo', 'Mpumalanga', 'Northern Cape', 'North West'
]

const southAfricanPostalCodes = [
  '8001', '2000', '4000', '0001', '6000', '9300', '5200', '3200', '8300', '0700'
]

async function cleanDatabase() {
  console.log('🧹 Cleaning existing data...')

  // Clean in reverse dependency order
  await prisma.couponUsage.deleteMany()
  await prisma.appliedDiscount.deleteMany()
  await prisma.orderRefundItem.deleteMany()
  await prisma.orderRefund.deleteMany()
  await prisma.orderReturnItem.deleteMany()
  await prisma.orderReturn.deleteMany()
  await prisma.orderFulfillmentItem.deleteMany()
  await prisma.orderFulfillment.deleteMany()
  await prisma.orderItem.deleteMany()
  await prisma.payment.deleteMany()
  await prisma.order.deleteMany()
  await prisma.cartItem.deleteMany()
  await prisma.cart.deleteMany()
  await prisma.wishlistItem.deleteMany()
  await prisma.wishlist.deleteMany()
  await prisma.productReview.deleteMany()
  await prisma.productAttributeValue.deleteMany()
  await prisma.productVariantOption.deleteMany()
  await prisma.productVariant.deleteMany()
  await prisma.productImage.deleteMany()
  await prisma.productTagRelation.deleteMany()
  await prisma.productCollectionRelation.deleteMany()
  await prisma.productCategoryRelation.deleteMany()
  await prisma.inventoryMovement.deleteMany()
  await prisma.inventoryItem.deleteMany()
  await prisma.product.deleteMany()
  await prisma.productTag.deleteMany()
  await prisma.productCollection.deleteMany()
  await prisma.productCategory.deleteMany()
  await prisma.productAttribute.deleteMany()
  await prisma.inventoryLocation.deleteMany()
  await prisma.pageBlock.deleteMany()
  await prisma.pageVersion.deleteMany()
  await prisma.page.deleteMany()
  await prisma.blockType.deleteMany()
  await prisma.pageTemplate.deleteMany()
  await prisma.postTaxonomyTerm.deleteMany()
  await prisma.postMeta.deleteMany()
  await prisma.postComment.deleteMany()
  await prisma.postRevision.deleteMany()
  await prisma.postBlock.deleteMany()
  await prisma.post.deleteMany()
  await prisma.taxonomyTerm.deleteMany()
  await prisma.taxonomy.deleteMany()
  await prisma.postType.deleteMany()
  await prisma.blogPostTag.deleteMany()
  await prisma.blogPostCategory.deleteMany()
  await prisma.blogComment.deleteMany()
  await prisma.blogPost.deleteMany()
  await prisma.blogTag.deleteMany()
  await prisma.blogCategory.deleteMany()
  await prisma.themeApplication.deleteMany()
  await prisma.theme.deleteMany()
  await prisma.workflowExecution.deleteMany()
  await prisma.workflow.deleteMany()
  await prisma.layoutBlock.deleteMany()
  await prisma.layoutSection.deleteMany()
  await prisma.layoutAssignment.deleteMany()
  await prisma.layoutVersion.deleteMany()
  await prisma.layout.deleteMany()
  await prisma.navigationMenu.deleteMany()
  await prisma.widgetArea.deleteMany()
  await prisma.adminActivity.deleteMany()
  await prisma.adminSession.deleteMany()
  await prisma.adminUserRole.deleteMany()
  await prisma.adminUser.deleteMany()
  await prisma.adminRole.deleteMany()
  await prisma.userActivity.deleteMany()
  await prisma.userSession.deleteMany()
  await prisma.userAddress.deleteMany()
  await prisma.userRole.deleteMany()
  await prisma.user.deleteMany()
  await prisma.permission.deleteMany()
  await prisma.role.deleteMany()
  await prisma.paymentMethod.deleteMany()
  await prisma.customerAddress.deleteMany()
  await prisma.customer.deleteMany()
  await prisma.coupon.deleteMany()
  await prisma.newsletterSubscriber.deleteMany()
  await prisma.siteSettings.deleteMany()
}

async function seedDatabase() {
  console.log('🌱 Starting comprehensive database seeding...')

  try {
    // Clean existing data (in development only)
    if (process.env.NODE_ENV !== 'production') {
      await cleanDatabase()
    }

    // 1. Create Roles and Permissions
    console.log('🔐 Creating roles and permissions...')
    const permissions = await Promise.all([
      prisma.permission.upsert({
        where: { resource_action: { resource: 'product', action: 'create' } },
        update: {},
        create: { name: 'Create Products', resource: 'product', action: 'create', description: 'Create new products' }
      }),
      prisma.permission.upsert({
        where: { resource_action: { resource: 'product', action: 'update' } },
        update: {},
        create: { name: 'Edit Products', resource: 'product', action: 'update', description: 'Edit existing products' }
      }),
      prisma.permission.upsert({
        where: { resource_action: { resource: 'product', action: 'delete' } },
        update: {},
        create: { name: 'Delete Products', resource: 'product', action: 'delete', description: 'Delete products' }
      }),
      prisma.permission.upsert({
        where: { resource_action: { resource: 'order', action: 'read' } },
        update: {},
        create: { name: 'View Orders', resource: 'order', action: 'read', description: 'View customer orders' }
      }),
      prisma.permission.upsert({
        where: { resource_action: { resource: 'order', action: 'update' } },
        update: {},
        create: { name: 'Manage Orders', resource: 'order', action: 'update', description: 'Manage order status' }
      }),
      prisma.permission.upsert({
        where: { resource_action: { resource: 'user', action: 'manage' } },
        update: {},
        create: { name: 'Manage Users', resource: 'user', action: 'manage', description: 'Manage user accounts' }
      }),
      prisma.permission.upsert({
        where: { resource_action: { resource: 'page', action: 'manage' } },
        update: {},
        create: { name: 'Manage Pages', resource: 'page', action: 'manage', description: 'Manage website pages' }
      }),
      prisma.permission.upsert({
        where: { resource_action: { resource: 'analytics', action: 'read' } },
        update: {},
        create: { name: 'View Analytics', resource: 'analytics', action: 'read', description: 'View site analytics' }
      })
    ])

    const roles = await Promise.all([
      prisma.role.upsert({
        where: { slug: 'super-admin' },
        update: {},
        create: {
          name: 'Super Admin',
          slug: 'super-admin',
          description: 'Full system access',
          level: 1,
          isSystem: true,
          permissions: { connect: permissions.map(p => ({ id: p.id })) }
        }
      }),
      prisma.role.upsert({
        where: { slug: 'admin' },
        update: {},
        create: {
          name: 'Admin',
          slug: 'admin',
          description: 'Administrative access',
          level: 2,
          permissions: { connect: permissions.slice(0, 6).map(p => ({ id: p.id })) }
        }
      }),
      prisma.role.upsert({
        where: { slug: 'manager' },
        update: {},
        create: {
          name: 'Manager',
          slug: 'manager',
          description: 'Store manager access',
          level: 3,
          permissions: { connect: permissions.slice(0, 5).map(p => ({ id: p.id })) }
        }
      }),
      prisma.role.upsert({
        where: { slug: 'customer' },
        update: {},
        create: {
          name: 'Customer',
          slug: 'customer',
          description: 'Customer account',
          level: 10
        }
      })
    ])

    // 2. Create Admin Roles and Users
    console.log('👨‍💼 Creating admin users...')
    const adminRoles = await Promise.all([
      prisma.adminRole.upsert({
        where: { name: 'Super Administrator' },
        update: {},
        create: {
          name: 'Super Administrator',
          description: 'Full system access',
          permissions: ['*'],
          isSystemRole: true
        }
      }),
      prisma.adminRole.upsert({
        where: { name: 'Store Manager' },
        update: {},
        create: {
          name: 'Store Manager',
          description: 'Store management access',
          permissions: ['products.*', 'orders.*', 'customers.*', 'inventory.*']
        }
      }),
      prisma.adminRole.upsert({
        where: { name: 'Content Manager' },
        update: {},
        create: {
          name: 'Content Manager',
          description: 'Content and page management',
          permissions: ['pages.*', 'posts.*', 'media.*']
        }
      })
    ])

    const adminUsers = await Promise.all([
      prisma.adminUser.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          firstName: 'System',
          lastName: 'Administrator',
          displayName: 'System Admin',
          passwordHash: await bcrypt.hash('admin123!', 12),
          isActive: true,
          isEmailVerified: true,
          roles: { create: { roleId: adminRoles[0].id } }
        }
      }),
      prisma.adminUser.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          firstName: 'Store',
          lastName: 'Manager',
          displayName: 'Store Manager',
          passwordHash: await bcrypt.hash('manager123!', 12),
          isActive: true,
          isEmailVerified: true,
          roles: { create: { roleId: adminRoles[1].id } }
        }
      })
    ])

    // 3. Create Inventory Locations
    console.log('📍 Creating inventory locations...')
    const inventoryLocations = await Promise.all([
      prisma.inventoryLocation.upsert({
        where: { code: 'WH-CPT-001' },
        update: {},
        create: {
          name: 'Main Warehouse - Cape Town',
          code: 'WH-CPT-001',
          type: 'warehouse',
          address: {
            street: '123 Industrial Road, Montague Gardens',
            city: 'Cape Town',
            province: 'Western Cape',
            postalCode: '7441',
            country: 'South Africa'
          },
          contactName: 'Warehouse Manager',
          contactEmail: '<EMAIL>',
          contactPhone: '+27 21 555 0123',
          isActive: true,
          isPrimary: true,
          allowsInventory: true,
          allowsFulfillment: true,
          maxCapacity: 10000,
          currentUtilization: 3500
        }
      }),
      prisma.inventoryLocation.upsert({
        where: { code: 'DC-JHB-001' },
        update: {},
        create: {
          name: 'Johannesburg Distribution Center',
          code: 'DC-JHB-001',
          type: 'warehouse',
          address: {
            street: '456 Logistics Avenue, Midrand',
            city: 'Johannesburg',
            province: 'Gauteng',
            postalCode: '1685',
            country: 'South Africa'
          },
          contactName: 'Distribution Manager',
          contactEmail: '<EMAIL>',
          contactPhone: '+27 11 555 0456',
          isActive: true,
          isPrimary: false,
          allowsInventory: true,
          allowsFulfillment: true,
          maxCapacity: 5000,
          currentUtilization: 1200
        }
      }),
      prisma.inventoryLocation.upsert({
        where: { code: 'ST-DBN-001' },
        update: {},
        create: {
          name: 'Durban Store',
          code: 'ST-DBN-001',
          type: 'store',
          address: {
            street: '789 Gateway Mall, Umhlanga',
            city: 'Durban',
            province: 'KwaZulu-Natal',
            postalCode: '4319',
            country: 'South Africa'
          },
          contactName: 'Store Manager',
          contactEmail: '<EMAIL>',
          contactPhone: '+27 31 555 0789',
          isActive: true,
          isPrimary: false,
          allowsInventory: true,
          allowsFulfillment: true,
          maxCapacity: 500,
          currentUtilization: 350
        }
      })
    ])

    const mainWarehouse = inventoryLocations[0]

    // 4. Create Product Categories with Hierarchy
    console.log('📂 Creating product categories...')
    const mainCategories = await Promise.all([
      prisma.productCategory.upsert({
        where: { slug: 'boys-clothing' },
        update: {},
        create: {
          name: 'Boys Clothing',
          slug: 'boys-clothing',
          description: 'Stylish and comfortable clothing for boys of all ages',
          isVisible: true,
          position: 1,
          seoTitle: 'Boys Clothing | Coco Milk Kids',
          seoDescription: 'Shop the latest boys clothing collection at Coco Milk Kids. Quality, comfort and style for your little ones.'
        }
      }),
      prisma.productCategory.upsert({
        where: { slug: 'girls-clothing' },
        update: {},
        create: {
          name: 'Girls Clothing',
          slug: 'girls-clothing',
          description: 'Beautiful and trendy clothing for girls',
          isVisible: true,
          position: 2,
          seoTitle: 'Girls Clothing | Coco Milk Kids',
          seoDescription: 'Discover our gorgeous girls clothing range. From dresses to casual wear, find the perfect outfit.'
        }
      }),
      prisma.productCategory.upsert({
        where: { slug: 'baby-clothing' },
        update: {},
        create: {
          name: 'Baby Clothing',
          slug: 'baby-clothing',
          description: 'Soft and comfortable clothing for babies and toddlers',
          isVisible: true,
          position: 3,
          seoTitle: 'Baby Clothing | Coco Milk Kids',
          seoDescription: 'Gentle, organic baby clothing designed for comfort and safety. Perfect for your precious little one.'
        }
      }),
      prisma.productCategory.upsert({
        where: { slug: 'school-uniforms' },
        update: {},
        create: {
          name: 'School Uniforms',
          slug: 'school-uniforms',
          description: 'Quality school uniforms that meet school requirements',
          isVisible: true,
          position: 4,
          seoTitle: 'School Uniforms | Coco Milk Kids',
          seoDescription: 'High-quality school uniforms for South African schools. Durable, comfortable and affordable.'
        }
      }),
      prisma.productCategory.upsert({
        where: { slug: 'accessories' },
        update: {},
        create: {
          name: 'Accessories',
          slug: 'accessories',
          description: 'Kids accessories, shoes, and extras',
          isVisible: true,
          position: 5,
          seoTitle: 'Kids Accessories | Coco Milk Kids',
          seoDescription: 'Complete your child\'s look with our range of accessories, shoes, and fun extras.'
        }
      })
    ])

    // Create subcategories
    const subCategories = await Promise.all([
      // Boys subcategories
      prisma.productCategory.upsert({
        where: { slug: 'boys-t-shirts' },
        update: {},
        create: {
          name: 'Boys T-Shirts',
          slug: 'boys-t-shirts',
          description: 'Comfortable t-shirts for boys',
          parentId: mainCategories[0].id,
          isVisible: true,
          position: 1
        }
      }),
      prisma.productCategory.upsert({
        where: { slug: 'boys-shorts' },
        update: {},
        create: {
          name: 'Boys Shorts',
          slug: 'boys-shorts',
          description: 'Casual and sports shorts for boys',
          parentId: mainCategories[0].id,
          isVisible: true,
          position: 2
        }
      }),
      prisma.productCategory.upsert({
        where: { slug: 'boys-jeans' },
        update: {},
        create: {
          name: 'Boys Jeans',
          slug: 'boys-jeans',
          description: 'Durable denim jeans for boys',
          parentId: mainCategories[0].id,
          isVisible: true,
          position: 3
        }
      }),
      // Girls subcategories
      prisma.productCategory.upsert({
        where: { slug: 'girls-dresses' },
        update: {},
        create: {
          name: 'Girls Dresses',
          slug: 'girls-dresses',
          description: 'Beautiful dresses for special occasions and everyday wear',
          parentId: mainCategories[1].id,
          isVisible: true,
          position: 1
        }
      }),
      prisma.productCategory.upsert({
        where: { slug: 'girls-tops' },
        update: {},
        create: {
          name: 'Girls Tops',
          slug: 'girls-tops',
          description: 'Stylish tops and blouses for girls',
          parentId: mainCategories[1].id,
          isVisible: true,
          position: 2
        }
      }),
      prisma.productCategory.upsert({
        where: { slug: 'girls-skirts' },
        update: {},
        create: {
          name: 'Girls Skirts',
          slug: 'girls-skirts',
          description: 'Cute and comfortable skirts',
          parentId: mainCategories[1].id,
          isVisible: true,
          position: 3
        }
      }),
      // Baby subcategories
      prisma.productCategory.upsert({
        where: { slug: 'baby-onesies' },
        update: {},
        create: {
          name: 'Baby Onesies',
          slug: 'baby-onesies',
          description: 'Soft cotton onesies for babies',
          parentId: mainCategories[2].id,
          isVisible: true,
          position: 1
        }
      }),
      prisma.productCategory.upsert({
        where: { slug: 'baby-sets' },
        update: {},
        create: {
          name: 'Baby Sets',
          slug: 'baby-sets',
          description: 'Coordinated baby clothing sets',
          parentId: mainCategories[2].id,
          isVisible: true,
          position: 2
        }
      })
    ])

    const categories = [...mainCategories, ...subCategories]

    // 5. Create Product Collections
    console.log('📚 Creating product collections...')
    const collections = await Promise.all([
      prisma.productCollection.upsert({
        where: { slug: 'new-arrivals' },
        update: {},
        create: {
          title: 'New Arrivals',
          slug: 'new-arrivals',
          description: 'Latest additions to our collection - fresh styles for the season',
          sortOrder: 'created',
          isVisible: true,
          seoTitle: 'New Arrivals | Coco Milk Kids',
          seoDescription: 'Discover the latest kids clothing arrivals at Coco Milk Kids. Fresh styles and trending designs.'
        }
      }),
      prisma.productCollection.upsert({
        where: { slug: 'best-sellers' },
        update: {},
        create: {
          title: 'Best Sellers',
          slug: 'best-sellers',
          description: 'Our most popular items loved by parents and kids alike',
          sortOrder: 'best-selling',
          isVisible: true,
          seoTitle: 'Best Sellers | Coco Milk Kids',
          seoDescription: 'Shop our best-selling kids clothing. Proven favorites that parents love and kids enjoy wearing.'
        }
      }),
      prisma.productCollection.upsert({
        where: { slug: 'summer-collection' },
        update: {},
        create: {
          title: 'Summer Collection',
          slug: 'summer-collection',
          description: 'Light, breathable clothing perfect for South African summers',
          sortOrder: 'manual',
          isVisible: true,
          seoTitle: 'Summer Collection | Coco Milk Kids',
          seoDescription: 'Beat the heat with our summer collection. Lightweight, comfortable clothing for hot days.'
        }
      }),
      prisma.productCollection.upsert({
        where: { slug: 'heritage-day-special' },
        update: {},
        create: {
          title: 'Heritage Day Special',
          slug: 'heritage-day-special',
          description: 'Celebrate South African heritage with traditional-inspired designs',
          sortOrder: 'manual',
          isVisible: true,
          seoTitle: 'Heritage Day Collection | Coco Milk Kids',
          seoDescription: 'Celebrate South African culture with our Heritage Day collection. Traditional designs with modern comfort.'
        }
      }),
      prisma.productCollection.upsert({
        where: { slug: 'sale-items' },
        update: {},
        create: {
          title: 'Sale Items',
          slug: 'sale-items',
          description: 'Quality clothing at discounted prices - limited time offers',
          sortOrder: 'price-asc',
          isVisible: true,
          seoTitle: 'Sale Items | Coco Milk Kids',
          seoDescription: 'Great deals on kids clothing. Quality items at discounted prices - shop now while stocks last!'
        }
      }),
      prisma.productCollection.upsert({
        where: { slug: 'organic-cotton' },
        update: {},
        create: {
          title: 'Organic Cotton',
          slug: 'organic-cotton',
          description: 'Eco-friendly organic cotton clothing for conscious parents',
          sortOrder: 'manual',
          isVisible: true,
          seoTitle: 'Organic Cotton Kids Clothing | Coco Milk Kids',
          seoDescription: 'Sustainable, organic cotton clothing for kids. Gentle on skin and kind to the environment.'
        }
      }),
      prisma.productCollection.upsert({
        where: { slug: 'back-to-school' },
        update: {},
        create: {
          title: 'Back to School',
          slug: 'back-to-school',
          description: 'Everything you need for the new school year',
          sortOrder: 'manual',
          isVisible: true,
          seoTitle: 'Back to School Collection | Coco Milk Kids',
          seoDescription: 'Get ready for school with our back-to-school collection. Uniforms, casual wear, and accessories.'
        }
      })
    ])

    // 6. Create Product Tags
    console.log('🏷️ Creating product tags...')
    const productTags = await Promise.all([
      prisma.productTag.upsert({
        where: { slug: 'organic' },
        update: {},
        create: { name: 'Organic', slug: 'organic', description: 'Made from organic materials' }
      }),
      prisma.productTag.upsert({
        where: { slug: 'cotton' },
        update: {},
        create: { name: 'Cotton', slug: 'cotton', description: '100% cotton fabric' }
      }),
      prisma.productTag.upsert({
        where: { slug: 'comfortable' },
        update: {},
        create: { name: 'Comfortable', slug: 'comfortable', description: 'Extra comfortable fit' }
      }),
      prisma.productTag.upsert({
        where: { slug: 'durable' },
        update: {},
        create: { name: 'Durable', slug: 'durable', description: 'Built to last' }
      }),
      prisma.productTag.upsert({
        where: { slug: 'trendy' },
        update: {},
        create: { name: 'Trendy', slug: 'trendy', description: 'Latest fashion trends' }
      }),
      prisma.productTag.upsert({
        where: { slug: 'casual' },
        update: {},
        create: { name: 'Casual', slug: 'casual', description: 'Perfect for everyday wear' }
      }),
      prisma.productTag.upsert({
        where: { slug: 'formal' },
        update: {},
        create: { name: 'Formal', slug: 'formal', description: 'Suitable for special occasions' }
      }),
      prisma.productTag.upsert({
        where: { slug: 'unisex' },
        update: {},
        create: { name: 'Unisex', slug: 'unisex', description: 'Suitable for both boys and girls' }
      }),
      prisma.productTag.upsert({
        where: { slug: 'machine-washable' },
        update: {},
        create: { name: 'Machine Washable', slug: 'machine-washable', description: 'Easy care - machine washable' }
      }),
      prisma.productTag.upsert({
        where: { slug: 'south-african-made' },
        update: {},
        create: { name: 'South African Made', slug: 'south-african-made', description: 'Proudly made in South Africa' }
      })
    ])

    // 7. Create Product Attributes
    console.log('🎨 Creating product attributes...')
    const productAttributes = await Promise.all([
      prisma.productAttribute.upsert({
        where: { slug: 'size' },
        update: {},
        create: {
          name: 'Size',
          slug: 'size',
          type: 'select',
          description: 'Clothing size',
          isRequired: true,
          isVariant: true,
          isFilter: true,
          position: 1,
          options: ['0-3M', '3-6M', '6-9M', '9-12M', '12-18M', '18-24M', '2T', '3T', '4T', '5T', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16']
        }
      }),
      prisma.productAttribute.upsert({
        where: { slug: 'color' },
        update: {},
        create: {
          name: 'Color',
          slug: 'color',
          type: 'color',
          description: 'Product color',
          isRequired: true,
          isVariant: true,
          isFilter: true,
          position: 2,
          options: ['Red', 'Blue', 'Green', 'Yellow', 'Pink', 'Purple', 'Orange', 'Black', 'White', 'Grey', 'Navy', 'Maroon']
        }
      }),
      prisma.productAttribute.upsert({
        where: { slug: 'material' },
        update: {},
        create: {
          name: 'Material',
          slug: 'material',
          type: 'select',
          description: 'Fabric material',
          isRequired: false,
          isVariant: false,
          isFilter: true,
          position: 3,
          options: ['100% Cotton', 'Cotton Blend', 'Polyester', 'Organic Cotton', 'Bamboo', 'Linen', 'Denim']
        }
      }),
      prisma.productAttribute.upsert({
        where: { slug: 'care-instructions' },
        update: {},
        create: {
          name: 'Care Instructions',
          slug: 'care-instructions',
          type: 'multiselect',
          description: 'How to care for the product',
          isRequired: false,
          isVariant: false,
          isFilter: false,
          position: 4,
          options: ['Machine Wash Cold', 'Tumble Dry Low', 'Do Not Bleach', 'Iron Low Heat', 'Dry Clean Only', 'Hand Wash Only']
        }
      }),
      prisma.productAttribute.upsert({
        where: { slug: 'season' },
        update: {},
        create: {
          name: 'Season',
          slug: 'season',
          type: 'select',
          description: 'Suitable season',
          isRequired: false,
          isVariant: false,
          isFilter: true,
          position: 5,
          options: ['Spring', 'Summer', 'Autumn', 'Winter', 'All Season']
        }
      })
    ])

    // 8. Create Sample Users
    console.log('👥 Creating sample users...')
    const users = await Promise.all([
      prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          firstName: 'Thabo',
          lastName: 'Mthembu',
          displayName: 'Thabo Mthembu',
          phone: '+27 82 123 4567',
          emailVerified: true,
          acceptsMarketing: true,
          preferredCurrency: 'ZAR',
          customerSince: faker.date.past({ years: 2 }),
          totalSpent: 2450.00,
          orderCount: 8,
          averageOrderValue: 306.25,
          lastOrderAt: faker.date.recent({ days: 30 }),
          loyaltyPoints: 245,
          loyaltyTier: 'Silver',
          isActive: true,
          addresses: {
            create: [
              {
                firstName: 'Thabo',
                lastName: 'Mthembu',
                address1: '123 Nelson Mandela Drive',
                address2: 'Unit 4B',
                city: 'Cape Town',
                province: 'Western Cape',
                country: 'South Africa',
                postalCode: '8001',
                phone: '+27 82 123 4567',
                type: 'both',
                isDefault: true,
                label: 'Home'
              }
            ]
          }
        }
      }),
      prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          firstName: 'Sarah',
          lastName: 'van der Merwe',
          displayName: 'Sarah van der Merwe',
          phone: '+27 83 987 6543',
          emailVerified: true,
          acceptsMarketing: false,
          preferredCurrency: 'ZAR',
          customerSince: faker.date.past({ years: 1 }),
          totalSpent: 1890.50,
          orderCount: 12,
          averageOrderValue: 157.54,
          lastOrderAt: faker.date.recent({ days: 15 }),
          loyaltyPoints: 189,
          loyaltyTier: 'Bronze',
          isActive: true,
          addresses: {
            create: [
              {
                firstName: 'Sarah',
                lastName: 'van der Merwe',
                address1: '456 Voortrekker Road',
                city: 'Stellenbosch',
                province: 'Western Cape',
                country: 'South Africa',
                postalCode: '7600',
                phone: '+27 83 987 6543',
                type: 'both',
                isDefault: true,
                label: 'Home'
              }
            ]
          }
        }
      }),
      prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          firstName: 'Nomsa',
          lastName: 'Dlamini',
          displayName: 'Nomsa Dlamini',
          phone: '+27 84 555 7890',
          emailVerified: true,
          acceptsMarketing: true,
          preferredCurrency: 'ZAR',
          customerSince: faker.date.past({ years: 3 }),
          totalSpent: 4200.75,
          orderCount: 15,
          averageOrderValue: 280.05,
          lastOrderAt: faker.date.recent({ days: 7 }),
          loyaltyPoints: 420,
          loyaltyTier: 'Gold',
          isActive: true,
          addresses: {
            create: [
              {
                firstName: 'Nomsa',
                lastName: 'Dlamini',
                address1: '789 Jan Smuts Avenue',
                address2: 'Apartment 12C',
                city: 'Johannesburg',
                province: 'Gauteng',
                country: 'South Africa',
                postalCode: '2000',
                phone: '+27 84 555 7890',
                type: 'both',
                isDefault: true,
                label: 'Home'
              }
            ]
          }
        }
      }),
      prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          firstName: 'Pieter',
          lastName: 'Botha',
          displayName: 'Pieter Botha',
          phone: '+27 71 234 5678',
          emailVerified: true,
          acceptsMarketing: true,
          preferredCurrency: 'ZAR',
          customerSince: faker.date.past({ years: 1 }),
          totalSpent: 980.25,
          orderCount: 4,
          averageOrderValue: 245.06,
          lastOrderAt: faker.date.recent({ days: 45 }),
          loyaltyPoints: 98,
          loyaltyTier: 'Bronze',
          isActive: true,
          addresses: {
            create: [
              {
                firstName: 'Pieter',
                lastName: 'Botha',
                address1: '321 Marine Drive',
                city: 'Durban',
                province: 'KwaZulu-Natal',
                country: 'South Africa',
                postalCode: '4000',
                phone: '+27 71 234 5678',
                type: 'both',
                isDefault: true,
                label: 'Home'
              }
            ]
          }
        }
      }),
      prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          firstName: 'Fatima',
          lastName: 'Patel',
          displayName: 'Fatima Patel',
          phone: '+27 72 345 6789',
          emailVerified: true,
          acceptsMarketing: true,
          preferredCurrency: 'ZAR',
          customerSince: faker.date.past({ years: 2 }),
          totalSpent: 3150.80,
          orderCount: 18,
          averageOrderValue: 175.04,
          lastOrderAt: faker.date.recent({ days: 10 }),
          loyaltyPoints: 315,
          loyaltyTier: 'Silver',
          isActive: true,
          addresses: {
            create: [
              {
                firstName: 'Fatima',
                lastName: 'Patel',
                address1: '654 Church Street',
                city: 'Pretoria',
                province: 'Gauteng',
                country: 'South Africa',
                postalCode: '0001',
                phone: '+27 72 345 6789',
                type: 'both',
                isDefault: true,
                label: 'Home'
              }
            ]
          }
        }
      })
    ])

    // Assign customer role to users (use first user as the assigner)
    for (let i = 0; i < users.length; i++) {
      const user = users[i]
      const existingUserRole = await prisma.userRole.findFirst({
        where: { userId: user.id, roleId: roles[3].id }
      })
      if (!existingUserRole) {
        await prisma.userRole.create({
          data: {
          userId: user.id,
          roleId: roles[3].id, // Customer role
          assignedBy: users[0].id // Use first user as assigner
        }
        })
      }
    }

    // 9. Create Sample Products with Realistic South African Kids Clothing
    console.log('👕 Creating sample products...')
    const products: any[] = []

    const productData = [
      // Boys Clothing
      {
        title: 'Safari Adventure Boys T-Shirt',
        description: 'Comfortable 100% organic cotton t-shirt featuring South African wildlife prints. Perfect for little explorers who love adventure and nature.',
        descriptionHtml: '<p>Comfortable <strong>100% organic cotton</strong> t-shirt featuring South African wildlife prints. Perfect for little explorers who love adventure and nature.</p><ul><li>Organic cotton fabric</li><li>Wildlife print design</li><li>Machine washable</li><li>Sizes 2-12 years</li></ul>',
        price: 189.99,
        compareAtPrice: 229.99,
        costPerItem: 95.00,
        categoryIndex: 5, // Boys T-Shirts subcategory
        collectionIndex: 0, // New Arrivals
        vendor: 'Coco Milk Kids',
        productType: 'T-Shirt',
        weight: 0.15,
        weightUnit: 'kg',
        seoKeywords: ['boys t-shirt', 'organic cotton', 'safari', 'wildlife', 'kids clothing'],
        tags: ['organic', 'cotton', 'comfortable', 'south-african-made'],
        images: [
          { url: '/images/products/safari-boys-tshirt-front.jpg', altText: 'Safari Adventure Boys T-Shirt - Front View' },
          { url: '/images/products/safari-boys-tshirt-back.jpg', altText: 'Safari Adventure Boys T-Shirt - Back View' },
          { url: '/images/products/safari-boys-tshirt-detail.jpg', altText: 'Safari Adventure Boys T-Shirt - Print Detail' }
        ],
        variants: [
          { size: '4', color: 'Khaki', sku: 'SAF-BOY-TSH-KHA-4', price: 189.99, inventory: 25 },
          { size: '6', color: 'Khaki', sku: 'SAF-BOY-TSH-KHA-6', price: 189.99, inventory: 30 },
          { size: '8', color: 'Khaki', sku: 'SAF-BOY-TSH-KHA-8', price: 189.99, inventory: 35 },
          { size: '4', color: 'Forest Green', sku: 'SAF-BOY-TSH-GRN-4', price: 189.99, inventory: 20 },
          { size: '6', color: 'Forest Green', sku: 'SAF-BOY-TSH-GRN-6', price: 189.99, inventory: 25 },
          { size: '8', color: 'Forest Green', sku: 'SAF-BOY-TSH-GRN-8', price: 189.99, inventory: 28 }
        ]
      },
      {
        title: 'Springbok Rugby Shorts',
        description: 'Durable rugby-style shorts inspired by the Springboks. Made from moisture-wicking fabric perfect for active boys.',
        descriptionHtml: '<p>Durable rugby-style shorts inspired by the <strong>Springboks</strong>. Made from moisture-wicking fabric perfect for active boys.</p><ul><li>Moisture-wicking fabric</li><li>Elastic waistband</li><li>Side pockets</li><li>Springbok emblem</li></ul>',
        price: 249.99,
        compareAtPrice: 299.99,
        costPerItem: 125.00,
        categoryIndex: 6, // Boys Shorts subcategory
        collectionIndex: 3, // Heritage Day Special
        vendor: 'Coco Milk Kids',
        productType: 'Shorts',
        weight: 0.25,
        weightUnit: 'kg',
        seoKeywords: ['boys shorts', 'rugby', 'springbok', 'sports', 'south africa'],
        tags: ['durable', 'comfortable', 'south-african-made'],
        images: [
          { url: '/images/products/springbok-shorts-front.jpg', altText: 'Springbok Rugby Shorts - Front View' },
          { url: '/images/products/springbok-shorts-side.jpg', altText: 'Springbok Rugby Shorts - Side View' }
        ],
        variants: [
          { size: '6', color: 'Green', sku: 'SPR-BOY-SHO-GRN-6', price: 249.99, inventory: 15 },
          { size: '8', color: 'Green', sku: 'SPR-BOY-SHO-GRN-8', price: 249.99, inventory: 20 },
          { size: '10', color: 'Green', sku: 'SPR-BOY-SHO-GRN-10', price: 249.99, inventory: 18 },
          { size: '6', color: 'Gold', sku: 'SPR-BOY-SHO-GLD-6', price: 249.99, inventory: 12 },
          { size: '8', color: 'Gold', sku: 'SPR-BOY-SHO-GLD-8', price: 249.99, inventory: 15 },
          { size: '10', color: 'Gold', sku: 'SPR-BOY-SHO-GLD-10', price: 249.99, inventory: 14 }
        ]
      },
      // Girls Clothing
      {
        title: 'Protea Princess Dress',
        description: 'Elegant dress featuring South Africa\'s national flower, the Protea. Perfect for special occasions and cultural celebrations.',
        descriptionHtml: '<p>Elegant dress featuring South Africa\'s national flower, the <strong>Protea</strong>. Perfect for special occasions and cultural celebrations.</p><ul><li>Protea floral print</li><li>Comfortable cotton blend</li><li>Twirl-worthy skirt</li><li>Button-back closure</li></ul>',
        price: 349.99,
        compareAtPrice: 429.99,
        costPerItem: 175.00,
        categoryIndex: 8, // Girls Dresses subcategory
        collectionIndex: 3, // Heritage Day Special
        vendor: 'Coco Milk Kids',
        productType: 'Dress',
        weight: 0.3,
        weightUnit: 'kg',
        seoKeywords: ['girls dress', 'protea', 'south africa', 'special occasion', 'floral'],
        tags: ['trendy', 'comfortable', 'formal', 'south-african-made'],
        images: [
          { url: '/images/products/protea-dress-front.jpg', altText: 'Protea Princess Dress - Front View' },
          { url: '/images/products/protea-dress-twirl.jpg', altText: 'Protea Princess Dress - Twirl View' },
          { url: '/images/products/protea-dress-detail.jpg', altText: 'Protea Princess Dress - Print Detail' }
        ],
        variants: [
          { size: '4', color: 'Pink', sku: 'PRO-GIR-DRE-PNK-4', price: 349.99, inventory: 12 },
          { size: '6', color: 'Pink', sku: 'PRO-GIR-DRE-PNK-6', price: 349.99, inventory: 15 },
          { size: '8', color: 'Pink', sku: 'PRO-GIR-DRE-PNK-8', price: 349.99, inventory: 18 },
          { size: '4', color: 'Coral', sku: 'PRO-GIR-DRE-COR-4', price: 349.99, inventory: 10 },
          { size: '6', color: 'Coral', sku: 'PRO-GIR-DRE-COR-6', price: 349.99, inventory: 12 },
          { size: '8', color: 'Coral', sku: 'PRO-GIR-DRE-COR-8', price: 349.99, inventory: 14 }
        ]
      },
      {
        title: 'Table Mountain Sunset Top',
        description: 'Beautiful girls\' top featuring an artistic print of Cape Town\'s iconic Table Mountain at sunset.',
        descriptionHtml: '<p>Beautiful girls\' top featuring an artistic print of Cape Town\'s iconic <strong>Table Mountain</strong> at sunset.</p><ul><li>Artistic sunset print</li><li>Soft cotton fabric</li><li>Relaxed fit</li><li>Cap sleeves</li></ul>',
        price: 199.99,
        compareAtPrice: 249.99,
        costPerItem: 100.00,
        categoryIndex: 9, // Girls Tops subcategory
        collectionIndex: 2, // Summer Collection
        vendor: 'Coco Milk Kids',
        productType: 'Top',
        weight: 0.12,
        weightUnit: 'kg',
        seoKeywords: ['girls top', 'table mountain', 'cape town', 'sunset', 'artistic'],
        tags: ['trendy', 'comfortable', 'casual', 'south-african-made'],
        images: [
          { url: '/images/products/table-mountain-top-front.jpg', altText: 'Table Mountain Sunset Top - Front View' },
          { url: '/images/products/table-mountain-top-back.jpg', altText: 'Table Mountain Sunset Top - Back View' }
        ],
        variants: [
          { size: '6', color: 'Orange', sku: 'TBL-GIR-TOP-ORA-6', price: 199.99, inventory: 22 },
          { size: '8', color: 'Orange', sku: 'TBL-GIR-TOP-ORA-8', price: 199.99, inventory: 25 },
          { size: '10', color: 'Orange', sku: 'TBL-GIR-TOP-ORA-10', price: 199.99, inventory: 20 },
          { size: '6', color: 'Purple', sku: 'TBL-GIR-TOP-PUR-6', price: 199.99, inventory: 18 },
          { size: '8', color: 'Purple', sku: 'TBL-GIR-TOP-PUR-8', price: 199.99, inventory: 20 },
          { size: '10', color: 'Purple', sku: 'TBL-GIR-TOP-PUR-10', price: 199.99, inventory: 17 }
        ]
      },
      // Baby Clothing
      {
        title: 'Little Lion Onesie Set',
        description: 'Adorable 3-piece onesie set featuring cute lion designs. Made from the softest organic cotton for baby\'s delicate skin.',
        descriptionHtml: '<p>Adorable <strong>3-piece onesie set</strong> featuring cute lion designs. Made from the softest organic cotton for baby\'s delicate skin.</p><ul><li>3-piece set</li><li>Organic cotton</li><li>Snap closures</li><li>Machine washable</li></ul>',
        price: 299.99,
        compareAtPrice: 359.99,
        costPerItem: 150.00,
        categoryIndex: 11, // Baby Onesies subcategory
        collectionIndex: 0, // New Arrivals
        vendor: 'Coco Milk Kids',
        productType: 'Onesie Set',
        weight: 0.2,
        weightUnit: 'kg',
        seoKeywords: ['baby onesie', 'lion', 'organic cotton', 'baby set', 'newborn'],
        tags: ['organic', 'cotton', 'comfortable', 'unisex'],
        images: [
          { url: '/images/products/lion-onesie-set.jpg', altText: 'Little Lion Onesie Set - Complete Set' },
          { url: '/images/products/lion-onesie-detail.jpg', altText: 'Little Lion Onesie Set - Detail View' }
        ],
        variants: [
          { size: '0-3M', color: 'Yellow', sku: 'LIO-BAB-ONE-YEL-0-3', price: 299.99, inventory: 15 },
          { size: '3-6M', color: 'Yellow', sku: 'LIO-BAB-ONE-YEL-3-6', price: 299.99, inventory: 18 },
          { size: '6-9M', color: 'Yellow', sku: 'LIO-BAB-ONE-YEL-6-9', price: 299.99, inventory: 20 },
          { size: '0-3M', color: 'Beige', sku: 'LIO-BAB-ONE-BEI-0-3', price: 299.99, inventory: 12 },
          { size: '3-6M', color: 'Beige', sku: 'LIO-BAB-ONE-BEI-3-6', price: 299.99, inventory: 15 },
          { size: '6-9M', color: 'Beige', sku: 'LIO-BAB-ONE-BEI-6-9', price: 299.99, inventory: 17 }
        ]
      }
    ]

    for (const productInfo of productData) {
      const totalInventory = productInfo.variants.reduce((sum, variant) => sum + variant.inventory, 0)

      const product = await prisma.product.create({
        data: {
          title: productInfo.title,
          slug: productInfo.title.toLowerCase().replace(/\s+/g, '-').replace(/'/g, ''),
          description: productInfo.description,
          descriptionHtml: productInfo.descriptionHtml,
          vendor: productInfo.vendor,
          productType: productInfo.productType,
          handle: productInfo.title.toLowerCase().replace(/\s+/g, '-').replace(/'/g, ''),
          status: 'active',
          publishedAt: new Date(),
          price: productInfo.price,
          compareAtPrice: productInfo.compareAtPrice,
          costPerItem: productInfo.costPerItem,
          currency: 'ZAR',
          trackQuantity: true,
          inventoryQuantity: totalInventory,
          weight: productInfo.weight,
          weightUnit: productInfo.weightUnit,
          hasVariants: true,
          seoTitle: `${productInfo.title} | Coco Milk Kids`,
          seoDescription: productInfo.description.substring(0, 160),
          seoKeywords: productInfo.seoKeywords,
          isVisible: true,
          isAvailable: true,
          availableForSale: true,
          images: {
            create: productInfo.images.map((img, index) => ({
              url: img.url,
              altText: img.altText,
              position: index + 1
            }))
          },
          categories: {
            create: {
              categoryId: categories[productInfo.categoryIndex].id
            }
          },
          collections: {
            create: {
              collectionId: collections[productInfo.collectionIndex].id,
              position: products.length
            }
          },
          tags: {
            create: productInfo.tags.map(tagSlug => {
              const tag = productTags.find(t => t.slug === tagSlug)
              return tag ? { tagId: tag.id } : null
            }).filter((item): item is { tagId: string } => item !== null)
          }
        }
      })

      // Create product variants
      for (const variantInfo of productInfo.variants) {
        const existingVariant = await prisma.productVariant.findFirst({
          where: { sku: variantInfo.sku }
        })

        let variant = existingVariant
        if (!existingVariant) {
          variant = await prisma.productVariant.create({
            data: {
              productId: product.id,
              sku: variantInfo.sku,
              title: `${variantInfo.size} - ${variantInfo.color}`,
              price: variantInfo.price,
              currency: 'ZAR',
              weight: productInfo.weight,
              weightUnit: productInfo.weightUnit,
              inventoryQuantity: variantInfo.inventory,
              available: true,
              position: productInfo.variants.indexOf(variantInfo),
              trackQuantity: true,
              options: {
                create: [
                  { name: 'Size', value: variantInfo.size },
                  { name: 'Color', value: variantInfo.color }
                ]
              }
            }
          })
        }

        // Create inventory item for each variant
        const existingInventory = await prisma.inventoryItem.findFirst({
          where: { sku: variantInfo.sku }
        })
        if (!existingInventory && variant) {
          await prisma.inventoryItem.create({
            data: {
              productId: product.id,
              variantId: variant.id,
              sku: variantInfo.sku,
              name: `${product.title} - ${variantInfo.size} - ${variantInfo.color}`,
              quantity: variantInfo.inventory,
              availableQuantity: variantInfo.inventory,
              costPrice: productInfo.costPerItem,
              averageCost: productInfo.costPerItem,
              currency: 'ZAR',
              locationId: inventoryLocations[0].id, // Use main warehouse
              trackQuantity: true,
              lowStockThreshold: 5,
              reorderPoint: 10,
              reorderQuantity: 25
            }
          })
        }
      }

      // Create product attribute values
      await prisma.productAttributeValue.create({
        data: {
          productId: product.id,
          attributeId: productAttributes[2].id, // Material attribute
          value: productInfo.tags.includes('organic') ? 'Organic Cotton' : '100% Cotton'
        }
      })

      await prisma.productAttributeValue.create({
        data: {
          productId: product.id,
          attributeId: productAttributes[4].id, // Season attribute
          value: productInfo.collectionIndex === 2 ? 'Summer' : 'All Season'
        }
      })

      products.push(product)
    }

    // 10. Create Sample Orders
    console.log('🛒 Creating sample orders...')
    const sampleOrders = []

    for (let i = 0; i < 25; i++) {
      const user = users[faker.number.int({ min: 0, max: users.length - 1 })]
      const orderProducts = faker.helpers.arrayElements(products, { min: 1, max: 3 })

      let subtotal = 0
      const orderItems = []

      for (const product of orderProducts) {
        const quantity = faker.number.int({ min: 1, max: 3 })
        const unitPrice = Number(product.price)
        const totalPrice = unitPrice * quantity
        subtotal += totalPrice

        orderItems.push({
          productId: product.id,
          quantity,
          unitPrice,
          totalPrice,
          currency: 'ZAR',
          productTitle: product.title,
          productSlug: product.slug,
          fulfillableQuantity: quantity,
          returnableQuantity: quantity,
          refundableQuantity: quantity
        })
      }

      const totalTax = subtotal * 0.15 // 15% VAT
      const totalShipping = subtotal > 500 ? 0 : 65 // Free shipping over R500
      const total = subtotal + totalTax + totalShipping

      const sampleAddress = {
        firstName: user.firstName!,
        lastName: user.lastName!,
        address1: faker.location.streetAddress(),
        city: faker.helpers.arrayElement(southAfricanCities),
        province: faker.helpers.arrayElement(southAfricanProvinces),
        country: 'South Africa',
        postalCode: faker.helpers.arrayElement(southAfricanPostalCodes),
        phone: user.phone!
      }

      const orderNumber = `ORD${new Date().getFullYear().toString().slice(-2)}${(new Date().getMonth() + 1).toString().padStart(2, '0')}${new Date().getDate().toString().padStart(2, '0')}${(i + 1).toString().padStart(3, '0')}`

      const existingOrder = await prisma.order.findFirst({
        where: { orderNumber }
      })
      if (!existingOrder) {
        const status = faker.helpers.arrayElement(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'])
        const paymentStatus = faker.helpers.arrayElement(['pending', 'paid', 'failed', 'refunded'])
        const createdAt = faker.date.past({ years: 0.2 }) // About 60 days

        // Generate realistic dates based on status
        let confirmedAt = null
        let processedAt = null
        let shippedAt = null
        let deliveredAt = null

        if (['confirmed', 'processing', 'shipped', 'delivered'].includes(status)) {
          confirmedAt = faker.date.between({ from: createdAt, to: new Date(createdAt.getTime() + 24 * 60 * 60 * 1000) })
        }
        if (['processing', 'shipped', 'delivered'].includes(status)) {
          processedAt = faker.date.between({ from: confirmedAt!, to: new Date(confirmedAt!.getTime() + 48 * 60 * 60 * 1000) })
        }
        if (['shipped', 'delivered'].includes(status)) {
          shippedAt = faker.date.between({ from: processedAt!, to: new Date(processedAt!.getTime() + 72 * 60 * 60 * 1000) })
        }
        if (status === 'delivered') {
          deliveredAt = faker.date.between({ from: shippedAt!, to: new Date(shippedAt!.getTime() + 168 * 60 * 60 * 1000) })
        }

        const order = await prisma.order.create({
          data: {
            orderNumber,
            userId: user.id,
            customerEmail: user.email!,
            customerFirstName: user.firstName!,
            customerLastName: user.lastName!,
            customerPhone: user.phone!,
            billingAddress: sampleAddress,
            shippingAddress: sampleAddress,
            itemCount: orderItems.length,
            subtotal,
            totalTax,
            totalShipping,
            total,
            currency: 'ZAR',
            paymentStatus,
            status,
            source: faker.helpers.arrayElement(['web', 'mobile', 'admin']),
            customerNote: Math.random() < 0.3 ? faker.lorem.sentence() : null,
            internalNotes: Math.random() < 0.2 ? [faker.lorem.sentence()] : [],
            tags: Math.random() < 0.3 ? faker.helpers.arrayElements(['urgent', 'vip', 'gift'], { min: 1, max: 2 }) : [],
            createdAt,
            confirmedAt,
            processedAt,
            shippedAt,
            deliveredAt,
            items: {
              create: orderItems
            }
          }
        })
        sampleOrders.push(order)
      }
    }

    // 11. Create Coupons
    console.log('🎫 Creating coupons...')
    const coupons = await Promise.all([
      prisma.coupon.upsert({
        where: { code: 'WELCOME10' },
        update: {},
        create: {
          code: 'WELCOME10',
          name: 'Welcome Discount',
          description: '10% off your first order',
          type: 'percentage',
          value: 10,
          minimumOrderAmount: 200,
          usageLimit: 1000,
          customerUsageLimit: 1,
          isActive: true,
          startsAt: new Date(),
          expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
        }
      }),
      prisma.coupon.upsert({
        where: { code: 'SUMMER25' },
        update: {},
        create: {
          code: 'SUMMER25',
          name: 'Summer Sale',
          description: '25% off summer collection',
          type: 'percentage',
          value: 25,
          minimumOrderAmount: 300,
          usageLimit: 500,
          isActive: true,
          applicableCategories: [categories[1].id], // Girls Clothing
          startsAt: new Date(),
          expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000) // 3 months
        }
      }),
      prisma.coupon.upsert({
        where: { code: 'FREESHIP' },
        update: {},
        create: {
          code: 'FREESHIP',
          name: 'Free Shipping',
          description: 'Free shipping on orders over R300',
          type: 'free_shipping',
          value: 0,
          minimumOrderAmount: 300,
          usageLimit: 2000,
          isActive: true,
          startsAt: new Date(),
          expiresAt: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000) // 6 months
        }
      })
    ])

    // 12. Create Site Settings
    console.log('⚙️ Creating site settings...')
    const existingSettings = await prisma.siteSettings.findFirst()
    if (!existingSettings) {
      await prisma.siteSettings.create({
        data: {
          siteName: 'Coco Milk Kids',
          siteDescription: 'Premium South African kids clothing with comfort, style, and quality at the heart of everything we do.',
          siteUrl: 'https://cocomilkkids.com',
          logoUrl: '/images/logo.svg',
          faviconUrl: '/images/favicon.ico',
          socialMedia: {
            facebook: 'https://facebook.com/cocomilkkids',
            instagram: 'https://instagram.com/cocomilkkids',
            twitter: 'https://twitter.com/cocomilkkids'
          },
          seo: {
            defaultTitle: 'Coco Milk Kids - Premium South African Kids Clothing',
            defaultDescription: 'Shop premium kids clothing at Coco Milk Kids. Quality, comfort, and style for South African children.',
            keywords: ['kids clothing', 'south africa', 'children fashion', 'organic cotton', 'baby clothes']
          },
          ecommerce: {
            currency: 'ZAR',
            taxRate: 0.15,
            freeShippingThreshold: 500,
            shippingRate: 65
          }
        }
      })
    }

    // 13. Create Newsletter Subscribers
    console.log('📧 Creating newsletter subscribers...')
    const subscribers = []
    for (let i = 0; i < 25; i++) {
      const subscriber = await prisma.newsletterSubscriber.create({
        data: {
          email: faker.internet.email(),
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          isActive: faker.datatype.boolean(0.9), // 90% active
          preferences: {
            newArrivals: true,
            sales: faker.datatype.boolean(0.8),
            newsletter: true
          },
          subscribedAt: faker.date.past({ years: 1 })
        }
      })
      subscribers.push(subscriber)
    }

    console.log('✅ Comprehensive database seeding completed successfully!')
    console.log(`Created:`)
    console.log(`- ${permissions.length} permissions`)
    console.log(`- ${roles.length} user roles`)
    console.log(`- ${adminRoles.length} admin roles`)
    console.log(`- ${adminUsers.length} admin users`)
    console.log(`- ${inventoryLocations.length} inventory locations`)
    console.log(`- ${categories.length} product categories (including subcategories)`)
    console.log(`- ${collections.length} product collections`)
    console.log(`- ${productTags.length} product tags`)
    console.log(`- ${productAttributes.length} product attributes`)
    console.log(`- ${users.length} sample users with addresses`)
    console.log(`- ${products.length} sample products with variants and inventory`)
    console.log(`- ${sampleOrders.length} sample orders`)
    console.log(`- ${coupons.length} coupons`)
    console.log(`- ${subscribers.length} newsletter subscribers`)
    console.log(`- 1 site settings configuration`)

  } catch (error) {
    console.error('❌ Error seeding database:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seeder
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('🎉 Comprehensive seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Seeding failed:', error)
      process.exit(1)
    })
}

export { seedDatabase }
