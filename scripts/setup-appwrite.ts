#!/usr/bin/env tsx

/**
 * Appwrite Media Library Setup Script
 * 
 * This script sets up the complete Appwrite infrastructure for the media library:
 * - Creates storage bucket for media files
 * - Creates database for metadata
 * - Creates collection with proper schema
 * - Sets up indexes for optimal performance
 * 
 * Usage:
 *   pnpm setup:appwrite
 *   or
 *   npx tsx scripts/setup-appwrite.ts
 */

import { config } from 'dotenv'
import { appwriteServer } from '../lib/appwrite/server'

// Load environment variables
config({ path: '.env.local' })
config({ path: '.env' })

interface SetupOptions {
  force?: boolean
  verbose?: boolean
}

class AppwriteSetup {
  private options: SetupOptions

  constructor(options: SetupOptions = {}) {
    this.options = options
  }

  private log(message: string, type: 'info' | 'success' | 'error' | 'warning' = 'info') {
    if (!this.options.verbose && type === 'info') return

    const icons = {
      info: '🔵',
      success: '✅',
      error: '❌',
      warning: '⚠️'
    }

    console.log(`${icons[type]} ${message}`)
  }

  private validateEnvironment(): boolean {
    const requiredVars = [
      'NEXT_PUBLIC_APPWRITE_ENDPOINT',
      'NEXT_PUBLIC_APPWRITE_PROJECT_ID',
      'APPWRITE_API_KEY'
    ]

    const missingVars = requiredVars.filter(varName => !process.env[varName])

    if (missingVars.length > 0) {
      this.log('Missing required environment variables:', 'error')
      missingVars.forEach(varName => {
        this.log(`  - ${varName}`, 'error')
      })
      this.log('\nPlease set these variables in your .env.local file', 'error')
      return false
    }

    this.log('Environment variables validated', 'success')
    return true
  }

  private async checkExistingInfrastructure(): Promise<{
    bucketExists: boolean
    databaseExists: boolean
    collectionExists: boolean
  }> {
    const status = {
      bucketExists: false,
      databaseExists: false,
      collectionExists: false
    }

    try {
      await appwriteServer.getBucketInfo()
      status.bucketExists = true
      this.log('Media bucket already exists', 'info')
    } catch (error) {
      this.log('Media bucket does not exist', 'info')
    }

    // Add database and collection checks here if needed

    return status
  }

  async run(): Promise<void> {
    console.log('🚀 Appwrite Media Library Setup')
    console.log('================================\n')

    // Validate environment
    if (!this.validateEnvironment()) {
      process.exit(1)
    }

    // Check existing infrastructure
    const existing = await this.checkExistingInfrastructure()

    if (existing.bucketExists && !this.options.force) {
      this.log('Infrastructure already exists. Use --force to recreate.', 'warning')
      
      // Show current configuration
      try {
        const bucketInfo = await appwriteServer.getBucketInfo()
        console.log('\nCurrent Configuration:')
        console.log(`  Bucket ID: ${bucketInfo.$id}`)
        console.log(`  Bucket Name: ${bucketInfo.name}`)
        console.log(`  Max File Size: ${Math.round(bucketInfo.maximumFileSize / 1024 / 1024)}MB`)
        console.log(`  Allowed Extensions: ${bucketInfo.allowedFileExtensions?.length || 0} types`)
        console.log(`  File Security: ${bucketInfo.fileSecurity ? 'Enabled' : 'Disabled'}`)
        console.log(`  Encryption: ${bucketInfo.encryption ? 'Enabled' : 'Disabled'}`)
        console.log(`  Antivirus: ${bucketInfo.antivirus ? 'Enabled' : 'Disabled'}`)
      } catch (error) {
        this.log('Could not fetch bucket information', 'warning')
      }
      
      return
    }

    try {
      this.log('Setting up Appwrite media library infrastructure...', 'info')

      // Setup complete infrastructure
      const result = await appwriteServer.setupMediaLibrary()

      this.log('Setup completed successfully!', 'success')

      // Display setup summary
      console.log('\n📋 Setup Summary:')
      console.log('================')
      
      console.log('\n🗄️  Storage Bucket:')
      console.log(`   ID: ${result.bucket.$id}`)
      console.log(`   Name: ${result.bucket.name}`)
      console.log(`   Max Size: ${Math.round(result.bucket.maximumFileSize / 1024 / 1024)}MB`)
      console.log(`   Extensions: ${result.bucket.allowedFileExtensions?.length || 0} types`)
      console.log(`   Security: ${result.bucket.fileSecurity ? '🔒 Enabled' : '🔓 Disabled'}`)

      console.log('\n🗃️  Database:')
      console.log(`   ID: ${result.database.$id}`)
      console.log(`   Name: ${result.database.name}`)
      console.log(`   Status: ${result.database.enabled ? '✅ Enabled' : '❌ Disabled'}`)

      console.log('\n📊 Collection:')
      console.log(`   ID: ${result.collection.$id}`)
      console.log(`   Name: ${result.collection.name}`)
      console.log(`   Status: ${result.collection.enabled ? '✅ Enabled' : '❌ Disabled'}`)

      console.log('\n🎉 Your media library is ready to use!')
      console.log('\nNext steps:')
      console.log('1. Start your development server: pnpm dev')
      console.log('2. Navigate to your page builder')
      console.log('3. Try uploading files using the media field')

    } catch (error: any) {
      this.log(`Setup failed: ${error.message}`, 'error')
      
      if (error.code === 401) {
        console.log('\n🔑 Authentication Error:')
        console.log('- Check your APPWRITE_API_KEY')
        console.log('- Ensure the API key has the required scopes:')
        console.log('  • files.read, files.write')
        console.log('  • buckets.read, buckets.write')
        console.log('  • databases.read, databases.write')
        console.log('  • collections.read, collections.write')
      }

      if (error.code === 404) {
        console.log('\n🔍 Project Not Found:')
        console.log('- Check your NEXT_PUBLIC_APPWRITE_PROJECT_ID')
        console.log('- Ensure the project exists in your Appwrite console')
      }

      process.exit(1)
    }
  }
}

// Parse command line arguments
const args = process.argv.slice(2)
const options: SetupOptions = {
  force: args.includes('--force') || args.includes('-f'),
  verbose: args.includes('--verbose') || args.includes('-v')
}

// Show help
if (args.includes('--help') || args.includes('-h')) {
  console.log('Appwrite Media Library Setup')
  console.log('Usage: npx tsx scripts/setup-appwrite.ts [options]')
  console.log('')
  console.log('Options:')
  console.log('  --force, -f     Force recreate existing infrastructure')
  console.log('  --verbose, -v   Show detailed output')
  console.log('  --help, -h      Show this help message')
  console.log('')
  console.log('Environment Variables Required:')
  console.log('  NEXT_PUBLIC_APPWRITE_ENDPOINT')
  console.log('  NEXT_PUBLIC_APPWRITE_PROJECT_ID')
  console.log('  APPWRITE_API_KEY')
  process.exit(0)
}

// Run setup
const setup = new AppwriteSetup(options)
setup.run().catch((error) => {
  console.error('❌ Unexpected error:', error)
  process.exit(1)
})
