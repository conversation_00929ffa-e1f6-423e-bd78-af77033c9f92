#!/usr/bin/env node

/**
 * System Verification Script
 * Verifies that all modules are properly imported and configured
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 Verifying Coco Milk Kids E-commerce System...\n')

// Check if required files exist
const requiredFiles = [
  // Payment System
  'lib/payments/types.ts',
  'lib/payments/config.ts',
  'lib/payments/utils.ts',
  'lib/payments/logger.ts',
  'lib/payments/gateway-factory.ts',
  'lib/payments/gateways/payfast.ts',
  'lib/payments/gateways/ozow.ts',
  'lib/payments/index.ts',
  
  // Inventory System
  'lib/inventory/types.ts',
  'lib/inventory/config.ts',
  'lib/inventory/utils.ts',
  'lib/inventory/manager.ts',
  
  // Shipping System
  'lib/shipping/types.ts',
  'lib/shipping/config.ts',
  
  // Analytics System
  'lib/analytics/types.ts',
  'lib/analytics/manager.ts',
  'lib/analytics/utils.ts',
  
  // Notification System
  'lib/notifications/types.ts',
  
  // WordPress Integration
  'lib/wordpress/index.ts',
  'lib/wordpress/auth.ts',
  'lib/wordpress/config.ts',
  'lib/wordpress/woocommerce.ts',
  
  // API Routes
  'app/api/payments/route.ts',
  'app/api/payments/status/[transactionId]/route.ts',
  'app/api/inventory/route.ts',
  'app/api/inventory/stock-check/route.ts',
  'app/api/inventory/alerts/route.ts',
  'app/api/analytics/route.ts',
  'app/api/webhooks/payfast/route.ts',
  'app/api/webhooks/ozow/route.ts',
  'app/api/woocommerce/products/route.ts',
  'app/api/woocommerce/orders/route.ts',
  'app/api/woocommerce/customers/route.ts',
  'app/api/woocommerce/auth/route.ts',
  
  // Utilities
  'lib/rate-limit.ts',
  
  // Configuration
  '.env.example',
  'package.json',
  
  // Documentation
  'WOOCOMMERCE_SETUP.md',
  'PAYMENT_SETUP.md',
  'ERROR_FIXES_SUMMARY.md',
]

let missingFiles = []
let existingFiles = []

console.log('📁 Checking required files...')
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    existingFiles.push(file)
    console.log(`✅ ${file}`)
  } else {
    missingFiles.push(file)
    console.log(`❌ ${file} - MISSING`)
  }
})

console.log(`\n📊 File Check Results:`)
console.log(`✅ Existing: ${existingFiles.length}`)
console.log(`❌ Missing: ${missingFiles.length}`)

// Check package.json dependencies
console.log('\n📦 Checking package.json dependencies...')
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))

const requiredDependencies = [
  '@woocommerce/woocommerce-rest-api',
  'axios',
  'crypto-js',
  'uuid',
  'winston',
  'winston-daily-rotate-file',
  'nodemailer',
  'rate-limiter-flexible',
  'joi',
  'helmet',
  'cors',
  'express-rate-limit',
  'express-validator',
  'moment-timezone',
  'jsonwebtoken',
  'bcryptjs',
]

let missingDeps = []
let existingDeps = []

requiredDependencies.forEach(dep => {
  if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
    existingDeps.push(dep)
    console.log(`✅ ${dep}`)
  } else {
    missingDeps.push(dep)
    console.log(`❌ ${dep} - MISSING`)
  }
})

console.log(`\n📊 Dependency Check Results:`)
console.log(`✅ Installed: ${existingDeps.length}`)
console.log(`❌ Missing: ${missingDeps.length}`)

// Check environment variables template
console.log('\n🔧 Checking environment variables template...')
const envExample = fs.readFileSync('.env.example', 'utf8')

const requiredEnvVars = [
  'WORDPRESS_URL',
  'WORDPRESS_USERNAME',
  'WORDPRESS_PASSWORD',
  'PAYFAST_MERCHANT_ID',
  'PAYFAST_MERCHANT_KEY',
  'PAYFAST_PASSPHRASE',
  'OZOW_API_KEY',
  'OZOW_PRIVATE_KEY',
  'OZOW_SITE_CODE',
  'PAYMENT_ENCRYPTION_KEY',
  'SA_VAT_NUMBER',
]

let missingEnvVars = []
let existingEnvVars = []

requiredEnvVars.forEach(envVar => {
  if (envExample.includes(envVar)) {
    existingEnvVars.push(envVar)
    console.log(`✅ ${envVar}`)
  } else {
    missingEnvVars.push(envVar)
    console.log(`❌ ${envVar} - MISSING`)
  }
})

console.log(`\n📊 Environment Variables Check:`)
console.log(`✅ Configured: ${existingEnvVars.length}`)
console.log(`❌ Missing: ${missingEnvVars.length}`)

// Final summary
console.log('\n' + '='.repeat(60))
console.log('🎯 SYSTEM VERIFICATION SUMMARY')
console.log('='.repeat(60))

const totalIssues = missingFiles.length + missingDeps.length + missingEnvVars.length

if (totalIssues === 0) {
  console.log('🎉 SUCCESS: All systems are properly configured!')
  console.log('✅ All required files exist')
  console.log('✅ All dependencies are installed')
  console.log('✅ All environment variables are configured')
  console.log('\n🚀 The Coco Milk Kids e-commerce system is ready for deployment!')
} else {
  console.log(`⚠️  ISSUES FOUND: ${totalIssues} issues need attention`)
  
  if (missingFiles.length > 0) {
    console.log(`\n❌ Missing Files (${missingFiles.length}):`)
    missingFiles.forEach(file => console.log(`   - ${file}`))
  }
  
  if (missingDeps.length > 0) {
    console.log(`\n❌ Missing Dependencies (${missingDeps.length}):`)
    missingDeps.forEach(dep => console.log(`   - ${dep}`))
    console.log('\n💡 Run: pnpm install')
  }
  
  if (missingEnvVars.length > 0) {
    console.log(`\n❌ Missing Environment Variables (${missingEnvVars.length}):`)
    missingEnvVars.forEach(envVar => console.log(`   - ${envVar}`))
    console.log('\n💡 Update .env.example with missing variables')
  }
}

console.log('\n📚 Next Steps:')
console.log('1. Copy .env.example to .env.local')
console.log('2. Fill in all environment variables')
console.log('3. Set up payment gateway accounts')
console.log('4. Configure shipping providers')
console.log('5. Test all integrations')
console.log('6. Deploy to production')

console.log('\n📖 Documentation:')
console.log('- WooCommerce Setup: WOOCOMMERCE_SETUP.md')
console.log('- Payment Setup: PAYMENT_SETUP.md')
console.log('- Error Fixes: ERROR_FIXES_SUMMARY.md')

console.log('\n✨ Happy coding!')
