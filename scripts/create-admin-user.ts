#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function createAdminUser() {
  console.log('🔐 Creating admin user...')

  try {
    // Check if admin user already exists
    const existingAdmin = await prisma.adminUser.findFirst()
    if (existingAdmin) {
      console.log('✅ Admin user already exists')
      console.log(`Email: ${existingAdmin.email}`)
      console.log(`Display Name: ${existingAdmin.displayName}`)
      return
    }

    // Create default admin role
    const adminRole = await prisma.adminRole.upsert({
      where: { name: 'admin' },
      update: {},
      create: {
        name: 'admin',
        description: 'Full administrative access',
        permissions: [
          'products.read',
          'products.write',
          'products.delete',
          'orders.read',
          'orders.write',
          'orders.delete',
          'customers.read',
          'customers.write',
          'customers.delete',
          'analytics.read',
          'settings.read',
          'settings.write',
          'users.read',
          'users.write',
          'users.delete',
          'inventory.read',
          'inventory.write',
          'reports.read',
          'page-builder.read',
          'page-builder.write'
        ],
        isSystemRole: true
      }
    })

    console.log('✅ Admin role created/updated')

    // Create default admin user
    const hashedPassword = await bcrypt.hash('admin123', 12)
    const adminUser = await prisma.adminUser.create({
      data: {
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        displayName: 'Admin User',
        passwordHash: hashedPassword,
        isActive: true,
        isEmailVerified: true,
        timezone: 'Africa/Johannesburg',
        locale: 'en_ZA',
        roles: {
          create: {
            roleId: adminRole.id
          }
        }
      }
    })

    console.log('✅ Admin user created successfully!')
    console.log(`Email: ${adminUser.email}`)
    console.log(`Password: admin123`)
    console.log(`Display Name: ${adminUser.displayName}`)
    console.log('')
    console.log('🚀 You can now log in to the admin panel at /admin')

  } catch (error) {
    console.error('❌ Error creating admin user:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
if (require.main === module) {
  createAdminUser()
    .then(() => {
      console.log('🎉 Admin user setup completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Admin user setup failed:', error)
      process.exit(1)
    })
}

export { createAdminUser }
