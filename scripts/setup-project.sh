#!/bin/bash

# Coco Milk Kids E-commerce Setup Script
# This script sets up the complete project from scratch

set -e  # Exit on any error

echo "🚀 Setting up Coco Milk Kids E-commerce Platform..."
echo "=================================================="

# Check if required tools are installed
echo "🔍 Checking prerequisites..."

if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm is not installed. Installing pnpm..."
    npm install -g pnpm
fi

echo "✅ Prerequisites check passed"

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
pnpm install

# Check if .env file exists
if [ ! -f .env ]; then
    echo ""
    echo "⚠️  .env file not found. Please create one with the following variables:"
    echo "DATABASE_URL=\"postgresql://username:password@localhost:5432/cocomilk\""
    echo "JWT_SECRET=\"your-jwt-secret-here\""
    echo "NEXTAUTH_SECRET=\"your-nextauth-secret-here\""
    echo "OPENAI_API_KEY=\"your-openai-api-key\""
    echo "APPWRITE_PROJECT_ID=\"your-appwrite-project-id\""
    echo "APPWRITE_API_KEY=\"your-appwrite-api-key\""
    echo "PAYFAST_MERCHANT_ID=\"your-payfast-merchant-id\""
    echo "PAYFAST_MERCHANT_KEY=\"your-payfast-merchant-key\""
    echo "OZOW_SITE_CODE=\"your-ozow-site-code\""
    echo "OZOW_PRIVATE_KEY=\"your-ozow-private-key\""
    echo ""
    echo "Please create the .env file and run this script again."
    exit 1
fi

echo "✅ Dependencies installed"

# Setup database
echo ""
echo "🗄️  Setting up database..."
echo "Make sure your PostgreSQL database is running and accessible."

# Push database schema
echo "📋 Pushing database schema..."
pnpm prisma db push

echo "✅ Database schema created"

# Seed database with initial data
echo ""
echo "🌱 Seeding database with initial data..."
pnpm exec tsx scripts/seed-database.ts

echo "✅ Database seeded successfully"

# Create admin user
echo ""
echo "👤 Creating admin user..."
pnpm exec tsx scripts/create-admin-user.ts

echo "✅ Admin user created"

# Build the project to check for errors
echo ""
echo "🔨 Building project..."
pnpm build

echo "✅ Project built successfully"

# Final setup complete
echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "Your Coco Milk Kids e-commerce platform is ready!"
echo ""
echo "🚀 To start the development server:"
echo "   pnpm dev"
echo ""
echo "🌐 Access the application:"
echo "   Frontend: http://localhost:3090"
echo "   Admin Panel: http://localhost:3090/admin"
echo ""
echo "🔐 Admin Login Credentials:"
echo "   Email: <EMAIL>"
echo "   Password: admin123"
echo ""
echo "📚 Documentation:"
echo "   See PROJECT_COMPLETION_SUMMARY.md for detailed information"
echo ""
echo "✨ Happy coding!"
