#!/usr/bin/env tsx

import seedLandingPage from '../lib/seeders/landing-page-seeder'

async function main() {
  try {
    console.log('🚀 Starting Landing Page seeding process...')
    console.log('⚠️  This will update the existing landing page with new blocks.')
    
    await seedLandingPage()
    
    console.log('🎉 Landing Page seeding completed successfully!')
    console.log('')
    console.log('📄 Updated landing page with:')
    console.log('  • Hero Section Block')
    console.log('  • Featured Categories Block')
    console.log('  • New Arrivals Block')
    console.log('  • Editorial Grid Block')
    console.log('  • Editorial Section Block')
    console.log('  • Special Offers Banner Block')
    console.log('  • Newsletter Signup Block')
    console.log('')
    console.log('🎯 Next steps:')
    console.log('  1. Visit / to see the updated landing page')
    console.log('  2. Visit /admin/page-builder to manage the page')
    console.log('  3. Test all blocks and functionality')
    
    process.exit(0)
  } catch (error) {
    console.error('❌ Error during seeding:', error)
    process.exit(1)
  }
}

main()
