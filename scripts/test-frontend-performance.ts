import { performance } from 'perf_hooks'

const BASE_URL = 'http://localhost:3090'

interface TestResult {
  url: string
  status: number
  responseTime: number
  contentLength: number
  hasMetadata: boolean
  hasStructuredData: boolean
  cacheHeaders: string[]
  errors: string[]
}

async function testRoute(path: string): Promise<TestResult> {
  const url = `${BASE_URL}${path}`
  const start = performance.now()
  
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; Frontend-Test/1.0)'
      }
    })
    
    const end = performance.now()
    const responseTime = Math.round(end - start)
    const content = await response.text()
    
    // Check for metadata
    const hasMetadata = content.includes('<meta') && content.includes('og:')
    
    // Check for structured data
    const hasStructuredData = content.includes('application/ld+json')
    
    // Get cache headers
    const cacheHeaders = []
    if (response.headers.get('cache-control')) {
      cacheHeaders.push(`Cache-Control: ${response.headers.get('cache-control')}`)
    }
    if (response.headers.get('x-route-type')) {
      cacheHeaders.push(`X-Route-Type: ${response.headers.get('x-route-type')}`)
    }
    
    return {
      url,
      status: response.status,
      responseTime,
      contentLength: content.length,
      hasMetadata,
      hasStructuredData,
      cacheHeaders,
      errors: []
    }
  } catch (error) {
    const end = performance.now()
    return {
      url,
      status: 0,
      responseTime: Math.round(end - start),
      contentLength: 0,
      hasMetadata: false,
      hasStructuredData: false,
      cacheHeaders: [],
      errors: [error instanceof Error ? error.message : 'Unknown error']
    }
  }
}

async function testSEORoutes(): Promise<void> {
  console.log('🔍 Testing SEO Routes...\n')
  
  const seoRoutes = [
    '/sitemap.xml',
    '/robots.txt'
  ]
  
  for (const route of seoRoutes) {
    const result = await testRoute(route)
    console.log(`📄 ${route}:`)
    console.log(`   Status: ${result.status === 200 ? '✅' : '❌'} ${result.status}`)
    console.log(`   Response Time: ${result.responseTime}ms`)
    console.log(`   Content Length: ${result.contentLength} bytes`)
    if (result.errors.length > 0) {
      console.log(`   Errors: ${result.errors.join(', ')}`)
    }
    console.log()
  }
}

async function testContentRoutes(): Promise<void> {
  console.log('📝 Testing Content Routes...\n')
  
  const contentRoutes = [
    { path: '/about-us-test', type: 'Page' },
    { path: '/choosing-right-school-uniform', type: 'Blog Post' },
    { path: '/portfolio/summer-collection-2024', type: 'Portfolio Item' },
    { path: '/testimonial/amazing-quality-sarah-m', type: 'Testimonial' },
    { path: '/portfolio', type: 'Portfolio Archive' },
    { path: '/testimonial', type: 'Testimonial Archive' }
  ]
  
  for (const route of contentRoutes) {
    const result = await testRoute(route.path)
    console.log(`${getTypeIcon(route.type)} ${route.type} (${route.path}):`)
    console.log(`   Status: ${result.status === 200 ? '✅' : '❌'} ${result.status}`)
    console.log(`   Response Time: ${result.responseTime}ms`)
    console.log(`   Content Length: ${result.contentLength} bytes`)
    console.log(`   SEO Metadata: ${result.hasMetadata ? '✅' : '❌'}`)
    console.log(`   Structured Data: ${result.hasStructuredData ? '✅' : '❌'}`)
    if (result.cacheHeaders.length > 0) {
      console.log(`   Cache Headers: ${result.cacheHeaders.join(', ')}`)
    }
    if (result.errors.length > 0) {
      console.log(`   Errors: ${result.errors.join(', ')}`)
    }
    console.log()
  }
}

function getTypeIcon(type: string): string {
  switch (type) {
    case 'Page': return '📄'
    case 'Blog Post': return '📝'
    case 'Portfolio Item': return '🎨'
    case 'Testimonial': return '💬'
    case 'Portfolio Archive': return '📂'
    case 'Testimonial Archive': return '📂'
    default: return '📄'
  }
}

async function testPerformance(): Promise<void> {
  console.log('⚡ Performance Summary...\n')
  
  const testRoutes = [
    '/about-us-test',
    '/choosing-right-school-uniform',
    '/portfolio/summer-collection-2024',
    '/portfolio'
  ]
  
  const results = await Promise.all(testRoutes.map(testRoute))
  
  const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length
  const successRate = (results.filter(r => r.status === 200).length / results.length) * 100
  const avgContentSize = results.reduce((sum, r) => sum + r.contentLength, 0) / results.length
  
  console.log(`📊 Performance Metrics:`)
  console.log(`   Average Response Time: ${Math.round(avgResponseTime)}ms`)
  console.log(`   Success Rate: ${successRate}%`)
  console.log(`   Average Content Size: ${Math.round(avgContentSize / 1024)}KB`)
  console.log(`   SEO Coverage: ${results.filter(r => r.hasMetadata).length}/${results.length} pages`)
  console.log(`   Structured Data: ${results.filter(r => r.hasStructuredData).length}/${results.length} pages`)
  console.log()
}

async function testErrorHandling(): Promise<void> {
  console.log('🚨 Testing Error Handling...\n')
  
  const errorRoutes = [
    '/non-existent-page',
    '/portfolio/non-existent-item',
    '/invalid/route/structure'
  ]
  
  for (const route of errorRoutes) {
    const result = await testRoute(route)
    console.log(`❌ ${route}:`)
    console.log(`   Status: ${result.status === 404 ? '✅' : '❌'} ${result.status} (Expected 404)`)
    console.log(`   Response Time: ${result.responseTime}ms`)
    console.log(`   Has Error Page: ${result.contentLength > 1000 ? '✅' : '❌'}`)
    console.log()
  }
}

async function runFrontendTests(): Promise<void> {
  console.log('🧪 Frontend Rendering System Tests')
  console.log('=====================================\n')
  
  try {
    // Test if server is running
    const healthCheck = await testRoute('/')
    if (healthCheck.status !== 200) {
      console.log('❌ Server is not running or not accessible')
      console.log('   Please ensure the development server is running on http://localhost:3090')
      return
    }
    
    console.log('✅ Server is running and accessible\n')
    
    // Run all tests
    await testSEORoutes()
    await testContentRoutes()
    await testPerformance()
    await testErrorHandling()
    
    console.log('🎉 Frontend testing complete!')
    console.log('\n📋 Next Steps:')
    console.log('   1. Check browser developer tools for any console errors')
    console.log('   2. Validate HTML markup and accessibility')
    console.log('   3. Test responsive design on different screen sizes')
    console.log('   4. Run Lighthouse audit for performance and SEO scores')
    console.log('   5. Test with real content and larger datasets')
    
  } catch (error) {
    console.error('❌ Test suite failed:', error)
  }
}

if (require.main === module) {
  runFrontendTests()
    .catch((error) => {
      console.error(error)
      process.exit(1)
    })
}

export default runFrontendTests
