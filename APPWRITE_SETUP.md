# Appwrite Integration Setup Guide

This guide will help you set up Appwrite integration for your e-commerce application.

## Prerequisites

- An Appwrite Cloud account (https://cloud.appwrite.io) or self-hosted Appwrite instance
- Node.js and pnpm installed
- Your Next.js e-commerce application

## Step 1: Create Appwrite Project

1. Go to [Appwrite Cloud](https://cloud.appwrite.io) and create an account
2. Create a new project
3. Note down your:
   - Project ID
   - API Endpoint (usually `https://cloud.appwrite.io/v1`)

## Step 2: Configure Environment Variables

Add the following environment variables to your `.env.local` file:

```bash
# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id_here
APPWRITE_API_KEY=your_api_key_here
NEXT_PUBLIC_APPWRITE_DATABASE_ID=your_database_id_here
NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=your_storage_bucket_id_here
```

## Step 3: Create API Key

1. In your Appwrite console, go to "Settings" > "API Keys"
2. Create a new API key with the following scopes:
   - `databases.read`
   - `databases.write`
   - `files.read`
   - `files.write`
   - `users.read`
   - `users.write`
3. Copy the API key and add it to your environment variables

## Step 4: Create Database

1. In your Appwrite console, go to "Databases"
2. Create a new database
3. Note the database ID and add it to your environment variables

### Create Collections

Create the following collections in your database:

#### Products Collection
- Collection ID: `products`
- Attributes:
  - `title` (string, required)
  - `description` (string)
  - `handle` (string, unique)
  - `price` (number, required)
  - `compareAtPrice` (number)
  - `sku` (string)
  - `status` (string, enum: active, draft, archived)
  - `images` (array of strings)
  - `tags` (array of strings)
  - `createdAt` (datetime)
  - `updatedAt` (datetime)

#### Cart Items Collection
- Collection ID: `cart_items`
- Attributes:
  - `userId` (string, required)
  - `productId` (string, required)
  - `quantity` (number, required)
  - `price` (number, required)
  - `createdAt` (datetime)
  - `updatedAt` (datetime)

#### Orders Collection
- Collection ID: `orders`
- Attributes:
  - `userId` (string)
  - `orderNumber` (string, unique, required)
  - `status` (string, enum: pending, processing, shipped, delivered, cancelled)
  - `total` (number, required)
  - `items` (array)
  - `shippingAddress` (object)
  - `billingAddress` (object)
  - `createdAt` (datetime)
  - `updatedAt` (datetime)

## Step 5: Create Storage Bucket

1. In your Appwrite console, go to "Storage"
2. Create a new bucket for product images
3. Configure the bucket:
   - Name: "Product Images"
   - File Security: Enable
   - Maximum File Size: 5MB
   - Allowed File Extensions: jpg, jpeg, png, webp, gif
4. Note the bucket ID and add it to your environment variables

### Set Bucket Permissions

Configure the following permissions for your storage bucket:
- Read access: `role:all` (for public product images)
- Write access: `role:member` (for authenticated users)

## Step 6: Configure Authentication (Optional)

If you want to use Appwrite for user authentication:

1. In your Appwrite console, go to "Auth"
2. Configure your authentication methods:
   - Email/Password
   - OAuth providers (Google, Facebook, etc.)
3. Set up email templates for verification and password reset

## Step 7: Test the Integration

1. Start your development server:
   ```bash
   pnpm dev
   ```

2. Check the console for Appwrite initialization messages

3. Test file upload functionality in the admin panel

4. Verify that files are being uploaded to your Appwrite storage bucket

## Step 8: Production Setup

For production deployment:

1. Update your environment variables in your hosting platform
2. Configure proper CORS settings in Appwrite console
3. Set up proper security rules for your collections
4. Configure webhooks for real-time updates (optional)

## Troubleshooting

### Common Issues

1. **"Appwrite not configured" error**
   - Check that all environment variables are set correctly
   - Ensure the project ID and endpoint are correct

2. **Upload failures**
   - Verify storage bucket permissions
   - Check file size and type restrictions
   - Ensure API key has proper scopes

3. **Database errors**
   - Verify collection IDs match your configuration
   - Check that required attributes are present
   - Ensure proper permissions are set

### Debug Mode

To enable debug mode, add this to your environment:
```bash
NODE_ENV=development
```

This will show detailed error messages and connection status.

## Features Enabled

With Appwrite integration, you get:

- ✅ File storage for product images
- ✅ Database operations (alternative to Prisma)
- ✅ User authentication
- ✅ Real-time updates
- ✅ Automatic image optimization
- ✅ CDN delivery for files

## Fallback Behavior

The integration includes fallback mechanisms:
- If Appwrite is not configured, the app uses local object URLs for image preview
- Database operations fall back to Prisma
- Authentication falls back to NextAuth

This ensures your application works even without Appwrite configured.

## Next Steps

1. Explore the Appwrite dashboard to monitor usage
2. Set up webhooks for advanced integrations
3. Configure additional storage buckets for different file types
4. Implement real-time features using Appwrite's realtime API

For more information, visit the [Appwrite Documentation](https://appwrite.io/docs).
