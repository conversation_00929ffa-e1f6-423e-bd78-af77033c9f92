import { create } from 'zustand'
import { persist } from 'zustand/middleware'

type Notification = {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  createdAt: Date
  read: boolean
}

type Breadcrumb = {
  label: string
  href: string
}

type AdminUIState = {
  // State
  isSidebarCollapsed: boolean
  sidebarWidth: number
  notifications: Notification[]
  unreadNotificationsCount: number
  isDarkMode: boolean
  currentSection: string
  breadcrumbs: Breadcrumb[]
}

type AdminUIActions = {
  toggleSidebar: () => void
  setSidebarWidth: (width: number) => void
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt' | 'read'>) => void
  markNotificationAsRead: (id: string) => void
  clearNotifications: () => void
  toggleTheme: () => void
  setCurrentSection: (section: string) => void
  setBreadcrumbs: (breadcrumbs: Breadcrumb[]) => void
}

type AdminUIStore = AdminUIState & AdminUIActions

const initialState: AdminUIState = {
  isSidebarCollapsed: false,
  sidebarWidth: 280,
  notifications: [],
  unreadNotificationsCount: 0,
  isDarkMode: false,
  currentSection: 'dashboard',
  breadcrumbs: [{ label: 'Dashboard', href: '/admin' }],
}

export const useAdminUI = create<AdminUIStore>()(
  persist(
    (set) => ({
      ...initialState,

      toggleSidebar: () =>
        set((state) => ({ isSidebarCollapsed: !state.isSidebarCollapsed })),

      setSidebarWidth: (width) =>
        set({ sidebarWidth: Math.max(200, Math.min(400, width)) }),

      addNotification: (notification) =>
        set((state) => {
          const newNotification = {
            ...notification,
            id: crypto.randomUUID(),
            createdAt: new Date(),
            read: false,
          }
          return {
            notifications: [newNotification, ...state.notifications].slice(0, 100),
            unreadNotificationsCount: state.unreadNotificationsCount + 1,
          }
        }),

      markNotificationAsRead: (id) =>
        set((state) => {
          const notifications = state.notifications.map((n) =>
            n.id === id ? { ...n, read: true } : n
          )
          return {
            notifications,
            unreadNotificationsCount: notifications.filter((n) => !n.read).length,
          }
        }),

      clearNotifications: () =>
        set({ notifications: [], unreadNotificationsCount: 0 }),

      toggleTheme: () => 
        set((state) => ({ isDarkMode: !state.isDarkMode })),

      setCurrentSection: (section) =>
        set({ currentSection: section }),

      setBreadcrumbs: (breadcrumbs) =>
        set({ breadcrumbs }),
    }),
    {
      name: 'admin-ui-storage',
      skipHydration: true,
    }
  )
)