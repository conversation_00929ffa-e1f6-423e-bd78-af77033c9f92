import { NextRequest, NextResponse } from 'next/server'

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for known static routes and API routes
  const skipPatterns = [
    /^\/api\//,
    /^\/admin\//,
    /^\/preview\//,
    /^\/blog\//,
    /^\/products\//,
    /^\/collections\//,
    /^\/cart/,
    /^\/checkout/,
    /^\/account/,
    /^\/wishlist/,
    /^\/compare/,
    /^\/search/,
    /^\/contact/,
    /^\/about/,
    /^\/faq/,
    /^\/help/,
    /^\/terms/,
    /^\/privacy/,
    /^\/shipping/,
    /^\/stores/,
    /^\/newsletter/,
    /^\/brand/,
    /^\/orders/,
    /^\/shadcn-showcase/,
    /^\/ai-demo/,
    /^\/error-demo/,
    /^\/frontend\//,
    /^\/dynamic-page\//,
    /^\/maintenance$/,
    /^\/_next\//,
    /^\/favicon\.ico$/,
    /\.(png|jpg|jpeg|gif|svg|css|js|woff|woff2|ttf|eot|ico|webp|mp4|webm|ogg|mp3|wav|flac|aac|opus|pdf|zip|tar|gz|rar|7z|doc|docx|xls|xlsx|ppt|pptx)$/
  ]

  // Handle root path (/) - check for dynamic homepage
  if (pathname === '/') {
    // Add a header to indicate this is a potential homepage request
    // The actual homepage logic will be handled in the app/page.tsx file
    const response = NextResponse.next()
    response.headers.set('x-homepage-request', 'true')
    return response
  }

  // Check if path should be skipped
  if (skipPatterns.some(pattern => pattern.test(pathname))) {
    return NextResponse.next()
  }

  // For dynamic page routing, we'll handle this in the app router
  // by creating a catch-all route that checks for page existence
  // This avoids the complexity of database calls in middleware

  try {
    // For other routes, add headers to help with route resolution
    const response = NextResponse.next()

    // Add route information to headers for the page component to use
    response.headers.set('x-pathname', pathname)
    response.headers.set('x-dynamic-route', 'true')

    // Set caching headers for dynamic content
    response.headers.set('Cache-Control', 'public, s-maxage=300, stale-while-revalidate=600')

    // Add security headers
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('X-XSS-Protection', '1; mode=block')

    return response

  } catch (error) {
    console.error('Middleware error:', error)
    return NextResponse.next()
  }
}



export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - admin (admin routes)
     * - preview (preview routes)
     * - Known static routes
     */
    '/((?!api|_next/static|_next/image|favicon.ico|admin|preview|blog|products|collections|cart|checkout|account|wishlist|compare|search|contact|about|faq|help|terms|privacy|shipping|stores|newsletter|brand|orders|shadcn-showcase|ai-demo|error-demo).*)',
  ],
}
