-- Layout Builder System Database Schema
-- Add to your existing Prisma schema

-- Layout Templates and Structures
CREATE TABLE IF NOT EXISTS layouts (
  id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  type VARCHAR(50) NOT NULL DEFAULT 'page', -- 'site', 'page', 'post-type', 'template'
  category VARCHAR(50) DEFAULT 'custom', -- 'ecommerce', 'blog', 'portfolio', 'landing', 'custom'
  
  -- Layout Structure (JSON)
  structure JSON NOT NULL DEFAULT '{}',
  
  -- Styling and Configuration
  styling JSON DEFAULT '{}',
  responsive JSON DEFAULT '{}',
  
  -- Conditional Display
  conditions JSON DEFAULT '{}',
  
  -- Template Settings
  is_template BOOLEAN DEFAULT false,
  is_system BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  
  -- Usage Tracking
  usage_count INTEGER DEFAULT 0,
  
  -- Metadata
  thumbnail VARCHAR(500),
  tags TEXT[],
  
  -- Timestamps
  created_by <PERSON><PERSON><PERSON><PERSON>(255),
  updated_by <PERSON><PERSON>HA<PERSON>(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Layout Assignments (Which layouts apply where)
CREATE TABLE IF NOT EXISTS layout_assignments (
  id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
  layout_id VARCHAR(255) NOT NULL REFERENCES layouts(id) ON DELETE CASCADE,
  
  -- Assignment Target
  target_type VARCHAR(50) NOT NULL, -- 'global', 'page', 'post-type', 'specific', 'conditional'
  target_id VARCHAR(255), -- page_id, post_type_name, or null for global
  target_slug VARCHAR(255), -- for URL-based assignments
  
  -- Priority and Conditions
  priority INTEGER DEFAULT 0,
  conditions JSON DEFAULT '{}',
  
  -- Status
  is_active BOOLEAN DEFAULT true,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Layout Sections (Header, Footer, Sidebar components)
CREATE TABLE IF NOT EXISTS layout_sections (
  id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
  layout_id VARCHAR(255) NOT NULL REFERENCES layouts(id) ON DELETE CASCADE,
  
  -- Section Details
  name VARCHAR(255) NOT NULL,
  type VARCHAR(50) NOT NULL, -- 'header', 'footer', 'sidebar', 'main', 'custom'
  position INTEGER DEFAULT 0,
  
  -- Section Configuration
  configuration JSON DEFAULT '{}',
  styling JSON DEFAULT '{}',
  responsive JSON DEFAULT '{}',
  
  -- Content and Blocks
  blocks JSON DEFAULT '[]',
  
  -- Status
  is_visible BOOLEAN DEFAULT true,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Layout Blocks (Individual components within sections)
CREATE TABLE IF NOT EXISTS layout_blocks (
  id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
  section_id VARCHAR(255) NOT NULL REFERENCES layout_sections(id) ON DELETE CASCADE,
  
  -- Block Details
  name VARCHAR(255) NOT NULL,
  type VARCHAR(50) NOT NULL, -- 'navigation', 'logo', 'search', 'content', 'widget', 'custom'
  position INTEGER DEFAULT 0,
  
  -- Block Configuration
  configuration JSON DEFAULT '{}',
  content JSON DEFAULT '{}',
  styling JSON DEFAULT '{}',
  responsive JSON DEFAULT '{}',
  
  -- Conditional Display
  conditions JSON DEFAULT '{}',
  
  -- Status
  is_visible BOOLEAN DEFAULT true,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Layout Versions (For version control and rollback)
CREATE TABLE IF NOT EXISTS layout_versions (
  id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
  layout_id VARCHAR(255) NOT NULL REFERENCES layouts(id) ON DELETE CASCADE,
  
  -- Version Details
  version_number INTEGER NOT NULL,
  name VARCHAR(255),
  description TEXT,
  
  -- Version Data (Complete snapshot)
  structure JSON NOT NULL,
  styling JSON DEFAULT '{}',
  responsive JSON DEFAULT '{}',
  
  -- Version Status
  is_published BOOLEAN DEFAULT false,
  is_current BOOLEAN DEFAULT false,
  
  -- Metadata
  created_by VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Navigation Menus (For layout navigation blocks)
CREATE TABLE IF NOT EXISTS navigation_menus (
  id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  description TEXT,
  
  -- Menu Structure
  items JSON DEFAULT '[]',
  
  -- Menu Settings
  settings JSON DEFAULT '{}',
  
  -- Status
  is_active BOOLEAN DEFAULT true,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Widget Areas (For sidebar and footer widgets)
CREATE TABLE IF NOT EXISTS widget_areas (
  id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  description TEXT,
  
  -- Widget Configuration
  widgets JSON DEFAULT '[]',
  settings JSON DEFAULT '{}',
  
  -- Status
  is_active BOOLEAN DEFAULT true,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_layouts_type ON layouts(type);
CREATE INDEX IF NOT EXISTS idx_layouts_category ON layouts(category);
CREATE INDEX IF NOT EXISTS idx_layouts_active ON layouts(is_active);
CREATE INDEX IF NOT EXISTS idx_layout_assignments_target ON layout_assignments(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_layout_assignments_priority ON layout_assignments(priority);
CREATE INDEX IF NOT EXISTS idx_layout_sections_layout ON layout_sections(layout_id);
CREATE INDEX IF NOT EXISTS idx_layout_sections_type ON layout_sections(type);
CREATE INDEX IF NOT EXISTS idx_layout_blocks_section ON layout_blocks(section_id);
CREATE INDEX IF NOT EXISTS idx_layout_blocks_type ON layout_blocks(type);
CREATE INDEX IF NOT EXISTS idx_layout_versions_layout ON layout_versions(layout_id);
CREATE INDEX IF NOT EXISTS idx_navigation_menus_slug ON navigation_menus(slug);
CREATE INDEX IF NOT EXISTS idx_widget_areas_slug ON widget_areas(slug);

-- Insert Default System Layouts
INSERT INTO layouts (id, name, description, type, category, structure, is_template, is_system, is_active) VALUES
('default-site', 'Default Site Layout', 'Basic site layout with header, main content, and footer', 'site', 'custom', 
 '{"header":{"type":"header","blocks":[{"type":"logo","position":1},{"type":"navigation","position":2}]},"main":{"type":"main","blocks":[{"type":"content","position":1}]},"footer":{"type":"footer","blocks":[{"type":"copyright","position":1}]}}', 
 true, true, true),

('ecommerce-store', 'E-commerce Store Layout', 'Complete e-commerce layout with cart, search, and product areas', 'site', 'ecommerce',
 '{"header":{"type":"header","blocks":[{"type":"logo","position":1},{"type":"search","position":2},{"type":"navigation","position":3},{"type":"cart","position":4}]},"main":{"type":"main","blocks":[{"type":"content","position":1}]},"sidebar":{"type":"sidebar","blocks":[{"type":"filters","position":1},{"type":"categories","position":2}]},"footer":{"type":"footer","blocks":[{"type":"links","position":1},{"type":"social","position":2},{"type":"copyright","position":3}]}}',
 true, true, true),

('blog-magazine', 'Blog/Magazine Layout', 'Blog layout with sidebar and article structure', 'site', 'blog',
 '{"header":{"type":"header","blocks":[{"type":"logo","position":1},{"type":"navigation","position":2},{"type":"search","position":3}]},"main":{"type":"main","blocks":[{"type":"content","position":1}]},"sidebar":{"type":"sidebar","blocks":[{"type":"recent-posts","position":1},{"type":"categories","position":2},{"type":"tags","position":3}]},"footer":{"type":"footer","blocks":[{"type":"links","position":1},{"type":"social","position":2},{"type":"copyright","position":3}]}}',
 true, true, true);

-- Insert Default Global Assignment
INSERT INTO layout_assignments (layout_id, target_type, priority, is_active) VALUES
('default-site', 'global', 0, true);
