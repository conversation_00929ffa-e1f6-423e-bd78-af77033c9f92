'use client'

import { createContext, useContext, useEffect } from 'react'
import { useAdminUI } from '@/stores/use-admin-ui'

const AdminLayoutContext = createContext<{
  isEditorMode: boolean
  toggleEditorMode: () => void
} | null>(null)

export function AdminLayoutProvider({
  children
}: {
  children: React.ReactNode
}) {
  const { isEditorMode, toggleEditorMode, setEditorMode } = useAdminUI()

  // Make sure editor mode is disabled by default
  useEffect(() => {
    setEditorMode(false)
  }, [setEditorMode])

  return (
    <AdminLayoutContext.Provider value={{ isEditorMode, toggleEditorMode }}>
      {children}
    </AdminLayoutContext.Provider>
  )
}

export function useAdminLayout() {
  const context = useContext(AdminLayoutContext)
  if (!context) {
    throw new Error('useAdminLayout must be used within an AdminLayoutProvider')
  }
  return context
}
