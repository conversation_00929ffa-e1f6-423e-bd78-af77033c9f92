"use client"

import Image from "next/image"
import Link from "next/link"
import { Instagram } from "lucide-react"
import { motion } from "framer-motion"

// Mock Instagram posts
const instagramPosts = [
  {
    id: "1",
    image: "https://images.unsplash.com/photo-1519238359922-989348752efb?q=80&w=600&h=600&auto=format&fit=crop",
    likes: 124,
    comments: 8,
    caption: "Our new striped cotton tees are perfect for summer adventures! #CocoMilkKids #KidsFashion",
    url: "https://instagram.com"
  },
  {
    id: "2",
    image: "https://images.unsplash.com/photo-1541580621-cb65cc53084b?q=80&w=600&h=600&auto=format&fit=crop",
    likes: 98,
    comments: 5,
    caption: "Comfort fit jeans that move with your little ones #CocoMilkKids #KidsStyle",
    url: "https://instagram.com"
  },
  {
    id: "3",
    image: "https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?q=80&w=600&h=600&auto=format&fit=crop",
    likes: 156,
    comments: 12,
    caption: "Cozy hoodies for those cooler summer evenings #CocoMilkKids #KidsOuterwear",
    url: "https://instagram.com"
  },
  {
    id: "4",
    image: "https://images.unsplash.com/photo-1603344204980-4edb0ea63148?q=80&w=600&h=600&auto=format&fit=crop",
    likes: 201,
    comments: 18,
    caption: "Summer dresses that twirl and delight! #CocoMilkKids #GirlsFashion",
    url: "https://instagram.com"
  },
  {
    id: "5",
    image: "https://images.unsplash.com/photo-1622560480654-d96214fdc887?q=80&w=600&h=600&auto=format&fit=crop",
    likes: 87,
    comments: 4,
    caption: "Back to school ready with our durable canvas backpacks #CocoMilkKids #BackToSchool",
    url: "https://instagram.com"
  },
  {
    id: "6",
    image: "https://images.unsplash.com/photo-1626566340238-0c6b72a23c88?q=80&w=600&h=600&auto=format&fit=crop",
    likes: 112,
    comments: 7,
    caption: "Denim shorts for active kids who love to explore #CocoMilkKids #SummerStyle",
    url: "https://instagram.com"
  }
]

export function InstagramFeed() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.5 }
    }
  }

  return (
    <section className="py-20 bg-gray-50">
      <div className="container px-4 md:px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-montserrat font-light tracking-wide mb-4">Follow Our Journey</h2>
          <div className="flex items-center justify-center gap-2">
            <Instagram className="h-5 w-5 text-[#012169]" />
            <p className="text-muted-foreground font-light">
              @cocomilkkids
            </p>
          </div>
        </div>

        <motion.div 
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4"
        >
          {instagramPosts.map((post) => (
            <motion.div key={post.id} variants={itemVariants}>
              <Link 
                href={post.url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="group block"
              >
                <div className="relative aspect-square overflow-hidden">
                  <Image
                    src={post.image}
                    alt={post.caption}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <div className="text-white text-center">
                      <div className="flex items-center justify-center gap-4">
                        <div className="flex items-center">
                          <span className="text-sm font-light">{post.likes}</span>
                          <svg className="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
                          </svg>
                        </div>
                        <div className="flex items-center">
                          <span className="text-sm font-light">{post.comments}</span>
                          <svg className="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M21 15a2 2 0 0 1-2 2h-2v3l-4-3H7a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v8z" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        <div className="text-center mt-10">
          <Link 
            href="https://instagram.com" 
            target="_blank" 
            rel="noopener noreferrer"
            className="inline-flex items-center text-[#012169] hover:text-[#012169]/80 font-light"
          >
            <span>View more on Instagram</span>
            <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  )
}
