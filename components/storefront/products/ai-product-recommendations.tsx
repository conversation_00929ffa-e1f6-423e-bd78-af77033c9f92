"use client"

import { useState, useEffect } from "react"
import { useChat } from "@ai-sdk/react"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { ProductCard } from "@/components/storefront/products/product-card"
import { Sparkles } from "lucide-react"
import type { Product } from "@/lib/products"

interface AIProductRecommendationsProps {
  productId: string
  browsingHistory?: string[]
}

export function AIProductRecommendations({ productId, browsingHistory = [] }: AIProductRecommendationsProps) {
  const [recommendations, setRecommendations] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [explanationText, setExplanationText] = useState<string>("")

  const { messages, append, status } = useChat({
    api: "/api/product-recommendations",
    id: `product-${productId}`,
    initialMessages: [],
    body: {
      productId,
      browsingHistory,
      preferences: {}, // Could be populated from user profile in a real app
    },
    onFinish: (message) => {
      // Extract text and tool results from the message
      let textContent = "";
      let productResults: Product[] = [];

      message.parts.forEach(part => {
        if (part.type === 'text') {
          textContent += part.text;
        } else if (part.type === 'tool-invocation' &&
                  part.toolInvocation.state === 'result' &&
                  part.toolInvocation.toolName === 'findComplementaryProducts') {
          productResults = part.toolInvocation.result;
        }
      });

      setExplanationText(textContent);
      if (productResults.length > 0) {
        setRecommendations(productResults);
      }
      setLoading(false);
    }
  });

  const isLoading = status === 'streaming'

  useEffect(() => {
    // Skip API call during SSR to prevent hydration mismatch
    if (typeof window === 'undefined') return;

    // Send initial message to get recommendations
    if (messages.length === 0) {
      append({
        role: "user",
        content: `Please recommend products that would go well with product ID: ${productId}`,
      });
    }
  }, [productId, browsingHistory, append, messages.length])

  if (loading || isLoading) {
    return (
      <div className="mt-12">
        <div className="flex items-center gap-2 mb-4">
          <Sparkles className="h-5 w-5 text-[#012169]" />
          <h2 className="text-xl font-light tracking-wide">AI-Powered Recommendations</h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="border-none">
              <CardContent className="p-0">
                <Skeleton className="h-64 w-full rounded-none" />
                <div className="mt-2 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (recommendations.length === 0) {
    return null
  }

  return (
    <div className="mt-12">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-[#012169]" />
          <h2 className="text-xl font-light tracking-wide">AI-Powered Recommendations</h2>
        </div>
        <span className="text-xs text-gray-500">Powered by SoImagine</span>
      </div>

      {explanationText && (
        <div className="mb-6 text-sm text-muted-foreground font-light">
          {explanationText}
        </div>
      )}

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {recommendations.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </div>
  )
}
