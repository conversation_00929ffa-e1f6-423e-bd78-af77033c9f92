"use client"

import { ProductCard } from "@/components/storefront/products/product-card"
import { useProducts } from "@/lib/ecommerce/hooks/use-products"
import { useEffect } from "react"
import { Skeleton } from "@/components/ui/skeleton"

interface ProductGridProps {
  sort?: string
  category?: string
  color?: string
  size?: string
}

export function ProductGrid({ sort, category, color, size }: ProductGridProps) {
  const { products, loading, error, searchProducts } = useProducts({ autoFetch: false })

  useEffect(() => {
    const params: any = {}

    // Map frontend filter params to e-commerce API params
    if (sort) {
      switch (sort) {
        case 'price-asc':
          params.sortBy = 'price'
          params.sortOrder = 'asc'
          break
        case 'price-desc':
          params.sortBy = 'price'
          params.sortOrder = 'desc'
          break
        case 'name-asc':
          params.sortBy = 'title'
          params.sortOrder = 'asc'
          break
        case 'name-desc':
          params.sortBy = 'title'
          params.sortOrder = 'desc'
          break
        case 'newest':
          params.sortBy = 'createdAt'
          params.sortOrder = 'desc'
          break
        default: // 'featured'
          params.sortBy = 'featured'
          params.sortOrder = 'desc'
      }
    }

    if (category) {
      params.category = category
    }

    if (color) {
      params.color = color
    }

    if (size) {
      params.size = size
    }

    // Only fetch active products
    params.status = 'active'
    params.limit = 50 // Reasonable limit for product listing

    searchProducts(params)
  }, [sort, category, color, size]) // Remove searchProducts dependency to prevent infinite loops

  if (loading) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
        {Array.from({ length: 12 }).map((_, i) => (
          <div key={i} className="space-y-3">
            <Skeleton className="aspect-[3/4] w-full" />
            <Skeleton className="h-4 w-2/3" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-medium mb-2">Error loading products</h2>
        <p className="text-muted-foreground">{error}</p>
      </div>
    )
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-medium mb-2">No products found</h2>
        <p className="text-muted-foreground">Try adjusting your filters to find what you're looking for.</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
      {products.map((product) => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  )
}
