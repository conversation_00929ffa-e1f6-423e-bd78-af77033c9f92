import Link from "next/link"
import Image from "next/image"

const categories = [
  {
    name: "Tops",
    image: "/assets/images/cocomilk_kids-20220820_161754-1684440538.jpg",
    slug: "tops",
  },
  {
    name: "Bottoms",
    image: "/assets/images/cocomilk_kids-20220921_110351-1857198841.jpg",
    slug: "bottoms",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    image: "/assets/images/cocomilk_kids-20220927_125643-1359487094.jpg",
    slug: "dresses",
  },
  {
    name: "Accessories",
    image: "/assets/images/cocomilk_kids-20221005_080805-1368653969.jpg",
    slug: "accessories",
  },
]

export function CategoryGrid() {
  return (
    <section className="py-16 md:py-24">
      <h2 className="text-2xl md:text-3xl font-light font-montserrat mb-12 tracking-wide">Shop by Category</h2>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8">
        {categories.map((category, index) => (
          <Link
            key={category.slug}
            href={`/products?category=${category.slug}`}
            className="group relative overflow-hidden image-zoom fade-in"
            style={{ animationDelay: `${0.1 * index}s` }}
          >
            <div className="aspect-square relative">
              <Image src={category.image || "/placeholder.svg"} alt={category.name} fill className="object-cover" />
              <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors duration-500" />
              <div className="absolute inset-0 flex items-center justify-center">
                <h3 className="text-xl md:text-2xl font-light text-white font-montserrat tracking-wide">
                  {category.name}
                </h3>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </section>
  )
}
