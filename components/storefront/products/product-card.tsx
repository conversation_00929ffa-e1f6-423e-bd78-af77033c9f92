"use client"

import type React from "react"

import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Eye, Scale } from "lucide-react"
import { QuickView } from "@/components/quick-view"
import { usePriceFormatter } from "@/hooks/use-price-formatter"
import { cn } from "@/lib/utils"
import { Product } from "@/lib/ecommerce/types"

interface ProductCardProps {
  product: Product
  className?: string
}

export function ProductCard({ product, className }: ProductCardProps) {
  const { formatPrice } = usePriceFormatter()

  // Extract price values from Money type
  const price = product.price.amount
  const compareAtPrice = product.compareAtPrice?.amount

  const discount = compareAtPrice
    ? Math.round(((compareAtPrice - price) / compareAtPrice) * 100)
    : 0

  const handleAddToCompare = (e: React.MouseEvent) => {
    e.preventDefault()
    if (typeof window !== "undefined" && (window as any).addToCompare) {
      ;(window as any).addToCompare(product.id)
    }
  }

  // Get the first image URL
  const imageUrl = product.images && product.images.length > 0
    ? product.images[0].url
    : "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"

  // Determine if product is on sale or new
  const isOnSale = !!compareAtPrice && compareAtPrice > price
  const isNew = product.createdAt && new Date(product.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days

  return (
    <div className={cn("group block product-card-clean", className)}>
      {/* Clean image container like selfi.co.za */}
      <div className="relative aspect-[3/4] mb-3 overflow-hidden bg-white image-zoom">
        <Link href={`/products/${product.slug}`}>
          <Image
            src={imageUrl}
            alt={product.title}
            fill
            className="object-cover"
          />
        </Link>

        {/* Simple badges like selfi.co.za */}
        {isOnSale && (
          <div className="absolute top-2 left-2">
            <span className="text-xs text-red-600 font-normal">
              Sale
            </span>
          </div>
        )}

        {isNew && !isOnSale && (
          <div className="absolute top-2 left-2">
            <span className="text-xs text-black font-normal">
              New
            </span>
          </div>
        )}
      </div>

      {/* Clean product info like selfi.co.za */}
      <div className="space-y-1">
        <Link href={`/products/${product.slug}`}>
          <h3 className="product-title text-black hover:text-gray-600 transition-colors duration-200">
            {product.title}
          </h3>
        </Link>

        {/* Vendor info */}
        <p className="text-xs text-gray-500 font-normal">
          {product.vendor || 'Coco Milk Kids'}
        </p>

        {/* Price */}
        <div className="flex items-center space-x-2">
          {compareAtPrice ? (
            <>
              <span className="text-sm font-normal text-black">
                {formatPrice(price)}
              </span>
              <span className="text-sm text-gray-500 line-through">
                {formatPrice(compareAtPrice)}
              </span>
            </>
          ) : (
            <span className="text-sm font-normal text-black">
              {formatPrice(price)}
            </span>
          )}
        </div>
      </div>
    </div>
  )
}
