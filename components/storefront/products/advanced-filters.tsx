"use client"

import { useState } from "react"
import { useRouter, usePathname, useSearchParams } from "next/navigation"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"

interface AdvancedFiltersProps {
  selectedFilters: {
    category?: string
    color?: string[]
    size?: string[]
    priceRange?: [number, number]
    brand?: string[]
    material?: string[]
    isNew?: boolean
    isSale?: boolean
  }
}

export function AdvancedFilters({ selectedFilters }: AdvancedFiltersProps) {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const [priceRange, setPriceRange] = useState<[number, number]>(selectedFilters.priceRange || [0, 100])
  const [tempFilters, setTempFilters] = useState(selectedFilters)

  const filterOptions = {
    categories: [
      { id: "tops", label: "Tops", count: 24 },
      { id: "bottoms", label: "Bottoms", count: 18 },
      { id: "dresses", label: "Dresses", count: 12 },
      { id: "outerwear", label: "Outerwear", count: 8 },
      { id: "accessories", label: "Accessories", count: 15 },
    ],
    colors: [
      { id: "black", label: "Black", hex: "#0D0D0D", count: 32 },
      { id: "white", label: "White", hex: "#FFFFFF", count: 28 },
      { id: "blue", label: "Blue", hex: "#012169", count: 24 },
      { id: "red", label: "Red", hex: "#6C1411", count: 18 },
      { id: "brown", label: "Brown", hex: "#2B1D18", count: 12 },
      { id: "gray", label: "Gray", hex: "#E5E5E5", count: 16 },
    ],
    sizes: [
      { id: "xs", label: "XS (2-3)", count: 45 },
      { id: "s", label: "S (4-5)", count: 52 },
      { id: "m", label: "M (6-7)", count: 48 },
      { id: "l", label: "L (8-9)", count: 41 },
      { id: "xl", label: "XL (10-12)", count: 38 },
    ],
    brands: [
      { id: "coco-milk", label: "Coco Milk", count: 77 },
      { id: "little-comfort", label: "Little Comfort", count: 23 },
      { id: "kids-style", label: "Kids Style", count: 18 },
    ],
    materials: [
      { id: "cotton", label: "100% Cotton", count: 45 },
      { id: "organic-cotton", label: "Organic Cotton", count: 32 },
      { id: "cotton-blend", label: "Cotton Blend", count: 28 },
      { id: "denim", label: "Denim", count: 15 },
      { id: "fleece", label: "Fleece", count: 12 },
    ],
  }

  const createQueryString = (filters: typeof tempFilters) => {
    const params = new URLSearchParams(searchParams.toString())

    // Clear existing filter params
    params.delete("category")
    params.delete("color")
    params.delete("size")
    params.delete("priceMin")
    params.delete("priceMax")
    params.delete("brand")
    params.delete("material")
    params.delete("isNew")
    params.delete("isSale")

    // Add new filter params
    if (filters.category) params.set("category", filters.category)
    if (filters.color?.length) params.set("color", filters.color.join(","))
    if (filters.size?.length) params.set("size", filters.size.join(","))
    if (filters.priceRange) {
      params.set("priceMin", filters.priceRange[0].toString())
      params.set("priceMax", filters.priceRange[1].toString())
    }
    if (filters.brand?.length) params.set("brand", filters.brand.join(","))
    if (filters.material?.length) params.set("material", filters.material.join(","))
    if (filters.isNew) params.set("isNew", "true")
    if (filters.isSale) params.set("isSale", "true")

    return params.toString()
  }

  const applyFilters = () => {
    const queryString = createQueryString({ ...tempFilters, priceRange })
    router.push(`${pathname}?${queryString}`)
  }

  const clearAllFilters = () => {
    setTempFilters({})
    setPriceRange([0, 100])
    router.push(pathname)
  }

  const removeFilter = (filterType: string, value?: string) => {
    const newFilters = { ...tempFilters }

    switch (filterType) {
      case "category":
        delete newFilters.category
        break
      case "color":
        if (value && newFilters.color) {
          newFilters.color = newFilters.color.filter((c) => c !== value)
          if (newFilters.color.length === 0) delete newFilters.color
        }
        break
      case "size":
        if (value && newFilters.size) {
          newFilters.size = newFilters.size.filter((s) => s !== value)
          if (newFilters.size.length === 0) delete newFilters.size
        }
        break
      case "brand":
        if (value && newFilters.brand) {
          newFilters.brand = newFilters.brand.filter((b) => b !== value)
          if (newFilters.brand.length === 0) delete newFilters.brand
        }
        break
      case "material":
        if (value && newFilters.material) {
          newFilters.material = newFilters.material.filter((m) => m !== value)
          if (newFilters.material.length === 0) delete newFilters.material
        }
        break
      case "isNew":
        delete newFilters.isNew
        break
      case "isSale":
        delete newFilters.isSale
        break
      case "price":
        delete newFilters.priceRange
        setPriceRange([0, 100])
        break
    }

    setTempFilters(newFilters)
    const queryString = createQueryString(newFilters)
    router.push(`${pathname}?${queryString}`)
  }

  const activeFiltersCount = Object.keys(selectedFilters).length

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Filters</h3>
        {activeFiltersCount > 0 && (
          <Button variant="ghost" size="sm" onClick={clearAllFilters}>
            Clear All ({activeFiltersCount})
          </Button>
        )}
      </div>

      {/* Active Filters */}
      {activeFiltersCount > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Active Filters:</h4>
          <div className="flex flex-wrap gap-2">
            {selectedFilters.category && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Category: {selectedFilters.category}
                <X className="h-3 w-3 cursor-pointer" onClick={() => removeFilter("category")} />
              </Badge>
            )}
            {selectedFilters.color?.map((color) => (
              <Badge key={color} variant="secondary" className="flex items-center gap-1">
                {color}
                <X className="h-3 w-3 cursor-pointer" onClick={() => removeFilter("color", color)} />
              </Badge>
            ))}
            {selectedFilters.size?.map((size) => (
              <Badge key={size} variant="secondary" className="flex items-center gap-1">
                {size}
                <X className="h-3 w-3 cursor-pointer" onClick={() => removeFilter("size", size)} />
              </Badge>
            ))}
            {selectedFilters.priceRange && (
              <Badge variant="secondary" className="flex items-center gap-1">
                ${selectedFilters.priceRange[0]} - ${selectedFilters.priceRange[1]}
                <X className="h-3 w-3 cursor-pointer" onClick={() => removeFilter("price")} />
              </Badge>
            )}
            {selectedFilters.isNew && (
              <Badge variant="secondary" className="flex items-center gap-1">
                New Arrivals
                <X className="h-3 w-3 cursor-pointer" onClick={() => removeFilter("isNew")} />
              </Badge>
            )}
            {selectedFilters.isSale && (
              <Badge variant="secondary" className="flex items-center gap-1">
                On Sale
                <X className="h-3 w-3 cursor-pointer" onClick={() => removeFilter("isSale")} />
              </Badge>
            )}
          </div>
        </div>
      )}

      <Accordion type="multiple" defaultValue={["price", "category", "color", "size"]} className="w-full">
        {/* Price Range */}
        <AccordionItem value="price">
          <AccordionTrigger>Price Range</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-4">
              <Slider value={priceRange} onValueChange={setPriceRange} max={100} min={0} step={5} className="w-full" />
              <div className="flex justify-between text-sm">
                <span>${priceRange[0]}</span>
                <span>${priceRange[1]}</span>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Category */}
        <AccordionItem value="category">
          <AccordionTrigger>Category</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2">
              {filterOptions.categories.map((category) => (
                <div key={category.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`category-${category.id}`}
                      checked={tempFilters.category === category.id}
                      onCheckedChange={(checked) => {
                        setTempFilters({
                          ...tempFilters,
                          category: checked ? category.id : undefined,
                        })
                      }}
                    />
                    <Label htmlFor={`category-${category.id}`} className="text-sm cursor-pointer">
                      {category.label}
                    </Label>
                  </div>
                  <span className="text-xs text-muted-foreground">({category.count})</span>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Color */}
        <AccordionItem value="color">
          <AccordionTrigger>Color</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2">
              {filterOptions.colors.map((color) => (
                <div key={color.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`color-${color.id}`}
                      checked={tempFilters.color?.includes(color.id) || false}
                      onCheckedChange={(checked) => {
                        const currentColors = tempFilters.color || []
                        setTempFilters({
                          ...tempFilters,
                          color: checked ? [...currentColors, color.id] : currentColors.filter((c) => c !== color.id),
                        })
                      }}
                    />
                    <div className="w-4 h-4 rounded-full border" style={{ backgroundColor: color.hex }} />
                    <Label htmlFor={`color-${color.id}`} className="text-sm cursor-pointer">
                      {color.label}
                    </Label>
                  </div>
                  <span className="text-xs text-muted-foreground">({color.count})</span>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Size */}
        <AccordionItem value="size">
          <AccordionTrigger>Size</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2">
              {filterOptions.sizes.map((size) => (
                <div key={size.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`size-${size.id}`}
                      checked={tempFilters.size?.includes(size.id) || false}
                      onCheckedChange={(checked) => {
                        const currentSizes = tempFilters.size || []
                        setTempFilters({
                          ...tempFilters,
                          size: checked ? [...currentSizes, size.id] : currentSizes.filter((s) => s !== size.id),
                        })
                      }}
                    />
                    <Label htmlFor={`size-${size.id}`} className="text-sm cursor-pointer">
                      {size.label}
                    </Label>
                  </div>
                  <span className="text-xs text-muted-foreground">({size.count})</span>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Brand */}
        <AccordionItem value="brand">
          <AccordionTrigger>Brand</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2">
              {filterOptions.brands.map((brand) => (
                <div key={brand.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`brand-${brand.id}`}
                      checked={tempFilters.brand?.includes(brand.id) || false}
                      onCheckedChange={(checked) => {
                        const currentBrands = tempFilters.brand || []
                        setTempFilters({
                          ...tempFilters,
                          brand: checked ? [...currentBrands, brand.id] : currentBrands.filter((b) => b !== brand.id),
                        })
                      }}
                    />
                    <Label htmlFor={`brand-${brand.id}`} className="text-sm cursor-pointer">
                      {brand.label}
                    </Label>
                  </div>
                  <span className="text-xs text-muted-foreground">({brand.count})</span>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Material */}
        <AccordionItem value="material">
          <AccordionTrigger>Material</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2">
              {filterOptions.materials.map((material) => (
                <div key={material.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`material-${material.id}`}
                      checked={tempFilters.material?.includes(material.id) || false}
                      onCheckedChange={(checked) => {
                        const currentMaterials = tempFilters.material || []
                        setTempFilters({
                          ...tempFilters,
                          material: checked
                            ? [...currentMaterials, material.id]
                            : currentMaterials.filter((m) => m !== material.id),
                        })
                      }}
                    />
                    <Label htmlFor={`material-${material.id}`} className="text-sm cursor-pointer">
                      {material.label}
                    </Label>
                  </div>
                  <span className="text-xs text-muted-foreground">({material.count})</span>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Special Filters */}
        <AccordionItem value="special">
          <AccordionTrigger>Special</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isNew"
                  checked={tempFilters.isNew || false}
                  onCheckedChange={(checked) => {
                    setTempFilters({
                      ...tempFilters,
                      isNew: checked ? true : undefined,
                    })
                  }}
                />
                <Label htmlFor="isNew" className="text-sm cursor-pointer">
                  New Arrivals
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isSale"
                  checked={tempFilters.isSale || false}
                  onCheckedChange={(checked) => {
                    setTempFilters({
                      ...tempFilters,
                      isSale: checked ? true : undefined,
                    })
                  }}
                />
                <Label htmlFor="isSale" className="text-sm cursor-pointer">
                  On Sale
                </Label>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <Button onClick={applyFilters} className="w-full">
        Apply Filters
      </Button>
    </div>
  )
}
