"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, DialogHeader, DialogT<PERSON>le, DialogTrigger } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Scale, X, ShoppingBag } from "lucide-react"
import Image from "next/image"
import { getProductsByIds } from "@/lib/products"
import { useCart } from "@/hooks/use-cart"
import { toast } from "@/components/ui/use-toast"

interface ComparisonProduct {
  id: string
  name: string
  price: number
  compareAtPrice?: number
  images: string[]
  colors: { name: string; value: string }[]
  sizes: string[]
  description: string
  isNew?: boolean
  isSale?: boolean
}

export function ProductComparison() {
  const [compareList, setCompareList] = useState<string[]>([])
  const [products, setProducts] = useState<ComparisonProduct[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const { addItem } = useCart()

  useEffect(() => {
    const stored = localStorage.getItem("compareList")
    if (stored) {
      setCompareList(JSON.parse(stored))
    }
  }, [])

  useEffect(() => {
    const loadProducts = async () => {
      if (compareList.length > 0) {
        const productData = await getProductsByIds(compareList)
        setProducts(productData)
      } else {
        setProducts([])
      }
    }
    loadProducts()
  }, [compareList])

  const addToCompare = (productId: string) => {
    const newList = [...compareList]
    if (!newList.includes(productId)) {
      if (newList.length >= 4) {
        toast({
          title: "Comparison limit reached",
          description: "You can compare up to 4 products at a time.",
        })
        return
      }
      newList.push(productId)
      setCompareList(newList)
      localStorage.setItem("compareList", JSON.stringify(newList))
      toast({
        title: "Added to comparison",
        description: "Product added to comparison list.",
      })
    }
  }

  const removeFromCompare = (productId: string) => {
    const newList = compareList.filter((id) => id !== productId)
    setCompareList(newList)
    localStorage.setItem("compareList", JSON.stringify(newList))
  }

  const clearComparison = () => {
    setCompareList([])
    setProducts([])
    localStorage.removeItem("compareList")
  }

  const handleAddToCart = (product: ComparisonProduct) => {
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      color: product.colors[0]?.value || "",
      size: product.sizes[0] || "",
      quantity: 1,
      image: product.images[0] || "https://images.unsplash.com/photo-1519238359922-989348752efb?q=80&w=80&h=80&auto=format&fit=crop",
    })

    toast({
      title: "Added to cart",
      description: `${product.name} has been added to your cart.`,
    })
  }

  // Export function to be used globally
  if (typeof window !== "undefined") {
    ;(window as any).addToCompare = addToCompare
  }

  if (compareList.length === 0) {
    return null
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="fixed bottom-4 right-4 z-50 shadow-lg">
          <Scale className="h-4 w-4 mr-2" />
          Compare ({compareList.length})
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex justify-between items-center">
            <DialogTitle>Product Comparison</DialogTitle>
            <Button variant="outline" size="sm" onClick={clearComparison}>
              Clear All
            </Button>
          </div>
        </DialogHeader>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr>
                <td className="p-4 font-medium">Product</td>
                {products.map((product) => (
                  <td key={product.id} className="p-4 min-w-[200px]">
                    <div className="relative">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute -top-2 -right-2 h-6 w-6 rounded-full bg-red-100 hover:bg-red-200"
                        onClick={() => removeFromCompare(product.id)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                      <div className="space-y-2">
                        <div className="relative aspect-square bg-muted rounded-md overflow-hidden">
                          <Image
                            src={product.images[0] || "https://images.unsplash.com/photo-1519238359922-989348752efb?q=80&w=200&h=200&auto=format&fit=crop"}
                            alt={product.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <h3 className="font-medium text-sm">{product.name}</h3>
                      </div>
                    </div>
                  </td>
                ))}
              </tr>
            </thead>
            <tbody>
              <tr className="border-t">
                <td className="p-4 font-medium">Price</td>
                {products.map((product) => (
                  <td key={product.id} className="p-4">
                    <div className="space-y-1">
                      <div className="font-semibold">${product.price.toFixed(2)}</div>
                      {product.compareAtPrice && (
                        <div className="text-sm text-muted-foreground line-through">
                          ${product.compareAtPrice.toFixed(2)}
                        </div>
                      )}
                    </div>
                  </td>
                ))}
              </tr>

              <tr className="border-t">
                <td className="p-4 font-medium">Status</td>
                {products.map((product) => (
                  <td key={product.id} className="p-4">
                    <div className="space-y-1">
                      {product.isNew && <Badge className="bg-[#012169]">New</Badge>}
                      {product.isSale && <Badge className="bg-[#6C1411]">Sale</Badge>}
                    </div>
                  </td>
                ))}
              </tr>

              <tr className="border-t">
                <td className="p-4 font-medium">Colors</td>
                {products.map((product) => (
                  <td key={product.id} className="p-4">
                    <div className="flex flex-wrap gap-1">
                      {product.colors.slice(0, 3).map((color) => (
                        <div
                          key={color.value}
                          className="w-4 h-4 rounded-full border"
                          style={{ backgroundColor: color.value }}
                          title={color.name}
                        />
                      ))}
                      {product.colors.length > 3 && (
                        <span className="text-xs text-muted-foreground">+{product.colors.length - 3} more</span>
                      )}
                    </div>
                  </td>
                ))}
              </tr>

              <tr className="border-t">
                <td className="p-4 font-medium">Sizes</td>
                {products.map((product) => (
                  <td key={product.id} className="p-4">
                    <div className="text-sm">{product.sizes.join(", ")}</div>
                  </td>
                ))}
              </tr>

              <tr className="border-t">
                <td className="p-4 font-medium">Description</td>
                {products.map((product) => (
                  <td key={product.id} className="p-4">
                    <div className="text-sm text-muted-foreground line-clamp-3">{product.description}</div>
                  </td>
                ))}
              </tr>

              <tr className="border-t">
                <td className="p-4 font-medium">Actions</td>
                {products.map((product) => (
                  <td key={product.id} className="p-4">
                    <Button
                      onClick={() => handleAddToCart(product)}
                      size="sm"
                      className="w-full bg-[#012169] hover:bg-[#012169]/90"
                    >
                      <ShoppingBag className="h-3 w-3 mr-1" />
                      Add to Cart
                    </Button>
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
        </div>
      </DialogContent>
    </Dialog>
  )
}
