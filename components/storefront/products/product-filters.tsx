"use client"

import { useRouter, usePathname, useSearchParams } from "next/navigation"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"

interface ProductFiltersProps {
  selectedCategory?: string
  selectedColor?: string
  selectedSize?: string
  onFilterChange?: (filters: { category: string; color: string; size: string }) => void
}

export function ProductFilters({ selectedCategory, selectedColor, selectedSize, onFilterChange }: ProductFiltersProps) {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const createQueryString = (name: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString())

    if (value) {
      params.set(name, value)
    } else {
      params.delete(name)
    }

    return params.toString()
  }

  const handleCategoryChange = (category: string) => {
    router.push(`${pathname}?${createQueryString("category", category)}`)
    if (onFilterChange) {
      onFilterChange({
        category,
        color: selectedColor || "",
        size: selectedSize || ""
      })
    }
  }

  const handleColorChange = (color: string) => {
    router.push(`${pathname}?${createQueryString("color", color)}`)
    if (onFilterChange) {
      onFilterChange({
        category: selectedCategory || "",
        color,
        size: selectedSize || ""
      })
    }
  }

  const handleSizeChange = (size: string) => {
    router.push(`${pathname}?${createQueryString("size", size)}`)
    if (onFilterChange) {
      onFilterChange({
        category: selectedCategory || "",
        color: selectedColor || "",
        size
      })
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-4">Filters</h3>
        <Accordion type="multiple" defaultValue={["category", "color", "size"]} className="w-full">
          <AccordionItem value="category">
            <AccordionTrigger>Category</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="category-tops"
                    checked={selectedCategory === "tops"}
                    onCheckedChange={() => handleCategoryChange(selectedCategory === "tops" ? "" : "tops")}
                  />
                  <Label htmlFor="category-tops" className="text-sm font-normal cursor-pointer">
                    Tops
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="category-bottoms"
                    checked={selectedCategory === "bottoms"}
                    onCheckedChange={() => handleCategoryChange(selectedCategory === "bottoms" ? "" : "bottoms")}
                  />
                  <Label htmlFor="category-bottoms" className="text-sm font-normal cursor-pointer">
                    Bottoms
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="category-dresses"
                    checked={selectedCategory === "dresses"}
                    onCheckedChange={() => handleCategoryChange(selectedCategory === "dresses" ? "" : "dresses")}
                  />
                  <Label htmlFor="category-dresses" className="text-sm font-normal cursor-pointer">
                    Dresses
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="category-outerwear"
                    checked={selectedCategory === "outerwear"}
                    onCheckedChange={() => handleCategoryChange(selectedCategory === "outerwear" ? "" : "outerwear")}
                  />
                  <Label htmlFor="category-outerwear" className="text-sm font-normal cursor-pointer">
                    Outerwear
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="category-accessories"
                    checked={selectedCategory === "accessories"}
                    onCheckedChange={() =>
                      handleCategoryChange(selectedCategory === "accessories" ? "" : "accessories")
                    }
                  />
                  <Label htmlFor="category-accessories" className="text-sm font-normal cursor-pointer">
                    Accessories
                  </Label>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="color">
            <AccordionTrigger>Color</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="color-black"
                    checked={selectedColor === "black"}
                    onCheckedChange={() => handleColorChange(selectedColor === "black" ? "" : "black")}
                  />
                  <Label htmlFor="color-black" className="text-sm font-normal cursor-pointer">
                    Black
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="color-white"
                    checked={selectedColor === "white"}
                    onCheckedChange={() => handleColorChange(selectedColor === "white" ? "" : "white")}
                  />
                  <Label htmlFor="color-white" className="text-sm font-normal cursor-pointer">
                    White
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="color-blue"
                    checked={selectedColor === "blue"}
                    onCheckedChange={() => handleColorChange(selectedColor === "blue" ? "" : "blue")}
                  />
                  <Label htmlFor="color-blue" className="text-sm font-normal cursor-pointer">
                    Blue
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="color-red"
                    checked={selectedColor === "red"}
                    onCheckedChange={() => handleColorChange(selectedColor === "red" ? "" : "red")}
                  />
                  <Label htmlFor="color-red" className="text-sm font-normal cursor-pointer">
                    Red
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="color-brown"
                    checked={selectedColor === "brown"}
                    onCheckedChange={() => handleColorChange(selectedColor === "brown" ? "" : "brown")}
                  />
                  <Label htmlFor="color-brown" className="text-sm font-normal cursor-pointer">
                    Brown
                  </Label>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="size">
            <AccordionTrigger>Size</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="size-xs"
                    checked={selectedSize === "xs"}
                    onCheckedChange={() => handleSizeChange(selectedSize === "xs" ? "" : "xs")}
                  />
                  <Label htmlFor="size-xs" className="text-sm font-normal cursor-pointer">
                    XS (2-3)
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="size-s"
                    checked={selectedSize === "s"}
                    onCheckedChange={() => handleSizeChange(selectedSize === "s" ? "" : "s")}
                  />
                  <Label htmlFor="size-s" className="text-sm font-normal cursor-pointer">
                    S (4-5)
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="size-m"
                    checked={selectedSize === "m"}
                    onCheckedChange={() => handleSizeChange(selectedSize === "m" ? "" : "m")}
                  />
                  <Label htmlFor="size-m" className="text-sm font-normal cursor-pointer">
                    M (6-7)
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="size-l"
                    checked={selectedSize === "l"}
                    onCheckedChange={() => handleSizeChange(selectedSize === "l" ? "" : "l")}
                  />
                  <Label htmlFor="size-l" className="text-sm font-normal cursor-pointer">
                    L (8-9)
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="size-xl"
                    checked={selectedSize === "xl"}
                    onCheckedChange={() => handleSizeChange(selectedSize === "xl" ? "" : "xl")}
                  />
                  <Label htmlFor="size-xl" className="text-sm font-normal cursor-pointer">
                    XL (10-12)
                  </Label>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  )
}
