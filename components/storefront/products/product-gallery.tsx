"use client"

import { useState } from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"

interface ProductGalleryProps {
  images: string[]
}

export function ProductGallery({ images }: ProductGalleryProps) {
  const [selectedImage, setSelectedImage] = useState(0)

  // Use Coco Milk Kids images if no images are provided
  const stockImages = [
    "/assets/images/cocomilk_kids-20221216_070828-1200554041.jpg",
    "/assets/images/cocomilk_kids-20221221_105615-2775996687.jpg",
    "/assets/images/cocomilk_kids-20230112_110126-3728995334.jpg",
    "/assets/images/cocomilk_kids-20221021_091054-2430450557.jpg",
  ]

  const displayImages = images.length > 0 ? images : stockImages

  return (
    <div className="space-y-4">
      <div className="relative aspect-square overflow-hidden bg-muted image-zoom">
        <Image
          src={displayImages[selectedImage] || "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"}
          alt="Product image"
          fill
          className="object-cover"
        />
      </div>

      <div className="flex space-x-3 overflow-x-auto pb-2">
        {displayImages.map((image, index) => (
          <button
            key={index}
            className={cn(
              "relative w-20 h-20 overflow-hidden bg-muted flex-shrink-0 cursor-pointer transition-all duration-300",
              selectedImage === index ? "ring-2 ring-[#6C1411]" : "opacity-70 hover:opacity-100",
            )}
            onClick={() => setSelectedImage(index)}
          >
            <Image
              src={image || "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"}
              alt={`Product thumbnail ${index + 1}`}
              fill
              className="object-cover"
            />
          </button>
        ))}
      </div>
    </div>
  )
}
