"use client"

import Image from "next/image"

interface ProductSearchResultProps {
  product: {
    id: string
    name: string
    slug: string
    price: number
    compareAtPrice?: number
    images: string[]
  }
  onClick: (slug: string) => void
}

export function ProductSearchResult({ product, onClick }: ProductSearchResultProps) {
  return (
    <button
      className="flex w-full items-center space-x-4 rounded-md p-2 text-left hover:bg-muted"
      onClick={() => onClick(product.slug)}
    >
      <div className="relative h-16 w-16 overflow-hidden rounded-md bg-muted">
        <Image
          src={product.images[0] || "https://images.unsplash.com/photo-1519238359922-989348752efb?q=80&w=64&h=64&auto=format&fit=crop"}
          alt={product.name}
          fill
          className="object-cover"
        />
      </div>
      <div className="flex-1">
        <h4 className="text-sm font-medium">{product.name}</h4>
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">${product.price.toFixed(2)}</span>
          {product.compareAtPrice && (
            <span className="text-xs text-muted-foreground line-through">${product.compareAtPrice.toFixed(2)}</span>
          )}
        </div>
      </div>
    </button>
  )
}
