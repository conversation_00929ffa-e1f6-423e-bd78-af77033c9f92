"use client"

import { useCart } from "@/hooks/use-cart"
import { usePriceFormatter } from "@/hooks/use-price-formatter"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import Link from "next/link"
import { usePathname } from "next/navigation"

export function CartSummary() {
  const { totalPrice } = useCart()
  const { formatPrice } = usePriceFormatter()
  const pathname = usePathname()
  const isCheckout = pathname === "/checkout"

  // Estimated values for demonstration (in ZAR)
  const shippingPrice = 99
  const taxRate = 0.15 // South African VAT rate
  const taxPrice = totalPrice * taxRate
  const totalWithTax = totalPrice + shippingPrice + taxPrice

  return (
    <div className="border rounded-md p-4 md:p-6 space-y-4">
      <h2 className="text-lg font-medium">Order Summary</h2>

      {!isCheckout && (
        <div className="flex space-x-2">
          <Input placeholder="Discount code" />
          <Button variant="outline">Apply</Button>
        </div>
      )}

      <div className="space-y-2">
        <div className="flex justify-between">
          <span>Subtotal</span>
          <span>{formatPrice(totalPrice)}</span>
        </div>
        <div className="flex justify-between">
          <span>Shipping</span>
          <span>{formatPrice(shippingPrice)}</span>
        </div>
        <div className="flex justify-between">
          <span>VAT (15%)</span>
          <span>{formatPrice(taxPrice)}</span>
        </div>
      </div>

      <Separator />

      <div className="flex justify-between text-lg font-medium">
        <span>Total</span>
        <span>{formatPrice(totalWithTax)}</span>
      </div>

      {!isCheckout && (
        <Button asChild className="w-full bg-[#012169] hover:bg-[#012169]/90">
          <Link href="/checkout">Proceed to Checkout</Link>
        </Button>
      )}
    </div>
  )
}
