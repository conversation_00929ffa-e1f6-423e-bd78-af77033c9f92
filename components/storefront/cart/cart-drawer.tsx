"use client"

import { useCart } from "@/hooks/use-cart"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/sheet"
import { CartItem } from "@/components/storefront/cart/cart-item"
import { ShoppingBag } from "lucide-react"
import Link from "next/link"

interface CartDrawerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CartDrawer({ open, onOpenChange }: CartDrawerProps) {
  const { items, totalPrice, isEmpty } = useCart()

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="flex flex-col w-full sm:max-w-lg">
        <SheetHeader>
          <SheetTitle>Shopping Cart</SheetTitle>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto py-4">
          {isEmpty ? (
            <div className="flex flex-col items-center justify-center h-full space-y-4 text-center">
              <ShoppingBag className="h-12 w-12 text-muted-foreground" />
              <div>
                <h3 className="text-lg font-medium">Your cart is empty</h3>
                <p className="text-sm text-muted-foreground">Looks like you haven't added anything to your cart yet.</p>
              </div>
              <Button asChild onClick={() => onOpenChange(false)}>
                <Link href="/products">Continue Shopping</Link>
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {items.map((item) => (
                <CartItem key={`${item.id}-${item.size}`} item={item} isCompact />
              ))}
            </div>
          )}
        </div>

        {!isEmpty && (
          <SheetFooter className="border-t pt-4">
            <div className="space-y-4 w-full">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>${totalPrice.toFixed(2)}</span>
              </div>
              <Button asChild className="w-full bg-coco-red hover:bg-coco-red/90">
                <Link href="/checkout" onClick={() => onOpenChange(false)}>
                  Checkout
                </Link>
              </Button>
              <Button variant="outline" asChild className="w-full" onClick={() => onOpenChange(false)}>
                <Link href="/cart">View Cart</Link>
              </Button>
            </div>
          </SheetFooter>
        )}
      </SheetContent>
    </Sheet>
  )
}
