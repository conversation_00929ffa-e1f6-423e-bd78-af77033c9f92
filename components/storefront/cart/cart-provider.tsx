"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState } from "react"
import { useCart as useEcommerceCart } from "@/lib/ecommerce/hooks/use-cart"
import { CartItem as EcommerceCartItem } from "@/lib/ecommerce/types"

type CartItem = {
  id: string
  name: string
  price: number
  color: string
  size: string
  quantity: number
  image: string
}

type CartContextType = {
  items: CartItem[]
  itemCount: number
  totalPrice: number
  isEmpty: boolean
  addItem: (item: CartItem) => void
  updateItemQuantity: (id: string, size: string, quantity: number) => void
  removeItem: (id: string, size: string) => void
  clearCart: () => void
  loading: boolean
  error: string | null
}

export const CartContext = createContext<CartContextType | undefined>(undefined)

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [items, setItems] = useState<CartItem[]>([])
  const [isClient, setIsClient] = useState(false)

  // Use e-commerce cart hook for real cart functionality
  const {
    cart: ecommerceCart,
    loading: ecommerceLoading,
    error: ecommerceError,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart: clearEcommerceCart
  } = useEcommerceCart({
    sessionId: typeof window !== 'undefined' ? window.sessionStorage.getItem('sessionId') || undefined : undefined,
    autoFetch: true
  })

  useEffect(() => {
    setIsClient(true)
    // Initialize with localStorage for backward compatibility
    const storedCart = localStorage.getItem("cart")
    if (storedCart) {
      try {
        const localItems = JSON.parse(storedCart)
        setItems(localItems)
      } catch (error) {
        console.error("Failed to parse cart from localStorage", error)
        setItems([])
      }
    }
  }, []) // Remove ecommerceCart dependency to prevent infinite loop

  // Sync with e-commerce cart when available
  useEffect(() => {
    if (ecommerceCart && ecommerceCart.items && isClient) {
      const convertedItems: CartItem[] = ecommerceCart.items.map((item: EcommerceCartItem) => ({
        id: item.productId,
        name: item.productTitle,
        price: item.unitPrice.amount,
        color: item.variantOptions?.find(opt => opt.name.toLowerCase() === 'color')?.value || '',
        size: item.variantOptions?.find(opt => opt.name.toLowerCase() === 'size')?.value || '',
        quantity: item.quantity,
        image: item.productImage || ''
      }))

      // Only update if items have actually changed to prevent infinite loops
      setItems(prevItems => {
        const hasChanged = JSON.stringify(prevItems) !== JSON.stringify(convertedItems)
        if (hasChanged) {
          // Update localStorage for backward compatibility
          localStorage.setItem("cart", JSON.stringify(convertedItems))
          return convertedItems
        }
        return prevItems
      })
    }
  }, [ecommerceCart?.items, isClient]) // Use ecommerceCart.items instead of entire cart object

  useEffect(() => {
    if (isClient && items.length > 0 && !ecommerceCart?.items?.length) {
      localStorage.setItem("cart", JSON.stringify(items))
    }
  }, [items, isClient, ecommerceCart?.items?.length]) // Use length instead of entire cart object

  const itemCount = items.reduce((total, item) => total + item.quantity, 0)

  const totalPrice = items.reduce((total, item) => total + item.price * item.quantity, 0)

  const isEmpty = items.length === 0

  const addItem = async (newItem: CartItem) => {
    try {
      // Try to add to e-commerce cart first
      if (addToCart) {
        await addToCart({
          productId: newItem.id,
          quantity: newItem.quantity,
          variantOptions: [
            { name: 'color', value: newItem.color },
            { name: 'size', value: newItem.size }
          ]
        })
      } else {
        // Fallback to localStorage
        setItems((prevItems) => {
          const existingItemIndex = prevItems.findIndex((item) => item.id === newItem.id && item.size === newItem.size)

          if (existingItemIndex > -1) {
            const updatedItems = [...prevItems]
            updatedItems[existingItemIndex].quantity += newItem.quantity
            return updatedItems
          } else {
            return [...prevItems, newItem]
          }
        })
      }
    } catch (error) {
      console.error('Failed to add item to cart:', error)
      // Fallback to localStorage on error
      setItems((prevItems) => {
        const existingItemIndex = prevItems.findIndex((item) => item.id === newItem.id && item.size === newItem.size)

        if (existingItemIndex > -1) {
          const updatedItems = [...prevItems]
          updatedItems[existingItemIndex].quantity += newItem.quantity
          return updatedItems
        } else {
          return [...prevItems, newItem]
        }
      })
    }
  }

  const updateItemQuantity = async (id: string, size: string, quantity: number) => {
    try {
      // Try to update e-commerce cart first
      if (updateCartItem && ecommerceCart) {
        const cartItem = ecommerceCart.items.find(item =>
          item.productId === id &&
          item.variantOptions?.some(opt => opt.name === 'size' && opt.value === size)
        )

        if (cartItem) {
          await updateCartItem({
            cartItemId: cartItem.id,
            quantity
          })
        }
      } else {
        // Fallback to localStorage
        setItems((prevItems) =>
          prevItems.map((item) => (item.id === id && item.size === size ? { ...item, quantity } : item)),
        )
      }
    } catch (error) {
      console.error('Failed to update cart item:', error)
      // Fallback to localStorage on error
      setItems((prevItems) =>
        prevItems.map((item) => (item.id === id && item.size === size ? { ...item, quantity } : item)),
      )
    }
  }

  const removeItem = async (id: string, size: string) => {
    try {
      // Try to remove from e-commerce cart first
      if (removeFromCart && ecommerceCart) {
        const cartItem = ecommerceCart.items.find(item =>
          item.productId === id &&
          item.variantOptions?.some(opt => opt.name === 'size' && opt.value === size)
        )

        if (cartItem) {
          await removeFromCart({
            cartItemId: cartItem.id
          })
        }
      } else {
        // Fallback to localStorage
        setItems((prevItems) => prevItems.filter((item) => !(item.id === id && item.size === size)))
      }
    } catch (error) {
      console.error('Failed to remove cart item:', error)
      // Fallback to localStorage on error
      setItems((prevItems) => prevItems.filter((item) => !(item.id === id && item.size === size)))
    }
  }

  const clearCart = async () => {
    try {
      // Try to clear e-commerce cart first
      if (clearEcommerceCart) {
        await clearEcommerceCart()
      }

      // Clear localStorage
      setItems([])
      if (isClient) {
        localStorage.removeItem("cart")
      }
    } catch (error) {
      console.error('Failed to clear cart:', error)
      // Fallback to localStorage on error
      setItems([])
      if (isClient) {
        localStorage.removeItem("cart")
      }
    }
  }

  return (
    <CartContext.Provider
      value={{
        items,
        itemCount,
        totalPrice,
        isEmpty,
        addItem,
        updateItemQuantity,
        removeItem,
        clearCart,
        loading: ecommerceLoading,
        error: ecommerceError,
      }}
    >
      {children}
    </CartContext.Provider>
  )
}

export function useCart() {
  const context = useContext(CartContext)
  if (context === undefined) {
    throw new Error("useCart must be used within a CartProvider")
  }
  return context
}
