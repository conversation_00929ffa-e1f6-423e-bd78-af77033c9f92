"use client"

import Image from "next/image"
import { useCart } from "@/hooks/use-cart"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { X } from "lucide-react"

interface CartItemProps {
  item: {
    id: string
    name: string
    price: number
    color: string
    size: string
    quantity: number
    image: string
  }
  isCompact?: boolean
}

export function CartItem({ item, isCompact = false }: CartItemProps) {
  const { updateItemQuantity, removeItem } = useCart()

  const handleQuantityChange = (value: string) => {
    const quantity = Number.parseInt(value)
    updateItemQuantity(item.id, item.size, quantity)
  }

  const handleRemove = () => {
    removeItem(item.id, item.size)
  }

  if (isCompact) {
    return (
      <div className="flex items-center space-x-4">
        <div className="relative w-16 h-16 bg-muted rounded-md overflow-hidden flex-shrink-0">
          <Image
            src={item.image || "https://images.unsplash.com/photo-1519238359922-989348752efb?q=80&w=64&h=64&auto=format&fit=crop"}
            alt={item.name}
            fill
            className="object-cover"
          />
        </div>

        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium truncate">{item.name}</h4>
          <p className="text-xs text-muted-foreground">
            {item.color} / {item.size} / Qty: {item.quantity}
          </p>
          <p className="text-sm font-medium">${(item.price * item.quantity).toFixed(2)}</p>
        </div>

        <Button variant="ghost" size="icon" className="h-8 w-8" onClick={handleRemove}>
          <X className="h-4 w-4" />
          <span className="sr-only">Remove</span>
        </Button>
      </div>
    )
  }

  return (
    <div className="flex items-start space-x-4 border rounded-md p-4">
      <div className="relative w-24 h-24 bg-muted rounded-md overflow-hidden flex-shrink-0">
        <Image
          src={item.image || "https://images.unsplash.com/photo-1519238359922-989348752efb?q=80&w=96&h=96&auto=format&fit=crop"}
          alt={item.name}
          fill
          className="object-cover"
        />
      </div>

      <div className="flex-1 min-w-0">
        <div className="flex justify-between">
          <h4 className="text-base font-medium">{item.name}</h4>
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={handleRemove}>
            <X className="h-4 w-4" />
            <span className="sr-only">Remove</span>
          </Button>
        </div>

        <p className="text-sm text-muted-foreground mb-2">
          {item.color} / {item.size}
        </p>

        <div className="flex justify-between items-center">
          <Select value={item.quantity.toString()} onValueChange={handleQuantityChange}>
            <SelectTrigger className="w-20">
              <SelectValue placeholder="1" />
            </SelectTrigger>
            <SelectContent>
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                <SelectItem key={num} value={num.toString()}>
                  {num}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <p className="text-base font-medium">${(item.price * item.quantity).toFixed(2)}</p>
        </div>
      </div>
    </div>
  )
}
