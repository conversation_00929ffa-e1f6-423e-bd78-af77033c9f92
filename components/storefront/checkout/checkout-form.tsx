"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { CreditCard, Truck, Loader2 } from "lucide-react"
import { useCart } from "@/hooks/use-cart"
import { useCheckout, CheckoutData } from "@/lib/ecommerce/hooks/use-payments"
import { toast } from "@/components/ui/use-toast"

interface CheckoutFormProps {
  step: "shipping" | "payment";
  setStep: (step: "shipping" | "payment") => void;
  shippingMethod: string;
  setShippingMethod: (method: string) => void;
  onCompleteOrder?: (orderId: string, paymentUrl?: string) => void;
}

export function CheckoutForm({
  step,
  setStep,
  shippingMethod,
  setShippingMethod,
  onCompleteOrder
}: CheckoutFormProps) {
  const { items } = useCart()
  const { processCheckout, loading: checkoutLoading, error: checkoutError } = useCheckout()

  const [paymentMethod, setPaymentMethod] = useState("card")
  const [isProcessing, setIsProcessing] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    phone: '',
    firstName: '',
    lastName: '',
    address: '',
    apartment: '',
    city: '',
    province: '',
    postalCode: '',
    billingAddressSame: true,
    saveCard: false,
    agreeToTerms: false
  })

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmitShipping = (e: React.FormEvent) => {
    e.preventDefault()

    // Validate required fields
    const requiredFields = ['email', 'phone', 'firstName', 'lastName', 'address', 'city', 'province', 'postalCode']
    const missingFields = requiredFields.filter(field => !formData[field as keyof typeof formData])

    if (missingFields.length > 0) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      })
      return
    }

    setStep("payment")
  }

  const handleSubmitPayment = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.agreeToTerms) {
      toast({
        title: "Terms Required",
        description: "Please agree to the terms and conditions.",
        variant: "destructive"
      })
      return
    }

    if (items.length === 0) {
      toast({
        title: "Empty Cart",
        description: "Your cart is empty. Please add items before checking out.",
        variant: "destructive"
      })
      return
    }

    setIsProcessing(true)

    try {
      const checkoutData: CheckoutData = {
        customerEmail: formData.email,
        customerPhone: formData.phone,
        customerName: `${formData.firstName} ${formData.lastName}`,
        shippingAddress: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          address: formData.address,
          apartment: formData.apartment,
          city: formData.city,
          province: formData.province,
          postalCode: formData.postalCode,
          country: 'ZA'
        },
        billingAddress: formData.billingAddressSame ? undefined : {
          firstName: formData.firstName,
          lastName: formData.lastName,
          address: formData.address,
          apartment: formData.apartment,
          city: formData.city,
          province: formData.province,
          postalCode: formData.postalCode,
          country: 'ZA'
        },
        shippingMethod,
        paymentMethod,
        items: items.map(item => ({
          productId: item.id,
          quantity: item.quantity,
          price: item.price,
          name: item.name,
          image: item.image,
          color: item.color,
          size: item.size
        }))
      }

      const result = await processCheckout(checkoutData)

      if (result?.success) {
        toast({
          title: "Order Created",
          description: `Order ${result.orderId} has been created successfully.`,
        })

        // Call the onCompleteOrder callback with order ID and payment URL
        if (onCompleteOrder) {
          onCompleteOrder(result.orderId, result.paymentUrl)
        }

        // If there's a payment URL, redirect to payment gateway
        if (result.paymentUrl) {
          window.location.href = result.paymentUrl
        }
      } else {
        toast({
          title: "Checkout Failed",
          description: result?.error || checkoutError || "Failed to process checkout. Please try again.",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Checkout error:', error)
      toast({
        title: "Checkout Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <Tabs value={step} className="space-y-6">
      <TabsList className="grid grid-cols-2">
        <TabsTrigger value="shipping" disabled={step === "payment"}>
          Shipping
        </TabsTrigger>
        <TabsTrigger value="payment" disabled={step === "shipping"}>
          Payment
        </TabsTrigger>
      </TabsList>

      <TabsContent value="shipping" className="space-y-6">
        <div>
          <h2 className="text-lg font-medium mb-4">Contact Information</h2>
          <form onSubmit={handleSubmitShipping} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  required
                />
              </div>
            </div>

            <Separator />

            <h2 className="text-lg font-medium">Shipping Address</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="apartment">Apartment, suite, etc. (optional)</Label>
              <Input
                id="apartment"
                value={formData.apartment}
                onChange={(e) => handleInputChange('apartment', e.target.value)}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="province">Province</Label>
                <Select value={formData.province} onValueChange={(value) => handleInputChange('province', value)}>
                  <SelectTrigger id="province">
                    <SelectValue placeholder="Select province" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gp">Gauteng</SelectItem>
                    <SelectItem value="wc">Western Cape</SelectItem>
                    <SelectItem value="kzn">KwaZulu-Natal</SelectItem>
                    <SelectItem value="ec">Eastern Cape</SelectItem>
                    <SelectItem value="fs">Free State</SelectItem>
                    <SelectItem value="lp">Limpopo</SelectItem>
                    <SelectItem value="mp">Mpumalanga</SelectItem>
                    <SelectItem value="nw">North West</SelectItem>
                    <SelectItem value="nc">Northern Cape</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="postalCode">Postal Code</Label>
                <Input
                  id="postalCode"
                  placeholder="0000"
                  value={formData.postalCode}
                  onChange={(e) => handleInputChange('postalCode', e.target.value)}
                  required
                />
              </div>
            </div>

            <Separator />

            <h2 className="text-lg font-medium">Shipping Method</h2>
            <RadioGroup
              value={shippingMethod}
              onValueChange={setShippingMethod}
              className="space-y-2"
            >
              <div className={`flex items-center space-x-2 border rounded-md p-3 ${shippingMethod === 'standard' ? 'border-[#012169]/50 bg-[#012169]/5' : ''}`}>
                <RadioGroupItem value="standard" id="standard" />
                <Label htmlFor="standard" className="flex-1 cursor-pointer">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <Truck className="h-4 w-4" />
                      <span>Standard Shipping (3-5 business days)</span>
                    </div>
                    <span>R99</span>
                  </div>
                </Label>
              </div>
              <div className={`flex items-center space-x-2 border rounded-md p-3 ${shippingMethod === 'express' ? 'border-[#012169]/50 bg-[#012169]/5' : ''}`}>
                <RadioGroupItem value="express" id="express" />
                <Label htmlFor="express" className="flex-1 cursor-pointer">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <Truck className="h-4 w-4" />
                      <span>Express Shipping (1-2 business days)</span>
                    </div>
                    <span>R199</span>
                  </div>
                </Label>
              </div>
              <div className={`flex items-center space-x-2 border rounded-md p-3 ${shippingMethod === 'collection' ? 'border-[#012169]/50 bg-[#012169]/5' : ''}`}>
                <RadioGroupItem value="collection" id="collection" />
                <Label htmlFor="collection" className="flex-1 cursor-pointer">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <span>🏪</span>
                      <span>Collection (Sandton Store)</span>
                    </div>
                    <span className="text-green-600 font-medium">Free</span>
                  </div>
                </Label>
              </div>
            </RadioGroup>

            {/* Estimated delivery date */}
            <div className="mt-2 text-sm text-muted-foreground">
              <p>
                Estimated delivery:
                <span className="font-medium ml-1">
                  {shippingMethod === 'express'
                    ? new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', { month: 'long', day: 'numeric' })
                    : new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', { month: 'long', day: 'numeric' })
                  }
                </span>
              </p>
            </div>

            <div className="flex justify-end">
              <Button type="submit" className="bg-[#012169] hover:bg-[#012169]/90">
                Continue to Payment
              </Button>
            </div>
          </form>
        </div>
      </TabsContent>

      <TabsContent value="payment" className="space-y-6">
        <div>
          <h2 className="text-lg font-medium mb-4">Payment Method</h2>
          <form onSubmit={handleSubmitPayment} className="space-y-4">
            <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod} className="space-y-2">
              <div className="flex items-center space-x-2 border rounded-md p-3">
                <RadioGroupItem value="card" id="card" />
                <Label htmlFor="card" className="flex-1 cursor-pointer">
                  <div className="flex items-center space-x-2">
                    <CreditCard className="h-4 w-4" />
                    <span>Credit / Debit Card</span>
                    <span className="text-xs text-gray-500">(Visa, Mastercard, Amex)</span>
                  </div>
                </Label>
              </div>
              <div className="flex items-center space-x-2 border rounded-md p-3">
                <RadioGroupItem value="eft" id="eft" />
                <Label htmlFor="eft" className="flex-1 cursor-pointer">
                  <div className="flex items-center space-x-2">
                    <span>EFT / Bank Transfer</span>
                    <span className="text-xs text-gray-500">(All major SA banks)</span>
                  </div>
                </Label>
              </div>
              <div className="flex items-center space-x-2 border rounded-md p-3">
                <RadioGroupItem value="snapscan" id="snapscan" />
                <Label htmlFor="snapscan" className="flex-1 cursor-pointer">
                  <div className="flex items-center space-x-2">
                    <span>SnapScan</span>
                    <span className="text-xs text-gray-500">(Scan to pay)</span>
                  </div>
                </Label>
              </div>
              {shippingMethod === "collection" && (
                <div className="flex items-center space-x-2 border rounded-md p-3">
                  <RadioGroupItem value="cash" id="cash" />
                  <Label htmlFor="cash" className="flex-1 cursor-pointer">
                    <div className="flex items-center space-x-2">
                      <span>Cash on Collection</span>
                      <span className="text-xs text-gray-500">(Pay when collecting)</span>
                    </div>
                  </Label>
                </div>
              )}
            </RadioGroup>

            {paymentMethod === "card" && (
              <div className="space-y-4 border rounded-md p-4">
                <div className="space-y-2">
                  <Label htmlFor="cardNumber">Card Number</Label>
                  <Input id="cardNumber" placeholder="1234 5678 9012 3456" required />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="expiry">Expiry Date</Label>
                    <Input id="expiry" placeholder="MM/YY" required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cvc">CVC</Label>
                    <Input id="cvc" placeholder="123" required />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="nameOnCard">Name on Card</Label>
                  <Input id="nameOnCard" required />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox id="saveCard" />
                  <Label htmlFor="saveCard" className="text-sm">
                    Save card for future purchases
                  </Label>
                </div>
              </div>
            )}

            <Separator />

            <div className="space-y-2">
              <Label htmlFor="billingAddress">Billing Address</Label>
              <Select defaultValue="same">
                <SelectTrigger id="billingAddress">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="same">Same as shipping address</SelectItem>
                  <SelectItem value="different">Use a different billing address</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="terms"
                checked={formData.agreeToTerms}
                onCheckedChange={(checked) => handleInputChange('agreeToTerms', checked as boolean)}
                required
              />
              <Label htmlFor="terms" className="text-sm">
                I agree to the terms and conditions and privacy policy
              </Label>
            </div>

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => setStep("shipping")}
                disabled={isProcessing}
              >
                Back to Shipping
              </Button>
              <Button
                type="submit"
                className="bg-[#012169] hover:bg-[#012169]/90"
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  "Place Order"
                )}
              </Button>
            </div>
          </form>
        </div>
      </TabsContent>
    </Tabs>
  )
}
