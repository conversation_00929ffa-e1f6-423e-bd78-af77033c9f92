"use client"

import { useCart } from "@/hooks/use-cart"
import { usePriceFormatter } from "@/hooks/use-price-formatter"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { useState } from "react"
import {
  Package,
  ChevronDown,
  ChevronUp,
  CreditCard,
  ShieldCheck,
  Gift,
  Truck
} from "lucide-react"

interface CheckoutSummaryProps {
  shippingMethod?: string;
  step?: "shipping" | "payment";
}

export function CheckoutSummary({ shippingMethod = "standard", step = "shipping" }: CheckoutSummaryProps) {
  const { items, totalPrice } = useCart()
  const { formatPrice } = usePriceFormatter()
  const [isOpen, setIsOpen] = useState(false)
  const [promoCode, setPromoCode] = useState("")
  const [promoApplied, setPromoApplied] = useState(false)
  const [giftWrap, setGiftWrap] = useState(false)

  // Shipping costs in ZAR
  const shippingCosts = {
    standard: 99,
    express: 199
  }

  // Estimated delivery dates
  const deliveryDates = {
    standard: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
    express: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000)   // 2 days from now
  }

  const shippingPrice = shippingCosts[shippingMethod as keyof typeof shippingCosts]
  const estimatedDelivery = deliveryDates[shippingMethod as keyof typeof deliveryDates]
  const giftWrapPrice = giftWrap ? 89 : 0 // Gift wrap price in ZAR
  const taxRate = 0.15 // South African VAT rate
  const discountAmount = promoApplied ? totalPrice * 0.1 : 0 // 10% discount if promo applied
  const subtotalAfterDiscount = totalPrice - discountAmount
  const taxPrice = subtotalAfterDiscount * taxRate
  const totalWithTax = subtotalAfterDiscount + shippingPrice + taxPrice + giftWrapPrice

  // Format date to display as "Month Day" using South African locale
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-ZA', { month: 'long', day: 'numeric' });
  };

  // Handle promo code application
  const handleApplyPromo = () => {
    if (promoCode.toLowerCase() === 'welcome10') {
      setPromoApplied(true);
    } else {
      setPromoApplied(false);
    }
  };

  return (
    <div className="border rounded-md p-4 md:p-6 space-y-4 sticky top-6 bg-white shadow-sm">
      <Collapsible open={isOpen || typeof window !== 'undefined' && window.innerWidth >= 768}>
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-medium">Order Summary</h2>
          <div className="md:hidden">
            <CollapsibleTrigger
              onClick={() => setIsOpen(!isOpen)}
              className="flex items-center text-sm text-muted-foreground"
            >
              {isOpen ? (
                <>
                  <span>Hide details</span>
                  <ChevronUp className="ml-1 h-4 w-4" />
                </>
              ) : (
                <>
                  <span>Show details</span>
                  <ChevronDown className="ml-1 h-4 w-4" />
                </>
              )}
            </CollapsibleTrigger>
          </div>
        </div>

        <CollapsibleContent className="space-y-4 md:block">
          <div className="space-y-2 max-h-40 overflow-y-auto pr-2">
            {items.map((item) => (
              <div key={`${item.id}-${item.size}`} className="flex justify-between text-sm">
                <div className="flex items-center">
                  <Package className="h-3 w-3 mr-2 text-muted-foreground" />
                  <span>
                    {item.name} ({item.size}) x {item.quantity}
                  </span>
                </div>
                <span>{formatPrice(item.price * item.quantity)}</span>
              </div>
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>

      <Separator />

      {/* Promo code section */}
      <div className="space-y-2">
        <Label htmlFor="promo-code" className="text-sm font-medium">Promo Code</Label>
        <div className="flex space-x-2">
          <Input
            id="promo-code"
            placeholder="Enter code"
            value={promoCode}
            onChange={(e) => setPromoCode(e.target.value)}
            className="h-9"
          />
          <Button
            onClick={handleApplyPromo}
            variant="outline"
            size="sm"
            className="whitespace-nowrap"
          >
            Apply
          </Button>
        </div>
        {promoApplied && (
          <p className="text-green-600 text-xs">Promo code applied: 10% off</p>
        )}
      </div>

      {/* Gift wrap option */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="gift-wrap"
          checked={giftWrap}
          onCheckedChange={(checked) => setGiftWrap(checked === true)}
        />
        <div className="grid gap-1.5 leading-none">
          <Label htmlFor="gift-wrap" className="text-sm font-medium flex items-center">
            <Gift className="h-3 w-3 mr-1" />
            Gift wrap
          </Label>
          <p className="text-xs text-muted-foreground">Add gift wrapping for {formatPrice(89)}</p>
        </div>
      </div>

      <Separator />

      <div className="space-y-2">
        <div className="flex justify-between">
          <span>Subtotal</span>
          <span>{formatPrice(totalPrice)}</span>
        </div>
        {promoApplied && (
          <div className="flex justify-between text-green-600">
            <span>Discount (10%)</span>
            <span>-{formatPrice(discountAmount)}</span>
          </div>
        )}
        <div className="flex justify-between">
          <span>Shipping ({shippingMethod === 'express' ? 'Express' : 'Standard'})</span>
          <span>{formatPrice(shippingPrice)}</span>
        </div>
        {giftWrap && (
          <div className="flex justify-between">
            <span>Gift Wrap</span>
            <span>{formatPrice(giftWrapPrice)}</span>
          </div>
        )}
        <div className="flex justify-between">
          <span>VAT (15%)</span>
          <span>{formatPrice(taxPrice)}</span>
        </div>
      </div>

      <Separator />

      <div className="flex justify-between text-lg font-medium">
        <span>Total</span>
        <span>{formatPrice(totalWithTax)}</span>
      </div>

      {/* Estimated delivery */}
      <div className="bg-gray-50 p-3 rounded-md text-sm">
        <div className="flex items-center text-muted-foreground mb-1">
          <Truck className="h-4 w-4 mr-2" />
          <span>Estimated Delivery</span>
        </div>
        <p className="font-medium">
          {formatDate(estimatedDelivery)}
          {shippingMethod === 'express' ? ' (Express)' : ' (Standard)'}
        </p>
      </div>

      {/* Security badges */}
      <div className="flex items-center justify-center space-x-4 text-xs text-muted-foreground">
        <div className="flex items-center">
          <ShieldCheck className="h-3 w-3 mr-1" />
          <span>Secure Checkout</span>
        </div>
        <div className="flex items-center">
          <CreditCard className="h-3 w-3 mr-1" />
          <span>Encrypted Payment</span>
        </div>
      </div>

      <div className="pt-2">
        <Button
          type="submit"
          form="checkout-form"
          className="w-full bg-[#012169] hover:bg-[#012169]/90 font-medium"
        >
          {step === "shipping" ? "Continue to Payment" : "Complete Order"}
        </Button>
      </div>
    </div>
  )
}