"use client"

import { <PERSON>, <PERSON>, <PERSON>, Truck } from "lucide-react"
import { motion } from "framer-motion"

const values = [
  {
    icon: <Leaf className="h-10 w-10" />,
    title: "Sustainable Materials",
    description: "We use organic cotton and eco-friendly materials that are gentle on your child's skin and the planet."
  },
  {
    icon: <Shield className="h-10 w-10" />,
    title: "Quality Guarantee",
    description: "Our clothes are built to last, designed to withstand active play and countless washes."
  },
  {
    icon: <Heart className="h-10 w-10" />,
    title: "Comfort First",
    description: "Soft fabrics and thoughtful designs ensure your child stays comfortable throughout their day."
  },
  {
    icon: <Truck className="h-10 w-10" />,
    title: "Free Shipping",
    description: "Enjoy free shipping on all orders over $50, with easy returns and exchanges."
  }
]

export function BrandValues() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  }

  return (
    <section className="py-20 bg-white">
      <div className="container px-4 md:px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-montserrat font-light tracking-wide mb-4">Why Choose Coco Milk Kids</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto font-light">
            Our commitment to quality, comfort, and sustainability makes us the preferred choice for parents
          </p>
        </div>

        <motion.div 
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {values.map((value, index) => (
            <motion.div 
              key={index} 
              variants={itemVariants}
              className="flex flex-col items-center text-center p-6 border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <div className="text-[#012169] mb-4">
                {value.icon}
              </div>
              <h3 className="text-xl font-medium mb-2">{value.title}</h3>
              <p className="text-muted-foreground font-light">{value.description}</p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
