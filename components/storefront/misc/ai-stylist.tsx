"use client"

import type React from "react"

import { useState } from "react"
import { useChat, Message } from "@ai-sdk/react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Loader2, Sparkles } from "lucide-react"

export function AIStylist() {
  const [preferences, setPreferences] = useState({
    age: "",
    gender: "Any",
    occasion: "Casual",
    season: "Summer",
    colors: "",
    style: "",
    climate: "Gauteng",
    budget: "Mid-range",
  })

  const [formSubmitted, setFormSubmitted] = useState(false)

  const { messages, status, error, append } = useChat({
    api: "/api/ai-stylist",
  })

  const isLoading = status === 'streaming'

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setPreferences((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setPreferences((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormSubmitted(true)

    // Create the prompt with the user's preferences
    const prompt = `Create a stylish outfit recommendation for a South African child with the following preferences:
    Age: ${preferences.age}
    Gender: ${preferences.gender}
    Occasion: ${preferences.occasion}
    Season: ${preferences.season} (South African seasons)
    Climate/Region: ${preferences.climate}
    Color Preferences: ${preferences.colors}
    Style Preferences: ${preferences.style}
    Budget: ${preferences.budget}

    Consider South African climate, lifestyle, and fashion preferences. Suggest a complete outfit with specific items from our Coco Milk Kids collection. Include pricing in South African Rand (ZAR) and mention why each piece works well for South African children. Consider factors like sun protection, comfort in heat, and durability for active play.`

    // Send the prompt to the AI
    await append({
      role: "user",
      content: prompt,
    })
  }

  // Get the latest assistant message
  const lastAssistantMessage = messages
    .filter((message: Message) => message.role === "assistant")
    .pop()

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="bg-[#012169] hover:bg-[#012169]/90 font-light tracking-wide flex items-center gap-2">
          <Sparkles className="h-4 w-4" />
          <span>AI Stylist</span>
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-light tracking-wide flex items-center justify-between">
            AI Stylist
            <span className="text-xs font-normal text-gray-500">Powered by SoImagine</span>
          </DialogTitle>
          <DialogDescription>Let our AI stylist create the perfect outfit for your child</DialogDescription>
        </DialogHeader>

        {!formSubmitted || (messages.length === 0 && !isLoading) ? (
          <form onSubmit={handleSubmit} className="space-y-4 mt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="age" className="text-sm font-light">
                  Age
                </Label>
                <Input
                  id="age"
                  name="age"
                  placeholder="e.g., 5"
                  required
                  value={preferences.age}
                  onChange={handleInputChange}
                  className="font-light"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="gender" className="text-sm font-light">
                  Gender
                </Label>
                <Select
                  name="gender"
                  value={preferences.gender}
                  onValueChange={(value) => handleSelectChange("gender", value)}
                >
                  <SelectTrigger id="gender" className="font-light">
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Boy">Boy</SelectItem>
                    <SelectItem value="Girl">Girl</SelectItem>
                    <SelectItem value="Any">Any</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="occasion" className="text-sm font-light">
                  Occasion
                </Label>
                <Select
                  name="occasion"
                  value={preferences.occasion}
                  onValueChange={(value) => handleSelectChange("occasion", value)}
                >
                  <SelectTrigger id="occasion" className="font-light">
                    <SelectValue placeholder="Select occasion" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Casual">Casual</SelectItem>
                    <SelectItem value="School">School</SelectItem>
                    <SelectItem value="Party">Party</SelectItem>
                    <SelectItem value="Heritage Day">Heritage Day</SelectItem>
                    <SelectItem value="Beach/Pool">Beach/Pool</SelectItem>
                    <SelectItem value="Braai">Braai</SelectItem>
                    <SelectItem value="Special Occasion">Special Occasion</SelectItem>
                    <SelectItem value="Outdoor/Safari">Outdoor/Safari</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="season" className="text-sm font-light">
                  Season
                </Label>
                <Select
                  name="season"
                  value={preferences.season}
                  onValueChange={(value) => handleSelectChange("season", value)}
                >
                  <SelectTrigger id="season" className="font-light">
                    <SelectValue placeholder="Select season" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Summer">Summer (Dec-Feb)</SelectItem>
                    <SelectItem value="Autumn">Autumn (Mar-May)</SelectItem>
                    <SelectItem value="Winter">Winter (Jun-Aug)</SelectItem>
                    <SelectItem value="Spring">Spring (Sep-Nov)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="colors" className="text-sm font-light">
                Color Preferences
              </Label>
              <Input
                id="colors"
                name="colors"
                placeholder="e.g., blue, red, neutral tones"
                value={preferences.colors}
                onChange={handleInputChange}
                className="font-light"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="style" className="text-sm font-light">
                Style Preferences
              </Label>
              <Input
                id="style"
                name="style"
                placeholder="e.g., sporty, classic, trendy"
                value={preferences.style}
                onChange={handleInputChange}
                className="font-light"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="climate" className="text-sm font-light">
                  Climate/Region
                </Label>
                <Select
                  name="climate"
                  value={preferences.climate}
                  onValueChange={(value) => handleSelectChange("climate", value)}
                >
                  <SelectTrigger id="climate" className="font-light">
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Gauteng">Gauteng (Highveld)</SelectItem>
                    <SelectItem value="Western Cape">Western Cape (Mediterranean)</SelectItem>
                    <SelectItem value="KwaZulu-Natal">KwaZulu-Natal (Coastal)</SelectItem>
                    <SelectItem value="Eastern Cape">Eastern Cape</SelectItem>
                    <SelectItem value="Limpopo">Limpopo (Subtropical)</SelectItem>
                    <SelectItem value="Mpumalanga">Mpumalanga</SelectItem>
                    <SelectItem value="Free State">Free State</SelectItem>
                    <SelectItem value="North West">North West</SelectItem>
                    <SelectItem value="Northern Cape">Northern Cape (Arid)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="budget" className="text-sm font-light">
                  Budget Range
                </Label>
                <Select
                  name="budget"
                  value={preferences.budget}
                  onValueChange={(value) => handleSelectChange("budget", value)}
                >
                  <SelectTrigger id="budget" className="font-light">
                    <SelectValue placeholder="Select budget" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Budget-friendly">Budget-friendly (R200-R500)</SelectItem>
                    <SelectItem value="Mid-range">Mid-range (R500-R1000)</SelectItem>
                    <SelectItem value="Premium">Premium (R1000+)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Button
              type="submit"
              disabled={isLoading}
              className="w-full bg-[#012169] hover:bg-[#012169]/90 font-light tracking-wide"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Outfit...
                </>
              ) : (
                "Create Outfit"
              )}
            </Button>
          </form>
        ) : (
          <div>
            {isLoading ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2">Generating your outfit recommendation...</span>
              </div>
            ) : error ? (
              <div className="p-4 bg-red-50 text-red-500 rounded-md">
                <p>Error: {error.message || "Failed to generate outfit recommendation"}</p>
                <Button
                  variant="outline"
                  className="mt-2"
                  onClick={() => setFormSubmitted(false)}
                >
                  Try Again
                </Button>
              </div>
            ) : lastAssistantMessage ? (
              <Card className="mt-4 border border-gray-200">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg font-light tracking-wide flex items-center justify-between">
                    Your Personalized Outfit
                    <span className="text-xs font-normal text-gray-500">Powered by SoImagine</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-sm max-w-none">
                    <div className="font-light whitespace-pre-line">
                      {lastAssistantMessage.content}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="outline"
                    className="font-light"
                    onClick={() => setFormSubmitted(false)}
                  >
                    Create Another Outfit
                  </Button>
                  <Button variant="outline" className="font-light">
                    Add All to Cart
                  </Button>
                </CardFooter>
              </Card>
            ) : null}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
