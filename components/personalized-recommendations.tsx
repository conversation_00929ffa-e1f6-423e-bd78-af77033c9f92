"use client"

import { useState, useEffect } from "react"
import { ProductCard } from "@/components/storefront/products/product-card"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { getProducts } from "@/lib/products"
import { Sparkles, TrendingUp, Heart, ShoppingBag } from "lucide-react"

interface Product {
  id: string
  name: string
  slug: string
  price: number
  compareAtPrice?: number
  images?: string[]
  colors?: { name: string; value: string }[]
  sizes?: string[]
  description: string
  isNew?: boolean
  isSale?: boolean
  categoryId?: string
}

export function PersonalizedRecommendations() {
  const [forYouProducts, setForYouProducts] = useState<Product[]>([])
  const [trendingProducts, setTrendingProducts] = useState<Product[]>([])
  const [similarProducts, setSimilarProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadRecommendations = async () => {
      try {
        const allProducts = await getProducts()
        
        // Simulate personalized recommendations based on browsing history
        const recentlyViewed = JSON.parse(localStorage.getItem("recentlyViewed") || "[]")
        const wishlist = JSON.parse(localStorage.getItem("wishlist") || "[]")
        
        // For You: Mix of new arrivals and products similar to viewed/wishlisted items
        const forYou = allProducts
          .filter(p => p.isNew || recentlyViewed.includes(p.id) || wishlist.includes(p.id))
          .slice(0, 8)
        
        // Trending: Products on sale or with high engagement
        const trending = allProducts
          .filter(p => p.isSale || p.compareAtPrice)
          .slice(0, 8)
        
        // Similar: Products from same categories as recently viewed
        const viewedProducts = allProducts.filter(p => recentlyViewed.includes(p.id))
        const viewedCategories = [...new Set(viewedProducts.map(p => p.categoryId))]
        const similar = allProducts
          .filter(p => viewedCategories.includes(p.categoryId) && !recentlyViewed.includes(p.id))
          .slice(0, 8)
        
        setForYouProducts(forYou)
        setTrendingProducts(trending)
        setSimilarProducts(similar)
      } catch (error) {
        console.error("Failed to load recommendations:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadRecommendations()
  }, [])

  if (isLoading) {
    return (
      <Card className="mb-8">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          </div>
        </CardContent>
      </Card>
    )
  }

  const hasRecommendations = forYouProducts.length > 0 || trendingProducts.length > 0 || similarProducts.length > 0

  if (!hasRecommendations) {
    return null
  }

  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Sparkles className="h-5 w-5" />
          <span>Recommended for You</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="for-you" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="for-you" className="flex items-center space-x-2">
              <Heart className="h-4 w-4" />
              <span>For You</span>
            </TabsTrigger>
            <TabsTrigger value="trending" className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4" />
              <span>Trending</span>
            </TabsTrigger>
            <TabsTrigger value="similar" className="flex items-center space-x-2">
              <ShoppingBag className="h-4 w-4" />
              <span>Similar Items</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="for-you" className="mt-6">
            {forYouProducts.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {forYouProducts.map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <p>Start browsing to see personalized recommendations!</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="trending" className="mt-6">
            {trendingProducts.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {trendingProducts.map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <p>No trending items available right now.</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="similar" className="mt-6">
            {similarProducts.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {similarProducts.map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <p>Browse more products to see similar recommendations!</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
