"use client"

import * as React from "react"
import { X, Filter, ChevronDown, ChevronUp } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetT<PERSON>le,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet"

interface FilterOption {
  id: string
  label: string
  count?: number
}

interface FilterGroup {
  id: string
  label: string
  type: "checkbox" | "range" | "color"
  options?: FilterOption[]
  min?: number
  max?: number
  value?: number[]
  colors?: { name: string; value: string; count?: number }[]
}

interface FilterSidebarProps {
  filters: FilterGroup[]
  activeFilters: Record<string, any>
  onFilterChange: (filterId: string, value: any) => void
  onClearFilters: () => void
  className?: string
  isMobile?: boolean
}

function FilterContent({ 
  filters, 
  activeFilters, 
  onFilterChange, 
  onClearFilters 
}: Omit<FilterSidebarProps, "className" | "isMobile">) {
  const [openGroups, setOpenGroups] = React.useState<Record<string, boolean>>({})

  const toggleGroup = (groupId: string) => {
    setOpenGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }))
  }

  const getActiveFilterCount = () => {
    return Object.values(activeFilters).filter(value => {
      if (Array.isArray(value)) return value.length > 0
      return value !== null && value !== undefined && value !== ""
    }).length
  }

  const renderFilterGroup = (group: FilterGroup) => {
    const isOpen = openGroups[group.id] ?? true

    return (
      <Collapsible
        key={group.id}
        open={isOpen}
        onOpenChange={() => toggleGroup(group.id)}
        className="space-y-2"
      >
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="flex w-full justify-between p-0 font-medium hover:bg-transparent"
          >
            <span>{group.label}</span>
            {isOpen ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>

        <CollapsibleContent className="space-y-3">
          {group.type === "checkbox" && group.options && (
            <div className="space-y-2">
              {group.options.map((option) => (
                <div key={option.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={option.id}
                    checked={activeFilters[group.id]?.includes(option.id) || false}
                    onCheckedChange={(checked) => {
                      const currentValues = activeFilters[group.id] || []
                      const newValues = checked
                        ? [...currentValues, option.id]
                        : currentValues.filter((id: string) => id !== option.id)
                      onFilterChange(group.id, newValues)
                    }}
                  />
                  <Label
                    htmlFor={option.id}
                    className="flex-1 text-sm font-normal cursor-pointer"
                  >
                    <span>{option.label}</span>
                    {option.count && (
                      <span className="ml-1 text-muted-foreground">({option.count})</span>
                    )}
                  </Label>
                </div>
              ))}
            </div>
          )}

          {group.type === "range" && group.min !== undefined && group.max !== undefined && (
            <div className="space-y-4">
              <div className="px-2">
                <Slider
                  value={activeFilters[group.id] || [group.min, group.max]}
                  onValueChange={(value) => onFilterChange(group.id, value)}
                  max={group.max}
                  min={group.min}
                  step={1}
                  className="w-full"
                />
              </div>
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>R{activeFilters[group.id]?.[0] || group.min}</span>
                <span>R{activeFilters[group.id]?.[1] || group.max}</span>
              </div>
            </div>
          )}

          {group.type === "color" && group.colors && (
            <div className="grid grid-cols-4 gap-2">
              {group.colors.map((color) => (
                <button
                  key={color.value}
                  className={cn(
                    "relative h-8 w-8 rounded-full border-2 border-gray-200",
                    activeFilters[group.id]?.includes(color.value) && "ring-2 ring-primary ring-offset-2"
                  )}
                  style={{ backgroundColor: color.value }}
                  onClick={() => {
                    const currentValues = activeFilters[group.id] || []
                    const newValues = currentValues.includes(color.value)
                      ? currentValues.filter((c: string) => c !== color.value)
                      : [...currentValues, color.value]
                    onFilterChange(group.id, newValues)
                  }}
                  title={color.name}
                />
              ))}
            </div>
          )}
        </CollapsibleContent>
      </Collapsible>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Filters</h3>
        {getActiveFilterCount() > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="text-muted-foreground hover:text-foreground"
          >
            Clear all
          </Button>
        )}
      </div>

      {/* Active Filters */}
      {getActiveFilterCount() > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Active Filters</h4>
          <div className="flex flex-wrap gap-2">
            {Object.entries(activeFilters).map(([filterId, value]) => {
              if (!value || (Array.isArray(value) && value.length === 0)) return null
              
              const filter = filters.find(f => f.id === filterId)
              if (!filter) return null

              if (Array.isArray(value)) {
                return value.map((v) => {
                  const option = filter.options?.find(o => o.id === v) || 
                               filter.colors?.find(c => c.value === v)
                  return (
                    <Badge
                      key={`${filterId}-${v}`}
                      variant="secondary"
                      className="gap-1"
                    >
                      {option?.label || option?.name || v}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => {
                          const newValues = value.filter((item: any) => item !== v)
                          onFilterChange(filterId, newValues)
                        }}
                      />
                    </Badge>
                  )
                })
              }

              if (filter.type === "range") {
                return (
                  <Badge key={filterId} variant="secondary" className="gap-1">
                    R{value[0]} - R{value[1]}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => onFilterChange(filterId, [filter.min!, filter.max!])}
                    />
                  </Badge>
                )
              }

              return null
            })}
          </div>
        </div>
      )}

      {/* Filter Groups */}
      <div className="space-y-6">
        {filters.map(renderFilterGroup)}
      </div>
    </div>
  )
}

export function FilterSidebar({
  filters,
  activeFilters,
  onFilterChange,
  onClearFilters,
  className,
  isMobile = false
}: FilterSidebarProps) {
  if (isMobile) {
    return (
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" size="sm" className="gap-2">
            <Filter className="h-4 w-4" />
            Filters
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-80">
          <SheetHeader>
            <SheetTitle>Filter Products</SheetTitle>
            <SheetDescription>
              Narrow down your search with these filters
            </SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            <FilterContent
              filters={filters}
              activeFilters={activeFilters}
              onFilterChange={onFilterChange}
              onClearFilters={onClearFilters}
            />
          </div>
        </SheetContent>
      </Sheet>
    )
  }

  return (
    <div className={cn("w-64 space-y-6 p-4", className)}>
      <FilterContent
        filters={filters}
        activeFilters={activeFilters}
        onFilterChange={onFilterChange}
        onClearFilters={onClearFilters}
      />
    </div>
  )
}
