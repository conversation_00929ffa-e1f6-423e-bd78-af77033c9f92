"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { MessageCircle, X, Send, Paperclip, Smile } from "lucide-react"
import { cn } from "@/lib/utils"

interface Message {
  id: string
  content: string
  sender: "user" | "agent"
  timestamp: Date
  status: "sent" | "delivered" | "read"
}

export function SupportChat() {
  const [isOpen, setIsOpen] = useState(false)
  const [message, setMessage] = useState("")
  const [messages, setMessages] = useState<Message[]>([])
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Initial welcome message
  useEffect(() => {
    if (messages.length === 0) {
      setMessages([
        {
          id: "welcome",
          content: "👋 Hi there! How can we help you today?",
          sender: "agent",
          timestamp: new Date(),
          status: "read",
        },
      ])
    }
  }, [messages.length])

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  const handleSendMessage = () => {
    if (!message.trim()) return

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: message,
      sender: "user",
      timestamp: new Date(),
      status: "sent",
    }

    setMessages((prev) => [...prev, userMessage])
    setMessage("")

    // Simulate agent typing
    setIsTyping(true)

    // Simulate agent response after a delay
    setTimeout(() => {
      setIsTyping(false)
      const responses = [
        "Thanks for reaching out! Let me check that for you.",
        "I'd be happy to help with your question about our products.",
        "Let me look into that for you. I'll get back to you shortly.",
        "Thanks for contacting Coco Milk Kids support. How can I assist you further?",
        "I understand your concern. Let me find the best solution for you.",
      ]
      const randomResponse = responses[Math.floor(Math.random() * responses.length)]

      const agentMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: randomResponse,
        sender: "agent",
        timestamp: new Date(),
        status: "read",
      }

      setMessages((prev) => [...prev, agentMessage])
    }, 2000)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <>
      {/* Chat Button */}
      <Button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "fixed bottom-4 right-4 z-50 rounded-full shadow-lg p-4 h-14 w-14",
          isOpen ? "bg-red-500 hover:bg-red-600" : "bg-[#012169] hover:bg-[#012169]/90",
        )}
      >
        {isOpen ? <X className="h-6 w-6" /> : <MessageCircle className="h-6 w-6" />}
      </Button>

      {/* Chat Window */}
      {isOpen && (
        <div className="fixed bottom-20 right-4 z-50 w-80 md:w-96 h-96 bg-white rounded-lg shadow-xl flex flex-col border">
          {/* Header */}
          <div className="bg-[#012169] text-white p-4 rounded-t-lg flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/placeholder.svg?height=32&width=32" alt="Support Agent" />
                <AvatarFallback>CM</AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium">Coco Milk Support</div>
                <div className="text-xs opacity-80">Online</div>
              </div>
            </div>
            <Button variant="ghost" size="icon" className="text-white" onClick={() => setIsOpen(false)}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((msg) => (
              <div key={msg.id} className={cn("flex", msg.sender === "user" ? "justify-end" : "justify-start")}>
                {msg.sender === "agent" && (
                  <Avatar className="h-8 w-8 mr-2 flex-shrink-0">
                    <AvatarImage src="/placeholder.svg?height=32&width=32" alt="Support Agent" />
                    <AvatarFallback>CM</AvatarFallback>
                  </Avatar>
                )}
                <div
                  className={cn(
                    "max-w-[80%] rounded-lg p-3",
                    msg.sender === "user"
                      ? "bg-[#012169] text-white rounded-tr-none"
                      : "bg-gray-100 text-gray-800 rounded-tl-none",
                  )}
                >
                  <p>{msg.content}</p>
                  <div
                    className={cn("text-xs mt-1", msg.sender === "user" ? "text-white/70 text-right" : "text-gray-500")}
                  >
                    {msg.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                  </div>
                </div>
              </div>
            ))}

            {isTyping && (
              <div className="flex justify-start">
                <Avatar className="h-8 w-8 mr-2 flex-shrink-0">
                  <AvatarImage src="/placeholder.svg?height=32&width=32" alt="Support Agent" />
                  <AvatarFallback>CM</AvatarFallback>
                </Avatar>
                <div className="bg-gray-100 text-gray-800 rounded-lg rounded-tl-none p-3 max-w-[80%]">
                  <div className="flex space-x-1">
                    <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce" />
                    <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-100" />
                    <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-200" />
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-3 border-t">
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="icon" className="text-muted-foreground">
                <Paperclip className="h-5 w-5" />
              </Button>
              <Input
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Type your message..."
                className="flex-1"
              />
              <Button variant="ghost" size="icon" className="text-muted-foreground">
                <Smile className="h-5 w-5" />
              </Button>
              <Button
                onClick={handleSendMessage}
                disabled={!message.trim()}
                size="icon"
                className="bg-[#012169] text-white hover:bg-[#012169]/90"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
