import { CreditCard, Smartphone, Building2, Banknote } from "lucide-react"

export function PaymentMethods() {
  const paymentMethods = [
    {
      name: "Credit & Debit Cards",
      description: "Visa, Mastercard, American Express",
      icon: CreditCard,
      popular: true,
    },
    {
      name: "EFT / Bank Transfer",
      description: "Direct bank transfer",
      icon: Building2,
      popular: false,
    },
    {
      name: "SnapScan",
      description: "Scan to pay with your banking app",
      icon: Smartphone,
      popular: true,
    },
    {
      name: "Cash on Collection",
      description: "Pay cash when collecting in-store",
      icon: Banknote,
      popular: false,
    },
  ]

  return (
    <div className="space-y-4">
      <h3 className="font-medium text-sm">Accepted Payment Methods</h3>
      <div className="grid grid-cols-2 gap-3">
        {paymentMethods.map((method) => (
          <div
            key={method.name}
            className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg"
          >
            <method.icon className="h-5 w-5 text-gray-600" />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {method.name}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {method.description}
              </p>
            </div>
            {method.popular && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Popular
              </span>
            )}
          </div>
        ))}
      </div>
      <p className="text-xs text-gray-500">
        All payments are processed securely. We never store your payment information.
      </p>
    </div>
  )
}
