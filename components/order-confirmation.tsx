"use client"

import { useEffect } from "react"
import { CheckCircle, Package, ArrowRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import Link from "next/link"
import { useCart } from "@/hooks/use-cart"
import { usePriceFormatter } from "@/hooks/use-price-formatter"

interface OrderConfirmationProps {
  orderId: string
  email?: string
  shippingMethod: string
}

export function OrderConfirmation({ orderId, email = "<EMAIL>", shippingMethod }: OrderConfirmationProps) {
  const { items, totalPrice, clearCart } = useCart()
  const { formatPrice } = usePriceFormatter()

  // Calculate estimated delivery date
  const deliveryDate = new Date(
    Date.now() + (shippingMethod === "express" ? 2 : 5) * 24 * 60 * 60 * 1000
  )

  // Format date as "Month Day, Year" using South African locale
  const formattedDate = deliveryDate.toLocaleDateString("en-ZA", {
    month: "long",
    day: "numeric",
    year: "numeric",
  })

  // South African pricing
  const shippingPrice = shippingMethod === "express" ? 199 : 99
  const taxRate = 0.15 // South African VAT
  const taxPrice = totalPrice * taxRate
  const totalWithTax = totalPrice + shippingPrice + taxPrice
  
  // Clear cart when component unmounts
  useEffect(() => {
    return () => {
      clearCart()
    }
  }, [clearCart])
  
  return (
    <div className="max-w-3xl mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
          <CheckCircle className="h-8 w-8 text-green-600" />
        </div>
        <h1 className="text-2xl md:text-3xl font-bold mb-2">Order Confirmed!</h1>
        <p className="text-muted-foreground">
          Thank you for your purchase. We've sent a confirmation email to {email}.
        </p>
      </div>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg font-medium">Order Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Order Number:</span>
            <span className="font-medium">{orderId}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Order Date:</span>
            <span className="font-medium">{new Date().toLocaleDateString()}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Payment Method:</span>
            <span className="font-medium">Credit Card</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Shipping Method:</span>
            <span className="font-medium">{shippingMethod === "express" ? "Express" : "Standard"}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Estimated Delivery:</span>
            <span className="font-medium">{formattedDate}</span>
          </div>
        </CardContent>
      </Card>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg font-medium">Order Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {items.map((item) => (
              <div key={`${item.id}-${item.size}`} className="flex justify-between text-sm">
                <div className="flex items-center">
                  <Package className="h-3 w-3 mr-2 text-muted-foreground" />
                  <span>
                    {item.name} ({item.size}) x {item.quantity}
                  </span>
                </div>
                <span>{formatPrice(item.price * item.quantity)}</span>
              </div>
            ))}
          </div>
          
          <Separator />
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Subtotal</span>
              <span>{formatPrice(totalPrice)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Shipping</span>
              <span>{formatPrice(shippingPrice)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>VAT (15%)</span>
              <span>{formatPrice(taxPrice)}</span>
            </div>
          </div>

          <Separator />

          <div className="flex justify-between font-medium">
            <span>Total</span>
            <span>{formatPrice(totalWithTax)}</span>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" asChild>
            <Link href="/account/orders">
              View All Orders
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/account/orders/track">
              Track Order <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </CardFooter>
      </Card>
      
      <div className="flex justify-center">
        <Button asChild className="bg-[#012169] hover:bg-[#012169]/90">
          <Link href="/">
            Continue Shopping
          </Link>
        </Button>
      </div>
    </div>
  )
}
