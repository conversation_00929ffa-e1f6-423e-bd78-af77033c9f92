"use client"

import { useState, useEffect } from "react"
import { ProductCard } from "@/components/storefront/products/product-card"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { getProductsByIds } from "@/lib/products"
import { Eye, X } from "lucide-react"

interface Product {
  id: string
  name: string
  slug: string
  price: number
  compareAtPrice?: number
  images?: string[]
  colors?: { name: string; value: string }[]
  sizes?: string[]
  description: string
  isNew?: boolean
  isSale?: boolean
}

export function RecentlyViewedEnhanced() {
  const [recentlyViewed, setRecentlyViewed] = useState<string[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Load recently viewed from localStorage
    const stored = localStorage.getItem("recentlyViewed")
    if (stored) {
      try {
        const parsed = JSON.parse(stored)
        setRecentlyViewed(parsed.slice(0, 8)) // Limit to 8 items
      } catch (error) {
        console.error("Failed to parse recently viewed:", error)
      }
    }
    setIsLoading(false)
  }, [])

  useEffect(() => {
    const loadProducts = async () => {
      if (recentlyViewed.length > 0) {
        try {
          const productData = await getProductsByIds(recentlyViewed)
          setProducts(productData)
        } catch (error) {
          console.error("Failed to load recently viewed products:", error)
        }
      }
    }
    loadProducts()
  }, [recentlyViewed])

  const addToRecentlyViewed = (productId: string) => {
    const updated = [productId, ...recentlyViewed.filter(id => id !== productId)].slice(0, 8)
    setRecentlyViewed(updated)
    localStorage.setItem("recentlyViewed", JSON.stringify(updated))
  }

  const removeFromRecentlyViewed = (productId: string) => {
    const updated = recentlyViewed.filter(id => id !== productId)
    setRecentlyViewed(updated)
    localStorage.setItem("recentlyViewed", JSON.stringify(updated))
  }

  const clearRecentlyViewed = () => {
    setRecentlyViewed([])
    localStorage.removeItem("recentlyViewed")
  }

  // Export function to be used globally
  if (typeof window !== "undefined") {
    ;(window as any).addToRecentlyViewed = addToRecentlyViewed
  }

  if (isLoading || products.length === 0) {
    return null
  }

  return (
    <Card className="mb-8">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Eye className="h-5 w-5" />
            <CardTitle>Recently Viewed</CardTitle>
          </div>
          <Button variant="ghost" size="sm" onClick={clearRecentlyViewed}>
            Clear All
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {products.map((product) => (
            <div key={product.id} className="relative group">
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1 z-10 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity bg-white/80 hover:bg-white"
                onClick={() => removeFromRecentlyViewed(product.id)}
              >
                <X className="h-3 w-3" />
              </Button>
              <ProductCard product={product} className="h-full" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
