"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Truck, Package, CheckCircle, Clock, MapPin } from "lucide-react"

interface TrackingEvent {
  date: string
  time: string
  status: string
  location: string
  description: string
}

interface DeliveryInfo {
  trackingNumber: string
  courier: string
  status: "processing" | "shipped" | "in_transit" | "out_for_delivery" | "delivered"
  estimatedDelivery: string
  events: TrackingEvent[]
}

export function DeliveryTrackerSA() {
  const [trackingNumber, setTrackingNumber] = useState("")
  const [deliveryInfo, setDeliveryInfo] = useState<DeliveryInfo | null>(null)
  const [loading, setLoading] = useState(false)

  // Mock tracking data for South African couriers
  const mockTrackingData: DeliveryInfo = {
    trackingNumber: "CMK123456789",
    courier: "The Courier Guy",
    status: "in_transit",
    estimatedDelivery: "Tomorrow by 5:00 PM",
    events: [
      {
        date: "2024-01-22",
        time: "14:30",
        status: "In Transit",
        location: "Johannesburg Hub",
        description: "Package is on its way to destination city"
      },
      {
        date: "2024-01-22",
        time: "09:15",
        status: "Departed",
        location: "Sandton Distribution Center",
        description: "Package has left our facility"
      },
      {
        date: "2024-01-21",
        time: "16:45",
        status: "Processed",
        location: "Sandton Distribution Center",
        description: "Package received and processed"
      },
      {
        date: "2024-01-21",
        time: "14:20",
        status: "Shipped",
        location: "Coco Milk Kids Store",
        description: "Package picked up from sender"
      }
    ]
  }

  const handleTrack = async () => {
    setLoading(true)
    // Simulate API call
    setTimeout(() => {
      if (trackingNumber.toLowerCase().includes("cmk")) {
        setDeliveryInfo(mockTrackingData)
      } else {
        setDeliveryInfo(null)
      }
      setLoading(false)
    }, 1000)
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "delivered":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "in_transit":
      case "out_for_delivery":
        return <Truck className="h-4 w-4 text-blue-600" />
      case "shipped":
      case "processed":
        return <Package className="h-4 w-4 text-orange-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "delivered":
        return "bg-green-100 text-green-800"
      case "out_for_delivery":
        return "bg-blue-100 text-blue-800"
      case "in_transit":
        return "bg-orange-100 text-orange-800"
      case "shipped":
        return "bg-purple-100 text-purple-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Track Your Delivery
          </CardTitle>
          <CardDescription>
            Enter your tracking number to see the latest delivery updates
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="tracking" className="sr-only">Tracking Number</Label>
              <Input
                id="tracking"
                placeholder="Enter tracking number (e.g., CMK123456789)"
                value={trackingNumber}
                onChange={(e) => setTrackingNumber(e.target.value)}
              />
            </div>
            <Button onClick={handleTrack} disabled={loading || !trackingNumber}>
              {loading ? "Tracking..." : "Track"}
            </Button>
          </div>

          <div className="text-xs text-gray-500">
            <p>Supported couriers: The Courier Guy, Fastway, PostNet, Aramex, DHL</p>
          </div>
        </CardContent>
      </Card>

      {deliveryInfo && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Tracking: {deliveryInfo.trackingNumber}</CardTitle>
                <CardDescription>via {deliveryInfo.courier}</CardDescription>
              </div>
              <Badge className={getStatusColor(deliveryInfo.status)}>
                {deliveryInfo.status.replace("_", " ").toUpperCase()}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">
                Estimated delivery: {deliveryInfo.estimatedDelivery}
              </span>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium">Tracking History</h4>
              <div className="space-y-3">
                {deliveryInfo.events.map((event, index) => (
                  <div key={index} className="flex gap-3 pb-3 border-b last:border-b-0">
                    <div className="flex-shrink-0 mt-1">
                      {getStatusIcon(event.status)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sm">{event.status}</span>
                        <span className="text-xs text-gray-500">
                          {event.date} at {event.time}
                        </span>
                      </div>
                      <div className="flex items-center gap-1 text-xs text-gray-600 mb-1">
                        <MapPin className="h-3 w-3" />
                        <span>{event.location}</span>
                      </div>
                      <p className="text-sm text-gray-700">{event.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {trackingNumber && !deliveryInfo && !loading && (
        <Card>
          <CardContent className="pt-6 text-center">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="font-medium mb-2">No tracking information found</h3>
            <p className="text-sm text-gray-600">
              Please check your tracking number and try again, or contact our customer service team.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
