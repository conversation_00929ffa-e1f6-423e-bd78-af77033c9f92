"use client"

import { useEffect, useState } from "react"
import { ProductCard } from "@/components/storefront/products/product-card"
import { getProductsByIds } from "@/lib/products"

export function RecentlyViewed() {
  const [recentProducts, setRecentProducts] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadRecentlyViewed = async () => {
      try {
        const recentIds = JSON.parse(localStorage.getItem("recentlyViewed") || "[]")
        if (recentIds.length > 0) {
          const products = await getProductsByIds(recentIds.slice(0, 4))
          setRecentProducts(products)
        }
      } catch (error) {
        console.error("Failed to load recently viewed products:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadRecentlyViewed()
  }, [])

  const addToRecentlyViewed = (productId: string) => {
    try {
      const recent = JSON.parse(localStorage.getItem("recentlyViewed") || "[]")
      const filtered = recent.filter((id: string) => id !== productId)
      const updated = [productId, ...filtered].slice(0, 10) // Keep last 10
      localStorage.setItem("recentlyViewed", JSON.stringify(updated))
    } catch (error) {
      console.error("Failed to update recently viewed:", error)
    }
  }

  // Export the function to be used in product pages
  useEffect(() => {
    if (typeof window !== "undefined") {
      ;(window as any).addToRecentlyViewed = addToRecentlyViewed
    }
  }, [addToRecentlyViewed])

  if (isLoading || recentProducts.length === 0) {
    return null
  }

  return (
    <section className="py-8">
      <h2 className="text-xl font-bold font-montserrat mb-4">Recently Viewed</h2>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {recentProducts.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </section>
  )
}
