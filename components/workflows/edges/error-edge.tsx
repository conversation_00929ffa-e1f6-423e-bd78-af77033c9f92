'use client'

import React from 'react'
import { EdgeProps, getBezierPath, EdgeLabelRenderer, BaseEdge } from 'reactflow'
import { AlertTriangle } from 'lucide-react'

interface ErrorEdgeData {
  errorType?: string
  label?: string
  severity?: 'low' | 'medium' | 'high'
}

export default function ErrorEdge({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  markerEnd,
}: EdgeProps<ErrorEdgeData>) {
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  })

  const severity = data?.severity || 'medium'
  const label = data?.label || 'Error'

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return {
          stroke: '#f59e0b',
          bg: 'bg-yellow-100',
          text: 'text-yellow-800',
          border: 'border-yellow-200'
        }
      case 'high':
        return {
          stroke: '#dc2626',
          bg: 'bg-red-100',
          text: 'text-red-800',
          border: 'border-red-200'
        }
      default:
        return {
          stroke: '#ea580c',
          bg: 'bg-orange-100',
          text: 'text-orange-800',
          border: 'border-orange-200'
        }
    }
  }

  const colors = getSeverityColor(severity)

  return (
    <>
      <BaseEdge
        path={edgePath}
        markerEnd={markerEnd}
        style={{
          ...style,
          stroke: colors.stroke,
          strokeWidth: 2,
          strokeDasharray: '8,4',
          animation: 'pulse 2s infinite',
        }}
      />
      <EdgeLabelRenderer>
        <div
          style={{
            position: 'absolute',
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
            fontSize: 12,
            pointerEvents: 'all',
          }}
          className="nodrag nopan"
        >
          <div
            className={`px-2 py-1 rounded text-xs font-medium flex items-center gap-1 ${colors.bg} ${colors.text} ${colors.border} border`}
          >
            <AlertTriangle className="h-3 w-3" />
            {label}
          </div>
        </div>
      </EdgeLabelRenderer>
    </>
  )
}

// Export as named export for consistency
export { ErrorEdge }
