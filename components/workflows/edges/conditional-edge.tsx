'use client'

import React from 'react'
import { EdgeProps, getBezierPath, EdgeLabelRenderer, BaseEdge } from 'reactflow'

interface ConditionalEdgeData {
  condition?: string
  label?: string
  isTrue?: boolean
}

export default function ConditionalEdge({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  markerEnd,
}: EdgeProps<ConditionalEdgeData>) {
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  })

  const isTrue = data?.isTrue ?? true
  const label = data?.label || (isTrue ? 'True' : 'False')

  return (
    <>
      <BaseEdge
        path={edgePath}
        markerEnd={markerEnd}
        style={{
          ...style,
          stroke: isTrue ? '#10b981' : '#ef4444',
          strokeWidth: 2,
          strokeDasharray: isTrue ? 'none' : '5,5',
        }}
      />
      <EdgeLabelRenderer>
        <div
          style={{
            position: 'absolute',
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
            fontSize: 12,
            pointerEvents: 'all',
          }}
          className="nodrag nopan"
        >
          <div
            className={`px-2 py-1 rounded text-xs font-medium ${
              isTrue
                ? 'bg-green-100 text-green-800 border border-green-200'
                : 'bg-red-100 text-red-800 border border-red-200'
            }`}
          >
            {label}
          </div>
        </div>
      </EdgeLabelRenderer>
    </>
  )
}

// Export as named export for consistency
export { ConditionalEdge }
