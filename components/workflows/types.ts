import { Node, Edge } from 'reactflow'

// Base workflow node interface
export interface WorkflowNode extends Node {
  type: 'trigger' | 'action' | 'condition' | 'delay' | 'integration' | 'parallel' | 'merge' | 'loop'
  data: {
    label: string
    description?: string
    config: Record<string, any>
    isValid: boolean
    errors: string[]
    isExecuting?: boolean
    isCompleted?: boolean
    isFailed?: boolean
    executionTime?: number
    retryCount?: number
    maxRetries?: number
  }
}

// Base workflow edge interface
export interface WorkflowEdge extends Edge {
  type?: 'default' | 'conditional' | 'error' | 'parallel'
  data?: {
    condition?: string
    label?: string
    isActive?: boolean
  }
}

// Flow state management
export interface FlowState {
  isExecuting: boolean
  executionPath: string[]
  errors: string[]
  selectedNodeId: string | null
  clipboard: WorkflowNode | null
  history: FlowHistoryEntry[]
  historyIndex: number
}

export interface FlowHistoryEntry {
  nodes: WorkflowNode[]
  edges: WorkflowEdge[]
  timestamp: number
}

// Execution state
export interface ExecutionState {
  isRunning: boolean
  currentStep: string | null
  completedSteps: string[]
  failedSteps: string[]
  executionLog: ExecutionLogEntry[]
}

export interface ExecutionLogEntry {
  stepId: string
  timestamp: number
  status: 'started' | 'completed' | 'failed' | 'skipped'
  message?: string
  data?: any
  duration?: number
}

// Node type definitions
export interface TriggerNodeData {
  triggerType: 'event' | 'schedule' | 'manual' | 'webhook'
  event?: string
  schedule?: string
  conditions?: ConditionConfig[]
  webhookUrl?: string
}

export interface ActionNodeData {
  actionType: 'email' | 'sms' | 'database' | 'api' | 'webhook' | 'file' | 'custom'
  template?: string
  recipient?: string
  endpoint?: string
  method?: string
  headers?: Record<string, string>
  body?: any
  query?: string
  operation?: string
}

export interface ConditionNodeData {
  conditions: ConditionConfig[]
  operator: 'and' | 'or'
  truePath?: string
  falsePath?: string
}

export interface ConditionConfig {
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'not_contains' | 'in' | 'not_in' | 'exists' | 'not_exists'
  value: any
  type?: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object'
}

export interface DelayNodeData {
  delayType: 'fixed' | 'dynamic' | 'until'
  duration?: number
  unit?: 'seconds' | 'minutes' | 'hours' | 'days'
  dynamicField?: string
  untilCondition?: ConditionConfig
  maxWait?: number
}

export interface IntegrationNodeData {
  service: string
  operation: string
  authentication?: {
    type: 'api_key' | 'oauth' | 'basic' | 'bearer'
    credentials: Record<string, string>
  }
  config: Record<string, any>
}

export interface ParallelNodeData {
  branches: ParallelBranch[]
  waitForAll: boolean
  timeout?: number
}

export interface ParallelBranch {
  id: string
  name: string
  steps: string[]
}

export interface LoopNodeData {
  loopType: 'for_each' | 'while' | 'count'
  collection?: string
  condition?: ConditionConfig
  count?: number
  maxIterations?: number
  breakCondition?: ConditionConfig
}

export interface MergeNodeData {
  mergeType: 'all' | 'any' | 'first'
  timeout?: number
  requiredInputs?: string[]
}

// Template definitions
export interface WorkflowTemplate {
  id: string
  name: string
  description: string
  category: string
  tags: string[]
  thumbnail?: string
  nodes: WorkflowNode[]
  edges: WorkflowEdge[]
  metadata: {
    author?: string
    version: string
    createdAt: string
    updatedAt: string
    usageCount?: number
    rating?: number
  }
}

// Testing and debugging
export interface TestConfiguration {
  testData: Record<string, any>
  mockResponses: Record<string, any>
  skipSteps: string[]
  breakpoints: string[]
  timeout: number
}

export interface DebugSession {
  id: string
  workflowId: string
  startedAt: number
  status: 'running' | 'paused' | 'completed' | 'failed'
  currentStep: string | null
  variables: Record<string, any>
  callStack: string[]
  breakpoints: string[]
}

// Validation
export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
}

export interface ValidationError {
  nodeId?: string
  edgeId?: string
  type: 'missing_connection' | 'invalid_config' | 'circular_dependency' | 'unreachable_node' | 'invalid_condition'
  message: string
  severity: 'error' | 'warning'
}

export interface ValidationWarning {
  nodeId?: string
  type: 'performance' | 'best_practice' | 'deprecated'
  message: string
}

// Export and import
export interface WorkflowExport {
  version: string
  workflow: {
    metadata: Record<string, any>
    nodes: WorkflowNode[]
    edges: WorkflowEdge[]
    settings: Record<string, any>
  }
  exportedAt: string
  exportedBy: string
}

// Collaboration
export interface CollaborationState {
  isCollaborating: boolean
  activeUsers: CollaborationUser[]
  cursors: Record<string, CursorPosition>
  selections: Record<string, string[]>
  locks: Record<string, string> // nodeId -> userId
}

export interface CollaborationUser {
  id: string
  name: string
  avatar?: string
  color: string
  isOnline: boolean
  lastSeen: number
}

export interface CursorPosition {
  x: number
  y: number
  userId: string
  timestamp: number
}

// Performance monitoring
export interface PerformanceMetrics {
  nodeCount: number
  edgeCount: number
  renderTime: number
  memoryUsage: number
  fps: number
  lastUpdate: number
}

// Accessibility
export interface AccessibilityOptions {
  highContrast: boolean
  reducedMotion: boolean
  screenReader: boolean
  keyboardNavigation: boolean
  fontSize: 'small' | 'medium' | 'large'
}

// Theme and styling
export interface FlowTheme {
  name: string
  colors: {
    background: string
    grid: string
    node: {
      default: string
      selected: string
      error: string
      success: string
      executing: string
    }
    edge: {
      default: string
      selected: string
      conditional: string
      error: string
    }
    text: {
      primary: string
      secondary: string
      muted: string
    }
  }
  spacing: {
    nodeGap: number
    gridSize: number
    padding: number
  }
  animation: {
    duration: number
    easing: string
  }
}

// Event system
export type FlowEvent = 
  | { type: 'NODE_ADDED'; payload: { node: WorkflowNode } }
  | { type: 'NODE_REMOVED'; payload: { nodeId: string } }
  | { type: 'NODE_UPDATED'; payload: { nodeId: string; updates: Partial<WorkflowNode> } }
  | { type: 'EDGE_ADDED'; payload: { edge: WorkflowEdge } }
  | { type: 'EDGE_REMOVED'; payload: { edgeId: string } }
  | { type: 'EXECUTION_STARTED'; payload: { workflowId: string } }
  | { type: 'EXECUTION_COMPLETED'; payload: { workflowId: string; result: any } }
  | { type: 'EXECUTION_FAILED'; payload: { workflowId: string; error: string } }
  | { type: 'VALIDATION_CHANGED'; payload: { result: ValidationResult } }
  | { type: 'SELECTION_CHANGED'; payload: { selectedIds: string[] } }
  | { type: 'VIEWPORT_CHANGED'; payload: { viewport: { x: number; y: number; zoom: number } } }

export type FlowEventHandler = (event: FlowEvent) => void
