import React, { memo } from 'react'
import { <PERSON>le, Position, NodeProps } from 'reactflow'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Clock,
  Timer,
  Calendar,
  AlertCircle,
  CheckCircle,
  Play,
  Pause
} from 'lucide-react'
import { WorkflowNode, DelayNodeData } from '../types'
import { cn } from '@/lib/utils'

interface DelayNodeProps extends NodeProps {
  data: WorkflowNode['data'] & DelayNodeData
}

export const DelayNode = memo(({ data, selected, id }: DelayNodeProps) => {
  const getStatusIcon = () => {
    if (data.isFailed) return <AlertCircle className="h-3 w-3 text-red-500" />
    if (data.isCompleted) return <CheckCircle className="h-3 w-3 text-green-500" />
    if (data.isExecuting) return <Pause className="h-3 w-3 text-orange-500 animate-pulse" />
    return null
  }

  const getDelayDescription = () => {
    if (!data.duration && !data.untilCondition) {
      return 'No delay configured'
    }

    switch (data.delayType) {
      case 'fixed':
        return formatDuration(data.duration, data.unit)
      case 'dynamic':
        return `Dynamic: ${data.dynamicField || 'field not set'}`
      case 'until':
        return `Until condition met`
      default:
        return formatDuration(data.duration, data.unit)
    }
  }

  const formatDuration = (duration?: number, unit?: string) => {
    if (!duration) return 'No duration set'
    
    const units = {
      seconds: 's',
      minutes: 'm',
      hours: 'h',
      days: 'd'
    }
    
    return `${duration}${units[unit as keyof typeof units] || 'ms'}`
  }

  const getDelayIcon = () => {
    switch (data.delayType) {
      case 'until':
        return Calendar
      case 'dynamic':
        return Timer
      default:
        return Clock
    }
  }

  const DelayIcon = getDelayIcon()

  const getRemainingTime = () => {
    // This would be calculated based on execution state
    // For now, just show if it's executing
    if (data.isExecuting && data.duration) {
      return `Waiting... (${formatDuration(data.duration, data.unit)})`
    }
    return null
  }

  return (
    <>
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-gray-400 border-2 border-white"
        style={{ top: -6 }}
      />

      <Card 
        className={cn(
          'min-w-[180px] transition-all duration-200 cursor-pointer',
          selected && 'ring-2 ring-blue-500 ring-offset-2',
          !data.isValid && 'border-red-300 bg-red-50',
          data.isExecuting && 'border-orange-300 bg-orange-50',
          data.isCompleted && 'border-green-300 bg-green-50',
          data.isFailed && 'border-red-300 bg-red-50'
        )}
      >
        <CardContent className="p-3">
          <div className="flex items-start space-x-3">
            {/* Icon */}
            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-orange-500 text-white">
              <DelayIcon className="h-4 w-4" />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-sm truncate">
                  {data.label}
                </h3>
                {getStatusIcon()}
              </div>
              
              <p className="text-xs text-muted-foreground mt-1">
                {getDelayDescription()}
              </p>

              {/* Delay Configuration */}
              {data.delayType === 'until' && data.untilCondition && (
                <div className="mt-2 p-2 bg-orange-50 border border-orange-200 rounded text-xs">
                  <div className="font-medium text-orange-700">Wait Until:</div>
                  <div className="text-orange-600">
                    {data.untilCondition.field} {data.untilCondition.operator} {data.untilCondition.value}
                  </div>
                  {data.maxWait && (
                    <div className="text-orange-500 mt-1">
                      Max wait: {formatDuration(data.maxWait, 'seconds')}
                    </div>
                  )}
                </div>
              )}

              {data.delayType === 'dynamic' && data.dynamicField && (
                <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
                  <div className="font-medium text-blue-700">Dynamic Field:</div>
                  <div className="text-blue-600">{data.dynamicField}</div>
                </div>
              )}

              {/* Badges */}
              <div className="flex items-center space-x-1 mt-2">
                <Badge variant="outline" className="text-xs">
                  delay
                </Badge>
                
                {data.delayType && data.delayType !== 'fixed' && (
                  <Badge variant="secondary" className="text-xs">
                    {data.delayType}
                  </Badge>
                )}

                {data.isExecuting && (
                  <Badge variant="default" className="text-xs bg-orange-500">
                    <Pause className="h-3 w-3 mr-1" />
                    Waiting
                  </Badge>
                )}
              </div>

              {/* Remaining Time */}
              {getRemainingTime() && (
                <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-700">
                  <div className="flex items-center space-x-1">
                    <Timer className="h-3 w-3" />
                    <span>{getRemainingTime()}</span>
                  </div>
                </div>
              )}

              {/* Errors */}
              {!data.isValid && data.errors.length > 0 && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                  <div className="flex items-center space-x-1">
                    <AlertCircle className="h-3 w-3" />
                    <span className="font-medium">Errors:</span>
                  </div>
                  <ul className="mt-1 space-y-1">
                    {data.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Execution Info */}
              {data.executionTime && (
                <div className="mt-2 text-xs text-muted-foreground">
                  Delayed for {data.executionTime}ms
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-gray-400 border-2 border-white"
        style={{ bottom: -6 }}
      />
    </>
  )
})

DelayNode.displayName = 'DelayNode'
