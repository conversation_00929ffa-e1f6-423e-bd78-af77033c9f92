import React, { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  GitBranch,
  Users,
  Clock,
  AlertCircle,
  CheckCircle,
  Play,
  Timer
} from 'lucide-react'
import { WorkflowNode, ParallelNodeData } from '../types'
import { cn } from '@/lib/utils'

interface ParallelNodeProps extends NodeProps {
  data: WorkflowNode['data'] & ParallelNodeData
}

export const ParallelNode = memo(({ data, selected, id }: ParallelNodeProps) => {
  const getStatusIcon = () => {
    if (data.isFailed) return <AlertCircle className="h-3 w-3 text-red-500" />
    if (data.isCompleted) return <CheckCircle className="h-3 w-3 text-green-500" />
    if (data.isExecuting) return <Play className="h-3 w-3 text-blue-500 animate-pulse" />
    return null
  }

  const getParallelDescription = () => {
    const branchCount = data.branches?.length || 0
    if (branchCount === 0) return 'No branches configured'
    
    const waitType = data.waitForAll ? 'Wait for all' : 'Wait for any'
    return `${branchCount} branches - ${waitType}`
  }

  const getBranchStatus = (branchId: string) => {
    // This would be determined from execution state
    // For now, just return a placeholder
    return 'pending'
  }

  const getBranchStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-500'
      case 'failed': return 'text-red-500'
      case 'running': return 'text-blue-500'
      default: return 'text-gray-500'
    }
  }

  return (
    <>
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-gray-400 border-2 border-white"
        style={{ top: -6 }}
      />

      <Card 
        className={cn(
          'min-w-[240px] transition-all duration-200 cursor-pointer',
          selected && 'ring-2 ring-blue-500 ring-offset-2',
          !data.isValid && 'border-red-300 bg-red-50',
          data.isExecuting && 'border-blue-300 bg-blue-50',
          data.isCompleted && 'border-green-300 bg-green-50',
          data.isFailed && 'border-red-300 bg-red-50'
        )}
      >
        <CardContent className="p-3">
          <div className="flex items-start space-x-3">
            {/* Icon */}
            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-indigo-500 text-white">
              <GitBranch className="h-4 w-4" />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-sm truncate">
                  {data.label}
                </h3>
                {getStatusIcon()}
              </div>
              
              <p className="text-xs text-muted-foreground mt-1">
                {getParallelDescription()}
              </p>

              {/* Branches List */}
              {data.branches && data.branches.length > 0 && (
                <div className="mt-2 space-y-1">
                  {data.branches.slice(0, 3).map((branch, index) => {
                    const status = getBranchStatus(branch.id)
                    return (
                      <div key={branch.id} className="flex items-center justify-between p-2 bg-gray-50 border border-gray-200 rounded text-xs">
                        <div className="flex items-center space-x-2">
                          <div className={cn('w-2 h-2 rounded-full', {
                            'bg-green-500': status === 'completed',
                            'bg-red-500': status === 'failed',
                            'bg-blue-500': status === 'running',
                            'bg-gray-400': status === 'pending'
                          })} />
                          <span className="font-medium">{branch.name}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Users className="h-3 w-3 text-gray-500" />
                          <span className="text-gray-500">{branch.steps?.length || 0}</span>
                        </div>
                      </div>
                    )
                  })}
                  
                  {data.branches.length > 3 && (
                    <div className="text-xs text-muted-foreground text-center">
                      +{data.branches.length - 3} more branch{data.branches.length - 3 !== 1 ? 'es' : ''}
                    </div>
                  )}
                </div>
              )}

              {/* Configuration */}
              <div className="mt-2 p-2 bg-indigo-50 border border-indigo-200 rounded text-xs">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-indigo-700">Execution Mode:</div>
                    <div className="text-indigo-600">
                      {data.waitForAll ? 'Wait for all branches' : 'Wait for any branch'}
                    </div>
                  </div>
                  {data.timeout && (
                    <div className="flex items-center space-x-1">
                      <Timer className="h-3 w-3 text-indigo-500" />
                      <span className="text-indigo-500">{data.timeout}ms</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Badges */}
              <div className="flex items-center space-x-1 mt-2">
                <Badge variant="outline" className="text-xs">
                  parallel
                </Badge>
                
                {data.branches && (
                  <Badge variant="secondary" className="text-xs">
                    {data.branches.length} branch{data.branches.length !== 1 ? 'es' : ''}
                  </Badge>
                )}

                {data.waitForAll ? (
                  <Badge variant="default" className="text-xs bg-blue-500">
                    Wait All
                  </Badge>
                ) : (
                  <Badge variant="default" className="text-xs bg-green-500">
                    Wait Any
                  </Badge>
                )}

                {data.isExecuting && (
                  <Badge variant="default" className="text-xs bg-indigo-500">
                    <Play className="h-3 w-3 mr-1" />
                    Running
                  </Badge>
                )}
              </div>

              {/* Errors */}
              {!data.isValid && data.errors.length > 0 && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                  <div className="flex items-center space-x-1">
                    <AlertCircle className="h-3 w-3" />
                    <span className="font-medium">Errors:</span>
                  </div>
                  <ul className="mt-1 space-y-1">
                    {data.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Execution Info */}
              {data.executionTime && (
                <div className="mt-2 text-xs text-muted-foreground">
                  Executed in {data.executionTime}ms
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Output Handles - Multiple for each branch */}
      {data.branches && data.branches.map((branch, index) => (
        <Handle
          key={branch.id}
          type="source"
          position={Position.Bottom}
          id={branch.id}
          className="w-3 h-3 bg-indigo-500 border-2 border-white"
          style={{ 
            bottom: -6, 
            left: `${20 + (index * (60 / Math.max(data.branches!.length - 1, 1)))}%` 
          }}
        />
      ))}

      {/* Branch Labels */}
      {data.branches && data.branches.length > 0 && (
        <div className="absolute bottom-2 left-0 right-0 flex justify-around px-2 pointer-events-none">
          {data.branches.slice(0, 4).map((branch, index) => (
            <span key={branch.id} className="text-xs text-indigo-600 font-medium truncate max-w-[40px]">
              {branch.name}
            </span>
          ))}
        </div>
      )}
    </>
  )
})

ParallelNode.displayName = 'ParallelNode'
