import React, { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Mail, 
  MessageSquare, 
  Database, 
  Globe,
  Webhook,
  FileText,
  Settings,
  AlertCircle,
  CheckCircle,
  Clock,
  Play,
  RotateCcw
} from 'lucide-react'
import { WorkflowNode, ActionNodeData } from '../types'
import { cn } from '@/lib/utils'

interface ActionNodeProps extends NodeProps {
  data: WorkflowNode['data'] & ActionNodeData
}

const actionIcons = {
  email: Mail,
  sms: MessageSquare,
  database: Database,
  api: Globe,
  webhook: Webhook,
  file: FileText,
  custom: Settings
}

const actionColors = {
  email: 'bg-blue-500',
  sms: 'bg-green-500',
  database: 'bg-purple-500',
  api: 'bg-orange-500',
  webhook: 'bg-red-500',
  file: 'bg-yellow-500',
  custom: 'bg-gray-500'
}

export const ActionNode = memo(({ data, selected, id }: ActionNodeProps) => {
  const actionType = data.actionType || 'custom'
  const Icon = actionIcons[actionType]
  const colorClass = actionColors[actionType]

  const getStatusIcon = () => {
    if (data.isFailed) return <AlertCircle className="h-3 w-3 text-red-500" />
    if (data.isCompleted) return <CheckCircle className="h-3 w-3 text-green-500" />
    if (data.isExecuting) return <Play className="h-3 w-3 text-blue-500 animate-pulse" />
    return null
  }

  const getActionDescription = () => {
    switch (actionType) {
      case 'email':
        return data.template ? `Template: ${data.template}` : 'Send email'
      case 'sms':
        return 'Send SMS message'
      case 'database':
        return data.operation ? `${data.operation} operation` : 'Database action'
      case 'api':
        return data.endpoint ? `${data.method || 'GET'} ${data.endpoint}` : 'API call'
      case 'webhook':
        return 'Send webhook'
      case 'file':
        return 'File operation'
      case 'custom':
        return 'Custom action'
      default:
        return 'Unknown action'
    }
  }

  const getRetryInfo = () => {
    if (data.retryCount && data.retryCount > 0) {
      return `${data.retryCount}/${data.maxRetries || 3} retries`
    }
    return null
  }

  return (
    <>
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-gray-400 border-2 border-white"
        style={{ top: -6 }}
      />

      <Card 
        className={cn(
          'min-w-[200px] transition-all duration-200 cursor-pointer',
          selected && 'ring-2 ring-blue-500 ring-offset-2',
          !data.isValid && 'border-red-300 bg-red-50',
          data.isExecuting && 'border-blue-300 bg-blue-50',
          data.isCompleted && 'border-green-300 bg-green-50',
          data.isFailed && 'border-red-300 bg-red-50'
        )}
      >
        <CardContent className="p-3">
          <div className="flex items-start space-x-3">
            {/* Icon */}
            <div className={cn(
              'flex items-center justify-center w-8 h-8 rounded-full text-white',
              colorClass
            )}>
              <Icon className="h-4 w-4" />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-sm truncate">
                  {data.label}
                </h3>
                {getStatusIcon()}
              </div>
              
              <p className="text-xs text-muted-foreground mt-1 truncate">
                {getActionDescription()}
              </p>

              {/* Badges */}
              <div className="flex items-center space-x-1 mt-2">
                <Badge variant="outline" className="text-xs">
                  {actionType}
                </Badge>
                
                {data.recipient && (
                  <Badge variant="secondary" className="text-xs">
                    To: {data.recipient.length > 20 ? `${data.recipient.substring(0, 20)}...` : data.recipient}
                  </Badge>
                )}

                {data.isExecuting && (
                  <Badge variant="default" className="text-xs bg-blue-500">
                    <Clock className="h-3 w-3 mr-1" />
                    Running
                  </Badge>
                )}

                {data.retryCount && data.retryCount > 0 && (
                  <Badge variant="destructive" className="text-xs">
                    <RotateCcw className="h-3 w-3 mr-1" />
                    {getRetryInfo()}
                  </Badge>
                )}
              </div>

              {/* Configuration Preview */}
              {actionType === 'api' && data.endpoint && (
                <div className="mt-2 p-2 bg-gray-50 border border-gray-200 rounded text-xs">
                  <div className="font-medium text-gray-700">API Endpoint:</div>
                  <div className="text-gray-600 truncate">
                    {data.method || 'GET'} {data.endpoint}
                  </div>
                </div>
              )}

              {actionType === 'email' && data.template && (
                <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
                  <div className="font-medium text-blue-700">Email Template:</div>
                  <div className="text-blue-600 truncate">{data.template}</div>
                </div>
              )}

              {actionType === 'database' && data.query && (
                <div className="mt-2 p-2 bg-purple-50 border border-purple-200 rounded text-xs">
                  <div className="font-medium text-purple-700">Query:</div>
                  <div className="text-purple-600 truncate">{data.query}</div>
                </div>
              )}

              {/* Errors */}
              {!data.isValid && data.errors.length > 0 && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                  <div className="flex items-center space-x-1">
                    <AlertCircle className="h-3 w-3" />
                    <span className="font-medium">Errors:</span>
                  </div>
                  <ul className="mt-1 space-y-1">
                    {data.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Execution Info */}
              {data.executionTime && (
                <div className="mt-2 text-xs text-muted-foreground">
                  Executed in {data.executionTime}ms
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-gray-400 border-2 border-white"
        style={{ bottom: -6 }}
      />
    </>
  )
})

ActionNode.displayName = 'ActionNode'
