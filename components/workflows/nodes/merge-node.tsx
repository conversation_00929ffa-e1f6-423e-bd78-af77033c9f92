import React, { memo } from 'react'
import { <PERSON>le, Position, NodeProps } from 'reactflow'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Merge,
  Users,
  Clock,
  AlertCircle,
  CheckCircle,
  Play,
  Timer,
  ArrowDown
} from 'lucide-react'
import { WorkflowNode, MergeNodeData } from '../types'
import { cn } from '@/lib/utils'

interface MergeNodeProps extends NodeProps {
  data: WorkflowNode['data'] & MergeNodeData
}

export const MergeNode = memo(({ data, selected, id }: MergeNodeProps) => {
  const getStatusIcon = () => {
    if (data.isFailed) return <AlertCircle className="h-3 w-3 text-red-500" />
    if (data.isCompleted) return <CheckCircle className="h-3 w-3 text-green-500" />
    if (data.isExecuting) return <Play className="h-3 w-3 text-blue-500 animate-pulse" />
    return null
  }

  const getMergeDescription = () => {
    const inputCount = data.requiredInputs?.length || 0
    
    switch (data.mergeType) {
      case 'all':
        return `Wait for all ${inputCount} inputs`
      case 'any':
        return `Wait for any of ${inputCount} inputs`
      case 'first':
        return 'Wait for first input'
      default:
        return 'Merge inputs'
    }
  }

  const getMergeTypeColor = () => {
    switch (data.mergeType) {
      case 'all': return 'bg-blue-500'
      case 'any': return 'bg-green-500'
      case 'first': return 'bg-orange-500'
      default: return 'bg-gray-500'
    }
  }

  const getMergeTypeIcon = () => {
    switch (data.mergeType) {
      case 'all': return Users
      case 'any': return ArrowDown
      case 'first': return Clock
      default: return Merge
    }
  }

  const MergeIcon = getMergeTypeIcon()
  const colorClass = getMergeTypeColor()

  const getInputStatus = (inputId: string) => {
    // This would be determined from execution state
    // For now, just return a placeholder
    return 'pending'
  }

  return (
    <>
      {/* Multiple Input Handles */}
      {data.requiredInputs && data.requiredInputs.map((inputId, index) => (
        <Handle
          key={inputId}
          type="target"
          position={Position.Top}
          id={inputId}
          className="w-3 h-3 bg-gray-400 border-2 border-white"
          style={{ 
            top: -6, 
            left: `${20 + (index * (60 / Math.max(data.requiredInputs!.length - 1, 1)))}%` 
          }}
        />
      ))}

      {/* Default input handle if no specific inputs defined */}
      {(!data.requiredInputs || data.requiredInputs.length === 0) && (
        <Handle
          type="target"
          position={Position.Top}
          className="w-3 h-3 bg-gray-400 border-2 border-white"
          style={{ top: -6 }}
        />
      )}

      <Card 
        className={cn(
          'min-w-[200px] transition-all duration-200 cursor-pointer',
          selected && 'ring-2 ring-blue-500 ring-offset-2',
          !data.isValid && 'border-red-300 bg-red-50',
          data.isExecuting && 'border-blue-300 bg-blue-50',
          data.isCompleted && 'border-green-300 bg-green-50',
          data.isFailed && 'border-red-300 bg-red-50'
        )}
      >
        <CardContent className="p-3">
          <div className="flex items-start space-x-3">
            {/* Icon */}
            <div className={cn(
              'flex items-center justify-center w-8 h-8 rounded-full text-white',
              colorClass
            )}>
              <MergeIcon className="h-4 w-4" />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-sm truncate">
                  {data.label}
                </h3>
                {getStatusIcon()}
              </div>
              
              <p className="text-xs text-muted-foreground mt-1">
                {getMergeDescription()}
              </p>

              {/* Input Status */}
              {data.requiredInputs && data.requiredInputs.length > 0 && (
                <div className="mt-2 space-y-1">
                  <div className="text-xs font-medium text-gray-700">Input Status:</div>
                  {data.requiredInputs.slice(0, 4).map((inputId, index) => {
                    const status = getInputStatus(inputId)
                    return (
                      <div key={inputId} className="flex items-center justify-between p-1 bg-gray-50 border border-gray-200 rounded text-xs">
                        <div className="flex items-center space-x-2">
                          <div className={cn('w-2 h-2 rounded-full', {
                            'bg-green-500': status === 'completed',
                            'bg-red-500': status === 'failed',
                            'bg-blue-500': status === 'running',
                            'bg-gray-400': status === 'pending'
                          })} />
                          <span>Input {index + 1}</span>
                        </div>
                        <span className={cn('text-xs', {
                          'text-green-600': status === 'completed',
                          'text-red-600': status === 'failed',
                          'text-blue-600': status === 'running',
                          'text-gray-600': status === 'pending'
                        })}>
                          {status}
                        </span>
                      </div>
                    )
                  })}
                  
                  {data.requiredInputs.length > 4 && (
                    <div className="text-xs text-muted-foreground text-center">
                      +{data.requiredInputs.length - 4} more input{data.requiredInputs.length - 4 !== 1 ? 's' : ''}
                    </div>
                  )}
                </div>
              )}

              {/* Configuration */}
              <div className="mt-2 p-2 bg-gray-50 border border-gray-200 rounded text-xs">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-gray-700">Merge Type:</div>
                    <div className="text-gray-600 capitalize">
                      {data.mergeType || 'all'}
                    </div>
                  </div>
                  {data.timeout && (
                    <div className="flex items-center space-x-1">
                      <Timer className="h-3 w-3 text-gray-500" />
                      <span className="text-gray-500">{data.timeout}ms</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Badges */}
              <div className="flex items-center space-x-1 mt-2">
                <Badge variant="outline" className="text-xs">
                  merge
                </Badge>
                
                <Badge variant="secondary" className="text-xs">
                  {data.mergeType || 'all'}
                </Badge>

                {data.requiredInputs && (
                  <Badge variant="outline" className="text-xs">
                    {data.requiredInputs.length} input{data.requiredInputs.length !== 1 ? 's' : ''}
                  </Badge>
                )}

                {data.isExecuting && (
                  <Badge variant="default" className="text-xs bg-blue-500">
                    <Clock className="h-3 w-3 mr-1" />
                    Waiting
                  </Badge>
                )}
              </div>

              {/* Errors */}
              {!data.isValid && data.errors.length > 0 && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                  <div className="flex items-center space-x-1">
                    <AlertCircle className="h-3 w-3" />
                    <span className="font-medium">Errors:</span>
                  </div>
                  <ul className="mt-1 space-y-1">
                    {data.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Execution Info */}
              {data.executionTime && (
                <div className="mt-2 text-xs text-muted-foreground">
                  Merged in {data.executionTime}ms
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-gray-400 border-2 border-white"
        style={{ bottom: -6 }}
      />
    </>
  )
})

MergeNode.displayName = 'MergeNode'
