import React, { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  GitBranch,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Clock,
  Play
} from 'lucide-react'
import { WorkflowNode, ConditionNodeData } from '../types'
import { cn } from '@/lib/utils'

interface ConditionNodeProps extends NodeProps {
  data: WorkflowNode['data'] & ConditionNodeData
}

export const ConditionNode = memo(({ data, selected, id }: ConditionNodeProps) => {
  const getStatusIcon = () => {
    if (data.isFailed) return <AlertCircle className="h-3 w-3 text-red-500" />
    if (data.isCompleted) return <CheckCircle2 className="h-3 w-3 text-green-500" />
    if (data.isExecuting) return <Play className="h-3 w-3 text-blue-500 animate-pulse" />
    return null
  }

  const getConditionSummary = () => {
    if (!data.conditions || data.conditions.length === 0) {
      return 'No conditions defined'
    }

    if (data.conditions.length === 1) {
      const condition = data.conditions[0]
      return `${condition.field} ${condition.operator} ${condition.value}`
    }

    return `${data.conditions.length} conditions (${data.operator || 'and'})`
  }

  const getOperatorColor = (operator: string) => {
    switch (operator) {
      case 'equals': return 'text-blue-600'
      case 'greater_than': return 'text-green-600'
      case 'less_than': return 'text-red-600'
      case 'contains': return 'text-purple-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <>
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-gray-400 border-2 border-white"
        style={{ top: -6 }}
      />

      <Card 
        className={cn(
          'min-w-[220px] transition-all duration-200 cursor-pointer',
          selected && 'ring-2 ring-blue-500 ring-offset-2',
          !data.isValid && 'border-red-300 bg-red-50',
          data.isExecuting && 'border-blue-300 bg-blue-50',
          data.isCompleted && 'border-green-300 bg-green-50',
          data.isFailed && 'border-red-300 bg-red-50'
        )}
      >
        <CardContent className="p-3">
          <div className="flex items-start space-x-3">
            {/* Icon */}
            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-yellow-500 text-white">
              <GitBranch className="h-4 w-4" />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-sm truncate">
                  {data.label}
                </h3>
                {getStatusIcon()}
              </div>
              
              <p className="text-xs text-muted-foreground mt-1">
                {getConditionSummary()}
              </p>

              {/* Conditions List */}
              {data.conditions && data.conditions.length > 0 && (
                <div className="mt-2 space-y-1">
                  {data.conditions.slice(0, 2).map((condition, index) => (
                    <div key={index} className="text-xs p-2 bg-gray-50 border border-gray-200 rounded">
                      <span className="font-medium">{condition.field}</span>
                      <span className={cn('mx-1', getOperatorColor(condition.operator))}>
                        {condition.operator.replace('_', ' ')}
                      </span>
                      <span className="text-gray-600">
                        {typeof condition.value === 'string' && condition.value.length > 15
                          ? `${condition.value.substring(0, 15)}...`
                          : String(condition.value)
                        }
                      </span>
                    </div>
                  ))}
                  
                  {data.conditions.length > 2 && (
                    <div className="text-xs text-muted-foreground text-center">
                      +{data.conditions.length - 2} more condition{data.conditions.length - 2 !== 1 ? 's' : ''}
                    </div>
                  )}
                </div>
              )}

              {/* Badges */}
              <div className="flex items-center space-x-1 mt-2">
                <Badge variant="outline" className="text-xs">
                  condition
                </Badge>
                
                {data.operator && data.conditions && data.conditions.length > 1 && (
                  <Badge variant="secondary" className="text-xs">
                    {data.operator.toUpperCase()}
                  </Badge>
                )}

                {data.isExecuting && (
                  <Badge variant="default" className="text-xs bg-blue-500">
                    <Clock className="h-3 w-3 mr-1" />
                    Evaluating
                  </Badge>
                )}
              </div>

              {/* Errors */}
              {!data.isValid && data.errors.length > 0 && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                  <div className="flex items-center space-x-1">
                    <AlertCircle className="h-3 w-3" />
                    <span className="font-medium">Errors:</span>
                  </div>
                  <ul className="mt-1 space-y-1">
                    {data.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Execution Info */}
              {data.executionTime && (
                <div className="mt-2 text-xs text-muted-foreground">
                  Evaluated in {data.executionTime}ms
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Output Handles - True and False paths */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="true"
        className="w-3 h-3 bg-green-500 border-2 border-white"
        style={{ bottom: -6, left: '25%' }}
      />
      
      <Handle
        type="source"
        position={Position.Bottom}
        id="false"
        className="w-3 h-3 bg-red-500 border-2 border-white"
        style={{ bottom: -6, right: '25%' }}
      />

      {/* Path Labels */}
      <div className="absolute bottom-2 left-0 right-0 flex justify-between px-4 pointer-events-none">
        <div className="flex items-center space-x-1">
          <CheckCircle2 className="h-3 w-3 text-green-500" />
          <span className="text-xs text-green-600 font-medium">True</span>
        </div>
        <div className="flex items-center space-x-1">
          <XCircle className="h-3 w-3 text-red-500" />
          <span className="text-xs text-red-600 font-medium">False</span>
        </div>
      </div>
    </>
  )
})

ConditionNode.displayName = 'ConditionNode'
