import React, { memo } from 'react'
import { <PERSON>le, Position, NodeProps } from 'reactflow'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  RotateCcw,
  List,
  Infinity,
  Hash,
  AlertCircle,
  CheckCircle,
  Play,
  Timer,
  StopCircle
} from 'lucide-react'
import { WorkflowNode, LoopNodeData } from '../types'
import { cn } from '@/lib/utils'

interface LoopNodeProps extends NodeProps {
  data: WorkflowNode['data'] & LoopNodeData
}

export const LoopNode = memo(({ data, selected, id }: LoopNodeProps) => {
  const getStatusIcon = () => {
    if (data.isFailed) return <AlertCircle className="h-3 w-3 text-red-500" />
    if (data.isCompleted) return <CheckCircle className="h-3 w-3 text-green-500" />
    if (data.isExecuting) return <Play className="h-3 w-3 text-blue-500 animate-pulse" />
    return null
  }

  const getLoopDescription = () => {
    switch (data.loopType) {
      case 'for_each':
        return data.collection ? `For each in ${data.collection}` : 'For each (no collection)'
      case 'while':
        return data.condition ? 'While condition is true' : 'While (no condition)'
      case 'count':
        return data.count ? `Repeat ${data.count} times` : 'Count loop (no count)'
      default:
        return 'Loop configuration needed'
    }
  }

  const getLoopIcon = () => {
    switch (data.loopType) {
      case 'for_each': return List
      case 'while': return Infinity
      case 'count': return Hash
      default: return RotateCcw
    }
  }

  const getLoopColor = () => {
    switch (data.loopType) {
      case 'for_each': return 'bg-green-500'
      case 'while': return 'bg-blue-500'
      case 'count': return 'bg-purple-500'
      default: return 'bg-gray-500'
    }
  }

  const LoopIcon = getLoopIcon()
  const colorClass = getLoopColor()

  const getCurrentIteration = () => {
    // This would be determined from execution state
    // For now, just return a placeholder
    if (data.isExecuting) {
      return Math.floor(Math.random() * 5) + 1
    }
    return null
  }

  const getMaxIterations = () => {
    switch (data.loopType) {
      case 'count':
        return data.count || 0
      case 'for_each':
        return 'Collection size'
      case 'while':
        return data.maxIterations || 'Unlimited'
      default:
        return 'Unknown'
    }
  }

  return (
    <>
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-gray-400 border-2 border-white"
        style={{ top: -6 }}
      />

      <Card 
        className={cn(
          'min-w-[220px] transition-all duration-200 cursor-pointer',
          selected && 'ring-2 ring-blue-500 ring-offset-2',
          !data.isValid && 'border-red-300 bg-red-50',
          data.isExecuting && 'border-blue-300 bg-blue-50',
          data.isCompleted && 'border-green-300 bg-green-50',
          data.isFailed && 'border-red-300 bg-red-50'
        )}
      >
        <CardContent className="p-3">
          <div className="flex items-start space-x-3">
            {/* Icon */}
            <div className={cn(
              'flex items-center justify-center w-8 h-8 rounded-full text-white',
              colorClass
            )}>
              <LoopIcon className="h-4 w-4" />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-sm truncate">
                  {data.label}
                </h3>
                {getStatusIcon()}
              </div>
              
              <p className="text-xs text-muted-foreground mt-1">
                {getLoopDescription()}
              </p>

              {/* Loop Configuration */}
              <div className="mt-2 p-2 bg-gray-50 border border-gray-200 rounded text-xs">
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-gray-700">Type:</span>
                    <span className="text-gray-600 capitalize">{data.loopType || 'not set'}</span>
                  </div>
                  
                  {data.loopType === 'for_each' && data.collection && (
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-700">Collection:</span>
                      <span className="text-gray-600 truncate max-w-[100px]">{data.collection}</span>
                    </div>
                  )}
                  
                  {data.loopType === 'count' && data.count && (
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-700">Count:</span>
                      <span className="text-gray-600">{data.count}</span>
                    </div>
                  )}
                  
                  {data.maxIterations && (
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-700">Max:</span>
                      <span className="text-gray-600">{data.maxIterations}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Condition Display */}
              {data.loopType === 'while' && data.condition && (
                <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
                  <div className="font-medium text-blue-700">While Condition:</div>
                  <div className="text-blue-600">
                    {data.condition.field} {data.condition.operator} {data.condition.value}
                  </div>
                </div>
              )}

              {/* Break Condition */}
              {data.breakCondition && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs">
                  <div className="font-medium text-red-700">Break Condition:</div>
                  <div className="text-red-600">
                    {data.breakCondition.field} {data.breakCondition.operator} {data.breakCondition.value}
                  </div>
                </div>
              )}

              {/* Current Iteration */}
              {data.isExecuting && getCurrentIteration() && (
                <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1">
                      <Timer className="h-3 w-3 text-yellow-600" />
                      <span className="font-medium text-yellow-700">Current Iteration:</span>
                    </div>
                    <span className="text-yellow-600">
                      {getCurrentIteration()} / {getMaxIterations()}
                    </span>
                  </div>
                </div>
              )}

              {/* Badges */}
              <div className="flex items-center space-x-1 mt-2">
                <Badge variant="outline" className="text-xs">
                  loop
                </Badge>
                
                <Badge variant="secondary" className="text-xs">
                  {data.loopType || 'not set'}
                </Badge>

                {data.maxIterations && (
                  <Badge variant="outline" className="text-xs">
                    max: {data.maxIterations}
                  </Badge>
                )}

                {data.isExecuting && (
                  <Badge variant="default" className="text-xs bg-blue-500">
                    <RotateCcw className="h-3 w-3 mr-1 animate-spin" />
                    Looping
                  </Badge>
                )}
              </div>

              {/* Errors */}
              {!data.isValid && data.errors.length > 0 && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                  <div className="flex items-center space-x-1">
                    <AlertCircle className="h-3 w-3" />
                    <span className="font-medium">Errors:</span>
                  </div>
                  <ul className="mt-1 space-y-1">
                    {data.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Execution Info */}
              {data.executionTime && (
                <div className="mt-2 text-xs text-muted-foreground">
                  Loop executed in {data.executionTime}ms
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Output Handles - Loop body and exit */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="loop"
        className="w-3 h-3 bg-blue-500 border-2 border-white"
        style={{ bottom: -6, left: '30%' }}
      />
      
      <Handle
        type="source"
        position={Position.Bottom}
        id="exit"
        className="w-3 h-3 bg-green-500 border-2 border-white"
        style={{ bottom: -6, right: '30%' }}
      />

      {/* Path Labels */}
      <div className="absolute bottom-2 left-0 right-0 flex justify-between px-6 pointer-events-none">
        <div className="flex items-center space-x-1">
          <RotateCcw className="h-3 w-3 text-blue-500" />
          <span className="text-xs text-blue-600 font-medium">Loop</span>
        </div>
        <div className="flex items-center space-x-1">
          <StopCircle className="h-3 w-3 text-green-500" />
          <span className="text-xs text-green-600 font-medium">Exit</span>
        </div>
      </div>
    </>
  )
})

LoopNode.displayName = 'LoopNode'
