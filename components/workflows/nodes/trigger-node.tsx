import React, { memo } from 'react'
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Zap, 
  Calendar, 
  MousePointer, 
  Webhook,
  AlertCircle,
  CheckCircle,
  Clock,
  Play
} from 'lucide-react'
import { WorkflowNode, TriggerNodeData } from '../types'
import { cn } from '@/lib/utils'

interface TriggerNodeProps extends NodeProps {
  data: WorkflowNode['data'] & TriggerNodeData
}

const triggerIcons = {
  event: Zap,
  schedule: Calendar,
  manual: MousePointer,
  webhook: Webhook
}

const triggerColors = {
  event: 'bg-blue-500',
  schedule: 'bg-green-500',
  manual: 'bg-purple-500',
  webhook: 'bg-orange-500'
}

export const TriggerNode = memo(({ data, selected, id }: TriggerNodeProps) => {
  const triggerType = data.triggerType || 'event'
  const Icon = triggerIcons[triggerType]
  const colorClass = triggerColors[triggerType]

  const getStatusIcon = () => {
    if (data.isFailed) return <AlertCircle className="h-3 w-3 text-red-500" />
    if (data.isCompleted) return <CheckCircle className="h-3 w-3 text-green-500" />
    if (data.isExecuting) return <Play className="h-3 w-3 text-blue-500 animate-pulse" />
    return null
  }

  const getTriggerDescription = () => {
    switch (triggerType) {
      case 'event':
        return data.event || 'No event specified'
      case 'schedule':
        return data.schedule || 'No schedule specified'
      case 'manual':
        return 'Manual trigger'
      case 'webhook':
        return 'Webhook trigger'
      default:
        return 'Unknown trigger'
    }
  }

  return (
    <>
      <Card 
        className={cn(
          'min-w-[200px] transition-all duration-200 cursor-pointer',
          selected && 'ring-2 ring-blue-500 ring-offset-2',
          !data.isValid && 'border-red-300 bg-red-50',
          data.isExecuting && 'border-blue-300 bg-blue-50',
          data.isCompleted && 'border-green-300 bg-green-50',
          data.isFailed && 'border-red-300 bg-red-50'
        )}
      >
        <CardContent className="p-3">
          <div className="flex items-start space-x-3">
            {/* Icon */}
            <div className={cn(
              'flex items-center justify-center w-8 h-8 rounded-full text-white',
              colorClass
            )}>
              <Icon className="h-4 w-4" />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-sm truncate">
                  {data.label}
                </h3>
                {getStatusIcon()}
              </div>
              
              <p className="text-xs text-muted-foreground mt-1 truncate">
                {getTriggerDescription()}
              </p>

              {/* Badges */}
              <div className="flex items-center space-x-1 mt-2">
                <Badge variant="outline" className="text-xs">
                  {triggerType}
                </Badge>
                
                {data.conditions && data.conditions.length > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {data.conditions.length} condition{data.conditions.length !== 1 ? 's' : ''}
                  </Badge>
                )}

                {data.isExecuting && (
                  <Badge variant="default" className="text-xs bg-blue-500">
                    <Clock className="h-3 w-3 mr-1" />
                    Running
                  </Badge>
                )}
              </div>

              {/* Errors */}
              {!data.isValid && data.errors.length > 0 && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                  <div className="flex items-center space-x-1">
                    <AlertCircle className="h-3 w-3" />
                    <span className="font-medium">Errors:</span>
                  </div>
                  <ul className="mt-1 space-y-1">
                    {data.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Execution Info */}
              {data.executionTime && (
                <div className="mt-2 text-xs text-muted-foreground">
                  Executed in {data.executionTime}ms
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-blue-500 border-2 border-white"
        style={{ bottom: -6 }}
      />
    </>
  )
})

TriggerNode.displayName = 'TriggerNode'
