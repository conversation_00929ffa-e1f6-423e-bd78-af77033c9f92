import React, { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Plug,
  Cloud,
  Database,
  Mail,
  CreditCard,
  ShoppingCart,
  MessageSquare,
  FileText,
  AlertCircle,
  CheckCircle,
  Play,
  Key,
  Shield
} from 'lucide-react'
import { WorkflowNode, IntegrationNodeData } from '../types'
import { cn } from '@/lib/utils'

interface IntegrationNodeProps extends NodeProps {
  data: WorkflowNode['data'] & IntegrationNodeData
}

const serviceIcons = {
  stripe: CreditCard,
  paypal: CreditCard,
  shopify: ShoppingCart,
  woocommerce: ShoppingCart,
  mailchimp: Mail,
  sendgrid: Mail,
  twilio: MessageSquare,
  slack: MessageSquare,
  google: Cloud,
  microsoft: Cloud,
  salesforce: Database,
  hubspot: Database,
  zapier: Plug,
  webhook: Plug,
  custom: FileText
}

const serviceColors = {
  stripe: 'bg-purple-500',
  paypal: 'bg-blue-500',
  shopify: 'bg-green-500',
  woocommerce: 'bg-purple-600',
  mailchimp: 'bg-yellow-500',
  sendgrid: 'bg-blue-600',
  twilio: 'bg-red-500',
  slack: 'bg-purple-400',
  google: 'bg-red-500',
  microsoft: 'bg-blue-500',
  salesforce: 'bg-blue-400',
  hubspot: 'bg-orange-500',
  zapier: 'bg-orange-400',
  webhook: 'bg-gray-500',
  custom: 'bg-gray-600'
}

export const IntegrationNode = memo(({ data, selected, id }: IntegrationNodeProps) => {
  const service = data.service?.toLowerCase() || 'custom'
  const Icon = serviceIcons[service as keyof typeof serviceIcons] || Plug
  const colorClass = serviceColors[service as keyof typeof serviceColors] || 'bg-gray-500'

  const getStatusIcon = () => {
    if (data.isFailed) return <AlertCircle className="h-3 w-3 text-red-500" />
    if (data.isCompleted) return <CheckCircle className="h-3 w-3 text-green-500" />
    if (data.isExecuting) return <Play className="h-3 w-3 text-blue-500 animate-pulse" />
    return null
  }

  const getIntegrationDescription = () => {
    if (!data.service) return 'No service configured'
    
    const operation = data.operation || 'operation'
    return `${data.service} - ${operation}`
  }

  const getAuthenticationBadge = () => {
    if (!data.authentication) return null

    const authTypes = {
      api_key: { label: 'API Key', color: 'bg-blue-500' },
      oauth: { label: 'OAuth', color: 'bg-green-500' },
      basic: { label: 'Basic Auth', color: 'bg-yellow-500' },
      bearer: { label: 'Bearer Token', color: 'bg-purple-500' }
    }

    const auth = authTypes[data.authentication.type as keyof typeof authTypes]
    if (!auth) return null

    return (
      <Badge variant="outline" className="text-xs">
        <Shield className="h-3 w-3 mr-1" />
        {auth.label}
      </Badge>
    )
  }

  const getServiceDisplayName = () => {
    const serviceNames = {
      stripe: 'Stripe',
      paypal: 'PayPal',
      shopify: 'Shopify',
      woocommerce: 'WooCommerce',
      mailchimp: 'Mailchimp',
      sendgrid: 'SendGrid',
      twilio: 'Twilio',
      slack: 'Slack',
      google: 'Google',
      microsoft: 'Microsoft',
      salesforce: 'Salesforce',
      hubspot: 'HubSpot',
      zapier: 'Zapier',
      webhook: 'Webhook',
      custom: 'Custom'
    }

    return serviceNames[service as keyof typeof serviceNames] || data.service || 'Unknown'
  }

  return (
    <>
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-gray-400 border-2 border-white"
        style={{ top: -6 }}
      />

      <Card 
        className={cn(
          'min-w-[200px] transition-all duration-200 cursor-pointer',
          selected && 'ring-2 ring-blue-500 ring-offset-2',
          !data.isValid && 'border-red-300 bg-red-50',
          data.isExecuting && 'border-blue-300 bg-blue-50',
          data.isCompleted && 'border-green-300 bg-green-50',
          data.isFailed && 'border-red-300 bg-red-50'
        )}
      >
        <CardContent className="p-3">
          <div className="flex items-start space-x-3">
            {/* Icon */}
            <div className={cn(
              'flex items-center justify-center w-8 h-8 rounded-full text-white',
              colorClass
            )}>
              <Icon className="h-4 w-4" />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-sm truncate">
                  {data.label}
                </h3>
                {getStatusIcon()}
              </div>
              
              <p className="text-xs text-muted-foreground mt-1 truncate">
                {getIntegrationDescription()}
              </p>

              {/* Service Info */}
              <div className="mt-2 p-2 bg-gray-50 border border-gray-200 rounded text-xs">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-gray-700">Service:</div>
                    <div className="text-gray-600">{getServiceDisplayName()}</div>
                  </div>
                  {data.authentication && (
                    <div className="flex items-center space-x-1">
                      <Key className="h-3 w-3 text-gray-500" />
                      <span className="text-gray-500">Auth</span>
                    </div>
                  )}
                </div>
                
                {data.operation && (
                  <div className="mt-1">
                    <div className="font-medium text-gray-700">Operation:</div>
                    <div className="text-gray-600">{data.operation}</div>
                  </div>
                )}
              </div>

              {/* Configuration Preview */}
              {data.config && Object.keys(data.config).length > 0 && (
                <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
                  <div className="font-medium text-blue-700">Configuration:</div>
                  <div className="text-blue-600 space-y-1">
                    {Object.entries(data.config).slice(0, 2).map(([key, value]) => (
                      <div key={key} className="truncate">
                        <span className="font-medium">{key}:</span> {String(value)}
                      </div>
                    ))}
                    {Object.keys(data.config).length > 2 && (
                      <div className="text-blue-500">
                        +{Object.keys(data.config).length - 2} more...
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Badges */}
              <div className="flex items-center space-x-1 mt-2">
                <Badge variant="outline" className="text-xs">
                  integration
                </Badge>
                
                {getAuthenticationBadge()}

                {data.isExecuting && (
                  <Badge variant="default" className="text-xs bg-blue-500">
                    <Play className="h-3 w-3 mr-1" />
                    Connecting
                  </Badge>
                )}
              </div>

              {/* Errors */}
              {!data.isValid && data.errors.length > 0 && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                  <div className="flex items-center space-x-1">
                    <AlertCircle className="h-3 w-3" />
                    <span className="font-medium">Errors:</span>
                  </div>
                  <ul className="mt-1 space-y-1">
                    {data.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Execution Info */}
              {data.executionTime && (
                <div className="mt-2 text-xs text-muted-foreground">
                  Executed in {data.executionTime}ms
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-gray-400 border-2 border-white"
        style={{ bottom: -6 }}
      />
    </>
  )
})

IntegrationNode.displayName = 'IntegrationNode'
