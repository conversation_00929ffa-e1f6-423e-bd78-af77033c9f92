'use client'

import React, { useState, use<PERSON><PERSON>back, useRef, useEffect } from 'react'
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  ReactFlowProvider,
  useReactFlow,
  Panel,
  NodeTypes,
  EdgeTypes,
  MarkerType
} from 'reactflow'
import 'reactflow/dist/style.css'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Play,
  Pause,
  Save,
  Undo,
  Redo,
  Copy,
  Clipboard,
  ZoomIn,
  ZoomOut,
  Maximize,
  Grid,
  Eye,
  Settings,
  Download,
  Upload,
  History,
  Bug,
  TestTube
} from 'lucide-react'

// Custom Node Components
import { TriggerNode } from './nodes/trigger-node'
import { ActionNode } from './nodes/action-node'
import { ConditionNode } from './nodes/condition-node'
import { DelayNode } from './nodes/delay-node'
import { IntegrationNode } from './nodes/integration-node'
import { ParallelNode } from './nodes/parallel-node'
import { MergeNode } from './nodes/merge-node'
import { LoopNode } from './nodes/loop-node'

// Custom Edge Components
import { ConditionalEdge } from './edges/conditional-edge'
import { ErrorEdge } from './edges/error-edge'

// Panels and Dialogs
import { NodePropertiesPanel } from './panels/node-properties-panel'
import { WorkflowSettingsPanel } from './panels/workflow-settings-panel'
import { TestingPanel } from './panels/testing-panel'
import { TemplateGallery } from './panels/template-gallery'

// Types
import { WorkflowNode, WorkflowEdge, FlowState, ExecutionState } from './types'

const nodeTypes: NodeTypes = {
  trigger: TriggerNode,
  action: ActionNode,
  condition: ConditionNode,
  delay: DelayNode,
  integration: IntegrationNode,
  parallel: ParallelNode,
  merge: MergeNode,
  loop: LoopNode
}

const edgeTypes: EdgeTypes = {
  conditional: ConditionalEdge,
  error: ErrorEdge
}

interface VisualFlowDesignerProps {
  initialWorkflow?: any
  onSave?: (workflow: any) => void
  onTest?: (workflow: any) => void
  readOnly?: boolean
}

export function VisualFlowDesigner({
  initialWorkflow,
  onSave,
  onTest,
  readOnly = false
}: VisualFlowDesignerProps) {
  // Flow state
  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])
  const [flowState, setFlowState] = useState<FlowState>({
    isExecuting: false,
    executionPath: [],
    errors: [],
    selectedNodeId: null,
    clipboard: null,
    history: [],
    historyIndex: -1
  })

  // UI state
  const [showPropertiesPanel, setShowPropertiesPanel] = useState(false)
  const [showSettingsPanel, setShowSettingsPanel] = useState(false)
  const [showTestingPanel, setShowTestingPanel] = useState(false)
  const [showTemplateGallery, setShowTemplateGallery] = useState(false)
  const [isGridVisible, setIsGridVisible] = useState(true)
  const [executionState, setExecutionState] = useState<ExecutionState>({
    isRunning: false,
    currentStep: null,
    completedSteps: [],
    failedSteps: [],
    executionLog: []
  })

  const reactFlowWrapper = useRef<HTMLDivElement>(null)
  const { project, getViewport, setViewport, fitView } = useReactFlow()

  // Initialize workflow from props
  useEffect(() => {
    if (initialWorkflow) {
      loadWorkflowFromDefinition(initialWorkflow)
    }
  }, [initialWorkflow])

  // Auto-save functionality
  useEffect(() => {
    const autoSaveInterval = setInterval(() => {
      if (!readOnly && nodes.length > 0) {
        saveToHistory()
      }
    }, 30000) // Auto-save every 30 seconds

    return () => clearInterval(autoSaveInterval)
  }, [nodes, edges, readOnly])

  const onConnect = useCallback(
    (params: Connection) => {
      const edge: WorkflowEdge = {
        ...params,
        id: `edge-${params.source}-${params.target}`,
        type: 'default',
        animated: false,
        markerEnd: {
          type: MarkerType.ArrowClosed,
          width: 20,
          height: 20,
          color: '#6366f1'
        }
      }
      setEdges((eds) => addEdge(edge, eds))
      saveToHistory()
    },
    [setEdges]
  )

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'move'
  }, [])

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault()

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect()
      if (!reactFlowBounds) return

      const type = event.dataTransfer.getData('application/reactflow')
      if (!type) return

      const position = project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top
      })

      const newNode: WorkflowNode = {
        id: `${type}-${Date.now()}`,
        type,
        position,
        data: {
          label: `New ${type} Node`,
          config: {},
          isValid: true,
          errors: []
        }
      }

      setNodes((nds) => nds.concat(newNode))
      saveToHistory()
    },
    [project, setNodes]
  )

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setFlowState(prev => ({ ...prev, selectedNodeId: node.id }))
    setShowPropertiesPanel(true)
  }, [])

  const onNodeContextMenu = useCallback((event: React.MouseEvent, node: Node) => {
    event.preventDefault()
    // Show context menu
    showNodeContextMenu(event, node)
  }, [])

  const saveToHistory = useCallback(() => {
    const state = {
      nodes: nodes.map(node => ({ ...node })),
      edges: edges.map(edge => ({ ...edge })),
      timestamp: Date.now()
    }

    setFlowState(prev => {
      const newHistory = prev.history.slice(0, prev.historyIndex + 1)
      newHistory.push(state)
      
      // Limit history to 50 entries
      if (newHistory.length > 50) {
        newHistory.shift()
      }

      return {
        ...prev,
        history: newHistory,
        historyIndex: newHistory.length - 1
      }
    })
  }, [nodes, edges])

  const undo = useCallback(() => {
    setFlowState(prev => {
      if (prev.historyIndex > 0) {
        const newIndex = prev.historyIndex - 1
        const state = prev.history[newIndex]
        setNodes(state.nodes)
        setEdges(state.edges)
        return { ...prev, historyIndex: newIndex }
      }
      return prev
    })
  }, [setNodes, setEdges])

  const redo = useCallback(() => {
    setFlowState(prev => {
      if (prev.historyIndex < prev.history.length - 1) {
        const newIndex = prev.historyIndex + 1
        const state = prev.history[newIndex]
        setNodes(state.nodes)
        setEdges(state.edges)
        return { ...prev, historyIndex: newIndex }
      }
      return prev
    })
  }, [setNodes, setEdges])

  const copySelectedNode = useCallback(() => {
    const selectedNode = nodes.find(node => node.id === flowState.selectedNodeId)
    if (selectedNode) {
      setFlowState(prev => ({ ...prev, clipboard: selectedNode }))
    }
  }, [nodes, flowState.selectedNodeId])

  const pasteNode = useCallback(() => {
    if (flowState.clipboard) {
      const viewport = getViewport()
      const newNode: WorkflowNode = {
        ...flowState.clipboard,
        id: `${flowState.clipboard.type}-${Date.now()}`,
        position: {
          x: flowState.clipboard.position.x + 50,
          y: flowState.clipboard.position.y + 50
        }
      }
      setNodes(nds => nds.concat(newNode))
      saveToHistory()
    }
  }, [flowState.clipboard, getViewport, setNodes, saveToHistory])

  const deleteSelectedNode = useCallback(() => {
    if (flowState.selectedNodeId) {
      setNodes(nds => nds.filter(node => node.id !== flowState.selectedNodeId))
      setEdges(eds => eds.filter(edge => 
        edge.source !== flowState.selectedNodeId && edge.target !== flowState.selectedNodeId
      ))
      setFlowState(prev => ({ ...prev, selectedNodeId: null }))
      setShowPropertiesPanel(false)
      saveToHistory()
    }
  }, [flowState.selectedNodeId, setNodes, setEdges, saveToHistory])

  const validateWorkflow = useCallback(() => {
    const errors: string[] = []
    const nodeErrors = new Map<string, string[]>()

    // Check for trigger node
    const triggerNodes = nodes.filter(node => node.type === 'trigger')
    if (triggerNodes.length === 0) {
      errors.push('Workflow must have at least one trigger node')
    } else if (triggerNodes.length > 1) {
      errors.push('Workflow can only have one trigger node')
    }

    // Check for disconnected nodes
    nodes.forEach(node => {
      if (node.type !== 'trigger') {
        const hasIncomingEdge = edges.some(edge => edge.target === node.id)
        if (!hasIncomingEdge) {
          const nodeErrs = nodeErrors.get(node.id) || []
          nodeErrs.push('Node is not connected to the workflow')
          nodeErrors.set(node.id, nodeErrs)
        }
      }
    })

    // Validate individual nodes
    nodes.forEach(node => {
      const nodeValidation = validateNode(node)
      if (!nodeValidation.isValid) {
        nodeErrors.set(node.id, nodeValidation.errors)
      }
    })

    // Update node error states
    setNodes(nds => nds.map(node => ({
      ...node,
      data: {
        ...node.data,
        isValid: !nodeErrors.has(node.id),
        errors: nodeErrors.get(node.id) || []
      }
    })))

    setFlowState(prev => ({ ...prev, errors }))
    return errors.length === 0
  }, [nodes, edges, setNodes])

  const validateNode = (node: WorkflowNode) => {
    const errors: string[] = []

    // Basic validation
    if (!node.data.label || node.data.label.trim() === '') {
      errors.push('Node must have a name')
    }

    // Type-specific validation
    switch (node.type) {
      case 'trigger':
        if (!node.data.config.event && !node.data.config.schedule) {
          errors.push('Trigger must specify an event or schedule')
        }
        break
      case 'action':
        if (!node.data.config.actionType) {
          errors.push('Action must specify an action type')
        }
        break
      case 'condition':
        if (!node.data.config.conditions || node.data.config.conditions.length === 0) {
          errors.push('Condition must have at least one condition')
        }
        break
      case 'delay':
        if (!node.data.config.delay || node.data.config.delay <= 0) {
          errors.push('Delay must specify a positive duration')
        }
        break
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  const executeWorkflow = useCallback(async (testData?: any) => {
    if (!validateWorkflow()) {
      return
    }

    setExecutionState({
      isRunning: true,
      currentStep: null,
      completedSteps: [],
      failedSteps: [],
      executionLog: []
    })

    try {
      const workflowDefinition = convertToWorkflowDefinition()
      if (onTest) {
        await onTest(workflowDefinition)
      }
    } catch (error) {
      console.error('Workflow execution failed:', error)
    } finally {
      setExecutionState(prev => ({ ...prev, isRunning: false }))
    }
  }, [validateWorkflow, onTest])

  const convertToWorkflowDefinition = () => {
    // Convert visual flow to workflow definition
    const triggerNode = nodes.find(node => node.type === 'trigger')
    if (!triggerNode) throw new Error('No trigger node found')

    const steps = nodes
      .filter(node => node.type !== 'trigger')
      .map(node => ({
        id: node.id,
        name: node.data.label,
        description: node.data.description || '',
        type: node.type,
        status: 'pending',
        config: node.data.config,
        dependencies: edges
          .filter(edge => edge.target === node.id)
          .map(edge => edge.source),
        retryCount: 0,
        maxRetries: node.data.config.maxRetries || 3
      }))

    return {
      name: 'Visual Workflow',
      description: 'Created with visual flow designer',
      version: '1.0.0',
      category: 'custom',
      trigger: triggerNode.data.config,
      steps,
      isActive: true,
      tags: ['visual', 'designer'],
      metadata: {
        createdWith: 'visual-flow-designer',
        nodeCount: nodes.length,
        edgeCount: edges.length
      }
    }
  }

  const loadWorkflowFromDefinition = (workflow: any) => {
    // Convert workflow definition to visual flow
    const flowNodes: WorkflowNode[] = []
    const flowEdges: WorkflowEdge[] = []

    // Create trigger node
    if (workflow.trigger) {
      flowNodes.push({
        id: 'trigger',
        type: 'trigger',
        position: { x: 100, y: 100 },
        data: {
          label: 'Trigger',
          config: workflow.trigger,
          isValid: true,
          errors: []
        }
      })
    }

    // Create step nodes
    workflow.steps?.forEach((step: any, index: number) => {
      flowNodes.push({
        id: step.id,
        type: step.type,
        position: { x: 100 + (index % 3) * 300, y: 250 + Math.floor(index / 3) * 150 },
        data: {
          label: step.name,
          description: step.description,
          config: step.config,
          isValid: true,
          errors: []
        }
      })

      // Create edges for dependencies
      if (step.dependencies) {
        step.dependencies.forEach((depId: string) => {
          flowEdges.push({
            id: `edge-${depId}-${step.id}`,
            source: depId,
            target: step.id,
            type: 'default',
            markerEnd: {
              type: MarkerType.ArrowClosed,
              width: 20,
              height: 20,
              color: '#6366f1'
            }
          })
        })
      } else if (index === 0) {
        // Connect first step to trigger
        flowEdges.push({
          id: `edge-trigger-${step.id}`,
          source: 'trigger',
          target: step.id,
          type: 'default',
          markerEnd: {
            type: MarkerType.ArrowClosed,
            width: 20,
            height: 20,
            color: '#6366f1'
          }
        })
      }
    })

    setNodes(flowNodes)
    setEdges(flowEdges)
    setTimeout(() => fitView(), 100)
  }

  const showNodeContextMenu = (event: React.MouseEvent, node: Node) => {
    // Implementation for context menu
    console.log('Context menu for node:', node.id)
  }

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'z':
            event.preventDefault()
            if (event.shiftKey) {
              redo()
            } else {
              undo()
            }
            break
          case 'c':
            event.preventDefault()
            copySelectedNode()
            break
          case 'v':
            event.preventDefault()
            pasteNode()
            break
          case 's':
            event.preventDefault()
            if (onSave) {
              onSave(convertToWorkflowDefinition())
            }
            break
        }
      } else if (event.key === 'Delete' || event.key === 'Backspace') {
        event.preventDefault()
        deleteSelectedNode()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [undo, redo, copySelectedNode, pasteNode, deleteSelectedNode, onSave])

  return (
    <div className="h-full w-full flex flex-col">
      {/* Toolbar */}
      <div className="flex items-center justify-between p-4 border-b bg-background">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={undo}
            disabled={flowState.historyIndex <= 0}
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={redo}
            disabled={flowState.historyIndex >= flowState.history.length - 1}
          >
            <Redo className="h-4 w-4" />
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <Button variant="outline" size="sm" onClick={copySelectedNode}>
            <Copy className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={pasteNode}>
            <Clipboard className="h-4 w-4" />
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsGridVisible(!isGridVisible)}
          >
            <Grid className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <Badge variant="outline">
            {nodes.length} nodes, {edges.length} connections
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowTemplateGallery(true)}
          >
            <Eye className="h-4 w-4 mr-2" />
            Templates
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowTestingPanel(true)}
          >
            <TestTube className="h-4 w-4 mr-2" />
            Test
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSettingsPanel(true)}
          >
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
          <Button
            onClick={() => {
              if (onSave) {
                onSave(convertToWorkflowDefinition())
              }
            }}
            disabled={!validateWorkflow()}
          >
            <Save className="h-4 w-4 mr-2" />
            Save Workflow
          </Button>
        </div>
      </div>

      {/* Main Flow Area */}
      <div className="flex-1 relative" ref={reactFlowWrapper}>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onDrop={onDrop}
          onDragOver={onDragOver}
          onNodeClick={onNodeClick}
          onNodeContextMenu={onNodeContextMenu}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          fitView
          snapToGrid
          snapGrid={[20, 20]}
          defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        >
          <Controls />
          <MiniMap 
            nodeStrokeColor="#6366f1"
            nodeColor="#e0e7ff"
            nodeBorderRadius={8}
            maskColor="rgba(0, 0, 0, 0.1)"
          />
          <Background 
            variant={BackgroundVariant.Dots} 
            gap={20} 
            size={1}
            style={{ opacity: isGridVisible ? 0.5 : 0 }}
          />
          
          {/* Execution Overlay */}
          {executionState.isRunning && (
            <Panel position="top-center">
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <Play className="h-4 w-4 text-blue-600 animate-pulse" />
                    <span className="text-sm font-medium text-blue-800">
                      Executing workflow...
                    </span>
                  </div>
                </CardContent>
              </Card>
            </Panel>
          )}

          {/* Validation Errors */}
          {flowState.errors.length > 0 && (
            <Panel position="top-left">
              <Card className="bg-red-50 border-red-200 max-w-sm">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm text-red-800">Validation Errors</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <ul className="text-xs text-red-700 space-y-1">
                    {flowState.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </Panel>
          )}
        </ReactFlow>
      </div>

      {/* Side Panels */}
      {showPropertiesPanel && (
        <NodePropertiesPanel
          nodeId={flowState.selectedNodeId}
          nodes={nodes}
          onUpdateNode={(nodeId, updates) => {
            setNodes(nds => nds.map(node => 
              node.id === nodeId ? { ...node, data: { ...node.data, ...updates } } : node
            ))
            saveToHistory()
          }}
          onClose={() => setShowPropertiesPanel(false)}
        />
      )}

      {showSettingsPanel && (
        <WorkflowSettingsPanel
          onClose={() => setShowSettingsPanel(false)}
        />
      )}

      {showTestingPanel && (
        <TestingPanel
          workflow={convertToWorkflowDefinition()}
          onExecute={executeWorkflow}
          onClose={() => setShowTestingPanel(false)}
        />
      )}

      {showTemplateGallery && (
        <TemplateGallery
          onSelectTemplate={loadWorkflowFromDefinition}
          onClose={() => setShowTemplateGallery(false)}
        />
      )}
    </div>
  )
}

export default function VisualFlowDesignerWrapper(props: VisualFlowDesignerProps) {
  return (
    <ReactFlowProvider>
      <VisualFlowDesigner {...props} />
    </ReactFlowProvider>
  )
}
