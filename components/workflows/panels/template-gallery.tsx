'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { 
  Search, 
  Star, 
  Download, 
  Eye, 
  Filter,
  ShoppingCart,
  Mail,
  Users,
  BarChart3,
  Zap,
  Clock,
  Database
} from 'lucide-react'

interface TemplateGalleryProps {
  onSelectTemplate: (template: WorkflowTemplate) => void
  onClose: () => void
}

interface WorkflowTemplate {
  id: string
  name: string
  description: string
  category: string
  tags: string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedTime: string
  rating: number
  downloads: number
  preview: string
  nodes: any[]
  edges: any[]
  isPopular?: boolean
  isFeatured?: boolean
}

const templates: WorkflowTemplate[] = [
  {
    id: 'order-confirmation',
    name: 'Order Confirmation Email',
    description: 'Automatically send confirmation emails when new orders are placed',
    category: 'ecommerce',
    tags: ['email', 'orders', 'automation'],
    difficulty: 'beginner',
    estimatedTime: '5 min',
    rating: 4.8,
    downloads: 1250,
    preview: 'Order Created → Send Email → Update Status',
    isPopular: true,
    nodes: [],
    edges: []
  },
  {
    id: 'abandoned-cart',
    name: 'Abandoned Cart Recovery',
    description: 'Send reminder emails to customers who left items in their cart',
    category: 'ecommerce',
    tags: ['email', 'cart', 'recovery', 'marketing'],
    difficulty: 'intermediate',
    estimatedTime: '15 min',
    rating: 4.6,
    downloads: 890,
    preview: 'Cart Abandoned → Wait 1 Hour → Send Reminder → Wait 24 Hours → Send Discount',
    isFeatured: true,
    nodes: [],
    edges: []
  },
  {
    id: 'inventory-alert',
    name: 'Low Inventory Alert',
    description: 'Notify staff when product inventory falls below threshold',
    category: 'inventory',
    tags: ['inventory', 'alerts', 'notifications'],
    difficulty: 'beginner',
    estimatedTime: '10 min',
    rating: 4.7,
    downloads: 650,
    preview: 'Inventory Check → If Low → Send Alert → Update Dashboard',
    nodes: [],
    edges: []
  },
  {
    id: 'customer-onboarding',
    name: 'Customer Onboarding Sequence',
    description: 'Welcome new customers with a series of helpful emails',
    category: 'marketing',
    tags: ['onboarding', 'email', 'sequence', 'customers'],
    difficulty: 'intermediate',
    estimatedTime: '20 min',
    rating: 4.9,
    downloads: 1100,
    preview: 'User Registered → Welcome Email → Wait 3 Days → Tips Email → Wait 7 Days → Survey',
    isPopular: true,
    nodes: [],
    edges: []
  },
  {
    id: 'order-fulfillment',
    name: 'Order Fulfillment Process',
    description: 'Complete order processing from payment to shipping',
    category: 'ecommerce',
    tags: ['orders', 'fulfillment', 'shipping', 'automation'],
    difficulty: 'advanced',
    estimatedTime: '30 min',
    rating: 4.5,
    downloads: 420,
    preview: 'Payment Confirmed → Check Inventory → Create Shipment → Send Tracking',
    nodes: [],
    edges: []
  },
  {
    id: 'review-request',
    name: 'Product Review Request',
    description: 'Request reviews from customers after successful delivery',
    category: 'marketing',
    tags: ['reviews', 'email', 'feedback'],
    difficulty: 'beginner',
    estimatedTime: '8 min',
    rating: 4.4,
    downloads: 780,
    preview: 'Order Delivered → Wait 3 Days → Send Review Request',
    nodes: [],
    edges: []
  }
]

export function TemplateGallery({ onSelectTemplate, onClose }: TemplateGalleryProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedDifficulty, setSelectedDifficulty] = useState('all')

  const categories = [
    { id: 'all', name: 'All Categories', icon: Filter },
    { id: 'ecommerce', name: 'E-commerce', icon: ShoppingCart },
    { id: 'marketing', name: 'Marketing', icon: Mail },
    { id: 'inventory', name: 'Inventory', icon: Database },
    { id: 'customer-service', name: 'Customer Service', icon: Users },
    { id: 'analytics', name: 'Analytics', icon: BarChart3 }
  ]

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory
    const matchesDifficulty = selectedDifficulty === 'all' || template.difficulty === selectedDifficulty

    return matchesSearch && matchesCategory && matchesDifficulty
  })

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'bg-green-100 text-green-800'
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800'
      case 'advanced':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getCategoryIcon = (category: string) => {
    const categoryData = categories.find(cat => cat.id === category)
    if (categoryData) {
      const Icon = categoryData.icon
      return <Icon className="h-4 w-4" />
    }
    return <Zap className="h-4 w-4" />
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-3 w-3 ${
          i < Math.floor(rating) 
            ? 'fill-yellow-400 text-yellow-400' 
            : 'text-gray-300'
        }`}
      />
    ))
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-6xl h-[80vh] m-4">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl">Workflow Templates</CardTitle>
              <CardDescription>
                Choose from pre-built workflows to get started quickly
              </CardDescription>
            </div>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
          
          <div className="flex gap-4 mt-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search templates..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
            
            <select
              value={selectedDifficulty}
              onChange={(e) => setSelectedDifficulty(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">All Levels</option>
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
            </select>
          </div>
        </CardHeader>
        
        <CardContent className="overflow-y-auto">
          <Tabs defaultValue="featured" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="featured">Featured</TabsTrigger>
              <TabsTrigger value="popular">Popular</TabsTrigger>
              <TabsTrigger value="all">All Templates</TabsTrigger>
            </TabsList>
            
            <TabsContent value="featured" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredTemplates
                  .filter(template => template.isFeatured)
                  .map(template => (
                    <TemplateCard
                      key={template.id}
                      template={template}
                      onSelect={onSelectTemplate}
                      getCategoryIcon={getCategoryIcon}
                      getDifficultyColor={getDifficultyColor}
                      renderStars={renderStars}
                    />
                  ))}
              </div>
            </TabsContent>
            
            <TabsContent value="popular" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredTemplates
                  .filter(template => template.isPopular)
                  .map(template => (
                    <TemplateCard
                      key={template.id}
                      template={template}
                      onSelect={onSelectTemplate}
                      getCategoryIcon={getCategoryIcon}
                      getDifficultyColor={getDifficultyColor}
                      renderStars={renderStars}
                    />
                  ))}
              </div>
            </TabsContent>
            
            <TabsContent value="all" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredTemplates.map(template => (
                  <TemplateCard
                    key={template.id}
                    template={template}
                    onSelect={onSelectTemplate}
                    getCategoryIcon={getCategoryIcon}
                    getDifficultyColor={getDifficultyColor}
                    renderStars={renderStars}
                  />
                ))}
              </div>
            </TabsContent>
          </Tabs>
          
          {filteredTemplates.length === 0 && (
            <div className="text-center py-12">
              <div className="text-muted-foreground">
                No templates found matching your criteria
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

interface TemplateCardProps {
  template: WorkflowTemplate
  onSelect: (template: WorkflowTemplate) => void
  getCategoryIcon: (category: string) => React.ReactNode
  getDifficultyColor: (difficulty: string) => string
  renderStars: (rating: number) => React.ReactNode[]
}

function TemplateCard({ 
  template, 
  onSelect, 
  getCategoryIcon, 
  getDifficultyColor, 
  renderStars 
}: TemplateCardProps) {
  return (
    <Card className="hover:shadow-lg transition-shadow cursor-pointer">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            {getCategoryIcon(template.category)}
            <div>
              <h3 className="font-semibold text-sm">{template.name}</h3>
              <div className="flex items-center gap-2 mt-1">
                <Badge className={getDifficultyColor(template.difficulty)}>
                  {template.difficulty}
                </Badge>
                <span className="text-xs text-muted-foreground flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {template.estimatedTime}
                </span>
              </div>
            </div>
          </div>
          {(template.isFeatured || template.isPopular) && (
            <Badge variant="secondary">
              {template.isFeatured ? 'Featured' : 'Popular'}
            </Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <p className="text-sm text-muted-foreground mb-3">
          {template.description}
        </p>
        
        <div className="text-xs text-muted-foreground mb-3 font-mono bg-muted p-2 rounded">
          {template.preview}
        </div>
        
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-1">
            {renderStars(template.rating)}
            <span className="text-xs text-muted-foreground ml-1">
              ({template.rating})
            </span>
          </div>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Download className="h-3 w-3" />
            {template.downloads}
          </div>
        </div>
        
        <div className="flex gap-1 flex-wrap mb-3">
          {template.tags.slice(0, 3).map(tag => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {template.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{template.tags.length - 3}
            </Badge>
          )}
        </div>
        
        <div className="flex gap-2">
          <Button
            size="sm"
            onClick={() => onSelect(template)}
            className="flex-1"
          >
            Use Template
          </Button>
          <Button
            size="sm"
            variant="outline"
            className="flex items-center gap-1"
          >
            <Eye className="h-3 w-3" />
            Preview
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
