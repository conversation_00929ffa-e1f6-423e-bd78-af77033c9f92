'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'

import { 
  Settings, 
  Code, 
  Database, 
  Mail, 
  Clock, 
  Repeat, 
  GitBranch,
  Trash2,
  Copy,
  Eye,
  EyeOff
} from 'lucide-react'

interface NodePropertiesPanelProps {
  selectedNode: any
  onUpdateNode: (nodeId: string, updates: any) => void
  onDeleteNode: (nodeId: string) => void
  onDuplicateNode: (nodeId: string) => void
}

export function NodePropertiesPanel({
  selectedNode,
  onUpdateNode,
  onDeleteNode,
  onDuplicateNode
}: NodePropertiesPanelProps) {
  const [activeTab, setActiveTab] = useState('general')

  if (!selectedNode) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Node Properties
          </CardTitle>
          <CardDescription>
            Select a node to view and edit its properties
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-64 text-muted-foreground">
          No node selected
        </CardContent>
      </Card>
    )
  }

  const handlePropertyChange = (property: string, value: any) => {
    onUpdateNode(selectedNode.id, {
      data: {
        ...selectedNode.data,
        [property]: value
      }
    })
  }

  const handleConfigChange = (configKey: string, value: any) => {
    onUpdateNode(selectedNode.id, {
      data: {
        ...selectedNode.data,
        config: {
          ...selectedNode.data.config,
          [configKey]: value
        }
      }
    })
  }

  const getNodeIcon = (type: string) => {
    switch (type) {
      case 'action':
        return <Code className="h-4 w-4" />
      case 'condition':
        return <GitBranch className="h-4 w-4" />
      case 'delay':
        return <Clock className="h-4 w-4" />
      case 'loop':
        return <Repeat className="h-4 w-4" />
      case 'integration':
        return <Database className="h-4 w-4" />
      default:
        return <Settings className="h-4 w-4" />
    }
  }

  const renderGeneralTab = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="node-label">Node Label</Label>
        <Input
          id="node-label"
          value={selectedNode.data.label || ''}
          onChange={(e) => handlePropertyChange('label', e.target.value)}
          placeholder="Enter node label"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="node-description">Description</Label>
        <Textarea
          id="node-description"
          value={selectedNode.data.description || ''}
          onChange={(e) => handlePropertyChange('description', e.target.value)}
          placeholder="Enter node description"
          rows={3}
        />
      </div>

      <div className="flex items-center justify-between">
        <Label htmlFor="node-enabled">Enabled</Label>
        <Switch
          id="node-enabled"
          checked={selectedNode.data.enabled !== false}
          onCheckedChange={(checked) => handlePropertyChange('enabled', checked)}
        />
      </div>

      <Separator />

      <div className="space-y-2">
        <Label>Node Actions</Label>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onDuplicateNode(selectedNode.id)}
            className="flex items-center gap-2"
          >
            <Copy className="h-4 w-4" />
            Duplicate
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => onDeleteNode(selectedNode.id)}
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>
    </div>
  )

  const renderConfigTab = () => {
    const config = selectedNode.data.config || {}

    switch (selectedNode.type) {
      case 'action':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="action-type">Action Type</Label>
              <Select
                value={config.actionType || ''}
                onValueChange={(value) => handleConfigChange('actionType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select action type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="email">Send Email</SelectItem>
                  <SelectItem value="webhook">Call Webhook</SelectItem>
                  <SelectItem value="database">Database Operation</SelectItem>
                  <SelectItem value="notification">Send Notification</SelectItem>
                  <SelectItem value="custom">Custom Action</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {config.actionType === 'email' && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="email-template">Email Template</Label>
                  <Input
                    id="email-template"
                    value={config.emailTemplate || ''}
                    onChange={(e) => handleConfigChange('emailTemplate', e.target.value)}
                    placeholder="Template ID or name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email-recipients">Recipients</Label>
                  <Textarea
                    id="email-recipients"
                    value={config.recipients || ''}
                    onChange={(e) => handleConfigChange('recipients', e.target.value)}
                    placeholder="Email addresses (comma separated)"
                    rows={2}
                  />
                </div>
              </>
            )}

            {config.actionType === 'webhook' && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="webhook-url">Webhook URL</Label>
                  <Input
                    id="webhook-url"
                    value={config.webhookUrl || ''}
                    onChange={(e) => handleConfigChange('webhookUrl', e.target.value)}
                    placeholder="https://api.example.com/webhook"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="webhook-method">HTTP Method</Label>
                  <Select
                    value={config.method || 'POST'}
                    onValueChange={(value) => handleConfigChange('method', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GET">GET</SelectItem>
                      <SelectItem value="POST">POST</SelectItem>
                      <SelectItem value="PUT">PUT</SelectItem>
                      <SelectItem value="PATCH">PATCH</SelectItem>
                      <SelectItem value="DELETE">DELETE</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}
          </div>
        )

      case 'condition':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="condition-field">Field to Check</Label>
              <Input
                id="condition-field"
                value={config.field || ''}
                onChange={(e) => handleConfigChange('field', e.target.value)}
                placeholder="e.g., user.email, order.total"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="condition-operator">Operator</Label>
              <Select
                value={config.operator || ''}
                onValueChange={(value) => handleConfigChange('operator', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select operator" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="equals">Equals</SelectItem>
                  <SelectItem value="not_equals">Not Equals</SelectItem>
                  <SelectItem value="greater_than">Greater Than</SelectItem>
                  <SelectItem value="less_than">Less Than</SelectItem>
                  <SelectItem value="contains">Contains</SelectItem>
                  <SelectItem value="starts_with">Starts With</SelectItem>
                  <SelectItem value="ends_with">Ends With</SelectItem>
                  <SelectItem value="is_empty">Is Empty</SelectItem>
                  <SelectItem value="is_not_empty">Is Not Empty</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="condition-value">Value</Label>
              <Input
                id="condition-value"
                value={config.value || ''}
                onChange={(e) => handleConfigChange('value', e.target.value)}
                placeholder="Comparison value"
              />
            </div>
          </div>
        )

      case 'delay':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="delay-duration">Delay Duration</Label>
              <Input
                id="delay-duration"
                type="number"
                value={config.duration || ''}
                onChange={(e) => handleConfigChange('duration', parseInt(e.target.value))}
                placeholder="Duration in seconds"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="delay-unit">Time Unit</Label>
              <Select
                value={config.unit || 'seconds'}
                onValueChange={(value) => handleConfigChange('unit', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="seconds">Seconds</SelectItem>
                  <SelectItem value="minutes">Minutes</SelectItem>
                  <SelectItem value="hours">Hours</SelectItem>
                  <SelectItem value="days">Days</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )

      case 'loop':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="loop-type">Loop Type</Label>
              <Select
                value={config.loopType || ''}
                onValueChange={(value) => handleConfigChange('loopType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select loop type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="for_each">For Each</SelectItem>
                  <SelectItem value="while">While</SelectItem>
                  <SelectItem value="count">Count</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {config.loopType === 'for_each' && (
              <div className="space-y-2">
                <Label htmlFor="loop-array">Array Field</Label>
                <Input
                  id="loop-array"
                  value={config.arrayField || ''}
                  onChange={(e) => handleConfigChange('arrayField', e.target.value)}
                  placeholder="e.g., order.items, users"
                />
              </div>
            )}

            {config.loopType === 'count' && (
              <div className="space-y-2">
                <Label htmlFor="loop-count">Iteration Count</Label>
                <Input
                  id="loop-count"
                  type="number"
                  value={config.count || ''}
                  onChange={(e) => handleConfigChange('count', parseInt(e.target.value))}
                  placeholder="Number of iterations"
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="loop-max">Max Iterations</Label>
              <Input
                id="loop-max"
                type="number"
                value={config.maxIterations || ''}
                onChange={(e) => handleConfigChange('maxIterations', parseInt(e.target.value))}
                placeholder="Safety limit"
              />
            </div>
          </div>
        )

      default:
        return (
          <div className="text-center text-muted-foreground py-8">
            No configuration options available for this node type
          </div>
        )
    }
  }

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          {getNodeIcon(selectedNode.type)}
          {selectedNode.data.label || selectedNode.type}
        </CardTitle>
        <div className="flex items-center gap-2">
          <Badge variant="secondary">{selectedNode.type}</Badge>
          <Badge variant={selectedNode.data.enabled !== false ? 'default' : 'destructive'}>
            {selectedNode.data.enabled !== false ? 'Enabled' : 'Disabled'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <TabsList className="grid w-full grid-cols-2 mx-6">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="config">Configuration</TabsTrigger>
          </TabsList>
          <div className="max-h-[calc(100vh-300px)] overflow-y-auto">
            <div className="p-6">
              <TabsContent value="general" className="mt-0">
                {renderGeneralTab()}
              </TabsContent>
              <TabsContent value="config" className="mt-0">
                {renderConfigTab()}
              </TabsContent>
            </div>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  )
}
