'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { 
  Play, 
  Square, 
  RotateCcw,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  FileText,
  Download,
  Trash2
} from 'lucide-react'

interface TestingPanelProps {
  workflow: any
  onRunTest: (testData: any) => Promise<any>
  onStopTest: () => void
  onClearResults: () => void
}

interface TestExecution {
  id: string
  status: 'running' | 'completed' | 'failed' | 'stopped'
  startTime: Date
  endTime?: Date
  duration?: number
  results: any[]
  errors: any[]
}

export function TestingPanel({
  workflow,
  onRunTest,
  onStopTest,
  onClearResults
}: TestingPanelProps) {
  const [activeTab, setActiveTab] = useState('input')
  const [testData, setTestData] = useState('{}')
  const [isRunning, setIsRunning] = useState(false)
  const [executions, setExecutions] = useState<TestExecution[]>([])
  const [selectedExecution, setSelectedExecution] = useState<string | null>(null)

  const handleRunTest = async () => {
    try {
      setIsRunning(true)
      const parsedData = JSON.parse(testData)
      
      const execution: TestExecution = {
        id: Date.now().toString(),
        status: 'running',
        startTime: new Date(),
        results: [],
        errors: []
      }
      
      setExecutions(prev => [execution, ...prev])
      setSelectedExecution(execution.id)
      
      const result = await onRunTest(parsedData)
      
      // Update execution with results
      setExecutions(prev => prev.map(exec => 
        exec.id === execution.id 
          ? {
              ...exec,
              status: result.success ? 'completed' : 'failed',
              endTime: new Date(),
              duration: Date.now() - execution.startTime.getTime(),
              results: result.results || [],
              errors: result.errors || []
            }
          : exec
      ))
    } catch (error) {
      console.error('Test execution error:', error)
      // Update execution with error
      setExecutions(prev => prev.map(exec => 
        exec.id === selectedExecution 
          ? {
              ...exec,
              status: 'failed',
              endTime: new Date(),
              duration: Date.now() - exec.startTime.getTime(),
              errors: [{ message: error instanceof Error ? error.message : 'Unknown error' }]
            }
          : exec
      ))
    } finally {
      setIsRunning(false)
    }
  }

  const handleStopTest = () => {
    setIsRunning(false)
    onStopTest()
    
    if (selectedExecution) {
      setExecutions(prev => prev.map(exec => 
        exec.id === selectedExecution 
          ? {
              ...exec,
              status: 'stopped',
              endTime: new Date(),
              duration: Date.now() - exec.startTime.getTime()
            }
          : exec
      ))
    }
  }

  const handleClearResults = () => {
    setExecutions([])
    setSelectedExecution(null)
    onClearResults()
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-blue-100 text-blue-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      case 'stopped':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Clock className="h-4 w-4 animate-spin" />
      case 'completed':
        return <CheckCircle className="h-4 w-4" />
      case 'failed':
        return <XCircle className="h-4 w-4" />
      case 'stopped':
        return <Square className="h-4 w-4" />
      default:
        return <AlertTriangle className="h-4 w-4" />
    }
  }

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
    return `${(ms / 60000).toFixed(1)}m`
  }

  const renderInputTab = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="test-data">Test Input Data (JSON)</Label>
        <Textarea
          id="test-data"
          value={testData}
          onChange={(e) => setTestData(e.target.value)}
          placeholder='{"user": {"id": 1, "email": "<EMAIL>"}, "order": {"total": 100}}'
          rows={8}
          className="font-mono text-sm"
        />
      </div>

      <div className="space-y-2">
        <Label>Quick Templates</Label>
        <div className="flex gap-2 flex-wrap">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setTestData('{"user": {"id": 1, "email": "<EMAIL>"}}')}
          >
            User Data
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setTestData('{"order": {"id": 1, "total": 100, "status": "pending"}}')}
          >
            Order Data
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setTestData('{"product": {"id": 1, "name": "Test Product", "price": 50}}')}
          >
            Product Data
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setTestData('{}')}
          >
            Empty
          </Button>
        </div>
      </div>

      <Separator />

      <div className="space-y-2">
        <Label>Test Actions</Label>
        <div className="flex gap-2">
          {!isRunning ? (
            <Button
              onClick={handleRunTest}
              className="flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              Run Test
            </Button>
          ) : (
            <Button
              onClick={handleStopTest}
              variant="destructive"
              className="flex items-center gap-2"
            >
              <Square className="h-4 w-4" />
              Stop Test
            </Button>
          )}
          
          <Button
            onClick={handleClearResults}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Clear Results
          </Button>
        </div>
      </div>
    </div>
  )

  const renderResultsTab = () => {
    const currentExecution = executions.find(exec => exec.id === selectedExecution)

    return (
      <div className="space-y-4">
        <div className="space-y-2">
          <Label>Test Executions</Label>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {executions.length === 0 ? (
              <div className="text-center text-muted-foreground py-4">
                No test executions yet
              </div>
            ) : (
              executions.map((execution) => (
                <div
                  key={execution.id}
                  className={`p-3 border rounded cursor-pointer transition-colors ${
                    selectedExecution === execution.id 
                      ? 'border-primary bg-primary/5' 
                      : 'border-border hover:bg-muted/50'
                  }`}
                  onClick={() => setSelectedExecution(execution.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(execution.status)}>
                        {getStatusIcon(execution.status)}
                        <span className="ml-1">{execution.status}</span>
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {execution.startTime.toLocaleTimeString()}
                      </span>
                    </div>
                    {execution.duration && (
                      <span className="text-sm text-muted-foreground">
                        {formatDuration(execution.duration)}
                      </span>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {currentExecution && (
          <>
            <Separator />
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Execution Details</h4>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                  onClick={() => {
                    const data = JSON.stringify(currentExecution, null, 2)
                    const blob = new Blob([data], { type: 'application/json' })
                    const url = URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.href = url
                    a.download = `test-execution-${currentExecution.id}.json`
                    a.click()
                    URL.revokeObjectURL(url)
                  }}
                >
                  <Download className="h-4 w-4" />
                  Export
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label className="text-xs text-muted-foreground">Status</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge className={getStatusColor(currentExecution.status)}>
                      {getStatusIcon(currentExecution.status)}
                      <span className="ml-1">{currentExecution.status}</span>
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Duration</Label>
                  <div className="mt-1">
                    {currentExecution.duration ? formatDuration(currentExecution.duration) : 'N/A'}
                  </div>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Start Time</Label>
                  <div className="mt-1">{currentExecution.startTime.toLocaleString()}</div>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">End Time</Label>
                  <div className="mt-1">
                    {currentExecution.endTime ? currentExecution.endTime.toLocaleString() : 'N/A'}
                  </div>
                </div>
              </div>

              {currentExecution.results.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Results</Label>
                  <div className="bg-muted p-3 rounded text-sm font-mono max-h-40 overflow-y-auto">
                    <pre>{JSON.stringify(currentExecution.results, null, 2)}</pre>
                  </div>
                </div>
              )}

              {currentExecution.errors.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-red-600">Errors</Label>
                  <div className="bg-red-50 border border-red-200 p-3 rounded text-sm max-h-40 overflow-y-auto">
                    {currentExecution.errors.map((error, index) => (
                      <div key={index} className="text-red-800">
                        {error.message || JSON.stringify(error)}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    )
  }

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Workflow Testing
        </CardTitle>
        <CardDescription>
          Test your workflow with sample data and view execution results
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <TabsList className="grid w-full grid-cols-2 mx-6">
            <TabsTrigger value="input">Test Input</TabsTrigger>
            <TabsTrigger value="results">Results</TabsTrigger>
          </TabsList>
          <div className="max-h-[calc(100vh-300px)] overflow-y-auto">
            <div className="p-6">
              <TabsContent value="input" className="mt-0">
                {renderInputTab()}
              </TabsContent>
              <TabsContent value="results" className="mt-0">
                {renderResultsTab()}
              </TabsContent>
            </div>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  )
}
