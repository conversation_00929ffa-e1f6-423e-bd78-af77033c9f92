'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { 
  Settings, 
  Save, 
  Play, 
  Pause, 
  RotateCcw,
  Clock,
  Users,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface WorkflowSettingsPanelProps {
  workflow: any
  onUpdateWorkflow: (updates: any) => void
  onSaveWorkflow: () => void
  onRunWorkflow: () => void
  onPauseWorkflow: () => void
  onResetWorkflow: () => void
}

export function WorkflowSettingsPanel({
  workflow,
  onUpdateWorkflow,
  onSaveWorkflow,
  onRunWorkflow,
  onPauseWorkflow,
  onResetWorkflow
}: WorkflowSettingsPanelProps) {
  const [activeTab, setActiveTab] = useState('general')
  const [isSaving, setIsSaving] = useState(false)

  const handleSave = async () => {
    setIsSaving(true)
    try {
      await onSaveWorkflow()
    } finally {
      setIsSaving(false)
    }
  }

  const handlePropertyChange = (property: string, value: any) => {
    onUpdateWorkflow({
      [property]: value
    })
  }

  const handleSettingsChange = (settingKey: string, value: any) => {
    onUpdateWorkflow({
      settings: {
        ...workflow.settings,
        [settingKey]: value
      }
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'paused':
        return 'bg-yellow-100 text-yellow-800'
      case 'error':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4" />
      case 'paused':
        return <Pause className="h-4 w-4" />
      case 'error':
        return <XCircle className="h-4 w-4" />
      default:
        return <AlertTriangle className="h-4 w-4" />
    }
  }

  const renderGeneralTab = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="workflow-name">Workflow Name</Label>
        <Input
          id="workflow-name"
          value={workflow.name || ''}
          onChange={(e) => handlePropertyChange('name', e.target.value)}
          placeholder="Enter workflow name"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="workflow-description">Description</Label>
        <Textarea
          id="workflow-description"
          value={workflow.description || ''}
          onChange={(e) => handlePropertyChange('description', e.target.value)}
          placeholder="Enter workflow description"
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="workflow-category">Category</Label>
        <Select
          value={workflow.category || ''}
          onValueChange={(value) => handlePropertyChange('category', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ecommerce">E-commerce</SelectItem>
            <SelectItem value="marketing">Marketing</SelectItem>
            <SelectItem value="customer-service">Customer Service</SelectItem>
            <SelectItem value="inventory">Inventory</SelectItem>
            <SelectItem value="analytics">Analytics</SelectItem>
            <SelectItem value="automation">Automation</SelectItem>
            <SelectItem value="integration">Integration</SelectItem>
            <SelectItem value="custom">Custom</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center justify-between">
        <Label htmlFor="workflow-enabled">Enabled</Label>
        <Switch
          id="workflow-enabled"
          checked={workflow.enabled !== false}
          onCheckedChange={(checked) => handlePropertyChange('enabled', checked)}
        />
      </div>

      <Separator />

      <div className="space-y-2">
        <Label>Current Status</Label>
        <div className="flex items-center gap-2">
          <Badge className={getStatusColor(workflow.status || 'draft')}>
            {getStatusIcon(workflow.status || 'draft')}
            <span className="ml-1">{workflow.status || 'Draft'}</span>
          </Badge>
        </div>
      </div>

      <div className="space-y-2">
        <Label>Workflow Actions</Label>
        <div className="flex gap-2 flex-wrap">
          <Button
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center gap-2"
            size="sm"
          >
            <Save className="h-4 w-4" />
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
          
          {workflow.status !== 'active' ? (
            <Button
              onClick={onRunWorkflow}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              Run
            </Button>
          ) : (
            <Button
              onClick={onPauseWorkflow}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Pause className="h-4 w-4" />
              Pause
            </Button>
          )}
          
          <Button
            onClick={onResetWorkflow}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Reset
          </Button>
        </div>
      </div>
    </div>
  )

  const renderSettingsTab = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="max-concurrent">Max Concurrent Executions</Label>
        <Input
          id="max-concurrent"
          type="number"
          value={workflow.settings?.maxConcurrentExecutions || ''}
          onChange={(e) => handleSettingsChange('maxConcurrentExecutions', parseInt(e.target.value))}
          placeholder="1"
          min="1"
          max="100"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="timeout">Execution Timeout (minutes)</Label>
        <Input
          id="timeout"
          type="number"
          value={workflow.settings?.timeoutMinutes || ''}
          onChange={(e) => handleSettingsChange('timeoutMinutes', parseInt(e.target.value))}
          placeholder="30"
          min="1"
          max="1440"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="retry-attempts">Retry Attempts</Label>
        <Input
          id="retry-attempts"
          type="number"
          value={workflow.settings?.retryAttempts || ''}
          onChange={(e) => handleSettingsChange('retryAttempts', parseInt(e.target.value))}
          placeholder="3"
          min="0"
          max="10"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="retry-delay">Retry Delay (seconds)</Label>
        <Input
          id="retry-delay"
          type="number"
          value={workflow.settings?.retryDelaySeconds || ''}
          onChange={(e) => handleSettingsChange('retryDelaySeconds', parseInt(e.target.value))}
          placeholder="60"
          min="1"
          max="3600"
        />
      </div>

      <div className="flex items-center justify-between">
        <Label htmlFor="error-notifications">Error Notifications</Label>
        <Switch
          id="error-notifications"
          checked={workflow.settings?.errorNotifications !== false}
          onCheckedChange={(checked) => handleSettingsChange('errorNotifications', checked)}
        />
      </div>

      <div className="flex items-center justify-between">
        <Label htmlFor="logging-enabled">Enable Logging</Label>
        <Switch
          id="logging-enabled"
          checked={workflow.settings?.loggingEnabled !== false}
          onCheckedChange={(checked) => handleSettingsChange('loggingEnabled', checked)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="log-level">Log Level</Label>
        <Select
          value={workflow.settings?.logLevel || 'info'}
          onValueChange={(value) => handleSettingsChange('logLevel', value)}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="debug">Debug</SelectItem>
            <SelectItem value="info">Info</SelectItem>
            <SelectItem value="warn">Warning</SelectItem>
            <SelectItem value="error">Error</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )

  const renderTriggersTab = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="trigger-type">Trigger Type</Label>
        <Select
          value={workflow.trigger?.type || ''}
          onValueChange={(value) => handlePropertyChange('trigger', { ...workflow.trigger, type: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select trigger type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="manual">Manual</SelectItem>
            <SelectItem value="schedule">Schedule</SelectItem>
            <SelectItem value="webhook">Webhook</SelectItem>
            <SelectItem value="event">Event</SelectItem>
            <SelectItem value="api">API Call</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {workflow.trigger?.type === 'schedule' && (
        <>
          <div className="space-y-2">
            <Label htmlFor="schedule-cron">Cron Expression</Label>
            <Input
              id="schedule-cron"
              value={workflow.trigger?.cron || ''}
              onChange={(e) => handlePropertyChange('trigger', { ...workflow.trigger, cron: e.target.value })}
              placeholder="0 0 * * *"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="schedule-timezone">Timezone</Label>
            <Select
              value={workflow.trigger?.timezone || 'UTC'}
              onValueChange={(value) => handlePropertyChange('trigger', { ...workflow.trigger, timezone: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="UTC">UTC</SelectItem>
                <SelectItem value="America/New_York">Eastern Time</SelectItem>
                <SelectItem value="America/Chicago">Central Time</SelectItem>
                <SelectItem value="America/Denver">Mountain Time</SelectItem>
                <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                <SelectItem value="Europe/London">London</SelectItem>
                <SelectItem value="Africa/Johannesburg">Johannesburg</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </>
      )}

      {workflow.trigger?.type === 'webhook' && (
        <div className="space-y-2">
          <Label htmlFor="webhook-url">Webhook URL</Label>
          <Input
            id="webhook-url"
            value={workflow.trigger?.webhookUrl || ''}
            onChange={(e) => handlePropertyChange('trigger', { ...workflow.trigger, webhookUrl: e.target.value })}
            placeholder="Generated automatically"
            readOnly
          />
        </div>
      )}

      {workflow.trigger?.type === 'event' && (
        <div className="space-y-2">
          <Label htmlFor="event-name">Event Name</Label>
          <Select
            value={workflow.trigger?.eventName || ''}
            onValueChange={(value) => handlePropertyChange('trigger', { ...workflow.trigger, eventName: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select event" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="order.created">Order Created</SelectItem>
              <SelectItem value="order.updated">Order Updated</SelectItem>
              <SelectItem value="order.cancelled">Order Cancelled</SelectItem>
              <SelectItem value="user.registered">User Registered</SelectItem>
              <SelectItem value="user.login">User Login</SelectItem>
              <SelectItem value="product.created">Product Created</SelectItem>
              <SelectItem value="inventory.low">Low Inventory</SelectItem>
              <SelectItem value="payment.completed">Payment Completed</SelectItem>
              <SelectItem value="payment.failed">Payment Failed</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}
    </div>
  )

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Workflow Settings
        </CardTitle>
        <CardDescription>
          Configure workflow properties, triggers, and execution settings
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <TabsList className="grid w-full grid-cols-3 mx-6">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="triggers">Triggers</TabsTrigger>
          </TabsList>
          <div className="max-h-[calc(100vh-300px)] overflow-y-auto">
            <div className="p-6">
              <TabsContent value="general" className="mt-0">
                {renderGeneralTab()}
              </TabsContent>
              <TabsContent value="settings" className="mt-0">
                {renderSettingsTab()}
              </TabsContent>
              <TabsContent value="triggers" className="mt-0">
                {renderTriggersTab()}
              </TabsContent>
            </div>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  )
}
