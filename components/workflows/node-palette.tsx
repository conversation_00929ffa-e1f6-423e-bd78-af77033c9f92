'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import {
  Zap,
  Calendar,
  MousePointer,
  Webhook,
  Mail,
  MessageSquare,
  Database,
  Globe,
  FileText,
  Settings,
  GitBranch,
  Clock,
  Plug,
  Users,
  Merge,
  RotateCcw,
  Search,
  Filter,
  Grid,
  List
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface NodeType {
  id: string
  name: string
  description: string
  icon: React.ComponentType<any>
  category: string
  color: string
  tags: string[]
  isPopular?: boolean
  isNew?: boolean
}

const nodeTypes: NodeType[] = [
  // Trigger Nodes
  {
    id: 'trigger',
    name: 'Event Trigger',
    description: 'Start workflow on events like order created, customer registered',
    icon: Zap,
    category: 'triggers',
    color: 'bg-blue-500',
    tags: ['trigger', 'event', 'start'],
    isPopular: true
  },
  {
    id: 'schedule-trigger',
    name: 'Schedule Trigger',
    description: 'Start workflow on a schedule (cron expression)',
    icon: Calendar,
    category: 'triggers',
    color: 'bg-green-500',
    tags: ['trigger', 'schedule', 'cron', 'time']
  },
  {
    id: 'manual-trigger',
    name: 'Manual Trigger',
    description: 'Start workflow manually by user action',
    icon: MousePointer,
    category: 'triggers',
    color: 'bg-purple-500',
    tags: ['trigger', 'manual', 'user']
  },
  {
    id: 'webhook-trigger',
    name: 'Webhook Trigger',
    description: 'Start workflow from external webhook calls',
    icon: Webhook,
    category: 'triggers',
    color: 'bg-orange-500',
    tags: ['trigger', 'webhook', 'external', 'api']
  },

  // Action Nodes
  {
    id: 'action',
    name: 'Send Email',
    description: 'Send email notifications using templates',
    icon: Mail,
    category: 'actions',
    color: 'bg-blue-500',
    tags: ['action', 'email', 'notification', 'communication'],
    isPopular: true
  },
  {
    id: 'sms-action',
    name: 'Send SMS',
    description: 'Send SMS messages to customers',
    icon: MessageSquare,
    category: 'actions',
    color: 'bg-green-500',
    tags: ['action', 'sms', 'notification', 'mobile']
  },
  {
    id: 'database-action',
    name: 'Database Action',
    description: 'Update, insert, or query database records',
    icon: Database,
    category: 'actions',
    color: 'bg-purple-500',
    tags: ['action', 'database', 'data', 'crud']
  },
  {
    id: 'api-action',
    name: 'API Call',
    description: 'Make HTTP requests to external APIs',
    icon: Globe,
    category: 'actions',
    color: 'bg-orange-500',
    tags: ['action', 'api', 'http', 'external'],
    isPopular: true
  },
  {
    id: 'file-action',
    name: 'File Operation',
    description: 'Create, read, or modify files',
    icon: FileText,
    category: 'actions',
    color: 'bg-yellow-500',
    tags: ['action', 'file', 'document', 'storage']
  },
  {
    id: 'custom-action',
    name: 'Custom Action',
    description: 'Execute custom code or scripts',
    icon: Settings,
    category: 'actions',
    color: 'bg-gray-500',
    tags: ['action', 'custom', 'code', 'script']
  },

  // Logic Nodes
  {
    id: 'condition',
    name: 'Condition',
    description: 'Branch workflow based on conditions (if/then/else)',
    icon: GitBranch,
    category: 'logic',
    color: 'bg-yellow-500',
    tags: ['logic', 'condition', 'branch', 'if'],
    isPopular: true
  },
  {
    id: 'delay',
    name: 'Delay',
    description: 'Wait for a specified time before continuing',
    icon: Clock,
    category: 'logic',
    color: 'bg-orange-500',
    tags: ['logic', 'delay', 'wait', 'time']
  },
  {
    id: 'parallel',
    name: 'Parallel',
    description: 'Execute multiple branches simultaneously',
    icon: Users,
    category: 'logic',
    color: 'bg-indigo-500',
    tags: ['logic', 'parallel', 'concurrent', 'branch'],
    isNew: true
  },
  {
    id: 'merge',
    name: 'Merge',
    description: 'Combine multiple workflow paths into one',
    icon: Merge,
    category: 'logic',
    color: 'bg-teal-500',
    tags: ['logic', 'merge', 'combine', 'join'],
    isNew: true
  },
  {
    id: 'loop',
    name: 'Loop',
    description: 'Repeat actions for collections or conditions',
    icon: RotateCcw,
    category: 'logic',
    color: 'bg-pink-500',
    tags: ['logic', 'loop', 'repeat', 'iteration'],
    isNew: true
  },

  // Integration Nodes
  {
    id: 'integration',
    name: 'Stripe Integration',
    description: 'Connect with Stripe for payments',
    icon: Plug,
    category: 'integrations',
    color: 'bg-purple-500',
    tags: ['integration', 'stripe', 'payment', 'external']
  },
  {
    id: 'shopify-integration',
    name: 'Shopify Integration',
    description: 'Connect with Shopify store',
    icon: Plug,
    category: 'integrations',
    color: 'bg-green-500',
    tags: ['integration', 'shopify', 'ecommerce', 'external']
  },
  {
    id: 'mailchimp-integration',
    name: 'Mailchimp Integration',
    description: 'Connect with Mailchimp for email marketing',
    icon: Plug,
    category: 'integrations',
    color: 'bg-yellow-500',
    tags: ['integration', 'mailchimp', 'email', 'marketing']
  },
  {
    id: 'slack-integration',
    name: 'Slack Integration',
    description: 'Send notifications to Slack channels',
    icon: Plug,
    category: 'integrations',
    color: 'bg-purple-400',
    tags: ['integration', 'slack', 'notification', 'team']
  }
]

const categories = [
  { id: 'all', name: 'All Nodes', icon: Grid },
  { id: 'triggers', name: 'Triggers', icon: Zap },
  { id: 'actions', name: 'Actions', icon: Settings },
  { id: 'logic', name: 'Logic', icon: GitBranch },
  { id: 'integrations', name: 'Integrations', icon: Plug }
]

interface NodePaletteProps {
  onNodeDragStart?: (event: React.DragEvent, nodeType: string) => void
  className?: string
}

export function NodePalette({ onNodeDragStart, className }: NodePaletteProps) {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  const handleDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType)
    event.dataTransfer.effectAllowed = 'move'
    
    if (onNodeDragStart) {
      onNodeDragStart(event, nodeType)
    }
  }

  const filteredNodes = nodeTypes.filter(node => {
    const matchesCategory = selectedCategory === 'all' || node.category === selectedCategory
    const matchesSearch = searchQuery === '' || 
      node.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      node.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      node.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    
    return matchesCategory && matchesSearch
  })

  const popularNodes = nodeTypes.filter(node => node.isPopular)
  const newNodes = nodeTypes.filter(node => node.isNew)

  return (
    <Card className={cn('w-80 h-full flex flex-col', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Node Palette</CardTitle>
          <div className="flex items-center space-x-1">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search nodes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Categories */}
        <div className="flex flex-wrap gap-1">
          {categories.map(category => {
            const Icon = category.icon
            return (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="text-xs"
              >
                <Icon className="h-3 w-3 mr-1" />
                {category.name}
              </Button>
            )
          })}
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-y-auto space-y-4">
        {/* Popular Nodes */}
        {selectedCategory === 'all' && searchQuery === '' && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-2">Popular</h3>
            <div className="grid grid-cols-2 gap-2">
              {popularNodes.map(node => {
                const Icon = node.icon
                return (
                  <div
                    key={`popular-${node.id}`}
                    draggable
                    onDragStart={(e) => handleDragStart(e, node.id)}
                    className="p-2 border border-dashed border-gray-300 rounded-lg cursor-grab hover:border-blue-400 hover:bg-blue-50 transition-colors"
                  >
                    <div className="flex items-center space-x-2">
                      <div className={cn('p-1 rounded text-white', node.color)}>
                        <Icon className="h-3 w-3" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="text-xs font-medium truncate">{node.name}</div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* New Nodes */}
        {selectedCategory === 'all' && searchQuery === '' && newNodes.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-2">New</h3>
            <div className="grid grid-cols-2 gap-2">
              {newNodes.map(node => {
                const Icon = node.icon
                return (
                  <div
                    key={`new-${node.id}`}
                    draggable
                    onDragStart={(e) => handleDragStart(e, node.id)}
                    className="p-2 border border-dashed border-gray-300 rounded-lg cursor-grab hover:border-blue-400 hover:bg-blue-50 transition-colors relative"
                  >
                    <Badge className="absolute -top-1 -right-1 text-xs bg-green-500">New</Badge>
                    <div className="flex items-center space-x-2">
                      <div className={cn('p-1 rounded text-white', node.color)}>
                        <Icon className="h-3 w-3" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="text-xs font-medium truncate">{node.name}</div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* All Nodes */}
        <div>
          <h3 className="text-sm font-medium text-muted-foreground mb-2">
            {selectedCategory === 'all' ? 'All Nodes' : categories.find(c => c.id === selectedCategory)?.name}
            <span className="ml-1 text-xs">({filteredNodes.length})</span>
          </h3>
          
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 gap-2">
              {filteredNodes.map(node => {
                const Icon = node.icon
                return (
                  <div
                    key={node.id}
                    draggable
                    onDragStart={(e) => handleDragStart(e, node.id)}
                    className="p-3 border border-dashed border-gray-300 rounded-lg cursor-grab hover:border-blue-400 hover:bg-blue-50 transition-colors relative"
                  >
                    {node.isNew && (
                      <Badge className="absolute top-1 right-1 text-xs bg-green-500">New</Badge>
                    )}
                    {node.isPopular && (
                      <Badge className="absolute top-1 right-1 text-xs bg-blue-500">Popular</Badge>
                    )}
                    
                    <div className="flex items-start space-x-3">
                      <div className={cn('p-2 rounded text-white', node.color)}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="text-sm font-medium">{node.name}</div>
                        <div className="text-xs text-muted-foreground mt-1">{node.description}</div>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {node.tags.slice(0, 2).map(tag => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="space-y-1">
              {filteredNodes.map(node => {
                const Icon = node.icon
                return (
                  <div
                    key={node.id}
                    draggable
                    onDragStart={(e) => handleDragStart(e, node.id)}
                    className="flex items-center space-x-2 p-2 border border-dashed border-gray-300 rounded cursor-grab hover:border-blue-400 hover:bg-blue-50 transition-colors"
                  >
                    <div className={cn('p-1 rounded text-white', node.color)}>
                      <Icon className="h-3 w-3" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="text-xs font-medium truncate">{node.name}</div>
                    </div>
                    {node.isNew && <Badge className="text-xs bg-green-500">New</Badge>}
                    {node.isPopular && <Badge className="text-xs bg-blue-500">Popular</Badge>}
                  </div>
                )
              })}
            </div>
          )}
        </div>

        {filteredNodes.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No nodes found</p>
            <p className="text-xs">Try adjusting your search or category filter</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
