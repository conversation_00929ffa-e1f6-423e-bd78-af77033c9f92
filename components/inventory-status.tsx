"use client"

interface InventoryStatusProps {
  stock: number
  lowStockThreshold?: number
}

export function InventoryStatus({ stock, lowStockThreshold = 5 }: InventoryStatusProps) {
  const getStatusInfo = () => {
    if (stock === 0) {
      return {
        status: "Out of Stock",
        color: "text-red-600",
        bgColor: "bg-red-50",
        borderColor: "border-red-200",
      }
    } else if (stock <= lowStockThreshold) {
      return {
        status: `Only ${stock} left in stock`,
        color: "text-orange-600",
        bgColor: "bg-orange-50",
        borderColor: "border-orange-200",
      }
    } else {
      return {
        status: "In Stock",
        color: "text-green-600",
        bgColor: "bg-green-50",
        borderColor: "border-green-200",
      }
    }
  }

  const { status, color, bgColor, borderColor } = getStatusInfo()

  return (
    <div
      className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium border ${color} ${bgColor} ${borderColor}`}
    >
      <div className={`w-2 h-2 rounded-full mr-2 ${stock > 0 ? "bg-current" : "bg-red-500"}`} />
      {status}
    </div>
  )
}
