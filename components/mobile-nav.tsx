"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Logo } from "@/components/logo"

const MobileNav = () => {
  const pathname = usePathname()

  const navLinks = [
    { href: "/", label: "Home" },
    { href: "/products", label: "Shop" },
    { href: "/collections/summer", label: "Summer Collection" },
    { href: "/collections/heritage-day", label: "Heritage Day" },
    { href: "/collections/sale", label: "Sale" },
    { href: "/collections/best-sellers", label: "Best Sellers" },
    { href: "/about", label: "About" },
    { href: "/contact", label: "Contact" },
  ]

  return (
    <div className="bg-white h-full">
      <div className="flex h-16 items-center px-4 border-b border-gray-100">
        <Logo variant="horizontal" theme="dark" className="h-8 w-auto" />
      </div>

      <nav className="px-4 py-6">
        <div className="space-y-4">
          {navLinks.map((link) => (
            <Link
              key={link.href}
              href={link.href}
              className={cn(
                "block py-2 text-base font-normal transition-colors",
                pathname === link.href ||
                (link.href !== "/" && pathname.startsWith(link.href))
                  ? "text-black font-medium"
                  : "text-gray-700 hover:text-black"
              )}
            >
              {link.label}
            </Link>
          ))}
        </div>

        <div className="mt-8 pt-6 border-t border-gray-100">
          <div className="space-y-4">
            <Link
              href="/account/dashboard"
              className="block py-2 text-base text-gray-700 hover:text-black transition-colors"
            >
              My Account
            </Link>
            <Link
              href="/orders"
              className="block py-2 text-base text-gray-700 hover:text-black transition-colors"
            >
              Track Orders
            </Link>
            <Link
              href="/faq"
              className="block py-2 text-base text-gray-700 hover:text-black transition-colors"
            >
              Help & FAQ
            </Link>
          </div>
        </div>
      </nav>
    </div>
  )
}

export default MobileNav
