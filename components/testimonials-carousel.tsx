"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Star, Quote } from "lucide-react"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Mother of two",
    avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=100&h=100&auto=format&fit=crop",
    content: "The quality of <PERSON><PERSON> <PERSON>'s clothing is exceptional. My kids love how comfortable everything is, and I love how well they hold up through countless washes and playground adventures.",
    rating: 5
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Father of three",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=100&h=100&auto=format&fit=crop",
    content: "Finally found a brand that my kids actually want to wear! The designs are stylish yet practical, and the fabrics are so soft. Worth every penny for the quality you receive.",
    rating: 5
  },
  {
    id: 3,
    name: "<PERSON>",
    role: "Mother of one",
    avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=100&h=100&auto=format&fit=crop",
    content: "I appreciate that Coco Milk creates clothes that let kids be kids. No restrictive designs, just comfortable, durable pieces that look adorable. My daughter's entire wardrobe is now from here!",
    rating: 4
  }
]

export function TestimonialsCarousel() {
  const [current, setCurrent] = useState(0)
  const [direction, setDirection] = useState(0)
  const [testimonials, setTestimonials] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  // Fetch testimonials from API
  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const response = await fetch('/api/e-commerce/testimonials?featured=true&limit=5')
        const data = await response.json()

        if (data.success && data.data.length > 0) {
          setTestimonials(data.data)
        } else {
          // Fallback to default testimonials if API fails or no data
          setTestimonials([
            {
              id: 1,
              name: "Sarah Johnson",
              role: "Mother of two",
              avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=100&h=100&auto=format&fit=crop",
              content: "The quality of Coco Milk's clothing is exceptional. My kids love how comfortable everything is, and I love how well they hold up through countless washes and playground adventures.",
              rating: 5
            },
            {
              id: 2,
              name: "Michael Chen",
              role: "Father of three",
              avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=100&h=100&auto=format&fit=crop",
              content: "Finally found a brand that my kids actually want to wear! The designs are stylish yet practical, and the fabrics are so soft. Worth every penny for the quality you receive.",
              rating: 5
            },
            {
              id: 3,
              name: "Emily Rodriguez",
              role: "Mother of one",
              avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=100&h=100&auto=format&fit=crop",
              content: "I'm impressed with the attention to detail in every piece. The fabrics are so soft and gentle on my daughter's skin, and the colors stay vibrant wash after wash.",
              rating: 5
            }
          ])
        }
      } catch (error) {
        console.error('Error fetching testimonials:', error)
        // Use fallback testimonials
        setTestimonials([
          {
            id: 1,
            name: "Sarah Johnson",
            role: "Mother of two",
            avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=100&h=100&auto=format&fit=crop",
            content: "The quality of Coco Milk's clothing is exceptional. My kids love how comfortable everything is, and I love how well they hold up through countless washes and playground adventures.",
            rating: 5
          }
        ])
      } finally {
        setLoading(false)
      }
    }

    fetchTestimonials()
  }, [])

  const nextTestimonial = useCallback(() => {
    setDirection(1)
    setCurrent((prev) => (prev + 1) % testimonials.length)
  }, [])

  const prevTestimonial = useCallback(() => {
    setDirection(-1)
    setCurrent((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }, [])

  // Auto-advance carousel
  useEffect(() => {
    // Only run on client-side
    if (typeof window === 'undefined') return;

    const timer = setTimeout(() => {
      nextTestimonial()
    }, 6000)

    return () => clearTimeout(timer)
  }, [current, nextTestimonial])

  const variants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 200 : -200,
      opacity: 0
    }),
    center: {
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      x: direction < 0 ? 200 : -200,
      opacity: 0
    })
  }

  if (loading) {
    return (
      <section className="py-20 bg-white">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-montserrat font-light tracking-wide mb-4">What Parents Say</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto font-light">
              Loading testimonials...
            </p>
          </div>
          <div className="max-w-4xl mx-auto">
            <div className="animate-pulse">
              <div className="bg-gray-200 rounded-lg h-64"></div>
            </div>
          </div>
        </div>
      </section>
    )
  }

  if (testimonials.length === 0) {
    return null
  }

  return (
    <section className="py-20 bg-white">
      <div className="container px-4 md:px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-montserrat font-light tracking-wide mb-4">What Parents Say</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto font-light">
            Hear from families who have made Coco Milk Kids a part of their children's wardrobe
          </p>
        </div>

        <div className="relative max-w-4xl mx-auto">
          <div className="absolute -top-10 -left-10 text-[#012169]/10">
            <Quote size={80} />
          </div>

          <AnimatePresence custom={direction} mode="wait" initial={false}>
            <motion.div
              key={current}
              custom={direction}
              variants={variants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{ duration: 0.5 }}
            >
              <Card className="border-none shadow-lg">
                <CardContent className="p-8 md:p-12">
                  <div className="flex flex-col md:flex-row gap-8 items-center">
                    <div className="flex-shrink-0">
                      <div className="relative w-24 h-24 rounded-full overflow-hidden border-4 border-[#012169]/10">
                        <Image
                          src={testimonials[current].avatar}
                          alt={testimonials[current].name}
                          fill
                          className="object-cover"
                        />
                      </div>
                    </div>
                    <div className="flex-1 text-center md:text-left">
                      <div className="flex justify-center md:justify-start mb-2">
                        {[...Array(testimonials[current].rating)].map((_, i) => (
                          <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <p className="text-lg font-light italic mb-4">"{testimonials[current].content}"</p>
                      <div>
                        <h4 className="font-medium">{testimonials[current].name}</h4>
                        <p className="text-sm text-muted-foreground">{testimonials[current].role}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </AnimatePresence>

          <div className="flex justify-center mt-8 space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={prevTestimonial}
              className="rounded-full border-[#012169]/20 hover:bg-[#012169]/5 hover:border-[#012169]/30"
            >
              <ChevronLeft className="h-5 w-5" />
              <span className="sr-only">Previous testimonial</span>
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={nextTestimonial}
              className="rounded-full border-[#012169]/20 hover:bg-[#012169]/5 hover:border-[#012169]/30"
            >
              <ChevronRight className="h-5 w-5" />
              <span className="sr-only">Next testimonial</span>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
