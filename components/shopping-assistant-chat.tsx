"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { useChat, Message, MessagePart } from "@ai-sdk/react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { X, Send, Bot } from "lucide-react"
import { cn } from "@/lib/utils"

export function ShoppingAssistantChat() {
  const [isOpen, setIsOpen] = useState(false)
  const [input, setInput] = useState("")
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const { messages, append, status, error } = useChat({
    api: "/api/shopping-assistant",
    id: "shopping-assistant",
    initialMessages: [
      {
        role: "assistant",
        content: "Sawubona! I'm your Coco Milk Kids shopping assistant. I'm here to help you find the perfect clothing for your little ones. Whether you need help with sizing, styling for South African weather, or finding the best deals, I'm here to help! How can I assist you today?",
      },
    ],
  })

  const isLoading = status === 'streaming'

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (isOpen) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
    }
  }, [messages, isOpen])

  const handleSendMessage = async () => {
    if (!input.trim()) return

    const userMessage = input.trim()
    setInput("")

    await append({
      role: "user",
      content: userMessage,
    })
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <>
      {/* Chat Button */}
      <Button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "fixed bottom-4 right-4 z-50 rounded-full shadow-lg p-4 h-14 w-14",
          isOpen ? "bg-red-500 hover:bg-red-600" : "bg-[#012169] hover:bg-[#012169]/90",
        )}
      >
        {isOpen ? <X className="h-6 w-6" /> : <Bot className="h-6 w-6" />}
      </Button>

      {/* Chat Window */}
      {isOpen && (
        <div className="fixed bottom-20 right-4 z-50 w-80 md:w-96 h-[500px] bg-white rounded-lg shadow-xl flex flex-col border">
          {/* Header */}
          <div className="bg-[#012169] text-white p-4 rounded-t-lg flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src="https://images.unsplash.com/photo-1519238359922-989348752efb?q=80&w=32&h=32&auto=format&fit=crop" alt="AI Assistant" />
                <AvatarFallback>AI</AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium">AI Shopping Assistant</div>
                <div className="text-xs opacity-80">Online • Powered by SoImagine</div>
              </div>
            </div>
            <Button variant="ghost" size="icon" className="text-white" onClick={() => setIsOpen(false)}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((msg, index) => (
              <div key={index} className={cn("flex", msg.role === "user" ? "justify-end" : "justify-start")}>
                {msg.role === "assistant" && (
                  <Avatar className="h-8 w-8 mr-2 flex-shrink-0">
                    <AvatarImage src="https://images.unsplash.com/photo-1519238359922-989348752efb?q=80&w=32&h=32&auto=format&fit=crop" alt="AI Assistant" />
                    <AvatarFallback>AI</AvatarFallback>
                  </Avatar>
                )}
                <div
                  className={cn(
                    "max-w-[80%] rounded-lg p-3",
                    msg.role === "user"
                      ? "bg-[#012169] text-white rounded-tr-none"
                      : "bg-gray-100 text-gray-800 rounded-tl-none",
                  )}
                >
                  {msg.role === "assistant" ? (
                    <div className="whitespace-pre-line">
                      {msg.parts && msg.parts.map((part: MessagePart, i: number) => {
                        if (part.type === 'text') {
                          return <span key={i}>{part.text}</span>
                        }
                        return null
                      })}
                    </div>
                  ) : (
                    <p className="whitespace-pre-line">{msg.content}</p>
                  )}
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="flex justify-start">
                <Avatar className="h-8 w-8 mr-2 flex-shrink-0">
                  <AvatarImage src="https://images.unsplash.com/photo-1519238359922-989348752efb?q=80&w=32&h=32&auto=format&fit=crop" alt="AI Assistant" />
                  <AvatarFallback>AI</AvatarFallback>
                </Avatar>
                <div className="bg-gray-100 text-gray-800 rounded-lg rounded-tl-none p-3 max-w-[80%]">
                  <div className="flex space-x-1">
                    <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce" />
                    <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-100" />
                    <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-200" />
                  </div>
                </div>
              </div>
            )}

            {error && (
              <div className="flex justify-start">
                <Avatar className="h-8 w-8 mr-2 flex-shrink-0">
                  <AvatarImage src="https://images.unsplash.com/photo-1519238359922-989348752efb?q=80&w=32&h=32&auto=format&fit=crop" alt="AI Assistant" />
                  <AvatarFallback>AI</AvatarFallback>
                </Avatar>
                <div className="bg-red-50 text-red-500 rounded-lg rounded-tl-none p-3 max-w-[80%]">
                  <p>Sorry, I'm having trouble connecting. Please try again.</p>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-3 border-t">
            <div className="flex items-center space-x-2">
              <Input
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Ask about products, sizes, SA weather styling..."
                className="flex-1 font-light"
                disabled={isLoading}
              />
              <Button
                onClick={handleSendMessage}
                disabled={!input.trim() || isLoading}
                size="icon"
                className="bg-[#012169] text-white hover:bg-[#012169]/90"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
