'use client'

import React from 'react'
import { usePathname } from 'next/navigation'
import { ShoppingAssistantChat } from "@/components/shopping-assistant-chat"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { Toaster as SonnerToaster } from "sonner"
import { CartProvider } from "@/components/storefront/cart/cart-provider"
import { WishlistProvider } from "@/components/wishlist-provider"
import { AuthProvider } from "@/components/auth-provider"
import Header from "@/components/header"
import Footer from "@/components/footer"
import { SupportChat } from "@/components/support-chat"
import { MobileBottomNav } from "@/components/mobile-bottom-nav"
import NextTopLoader from 'nextjs-toploader'

interface LayoutWrapperProps {
  children: React.ReactNode
  showDefaultHeader?: boolean
}

export function LayoutWrapper({ children, showDefaultHeader = true }: LayoutWrapperProps) {
  const pathname = usePathname()
  const isAdminRoute = pathname?.startsWith('/admin')

  return (
    <>
      <NextTopLoader
        color="#131540"
        initialPosition={0.08}
        crawlSpeed={200}
        height={5}
        crawl={true}
        showSpinner={false}
      />
      <AuthProvider>
        <CartProvider>
          <WishlistProvider>
            <ThemeProvider attribute="class" defaultTheme="light" enableSystem={false}>
              {!isAdminRoute && showDefaultHeader && <Header />}
              {children}
              {!isAdminRoute && <Footer />}
              {/* {!isAdminRoute && <MobileBottomNav />} */}
              <Toaster />
              <SonnerToaster position="top-right" />
              {/* {!isAdminRoute && <SupportChat />}
              {!isAdminRoute && <ShoppingAssistantChat />} */}
            </ThemeProvider>
          </WishlistProvider>
        </CartProvider>
      </AuthProvider>
    </>
  )
}
