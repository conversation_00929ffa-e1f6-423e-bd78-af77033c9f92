import Link from "next/link"
import { ProductCard } from "@/components/storefront/products/product-card"
import { getProducts } from "@/lib/products"

export async function NewArrivals() {
  const products = await getProducts()
  const newArrivals = products.filter((product) => product.isNew).slice(0, 4)

  return (
    <section className="py-16 md:py-24">
      <div className="flex flex-col md:flex-row justify-between items-baseline mb-12">
        <h2 className="text-2xl md:text-3xl font-light font-montserrat tracking-wide">New Arrivals</h2>
        <Link
          href="/collections/new-arrivals"
          className="text-sm text-muted-foreground hover:text-foreground mt-2 md:mt-0 btn-minimal"
        >
          View All New Arrivals
        </Link>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8">
        {newArrivals.map((product, index) => (
          <div key={product.id} className="fade-in" style={{ animationDelay: `${0.1 * index}s` }}>
            <ProductCard product={product} />
          </div>
        ))}
      </div>
    </section>
  )
}
