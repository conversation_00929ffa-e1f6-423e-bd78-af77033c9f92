"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'

interface User {
  id: number
  username: string
  email: string
  first_name: string
  last_name: string
  display_name: string
  roles: string[]
  avatar_url?: string
}

interface Customer {
  id: number
  billing: any
  shipping: any
  is_paying_customer: boolean
}

interface AuthContextType {
  user: User | null
  customer: Customer | null
  token: string | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (usernameOrEmail: string, password: string) => Promise<{ success: boolean; error?: string }>
  register: (userData: {
    email: string
    password: string
    first_name?: string
    last_name?: string
    username?: string
  }) => Promise<{ success: boolean; error?: string }>
  logout: () => Promise<void>
  updateUser: (userData: Partial<User>) => Promise<{ success: boolean; error?: string }>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user && !!token

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const storedToken = localStorage.getItem('auth_token')
        const storedUser = localStorage.getItem('auth_user')
        const storedCustomer = localStorage.getItem('auth_customer')

        if (storedToken && storedUser) {
          // Validate token with server
          const isValid = await validateToken(storedToken)
          
          if (isValid) {
            setToken(storedToken)
            setUser(JSON.parse(storedUser))
            
            if (storedCustomer) {
              setCustomer(JSON.parse(storedCustomer))
            }
          } else {
            // Token is invalid, clear stored data
            clearAuthData()
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
        clearAuthData()
      } finally {
        setIsLoading(false)
      }
    }

    initializeAuth()
  }, [])

  const validateToken = async (token: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/woocommerce/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ action: 'validate' }),
      })

      const data = await response.json()
      return data.success && data.data.valid
    } catch (error) {
      console.error('Token validation error:', error)
      return false
    }
  }

  const clearAuthData = () => {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('auth_user')
    localStorage.removeItem('auth_customer')
    setToken(null)
    setUser(null)
    setCustomer(null)
  }

  const login = async (usernameOrEmail: string, password: string) => {
    try {
      setIsLoading(true)

      const response = await fetch('/api/woocommerce/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'login',
          username: usernameOrEmail,
          password,
        }),
      })

      const data = await response.json()

      if (data.success) {
        const { token: authToken, user: userData, customer: customerData } = data.data

        // Store auth data
        localStorage.setItem('auth_token', authToken)
        localStorage.setItem('auth_user', JSON.stringify(userData))
        
        if (customerData) {
          localStorage.setItem('auth_customer', JSON.stringify(customerData))
        }

        // Update state
        setToken(authToken)
        setUser(userData)
        setCustomer(customerData)

        return { success: true }
      } else {
        return { success: false, error: data.error }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: 'Login failed. Please try again.' }
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (userData: {
    email: string
    password: string
    first_name?: string
    last_name?: string
    username?: string
  }) => {
    try {
      setIsLoading(true)

      const response = await fetch('/api/woocommerce/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'register',
          ...userData,
        }),
      })

      const data = await response.json()

      if (data.success) {
        const { token: authToken, user: newUser, customer: customerData } = data.data

        // Store auth data
        localStorage.setItem('auth_token', authToken)
        localStorage.setItem('auth_user', JSON.stringify(newUser))
        
        if (customerData) {
          localStorage.setItem('auth_customer', JSON.stringify(customerData))
        }

        // Update state
        setToken(authToken)
        setUser(newUser)
        setCustomer(customerData)

        return { success: true }
      } else {
        return { success: false, error: data.error }
      }
    } catch (error) {
      console.error('Registration error:', error)
      return { success: false, error: 'Registration failed. Please try again.' }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      if (token) {
        // Notify server about logout
        await fetch('/api/woocommerce/auth', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({ action: 'logout' }),
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Clear auth data regardless of server response
      clearAuthData()
    }
  }

  const updateUser = async (userData: Partial<User>) => {
    try {
      if (!token) {
        return { success: false, error: 'Not authenticated' }
      }

      const response = await fetch('/api/woocommerce/customers', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(userData),
      })

      const data = await response.json()

      if (data.success) {
        // Update user state
        const updatedUser = { ...user, ...userData } as User
        setUser(updatedUser)
        localStorage.setItem('auth_user', JSON.stringify(updatedUser))

        return { success: true }
      } else {
        return { success: false, error: data.error }
      }
    } catch (error) {
      console.error('Update user error:', error)
      return { success: false, error: 'Failed to update user information' }
    }
  }

  const refreshUser = async () => {
    try {
      if (!token) return

      const response = await fetch('/api/woocommerce/customers', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const data = await response.json()

      if (data.success) {
        setCustomer(data.data)
        localStorage.setItem('auth_customer', JSON.stringify(data.data))
      }
    } catch (error) {
      console.error('Refresh user error:', error)
    }
  }

  const value: AuthContextType = {
    user,
    customer,
    token,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    updateUser,
    refreshUser,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
