import * as React from "react";

interface CocoLogoHoriProps extends React.SVGProps<SVGSVGElement> {}

function CocoLogoHori(props: CocoLogoHoriProps) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 784.384 141.754" {...props}>
      <path d="M187.08 131.876c-1.166-.256-8.555-2.217-16.421-4.356-19.656-5.346-32.045-8.618-38.315-10.12-2.914-.697-7.204-1.73-9.535-2.294-14.613-3.538-35.682-7.158-77.032-13.236-10.057-1.478-11.86-1.952-13.44-3.533-3.43-3.43.597-7.024 12.473-11.128 6.959-2.405 22.826-7.358 30.15-9.41 11.375-3.19 39.083-9.988 45.289-11.112l2.384-.432.011-1.263c.034-3.442 2-9.085 4.439-12.74.842-1.263 2.986-3.885 4.765-5.827 6.344-6.93 7.83-9.632 7.876-14.319.023-2.336-.12-2.96-1.15-5.053-2.802-5.693-7.993-6.508-15.942-2.503-1.262.636-3.161 1.808-4.22 2.604-1.706 1.284-1.984 1.388-2.451.92-.468-.467-.406-.677.545-1.858 1.4-1.738 6.194-4.981 9.018-6.1 1.795-.71 2.847-.882 5.407-.882 2.768 0 3.439.128 5.196.99 4.917 2.415 7.732 8.385 6.726 14.263-.765 4.462-2.309 6.829-10.812 16.578-1.331 1.527-2.882 3.57-3.446 4.542-2.11 3.633-3.375 9.361-2.448 11.092.185.346 3.227 2.838 6.76 5.539 26.738 20.44 37.87 29.452 49.978 40.465 8.886 8.082 11.361 11.018 12.674 15.03.739 2.26.49 3.439-.875 4.145-1.26.652-4.64.651-7.605-.001zm6.234-3.17c-.752-4.345-16.91-19.431-40.135-37.476-19.648-15.265-28.591-21.967-29.678-22.24-1.26-.316-3.28.114-20.114 4.283-15.178 3.758-28.37 7.393-40.83 11.25-22.75 7.04-23.273 7.235-26.952 10.02-.96.727-1.735 1.48-1.724 1.674.052.886 4.67 1.831 19.89 4.072 15.458 2.276 36.196 5.497 45.209 7.022 14.72 2.49 35.909 7.444 59.849 13.99 19.5 5.332 21.267 5.81 26.132 7.077 5.397 1.406 8.561 1.53 8.353.328zM530.754 74.177c-.134-.134-.245-11.544-.245-25.353v-25.11h3.656l.101 5.597.101 5.596 1.768-2.478c2.265-3.175 5.542-5.892 9.256-7.674 4.06-1.947 7.53-2.631 12.125-2.39 4.158.22 6.504.84 10.473 2.767 3.516 1.708 8.364 6.377 10.642 10.25.862 1.464 1.672 2.663 1.8 2.662.13 0 .699-.889 1.266-1.974.567-1.086 1.92-3.051 3.005-4.367 7.821-9.483 21.029-12.124 31.973-6.395 3.427 1.794 8.496 7.046 10.418 10.795 2.982 5.815 3.115 6.762 3.274 23.346l.14 14.606h-3.661l-.165-14.239c-.14-12.08-.26-14.571-.801-16.436-2.281-7.872-8.083-14.107-15.268-16.408-3.554-1.138-8.808-1.074-12.597.155-6.666 2.16-11.747 7.164-14.447 14.225l-1.054 2.756-.19 15.065-.189 15.066-1.745.111-1.745.112-.007-13.707c-.007-15.334-.182-16.914-2.375-21.533-1.493-3.143-2.02-3.872-5.034-6.949-1.605-1.64-3.071-2.689-5.217-3.731-3.776-1.835-6.22-2.402-10.15-2.355-9.343.113-17.458 6.405-20.694 16.044-.7 2.082-.764 3.31-.892 16.997l-.138 14.753-.804.215c-1.08.29-2.28.281-2.58-.019zm159.029-.214c-.096-.253-.133-19.144-.08-41.98l.094-41.522 1.745-.112 1.746-.112v83.732l-.827.222c-1.346.36-2.491.263-2.678-.228zm30.696-41.613V-9.723h3.307l.048 24.343c.026 13.39.15 25.536.276 26.994l.227 2.65 4.042-3.234c7.746-6.197 18.39-14.422 20.8-16.076l2.428-1.665 2.533.305 2.532.304-2.388 1.82c-7.536 5.743-20.577 16.169-20.577 16.451 0 .34 6.87 8.113 18.552 20.994 3.64 4.013 7.417 8.188 8.394 9.278l1.776 1.981h-5.556L745.375 61.47c-6.324-7.124-12.239-13.82-13.144-14.882-.906-1.06-1.813-1.928-2.015-1.927-.202.001-1.731 1.09-3.399 2.42l-3.031 2.418V74.422h-3.307zm-62.466 16.535v-25.17h3.674v50.34h-3.674zm-.032-33.358c-2.104-1.616-1.494-4.52 1.082-5.148 2.028-.495 3.988 1.388 3.545 3.406-.395 1.797-3.186 2.848-4.627 1.742zM535.813 131.567c-2.635-.861-4.685-2.252-6.393-4.336-2.483-3.03-1.782-3.338 1.376-.603 4.723 4.092 10.212 5.098 15.007 2.752 5.213-2.55 6.527-8.51 2.692-12.208-1.575-1.518-3.433-2.377-9.503-4.392-2.357-.783-5.014-2.005-6.004-2.762-3.458-2.644-4.64-6.796-2.966-10.414 1.729-3.734 4.924-5.498 9.967-5.502 3.945-.003 6.888 1.123 9.246 3.538 2.03 2.08 1.312 2.736-.98.895-3.403-2.735-9.3-3.87-12.71-2.446-1.888.79-4.079 3.377-4.427 5.23-.946 5.044 2.142 8.036 11.08 10.74 5.844 1.767 9.057 4.317 9.886 7.846.999 4.253-1.15 8.58-5.313 10.702-2.958 1.508-7.945 1.945-10.958.96zm-52.86-.145l-1.185-.18V94.124l8.54.153 8.54.153 3.211 1.615c3.634 1.828 4.854 2.812 7.043 5.68 4.3 5.632 4.416 15.997.248 22.054-2.92 4.246-7.126 6.693-12.942 7.534-2.731.395-11.123.463-13.454.11zm16.016-2.12c7.492-1.935 12.058-8.762 11.59-17.33-.29-5.321-1.503-8.227-4.82-11.544-4.018-4.019-5.968-4.63-14.915-4.672l-7.116-.034-.113 16.818c-.062 9.25-.01 17.086.117 17.415.189.492 1.347.542 6.533.285 3.467-.171 7.393-.593 8.724-.937zm-51.16 1.459c-.494-.625-6.576-10.941-11.248-19.083-1.77-3.083-3.272-5.672-3.338-5.752-.065-.08-2.269 1.753-4.896 4.075l-4.777 4.223v8.322c0 8.035-.03 8.323-.863 8.323-.848 0-.862-.288-.862-18.328 0-15.264.1-18.328.593-18.328.962 0 1.14 2.203.845 10.385-.154 4.25-.144 7.727.022 7.727.166 0 4.77-4.027 10.23-8.948 5.46-4.922 10.337-9.1 10.836-9.285 2.409-.893 1.118.827-4.15 5.532l-5.77 5.154.657 1.294c.362.712 3.028 5.272 5.925 10.134 5.598 9.396 8.412 14.351 8.412 14.812 0 .49-1.17.304-1.615-.257zm16.278-18.22c0-18.04.013-18.328.862-18.328.85 0 .863.287.863 18.328 0 18.04-.013 18.328-.863 18.328-.849 0-.862-.288-.862-18.328zM213.052 72.475c-7.292-.793-13.66-4.258-18.154-9.878-2.24-2.802-3.668-5.534-4.833-9.243-1.119-3.563-1.218-10.938-.197-14.685 2.036-7.471 7.32-13.755 14.4-17.128 4.342-2.067 6.685-2.532 12.678-2.515 5.035.014 5.52.08 8.818 1.213 1.92.658 4.112 1.605 4.87 2.103l1.38.906-.97 1.464c-.532.805-1.01 1.464-1.061 1.464-.052 0-1.146-.49-2.432-1.088-10.359-4.822-22.488-2.34-29.338 6.003-3.493 4.255-5.14 8.98-5.14 14.743 0 4.23.728 7.274 2.602 10.876 2.608 5.013 7.16 8.863 12.88 10.893 3.83 1.36 10.648 1.607 14.867.54 2.4-.606 4.762-1.459 6.052-2.184.197-.11 2.17 2.6 2.17 2.982 0 .342-5.588 2.458-7.78 2.946-3.101.69-7.614.936-10.812.588zm63.16-.164c-4.79-1.09-9.991-4.366-13.325-8.392-2.366-2.859-5-8.166-5.706-11.5-.695-3.283-.695-9.97 0-12.92 2.264-9.6 10.1-17.394 19.485-19.382 3.213-.68 8.875-.689 11.688-.017 4.776 1.141 10.008 4.32 13.43 8.158 2.086 2.339 5.194 8.29 5.906 11.307 2.68 11.36-2.395 23.884-12.054 29.751-3.754 2.28-7.328 3.245-12.55 3.388-3.06.084-5.354-.048-6.874-.393zm12.682-4.225c4.26-1.27 8.5-4.376 11.273-8.263 2.833-3.97 4.22-8.387 4.219-13.438-.001-7.435-2.924-13.779-8.426-18.29-12.004-9.843-30.164-3.843-34.784 11.493-1.006 3.338-.915 9.803.188 13.411 3.718 12.162 15.56 18.651 27.53 15.087zm65.66 4.343c-6.167-.796-11.093-3.148-15.283-7.297-5.257-5.206-7.644-10.674-7.944-18.195-.223-5.583.391-8.643 2.678-13.343 1.418-2.915 2.216-4.006 4.876-6.668 4.53-4.532 9.115-6.748 16.04-7.752 4.959-.718 11.463.26 16.401 2.465 2.284 1.02 2.385 1.388.894 3.258l-.843 1.057-2.179-.978c-3.236-1.452-6.484-2.096-10.613-2.102-6.717-.01-12.344 2.24-16.865 6.744-4.508 4.49-6.58 9.589-6.587 16.216-.007 6.413 2.108 11.63 6.56 16.178 4.736 4.839 10.77 7.043 18.339 6.7 3.335-.15 4.683-.397 7.387-1.351 4.983-1.758 4.706-1.748 5.443-.203.845 1.772.58 2.055-3.21 3.427-4.88 1.766-10.44 2.445-15.094 1.844zm63.565.039c-4.74-.942-8.772-3.133-12.504-6.792-3.03-2.97-5.216-6.478-6.642-10.656-.835-2.448-1.028-3.738-1.155-7.716-.194-6.069.4-9.08 2.72-13.778 1.426-2.89 2.268-4.035 4.89-6.658 2.554-2.554 3.771-3.46 6.278-4.671 4.031-1.95 7.395-2.736 11.744-2.745 4.226-.01 7.222.702 11.207 2.66 5.498 2.703 9.702 7.01 12.336 12.64 1.512 3.23 2.133 5.657 2.412 9.43.468 6.33-1.326 12.832-5.026 18.21-2.972 4.32-9.098 8.357-14.728 9.706-3.208.77-8.646.944-11.532.37zm8.752-3.71c3.343-.519 5.594-1.406 8.662-3.416 2.855-1.87 4.634-3.676 6.37-6.463 2.524-4.053 3.575-7.721 3.579-12.494.006-6.713-2.223-12.167-6.837-16.737-4.586-4.542-10.02-6.648-16.297-6.317-5.813.306-9.901 2.104-13.992 6.152-2.974 2.943-4.515 5.354-5.872 9.186-1.123 3.17-1.233 10.722-.207 14.127 2.44 8.095 8.688 14.087 16.463 15.789 2.948.645 4.823.685 8.13.172z" />
    </svg>
  );
}

const MemoCocoLogoHori = React.memo(CocoLogoHori);
export default MemoCocoLogoHori;
