# Next.js Error Component

A modern, feature-rich error component for Next.js applications built with Tailwind CSS and Shadcn UI components.

## Features

- **Error Details Display**: Shows error messages and optional stack traces
- **Retry Functionality**: Allows users to attempt recovery from errors
- **Custom Styling with Animations**: Smooth transitions and modern UI
- **Error Reporting/Logging**: Built-in error reporting to your backend
- **Responsive Design**: Works on all device sizes
- **Dark Mode Support**: Seamlessly integrates with your theme
- **Customizable Actions**: Add custom action buttons
- **Accessibility**: Follows best practices for screen readers

## Usage

### Basic Usage

```tsx
import { ErrorComponent } from "@/components/ui/error";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <div className="container mx-auto py-10">
      <ErrorComponent
        error={error}
        reset={reset}
      />
    </div>
  );
}
```

### Advanced Usage

```tsx
import { ErrorComponent } from "@/components/ui/error";

export default function CustomError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <div className="container mx-auto py-10">
      <ErrorComponent
        error={error}
        reset={reset}
        title="Custom Error Title"
        description="A detailed description of what went wrong"
        showHomeButton={true}
        showReportButton={true}
        showStackTrace={process.env.NODE_ENV === "development"}
        customAction={{
          label: "Go to Dashboard",
          action: () => window.location.href = "/dashboard",
        }}
        errorReportEndpoint="/api/custom-error-report"
      />
    </div>
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `error` | `Error` | Required | The error object to display |
| `reset` | `() => void` | Optional | Function to reset/retry after error |
| `title` | `string` | "Something went wrong" | Title of the error message |
| `description` | `string` | "We encountered an error..." | Description of the error |
| `showHomeButton` | `boolean` | `true` | Whether to show the home navigation button |
| `showReportButton` | `boolean` | `true` | Whether to show the error reporting form |
| `showStackTrace` | `boolean` | `true` | Whether to show technical stack trace details |
| `customAction` | `{ label: string, action: () => void }` | Optional | Custom action button |
| `errorReportEndpoint` | `string` | "/api/error-report" | API endpoint for error reporting |

## Error Reporting

The component includes built-in error reporting functionality. When a user submits an error report, it sends the following data to your API endpoint:

```json
{
  "errorMessage": "Error message from the error object",
  "errorStack": "Full stack trace if available",
  "userFeedback": "User's description of what happened",
  "url": "Current page URL",
  "timestamp": "ISO timestamp of when the error occurred"
}
```

## Implementation Examples

### Global Error Boundary

```tsx
// app/global-error.tsx
"use client";

import { ErrorComponent } from "@/components/ui/error";

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <html>
      <body>
        <div className="min-h-screen flex items-center justify-center p-4">
          <ErrorComponent 
            error={error} 
            reset={reset}
            title="Application Error"
            description="We're sorry, but something went wrong with the application."
          />
        </div>
      </body>
    </html>
  );
}
```

### Page-Level Error Boundary

```tsx
// app/some-page/error.tsx
"use client";

import { ErrorComponent } from "@/components/ui/error";

export default function PageError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <div className="container mx-auto py-10">
      <ErrorComponent
        error={error}
        reset={reset}
        title="Page Error"
        description="There was a problem loading this page."
      />
    </div>
  );
}
```

## Best Practices

1. **Environment-Based Stack Traces**: Only show stack traces in development
   ```tsx
   showStackTrace={process.env.NODE_ENV === "development"}
   ```

2. **Custom Error Reporting**: Create specialized endpoints for different areas of your app
   ```tsx
   errorReportEndpoint="/api/admin/error-report" // For admin errors
   ```

3. **Contextual Information**: Provide clear, user-friendly error messages
   ```tsx
   title="Payment Processing Error"
   description="We couldn't process your payment. Please try again or use a different payment method."
   ```

4. **Recovery Options**: Always provide a way for users to continue using the app
   ```tsx
   showHomeButton={true}
   customAction={{
     label: "View Cart",
     action: () => router.push("/cart"),
   }}
   ```