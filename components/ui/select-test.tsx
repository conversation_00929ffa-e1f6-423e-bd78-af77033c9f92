"use client"

import React from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, useSelectValue } from './select'

// Test component to verify the enhanced Select works correctly
export function SelectTest() {
  // Test the useSelectValue hook
  const { value: country, setValue: setCountry } = useSelectValue("", "ZA")
  
  // Test regular useState (problematic case)
  const [status, setStatus] = React.useState("")
  
  return (
    <div className="space-y-4 p-4">
      <h3 className="text-lg font-semibold">Select Component Tests</h3>
      
      {/* Test 1: Using the useSelectValue hook (recommended) */}
      <div className="space-y-2">
        <label>Country (using useSelectValue hook):</label>
        <Select value={country} onValueChange={setCountry}>
          <SelectTrigger>
            <SelectValue placeholder="Select country" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ZA">South Africa</SelectItem>
            <SelectItem value="US">United States</SelectItem>
            <SelectItem value="UK">United Kingdom</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-sm text-gray-600">Selected: {country || "None"}</p>
      </div>

      {/* Test 2: Using regular useState with empty string (should now work) */}
      <div className="space-y-2">
        <label>Status (using regular useState with empty string):</label>
        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger>
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-sm text-gray-600">Selected: {status || "None"}</p>
      </div>

      {/* Test 3: Uncontrolled with defaultValue */}
      <div className="space-y-2">
        <label>Priority (uncontrolled with defaultValue):</label>
        <Select defaultValue="medium">
          <SelectTrigger>
            <SelectValue placeholder="Select priority" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="low">Low</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="high">High</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}