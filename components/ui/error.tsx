"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>riangle, RefreshCw, ChevronDown, ChevronUp, Send, Home } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";

interface ErrorComponentProps {
  error: Error;
  reset?: () => void;
  title?: string;
  description?: string;
  showHomeButton?: boolean;
  showReportButton?: boolean;
  showStackTrace?: boolean;
  customAction?: {
    label: string;
    action: () => void;
  };
  errorReportEndpoint?: string;
}

/**
 * A modern, feature-rich error component for Next.js applications
 * 
 * Features:
 * - Error details display with stack trace
 * - Retry functionality
 * - Custom styling with animations
 * - Error reporting capability
 * - Home navigation
 * - Custom actions
 */
export function ErrorComponent({
  error,
  reset,
  title = "Something went wrong",
  description = "We encountered an error while processing your request",
  showHomeButton = true,
  showReportButton = true,
  showStackTrace = true,
  customAction,
  errorReportEndpoint = "/api/error-report"
}: ErrorComponentProps) {
  const [showDetails, setShowDetails] = useState(false);
  const [reportText, setReportText] = useState("");
  const [isReporting, setIsReporting] = useState(false);
  const [reportSent, setReportSent] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  // Log the error to console in development
  useEffect(() => {
    console.error("Application error:", error);
  }, [error]);

  // Handle retry action
  const handleRetry = () => {
    if (reset) {
      toast({
        title: "Retrying...",
        description: "Attempting to recover from the error",
        duration: 3000,
      });
      reset();
    }
  };

  // Handle navigation to home
  const handleGoHome = () => {
    router.push("/");
    toast({
      title: "Navigating home",
      description: "Taking you back to safety",
      duration: 2000,
    });
  };

  // Handle error reporting
  const handleReportError = async () => {
    if (!reportText.trim() && !error) return;

    setIsReporting(true);
    try {
      const response = await fetch(errorReportEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          errorMessage: error.message,
          errorStack: error.stack,
          userFeedback: reportText,
          url: typeof window !== "undefined" ? window.location.href : "",
          timestamp: new Date().toISOString(),
        }),
      });

      if (response.ok) {
        setReportSent(true);
        toast({
          title: "Error report sent",
          description: "Thank you for helping us improve",
          duration: 3000,
        });
      } else {
        throw new Error("Failed to send error report");
      }
    } catch (reportError) {
      toast({
        title: "Couldn't send report",
        description: "Please try again later or contact support",
        variant: "destructive",
        duration: 3000,
      });
      console.error("Error reporting failed:", reportError);
    } finally {
      setIsReporting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full max-w-3xl mx-auto p-4"
    >
      <Alert variant="destructive" className="mb-6 border-red-300 bg-red-50 dark:bg-red-950/30">
        <AlertTriangle className="h-5 w-5" />
        <AlertTitle className="text-red-800 dark:text-red-300 ml-2">{title}</AlertTitle>
        <AlertDescription className="text-red-700 dark:text-red-400 ml-2">
          {description}
        </AlertDescription>
      </Alert>

      <Card className="shadow-lg border-red-100 dark:border-red-900/30">
        <CardHeader>
          <CardTitle className="text-xl font-semibold">Error Details</CardTitle>
          <CardDescription>
            {error.message || "An unexpected error occurred"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {showStackTrace && (
            <Accordion type="single" collapsible>
              <AccordionItem value="stack-trace">
                <AccordionTrigger className="text-sm font-medium">
                  Technical Details
                </AccordionTrigger>
                <AccordionContent>
                  <div className="bg-slate-100 dark:bg-slate-900 p-3 rounded-md overflow-x-auto">
                    <pre className="text-xs text-slate-700 dark:text-slate-300 whitespace-pre-wrap">
                      {error.stack || "No stack trace available"}
                    </pre>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          )}

          {showReportButton && (
            <AnimatePresence mode="wait">
              {!reportSent ? (
                <motion.div
                  key="report-form"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-3"
                >
                  <h3 className="text-sm font-medium">Help us fix this issue</h3>
                  <Textarea
                    placeholder="What were you trying to do when this error occurred? (optional)"
                    value={reportText}
                    onChange={(e) => setReportText(e.target.value)}
                    className="min-h-[100px] text-sm"
                  />
                  <Button 
                    onClick={handleReportError} 
                    disabled={isReporting}
                    className="w-full"
                    variant="outline"
                  >
                    {isReporting ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Sending Report...
                      </>
                    ) : (
                      <>
                        <Send className="mr-2 h-4 w-4" />
                        Send Error Report
                      </>
                    )}
                  </Button>
                </motion.div>
              ) : (
                <motion.div
                  key="report-success"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-900 rounded-md p-3 text-green-800 dark:text-green-300 text-sm"
                >
                  Thank you for your feedback! Our team has been notified and will work on fixing this issue.
                </motion.div>
              )}
            </AnimatePresence>
          )}
        </CardContent>
        <CardFooter className="flex flex-wrap gap-2 justify-end">
          {customAction && (
            <Button variant="outline" onClick={customAction.action}>
              {customAction.label}
            </Button>
          )}
          
          {showHomeButton && (
            <Button variant="outline" onClick={handleGoHome}>
              <Home className="mr-2 h-4 w-4" />
              Go Home
            </Button>
          )}
          
          {reset && (
            <Button onClick={handleRetry}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          )}
        </CardFooter>
      </Card>
    </motion.div>
  );
}

/**
 * Global error boundary component for Next.js applications
 */
export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <html>
      <body>
        <div className="min-h-screen flex items-center justify-center p-4 bg-slate-50 dark:bg-slate-950">
          <ErrorComponent 
            error={error} 
            reset={reset}
            title="Application Error"
            description="We're sorry, but something went wrong with the application."
          />
        </div>
      </body>
    </html>
  );
}