'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import CocoMilkLogo from './coco-milk-logo';

interface LoadingProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
}

const sizeConfig = {
  sm: {
    width: 100,
    height: 75,
    scale: 0.6,
  },
  md: {
    width: 150,
    height: 112,
    scale: 0.8,
  },
  lg: {
    width: 200,
    height: 150,
    scale: 1,
  },
};

export const Loading = ({ 
  className,
  size = 'md',
  showText = false,
}: LoadingProps) => {
  const dimensions = sizeConfig[size];

  return (
    <div className={cn(
      'flex flex-col items-center justify-center min-h-[200px] w-full',
      className
    )}>
      <motion.div
        initial={{ opacity: 0.4 }}
        animate={{
          opacity: [0.4, 0.8, 0.4],
          scale: [1, 1.05, 1],
          rotate: [-2, 2, -2]
        }}
        transition={{
          duration: 2,
          ease: "easeInOut",
          repeat: Infinity,
          repeatType: "reverse"
        }}
      >
        <CocoMilkLogo
          {...dimensions}
          hideText={!showText}
          className="opacity-70"
        />
      </motion.div>
    </div>
  );
};

export default Loading;
