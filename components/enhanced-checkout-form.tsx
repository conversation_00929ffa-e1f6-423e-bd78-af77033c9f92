'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { useCart } from '@/hooks/use-cart'
import { toast } from '@/components/ui/use-toast'
import { CreditCard, Truck, Shield, Loader2 } from 'lucide-react'

interface EnhancedCheckoutFormProps {
  onOrderComplete?: (order: any) => void
}

interface ShippingFormData {
  email: string
  phone: string
  firstName: string
  lastName: string
  address1: string
  address2: string
  city: string
  province: string
  postalCode: string
  country: string
}

export function EnhancedCheckoutForm({ onOrderComplete }: EnhancedCheckoutFormProps) {
  const { items, total, clearCart } = useCart()
  const [currentStep, setCurrentStep] = useState<'shipping' | 'payment' | 'review'>('shipping')
  const [isProcessing, setIsProcessing] = useState(false)
  
  // Form data
  const [shippingData, setShippingData] = useState<ShippingFormData>({
    email: '',
    phone: '',
    firstName: '',
    lastName: '',
    address1: '',
    address2: '',
    city: '',
    province: '',
    postalCode: '',
    country: 'South Africa'
  })
  
  const [shippingMethod, setShippingMethod] = useState('standard')
  const [paymentMethod, setPaymentMethod] = useState('payfast')
  const [billingAddressSame, setBillingAddressSame] = useState(true)
  const [agreeToTerms, setAgreeToTerms] = useState(false)

  const shippingOptions = [
    { id: 'standard', name: 'Standard Shipping (3-5 business days)', price: 99.00 },
    { id: 'express', name: 'Express Shipping (1-2 business days)', price: 149.00 },
    { id: 'overnight', name: 'Overnight Shipping (Next business day)', price: 199.00 },
    { id: 'collection', name: 'Collection (Sandton Store)', price: 0.00 }
  ]

  const paymentOptions = [
    { id: 'payfast', name: 'PayFast', description: 'Secure payment via PayFast (Cards, EFT, etc.)' },
    { id: 'ozow', name: 'Ozow', description: 'Instant EFT payments via Ozow' }
  ]

  const calculateShippingCost = () => {
    const option = shippingOptions.find(opt => opt.id === shippingMethod)
    return option?.price || 0
  }

  const calculateTax = () => {
    const subtotal = total + calculateShippingCost()
    return subtotal * 0.15 // 15% VAT
  }

  const calculateTotal = () => {
    return total + calculateShippingCost() + calculateTax()
  }

  const handleShippingSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate shipping form
    if (!shippingData.email || !shippingData.firstName || !shippingData.lastName || 
        !shippingData.address1 || !shippingData.city || !shippingData.province || 
        !shippingData.postalCode) {
      toast({
        title: 'Missing Information',
        description: 'Please fill in all required fields.',
        variant: 'destructive'
      })
      return
    }

    setCurrentStep('payment')
  }

  const handlePaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!agreeToTerms) {
      toast({
        title: 'Terms Required',
        description: 'Please agree to the terms and conditions.',
        variant: 'destructive'
      })
      return
    }

    setIsProcessing(true)

    try {
      // Create order
      const orderData = {
        customerEmail: shippingData.email,
        customerPhone: shippingData.phone,
        items: items.map(item => ({
          productId: item.id,
          quantity: item.quantity,
          price: item.price,
          name: item.name,
          image: item.image,
          color: item.color,
          size: item.size
        })),
        shippingAddress: {
          firstName: shippingData.firstName,
          lastName: shippingData.lastName,
          address1: shippingData.address1,
          address2: shippingData.address2,
          city: shippingData.city,
          province: shippingData.province,
          postalCode: shippingData.postalCode,
          country: shippingData.country,
          phone: shippingData.phone
        },
        shippingMethod,
        paymentMethod
      }

      // Create order via API
      const orderResponse = await fetch('/api/e-commerce/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
      })

      if (!orderResponse.ok) {
        throw new Error('Failed to create order')
      }

      const order = await orderResponse.json()

      // Create payment
      const paymentResponse = await fetch('/api/e-commerce/payments/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          orderId: order.id,
          amount: calculateTotal(),
          currency: 'ZAR',
          customerEmail: shippingData.email,
          customerName: `${shippingData.firstName} ${shippingData.lastName}`,
          description: `Order ${order.orderNumber}`,
          paymentMethod,
          returnUrl: `${window.location.origin}/checkout/success?order=${order.id}`,
          cancelUrl: `${window.location.origin}/checkout/cancel?order=${order.id}`,
          notifyUrl: `${window.location.origin}/api/e-commerce/payments/webhook`
        })
      })

      if (!paymentResponse.ok) {
        throw new Error('Failed to create payment')
      }

      const payment = await paymentResponse.json()

      if (payment.redirectUrl) {
        // Redirect to payment gateway
        window.location.href = payment.redirectUrl
      } else {
        // Handle payment completion
        clearCart()
        if (onOrderComplete) {
          onOrderComplete(order)
        }
      }

    } catch (error) {
      console.error('Checkout error:', error)
      toast({
        title: 'Checkout Failed',
        description: 'There was an error processing your order. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const renderShippingForm = () => (
    <Card>
      <CardHeader>
        <CardTitle>Shipping Information</CardTitle>
        <CardDescription>Enter your shipping details</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleShippingSubmit} className="space-y-4">
          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={shippingData.email}
                onChange={(e) => setShippingData(prev => ({ ...prev, email: e.target.value }))}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone *</Label>
              <Input
                id="phone"
                type="tel"
                value={shippingData.phone}
                onChange={(e) => setShippingData(prev => ({ ...prev, phone: e.target.value }))}
                required
              />
            </div>
          </div>

          {/* Name */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name *</Label>
              <Input
                id="firstName"
                value={shippingData.firstName}
                onChange={(e) => setShippingData(prev => ({ ...prev, firstName: e.target.value }))}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name *</Label>
              <Input
                id="lastName"
                value={shippingData.lastName}
                onChange={(e) => setShippingData(prev => ({ ...prev, lastName: e.target.value }))}
                required
              />
            </div>
          </div>

          {/* Address */}
          <div className="space-y-2">
            <Label htmlFor="address1">Address *</Label>
            <Input
              id="address1"
              value={shippingData.address1}
              onChange={(e) => setShippingData(prev => ({ ...prev, address1: e.target.value }))}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="address2">Apartment, suite, etc. (optional)</Label>
            <Input
              id="address2"
              value={shippingData.address2}
              onChange={(e) => setShippingData(prev => ({ ...prev, address2: e.target.value }))}
            />
          </div>

          {/* City, Province, Postal Code */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">City *</Label>
              <Input
                id="city"
                value={shippingData.city}
                onChange={(e) => setShippingData(prev => ({ ...prev, city: e.target.value }))}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="province">Province *</Label>
              <Select value={shippingData.province} onValueChange={(value) => setShippingData(prev => ({ ...prev, province: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select province" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GP">Gauteng</SelectItem>
                  <SelectItem value="WC">Western Cape</SelectItem>
                  <SelectItem value="KZN">KwaZulu-Natal</SelectItem>
                  <SelectItem value="EC">Eastern Cape</SelectItem>
                  <SelectItem value="FS">Free State</SelectItem>
                  <SelectItem value="LP">Limpopo</SelectItem>
                  <SelectItem value="MP">Mpumalanga</SelectItem>
                  <SelectItem value="NW">North West</SelectItem>
                  <SelectItem value="NC">Northern Cape</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="postalCode">Postal Code *</Label>
              <Input
                id="postalCode"
                value={shippingData.postalCode}
                onChange={(e) => setShippingData(prev => ({ ...prev, postalCode: e.target.value }))}
                required
              />
            </div>
          </div>

          {/* Shipping Method */}
          <Separator />
          <div className="space-y-4">
            <Label>Shipping Method</Label>
            <RadioGroup value={shippingMethod} onValueChange={setShippingMethod}>
              {shippingOptions.map((option) => (
                <div key={option.id} className="flex items-center space-x-2 border rounded-md p-3">
                  <RadioGroupItem value={option.id} id={option.id} />
                  <Label htmlFor={option.id} className="flex-1 cursor-pointer">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-2">
                        <Truck className="h-4 w-4" />
                        <span>{option.name}</span>
                      </div>
                      <span className={option.price === 0 ? 'text-green-600 font-medium' : ''}>
                        {option.price === 0 ? 'Free' : `R${option.price.toFixed(2)}`}
                      </span>
                    </div>
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>

          <div className="flex justify-end">
            <Button type="submit" className="bg-[#012169] hover:bg-[#012169]/90">
              Continue to Payment
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )

  const renderPaymentForm = () => (
    <Card>
      <CardHeader>
        <CardTitle>Payment Information</CardTitle>
        <CardDescription>Choose your payment method</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handlePaymentSubmit} className="space-y-4">
          {/* Payment Method */}
          <div className="space-y-4">
            <Label>Payment Method</Label>
            <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod}>
              {paymentOptions.map((option) => (
                <div key={option.id} className="flex items-center space-x-2 border rounded-md p-3">
                  <RadioGroupItem value={option.id} id={option.id} />
                  <Label htmlFor={option.id} className="flex-1 cursor-pointer">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <CreditCard className="h-4 w-4" />
                        <span className="font-medium">{option.name}</span>
                      </div>
                      <p className="text-sm text-muted-foreground">{option.description}</p>
                    </div>
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>

          {/* Security Notice */}
          <div className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-md">
            <Shield className="h-4 w-4 text-green-600" />
            <span className="text-sm text-green-800">
              Your payment information is secure and encrypted
            </span>
          </div>

          {/* Terms and Conditions */}
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="terms" 
              checked={agreeToTerms}
              onCheckedChange={(checked) => setAgreeToTerms(checked as boolean)}
            />
            <Label htmlFor="terms" className="text-sm">
              I agree to the <a href="/terms" className="underline">terms and conditions</a> and <a href="/privacy" className="underline">privacy policy</a>
            </Label>
          </div>

          <div className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => setCurrentStep('shipping')}
              disabled={isProcessing}
            >
              Back to Shipping
            </Button>
            <Button
              type="submit"
              className="bg-[#012169] hover:bg-[#012169]/90"
              disabled={isProcessing || !agreeToTerms}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                `Pay R${calculateTotal().toFixed(2)}`
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )

  return (
    <div className="space-y-6">
      {/* Progress Indicator */}
      <div className="flex items-center space-x-4">
        <div className={`flex items-center space-x-2 ${currentStep === 'shipping' ? 'text-primary' : 'text-muted-foreground'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            currentStep === 'shipping' ? 'bg-primary text-primary-foreground' : 'bg-muted'
          }`}>
            1
          </div>
          <span>Shipping</span>
        </div>
        <div className="flex-1 h-px bg-border" />
        <div className={`flex items-center space-x-2 ${currentStep === 'payment' ? 'text-primary' : 'text-muted-foreground'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            currentStep === 'payment' ? 'bg-primary text-primary-foreground' : 'bg-muted'
          }`}>
            2
          </div>
          <span>Payment</span>
        </div>
      </div>

      {/* Form Content */}
      {currentStep === 'shipping' && renderShippingForm()}
      {currentStep === 'payment' && renderPaymentForm()}
    </div>
  )
}
