"use client"

import type React from "react"

import { useState } from "react"
import { useChat, Message } from "@ai-sdk/react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2, Ruler } from "lucide-react"

export function SizeRecommendation() {
  const [childInfo, setChildInfo] = useState({
    age: "",
    height: "",
    weight: "",
    bodyType: "Average",
  })

  const [showForm, setShowForm] = useState(false)
  const [formSubmitted, setFormSubmitted] = useState(false)

  const { messages, append, status, error } = useChat({
    api: "/api/size-assistant",
    id: "size-recommendation",
    initialMessages: [],
    body: {
      childInfo: {
        ...childInfo,
        age: parseInt(childInfo.age) || 0,
        height: parseInt(childInfo.height) || 0,
        weight: parseInt(childInfo.weight) || 0,
      }
    },
  })

  const isLoading = status === 'streaming'

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setChildInfo((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (value: string) => {
    setChildInfo((prev) => ({ ...prev, bodyType: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Skip API call during SSR
    if (typeof window === 'undefined') return;

    setFormSubmitted(true)

    // Create the prompt with the child's information
    const prompt = `Based on the following information about a South African child, recommend the best size from our Coco Milk Kids collection:
    Age: ${childInfo.age} years
    Height: ${childInfo.height} cm
    Weight: ${childInfo.weight} kg
    Body Type: ${childInfo.bodyType}

    Consider that our clothing is designed with an oversized, comfortable fit suitable for South African climate and active children. Please provide a size recommendation (XS, S, M, L, XL) with a brief explanation. Also mention how this size will work well for growth and the South African lifestyle (outdoor play, warm weather, etc.). Include any care tips for South African conditions.`

    // Send the prompt to the AI
    await append({
      role: "user",
      content: prompt,
    })
  }

  // Get the latest assistant message
  const lastAssistantMessage = messages
    .filter((message: Message) => message.role === "assistant")
    .pop()

  return (
    <div className="mt-4">
      <Button
        variant="outline"
        onClick={() => setShowForm(!showForm)}
        className="flex items-center gap-2 text-sm font-light tracking-wide"
      >
        <Ruler className="h-4 w-4" />
        <span>Size Recommendation</span>
      </Button>

      {showForm && (
        <Card className="mt-4 border border-gray-200 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-light tracking-wide flex items-center justify-between">
              Size Recommendation
              <span className="text-xs font-normal text-gray-500">Powered by SoImagine</span>
            </CardTitle>
            <CardDescription>Enter your child's information for a personalized size recommendation</CardDescription>
          </CardHeader>

          {!formSubmitted || (messages.length === 0 && !isLoading) ? (
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="age" className="text-sm font-light">
                      Age (years)
                    </Label>
                    <Input
                      id="age"
                      name="age"
                      type="number"
                      min="2"
                      max="12"
                      required
                      value={childInfo.age}
                      onChange={handleInputChange}
                      className="font-light"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="height" className="text-sm font-light">
                      Height (cm)
                    </Label>
                    <Input
                      id="height"
                      name="height"
                      type="number"
                      min="50"
                      max="160"
                      required
                      value={childInfo.height}
                      onChange={handleInputChange}
                      className="font-light"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="weight" className="text-sm font-light">
                      Weight (kg)
                    </Label>
                    <Input
                      id="weight"
                      name="weight"
                      type="number"
                      min="10"
                      max="60"
                      required
                      value={childInfo.weight}
                      onChange={handleInputChange}
                      className="font-light"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bodyType" className="text-sm font-light">
                      Body Type
                    </Label>
                    <Select value={childInfo.bodyType} onValueChange={handleSelectChange}>
                      <SelectTrigger id="bodyType" className="font-light">
                        <SelectValue placeholder="Select body type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Slim">Slim</SelectItem>
                        <SelectItem value="Average">Average</SelectItem>
                        <SelectItem value="Solid">Solid</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-[#012169] hover:bg-[#012169]/90 font-light tracking-wide"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Getting Recommendation...
                    </>
                  ) : (
                    "Get Size Recommendation"
                  )}
                </Button>
              </form>
            </CardContent>
          ) : (
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-2">Calculating the perfect size...</span>
                </div>
              ) : error ? (
                <div className="p-4 bg-red-50 text-red-500 rounded-md">
                  <p>Error: {error.message || "Failed to generate size recommendation"}</p>
                  <Button
                    variant="outline"
                    className="mt-2"
                    onClick={() => setFormSubmitted(false)}
                  >
                    Try Again
                  </Button>
                </div>
              ) : lastAssistantMessage ? (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Our Recommendation:</h4>
                    <div className="font-light">
                      {lastAssistantMessage.parts.map((part, i) => {
                        if (part.type === 'text') {
                          return <div key={i} className="whitespace-pre-line">{part.text}</div>
                        }
                        return null
                      })}
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => setFormSubmitted(false)}
                  >
                    Get Another Recommendation
                  </Button>
                </div>
              ) : null}
            </CardContent>
          )}
        </Card>
      )}
    </div>
  )
}
