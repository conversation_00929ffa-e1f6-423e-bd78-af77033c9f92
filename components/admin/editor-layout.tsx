'use client'

import { cn } from '@/lib/utils'
import { useAdminUI } from '@/stores/use-admin-ui'
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable-panel'
import { useEffect } from 'react'

interface EditorLayoutProps {
  children: React.ReactNode
  leftPanel?: React.ReactNode
  rightPanel?: React.ReactNode
  className?: string
}

export function EditorLayout({
  children,
  leftPanel,
  rightPanel,
  className
}: EditorLayoutProps) {
  const {
    editorLeftPanelOpen,
    editorRightPanelOpen,
    toggleEditorLeftPanel,
    toggleEditorRightPanel,
    isSidebarCollapsed,
    toggleSidebar
  } = useAdminUI()

  // When entering editor mode, collapse the sidebar by default
  useEffect(() => {
    if (!isSidebarCollapsed) {
      toggleSidebar()
    }
  }, [isSidebarCollapsed, toggleSidebar])

  return (
    <div className={cn('flex h-full flex-1 flex-col overflow-hidden', className)}>
      <ResizablePanelGroup direction="horizontal" className="flex-1">
        {/* Left Panel */}
        {leftPanel && editorLeftPanelOpen && (
          <>
            <ResizablePanel
              defaultSize={20}
              minSize={15}
              maxSize={40}
              className="bg-background"
            >
              {leftPanel}
            </ResizablePanel>
            <ResizableHandle withHandle />
          </>
        )}

        {/* Main Content */}
        <ResizablePanel
          defaultSize={editorLeftPanelOpen && editorRightPanelOpen ? 60 : 80}
          className="bg-muted/20"
        >
          {children}
        </ResizablePanel>

        {/* Right Panel */}
        {rightPanel && editorRightPanelOpen && (
          <>
            <ResizableHandle withHandle />
            <ResizablePanel
              defaultSize={20}
              minSize={15}
              maxSize={40}
              className="bg-background"
            >
              {rightPanel}
            </ResizablePanel>
          </>
        )}
      </ResizablePanelGroup>
    </div>
  )
}
