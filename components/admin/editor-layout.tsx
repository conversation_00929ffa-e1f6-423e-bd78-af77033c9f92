'use client'

import { cn } from '@/lib/utils'
import { useAdminUI } from '@/stores/use-admin-ui'
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable-panel'
import { Button } from '@/components/ui/button'
import { PanelLeftClose, PanelRightClose } from 'lucide-react'
import { useEffect, useState } from 'react'

interface EditorLayoutProps {
  children: React.ReactNode
  leftPanel?: React.ReactNode
  rightPanel?: React.ReactNode
  className?: string
}

export function EditorLayout({
  children,
  leftPanel,
  rightPanel,
  className
}: EditorLayoutProps) {
  const {
    editorLeftPanelOpen,
    editorRightPanelOpen,
    toggleEditorLeftPanel,
    toggleEditorRightPanel,
    isSidebarCollapsed,
    toggleSidebar,
    setEditorLeftPanelWidth,
    setEditorRightPanelWidth,
    editorLeft<PERSON>anelWidth,
    editorRightPanelWidth
  } = useAdminUI()

  const [leftSize, setLeftSize] = useState(editorLeftPanelWidth)
  const [rightSize, setRightSize] = useState(editorRightPanelWidth)

  // When entering editor mode, collapse the sidebar by default
  useEffect(() => {
    if (!isSidebarCollapsed) {
      toggleSidebar()
    }
  }, [isSidebarCollapsed, toggleSidebar])

  // Save panel sizes when they change
  useEffect(() => {
    setEditorLeftPanelWidth(leftSize)
  }, [leftSize, setEditorLeftPanelWidth])

  useEffect(() => {
    setEditorRightPanelWidth(rightSize)
  }, [rightSize, setEditorRightPanelWidth])

  return (
    <div className={cn('h-screen flex flex-col overflow-hidden bg-background', className)}>
      {/* Header with panel controls */}
      <div className="flex h-12 items-center justify-between border-b px-4">
        <div className="flex items-center gap-2">
          {leftPanel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleEditorLeftPanel}
              className="h-8 w-8 p-0"
            >
              <PanelLeftClose className={cn(
                "h-4 w-4 transition-transform",
                editorLeftPanelOpen && "rotate-180"
              )} />
            </Button>
          )}
          <span className="text-sm font-medium">Editor</span>
        </div>
        <div className="flex items-center gap-2">
          {rightPanel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleEditorRightPanel}
              className="h-8 w-8 p-0"
            >
              <PanelRightClose className={cn(
                "h-4 w-4 transition-transform",
                editorRightPanelOpen && "rotate-180"
              )} />
            </Button>
          )}
        </div>
      </div>

      {/* Main content with resizable panels */}
      <ResizablePanelGroup
        direction="horizontal"
        className="flex-1 overflow-hidden"
        onLayout={(sizes) => {
          // Update panel sizes when layout changes
          const [left, , right] = sizes
          if (left) setLeftSize(left)
          if (right) setRightSize(right)
        }}
      >
        {/* Left Panel */}
        {leftPanel && editorLeftPanelOpen && (
          <>
            <ResizablePanel
              defaultSize={leftSize}
              minSize={15}
              maxSize={40}
              className="overflow-auto"
            >
              {leftPanel}
            </ResizablePanel>
            <ResizableHandle withHandle />
          </>
        )}

        {/* Main Content */}
        <ResizablePanel
          defaultSize={editorLeftPanelOpen && editorRightPanelOpen ? 100 - leftSize - rightSize : 100 - (editorLeftPanelOpen ? leftSize : 0) - (editorRightPanelOpen ? rightSize : 0)}
          className="overflow-auto"
        >
          {children}
        </ResizablePanel>

        {/* Right Panel */}
        {rightPanel && editorRightPanelOpen && (
          <>
            <ResizableHandle withHandle />
            <ResizablePanel
              defaultSize={rightSize}
              minSize={15}
              maxSize={40}
              className="overflow-auto"
            >
              {rightPanel}
            </ResizablePanel>
          </>
        )}
      </ResizablePanelGroup>
    </div>
  )
}
