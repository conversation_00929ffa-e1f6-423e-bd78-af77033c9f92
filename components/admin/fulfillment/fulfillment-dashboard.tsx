'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Package, 
  Truck, 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  TrendingUp,
  MapPin,
  Calendar,
  BarChart3
} from 'lucide-react'

interface FulfillmentMetrics {
  totalFulfillments: number
  totalItemsFulfilled: number
  averageFulfillmentTimeHours: number
  onTimeDeliveryRate: string
  fulfillmentRate: string
  statusBreakdown: Record<string, number>
  carrierBreakdown: Record<string, number>
}

interface RecentFulfillment {
  id: string
  orderNumber: string
  customerName: string
  status: string
  trackingNumber?: string
  carrier: string
  createdAt: string
  estimatedDelivery?: string
}

export function FulfillmentDashboard() {
  const [metrics, setMetrics] = useState<FulfillmentMetrics | null>(null)
  const [recentFulfillments, setRecentFulfillments] = useState<RecentFulfillment[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      
      // Fetch metrics
      const metricsResponse = await fetch('/api/e-commerce/fulfillments/metrics?period=30d', {
        headers: { 'x-admin-request': 'true' }
      })
      const metricsData = await metricsResponse.json()
      
      if (metricsData.success) {
        setMetrics(metricsData.data.overview)
      }

      // Fetch recent fulfillments
      const fulfillmentsResponse = await fetch('/api/e-commerce/fulfillments?limit=10', {
        headers: { 'x-admin-request': 'true' }
      })
      const fulfillmentsData = await fulfillmentsResponse.json()
      
      if (fulfillmentsData.success) {
        const transformed = fulfillmentsData.data.map((f: any) => ({
          id: f.id,
          orderNumber: f.order.orderNumber,
          customerName: `${f.order.customerFirstName} ${f.order.customerLastName}`,
          status: f.status,
          trackingNumber: f.trackingNumber,
          carrier: f.trackingCompany,
          createdAt: f.createdAt,
          estimatedDelivery: f.estimatedDeliveryAt
        }))
        setRecentFulfillments(transformed)
      }

    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending': return 'secondary'
      case 'open': return 'default'
      case 'delivered': return 'default'
      case 'cancelled': return 'destructive'
      default: return 'secondary'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4" />
      case 'open': return <Truck className="h-4 w-4" />
      case 'delivered': return <CheckCircle className="h-4 w-4" />
      case 'cancelled': return <AlertTriangle className="h-4 w-4" />
      default: return <Package className="h-4 w-4" />
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Fulfillment Dashboard</h2>
          <p className="text-muted-foreground">
            Monitor order fulfillment performance and shipping status
          </p>
        </div>
        <Button onClick={fetchDashboardData} variant="outline">
          <TrendingUp className="mr-2 h-4 w-4" />
          Refresh Data
        </Button>
      </div>

      {/* Metrics Cards */}
      {metrics && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Fulfillments</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.totalFulfillments}</div>
              <p className="text-xs text-muted-foreground">
                {metrics.totalItemsFulfilled} items fulfilled
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Processing Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.averageFulfillmentTimeHours}h</div>
              <p className="text-xs text-muted-foreground">
                From order to ship
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">On-Time Delivery</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.onTimeDeliveryRate}%</div>
              <Progress value={parseFloat(metrics.onTimeDeliveryRate)} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Fulfillment Rate</CardTitle>
              <Truck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.fulfillmentRate}%</div>
              <Progress value={parseFloat(metrics.fulfillmentRate)} className="mt-2" />
            </CardContent>
          </Card>
        </div>
      )}

      {/* Status Breakdown and Recent Fulfillments */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Status Breakdown */}
        {metrics && (
          <Card>
            <CardHeader>
              <CardTitle>Status Breakdown</CardTitle>
              <CardDescription>Current fulfillment status distribution</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(metrics.statusBreakdown).map(([status, count]) => (
                  <div key={status} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(status)}
                      <span className="capitalize">{status.replace('_', ' ')}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{count}</span>
                      <Badge variant={getStatusBadgeVariant(status)}>
                        {((count / metrics.totalFulfillments) * 100).toFixed(0)}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Carrier Performance */}
        {metrics && (
          <Card>
            <CardHeader>
              <CardTitle>Carrier Distribution</CardTitle>
              <CardDescription>Fulfillments by shipping carrier</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(metrics.carrierBreakdown).map(([carrier, count]) => (
                  <div key={carrier} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="capitalize">{carrier.replace('-', ' ')}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{count}</span>
                      <Badge variant="outline">
                        {((count / metrics.totalFulfillments) * 100).toFixed(0)}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Recent Fulfillments */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Fulfillments</CardTitle>
          <CardDescription>Latest fulfillment activities</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentFulfillments.map((fulfillment) => (
              <div key={fulfillment.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  {getStatusIcon(fulfillment.status)}
                  <div>
                    <div className="font-medium">#{fulfillment.orderNumber}</div>
                    <div className="text-sm text-muted-foreground">{fulfillment.customerName}</div>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <div className="text-sm font-medium">{fulfillment.carrier}</div>
                    {fulfillment.trackingNumber && (
                      <div className="text-xs text-muted-foreground">{fulfillment.trackingNumber}</div>
                    )}
                  </div>
                  <Badge variant={getStatusBadgeVariant(fulfillment.status)}>
                    {fulfillment.status}
                  </Badge>
                  <div className="text-sm text-muted-foreground">
                    {formatDate(fulfillment.createdAt)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
