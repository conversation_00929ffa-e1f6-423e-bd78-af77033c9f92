'use client'

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'

interface ZarPriceInputProps {
  value?: number
  onChange: (value: number) => void
  label?: string
  placeholder?: string
  disabled?: boolean
  required?: boolean
  className?: string
  error?: string
  description?: string
}

export function ZarPriceInput({
  value = 0,
  onChange,
  label,
  placeholder = "0.00",
  disabled = false,
  required = false,
  className,
  error,
  description
}: ZarPriceInputProps) {
  const [displayValue, setDisplayValue] = useState('')
  const [focused, setFocused] = useState(false)

  // Format number to ZAR currency display
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  }

  // Format number for input display (without currency symbol)
  const formatForInput = (amount: number): string => {
    return amount.toFixed(2)
  }

  // Parse input value to number
  const parseInputValue = (input: string): number => {
    // Remove any non-numeric characters except decimal point
    const cleaned = input.replace(/[^\d.]/g, '')
    const parsed = parseFloat(cleaned)
    return isNaN(parsed) ? 0 : Math.max(0, parsed)
  }

  // Update display value when value prop changes
  useEffect(() => {
    if (!focused) {
      setDisplayValue(value > 0 ? formatForInput(value) : '')
    }
  }, [value, focused])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value
    setDisplayValue(inputValue)
    
    const numericValue = parseInputValue(inputValue)
    onChange(numericValue)
  }

  const handleFocus = () => {
    setFocused(true)
    // Show raw number when focused
    setDisplayValue(value > 0 ? formatForInput(value) : '')
  }

  const handleBlur = () => {
    setFocused(false)
    const numericValue = parseInputValue(displayValue)
    onChange(numericValue)
    
    // Format display value
    setDisplayValue(numericValue > 0 ? formatForInput(numericValue) : '')
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow: backspace, delete, tab, escape, enter, decimal point
    if ([8, 9, 27, 13, 46, 110, 190].indexOf(e.keyCode) !== -1 ||
        // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
        (e.keyCode === 65 && e.ctrlKey === true) ||
        (e.keyCode === 67 && e.ctrlKey === true) ||
        (e.keyCode === 86 && e.ctrlKey === true) ||
        (e.keyCode === 88 && e.ctrlKey === true) ||
        // Allow: home, end, left, right
        (e.keyCode >= 35 && e.keyCode <= 39)) {
      return
    }
    
    // Ensure that it is a number and stop the keypress
    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
      e.preventDefault()
    }
    
    // Only allow one decimal point
    if (e.key === '.' && displayValue.includes('.')) {
      e.preventDefault()
    }
  }

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label htmlFor={`zar-price-${label}`} className="text-sm font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      
      <div className="relative">
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm font-medium">
          R
        </div>
        <Input
          id={`zar-price-${label}`}
          type="text"
          value={displayValue}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className={cn(
            "pl-8 text-right",
            error && "border-red-500 focus:border-red-500"
          )}
        />
      </div>
      
      {!focused && value > 0 && (
        <div className="text-sm text-muted-foreground">
          Display: {formatCurrency(value)}
        </div>
      )}
      
      {description && !error && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
      
      {error && (
        <p className="text-sm text-red-500">{error}</p>
      )}
    </div>
  )
}

// Hook for formatting ZAR currency
export function useZarFormatter() {
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  }

  const formatPrice = (amount: string | number): string => {
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount
    return formatCurrency(numericAmount)
  }

  const parseCurrency = (currencyString: string): number => {
    // Remove currency symbol and parse
    const cleaned = currencyString.replace(/[^\d.]/g, '')
    const parsed = parseFloat(cleaned)
    return isNaN(parsed) ? 0 : parsed
  }

  return {
    formatCurrency,
    formatPrice,
    parseCurrency
  }
}
