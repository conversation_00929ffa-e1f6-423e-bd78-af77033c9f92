"use client"

import * as React from "react"
import { usePageBuilder } from "@/lib/page-builder/context"
import { PropertiesPanel } from "@/lib/page-builder/components/properties-panel"
import { PageSettingsPanel } from "@/lib/page-builder/components/page-settings-panel"
import { useState } from "react"
import {
  Settings,
  Palette,
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar"
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"

interface PageBuilderPropertiesSidebarProps extends React.ComponentProps<typeof Sidebar> {
  // Additional props if needed
}

export function PageBuilderPropertiesSidebar({ ...props }: PageBuilderPropertiesSidebarProps) {
  const { state } = usePageBuilder()
  const { selectedBlockId } = state
  const [showPageSettings, setShowPageSettings] = useState(false)

  return (
    <Sidebar side="right" collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <div className="flex items-center">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-blue-600 text-white">
                  <Palette className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">Properties</span>
                  <span className="truncate text-xs">Block Settings</span>
                </div>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        {/* Page Settings Button */}
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <Sheet open={showPageSettings} onOpenChange={setShowPageSettings}>
                  <SheetTrigger asChild>
                    <SidebarMenuButton>
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Page Settings</span>
                    </SidebarMenuButton>
                  </SheetTrigger>
                  <SheetContent side="right" className="w-96">
                    <SheetHeader>
                      <SheetTitle>Page Settings</SheetTitle>
                    </SheetHeader>
                    <PageSettingsPanel />
                  </SheetContent>
                </Sheet>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Properties Panel Content */}
        <SidebarGroup className="flex-1">
          <SidebarGroupLabel>
            {selectedBlockId ? 'Block Properties' : 'No Selection'}
          </SidebarGroupLabel>
          <SidebarGroupContent className="flex-1 overflow-hidden">
            <div className="h-full">
              {selectedBlockId ? (
                <div className="h-full">
                  <PropertiesPanel blockId={selectedBlockId} />
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-64 text-center text-muted-foreground p-4">
                  <div className="mb-4">
                    <div className="w-16 h-16 bg-gray-100 rounded-full mx-auto flex items-center justify-center">
                      <Settings className="h-8 w-8 text-gray-400" />
                    </div>
                  </div>
                  <h3 className="font-medium mb-2">No Block Selected</h3>
                  <p className="text-sm">
                    Select a block from the canvas to edit its properties.
                  </p>
                </div>
              )}
            </div>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  )
}
