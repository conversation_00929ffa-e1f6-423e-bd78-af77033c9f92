'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ResponsivePie } from '@nivo/pie'
import { ResponsiveBar } from '@nivo/bar'
import { ShoppingCart, TrendingUp, TrendingDown, RefreshCw, Package } from 'lucide-react'
import { useState } from 'react'
import { useOrderMetrics } from '@/lib/ecommerce/hooks/use-dashboard-analytics'
import { formatCurrency } from '@/lib/utils'

interface OrdersAnalyticsWidgetProps {
  className?: string
}

export function OrdersAnalyticsWidget({ className }: OrdersAnalyticsWidgetProps) {
  const [view, setView] = useState<'status' | 'recent'>('status')
  const { totalOrders, orderGrowth, ordersByStatus, recentOrders, loading, error } = useOrderMetrics(30)

  const pieData = ordersByStatus.map(status => ({
    id: status.status,
    label: status.status.charAt(0).toUpperCase() + status.status.slice(1),
    value: status.count,
    color: getStatusColor(status.status)
  }))

  const getTrendIcon = () => {
    if (orderGrowth > 0) return <TrendingUp className="h-4 w-4 text-green-600" />
    if (orderGrowth < 0) return <TrendingDown className="h-4 w-4 text-red-600" />
    return <ShoppingCart className="h-4 w-4 text-gray-600" />
  }

  const getTrendColor = () => {
    if (orderGrowth > 0) return 'text-green-600'
    if (orderGrowth < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  function getStatusColor(status: string): string {
    const colors: Record<string, string> = {
      'pending': 'hsl(45, 93%, 47%)',
      'processing': 'hsl(217, 91%, 60%)',
      'shipped': 'hsl(262, 83%, 58%)',
      'delivered': 'hsl(142, 76%, 36%)',
      'cancelled': 'hsl(0, 84%, 60%)',
      'refunded': 'hsl(24, 70%, 50%)'
    }
    return colors[status] || 'hsl(210, 40%, 50%)'
  }

  function getStatusBadgeVariant(status: string) {
    const variants: Record<string, any> = {
      'pending': 'secondary',
      'processing': 'default',
      'shipped': 'outline',
      'delivered': 'default',
      'cancelled': 'destructive',
      'refunded': 'secondary'
    }
    return variants[status] || 'outline'
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Orders Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600">Error loading orders data</p>
            <p className="text-sm text-muted-foreground mt-2">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Orders Analytics
            </CardTitle>
            <CardDescription>
              Monitor order status and recent activity
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant={view === 'status' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setView('status')}
            >
              Status
            </Button>
            <Button
              variant={view === 'recent' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setView('recent')}
            >
              Recent
            </Button>
            <Button variant="outline" size="sm" disabled={loading}>
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">Loading orders data...</p>
            </div>
          </div>
        ) : (
          <>
            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="text-center">
                <div className="text-3xl font-bold">{totalOrders}</div>
                <div className="text-sm text-muted-foreground">Total Orders</div>
                <div className={`flex items-center justify-center gap-1 mt-1 ${getTrendColor()}`}>
                  {getTrendIcon()}
                  <span className="text-sm font-medium">
                    {Math.abs(orderGrowth).toFixed(1)}%
                  </span>
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{ordersByStatus.length}</div>
                <div className="text-sm text-muted-foreground">Status Types</div>
                <Badge variant="outline" className="mt-1">
                  Last 30 days
                </Badge>
              </div>
            </div>

            {/* Content based on view */}
            {view === 'status' ? (
              <div className="space-y-4">
                {/* Status Distribution Chart */}
                <div className="h-64">
                  {pieData.length > 0 ? (
                    <ResponsivePie
                      data={pieData}
                      margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
                      innerRadius={0.4}
                      padAngle={2}
                      cornerRadius={4}
                      activeOuterRadiusOffset={8}
                      colors={{ datum: 'data.color' }}
                      borderWidth={2}
                      borderColor={{ from: 'color', modifiers: [['darker', 0.2]] }}
                      arcLinkLabelsSkipAngle={10}
                      arcLinkLabelsTextColor="hsl(var(--foreground))"
                      arcLinkLabelsThickness={2}
                      arcLinkLabelsColor={{ from: 'color' }}
                      arcLabelsSkipAngle={10}
                      arcLabelsTextColor={{ from: 'color', modifiers: [['darker', 2]] }}
                      animate={true}
                      motionConfig="gentle"
                      tooltip={({ datum }) => (
                        <div className="bg-white p-3 border rounded-lg shadow-lg">
                          <div className="font-medium">{datum.label}</div>
                          <div className="text-sm text-muted-foreground">
                            {datum.value} orders ({datum.formattedValue}%)
                          </div>
                        </div>
                      )}
                      theme={{
                        background: 'transparent',
                        text: {
                          fontSize: 12,
                          fill: 'hsl(var(--foreground))'
                        }
                      }}
                    />
                  ) : (
                    <div className="h-full flex items-center justify-center">
                      <div className="text-center">
                        <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground">No order data available</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Status Breakdown */}
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {ordersByStatus.map(status => (
                    <div key={status.status} className="text-center p-3 border rounded-lg">
                      <Badge variant={getStatusBadgeVariant(status.status)} className="mb-2">
                        {status.status.charAt(0).toUpperCase() + status.status.slice(1)}
                      </Badge>
                      <div className="text-lg font-semibold">{status.count}</div>
                      <div className="text-xs text-muted-foreground">
                        {status.percentage.toFixed(1)}%
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Recent Orders List */}
                <div className="space-y-2">
                  {recentOrders.length > 0 ? (
                    recentOrders.slice(0, 8).map(order => (
                      <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <div className="font-medium">#{order.id.slice(-8)}</div>
                          <div className="text-sm text-muted-foreground">
                            {order.customerName}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{formatCurrency(order.total)}</div>
                          <Badge variant={getStatusBadgeVariant(order.status)} className="text-xs">
                            {order.status}
                          </Badge>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <ShoppingCart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">No recent orders</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Recent orders will appear here
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
