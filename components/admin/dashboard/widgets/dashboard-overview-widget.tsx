'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  DollarSign, 
  ShoppingCart, 
  Package, 
  Users, 
  TrendingUp, 
  TrendingDown, 
  RefreshCw,
  Calendar,
  AlertTriangle
} from 'lucide-react'
import { useState } from 'react'
import { useDashboardOverview } from '@/lib/ecommerce/hooks/use-dashboard-analytics'
import { formatCurrency } from '@/lib/utils'

interface DashboardOverviewWidgetProps {
  className?: string
}

export function DashboardOverviewWidget({ className }: DashboardOverviewWidgetProps) {
  const [timeRange, setTimeRange] = useState('30')
  const { overviewStats, metrics, loading, error, lastUpdated, refetch } = useDashboardOverview(parseInt(timeRange))

  const formatValue = (value: number, format: string) => {
    switch (format) {
      case 'currency':
        return formatCurrency(value)
      case 'number':
        return value.toLocaleString()
      case 'percentage':
        return `${value.toFixed(1)}%`
      default:
        return value.toString()
    }
  }

  const getTrendIcon = (growth: number) => {
    if (growth > 0) return <TrendingUp className="h-4 w-4 text-green-600" />
    if (growth < 0) return <TrendingDown className="h-4 w-4 text-red-600" />
    return <div className="h-4 w-4" />
  }

  const getTrendColor = (growth: number) => {
    if (growth > 0) return 'text-green-600'
    if (growth < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  const getStatIcon = (title: string) => {
    switch (title.toLowerCase()) {
      case 'total revenue':
        return <DollarSign className="h-5 w-5" />
      case 'total orders':
        return <ShoppingCart className="h-5 w-5" />
      case 'total products':
        return <Package className="h-5 w-5" />
      case 'total customers':
        return <Users className="h-5 w-5" />
      default:
        return <DollarSign className="h-5 w-5" />
    }
  }

  const getStatColor = (title: string) => {
    switch (title.toLowerCase()) {
      case 'total revenue':
        return 'text-green-600'
      case 'total orders':
        return 'text-blue-600'
      case 'total products':
        return 'text-purple-600'
      case 'total customers':
        return 'text-orange-600'
      default:
        return 'text-gray-600'
    }
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Dashboard Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertTriangle className="h-12 w-12 text-red-600 mx-auto mb-4" />
            <p className="text-red-600 font-medium">Error loading dashboard data</p>
            <p className="text-sm text-muted-foreground mt-2">{error}</p>
            <Button variant="outline" onClick={refetch} className="mt-4">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Dashboard Overview
            </CardTitle>
            <CardDescription>
              Key performance metrics and business insights
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">Last 7 days</SelectItem>
                <SelectItem value="30">Last 30 days</SelectItem>
                <SelectItem value="90">Last 90 days</SelectItem>
                <SelectItem value="365">Last year</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm" onClick={refetch} disabled={loading}>
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            {/* Loading skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-5 w-5 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                  <div className="h-8 w-24 bg-gray-200 rounded animate-pulse mb-2"></div>
                  <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Main Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {overviewStats.map((stat, index) => (
                <Card key={index} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-sm font-medium text-muted-foreground">
                        {stat.title}
                      </p>
                      <div className={getStatColor(stat.title)}>
                        {getStatIcon(stat.title)}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <p className="text-2xl font-bold">
                        {formatValue(stat.value, stat.format)}
                      </p>
                      {stat.growth !== 0 && (
                        <div className={`flex items-center gap-1 ${getTrendColor(stat.growth)}`}>
                          {getTrendIcon(stat.growth)}
                          <span className="text-sm font-medium">
                            {Math.abs(stat.growth).toFixed(1)}%
                          </span>
                          <span className="text-xs text-muted-foreground">
                            vs previous period
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Additional Metrics */}
            {metrics && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Performance Metrics */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Performance
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Avg Order Value</span>
                      <span className="font-medium">{formatCurrency(metrics.averageOrderValue)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Customer LTV</span>
                      <span className="font-medium">{formatCurrency(metrics.customerLifetimeValue)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Conversion Rate</span>
                      <span className="font-medium">{metrics.conversionRate.toFixed(2)}%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Inventory Turnover</span>
                      <span className="font-medium">{metrics.inventoryTurnover.toFixed(2)}</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Inventory Status */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      Inventory Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Low Stock Items</span>
                      <Badge variant={metrics.lowStockProducts.length > 0 ? 'secondary' : 'outline'}>
                        {metrics.lowStockProducts.length}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Out of Stock</span>
                      <Badge variant={metrics.outOfStockProducts.length > 0 ? 'destructive' : 'outline'}>
                        {metrics.outOfStockProducts.length}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Tracked Products</span>
                      <span className="font-medium">{metrics.totalProducts}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Categories</span>
                      <span className="font-medium">{metrics.topCategories.length}</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Customer Insights */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Customer Insights
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Recent Orders</span>
                      <span className="font-medium">{metrics.recentOrders.length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">New Customers</span>
                      <span className="font-medium">{metrics.recentCustomers.length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Customer Segments</span>
                      <span className="font-medium">{metrics.customerSegments.length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Customer Growth</span>
                      <div className={`flex items-center gap-1 ${getTrendColor(metrics.customerGrowth)}`}>
                        {getTrendIcon(metrics.customerGrowth)}
                        <span className="font-medium">{Math.abs(metrics.customerGrowth).toFixed(1)}%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Last Updated */}
            {lastUpdated && (
              <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>Last updated: {lastUpdated.toLocaleString()}</span>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
