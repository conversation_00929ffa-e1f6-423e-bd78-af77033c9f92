'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ResponsiveLine } from '@nivo/line'
import { TrendingUp, TrendingDown, DollarSign, RefreshCw } from 'lucide-react'
import { useState } from 'react'
import { useRevenueMetrics } from '@/lib/ecommerce/hooks/use-dashboard-analytics'
import { formatCurrency } from '@/lib/utils'

interface RevenueChartWidgetProps {
  className?: string
}

export function RevenueChartWidget({ className }: RevenueChartWidgetProps) {
  const [timeRange, setTimeRange] = useState('30')
  const { totalRevenue, revenueGrowth, averageOrderValue, revenueByDay, loading, error } = useRevenueMetrics(parseInt(timeRange))

  const chartData = [
    {
      id: 'revenue',
      color: 'hsl(142, 76%, 36%)',
      data: revenueByDay.map(day => ({
        x: day.date,
        y: day.revenue
      }))
    }
  ]

  const formatValue = (value: number) => formatCurrency(value)

  const getTrendIcon = () => {
    if (revenueGrowth > 0) return <TrendingUp className="h-4 w-4 text-green-600" />
    if (revenueGrowth < 0) return <TrendingDown className="h-4 w-4 text-red-600" />
    return <DollarSign className="h-4 w-4 text-gray-600" />
  }

  const getTrendColor = () => {
    if (revenueGrowth > 0) return 'text-green-600'
    if (revenueGrowth < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Revenue Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600">Error loading revenue data</p>
            <p className="text-sm text-muted-foreground mt-2">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Revenue Analytics
            </CardTitle>
            <CardDescription>
              Track revenue performance and trends
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">Last 7 days</SelectItem>
                <SelectItem value="30">Last 30 days</SelectItem>
                <SelectItem value="90">Last 90 days</SelectItem>
                <SelectItem value="365">Last year</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm" disabled={loading}>
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">Loading revenue data...</p>
            </div>
          </div>
        ) : (
          <>
            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold">{formatValue(totalRevenue)}</div>
                <div className="text-sm text-muted-foreground">Total Revenue</div>
                <div className={`flex items-center justify-center gap-1 mt-1 ${getTrendColor()}`}>
                  {getTrendIcon()}
                  <span className="text-sm font-medium">
                    {Math.abs(revenueGrowth).toFixed(1)}%
                  </span>
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{formatValue(averageOrderValue)}</div>
                <div className="text-sm text-muted-foreground">Avg Order Value</div>
                <Badge variant="secondary" className="mt-1">
                  AOV
                </Badge>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{revenueByDay.length}</div>
                <div className="text-sm text-muted-foreground">Active Days</div>
                <Badge variant="outline" className="mt-1">
                  {timeRange} days
                </Badge>
              </div>
            </div>

            {/* Chart */}
            <div className="h-80">
              {revenueByDay.length > 0 ? (
                <ResponsiveLine
                  data={chartData}
                  margin={{ top: 20, right: 20, bottom: 60, left: 80 }}
                  xScale={{ type: 'point' }}
                  yScale={{
                    type: 'linear',
                    min: 'auto',
                    max: 'auto',
                    stacked: false,
                    reverse: false
                  }}
                  yFormat={value => formatValue(Number(value))}
                  axisTop={null}
                  axisRight={null}
                  axisBottom={{
                    tickSize: 5,
                    tickPadding: 5,
                    tickRotation: -45,
                    legend: 'Date',
                    legendOffset: 50,
                    legendPosition: 'middle'
                  }}
                  axisLeft={{
                    tickSize: 5,
                    tickPadding: 5,
                    tickRotation: 0,
                    legend: 'Revenue (ZAR)',
                    legendOffset: -60,
                    legendPosition: 'middle',
                    format: value => formatValue(Number(value))
                  }}
                  pointSize={6}
                  pointColor={{ theme: 'background' }}
                  pointBorderWidth={2}
                  pointBorderColor={{ from: 'serieColor' }}
                  pointLabelYOffset={-12}
                  useMesh={true}
                  enableGridX={false}
                  enableGridY={true}
                  gridYValues={5}
                  curve="monotoneX"
                  animate={true}
                  motionConfig="gentle"
                  tooltip={({ point }) => (
                    <div className="bg-white p-3 border rounded-lg shadow-lg">
                      <div className="font-medium">{point.data.xFormatted}</div>
                      <div className="text-sm text-muted-foreground">
                        Revenue: {formatValue(Number(point.data.yFormatted))}
                      </div>
                    </div>
                  )}
                  theme={{
                    background: 'transparent',
                    text: {
                      fontSize: 12,
                      fill: 'hsl(var(--foreground))',
                      outlineWidth: 0,
                      outlineColor: 'transparent'
                    },
                    axis: {
                      domain: {
                        line: {
                          stroke: 'hsl(var(--border))',
                          strokeWidth: 1
                        }
                      },
                      legend: {
                        text: {
                          fontSize: 12,
                          fill: 'hsl(var(--foreground))'
                        }
                      },
                      ticks: {
                        line: {
                          stroke: 'hsl(var(--border))',
                          strokeWidth: 1
                        },
                        text: {
                          fontSize: 11,
                          fill: 'hsl(var(--muted-foreground))'
                        }
                      }
                    },
                    grid: {
                      line: {
                        stroke: 'hsl(var(--border))',
                        strokeWidth: 1,
                        strokeOpacity: 0.5
                      }
                    },
                    crosshair: {
                      line: {
                        stroke: 'hsl(var(--border))',
                        strokeWidth: 1,
                        strokeOpacity: 0.75,
                        strokeDasharray: '6 6'
                      }
                    }
                  }}
                />
              ) : (
                <div className="h-full flex items-center justify-center">
                  <div className="text-center">
                    <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">No revenue data available</p>
                    <p className="text-sm text-muted-foreground mt-2">
                      Revenue data will appear here once orders are processed
                    </p>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
