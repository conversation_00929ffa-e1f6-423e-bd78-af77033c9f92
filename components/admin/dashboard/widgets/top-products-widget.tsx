'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ResponsiveBar } from '@nivo/bar'
import { ResponsiveTreeMap } from '@nivo/treemap'
import { Package, TrendingUp, RefreshCw, Star, AlertTriangle } from 'lucide-react'
import { useState } from 'react'
import { useProductMetrics } from '@/lib/ecommerce/hooks/use-dashboard-analytics'
import { formatCurrency } from '@/lib/utils'
import Link from 'next/link'

interface TopProductsWidgetProps {
  className?: string
}

export function TopProductsWidget({ className }: TopProductsWidgetProps) {
  const [view, setView] = useState<'products' | 'categories' | 'inventory'>('products')
  const { 
    totalProducts, 
    topProducts, 
    topCategories, 
    lowStockProducts, 
    outOfStockProducts, 
    loading, 
    error 
  } = useProductMetrics(30)

  const barData = topProducts.slice(0, 8).map(product => ({
    product: product.name.length > 20 ? product.name.substring(0, 20) + '...' : product.name,
    sales: product.sales,
    revenue: product.revenue,
    fullName: product.name
  }))

  const treeMapData = {
    name: 'categories',
    children: topCategories.map(category => ({
      name: category.name,
      value: category.revenue,
      sales: category.sales,
      percentage: category.percentage
    }))
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Product Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600">Error loading product data</p>
            <p className="text-sm text-muted-foreground mt-2">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Product Analytics
            </CardTitle>
            <CardDescription>
              Track top performing products and inventory
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant={view === 'products' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setView('products')}
            >
              Products
            </Button>
            <Button
              variant={view === 'categories' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setView('categories')}
            >
              Categories
            </Button>
            <Button
              variant={view === 'inventory' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setView('inventory')}
            >
              Inventory
            </Button>
            <Button variant="outline" size="sm" disabled={loading}>
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">Loading product data...</p>
            </div>
          </div>
        ) : (
          <>
            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold">{totalProducts}</div>
                <div className="text-sm text-muted-foreground">Total Products</div>
                <Badge variant="outline" className="mt-1">Active</Badge>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{lowStockProducts.length}</div>
                <div className="text-sm text-muted-foreground">Low Stock</div>
                <Badge variant="secondary" className="mt-1">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  Alert
                </Badge>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{outOfStockProducts.length}</div>
                <div className="text-sm text-muted-foreground">Out of Stock</div>
                <Badge variant="destructive" className="mt-1">Critical</Badge>
              </div>
            </div>

            {/* Content based on view */}
            {view === 'products' && (
              <div className="space-y-4">
                {/* Top Products Chart */}
                <div className="h-64">
                  {barData.length > 0 ? (
                    <ResponsiveBar
                      data={barData}
                      keys={['sales']}
                      indexBy="product"
                      margin={{ top: 20, right: 20, bottom: 60, left: 60 }}
                      padding={0.3}
                      valueScale={{ type: 'linear' }}
                      indexScale={{ type: 'band', round: true }}
                      colors={{ scheme: 'blues' }}
                      borderColor={{ from: 'color', modifiers: [['darker', 1.6]] }}
                      axisTop={null}
                      axisRight={null}
                      axisBottom={{
                        tickSize: 5,
                        tickPadding: 5,
                        tickRotation: -45,
                        legend: 'Products',
                        legendPosition: 'middle',
                        legendOffset: 50
                      }}
                      axisLeft={{
                        tickSize: 5,
                        tickPadding: 5,
                        tickRotation: 0,
                        legend: 'Sales',
                        legendPosition: 'middle',
                        legendOffset: -40
                      }}
                      labelSkipWidth={12}
                      labelSkipHeight={12}
                      labelTextColor={{ from: 'color', modifiers: [['darker', 1.6]] }}
                      animate={true}
                      motionConfig="gentle"
                      tooltip={({ data }) => (
                        <div className="bg-white p-3 border rounded-lg shadow-lg">
                          <div className="font-medium">{data.fullName}</div>
                          <div className="text-sm text-muted-foreground">
                            Sales: {data.sales} units
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Revenue: {formatCurrency(data.revenue)}
                          </div>
                        </div>
                      )}
                      theme={{
                        background: 'transparent',
                        text: {
                          fontSize: 12,
                          fill: 'hsl(var(--foreground))'
                        },
                        axis: {
                          domain: {
                            line: {
                              stroke: 'hsl(var(--border))',
                              strokeWidth: 1
                            }
                          },
                          legend: {
                            text: {
                              fontSize: 12,
                              fill: 'hsl(var(--foreground))'
                            }
                          },
                          ticks: {
                            line: {
                              stroke: 'hsl(var(--border))',
                              strokeWidth: 1
                            },
                            text: {
                              fontSize: 11,
                              fill: 'hsl(var(--muted-foreground))'
                            }
                          }
                        },
                        grid: {
                          line: {
                            stroke: 'hsl(var(--border))',
                            strokeWidth: 1,
                            strokeOpacity: 0.5
                          }
                        }
                      }}
                    />
                  ) : (
                    <div className="h-full flex items-center justify-center">
                      <div className="text-center">
                        <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground">No product sales data</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Top Products List */}
                <div className="space-y-2">
                  <h4 className="font-medium flex items-center gap-2">
                    <Star className="h-4 w-4" />
                    Top Performing Products
                  </h4>
                  {topProducts.slice(0, 5).map((product, index) => (
                    <div key={product.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline">#{index + 1}</Badge>
                        <div>
                          <div className="font-medium">{product.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {product.sales} units sold
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{formatCurrency(product.revenue)}</div>
                        <Link href={`/admin/products/${product.id}`}>
                          <Button variant="ghost" size="sm">View</Button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {view === 'categories' && (
              <div className="space-y-4">
                {/* Categories TreeMap */}
                <div className="h-64">
                  {topCategories.length > 0 ? (
                    <ResponsiveTreeMap
                      data={treeMapData}
                      identity="name"
                      value="value"
                      margin={{ top: 10, right: 10, bottom: 10, left: 10 }}
                      labelSkipSize={12}
                      labelTextColor={{ from: 'color', modifiers: [['darker', 1.2]] }}
                      parentLabelTextColor={{ from: 'color', modifiers: [['darker', 2]] }}
                      borderColor={{ from: 'color', modifiers: [['darker', 0.1]] }}
                      colors={{ scheme: 'spectral' }}
                      animate={true}
                      motionConfig="gentle"
                      tooltip={({ node }) => (
                        <div className="bg-white p-3 border rounded-lg shadow-lg">
                          <div className="font-medium">{node.data.name}</div>
                          <div className="text-sm text-muted-foreground">
                            Revenue: {formatCurrency(node.data.value)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Sales: {node.data.sales} units
                          </div>
                        </div>
                      )}
                    />
                  ) : (
                    <div className="h-full flex items-center justify-center">
                      <div className="text-center">
                        <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground">No category data</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Categories List */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {topCategories.map((category, index) => (
                    <div key={category.name} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline">#{index + 1}</Badge>
                        <span className="text-sm text-muted-foreground">
                          {category.percentage.toFixed(1)}%
                        </span>
                      </div>
                      <div className="font-medium">{category.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {category.sales} sales • {formatCurrency(category.revenue)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {view === 'inventory' && (
              <div className="space-y-4">
                {/* Inventory Alerts */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Low Stock */}
                  <div>
                    <h4 className="font-medium flex items-center gap-2 mb-3">
                      <AlertTriangle className="h-4 w-4 text-orange-600" />
                      Low Stock ({lowStockProducts.length})
                    </h4>
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {lowStockProducts.slice(0, 10).map(product => (
                        <div key={product.id} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex-1">
                            <div className="font-medium text-sm">{product.name}</div>
                            <div className="text-xs text-muted-foreground">
                              Stock: {product.stock} / Threshold: {product.threshold}
                            </div>
                          </div>
                          <Badge variant="secondary" className="text-orange-600">
                            {product.stock}
                          </Badge>
                        </div>
                      ))}
                      {lowStockProducts.length === 0 && (
                        <p className="text-sm text-muted-foreground text-center py-4">
                          No low stock alerts
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Out of Stock */}
                  <div>
                    <h4 className="font-medium flex items-center gap-2 mb-3">
                      <Package className="h-4 w-4 text-red-600" />
                      Out of Stock ({outOfStockProducts.length})
                    </h4>
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {outOfStockProducts.slice(0, 10).map(product => (
                        <div key={product.id} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex-1">
                            <div className="font-medium text-sm">{product.name}</div>
                            <div className="text-xs text-muted-foreground">
                              Last sold: {product.lastSold ? new Date(product.lastSold).toLocaleDateString() : 'Never'}
                            </div>
                          </div>
                          <Badge variant="destructive">
                            0
                          </Badge>
                        </div>
                      ))}
                      {outOfStockProducts.length === 0 && (
                        <p className="text-sm text-muted-foreground text-center py-4">
                          No out of stock products
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
