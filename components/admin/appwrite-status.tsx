'use client'

import { useAppwriteStatus } from '@/lib/appwrite/hooks'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { CheckCircle, XCircle, AlertCircle, Loader2, ExternalLink } from 'lucide-react'
import { AppwriteStatus } from '@/lib/appwrite'

export function AppwriteStatusCard() {
  const { status, loading, isConfigured, isConnected, hasError } = useAppwriteStatus()

  const getStatusIcon = () => {
    if (loading) return <Loader2 className="h-4 w-4 animate-spin" />
    
    switch (status) {
      case AppwriteStatus.CONNECTED:
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case AppwriteStatus.CONFIGURED:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case AppwriteStatus.ERROR:
        return <XCircle className="h-4 w-4 text-red-500" />
      case AppwriteStatus.NOT_CONFIGURED:
      default:
        return <XCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusText = () => {
    if (loading) return 'Checking...'
    
    switch (status) {
      case AppwriteStatus.CONNECTED:
        return 'Connected'
      case AppwriteStatus.CONFIGURED:
        return 'Configured'
      case AppwriteStatus.ERROR:
        return 'Error'
      case AppwriteStatus.NOT_CONFIGURED:
      default:
        return 'Not Configured'
    }
  }

  const getStatusVariant = (): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case AppwriteStatus.CONNECTED:
        return 'default'
      case AppwriteStatus.CONFIGURED:
        return 'secondary'
      case AppwriteStatus.ERROR:
        return 'destructive'
      case AppwriteStatus.NOT_CONFIGURED:
      default:
        return 'outline'
    }
  }

  const getDescription = () => {
    switch (status) {
      case AppwriteStatus.CONNECTED:
        return 'Appwrite is properly configured and connected. File uploads and database operations are available.'
      case AppwriteStatus.CONFIGURED:
        return 'Appwrite is configured but connection could not be verified. Some features may be limited.'
      case AppwriteStatus.ERROR:
        return 'There was an error connecting to Appwrite. Check your configuration and network connection.'
      case AppwriteStatus.NOT_CONFIGURED:
      default:
        return 'Appwrite is not configured. File uploads will use local preview mode.'
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <CardTitle className="text-lg">Appwrite Integration</CardTitle>
          </div>
          <Badge variant={getStatusVariant()}>
            {getStatusText()}
          </Badge>
        </div>
        <CardDescription>
          {getDescription()}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Feature Status */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Available Features</h4>
              <div className="space-y-1">
                <div className="flex items-center space-x-2 text-sm">
                  {isConnected ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <XCircle className="h-3 w-3 text-gray-400" />
                  )}
                  <span className={isConnected ? 'text-green-700' : 'text-gray-500'}>
                    File Storage
                  </span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  {isConnected ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <XCircle className="h-3 w-3 text-gray-400" />
                  )}
                  <span className={isConnected ? 'text-green-700' : 'text-gray-500'}>
                    Database Operations
                  </span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  {isConnected ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <XCircle className="h-3 w-3 text-gray-400" />
                  )}
                  <span className={isConnected ? 'text-green-700' : 'text-gray-500'}>
                    Real-time Updates
                  </span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  {isConnected ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <XCircle className="h-3 w-3 text-gray-400" />
                  )}
                  <span className={isConnected ? 'text-green-700' : 'text-gray-500'}>
                    User Authentication
                  </span>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Fallback Features</h4>
              <div className="space-y-1">
                <div className="flex items-center space-x-2 text-sm">
                  <CheckCircle className="h-3 w-3 text-blue-500" />
                  <span className="text-blue-700">Local Image Preview</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <CheckCircle className="h-3 w-3 text-blue-500" />
                  <span className="text-blue-700">Prisma Database</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <CheckCircle className="h-3 w-3 text-blue-500" />
                  <span className="text-blue-700">NextAuth Authentication</span>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2 pt-4 border-t">
            {!isConfigured && (
              <Button variant="outline" size="sm" asChild>
                <a 
                  href="/APPWRITE_SETUP.md" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center space-x-1"
                >
                  <span>Setup Guide</span>
                  <ExternalLink className="h-3 w-3" />
                </a>
              </Button>
            )}
            
            <Button variant="outline" size="sm" asChild>
              <a 
                href="https://cloud.appwrite.io" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center space-x-1"
              >
                <span>Appwrite Console</span>
                <ExternalLink className="h-3 w-3" />
              </a>
            </Button>
          </div>

          {/* Configuration Hint */}
          {!isConfigured && (
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
              <p className="text-sm text-blue-800">
                <strong>Tip:</strong> To enable Appwrite features, add your Appwrite configuration 
                to your environment variables. See the setup guide for detailed instructions.
              </p>
            </div>
          )}

          {hasError && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-800">
                <strong>Error:</strong> Unable to connect to Appwrite. Please check your 
                configuration and ensure your Appwrite instance is accessible.
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export function AppwriteStatusBadge() {
  const { status, loading } = useAppwriteStatus()

  if (loading) {
    return (
      <Badge variant="outline" className="flex items-center space-x-1">
        <Loader2 className="h-3 w-3 animate-spin" />
        <span>Checking Appwrite...</span>
      </Badge>
    )
  }

  const getVariant = (): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case AppwriteStatus.CONNECTED:
        return 'default'
      case AppwriteStatus.CONFIGURED:
        return 'secondary'
      case AppwriteStatus.ERROR:
        return 'destructive'
      case AppwriteStatus.NOT_CONFIGURED:
      default:
        return 'outline'
    }
  }

  const getIcon = () => {
    switch (status) {
      case AppwriteStatus.CONNECTED:
        return <CheckCircle className="h-3 w-3" />
      case AppwriteStatus.CONFIGURED:
        return <AlertCircle className="h-3 w-3" />
      case AppwriteStatus.ERROR:
        return <XCircle className="h-3 w-3" />
      case AppwriteStatus.NOT_CONFIGURED:
      default:
        return <XCircle className="h-3 w-3" />
    }
  }

  const getText = () => {
    switch (status) {
      case AppwriteStatus.CONNECTED:
        return 'Appwrite Connected'
      case AppwriteStatus.CONFIGURED:
        return 'Appwrite Configured'
      case AppwriteStatus.ERROR:
        return 'Appwrite Error'
      case AppwriteStatus.NOT_CONFIGURED:
      default:
        return 'Appwrite Not Configured'
    }
  }

  return (
    <Badge variant={getVariant()} className="flex items-center space-x-1">
      {getIcon()}
      <span>{getText()}</span>
    </Badge>
  )
}
