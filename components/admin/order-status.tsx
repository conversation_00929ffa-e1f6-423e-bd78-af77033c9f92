'use client'

import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import {
  Clock,
  Package,
  Truck,
  CheckCircle,
  XCircle,
  AlertTriangle,
  MoreHorizontal,
  Mail,
  Phone,
  MessageSquare,
  RefreshCw,
  CreditCard,
  ShoppingCart
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { Order } from '@/lib/ecommerce/types'
import { useZarFormatter } from '@/components/admin/zar-price-input'

interface OrderStatusProps {
  order: Order
  onStatusChange?: (orderId: string, newStatus: Order['status']) => Promise<void>
  onPaymentStatusChange?: (orderId: string, newStatus: Order['paymentStatus']) => Promise<void>
  showActions?: boolean
  compact?: boolean
}

const ORDER_STATUSES = [
  { value: 'pending', label: 'Pending', icon: Clock, color: 'yellow' },
  { value: 'confirmed', label: 'Confirmed', icon: CheckCircle, color: 'blue' },
  { value: 'processing', label: 'Processing', icon: Package, color: 'blue' },
  { value: 'shipped', label: 'Shipped', icon: Truck, color: 'purple' },
  { value: 'delivered', label: 'Delivered', icon: CheckCircle, color: 'green' },
  { value: 'cancelled', label: 'Cancelled', icon: XCircle, color: 'red' },
  { value: 'refunded', label: 'Refunded', icon: RefreshCw, color: 'orange' }
] as const

const PAYMENT_STATUSES = [
  { value: 'pending', label: 'Pending', icon: Clock, color: 'yellow' },
  { value: 'paid', label: 'Paid', icon: CheckCircle, color: 'green' },
  { value: 'failed', label: 'Failed', icon: XCircle, color: 'red' },
  { value: 'refunded', label: 'Refunded', icon: RefreshCw, color: 'orange' },
  { value: 'partially_refunded', label: 'Partially Refunded', icon: RefreshCw, color: 'orange' }
] as const

const ORDER_PROGRESS = {
  'pending': 10,
  'confirmed': 25,
  'processing': 50,
  'shipped': 75,
  'delivered': 100,
  'cancelled': 0,
  'refunded': 0
} as const

export function OrderStatus({ order, onStatusChange, onPaymentStatusChange, showActions = true, compact = false }: OrderStatusProps) {
  const [isUpdating, setIsUpdating] = useState(false)
  const [isUpdatingPayment, setIsUpdatingPayment] = useState(false)
  const { formatPrice } = useZarFormatter()

  const currentStatus = ORDER_STATUSES.find(status => status.value === order.status)
  const currentPaymentStatus = PAYMENT_STATUSES.find(status => status.value === order.paymentStatus)
  const StatusIcon = currentStatus?.icon || Clock
  const PaymentIcon = currentPaymentStatus?.icon || CreditCard

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'delivered':
        return 'default'
      case 'processing':
      case 'confirmed':
        return 'secondary'
      case 'pending':
      case 'shipped':
        return 'outline'
      case 'cancelled':
      case 'refunded':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  const getPaymentBadgeVariant = (status: string) => {
    switch (status) {
      case 'paid':
        return 'default'
      case 'pending':
        return 'outline'
      case 'failed':
        return 'destructive'
      case 'refunded':
      case 'partially_refunded':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  const getStatusColor = (status: string) => {
    const statusConfig = ORDER_STATUSES.find(s => s.value === status)
    switch (statusConfig?.color) {
      case 'green':
        return 'text-green-600'
      case 'blue':
        return 'text-blue-600'
      case 'yellow':
        return 'text-yellow-600'
      case 'orange':
        return 'text-orange-600'
      case 'red':
        return 'text-red-600'
      case 'purple':
        return 'text-purple-600'
      default:
        return 'text-gray-600'
    }
  }

  const handleStatusChange = async (newStatus: string) => {
    if (!onStatusChange) return

    try {
      setIsUpdating(true)
      await onStatusChange(order.id, newStatus as Order['status'])
    } catch (error) {
      console.error('Error updating order status:', error)
    } finally {
      setIsUpdating(false)
    }
  }

  const handlePaymentStatusChange = async (newStatus: string) => {
    if (!onPaymentStatusChange) return

    try {
      setIsUpdatingPayment(true)
      await onPaymentStatusChange(order.id, newStatus as Order['paymentStatus'])
    } catch (error) {
      console.error('Error updating payment status:', error)
    } finally {
      setIsUpdatingPayment(false)
    }
  }

  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    return dateObj.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (compact) {
    return (
      <div className="flex items-center space-x-2">
        <StatusIcon className={cn("h-4 w-4", getStatusColor(order.status))} />
        <Badge variant={getStatusBadgeVariant(order.status)}>
          {currentStatus?.label || order.status}
        </Badge>
        <PaymentIcon className={cn("h-4 w-4", getStatusColor(order.paymentStatus))} />
        <Badge variant={getPaymentBadgeVariant(order.paymentStatus)}>
          {currentPaymentStatus?.label || order.paymentStatus}
        </Badge>
        {showActions && onStatusChange && (
          <Select value={order.status} onValueChange={handleStatusChange} disabled={isUpdating}>
            <SelectTrigger className="w-[140px] h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {ORDER_STATUSES.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  <div className="flex items-center space-x-2">
                    <status.icon className="h-4 w-4" />
                    <span>{status.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <StatusIcon className={cn("h-5 w-5", getStatusColor(order.status))} />
            <CardTitle className="text-lg">Order #{order.orderNumber}</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={getStatusBadgeVariant(order.status)} className="text-sm">
              {currentStatus?.label || order.status}
            </Badge>
            <Badge variant={getPaymentBadgeVariant(order.paymentStatus)} className="text-sm">
              <CreditCard className="mr-1 h-3 w-3" />
              {currentPaymentStatus?.label || order.paymentStatus}
            </Badge>
            {showActions && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Mail className="mr-2 h-4 w-4" />
                    Email Customer
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Phone className="mr-2 h-4 w-4" />
                    Call Customer
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Add Note
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
        <CardDescription>
          Placed on {formatDate(order.createdAt)} • Last updated {formatDate(order.updatedAt)}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Order Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Order Progress</span>
            <span>{ORDER_PROGRESS[order.status as keyof typeof ORDER_PROGRESS]}%</span>
          </div>
          <Progress value={ORDER_PROGRESS[order.status as keyof typeof ORDER_PROGRESS]} />
        </div>

        {/* Customer Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">Customer</h4>
            <p>{order.customer?.firstName} {order.customer?.lastName}</p>
            <p className="text-muted-foreground">{order.customer?.email}</p>
            {order.customer?.phone && (
              <p className="text-muted-foreground">{order.customer?.phone}</p>
            )}
          </div>

          <div>
            <h4 className="font-medium mb-2">Shipping Address</h4>
            <p>{order.shippingAddress.address1}</p>
            {order.shippingAddress.address2 && <p>{order.shippingAddress.address2}</p>}
            <p>{order.shippingAddress.city}, {order.shippingAddress.province} {order.shippingAddress.postalCode}</p>
            <p>{order.shippingAddress.country}</p>
          </div>
        </div>

        {/* Order Items Summary */}
        <div>
          <h4 className="font-medium mb-2">Items ({order.itemCount})</h4>
          <div className="space-y-2">
            {order.items.slice(0, 3).map((item, index) => (
              <div key={index} className="flex items-center justify-between text-sm">
                <span>{item.productTitle} × {item.quantity}</span>
                <span>{formatPrice(item.unitPrice.amount)}</span>
              </div>
            ))}
            {order.items.length > 3 && (
              <p className="text-sm text-muted-foreground">
                +{order.items.length - 3} more items
              </p>
            )}
          </div>
        </div>

        {/* Payment Information */}
        <div className="flex items-center justify-between text-sm border-t pt-4">
          <div>
            <span className="font-medium">Payment Method: </span>
            <span>{order.paymentMethod?.type || 'Not specified'}</span>
          </div>
          <div className="text-right">
            <div className="font-medium">Total: {formatPrice(order.total.amount)}</div>
            <div className="text-muted-foreground">
              {order.paymentStatus === 'paid' ? 'Paid' : 'Pending Payment'}
            </div>
          </div>
        </div>

        {/* Status Change Actions */}
        {showActions && (onStatusChange || onPaymentStatusChange) && (
          <div className="border-t pt-4 space-y-3">
            {onStatusChange && (
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Update Status:</span>
                <Select value={order.status} onValueChange={handleStatusChange} disabled={isUpdating}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {ORDER_STATUSES.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        <div className="flex items-center space-x-2">
                          <status.icon className="h-4 w-4" />
                          <span>{status.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {onPaymentStatusChange && (
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Payment Status:</span>
                <Select value={order.paymentStatus} onValueChange={handlePaymentStatusChange} disabled={isUpdatingPayment}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {PAYMENT_STATUSES.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        <div className="flex items-center space-x-2">
                          <status.icon className="h-4 w-4" />
                          <span>{status.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        )}

        {/* Status-specific Actions */}
        {order.status === 'processing' && (
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="flex items-center space-x-2 text-blue-800">
              <Package className="h-4 w-4" />
              <span className="text-sm font-medium">Ready to Ship</span>
            </div>
            <p className="text-xs text-blue-600 mt-1">
              This order is being prepared for shipment. Update to shipped once ready.
            </p>
          </div>
        )}

        {order.status === 'shipped' && (
          <div className="bg-purple-50 p-3 rounded-lg">
            <div className="flex items-center space-x-2 text-purple-800">
              <Truck className="h-4 w-4" />
              <span className="text-sm font-medium">Order Shipped</span>
            </div>
            <p className="text-xs text-purple-600 mt-1">
              Order is on its way to the customer. Update to delivered once confirmed.
            </p>
          </div>
        )}

        {order.status === 'pending' && (
          <div className="bg-yellow-50 p-3 rounded-lg">
            <div className="flex items-center space-x-2 text-yellow-800">
              <Clock className="h-4 w-4" />
              <span className="text-sm font-medium">Awaiting Confirmation</span>
            </div>
            <p className="text-xs text-yellow-600 mt-1">
              Order is pending confirmation. Check payment status and confirm order.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
