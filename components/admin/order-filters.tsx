'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Calendar } from '@/components/ui/calendar'
import { 
  Filter,
  X,
  Calendar as CalendarIcon,
  Search,
  RotateCcw,
  Download
} from 'lucide-react'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'

export interface OrderFilters {
  search?: string
  status?: string[]
  paymentStatus?: string[]
  dateRange?: {
    from?: Date
    to?: Date
  }
  customerEmail?: string
  orderNumber?: string
  minAmount?: number
  maxAmount?: number
  tags?: string[]
  shippingMethod?: string
}

interface OrderFiltersProps {
  filters: OrderFilters
  onFiltersChange: (filters: OrderFilters) => void
  onExport?: () => void
  isLoading?: boolean
}

const ORDER_STATUSES = [
  { value: 'pending', label: 'Pending' },
  { value: 'confirmed', label: 'Confirmed' },
  { value: 'processing', label: 'Processing' },
  { value: 'shipped', label: 'Shipped' },
  { value: 'delivered', label: 'Delivered' },
  { value: 'cancelled', label: 'Cancelled' },
  { value: 'refunded', label: 'Refunded' }
]

const PAYMENT_STATUSES = [
  { value: 'pending', label: 'Pending' },
  { value: 'paid', label: 'Paid' },
  { value: 'failed', label: 'Failed' },
  { value: 'refunded', label: 'Refunded' },
  { value: 'partially_refunded', label: 'Partially Refunded' }
]

const SHIPPING_METHODS = [
  { value: 'standard', label: 'Standard Shipping' },
  { value: 'express', label: 'Express Shipping' },
  { value: 'overnight', label: 'Overnight Shipping' },
  { value: 'pickup', label: 'Store Pickup' },
  { value: 'free', label: 'Free Shipping' }
]

export function OrderFilters({ filters, onFiltersChange, onExport, isLoading = false }: OrderFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({
    from: filters.dateRange?.from,
    to: filters.dateRange?.to
  })

  const updateFilter = (key: keyof OrderFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const toggleStatus = (status: string) => {
    const currentStatuses = filters.status || []
    const newStatuses = currentStatuses.includes(status)
      ? currentStatuses.filter(s => s !== status)
      : [...currentStatuses, status]
    
    updateFilter('status', newStatuses.length > 0 ? newStatuses : undefined)
  }

  const togglePaymentStatus = (status: string) => {
    const currentStatuses = filters.paymentStatus || []
    const newStatuses = currentStatuses.includes(status)
      ? currentStatuses.filter(s => s !== status)
      : [...currentStatuses, status]
    
    updateFilter('paymentStatus', newStatuses.length > 0 ? newStatuses : undefined)
  }

  const clearFilters = () => {
    onFiltersChange({})
    setDateRange({ from: undefined, to: undefined })
  }

  const hasActiveFilters = () => {
    return Object.keys(filters).some(key => {
      const value = filters[key as keyof OrderFilters]
      if (Array.isArray(value)) return value.length > 0
      if (typeof value === 'object' && value !== null) return Object.keys(value).length > 0
      return value !== undefined && value !== ''
    })
  }

  const getActiveFilterCount = () => {
    let count = 0
    if (filters.search) count++
    if (filters.status?.length) count++
    if (filters.paymentStatus?.length) count++
    if (filters.dateRange?.from || filters.dateRange?.to) count++
    if (filters.customerEmail) count++
    if (filters.orderNumber) count++
    if (filters.minAmount || filters.maxAmount) count++
    if (filters.tags?.length) count++
    if (filters.shippingMethod) count++
    return count
  }

  useEffect(() => {
    if (dateRange.from || dateRange.to) {
      updateFilter('dateRange', dateRange)
    }
  }, [dateRange])

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Filter className="mr-2 h-5 w-5" />
              Order Filters
              {hasActiveFilters() && (
                <Badge variant="secondary" className="ml-2">
                  {getActiveFilterCount()}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Filter and search orders by various criteria
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            {onExport && (
              <Button variant="outline" size="sm" onClick={onExport} disabled={isLoading}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            )}
            {hasActiveFilters() && (
              <Button variant="outline" size="sm" onClick={clearFilters}>
                <RotateCcw className="mr-2 h-4 w-4" />
                Clear
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
            >
              {showAdvanced ? 'Simple' : 'Advanced'}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Basic Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="search">Search</Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                placeholder="Search orders..."
                value={filters.search || ''}
                onChange={(e) => updateFilter('search', e.target.value || undefined)}
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <Label>Order Status</Label>
            <div className="flex flex-wrap gap-1 mt-1">
              {ORDER_STATUSES.map((status) => (
                <Badge
                  key={status.value}
                  variant={filters.status?.includes(status.value) ? 'default' : 'outline'}
                  className="cursor-pointer text-xs"
                  onClick={() => toggleStatus(status.value)}
                >
                  {status.label}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <Label>Payment Status</Label>
            <div className="flex flex-wrap gap-1 mt-1">
              {PAYMENT_STATUSES.map((status) => (
                <Badge
                  key={status.value}
                  variant={filters.paymentStatus?.includes(status.value) ? 'default' : 'outline'}
                  className="cursor-pointer text-xs"
                  onClick={() => togglePaymentStatus(status.value)}
                >
                  {status.label}
                </Badge>
              ))}
            </div>
          </div>
        </div>

        {/* Date Range */}
        <div>
          <Label>Date Range</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !dateRange.from && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "LLL dd, y")} -{" "}
                      {format(dateRange.to, "LLL dd, y")}
                    </>
                  ) : (
                    format(dateRange.from, "LLL dd, y")
                  )
                ) : (
                  <span>Pick a date range</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange.from}
                selected={{ from: dateRange.from, to: dateRange.to }}
                onSelect={(range) => setDateRange({ from: range?.from, to: range?.to })}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Advanced Filters */}
        {showAdvanced && (
          <div className="space-y-4 pt-4 border-t">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="customerEmail">Customer Email</Label>
                <Input
                  id="customerEmail"
                  placeholder="<EMAIL>"
                  value={filters.customerEmail || ''}
                  onChange={(e) => updateFilter('customerEmail', e.target.value || undefined)}
                />
              </div>

              <div>
                <Label htmlFor="orderNumber">Order Number</Label>
                <Input
                  id="orderNumber"
                  placeholder="ORD240101001"
                  value={filters.orderNumber || ''}
                  onChange={(e) => updateFilter('orderNumber', e.target.value || undefined)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="minAmount">Min Amount (ZAR)</Label>
                <Input
                  id="minAmount"
                  type="number"
                  placeholder="0"
                  value={filters.minAmount || ''}
                  onChange={(e) => updateFilter('minAmount', e.target.value ? parseFloat(e.target.value) : undefined)}
                />
              </div>

              <div>
                <Label htmlFor="maxAmount">Max Amount (ZAR)</Label>
                <Input
                  id="maxAmount"
                  type="number"
                  placeholder="10000"
                  value={filters.maxAmount || ''}
                  onChange={(e) => updateFilter('maxAmount', e.target.value ? parseFloat(e.target.value) : undefined)}
                />
              </div>

              <div>
                <Label htmlFor="shippingMethod">Shipping Method</Label>
                <Select
                  value={filters.shippingMethod || ''}
                  onValueChange={(value) => updateFilter('shippingMethod', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Any method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Any method</SelectItem>
                    {SHIPPING_METHODS.map((method) => (
                      <SelectItem key={method.value} value={method.value}>
                        {method.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
