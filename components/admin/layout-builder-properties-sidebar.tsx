'use client'

import React, { useState } from 'react'
import { useLayoutBuilder } from '@/lib/layout-builder/context'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import {
  Settings,
  Palette,
  Monitor,
  Tablet,
  Smartphone,
  Eye,
  EyeOff,
  ChevronDown,
  ChevronRight,
  X,
  Layers,
  Type,
  Layout,
  Move,
  BorderAll,
  Shadow,
  Paintbrush,
  Brain,
  <PERSON><PERSON><PERSON>,
  <PERSON>d2,
  Lightbulb,
  Send,
  Loader2
} from 'lucide-react'
import { cn } from '@/lib/utils'

export function LayoutBuilderPropertiesSidebar() {
  const { 
    state, 
    updateLayout, 
    updateSection, 
    updateBlock, 
    setDevicePreview 
  } = useLayoutBuilder()

  const { 
    layout, 
    selectedSectionId, 
    selectedBlockId, 
    devicePreview 
  } = state

  const [activeTab, setActiveTab] = useState<'properties' | 'styling' | 'responsive' | 'ai'>('properties')
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    general: true,
    layout: true,
    spacing: false,
    colors: false,
    typography: false,
    borders: false,
    shadows: false,
    ai: true
  })

  // AI-powered property suggestions state
  const [showAIAssistant, setShowAIAssistant] = useState(false)
  const [aiPrompt, setAiPrompt] = useState('')
  const [isGeneratingAI, setIsGeneratingAI] = useState(false)
  const [aiSuggestions, setAiSuggestions] = useState<any[]>([])
  const [showNaturalLanguageEditor, setShowNaturalLanguageEditor] = useState(false)

  // Get selected section or block
  const selectedSection = selectedSectionId 
    ? Object.values(layout.structure).find(section => section?.id === selectedSectionId)
    : null

  const selectedBlock = selectedBlockId && selectedSection
    ? selectedSection.blocks.find(block => block.id === selectedBlockId)
    : null

  const hasSelection = selectedSection || selectedBlock

  // Toggle expanded sections
  const toggleSection = (sectionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey]
    }))
  }

  // AI-powered property suggestions handlers
  const handleAIPropertySuggestion = async () => {
    if (!aiPrompt.trim()) return

    setIsGeneratingAI(true)
    try {
      // Mock AI suggestions - in production, this would call an AI service
      const mockSuggestions = [
        {
          property: 'layout',
          value: 'flex',
          reason: 'Flex layout provides better responsive behavior for this section type'
        },
        {
          property: 'alignment',
          value: 'center',
          reason: 'Center alignment improves visual balance and user focus'
        },
        {
          property: 'spacing',
          value: 'large',
          reason: 'Increased spacing enhances readability and modern appearance'
        }
      ]

      setAiSuggestions(mockSuggestions)
    } catch (error) {
      console.error('AI suggestion error:', error)
    } finally {
      setIsGeneratingAI(false)
    }
  }

  const handleApplyAISuggestion = (suggestion: any) => {
    try {
      if (selectedSection && suggestion.property === 'layout') {
        updateSection(selectedSection.id, {
          configuration: {
            ...selectedSection.configuration,
            layout: suggestion.value
          }
        })
      } else if (selectedSection && suggestion.property === 'alignment') {
        updateSection(selectedSection.id, {
          configuration: {
            ...selectedSection.configuration,
            alignment: suggestion.value
          }
        })
      }
      // Add more property mappings as needed
    } catch (error) {
      console.error('Apply suggestion error:', error)
    }
  }

  const handleNaturalLanguageEdit = async () => {
    if (!aiPrompt.trim()) return

    setIsGeneratingAI(true)
    try {
      // Mock natural language processing
      const updates: any = {}

      if (aiPrompt.toLowerCase().includes('center') || aiPrompt.toLowerCase().includes('middle')) {
        updates.alignment = 'center'
      }

      if (aiPrompt.toLowerCase().includes('flex') || aiPrompt.toLowerCase().includes('flexible')) {
        updates.layout = 'flex'
      }

      if (aiPrompt.toLowerCase().includes('grid')) {
        updates.layout = 'grid'
      }

      if (selectedSection && Object.keys(updates).length > 0) {
        updateSection(selectedSection.id, {
          configuration: {
            ...selectedSection.configuration,
            ...updates
          }
        })
      }

      setAiPrompt('')
    } catch (error) {
      console.error('Natural language edit error:', error)
    } finally {
      setIsGeneratingAI(false)
    }
  }

  // Render device preview buttons
  const renderDeviceButtons = () => (
    <div className="flex items-center gap-1 p-1 bg-gray-100 rounded-lg">
      {(['desktop', 'tablet', 'mobile'] as const).map((device) => {
        const Icon = device === 'desktop' ? Monitor : device === 'tablet' ? Tablet : Smartphone
        return (
          <Button
            key={device}
            variant={devicePreview === device ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setDevicePreview(device)}
            className="h-8 w-8 p-0"
          >
            <Icon className="h-3 w-3" />
          </Button>
        )
      })}
    </div>
  )

  // Render collapsible section
  const renderCollapsibleSection = (
    key: string,
    title: string,
    icon: React.ComponentType<any>,
    children: React.ReactNode
  ) => {
    const Icon = icon
    const isExpanded = expandedSections[key]

    return (
      <div className="border rounded-lg">
        <Button
          variant="ghost"
          onClick={() => toggleSection(key)}
          className="w-full justify-between p-3 h-auto font-medium"
        >
          <div className="flex items-center gap-2">
            <Icon className="h-4 w-4" />
            {title}
          </div>
          {isExpanded ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </Button>
        {isExpanded && (
          <div className="p-3 pt-0 space-y-3">
            {children}
          </div>
        )}
      </div>
    )
  }

  if (!hasSelection) {
    return (
      <div className="w-80 border-l bg-white flex flex-col">
        <div className="p-4 border-b">
          <div className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-gray-600" />
            <h2 className="font-semibold">Properties</h2>
          </div>
        </div>
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Layers className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="font-medium text-gray-900 mb-2">No Selection</h3>
            <p className="text-sm text-muted-foreground">
              Select a section or block to edit its properties
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-80 border-l bg-white flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-gray-600" />
            <h2 className="font-semibold">Properties</h2>
          </div>
          {renderDeviceButtons()}
        </div>

        {/* Selection Info */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {selectedBlock ? 'Block' : 'Section'}
            </Badge>
            <span className="text-sm font-medium truncate">
              {selectedBlock?.name || selectedSection?.name}
            </span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowNaturalLanguageEditor(!showNaturalLanguageEditor)}
            className="text-xs h-7"
          >
            <Brain className="h-3 w-3 mr-1" />
            AI
          </Button>
        </div>

        {/* AI Natural Language Editor */}
        {showNaturalLanguageEditor && (
          <div className="mt-3 p-3 bg-purple-50 rounded-lg border">
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="h-4 w-4 text-purple-500" />
              <span className="text-sm font-medium">AI Property Assistant</span>
            </div>

            <div className="space-y-2">
              <Textarea
                value={aiPrompt}
                onChange={(e) => setAiPrompt(e.target.value)}
                placeholder="Describe how you want to modify this layout... e.g., 'Make this section centered and use flex layout'"
                className="min-h-[60px] text-sm"
                disabled={isGeneratingAI}
              />

              <div className="flex gap-2">
                <Button
                  onClick={handleNaturalLanguageEdit}
                  disabled={isGeneratingAI || !aiPrompt.trim()}
                  size="sm"
                  className="flex-1"
                >
                  {isGeneratingAI ? (
                    <>
                      <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-3 w-3 mr-2" />
                      Apply AI Edit
                    </>
                  )}
                </Button>

                <Button
                  onClick={handleAIPropertySuggestion}
                  disabled={isGeneratingAI}
                  variant="outline"
                  size="sm"
                >
                  <Lightbulb className="h-3 w-3 mr-1" />
                  Suggest
                </Button>
              </div>
            </div>

            {/* AI Suggestions */}
            {aiSuggestions.length > 0 && (
              <div className="mt-3 space-y-2 border-t pt-3">
                <div className="flex items-center gap-2">
                  <Lightbulb className="h-3 w-3 text-yellow-500" />
                  <span className="text-xs font-medium">AI Suggestions</span>
                </div>

                {aiSuggestions.map((suggestion, index) => (
                  <div key={index} className="p-2 bg-white rounded border">
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1">
                        <div className="text-xs font-medium text-gray-700 mb-1">
                          {suggestion.property}
                        </div>
                        <div className="text-xs text-gray-600 mb-1">
                          {suggestion.value}
                        </div>
                        <div className="text-xs text-gray-500">
                          {suggestion.reason}
                        </div>
                      </div>
                      <Button
                        onClick={() => handleApplyAISuggestion(suggestion)}
                        size="sm"
                        variant="outline"
                        className="text-xs h-6 px-2"
                      >
                        Apply
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="border-b">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="ai" className="text-xs">
              <Brain className="h-3 w-3 mr-1" />
              AI
            </TabsTrigger>
            <TabsTrigger value="properties" className="text-xs">
              <Settings className="h-3 w-3 mr-1" />
              Props
            </TabsTrigger>
            <TabsTrigger value="styling" className="text-xs">
              <Palette className="h-3 w-3 mr-1" />
              Style
            </TabsTrigger>
            <TabsTrigger value="responsive" className="text-xs">
              <Monitor className="h-3 w-3 mr-1" />
              Responsive
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Content */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
            {/* AI Tab */}
            <TabsContent value="ai" className="mt-0 space-y-4">
              <div className="text-center py-6">
                <Brain className="h-12 w-12 text-purple-500 mx-auto mb-3" />
                <h3 className="font-medium text-gray-900 mb-2">AI Layout Assistant</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Get intelligent suggestions and optimize your layout properties with AI
                </p>

                <div className="space-y-3">
                  <Button
                    onClick={() => setShowNaturalLanguageEditor(true)}
                    className="w-full"
                    variant="outline"
                  >
                    <Sparkles className="h-4 w-4 mr-2" />
                    Open AI Assistant
                  </Button>

                  <Button
                    onClick={handleAIPropertySuggestion}
                    disabled={isGeneratingAI}
                    className="w-full"
                    variant="outline"
                  >
                    {isGeneratingAI ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Lightbulb className="h-4 w-4 mr-2" />
                        Get Smart Suggestions
                      </>
                    )}
                  </Button>
                </div>

                {/* Quick AI Actions */}
                <div className="mt-6 space-y-2">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Quick AI Actions</h4>

                  <Button
                    onClick={() => {
                      setAiPrompt('Optimize this layout for better user experience')
                      handleNaturalLanguageEdit()
                    }}
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start text-xs"
                    disabled={isGeneratingAI}
                  >
                    <Wand2 className="h-3 w-3 mr-2" />
                    Optimize for UX
                  </Button>

                  <Button
                    onClick={() => {
                      setAiPrompt('Make this layout more responsive and mobile-friendly')
                      handleNaturalLanguageEdit()
                    }}
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start text-xs"
                    disabled={isGeneratingAI}
                  >
                    <Monitor className="h-3 w-3 mr-2" />
                    Improve Responsiveness
                  </Button>

                  <Button
                    onClick={() => {
                      setAiPrompt('Center align this section and use modern spacing')
                      handleNaturalLanguageEdit()
                    }}
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start text-xs"
                    disabled={isGeneratingAI}
                  >
                    <Layout className="h-3 w-3 mr-2" />
                    Modern Alignment
                  </Button>
                </div>
              </div>
            </TabsContent>

            {/* Properties Tab */}
            <TabsContent value="properties" className="mt-0 space-y-4">
              {/* General Properties */}
              {renderCollapsibleSection('general', 'General', Settings, (
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="name" className="text-xs font-medium">Name</Label>
                    <Input
                      id="name"
                      value={selectedBlock?.name || selectedSection?.name || ''}
                      onChange={(e) => {
                        if (selectedBlock) {
                          updateBlock(selectedBlock.id, { name: e.target.value })
                        } else if (selectedSection) {
                          updateSection(selectedSection.id, { name: e.target.value })
                        }
                      }}
                      className="mt-1"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="visible" className="text-xs font-medium">Visible</Label>
                    <Switch
                      id="visible"
                      checked={selectedBlock?.isVisible ?? selectedSection?.isVisible ?? true}
                      onCheckedChange={(checked) => {
                        if (selectedBlock) {
                          updateBlock(selectedBlock.id, { isVisible: checked })
                        } else if (selectedSection) {
                          updateSection(selectedSection.id, { isVisible: checked })
                        }
                      }}
                    />
                  </div>

                  {selectedBlock && (
                    <div>
                      <Label htmlFor="position" className="text-xs font-medium">Position</Label>
                      <Input
                        id="position"
                        type="number"
                        value={selectedBlock.position}
                        onChange={(e) => {
                          updateBlock(selectedBlock.id, { position: parseInt(e.target.value) || 0 })
                        }}
                        className="mt-1"
                      />
                    </div>
                  )}
                </div>
              ))}

              {/* Layout Properties */}
              {selectedSection && renderCollapsibleSection('layout', 'Layout', Layout, (
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="layout-type" className="text-xs font-medium">Layout Type</Label>
                    <Select
                      value={selectedSection.configuration.layout}
                      onValueChange={(value) => {
                        updateSection(selectedSection.id, {
                          configuration: {
                            ...selectedSection.configuration,
                            layout: value as 'flex' | 'grid' | 'block'
                          }
                        })
                      }}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="block">Block</SelectItem>
                        <SelectItem value="flex">Flex</SelectItem>
                        <SelectItem value="grid">Grid</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="alignment" className="text-xs font-medium">Alignment</Label>
                    <Select
                      value={selectedSection.configuration.alignment}
                      onValueChange={(value) => {
                        updateSection(selectedSection.id, {
                          configuration: {
                            ...selectedSection.configuration,
                            alignment: value as 'left' | 'center' | 'right' | 'justify'
                          }
                        })
                      }}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                        <SelectItem value="justify">Justify</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              ))}
            </TabsContent>

            {/* Styling Tab */}
            <TabsContent value="styling" className="mt-0 space-y-4">
              {renderCollapsibleSection('spacing', 'Spacing', Move, (
                <div className="space-y-3">
                  <div className="text-xs text-muted-foreground">
                    Spacing controls will be implemented here
                  </div>
                </div>
              ))}

              {renderCollapsibleSection('colors', 'Colors', Paintbrush, (
                <div className="space-y-3">
                  <div className="text-xs text-muted-foreground">
                    Color controls will be implemented here
                  </div>
                </div>
              ))}

              {renderCollapsibleSection('typography', 'Typography', Type, (
                <div className="space-y-3">
                  <div className="text-xs text-muted-foreground">
                    Typography controls will be implemented here
                  </div>
                </div>
              ))}
            </TabsContent>

            {/* Responsive Tab */}
            <TabsContent value="responsive" className="mt-0 space-y-4">
              <div className="text-center py-8">
                <Monitor className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                <h3 className="font-medium text-gray-900 mb-2">Responsive Settings</h3>
                <p className="text-sm text-muted-foreground">
                  Responsive controls will be implemented here
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </ScrollArea>
    </div>
  )
}
