'use client'

import { useState, useEffect } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Save, 
  Loader2, 
  Plus, 
  Trash2, 
  Upload, 
  <PERSON>, 
  <PERSON>Off,
  Alert<PERSON>riangle,
  Package,
  Tag,
  Image as ImageIcon
} from 'lucide-react'
import { useCategories } from '@/lib/ecommerce/hooks/use-categories'
import { useCollections } from '@/lib/ecommerce/hooks/use-collections'
import { ZarPriceInput } from './zar-price-input'
import { ImageUpload } from './image-upload'
import type { Product, CreateProductInput, UpdateProductInput } from '@/lib/ecommerce/types'

// Form validation schema
const productFormSchema = z.object({
  title: z.string().min(1, 'Product title is required'),
  description: z.string().min(1, 'Product description is required'),
  slug: z.string().optional(),
  vendor: z.string().optional(),
  productType: z.string().optional(),
  price: z.number().min(0, 'Price must be positive'),
  compareAtPrice: z.number().optional(),
  costPerItem: z.number().optional(),
  status: z.enum(['active', 'draft', 'archived']),
  trackQuantity: z.boolean(),
  inventoryQuantity: z.number().min(0, 'Inventory must be non-negative'),
  continueSellingWhenOutOfStock: z.boolean(),
  weight: z.number().optional(),
  weightUnit: z.string().optional(),
  requiresShipping: z.boolean(),
  isTaxable: z.boolean(),
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.array(z.string()).optional(),
  categoryIds: z.array(z.string()).optional(),
  collectionIds: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  images: z.array(z.object({
    url: z.string(),
    altText: z.string(),
    position: z.number()
  })).optional(),
  variants: z.array(z.object({
    sku: z.string(),
    title: z.string(),
    price: z.number(),
    compareAtPrice: z.number().optional(),
    inventoryQuantity: z.number(),
    weight: z.number().optional(),
    options: z.array(z.object({
      name: z.string(),
      value: z.string()
    }))
  })).optional()
})

type ProductFormData = z.infer<typeof productFormSchema>

interface EnhancedProductFormProps {
  product?: Product
  onSubmit: (data: CreateProductInput | UpdateProductInput) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

export function EnhancedProductForm({ 
  product, 
  onSubmit, 
  onCancel, 
  loading = false 
}: EnhancedProductFormProps) {
  const [activeTab, setActiveTab] = useState('general')
  const [previewMode, setPreviewMode] = useState(false)
  
  const { categories, loading: categoriesLoading } = useCategories()
  const { collections, loading: collectionsLoading } = useCollections()

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting, isDirty }
  } = useForm<ProductFormData>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      title: product?.title || '',
      description: product?.description || '',
      slug: product?.slug || '',
      vendor: product?.vendor || '',
      productType: product?.productType || '',
      price: product?.price?.amount || 0,
      compareAtPrice: product?.compareAtPrice?.amount || undefined,
      costPerItem: product?.costPerItem?.amount || undefined,
      status: product?.status || 'draft',
      trackQuantity: product?.trackQuantity ?? true,
      inventoryQuantity: product?.inventoryQuantity || 0,
      continueSellingWhenOutOfStock: product?.continueSellingWhenOutOfStock ?? false,
      weight: product?.weight || undefined,
      weightUnit: product?.weightUnit || 'kg',
      requiresShipping: product?.requiresShipping ?? true,
      isTaxable: product?.isTaxable ?? true,
      seoTitle: product?.seo?.title || '',
      seoDescription: product?.seo?.description || '',
      seoKeywords: product?.seo?.keywords || [],
      categoryIds: product?.categories?.map(c => c.id) || [],
      collectionIds: product?.collections?.map(c => c.id) || [],
      tags: product?.tags?.map(t => t.name) || [],
      images: product?.images || [],
      variants: product?.variants || []
    }
  })

  const { fields: variantFields, append: appendVariant, remove: removeVariant } = useFieldArray({
    control,
    name: 'variants'
  })

  const { fields: imageFields, append: appendImage, remove: removeImage } = useFieldArray({
    control,
    name: 'images'
  })

  // Auto-generate slug from title
  const watchedTitle = watch('title')
  useEffect(() => {
    if (watchedTitle && !product) {
      const slug = watchedTitle
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '')
      setValue('slug', slug)
    }
  }, [watchedTitle, setValue, product])

  const handleFormSubmit = async (data: ProductFormData) => {
    try {
      const submitData = {
        ...data,
        id: product?.id,
        price: { amount: data.price, currency: 'ZAR' },
        compareAtPrice: data.compareAtPrice ? { amount: data.compareAtPrice, currency: 'ZAR' } : undefined,
        costPerItem: data.costPerItem ? { amount: data.costPerItem, currency: 'ZAR' } : undefined,
        seo: {
          title: data.seoTitle,
          description: data.seoDescription,
          keywords: data.seoKeywords
        }
      }

      await onSubmit(submitData as CreateProductInput | UpdateProductInput)
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  const addVariant = () => {
    appendVariant({
      sku: '',
      title: '',
      price: watch('price') || 0,
      inventoryQuantity: 0,
      options: []
    })
  }

  const addImage = (imageData: { url: string; altText: string }) => {
    appendImage({
      ...imageData,
      position: imageFields.length
    })
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {product ? 'Edit Product' : 'Create New Product'}
            </h1>
            <p className="text-muted-foreground">
              {product ? 'Update product information and settings' : 'Add a new product to your store'}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => setPreviewMode(!previewMode)}
          >
            {previewMode ? <EyeOff className="mr-2 h-4 w-4" /> : <Eye className="mr-2 h-4 w-4" />}
            {previewMode ? 'Edit' : 'Preview'}
          </Button>
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading || isSubmitting || !isDirty}>
            {(loading || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            <Save className="mr-2 h-4 w-4" />
            {product ? 'Update Product' : 'Create Product'}
          </Button>
        </div>
      </div>

      {/* Form Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="pricing">Pricing</TabsTrigger>
          <TabsTrigger value="inventory">Inventory</TabsTrigger>
          <TabsTrigger value="variants">Variants</TabsTrigger>
          <TabsTrigger value="media">Media</TabsTrigger>
          <TabsTrigger value="seo">SEO</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="mr-2 h-5 w-5" />
                Basic Information
              </CardTitle>
              <CardDescription>
                Essential product details and description
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Product Title *</Label>
                  <Input
                    id="title"
                    {...register('title')}
                    placeholder="Enter product title"
                  />
                  {errors.title && (
                    <p className="text-sm text-red-500">{errors.title.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="slug">URL Slug</Label>
                  <Input
                    id="slug"
                    {...register('slug')}
                    placeholder="product-url-slug"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Describe your product..."
                  rows={4}
                />
                {errors.description && (
                  <p className="text-sm text-red-500">{errors.description.message}</p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="vendor">Vendor</Label>
                  <Input
                    id="vendor"
                    {...register('vendor')}
                    placeholder="Brand or vendor name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="productType">Product Type</Label>
                  <Input
                    id="productType"
                    {...register('productType')}
                    placeholder="e.g., T-Shirt, Dress, Shoes"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Additional tabs would be implemented here */}
      </Tabs>
    </form>
  )
}
