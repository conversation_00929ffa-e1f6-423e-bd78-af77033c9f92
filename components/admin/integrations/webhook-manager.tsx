'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Webhook,
  Plus,
  Edit,
  Trash2,
  Send,
  Eye,
  Copy,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle
} from 'lucide-react'
import { toast } from 'sonner'

interface WebhookEndpoint {
  id: string
  name: string
  url: string
  events: string[]
  enabled: boolean
  secret: string
  status: 'active' | 'inactive' | 'error'
  lastTriggered?: string
  successRate: number
  totalCalls: number
  failedCalls: number
}

interface WebhookLog {
  id: string
  webhookId: string
  event: string
  url: string
  status: 'success' | 'failed' | 'pending'
  responseCode?: number
  responseTime?: number
  timestamp: string
  payload: any
  response?: string
  error?: string
}

export function WebhookManager() {
  const [webhooks, setWebhooks] = useState<WebhookEndpoint[]>([
    {
      id: '1',
      name: 'Order Processing Webhook',
      url: 'https://api.example.com/webhooks/orders',
      events: ['order.created', 'order.updated', 'order.completed'],
      enabled: true,
      secret: 'whsec_1234567890abcdef',
      status: 'active',
      lastTriggered: '2024-01-15T10:30:00Z',
      successRate: 98.5,
      totalCalls: 1250,
      failedCalls: 18
    },
    {
      id: '2',
      name: 'Inventory Sync',
      url: 'https://inventory.example.com/sync',
      events: ['product.updated', 'inventory.low'],
      enabled: true,
      secret: 'whsec_abcdef1234567890',
      status: 'active',
      lastTriggered: '2024-01-15T09:15:00Z',
      successRate: 100,
      totalCalls: 450,
      failedCalls: 0
    },
    {
      id: '3',
      name: 'Analytics Tracker',
      url: 'https://analytics.example.com/events',
      events: ['order.created', 'customer.registered'],
      enabled: false,
      secret: 'whsec_fedcba0987654321',
      status: 'inactive',
      successRate: 85.2,
      totalCalls: 890,
      failedCalls: 132
    }
  ])

  const [webhookLogs, setWebhookLogs] = useState<WebhookLog[]>([
    {
      id: '1',
      webhookId: '1',
      event: 'order.created',
      url: 'https://api.example.com/webhooks/orders',
      status: 'success',
      responseCode: 200,
      responseTime: 245,
      timestamp: '2024-01-15T10:30:00Z',
      payload: { orderId: 'CM000123', amount: 299.99 },
      response: '{"status": "received"}'
    },
    {
      id: '2',
      webhookId: '2',
      event: 'inventory.low',
      url: 'https://inventory.example.com/sync',
      status: 'failed',
      responseCode: 500,
      responseTime: 5000,
      timestamp: '2024-01-15T09:15:00Z',
      payload: { productId: 'prod_123', stock: 2 },
      error: 'Internal Server Error'
    }
  ])

  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingWebhook, setEditingWebhook] = useState<WebhookEndpoint | null>(null)
  const [selectedLog, setSelectedLog] = useState<WebhookLog | null>(null)

  const availableEvents = [
    'order.created',
    'order.updated',
    'order.completed',
    'order.cancelled',
    'product.created',
    'product.updated',
    'product.deleted',
    'inventory.low',
    'customer.registered',
    'customer.updated',
    'payment.completed',
    'payment.failed'
  ]

  const toggleWebhook = (webhookId: string) => {
    setWebhooks(prev => prev.map(webhook => 
      webhook.id === webhookId 
        ? { 
            ...webhook, 
            enabled: !webhook.enabled,
            status: !webhook.enabled ? 'active' : 'inactive'
          }
        : webhook
    ))
  }

  const deleteWebhook = (webhookId: string) => {
    if (confirm('Are you sure you want to delete this webhook?')) {
      setWebhooks(prev => prev.filter(webhook => webhook.id !== webhookId))
      toast.success('Webhook deleted successfully')
    }
  }

  const testWebhook = async (webhookId: string) => {
    const webhook = webhooks.find(w => w.id === webhookId)
    if (!webhook) return

    try {
      // Mock webhook test
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const success = Math.random() > 0.2
      
      const testLog: WebhookLog = {
        id: Date.now().toString(),
        webhookId,
        event: 'test.event',
        url: webhook.url,
        status: success ? 'success' : 'failed',
        responseCode: success ? 200 : 500,
        responseTime: Math.floor(Math.random() * 1000) + 100,
        timestamp: new Date().toISOString(),
        payload: { test: true, timestamp: new Date().toISOString() },
        response: success ? '{"status": "ok"}' : undefined,
        error: success ? undefined : 'Connection timeout'
      }

      setWebhookLogs(prev => [testLog, ...prev])
      
      if (success) {
        toast.success('Webhook test successful')
      } else {
        toast.error('Webhook test failed')
      }
    } catch (error) {
      toast.error('Failed to test webhook')
    }
  }

  const copyWebhookUrl = (url: string) => {
    navigator.clipboard.writeText(url)
    toast.success('Webhook URL copied to clipboard')
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'inactive':
        return <XCircle className="h-4 w-4 text-gray-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
      case 'active':
        return <Badge variant="default">Active</Badge>
      case 'failed':
      case 'error':
        return <Badge variant="destructive">Error</Badge>
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>
      case 'inactive':
        return <Badge variant="outline">Inactive</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  return (
    <div className="space-y-6">
      {/* Webhook Endpoints */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Webhook className="h-5 w-5" />
                <span>Webhook Endpoints</span>
              </CardTitle>
              <CardDescription>
                Manage webhook endpoints for real-time event notifications
              </CardDescription>
            </div>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Webhook
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {webhooks.map((webhook) => (
              <div key={webhook.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(webhook.status)}
                    <div>
                      <h4 className="font-medium">{webhook.name}</h4>
                      <p className="text-sm text-muted-foreground font-mono">{webhook.url}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(webhook.status)}
                    <Switch
                      checked={webhook.enabled}
                      onCheckedChange={() => toggleWebhook(webhook.id)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                  <div>
                    <Label className="text-xs text-muted-foreground">Events</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {webhook.events.slice(0, 3).map((event) => (
                        <Badge key={event} variant="outline" className="text-xs">
                          {event}
                        </Badge>
                      ))}
                      {webhook.events.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{webhook.events.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">Success Rate</Label>
                    <p className="text-sm font-medium">{webhook.successRate}%</p>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">Total Calls</Label>
                    <p className="text-sm font-medium">{webhook.totalCalls.toLocaleString()}</p>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">
                    Last triggered: {webhook.lastTriggered ? formatDate(webhook.lastTriggered) : 'Never'}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyWebhookUrl(webhook.url)}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => testWebhook(webhook.id)}
                    >
                      <Send className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setEditingWebhook(webhook)}
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteWebhook(webhook.id)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Webhook Logs */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Webhook Calls</CardTitle>
          <CardDescription>
            Monitor webhook delivery attempts and responses
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Event</TableHead>
                <TableHead>Endpoint</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Response Time</TableHead>
                <TableHead>Timestamp</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {webhookLogs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell>
                    <Badge variant="outline">{log.event}</Badge>
                  </TableCell>
                  <TableCell className="font-mono text-sm max-w-xs truncate">
                    {log.url}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(log.status)}
                      <span className="text-sm">{log.responseCode}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {log.responseTime ? `${log.responseTime}ms` : '-'}
                  </TableCell>
                  <TableCell>{formatDate(log.timestamp)}</TableCell>
                  <TableCell>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-3 w-3" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>Webhook Call Details</DialogTitle>
                          <DialogDescription>
                            Detailed information about this webhook call
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <Label className="text-sm font-medium">Event</Label>
                              <p className="text-sm">{log.event}</p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium">Status</Label>
                              <div className="flex items-center space-x-2">
                                {getStatusIcon(log.status)}
                                <span className="text-sm">{log.status}</span>
                              </div>
                            </div>
                          </div>
                          <div>
                            <Label className="text-sm font-medium">Payload</Label>
                            <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                              {JSON.stringify(log.payload, null, 2)}
                            </pre>
                          </div>
                          {log.response && (
                            <div>
                              <Label className="text-sm font-medium">Response</Label>
                              <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                                {log.response}
                              </pre>
                            </div>
                          )}
                          {log.error && (
                            <div>
                              <Label className="text-sm font-medium text-red-600">Error</Label>
                              <p className="text-sm text-red-600">{log.error}</p>
                            </div>
                          )}
                        </div>
                      </DialogContent>
                    </Dialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
