'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import {
  Settings,
  Save,
  TestTube,
  Eye,
  EyeOff,
  ExternalLink,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react'
import { toast } from 'sonner'

interface Integration {
  id: string
  name: string
  description: string
  category: string
  icon: any
  enabled: boolean
  configured: boolean
  status: 'connected' | 'disconnected' | 'error' | 'testing'
  settings: Record<string, any>
  webhookUrl?: string
  lastSync?: string
}

interface IntegrationConfigDialogProps {
  integration: Integration | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave: (integration: Integration) => void
}

export function IntegrationConfigDialog({ 
  integration, 
  open, 
  onOpenChange, 
  onSave 
}: IntegrationConfigDialogProps) {
  const [editedIntegration, setEditedIntegration] = useState<Integration | null>(integration)
  const [saving, setSaving] = useState(false)
  const [testing, setTesting] = useState(false)
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({})

  if (!integration || !editedIntegration) return null

  const handleSave = async () => {
    try {
      setSaving(true)
      // Mock save delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      onSave(editedIntegration)
      toast.success(`${integration.name} configuration saved successfully`)
      onOpenChange(false)
    } catch (error) {
      toast.error('Failed to save integration configuration')
    } finally {
      setSaving(false)
    }
  }

  const handleTest = async () => {
    try {
      setTesting(true)
      // Mock test delay
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Randomly succeed or fail for demo
      const success = Math.random() > 0.3
      
      if (success) {
        setEditedIntegration(prev => prev ? { ...prev, status: 'connected' } : null)
        toast.success(`${integration.name} connection test successful`)
      } else {
        setEditedIntegration(prev => prev ? { ...prev, status: 'error' } : null)
        toast.error(`${integration.name} connection test failed`)
      }
    } catch (error) {
      toast.error('Failed to test integration')
    } finally {
      setTesting(false)
    }
  }

  const updateSetting = (key: string, value: any) => {
    setEditedIntegration(prev => prev ? {
      ...prev,
      settings: { ...prev.settings, [key]: value }
    } : null)
  }

  const toggleShowSecret = (fieldId: string) => {
    setShowSecrets(prev => ({ ...prev, [fieldId]: !prev[fieldId] }))
  }

  const renderConfigFields = () => {
    switch (integration.id) {
      case 'google-analytics':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="trackingId">Tracking ID</Label>
              <Input
                id="trackingId"
                value={editedIntegration.settings.trackingId || ''}
                onChange={(e) => updateSetting('trackingId', e.target.value)}
                placeholder="GA-XXXXXXXXX"
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enhanced E-commerce</Label>
                <p className="text-sm text-muted-foreground">
                  Track detailed e-commerce events
                </p>
              </div>
              <Switch
                checked={editedIntegration.settings.enhancedEcommerce || false}
                onCheckedChange={(checked) => updateSetting('enhancedEcommerce', checked)}
              />
            </div>
          </div>
        )

      case 'facebook-pixel':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="pixelId">Pixel ID</Label>
              <Input
                id="pixelId"
                value={editedIntegration.settings.pixelId || ''}
                onChange={(e) => updateSetting('pixelId', e.target.value)}
                placeholder="123456789012345"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="accessToken">Access Token</Label>
              <div className="relative">
                <Input
                  id="accessToken"
                  type={showSecrets['accessToken'] ? 'text' : 'password'}
                  value={editedIntegration.settings.accessToken || ''}
                  onChange={(e) => updateSetting('accessToken', e.target.value)}
                  placeholder="Enter your Facebook access token"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => toggleShowSecret('accessToken')}
                >
                  {showSecrets['accessToken'] ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        )

      case 'mailchimp':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="apiKey">API Key</Label>
              <div className="relative">
                <Input
                  id="apiKey"
                  type={showSecrets['apiKey'] ? 'text' : 'password'}
                  value={editedIntegration.settings.apiKey || ''}
                  onChange={(e) => updateSetting('apiKey', e.target.value)}
                  placeholder="xxxxxxxx-us1"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => toggleShowSecret('apiKey')}
                >
                  {showSecrets['apiKey'] ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="listId">List ID</Label>
              <Input
                id="listId"
                value={editedIntegration.settings.listId || ''}
                onChange={(e) => updateSetting('listId', e.target.value)}
                placeholder="abc123"
              />
            </div>
          </div>
        )

      case 'whatsapp-business':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="phoneNumberId">Phone Number ID</Label>
              <Input
                id="phoneNumberId"
                value={editedIntegration.settings.phoneNumberId || ''}
                onChange={(e) => updateSetting('phoneNumberId', e.target.value)}
                placeholder="123456789012345"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="accessToken">Access Token</Label>
              <div className="relative">
                <Input
                  id="accessToken"
                  type={showSecrets['whatsappToken'] ? 'text' : 'password'}
                  value={editedIntegration.settings.accessToken || ''}
                  onChange={(e) => updateSetting('accessToken', e.target.value)}
                  placeholder="Enter your WhatsApp access token"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => toggleShowSecret('whatsappToken')}
                >
                  {showSecrets['whatsappToken'] ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        )

      case 'aramex':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  value={editedIntegration.settings.username || ''}
                  onChange={(e) => updateSetting('username', e.target.value)}
                  placeholder="Your Aramex username"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showSecrets['aramexPassword'] ? 'text' : 'password'}
                    value={editedIntegration.settings.password || ''}
                    onChange={(e) => updateSetting('password', e.target.value)}
                    placeholder="Your Aramex password"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3"
                    onClick={() => toggleShowSecret('aramexPassword')}
                  >
                    {showSecrets['aramexPassword'] ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="accountNumber">Account Number</Label>
              <Input
                id="accountNumber"
                value={editedIntegration.settings.accountNumber || ''}
                onChange={(e) => updateSetting('accountNumber', e.target.value)}
                placeholder="123456"
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Sandbox Mode</Label>
                <p className="text-sm text-muted-foreground">
                  Use test environment for development
                </p>
              </div>
              <Switch
                checked={editedIntegration.settings.sandbox || false}
                onCheckedChange={(checked) => updateSetting('sandbox', checked)}
              />
            </div>
          </div>
        )

      case 'zapier':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="webhookUrl">Webhook URL</Label>
              <Input
                id="webhookUrl"
                value={editedIntegration.settings.webhookUrl || ''}
                onChange={(e) => updateSetting('webhookUrl', e.target.value)}
                placeholder="https://hooks.zapier.com/hooks/catch/..."
              />
            </div>
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="flex items-start space-x-2">
                <AlertTriangle className="h-4 w-4 text-blue-600 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-blue-800">Setup Instructions</p>
                  <p className="text-blue-700 mt-1">
                    Create a new Zap in Zapier and copy the webhook URL here. 
                    This will allow you to trigger Zapier workflows from your store events.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )

      default:
        return (
          <div className="text-center py-8 text-muted-foreground">
            <Settings className="h-8 w-8 mx-auto mb-2" />
            <p>No configuration options available for this integration.</p>
          </div>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <integration.icon className="h-5 w-5" />
            <span>Configure {integration.name}</span>
          </DialogTitle>
          <DialogDescription>
            {integration.description}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="settings" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="info">Information</TabsTrigger>
          </TabsList>

          <TabsContent value="settings" className="space-y-4">
            {renderConfigFields()}
          </TabsContent>

          <TabsContent value="info" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Status</Label>
                <div className="flex items-center space-x-2 mt-1">
                  {editedIntegration.status === 'connected' && (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                  {editedIntegration.status === 'error' && (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  {editedIntegration.status === 'disconnected' && (
                    <XCircle className="h-4 w-4 text-gray-500" />
                  )}
                  <span className="capitalize">{editedIntegration.status}</span>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium">Category</Label>
                <p className="text-sm capitalize mt-1">{integration.category}</p>
              </div>
            </div>
            
            {editedIntegration.lastSync && (
              <div>
                <Label className="text-sm font-medium">Last Sync</Label>
                <p className="text-sm mt-1">
                  {new Date(editedIntegration.lastSync).toLocaleString()}
                </p>
              </div>
            )}

            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">Documentation</h4>
              <p className="text-sm text-muted-foreground mb-3">
                Need help setting up this integration? Check out our documentation.
              </p>
              <Button variant="outline" size="sm">
                <ExternalLink className="mr-2 h-4 w-4" />
                View Documentation
              </Button>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <div className="flex items-center justify-between w-full">
            <Button
              variant="outline"
              onClick={handleTest}
              disabled={testing || saving}
            >
              {testing ? (
                <>
                  <TestTube className="mr-2 h-4 w-4 animate-pulse" />
                  Testing...
                </>
              ) : (
                <>
                  <TestTube className="mr-2 h-4 w-4" />
                  Test Connection
                </>
              )}
            </Button>
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button onClick={handleSave} disabled={saving || testing}>
                {saving ? (
                  <>
                    <Save className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Configuration
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
