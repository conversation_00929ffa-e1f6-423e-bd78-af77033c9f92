'use client'

import React, { useState } from 'react'
import {
  ArrowLeft,
  Layers,
  Package,
  Settings,
  Eye,
  EyeOff,
  Monitor,
  Tablet,
  Smartphone,
  Undo,
  Redo,
  ChevronDown,
  User,
  LogOut,
  Brain,
} from 'lucide-react'

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar"
import { AIFirstLayoutInterface } from "@/lib/layout-builder/components/ai-first-layout-interface"
import { EnhancedAILayoutGenerator } from "@/lib/layout-builder/components/enhanced-ai-layout-generator"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { SectionDesignerManager } from "@/lib/layout-builder/components/section-designer-manager"
// import { LayoutBlockLibrary } from "@/lib/layout-builder/components/layout-block-library"
// import { LayoutStructureTree } from "@/lib/layout-builder/components/layout-structure-tree"
import { useLayoutBuilder } from "@/lib/layout-builder/context"

interface LayoutBuilderSidebarProps extends React.ComponentProps<typeof Sidebar> {
  onBackToAdmin?: () => void
}

export function LayoutBuilderSidebar({ onBackToAdmin, ...props }: LayoutBuilderSidebarProps) {
  const [activeTab, setActiveTab] = useState<'ai' | 'sections' | 'blocks' | 'structure' | 'settings'>('ai')
  const { 
    state, 
    setPreviewMode, 
    setDevicePreview, 
    undo, 
    redo, 
    canUndo, 
    canRedo 
  } = useLayoutBuilder()

  const { isPreviewMode, devicePreview, hasUnsavedChanges } = state

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <div className="flex items-center">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-purple-600 text-white">
                  <Layers className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">Layout Builder</span>
                  <span className="truncate text-xs">Visual Layout Editor</span>
                </div>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        {/* Back to Admin Button */}
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={onBackToAdmin} className="w-full">
                  <ArrowLeft className="size-4" />
                  <span>Back to Admin</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator />

        {/* Status Indicator */}
        <SidebarGroup>
          <SidebarGroupContent>
            <div className="px-2 py-1">
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Status</span>
                <Badge variant={hasUnsavedChanges ? "destructive" : "secondary"} className="text-xs">
                  {hasUnsavedChanges ? 'Unsaved' : 'Saved'}
                </Badge>
              </div>
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Action Buttons */}
        <SidebarGroup>
          <SidebarGroupContent>
            <div className="grid grid-cols-2 gap-1 px-2">
              {/* Preview Toggle */}
              <Button
                variant={isPreviewMode ? "default" : "outline"}
                size="sm"
                onClick={() => setPreviewMode(!isPreviewMode)}
                className="h-8"
              >
                {isPreviewMode ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
              </Button>

              {/* Device Preview */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8">
                    {devicePreview === 'mobile' && <Smartphone className="h-3 w-3" />}
                    {devicePreview === 'tablet' && <Tablet className="h-3 w-3" />}
                    {devicePreview === 'desktop' && <Monitor className="h-3 w-3" />}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setDevicePreview('desktop')}>
                    <Monitor className="h-4 w-4 mr-2" />
                    Desktop
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setDevicePreview('tablet')}>
                    <Tablet className="h-4 w-4 mr-2" />
                    Tablet
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setDevicePreview('mobile')}>
                    <Smartphone className="h-4 w-4 mr-2" />
                    Mobile
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Undo */}
              <Button
                variant="outline"
                size="sm"
                onClick={undo}
                disabled={!canUndo}
                className="h-8"
              >
                <Undo className="h-3 w-3" />
              </Button>

              {/* Redo */}
              <Button
                variant="outline"
                size="sm"
                onClick={redo}
                disabled={!canRedo}
                className="h-8"
              >
                <Redo className="h-3 w-3" />
              </Button>
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator />

        {/* Tab Navigation */}
        <SidebarGroup>
          <SidebarGroupContent>
            <div className="grid grid-cols-2 gap-1 px-2 mb-2">
              <Button
                variant={activeTab === 'ai' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('ai')}
                className="h-8 text-xs"
              >
                <Brain className="h-3 w-3 mr-1" />
                AI Designer
              </Button>
              <Button
                variant={activeTab === 'sections' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('sections')}
                className="h-8 text-xs"
              >
                <Layers className="h-3 w-3 mr-1" />
                Sections
              </Button>
            </div>
            <div className="grid grid-cols-2 gap-1 px-2">
              <Button
                variant={activeTab === 'blocks' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('blocks')}
                className="h-8 text-xs"
              >
                <Package className="h-3 w-3 mr-1" />
                Blocks
              </Button>
              <Button
                variant={activeTab === 'structure' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('structure')}
                className="h-8 text-xs"
              >
                <Layers className="h-3 w-3 mr-1" />
                Structure
              </Button>
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Content Area */}
        <SidebarGroup className="flex-1">
          <SidebarGroupLabel>
            {activeTab === 'ai' && 'AI Layout Designer'}
            {activeTab === 'sections' && 'Section Designers'}
            {activeTab === 'blocks' && 'Layout Blocks'}
            {activeTab === 'structure' && 'Layout Structure'}
            {activeTab === 'settings' && 'Layout Settings'}
          </SidebarGroupLabel>
          <SidebarGroupContent className="flex-1 overflow-hidden">
            <div className="h-full">
              {activeTab === 'ai' && (
                <div className="h-full flex flex-col">
                  <div className="flex-1 overflow-hidden">
                    <AIFirstLayoutInterface className="h-full" />
                  </div>
                  <div className="border-t p-2">
                    <EnhancedAILayoutGenerator className="h-auto" />
                  </div>
                </div>
              )}
              {activeTab === 'sections' && (
                <div className="h-full">
                  <SectionDesignerManager />
                </div>
              )}
              {activeTab === 'blocks' && (
                <div className="h-full px-2">
                  <div className="text-sm text-muted-foreground mb-4">
                    Manual layout blocks - Use AI Designer for better results
                  </div>
                  <div className="text-center py-8 text-gray-400">
                    <Package className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm">Use AI Designer tab for intelligent layout generation</p>
                  </div>
                </div>
              )}
              {activeTab === 'structure' && (
                <div className="h-full px-2">
                  <div className="text-sm text-muted-foreground">
                    Layout structure tree will be displayed here
                  </div>
                </div>
              )}
              {activeTab === 'settings' && (
                <div className="h-full px-2">
                  <div className="text-sm text-muted-foreground">
                    Layout settings panel will be implemented here
                  </div>
                </div>
              )}
            </div>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src="/avatars/admin.png" alt="Admin" />
                    <AvatarFallback className="rounded-lg">AD</AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">Admin User</span>
                    <span className="truncate text-xs">Layout Builder</span>
                  </div>
                  <ChevronDown className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuLabel className="p-0 font-normal">
                  <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarImage src="/avatars/admin.png" alt="Admin" />
                      <AvatarFallback className="rounded-lg">AD</AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold">Admin User</span>
                      <span className="truncate text-xs"><EMAIL></span>
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <User className="h-4 w-4 mr-2" />
                  Account
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <LogOut className="h-4 w-4 mr-2" />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
