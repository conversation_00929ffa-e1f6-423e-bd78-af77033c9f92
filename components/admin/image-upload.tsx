'use client'

import { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import {
  Upload,
  X,
  Image as ImageIcon,
  Loader2,
  Move
} from 'lucide-react'
import { uploadProductImages, storageService, isAppwriteConfigured } from '@/lib/appwrite'
import { toast } from 'sonner'

interface ImageUploadProps {
  images: string[]
  onChange: (images: string[]) => void
  maxImages?: number
  label?: string
  description?: string
  className?: string
  disabled?: boolean
  required?: boolean
  error?: string
  useAppwrite?: boolean // Option to use Appwrite or fallback to object URLs
}

export function ImageUpload({
  images = [],
  onChange,
  maxImages = 5,
  label,
  description,
  className,
  disabled = false,
  required = false,
  error,
  useAppwrite = true
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [dragOver, setDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Check if Appwrite is configured
  const appwriteConfigured = isAppwriteConfigured()

  const handleFileSelect = async (files: FileList | null) => {
    if (!files || files.length === 0) return

    const remainingSlots = maxImages - images.length
    const filesToProcess = Array.from(files).slice(0, remainingSlots)

    setUploading(true)
    setUploadProgress(0)

    try {
      const newImages: string[] = []

      // Use Appwrite if configured and enabled, otherwise fallback to object URLs
      if (useAppwrite && appwriteConfigured) {
        try {
          const uploadResults = await uploadProductImages(filesToProcess)

          for (const result of uploadResults) {
            newImages.push(result.url)
          }

          toast.success(`Successfully uploaded ${uploadResults.length} image(s)`)
        } catch (appwriteError) {
          console.error('Appwrite upload failed, falling back to object URLs:', appwriteError)
          toast.error('Upload failed, using local preview instead')

          // Fallback to object URLs
          for (const file of filesToProcess) {
            if (file.type.startsWith('image/') && file.size <= 5 * 1024 * 1024) {
              const imageUrl = URL.createObjectURL(file)
              newImages.push(imageUrl)
            }
          }
        }
      } else {
        // Fallback to object URLs
        for (const file of filesToProcess) {
          // Validate file type
          if (!file.type.startsWith('image/')) {
            console.warn(`Skipping non-image file: ${file.name}`)
            continue
          }

          // Validate file size (max 5MB)
          if (file.size > 5 * 1024 * 1024) {
            console.warn(`File too large: ${file.name}`)
            toast.error(`File ${file.name} is too large (max 5MB)`)
            continue
          }

          const imageUrl = URL.createObjectURL(file)
          newImages.push(imageUrl)
        }

        if (!appwriteConfigured && useAppwrite) {
          toast.warning('Appwrite not configured, using local preview')
        }
      }

      onChange([...images, ...newImages])
    } catch (error) {
      console.error('Error processing images:', error)
      toast.error('Failed to process images')
    } finally {
      setUploading(false)
      setUploadProgress(0)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    if (disabled) return
    
    handleFileSelect(e.dataTransfer.files)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    if (!disabled) {
      setDragOver(true)
    }
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index)
    onChange(newImages)
  }

  const moveImage = (fromIndex: number, toIndex: number) => {
    const newImages = [...images]
    const [movedImage] = newImages.splice(fromIndex, 1)
    newImages.splice(toIndex, 0, movedImage)
    onChange(newImages)
  }

  const openFileDialog = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const canAddMore = images.length < maxImages && !disabled

  return (
    <div className={cn("space-y-4", className)}>
      {label && (
        <Label className="text-sm font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}

      {/* Image Grid */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image, index) => (
            <Card key={index} className="relative group">
              <CardContent className="p-2">
                <div className="aspect-square relative rounded-md overflow-hidden bg-gray-100">
                  <img
                    src={image}
                    alt={`Product image ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                  
                  {/* Overlay with actions */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                    {index > 0 && (
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => moveImage(index, index - 1)}
                        className="h-8 w-8 p-0"
                      >
                        <Move className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => removeImage(index)}
                      className="h-8 w-8 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                    {index < images.length - 1 && (
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => moveImage(index, index + 1)}
                        className="h-8 w-8 p-0"
                      >
                        <Move className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                  
                  {/* Primary image indicator */}
                  {index === 0 && (
                    <div className="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                      Primary
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Upload Area */}
      {canAddMore && (
        <Card 
          className={cn(
            "border-2 border-dashed transition-colors cursor-pointer",
            dragOver ? "border-blue-500 bg-blue-50" : "border-gray-300",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={openFileDialog}
        >
          <CardContent className="p-8">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              {uploading ? (
                <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
              ) : (
                <div className="p-4 bg-gray-100 rounded-full">
                  <Upload className="h-8 w-8 text-gray-600" />
                </div>
              )}
              
              <div>
                <p className="text-lg font-medium">
                  {uploading ? 'Uploading images...' : 'Upload product images'}
                </p>
                <p className="text-sm text-muted-foreground">
                  Drag and drop images here, or click to browse
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  {images.length}/{maxImages} images • Max 5MB per image • JPG, PNG, WebP
                </p>
              </div>
              
              {!uploading && (
                <Button variant="outline" size="sm" disabled={disabled}>
                  <ImageIcon className="mr-2 h-4 w-4" />
                  Choose Images
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*"
        onChange={(e) => handleFileSelect(e.target.files)}
        className="hidden"
        disabled={disabled}
      />
      
      {error && (
        <p className="text-sm text-red-500">{error}</p>
      )}
    </div>
  )
}
