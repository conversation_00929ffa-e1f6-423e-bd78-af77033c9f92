"use client"

import React from "react"
import { usePathname } from "next/navigation"
import {
  <PERSON><PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"

// Route mapping for breadcrumbs
const routeMap: Record<string, string> = {
  '/admin': 'Dashboard',
  '/admin/analytics': 'Analytics',
  '/admin/products': 'Products',
  '/admin/products/new': 'Add Product',
  '/admin/products/categories': 'Categories',
  '/admin/products/attributes': 'Attributes',
  '/admin/orders': 'Orders',
  '/admin/inventory': 'Inventory',
  '/admin/customers': 'Customers',
  '/admin/page-builder': 'Page Builder',
  '/admin/pages': 'Pages',
  '/admin/blog': 'Blog',
  '/admin/reports': 'Reports',
  '/admin/reports/sales': 'Sales Report',
  '/admin/reports/products': 'Product Report',
  '/admin/reports/customers': 'Customer Report',
  '/admin/settings': 'Settings',
  '/admin/settings/general': 'General',
  '/admin/settings/store': 'Store',
  '/admin/settings/payments': 'Payments',
  '/admin/settings/shipping': 'Shipping',
  '/admin/settings/taxes': 'Taxes',
  '/admin/settings/security': 'Security',
}

// Check if we're in page builder editing mode by looking for specific URL patterns or state
function isPageBuilderEditing(): boolean {
  if (typeof window === 'undefined') return false

  // Check if the URL contains page builder and we're in editing mode
  // This is a simple check - in a real app you might use more sophisticated state management
  const url = window.location.href
  return url.includes('/admin/page-builder') && (
    url.includes('?edit=') ||
    document.querySelector('[data-page-builder-editing="true"]') !== null
  )
}

function generateBreadcrumbs(pathname: string) {
  const segments = pathname.split('/').filter(Boolean)
  const breadcrumbs = []

  // Always start with Admin
  breadcrumbs.push({
    label: 'Admin',
    href: '/admin',
    isLast: false,
  })

  // Build path progressively
  let currentPath = ''
  for (let i = 0; i < segments.length; i++) {
    currentPath += `/${segments[i]}`
    
    // Skip the first 'admin' segment since we already added it
    if (segments[i] === 'admin') continue

    const isLast = i === segments.length - 1
    const label = routeMap[currentPath] || segments[i].charAt(0).toUpperCase() + segments[i].slice(1)

    breadcrumbs.push({
      label,
      href: currentPath,
      isLast,
    })
  }

  return breadcrumbs
}

export function AdminBreadcrumb() {
  const pathname = usePathname()

  // Check if we're in page builder editing mode
  const isEditing = isPageBuilderEditing()

  // Don't show breadcrumbs on the main dashboard
  if (pathname === '/admin') {
    return (
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbPage>Dashboard</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    )
  }

  // Special handling for page builder editing mode
  if (pathname === '/admin/page-builder' && isEditing) {
    return (
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem className="hidden md:block">
            <BreadcrumbLink href="/admin">
              Admin
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator className="hidden md:block" />
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin/page-builder">
              Page Builder
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator className="hidden md:block" />
          <BreadcrumbItem>
            <BreadcrumbPage>Editing Page</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    )
  }

  const breadcrumbs = generateBreadcrumbs(pathname)

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbs.map((crumb, index) => (
          <React.Fragment key={crumb.href}>
            <BreadcrumbItem className={index === 0 ? "hidden md:block" : ""}>
              {crumb.isLast ? (
                <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
              ) : (
                <BreadcrumbLink href={crumb.href}>
                  {crumb.label}
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
            {!crumb.isLast && (
              <BreadcrumbSeparator className="hidden md:block" />
            )}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
