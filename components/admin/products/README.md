# Product Form Component

## Overview

The `ProductForm` component is a comprehensive form for creating and editing products in the e-commerce system. It has been refactored to use the `useProductMutations` hook for better consistency and error handling.

## Key Improvements Made

### 1. **Hook Integration**
- **Before**: Used manual `onSubmit` prop for API calls
- **After**: Uses `useProductMutations` hook from `lib/ecommerce/hooks/use-products.ts`
- **Benefits**: 
  - Consistent error handling across the app
  - Built-in loading states
  - Automatic error management
  - Better separation of concerns

### 2. **Enhanced Form Validation**
- Comprehensive Zod schema with proper validation rules
- Real-time validation with `mode: 'onChange'`
- Better error messages and field-specific validation
- Proper handling of nullable fields

### 3. **Improved User Experience**
- **Loading States**: Proper loading indicators during form submission
- **Success Notifications**: Toast notifications for successful operations
- **Error Handling**: Clear error messages with automatic clearing
- **Form Reset**: Automatic form reset after successful creation
- **Profit Margin Calculator**: Real-time profit margin display in pricing section

### 4. **Better Error Handling**
- Global error alerts for mutation errors
- Field-specific error messages
- Loading error states for categories/collections
- Automatic error clearing when user starts typing

### 5. **Enhanced Pricing Section**
- ZAR-specific price inputs with proper formatting
- Real-time profit margin calculations
- Visual indicators for profit/loss
- Better validation for price fields

### 6. **Improved Inventory Management**
- Stock status indicators with color coding
- Better validation for inventory quantities
- Clear warnings for out-of-stock scenarios
- Conditional fields based on tracking preferences

### 7. **Categories and Collections**
- Better loading states with visual indicators
- Error handling for failed API calls
- Improved user feedback
- Optional field handling

## Usage

### Basic Usage

```tsx
import { ProductForm } from '@/components/admin/products/product-form'

// Create new product
<ProductForm
  onSuccess={(product) => {
    console.log('Product created:', product)
    router.push(`/admin/products/${product.id}`)
  }}
  onCancel={() => router.back()}
/>

// Edit existing product
<ProductForm
  product={existingProduct}
  onSuccess={(product) => {
    console.log('Product updated:', product)
    router.refresh()
  }}
  onCancel={() => router.back()}
/>
```

### Props Interface

```tsx
interface ProductFormProps {
  product?: Product              // Optional: Product to edit
  onSuccess?: (product: Product) => void  // Called when form is successfully submitted
  onCancel: () => void          // Called when user cancels the form
}
```

## Features

### Form Sections (Tabs)

1. **General**: Basic product information (title, description, vendor, etc.)
2. **Pricing**: Price management with profit calculations
3. **Inventory**: Stock management and tracking
4. **Variants**: Product variants management (if applicable)
5. **Media**: Image upload and management
6. **SEO**: Search engine optimization settings

### Key Features

- **Auto-slug generation** from product title
- **Real-time validation** with immediate feedback
- **Profit margin calculator** in pricing section
- **Stock status indicators** in inventory section
- **Tag and keyword management** with add/remove functionality
- **Image upload** with drag-and-drop support
- **Responsive design** with mobile-friendly layout

## Dependencies

- `react-hook-form` with Zod validation
- `@/lib/ecommerce/hooks/use-products` for mutations
- `@/lib/ecommerce/hooks/use-categories` for categories
- `@/lib/ecommerce/hooks/use-collections` for collections
- `sonner` for toast notifications
- Various UI components from `@/components/ui`

## Error Handling

The component handles various error scenarios:

1. **Validation Errors**: Field-level validation with immediate feedback
2. **API Errors**: Global error alerts for failed API calls
3. **Loading Errors**: Specific error handling for categories/collections loading
4. **Network Errors**: Graceful handling of network failures

## Performance Considerations

- **Optimized re-renders** with proper dependency arrays
- **Conditional rendering** for optional sections
- **Debounced validation** for better performance
- **Lazy loading** of dependent data

## Future Enhancements

1. **Bulk operations** support
2. **Advanced variant management**
3. **Rich text editor** for descriptions
4. **Advanced image editing** capabilities
5. **Product templates** for quick creation
6. **Draft auto-save** functionality