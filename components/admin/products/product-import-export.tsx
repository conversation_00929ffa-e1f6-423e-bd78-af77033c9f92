'use client'

import { useState, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Upload,
  Download,
  FileText,
  CheckCircle,
  XCircle,
  AlertT<PERSON>gle,
  Eye,
  RefreshCw
} from 'lucide-react'
import { toast } from 'sonner'

interface ImportResult {
  success: boolean
  totalRows: number
  successCount: number
  errorCount: number
  warnings: Array<{
    row: number
    field: string
    message: string
  }>
  errors: Array<{
    row: number
    field: string
    message: string
  }>
}

interface ExportOptions {
  format: 'csv' | 'xlsx'
  includeVariants: boolean
  includeImages: boolean
  includeCategories: boolean
  includeTags: boolean
  includeInventory: boolean
  dateRange?: {
    from: Date
    to: Date
  }
  status?: string[]
  categories?: string[]
}

export function ProductImportExport() {
  const [activeTab, setActiveTab] = useState('import')
  const [importFile, setImportFile] = useState<File | null>(null)
  const [importProgress, setImportProgress] = useState(0)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [isImporting, setIsImporting] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [previewData, setPreviewData] = useState<any[]>([])
  
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'csv',
    includeVariants: true,
    includeImages: false,
    includeCategories: true,
    includeTags: true,
    includeInventory: true
  })

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
        toast.error('Please select a CSV file')
        return
      }
      setImportFile(file)
      setImportResult(null)
    }
  }

  const handleImport = async () => {
    if (!importFile) {
      toast.error('Please select a file to import')
      return
    }

    setIsImporting(true)
    setImportProgress(0)

    try {
      // Simulate file processing
      const formData = new FormData()
      formData.append('file', importFile)

      // Mock progress updates
      const progressInterval = setInterval(() => {
        setImportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return prev
          }
          return prev + 10
        })
      }, 200)

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      clearInterval(progressInterval)
      setImportProgress(100)

      // Mock result
      const mockResult: ImportResult = {
        success: true,
        totalRows: 150,
        successCount: 142,
        errorCount: 3,
        warnings: [
          { row: 5, field: 'price', message: 'Price seems unusually high' },
          { row: 12, field: 'weight', message: 'Weight not specified, using default' },
          { row: 23, field: 'category', message: 'Category not found, using default' }
        ],
        errors: [
          { row: 8, field: 'sku', message: 'SKU already exists' },
          { row: 15, field: 'title', message: 'Product title is required' },
          { row: 34, field: 'price', message: 'Invalid price format' }
        ]
      }

      setImportResult(mockResult)
      
      if (mockResult.success) {
        toast.success(`Import completed! ${mockResult.successCount} products imported successfully.`)
      } else {
        toast.error(`Import completed with errors. ${mockResult.errorCount} products failed.`)
      }
    } catch (error) {
      console.error('Import error:', error)
      toast.error('Import failed. Please try again.')
    } finally {
      setIsImporting(false)
    }
  }

  const handleExport = async () => {
    setIsExporting(true)

    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Create mock CSV content
      const csvContent = `Title,SKU,Price,Status,Category,Stock
Kids T-Shirt,TSH-001,299.99,active,T-Shirts,25
Summer Dress,DRS-001,459.99,active,Dresses,18
Denim Shorts,SHT-001,349.99,active,Shorts,32`

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `products-export-${new Date().toISOString().split('T')[0]}.${exportOptions.format}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.success('Export completed successfully!')
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Export failed. Please try again.')
    } finally {
      setIsExporting(false)
    }
  }

  const handlePreview = async () => {
    if (!importFile) return

    try {
      // Mock preview data
      const mockPreview = [
        { title: 'Kids T-Shirt', sku: 'TSH-001', price: '299.99', status: 'active' },
        { title: 'Summer Dress', sku: 'DRS-001', price: '459.99', status: 'active' },
        { title: 'Denim Shorts', sku: 'SHT-001', price: '349.99', status: 'draft' }
      ]
      
      setPreviewData(mockPreview)
      setShowPreview(true)
    } catch (error) {
      toast.error('Failed to preview file')
    }
  }

  const downloadTemplate = () => {
    const templateContent = `Title,SKU,Description,Price,Compare At Price,Status,Category,Tags,Weight,Inventory Quantity,Track Quantity,Requires Shipping,Taxable
Kids T-Shirt,TSH-001,Comfortable cotton t-shirt for kids,299.99,399.99,active,T-Shirts,"kids,cotton,summer",0.2,25,true,true,true
Summer Dress,DRS-001,Beautiful summer dress,459.99,,active,Dresses,"kids,summer,dress",0.3,18,true,true,true`

    const blob = new Blob([templateContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'product-import-template.csv'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    toast.success('Template downloaded successfully!')
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Import & Export</h2>
          <p className="text-muted-foreground">
            Bulk import products from CSV or export your catalog
          </p>
        </div>
        <Button variant="outline" onClick={downloadTemplate}>
          <FileText className="mr-2 h-4 w-4" />
          Download Template
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="import">Import Products</TabsTrigger>
          <TabsTrigger value="export">Export Products</TabsTrigger>
        </TabsList>

        <TabsContent value="import" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Import Products from CSV</CardTitle>
              <CardDescription>
                Upload a CSV file to bulk import products. Download the template to see the required format.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="import-file">Select CSV File</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="import-file"
                    type="file"
                    accept=".csv"
                    onChange={handleFileSelect}
                    ref={fileInputRef}
                    className="flex-1"
                  />
                  {importFile && (
                    <Button variant="outline" size="sm" onClick={handlePreview}>
                      <Eye className="mr-2 h-4 w-4" />
                      Preview
                    </Button>
                  )}
                </div>
                {importFile && (
                  <p className="text-sm text-muted-foreground">
                    Selected: {importFile.name} ({(importFile.size / 1024).toFixed(1)} KB)
                  </p>
                )}
              </div>

              {isImporting && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Importing products...</span>
                    <span>{Math.round(importProgress)}%</span>
                  </div>
                  <Progress value={importProgress} className="w-full" />
                </div>
              )}

              {importResult && (
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center p-4 border rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{importResult.successCount}</div>
                      <div className="text-sm text-muted-foreground">Successful</div>
                    </div>
                    <div className="text-center p-4 border rounded-lg">
                      <div className="text-2xl font-bold text-red-600">{importResult.errorCount}</div>
                      <div className="text-sm text-muted-foreground">Errors</div>
                    </div>
                    <div className="text-center p-4 border rounded-lg">
                      <div className="text-2xl font-bold text-orange-600">{importResult.warnings.length}</div>
                      <div className="text-sm text-muted-foreground">Warnings</div>
                    </div>
                  </div>

                  {(importResult.errors.length > 0 || importResult.warnings.length > 0) && (
                    <div className="space-y-2">
                      <h4 className="font-medium">Issues Found:</h4>
                      <div className="max-h-48 overflow-y-auto space-y-1">
                        {importResult.errors.map((error, index) => (
                          <div key={`error-${index}`} className="flex items-center space-x-2 text-sm p-2 bg-red-50 rounded">
                            <XCircle className="h-4 w-4 text-red-500" />
                            <span>Row {error.row}: {error.field} - {error.message}</span>
                          </div>
                        ))}
                        {importResult.warnings.map((warning, index) => (
                          <div key={`warning-${index}`} className="flex items-center space-x-2 text-sm p-2 bg-orange-50 rounded">
                            <AlertTriangle className="h-4 w-4 text-orange-500" />
                            <span>Row {warning.row}: {warning.field} - {warning.message}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setImportFile(null)
                    setImportResult(null)
                    if (fileInputRef.current) {
                      fileInputRef.current.value = ''
                    }
                  }}
                  disabled={isImporting}
                >
                  Clear
                </Button>
                <Button
                  onClick={handleImport}
                  disabled={!importFile || isImporting}
                >
                  {isImporting ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Importing...
                    </>
                  ) : (
                    <>
                      <Upload className="mr-2 h-4 w-4" />
                      Import Products
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="export" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Export Products</CardTitle>
              <CardDescription>
                Export your product catalog to CSV or Excel format
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Export Format</Label>
                  <Select 
                    value={exportOptions.format} 
                    onValueChange={(value: 'csv' | 'xlsx') => 
                      setExportOptions({ ...exportOptions, format: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="csv">CSV</SelectItem>
                      <SelectItem value="xlsx">Excel (XLSX)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-3">
                <Label>Include Data</Label>
                <div className="grid grid-cols-2 gap-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="include-variants"
                      checked={exportOptions.includeVariants}
                      onCheckedChange={(checked) => 
                        setExportOptions({ ...exportOptions, includeVariants: checked as boolean })
                      }
                    />
                    <Label htmlFor="include-variants">Product Variants</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="include-images"
                      checked={exportOptions.includeImages}
                      onCheckedChange={(checked) => 
                        setExportOptions({ ...exportOptions, includeImages: checked as boolean })
                      }
                    />
                    <Label htmlFor="include-images">Image URLs</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="include-categories"
                      checked={exportOptions.includeCategories}
                      onCheckedChange={(checked) => 
                        setExportOptions({ ...exportOptions, includeCategories: checked as boolean })
                      }
                    />
                    <Label htmlFor="include-categories">Categories</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="include-tags"
                      checked={exportOptions.includeTags}
                      onCheckedChange={(checked) => 
                        setExportOptions({ ...exportOptions, includeTags: checked as boolean })
                      }
                    />
                    <Label htmlFor="include-tags">Tags</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="include-inventory"
                      checked={exportOptions.includeInventory}
                      onCheckedChange={(checked) => 
                        setExportOptions({ ...exportOptions, includeInventory: checked as boolean })
                      }
                    />
                    <Label htmlFor="include-inventory">Inventory Data</Label>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={handleExport} disabled={isExporting}>
                  {isExporting ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Exporting...
                    </>
                  ) : (
                    <>
                      <Download className="mr-2 h-4 w-4" />
                      Export Products
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Preview Dialog */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Import Preview</DialogTitle>
            <DialogDescription>
              Preview of the first few rows from your CSV file
            </DialogDescription>
          </DialogHeader>
          
          <div className="max-h-96 overflow-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {previewData.map((row, index) => (
                  <TableRow key={index}>
                    <TableCell>{row.title}</TableCell>
                    <TableCell>{row.sku}</TableCell>
                    <TableCell>R{row.price}</TableCell>
                    <TableCell>
                      <Badge variant={row.status === 'active' ? 'default' : 'secondary'}>
                        {row.status}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPreview(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
