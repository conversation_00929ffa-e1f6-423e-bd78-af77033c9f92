'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts'
import {
  Package,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  DollarSign,
  ShoppingCart,
  Eye,
  Star,
  Users,
  Layers,
  Tag,
  Gift
} from 'lucide-react'

interface DashboardStats {
  totalProducts: number
  activeProducts: number
  draftProducts: number
  outOfStockProducts: number
  totalVariants: number
  totalCategories: number
  totalBundles: number
  averageRating: number
  totalReviews: number
  revenueThisMonth: number
  ordersThisMonth: number
  topSellingProducts: Array<{
    id: string
    title: string
    sales: number
    revenue: number
  }>
  categoryDistribution: Array<{
    name: string
    count: number
    percentage: number
  }>
  inventoryAlerts: Array<{
    id: string
    title: string
    sku: string
    stock: number
    type: 'low' | 'out'
  }>
  recentActivity: Array<{
    id: string
    action: string
    product: string
    timestamp: Date
    user: string
  }>
}

export function ProductManagementDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')

  useEffect(() => {
    fetchDashboardStats()
  }, [timeRange])

  const fetchDashboardStats = async () => {
    try {
      setLoading(true)
      // This would be replaced with actual API calls
      const mockStats: DashboardStats = {
        totalProducts: 156,
        activeProducts: 142,
        draftProducts: 14,
        outOfStockProducts: 8,
        totalVariants: 324,
        totalCategories: 12,
        totalBundles: 6,
        averageRating: 4.6,
        totalReviews: 89,
        revenueThisMonth: 45670.50,
        ordersThisMonth: 234,
        topSellingProducts: [
          { id: '1', title: 'Kids Cotton T-Shirt', sales: 45, revenue: 1350 },
          { id: '2', title: 'Summer Dress', sales: 38, revenue: 1520 },
          { id: '3', title: 'Denim Shorts', sales: 32, revenue: 960 },
          { id: '4', title: 'Sneakers', sales: 28, revenue: 1680 },
          { id: '5', title: 'Hoodie', sales: 25, revenue: 1250 }
        ],
        categoryDistribution: [
          { name: 'T-Shirts', count: 45, percentage: 28.8 },
          { name: 'Dresses', count: 32, percentage: 20.5 },
          { name: 'Pants', count: 28, percentage: 17.9 },
          { name: 'Shoes', count: 24, percentage: 15.4 },
          { name: 'Accessories', count: 18, percentage: 11.5 },
          { name: 'Others', count: 9, percentage: 5.8 }
        ],
        inventoryAlerts: [
          { id: '1', title: 'Red T-Shirt Size M', sku: 'TSH-RED-M', stock: 2, type: 'low' },
          { id: '2', title: 'Blue Dress Size S', sku: 'DRS-BLU-S', stock: 0, type: 'out' },
          { id: '3', title: 'White Sneakers Size 8', sku: 'SNK-WHT-8', stock: 1, type: 'low' }
        ],
        recentActivity: [
          { id: '1', action: 'Created', product: 'Summer Collection Dress', timestamp: new Date(), user: 'Admin' },
          { id: '2', action: 'Updated', product: 'Kids T-Shirt Variants', timestamp: new Date(), user: 'Admin' },
          { id: '3', action: 'Published', product: 'New Arrivals Bundle', timestamp: new Date(), user: 'Admin' }
        ]
      }
      setStats(mockStats)
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount)
  }

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

  if (loading || !stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Product Management</h1>
          <p className="text-muted-foreground">
            Overview of your product catalog and performance
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            Export Report
          </Button>
          <Button size="sm">
            <Package className="mr-2 h-4 w-4" />
            Add Product
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+12%</span> from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Products</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeProducts}</div>
            <p className="text-xs text-muted-foreground">
              {stats.draftProducts} drafts, {stats.outOfStockProducts} out of stock
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Variants</CardTitle>
            <Layers className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalVariants}</div>
            <p className="text-xs text-muted-foreground">
              Across {stats.totalProducts} products
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageRating}</div>
            <p className="text-xs text-muted-foreground">
              From {stats.totalReviews} reviews
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Selling Products */}
        <Card>
          <CardHeader>
            <CardTitle>Top Selling Products</CardTitle>
            <CardDescription>Best performing products this month</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={stats.topSellingProducts}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="title" 
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis />
                <Tooltip />
                <Bar dataKey="sales" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Category Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Category Distribution</CardTitle>
            <CardDescription>Product distribution by category</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={stats.categoryDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percentage }) => `${name} ${percentage}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {stats.categoryDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Tabs */}
      <Tabs defaultValue="inventory" className="space-y-4">
        <TabsList>
          <TabsTrigger value="inventory">Inventory Alerts</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="inventory" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="mr-2 h-5 w-5 text-orange-500" />
                Inventory Alerts
              </CardTitle>
              <CardDescription>
                Products that need attention
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stats.inventoryAlerts.map((alert) => (
                  <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        alert.type === 'out' ? 'bg-red-500' : 'bg-orange-500'
                      }`} />
                      <div>
                        <p className="font-medium">{alert.title}</p>
                        <p className="text-sm text-muted-foreground">SKU: {alert.sku}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant={alert.type === 'out' ? 'destructive' : 'secondary'}>
                        {alert.stock === 0 ? 'Out of Stock' : `${alert.stock} left`}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest changes to your product catalog
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stats.recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <div className="flex-1">
                      <p className="text-sm">
                        <span className="font-medium">{activity.action}</span> {activity.product}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        by {activity.user} • {activity.timestamp.toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Revenue This Month</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600">
                  {formatCurrency(stats.revenueThisMonth)}
                </div>
                <p className="text-sm text-muted-foreground">
                  From {stats.ordersThisMonth} orders
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Product Bundles</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{stats.totalBundles}</div>
                <p className="text-sm text-muted-foreground">
                  Active product bundles
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
