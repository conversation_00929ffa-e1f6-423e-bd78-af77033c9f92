'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Template, 
  Search, 
  Star, 
  Clock, 
  Tag,
  Shirt,
  Baby,
  Crown,
  Footprints
} from 'lucide-react'

interface ProductTemplate {
  id: string
  name: string
  description: string
  category: string
  ageGroup: string
  price: number
  images: string[]
  tags: string[]
  isPopular: boolean
  lastUsed?: string
  icon: React.ComponentType<{ className?: string }>
  template: {
    name: string
    description: string
    short_description: string
    regular_price: number
    category_ids: number[]
    manage_stock: boolean
    stock_quantity: number
    weight: string
    dimensions: {
      length: string
      width: string
      height: string
    }
  }
}

interface ProductTemplatesProps {
  onTemplateSelect: (template: ProductTemplate) => void
}

export function ProductTemplates({ onTemplateSelect }: ProductTemplatesProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedAgeGroup, setSelectedAgeGroup] = useState('all')

  const templates: ProductTemplate[] = [
    {
      id: '1',
      name: 'Kids Cotton T-Shirt',
      description: 'Basic cotton t-shirt template for kids aged 2-12',
      category: 'T-Shirts',
      ageGroup: 'Kids (2-12)',
      price: 199.99,
      images: ['/images/templates/kids-tshirt.jpg'],
      tags: ['cotton', 'basic', 'everyday'],
      isPopular: true,
      lastUsed: '2024-01-15',
      icon: Shirt,
      template: {
        name: 'Kids Cotton T-Shirt - [Color]',
        description: 'Comfortable 100% cotton t-shirt perfect for everyday wear. Made from soft, breathable fabric that\'s gentle on sensitive skin. Machine washable and durable for active kids.',
        short_description: 'Soft cotton t-shirt for kids - comfortable and durable',
        regular_price: 199.99,
        category_ids: [1],
        manage_stock: true,
        stock_quantity: 50,
        weight: '0.15',
        dimensions: {
          length: '40',
          width: '30',
          height: '2'
        }
      }
    },
    {
      id: '2',
      name: 'Baby Onesie',
      description: 'Soft onesie template for babies 0-24 months',
      category: 'Onesies',
      ageGroup: 'Baby (0-24m)',
      price: 149.99,
      images: ['/images/templates/baby-onesie.jpg'],
      tags: ['baby', 'soft', 'organic'],
      isPopular: true,
      lastUsed: '2024-01-12',
      icon: Baby,
      template: {
        name: 'Baby Organic Onesie - [Size]',
        description: 'Ultra-soft organic cotton onesie for babies. Features snap closures for easy diaper changes and envelope shoulders for easy dressing. Hypoallergenic and perfect for sensitive baby skin.',
        short_description: 'Organic cotton onesie with snap closures',
        regular_price: 149.99,
        category_ids: [2],
        manage_stock: true,
        stock_quantity: 75,
        weight: '0.08',
        dimensions: {
          length: '35',
          width: '25',
          height: '1'
        }
      }
    },
    {
      id: '3',
      name: 'Kids Denim Jeans',
      description: 'Durable denim jeans for active kids',
      category: 'Jeans',
      ageGroup: 'Kids (2-12)',
      price: 349.99,
      images: ['/images/templates/kids-jeans.jpg'],
      tags: ['denim', 'durable', 'adjustable'],
      isPopular: false,
      lastUsed: '2024-01-10',
      icon: Shirt,
      template: {
        name: 'Kids Denim Jeans - [Wash]',
        description: 'High-quality denim jeans built to withstand active play. Features reinforced knees, adjustable waistband, and classic 5-pocket styling. Pre-washed for comfort and to prevent shrinking.',
        short_description: 'Durable denim jeans with adjustable waist',
        regular_price: 349.99,
        category_ids: [3],
        manage_stock: true,
        stock_quantity: 30,
        weight: '0.4',
        dimensions: {
          length: '60',
          width: '35',
          height: '3'
        }
      }
    },
    {
      id: '4',
      name: 'Girls Summer Dress',
      description: 'Light and airy summer dress for girls',
      category: 'Dresses',
      ageGroup: 'Girls (2-12)',
      price: 299.99,
      images: ['/images/templates/summer-dress.jpg'],
      tags: ['summer', 'light', 'pretty'],
      isPopular: true,
      lastUsed: '2024-01-08',
      icon: Crown,
      template: {
        name: 'Girls Summer Dress - [Pattern]',
        description: 'Beautiful summer dress made from lightweight, breathable fabric. Perfect for warm weather with a comfortable fit that allows for easy movement. Features cute details and vibrant colors.',
        short_description: 'Lightweight summer dress for girls',
        regular_price: 299.99,
        category_ids: [4],
        manage_stock: true,
        stock_quantity: 40,
        weight: '0.2',
        dimensions: {
          length: '50',
          width: '35',
          height: '2'
        }
      }
    },
    {
      id: '5',
      name: 'Kids Sneakers',
      description: 'Comfortable sneakers for active kids',
      category: 'Shoes',
      ageGroup: 'Kids (2-12)',
      price: 449.99,
      images: ['/images/templates/kids-sneakers.jpg'],
      tags: ['shoes', 'comfortable', 'active'],
      isPopular: false,
      lastUsed: '2024-01-05',
      icon: Footprints,
      template: {
        name: 'Kids Athletic Sneakers - [Color]',
        description: 'High-quality athletic sneakers designed for active kids. Features cushioned sole, breathable mesh upper, and secure velcro straps for easy on/off. Perfect for sports and everyday wear.',
        short_description: 'Athletic sneakers with velcro straps',
        regular_price: 449.99,
        category_ids: [5],
        manage_stock: true,
        stock_quantity: 25,
        weight: '0.6',
        dimensions: {
          length: '25',
          width: '15',
          height: '10'
        }
      }
    },
    {
      id: '6',
      name: 'Boys Polo Shirt',
      description: 'Smart casual polo shirt for boys',
      category: 'Shirts',
      ageGroup: 'Boys (2-12)',
      price: 249.99,
      images: ['/images/templates/boys-polo.jpg'],
      tags: ['polo', 'smart', 'casual'],
      isPopular: false,
      lastUsed: '2024-01-03',
      icon: Shirt,
      template: {
        name: 'Boys Polo Shirt - [Color]',
        description: 'Classic polo shirt perfect for smart casual occasions. Made from comfortable cotton blend with ribbed collar and cuffs. Features traditional button placket and side vents for comfort.',
        short_description: 'Classic polo shirt for boys',
        regular_price: 249.99,
        category_ids: [6],
        manage_stock: true,
        stock_quantity: 35,
        weight: '0.18',
        dimensions: {
          length: '42',
          width: '32',
          height: '2'
        }
      }
    }
  ]

  // Filter templates
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory
    const matchesAgeGroup = selectedAgeGroup === 'all' || template.ageGroup === selectedAgeGroup
    
    return matchesSearch && matchesCategory && matchesAgeGroup
  })

  // Get unique categories and age groups
  const categories = Array.from(new Set(templates.map(t => t.category)))
  const ageGroups = Array.from(new Set(templates.map(t => t.ageGroup)))

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(price)
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
        
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map(category => (
              <SelectItem key={category} value={category}>{category}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select value={selectedAgeGroup} onValueChange={setSelectedAgeGroup}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Age Group" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Ages</SelectItem>
            {ageGroups.map(ageGroup => (
              <SelectItem key={ageGroup} value={ageGroup}>{ageGroup}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Popular Templates */}
      {searchQuery === '' && selectedCategory === 'all' && selectedAgeGroup === 'all' && (
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Star className="h-5 w-5 text-yellow-500" />
            <h3 className="text-lg font-semibold">Popular Templates</h3>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {templates.filter(t => t.isPopular).map(template => (
              <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <template.icon className="h-5 w-5 text-blue-600" />
                      <CardTitle className="text-base">{template.name}</CardTitle>
                    </div>
                    <Badge variant="secondary">Popular</Badge>
                  </div>
                  <CardDescription className="text-sm">
                    {template.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Starting price:</span>
                    <span className="font-medium">{formatPrice(template.price)}</span>
                  </div>
                  
                  <div className="flex flex-wrap gap-1">
                    {template.tags.slice(0, 3).map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  
                  <Button 
                    className="w-full" 
                    size="sm"
                    onClick={() => onTemplateSelect(template)}
                  >
                    <Template className="mr-2 h-4 w-4" />
                    Use Template
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* All Templates */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">
            {searchQuery || selectedCategory !== 'all' || selectedAgeGroup !== 'all' 
              ? 'Search Results' 
              : 'All Templates'
            }
          </h3>
          <span className="text-sm text-muted-foreground">
            {filteredTemplates.length} template{filteredTemplates.length !== 1 ? 's' : ''}
          </span>
        </div>
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredTemplates.map(template => (
            <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <template.icon className="h-5 w-5 text-blue-600" />
                    <CardTitle className="text-base">{template.name}</CardTitle>
                  </div>
                  {template.isPopular && (
                    <Badge variant="secondary">
                      <Star className="mr-1 h-3 w-3" />
                      Popular
                    </Badge>
                  )}
                </div>
                <CardDescription className="text-sm">
                  {template.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Category:</span>
                  <Badge variant="outline">{template.category}</Badge>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Age Group:</span>
                  <span className="font-medium">{template.ageGroup}</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Starting price:</span>
                  <span className="font-medium">{formatPrice(template.price)}</span>
                </div>
                
                {template.lastUsed && (
                  <div className="flex items-center text-xs text-muted-foreground">
                    <Clock className="mr-1 h-3 w-3" />
                    Last used: {new Date(template.lastUsed).toLocaleDateString()}
                  </div>
                )}
                
                <div className="flex flex-wrap gap-1">
                  {template.tags.slice(0, 3).map(tag => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      <Tag className="mr-1 h-2 w-2" />
                      {tag}
                    </Badge>
                  ))}
                </div>
                
                <Button 
                  className="w-full" 
                  size="sm"
                  onClick={() => onTemplateSelect(template)}
                >
                  <Template className="mr-2 h-4 w-4" />
                  Use Template
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {filteredTemplates.length === 0 && (
          <div className="text-center py-8">
            <Template className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
            <p className="text-gray-500">
              Try adjusting your search criteria or browse all templates.
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
