'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Package, 
  Plus, 
  BarChart3, 
  Settings, 
  Tags, 
  FolderOpen,
  ArrowLeft
} from 'lucide-react'
import { EnhancedProductList } from './enhanced-product-list'
import { ProductForm } from './product-form'
import { ProductAnalytics } from './product-analytics'
import { useProductMutations } from '@/lib/ecommerce/hooks/use-products'
import type { Product, CreateProductInput, UpdateProductInput } from '@/lib/ecommerce/types'

type ViewMode = 'list' | 'create' | 'edit' | 'analytics' | 'categories' | 'collections'

export function ProductsManagementPage() {
  const [currentView, setCurrentView] = useState<ViewMode>('list')
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [activeTab, setActiveTab] = useState('products')

  const { 
    createProduct, 
    updateProduct, 
    loading, 
    error, 
    clearError 
  } = useProductMutations()

  const handleCreateProduct = () => {
    setSelectedProduct(null)
    setCurrentView('create')
  }

  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product)
    setCurrentView('edit')
  }

  const handleViewProduct = (product: Product) => {
    // For now, redirect to edit - could implement a read-only view
    handleEditProduct(product)
  }

  const handleSaveProduct = async (data: CreateProductInput | UpdateProductInput) => {
    try {
      let result
      if ('id' in data && data.id) {
        // Update existing product
        result = await updateProduct(data as UpdateProductInput)
      } else {
        // Create new product
        result = await createProduct(data as CreateProductInput)
      }

      if (result) {
        setCurrentView('list')
        setSelectedProduct(null)
      }
    } catch (error) {
      console.error('Error saving product:', error)
    }
  }

  const handleCancel = () => {
    setCurrentView('list')
    setSelectedProduct(null)
    clearError()
  }

  const renderHeader = () => {
    switch (currentView) {
      case 'create':
        return (
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={handleCancel}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Create Product</h1>
              <p className="text-muted-foreground">Add a new product to your store</p>
            </div>
          </div>
        )
      case 'edit':
        return (
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={handleCancel}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Edit Product</h1>
              <p className="text-muted-foreground">
                {selectedProduct ? `Editing "${selectedProduct.title}"` : 'Update product information'}
              </p>
            </div>
          </div>
        )
      case 'analytics':
        return (
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Product Analytics</h1>
            <p className="text-muted-foreground">Performance insights and metrics</p>
          </div>
        )
      default:
        return (
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Products</h1>
              <p className="text-muted-foreground">Manage your product catalog</p>
            </div>
            <Button onClick={handleCreateProduct}>
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </div>
        )
    }
  }

  const renderContent = () => {
    switch (currentView) {
      case 'create':
      case 'edit':
        return (
          <ProductForm
            product={selectedProduct || undefined}
            onSubmit={handleSaveProduct}
            onCancel={handleCancel}
            loading={loading}
          />
        )
      case 'analytics':
        return <ProductAnalytics />
      default:
        return (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="products" className="flex items-center space-x-2">
                <Package className="h-4 w-4" />
                <span>Products</span>
              </TabsTrigger>
              <TabsTrigger value="analytics" className="flex items-center space-x-2">
                <BarChart3 className="h-4 w-4" />
                <span>Analytics</span>
              </TabsTrigger>
              <TabsTrigger value="categories" className="flex items-center space-x-2">
                <FolderOpen className="h-4 w-4" />
                <span>Categories</span>
              </TabsTrigger>
              <TabsTrigger value="collections" className="flex items-center space-x-2">
                <Tags className="h-4 w-4" />
                <span>Collections</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="products">
              <EnhancedProductList
                onCreateProduct={handleCreateProduct}
                onEditProduct={handleEditProduct}
                onViewProduct={handleViewProduct}
              />
            </TabsContent>

            <TabsContent value="analytics">
              <ProductAnalytics />
            </TabsContent>

            <TabsContent value="categories">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FolderOpen className="mr-2 h-5 w-5" />
                    Category Management
                  </CardTitle>
                  <CardDescription>
                    Organize your products into categories for better navigation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <FolderOpen className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-2 text-sm font-semibold">Category Management</h3>
                    <p className="mt-1 text-sm text-muted-foreground">
                      Category management interface coming soon.
                    </p>
                    <Badge variant="secondary" className="mt-2">Coming Soon</Badge>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="collections">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Tags className="mr-2 h-5 w-5" />
                    Collection Management
                  </CardTitle>
                  <CardDescription>
                    Create and manage product collections for marketing and organization
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Tags className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-2 text-sm font-semibold">Collection Management</h3>
                    <p className="mt-1 text-sm text-muted-foreground">
                      Collection management interface coming soon.
                    </p>
                    <Badge variant="secondary" className="mt-2">Coming Soon</Badge>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )
    }
  }

  return (
    <div className="space-y-6">
      {renderHeader()}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <div className="text-red-600 text-sm">{error}</div>
              <Button variant="outline" size="sm" onClick={clearError}>
                Dismiss
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
      {renderContent()}
    </div>
  )
}
