'use client'

import { useState, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Upload, 
  Download, 
  FileSpreadsheet, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Loader2,
  Eye,
  Trash2
} from 'lucide-react'

interface BulkProductImportProps {
  onImport: (products: any[]) => Promise<void>
  loading?: boolean
}

interface ImportedProduct {
  id: string
  name: string
  sku: string
  price: number
  stock: number
  category: string
  status: 'valid' | 'warning' | 'error'
  errors: string[]
  warnings: string[]
}

export function BulkProductImport({ onImport, loading = false }: BulkProductImportProps) {
  const [importedProducts, setImportedProducts] = useState<ImportedProduct[]>([])
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isProcessing, setIsProcessing] = useState(false)
  const [validationResults, setValidationResults] = useState<{
    valid: number
    warnings: number
    errors: number
  }>({ valid: 0, warnings: 0, errors: 0 })
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsProcessing(true)
    setUploadProgress(0)

    try {
      // Simulate file processing
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval)
            return 100
          }
          return prev + 10
        })
      }, 200)

      // Parse CSV/Excel file (simplified simulation)
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Mock imported data
      const mockProducts: ImportedProduct[] = [
        {
          id: '1',
          name: 'Kids Cotton T-Shirt - Blue',
          sku: 'KCT-001-BL',
          price: 199.99,
          stock: 50,
          category: 'T-Shirts',
          status: 'valid',
          errors: [],
          warnings: []
        },
        {
          id: '2',
          name: 'Toddler Denim Jeans',
          sku: 'TDJ-002',
          price: 349.99,
          stock: 25,
          category: 'Jeans',
          status: 'warning',
          errors: [],
          warnings: ['No product description provided']
        },
        {
          id: '3',
          name: 'Baby Onesie Set',
          sku: '',
          price: 0,
          stock: 0,
          category: '',
          status: 'error',
          errors: ['SKU is required', 'Price must be greater than 0', 'Category is required'],
          warnings: []
        },
        {
          id: '4',
          name: 'Kids Summer Dress - Pink',
          sku: 'KSD-004-PK',
          price: 299.99,
          stock: 30,
          category: 'Dresses',
          status: 'valid',
          errors: [],
          warnings: []
        },
        {
          id: '5',
          name: 'Boys Polo Shirt',
          sku: 'BPS-005',
          price: 249.99,
          stock: 15,
          category: 'Shirts',
          status: 'warning',
          errors: [],
          warnings: ['Low stock quantity']
        }
      ]

      setImportedProducts(mockProducts)
      
      // Calculate validation results
      const results = {
        valid: mockProducts.filter(p => p.status === 'valid').length,
        warnings: mockProducts.filter(p => p.status === 'warning').length,
        errors: mockProducts.filter(p => p.status === 'error').length
      }
      setValidationResults(results)

    } catch (error) {
      console.error('Error processing file:', error)
    } finally {
      setIsProcessing(false)
      setUploadProgress(100)
    }
  }

  const handleImport = async () => {
    const validProducts = importedProducts.filter(p => p.status !== 'error')
    
    if (validProducts.length === 0) {
      alert('No valid products to import')
      return
    }

    // Transform to WooCommerce format
    const wooCommerceProducts = validProducts.map(product => ({
      name: product.name,
      sku: product.sku,
      regular_price: product.price.toString(),
      manage_stock: true,
      stock_quantity: product.stock,
      categories: [{ name: product.category }],
      status: 'publish'
    }))

    await onImport(wooCommerceProducts)
  }

  const removeProduct = (id: string) => {
    setImportedProducts(prev => prev.filter(p => p.id !== id))
    
    // Recalculate validation results
    const remaining = importedProducts.filter(p => p.id !== id)
    const results = {
      valid: remaining.filter(p => p.status === 'valid').length,
      warnings: remaining.filter(p => p.status === 'warning').length,
      errors: remaining.filter(p => p.status === 'error').length
    }
    setValidationResults(results)
  }

  const downloadTemplate = () => {
    // Create CSV template
    const csvContent = `name,sku,regular_price,sale_price,stock_quantity,category,description,weight,length,width,height
Kids Cotton T-Shirt,KCT-001,199.99,,50,T-Shirts,Comfortable cotton t-shirt for kids,0.2,30,25,1
Toddler Jeans,TJ-002,349.99,299.99,25,Jeans,Durable denim jeans for toddlers,0.4,35,30,2`

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'product-import-template.csv'
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const getStatusIcon = (status: ImportedProduct['status']) => {
    switch (status) {
      case 'valid':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusBadge = (status: ImportedProduct['status']) => {
    switch (status) {
      case 'valid':
        return <Badge variant="default" className="bg-green-100 text-green-800">Valid</Badge>
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Warning</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Upload Section */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Upload className="h-5 w-5" />
              <span>Upload File</span>
            </CardTitle>
            <CardDescription>
              Upload a CSV or Excel file with your product data
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <FileSpreadsheet className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-sm text-gray-600 mb-4">
                Drag and drop your file here, or click to browse
              </p>
              <Button 
                onClick={() => fileInputRef.current?.click()}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Choose File
                  </>
                )}
              </Button>
              <input
                ref={fileInputRef}
                type="file"
                accept=".csv,.xlsx,.xls"
                onChange={handleFileUpload}
                className="hidden"
              />
            </div>
            
            {isProcessing && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Processing file...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <Progress value={uploadProgress} />
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Download className="h-5 w-5" />
              <span>Download Template</span>
            </CardTitle>
            <CardDescription>
              Get a sample CSV file with the correct format
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-gray-600">
              Download our template to ensure your data is formatted correctly for import.
            </p>
            <Button variant="outline" onClick={downloadTemplate}>
              <Download className="mr-2 h-4 w-4" />
              Download Template
            </Button>
            
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Make sure your CSV includes columns for: name, sku, regular_price, stock_quantity, and category.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>

      {/* Validation Results */}
      {importedProducts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Import Preview</CardTitle>
            <CardDescription>
              Review your products before importing to WooCommerce
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Summary */}
            <div className="grid gap-4 md:grid-cols-3">
              <div className="p-4 border rounded-lg text-center">
                <div className="text-2xl font-bold text-green-600">{validationResults.valid}</div>
                <div className="text-sm text-muted-foreground">Valid Products</div>
              </div>
              <div className="p-4 border rounded-lg text-center">
                <div className="text-2xl font-bold text-yellow-600">{validationResults.warnings}</div>
                <div className="text-sm text-muted-foreground">Warnings</div>
              </div>
              <div className="p-4 border rounded-lg text-center">
                <div className="text-2xl font-bold text-red-600">{validationResults.errors}</div>
                <div className="text-sm text-muted-foreground">Errors</div>
              </div>
            </div>

            {/* Product List */}
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Status</TableHead>
                    <TableHead>Product Name</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Stock</TableHead>
                    <TableHead>Issues</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {importedProducts.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(product.status)}
                          {getStatusBadge(product.status)}
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{product.name}</TableCell>
                      <TableCell className="font-mono text-sm">{product.sku || '-'}</TableCell>
                      <TableCell>R{product.price.toFixed(2)}</TableCell>
                      <TableCell>{product.stock}</TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {product.errors.map((error, index) => (
                            <div key={index} className="text-xs text-red-600 flex items-center">
                              <XCircle className="h-3 w-3 mr-1" />
                              {error}
                            </div>
                          ))}
                          {product.warnings.map((warning, index) => (
                            <div key={index} className="text-xs text-yellow-600 flex items-center">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              {warning}
                            </div>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => removeProduct(product.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Import Actions */}
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                {validationResults.valid} products ready to import
                {validationResults.errors > 0 && ` • ${validationResults.errors} products with errors will be skipped`}
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" onClick={() => setImportedProducts([])}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleImport} 
                  disabled={loading || validationResults.valid === 0}
                >
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Import {validationResults.valid} Products
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
