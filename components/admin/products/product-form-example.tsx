'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { ProductForm } from './product-form'
import type { Product } from '@/lib/ecommerce/types'

interface ProductFormExampleProps {
  product?: Product
  mode?: 'create' | 'edit'
}

export function ProductFormExample({ product, mode = 'create' }: ProductFormExampleProps) {
  const router = useRouter()
  const [isOpen, setIsOpen] = useState(true)

  const handleSuccess = (savedProduct: Product) => {
    console.log('Product saved successfully:', savedProduct)
    
    // Navigate to the product detail page or products list
    if (mode === 'create') {
      router.push(`/admin/products/${savedProduct.id}`)
    } else {
      // Optionally refresh the page or navigate somewhere
      router.refresh()
    }
    
    setIsOpen(false)
  }

  const handleCancel = () => {
    console.log('Form cancelled')
    setIsOpen(false)
    router.back() // Go back to previous page
  }

  if (!isOpen) {
    return null
  }

  return (
    <div className="container mx-auto py-6">
      <ProductForm
        product={product}
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  )
}

// Example usage in a page component:
/*
// app/admin/products/new/page.tsx
export default function NewProductPage() {
  return <ProductFormExample mode="create" />
}

// app/admin/products/[id]/edit/page.tsx
export default function EditProductPage({ params }: { params: { id: string } }) {
  const { product, loading, error } = useProduct({ productId: params.id })
  
  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error}</div>
  if (!product) return <div>Product not found</div>
  
  return <ProductFormExample product={product} mode="edit" />
}
*/