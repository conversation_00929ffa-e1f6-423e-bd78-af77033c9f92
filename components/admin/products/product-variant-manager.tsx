'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  <PERSON>bsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import {
  Plus,
  Edit,
  Trash2,
  Copy,
  Package,
  Wand2,
  Upload,
  Download,
  MoreHorizontal
} from 'lucide-react'
import { ZarPriceInput } from '../zar-price-input'
import { ImageUpload } from '../image-upload'
import type { ProductVariant, CreateVariantInput, Product } from '@/lib/ecommerce/types'

interface ProductVariantManagerProps {
  product: Product
  variants: ProductVariant[]
  onVariantsChange: (variants: ProductVariant[]) => void
}

export function ProductVariantManager({ product, variants, onVariantsChange }: ProductVariantManagerProps) {
  const [isAddingVariant, setIsAddingVariant] = useState(false)
  const [editingVariant, setEditingVariant] = useState<ProductVariant | null>(null)
  const [selectedVariants, setSelectedVariants] = useState<string[]>([])
  const [bulkAction, setBulkAction] = useState<string>('')
  const [isGeneratingVariants, setIsGeneratingVariants] = useState(false)

  const [newVariant, setNewVariant] = useState<CreateVariantInput>({
    title: '',
    sku: '',
    price: { amount: 0, currency: 'ZAR' },
    compareAtPrice: undefined,
    costPerItem: undefined,
    weight: undefined,
    weightUnit: 'kg',
    inventoryQuantity: 0,
    inventoryPolicy: 'deny',
    fulfillmentService: 'manual',
    inventoryManagement: true,
    options: [],
    barcode: '',
    taxable: true,
    requiresShipping: true,
    trackQuantity: true,
    continueSellingWhenOutOfStock: false,
    metafields: {}
  })

  const handleAddVariant = async () => {
    try {
      // Validate variant data
      if (!newVariant.title.trim()) {
        throw new Error('Variant title is required')
      }

      if (!newVariant.price || newVariant.price.amount <= 0) {
        throw new Error('Valid price is required')
      }

      // Generate SKU if not provided
      const sku = newVariant.sku || `${product.handle}-${variants.length + 1}`

      const variantToAdd: ProductVariant = {
        id: `temp-${Date.now()}`, // Temporary ID
        productId: product.id,
        sku,
        title: newVariant.title,
        price: newVariant.price,
        compareAtPrice: newVariant.compareAtPrice,
        costPerItem: newVariant.costPerItem,
        weight: newVariant.weight,
        weightUnit: newVariant.weightUnit,
        inventoryQuantity: newVariant.inventoryQuantity || 0,
        inventoryPolicy: newVariant.inventoryPolicy || 'deny',
        fulfillmentService: newVariant.fulfillmentService || 'manual',
        inventoryManagement: newVariant.inventoryManagement ?? true,
        options: newVariant.options || [],
        available: true,
        barcode: newVariant.barcode,
        taxable: newVariant.taxable ?? true,
        requiresShipping: newVariant.requiresShipping ?? true,
        trackQuantity: newVariant.trackQuantity ?? true,
        continueSellingWhenOutOfStock: newVariant.continueSellingWhenOutOfStock ?? false,
        metafields: newVariant.metafields,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      onVariantsChange([...variants, variantToAdd])
      setIsAddingVariant(false)
      resetNewVariant()
    } catch (error) {
      console.error('Error adding variant:', error)
      // Handle error (show toast, etc.)
    }
  }

  const handleEditVariant = (variant: ProductVariant) => {
    setEditingVariant(variant)
  }

  const handleUpdateVariant = async (updatedVariant: ProductVariant) => {
    const updatedVariants = variants.map(v => 
      v.id === updatedVariant.id ? updatedVariant : v
    )
    onVariantsChange(updatedVariants)
    setEditingVariant(null)
  }

  const handleDeleteVariant = (variantId: string) => {
    const updatedVariants = variants.filter(v => v.id !== variantId)
    onVariantsChange(updatedVariants)
  }

  const handleDuplicateVariant = (variant: ProductVariant) => {
    const duplicatedVariant: ProductVariant = {
      ...variant,
      id: `temp-${Date.now()}`,
      title: `${variant.title} (Copy)`,
      sku: `${variant.sku}-copy`,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    onVariantsChange([...variants, duplicatedVariant])
  }

  const handleBulkAction = async () => {
    if (!bulkAction || selectedVariants.length === 0) return

    switch (bulkAction) {
      case 'delete':
        const remainingVariants = variants.filter(v => !selectedVariants.includes(v.id))
        onVariantsChange(remainingVariants)
        break
      case 'enable':
        const enabledVariants = variants.map(v => 
          selectedVariants.includes(v.id) ? { ...v, available: true } : v
        )
        onVariantsChange(enabledVariants)
        break
      case 'disable':
        const disabledVariants = variants.map(v => 
          selectedVariants.includes(v.id) ? { ...v, available: false } : v
        )
        onVariantsChange(disabledVariants)
        break
    }

    setSelectedVariants([])
    setBulkAction('')
  }

  const handleGenerateVariants = async () => {
    setIsGeneratingVariants(true)
    try {
      // This would call the API to generate variants from product options
      // For now, we'll simulate it
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Generate sample variants based on product options
      const generatedVariants: ProductVariant[] = []
      
      // This is a simplified example - in reality, you'd generate all combinations
      const colors = ['Red', 'Blue', 'Green']
      const sizes = ['S', 'M', 'L']
      
      colors.forEach((color, colorIndex) => {
        sizes.forEach((size, sizeIndex) => {
          generatedVariants.push({
            id: `generated-${colorIndex}-${sizeIndex}`,
            productId: product.id,
            sku: `${product.handle}-${color.toLowerCase()}-${size.toLowerCase()}`,
            title: `${color} / ${size}`,
            price: { amount: 299.99, currency: 'ZAR' },
            inventoryQuantity: 10,
            inventoryPolicy: 'deny',
            fulfillmentService: 'manual',
            inventoryManagement: true,
            options: [
              { name: 'Color', value: color },
              { name: 'Size', value: size }
            ],
            available: true,
            taxable: true,
            requiresShipping: true,
            trackQuantity: true,
            continueSellingWhenOutOfStock: false,
            createdAt: new Date(),
            updatedAt: new Date()
          })
        })
      })

      onVariantsChange([...variants, ...generatedVariants])
    } catch (error) {
      console.error('Error generating variants:', error)
    } finally {
      setIsGeneratingVariants(false)
    }
  }

  const resetNewVariant = () => {
    setNewVariant({
      title: '',
      sku: '',
      price: { amount: 0, currency: 'ZAR' },
      compareAtPrice: undefined,
      costPerItem: undefined,
      weight: undefined,
      weightUnit: 'kg',
      inventoryQuantity: 0,
      inventoryPolicy: 'deny',
      fulfillmentService: 'manual',
      inventoryManagement: true,
      options: [],
      barcode: '',
      taxable: true,
      requiresShipping: true,
      trackQuantity: true,
      continueSellingWhenOutOfStock: false,
      metafields: {}
    })
  }

  const formatPrice = (amount: number, currency: string = 'ZAR') => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Product Variants</h3>
          <p className="text-sm text-muted-foreground">
            Manage different variations of this product
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleGenerateVariants}
            disabled={isGeneratingVariants}
          >
            <Wand2 className="mr-2 h-4 w-4" />
            {isGeneratingVariants ? 'Generating...' : 'Generate Variants'}
          </Button>
          <Button
            size="sm"
            onClick={() => setIsAddingVariant(true)}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Variant
          </Button>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedVariants.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium">
                {selectedVariants.length} variant(s) selected
              </span>
              <Select value={bulkAction} onValueChange={setBulkAction}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Choose action" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="enable">Enable variants</SelectItem>
                  <SelectItem value="disable">Disable variants</SelectItem>
                  <SelectItem value="delete">Delete variants</SelectItem>
                </SelectContent>
              </Select>
              <Button
                size="sm"
                onClick={handleBulkAction}
                disabled={!bulkAction}
              >
                Apply
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedVariants([])}
              >
                Clear Selection
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Variants Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <input
                    type="checkbox"
                    checked={selectedVariants.length === variants.length && variants.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedVariants(variants.map(v => v.id))
                      } else {
                        setSelectedVariants([])
                      }
                    }}
                  />
                </TableHead>
                <TableHead>Variant</TableHead>
                <TableHead>SKU</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Inventory</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {variants.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex flex-col items-center space-y-2">
                      <Package className="h-8 w-8 text-muted-foreground" />
                      <p className="text-muted-foreground">No variants created yet</p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsAddingVariant(true)}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Add First Variant
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                variants.map((variant) => (
                  <TableRow key={variant.id}>
                    <TableCell>
                      <input
                        type="checkbox"
                        checked={selectedVariants.includes(variant.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedVariants([...selectedVariants, variant.id])
                          } else {
                            setSelectedVariants(selectedVariants.filter(id => id !== variant.id))
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{variant.title}</div>
                        {variant.options && variant.options.length > 0 && (
                          <div className="text-sm text-muted-foreground">
                            {variant.options.map(opt => `${opt.name}: ${opt.value}`).join(' • ')}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="font-mono text-sm">{variant.sku}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {formatPrice(variant.price.amount, variant.price.currency)}
                        </div>
                        {variant.compareAtPrice && (
                          <div className="text-sm text-muted-foreground line-through">
                            {formatPrice(variant.compareAtPrice.amount, variant.compareAtPrice.currency)}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{variant.inventoryQuantity} in stock</div>
                        {variant.trackQuantity && (
                          <div className="text-muted-foreground">
                            {variant.inventoryPolicy === 'continue' ? 'Continue selling' : 'Stop when out'}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={variant.available ? 'default' : 'secondary'}>
                        {variant.available ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditVariant(variant)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDuplicateVariant(variant)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteVariant(variant.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add Variant Dialog */}
      <Dialog open={isAddingVariant} onOpenChange={setIsAddingVariant}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Variant</DialogTitle>
            <DialogDescription>
              Create a new variant for {product.title}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="variant-title">Variant Title *</Label>
                <Input
                  id="variant-title"
                  value={newVariant.title}
                  onChange={(e) => setNewVariant({ ...newVariant, title: e.target.value })}
                  placeholder="e.g., Red / Large"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="variant-sku">SKU</Label>
                <Input
                  id="variant-sku"
                  value={newVariant.sku}
                  onChange={(e) => setNewVariant({ ...newVariant, sku: e.target.value })}
                  placeholder="Auto-generated if empty"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <ZarPriceInput
                label="Price *"
                value={newVariant.price.amount}
                onChange={(amount) => setNewVariant({ 
                  ...newVariant, 
                  price: { ...newVariant.price, amount } 
                })}
                required
              />
              <ZarPriceInput
                label="Compare at Price"
                value={newVariant.compareAtPrice?.amount || 0}
                onChange={(amount) => setNewVariant({ 
                  ...newVariant, 
                  compareAtPrice: amount > 0 ? { amount, currency: 'ZAR' } : undefined
                })}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="inventory-quantity">Inventory Quantity</Label>
                <Input
                  id="inventory-quantity"
                  type="number"
                  value={newVariant.inventoryQuantity}
                  onChange={(e) => setNewVariant({ 
                    ...newVariant, 
                    inventoryQuantity: parseInt(e.target.value) || 0 
                  })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="barcode">Barcode</Label>
                <Input
                  id="barcode"
                  value={newVariant.barcode}
                  onChange={(e) => setNewVariant({ ...newVariant, barcode: e.target.value })}
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="track-quantity"
                  checked={newVariant.trackQuantity}
                  onCheckedChange={(checked) => setNewVariant({ 
                    ...newVariant, 
                    trackQuantity: checked 
                  })}
                />
                <Label htmlFor="track-quantity">Track quantity</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="continue-selling"
                  checked={newVariant.continueSellingWhenOutOfStock}
                  onCheckedChange={(checked) => setNewVariant({ 
                    ...newVariant, 
                    continueSellingWhenOutOfStock: checked 
                  })}
                />
                <Label htmlFor="continue-selling">Continue selling when out of stock</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="requires-shipping"
                  checked={newVariant.requiresShipping}
                  onCheckedChange={(checked) => setNewVariant({ 
                    ...newVariant, 
                    requiresShipping: checked 
                  })}
                />
                <Label htmlFor="requires-shipping">Requires shipping</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="taxable"
                  checked={newVariant.taxable}
                  onCheckedChange={(checked) => setNewVariant({ 
                    ...newVariant, 
                    taxable: checked 
                  })}
                />
                <Label htmlFor="taxable">Taxable</Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingVariant(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddVariant}>
              Add Variant
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
