'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import {
  Mail,
  Eye,
  Save,
  X,
  Code,
  Smartphone,
  Monitor,
  Send
} from 'lucide-react'
import { toast } from 'sonner'

interface EmailTemplate {
  id: string
  name: string
  subject: string
  htmlContent: string
  textContent: string
  variables: string[]
  enabled: boolean
}

interface EmailTemplateEditorProps {
  template: EmailTemplate
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave: (template: EmailTemplate) => void
}

export function EmailTemplateEditor({ 
  template, 
  open, 
  onOpenChange, 
  onSave 
}: EmailTemplateEditorProps) {
  const [editedTemplate, setEditedTemplate] = useState<EmailTemplate>(template)
  const [previewMode, setPreviewMode] = useState<'desktop' | 'mobile'>('desktop')
  const [saving, setSaving] = useState(false)

  const handleSave = async () => {
    try {
      setSaving(true)
      // Mock save delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      onSave(editedTemplate)
      toast.success('Email template saved successfully')
      onOpenChange(false)
    } catch (error) {
      toast.error('Failed to save email template')
    } finally {
      setSaving(false)
    }
  }

  const handleSendTest = async () => {
    try {
      // Mock send test email
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Test email sent successfully')
    } catch (error) {
      toast.error('Failed to send test email')
    }
  }

  const insertVariable = (variable: string) => {
    const textarea = document.getElementById('html-content') as HTMLTextAreaElement
    if (textarea) {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const text = editedTemplate.htmlContent
      const before = text.substring(0, start)
      const after = text.substring(end, text.length)
      const newText = before + `{{${variable}}}` + after
      
      setEditedTemplate(prev => ({ ...prev, htmlContent: newText }))
      
      // Reset cursor position
      setTimeout(() => {
        textarea.focus()
        textarea.setSelectionRange(start + variable.length + 4, start + variable.length + 4)
      }, 0)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Mail className="h-5 w-5" />
            <span>Edit Email Template: {template.name}</span>
          </DialogTitle>
          <DialogDescription>
            Customize the email template content and preview how it will look
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs defaultValue="edit" className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="edit">Edit Template</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
            </TabsList>

            <TabsContent value="edit" className="flex-1 overflow-auto space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {/* Template Settings */}
                <div className="lg:col-span-1 space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Template Settings</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="subject">Email Subject</Label>
                        <Input
                          id="subject"
                          value={editedTemplate.subject}
                          onChange={(e) => setEditedTemplate(prev => ({ 
                            ...prev, 
                            subject: e.target.value 
                          }))}
                          placeholder="Enter email subject"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Available Variables</Label>
                        <div className="space-y-1">
                          {editedTemplate.variables.map((variable) => (
                            <Button
                              key={variable}
                              variant="outline"
                              size="sm"
                              className="w-full justify-start text-xs"
                              onClick={() => insertVariable(variable)}
                            >
                              {`{{${variable}}}`}
                            </Button>
                          ))}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>Template Status</Label>
                        <Badge variant={editedTemplate.enabled ? "default" : "secondary"}>
                          {editedTemplate.enabled ? "Enabled" : "Disabled"}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Content Editor */}
                <div className="lg:col-span-2 space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">HTML Content</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Textarea
                        id="html-content"
                        value={editedTemplate.htmlContent}
                        onChange={(e) => setEditedTemplate(prev => ({ 
                          ...prev, 
                          htmlContent: e.target.value 
                        }))}
                        placeholder="Enter HTML email content..."
                        className="min-h-[300px] font-mono text-sm"
                      />
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Plain Text Content</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Textarea
                        value={editedTemplate.textContent}
                        onChange={(e) => setEditedTemplate(prev => ({ 
                          ...prev, 
                          textContent: e.target.value 
                        }))}
                        placeholder="Enter plain text email content..."
                        className="min-h-[150px]"
                      />
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="flex-1 overflow-auto">
              <div className="space-y-4">
                {/* Preview Controls */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant={previewMode === 'desktop' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setPreviewMode('desktop')}
                    >
                      <Monitor className="mr-2 h-4 w-4" />
                      Desktop
                    </Button>
                    <Button
                      variant={previewMode === 'mobile' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setPreviewMode('mobile')}
                    >
                      <Smartphone className="mr-2 h-4 w-4" />
                      Mobile
                    </Button>
                  </div>
                  <Button variant="outline" size="sm" onClick={handleSendTest}>
                    <Send className="mr-2 h-4 w-4" />
                    Send Test Email
                  </Button>
                </div>

                {/* Email Preview */}
                <Card>
                  <CardHeader>
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">Subject:</div>
                      <div className="font-medium">{editedTemplate.subject}</div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div 
                      className={`border rounded-lg overflow-auto ${
                        previewMode === 'mobile' ? 'max-w-sm mx-auto' : 'w-full'
                      }`}
                      style={{ height: '400px' }}
                    >
                      <iframe
                        srcDoc={editedTemplate.htmlContent}
                        className="w-full h-full"
                        title="Email Preview"
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <Save className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Template
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
