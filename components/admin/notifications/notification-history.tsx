'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Mail,
  Smartphone,
  Bell,
  Search,
  Filter,
  Eye,
  RefreshCw,
  Download,
  Calendar,
  User,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react'

interface NotificationRecord {
  id: string
  type: 'email' | 'sms' | 'push'
  template: string
  recipient: string
  subject: string
  status: 'sent' | 'failed' | 'pending' | 'delivered'
  sentAt: string
  deliveredAt?: string
  errorMessage?: string
  metadata: {
    orderId?: string
    customerId?: string
    adminId?: string
  }
}

interface NotificationHistoryProps {
  className?: string
}

export function NotificationHistory({ className }: NotificationHistoryProps) {
  const [notifications, setNotifications] = useState<NotificationRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [selectedNotification, setSelectedNotification] = useState<NotificationRecord | null>(null)

  useEffect(() => {
    loadNotifications()
  }, [])

  const loadNotifications = async () => {
    try {
      setLoading(true)
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock data
      const mockNotifications: NotificationRecord[] = [
        {
          id: '1',
          type: 'email',
          template: 'Order Confirmation',
          recipient: '<EMAIL>',
          subject: 'Your order #CM000123 has been confirmed',
          status: 'delivered',
          sentAt: '2024-01-15T10:30:00Z',
          deliveredAt: '2024-01-15T10:30:15Z',
          metadata: {
            orderId: 'CM000123',
            customerId: 'cust_123'
          }
        },
        {
          id: '2',
          type: 'sms',
          template: 'Order Shipped',
          recipient: '+27821234567',
          subject: 'Your order has been shipped',
          status: 'sent',
          sentAt: '2024-01-15T09:15:00Z',
          metadata: {
            orderId: 'CM000122',
            customerId: 'cust_124'
          }
        },
        {
          id: '3',
          type: 'email',
          template: 'Low Stock Alert',
          recipient: '<EMAIL>',
          subject: 'Low stock alert: Blue Denim Jacket',
          status: 'failed',
          sentAt: '2024-01-15T08:45:00Z',
          errorMessage: 'SMTP connection failed',
          metadata: {
            adminId: 'admin_1'
          }
        },
        {
          id: '4',
          type: 'push',
          template: 'New Order',
          recipient: '<EMAIL>',
          subject: 'New order received',
          status: 'pending',
          sentAt: '2024-01-15T11:00:00Z',
          metadata: {
            orderId: 'CM000124',
            adminId: 'admin_1'
          }
        }
      ]
      
      setNotifications(mockNotifications)
    } catch (error) {
      console.error('Error loading notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.recipient.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.template.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || notification.status === statusFilter
    const matchesType = typeFilter === 'all' || notification.type === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'delivered':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'sent':
        return <CheckCircle className="h-4 w-4 text-blue-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'delivered':
        return <Badge variant="default">Delivered</Badge>
      case 'sent':
        return <Badge variant="secondary">Sent</Badge>
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>
      case 'pending':
        return <Badge variant="outline">Pending</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <Mail className="h-4 w-4" />
      case 'sms':
        return <Smartphone className="h-4 w-4" />
      case 'push':
        return <Bell className="h-4 w-4" />
      default:
        return <Mail className="h-4 w-4" />
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const exportHistory = async () => {
    try {
      // Mock export functionality
      const csvContent = [
        'ID,Type,Template,Recipient,Subject,Status,Sent At,Delivered At,Error',
        ...filteredNotifications.map(n => 
          `${n.id},${n.type},${n.template},${n.recipient},"${n.subject}",${n.status},${n.sentAt},${n.deliveredAt || ''},${n.errorMessage || ''}`
        )
      ].join('\n')
      
      const blob = new Blob([csvContent], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'notification-history.csv'
      a.click()
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error exporting history:', error)
    }
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Bell className="h-5 w-5" />
                <span>Notification History</span>
              </CardTitle>
              <CardDescription>
                View and manage notification delivery history
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={exportHistory}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button variant="outline" size="sm" onClick={loadNotifications} disabled={loading}>
                <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search notifications..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="sent">Sent</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="email">Email</SelectItem>
                <SelectItem value="sms">SMS</SelectItem>
                <SelectItem value="push">Push</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Notifications Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Type</TableHead>
                  <TableHead>Template</TableHead>
                  <TableHead>Recipient</TableHead>
                  <TableHead>Subject</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Sent At</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                      Loading notifications...
                    </TableCell>
                  </TableRow>
                ) : filteredNotifications.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                      No notifications found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredNotifications.map((notification) => (
                    <TableRow key={notification.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getTypeIcon(notification.type)}
                          <span className="capitalize">{notification.type}</span>
                        </div>
                      </TableCell>
                      <TableCell>{notification.template}</TableCell>
                      <TableCell className="font-mono text-sm">{notification.recipient}</TableCell>
                      <TableCell className="max-w-xs truncate">{notification.subject}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(notification.status)}
                          {getStatusBadge(notification.status)}
                        </div>
                      </TableCell>
                      <TableCell>{formatDate(notification.sentAt)}</TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <Eye className="h-3 w-3" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Notification Details</DialogTitle>
                              <DialogDescription>
                                View detailed information about this notification
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <Label className="text-sm font-medium">Type</Label>
                                  <p className="text-sm capitalize">{notification.type}</p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium">Template</Label>
                                  <p className="text-sm">{notification.template}</p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium">Recipient</Label>
                                  <p className="text-sm font-mono">{notification.recipient}</p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium">Status</Label>
                                  <div className="flex items-center space-x-2">
                                    {getStatusIcon(notification.status)}
                                    {getStatusBadge(notification.status)}
                                  </div>
                                </div>
                              </div>
                              <div>
                                <Label className="text-sm font-medium">Subject</Label>
                                <p className="text-sm">{notification.subject}</p>
                              </div>
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <Label className="text-sm font-medium">Sent At</Label>
                                  <p className="text-sm">{formatDate(notification.sentAt)}</p>
                                </div>
                                {notification.deliveredAt && (
                                  <div>
                                    <Label className="text-sm font-medium">Delivered At</Label>
                                    <p className="text-sm">{formatDate(notification.deliveredAt)}</p>
                                  </div>
                                )}
                              </div>
                              {notification.errorMessage && (
                                <div>
                                  <Label className="text-sm font-medium text-red-600">Error Message</Label>
                                  <p className="text-sm text-red-600">{notification.errorMessage}</p>
                                </div>
                              )}
                            </div>
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
