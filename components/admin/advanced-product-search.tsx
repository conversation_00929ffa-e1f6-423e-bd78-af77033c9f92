'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Checkbox } from '@/components/ui/checkbox'
import { Slider } from '@/components/ui/slider'
import {
  Search,
  Filter,
  X,
  SlidersHorizontal,
  Download,
  RefreshCw
} from 'lucide-react'
import { debounce } from 'lodash'

interface SearchFilters {
  query: string
  status: string[]
  categories: string[]
  vendors: string[]
  priceRange: [number, number]
  stockStatus: string[]
  hasVariants: boolean | null
  dateRange: {
    from: Date | null
    to: Date | null
  }
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

interface FilterOptions {
  categories: Array<{ id: string; name: string; count: number }>
  vendors: Array<{ name: string; count: number }>
  priceRange: { min: number; max: number }
  statusOptions: Array<{ value: string; label: string; count: number }>
  stockOptions: Array<{ value: string; label: string; count: number }>
}

interface AdvancedProductSearchProps {
  onFiltersChange: (filters: SearchFilters) => void
  onExport: () => void
  totalResults: number
  loading?: boolean
}

export function AdvancedProductSearch({ 
  onFiltersChange, 
  onExport, 
  totalResults, 
  loading = false 
}: AdvancedProductSearchProps) {
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    status: [],
    categories: [],
    vendors: [],
    priceRange: [0, 1000],
    stockStatus: [],
    hasVariants: null,
    dateRange: {
      from: null,
      to: null
    },
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })

  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    categories: [],
    vendors: [],
    priceRange: { min: 0, max: 1000 },
    statusOptions: [
      { value: 'active', label: 'Active', count: 142 },
      { value: 'draft', label: 'Draft', count: 14 },
      { value: 'archived', label: 'Archived', count: 8 }
    ],
    stockOptions: [
      { value: 'in_stock', label: 'In Stock', count: 148 },
      { value: 'low_stock', label: 'Low Stock', count: 12 },
      { value: 'out_of_stock', label: 'Out of Stock', count: 8 }
    ]
  })

  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [activeFilterCount, setActiveFilterCount] = useState(0)

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((searchFilters: SearchFilters) => {
      onFiltersChange(searchFilters)
    }, 300),
    [onFiltersChange]
  )

  useEffect(() => {
    fetchFilterOptions()
  }, [])

  useEffect(() => {
    debouncedSearch(filters)
    updateActiveFilterCount()
  }, [filters, debouncedSearch])

  const fetchFilterOptions = async () => {
    try {
      // Mock data - replace with actual API calls
      setFilterOptions({
        categories: [
          { id: '1', name: 'T-Shirts', count: 45 },
          { id: '2', name: 'Dresses', count: 32 },
          { id: '3', name: 'Pants', count: 28 },
          { id: '4', name: 'Shoes', count: 24 },
          { id: '5', name: 'Accessories', count: 18 }
        ],
        vendors: [
          { name: 'Brand A', count: 45 },
          { name: 'Brand B', count: 32 },
          { name: 'Brand C', count: 28 }
        ],
        priceRange: { min: 0, max: 2000 },
        statusOptions: [
          { value: 'active', label: 'Active', count: 142 },
          { value: 'draft', label: 'Draft', count: 14 },
          { value: 'archived', label: 'Archived', count: 8 }
        ],
        stockOptions: [
          { value: 'in_stock', label: 'In Stock', count: 148 },
          { value: 'low_stock', label: 'Low Stock', count: 12 },
          { value: 'out_of_stock', label: 'Out of Stock', count: 8 }
        ]
      })
    } catch (error) {
      console.error('Error fetching filter options:', error)
    }
  }

  const updateActiveFilterCount = () => {
    let count = 0
    if (filters.query) count++
    if (filters.status.length > 0) count++
    if (filters.categories.length > 0) count++
    if (filters.vendors.length > 0) count++
    if (filters.priceRange[0] > filterOptions.priceRange.min || 
        filters.priceRange[1] < filterOptions.priceRange.max) count++
    if (filters.stockStatus.length > 0) count++
    if (filters.hasVariants !== null) count++
    if (filters.dateRange.from || filters.dateRange.to) count++
    
    setActiveFilterCount(count)
  }

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleMultiSelectFilter = (key: keyof SearchFilters, value: string, checked: boolean) => {
    setFilters(prev => {
      const currentValues = prev[key] as string[]
      const newValues = checked 
        ? [...currentValues, value]
        : currentValues.filter(v => v !== value)
      
      return {
        ...prev,
        [key]: newValues
      }
    })
  }

  const clearAllFilters = () => {
    setFilters({
      query: '',
      status: [],
      categories: [],
      vendors: [],
      priceRange: [filterOptions.priceRange.min, filterOptions.priceRange.max],
      stockStatus: [],
      hasVariants: null,
      dateRange: {
        from: null,
        to: null
      },
      sortBy: 'createdAt',
      sortOrder: 'desc'
    })
  }

  const removeFilter = (filterType: string, value?: string) => {
    switch (filterType) {
      case 'query':
        handleFilterChange('query', '')
        break
      case 'status':
        if (value) {
          handleMultiSelectFilter('status', value, false)
        } else {
          handleFilterChange('status', [])
        }
        break
      case 'categories':
        if (value) {
          handleMultiSelectFilter('categories', value, false)
        } else {
          handleFilterChange('categories', [])
        }
        break
      case 'vendors':
        if (value) {
          handleMultiSelectFilter('vendors', value, false)
        } else {
          handleFilterChange('vendors', [])
        }
        break
      case 'priceRange':
        handleFilterChange('priceRange', [filterOptions.priceRange.min, filterOptions.priceRange.max])
        break
      case 'stockStatus':
        if (value) {
          handleMultiSelectFilter('stockStatus', value, false)
        } else {
          handleFilterChange('stockStatus', [])
        }
        break
      case 'hasVariants':
        handleFilterChange('hasVariants', null)
        break
    }
  }

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search products by name, SKU, or description..."
            value={filters.query}
            onChange={(e) => handleFilterChange('query', e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className="relative">
              <SlidersHorizontal className="mr-2 h-4 w-4" />
              Filters
              {activeFilterCount > 0 && (
                <Badge variant="destructive" className="ml-2 h-5 w-5 rounded-full p-0 text-xs">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="end">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Filters</h4>
                <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                  Clear All
                </Button>
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <Label>Status</Label>
                <div className="space-y-2">
                  {filterOptions.statusOptions.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`status-${option.value}`}
                        checked={filters.status.includes(option.value)}
                        onCheckedChange={(checked) => 
                          handleMultiSelectFilter('status', option.value, checked as boolean)
                        }
                      />
                      <Label htmlFor={`status-${option.value}`} className="text-sm">
                        {option.label} ({option.count})
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Categories Filter */}
              <div className="space-y-2">
                <Label>Categories</Label>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {filterOptions.categories.map((category) => (
                    <div key={category.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`category-${category.id}`}
                        checked={filters.categories.includes(category.id)}
                        onCheckedChange={(checked) => 
                          handleMultiSelectFilter('categories', category.id, checked as boolean)
                        }
                      />
                      <Label htmlFor={`category-${category.id}`} className="text-sm">
                        {category.name} ({category.count})
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Price Range Filter */}
              <div className="space-y-2">
                <Label>Price Range (ZAR)</Label>
                <div className="px-2">
                  <Slider
                    value={filters.priceRange}
                    onValueChange={(value) => handleFilterChange('priceRange', value)}
                    max={filterOptions.priceRange.max}
                    min={filterOptions.priceRange.min}
                    step={10}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>R{filters.priceRange[0]}</span>
                    <span>R{filters.priceRange[1]}</span>
                  </div>
                </div>
              </div>

              {/* Stock Status Filter */}
              <div className="space-y-2">
                <Label>Stock Status</Label>
                <div className="space-y-2">
                  {filterOptions.stockOptions.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`stock-${option.value}`}
                        checked={filters.stockStatus.includes(option.value)}
                        onCheckedChange={(checked) => 
                          handleMultiSelectFilter('stockStatus', option.value, checked as boolean)
                        }
                      />
                      <Label htmlFor={`stock-${option.value}`} className="text-sm">
                        {option.label} ({option.count})
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        <Select value={filters.sortBy} onValueChange={(value) => handleFilterChange('sortBy', value)}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="createdAt">Date Created</SelectItem>
            <SelectItem value="title">Name</SelectItem>
            <SelectItem value="price">Price</SelectItem>
            <SelectItem value="inventoryQuantity">Stock</SelectItem>
            <SelectItem value="status">Status</SelectItem>
          </SelectContent>
        </Select>

        <Button variant="outline" onClick={onExport} disabled={loading}>
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
      </div>

      {/* Active Filters */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.query && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: "{filters.query}"
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => removeFilter('query')}
              />
            </Badge>
          )}
          
          {filters.status.map((status) => (
            <Badge key={status} variant="secondary" className="flex items-center gap-1">
              Status: {status}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => removeFilter('status', status)}
              />
            </Badge>
          ))}

          {filters.categories.map((categoryId) => {
            const category = filterOptions.categories.find(c => c.id === categoryId)
            return (
              <Badge key={categoryId} variant="secondary" className="flex items-center gap-1">
                Category: {category?.name}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => removeFilter('categories', categoryId)}
                />
              </Badge>
            )
          })}

          {(filters.priceRange[0] > filterOptions.priceRange.min || 
            filters.priceRange[1] < filterOptions.priceRange.max) && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Price: R{filters.priceRange[0]} - R{filters.priceRange[1]}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => removeFilter('priceRange')}
              />
            </Badge>
          )}
        </div>
      )}

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <span>
          {loading ? 'Searching...' : `${totalResults} products found`}
        </span>
        {loading && <RefreshCw className="h-4 w-4 animate-spin" />}
      </div>
    </div>
  )
}
