'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { LucideIcon } from 'lucide-react'

export interface StatCard {
  title: string
  value: string | number
  description?: string
  icon?: LucideIcon
  trend?: {
    value: number
    label: string
    direction: 'up' | 'down' | 'neutral'
  }
  badge?: {
    text: string
    variant: 'default' | 'secondary' | 'destructive' | 'outline'
  }
  color?: 'default' | 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'pink'
}

interface DashboardStatsProps {
  stats: StatCard[]
  loading?: boolean
  className?: string
}

export function DashboardStats({ stats, loading = false, className }: DashboardStatsProps) {
  const getColorClasses = (color: StatCard['color']) => {
    switch (color) {
      case 'blue':
        return 'text-blue-600'
      case 'green':
        return 'text-green-600'
      case 'yellow':
        return 'text-yellow-600'
      case 'red':
        return 'text-red-600'
      case 'purple':
        return 'text-purple-600'
      case 'pink':
        return 'text-pink-600'
      default:
        return 'text-foreground'
    }
  }

  const getTrendIcon = (direction: 'up' | 'down' | 'neutral') => {
    switch (direction) {
      case 'up':
        return '↗'
      case 'down':
        return '↘'
      case 'neutral':
        return '→'
    }
  }

  const getTrendColor = (direction: 'up' | 'down' | 'neutral') => {
    switch (direction) {
      case 'up':
        return 'text-green-600'
      case 'down':
        return 'text-red-600'
      case 'neutral':
        return 'text-gray-600'
    }
  }

  if (loading) {
    return (
      <div className={cn("grid gap-4 md:grid-cols-2 lg:grid-cols-4", className)}>
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-gray-200 rounded animate-pulse mb-1"></div>
              <div className="h-3 w-24 bg-gray-200 rounded animate-pulse"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className={cn("grid gap-4 md:grid-cols-2 lg:grid-cols-4", className)}>
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            {stat.icon && <stat.icon className="h-4 w-4 text-muted-foreground" />}
          </CardHeader>
          <CardContent>
            <div className={cn("text-2xl font-bold", getColorClasses(stat.color))}>
              {stat.value}
            </div>
            
            <div className="flex items-center justify-between mt-1">
              {stat.description && (
                <p className="text-xs text-muted-foreground flex-1">
                  {stat.description}
                </p>
              )}
              
              {stat.badge && (
                <Badge variant={stat.badge.variant} className="ml-2">
                  {stat.badge.text}
                </Badge>
              )}
            </div>
            
            {stat.trend && (
              <div className={cn("flex items-center text-xs mt-1", getTrendColor(stat.trend.direction))}>
                <span className="mr-1">{getTrendIcon(stat.trend.direction)}</span>
                <span>{stat.trend.value}% {stat.trend.label}</span>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

// Predefined stat configurations for common use cases
export const createProductStats = (data: {
  total: number
  inStock: number
  lowStock: number
  outOfStock: number
}): StatCard[] => [
  {
    title: 'Total Products',
    value: data.total,
    description: 'All products',
    color: 'default'
  },
  {
    title: 'In Stock',
    value: data.inStock,
    description: 'Available products',
    color: 'green'
  },
  {
    title: 'Low Stock',
    value: data.lowStock,
    description: 'Need restocking',
    color: 'yellow',
    badge: data.lowStock > 0 ? { text: 'Alert', variant: 'secondary' } : undefined
  },
  {
    title: 'Out of Stock',
    value: data.outOfStock,
    description: 'Unavailable',
    color: 'red',
    badge: data.outOfStock > 0 ? { text: 'Critical', variant: 'destructive' } : undefined
  }
]

export const createOrderStats = (data: {
  total: number
  pending: number
  processing: number
  completed: number
  revenue: number
}): StatCard[] => [
  {
    title: 'Total Orders',
    value: data.total,
    description: 'All time',
    color: 'default'
  },
  {
    title: 'Pending',
    value: data.pending,
    description: 'Awaiting payment',
    color: 'yellow'
  },
  {
    title: 'Processing',
    value: data.processing,
    description: 'Being fulfilled',
    color: 'blue'
  },
  {
    title: 'Completed',
    value: data.completed,
    description: 'Successfully delivered',
    color: 'green'
  }
]

export const createCustomerStats = (data: {
  total: number
  new: number
  returning: number
  vip: number
}): StatCard[] => [
  {
    title: 'Total Customers',
    value: data.total,
    description: 'Registered users',
    color: 'default'
  },
  {
    title: 'New Customers',
    value: data.new,
    description: 'This month',
    color: 'blue'
  },
  {
    title: 'Returning',
    value: data.returning,
    description: 'Repeat customers',
    color: 'green'
  },
  {
    title: 'VIP Customers',
    value: data.vip,
    description: 'High value',
    color: 'purple'
  }
]

export const createRevenueStats = (data: {
  total: number
  thisMonth: number
  lastMonth: number
  growth: number
}): StatCard[] => [
  {
    title: 'Total Revenue',
    value: `R${data.total.toLocaleString()}`,
    description: 'All time',
    color: 'default'
  },
  {
    title: 'This Month',
    value: `R${data.thisMonth.toLocaleString()}`,
    description: 'Current month',
    color: 'green',
    trend: {
      value: data.growth,
      label: 'vs last month',
      direction: data.growth > 0 ? 'up' : data.growth < 0 ? 'down' : 'neutral'
    }
  },
  {
    title: 'Last Month',
    value: `R${data.lastMonth.toLocaleString()}`,
    description: 'Previous month',
    color: 'blue'
  },
  {
    title: 'Growth Rate',
    value: `${data.growth > 0 ? '+' : ''}${data.growth}%`,
    description: 'Month over month',
    color: data.growth > 0 ? 'green' : data.growth < 0 ? 'red' : 'default'
  }
]
