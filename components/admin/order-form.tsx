'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { 
  Plus, 
  Minus, 
  X, 
  Search,
  User,
  MapPin,
  CreditCard,
  Package,
  Save,
  AlertCircle
} from 'lucide-react'
import type { Order } from '@/lib/ecommerce/types'
import { useZarFormatter } from '@/components/admin/zar-price-input'

// Form validation schema
const orderFormSchema = z.object({
  customer: z.object({
    email: z.string().email('Invalid email address'),
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    phone: z.string().optional()
  }),
  shippingAddress: z.object({
    line1: z.string().min(1, 'Address line 1 is required'),
    line2: z.string().optional(),
    city: z.string().min(1, 'City is required'),
    province: z.string().min(1, 'Province is required'),
    postalCode: z.string().min(1, 'Postal code is required'),
    country: z.string().default('South Africa')
  }),
  billingAddress: z.object({
    line1: z.string().min(1, 'Address line 1 is required'),
    line2: z.string().optional(),
    city: z.string().min(1, 'City is required'),
    province: z.string().min(1, 'Province is required'),
    postalCode: z.string().min(1, 'Postal code is required'),
    country: z.string().default('South Africa')
  }),
  items: z.array(z.object({
    productId: z.string().min(1, 'Product is required'),
    variantId: z.string().optional(),
    quantity: z.number().min(1, 'Quantity must be at least 1'),
    unitPrice: z.number().min(0, 'Price must be positive')
  })).min(1, 'At least one item is required'),
  shippingMethodId: z.string().min(1, 'Shipping method is required'),
  paymentMethodId: z.string().optional(),
  customerNote: z.string().optional(),
  internalNotes: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional()
})

type OrderFormData = z.infer<typeof orderFormSchema>

interface OrderFormProps {
  order?: Order
  onSubmit: (data: OrderFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
  mode?: 'create' | 'edit'
}

export function OrderForm({ order, onSubmit, onCancel, isLoading = false, mode = 'create' }: OrderFormProps) {
  const [useSameAddress, setUseSameAddress] = useState(true)
  const [selectedProducts, setSelectedProducts] = useState<any[]>([])
  const { formatPrice } = useZarFormatter()

  const form = useForm<OrderFormData>({
    resolver: zodResolver(orderFormSchema),
    defaultValues: {
      customer: {
        email: order?.customer?.email || '',
        firstName: order?.customer?.firstName || '',
        lastName: order?.customer?.lastName || '',
        phone: order?.customer?.phone || ''
      },
      shippingAddress: {
        line1: order?.shippingAddress?.line1 || '',
        line2: order?.shippingAddress?.line2 || '',
        city: order?.shippingAddress?.city || '',
        province: order?.shippingAddress?.province || '',
        postalCode: order?.shippingAddress?.postalCode || '',
        country: order?.shippingAddress?.country || 'South Africa'
      },
      billingAddress: {
        line1: order?.billingAddress?.line1 || '',
        line2: order?.billingAddress?.line2 || '',
        city: order?.billingAddress?.city || '',
        province: order?.billingAddress?.province || '',
        postalCode: order?.billingAddress?.postalCode || '',
        country: order?.billingAddress?.country || 'South Africa'
      },
      items: order?.items?.map(item => ({
        productId: item.productId,
        variantId: item.variantId,
        quantity: item.quantity,
        unitPrice: item.unitPrice.amount
      })) || [],
      shippingMethodId: order?.shippingMethod?.id || '',
      paymentMethodId: order?.paymentMethod?.id || '',
      customerNote: order?.customerNote || '',
      internalNotes: order?.internalNotes || [],
      tags: order?.tags || []
    }
  })

  const handleSubmit = async (data: OrderFormData) => {
    try {
      await onSubmit(data)
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  const addOrderItem = () => {
    const currentItems = form.getValues('items')
    form.setValue('items', [
      ...currentItems,
      { productId: '', variantId: '', quantity: 1, unitPrice: 0 }
    ])
  }

  const removeOrderItem = (index: number) => {
    const currentItems = form.getValues('items')
    form.setValue('items', currentItems.filter((_, i) => i !== index))
  }

  const copyShippingToBilling = () => {
    const shippingAddress = form.getValues('shippingAddress')
    form.setValue('billingAddress', shippingAddress)
  }

  useEffect(() => {
    if (useSameAddress) {
      copyShippingToBilling()
    }
  }, [useSameAddress, form.watch('shippingAddress')])

  const calculateTotal = () => {
    const items = form.watch('items')
    return items.reduce((total, item) => total + (item.quantity * item.unitPrice), 0)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            {mode === 'create' ? 'Create New Order' : 'Edit Order'}
          </h2>
          <p className="text-muted-foreground">
            {mode === 'create' 
              ? 'Create a new order manually for a customer'
              : 'Update order details and information'
            }
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button 
            onClick={form.handleSubmit(handleSubmit)} 
            disabled={isLoading}
          >
            <Save className="mr-2 h-4 w-4" />
            {mode === 'create' ? 'Create Order' : 'Update Order'}
          </Button>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5" />
                Customer Information
              </CardTitle>
              <CardDescription>
                Enter the customer's contact details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="customer.firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input placeholder="John" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="customer.lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Doe" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="customer.email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="customer.phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="+27 12 345 6789" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
              <CardDescription>
                Total: {formatPrice(calculateTotal())}
              </CardDescription>
            </CardHeader>
          </Card>
        </form>
      </Form>
    </div>
  )
}
