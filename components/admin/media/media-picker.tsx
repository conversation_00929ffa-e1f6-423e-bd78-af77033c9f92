'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Image,
  Video,
  Music,
  FileText,
  File,
  Plus,
  X,
  Eye,
  Upload
} from 'lucide-react'
import { MediaBrowser } from './media-browser'
import { MediaFile } from '@/hooks/use-media'
import { formatFileSize, cn } from '@/lib/utils'

interface MediaPickerProps {
  value?: MediaFile[]
  onChange: (files: MediaFile[]) => void
  multiple?: boolean
  accept?: 'all' | 'image' | 'video' | 'audio' | 'document'
  label?: string
  description?: string
  className?: string
  disabled?: boolean
  required?: boolean
  error?: string
  maxFiles?: number
}

export function MediaPicker({
  value = [],
  onChange,
  multiple = false,
  accept = 'all',
  label,
  description,
  className,
  disabled = false,
  required = false,
  error,
  maxFiles = 10
}: MediaPickerProps) {
  const [showBrowser, setShowBrowser] = useState(false)

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <Image className="h-4 w-4 text-green-600" />
      case 'video':
        return <Video className="h-4 w-4 text-blue-600" />
      case 'audio':
        return <Music className="h-4 w-4 text-purple-600" />
      case 'document':
        return <FileText className="h-4 w-4 text-orange-600" />
      default:
        return <File className="h-4 w-4 text-gray-600" />
    }
  }

  const getFileTypeColor = (type: string) => {
    switch (type) {
      case 'image':
        return 'bg-green-100 text-green-800'
      case 'video':
        return 'bg-blue-100 text-blue-800'
      case 'audio':
        return 'bg-purple-100 text-purple-800'
      case 'document':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleSelect = (files: MediaFile[]) => {
    if (multiple) {
      const newFiles = [...value, ...files]
      const uniqueFiles = newFiles.filter((file, index, self) => 
        index === self.findIndex(f => f.id === file.id)
      )
      onChange(uniqueFiles.slice(0, maxFiles))
    } else {
      onChange(files.slice(0, 1))
    }
  }

  const handleRemove = (fileId: string) => {
    onChange(value.filter(file => file.id !== fileId))
  }

  const handlePreview = (file: MediaFile) => {
    window.open(file.url, '_blank')
  }

  const canAddMore = multiple && value.length < maxFiles

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label className={cn(required && "after:content-['*'] after:text-red-500 after:ml-1")}>
          {label}
        </Label>
      )}
      
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}

      <div className="space-y-3">
        {/* Selected Files */}
        {value.length > 0 && (
          <div className="space-y-2">
            {value.map((file) => (
              <Card key={file.id} className="p-3">
                <div className="flex items-center space-x-3">
                  {file.type === 'image' && file.previewUrl ? (
                    <img
                      src={file.previewUrl}
                      alt={file.metadata.alt || file.name}
                      className="w-12 h-12 object-cover rounded"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-gray-100 rounded flex items-center justify-center">
                      {getFileIcon(file.type)}
                    </div>
                  )}

                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-sm truncate" title={file.name}>
                      {file.name}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge
                        variant="secondary"
                        className={cn("text-xs", getFileTypeColor(file.type))}
                      >
                        {file.type.toUpperCase()}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {formatFileSize(file.size)}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handlePreview(file)}
                      disabled={disabled}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemove(file.id)}
                      disabled={disabled}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Add Files Button */}
        {(value.length === 0 || canAddMore) && (
          <Card
            className={cn(
              "border-2 border-dashed transition-colors cursor-pointer hover:border-gray-400",
              disabled && "pointer-events-none opacity-50",
              error && "border-red-300"
            )}
            onClick={() => !disabled && setShowBrowser(true)}
          >
            <CardContent className="p-6">
              <div className="flex flex-col items-center justify-center space-y-2 text-center">
                <div className="p-3 bg-gray-100 rounded-full">
                  {value.length === 0 ? (
                    <Upload className="h-6 w-6 text-gray-600" />
                  ) : (
                    <Plus className="h-6 w-6 text-gray-600" />
                  )}
                </div>
                <div>
                  <p className="font-medium">
                    {value.length === 0 
                      ? `Select ${accept === 'all' ? 'files' : accept}`
                      : 'Add more files'
                    }
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {multiple 
                      ? `Choose up to ${maxFiles} files from your media library`
                      : 'Choose a file from your media library'
                    }
                  </p>
                  {value.length > 0 && multiple && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {value.length}/{maxFiles} files selected
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}

      {/* Media Browser */}
      <MediaBrowser
        open={showBrowser}
        onOpenChange={setShowBrowser}
        onSelect={handleSelect}
        multiple={multiple}
        accept={accept}
        title={`Select ${accept === 'all' ? 'Media' : accept.charAt(0).toUpperCase() + accept.slice(1)}`}
        description={
          multiple 
            ? `Choose up to ${maxFiles - value.length} more files`
            : 'Choose a file from your media library'
        }
      />
    </div>
  )
}
