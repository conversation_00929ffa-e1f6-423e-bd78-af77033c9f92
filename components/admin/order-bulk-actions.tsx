'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { 
  ChevronDown,
  Package,
  X,
  CheckCircle,
  Truck,
  Download,
  Tag,
  AlertTriangle,
  RefreshCw
} from 'lucide-react'
import type { Order } from '@/lib/ecommerce/types'

interface OrderBulkActionsProps {
  selectedOrders: Order[]
  onBulkAction: (action: string, data?: any) => Promise<void>
  onClearSelection: () => void
  isLoading?: boolean
}

export function OrderBulkActions({ 
  selectedOrders, 
  onBulkAction, 
  onClearSelection,
  isLoading = false 
}: OrderBulkActionsProps) {
  const [showDialog, setShowDialog] = useState(false)
  const [currentAction, setCurrentAction] = useState<string>('')
  const [actionData, setActionData] = useState<any>({})

  const handleAction = async (action: string, requiresDialog = false) => {
    if (requiresDialog) {
      setCurrentAction(action)
      setActionData({})
      setShowDialog(true)
    } else {
      await onBulkAction(action)
    }
  }

  const handleDialogSubmit = async () => {
    await onBulkAction(currentAction, actionData)
    setShowDialog(false)
    setCurrentAction('')
    setActionData({})
  }

  const renderDialogContent = () => {
    switch (currentAction) {
      case 'update_status':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="status">New Status</Label>
              <Select 
                value={actionData.status || ''} 
                onValueChange={(value) => setActionData({ ...actionData, status: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="shipped">Shipped</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="refunded">Refunded</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {actionData.status === 'shipped' && (
              <div>
                <Label htmlFor="trackingNumber">Tracking Number (Optional)</Label>
                <Input
                  id="trackingNumber"
                  value={actionData.trackingNumber || ''}
                  onChange={(e) => setActionData({ ...actionData, trackingNumber: e.target.value })}
                  placeholder="Enter tracking number"
                />
              </div>
            )}
          </div>
        )

      case 'update_payment_status':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="paymentStatus">New Payment Status</Label>
              <Select 
                value={actionData.paymentStatus || ''} 
                onValueChange={(value) => setActionData({ ...actionData, paymentStatus: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select payment status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="refunded">Refunded</SelectItem>
                  <SelectItem value="partially_refunded">Partially Refunded</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )

      case 'add_tags':
      case 'remove_tags':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="tags">Tags (comma-separated)</Label>
              <Input
                id="tags"
                value={actionData.tagsInput || ''}
                onChange={(e) => setActionData({ 
                  ...actionData, 
                  tagsInput: e.target.value,
                  tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
                })}
                placeholder="urgent, priority, wholesale"
              />
            </div>
          </div>
        )

      case 'fulfill':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="trackingNumber">Tracking Number</Label>
              <Input
                id="trackingNumber"
                value={actionData.trackingNumber || ''}
                onChange={(e) => setActionData({ ...actionData, trackingNumber: e.target.value })}
                placeholder="Enter tracking number"
              />
            </div>
            <div>
              <Label htmlFor="reason">Fulfillment Notes (Optional)</Label>
              <Textarea
                id="reason"
                value={actionData.reason || ''}
                onChange={(e) => setActionData({ ...actionData, reason: e.target.value })}
                placeholder="Add any notes about the fulfillment"
              />
            </div>
          </div>
        )

      case 'cancel':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="reason">Cancellation Reason</Label>
              <Textarea
                id="reason"
                value={actionData.reason || ''}
                onChange={(e) => setActionData({ ...actionData, reason: e.target.value })}
                placeholder="Explain why these orders are being cancelled"
              />
            </div>
          </div>
        )

      default:
        return null
    }
  }

  const getDialogTitle = () => {
    switch (currentAction) {
      case 'update_status':
        return 'Update Order Status'
      case 'update_payment_status':
        return 'Update Payment Status'
      case 'add_tags':
        return 'Add Tags'
      case 'remove_tags':
        return 'Remove Tags'
      case 'fulfill':
        return 'Fulfill Orders'
      case 'cancel':
        return 'Cancel Orders'
      default:
        return 'Bulk Action'
    }
  }

  const getDialogDescription = () => {
    const count = selectedOrders.length
    switch (currentAction) {
      case 'update_status':
        return `Update the status for ${count} selected order${count > 1 ? 's' : ''}.`
      case 'update_payment_status':
        return `Update the payment status for ${count} selected order${count > 1 ? 's' : ''}.`
      case 'add_tags':
        return `Add tags to ${count} selected order${count > 1 ? 's' : ''}.`
      case 'remove_tags':
        return `Remove tags from ${count} selected order${count > 1 ? 's' : ''}.`
      case 'fulfill':
        return `Mark ${count} selected order${count > 1 ? 's' : ''} as fulfilled and shipped.`
      case 'cancel':
        return `Cancel ${count} selected order${count > 1 ? 's' : ''}. This action cannot be undone.`
      default:
        return `Perform bulk action on ${count} selected order${count > 1 ? 's' : ''}.`
    }
  }

  if (selectedOrders.length === 0) {
    return null
  }

  return (
    <>
      <div className="flex items-center justify-between p-4 bg-muted/50 border rounded-lg">
        <div className="flex items-center space-x-4">
          <Badge variant="secondary" className="text-sm">
            {selectedOrders.length} order{selectedOrders.length > 1 ? 's' : ''} selected
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearSelection}
            className="h-8 px-2"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" disabled={isLoading}>
                Bulk Actions
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Order Status</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleAction('update_status', true)}>
                <Package className="mr-2 h-4 w-4" />
                Update Status
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAction('update_payment_status', true)}>
                <CheckCircle className="mr-2 h-4 w-4" />
                Update Payment Status
              </DropdownMenuItem>
              
              <DropdownMenuSeparator />
              <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleAction('fulfill', true)}>
                <Truck className="mr-2 h-4 w-4" />
                Mark as Fulfilled
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAction('cancel', true)}>
                <X className="mr-2 h-4 w-4" />
                Cancel Orders
              </DropdownMenuItem>
              
              <DropdownMenuSeparator />
              <DropdownMenuLabel>Organization</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleAction('add_tags', true)}>
                <Tag className="mr-2 h-4 w-4" />
                Add Tags
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAction('remove_tags', true)}>
                <Tag className="mr-2 h-4 w-4" />
                Remove Tags
              </DropdownMenuItem>
              
              <DropdownMenuSeparator />
              <DropdownMenuLabel>Export</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleAction('export')}>
                <Download className="mr-2 h-4 w-4" />
                Export Selected
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{getDialogTitle()}</DialogTitle>
            <DialogDescription>
              {getDialogDescription()}
            </DialogDescription>
          </DialogHeader>
          
          {renderDialogContent()}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleDialogSubmit} disabled={isLoading}>
              {currentAction === 'cancel' && <AlertTriangle className="mr-2 h-4 w-4" />}
              {currentAction === 'fulfill' && <Truck className="mr-2 h-4 w-4" />}
              {currentAction === 'update_status' && <RefreshCw className="mr-2 h-4 w-4" />}
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
