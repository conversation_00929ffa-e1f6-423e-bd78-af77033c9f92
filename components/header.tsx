"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { She<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/sheet"
import {
  Search,
  Menu,
  ShoppingBag,
  User,
  Heart
} from "lucide-react"
import { useCart } from "@/hooks/use-cart"
import { NavigationSidebar } from "@/components/navigation-sidebar"
import { CartDrawer } from "@/components/storefront/cart/cart-drawer"
import { SearchDialog } from "@/components/search-dialog"
export default function Header() {
  const pathname = usePathname()
  const { itemCount } = useCart()
  const [isCartOpen, setIsCartOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)

  // Mock user data - replace with actual auth state
  const user = {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg",
    isLoggedIn: true
  }

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Clean navigation items - Zara/Selfi style
  const navigationItems = [
    {
      title: "New",
      href: "/products?category=new-arrivals"
    },
    {
      title: "Girls",
      href: "/products?category=girls"
    },
    {
      title: "Boys",
      href: "/products?category=boys"
    },
    {
      title: "Gender Neutral",
      href: "/products?category=gender-neutral"
    },
    {
      title: "Sale",
      href: "/products?category=sale"
    }
  ]

  return (
    <>
      {/* Clean Header - Zara/Selfi Style */}
      <header
        className={cn(
          "sticky top-0 z-50 w-full transition-all duration-200",
          scrolled ? "bg-white/95 backdrop-blur-sm border-b border-gray-200" : "bg-white border-b border-gray-100",
        )}
      >
        {/* Main Header Content */}
        <div className="container flex h-16 items-center px-4 md:px-6">
          {/* Mobile Navigation */}
          <Sheet open={isMobileNavOpen} onOpenChange={setIsMobileNavOpen}>
            {/* Mobile Menu Button */}
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden hover:bg-gray-100 transition-colors mr-2"
              >
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>

            <SheetContent side="left" className="p-0 w-80">
              <SheetHeader className="sr-only">
                <SheetTitle>Navigation Menu</SheetTitle>
              </SheetHeader>
              <div className="h-full">
                <NavigationSidebar
                  user={user}
                  onSignOut={() => {
                    console.log("Sign out")
                    setIsMobileNavOpen(false)
                  }}
                />
              </div>
            </SheetContent>
          </Sheet>

          {/* Logo - Using actual SVG */}
          <Link href="/" className="flex items-center">
            <img
              src="/assets/coco-logo-hori.svg"
              alt="Coco Milk Kids"
              className="h-8 w-auto object-contain"
              style={{ maxWidth: '140px' }}
            />
          </Link>

          {/* Desktop Navigation - Clean Zara/Selfi Style */}
          <nav className="hidden md:flex items-center space-x-8 text-sm font-normal flex-1 justify-center">
            {navigationItems.map((item) => (
              <Link
                key={item.title}
                href={item.href}
                className={cn(
                  "relative py-2 transition-colors duration-200 hover-underline",
                  pathname === item.href || pathname.startsWith(item.href)
                    ? "text-black font-medium"
                    : "text-gray-700 hover:text-black"
                )}
              >
                {item.title}
              </Link>
            ))}

            {/* Development Link */}
            {process.env.NODE_ENV === 'development' && (
              <Link
                href="/admin/page-builder"
                className={cn(
                  "px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors",
                  pathname === "/admin/page-builder" && "bg-blue-200 text-blue-800"
                )}
              >
                Page Builder
              </Link>
            )}
          </nav>

          {/* Action Buttons - Clean Zara/Selfi Style */}
          <div className="flex items-center space-x-4">
            {/* Search */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsSearchOpen(true)}
              className="hover:bg-transparent transition-colors"
              title="Search"
            >
              <Search className="h-5 w-5 text-gray-700" />
              <span className="sr-only">Search</span>
            </Button>

            {/* User Account */}
            <Button
              variant="ghost"
              size="icon"
              asChild
              className="hover:bg-transparent transition-colors"
              title="Account"
            >
              <Link href="/account">
                <User className="h-5 w-5 text-gray-700" />
                <span className="sr-only">Account</span>
              </Link>
            </Button>

            {/* Wishlist */}
            <Button
              variant="ghost"
              size="icon"
              asChild
              className="hover:bg-transparent transition-colors"
              title="Wishlist"
            >
              <Link href="/wishlist">
                <Heart className="h-5 w-5 text-gray-700" />
                <span className="sr-only">Wishlist</span>
              </Link>
            </Button>

            {/* Shopping Cart - Clean Style */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsCartOpen(true)}
              className="hover:bg-transparent transition-colors relative"
              title="Shopping Cart"
            >
              <ShoppingBag className="h-5 w-5 text-gray-700" />
              {itemCount > 0 && (
                <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-black text-[10px] font-medium text-white">
                  {itemCount > 9 ? '9+' : itemCount}
                </span>
              )}
              <span className="sr-only">Shopping cart</span>
            </Button>
          </div>
        </div>
      </header>

      {/* Drawers and Dialogs */}
      <CartDrawer open={isCartOpen} onOpenChange={setIsCartOpen} />
      <SearchDialog open={isSearchOpen} onOpenChange={setIsSearchOpen} />
    </>
  )
}
