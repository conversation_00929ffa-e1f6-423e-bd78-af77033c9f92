"use client"

import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"

const categories = [
  {
    name: "Girls Collection",
    subtitle: "Stylish & Comfortable",
    image: "/assets/images/cocomilk_kids-20220819_100135-2187605151.jpg",
    link: "/products?category=girls"
  },
  {
    name: "Boys Collection",
    subtitle: "Adventure Ready",
    image: "/assets/images/cocomilk_kids-20220822_112525-1393039322.jpg",
    link: "/products?category=boys"
  },
  {
    name: "New Arrivals",
    subtitle: "Latest Trends",
    image: "/assets/images/cocomilk_kids-20221028_102959-387306553.jpg",
    link: "/products?category=new-arrivals"
  },
  {
    name: "Sale",
    subtitle: "Up to 50% Off",
    image: "/assets/images/cocomilk_kids-20220912_082247-3005247592.jpg",
    link: "/products?category=sale"
  }
]

export function FeaturedCategories() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  }

  return (
    <section className="py-16 bg-white">
      <div className="container px-4 md:px-6 max-w-7xl">
        {/* Zara-style 2x2 Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6"
        >
          {categories.map((category) => (
            <motion.div key={category.name} variants={itemVariants}>
              <Link href={category.link} className="group block">
                <div className="relative aspect-square overflow-hidden bg-gray-100">
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-102"
                  />

                  {/* Zara-style overlay */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300" />

                  {/* Zara-style text overlay */}
                  <div className="absolute bottom-0 left-0 right-0 p-6 md:p-8">
                    <div className="bg-white/95 backdrop-blur-sm p-4 md:p-6 transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
                      <h3 className="text-xl md:text-2xl font-light tracking-wide text-black mb-1">
                        {category.name}
                      </h3>
                      <p className="text-sm md:text-base text-gray-600 font-light">
                        {category.subtitle}
                      </p>
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
