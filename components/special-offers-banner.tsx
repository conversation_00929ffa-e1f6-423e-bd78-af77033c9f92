"use client"

import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"

export function SpecialOffersBanner() {
  return (
    <section className="py-16 md:py-24 bg-black text-white">
      <div className="container px-4 md:px-6 max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-16 items-center">
          {/* Zara-style minimal text */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="space-y-6 md:space-y-8"
          >
            <div className="space-y-4">
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-light leading-tight">
                SALE
              </h2>
              <p className="text-lg md:text-xl font-light text-gray-300">
                Up to 50% off selected items
              </p>
            </div>
            <Link
              href="/collections/sale"
              className="inline-block bg-white text-black px-8 py-3 text-sm font-medium tracking-wider hover:bg-gray-100 transition-colors duration-200"
            >
              SHOP SALE
            </Link>
          </motion.div>

          {/* Zara-style image */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="relative aspect-[4/5] overflow-hidden"
          >
            <Image
              src="/assets/images/cocomilk_kids-20220906_082318-3283935526.jpg"
              alt="Sale collection"
              fill
              className="object-cover"
            />
          </motion.div>
        </div>
      </div>
    </section>
  )
}
