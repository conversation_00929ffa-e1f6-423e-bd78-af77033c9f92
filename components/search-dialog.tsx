"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, Search, X } from "lucide-react"
import { ProductSearchResult } from "@/components/storefront/products/product-search-result"
import { searchProducts } from "@/lib/products"

interface SearchDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function SearchDialog({ open, onOpenChange }: SearchDialogProps) {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [results, setResults] = useState([])
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const delayDebounceFn = setTimeout(async () => {
      if (searchQuery.trim().length > 2) {
        setIsLoading(true)
        const searchResults = await searchProducts(searchQuery)
        setResults(searchResults)
        setIsLoading(false)
      } else {
        setResults([])
      }
    }, 300)

    return () => clearTimeout(delayDebounceFn)
  }, [searchQuery])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/products?search=${encodeURIComponent(searchQuery.trim())}`)
      onOpenChange(false)
    }
  }

  const handleProductClick = (slug: string) => {
    router.push(`/products/${slug}`)
    onOpenChange(false)
  }

  const clearSearch = () => {
    setSearchQuery("")
    setResults([])
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Search Products</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSearch} className="relative">
          <Input
            type="search"
            placeholder="Search for products..."
            className="pr-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            autoFocus
          />
          {searchQuery && (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="absolute right-10 top-0 h-10 w-10"
              onClick={clearSearch}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Clear search</span>
            </Button>
          )}
          <Button type="submit" size="icon" className="absolute right-0 top-0 h-10 w-10">
            <Search className="h-4 w-4" />
            <span className="sr-only">Search</span>
          </Button>
        </form>

        <div className="mt-4 max-h-[300px] overflow-y-auto">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : results.length > 0 ? (
            <div className="space-y-2">
              {results.map((product) => (
                <ProductSearchResult key={product.id} product={product} onClick={handleProductClick} />
              ))}
            </div>
          ) : searchQuery.trim().length > 2 ? (
            <p className="py-8 text-center text-muted-foreground">No products found</p>
          ) : null}
        </div>
      </DialogContent>
    </Dialog>
  )
}
