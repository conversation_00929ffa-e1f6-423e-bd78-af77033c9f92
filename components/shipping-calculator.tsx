"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { usePriceFormatter } from "@/hooks/use-price-formatter"
import { Truck, Calculator } from "lucide-react"

interface ShippingOption {
  id: string
  name: string
  price: number
  estimatedDays: string
  description: string
}

export function ShippingCalculator() {
  const { formatPrice } = usePriceFormatter()
  const [zipCode, setZipCode] = useState("")
  const [country, setCountry] = useState("")
  const [shippingOptions, setShippingOptions] = useState<ShippingOption[]>([])
  const [isCalculating, setIsCalculating] = useState(false)

  const calculateShipping = async () => {
    setIsCalculating(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Mock shipping options based on location (prices in ZAR)
    const options: ShippingOption[] = [
      {
        id: "standard",
        name: "Standard Shipping",
        price: 99,
        estimatedDays: "5-7 business days",
        description: "Regular delivery to your doorstep",
      },
      {
        id: "express",
        name: "Express Shipping",
        price: 199,
        estimatedDays: "2-3 business days",
        description: "Faster delivery for urgent orders",
      },
      {
        id: "overnight",
        name: "Overnight Shipping",
        price: 399,
        estimatedDays: "1 business day",
        description: "Next day delivery",
      },
    ]

    // Add free shipping for orders over R1,500
    if (country === "ZA") {
      options.unshift({
        id: "free",
        name: "Free Standard Shipping",
        price: 0,
        estimatedDays: "7-10 business days",
        description: "Free shipping on orders over R1,500",
      })
    }

    setShippingOptions(options)
    setIsCalculating(false)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Calculator className="h-5 w-5 mr-2" />
          Shipping Calculator
        </CardTitle>
        <CardDescription>Calculate shipping costs to your location</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="country">Country</Label>
            <Select value={country} onValueChange={setCountry}>
              <SelectTrigger id="country">
                <SelectValue placeholder="Select country" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ZA">South Africa</SelectItem>
                <SelectItem value="BW">Botswana</SelectItem>
                <SelectItem value="NA">Namibia</SelectItem>
                <SelectItem value="US">United States</SelectItem>
                <SelectItem value="UK">United Kingdom</SelectItem>
                <SelectItem value="AU">Australia</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="zipCode">ZIP/Postal Code</Label>
            <Input
              id="zipCode"
              value={zipCode}
              onChange={(e) => setZipCode(e.target.value)}
              placeholder="Enter ZIP code"
            />
          </div>
        </div>

        <Button onClick={calculateShipping} disabled={!country || !zipCode || isCalculating} className="w-full">
          {isCalculating ? "Calculating..." : "Calculate Shipping"}
        </Button>

        {shippingOptions.length > 0 && (
          <div className="space-y-3 border-t pt-4">
            <h4 className="font-medium">Available Shipping Options:</h4>
            {shippingOptions.map((option) => (
              <div key={option.id} className="flex justify-between items-center p-3 border rounded-md">
                <div className="flex items-center space-x-3">
                  <Truck className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="font-medium">{option.name}</div>
                    <div className="text-sm text-muted-foreground">{option.description}</div>
                    <div className="text-sm text-muted-foreground">{option.estimatedDays}</div>
                  </div>
                </div>
                <div className="font-semibold">{option.price === 0 ? "FREE" : formatPrice(option.price)}</div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
