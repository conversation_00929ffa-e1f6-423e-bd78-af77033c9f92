"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { useCart } from "@/hooks/use-cart"
import { useWishlist } from "@/components/wishlist-provider"
import { 
  Home, 
  Search, 
  ShoppingBag, 
  Heart, 
  User,
  Grid3X3,
  <PERSON>rkles
} from "lucide-react"
import { Badge } from "@/components/ui/badge"

export function MobileBottomNav() {
  const pathname = usePathname()
  const { itemCount } = useCart()
  const { items: wishlistItems } = useWishlist()
  const [isVisible, setIsVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)

  // Hide/show nav on scroll
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY
      
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false)
      } else {
        setIsVisible(true)
      }
      
      setLastScrollY(currentScrollY)
    }

    window.addEventListener("scroll", handleScroll, { passive: true })
    return () => window.removeEventListener("scroll", handleScroll)
  }, [lastScrollY])

  const navItems = [
    {
      href: "/",
      icon: Home,
      label: "Home",
      isActive: pathname === "/"
    },
    {
      href: "/products",
      icon: Grid3X3,
      label: "Shop",
      isActive: pathname.startsWith("/products") || pathname.startsWith("/collections")
    },
    {
      href: "/search",
      icon: Search,
      label: "Search",
      isActive: pathname === "/search"
    },
    {
      href: "/wishlist",
      icon: Heart,
      label: "Wishlist",
      isActive: pathname === "/wishlist",
      badge: wishlistItems.length > 0 ? wishlistItems.length : undefined
    },
    {
      href: "/cart",
      icon: ShoppingBag,
      label: "Cart",
      isActive: pathname === "/cart",
      badge: itemCount > 0 ? itemCount : undefined
    }
  ]

  return (
    <>
      {/* Spacer to prevent content from being hidden behind fixed nav */}
      <div className="h-16 md:hidden" />
      
      {/* Bottom Navigation */}
      <nav 
        className={cn(
          "fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 md:hidden transition-transform duration-300",
          isVisible ? "translate-y-0" : "translate-y-full"
        )}
      >
        <div className="grid grid-cols-5 h-16">
          {navItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center space-y-1 text-xs transition-colors relative",
                item.isActive 
                  ? "text-primary" 
                  : "text-muted-foreground hover:text-foreground"
              )}
            >
              <div className="relative">
                <item.icon className={cn(
                  "h-5 w-5",
                  item.isActive && "text-primary"
                )} />
                {item.badge && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-2 -right-2 h-4 w-4 p-0 flex items-center justify-center text-xs"
                  >
                    {item.badge > 99 ? "99+" : item.badge}
                  </Badge>
                )}
              </div>
              <span className={cn(
                "text-xs",
                item.isActive && "text-primary font-medium"
              )}>
                {item.label}
              </span>
              {item.isActive && (
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-primary rounded-full" />
              )}
            </Link>
          ))}
        </div>
      </nav>

      {/* Floating Action Button for AI Stylist */}
      <div className="fixed bottom-20 right-4 z-40 md:hidden">
        <Link
          href="/ai-stylist"
          className="flex items-center justify-center w-14 h-14 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
        >
          <Sparkles className="h-6 w-6" />
        </Link>
      </div>
    </>
  )
}
