"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { toast } from "@/components/ui/use-toast"
import { Mail, Check } from "lucide-react"
import { motion } from "framer-motion"

export function NewsletterSignup() {
  const [email, setEmail] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isSubscribed, setIsSubscribed] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Real API call to newsletter subscription endpoint
      const response = await fetch('/api/e-commerce/newsletter/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        toast({
          title: "Successfully subscribed!",
          description: "Thank you for subscribing to our newsletter.",
        })
        setIsSubscribed(true)
        setEmail("")
      } else {
        toast({
          title: "Subscription failed",
          description: data.error || "Please try again later.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Subscription failed",
        description: "Please check your connection and try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <section className="py-16 md:py-24 bg-gray-50">
      <div className="container px-4 md:px-6 max-w-4xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h2 className="text-2xl md:text-3xl font-light tracking-wide mb-8">
            NEWSLETTER
          </h2>

          <p className="text-gray-600 max-w-md mx-auto mb-8 font-light">
            Subscribe to receive updates on new arrivals and exclusive offers
          </p>

          {isSubscribed ? (
            <div className="flex items-center justify-center space-x-2 text-lg">
              <Check className="h-5 w-5 text-green-600" />
              <span className="text-gray-800">Thank you for subscribing</span>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="max-w-md mx-auto">
              <div className="flex border-b border-black">
                <Input
                  type="email"
                  placeholder="Email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="border-0 bg-transparent placeholder:text-gray-500 text-black focus-visible:ring-0 focus-visible:ring-offset-0 px-0"
                />
                <button
                  type="submit"
                  disabled={isLoading}
                  className="text-sm font-medium tracking-wider text-black hover:text-gray-600 transition-colors duration-200 px-4 py-2"
                >
                  {isLoading ? "..." : "SUBSCRIBE"}
                </button>
              </div>
            </form>
          )}

          <p className="text-gray-500 text-xs mt-6 font-light">
            By subscribing, you agree to our Privacy Policy
          </p>
        </motion.div>
      </div>
    </section>
  )
}
