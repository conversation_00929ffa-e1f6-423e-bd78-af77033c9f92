"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState } from "react"

type WishlistContextType = {
  items: string[]
  isInWishlist: (productId: string) => boolean
  addToWishlist: (productId: string) => void
  removeFromWishlist: (productId: string) => void
  toggleWishlist: (productId: string) => void
}

export const WishlistContext = createContext<WishlistContextType | undefined>(undefined)

export function WishlistProvider({ children }: { children: React.ReactNode }) {
  const [items, setItems] = useState<string[]>([])
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    const storedWishlist = localStorage.getItem("wishlist")
    if (storedWishlist) {
      try {
        setItems(JSON.parse(storedWishlist))
      } catch (error) {
        console.error("Failed to parse wishlist from localStorage", error)
        setItems([])
      }
    }
  }, [])

  useEffect(() => {
    if (isClient) {
      localStorage.setItem("wishlist", JSON.stringify(items))
    }
  }, [items, isClient])

  const isInWishlist = (productId: string) => {
    return items.includes(productId)
  }

  const addToWishlist = (productId: string) => {
    if (!isInWishlist(productId)) {
      setItems((prev) => [...prev, productId])
    }
  }

  const removeFromWishlist = (productId: string) => {
    setItems((prev) => prev.filter((id) => id !== productId))
  }

  const toggleWishlist = (productId: string) => {
    if (isInWishlist(productId)) {
      removeFromWishlist(productId)
    } else {
      addToWishlist(productId)
    }
  }

  return (
    <WishlistContext.Provider
      value={{
        items,
        isInWishlist,
        addToWishlist,
        removeFromWishlist,
        toggleWishlist,
      }}
    >
      {children}
    </WishlistContext.Provider>
  )
}

export function useWishlist() {
  const context = useContext(WishlistContext)
  if (context === undefined) {
    throw new Error("useWishlist must be used within a WishlistProvider")
  }
  return context
}
