"use client"

import { useState } from "react"
import { useRouter, useSearchPara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>rigger } from "@/components/ui/sheet"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Filter, X } from "lucide-react"

interface MobileFiltersProps {
  selectedCategory?: string
  selectedColor?: string
  selectedSize?: string
}

export function MobileFilters({ selectedCategory, selectedColor, selectedSize }: MobileFiltersProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isOpen, setIsOpen] = useState(false)

  const categories = [
    { id: "dresses", name: "Dress<PERSON>" },
    { id: "tops", name: "Tops" },
    { id: "bottoms", name: "Bottoms" },
    { id: "outerwear", name: "Outerwear" },
    { id: "accessories", name: "Accessories" },
    { id: "shoes", name: "Shoes" },
  ]

  const colors = [
    { id: "red", name: "Red", value: "#ef4444" },
    { id: "blue", name: "Blue", value: "#3b82f6" },
    { id: "green", name: "Green", value: "#10b981" },
    { id: "pink", name: "Pink", value: "#ec4899" },
    { id: "purple", name: "Purple", value: "#8b5cf6" },
    { id: "yellow", name: "Yellow", value: "#f59e0b" },
    { id: "black", name: "Black", value: "#000000" },
    { id: "white", name: "White", value: "#ffffff" },
  ]

  const sizes = ["2T", "3T", "4T", "5T", "6", "7", "8", "10", "12", "14", "16"]

  const updateFilter = (key: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString())
    
    if (value) {
      params.set(key, value)
    } else {
      params.delete(key)
    }
    
    router.push(`/products?${params.toString()}`)
  }

  const clearFilters = () => {
    router.push("/products")
    setIsOpen(false)
  }

  const activeFiltersCount = [selectedCategory, selectedColor, selectedSize].filter(Boolean).length

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <Filter className="h-4 w-4 mr-2" />
          Filters
          {activeFiltersCount > 0 && (
            <Badge variant="destructive" className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      
      <SheetContent side="bottom" className="h-[80vh] overflow-y-auto">
        <SheetHeader>
          <div className="flex items-center justify-between">
            <SheetTitle>Filters</SheetTitle>
            {activeFiltersCount > 0 && (
              <Button variant="ghost" size="sm" onClick={clearFilters}>
                Clear All
              </Button>
            )}
          </div>
        </SheetHeader>

        <div className="mt-6 space-y-6">
          {/* Categories */}
          <div>
            <h3 className="font-medium mb-3">Category</h3>
            <div className="grid grid-cols-2 gap-2">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => updateFilter("category", selectedCategory === category.id ? "" : category.id)}
                >
                  {category.name}
                </Button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Colors */}
          <div>
            <h3 className="font-medium mb-3">Color</h3>
            <div className="grid grid-cols-4 gap-3">
              {colors.map((color) => (
                <button
                  key={color.id}
                  className={`relative w-12 h-12 rounded-full border-2 transition-all ${
                    selectedColor === color.id 
                      ? "border-primary scale-110" 
                      : "border-gray-300 hover:border-gray-400"
                  }`}
                  style={{ backgroundColor: color.value }}
                  onClick={() => updateFilter("color", selectedColor === color.id ? "" : color.id)}
                  title={color.name}
                >
                  {selectedColor === color.id && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className={`w-3 h-3 rounded-full ${color.value === "#ffffff" ? "bg-black" : "bg-white"}`} />
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Sizes */}
          <div>
            <h3 className="font-medium mb-3">Size</h3>
            <div className="grid grid-cols-4 gap-2">
              {sizes.map((size) => (
                <Button
                  key={size}
                  variant={selectedSize === size ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateFilter("size", selectedSize === size ? "" : size)}
                >
                  {size}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Apply Button */}
        <div className="mt-8 pt-4 border-t">
          <Button 
            className="w-full" 
            onClick={() => setIsOpen(false)}
          >
            Apply Filters
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  )
}
