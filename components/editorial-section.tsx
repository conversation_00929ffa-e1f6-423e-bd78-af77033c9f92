"use client"

import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"

export function EditorialSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container px-4 md:px-6 max-w-7xl">
        {/* Zara-style editorial grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
          {/* Large featured image */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="lg:col-span-2"
          >
            <Link href="/collections/editorial" className="group block">
              <div className="relative aspect-[4/3] overflow-hidden bg-gray-100">
                <Image
                  src="/assets/images/cocomilk_kids-20220829_111232-3780725228.jpg"
                  alt="Spring Essentials Collection"
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-102"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300" />
                
                {/* Zara-style text overlay */}
                <div className="absolute bottom-0 left-0 right-0 p-8 md:p-12">
                  <div className="bg-white/95 backdrop-blur-sm p-6 md:p-8 transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
                    <h3 className="text-2xl md:text-3xl font-light tracking-wide text-black mb-2">
                      SPRING ESSENTIALS
                    </h3>
                    <p className="text-gray-600 font-light">
                      Discover the season's must-have pieces
                    </p>
                  </div>
                </div>
              </div>
            </Link>
          </motion.div>

          {/* Side content */}
          <div className="space-y-4 md:space-y-6">
            {/* Small image 1 */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Link href="/collections/accessories" className="group block">
                <div className="relative aspect-square overflow-hidden bg-gray-100">
                  <Image
                    src="/assets/images/cocomilk_kids-20220824_102244-4040552385.jpg"
                    alt="Accessories collection"
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-102"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300" />
                  
                  <div className="absolute bottom-0 left-0 right-0 p-4 md:p-6">
                    <div className="bg-white/95 backdrop-blur-sm p-4 transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
                      <h4 className="text-lg font-light tracking-wide text-black">
                        ACCESSORIES
                      </h4>
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>

            {/* Small image 2 */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Link href="/collections/shoes" className="group block">
                <div className="relative aspect-square overflow-hidden bg-gray-100">
                  <Image
                    src="/assets/images/cocomilk_kids-20220901_072641-574408837.jpg"
                    alt="Shoes collection"
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-102"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300" />
                  
                  <div className="absolute bottom-0 left-0 right-0 p-4 md:p-6">
                    <div className="bg-white/95 backdrop-blur-sm p-4 transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
                      <h4 className="text-lg font-light tracking-wide text-black">
                        SHOES
                      </h4>
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )
}
