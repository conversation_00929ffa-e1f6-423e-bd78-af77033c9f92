"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

interface Currency {
  code: string
  name: string
  symbol: string
  rate: number
}

const currencies: Currency[] = [
  { code: "ZAR", name: "South African Rand", symbol: "R", rate: 1 },
  { code: "USD", name: "US Dollar", symbol: "$", rate: 0.054 },
  { code: "EUR", name: "Euro", symbol: "€", rate: 0.050 },
  { code: "GBP", name: "British Pound", symbol: "£", rate: 0.043 },
  { code: "BWP", name: "Botswana Pula", symbol: "P", rate: 0.74 },
  { code: "NAD", name: "Namibian Dollar", symbol: "N$", rate: 1 },
]

export function CurrencySelector() {
  const [selectedCurrency, setSelectedCurrency] = useState<Currency>(currencies[0])
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    const storedCurrency = localStorage.getItem("selectedCurrency")
    if (storedCurrency) {
      const currency = currencies.find((c) => c.code === storedCurrency)
      if (currency) {
        setSelectedCurrency(currency)
      }
    } else {
      // Default to ZAR if no currency is stored
      setSelectedCurrency(currencies[0]) // ZAR is first in the array
    }
  }, [])

  const handleCurrencyChange = (currency: Currency) => {
    setSelectedCurrency(currency)
    if (typeof window !== "undefined") {
      localStorage.setItem("selectedCurrency", currency.code)
      // Dispatch a custom event to notify other components about the currency change
      window.dispatchEvent(new CustomEvent("currencyChange", { detail: currency }))
    }
  }

  if (!isClient) {
    return null
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-12 px-1">
          {selectedCurrency.code}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {currencies.map((currency) => (
          <DropdownMenuItem
            key={currency.code}
            onClick={() => handleCurrencyChange(currency)}
            className={selectedCurrency.code === currency.code ? "bg-muted" : ""}
          >
            <div className="flex items-center">
              <span className="w-8">{currency.symbol}</span>
              <span>{currency.code}</span>
              <span className="ml-2 text-muted-foreground">- {currency.name}</span>
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
