import Image from "next/image"
import Link from "next/link"
import { Button } from "@/components/ui/button"

export function HeroBanner() {
  return (
    <div className="relative">
      <div className="relative h-[85vh] overflow-hidden">
        <Image
          src="/assets/images/cocomilk_kids-20221129_105450-1129022754.jpg"
          alt="Coco Milk Kids summer collection"
          width={1920}
          height={1080}
          className="object-cover w-full h-full"
          priority
        />
        <div className="absolute inset-0 bg-black/20" />
        <div className="absolute inset-0 flex flex-col justify-center items-center text-center p-4 md:p-6">
          <div className="max-w-2xl fade-in" style={{ animationDelay: "0.3s" }}>
            <h1 className="text-3xl md:text-5xl lg:text-6xl font-light text-white font-montserrat mb-6 tracking-wide">
              Summer Collection 2025
            </h1>
            <p className="text-lg md:text-xl text-white max-w-xl mb-8 font-light">
              Oversized comfort, undeniably cool. Discover our new summer styles for kids.
            </p>
            <Button
              asChild
              size="lg"
              className="bg-white text-black hover:bg-white/90 rounded-none px-8 py-6 font-light tracking-widest text-sm"
            >
              <Link href="/products">SHOP NOW</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
