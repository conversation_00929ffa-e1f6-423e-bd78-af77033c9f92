"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, Search } from "lucide-react"
import { Badge } from "@/components/ui/badge"

export function SmartSearch() {
  const router = useRouter()
  const [query, setQuery] = useState("")
  const [loading, setLoading] = useState(false)
  const [searchParams, setSearchParams] = useState<any>(null)
  const [showParams, setShowParams] = useState(false)

  // Reset search params when query changes
  useEffect(() => {
    setSearchParams(null)
    setShowParams(false)
  }, [query])

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!query.trim()) return

    setLoading(true)

    try {
      const response = await fetch("/api/smart-search", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ query }),
      })

      const data = await response.json()
      setSearchParams(data.searchParams)
      setShowParams(true)

      // Build the search URL with parameters
      const urlParams = new URLSearchParams()

      if (data.searchParams.productType) {
        urlParams.set("category", data.searchParams.productType)
      }

      if (data.searchParams.colors && data.searchParams.colors.length) {
        urlParams.set("color", data.searchParams.colors[0])
      }

      if (data.searchParams.sizes && data.searchParams.sizes.length) {
        urlParams.set("size", data.searchParams.sizes[0])
      }

      // Add the original query
      urlParams.set("q", query)

      // Navigate to search results
      router.push(`/search?${urlParams.toString()}`)
    } catch (error) {
      console.error("Error processing smart search:", error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="w-full max-w-xl mx-auto">
      <form onSubmit={handleSearch} className="relative">
        <Input
          type="search"
          placeholder="Try: 'Blue summer dress for a 5 year old girl' • Powered by SoImagine"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="pr-10 font-light"
        />
        <Button type="submit" size="icon" disabled={loading} className="absolute right-0 top-0 h-10 w-10">
          {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
        </Button>
      </form>

      {showParams && searchParams && (
        <div className="mt-2 flex flex-wrap gap-2">
          {searchParams.productType && (
            <Badge variant="outline" className="font-light">
              Type: {searchParams.productType}
            </Badge>
          )}

          {searchParams.colors &&
            searchParams.colors.map((color: string) => (
              <Badge key={color} variant="outline" className="font-light">
                Color: {color}
              </Badge>
            ))}

          {searchParams.sizes &&
            searchParams.sizes.map((size: string) => (
              <Badge key={size} variant="outline" className="font-light">
                Size: {size}
              </Badge>
            ))}

          {searchParams.occasion && (
            <Badge variant="outline" className="font-light">
              Occasion: {searchParams.occasion}
            </Badge>
          )}

          {searchParams.season && (
            <Badge variant="outline" className="font-light">
              Season: {searchParams.season}
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}
