import Link from "next/link"
import { Instagram, Facebook, Twitter } from "lucide-react"
import CocoMilkLogo from "@/components/ui/coco-milk-logo"

export default function Footer() {
  return (
    <footer className="border-t bg-background py-12">
      <div className="container px-4 md:px-6 max-w-4xl mx-auto">
        {/* Logo and Brand */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-block mb-4">
            <CocoMilkLogo
              variant="vertical"
              width={100}
              height={75}
              scale={0.8}
              textColor="currentColor"
              className="text-foreground hover:opacity-90 transition-opacity"
            />
          </Link>
          <p className="text-sm text-muted-foreground font-light max-w-md mx-auto">
            Oversized comfort, undeniably cool. Premium children's clothing for comfort and style.
          </p>
        </div>

        {/* Navigation Links */}
        <div className="flex flex-wrap justify-center gap-6 md:gap-8 mb-8 text-sm">
          <Link href="/products" className="text-muted-foreground hover:text-foreground transition-colors">
            Shop
          </Link>
          <Link href="/about" className="text-muted-foreground hover:text-foreground transition-colors">
            About
          </Link>
          <Link href="/contact" className="text-muted-foreground hover:text-foreground transition-colors">
            Contact
          </Link>
          <Link href="/shipping" className="text-muted-foreground hover:text-foreground transition-colors">
            Shipping
          </Link>
          <Link href="/account" className="text-muted-foreground hover:text-foreground transition-colors">
            Account
          </Link>
        </div>

        {/* Social Media */}
        <div className="flex justify-center space-x-6 mb-8">
          <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
            <Instagram className="h-5 w-5" />
            <span className="sr-only">Instagram</span>
          </Link>
          <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
            <Facebook className="h-5 w-5" />
            <span className="sr-only">Facebook</span>
          </Link>
          <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
            <Twitter className="h-5 w-5" />
            <span className="sr-only">Twitter</span>
          </Link>
        </div>

        {/* Bottom Section */}
        <div className="border-t pt-6 text-center">
          <div className="text-xs text-muted-foreground mb-3 font-light">
            <p>© {new Date().getFullYear()} Coco Milk Kids (Pty) Ltd. All rights reserved.</p>
          </div>
          <div className="flex flex-wrap justify-center gap-4 text-xs">
            <Link
              href="/privacy"
              className="text-muted-foreground hover:text-foreground transition-colors font-light"
            >
              Privacy Policy
            </Link>
            <Link
              href="/terms"
              className="text-muted-foreground hover:text-foreground transition-colors font-light"
            >
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
