"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogTrigger } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ShoppingBag, Heart } from "lucide-react"
import { ProductGallery } from "@/components/storefront/products/product-gallery"
import { useCart } from "@/hooks/use-cart"
import { useWishlist } from "@/components/wishlist-provider"
import { toast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"

interface QuickViewProps {
  product: {
    id: string
    name: string
    slug: string
    price: number
    compareAtPrice?: number
    images?: string[]
    colors?: { name: string; value: string }[]
    sizes?: string[]
    description: string
    isNew?: boolean
    isSale?: boolean
  }
  children: React.ReactNode
}

export function QuickView({ product, children }: QuickViewProps) {
  const [selectedColor, setSelectedColor] = useState(product.colors?.[0]?.value || "")
  const [selectedSize, setSelectedSize] = useState(product.sizes?.[0] || "")
  const [quantity, setQuantity] = useState(1)
  const [isOpen, setIsOpen] = useState(false)

  const { addItem } = useCart()
  const { isInWishlist, toggleWishlist } = useWishlist()
  const isWishlisted = isInWishlist(product.id)

  const handleAddToCart = () => {
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      color: selectedColor,
      size: selectedSize,
      quantity,
      image: product.images?.[0] || "/placeholder.svg?height=80&width=80",
    })

    toast({
      title: "Added to cart",
      description: `${product.name} has been added to your cart.`,
    })
    setIsOpen(false)
  }

  const handleToggleWishlist = () => {
    toggleWishlist(product.id)
    toast({
      title: isWishlisted ? "Removed from wishlist" : "Added to wishlist",
      description: `${product.name} has been ${isWishlisted ? "removed from" : "added to"} your wishlist.`,
    })
  }

  const discount = product.compareAtPrice
    ? Math.round(((product.compareAtPrice - product.price) / product.compareAtPrice) * 100)
    : 0

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="grid md:grid-cols-2 gap-6">
          {/* Product Images */}
          <div className="relative">
            <ProductGallery images={product.images || []} />
            {product.isNew && <Badge className="absolute top-2 right-2 bg-[#012169]">New</Badge>}
            {product.isSale && (
              <Badge className="absolute top-2 right-2 bg-[#6C1411]">{discount > 0 ? `-${discount}%` : "Sale"}</Badge>
            )}
          </div>

          {/* Product Info */}
          <div className="space-y-4">
            <div>
              <h2 className="text-xl font-bold font-montserrat">{product.name}</h2>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-lg font-semibold">${product.price.toFixed(2)}</span>
                {product.compareAtPrice && (
                  <>
                    <span className="text-muted-foreground line-through text-sm">
                      ${product.compareAtPrice.toFixed(2)}
                    </span>
                    <Badge variant="destructive" className="text-xs">
                      Save {discount}%
                    </Badge>
                  </>
                )}
              </div>
            </div>

            <p className="text-sm text-muted-foreground">{product.description}</p>

            {/* Color Selection */}
            {product.colors && product.colors.length > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-2">Color</h4>
                <div className="flex space-x-2">
                  {product.colors.map((color) => (
                    <button
                      key={color.value}
                      className={cn(
                        "w-8 h-8 rounded-full border-2 border-transparent",
                        selectedColor === color.value && "border-black",
                      )}
                      style={{ backgroundColor: color.value }}
                      onClick={() => setSelectedColor(color.value)}
                      title={color.name}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Size Selection */}
            {product.sizes && product.sizes.length > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-2">Size</h4>
                <div className="flex flex-wrap gap-2">
                  {product.sizes.map((size) => (
                    <button
                      key={size}
                      className={cn(
                        "px-3 py-1 border rounded text-sm",
                        selectedSize === size
                          ? "bg-black text-white border-black"
                          : "bg-white text-black border-gray-300 hover:border-gray-400",
                      )}
                      onClick={() => setSelectedSize(size)}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Quantity */}
            <div>
              <h4 className="text-sm font-medium mb-2">Quantity</h4>
              <div className="flex items-center space-x-2">
                <button
                  className="w-8 h-8 border rounded flex items-center justify-center"
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                >
                  -
                </button>
                <span className="w-8 text-center">{quantity}</span>
                <button
                  className="w-8 h-8 border rounded flex items-center justify-center"
                  onClick={() => setQuantity(quantity + 1)}
                >
                  +
                </button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button onClick={handleAddToCart} className="w-full bg-[#012169] hover:bg-[#012169]/90">
                <ShoppingBag className="w-4 h-4 mr-2" />
                Add to Cart
              </Button>
              <Button
                variant="outline"
                onClick={handleToggleWishlist}
                className={cn("w-full", isWishlisted && "bg-pink-50 border-pink-200 text-pink-600")}
              >
                <Heart className={cn("w-4 h-4 mr-2", isWishlisted && "fill-current")} />
                {isWishlisted ? "Remove from Wishlist" : "Add to Wishlist"}
              </Button>
            </div>

            {/* Product Details */}
            <div className="border-t pt-4 space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">SKU:</span>
                <span>{product.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Availability:</span>
                <span className="text-green-600">In Stock</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Material:</span>
                <span>100% Organic Cotton</span>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
