import { cn } from "@/lib/utils"

export type LogoVariant = "standard" | "horizontal" | "square" | "icon-only"
export type LogoTheme = "light" | "dark" | "color"

export interface LogoProps {
  variant?: LogoVariant
  theme?: LogoTheme
  className?: string
  width?: number
  height?: number
}

export function Logo({ variant = "standard", theme = "dark", className, width, height }: LogoProps) {
  // Base colors for different themes
  const colors = {
    dark: {
      primary: "#0D0D0D",
      secondary: "#2B1D18",
    },
    light: {
      primary: "#FFFFFF",
      secondary: "#E5E5E5",
    },
    color: {
      primary: "#6C1411",
      secondary: "#012169",
    },
  }

  const currentColors = colors[theme]

  // Standard (vertical) logo
  if (variant === "standard") {
    return (
      <svg
        width={width || 120}
        height={height || 80}
        viewBox="0 0 120 80"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={cn("transition-colors", className)}
        aria-label="Coco Milk Kids"
      >
        {/* Hanger */}
        <path
          d="M60 20C60 20 55 20 55 25L50 35H70L65 25C65 20 60 20 60 20Z"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
          fill="none"
        />
        <path
          d="M60 20V15"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />

        {/* coco */}
        <path
          d="M35 50C35 47.2386 37.2386 45 40 45C42.7614 45 45 47.2386 45 50C45 52.7614 42.7614 55 40 55C37.2386 55 35 52.7614 35 50Z"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          fill="none"
        />
        <path
          d="M50 50C50 47.2386 52.2386 45 55 45C57.7614 45 60 47.2386 60 50C60 52.7614 57.7614 55 55 55C52.2386 55 50 52.7614 50 50Z"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          fill="none"
        />
        <path
          d="M65 50C65 47.2386 67.2386 45 70 45C72.7614 45 75 47.2386 75 50C75 52.7614 72.7614 55 70 55C67.2386 55 65 52.7614 65 50Z"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          fill="none"
        />
        <path
          d="M80 50C80 47.2386 82.2386 45 85 45C87.7614 45 90 47.2386 90 50C90 52.7614 87.7614 55 85 55C82.2386 55 80 52.7614 80 50Z"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          fill="none"
        />

        {/* milk */}
        <path
          d="M40 65V60M40 65L35 60M40 65L45 60"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M50 60V65"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M55 60V65L60 60V65"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M70 60V65M70 60L65 65M70 60L75 65"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />

        {/* KIDS */}
        <path
          d="M50 75L45 70L50 70L45 75"
          stroke={currentColors.secondary}
          strokeWidth="1"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M55 70V75"
          stroke={currentColors.secondary}
          strokeWidth="1"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M60 70H65V72.5H60V75H65"
          stroke={currentColors.secondary}
          strokeWidth="1"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M70 70C70 70 75 70 75 72.5C75 75 70 75 70 75"
          stroke={currentColors.secondary}
          strokeWidth="1"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    )
  }

  // Horizontal logo
  if (variant === "horizontal") {
    return (
      <svg
        width={width || 200}
        height={height || 50}
        viewBox="0 0 200 50"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={cn("transition-colors", className)}
        aria-label="Coco Milk Kids"
      >
        {/* Hanger */}
        <path
          d="M30 25C30 25 25 25 25 30L20 40H40L35 30C35 25 30 25 30 25Z"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
          fill="none"
        />
        <path
          d="M30 25V20"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />

        {/* coco */}
        <path
          d="M55 30C55 27.2386 57.2386 25 60 25C62.7614 25 65 27.2386 65 30C65 32.7614 62.7614 35 60 35C57.2386 35 55 32.7614 55 30Z"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          fill="none"
        />
        <path
          d="M70 30C70 27.2386 72.2386 25 75 25C77.7614 25 80 27.2386 80 30C80 32.7614 77.7614 35 75 35C72.2386 35 70 32.7614 70 30Z"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          fill="none"
        />
        <path
          d="M85 30C85 27.2386 87.2386 25 90 25C92.7614 25 95 27.2386 95 30C95 32.7614 92.7614 35 90 35C87.2386 35 85 32.7614 85 30Z"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          fill="none"
        />
        <path
          d="M100 30C100 27.2386 102.239 25 105 25C107.761 25 110 27.2386 110 30C110 32.7614 107.761 35 105 35C102.239 35 100 32.7614 100 30Z"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          fill="none"
        />

        {/* milk */}
        <path
          d="M125 25V30M125 30L120 25M125 30L130 25"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M135 25V30"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M145 25V30L150 25V30"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M160 25V30M160 25L155 30M160 25L165 30"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />

        {/* KIDS */}
        <path
          d="M175 35L170 30L175 30L170 35"
          stroke={currentColors.secondary}
          strokeWidth="1"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M180 30V35"
          stroke={currentColors.secondary}
          strokeWidth="1"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M185 30H190V32.5H185V35H190"
          stroke={currentColors.secondary}
          strokeWidth="1"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M195 30C195 30 200 30 200 32.5C200 35 195 35 195 35"
          stroke={currentColors.secondary}
          strokeWidth="1"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    )
  }

  // Square/compact logo
  if (variant === "square") {
    return (
      <svg
        width={width || 80}
        height={height || 80}
        viewBox="0 0 80 80"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={cn("transition-colors", className)}
        aria-label="Coco Milk Kids"
      >
        {/* Hanger */}
        <path
          d="M40 20C40 20 35 20 35 25L30 35H50L45 25C45 20 40 20 40 20Z"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
          fill="none"
        />
        <path
          d="M40 20V15"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />

        {/* coco */}
        <path
          d="M20 50C20 47.2386 22.2386 45 25 45C27.7614 45 30 47.2386 30 50C30 52.7614 27.7614 55 25 55C22.2386 55 20 52.7614 20 50Z"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          fill="none"
        />
        <path
          d="M35 50C35 47.2386 37.2386 45 40 45C42.7614 45 45 47.2386 45 50C45 52.7614 42.7614 55 40 55C37.2386 55 35 52.7614 35 50Z"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          fill="none"
        />
        <path
          d="M50 50C50 47.2386 52.2386 45 55 45C57.7614 45 60 47.2386 60 50C60 52.7614 57.7614 55 55 55C52.2386 55 50 52.7614 50 50Z"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          fill="none"
        />

        {/* KIDS */}
        <path
          d="M30 65L25 60L30 60L25 65"
          stroke={currentColors.secondary}
          strokeWidth="1"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M35 60V65"
          stroke={currentColors.secondary}
          strokeWidth="1"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M40 60H45V62.5H40V65H45"
          stroke={currentColors.secondary}
          strokeWidth="1"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M50 60C50 60 55 60 55 62.5C55 65 50 65 50 65"
          stroke={currentColors.secondary}
          strokeWidth="1"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    )
  }

  // Icon-only logo (just the hanger)
  if (variant === "icon-only") {
    return (
      <svg
        width={width || 40}
        height={height || 40}
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={cn("transition-colors", className)}
        aria-label="Coco Milk Kids"
      >
        {/* Hanger */}
        <path
          d="M20 10C20 10 15 10 15 15L10 25H30L25 15C25 10 20 10 20 10Z"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
          fill="none"
        />
        <path
          d="M20 10V5"
          stroke={currentColors.primary}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    )
  }

  // Default fallback
  return null
}
