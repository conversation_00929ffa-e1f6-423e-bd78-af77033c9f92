"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Gift, CreditCard } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

export function GiftOptions() {
  const [includeGiftWrap, setIncludeGiftWrap] = useState(false)
  const [giftMessage, setGiftMessage] = useState("")
  const [giftCardAmount, setGiftCardAmount] = useState("50")
  const [recipientEmail, setRecipientEmail] = useState("")
  const [recipientName, setRecipientName] = useState("")
  const [senderName, setSenderName] = useState("")
  const [personalMessage, setPersonalMessage] = useState("")

  const handleGiftWrapToggle = (checked: boolean) => {
    setIncludeGiftWrap(checked)
    if (checked) {
      toast({
        title: "Gift wrapping added",
        description: "Gift wrapping has been added to your order for $5.00.",
      })
    } else {
      toast({
        title: "Gift wrapping removed",
        description: "Gift wrapping has been removed from your order.",
      })
    }
  }

  const handleGiftCardPurchase = (e: React.FormEvent) => {
    e.preventDefault()
    toast({
      title: "Gift card purchased",
      description: `A $${giftCardAmount} gift card will be sent to ${recipientName} at ${recipientEmail}.`,
    })
  }

  return (
    <Tabs defaultValue="gift-wrap" className="w-full">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="gift-wrap">
          <Gift className="h-4 w-4 mr-2" />
          Gift Wrapping
        </TabsTrigger>
        <TabsTrigger value="gift-card">
          <CreditCard className="h-4 w-4 mr-2" />
          Gift Cards
        </TabsTrigger>
      </TabsList>

      <TabsContent value="gift-wrap" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Gift Wrapping Options</CardTitle>
            <CardDescription>Add a special touch to your gift</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox id="gift-wrap" checked={includeGiftWrap} onCheckedChange={handleGiftWrapToggle} />
              <Label htmlFor="gift-wrap" className="flex-1 cursor-pointer">
                <div className="font-medium">Add Gift Wrapping</div>
                <div className="text-sm text-muted-foreground">$5.00 per order</div>
              </Label>
            </div>

            {includeGiftWrap && (
              <div className="space-y-4 pt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="aspect-square rounded-md overflow-hidden border">
                      <img
                        src="/placeholder.svg?height=200&width=200"
                        alt="Kraft Paper Gift Wrap"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-center mt-2">
                      <div className="font-medium">Kraft Paper</div>
                      <div className="text-sm text-muted-foreground">Eco-friendly</div>
                    </div>
                  </div>
                  <div>
                    <div className="aspect-square rounded-md overflow-hidden border">
                      <img
                        src="/placeholder.svg?height=200&width=200"
                        alt="Colorful Gift Wrap"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-center mt-2">
                      <div className="font-medium">Colorful Pattern</div>
                      <div className="text-sm text-muted-foreground">Festive design</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="gift-message">Gift Message (Optional)</Label>
                  <Textarea
                    id="gift-message"
                    placeholder="Enter your gift message here..."
                    value={giftMessage}
                    onChange={(e) => setGiftMessage(e.target.value)}
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground text-right">{giftMessage.length}/200 characters</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="gift-card" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Purchase a Gift Card</CardTitle>
            <CardDescription>The perfect gift for any occasion</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleGiftCardPurchase} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="gift-card-amount">Gift Card Amount</Label>
                <div className="flex flex-wrap gap-2">
                  {["25", "50", "75", "100", "150", "200"].map((amount) => (
                    <Button
                      key={amount}
                      type="button"
                      variant={giftCardAmount === amount ? "default" : "outline"}
                      onClick={() => setGiftCardAmount(amount)}
                      className="flex-1 min-w-[80px]"
                    >
                      ${amount}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="recipient-name">Recipient's Name</Label>
                  <Input
                    id="recipient-name"
                    value={recipientName}
                    onChange={(e) => setRecipientName(e.target.value)}
                    placeholder="Enter recipient's name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="recipient-email">Recipient's Email</Label>
                  <Input
                    id="recipient-email"
                    type="email"
                    value={recipientEmail}
                    onChange={(e) => setRecipientEmail(e.target.value)}
                    placeholder="Enter recipient's email"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="sender-name">Your Name</Label>
                <Input
                  id="sender-name"
                  value={senderName}
                  onChange={(e) => setSenderName(e.target.value)}
                  placeholder="Enter your name"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="personal-message">Personal Message (Optional)</Label>
                <Textarea
                  id="personal-message"
                  placeholder="Enter your personal message..."
                  value={personalMessage}
                  onChange={(e) => setPersonalMessage(e.target.value)}
                  rows={3}
                />
                <p className="text-xs text-muted-foreground text-right">{personalMessage.length}/200 characters</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="gift-card-terms" required />
                  <Label htmlFor="gift-card-terms" className="text-sm">
                    I agree to the gift card terms and conditions
                  </Label>
                </div>
              </div>

              <Button type="submit" className="w-full">
                Purchase Gift Card
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Gift Card Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">How it works</h4>
              <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1">
                <li>Gift cards are delivered by email and contain instructions to redeem them at checkout.</li>
                <li>Our gift cards have no additional processing fees.</li>
                <li>Gift cards are non-refundable and cannot be redeemed for cash.</li>
                <li>Gift cards do not expire and can be used for multiple purchases.</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
