"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import { 
  MessageCircle, 
  Phone, 
  Mail, 
  HelpCircle, 
  Clock, 
  CheckCircle,
  Package,
  CreditCard,
  Truck,
  RotateCcw
} from "lucide-react"

const faqs = [
  {
    category: "Orders",
    icon: Package,
    questions: [
      {
        question: "How can I track my order?",
        answer: "You can track your order by visiting the 'Orders' page in your account or using the order tracking tool on our website. You'll need your order number and email address."
      },
      {
        question: "Can I modify or cancel my order?",
        answer: "Orders can be modified or cancelled within 1 hour of placement. After that, please contact our customer service team for assistance."
      },
      {
        question: "What payment methods do you accept?",
        answer: "We accept all major credit cards (Visa, MasterCard, American Express), PayPal, Apple Pay, and Google Pay."
      }
    ]
  },
  {
    category: "Shipping",
    icon: Truck,
    questions: [
      {
        question: "What are your shipping options?",
        answer: "We offer standard shipping (3-5 business days), express shipping (1-2 business days), and overnight shipping. Free standard shipping on orders over $75."
      },
      {
        question: "Do you ship internationally?",
        answer: "Currently, we ship to the United States, Canada, and select European countries. International shipping rates and times vary by destination."
      }
    ]
  },
  {
    category: "Returns",
    icon: RotateCcw,
    questions: [
      {
        question: "What is your return policy?",
        answer: "We offer free returns within 30 days of purchase. Items must be unworn, unwashed, and in original condition with tags attached."
      },
      {
        question: "How do I start a return?",
        answer: "You can initiate a return through your account dashboard or contact our customer service team. We'll provide a prepaid return label."
      }
    ]
  }
]

export function CustomerServiceHub() {
  const [chatOpen, setChatOpen] = useState(false)
  const [contactForm, setContactForm] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
    orderNumber: ""
  })

  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, this would submit to customer service
    console.log("Contact form submitted:", contactForm)
    setContactForm({ name: "", email: "", subject: "", message: "", orderNumber: "" })
  }

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Dialog open={chatOpen} onOpenChange={setChatOpen}>
          <DialogTrigger asChild>
            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="p-6 text-center">
                <MessageCircle className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                <h3 className="font-medium">Live Chat</h3>
                <p className="text-sm text-muted-foreground">Chat with our support team</p>
                <Badge variant="secondary" className="mt-2">
                  <Clock className="h-3 w-3 mr-1" />
                  Online
                </Badge>
              </CardContent>
            </Card>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Live Chat Support</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm">
                    CS
                  </div>
                  <span className="font-medium">Customer Service</span>
                  <Badge variant="secondary" className="text-xs">Online</Badge>
                </div>
                <p className="text-sm">Hi! How can I help you today?</p>
              </div>
              <Input placeholder="Type your message..." />
              <Button className="w-full">Send Message</Button>
            </div>
          </DialogContent>
        </Dialog>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <Phone className="h-8 w-8 mx-auto mb-2 text-green-600" />
            <h3 className="font-medium">Call Us</h3>
            <p className="text-sm text-muted-foreground">1-800-COCO-KIDS</p>
            <p className="text-xs text-muted-foreground mt-1">Mon-Fri 9AM-6PM EST</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <Mail className="h-8 w-8 mx-auto mb-2 text-purple-600" />
            <h3 className="font-medium">Email Support</h3>
            <p className="text-sm text-muted-foreground"><EMAIL></p>
            <p className="text-xs text-muted-foreground mt-1">24-48 hour response</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <HelpCircle className="h-8 w-8 mx-auto mb-2 text-orange-600" />
            <h3 className="font-medium">Help Center</h3>
            <p className="text-sm text-muted-foreground">Browse FAQs & guides</p>
            <Badge variant="outline" className="mt-2">
              <CheckCircle className="h-3 w-3 mr-1" />
              Self-service
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="faq" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="faq">Frequently Asked Questions</TabsTrigger>
          <TabsTrigger value="contact">Contact Us</TabsTrigger>
        </TabsList>
        
        <TabsContent value="faq" className="mt-6">
          <div className="space-y-6">
            {faqs.map((category) => (
              <Card key={category.category}>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <category.icon className="h-5 w-5" />
                    <span>{category.category}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Accordion type="single" collapsible className="w-full">
                    {category.questions.map((faq, index) => (
                      <AccordionItem key={index} value={`${category.category}-${index}`}>
                        <AccordionTrigger className="text-left">
                          {faq.question}
                        </AccordionTrigger>
                        <AccordionContent>
                          {faq.answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="contact" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Contact Our Support Team</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleContactSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      value={contactForm.name}
                      onChange={(e) => setContactForm({ ...contactForm, name: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={contactForm.email}
                      onChange={(e) => setContactForm({ ...contactForm, email: e.target.value })}
                      required
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="subject">Subject</Label>
                    <Input
                      id="subject"
                      value={contactForm.subject}
                      onChange={(e) => setContactForm({ ...contactForm, subject: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="orderNumber">Order Number (Optional)</Label>
                    <Input
                      id="orderNumber"
                      value={contactForm.orderNumber}
                      onChange={(e) => setContactForm({ ...contactForm, orderNumber: e.target.value })}
                      placeholder="ORD-2024-001"
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="message">Message</Label>
                  <Textarea
                    id="message"
                    value={contactForm.message}
                    onChange={(e) => setContactForm({ ...contactForm, message: e.target.value })}
                    rows={4}
                    required
                  />
                </div>
                
                <Button type="submit" className="w-full">
                  Send Message
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
