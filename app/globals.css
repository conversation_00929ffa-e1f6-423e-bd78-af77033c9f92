@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base font is handled by Tailwind through the sans font family */

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    /* Clean light theme inspired by selfi.co.za */
    --background: 0 0% 100%;
    --foreground: 0 0% 10%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 10%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 10%;
    --primary: 0 0% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 98%;
    --secondary-foreground: 0 0% 10%;
    --muted: 0 0% 97%;
    --muted-foreground: 0 0% 50%;
    --accent: 0 0% 97%;
    --accent-foreground: 0 0% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 92%;
    --input: 0 0% 96%;
    --ring: 0 0% 10%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Clean Typography - Selfi.co.za inspired */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 400;
  letter-spacing: 0.01em;
  line-height: 1.2;
}

body {
  letter-spacing: 0.005em;
  font-weight: 400;
  line-height: 1.5;
}

/* Clean product titles */
.product-title {
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.3;
  letter-spacing: 0.01em;
}

/* Clean Animations - Selfi.co.za inspired */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.4s ease-out forwards;
}

/* Subtle hover effects */
.hover-lift {
  transition: transform 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.image-zoom {
  overflow: hidden;
}

.image-zoom img {
  transition: transform 0.6s ease;
}

.image-zoom:hover img {
  transform: scale(1.02);
}

/* Clean Button Styles - Selfi.co.za inspired */
.btn-minimal {
  font-weight: 400;
  letter-spacing: 0.01em;
  transition: all 0.2s ease;
  position: relative;
}

.btn-minimal::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: currentColor;
  transition: width 0.2s ease;
}

.btn-minimal:hover::after {
  width: 100%;
}

/* Clean Product Card */
.product-card-clean {
  transition: opacity 0.2s ease;
}

.product-card-clean:hover {
  opacity: 0.8;
}

/* Simple Hover Effects */
.hover-underline {
  position: relative;
}

.hover-underline::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: currentColor;
  transition: width 0.2s ease;
}

.hover-underline:hover::after {
  width: 100%;
}

/* Clean borders */
.border-clean {
  border: 1px solid #f0f0f0;
}

/* Minimal spacing */
.space-clean > * + * {
  margin-top: 0.5rem;
}
