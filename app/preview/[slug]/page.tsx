import { notFound } from 'next/navigation'
import { prisma } from '@/lib/prisma'
import { PageRenderer } from '@/lib/page-builder/components/page-renderer'

interface PreviewPageProps {
  params: {
    slug: string
  }
}

export default async function PreviewPage({ params }: PreviewPageProps) {
  const { slug } = params

  try {
    // Fetch the page from database
    const page = await prisma.page.findUnique({
      where: { slug },
      include: {
        blocks: {
          orderBy: { position: 'asc' }
        }
      }
    })

    if (!page) {
      notFound()
    }

    // Transform page data
    const pageData = {
      id: page.id,
      title: page.title,
      slug: page.slug,
      description: page.description,
      status: page.status,
      type: page.type,
      template: page.template,
      seoTitle: page.seoTitle,
      seoDescription: page.seoDescription,
      seoKeywords: page.seoKeywords,
      ogImage: page.ogImage,
      publishedAt: page.publishedAt,
      scheduledAt: page.scheduledAt,
      expiresAt: page.expiresAt,
      isHomePage: page.isHomePage,
      isLandingPage: page.isLandingPage,
      requiresAuth: page.requiresAuth,
      allowComments: page.allowComments,
      viewCount: page.viewCount,
      shareCount: page.shareCount,
      metadata: page.metadata,
      customCss: page.customCss,
      customJs: page.customJs,
      createdBy: page.createdBy,
      updatedBy: page.updatedBy,
      createdAt: page.createdAt,
      updatedAt: page.updatedAt,
      blocks: page.blocks.map(block => ({
        id: block.id,
        type: block.blockType,
        position: block.position,
        isVisible: block.isVisible,
        configuration: block.configuration,
        content: block.content,
        styling: block.styling,
        responsive: block.responsive,
        animation: block.animation,
        conditions: block.conditions,
        createdAt: block.createdAt,
        updatedAt: block.updatedAt,
      })),
      settings: {
        title: page.title,
        description: page.description,
        seoTitle: page.seoTitle,
        seoDescription: page.seoDescription,
        seoKeywords: page.seoKeywords,
        ogImage: page.ogImage,
        customCss: page.customCss,
        customJs: page.customJs,
        requiresAuth: page.requiresAuth,
        allowComments: page.allowComments,
      }
    }

    return (
      <div>
        {/* SEO Meta Tags */}
        <head>
          <title>{page.seoTitle || page.title}</title>
          {page.seoDescription && (
            <meta name="description" content={page.seoDescription} />
          )}
          {page.seoKeywords && page.seoKeywords.length > 0 && (
            <meta name="keywords" content={page.seoKeywords.join(', ')} />
          )}
          {page.ogImage && (
            <>
              <meta property="og:image" content={page.ogImage} />
              <meta name="twitter:image" content={page.ogImage} />
            </>
          )}
          <meta property="og:title" content={page.seoTitle || page.title} />
          <meta property="og:description" content={page.seoDescription || page.description || ''} />
          <meta name="twitter:title" content={page.seoTitle || page.title} />
          <meta name="twitter:description" content={page.seoDescription || page.description || ''} />
        </head>

        {/* Custom CSS */}
        {page.customCss && (
          <style dangerouslySetInnerHTML={{ __html: page.customCss }} />
        )}

        {/* Page Content */}
        <PageRenderer page={pageData} />

        {/* Custom JavaScript */}
        {page.customJs && (
          <script dangerouslySetInnerHTML={{ __html: page.customJs }} />
        )}
      </div>
    )

  } catch (error) {
    console.error('Error loading page:', error)
    notFound()
  }
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PreviewPageProps) {
  const { slug } = params

  try {
    const page = await prisma.page.findUnique({
      where: { slug },
      select: {
        title: true,
        seoTitle: true,
        description: true,
        seoDescription: true,
        seoKeywords: true,
        ogImage: true,
      }
    })

    if (!page) {
      return {
        title: 'Page Not Found',
        description: 'The requested page could not be found.',
      }
    }

    return {
      title: page.seoTitle || page.title,
      description: page.seoDescription || page.description,
      keywords: page.seoKeywords?.join(', '),
      openGraph: {
        title: page.seoTitle || page.title,
        description: page.seoDescription || page.description,
        images: page.ogImage ? [{ url: page.ogImage }] : [],
      },
      twitter: {
        title: page.seoTitle || page.title,
        description: page.seoDescription || page.description,
        images: page.ogImage ? [page.ogImage] : [],
      },
    }
  } catch (error) {
    console.error('Error generating metadata:', error)
    return {
      title: 'Page Not Found',
      description: 'The requested page could not be found.',
    }
  }
}
