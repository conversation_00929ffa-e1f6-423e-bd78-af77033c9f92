"use client"

import { useCart } from "@/hooks/use-cart"
import { CartItem } from "@/components/storefront/cart/cart-item"
import { CartSummary } from "@/components/storefront/cart/cart-summary"
import { But<PERSON> } from "@/components/ui/button"
import { ShoppingBag } from "lucide-react"
import Link from "next/link"

export default function CartPage() {
  const { items, isEmpty } = useCart()

  if (isEmpty) {
    return (
      <div className="container px-4 md:px-6 py-16">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <ShoppingBag className="h-16 w-16 text-muted-foreground" />
          <h1 className="text-2xl font-bold font-montserrat">Your cart is empty</h1>
          <p className="text-muted-foreground">Looks like you haven't added anything to your cart yet.</p>
          <Button asChild>
            <Link href="/products">Continue Shopping</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      <h1 className="text-2xl md:text-3xl font-bold font-montserrat mb-6">Shopping Cart</h1>
      <div className="grid lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="space-y-4">
            {items.map((item) => (
              <CartItem key={`${item.id}-${item.size}`} item={item} />
            ))}
          </div>
        </div>
        <div>
          <CartSummary />
        </div>
      </div>
    </div>
  )
}
