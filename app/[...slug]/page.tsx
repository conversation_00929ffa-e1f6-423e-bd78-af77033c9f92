import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { UniversalRenderer } from '@/lib/rendering/universal-renderer'
import { RouteResolver } from '@/lib/routing/route-resolver'
import { generateSeoMeta } from '@/lib/posts/utils'

interface DynamicPageProps {
  params: { slug: string[] }
  searchParams: { [key: string]: string | string[] | undefined }
}

// Generate metadata for dynamic pages
export async function generateMetadata({ params }: DynamicPageProps): Promise<Metadata> {
  const resolvedParams = await params
  const pathname = '/' + resolvedParams.slug.join('/')

  try {
    // Resolve the route to get metadata
    const resolution = await RouteResolver.resolve(pathname)

    if (resolution.type === 'notfound') {
      return {
        title: 'Page Not Found',
        description: 'The page you are looking for could not be found.',
      }
    }

    // Generate metadata based on resolution type
    switch (resolution.type) {
      case 'page':
        const pageData = await RouteResolver.getPageData(resolution.data.id)
        if (!pageData) {
          return { title: 'Page Not Found' }
        }

        return {
          title: pageData.seoTitle || pageData.title,
          description: pageData.seoDescription || pageData.description || undefined,
          keywords: pageData.seoKeywords?.join(', ') || undefined,
          openGraph: {
            title: pageData.seoTitle || pageData.title,
            description: pageData.seoDescription || pageData.description || undefined,
            images: pageData.ogImage ? [{ url: pageData.ogImage }] : undefined,
            type: 'website',
            url: pathname,
          },
          twitter: {
            card: 'summary_large_image',
            title: pageData.seoTitle || pageData.title,
            description: pageData.seoDescription || pageData.description || undefined,
            images: pageData.ogImage ? [pageData.ogImage] : undefined,
          },
          robots: {
            index: pageData.status === 'published',
            follow: pageData.status === 'published',
          },
          alternates: {
            canonical: pathname,
          },
        }

      case 'post':
        const postData = await RouteResolver.getPostData(resolution.data.id)
        if (!postData) {
          return { title: 'Post Not Found' }
        }

        return generateSeoMeta(postData, pathname)

      case 'archive':
        return {
          title: `${resolution.data.label} Archive`,
          description: resolution.data.description || `Browse all ${resolution.data.label.toLowerCase()}`,
          openGraph: {
            title: `${resolution.data.label} Archive`,
            description: resolution.data.description || `Browse all ${resolution.data.label.toLowerCase()}`,
            type: 'website',
            url: pathname,
          },
          robots: {
            index: true,
            follow: true,
          },
          alternates: {
            canonical: pathname,
          },
        }

      default:
        return {
          title: 'Coco Milk Kids',
          description: 'Premium children\'s clothing designed for comfort and style.',
        }
    }
  } catch (error) {
    console.error('Error generating metadata:', error)
    return {
      title: 'Coco Milk Kids',
      description: 'Premium children\'s clothing designed for comfort and style.',
    }
  }
}

export default async function DynamicPage({ params, searchParams }: DynamicPageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const pathname = '/' + resolvedParams.slug.join('/')

  return (
    <UniversalRenderer
      pathname={pathname}
      searchParams={resolvedSearchParams}
    />
  )
}

// Generate static params for known routes (optional optimization)
export async function generateStaticParams() {
  try {
    // Generate static params for published pages and posts
    const sitemapData = await RouteResolver.generateSitemapData()
    
    return sitemapData
      .filter(entry => entry.url !== '/') // Exclude home page
      .map(entry => ({
        slug: entry.url.split('/').filter(Boolean)
      }))
      .slice(0, 100) // Limit to first 100 for build performance
  } catch (error) {
    console.error('Error generating static params:', error)
    return []
  }
}
