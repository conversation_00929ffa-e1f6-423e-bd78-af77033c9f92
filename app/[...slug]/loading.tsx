import { LoadingSpinner } from '@/components/ui/loading-spinner'

export default function Loading() {
  return (
    <div className="min-h-screen">
      {/* Header Skeleton */}
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          {/* Title */}
          <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
          
          {/* Subtitle */}
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          
          {/* Content Blocks */}
          <div className="space-y-6">
            {/* Hero Block */}
            <div className="h-64 bg-gray-200 rounded-lg"></div>
            
            {/* Text Block */}
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded w-full"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/5"></div>
            </div>
            
            {/* Grid Block */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="space-y-3">
                  <div className="h-48 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
            
            {/* Another Text Block */}
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded w-full"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Loading Indicator */}
      <div className="fixed bottom-4 right-4">
        <div className="bg-white rounded-full p-3 shadow-lg border">
          <LoadingSpinner size="sm" />
        </div>
      </div>
    </div>
  )
}
