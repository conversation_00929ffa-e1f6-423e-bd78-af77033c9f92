"use client";

import { ErrorComponent } from "@/components/ui/error";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <div className="container mx-auto py-10">
      <ErrorComponent
        error={error}
        reset={reset}
        title="Something went wrong"
        description="We apologize for the inconvenience. Please try again or contact customer support if the issue persists."
        showHomeButton={true}
        showReportButton={true}
        showStackTrace={process.env.NODE_ENV === "development"}
      />
    </div>
  );
}