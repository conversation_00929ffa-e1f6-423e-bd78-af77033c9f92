"use client"

import { useEffect, useState } from "react"
import { useWishlist } from "@/components/wishlist-provider"
import { ProductCard } from "@/components/storefront/products/product-card"
import { Button } from "@/components/ui/button"
import { Heart } from "lucide-react"
import Link from "next/link"
import { getProductsByIds } from "@/lib/products"

export default function WishlistPage() {
  const { items, removeFromWishlist } = useWishlist()
  const [products, setProducts] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchWishlistProducts() {
      if (items.length === 0) {
        setProducts([])
        setIsLoading(false)
        return
      }

      try {
        const wishlistProducts = await getProductsByIds(items)
        setProducts(wishlistProducts)
      } catch (error) {
        console.error("Failed to fetch wishlist products:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchWishlistProducts()
  }, [items])

  if (isLoading) {
    return (
      <div className="container px-4 md:px-6 py-16">
        <div className="flex justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
        </div>
      </div>
    )
  }

  if (items.length === 0 || products.length === 0) {
    return (
      <div className="container px-4 md:px-6 py-16">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <Heart className="h-16 w-16 text-muted-foreground" />
          <h1 className="text-2xl font-bold font-montserrat">Your wishlist is empty</h1>
          <p className="text-muted-foreground">Looks like you haven't added anything to your wishlist yet.</p>
          <Button asChild>
            <Link href="/products">Continue Shopping</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      <h1 className="text-2xl md:text-3xl font-bold font-montserrat mb-6">My Wishlist</h1>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
        {products.map((product) => (
          <div key={product.id} className="relative">
            <Button
              variant="outline"
              size="icon"
              className="absolute right-2 top-2 z-10 h-8 w-8 rounded-full bg-white"
              onClick={() => removeFromWishlist(product.id)}
            >
              <Heart className="h-4 w-4 fill-current text-red-500" />
              <span className="sr-only">Remove from wishlist</span>
            </Button>
            <ProductCard product={product} />
          </div>
        ))}
      </div>
    </div>
  )
}
