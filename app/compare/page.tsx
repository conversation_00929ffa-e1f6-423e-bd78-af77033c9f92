"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { getProductsByIds } from "@/lib/products"
import { useCart } from "@/hooks/use-cart"
import { toast } from "@/components/ui/use-toast"
import { Scale, X, ShoppingBag, Heart, Star } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

interface Product {
  id: string
  name: string
  slug: string
  price: number
  compareAtPrice?: number
  images: string[]
  colors: { name: string; value: string }[]
  sizes: string[]
  description: string
  isNew?: boolean
  isSale?: boolean
  categoryId?: string
}

export default function ComparePage() {
  const [compareList, setCompareList] = useState<string[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { addItem } = useCart()

  useEffect(() => {
    const stored = localStorage.getItem("compareList")
    if (stored) {
      try {
        setCompareList(JSON.parse(stored))
      } catch (error) {
        console.error("Failed to parse compare list:", error)
      }
    }
    setIsLoading(false)
  }, [])

  useEffect(() => {
    const loadProducts = async () => {
      if (compareList.length > 0) {
        try {
          const productData = await getProductsByIds(compareList)
          setProducts(productData)
        } catch (error) {
          console.error("Failed to load products:", error)
        }
      } else {
        setProducts([])
      }
    }
    loadProducts()
  }, [compareList])

  const removeFromCompare = (productId: string) => {
    const newList = compareList.filter((id) => id !== productId)
    setCompareList(newList)
    localStorage.setItem("compareList", JSON.stringify(newList))
  }

  const clearComparison = () => {
    setCompareList([])
    setProducts([])
    localStorage.removeItem("compareList")
  }

  const handleAddToCart = (product: Product) => {
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      color: product.colors[0]?.value || "",
      size: product.sizes[0] || "",
      quantity: 1,
      image: product.images[0] || "https://images.unsplash.com/photo-1519238359922-989348752efb?q=80&w=80&h=80&auto=format&fit=crop",
    })

    toast({
      title: "Added to cart",
      description: `${product.name} has been added to your cart.`,
    })
  }

  if (isLoading) {
    return (
      <div className="container px-4 md:px-6 py-16">
        <div className="flex justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
        </div>
      </div>
    )
  }

  if (products.length === 0) {
    return (
      <div className="container px-4 md:px-6 py-16">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <Scale className="h-16 w-16 text-muted-foreground" />
          <h1 className="text-2xl font-bold font-montserrat">No Products to Compare</h1>
          <p className="text-muted-foreground max-w-md">
            Start adding products to your comparison list to see them here. You can compare up to 4 products at once.
          </p>
          <Button asChild>
            <Link href="/products">Browse Products</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold font-montserrat">Product Comparison</h1>
          <p className="text-muted-foreground">Compare up to 4 products side by side</p>
        </div>
        <Button variant="outline" onClick={clearComparison}>
          Clear All
        </Button>
      </div>

      <div className="overflow-x-auto">
        <div className="min-w-full">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {products.map((product) => (
              <Card key={product.id} className="relative">
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-2 z-10 h-8 w-8"
                  onClick={() => removeFromCompare(product.id)}
                >
                  <X className="h-4 w-4" />
                </Button>

                <CardContent className="p-6">
                  {/* Product Image */}
                  <div className="aspect-square mb-4 overflow-hidden rounded-lg bg-gray-100">
                    <Image
                      src={product.images[0] || "https://images.unsplash.com/photo-1519238359922-989348752efb?q=80&w=400&h=400&auto=format&fit=crop"}
                      alt={product.name}
                      width={400}
                      height={400}
                      className="h-full w-full object-cover"
                    />
                  </div>

                  {/* Product Info */}
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium text-lg">{product.name}</h3>
                      <div className="flex items-center space-x-2 mt-1">
                        {product.compareAtPrice ? (
                          <>
                            <span className="text-lg font-semibold">${product.price.toFixed(2)}</span>
                            <span className="text-sm text-muted-foreground line-through">
                              ${product.compareAtPrice.toFixed(2)}
                            </span>
                            <Badge variant="destructive">Sale</Badge>
                          </>
                        ) : (
                          <span className="text-lg font-semibold">${product.price.toFixed(2)}</span>
                        )}
                      </div>
                    </div>

                    <Separator />

                    {/* Features */}
                    <div className="space-y-3 text-sm">
                      <div>
                        <span className="font-medium">Available Sizes:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {product.sizes.map((size) => (
                            <Badge key={size} variant="outline" className="text-xs">
                              {size}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div>
                        <span className="font-medium">Available Colors:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {product.colors.map((color) => (
                            <div
                              key={color.name}
                              className="w-4 h-4 rounded-full border border-gray-300"
                              style={{ backgroundColor: color.value }}
                              title={color.name}
                            />
                          ))}
                        </div>
                      </div>

                      <div>
                        <span className="font-medium">Features:</span>
                        <ul className="mt-1 space-y-1 text-muted-foreground">
                          {product.isNew && <li>• New Arrival</li>}
                          {product.isSale && <li>• On Sale</li>}
                          <li>• Premium Quality</li>
                          <li>• Machine Washable</li>
                        </ul>
                      </div>
                    </div>

                    <Separator />

                    {/* Actions */}
                    <div className="space-y-2">
                      <Button 
                        className="w-full" 
                        onClick={() => handleAddToCart(product)}
                      >
                        <ShoppingBag className="h-4 w-4 mr-2" />
                        Add to Cart
                      </Button>
                      <Button variant="outline" className="w-full" asChild>
                        <Link href={`/products/${product.slug}`}>
                          View Details
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* Add more products */}
      {products.length < 4 && (
        <div className="mt-8 text-center">
          <p className="text-muted-foreground mb-4">
            You can compare up to {4 - products.length} more product{4 - products.length !== 1 ? 's' : ''}.
          </p>
          <Button variant="outline" asChild>
            <Link href="/products">Add More Products</Link>
          </Button>
        </div>
      )}
    </div>
  )
}
