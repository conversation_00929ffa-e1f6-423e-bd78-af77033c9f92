import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Truck, RotateCcw, Shield, Clock } from "lucide-react"

export default function ShippingPage() {
  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-3xl md:text-4xl font-bold font-montserrat mb-4">Shipping & Returns</h1>
        <p className="text-lg text-muted-foreground font-light max-w-2xl mx-auto">
          Everything you need to know about shipping, returns, and exchanges for your Coco Milk Kids orders.
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-8 mb-12">
        {/* Shipping Information */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Truck className="h-5 w-5 text-[#012169]" />
              <CardTitle className="font-montserrat">Shipping Information</CardTitle>
            </div>
            <CardDescription>
              Fast and reliable shipping options for your convenience
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="font-medium mb-2">Shipping Options</h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span>Standard Shipping (3-5 business days)</span>
                  <span className="font-medium">R99</span>
                </div>
                <div className="flex justify-between">
                  <span>Express Shipping (1-2 business days)</span>
                  <span className="font-medium">R199</span>
                </div>
                <div className="flex justify-between">
                  <span>Free Standard Shipping</span>
                  <span className="font-medium">Orders over R1,500</span>
                </div>
                <div className="flex justify-between">
                  <span>Collection (Sandton)</span>
                  <span className="font-medium">Free</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-medium mb-2">Processing Time</h3>
              <p className="text-sm text-muted-foreground">
                Orders are typically processed within 1-2 business days. You'll receive a tracking number
                once your order ships.
              </p>
            </div>

            <div>
              <h3 className="font-medium mb-2">Shipping Locations</h3>
              <p className="text-sm text-muted-foreground">
                We ship nationwide across South Africa. Free collection available from our Sandton store.
                International shipping to SADC countries available on request.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Returns Information */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <RotateCcw className="h-5 w-5 text-[#012169]" />
              <CardTitle className="font-montserrat">Returns & Exchanges</CardTitle>
            </div>
            <CardDescription>
              Easy returns and exchanges within 30 days
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="font-medium mb-2">Return Policy</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 30-day return window from delivery date</li>
                <li>• Items must be unworn with original tags</li>
                <li>• Original packaging required</li>
                <li>• Free returns on defective items</li>
              </ul>
            </div>

            <div>
              <h3 className="font-medium mb-2">How to Return</h3>
              <ol className="text-sm text-muted-foreground space-y-1">
                <li>1. Contact our customer service team</li>
                <li>2. Receive your return authorization</li>
                <li>3. Package items securely</li>
                <li>4. Ship using provided return label</li>
              </ol>
            </div>

            <div>
              <h3 className="font-medium mb-2">Refund Timeline</h3>
              <p className="text-sm text-muted-foreground">
                Refunds are processed within 5-7 business days after we receive your return.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Information */}
      <div className="grid md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="pt-6 text-center">
            <Shield className="h-12 w-12 text-[#012169] mx-auto mb-4" />
            <h3 className="font-medium mb-2">Secure Packaging</h3>
            <p className="text-sm text-muted-foreground">
              All orders are carefully packaged to ensure your items arrive in perfect condition.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6 text-center">
            <Clock className="h-12 w-12 text-[#012169] mx-auto mb-4" />
            <h3 className="font-medium mb-2">Order Tracking</h3>
            <p className="text-sm text-muted-foreground">
              Track your order every step of the way with real-time updates and notifications.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6 text-center">
            <Truck className="h-12 w-12 text-[#012169] mx-auto mb-4" />
            <h3 className="font-medium mb-2">Fast Delivery</h3>
            <p className="text-sm text-muted-foreground">
              Most orders arrive within 5-7 business days with our standard shipping option.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Contact Section */}
      <div className="mt-12 text-center bg-gray-50 rounded-lg p-8">
        <h2 className="text-xl font-bold font-montserrat mb-4">Need Help?</h2>
        <p className="text-muted-foreground mb-4">
          Have questions about shipping or returns? Our customer service team is here to help.
        </p>
        <p className="text-sm">
          Email us at <a href="mailto:<EMAIL>" className="text-[#012169] hover:underline"><EMAIL></a> or
          call <a href="tel:+***********" className="text-[#012169] hover:underline">+27 11 123 4567</a>
        </p>
      </div>
    </div>
  )
}
