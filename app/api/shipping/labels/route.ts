// Shipping Labels API Route
// POST /api/shipping/labels - Generate shipping labels

import { NextRequest, NextResponse } from 'next/server'
import { ShippingService } from '@/lib/ecommerce/services/shipping-service'

const shippingService = new ShippingService()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      orderId,
      fulfillmentId,
      carrier,
      service,
      fromAddress,
      toAddress,
      packageDetails,
      declaredValue,
      currency
    } = body

    // Validate required fields
    if (!orderId || !carrier || !service || !fromAddress || !toAddress || !packageDetails) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate addresses
    const fromValidation = await shippingService.validateAddress(fromAddress)
    if (!fromValidation.valid) {
      return NextResponse.json(
        { success: false, error: `Invalid from address: ${fromValidation.error}` },
        { status: 400 }
      )
    }

    const toValidation = await shippingService.validateAddress(toAddress)
    if (!toValidation.valid) {
      return NextResponse.json(
        { success: false, error: `Invalid to address: ${toValidation.error}` },
        { status: 400 }
      )
    }

    // Set declared value in package details
    const packageWithValue = {
      ...packageDetails,
      declaredValue: declaredValue || 0
    }

    // Generate shipping label
    const result = await shippingService.generateLabel(
      orderId,
      fulfillmentId || orderId,
      carrier,
      service,
      fromAddress,
      toAddress,
      packageWithValue
    )

    if (result.success && result.data) {
      return NextResponse.json({
        success: true,
        labelUrl: result.data.labelUrl,
        trackingNumber: result.data.trackingNumber,
        cost: result.data.cost,
        estimatedDelivery: result.data.estimatedDelivery,
        carrier: result.data.carrier,
        service: result.data.service,
        labelFormat: result.data.labelFormat
      })
    } else {
      return NextResponse.json(
        { success: false, error: result.error || 'Failed to generate shipping label' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Generate shipping label error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to generate shipping label' },
      { status: 500 }
    )
  }
}
