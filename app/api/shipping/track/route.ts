// Shipping Tracking API Route
// POST /api/shipping/track - Track shipments

import { NextRequest, NextResponse } from 'next/server'
import { ShippingService } from '@/lib/ecommerce/services/shipping-service'

const shippingService = new ShippingService()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { carrier, trackingNumber } = body

    if (!carrier || !trackingNumber) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: carrier, trackingNumber' },
        { status: 400 }
      )
    }

    // Track shipment
    const result = await shippingService.trackShipment(carrier, trackingNumber)

    if (result.success && result.data) {
      return NextResponse.json({
        success: true,
        trackingNumber: result.data.trackingNumber,
        status: result.data.status,
        events: result.data.events,
        estimatedDelivery: result.data.estimatedDelivery,
        actualDelivery: result.data.actualDelivery
      })
    } else {
      return NextResponse.json(
        { success: false, error: result.error || 'Failed to track shipment' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Track shipment error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to track shipment' },
      { status: 500 }
    )
  }
}
