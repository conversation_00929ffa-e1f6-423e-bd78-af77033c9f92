import { NextRequest, NextResponse } from 'next/server'
import { PageGenerator, PageGenerationOptions } from '@/lib/page-builder/services/page-generator'
import { PageCache, invalidatePageCache } from '@/lib/page-builder/services/page-cache'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const {
      title,
      slug,
      description,
      type = 'custom',
      template,
      status = 'draft',
      seoTitle,
      seoDescription,
      seoKeywords,
      ogImage,
      requiresAuth = false,
      allowComments = false,
      customCss,
      customJs,
      blocks,
      publishedAt,
      scheduledAt,
      expiresAt,
      isHomePage = false,
      isLandingPage = false,
      metadata = {},
      createdBy,
      updatedBy
    } = body

    // Validate required fields
    if (!title || !slug) {
      return NextResponse.json(
        { success: false, error: 'Title and slug are required' },
        { status: 400 }
      )
    }

    // Validate slug format
    const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/
    if (!slugRegex.test(slug)) {
      return NextResponse.json(
        { success: false, error: 'Slug must contain only lowercase letters, numbers, and hyphens' },
        { status: 400 }
      )
    }

    const options: PageGenerationOptions = {
      title,
      slug,
      description,
      type,
      template,
      status,
      seoTitle,
      seoDescription,
      seoKeywords,
      ogImage,
      requiresAuth,
      allowComments,
      customCss,
      customJs,
      blocks,
      publishedAt: publishedAt ? new Date(publishedAt) : undefined,
      scheduledAt: scheduledAt ? new Date(scheduledAt) : undefined,
      expiresAt: expiresAt ? new Date(expiresAt) : undefined,
      isHomePage,
      isLandingPage,
      metadata,
      createdBy,
      updatedBy
    }

    // Generate the page
    const pageData = await PageGenerator.generatePage(options)

    // Cache the new page if it's published
    if (status === 'published') {
      PageCache.set(PageCache.getPageKey(slug), pageData)
    }

    return NextResponse.json({
      success: true,
      data: pageData,
      message: 'Page generated successfully'
    })

  } catch (error: any) {
    console.error('Page generation error:', error)
    
    if (error.message.includes('already exists')) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 409 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to generate page' },
      { status: 500 }
    )
  }
}

// Duplicate an existing page
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { pageId, newSlug, newTitle } = body

    if (!pageId || !newSlug) {
      return NextResponse.json(
        { success: false, error: 'Page ID and new slug are required' },
        { status: 400 }
      )
    }

    // Validate slug format
    const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/
    if (!slugRegex.test(newSlug)) {
      return NextResponse.json(
        { success: false, error: 'Slug must contain only lowercase letters, numbers, and hyphens' },
        { status: 400 }
      )
    }

    const duplicatedPage = await PageGenerator.duplicatePage(pageId, newSlug, newTitle)

    return NextResponse.json({
      success: true,
      data: duplicatedPage,
      message: 'Page duplicated successfully'
    })

  } catch (error: any) {
    console.error('Page duplication error:', error)
    
    if (error.message.includes('already exists')) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 409 }
      )
    }

    if (error.message.includes('not found')) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to duplicate page' },
      { status: 500 }
    )
  }
}

// Get page templates and generation options
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'templates':
        // Return available page templates
        const templates = [
          {
            id: 'home',
            name: 'Home Page',
            description: 'Complete homepage with hero, featured products, and newsletter',
            type: 'home',
            preview: '/images/templates/home-preview.jpg',
            blocks: ['hero', 'featured-products', 'newsletter']
          },
          {
            id: 'landing',
            name: 'Landing Page',
            description: 'Conversion-focused landing page with hero and testimonials',
            type: 'landing',
            preview: '/images/templates/landing-preview.jpg',
            blocks: ['hero', 'testimonials', 'cta']
          },
          {
            id: 'product',
            name: 'Product Page',
            description: 'Product detail page with gallery and reviews',
            type: 'product',
            preview: '/images/templates/product-preview.jpg',
            blocks: ['product-details', 'product-gallery', 'reviews']
          },
          {
            id: 'about',
            name: 'About Page',
            description: 'Company information and team showcase',
            type: 'custom',
            preview: '/images/templates/about-preview.jpg',
            blocks: ['heading', 'text', 'team', 'values']
          },
          {
            id: 'contact',
            name: 'Contact Page',
            description: 'Contact form and company information',
            type: 'custom',
            preview: '/images/templates/contact-preview.jpg',
            blocks: ['heading', 'contact-form', 'map', 'contact-info']
          },
          {
            id: 'blank',
            name: 'Blank Page',
            description: 'Start with a clean slate',
            type: 'custom',
            preview: '/images/templates/blank-preview.jpg',
            blocks: ['heading', 'text']
          }
        ]

        return NextResponse.json({
          success: true,
          data: templates
        })

      case 'cache-stats':
        // Return cache statistics
        const stats = PageCache.getStats()
        return NextResponse.json({
          success: true,
          data: stats
        })

      case 'popular-pages':
        // Return popular pages for cache warming
        const popularPages = await prisma.page.findMany({
          where: {
            status: 'published',
            viewCount: { gt: 0 }
          },
          orderBy: { viewCount: 'desc' },
          take: 20,
          select: {
            slug: true,
            title: true,
            viewCount: true
          }
        })

        return NextResponse.json({
          success: true,
          data: popularPages
        })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action parameter'
        }, { status: 400 })
    }

  } catch (error) {
    console.error('Page generation GET error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to process request' },
      { status: 500 }
    )
  }
}

// Clear page cache
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const pageId = searchParams.get('pageId')
    const slug = searchParams.get('slug')

    switch (action) {
      case 'clear-cache':
        PageCache.clear()
        return NextResponse.json({
          success: true,
          message: 'Cache cleared successfully'
        })

      case 'invalidate-page':
        if (pageId) {
          invalidatePageCache(pageId, slug || undefined)
          return NextResponse.json({
            success: true,
            message: 'Page cache invalidated'
          })
        }
        return NextResponse.json(
          { success: false, error: 'Page ID required' },
          { status: 400 }
        )

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action parameter'
        }, { status: 400 })
    }

  } catch (error) {
    console.error('Page generation DELETE error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to process request' },
      { status: 500 }
    )
  }
}
