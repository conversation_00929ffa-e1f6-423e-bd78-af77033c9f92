import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const slug = searchParams.get('slug')

    if (!slug) {
      return NextResponse.json({
        exists: false,
        error: 'Slug parameter is required'
      }, { status: 400 })
    }

    // Check if a published page exists with this slug
    const page = await prisma.page.findUnique({
      where: {
        slug,
        status: 'published'
      },
      select: {
        id: true,
        slug: true,
        title: true,
        type: true
      }
    })

    return NextResponse.json({
      exists: !!page,
      page: page || null
    })

  } catch (error) {
    console.error('Check slug error:', error)
    return NextResponse.json({
      exists: false,
      error: 'Failed to check slug'
    }, { status: 500 })
  }
}
