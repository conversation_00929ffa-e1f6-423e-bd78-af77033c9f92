import { NextRequest, NextResponse } from 'next/server'
import { storageService, isAppwriteConfigured } from '@/lib/appwrite'
import { STORAGE_BUCKETS } from '@/lib/appwrite/config'

interface RouteParams {
  params: {
    fileId: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    if (!isAppwriteConfigured()) {
      return NextResponse.json(
        { error: 'Appwrite is not configured' },
        { status: 503 }
      )
    }

    const { fileId } = params
    const bucketId = STORAGE_BUCKETS.MEDIA_LIBRARY.id

    // Get file details
    const file = await storageService.getFile(bucketId, fileId)

    const transformedFile = {
      id: file.$id,
      name: file.name,
      mimeType: file.mimeType,
      size: file.sizeOriginal,
      createdAt: file.$createdAt,
      updatedAt: file.$updatedAt,
      url: storageService.getFileUrl(bucketId, file.$id),
      previewUrl: file.mimeType.startsWith('image/') 
        ? storageService.getFilePreview(bucketId, file.$id, 800, 600)
        : null,
      downloadUrl: storageService.getFileDownloadUrl(bucketId, file.$id),
      type: getFileType(file.mimeType),
      metadata: {
        folder: 'root',
        alt: '',
        title: file.name,
        description: '',
        tags: []
      }
    }

    return NextResponse.json({
      success: true,
      data: transformedFile
    })

  } catch (error) {
    console.error('Error fetching file:', error)
    return NextResponse.json(
      { error: 'File not found' },
      { status: 404 }
    )
  }
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    if (!isAppwriteConfigured()) {
      return NextResponse.json(
        { error: 'Appwrite is not configured' },
        { status: 503 }
      )
    }

    const { fileId } = params
    const body = await request.json()
    const { name, metadata } = body

    const bucketId = STORAGE_BUCKETS.MEDIA_LIBRARY.id

    // Get current file
    const file = await storageService.getFile(bucketId, fileId)

    // For now, we'll return the updated metadata since Appwrite doesn't support 
    // updating file metadata directly. In a production system, you'd store 
    // metadata in a database collection.
    const updatedFile = {
      id: file.$id,
      name: name || file.name,
      mimeType: file.mimeType,
      size: file.sizeOriginal,
      createdAt: file.$createdAt,
      updatedAt: new Date().toISOString(),
      url: storageService.getFileUrl(bucketId, file.$id),
      previewUrl: file.mimeType.startsWith('image/') 
        ? storageService.getFilePreview(bucketId, file.$id, 800, 600)
        : null,
      downloadUrl: storageService.getFileDownloadUrl(bucketId, file.$id),
      type: getFileType(file.mimeType),
      metadata: {
        folder: metadata?.folder || 'root',
        alt: metadata?.alt || '',
        title: metadata?.title || file.name,
        description: metadata?.description || '',
        tags: metadata?.tags || []
      }
    }

    return NextResponse.json({
      success: true,
      data: updatedFile,
      message: 'File metadata updated successfully'
    })

  } catch (error) {
    console.error('Error updating file:', error)
    return NextResponse.json(
      { error: 'Failed to update file' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    if (!isAppwriteConfigured()) {
      return NextResponse.json(
        { error: 'Appwrite is not configured' },
        { status: 503 }
      )
    }

    const { fileId } = params
    const bucketId = STORAGE_BUCKETS.MEDIA_LIBRARY.id

    // Delete file from storage
    await storageService.deleteFile(bucketId, fileId)

    return NextResponse.json({
      success: true,
      message: 'File deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting file:', error)
    return NextResponse.json(
      { error: 'Failed to delete file' },
      { status: 500 }
    )
  }
}

function getFileType(mimeType: string): string {
  if (mimeType.startsWith('image/')) return 'image'
  if (mimeType.startsWith('video/')) return 'video'
  if (mimeType.startsWith('audio/')) return 'audio'
  if (mimeType.startsWith('application/') || mimeType.startsWith('text/')) return 'document'
  return 'other'
}
