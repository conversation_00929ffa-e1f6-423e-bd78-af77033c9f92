import { NextRequest, NextResponse } from 'next/server'
import { storageService, isAppwriteConfigured } from '@/lib/appwrite'
import { STORAGE_BUCKETS } from '@/lib/appwrite/config'
import { Query } from 'appwrite'

export async function GET(request: NextRequest) {
  try {
    if (!isAppwriteConfigured()) {
      return NextResponse.json(
        { error: 'Appwrite is not configured' },
        { status: 503 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const type = searchParams.get('type') || 'all'
    const folder = searchParams.get('folder') || ''
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // Get files from media library bucket
    const bucketId = STORAGE_BUCKETS.MEDIA_LIBRARY.id
    const offset = (page - 1) * limit

    // Build queries
    const queries = [
      Query.limit(limit),
      Query.offset(offset),
      Query.orderDesc('$createdAt') // Default sort by creation date
    ]

    // Add search filter if provided
    if (search) {
      queries.push(Query.search('name', search))
    }

    const files = await storageService.listFiles(bucketId, queries)

    // Filter by type if specified
    let filteredFiles = files.files
    if (type !== 'all') {
      filteredFiles = files.files.filter(file => {
        const mimeType = file.mimeType
        switch (type) {
          case 'image':
            return mimeType.startsWith('image/')
          case 'video':
            return mimeType.startsWith('video/')
          case 'audio':
            return mimeType.startsWith('audio/')
          case 'document':
            return mimeType.startsWith('application/') || mimeType.startsWith('text/')
          default:
            return true
        }
      })
    }

    // Transform files to include URLs and metadata
    const transformedFiles = filteredFiles.map(file => ({
      id: file.$id,
      name: file.name,
      mimeType: file.mimeType,
      size: file.sizeOriginal,
      createdAt: file.$createdAt,
      updatedAt: file.$updatedAt,
      url: storageService.getFileUrl(bucketId, file.$id),
      previewUrl: file.mimeType.startsWith('image/') 
        ? storageService.getFilePreview(bucketId, file.$id, 300, 300)
        : null,
      downloadUrl: storageService.getFileDownloadUrl(bucketId, file.$id),
      type: getFileType(file.mimeType),
      metadata: {
        folder: folder || 'root',
        alt: '',
        title: file.name,
        description: '',
        tags: []
      }
    }))

    return NextResponse.json({
      success: true,
      data: {
        files: transformedFiles,
        pagination: {
          page,
          limit,
          total: files.total,
          totalPages: Math.ceil(files.total / limit)
        }
      }
    })

  } catch (error) {
    console.error('Error fetching media files:', error)
    return NextResponse.json(
      { error: 'Failed to fetch media files' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    if (!isAppwriteConfigured()) {
      return NextResponse.json(
        { error: 'Appwrite is not configured' },
        { status: 503 }
      )
    }

    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const folder = formData.get('folder') as string || 'root'
    const alt = formData.get('alt') as string || ''
    const title = formData.get('title') as string || ''
    const description = formData.get('description') as string || ''
    const tags = formData.get('tags') as string || ''

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      )
    }

    const bucketId = STORAGE_BUCKETS.MEDIA_LIBRARY.id
    const uploadResults = []

    for (const file of files) {
      try {
        // Validate file
        if (!STORAGE_BUCKETS.MEDIA_LIBRARY.allowedFileTypes.includes(file.type)) {
          throw new Error(`File type ${file.type} is not allowed`)
        }

        if (file.size > STORAGE_BUCKETS.MEDIA_LIBRARY.maxFileSize) {
          throw new Error(`File size exceeds maximum allowed size`)
        }

        // Upload file
        const result = await storageService.uploadFile(file, {
          bucketId
        })

        // Transform result
        const transformedResult = {
          id: result.fileId,
          name: file.name,
          mimeType: file.type,
          size: file.size,
          url: result.url,
          previewUrl: file.type.startsWith('image/') 
            ? storageService.getFilePreview(bucketId, result.fileId, 300, 300)
            : null,
          downloadUrl: result.downloadUrl,
          type: getFileType(file.type),
          metadata: {
            folder,
            alt: alt || file.name,
            title: title || file.name,
            description,
            tags: tags ? tags.split(',').map(tag => tag.trim()) : []
          }
        }

        uploadResults.push(transformedResult)

      } catch (fileError) {
        console.error(`Error uploading file ${file.name}:`, fileError)
        uploadResults.push({
          name: file.name,
          error: fileError instanceof Error ? fileError.message : 'Upload failed'
        })
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        files: uploadResults,
        uploaded: uploadResults.filter(result => !result.error).length,
        failed: uploadResults.filter(result => result.error).length
      }
    })

  } catch (error) {
    console.error('Error uploading files:', error)
    return NextResponse.json(
      { error: 'Failed to upload files' },
      { status: 500 }
    )
  }
}

function getFileType(mimeType: string): string {
  if (mimeType.startsWith('image/')) return 'image'
  if (mimeType.startsWith('video/')) return 'video'
  if (mimeType.startsWith('audio/')) return 'audio'
  if (mimeType.startsWith('application/') || mimeType.startsWith('text/')) return 'document'
  return 'other'
}
