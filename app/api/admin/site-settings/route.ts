import { NextRequest, NextResponse } from 'next/server'
import { SiteSettingsService } from '@/lib/site-settings/site-settings-service'
import { z } from 'zod'

const updateSiteSettingsSchema = z.object({
  homepageId: z.string().nullable().optional(),
  siteName: z.string().optional(),
  siteDescription: z.string().optional(),
  siteUrl: z.string().url().optional(),
  logoUrl: z.string().nullable().optional(),
  faviconUrl: z.string().nullable().optional(),
  socialMedia: z.object({
    facebook: z.string().optional(),
    instagram: z.string().optional(),
    twitter: z.string().optional(),
    youtube: z.string().optional()
  }).optional(),
  seo: z.object({
    defaultTitle: z.string().optional(),
    defaultDescription: z.string().optional(),
    defaultKeywords: z.array(z.string()).optional(),
    ogImage: z.string().optional()
  }).optional(),
  ecommerce: z.object({
    currency: z.string().optional(),
    taxRate: z.number().min(0).max(1).optional(),
    freeShippingThreshold: z.number().min(0).optional(),
    defaultShippingCost: z.number().min(0).optional()
  }).optional(),
  maintenance: z.object({
    enabled: z.boolean().optional(),
    message: z.string().optional(),
    allowedIps: z.array(z.string()).optional()
  }).optional(),
  analytics: z.object({
    googleAnalyticsId: z.string().optional(),
    facebookPixelId: z.string().optional(),
    hotjarId: z.string().optional()
  }).optional()
})

const siteSettingsService = new SiteSettingsService()

export async function GET(request: NextRequest) {
  try {
    // TODO: Add admin authentication check
    // const isAdmin = await checkAdminAuth(request)
    // if (!isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    // }

    const settings = await siteSettingsService.getSiteSettings()

    return NextResponse.json({
      success: true,
      data: settings
    })

  } catch (error) {
    console.error('Get site settings error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch site settings'
    }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // TODO: Add admin authentication check
    // const isAdmin = await checkAdminAuth(request)
    // if (!isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    // }

    const body = await request.json()
    const validatedData = updateSiteSettingsSchema.parse(body)

    const updatedSettings = await siteSettingsService.updateSiteSettings(validatedData)

    return NextResponse.json({
      success: true,
      message: 'Site settings updated successfully',
      data: updatedSettings
    })

  } catch (error) {
    console.error('Update site settings error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid request data',
        details: error.errors
      }, { status: 400 })
    }

    if (error instanceof Error) {
      return NextResponse.json({
        success: false,
        error: error.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to update site settings'
    }, { status: 500 })
  }
}
