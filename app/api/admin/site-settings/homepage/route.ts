import { NextRequest, NextResponse } from 'next/server'
import { SiteSettingsService } from '@/lib/site-settings/site-settings-service'
import { z } from 'zod'

const setHomepageSchema = z.object({
  pageId: z.string()
})

const siteSettingsService = new SiteSettingsService()

export async function GET(request: NextRequest) {
  try {
    // TODO: Add admin authentication check
    // const isAdmin = await checkAdminAuth(request)
    // if (!isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    // }

    const homepage = await siteSettingsService.getHomepage()
    const availablePages = await siteSettingsService.getAvailablePages()

    return NextResponse.json({
      success: true,
      data: {
        currentHomepage: homepage,
        availablePages
      }
    })

  } catch (error) {
    console.error('Get homepage settings error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch homepage settings'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // TODO: Add admin authentication check
    // const isAdmin = await checkAdminAuth(request)
    // if (!isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    // }

    const body = await request.json()
    const { pageId } = setHomepageSchema.parse(body)

    const updatedSettings = await siteSettingsService.setHomepage(pageId)

    return NextResponse.json({
      success: true,
      message: 'Homepage set successfully',
      data: updatedSettings
    })

  } catch (error) {
    console.error('Set homepage error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid request data',
        details: error.errors
      }, { status: 400 })
    }

    if (error instanceof Error) {
      return NextResponse.json({
        success: false,
        error: error.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to set homepage'
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // TODO: Add admin authentication check
    // const isAdmin = await checkAdminAuth(request)
    // if (!isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    // }

    const updatedSettings = await siteSettingsService.clearHomepage()

    return NextResponse.json({
      success: true,
      message: 'Homepage cleared successfully',
      data: updatedSettings
    })

  } catch (error) {
    console.error('Clear homepage error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to clear homepage'
    }, { status: 500 })
  }
}
