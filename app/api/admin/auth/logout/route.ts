import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import jwt from 'jsonwebtoken'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'No token provided' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)

    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as any

      // Find and deactivate session
      const session = await prisma.adminSession.findFirst({
        where: {
          token,
          userId: decoded.userId,
          isActive: true
        },
        include: {
          user: true
        }
      })

      if (session) {
        // Deactivate session
        await prisma.adminSession.update({
          where: { id: session.id },
          data: { isActive: false }
        })

        // Log admin activity
        await prisma.adminActivity.create({
          data: {
            userId: session.userId,
            userName: session.user.displayName,
            action: 'LOGOUT',
            resource: 'AUTH',
            description: 'Admin user logged out',
            ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown',
            sessionId: session.id
          }
        })
      }

      return NextResponse.json({
        success: true,
        message: 'Logged out successfully'
      })

    } catch (jwtError) {
      // Token is invalid, but we still return success for logout
      return NextResponse.json({
        success: true,
        message: 'Logged out successfully'
      })
    }

  } catch (error) {
    console.error('Admin logout error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
