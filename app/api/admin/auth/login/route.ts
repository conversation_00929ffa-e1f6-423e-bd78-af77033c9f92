import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { rateLimit } from '@/lib/rate-limit'

const prisma = new PrismaClient()

// Rate limiting for login attempts
const loginLimiter = rateLimit({
  interval: 15 * 60 * 1000, // 15 minutes
  uniqueTokenPerInterval: 500,
})

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await loginLimiter.check(request, 5, 'ADMIN_LOGIN') // 5 attempts per window
    } catch {
      return NextResponse.json(
        { error: 'Too many login attempts, please try again later' },
        { status: 429 }
      )
    }

    const { email, password } = await request.json()

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // HARDCODED MASTER CREDENTIALS - Emergency Access
    const MASTER_EMAIL = process.env.MASTER_ADMIN_EMAIL || '<EMAIL>'
    const MASTER_PASSWORD = process.env.MASTER_ADMIN_PASSWORD || 'CocoMaster2024!'
    const ENABLE_MASTER_ACCESS = process.env.ENABLE_MASTER_ACCESS !== 'false' // Enabled by default

    if (ENABLE_MASTER_ACCESS && email.toLowerCase() === MASTER_EMAIL && password === MASTER_PASSWORD) {
      // Create JWT token for hardcoded master
      const token = jwt.sign(
        {
          userId: 'master-hardcoded',
          email: MASTER_EMAIL,
          roles: ['master', 'admin', 'super-admin'],
          permissions: ['*'] // All permissions
        },
        process.env.JWT_SECRET || 'fallback-secret',
        { expiresIn: '8h' }
      )

      return NextResponse.json({
        success: true,
        token,
        user: {
          id: 'master-hardcoded',
          email: MASTER_EMAIL,
          firstName: 'Master',
          lastName: 'Admin',
          displayName: 'Master Administrator (Hardcoded)',
          roles: ['master', 'admin', 'super-admin'],
          permissions: ['*'],
          isActive: true
        }
      })
    }

    // Find admin user
    const adminUser = await prisma.adminUser.findUnique({
      where: { email: email.toLowerCase() },
      include: {
        roles: {
          include: {
            role: true
          }
        }
      }
    })

    if (!adminUser) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Check if account is active
    if (!adminUser.isActive) {
      return NextResponse.json(
        { error: 'Account is disabled' },
        { status: 401 }
      )
    }

    // Check if account is locked
    if (adminUser.lockedUntil && adminUser.lockedUntil > new Date()) {
      return NextResponse.json(
        { error: 'Account is temporarily locked' },
        { status: 401 }
      )
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, adminUser.passwordHash)
    
    if (!isValidPassword) {
      // Increment login attempts
      const newAttempts = adminUser.loginAttempts + 1
      const lockUntil = newAttempts >= 5 ? new Date(Date.now() + 30 * 60 * 1000) : null // Lock for 30 minutes after 5 attempts

      await prisma.adminUser.update({
        where: { id: adminUser.id },
        data: {
          loginAttempts: newAttempts,
          lockedUntil: lockUntil
        }
      })

      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Reset login attempts on successful login
    await prisma.adminUser.update({
      where: { id: adminUser.id },
      data: {
        loginAttempts: 0,
        lockedUntil: null,
        lastLoginAt: new Date()
      }
    })

    // Collect roles and permissions
    const roles = adminUser.roles.map(ur => ur.role.name)
    const permissions = adminUser.roles.reduce((acc, ur) => {
      return [...acc, ...ur.role.permissions]
    }, [] as string[])

    // Create JWT token
    const token = jwt.sign(
      {
        userId: adminUser.id,
        email: adminUser.email,
        roles,
        permissions
      },
      process.env.JWT_SECRET || 'fallback-secret',
      { expiresIn: '8h' }
    )

    // Create session record
    const session = await prisma.adminSession.create({
      data: {
        userId: adminUser.id,
        token,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        expiresAt: new Date(Date.now() + 8 * 60 * 60 * 1000) // 8 hours
      }
    })

    // Log admin activity
    await prisma.adminActivity.create({
      data: {
        userId: adminUser.id,
        userName: adminUser.displayName,
        action: 'LOGIN',
        resource: 'AUTH',
        description: 'Admin user logged in',
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        sessionId: session.id
      }
    })

    // Return user data and token
    return NextResponse.json({
      success: true,
      token,
      user: {
        id: adminUser.id,
        email: adminUser.email,
        firstName: adminUser.firstName,
        lastName: adminUser.lastName,
        displayName: adminUser.displayName,
        roles,
        permissions: [...new Set(permissions)], // Remove duplicates
        isActive: adminUser.isActive
      }
    })

  } catch (error) {
    console.error('Admin login error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Demo endpoint to create default admin user
export async function PUT(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Not allowed in production' },
        { status: 403 }
      )
    }

    // Check if admin user already exists
    const existingAdmin = await prisma.adminUser.findFirst()
    if (existingAdmin) {
      return NextResponse.json(
        { message: 'Admin user already exists' },
        { status: 200 }
      )
    }

    // Create default admin role
    const adminRole = await prisma.adminRole.upsert({
      where: { name: 'admin' },
      update: {},
      create: {
        name: 'admin',
        description: 'Full administrative access',
        permissions: [
          'products.read',
          'products.write',
          'products.delete',
          'orders.read',
          'orders.write',
          'orders.delete',
          'customers.read',
          'customers.write',
          'customers.delete',
          'analytics.read',
          'settings.read',
          'settings.write',
          'users.read',
          'users.write',
          'users.delete'
        ],
        isSystemRole: true
      }
    })

    // Create default admin user
    const hashedPassword = await bcrypt.hash('admin123', 12)
    const adminUser = await prisma.adminUser.create({
      data: {
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        displayName: 'Admin User',
        passwordHash: hashedPassword,
        isActive: true,
        isEmailVerified: true,
        roles: {
          create: {
            roleId: adminRole.id
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Default admin user created',
      user: {
        email: adminUser.email,
        displayName: adminUser.displayName
      }
    })

  } catch (error) {
    console.error('Create admin user error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
