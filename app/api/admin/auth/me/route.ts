import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import jwt from 'jsonwebtoken'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'No token provided' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)

    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as any

      // Handle hardcoded master user
      if (decoded.userId === 'master-hardcoded') {
        return NextResponse.json({
          success: true,
          user: {
            id: 'master-hardcoded',
            email: '<EMAIL>',
            firstName: 'Master',
            lastName: 'Admin',
            displayName: 'Master Administrator (Hardcoded)',
            roles: ['master', 'admin', 'super-admin'],
            permissions: ['*'],
            isActive: true
          }
        })
      }

      // Find active session
      const session = await prisma.adminSession.findFirst({
        where: {
          token,
          userId: decoded.userId,
          isActive: true,
          expiresAt: {
            gt: new Date()
          }
        },
        include: {
          user: {
            include: {
              roles: {
                include: {
                  role: true
                }
              }
            }
          }
        }
      })

      if (!session || !session.user.isActive) {
        return NextResponse.json(
          { error: 'Invalid or expired session' },
          { status: 401 }
        )
      }

      // Update last activity
      await prisma.adminSession.update({
        where: { id: session.id },
        data: { lastActivityAt: new Date() }
      })

      // Collect roles and permissions
      const roles = session.user.roles.map(ur => ur.role.name)
      const permissions = session.user.roles.reduce((acc, ur) => {
        return [...acc, ...ur.role.permissions]
      }, [] as string[])

      return NextResponse.json({
        success: true,
        user: {
          id: session.user.id,
          email: session.user.email,
          firstName: session.user.firstName,
          lastName: session.user.lastName,
          displayName: session.user.displayName,
          roles,
          permissions: [...new Set(permissions)], // Remove duplicates
          isActive: session.user.isActive
        }
      })

    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }

  } catch (error) {
    console.error('Admin auth check error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
