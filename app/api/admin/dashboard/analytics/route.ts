import { NextRequest, NextResponse } from 'next/server'
import { DashboardAnalyticsService } from '@/lib/ecommerce/services/dashboard-analytics-service'

const dashboardService = new DashboardAnalyticsService()

export async function GET(request: NextRequest) {
  try {
    // TODO: Add admin authentication check
    // const isAdmin = await checkAdminAuth(request)
    // if (!isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    // }

    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '30')
    const timeRange = searchParams.get('timeRange') || '30d'

    // Validate days parameter
    if (days < 1 || days > 365) {
      return NextResponse.json(
        { success: false, error: 'Days must be between 1 and 365' },
        { status: 400 }
      )
    }

    const metrics = await dashboardService.getDashboardMetrics(days)

    return NextResponse.json({
      success: true,
      data: metrics,
      meta: {
        timeRange,
        days,
        generatedAt: new Date().toISOString()
      }
    })

  } catch (error: any) {
    console.error('Dashboard analytics error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to fetch dashboard analytics',
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    )
  }
}

// POST endpoint for refreshing analytics cache (if needed)
export async function POST(request: NextRequest) {
  try {
    // TODO: Add admin authentication check
    // const isAdmin = await checkAdminAuth(request)
    // if (!isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    // }

    const body = await request.json()
    const { days = 30, forceRefresh = false } = body

    // Validate days parameter
    if (days < 1 || days > 365) {
      return NextResponse.json(
        { success: false, error: 'Days must be between 1 and 365' },
        { status: 400 }
      )
    }

    const metrics = await dashboardService.getDashboardMetrics(days)

    return NextResponse.json({
      success: true,
      data: metrics,
      meta: {
        days,
        forceRefresh,
        generatedAt: new Date().toISOString()
      }
    })

  } catch (error: any) {
    console.error('Dashboard analytics refresh error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to refresh dashboard analytics',
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    )
  }
}
