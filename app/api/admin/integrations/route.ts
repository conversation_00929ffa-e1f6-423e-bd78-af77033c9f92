import { NextRequest, NextResponse } from 'next/server'

interface Integration {
  id: string
  name: string
  description: string
  category: 'analytics' | 'marketing' | 'shipping' | 'payment' | 'communication' | 'other'
  enabled: boolean
  configured: boolean
  status: 'connected' | 'disconnected' | 'error' | 'testing'
  settings: Record<string, any>
  webhookUrl?: string
  lastSync?: string
}

// Mock integrations storage
let mockIntegrations: Integration[] = [
  {
    id: 'google-analytics',
    name: 'Google Analytics',
    description: 'Track website traffic and user behavior',
    category: 'analytics',
    enabled: true,
    configured: true,
    status: 'connected',
    settings: {
      trackingId: 'GA-XXXXXXXXX',
      enhancedEcommerce: true
    },
    lastSync: '2024-01-15T10:30:00Z'
  },
  {
    id: 'facebook-pixel',
    name: 'Facebook Pixel',
    description: 'Track conversions and optimize Facebook ads',
    category: 'marketing',
    enabled: false,
    configured: false,
    status: 'disconnected',
    settings: {
      pixelId: '',
      accessToken: ''
    }
  },
  {
    id: 'mailchimp',
    name: 'Mailchimp',
    description: 'Email marketing and automation',
    category: 'marketing',
    enabled: true,
    configured: true,
    status: 'connected',
    settings: {
      apiKey: 'xxxxxxxx-us1',
      listId: 'abc123'
    },
    lastSync: '2024-01-15T09:15:00Z'
  },
  {
    id: 'whatsapp-business',
    name: 'WhatsApp Business',
    description: 'Send order updates via WhatsApp',
    category: 'communication',
    enabled: false,
    configured: false,
    status: 'disconnected',
    settings: {
      phoneNumberId: '',
      accessToken: ''
    }
  },
  {
    id: 'aramex',
    name: 'Aramex',
    description: 'Shipping and logistics integration',
    category: 'shipping',
    enabled: true,
    configured: true,
    status: 'connected',
    settings: {
      username: 'testuser',
      password: 'testpass',
      accountNumber: '123456',
      sandbox: true
    },
    lastSync: '2024-01-15T08:45:00Z'
  },
  {
    id: 'zapier',
    name: 'Zapier',
    description: 'Connect with 5000+ apps and automate workflows',
    category: 'other',
    enabled: false,
    configured: false,
    status: 'disconnected',
    settings: {
      webhookUrl: ''
    }
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const enabled = searchParams.get('enabled')

    let filteredIntegrations = [...mockIntegrations]

    // Apply filters
    if (category && category !== 'all') {
      filteredIntegrations = filteredIntegrations.filter(i => i.category === category)
    }

    if (enabled !== null) {
      const isEnabled = enabled === 'true'
      filteredIntegrations = filteredIntegrations.filter(i => i.enabled === isEnabled)
    }

    return NextResponse.json({
      success: true,
      data: {
        integrations: filteredIntegrations,
        stats: {
          total: mockIntegrations.length,
          enabled: mockIntegrations.filter(i => i.enabled).length,
          connected: mockIntegrations.filter(i => i.status === 'connected').length,
          errors: mockIntegrations.filter(i => i.status === 'error').length
        }
      }
    })

  } catch (error) {
    console.error('Error fetching integrations:', error)
    return NextResponse.json(
      { error: 'Failed to fetch integrations' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { integrationId, settings, enabled } = body

    if (!integrationId) {
      return NextResponse.json(
        { error: 'Integration ID is required' },
        { status: 400 }
      )
    }

    const integrationIndex = mockIntegrations.findIndex(i => i.id === integrationId)
    if (integrationIndex === -1) {
      return NextResponse.json(
        { error: 'Integration not found' },
        { status: 404 }
      )
    }

    // Update integration
    const integration = mockIntegrations[integrationIndex]
    
    if (settings) {
      integration.settings = { ...integration.settings, ...settings }
      integration.configured = Object.values(integration.settings).some(value => 
        value !== '' && value !== null && value !== undefined
      )
    }

    if (enabled !== undefined) {
      integration.enabled = enabled
      integration.status = enabled ? 'connected' : 'disconnected'
    }

    integration.lastSync = new Date().toISOString()

    return NextResponse.json({
      success: true,
      data: integration,
      message: `${integration.name} updated successfully`
    })

  } catch (error) {
    console.error('Error updating integration:', error)
    return NextResponse.json(
      { error: 'Failed to update integration' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, integrationId, data } = body

    switch (action) {
      case 'test_connection':
        const integration = mockIntegrations.find(i => i.id === integrationId)
        if (!integration) {
          return NextResponse.json(
            { error: 'Integration not found' },
            { status: 404 }
          )
        }

        // Update status to testing
        integration.status = 'testing'

        // Simulate connection test
        await new Promise(resolve => setTimeout(resolve, 2000))

        // Randomly succeed or fail for demo
        const success = Math.random() > 0.3

        integration.status = success ? 'connected' : 'error'
        if (success) {
          integration.lastSync = new Date().toISOString()
        }

        return NextResponse.json({
          success,
          data: integration,
          message: success 
            ? `${integration.name} connection test successful`
            : `${integration.name} connection test failed`
        })

      case 'sync_data':
        const syncIntegration = mockIntegrations.find(i => i.id === integrationId)
        if (!syncIntegration) {
          return NextResponse.json(
            { error: 'Integration not found' },
            { status: 404 }
          )
        }

        if (!syncIntegration.enabled || syncIntegration.status !== 'connected') {
          return NextResponse.json(
            { error: 'Integration must be enabled and connected to sync data' },
            { status: 400 }
          )
        }

        // Simulate data sync
        await new Promise(resolve => setTimeout(resolve, 3000))

        syncIntegration.lastSync = new Date().toISOString()

        return NextResponse.json({
          success: true,
          data: syncIntegration,
          message: `${syncIntegration.name} data sync completed`
        })

      case 'reset_integration':
        const resetIntegration = mockIntegrations.find(i => i.id === integrationId)
        if (!resetIntegration) {
          return NextResponse.json(
            { error: 'Integration not found' },
            { status: 404 }
          )
        }

        // Reset integration to default state
        resetIntegration.enabled = false
        resetIntegration.configured = false
        resetIntegration.status = 'disconnected'
        resetIntegration.settings = {}
        resetIntegration.lastSync = undefined

        return NextResponse.json({
          success: true,
          data: resetIntegration,
          message: `${resetIntegration.name} has been reset to default settings`
        })

      case 'bulk_enable':
        const { integrationIds } = data
        if (!integrationIds || !Array.isArray(integrationIds)) {
          return NextResponse.json(
            { error: 'Integration IDs array is required' },
            { status: 400 }
          )
        }

        const updatedIntegrations = mockIntegrations.map(integration => {
          if (integrationIds.includes(integration.id)) {
            return {
              ...integration,
              enabled: true,
              status: integration.configured ? 'connected' : 'disconnected'
            }
          }
          return integration
        })

        mockIntegrations = updatedIntegrations

        return NextResponse.json({
          success: true,
          data: {
            updated: integrationIds.length,
            integrations: updatedIntegrations.filter(i => integrationIds.includes(i.id))
          },
          message: `${integrationIds.length} integrations enabled`
        })

      case 'bulk_disable':
        const { integrationIds: disableIds } = data
        if (!disableIds || !Array.isArray(disableIds)) {
          return NextResponse.json(
            { error: 'Integration IDs array is required' },
            { status: 400 }
          )
        }

        const disabledIntegrations = mockIntegrations.map(integration => {
          if (disableIds.includes(integration.id)) {
            return {
              ...integration,
              enabled: false,
              status: 'disconnected'
            }
          }
          return integration
        })

        mockIntegrations = disabledIntegrations

        return NextResponse.json({
          success: true,
          data: {
            updated: disableIds.length,
            integrations: disabledIntegrations.filter(i => disableIds.includes(i.id))
          },
          message: `${disableIds.length} integrations disabled`
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Error processing integration action:', error)
    return NextResponse.json(
      { error: 'Failed to process integration action' },
      { status: 500 }
    )
  }
}
