import { NextRequest, NextResponse } from 'next/server'

interface NotificationRecord {
  id: string
  type: 'email' | 'sms' | 'push'
  template: string
  recipient: string
  subject: string
  status: 'sent' | 'failed' | 'pending' | 'delivered'
  sentAt: string
  deliveredAt?: string
  errorMessage?: string
  metadata: {
    orderId?: string
    customerId?: string
    adminId?: string
  }
}

// Mock notification history storage
const mockNotifications: NotificationRecord[] = [
  {
    id: '1',
    type: 'email',
    template: 'Order Confirmation',
    recipient: '<EMAIL>',
    subject: 'Your order #CM000123 has been confirmed',
    status: 'delivered',
    sentAt: '2024-01-15T10:30:00Z',
    deliveredAt: '2024-01-15T10:30:15Z',
    metadata: {
      orderId: 'CM000123',
      customerId: 'cust_123'
    }
  },
  {
    id: '2',
    type: 'sms',
    template: 'Order Shipped',
    recipient: '+27821234567',
    subject: 'Your order has been shipped',
    status: 'sent',
    sentAt: '2024-01-15T09:15:00Z',
    metadata: {
      orderId: 'CM000122',
      customerId: 'cust_124'
    }
  },
  {
    id: '3',
    type: 'email',
    template: 'Low Stock Alert',
    recipient: '<EMAIL>',
    subject: 'Low stock alert: Blue Denim Jacket',
    status: 'failed',
    sentAt: '2024-01-15T08:45:00Z',
    errorMessage: 'SMTP connection failed',
    metadata: {
      adminId: 'admin_1'
    }
  },
  {
    id: '4',
    type: 'push',
    template: 'New Order',
    recipient: '<EMAIL>',
    subject: 'New order received',
    status: 'pending',
    sentAt: '2024-01-15T11:00:00Z',
    metadata: {
      orderId: 'CM000124',
      adminId: 'admin_1'
    }
  },
  {
    id: '5',
    type: 'email',
    template: 'Welcome Email',
    recipient: '<EMAIL>',
    subject: 'Welcome to Coco Milk Kids!',
    status: 'delivered',
    sentAt: '2024-01-14T16:20:00Z',
    deliveredAt: '2024-01-14T16:20:08Z',
    metadata: {
      customerId: 'cust_125'
    }
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const type = searchParams.get('type')
    const status = searchParams.get('status')
    const search = searchParams.get('search')

    let filteredNotifications = [...mockNotifications]

    // Apply filters
    if (type && type !== 'all') {
      filteredNotifications = filteredNotifications.filter(n => n.type === type)
    }

    if (status && status !== 'all') {
      filteredNotifications = filteredNotifications.filter(n => n.status === status)
    }

    if (search) {
      const searchLower = search.toLowerCase()
      filteredNotifications = filteredNotifications.filter(n => 
        n.recipient.toLowerCase().includes(searchLower) ||
        n.subject.toLowerCase().includes(searchLower) ||
        n.template.toLowerCase().includes(searchLower)
      )
    }

    // Sort by sent date (newest first)
    filteredNotifications.sort((a, b) => new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime())

    // Pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedNotifications = filteredNotifications.slice(startIndex, endIndex)

    return NextResponse.json({
      success: true,
      data: {
        notifications: paginatedNotifications,
        pagination: {
          page,
          limit,
          total: filteredNotifications.length,
          totalPages: Math.ceil(filteredNotifications.length / limit)
        }
      }
    })

  } catch (error) {
    console.error('Error fetching notifications:', error)
    return NextResponse.json(
      { error: 'Failed to fetch notifications' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, data } = body

    switch (action) {
      case 'send':
        // Send a new notification
        const { type, template, recipient, subject, content } = data

        if (!type || !template || !recipient || !subject) {
          return NextResponse.json(
            { error: 'Missing required fields' },
            { status: 400 }
          )
        }

        const newNotification: NotificationRecord = {
          id: Date.now().toString(),
          type,
          template,
          recipient,
          subject,
          status: 'pending',
          sentAt: new Date().toISOString(),
          metadata: data.metadata || {}
        }

        // Simulate sending process
        setTimeout(() => {
          // Randomly succeed or fail for demo
          const success = Math.random() > 0.2
          newNotification.status = success ? 'sent' : 'failed'
          if (!success) {
            newNotification.errorMessage = 'Simulated delivery failure'
          }
          mockNotifications.unshift(newNotification)
        }, 1000)

        mockNotifications.unshift(newNotification)

        return NextResponse.json({
          success: true,
          data: newNotification,
          message: 'Notification queued for sending'
        })

      case 'test':
        // Send a test notification
        const { email, template: testTemplate } = data

        if (!email || !testTemplate) {
          return NextResponse.json(
            { error: 'Email and template are required for test' },
            { status: 400 }
          )
        }

        const testNotification: NotificationRecord = {
          id: `test_${Date.now()}`,
          type: 'email',
          template: `Test: ${testTemplate}`,
          recipient: email,
          subject: `Test notification: ${testTemplate}`,
          status: 'sent',
          sentAt: new Date().toISOString(),
          deliveredAt: new Date().toISOString(),
          metadata: {
            test: true
          }
        }

        mockNotifications.unshift(testNotification)

        return NextResponse.json({
          success: true,
          data: testNotification,
          message: 'Test notification sent successfully'
        })

      case 'retry':
        // Retry a failed notification
        const { notificationId } = data

        const notification = mockNotifications.find(n => n.id === notificationId)
        if (!notification) {
          return NextResponse.json(
            { error: 'Notification not found' },
            { status: 404 }
          )
        }

        if (notification.status !== 'failed') {
          return NextResponse.json(
            { error: 'Only failed notifications can be retried' },
            { status: 400 }
          )
        }

        // Update notification status
        notification.status = 'pending'
        notification.errorMessage = undefined

        // Simulate retry process
        setTimeout(() => {
          notification.status = 'sent'
          notification.sentAt = new Date().toISOString()
        }, 1000)

        return NextResponse.json({
          success: true,
          data: notification,
          message: 'Notification retry initiated'
        })

      case 'bulk_send':
        // Send notifications to multiple recipients
        const { recipients, template: bulkTemplate, subject: bulkSubject } = data

        if (!recipients || !Array.isArray(recipients) || recipients.length === 0) {
          return NextResponse.json(
            { error: 'Recipients array is required' },
            { status: 400 }
          )
        }

        const bulkNotifications = recipients.map((recipient, index) => ({
          id: `bulk_${Date.now()}_${index}`,
          type: 'email' as const,
          template: bulkTemplate,
          recipient,
          subject: bulkSubject,
          status: 'pending' as const,
          sentAt: new Date().toISOString(),
          metadata: {
            bulk: true,
            batchId: `batch_${Date.now()}`
          }
        }))

        // Add to mock storage
        mockNotifications.unshift(...bulkNotifications)

        return NextResponse.json({
          success: true,
          data: {
            notifications: bulkNotifications,
            count: bulkNotifications.length
          },
          message: `${bulkNotifications.length} notifications queued for sending`
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Error processing notification action:', error)
    return NextResponse.json(
      { error: 'Failed to process notification action' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Notification ID is required' },
        { status: 400 }
      )
    }

    const index = mockNotifications.findIndex(n => n.id === id)
    if (index === -1) {
      return NextResponse.json(
        { error: 'Notification not found' },
        { status: 404 }
      )
    }

    mockNotifications.splice(index, 1)

    return NextResponse.json({
      success: true,
      message: 'Notification deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting notification:', error)
    return NextResponse.json(
      { error: 'Failed to delete notification' },
      { status: 500 }
    )
  }
}
