import { NextRequest, NextResponse } from 'next/server'

interface NotificationSettings {
  emailEnabled: boolean
  smsEnabled: boolean
  pushEnabled: boolean
  adminNotifications: {
    newOrder: boolean
    lowStock: boolean
    newCustomer: boolean
    systemAlerts: boolean
  }
  customerNotifications: {
    orderConfirmation: boolean
    orderStatusUpdate: boolean
    shipmentTracking: boolean
    promotionalEmails: boolean
  }
  emailConfig: {
    smtpHost: string
    smtpPort: number
    username: string
    password: string
    encryption: 'tls' | 'ssl' | 'none'
    fromName: string
    fromEmail: string
  }
  smsConfig: {
    provider: 'twilio' | 'clickatell' | 'custom'
    apiKey: string
    apiSecret: string
    fromNumber: string
  }
}

// Mock settings storage
let mockSettings: NotificationSettings = {
  emailEnabled: true,
  smsEnabled: false,
  pushEnabled: true,
  adminNotifications: {
    newOrder: true,
    lowStock: true,
    newCustomer: false,
    systemAlerts: true
  },
  customerNotifications: {
    orderConfirmation: true,
    orderStatusUpdate: true,
    shipmentTracking: true,
    promotionalEmails: false
  },
  emailConfig: {
    smtpHost: 'smtp.gmail.com',
    smtpPort: 587,
    username: '',
    password: '',
    encryption: 'tls',
    fromName: 'Coco Milk Kids',
    fromEmail: '<EMAIL>'
  },
  smsConfig: {
    provider: 'clickatell',
    apiKey: '',
    apiSecret: '',
    fromNumber: ''
  }
}

export async function GET(request: NextRequest) {
  try {
    return NextResponse.json({
      success: true,
      data: {
        settings: mockSettings,
        lastUpdated: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Error fetching notification settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch notification settings' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const settings = await request.json()

    // Validate settings structure
    if (typeof settings !== 'object' || settings === null) {
      return NextResponse.json(
        { error: 'Settings must be an object' },
        { status: 400 }
      )
    }

    // Update settings
    mockSettings = { ...mockSettings, ...settings }

    return NextResponse.json({
      success: true,
      data: {
        settings: mockSettings,
        lastUpdated: new Date().toISOString()
      },
      message: 'Notification settings updated successfully'
    })

  } catch (error) {
    console.error('Error updating notification settings:', error)
    return NextResponse.json(
      { error: 'Failed to update notification settings' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, data } = body

    switch (action) {
      case 'test_email':
        // Test email configuration
        const { email } = data

        if (!email) {
          return NextResponse.json(
            { error: 'Email address is required for test' },
            { status: 400 }
          )
        }

        if (!mockSettings.emailEnabled) {
          return NextResponse.json(
            { error: 'Email notifications are disabled' },
            { status: 400 }
          )
        }

        // Simulate email test
        await new Promise(resolve => setTimeout(resolve, 2000))

        // Randomly succeed or fail for demo
        const success = Math.random() > 0.3

        if (success) {
          return NextResponse.json({
            success: true,
            message: 'Test email sent successfully'
          })
        } else {
          return NextResponse.json(
            { error: 'Failed to send test email. Please check your SMTP configuration.' },
            { status: 400 }
          )
        }

      case 'test_sms':
        // Test SMS configuration
        const { phoneNumber } = data

        if (!phoneNumber) {
          return NextResponse.json(
            { error: 'Phone number is required for test' },
            { status: 400 }
          )
        }

        if (!mockSettings.smsEnabled) {
          return NextResponse.json(
            { error: 'SMS notifications are disabled' },
            { status: 400 }
          )
        }

        // Simulate SMS test
        await new Promise(resolve => setTimeout(resolve, 1500))

        // Randomly succeed or fail for demo
        const smsSuccess = Math.random() > 0.2

        if (smsSuccess) {
          return NextResponse.json({
            success: true,
            message: 'Test SMS sent successfully'
          })
        } else {
          return NextResponse.json(
            { error: 'Failed to send test SMS. Please check your SMS provider configuration.' },
            { status: 400 }
          )
        }

      case 'validate_config':
        // Validate notification configuration
        const errors: string[] = []

        if (mockSettings.emailEnabled) {
          if (!mockSettings.emailConfig.smtpHost) {
            errors.push('SMTP host is required for email notifications')
          }
          if (!mockSettings.emailConfig.username) {
            errors.push('SMTP username is required for email notifications')
          }
          if (!mockSettings.emailConfig.password) {
            errors.push('SMTP password is required for email notifications')
          }
          if (!mockSettings.emailConfig.fromEmail) {
            errors.push('From email address is required for email notifications')
          }
        }

        if (mockSettings.smsEnabled) {
          if (!mockSettings.smsConfig.apiKey) {
            errors.push('SMS API key is required for SMS notifications')
          }
          if (!mockSettings.smsConfig.fromNumber) {
            errors.push('From phone number is required for SMS notifications')
          }
        }

        return NextResponse.json({
          success: true,
          data: {
            valid: errors.length === 0,
            errors
          }
        })

      case 'reset_to_defaults':
        // Reset settings to defaults
        mockSettings = {
          emailEnabled: true,
          smsEnabled: false,
          pushEnabled: true,
          adminNotifications: {
            newOrder: true,
            lowStock: true,
            newCustomer: false,
            systemAlerts: true
          },
          customerNotifications: {
            orderConfirmation: true,
            orderStatusUpdate: true,
            shipmentTracking: true,
            promotionalEmails: false
          },
          emailConfig: {
            smtpHost: '',
            smtpPort: 587,
            username: '',
            password: '',
            encryption: 'tls',
            fromName: 'Coco Milk Kids',
            fromEmail: '<EMAIL>'
          },
          smsConfig: {
            provider: 'clickatell',
            apiKey: '',
            apiSecret: '',
            fromNumber: ''
          }
        }

        return NextResponse.json({
          success: true,
          data: {
            settings: mockSettings,
            lastUpdated: new Date().toISOString()
          },
          message: 'Notification settings reset to defaults'
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Error processing notification settings action:', error)
    return NextResponse.json(
      { error: 'Failed to process notification settings action' },
      { status: 500 }
    )
  }
}
