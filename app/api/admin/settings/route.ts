import { NextRequest, NextResponse } from 'next/server'

// Mock settings storage - in production, this would use a database
const mockSettings: Record<string, Record<string, any>> = {
  general: {
    siteName: 'Coco Milk Kids',
    siteDescription: 'Premium children\'s clothing and accessories for South African families',
    siteUrl: 'https://cocomilkkids.co.za',
    adminEmail: '<EMAIL>',
    timezone: 'Africa/Johannesburg',
    dateFormat: 'Y-m-d',
    timeFormat: 'H:i',
    weekStartsOn: '1',
    language: 'en_ZA',
    maintenanceMode: false,
    registrationEnabled: true,
    defaultUserRole: 'customer',
    enableComments: true,
    moderateComments: true,
    enableGravatar: true
  },
  store: {
    storeName: 'Coco Milk Kids',
    storeDescription: 'Premium children\'s clothing and accessories designed for comfort, style, and adventure.',
    storeTagline: 'Where Style Meets Adventure',
    email: '<EMAIL>',
    phone: '+27 21 123 4567',
    address: {
      street: '123 Main Street',
      city: 'Cape Town',
      state: 'Western Cape',
      postalCode: '8001',
      country: 'ZA'
    },
    vatNumber: '4123456789',
    registrationNumber: '2023/123456/07',
    currency: 'ZAR',
    currencyPosition: 'before',
    currencySymbol: 'R',
    decimalPlaces: 2,
    thousandSeparator: ',',
    decimalSeparator: '.',
    pricesIncludeTax: true,
    taxCalculation: 'inclusive',
    displayPricesWithTax: true
  },
  payments: {
    enablePayments: true,
    defaultGateway: 'payfast',
    testMode: true,
    gateways: {
      payfast: {
        enabled: true,
        merchantId: '********',
        merchantKey: '46f0cd694581a',
        passphrase: '',
        testMode: true
      },
      ozow: {
        enabled: true,
        siteCode: 'TEST-SITE',
        privateKey: '',
        apiKey: '',
        testMode: true,
        bankReference: 'Coco Milk Kids'
      },
      stripe: {
        enabled: false,
        publishableKey: '',
        secretKey: '',
        webhookSecret: '',
        testMode: true
      },
      cod: {
        enabled: true,
        title: 'Cash on Delivery',
        description: 'Pay when you receive your order',
        instructions: 'Please have the exact amount ready when the courier arrives.'
      }
    }
  },
  shipping: {
    enableShipping: true,
    enableShippingCalculator: true,
    hideShippingUntilAddress: false,
    enableLocalPickup: true,
    pickupLocations: ['Cape Town Store', 'Johannesburg Store'],
    weightUnit: 'kg',
    dimensionUnit: 'cm',
    defaultDimensions: {
      length: 20,
      width: 15,
      height: 5,
      weight: 0.5
    }
  },
  taxes: {
    enableTaxes: true,
    pricesIncludeTax: true,
    calculateTaxBased: 'shipping',
    shippingTaxClass: 'standard',
    roundingMode: 'round',
    displayPricesInShop: 'including',
    displayPricesInCart: 'including',
    displayTaxTotals: 'itemized',
    enableTaxReports: true
  },
  security: {
    minPasswordLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
    passwordExpiry: 90,
    preventPasswordReuse: 5,
    enable2FA: false,
    force2FA: false,
    allowedMethods: ['totp', 'sms'],
    sessionTimeout: 60,
    maxConcurrentSessions: 3,
    logoutInactiveSessions: true,
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    enableCaptcha: true,
    enableIPWhitelist: false,
    allowedIPs: [],
    enableCSP: true,
    enableHSTS: true,
    enableXFrameOptions: true,
    enableXSSProtection: true,
    enableAuditLog: true,
    logFailedLogins: true,
    logPasswordChanges: true,
    logPermissionChanges: true,
    retentionPeriod: 365
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const section = searchParams.get('section')

    if (section) {
      // Return specific section
      if (!mockSettings[section]) {
        return NextResponse.json(
          { error: `Settings section '${section}' not found` },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        data: {
          section,
          settings: mockSettings[section],
          lastUpdated: new Date().toISOString()
        }
      })
    }

    // Return all settings
    const allSettings = Object.entries(mockSettings).map(([key, data]) => ({
      section: key,
      settings: data,
      lastUpdated: new Date().toISOString()
    }))

    return NextResponse.json({
      success: true,
      data: allSettings
    })

  } catch (error) {
    console.error('Error fetching settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { section, settings } = body

    if (!section || !settings) {
      return NextResponse.json(
        { error: 'Section and settings are required' },
        { status: 400 }
      )
    }

    if (!mockSettings[section]) {
      return NextResponse.json(
        { error: `Settings section '${section}' not found` },
        { status: 404 }
      )
    }

    // Validate settings structure (basic validation)
    if (typeof settings !== 'object' || settings === null) {
      return NextResponse.json(
        { error: 'Settings must be an object' },
        { status: 400 }
      )
    }

    // Update settings
    mockSettings[section] = { ...mockSettings[section], ...settings }

    return NextResponse.json({
      success: true,
      data: {
        section,
        settings: mockSettings[section],
        lastUpdated: new Date().toISOString()
      },
      message: `${section.charAt(0).toUpperCase() + section.slice(1)} settings updated successfully`
    })

  } catch (error) {
    console.error('Error updating settings:', error)
    return NextResponse.json(
      { error: 'Failed to update settings' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, data } = body

    switch (action) {
      case 'export':
        const exportData = {
          version: '1.0',
          timestamp: new Date().toISOString(),
          settings: mockSettings
        }
        
        return NextResponse.json({
          success: true,
          data: exportData
        })

      case 'import':
        if (!data || !data.settings) {
          return NextResponse.json(
            { error: 'Invalid import data' },
            { status: 400 }
          )
        }

        // Validate and import settings
        for (const [section, sectionSettings] of Object.entries(data.settings)) {
          if (mockSettings[section] && typeof sectionSettings === 'object') {
            mockSettings[section] = { ...mockSettings[section], ...sectionSettings }
          }
        }

        return NextResponse.json({
          success: true,
          message: 'Settings imported successfully'
        })

      case 'reset':
        const { section } = data
        if (!section || !mockSettings[section]) {
          return NextResponse.json(
            { error: 'Invalid section for reset' },
            { status: 400 }
          )
        }

        // Reset to default values (you would define defaults elsewhere)
        // For now, just return success
        return NextResponse.json({
          success: true,
          message: `${section.charAt(0).toUpperCase() + section.slice(1)} settings reset successfully`
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Error processing settings action:', error)
    return NextResponse.json(
      { error: 'Failed to process settings action' },
      { status: 500 }
    )
  }
}
