import { NextRequest, NextResponse } from 'next/server'

interface RouteParams {
  params: {
    section: string
  }
}

// Mock settings storage - in production, this would use a database
const mockSettings: Record<string, Record<string, any>> = {
  general: {
    siteName: 'Coco Milk Kids',
    siteDescription: 'Premium children\'s clothing and accessories for South African families',
    siteUrl: 'https://cocomilkkids.co.za',
    adminEmail: '<EMAIL>',
    timezone: 'Africa/Johannesburg',
    dateFormat: 'Y-m-d',
    timeFormat: 'H:i',
    weekStartsOn: '1',
    language: 'en_ZA',
    logoUrl: '',
    faviconUrl: '',
    maintenanceMode: false,
    maintenanceMessage: 'We are currently performing scheduled maintenance. Please check back soon.',
    registrationEnabled: true,
    defaultUserRole: 'customer',
    enableComments: true,
    moderateComments: true,
    enableGravatar: true
  },
  store: {
    storeName: 'Coco Milk Kids',
    storeDescription: 'Premium children\'s clothing and accessories designed for comfort, style, and adventure.',
    storeTagline: 'Where Style Meets Adventure',
    email: '<EMAIL>',
    phone: '+27 21 123 4567',
    address: {
      street: '123 Main Street',
      city: 'Cape Town',
      state: 'Western Cape',
      postalCode: '8001',
      country: 'ZA'
    },
    vatNumber: '4123456789',
    registrationNumber: '2023/123456/07',
    currency: 'ZAR',
    currencyPosition: 'before',
    currencySymbol: 'R',
    decimalPlaces: 2,
    thousandSeparator: ',',
    decimalSeparator: '.',
    pricesIncludeTax: true,
    taxCalculation: 'inclusive',
    displayPricesWithTax: true,
    manageStock: true,
    stockNotifications: true,
    lowStockThreshold: 5,
    outOfStockVisibility: 'hidden',
    orderNumberPrefix: 'CM',
    orderNumberSuffix: '',
    orderNumberLength: 6,
    enableGuestCheckout: true,
    requireAccountForPurchase: false,
    enableReviews: true,
    reviewsRequireApproval: false,
    enableReviewRatings: true,
    onlyVerifiedPurchasers: true
  },
  payments: {
    payfast: {
      enabled: true,
      merchantId: '********',
      merchantKey: '46f0cd694581a',
      passphrase: '',
      testMode: true,
      status: 'connected'
    },
    ozow: {
      enabled: true,
      siteCode: 'TEST-SITE',
      privateKey: '',
      apiKey: '',
      testMode: true,
      bankReference: 'Coco Milk Kids',
      status: 'connected'
    },
    stripe: {
      enabled: false,
      publishableKey: '',
      secretKey: '',
      webhookSecret: '',
      currency: 'ZAR',
      testMode: true,
      status: 'disconnected'
    },
    paypal: {
      enabled: false,
      clientId: '',
      clientSecret: '',
      mode: 'sandbox',
      testMode: true,
      status: 'disconnected'
    },
    cod: {
      enabled: true,
      title: 'Cash on Delivery',
      description: 'Pay when you receive your order',
      instructions: 'Please have the exact amount ready when the courier arrives.',
      enableForShippingMethods: ['standard', 'express'],
      status: 'connected'
    }
  },
  shipping: {
    enableShipping: true,
    enableShippingCalculator: true,
    hideShippingUntilAddress: false,
    enableLocalPickup: true,
    pickupLocations: ['Cape Town Store', 'Johannesburg Store'],
    weightUnit: 'kg',
    dimensionUnit: 'cm',
    defaultDimensions: {
      length: 20,
      width: 15,
      height: 5,
      weight: 0.5
    },
    zones: [
      {
        id: 'south-africa',
        name: 'South Africa',
        regions: ['Western Cape', 'Gauteng', 'KwaZulu-Natal', 'Eastern Cape', 'Free State', 'Limpopo', 'Mpumalanga', 'Northern Cape', 'North West'],
        methods: [
          {
            id: 'standard',
            name: 'Standard Shipping',
            description: 'Regular delivery within 3-5 business days',
            type: 'flat_rate',
            cost: 99,
            enabled: true,
            estimatedDays: '3-5'
          },
          {
            id: 'express',
            name: 'Express Shipping',
            description: 'Fast delivery within 1-2 business days',
            type: 'flat_rate',
            cost: 199,
            enabled: true,
            estimatedDays: '1-2'
          }
        ]
      }
    ]
  },
  taxes: {
    enableTaxes: true,
    pricesIncludeTax: true,
    calculateTaxBased: 'shipping',
    shippingTaxClass: 'standard',
    roundingMode: 'round',
    displayPricesInShop: 'including',
    displayPricesInCart: 'including',
    displayTaxTotals: 'itemized',
    enableTaxReports: true,
    rates: [
      {
        id: 'vat-za',
        name: 'South Africa VAT',
        rate: 15,
        type: 'percentage',
        country: 'ZA',
        priority: 1,
        compound: false,
        shipping: true,
        enabled: true
      }
    ],
    classes: [
      {
        id: 'standard',
        name: 'Standard Rate',
        description: 'Standard VAT rate for most products',
        rates: ['vat-za']
      }
    ]
  },
  security: {
    minPasswordLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
    passwordExpiry: 90,
    preventPasswordReuse: 5,
    enable2FA: false,
    force2FA: false,
    allowedMethods: ['totp', 'sms'],
    sessionTimeout: 60,
    maxConcurrentSessions: 3,
    logoutInactiveSessions: true,
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    enableCaptcha: true,
    enableIPWhitelist: false,
    allowedIPs: [],
    enableCSP: true,
    enableHSTS: true,
    enableXFrameOptions: true,
    enableXSSProtection: true,
    enableAuditLog: true,
    logFailedLogins: true,
    logPasswordChanges: true,
    logPermissionChanges: true,
    retentionPeriod: 365
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { section } = params

    if (!mockSettings[section]) {
      return NextResponse.json(
        { error: `Settings section '${section}' not found` },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        section,
        settings: mockSettings[section],
        lastUpdated: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Error fetching settings section:', error)
    return NextResponse.json(
      { error: 'Failed to fetch settings section' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { section } = params
    const settings = await request.json()

    if (!mockSettings[section]) {
      return NextResponse.json(
        { error: `Settings section '${section}' not found` },
        { status: 404 }
      )
    }

    // Validate settings structure (basic validation)
    if (typeof settings !== 'object' || settings === null) {
      return NextResponse.json(
        { error: 'Settings must be an object' },
        { status: 400 }
      )
    }

    // Update settings
    mockSettings[section] = { ...mockSettings[section], ...settings }

    return NextResponse.json({
      success: true,
      data: {
        section,
        settings: mockSettings[section],
        lastUpdated: new Date().toISOString()
      },
      message: `${section.charAt(0).toUpperCase() + section.slice(1)} settings updated successfully`
    })

  } catch (error) {
    console.error('Error updating settings section:', error)
    return NextResponse.json(
      { error: 'Failed to update settings section' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const { section } = params
    const updates = await request.json()

    if (!mockSettings[section]) {
      return NextResponse.json(
        { error: `Settings section '${section}' not found` },
        { status: 404 }
      )
    }

    // Validate updates structure
    if (typeof updates !== 'object' || updates === null) {
      return NextResponse.json(
        { error: 'Updates must be an object' },
        { status: 400 }
      )
    }

    // Apply partial updates
    mockSettings[section] = { ...mockSettings[section], ...updates }

    return NextResponse.json({
      success: true,
      data: {
        section,
        settings: mockSettings[section],
        lastUpdated: new Date().toISOString()
      },
      message: `${section.charAt(0).toUpperCase() + section.slice(1)} settings updated successfully`
    })

  } catch (error) {
    console.error('Error patching settings section:', error)
    return NextResponse.json(
      { error: 'Failed to patch settings section' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { section } = params

    if (!mockSettings[section]) {
      return NextResponse.json(
        { error: `Settings section '${section}' not found` },
        { status: 404 }
      )
    }

    // Reset to default values (in production, you'd have a defaults system)
    // For now, just clear the section
    delete mockSettings[section]

    return NextResponse.json({
      success: true,
      message: `${section.charAt(0).toUpperCase() + section.slice(1)} settings reset successfully`
    })

  } catch (error) {
    console.error('Error resetting settings section:', error)
    return NextResponse.json(
      { error: 'Failed to reset settings section' },
      { status: 500 }
    )
  }
}
