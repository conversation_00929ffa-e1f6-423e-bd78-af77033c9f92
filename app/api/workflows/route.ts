import { NextRequest, NextResponse } from 'next/server'
import { WorkflowService } from '@/lib/workflows/workflow-service'
import { WorkflowDefinition, WorkflowEventType } from '@/lib/workflows/types'

const workflowService = new WorkflowService()

// Initialize workflow service
workflowService.initialize().catch(console.error)

// Get workflows
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const isActive = searchParams.get('isActive')
    const tags = searchParams.get('tags')?.split(',')
    const search = searchParams.get('search')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    const filters = {
      category: category || undefined,
      isActive: isActive ? isActive === 'true' : undefined,
      tags,
      search: search || undefined,
      limit,
      offset
    }

    const result = await workflowService.listWorkflows(filters)

    return NextResponse.json({
      success: true,
      data: result.workflows,
      pagination: {
        total: result.total,
        limit,
        offset,
        hasMore: offset + limit < result.total
      }
    })

  } catch (error) {
    console.error('Error fetching workflows:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch workflows' },
      { status: 500 }
    )
  }
}

// Create workflow
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { fromTemplate, templateId, ...workflowData } = body

    let workflow: WorkflowDefinition

    if (fromTemplate && templateId) {
      // Create from template
      workflow = await workflowService.createWorkflowFromTemplate(templateId, workflowData)
    } else {
      // Create custom workflow
      workflow = await workflowService.createWorkflow(workflowData)
    }

    return NextResponse.json({
      success: true,
      data: workflow,
      message: 'Workflow created successfully'
    })

  } catch (error: any) {
    console.error('Error creating workflow:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to create workflow' },
      { status: 500 }
    )
  }
}

// Trigger workflow
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { eventType, eventData, context } = body

    if (!eventType) {
      return NextResponse.json(
        { success: false, error: 'Event type is required' },
        { status: 400 }
      )
    }

    const executionIds = await workflowService.triggerWorkflow(
      eventType as WorkflowEventType,
      eventData || {},
      context || {}
    )

    return NextResponse.json({
      success: true,
      data: { executionIds },
      message: `Triggered ${executionIds.length} workflow(s)`
    })

  } catch (error: any) {
    console.error('Error triggering workflow:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to trigger workflow' },
      { status: 500 }
    )
  }
}

// Bulk operations
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, workflowIds, data } = body

    if (!action || !workflowIds || !Array.isArray(workflowIds)) {
      return NextResponse.json(
        { success: false, error: 'Action and workflow IDs are required' },
        { status: 400 }
      )
    }

    let results: any[] = []

    switch (action) {
      case 'activate':
        for (const workflowId of workflowIds) {
          const result = await workflowService.updateWorkflow(workflowId, { isActive: true })
          results.push(result)
        }
        break

      case 'deactivate':
        for (const workflowId of workflowIds) {
          const result = await workflowService.updateWorkflow(workflowId, { isActive: false })
          results.push(result)
        }
        break

      case 'delete':
        for (const workflowId of workflowIds) {
          const result = await workflowService.deleteWorkflow(workflowId)
          results.push({ id: workflowId, deleted: result })
        }
        break

      case 'update':
        if (!data) {
          return NextResponse.json(
            { success: false, error: 'Update data is required' },
            { status: 400 }
          )
        }

        for (const workflowId of workflowIds) {
          const result = await workflowService.updateWorkflow(workflowId, data)
          results.push(result)
        }
        break

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      data: results,
      message: `Bulk ${action} completed`
    })

  } catch (error: any) {
    console.error('Error in bulk workflow operation:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Bulk operation failed' },
      { status: 500 }
    )
  }
}

// Get workflow statistics
export async function OPTIONS(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'stats':
        const [activeWorkflows, totalExecutions] = await Promise.all([
          workflowService.listWorkflows({ isActive: true }),
          workflowService.listExecutions({ limit: 1 })
        ])

        const stats = {
          totalWorkflows: activeWorkflows.total,
          activeWorkflows: activeWorkflows.workflows.length,
          totalExecutions: totalExecutions.total,
          categories: {
            customer: activeWorkflows.workflows.filter(w => w.category === 'customer').length,
            order: activeWorkflows.workflows.filter(w => w.category === 'order').length,
            inventory: activeWorkflows.workflows.filter(w => w.category === 'inventory').length,
            marketing: activeWorkflows.workflows.filter(w => w.category === 'marketing').length,
            admin: activeWorkflows.workflows.filter(w => w.category === 'admin').length
          }
        }

        return NextResponse.json({
          success: true,
          data: stats
        })

      case 'templates':
        const templates = workflowService.getTemplates()
        return NextResponse.json({
          success: true,
          data: templates
        })

      case 'event-types':
        const eventTypes = [
          'customer.registered',
          'customer.login',
          'customer.profile_updated',
          'order.created',
          'order.paid',
          'order.shipped',
          'order.delivered',
          'order.cancelled',
          'order.refunded',
          'cart.abandoned',
          'cart.recovered',
          'product.viewed',
          'product.added_to_cart',
          'product.purchased',
          'inventory.low_stock',
          'inventory.out_of_stock',
          'inventory.reorder_point',
          'payment.succeeded',
          'payment.failed',
          'shipping.label_created',
          'shipping.dispatched',
          'shipping.delivered',
          'review.submitted',
          'support.ticket_created',
          'marketing.campaign_sent',
          'marketing.email_opened',
          'marketing.email_clicked'
        ]

        return NextResponse.json({
          success: true,
          data: eventTypes
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Error getting workflow options:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get workflow options' },
      { status: 500 }
    )
  }
}
