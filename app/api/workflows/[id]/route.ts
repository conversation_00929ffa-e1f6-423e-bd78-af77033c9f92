import { NextRequest, NextResponse } from 'next/server'
import { WorkflowService } from '@/lib/workflows/workflow-service'

const workflowService = new WorkflowService()

interface RouteParams {
  params: { id: string }
}

// Get single workflow
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params

    const workflow = await workflowService.getWorkflow(id)

    if (!workflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: workflow
    })

  } catch (error) {
    console.error('Error fetching workflow:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch workflow' },
      { status: 500 }
    )
  }
}

// Update workflow
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params
    const updates = await request.json()

    const workflow = await workflowService.updateWorkflow(id, updates)

    if (!workflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: workflow,
      message: 'Workflow updated successfully'
    })

  } catch (error: any) {
    console.error('Error updating workflow:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to update workflow' },
      { status: 500 }
    )
  }
}

// Delete workflow
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params

    const deleted = await workflowService.deleteWorkflow(id)

    if (!deleted) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found or could not be deleted' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Workflow deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting workflow:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete workflow' },
      { status: 500 }
    )
  }
}

// Workflow actions
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params
    const { action, ...data } = await request.json()

    switch (action) {
      case 'activate':
        const activatedWorkflow = await workflowService.updateWorkflow(id, { isActive: true })
        if (!activatedWorkflow) {
          return NextResponse.json(
            { success: false, error: 'Workflow not found' },
            { status: 404 }
          )
        }
        return NextResponse.json({
          success: true,
          data: activatedWorkflow,
          message: 'Workflow activated successfully'
        })

      case 'deactivate':
        const deactivatedWorkflow = await workflowService.updateWorkflow(id, { isActive: false })
        if (!deactivatedWorkflow) {
          return NextResponse.json(
            { success: false, error: 'Workflow not found' },
            { status: 404 }
          )
        }
        return NextResponse.json({
          success: true,
          data: deactivatedWorkflow,
          message: 'Workflow deactivated successfully'
        })

      case 'duplicate':
        const originalWorkflow = await workflowService.getWorkflow(id)
        if (!originalWorkflow) {
          return NextResponse.json(
            { success: false, error: 'Workflow not found' },
            { status: 404 }
          )
        }

        const duplicatedWorkflow = await workflowService.createWorkflow({
          ...originalWorkflow,
          name: `${originalWorkflow.name} (Copy)`,
          isActive: false,
          createdBy: data.createdBy || 'system'
        })

        return NextResponse.json({
          success: true,
          data: duplicatedWorkflow,
          message: 'Workflow duplicated successfully'
        })

      case 'analytics':
        const period = data.period || 'month'
        const analytics = await workflowService.getWorkflowAnalytics(id, period)
        
        if (!analytics) {
          return NextResponse.json(
            { success: false, error: 'Analytics not available' },
            { status: 404 }
          )
        }

        return NextResponse.json({
          success: true,
          data: analytics
        })

      case 'executions':
        const limit = parseInt(data.limit || '20')
        const offset = parseInt(data.offset || '0')
        const status = data.status

        const executions = await workflowService.listExecutions({
          workflowId: id,
          status,
          limit,
          offset
        })

        return NextResponse.json({
          success: true,
          data: executions.executions,
          pagination: {
            total: executions.total,
            limit,
            offset,
            hasMore: offset + limit < executions.total
          }
        })

      case 'test':
        // Test workflow with sample data
        const testData = data.testData || {}
        const testContext = data.testContext || { userId: 'test-user' }
        
        const executionIds = await workflowService.triggerWorkflow(
          'order.created', // Default test event
          testData,
          testContext
        )

        return NextResponse.json({
          success: true,
          data: { executionIds },
          message: 'Workflow test triggered successfully'
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error: any) {
    console.error('Error in workflow action:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Workflow action failed' },
      { status: 500 }
    )
  }
}
