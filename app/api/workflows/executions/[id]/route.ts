import { NextRequest, NextResponse } from 'next/server'
import { WorkflowService } from '@/lib/workflows/workflow-service'

const workflowService = new WorkflowService()

interface RouteParams {
  params: { id: string }
}

// Get single workflow execution
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params

    const execution = await workflowService.getExecution(id)

    if (!execution) {
      return NextResponse.json(
        { success: false, error: 'Workflow execution not found' },
        { status: 404 }
      )
    }

    // Also get the workflow definition for context
    const workflow = await workflowService.getWorkflow(execution.workflowId)

    return NextResponse.json({
      success: true,
      data: {
        execution,
        workflow
      }
    })

  } catch (error) {
    console.error('Error fetching workflow execution:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch workflow execution' },
      { status: 500 }
    )
  }
}

// Execution actions
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params
    const { action, ...data } = await request.json()

    const execution = await workflowService.getExecution(id)
    if (!execution) {
      return NextResponse.json(
        { success: false, error: 'Workflow execution not found' },
        { status: 404 }
      )
    }

    switch (action) {
      case 'cancel':
        // Cancel execution (if still running)
        if (execution.status === 'running' || execution.status === 'pending') {
          // Implementation would update execution status to cancelled
          // For now, just return success
          return NextResponse.json({
            success: true,
            message: 'Workflow execution cancelled successfully'
          })
        } else {
          return NextResponse.json(
            { success: false, error: 'Cannot cancel completed or failed execution' },
            { status: 400 }
          )
        }

      case 'retry':
        // Retry failed execution
        if (execution.status === 'failed') {
          // Implementation would create a new execution with same parameters
          const retryExecutionIds = await workflowService.triggerWorkflow(
            'order.created', // Would use original trigger event
            execution.triggerData,
            execution.context
          )

          return NextResponse.json({
            success: true,
            data: { retryExecutionIds },
            message: 'Workflow execution retried successfully'
          })
        } else {
          return NextResponse.json(
            { success: false, error: 'Can only retry failed executions' },
            { status: 400 }
          )
        }

      case 'logs':
        // Get detailed logs for execution
        const logs = execution.steps.flatMap(step => 
          step.logs.map(log => ({
            ...log,
            stepId: step.stepId,
            stepStatus: step.status
          }))
        ).sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())

        return NextResponse.json({
          success: true,
          data: logs
        })

      case 'timeline':
        // Get execution timeline
        const timeline = execution.steps.map(step => ({
          stepId: step.stepId,
          status: step.status,
          startedAt: step.startedAt,
          completedAt: step.completedAt,
          duration: step.duration,
          retryCount: step.retryCount,
          error: step.error
        }))

        return NextResponse.json({
          success: true,
          data: {
            execution: {
              id: execution.id,
              status: execution.status,
              startedAt: execution.startedAt,
              completedAt: execution.completedAt,
              duration: execution.duration
            },
            timeline
          }
        })

      case 'export':
        // Export execution data
        const exportData = {
          execution,
          workflow: await workflowService.getWorkflow(execution.workflowId),
          exportedAt: new Date(),
          exportedBy: data.exportedBy || 'system'
        }

        return NextResponse.json({
          success: true,
          data: exportData,
          message: 'Execution data exported successfully'
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error: any) {
    console.error('Error in execution action:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Execution action failed' },
      { status: 500 }
    )
  }
}
