import { NextRequest, NextResponse } from 'next/server'
import { WorkflowService } from '@/lib/workflows/workflow-service'

const workflowService = new WorkflowService()

// Get workflow executions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const workflowId = searchParams.get('workflowId')
    const status = searchParams.get('status')
    const triggeredBy = searchParams.get('triggeredBy')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    const filters = {
      workflowId: workflowId || undefined,
      status: status || undefined,
      triggeredBy: triggeredBy || undefined,
      limit,
      offset
    }

    const result = await workflowService.listExecutions(filters)

    return NextResponse.json({
      success: true,
      data: result.executions,
      pagination: {
        total: result.total,
        limit,
        offset,
        hasMore: offset + limit < result.total
      }
    })

  } catch (error) {
    console.error('Error fetching workflow executions:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch workflow executions' },
      { status: 500 }
    )
  }
}

// Get execution statistics
export async function OPTIONS(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'stats':
        const allExecutions = await workflowService.listExecutions({ limit: 1000 })
        
        const stats = {
          total: allExecutions.total,
          byStatus: {
            pending: allExecutions.executions.filter(e => e.status === 'pending').length,
            running: allExecutions.executions.filter(e => e.status === 'running').length,
            completed: allExecutions.executions.filter(e => e.status === 'completed').length,
            failed: allExecutions.executions.filter(e => e.status === 'failed').length,
            cancelled: allExecutions.executions.filter(e => e.status === 'cancelled').length
          },
          successRate: allExecutions.total > 0 
            ? (allExecutions.executions.filter(e => e.status === 'completed').length / allExecutions.total) * 100 
            : 0,
          averageDuration: allExecutions.executions
            .filter(e => e.duration)
            .reduce((sum, e, _, arr) => sum + (e.duration || 0) / arr.length, 0)
        }

        return NextResponse.json({
          success: true,
          data: stats
        })

      case 'recent':
        const recentExecutions = await workflowService.listExecutions({ 
          limit: 10,
          offset: 0 
        })

        return NextResponse.json({
          success: true,
          data: recentExecutions.executions
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Error getting execution options:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get execution options' },
      { status: 500 }
    )
  }
}
