import { NextRequest, NextResponse } from 'next/server'
import { SiteSettingsService } from '@/lib/site-settings/site-settings-service'

const siteSettingsService = new SiteSettingsService()

export async function GET(request: NextRequest) {
  try {
    const homepage = await siteSettingsService.getHomepage()
    const settings = await siteSettingsService.getSiteSettings()

    return NextResponse.json({
      success: true,
      data: {
        homepage,
        maintenance: settings.maintenance
      }
    })

  } catch (error) {
    console.error('Get public homepage settings error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch homepage settings'
    }, { status: 500 })
  }
}
