import { openai } from "@ai-sdk/openai"
import { streamText } from "ai"
import { NextResponse } from "next/server"

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

export async function POST(req: Request) {
  try {
    const { preferences } = await req.json()

    const prompt = `Create a stylish outfit recommendation for a child with the following preferences:
    Age: ${preferences.age}
    Gender: ${preferences.gender}
    Occasion: ${preferences.occasion}
    Season: ${preferences.season}
    Color Preferences: ${preferences.colors}
    Style Preferences: ${preferences.style}

    Suggest a complete outfit with specific items from our collection categories.`

    const system = `You are an expert kids fashion stylist for Coco Milk Kids, a proudly South African children's clothing brand, powered by SoImagine AI.
    Your goal is to suggest outfit combinations based on the user's preferences and needs, specifically for South African children and climate.

    Key considerations:
    - South African climate (hot summers, mild winters, UV protection needs)
    - Active outdoor lifestyle (braais, beach, safari, sports)
    - Cultural diversity and Heritage Day celebrations
    - School uniform requirements and after-school activities
    - Budget-conscious South African families
    - Durability for active children

    Always recommend items from our collection categories: tops, bottoms, dresses, outerwear, and accessories.
    Include pricing in South African Rand (ZAR) and mention why each piece works well for South African conditions.
    Keep suggestions practical, stylish, and culturally relevant.
    Use friendly South African expressions where appropriate.`

    const result = streamText({
      model: openai('gpt-4o'),
      system,
      prompt,
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error("AI stylist error:", error)
    return NextResponse.json({ error: "Failed to generate outfit recommendation" }, { status: 500 })
  }
}
