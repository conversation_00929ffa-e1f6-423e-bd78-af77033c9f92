import { NextRequest, NextResponse } from 'next/server'
import { schemaGeneratorService } from '@/lib/database/schema-generator-service'
import { DatabaseSchema } from '@/lib/posts/types'

// POST /api/database/validate - Validate schema
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const schema: DatabaseSchema = body.schema

    if (!schema) {
      return NextResponse.json(
        { success: false, error: 'Schema is required' },
        { status: 400 }
      )
    }

    // Validate schema
    const validationResult = schemaGeneratorService.validateSchema(schema)

    return NextResponse.json({
      success: true,
      data: validationResult,
      message: 'Schema validation completed'
    })

  } catch (error: any) {
    console.error('Error validating schema:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to validate schema' },
      { status: 500 }
    )
  }
}
