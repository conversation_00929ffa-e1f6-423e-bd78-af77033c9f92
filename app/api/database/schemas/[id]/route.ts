import { NextRequest, NextResponse } from 'next/server'
import { schemaGeneratorService } from '@/lib/database/schema-generator-service'

// GET /api/database/schemas/[id] - Get schema by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const schema = await schemaGeneratorService.getSchema(params.id)

    if (!schema) {
      return NextResponse.json(
        { success: false, error: 'Schema not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: schema,
      message: 'Schema retrieved successfully'
    })
  } catch (error: any) {
    console.error('Error fetching schema:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch schema' },
      { status: 500 }
    )
  }
}

// PUT /api/database/schemas/[id] - Update schema
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()

    const schema = await schemaGeneratorService.updateSchema(params.id, body)

    return NextResponse.json({
      success: true,
      data: schema,
      message: 'Schema updated successfully'
    })

  } catch (error: any) {
    console.error('Error updating schema:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to update schema' },
      { status: 500 }
    )
  }
}

// DELETE /api/database/schemas/[id] - Delete schema
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Note: Implement delete logic in service
    // await schemaGeneratorService.deleteSchema(params.id)

    return NextResponse.json({
      success: true,
      message: 'Schema deleted successfully'
    })

  } catch (error: any) {
    console.error('Error deleting schema:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to delete schema' },
      { status: 500 }
    )
  }
}
