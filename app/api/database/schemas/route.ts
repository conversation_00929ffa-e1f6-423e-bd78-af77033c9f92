import { NextRequest, NextResponse } from 'next/server'
import { schemaGeneratorService } from '@/lib/database/schema-generator-service'
import { DatabaseSchema } from '@/lib/posts/types'

// GET /api/database/schemas - Get all schemas
export async function GET(request: NextRequest) {
  try {
    const schemas = await schemaGeneratorService.getAllSchemas()

    return NextResponse.json({
      success: true,
      data: schemas,
      message: 'Schemas retrieved successfully'
    })
  } catch (error: any) {
    console.error('Error fetching schemas:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch schemas' },
      { status: 500 }
    )
  }
}

// POST /api/database/schemas - Create new schema
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const schemaData: Omit<DatabaseSchema, 'id' | 'createdAt' | 'updatedAt'> = body

    // Validate required fields
    if (!schemaData.name || !schemaData.version) {
      return NextResponse.json(
        { success: false, error: 'Name and version are required' },
        { status: 400 }
      )
    }

    // Set default values
    const completeSchemaData = {
      ...schemaData,
      tables: schemaData.tables || [],
      views: schemaData.views || [],
      functions: schemaData.functions || [],
      triggers: schemaData.triggers || [],
      migrations: schemaData.migrations || [],
      metadata: schemaData.metadata || {
        version: schemaData.version,
        compatibility: [],
        features: [],
      },
    }

    const schema = await schemaGeneratorService.createSchema(completeSchemaData)

    return NextResponse.json({
      success: true,
      data: schema,
      message: 'Schema created successfully'
    })

  } catch (error: any) {
    console.error('Error creating schema:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to create schema' },
      { status: 500 }
    )
  }
}

// PUT /api/database/schemas - Update schema
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updates } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Schema ID is required' },
        { status: 400 }
      )
    }

    const schema = await schemaGeneratorService.updateSchema(id, updates)

    return NextResponse.json({
      success: true,
      data: schema,
      message: 'Schema updated successfully'
    })

  } catch (error: any) {
    console.error('Error updating schema:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to update schema' },
      { status: 500 }
    )
  }
}
