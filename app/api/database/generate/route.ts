import { NextRequest, NextResponse } from 'next/server'
import { schemaGeneratorService } from '@/lib/database/schema-generator-service'
import { DatabaseSchema, SchemaGenerationOptions } from '@/lib/posts/types'

// POST /api/database/generate - Generate code from schema
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { schema, options }: { schema: DatabaseSchema; options: SchemaGenerationOptions } = body

    if (!schema) {
      return NextResponse.json(
        { success: false, error: 'Schema is required' },
        { status: 400 }
      )
    }

    if (!options || !options.target) {
      return NextResponse.json(
        { success: false, error: 'Generation options with target are required' },
        { status: 400 }
      )
    }

    // Validate target
    const validTargets = ['prisma', 'sql', 'typescript', 'graphql']
    if (!validTargets.includes(options.target)) {
      return NextResponse.json(
        { success: false, error: `Invalid target. Must be one of: ${validTargets.join(', ')}` },
        { status: 400 }
      )
    }

    // Generate code
    const generatedCode = schemaGeneratorService.generateSchemaCode(schema, options)

    return NextResponse.json({
      success: true,
      data: {
        code: generatedCode,
        target: options.target,
        schema: schema.name,
        version: schema.version,
        generatedAt: new Date().toISOString()
      },
      message: `${options.target.toUpperCase()} code generated successfully`
    })

  } catch (error: any) {
    console.error('Error generating code:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to generate code' },
      { status: 500 }
    )
  }
}
