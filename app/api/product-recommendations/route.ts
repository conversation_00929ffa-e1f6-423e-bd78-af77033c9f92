import { openai } from "@ai-sdk/openai"
import { streamText, tool } from "ai"
import { NextResponse } from "next/server"
import { getProducts } from "@/lib/products"
import { z } from "zod"

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

export async function POST(req: Request) {
  try {
    const { productId, browsingHistory, preferences } = await req.json()

    // Get all products
    const allProducts = await getProducts()

    // Get the current product
    const currentProduct = allProducts.find((p) => p.id === productId)

    if (!currentProduct) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 })
    }

    // Create a system prompt for recommendations
    const systemPrompt = `You are a product recommendation system for a children's clothing store.
    Based on the current product and user's browsing history, recommend similar or complementary products.
    Focus on style compatibility, color coordination, and age appropriateness.
    Use the provided tools to get product information and make recommendations.`

    // Create a detailed prompt with product information
    const prompt = `
    I'm looking at this product: ${currentProduct.name} (ID: ${currentProduct.id}).

    My browsing history includes: ${browsingHistory ? browsingHistory.map((id: string) => {
      const product = allProducts.find(p => p.id === id);
      return product ? product.name : id;
    }).join(", ") : "No browsing history"}.

    Can you recommend some complementary products that would go well with this item?`

    const result = streamText({
      model: openai("gpt-4o"),
      system: systemPrompt,
      prompt,
      maxSteps: 3,
      tools: {
        getAllProducts: tool({
          description: "Get all available products in the store",
          parameters: z.object({}),
          execute: async () => {
            return allProducts.map((p) => ({
              id: p.id,
              name: p.name,
              category: p.categoryId,
              colors: p.colors.map((c) => c.name),
              price: p.price,
              isNew: p.isNew,
              isSale: p.isSale,
            }))
          },
        }),
        getProductsByCategory: tool({
          description: "Get products filtered by category",
          parameters: z.object({
            category: z.string().describe("The category to filter by (e.g., tops, bottoms, dresses, outerwear, accessories)"),
          }),
          execute: async ({ category }) => {
            return allProducts
              .filter((p) => p.categoryId === category)
              .map((p) => ({
                id: p.id,
                name: p.name,
                category: p.categoryId,
                colors: p.colors.map((c) => c.name),
                price: p.price,
                isNew: p.isNew,
                isSale: p.isSale,
              }))
          },
        }),
        findComplementaryProducts: tool({
          description: "Find products that complement the current product",
          parameters: z.object({
            productId: z.string().describe("The ID of the product to find complementary items for"),
            count: z.number().min(1).max(6).default(4).describe("Number of complementary products to return"),
          }),
          execute: async ({ productId, count }) => {
            const product = allProducts.find((p) => p.id === productId)
            if (!product) return []

            // Simple logic to find complementary products
            // In a real app, this would use more sophisticated matching
            const complementaryCategories: Record<string, string[]> = {
              tops: ["bottoms", "accessories"],
              bottoms: ["tops", "accessories"],
              dresses: ["accessories", "outerwear"],
              outerwear: ["tops", "bottoms", "dresses"],
              accessories: ["tops", "bottoms", "dresses", "outerwear"],
            }

            const categories = complementaryCategories[product.categoryId] || []
            const complementary = allProducts
              .filter((p) => p.id !== productId && categories.includes(p.categoryId))
              .slice(0, count)

            return complementary.map((p) => ({
              id: p.id,
              name: p.name,
              category: p.categoryId,
              price: p.price,
            }))
          },
        }),
      },
    })

    // Extract the recommended products from the result
    const response = await result.toDataStreamResponse()

    // Return the response
    return response
  } catch (error) {
    console.error("Product recommendations error:", error)
    return NextResponse.json({ error: "Failed to generate product recommendations" }, { status: 500 })
  }
}
