import { NextRequest, NextResponse } from 'next/server'
import { ContentService } from '@/lib/cms/services/content-service'
import { HookSystem, CMS_HOOKS } from '@/lib/cms/plugins/hook-system'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const options = {
      postType: searchParams.get('postType') || undefined,
      status: searchParams.get('status') || undefined,
      authorId: searchParams.get('authorId') || undefined,
      parentId: searchParams.get('parentId') || undefined,
      search: searchParams.get('search') || undefined,
      limit: parseInt(searchParams.get('limit') || '20'),
      offset: parseInt(searchParams.get('offset') || '0'),
      orderBy: (searchParams.get('orderBy') as any) || 'updatedAt',
      orderDirection: (searchParams.get('orderDirection') as any) || 'desc'
    }

    const result = await ContentService.getContentList(options)

    // Apply content filter hook
    const filteredContent = await Promise.all(
      result.content.map(content => 
        HookSystem.applyFilters(CMS_HOOKS.CONTENT_FILTER, content)
      )
    )

    return NextResponse.json({
      success: true,
      content: filteredContent,
      total: result.total,
      pagination: {
        limit: options.limit,
        offset: options.offset,
        hasMore: options.offset + options.limit < result.total
      }
    })
  } catch (error) {
    console.error('Get content list error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch content' 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.title || !body.postType) {
      return NextResponse.json(
        { success: false, error: 'Title and post type are required' },
        { status: 400 }
      )
    }

    // Create content
    const content = await ContentService.createContent(body)

    // Execute content created hook
    await HookSystem.doAction(CMS_HOOKS.CONTENT_CREATED, content)

    return NextResponse.json({
      success: true,
      content
    }, { status: 201 })
  } catch (error) {
    console.error('Create content error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to create content' 
      },
      { status: 500 }
    )
  }
}
