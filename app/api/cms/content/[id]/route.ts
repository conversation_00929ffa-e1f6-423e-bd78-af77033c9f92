import { NextRequest, NextResponse } from 'next/server'
import { ContentService } from '@/lib/cms/services/content-service'
import { HookSystem, CMS_HOOKS } from '@/lib/cms/plugins/hook-system'

interface RouteParams {
  params: {
    id: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    
    const content = await ContentService.getContent(id)
    
    if (!content) {
      return NextResponse.json(
        { success: false, error: 'Content not found' },
        { status: 404 }
      )
    }

    // Apply content filter hook
    const filteredContent = await HookSystem.applyFilters(CMS_HOOKS.CONTENT_FILTER, content)

    return NextResponse.json({
      success: true,
      content: filteredContent
    })
  } catch (error) {
    console.error('Get content error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch content' 
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    const body = await request.json()
    
    // Check if content exists
    const existingContent = await ContentService.getContent(id)
    if (!existingContent) {
      return NextResponse.json(
        { success: false, error: 'Content not found' },
        { status: 404 }
      )
    }

    // Update content
    const content = await ContentService.updateContent(id, body)

    // Execute content updated hook
    await HookSystem.doAction(CMS_HOOKS.CONTENT_UPDATED, content, existingContent)

    return NextResponse.json({
      success: true,
      content
    })
  } catch (error) {
    console.error('Update content error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to update content' 
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    
    // Check if content exists
    const existingContent = await ContentService.getContent(id)
    if (!existingContent) {
      return NextResponse.json(
        { success: false, error: 'Content not found' },
        { status: 404 }
      )
    }

    // Delete content
    await ContentService.deleteContent(id)

    // Execute content deleted hook
    await HookSystem.doAction(CMS_HOOKS.CONTENT_DELETED, existingContent)

    return NextResponse.json({
      success: true,
      message: 'Content deleted successfully'
    })
  } catch (error) {
    console.error('Delete content error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to delete content' 
      },
      { status: 500 }
    )
  }
}
