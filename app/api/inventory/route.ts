import { NextRequest, NextResponse } from 'next/server'
import { wordpressAuth } from '@/lib/wordpress'
import { rateLimit } from '@/lib/rate-limit'
import type { InventoryUpdateRequest, BulkStockUpdateRequest } from '@/lib/inventory/types'

// Lazy import function to avoid circular dependencies
let inventoryManagerInstance: any = null
const getInventoryManager = async () => {
  if (!inventoryManagerInstance) {
    try {
      const { inventoryManager } = await import('@/lib/inventory/manager')
      inventoryManagerInstance = inventoryManager
    } catch (error) {
      console.error('Failed to import inventory manager', error)
      // Return placeholder if import fails
      inventoryManagerInstance = {
        getAllInventoryItems: async () => [],
        updateStock: async (_request: any, _userId: string) => {},
        bulkUpdateStock: async (_request: any, _userId: string) => {}
      }
    }
  }
  return inventoryManagerInstance
}

// Simple logger for inventory API
const logger = {
  error: (message: string, meta?: any) => {
    console.error(`[Inventory API Error] ${message}`, meta)
  }
}

// Rate limiting
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500,
})

export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 20, 'INVENTORY_API')
    } catch {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Check authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const isValidToken = await wordpressAuth.validateToken(token)
    
    if (!isValidToken) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const productId = searchParams.get('productId')

    const inventoryManager = await getInventoryManager()

    if (productId) {
      // Get specific inventory item
      const item = await inventoryManager.getInventoryItem(productId)
      if (!item) {
        return NextResponse.json(
          { success: false, error: 'Inventory item not found' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        data: item,
      })
    } else {
      // Get all inventory items
      const items = await inventoryManager.getAllInventoryItems()

      return NextResponse.json({
        success: true,
        data: items,
        total: items.length,
      })
    }

  } catch (error) {
    logger.error('Inventory API GET error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    })

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 10, 'INVENTORY_UPDATE')
    } catch {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Check authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const isValidToken = await wordpressAuth.validateToken(token)
    
    if (!isValidToken) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Get current user
    const currentUser = await wordpressAuth.getCurrentUser(token)
    
    // Check if user has permission to update inventory
    if (!wordpressAuth.hasRole(currentUser, 'administrator') && 
        !wordpressAuth.hasRole(currentUser, 'shop_manager')) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'update_stock':
        return await handleStockUpdate(body, currentUser.id.toString())
      
      case 'bulk_update':
        return await handleBulkUpdate(body, currentUser.id.toString())
      
      case 'reserve_stock':
        return await handleReserveStock(body)
      
      case 'release_stock':
        return await handleReleaseStock(body)
      
      case 'fulfill_order':
        return await handleFulfillOrder(body)
      
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    logger.error('Inventory API POST error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    })

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function handleStockUpdate(body: any, userId: string) {
  const { productId, quantity, type, reason, notes, referenceType, referenceId } = body

  if (!productId || typeof quantity !== 'number' || !type || !reason) {
    return NextResponse.json(
      { success: false, error: 'Missing required fields: productId, quantity, type, reason' },
      { status: 400 }
    )
  }

  const updateRequest: InventoryUpdateRequest = {
    productId,
    quantity,
    type,
    reason,
    notes,
    referenceType,
    referenceId,
  }

  try {
    const inventoryManager = await getInventoryManager()
    await inventoryManager.updateStock(updateRequest, userId)

    return NextResponse.json({
      success: true,
      message: 'Stock updated successfully',
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update stock'
      },
      { status: 400 }
    )
  }
}

async function handleBulkUpdate(body: any, userId: string) {
  const { updates, batchId } = body

  if (!updates || !Array.isArray(updates) || updates.length === 0) {
    return NextResponse.json(
      { success: false, error: 'Updates array is required' },
      { status: 400 }
    )
  }

  const bulkRequest: BulkStockUpdateRequest = {
    updates,
    batchId,
  }

  try {
    const inventoryManager = await getInventoryManager()
    await inventoryManager.bulkUpdateStock(bulkRequest, userId)

    return NextResponse.json({
      success: true,
      message: 'Bulk stock update completed successfully',
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update stock'
      },
      { status: 400 }
    )
  }
}

async function handleReserveStock(body: any) {
  const { productId, quantity, orderId } = body

  if (!productId || typeof quantity !== 'number' || !orderId) {
    return NextResponse.json(
      { success: false, error: 'Missing required fields: productId, quantity, orderId' },
      { status: 400 }
    )
  }

  try {
    const inventoryManager = await getInventoryManager()
    await inventoryManager.reserveStock(productId, quantity, orderId)

    return NextResponse.json({
      success: true,
      message: 'Stock reserved successfully',
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to reserve stock'
      },
      { status: 400 }
    )
  }
}

async function handleReleaseStock(body: any) {
  const { productId, quantity, orderId } = body

  if (!productId || typeof quantity !== 'number' || !orderId) {
    return NextResponse.json(
      { success: false, error: 'Missing required fields: productId, quantity, orderId' },
      { status: 400 }
    )
  }

  try {
    const inventoryManager = await getInventoryManager()
    await inventoryManager.releaseReservedStock(productId, quantity, orderId)

    return NextResponse.json({
      success: true,
      message: 'Reserved stock released successfully',
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to release stock'
      },
      { status: 400 }
    )
  }
}

async function handleFulfillOrder(body: any) {
  const { productId, quantity, orderId } = body

  if (!productId || typeof quantity !== 'number' || !orderId) {
    return NextResponse.json(
      { success: false, error: 'Missing required fields: productId, quantity, orderId' },
      { status: 400 }
    )
  }

  try {
    const inventoryManager = await getInventoryManager()
    await inventoryManager.fulfillOrder(productId, quantity, orderId)

    return NextResponse.json({
      success: true,
      message: 'Order fulfilled successfully',
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fulfill order'
      },
      { status: 400 }
    )
  }
}
