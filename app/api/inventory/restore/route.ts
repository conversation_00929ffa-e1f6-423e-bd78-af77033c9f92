// Inventory Restore API Route
// POST /api/inventory/restore - Restore inventory (for returns, cancellations)

import { NextRequest, NextResponse } from 'next/server'
import { InventoryService } from '@/lib/ecommerce/services/inventory-service'

const inventoryService = new InventoryService()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { productId, variantId, quantity, orderId, reason } = body

    if (!productId || typeof quantity !== 'number' || quantity <= 0) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: productId, quantity' },
        { status: 400 }
      )
    }

    // Restore inventory by adjusting quantity
    const result = await inventoryService.adjustInventory(
      productId,
      variantId,
      quantity, // Positive adjustment to restore
      reason || 'Inventory restored',
      orderId
    )

    if (result) {
      return NextResponse.json({
        success: true,
        restoredQuantity: quantity,
        newQuantity: result.quantity,
        newAvailable: result.available,
        message: 'Inventory restored successfully'
      })
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to restore inventory' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Inventory restore error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to restore inventory' },
      { status: 500 }
    )
  }
}
