import { NextRequest, NextResponse } from 'next/server'
import { rateLimit } from '@/lib/rate-limit'
import type { StockCheckRequest } from '@/lib/inventory/types'

// Lazy import function to avoid circular dependencies
let inventoryManagerInstance: any = null
const getInventoryManager = async () => {
  if (!inventoryManagerInstance) {
    try {
      const { inventoryManager } = await import('@/lib/inventory/manager')
      inventoryManagerInstance = inventoryManager
    } catch (error) {
      console.error('Failed to import inventory manager', error)
      // Return placeholder if import fails
      inventoryManagerInstance = {
        checkStock: async (_request: any) => ({ success: false, data: [] })
      }
    }
  }
  return inventoryManagerInstance
}

// Simple logger for stock check API
const logger = {
  info: (message: string, meta?: any) => {
    console.log(`[Stock Check API] ${message}`, meta)
  },
  error: (message: string, meta?: any) => {
    console.error(`[Stock Check API Error] ${message}`, meta)
  }
}

// Rate limiting
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500,
})

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 30, 'STOCK_CHECK')
    } catch {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const body = await request.json()
    const { productIds, includeReserved } = body

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Product IDs array is required' },
        { status: 400 }
      )
    }

    if (productIds.length > 100) {
      return NextResponse.json(
        { success: false, error: 'Maximum 100 products can be checked at once' },
        { status: 400 }
      )
    }

    const stockCheckRequest: StockCheckRequest = {
      productIds,
      includeReserved: includeReserved || false,
    }

    const inventoryManager = await getInventoryManager()
    const response = await inventoryManager.checkStock(stockCheckRequest)

    logger.info('Stock check performed', {
      productCount: productIds.length,
      includeReserved,
      success: response.success,
    })

    return NextResponse.json(response)

  } catch (error) {
    logger.error('Stock check API error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    })

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 30, 'STOCK_CHECK')
    } catch {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const { searchParams } = new URL(request.url)
    const productIdsParam = searchParams.get('productIds')
    const includeReserved = searchParams.get('includeReserved') === 'true'

    if (!productIdsParam) {
      return NextResponse.json(
        { success: false, error: 'productIds parameter is required' },
        { status: 400 }
      )
    }

    const productIds = productIdsParam.split(',').filter(id => id.trim())

    if (productIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'At least one product ID is required' },
        { status: 400 }
      )
    }

    if (productIds.length > 100) {
      return NextResponse.json(
        { success: false, error: 'Maximum 100 products can be checked at once' },
        { status: 400 }
      )
    }

    const stockCheckRequest: StockCheckRequest = {
      productIds,
      includeReserved,
    }

    const inventoryManager = await getInventoryManager()
    const response = await inventoryManager.checkStock(stockCheckRequest)

    logger.info('Stock check performed via GET', {
      productCount: productIds.length,
      includeReserved,
      success: response.success,
    })

    return NextResponse.json(response)

  } catch (error) {
    logger.error('Stock check GET API error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    })

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
