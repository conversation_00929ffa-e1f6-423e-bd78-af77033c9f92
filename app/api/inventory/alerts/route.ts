import { NextRequest, NextResponse } from 'next/server'
import { wordpressAuth } from '@/lib/wordpress'
import { rateLimit } from '@/lib/rate-limit'

// Lazy import function to avoid circular dependencies
let inventoryManagerInstance: any = null
const getInventoryManager = async () => {
  if (!inventoryManagerInstance) {
    try {
      const { inventoryManager } = await import('@/lib/inventory/manager')
      inventoryManagerInstance = inventoryManager
    } catch (error) {
      console.error('Failed to import inventory manager', error)
      // Return placeholder if import fails
      inventoryManagerInstance = {
        getActiveAlerts: async () => [],
        acknowledgeAlert: async (_alertId: string, _userId: string) => {}
      }
    }
  }
  return inventoryManagerInstance
}

// Simple logger for inventory alerts API
const logger = {
  info: (message: string, meta?: any) => {
    console.log(`[Inventory Alerts API] ${message}`, meta)
  },
  error: (message: string, meta?: any) => {
    console.error(`[Inventory Alerts API Error] ${message}`, meta)
  }
}

// Rate limiting
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500,
})

export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 20, 'INVENTORY_ALERTS')
    } catch {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Check authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const isValidToken = await wordpressAuth.validateToken(token)
    
    if (!isValidToken) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Get current user
    const currentUser = await wordpressAuth.getCurrentUser(token)
    
    // Check if user has permission to view inventory alerts
    if (!wordpressAuth.hasRole(currentUser, 'administrator') && 
        !wordpressAuth.hasRole(currentUser, 'shop_manager')) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const inventoryManager = await getInventoryManager()
    const alerts = await inventoryManager.getActiveAlerts()

    // Group alerts by severity
    const groupedAlerts = {
      critical: alerts.filter((alert: any) => alert.severity === 'critical'),
      high: alerts.filter((alert: any) => alert.severity === 'high'),
      medium: alerts.filter((alert: any) => alert.severity === 'medium'),
      low: alerts.filter((alert: any) => alert.severity === 'low'),
    }

    const summary = {
      total: alerts.length,
      critical: groupedAlerts.critical.length,
      high: groupedAlerts.high.length,
      medium: groupedAlerts.medium.length,
      low: groupedAlerts.low.length,
    }

    logger.info('Inventory alerts retrieved', {
      userId: currentUser.id,
      totalAlerts: alerts.length,
      summary,
    })

    return NextResponse.json({
      success: true,
      data: {
        alerts,
        grouped: groupedAlerts,
        summary,
      },
    })

  } catch (error) {
    logger.error('Inventory alerts API error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    })

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 10, 'INVENTORY_ALERTS_UPDATE')
    } catch {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Check authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const isValidToken = await wordpressAuth.validateToken(token)
    
    if (!isValidToken) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Get current user
    const currentUser = await wordpressAuth.getCurrentUser(token)
    
    // Check if user has permission to manage inventory alerts
    if (!wordpressAuth.hasRole(currentUser, 'administrator') && 
        !wordpressAuth.hasRole(currentUser, 'shop_manager')) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { action, alertId, alertIds } = body

    const inventoryMgr = await getInventoryManager()

    switch (action) {
      case 'acknowledge':
        if (!alertId) {
          return NextResponse.json(
            { success: false, error: 'Alert ID is required' },
            { status: 400 }
          )
        }

        await inventoryMgr.acknowledgeAlert(alertId, currentUser.id.toString())
        
        logger.info('Alert acknowledged', {
          alertId,
          userId: currentUser.id,
        })

        return NextResponse.json({
          success: true,
          message: 'Alert acknowledged successfully',
        })

      case 'acknowledge_multiple':
        if (!alertIds || !Array.isArray(alertIds) || alertIds.length === 0) {
          return NextResponse.json(
            { success: false, error: 'Alert IDs array is required' },
            { status: 400 }
          )
        }

        for (const id of alertIds) {
          await inventoryMgr.acknowledgeAlert(id, currentUser.id.toString())
        }

        logger.info('Multiple alerts acknowledged', {
          alertIds,
          count: alertIds.length,
          userId: currentUser.id,
        })

        return NextResponse.json({
          success: true,
          message: `${alertIds.length} alerts acknowledged successfully`,
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    logger.error('Inventory alerts POST API error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    })

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
