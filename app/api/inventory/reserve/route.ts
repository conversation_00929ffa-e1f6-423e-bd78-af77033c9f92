// Inventory Reservation API Route
// POST /api/inventory/reserve - Reserve inventory for fulfillment

import { NextRequest, NextResponse } from 'next/server'
import { InventoryService } from '@/lib/ecommerce/services/inventory-service'

const inventoryService = new InventoryService()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { productId, variantId, quantity, orderId, reason } = body

    if (!productId || typeof quantity !== 'number' || quantity <= 0 || !orderId) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: productId, quantity, orderId' },
        { status: 400 }
      )
    }

    // Reserve inventory
    const success = await inventoryService.reserveInventory(
      productId,
      variantId,
      quantity,
      orderId,
      30 // 30 minutes expiration
    )

    if (success) {
      return NextResponse.json({
        success: true,
        reservationId: `${orderId}-${productId}-${Date.now()}`,
        allocatedQuantity: quantity,
        expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
        message: 'Inventory reserved successfully'
      })
    } else {
      // Check current availability for error details
      const inventory = await inventoryService.getInventory(productId, variantId)
      
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to reserve inventory',
          availableQuantity: inventory?.available || 0,
          requestedQuantity: quantity
        },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Inventory reservation error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to reserve inventory' },
      { status: 500 }
    )
  }
}
