// Inventory Check API Route
// POST /api/inventory/check - Check inventory availability

import { NextRequest, NextResponse } from 'next/server'
import { InventoryService } from '@/lib/ecommerce/services/inventory-service'

const inventoryService = new InventoryService()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { productId, variantId, quantity } = body

    if (!productId || typeof quantity !== 'number' || quantity <= 0) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: productId, quantity' },
        { status: 400 }
      )
    }

    // Check inventory availability
    const inventory = await inventoryService.getInventory(productId, variantId)
    
    if (!inventory) {
      return NextResponse.json({
        success: false,
        available: false,
        availableQuantity: 0,
        error: 'Product not found in inventory'
      })
    }

    const available = inventory.available >= quantity
    const recommendedStock = inventory.reorderLevel

    return NextResponse.json({
      success: true,
      available,
      availableQuantity: inventory.available,
      totalQuantity: inventory.quantity,
      reservedQuantity: inventory.reserved,
      requestedQuantity: quantity,
      recommendedStock,
      lowStock: inventory.quantity <= recommendedStock,
      outOfStock: inventory.available === 0
    })

  } catch (error) {
    console.error('Inventory check error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to check inventory' },
      { status: 500 }
    )
  }
}
