// WordPress-Style Taxonomy Terms API Routes
// RESTful API endpoints for managing taxonomy terms

import { NextRequest, NextResponse } from 'next/server'
import { TaxonomyService } from '@/lib/posts/services/taxonomy-service'
import { CreateTaxonomyTermInput } from '@/lib/posts/types'

const taxonomyService = new TaxonomyService()

/**
 * GET /api/taxonomies/[name]/terms - Get terms for a taxonomy
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { name: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const parentId = searchParams.get('parentId') || undefined
    const includeChildren = searchParams.get('includeChildren') !== 'false'
    
    const result = await taxonomyService.getTerms(
      params.name, 
      parentId, 
      includeChildren
    )
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Taxonomy not found' ? 404 : 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data
    })

  } catch (error) {
    console.error('Error in GET /api/taxonomies/[name]/terms:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/taxonomies/[name]/terms - Create a new term
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { name: string } }
) {
  try {
    const body = await request.json()
    
    // Get taxonomy first to validate it exists
    const taxonomyResult = await taxonomyService.getTaxonomy(params.name)
    if (!taxonomyResult.success || !taxonomyResult.data) {
      return NextResponse.json(
        { success: false, error: 'Taxonomy not found' },
        { status: 404 }
      )
    }

    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { success: false, error: 'Name is required' },
        { status: 400 }
      )
    }

    const input: CreateTaxonomyTermInput = {
      name: body.name,
      slug: body.slug,
      description: body.description,
      taxonomyId: taxonomyResult.data.id,
      parentId: body.parentId,
      color: body.color,
      image: body.image,
      metadata: body.metadata,
    }

    const result = await taxonomyService.createTerm(input)
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: result.message
    }, { status: 201 })

  } catch (error) {
    console.error('Error in POST /api/taxonomies/[name]/terms:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
