// WordPress-Style Taxonomies API Routes
// RESTful API endpoints for managing taxonomies

import { NextRequest, NextResponse } from 'next/server'
import { TaxonomyService } from '@/lib/posts/services/taxonomy-service'
import { CreateTaxonomyInput } from '@/lib/posts/types'

const taxonomyService = new TaxonomyService()

/**
 * GET /api/taxonomies - Get all taxonomies
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeInactive = searchParams.get('includeInactive') === 'true'
    const publicOnly = searchParams.get('public') === 'true'
    
    let result
    if (publicOnly) {
      // Filter for public taxonomies only
      const allResult = await taxonomyService.getTaxonomies(includeInactive)
      if (allResult.success && allResult.data) {
        result = {
          ...allResult,
          data: allResult.data.filter(t => t.isPublic)
        }
      } else {
        result = allResult
      }
    } else {
      result = await taxonomyService.getTaxonomies(includeInactive)
    }
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data
    })

  } catch (error) {
    console.error('Error in GET /api/taxonomies:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/taxonomies - Register a new taxonomy
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.name || !body.label || !body.labelPlural) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Name, label, and labelPlural are required' 
        },
        { status: 400 }
      )
    }

    const input: CreateTaxonomyInput = {
      name: body.name,
      label: body.label,
      labelPlural: body.labelPlural,
      description: body.description,
      isHierarchical: body.isHierarchical,
      isPublic: body.isPublic,
      showInMenu: body.showInMenu,
      showInRest: body.showInRest,
      postTypes: body.postTypes,
      capabilities: body.capabilities,
      metaBoxCallback: body.metaBoxCallback,
    }

    const result = await taxonomyService.registerTaxonomy(input)
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: result.message
    }, { status: 201 })

  } catch (error) {
    console.error('Error in POST /api/taxonomies:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/taxonomies - Initialize default taxonomies
 */
export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    
    if (action === 'initialize') {
      const result = await taxonomyService.initializeDefaultTaxonomies()
      
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.error },
          { status: 400 }
        )
      }

      return NextResponse.json({
        success: true,
        message: result.message
      })
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Error in PUT /api/taxonomies:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
