import { NextRequest, NextResponse } from 'next/server'
import { rolePermissionService } from '@/lib/auth/role-permission-service'

// GET /api/auth/roles/[id] - Get role by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const role = await rolePermissionService.getRole(params.id)

    if (!role) {
      return NextResponse.json(
        { success: false, error: 'Role not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: role,
      message: 'Role retrieved successfully'
    })
  } catch (error: any) {
    console.error('Error fetching role:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch role' },
      { status: 500 }
    )
  }
}

// PUT /api/auth/roles/[id] - Update role
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()

    // Validate slug format if being updated
    if (body.slug && !/^[a-z0-9-]+$/.test(body.slug)) {
      return NextResponse.json(
        { success: false, error: 'Slug must contain only lowercase letters, numbers, and hyphens' },
        { status: 400 }
      )
    }

    const role = await rolePermissionService.updateRole(params.id, body)

    return NextResponse.json({
      success: true,
      data: role,
      message: 'Role updated successfully'
    })

  } catch (error: any) {
    console.error('Error updating role:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to update role' },
      { status: 500 }
    )
  }
}

// DELETE /api/auth/roles/[id] - Delete role
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await rolePermissionService.deleteRole(params.id)

    return NextResponse.json({
      success: true,
      message: 'Role deleted successfully'
    })

  } catch (error: any) {
    console.error('Error deleting role:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to delete role' },
      { status: 500 }
    )
  }
}
