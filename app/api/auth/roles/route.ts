import { NextRequest, NextResponse } from 'next/server'
import { rolePermissionService } from '@/lib/auth/role-permission-service'
import { Role } from '@/lib/posts/types'

// GET /api/auth/roles - Get all roles
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeInactive = searchParams.get('includeInactive') === 'true'
    
    const roles = await rolePermissionService.getAllRoles(includeInactive)

    return NextResponse.json({
      success: true,
      data: roles,
      message: 'Roles retrieved successfully'
    })
  } catch (error: any) {
    console.error('Error fetching roles:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch roles' },
      { status: 500 }
    )
  }
}

// POST /api/auth/roles - Create new role
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const roleData: Omit<Role, 'id' | 'createdAt' | 'updatedAt'> = body

    // Validate required fields
    if (!roleData.name || !roleData.slug) {
      return NextResponse.json(
        { success: false, error: 'Name and slug are required' },
        { status: 400 }
      )
    }

    // Validate slug format
    if (!/^[a-z0-9-]+$/.test(roleData.slug)) {
      return NextResponse.json(
        { success: false, error: 'Slug must contain only lowercase letters, numbers, and hyphens' },
        { status: 400 }
      )
    }

    // Set default values
    const completeRoleData = {
      ...roleData,
      isSystem: false,
      isActive: roleData.isActive ?? true,
      level: roleData.level || 10,
      permissions: roleData.permissions || [],
      capabilities: roleData.capabilities || {
        accessAdmin: false,
        manageUsers: false,
        manageRoles: false,
        manageSettings: false,
        viewAnalytics: false,
        managePlugins: false,
        manageThemes: false,
        manageBackups: false,
        createContent: false,
        editOwnContent: false,
        editOthersContent: false,
        deleteOwnContent: false,
        deleteOthersContent: false,
        publishContent: false,
        moderateComments: false,
        uploadMedia: false,
        editMedia: false,
        deleteMedia: false,
        editCode: false,
        manageDatabase: false,
        viewLogs: false,
        exportData: false,
        importData: false,
      },
      contentTypePermissions: roleData.contentTypePermissions || {},
      restrictions: roleData.restrictions || {},
    }

    const role = await rolePermissionService.createRole(completeRoleData)

    return NextResponse.json({
      success: true,
      data: role,
      message: 'Role created successfully'
    })

  } catch (error: any) {
    console.error('Error creating role:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to create role' },
      { status: 500 }
    )
  }
}

// PUT /api/auth/roles - Update role
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updates } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Role ID is required' },
        { status: 400 }
      )
    }

    // Validate slug format if being updated
    if (updates.slug && !/^[a-z0-9-]+$/.test(updates.slug)) {
      return NextResponse.json(
        { success: false, error: 'Slug must contain only lowercase letters, numbers, and hyphens' },
        { status: 400 }
      )
    }

    const role = await rolePermissionService.updateRole(id, updates)

    return NextResponse.json({
      success: true,
      data: role,
      message: 'Role updated successfully'
    })

  } catch (error: any) {
    console.error('Error updating role:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to update role' },
      { status: 500 }
    )
  }
}
