import { NextRequest, NextResponse } from 'next/server'
import { rolePermissionService } from '@/lib/auth/role-permission-service'
import { PermissionCheck } from '@/lib/posts/types'

// POST /api/auth/permissions/check - Check user permission
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const permissionCheck: PermissionCheck = body

    if (!permissionCheck.resource || !permissionCheck.action) {
      return NextResponse.json(
        { success: false, error: 'Resource and action are required' },
        { status: 400 }
      )
    }

    if (!permissionCheck.userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      )
    }

    const result = await rolePermissionService.checkPermission(permissionCheck)

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Permission check completed'
    })

  } catch (error: any) {
    console.error('Error checking permission:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to check permission' },
      { status: 500 }
    )
  }
}

// GET /api/auth/permissions/check - Check user permission via query params
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const resource = searchParams.get('resource')
    const action = searchParams.get('action')
    const userId = searchParams.get('userId')
    const resourceId = searchParams.get('resourceId')

    if (!resource || !action || !userId) {
      return NextResponse.json(
        { success: false, error: 'Resource, action, and userId are required' },
        { status: 400 }
      )
    }

    const permissionCheck: PermissionCheck = {
      resource,
      action: action as any,
      userId,
      resourceId: resourceId || undefined,
      context: {}
    }

    const result = await rolePermissionService.checkPermission(permissionCheck)

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Permission check completed'
    })

  } catch (error: any) {
    console.error('Error checking permission:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to check permission' },
      { status: 500 }
    )
  }
}
