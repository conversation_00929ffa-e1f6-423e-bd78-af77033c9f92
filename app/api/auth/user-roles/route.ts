import { NextRequest, NextResponse } from 'next/server'
import { rolePermissionService } from '@/lib/auth/role-permission-service'

// GET /api/auth/user-roles - Get user roles
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      )
    }

    const userRoles = await rolePermissionService.getUserRoles(userId)

    return NextResponse.json({
      success: true,
      data: userRoles,
      message: 'User roles retrieved successfully'
    })
  } catch (error: any) {
    console.error('Error fetching user roles:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch user roles' },
      { status: 500 }
    )
  }
}

// POST /api/auth/user-roles - Assign role to user
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, roleId, assignedBy, expiresAt } = body

    if (!userId || !roleId || !assignedBy) {
      return NextResponse.json(
        { success: false, error: 'userId, roleId, and assignedBy are required' },
        { status: 400 }
      )
    }

    const userRole = await rolePermissionService.assignRole(
      userId,
      roleId,
      assignedBy,
      expiresAt ? new Date(expiresAt) : undefined
    )

    return NextResponse.json({
      success: true,
      data: userRole,
      message: 'Role assigned successfully'
    })

  } catch (error: any) {
    console.error('Error assigning role:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to assign role' },
      { status: 500 }
    )
  }
}

// DELETE /api/auth/user-roles - Remove role from user
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const roleId = searchParams.get('roleId')

    if (!userId || !roleId) {
      return NextResponse.json(
        { success: false, error: 'userId and roleId are required' },
        { status: 400 }
      )
    }

    await rolePermissionService.removeRole(userId, roleId)

    return NextResponse.json({
      success: true,
      message: 'Role removed successfully'
    })

  } catch (error: any) {
    console.error('Error removing role:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to remove role' },
      { status: 500 }
    )
  }
}
