import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { ThemeApplication } from '@/lib/theme-generator/types'

const prisma = new PrismaClient()

// Get theme applications
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const scope = searchParams.get('scope')
    const targetId = searchParams.get('targetId')
    const themeId = searchParams.get('themeId')
    const isActive = searchParams.get('isActive')

    const where: any = {}
    
    if (scope) where.scope = scope
    if (targetId) where.targetId = targetId
    if (themeId) where.themeId = themeId
    if (isActive !== null) where.isActive = isActive === 'true'

    const applications = await prisma.themeApplication.findMany({
      where,
      include: {
        theme: {
          select: {
            id: true,
            name: true,
            category: true,
            preview: true
          }
        }
      },
      orderBy: { appliedAt: 'desc' }
    })

    return NextResponse.json({
      success: true,
      data: applications
    })

  } catch (error) {
    console.error('Error fetching theme applications:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch theme applications' },
      { status: 500 }
    )
  }
}

// Apply theme to scope
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      scope,
      targetId,
      themeId,
      customizations,
      appliedBy = 'system'
    } = body

    // Validate required fields
    if (!scope || !themeId) {
      return NextResponse.json(
        { success: false, error: 'Scope and theme ID are required' },
        { status: 400 }
      )
    }

    // Validate scope
    const validScopes = ['global', 'page', 'block', 'component']
    if (!validScopes.includes(scope)) {
      return NextResponse.json(
        { success: false, error: 'Invalid scope' },
        { status: 400 }
      )
    }

    // Check if theme exists
    const theme = await prisma.theme.findUnique({
      where: { id: themeId }
    })

    if (!theme) {
      return NextResponse.json(
        { success: false, error: 'Theme not found' },
        { status: 404 }
      )
    }

    // For global scope, deactivate other global applications
    if (scope === 'global') {
      await prisma.themeApplication.updateMany({
        where: {
          scope: 'global',
          isActive: true
        },
        data: { isActive: false }
      })
    }

    // For specific target, deactivate other applications for the same target
    if (targetId) {
      await prisma.themeApplication.updateMany({
        where: {
          scope,
          targetId,
          isActive: true
        },
        data: { isActive: false }
      })
    }

    // Create new application
    const application = await prisma.themeApplication.create({
      data: {
        scope,
        targetId,
        themeId,
        customizations: customizations ? JSON.stringify(customizations) : null,
        appliedBy,
        isActive: true
      },
      include: {
        theme: {
          select: {
            id: true,
            name: true,
            category: true,
            preview: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: application,
      message: 'Theme applied successfully'
    })

  } catch (error) {
    console.error('Error applying theme:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to apply theme' },
      { status: 500 }
    )
  }
}

// Update theme application
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      customizations,
      isActive,
      updatedBy = 'system'
    } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Application ID is required' },
        { status: 400 }
      )
    }

    const application = await prisma.themeApplication.findUnique({
      where: { id }
    })

    if (!application) {
      return NextResponse.json(
        { success: false, error: 'Theme application not found' },
        { status: 404 }
      )
    }

    const updatedApplication = await prisma.themeApplication.update({
      where: { id },
      data: {
        customizations: customizations ? JSON.stringify(customizations) : application.customizations,
        isActive: isActive !== undefined ? isActive : application.isActive,
        updatedBy
      },
      include: {
        theme: {
          select: {
            id: true,
            name: true,
            category: true,
            preview: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: updatedApplication,
      message: 'Theme application updated successfully'
    })

  } catch (error) {
    console.error('Error updating theme application:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update theme application' },
      { status: 500 }
    )
  }
}

// Remove theme application
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    const scope = searchParams.get('scope')
    const targetId = searchParams.get('targetId')
    const themeId = searchParams.get('themeId')

    if (id) {
      // Delete specific application by ID
      const application = await prisma.themeApplication.findUnique({
        where: { id }
      })

      if (!application) {
        return NextResponse.json(
          { success: false, error: 'Theme application not found' },
          { status: 404 }
        )
      }

      await prisma.themeApplication.delete({
        where: { id }
      })

      return NextResponse.json({
        success: true,
        message: 'Theme application removed successfully'
      })
    }

    // Delete by scope and target
    if (scope) {
      const where: any = { scope }
      if (targetId) where.targetId = targetId
      if (themeId) where.themeId = themeId

      const deletedCount = await prisma.themeApplication.deleteMany({
        where
      })

      return NextResponse.json({
        success: true,
        message: `${deletedCount.count} theme application(s) removed successfully`
      })
    }

    return NextResponse.json(
      { success: false, error: 'Application ID or scope is required' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Error removing theme application:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to remove theme application' },
      { status: 500 }
    )
  }
}

// Bulk operations on theme applications
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, applicationIds, data } = body

    if (!action || !applicationIds || !Array.isArray(applicationIds)) {
      return NextResponse.json(
        { success: false, error: 'Action and application IDs are required' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'activate':
        await prisma.themeApplication.updateMany({
          where: { id: { in: applicationIds } },
          data: { isActive: true }
        })
        break

      case 'deactivate':
        await prisma.themeApplication.updateMany({
          where: { id: { in: applicationIds } },
          data: { isActive: false }
        })
        break

      case 'delete':
        await prisma.themeApplication.deleteMany({
          where: { id: { in: applicationIds } }
        })
        break

      case 'update-customizations':
        if (!data.customizations) {
          return NextResponse.json(
            { success: false, error: 'Customizations data is required' },
            { status: 400 }
          )
        }

        await prisma.themeApplication.updateMany({
          where: { id: { in: applicationIds } },
          data: { 
            customizations: JSON.stringify(data.customizations),
            updatedBy: data.updatedBy || 'system'
          }
        })
        break

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: `Bulk ${action} completed successfully`
    })

  } catch (error) {
    console.error('Error in bulk theme application operation:', error)
    return NextResponse.json(
      { success: false, error: 'Bulk operation failed' },
      { status: 500 }
    )
  }
}
