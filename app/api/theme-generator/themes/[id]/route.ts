import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { ThemeConfig } from '@/lib/theme-generator/types'

const prisma = new PrismaClient()

interface RouteParams {
  params: { id: string }
}

// Get single theme
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params

    const theme = await prisma.theme.findUnique({
      where: { id },
      include: {
        applications: {
          where: { isActive: true },
          orderBy: { appliedAt: 'desc' }
        }
      }
    })

    if (!theme) {
      return NextResponse.json(
        { success: false, error: 'Theme not found' },
        { status: 404 }
      )
    }

    // Parse theme configuration
    const parsedTheme = {
      ...theme,
      config: JSON.parse(theme.config as string) as ThemeConfig
    }

    return NextResponse.json({
      success: true,
      data: parsedTheme
    })

  } catch (error) {
    console.error('Error fetching theme:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch theme' },
      { status: 500 }
    )
  }
}

// Update theme
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params
    const body = await request.json()

    // Check if theme exists
    const existingTheme = await prisma.theme.findUnique({
      where: { id }
    })

    if (!existingTheme) {
      return NextResponse.json(
        { success: false, error: 'Theme not found' },
        { status: 404 }
      )
    }

    // Prevent updating default themes unless explicitly allowed
    if (existingTheme.isDefault && !body.allowDefaultUpdate) {
      return NextResponse.json(
        { success: false, error: 'Cannot update default theme' },
        { status: 403 }
      )
    }

    const theme: ThemeConfig = body.config || body
    
    // Update theme
    const updatedTheme = await prisma.theme.update({
      where: { id },
      data: {
        name: theme.name || existingTheme.name,
        description: theme.description || existingTheme.description,
        version: theme.version || existingTheme.version,
        author: theme.author || existingTheme.author,
        category: theme.metadata?.category || existingTheme.category,
        tags: theme.metadata?.tags || existingTheme.tags,
        preview: theme.metadata?.preview || existingTheme.preview,
        config: JSON.stringify(theme),
        isActive: body.isActive !== undefined ? body.isActive : existingTheme.isActive,
        updatedBy: body.updatedBy || 'system'
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        ...updatedTheme,
        config: theme
      },
      message: 'Theme updated successfully'
    })

  } catch (error) {
    console.error('Error updating theme:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update theme' },
      { status: 500 }
    )
  }
}

// Delete theme
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params

    // Check if theme exists
    const existingTheme = await prisma.theme.findUnique({
      where: { id }
    })

    if (!existingTheme) {
      return NextResponse.json(
        { success: false, error: 'Theme not found' },
        { status: 404 }
      )
    }

    // Prevent deletion of default themes
    if (existingTheme.isDefault) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete default theme' },
        { status: 403 }
      )
    }

    // Check if theme is currently active
    if (existingTheme.isActive) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete active theme. Please activate another theme first.' },
        { status: 409 }
      )
    }

    // Delete theme applications first
    await prisma.themeApplication.deleteMany({
      where: { themeId: id }
    })

    // Delete theme
    await prisma.theme.delete({
      where: { id }
    })

    return NextResponse.json({
      success: true,
      message: 'Theme deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting theme:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete theme' },
      { status: 500 }
    )
  }
}

// Activate/Deactivate theme
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params
    const { action, ...data } = await request.json()

    const theme = await prisma.theme.findUnique({
      where: { id }
    })

    if (!theme) {
      return NextResponse.json(
        { success: false, error: 'Theme not found' },
        { status: 404 }
      )
    }

    switch (action) {
      case 'activate':
        // Deactivate all other themes first
        await prisma.theme.updateMany({
          data: { isActive: false }
        })
        
        // Activate this theme
        await prisma.theme.update({
          where: { id },
          data: { isActive: true }
        })
        break

      case 'deactivate':
        await prisma.theme.update({
          where: { id },
          data: { isActive: false }
        })
        break

      case 'duplicate':
        const originalConfig = JSON.parse(theme.config as string) as ThemeConfig
        const duplicatedConfig = {
          ...originalConfig,
          id: `${originalConfig.id}-copy-${Date.now()}`,
          name: `${originalConfig.name} (Copy)`,
          metadata: {
            ...originalConfig.metadata,
            isDefault: false,
            isActive: false,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        }

        const duplicatedTheme = await prisma.theme.create({
          data: {
            id: duplicatedConfig.id,
            name: duplicatedConfig.name,
            description: duplicatedConfig.description,
            version: duplicatedConfig.version,
            author: duplicatedConfig.author,
            category: duplicatedConfig.metadata.category,
            tags: duplicatedConfig.metadata.tags,
            preview: duplicatedConfig.metadata.preview,
            config: JSON.stringify(duplicatedConfig),
            isDefault: false,
            isActive: false,
            createdBy: data.createdBy || 'system',
            updatedBy: data.updatedBy || 'system'
          }
        })

        return NextResponse.json({
          success: true,
          data: {
            ...duplicatedTheme,
            config: duplicatedConfig
          },
          message: 'Theme duplicated successfully'
        })

      case 'set-default':
        // Remove default from all themes
        await prisma.theme.updateMany({
          data: { isDefault: false }
        })
        
        // Set this theme as default
        await prisma.theme.update({
          where: { id },
          data: { isDefault: true }
        })
        break

      case 'export':
        const exportConfig = JSON.parse(theme.config as string) as ThemeConfig
        return NextResponse.json({
          success: true,
          data: {
            theme: exportConfig,
            metadata: {
              exportedAt: new Date(),
              exportedBy: data.exportedBy || 'system',
              version: theme.version
            }
          },
          message: 'Theme exported successfully'
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: `Theme ${action} completed successfully`
    })

  } catch (error) {
    console.error('Error in theme action:', error)
    return NextResponse.json(
      { success: false, error: 'Theme action failed' },
      { status: 500 }
    )
  }
}
