import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { ThemeConfig, ThemeGeneratorOptions } from '@/lib/theme-generator/types'
import { ThemeGenerator } from '@/lib/theme-generator/theme-generator'

const prisma = new PrismaClient()

// Get all themes
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const limit = parseInt(searchParams.get('limit') || '50')
    const page = parseInt(searchParams.get('page') || '1')

    const where: any = {}
    
    if (category && category !== 'all') {
      where.category = category
    }
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { tags: { has: search } }
      ]
    }

    const [themes, total] = await Promise.all([
      prisma.theme.findMany({
        where,
        orderBy: [
          { isDefault: 'desc' },
          { isActive: 'desc' },
          { updatedAt: 'desc' }
        ],
        take: limit,
        skip: (page - 1) * limit
      }),
      prisma.theme.count({ where })
    ])

    // Parse theme configurations
    const parsedThemes = themes.map(theme => ({
      ...theme,
      config: JSON.parse(theme.config as string) as ThemeConfig
    }))

    return NextResponse.json({
      success: true,
      data: parsedThemes,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        current: page,
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Error fetching themes:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch themes' },
      { status: 500 }
    )
  }
}

// Create or update theme
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Check if it's a theme generation request
    if (body.generate) {
      const options: ThemeGeneratorOptions = body.options
      const generatedTheme = ThemeGenerator.generateTheme(options)
      
      // Save generated theme
      const savedTheme = await prisma.theme.create({
        data: {
          id: generatedTheme.id,
          name: generatedTheme.name,
          description: generatedTheme.description,
          version: generatedTheme.version,
          author: generatedTheme.author,
          category: generatedTheme.metadata.category,
          tags: generatedTheme.metadata.tags,
          preview: generatedTheme.metadata.preview,
          config: JSON.stringify(generatedTheme),
          isDefault: false,
          isActive: false,
          createdBy: body.createdBy || 'system',
          updatedBy: body.updatedBy || 'system'
        }
      })

      return NextResponse.json({
        success: true,
        data: {
          ...savedTheme,
          config: generatedTheme
        },
        message: 'Theme generated and saved successfully'
      })
    }

    // Regular theme save/update
    const theme: ThemeConfig = body
    
    // Validate theme structure
    if (!theme.id || !theme.name || !theme.colors) {
      return NextResponse.json(
        { success: false, error: 'Invalid theme structure' },
        { status: 400 }
      )
    }

    // Check if theme exists
    const existingTheme = await prisma.theme.findUnique({
      where: { id: theme.id }
    })

    let savedTheme
    if (existingTheme) {
      // Update existing theme
      savedTheme = await prisma.theme.update({
        where: { id: theme.id },
        data: {
          name: theme.name,
          description: theme.description,
          version: theme.version,
          author: theme.author,
          category: theme.metadata.category,
          tags: theme.metadata.tags,
          preview: theme.metadata.preview,
          config: JSON.stringify(theme),
          updatedBy: body.updatedBy || 'system'
        }
      })
    } else {
      // Create new theme
      savedTheme = await prisma.theme.create({
        data: {
          id: theme.id,
          name: theme.name,
          description: theme.description,
          version: theme.version,
          author: theme.author,
          category: theme.metadata.category,
          tags: theme.metadata.tags,
          preview: theme.metadata.preview,
          config: JSON.stringify(theme),
          isDefault: theme.metadata.isDefault,
          isActive: theme.metadata.isActive,
          createdBy: body.createdBy || 'system',
          updatedBy: body.updatedBy || 'system'
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        ...savedTheme,
        config: theme
      },
      message: existingTheme ? 'Theme updated successfully' : 'Theme created successfully'
    })

  } catch (error) {
    console.error('Error saving theme:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to save theme' },
      { status: 500 }
    )
  }
}

// Generate theme from options
export async function PUT(request: NextRequest) {
  try {
    const options: ThemeGeneratorOptions = await request.json()
    
    // Validate options
    if (!options.baseColor || !options.style) {
      return NextResponse.json(
        { success: false, error: 'Base color and style are required' },
        { status: 400 }
      )
    }

    // Generate theme
    const generatedTheme = ThemeGenerator.generateTheme(options)
    
    return NextResponse.json({
      success: true,
      data: generatedTheme,
      message: 'Theme generated successfully'
    })

  } catch (error) {
    console.error('Error generating theme:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to generate theme' },
      { status: 500 }
    )
  }
}

// Bulk operations
export async function PATCH(request: NextRequest) {
  try {
    const { action, themeIds, data } = await request.json()

    switch (action) {
      case 'activate':
        // Deactivate all themes first
        await prisma.theme.updateMany({
          data: { isActive: false }
        })
        
        // Activate selected theme
        if (themeIds.length === 1) {
          await prisma.theme.update({
            where: { id: themeIds[0] },
            data: { isActive: true }
          })
        }
        break

      case 'deactivate':
        await prisma.theme.updateMany({
          where: { id: { in: themeIds } },
          data: { isActive: false }
        })
        break

      case 'delete':
        await prisma.theme.deleteMany({
          where: { 
            id: { in: themeIds },
            isDefault: false // Prevent deletion of default themes
          }
        })
        break

      case 'update-category':
        await prisma.theme.updateMany({
          where: { id: { in: themeIds } },
          data: { category: data.category }
        })
        break

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: `${action} completed successfully`
    })

  } catch (error) {
    console.error('Error in bulk operation:', error)
    return NextResponse.json(
      { success: false, error: 'Bulk operation failed' },
      { status: 500 }
    )
  }
}

// Get theme presets/templates
export async function OPTIONS(request: NextRequest) {
  try {
    const presets = [
      {
        id: 'modern-blue',
        name: 'Modern Blue',
        description: 'Clean and professional blue theme',
        category: 'business',
        preview: '/images/theme-previews/modern-blue.jpg',
        options: {
          baseColor: '#3b82f6',
          style: 'modern',
          contrast: 'medium',
          saturation: 'normal',
          borderRadius: 'rounded',
          fontPairing: 'modern',
          spacing: 'normal'
        }
      },
      {
        id: 'elegant-purple',
        name: 'Elegant Purple',
        description: 'Sophisticated purple theme with elegant styling',
        category: 'elegant',
        preview: '/images/theme-previews/elegant-purple.jpg',
        options: {
          baseColor: '#8b5cf6',
          style: 'elegant',
          contrast: 'medium',
          saturation: 'normal',
          borderRadius: 'rounded',
          fontPairing: 'classic',
          spacing: 'spacious'
        }
      },
      {
        id: 'bold-orange',
        name: 'Bold Orange',
        description: 'Vibrant and energetic orange theme',
        category: 'bold',
        preview: '/images/theme-previews/bold-orange.jpg',
        options: {
          baseColor: '#f97316',
          style: 'bold',
          contrast: 'high',
          saturation: 'vibrant',
          borderRadius: 'rounded',
          fontPairing: 'modern',
          spacing: 'compact'
        }
      },
      {
        id: 'minimal-gray',
        name: 'Minimal Gray',
        description: 'Clean and minimal gray theme',
        category: 'minimal',
        preview: '/images/theme-previews/minimal-gray.jpg',
        options: {
          baseColor: '#6b7280',
          style: 'minimal',
          contrast: 'low',
          saturation: 'muted',
          borderRadius: 'sharp',
          fontPairing: 'modern',
          spacing: 'normal'
        }
      },
      {
        id: 'creative-pink',
        name: 'Creative Pink',
        description: 'Playful and creative pink theme',
        category: 'creative',
        preview: '/images/theme-previews/creative-pink.jpg',
        options: {
          baseColor: '#ec4899',
          style: 'playful',
          contrast: 'medium',
          saturation: 'vibrant',
          borderRadius: 'pill',
          fontPairing: 'creative',
          spacing: 'spacious'
        }
      }
    ]

    return NextResponse.json({
      success: true,
      data: presets
    })

  } catch (error) {
    console.error('Error fetching theme presets:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch theme presets' },
      { status: 500 }
    )
  }
}
