// WordPress-Style Post Types API Routes
// RESTful API endpoints for managing post types

import { NextRequest, NextResponse } from 'next/server'
import { PostTypeService } from '@/lib/posts/services/post-type-service'
import { CreatePostTypeInput } from '@/lib/posts/types'

const postTypeService = new PostTypeService()

/**
 * GET /api/post-types - Get all post types
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeInactive = searchParams.get('includeInactive') === 'true'
    const publicOnly = searchParams.get('public') === 'true'
    
    let result
    if (publicOnly) {
      result = await postTypeService.getPublicPostTypes()
    } else {
      result = await postTypeService.getPostTypes(includeInactive)
    }
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data
    })

  } catch (error) {
    console.error('Error in GET /api/post-types:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/post-types - Register a new post type
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.name || !body.label || !body.labelPlural) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Name, label, and labelPlural are required' 
        },
        { status: 400 }
      )
    }

    const input: CreatePostTypeInput = {
      name: body.name,
      label: body.label,
      labelPlural: body.labelPlural,
      description: body.description,
      icon: body.icon,
      isPublic: body.isPublic,
      isHierarchical: body.isHierarchical,
      hasArchive: body.hasArchive,
      supportsTitle: body.supportsTitle,
      supportsContent: body.supportsContent,
      supportsExcerpt: body.supportsExcerpt,
      supportsThumbnail: body.supportsThumbnail,
      supportsComments: body.supportsComments,
      supportsRevisions: body.supportsRevisions,
      supportsPageBuilder: body.supportsPageBuilder,
      menuPosition: body.menuPosition,
      capabilities: body.capabilities,
      taxonomies: body.taxonomies,
      customFields: body.customFields,
      templates: body.templates,
    }

    const result = await postTypeService.registerPostType(input)
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: result.message
    }, { status: 201 })

  } catch (error) {
    console.error('Error in POST /api/post-types:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/post-types - Initialize default post types
 */
export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    
    if (action === 'initialize') {
      const result = await postTypeService.initializeDefaultPostTypes()
      
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.error },
          { status: 400 }
        )
      }

      return NextResponse.json({
        success: true,
        message: result.message
      })
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Error in PUT /api/post-types:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
