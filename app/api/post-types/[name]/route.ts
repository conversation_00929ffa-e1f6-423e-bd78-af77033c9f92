// Individual Post Type API Routes
// RESTful API endpoints for managing individual post types

import { NextRequest, NextResponse } from 'next/server'
import { PostTypeService } from '@/lib/posts/services/post-type-service'
import { CreatePostTypeInput } from '@/lib/posts/types'

const postTypeService = new PostTypeService()

/**
 * GET /api/post-types/[name] - Get a single post type by name
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { name: string } }
) {
  try {
    const result = await postTypeService.getPostType(params.name)
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Post type not found' ? 404 : 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data
    })

  } catch (error) {
    console.error('Error in GET /api/post-types/[name]:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/post-types/[name] - Update a post type
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { name: string } }
) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (body.label && !body.labelPlural) {
      return NextResponse.json(
        { success: false, error: 'labelPlural is required when label is provided' },
        { status: 400 }
      )
    }

    const updates: Partial<CreatePostTypeInput> = {
      label: body.label,
      labelPlural: body.labelPlural,
      description: body.description,
      icon: body.icon,
      isPublic: body.isPublic,
      isHierarchical: body.isHierarchical,
      hasArchive: body.hasArchive,
      supportsTitle: body.supportsTitle,
      supportsContent: body.supportsContent,
      supportsExcerpt: body.supportsExcerpt,
      supportsThumbnail: body.supportsThumbnail,
      supportsComments: body.supportsComments,
      supportsRevisions: body.supportsRevisions,
      supportsPageBuilder: body.supportsPageBuilder,
      menuPosition: body.menuPosition,
      capabilities: body.capabilities,
      taxonomies: body.taxonomies,
      customFields: body.customFields,
      templates: body.templates,
    }

    // Remove undefined values
    Object.keys(updates).forEach(key => {
      if (updates[key as keyof CreatePostTypeInput] === undefined) {
        delete updates[key as keyof CreatePostTypeInput]
      }
    })

    const result = await postTypeService.updatePostType(params.name, updates)
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Post type not found' ? 404 : 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: result.message
    })

  } catch (error) {
    console.error('Error in PUT /api/post-types/[name]:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * PATCH /api/post-types/[name] - Partial update of a post type
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { name: string } }
) {
  try {
    const body = await request.json()
    
    const result = await postTypeService.updatePostType(params.name, body)
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Post type not found' ? 404 : 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: result.message
    })

  } catch (error) {
    console.error('Error in PATCH /api/post-types/[name]:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/post-types/[name] - Deactivate a post type
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { name: string } }
) {
  try {
    const result = await postTypeService.deactivatePostType(params.name)
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Post type not found' ? 404 : 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: result.message
    })

  } catch (error) {
    console.error('Error in DELETE /api/post-types/[name]:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
