import { NextRequest, NextResponse } from 'next/server'
import { OrderService } from '@/lib/ecommerce/services/order-service'

const orderService = new OrderService()

export async function GET(request: NextRequest) {
  try {
    // TODO: Get customer ID from authentication
    // For now, we'll use a mock customer ID
    const customerId = 'mock-customer-id'

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')

    const orders = await orderService.getCustomerOrders(customerId, limit, offset)

    return NextResponse.json({
      success: true,
      orders,
      pagination: {
        limit,
        offset,
        total: orders.length
      }
    })

  } catch (error) {
    console.error('Get customer orders error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch customer orders'
    }, { status: 500 })
  }
}
