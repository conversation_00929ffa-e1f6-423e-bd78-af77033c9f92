import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const addressSchema = z.object({
  type: z.enum(['shipping', 'billing']),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  company: z.string().optional(),
  address1: z.string().min(1),
  address2: z.string().optional(),
  city: z.string().min(1),
  province: z.string().min(1),
  postalCode: z.string().min(1),
  country: z.string().min(1),
  phone: z.string().optional(),
  isDefault: z.boolean().optional()
})

export async function GET(request: NextRequest) {
  try {
    // TODO: Get customer ID from authentication
    const customerId = 'mock-customer-id'

    const addresses = await prisma.customerAddress.findMany({
      where: { customerId },
      orderBy: [
        { isDefault: 'desc' },
        { createdAt: 'desc' }
      ]
    })

    const formattedAddresses = addresses.map(address => ({
      id: address.id,
      type: address.type,
      firstName: address.firstName,
      lastName: address.lastName,
      company: address.company,
      address1: address.address1,
      address2: address.address2,
      city: address.city,
      province: address.province,
      postalCode: address.postalCode,
      country: address.country,
      phone: address.phone,
      isDefault: address.isDefault
    }))

    return NextResponse.json({
      success: true,
      addresses: formattedAddresses
    })

  } catch (error) {
    console.error('Get customer addresses error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch customer addresses'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // TODO: Get customer ID from authentication
    const customerId = 'mock-customer-id'

    const body = await request.json()
    const validatedData = addressSchema.parse(body)

    // If this is set as default, unset other default addresses of the same type
    if (validatedData.isDefault) {
      await prisma.customerAddress.updateMany({
        where: {
          customerId,
          type: validatedData.type
        },
        data: {
          isDefault: false
        }
      })
    }

    // Create new address
    const address = await prisma.customerAddress.create({
      data: {
        customerId,
        type: validatedData.type,
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        company: validatedData.company,
        address1: validatedData.address1,
        address2: validatedData.address2,
        city: validatedData.city,
        province: validatedData.province,
        postalCode: validatedData.postalCode,
        country: validatedData.country,
        phone: validatedData.phone,
        isDefault: validatedData.isDefault || false
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Address added successfully',
      address: {
        id: address.id,
        type: address.type,
        firstName: address.firstName,
        lastName: address.lastName,
        company: address.company,
        address1: address.address1,
        address2: address.address2,
        city: address.city,
        province: address.province,
        postalCode: address.postalCode,
        country: address.country,
        phone: address.phone,
        isDefault: address.isDefault
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Add customer address error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid request data',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to add address'
    }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // TODO: Get customer ID from authentication
    const customerId = 'mock-customer-id'

    const { searchParams } = new URL(request.url)
    const addressId = searchParams.get('addressId')

    if (!addressId) {
      return NextResponse.json({
        success: false,
        error: 'Address ID is required'
      }, { status: 400 })
    }

    const body = await request.json()
    const validatedData = addressSchema.partial().parse(body)

    // If this is set as default, unset other default addresses of the same type
    if (validatedData.isDefault && validatedData.type) {
      await prisma.customerAddress.updateMany({
        where: {
          customerId,
          type: validatedData.type,
          id: { not: addressId }
        },
        data: {
          isDefault: false
        }
      })
    }

    // Update address
    const updatedAddress = await prisma.customerAddress.update({
      where: {
        id: addressId,
        customerId
      },
      data: validatedData
    })

    return NextResponse.json({
      success: true,
      message: 'Address updated successfully',
      address: updatedAddress
    })

  } catch (error) {
    console.error('Update customer address error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid request data',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to update address'
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // TODO: Get customer ID from authentication
    const customerId = 'mock-customer-id'

    const { searchParams } = new URL(request.url)
    const addressId = searchParams.get('addressId')

    if (!addressId) {
      return NextResponse.json({
        success: false,
        error: 'Address ID is required'
      }, { status: 400 })
    }

    // Delete address
    await prisma.customerAddress.delete({
      where: {
        id: addressId,
        customerId
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Address deleted successfully'
    })

  } catch (error) {
    console.error('Delete customer address error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to delete address'
    }, { status: 500 })
  }
}
