import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const addToWishlistSchema = z.object({
  productId: z.string(),
  variantId: z.string().optional()
})

export async function GET(request: NextRequest) {
  try {
    // TODO: Get customer ID from authentication
    const customerId = 'mock-customer-id'

    const wishlistItems = await prisma.wishlistItem.findMany({
      where: { customerId },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            price: true,
            images: true,
            slug: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    const formattedItems = wishlistItems.map(item => ({
      id: item.id,
      productId: item.productId,
      variantId: item.variantId,
      product: item.product,
      addedAt: item.createdAt
    }))

    return NextResponse.json({
      success: true,
      items: formattedItems
    })

  } catch (error) {
    console.error('Get wishlist error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch wishlist'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // TODO: Get customer ID from authentication
    const customerId = 'mock-customer-id'

    const body = await request.json()
    const validatedData = addToWishlistSchema.parse(body)

    // Check if item already exists in wishlist
    const existingItem = await prisma.wishlistItem.findFirst({
      where: {
        customerId,
        productId: validatedData.productId,
        variantId: validatedData.variantId || null
      }
    })

    if (existingItem) {
      return NextResponse.json({
        success: false,
        error: 'Item already in wishlist'
      }, { status: 409 })
    }

    // Add item to wishlist
    const wishlistItem = await prisma.wishlistItem.create({
      data: {
        customerId,
        productId: validatedData.productId,
        variantId: validatedData.variantId
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            price: true,
            images: true,
            slug: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Item added to wishlist',
      item: {
        id: wishlistItem.id,
        productId: wishlistItem.productId,
        variantId: wishlistItem.variantId,
        product: wishlistItem.product,
        addedAt: wishlistItem.createdAt
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Add to wishlist error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid request data',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to add item to wishlist'
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // TODO: Get customer ID from authentication
    const customerId = 'mock-customer-id'

    const { searchParams } = new URL(request.url)
    const itemId = searchParams.get('itemId')
    const productId = searchParams.get('productId')
    const variantId = searchParams.get('variantId')

    if (itemId) {
      // Remove by wishlist item ID
      await prisma.wishlistItem.deleteMany({
        where: {
          id: itemId,
          customerId
        }
      })
    } else if (productId) {
      // Remove by product ID and optional variant ID
      await prisma.wishlistItem.deleteMany({
        where: {
          customerId,
          productId,
          variantId: variantId || null
        }
      })
    } else {
      return NextResponse.json({
        success: false,
        error: 'Missing itemId or productId parameter'
      }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      message: 'Item removed from wishlist'
    })

  } catch (error) {
    console.error('Remove from wishlist error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to remove item from wishlist'
    }, { status: 500 })
  }
}
