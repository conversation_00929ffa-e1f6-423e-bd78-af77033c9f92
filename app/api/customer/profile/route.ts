import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateProfileSchema = z.object({
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  dateOfBirth: z.string().optional(),
  preferences: z.object({
    newsletter: z.boolean().optional(),
    smsNotifications: z.boolean().optional(),
    emailNotifications: z.boolean().optional()
  }).optional()
})

export async function GET(request: NextRequest) {
  try {
    // TODO: Get customer ID from authentication
    // For now, we'll use a mock customer ID
    const customerId = 'mock-customer-id'

    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        dateOfBirth: true,
        preferences: true,
        createdAt: true
      }
    })

    if (!customer) {
      return NextResponse.json({
        success: false,
        error: 'Customer not found'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      profile: {
        id: customer.id,
        email: customer.email,
        firstName: customer.firstName,
        lastName: customer.lastName,
        phone: customer.phone,
        dateOfBirth: customer.dateOfBirth,
        preferences: customer.preferences || {
          newsletter: true,
          smsNotifications: false,
          emailNotifications: true
        }
      }
    })

  } catch (error) {
    console.error('Get customer profile error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch customer profile'
    }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // TODO: Get customer ID from authentication
    const customerId = 'mock-customer-id'

    const body = await request.json()
    const validatedData = updateProfileSchema.parse(body)

    const updatedCustomer = await prisma.customer.update({
      where: { id: customerId },
      data: {
        ...(validatedData.firstName && { firstName: validatedData.firstName }),
        ...(validatedData.lastName && { lastName: validatedData.lastName }),
        ...(validatedData.email && { email: validatedData.email }),
        ...(validatedData.phone && { phone: validatedData.phone }),
        ...(validatedData.dateOfBirth && { dateOfBirth: validatedData.dateOfBirth }),
        ...(validatedData.preferences && { preferences: validatedData.preferences })
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        dateOfBirth: true,
        preferences: true
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Profile updated successfully',
      profile: updatedCustomer
    })

  } catch (error) {
    console.error('Update customer profile error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid request data',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to update customer profile'
    }, { status: 500 })
  }
}
