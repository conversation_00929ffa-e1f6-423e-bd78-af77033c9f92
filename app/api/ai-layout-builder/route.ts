import { openai } from '@ai-sdk/openai'
import { streamText, tool } from 'ai'
import { z } from 'zod'

export const maxDuration = 30

// Layout structure schema
const layoutStructureSchema = z.object({
  structure: z.record(z.object({
    id: z.string(),
    type: z.enum(['header', 'main', 'sidebar', 'footer', 'custom']),
    name: z.string(),
    position: z.number(),
    blocks: z.array(z.object({
      id: z.string(),
      type: z.string(),
      name: z.string(),
      position: z.number(),
      configuration: z.object({}).passthrough(),
      content: z.object({}).passthrough(),
      styling: z.object({}).passthrough(),
      responsive: z.object({}).passthrough(),
      conditions: z.object({}).passthrough(),
      isVisible: z.boolean()
    })),
    configuration: z.object({
      layout: z.string(),
      alignment: z.string(),
      spacing: z.object({
        top: z.string(),
        right: z.string(),
        bottom: z.string(),
        left: z.string()
      }),
      background: z.object({
        type: z.string()
      }),
      container: z.object({
        maxWidth: z.string(),
        padding: z.string()
      })
    }),
    styling: z.object({
      background: z.object({ type: z.string() }),
      border: z.object({
        width: z.string(),
        style: z.string(),
        color: z.string()
      }),
      spacing: z.object({
        top: z.string(),
        right: z.string(),
        bottom: z.string(),
        left: z.string()
      }),
      shadow: z.object({ type: z.string() })
    }),
    responsive: z.object({
      mobile: z.object({ isVisible: z.boolean() }),
      tablet: z.object({ isVisible: z.boolean() }),
      desktop: z.object({ isVisible: z.boolean() }),
      large: z.object({ isVisible: z.boolean() })
    }),
    isVisible: z.boolean()
  })),
  styling: z.object({}).passthrough(),
  responsive: z.object({}).passthrough(),
  metadata: z.object({
    layoutType: z.string(),
    layoutCategory: z.string(),
    designStyle: z.string(),
    targetDevice: z.string(),
    generatedAt: z.string()
  })
})

export async function POST(req: Request) {
  const { messages } = await req.json()
  
  const result = await streamText({
    model: openai('gpt-4-turbo'),
    messages,
    tools: {
      generateLayout: tool({
        description: 'Generate a complete layout structure with sections and blocks',
        parameters: z.object({
          prompt: z.string().describe('User prompt describing the desired layout'),
          layoutType: z.string().describe('Type of layout (page, site, template)'),
          layoutCategory: z.string().describe('Category (ecommerce, blog, portfolio, etc.)'),
          includeSections: z.array(z.string()).describe('Sections to include'),
          designStyle: z.string().describe('Design style (modern, minimal, etc.)'),
          targetDevice: z.string().describe('Primary target device')
        }),
        execute: async ({ 
          prompt, 
          layoutType, 
          layoutCategory, 
          includeSections, 
          designStyle, 
          targetDevice 
        }) => {
          const layout = generateLayoutStructure({
            prompt,
            layoutType,
            layoutCategory,
            includeSections,
            designStyle,
            targetDevice
          })
          
          return {
            layout,
            message: `Generated a ${designStyle} ${layoutType} layout for ${layoutCategory} with ${includeSections.length} sections optimized for ${targetDevice}.`
          }
        }
      }),
      
      optimizeLayoutStructure: tool({
        description: 'Optimize layout structure for better performance and UX',
        parameters: z.object({
          currentLayout: z.object({}).passthrough(),
          optimizationGoals: z.array(z.string()),
          targetDevice: z.string()
        }),
        execute: async ({ currentLayout, optimizationGoals, targetDevice }) => {
          const optimizedLayout = optimizeLayout(currentLayout, optimizationGoals, targetDevice)
          
          return {
            layout: optimizedLayout,
            message: `Optimized layout structure for ${targetDevice} focusing on: ${optimizationGoals.join(', ')}`
          }
        }
      }),
      
      addLayoutSection: tool({
        description: 'Add a new section to the layout',
        parameters: z.object({
          currentLayout: z.object({}).passthrough(),
          sectionType: z.string(),
          position: z.number(),
          configuration: z.object({}).passthrough().optional()
        }),
        execute: async ({ currentLayout, sectionType, position, configuration }) => {
          const updatedLayout = addSectionToLayout(currentLayout, sectionType, position, configuration)
          
          return {
            layout: updatedLayout,
            message: `Added ${sectionType} section to layout at position ${position}`
          }
        }
      })
    },
    system: `You are an expert layout designer and UX architect who creates comprehensive layout structures for modern web applications. You understand:

1. Layout Architecture:
   - Site-wide layouts vs page-specific layouts
   - Section hierarchy and organization
   - Block placement and relationships
   - Responsive design principles

2. Section Types:
   - Header: Navigation, branding, user actions
   - Main: Primary content area with flexible blocks
   - Sidebar: Secondary content, widgets, navigation
   - Footer: Links, information, legal content
   - Custom: Specialized sections for specific needs

3. Block Types for Each Section:
   Header: logo, navigation, search, cart, user-menu
   Main: content, hero, features, products, gallery, testimonials
   Sidebar: categories, filters, recent-posts, tags, widgets
   Footer: links, social, copyright, newsletter, contact

4. Layout Categories:
   - E-commerce: Product-focused with shopping features
   - Blog: Content-focused with reading experience
   - Portfolio: Visual showcase with project displays
   - Landing: Conversion-focused with clear CTAs
   - Corporate: Professional with trust elements
   - Dashboard: Data-focused with navigation

5. Design Principles:
   - Mobile-first responsive design
   - Accessibility and semantic structure
   - Performance optimization
   - SEO-friendly structure
   - User experience flow

6. Layout Patterns:
   - Single column (mobile, simple pages)
   - Two column (content + sidebar)
   - Three column (content + dual sidebars)
   - Grid layouts (product catalogs, portfolios)
   - Dashboard layouts (navigation + content)

When generating layouts:
1. Consider the layout category and optimize accordingly
2. Create logical section hierarchy
3. Include appropriate blocks for each section
4. Ensure responsive behavior
5. Follow accessibility best practices
6. Consider SEO and performance implications

Always respond with the generateLayout tool to create the actual layout structure.`,
  })

  return result.toDataStreamResponse()
}

// Generate layout structure
function generateLayoutStructure({
  prompt,
  layoutType,
  layoutCategory,
  includeSections,
  designStyle,
  targetDevice
}: any) {
  const structure: any = {}
  
  // Define section configurations based on category
  const categoryConfigs = {
    ecommerce: {
      header: {
        blocks: ['logo', 'navigation', 'search', 'cart'],
        height: 'auto',
        sticky: true
      },
      main: {
        blocks: ['hero', 'categories', 'products', 'testimonials'],
        layout: 'grid'
      },
      sidebar: {
        blocks: ['categories', 'filters', 'recent-products'],
        width: '300px'
      },
      footer: {
        blocks: ['links', 'social', 'newsletter', 'copyright'],
        columns: 4
      }
    },
    blog: {
      header: {
        blocks: ['logo', 'navigation', 'search'],
        height: 'auto',
        sticky: false
      },
      main: {
        blocks: ['content', 'related-posts'],
        layout: 'single-column'
      },
      sidebar: {
        blocks: ['recent-posts', 'categories', 'tags'],
        width: '280px'
      },
      footer: {
        blocks: ['links', 'social', 'copyright'],
        columns: 3
      }
    },
    landing: {
      header: {
        blocks: ['logo', 'navigation', 'cta'],
        height: 'auto',
        sticky: true
      },
      main: {
        blocks: ['hero', 'features', 'testimonials', 'cta'],
        layout: 'single-column'
      },
      footer: {
        blocks: ['links', 'social', 'copyright'],
        columns: 3
      }
    },
    dashboard: {
      header: {
        blocks: ['logo', 'user-menu', 'notifications'],
        height: '60px',
        sticky: true
      },
      sidebar: {
        blocks: ['navigation', 'user-info'],
        width: '250px',
        position: 'left'
      },
      main: {
        blocks: ['metrics', 'charts', 'tables'],
        layout: 'dashboard'
      }
    }
  }

  const config = categoryConfigs[layoutCategory as keyof typeof categoryConfigs] || categoryConfigs.ecommerce

  // Generate sections based on includeSections
  includeSections.forEach((sectionType: string, index: number) => {
    const sectionId = `${sectionType}-${Date.now()}-${index}`
    const sectionConfig = config[sectionType as keyof typeof config] || {}
    
    structure[sectionType] = {
      id: sectionId,
      type: sectionType,
      name: sectionType.charAt(0).toUpperCase() + sectionType.slice(1),
      position: index,
      blocks: generateSectionBlocks(sectionType, sectionConfig, designStyle),
      configuration: generateSectionConfiguration(sectionType, sectionConfig, targetDevice),
      styling: generateSectionStyling(sectionType, designStyle),
      responsive: generateResponsiveSettings(sectionType, targetDevice),
      isVisible: true
    }
  })

  return {
    structure,
    styling: generateLayoutStyling(designStyle),
    responsive: generateLayoutResponsive(targetDevice),
    metadata: {
      layoutType,
      layoutCategory,
      designStyle,
      targetDevice,
      generatedAt: new Date().toISOString()
    }
  }
}

// Generate blocks for a section
function generateSectionBlocks(sectionType: string, config: any, designStyle: string) {
  const blockTypes = config.blocks || []
  
  return blockTypes.map((blockType: string, index: number) => ({
    id: `${blockType}-${Date.now()}-${index}`,
    type: blockType,
    name: blockType.replace('-', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
    position: index,
    configuration: generateBlockConfiguration(blockType, designStyle),
    content: generateBlockContent(blockType, sectionType),
    styling: generateBlockStyling(blockType, designStyle),
    responsive: generateBlockResponsive(blockType),
    conditions: {},
    isVisible: true
  }))
}

// Generate section configuration
function generateSectionConfiguration(sectionType: string, config: any, targetDevice: string) {
  const baseConfig = {
    layout: config.layout || 'block',
    alignment: 'left',
    spacing: { top: '0', right: '0', bottom: '0', left: '0' },
    background: { type: 'none' },
    container: { maxWidth: '1200px', padding: '1rem' }
  }

  // Adjust for section type
  switch (sectionType) {
    case 'header':
      return {
        ...baseConfig,
        layout: 'flex',
        alignment: 'space-between',
        container: { maxWidth: '100%', padding: '0 1rem' },
        sticky: config.sticky || false,
        height: config.height || 'auto'
      }
    case 'sidebar':
      return {
        ...baseConfig,
        layout: 'stack',
        width: config.width || '280px',
        position: config.position || 'right'
      }
    case 'footer':
      return {
        ...baseConfig,
        layout: 'grid',
        columns: config.columns || 3,
        background: { type: 'color', value: '#f8f9fa' }
      }
    default:
      return baseConfig
  }
}

// Generate section styling
function generateSectionStyling(sectionType: string, designStyle: string) {
  const baseStyle = {
    background: { type: 'none' },
    border: { width: '0', style: 'none', color: 'transparent' },
    spacing: { top: '0', right: '0', bottom: '0', left: '0' },
    shadow: { type: 'none' }
  }

  // Style adjustments based on design style
  const styleAdjustments = {
    modern: {
      borderRadius: '8px',
      shadow: { type: 'sm' }
    },
    minimal: {
      borderRadius: '4px',
      spacing: { top: '1rem', right: '0', bottom: '1rem', left: '0' }
    },
    bold: {
      borderRadius: '12px',
      shadow: { type: 'lg' },
      spacing: { top: '2rem', right: '0', bottom: '2rem', left: '0' }
    }
  }

  return {
    ...baseStyle,
    ...styleAdjustments[designStyle as keyof typeof styleAdjustments]
  }
}

// Generate responsive settings
function generateResponsiveSettings(sectionType: string, targetDevice: string) {
  return {
    mobile: { isVisible: true },
    tablet: { isVisible: true },
    desktop: { isVisible: true },
    large: { isVisible: true }
  }
}

// Generate block configuration
function generateBlockConfiguration(blockType: string, designStyle: string) {
  return {
    style: designStyle,
    animation: 'fadeIn',
    responsive: true
  }
}

// Generate block content
function generateBlockContent(blockType: string, sectionType: string) {
  const contentTemplates: Record<string, any> = {
    logo: { text: 'Your Logo', image: '/logo.svg' },
    navigation: { 
      items: [
        { label: 'Home', href: '/' },
        { label: 'Products', href: '/products' },
        { label: 'About', href: '/about' },
        { label: 'Contact', href: '/contact' }
      ]
    },
    hero: {
      title: 'Welcome to Our Store',
      subtitle: 'Discover amazing products',
      cta: 'Shop Now'
    },
    features: {
      title: 'Why Choose Us',
      items: [
        { title: 'Quality', description: 'Premium products' },
        { title: 'Service', description: 'Excellent support' },
        { title: 'Delivery', description: 'Fast shipping' }
      ]
    }
  }
  
  return contentTemplates[blockType] || {}
}

// Generate block styling
function generateBlockStyling(blockType: string, designStyle: string) {
  return {
    spacing: { top: '1rem', right: '1rem', bottom: '1rem', left: '1rem' },
    background: { type: 'none' },
    border: { width: '0', style: 'none', color: 'transparent' }
  }
}

// Generate block responsive settings
function generateBlockResponsive(blockType: string) {
  return {
    mobile: { isVisible: true },
    tablet: { isVisible: true },
    desktop: { isVisible: true },
    large: { isVisible: true }
  }
}

// Generate layout styling
function generateLayoutStyling(designStyle: string) {
  return {
    typography: {
      fontFamily: 'Inter, sans-serif',
      fontSize: '16px',
      lineHeight: '1.5'
    },
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      background: '#ffffff',
      text: '#1e293b'
    },
    spacing: {
      unit: '1rem'
    }
  }
}

// Generate layout responsive settings
function generateLayoutResponsive(targetDevice: string) {
  return {
    breakpoints: {
      mobile: '480px',
      tablet: '768px',
      desktop: '1024px',
      large: '1280px'
    },
    optimizedFor: targetDevice
  }
}

// Optimize layout
function optimizeLayout(layout: any, goals: string[], targetDevice: string) {
  // Implementation for layout optimization
  return layout
}

// Add section to layout
function addSectionToLayout(layout: any, sectionType: string, position: number, configuration?: any) {
  // Implementation for adding section
  return layout
}
