import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// GET /api/layout-builder/assignments - Get layout assignments
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const layoutId = searchParams.get('layoutId')
    const targetType = searchParams.get('targetType')
    const targetId = searchParams.get('targetId')

    const where: any = { isActive: true }
    
    if (layoutId) where.layoutId = layoutId
    if (targetType) where.targetType = targetType
    if (targetId) where.targetId = targetId

    const assignments = await prisma.layoutAssignment.findMany({
      where,
      include: {
        layout: {
          select: {
            id: true,
            name: true,
            type: true,
            category: true,
            isActive: true
          }
        }
      },
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'desc' }
      ]
    })

    return NextResponse.json({
      success: true,
      data: assignments,
      count: assignments.length
    })
  } catch (error) {
    console.error('Error fetching assignments:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch assignments' },
      { status: 500 }
    )
  }
}

// POST /api/layout-builder/assignments - Create layout assignment
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      layoutId,
      targetType,
      targetId,
      targetSlug,
      priority = 0,
      conditions = {}
    } = body

    // Validate required fields
    if (!layoutId || !targetType) {
      return NextResponse.json(
        { success: false, error: 'Layout ID and target type are required' },
        { status: 400 }
      )
    }

    // Check if layout exists
    const layout = await prisma.layout.findUnique({
      where: { id: layoutId, isActive: true },
      select: { id: true, name: true }
    })

    if (!layout) {
      return NextResponse.json(
        { success: false, error: 'Layout not found or inactive' },
        { status: 404 }
      )
    }

    // Check for existing assignment with same target
    const existingAssignment = await prisma.layoutAssignment.findFirst({
      where: {
        targetType,
        targetId: targetId || null,
        targetSlug: targetSlug || null,
        isActive: true
      }
    })

    if (existingAssignment) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'An active assignment already exists for this target',
          existingAssignment: {
            id: existingAssignment.id,
            layoutId: existingAssignment.layoutId
          }
        },
        { status: 409 }
      )
    }

    // Create the assignment
    const assignment = await prisma.layoutAssignment.create({
      data: {
        layoutId,
        targetType,
        targetId,
        targetSlug,
        priority,
        conditions,
        isActive: true
      },
      include: {
        layout: {
          select: {
            id: true,
            name: true,
            type: true,
            category: true
          }
        }
      }
    })

    // Increment layout usage count
    await prisma.layout.update({
      where: { id: layoutId },
      data: { usageCount: { increment: 1 } }
    })

    return NextResponse.json({
      success: true,
      data: assignment,
      message: 'Layout assignment created successfully'
    })
  } catch (error) {
    console.error('Error creating assignment:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create assignment' },
      { status: 500 }
    )
  }
}

// PUT /api/layout-builder/assignments - Bulk update assignments
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, assignmentIds, data } = body

    switch (action) {
      case 'activate':
        await prisma.layoutAssignment.updateMany({
          where: { id: { in: assignmentIds } },
          data: { isActive: true }
        })
        break

      case 'deactivate':
        await prisma.layoutAssignment.updateMany({
          where: { id: { in: assignmentIds } },
          data: { isActive: false }
        })
        break

      case 'updatePriority':
        // Update priorities in batch
        for (const update of data.updates) {
          await prisma.layoutAssignment.update({
            where: { id: update.id },
            data: { priority: update.priority }
          })
        }
        break

      case 'delete':
        await prisma.layoutAssignment.deleteMany({
          where: { id: { in: assignmentIds } }
        })
        break

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: `Assignments ${action}d successfully`
    })
  } catch (error) {
    console.error('Error updating assignments:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update assignments' },
      { status: 500 }
    )
  }
}

// DELETE /api/layout-builder/assignments - Delete assignments
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const assignmentIds = searchParams.get('ids')?.split(',') || []

    if (assignmentIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No assignment IDs provided' },
        { status: 400 }
      )
    }

    // Get assignments to update layout usage counts
    const assignments = await prisma.layoutAssignment.findMany({
      where: { id: { in: assignmentIds } },
      select: { layoutId: true }
    })

    // Delete assignments
    await prisma.layoutAssignment.deleteMany({
      where: { id: { in: assignmentIds } }
    })

    // Decrement usage counts for affected layouts
    const layoutIds = [...new Set(assignments.map(a => a.layoutId))]
    for (const layoutId of layoutIds) {
      await prisma.layout.update({
        where: { id: layoutId },
        data: { usageCount: { decrement: 1 } }
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Assignments deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting assignments:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete assignments' },
      { status: 500 }
    )
  }
}
