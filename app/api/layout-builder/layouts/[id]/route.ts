import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// GET /api/layout-builder/layouts/[id] - Get specific layout
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const layout = await prisma.layout.findUnique({
      where: { id },
      include: {
        assignments: {
          where: { isActive: true },
          orderBy: { priority: 'desc' }
        },
        sections: {
          where: { isVisible: true },
          include: {
            layoutBlocks: {
              where: { isVisible: true },
              orderBy: { position: 'asc' }
            }
          },
          orderBy: { position: 'asc' }
        },
        versions: {
          orderBy: { versionNumber: 'desc' },
          take: 10
        },
        _count: {
          select: {
            assignments: true,
            sections: true,
            versions: true
          }
        }
      }
    })

    if (!layout) {
      return NextResponse.json(
        { success: false, error: 'Layout not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: layout
    })
  } catch (error) {
    console.error('Error fetching layout:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch layout' },
      { status: 500 }
    )
  }
}

// PUT /api/layout-builder/layouts/[id] - Update layout
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    const {
      name,
      description,
      type,
      category,
      structure,
      styling,
      responsive,
      conditions,
      isTemplate,
      isActive,
      tags,
      thumbnail,
      createVersion = false
    } = body

    // Check if layout exists and is not system layout for certain operations
    const existingLayout = await prisma.layout.findUnique({
      where: { id },
      select: { isSystem: true, usageCount: true }
    })

    if (!existingLayout) {
      return NextResponse.json(
        { success: false, error: 'Layout not found' },
        { status: 404 }
      )
    }

    // Prepare update data
    const updateData: any = {
      updatedBy: 'admin' // TODO: Get from auth context
    }

    if (name !== undefined) updateData.name = name
    if (description !== undefined) updateData.description = description
    if (type !== undefined) updateData.type = type
    if (category !== undefined) updateData.category = category
    if (structure !== undefined) updateData.structure = structure
    if (styling !== undefined) updateData.styling = styling
    if (responsive !== undefined) updateData.responsive = responsive
    if (conditions !== undefined) updateData.conditions = conditions
    if (isTemplate !== undefined) updateData.isTemplate = isTemplate
    if (isActive !== undefined) updateData.isActive = isActive
    if (tags !== undefined) updateData.tags = tags
    if (thumbnail !== undefined) updateData.thumbnail = thumbnail

    // Update the layout
    const updatedLayout = await prisma.layout.update({
      where: { id },
      data: updateData,
      include: {
        assignments: true,
        sections: {
          include: {
            layoutBlocks: true
          }
        },
        versions: {
          orderBy: { versionNumber: 'desc' },
          take: 5
        }
      }
    })

    // Create new version if requested or if structure changed
    if (createVersion || structure !== undefined) {
      const latestVersion = await prisma.layoutVersion.findFirst({
        where: { layoutId: id },
        orderBy: { versionNumber: 'desc' }
      })

      const nextVersionNumber = (latestVersion?.versionNumber || 0) + 1

      // Mark previous versions as not current
      await prisma.layoutVersion.updateMany({
        where: { layoutId: id },
        data: { isCurrent: false }
      })

      // Create new version
      await prisma.layoutVersion.create({
        data: {
          layoutId: id,
          versionNumber: nextVersionNumber,
          name: `Version ${nextVersionNumber}`,
          structure: structure || updatedLayout.structure,
          styling: styling || updatedLayout.styling,
          responsive: responsive || updatedLayout.responsive,
          isPublished: true,
          isCurrent: true,
          createdBy: 'admin'
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: updatedLayout,
      message: 'Layout updated successfully'
    })
  } catch (error) {
    console.error('Error updating layout:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update layout' },
      { status: 500 }
    )
  }
}

// DELETE /api/layout-builder/layouts/[id] - Delete layout
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // Check if layout exists and is not system layout
    const layout = await prisma.layout.findUnique({
      where: { id },
      select: { 
        isSystem: true, 
        name: true,
        usageCount: true,
        assignments: {
          where: { isActive: true },
          select: { id: true }
        }
      }
    })

    if (!layout) {
      return NextResponse.json(
        { success: false, error: 'Layout not found' },
        { status: 404 }
      )
    }

    if (layout.isSystem) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete system layout' },
        { status: 400 }
      )
    }

    if (layout.assignments.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Cannot delete layout that is currently assigned. Remove assignments first.',
          assignmentCount: layout.assignments.length
        },
        { status: 400 }
      )
    }

    // Soft delete by deactivating
    await prisma.layout.update({
      where: { id },
      data: { 
        isActive: false,
        updatedBy: 'admin'
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Layout deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting layout:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete layout' },
      { status: 500 }
    )
  }
}

// PATCH /api/layout-builder/layouts/[id] - Partial update (for quick actions)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    const { action, data } = body

    switch (action) {
      case 'toggle-active':
        const layout = await prisma.layout.findUnique({
          where: { id },
          select: { isActive: true, isSystem: true }
        })

        if (!layout) {
          return NextResponse.json(
            { success: false, error: 'Layout not found' },
            { status: 404 }
          )
        }

        await prisma.layout.update({
          where: { id },
          data: { 
            isActive: !layout.isActive,
            updatedBy: 'admin'
          }
        })
        break

      case 'increment-usage':
        await prisma.layout.update({
          where: { id },
          data: { 
            usageCount: { increment: 1 }
          }
        })
        break

      case 'update-thumbnail':
        await prisma.layout.update({
          where: { id },
          data: { 
            thumbnail: data.thumbnail,
            updatedBy: 'admin'
          }
        })
        break

      case 'add-tags':
        const currentLayout = await prisma.layout.findUnique({
          where: { id },
          select: { tags: true }
        })

        if (currentLayout) {
          const newTags = [...new Set([...currentLayout.tags, ...data.tags])]
          await prisma.layout.update({
            where: { id },
            data: { 
              tags: newTags,
              updatedBy: 'admin'
            }
          })
        }
        break

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: `Layout ${action} completed successfully`
    })
  } catch (error) {
    console.error('Error updating layout:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update layout' },
      { status: 500 }
    )
  }
}
