import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { LayoutResolver } from '@/lib/layout-builder/layout-resolver'

const prisma = new PrismaClient()

// GET /api/layout-builder/layouts - Get all layouts
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const category = searchParams.get('category')
    const includeTemplates = searchParams.get('includeTemplates') === 'true'
    const includeInactive = searchParams.get('includeInactive') === 'true'

    const where: any = {}
    
    if (type) where.type = type
    if (category && category !== 'all') where.category = category
    if (!includeTemplates) where.isTemplate = false
    if (!includeInactive) where.isActive = true

    const layouts = await prisma.layout.findMany({
      where,
      include: {
        assignments: {
          where: { isActive: true },
          select: {
            id: true,
            targetType: true,
            targetId: true,
            priority: true
          }
        },
        _count: {
          select: {
            assignments: true,
            sections: true,
            versions: true
          }
        }
      },
      orderBy: [
        { isSystem: 'desc' },
        { usageCount: 'desc' },
        { name: 'asc' }
      ]
    })

    return NextResponse.json({
      success: true,
      data: layouts,
      count: layouts.length
    })
  } catch (error) {
    console.error('Error fetching layouts:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch layouts' },
      { status: 500 }
    )
  }
}

// POST /api/layout-builder/layouts - Create new layout
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      description,
      type = 'page',
      category = 'custom',
      structure,
      styling = {},
      responsive = {},
      conditions = {},
      isTemplate = false,
      tags = [],
      thumbnail
    } = body

    // Validate required fields
    if (!name || !structure) {
      return NextResponse.json(
        { success: false, error: 'Name and structure are required' },
        { status: 400 }
      )
    }

    // Create the layout
    const layout = await prisma.layout.create({
      data: {
        name,
        description,
        type,
        category,
        structure,
        styling,
        responsive,
        conditions,
        isTemplate,
        isSystem: false,
        isActive: true,
        usageCount: 0,
        tags,
        thumbnail,
        createdBy: 'admin', // TODO: Get from auth context
        updatedBy: 'admin'
      }
    })

    // Create initial version
    await prisma.layoutVersion.create({
      data: {
        layoutId: layout.id,
        versionNumber: 1,
        name: 'Initial Version',
        structure,
        styling,
        responsive,
        isPublished: true,
        isCurrent: true,
        createdBy: 'admin'
      }
    })

    return NextResponse.json({
      success: true,
      data: layout,
      message: 'Layout created successfully'
    })
  } catch (error) {
    console.error('Error creating layout:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create layout' },
      { status: 500 }
    )
  }
}

// PUT /api/layout-builder/layouts - Bulk update layouts
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, layoutIds, data } = body

    switch (action) {
      case 'activate':
        await prisma.layout.updateMany({
          where: { id: { in: layoutIds } },
          data: { isActive: true, updatedBy: 'admin' }
        })
        break

      case 'deactivate':
        await prisma.layout.updateMany({
          where: { id: { in: layoutIds } },
          data: { isActive: false, updatedBy: 'admin' }
        })
        break

      case 'delete':
        // Soft delete by deactivating
        await prisma.layout.updateMany({
          where: { 
            id: { in: layoutIds },
            isSystem: false // Prevent deletion of system layouts
          },
          data: { isActive: false, updatedBy: 'admin' }
        })
        break

      case 'updateCategory':
        await prisma.layout.updateMany({
          where: { id: { in: layoutIds } },
          data: { category: data.category, updatedBy: 'admin' }
        })
        break

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: `Layouts ${action}d successfully`
    })
  } catch (error) {
    console.error('Error updating layouts:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update layouts' },
      { status: 500 }
    )
  }
}

// DELETE /api/layout-builder/layouts - Delete layouts
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const layoutIds = searchParams.get('ids')?.split(',') || []

    if (layoutIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No layout IDs provided' },
        { status: 400 }
      )
    }

    // Check if any layouts are system layouts
    const systemLayouts = await prisma.layout.findMany({
      where: {
        id: { in: layoutIds },
        isSystem: true
      },
      select: { id: true, name: true }
    })

    if (systemLayouts.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Cannot delete system layouts',
          systemLayouts: systemLayouts.map(l => l.name)
        },
        { status: 400 }
      )
    }

    // Soft delete by deactivating
    await prisma.layout.updateMany({
      where: { id: { in: layoutIds } },
      data: { 
        isActive: false, 
        updatedBy: 'admin'
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Layouts deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting layouts:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete layouts' },
      { status: 500 }
    )
  }
}
