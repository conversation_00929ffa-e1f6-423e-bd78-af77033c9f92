import { NextRequest, NextResponse } from 'next/server'
import { LayoutTemplateGenerator } from '@/lib/layout-builder/templates/template-generator'

// GET /api/layout-builder/templates - Get all available templates
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const templateId = searchParams.get('id')

    // If specific template requested
    if (templateId) {
      const templates = LayoutTemplateGenerator.getAllTemplates()
      const template = templates[templateId as keyof typeof templates]
      
      if (!template) {
        return NextResponse.json(
          { success: false, error: 'Template not found' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        data: template
      })
    }

    // Template metadata for the gallery
    const templateMetadata = [
      {
        id: 'modern-ecommerce',
        name: 'Modern E-commerce Store',
        description: 'A sleek, modern e-commerce layout with advanced product showcase, cart functionality, and mobile-optimized design.',
        category: 'ecommerce',
        preview: '/templates/modern-ecommerce-preview.jpg',
        thumbnail: '/templates/modern-ecommerce-thumb.jpg',
        features: [
          'Product Grid', 
          'Shopping Cart', 
          'Search Bar', 
          'Mobile Menu', 
          'Footer Links',
          'Responsive Design',
          'Social Media Integration'
        ],
        difficulty: 'intermediate',
        rating: 4.8,
        downloads: 1247,
        isPremium: false,
        tags: ['responsive', 'modern', 'clean', 'professional', 'ecommerce'],
        colors: ['#3b82f6', '#1f2937', '#ff6b6b'],
        devices: ['mobile', 'tablet', 'desktop'],
        lastUpdated: '2024-01-15',
        author: 'Layout Builder Team'
      },
      {
        id: 'minimal-blog',
        name: 'Minimal Blog Layout',
        description: 'Clean and minimal blog layout focused on readability and content presentation with elegant typography.',
        category: 'blog',
        preview: '/templates/minimal-blog-preview.jpg',
        thumbnail: '/templates/minimal-blog-thumb.jpg',
        features: [
          'Article Layout', 
          'Sidebar Widgets', 
          'Typography Focus', 
          'Reading Experience',
          'Author Section',
          'Related Posts',
          'Comment System'
        ],
        difficulty: 'beginner',
        rating: 4.6,
        downloads: 892,
        isPremium: false,
        tags: ['minimal', 'clean', 'typography', 'readable', 'blog'],
        colors: ['#1f2937', '#6b7280', '#3b82f6'],
        devices: ['mobile', 'tablet', 'desktop'],
        lastUpdated: '2024-01-10',
        author: 'Layout Builder Team'
      },
      {
        id: 'creative-portfolio',
        name: 'Creative Portfolio',
        description: 'Stunning portfolio layout for creative professionals with image galleries and project showcases.',
        category: 'portfolio',
        preview: '/templates/creative-portfolio-preview.jpg',
        thumbnail: '/templates/creative-portfolio-thumb.jpg',
        features: [
          'Image Gallery', 
          'Project Showcase', 
          'Contact Form', 
          'About Section',
          'Skills Display',
          'Testimonials',
          'Social Links'
        ],
        difficulty: 'advanced',
        rating: 4.9,
        downloads: 634,
        isPremium: true,
        tags: ['creative', 'visual', 'gallery', 'artistic', 'portfolio'],
        colors: ['#000000', '#ffffff', '#ff6b6b'],
        devices: ['mobile', 'tablet', 'desktop'],
        lastUpdated: '2024-01-20',
        author: 'Creative Studio'
      },
      {
        id: 'business-corporate',
        name: 'Corporate Business',
        description: 'Professional corporate layout with service sections, team showcase, and contact information.',
        category: 'business',
        preview: '/templates/business-corporate-preview.jpg',
        thumbnail: '/templates/business-corporate-thumb.jpg',
        features: [
          'Service Grid', 
          'Team Section', 
          'Testimonials', 
          'Contact Info', 
          'CTA Buttons',
          'Company Stats',
          'Newsletter Signup'
        ],
        difficulty: 'intermediate',
        rating: 4.7,
        downloads: 1156,
        isPremium: false,
        tags: ['professional', 'corporate', 'business', 'services'],
        colors: ['#1e40af', '#1f2937', '#f59e0b'],
        devices: ['mobile', 'tablet', 'desktop'],
        lastUpdated: '2024-01-12',
        author: 'Business Templates'
      },
      {
        id: 'landing-conversion',
        name: 'High-Converting Landing',
        description: 'Optimized landing page layout designed for maximum conversions with clear CTAs and social proof.',
        category: 'landing',
        preview: '/templates/landing-conversion-preview.jpg',
        thumbnail: '/templates/landing-conversion-thumb.jpg',
        features: [
          'Hero Section', 
          'Features Grid', 
          'Testimonials', 
          'Pricing Table', 
          'CTA Sections',
          'Social Proof',
          'FAQ Section'
        ],
        difficulty: 'advanced',
        rating: 4.9,
        downloads: 2341,
        isPremium: true,
        tags: ['conversion', 'marketing', 'cta', 'optimized', 'landing'],
        colors: ['#10b981', '#1f2937', '#f59e0b'],
        devices: ['mobile', 'tablet', 'desktop'],
        lastUpdated: '2024-01-18',
        author: 'Marketing Pro'
      },
      {
        id: 'personal-blog',
        name: 'Personal Blog & Journal',
        description: 'Warm and personal blog layout perfect for lifestyle, travel, and personal content.',
        category: 'personal',
        preview: '/templates/personal-blog-preview.jpg',
        thumbnail: '/templates/personal-blog-thumb.jpg',
        features: [
          'Personal Header', 
          'Story Layout', 
          'Image Focus', 
          'Social Links', 
          'Archive',
          'About Widget',
          'Instagram Feed'
        ],
        difficulty: 'beginner',
        rating: 4.5,
        downloads: 567,
        isPremium: false,
        tags: ['personal', 'lifestyle', 'warm', 'friendly', 'journal'],
        colors: ['#ec4899', '#1f2937', '#f59e0b'],
        devices: ['mobile', 'tablet', 'desktop'],
        lastUpdated: '2024-01-08',
        author: 'Personal Themes'
      }
    ]

    // Filter by category if specified
    let filteredTemplates = templateMetadata
    if (category && category !== 'all') {
      filteredTemplates = templateMetadata.filter(template => template.category === category)
    }

    return NextResponse.json({
      success: true,
      data: filteredTemplates,
      count: filteredTemplates.length,
      categories: [
        { id: 'all', name: 'All Templates', count: templateMetadata.length },
        { id: 'ecommerce', name: 'E-commerce', count: templateMetadata.filter(t => t.category === 'ecommerce').length },
        { id: 'blog', name: 'Blog & Magazine', count: templateMetadata.filter(t => t.category === 'blog').length },
        { id: 'portfolio', name: 'Portfolio', count: templateMetadata.filter(t => t.category === 'portfolio').length },
        { id: 'business', name: 'Business', count: templateMetadata.filter(t => t.category === 'business').length },
        { id: 'landing', name: 'Landing Pages', count: templateMetadata.filter(t => t.category === 'landing').length },
        { id: 'personal', name: 'Personal', count: templateMetadata.filter(t => t.category === 'personal').length }
      ]
    })
  } catch (error) {
    console.error('Error fetching templates:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch templates' },
      { status: 500 }
    )
  }
}

// POST /api/layout-builder/templates - Apply template to create new layout
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { templateId, name, description, customizations = {} } = body

    if (!templateId) {
      return NextResponse.json(
        { success: false, error: 'Template ID is required' },
        { status: 400 }
      )
    }

    // Get template structure
    const templates = LayoutTemplateGenerator.getAllTemplates()
    const template = templates[templateId as keyof typeof templates]
    
    if (!template) {
      return NextResponse.json(
        { success: false, error: 'Template not found' },
        { status: 404 }
      )
    }

    // Apply customizations if provided
    let { structure, styling, responsive } = template
    
    if (customizations.colors) {
      styling = {
        ...styling,
        colors: { ...styling.colors, ...customizations.colors }
      }
    }

    if (customizations.typography) {
      styling = {
        ...styling,
        typography: { ...styling.typography, ...customizations.typography }
      }
    }

    // Create layout from template
    const layoutData = {
      name: name || `Template: ${templateId}`,
      description: description || `Layout created from ${templateId} template`,
      type: 'page',
      category: 'custom',
      structure,
      styling,
      responsive,
      isTemplate: false,
      tags: ['from-template', templateId],
      thumbnail: `/templates/${templateId}-thumb.jpg`
    }

    // Here you would typically save to database
    // For now, return the layout data
    return NextResponse.json({
      success: true,
      data: {
        id: `layout-${Date.now()}`,
        ...layoutData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      message: 'Layout created from template successfully'
    })
  } catch (error) {
    console.error('Error applying template:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to apply template' },
      { status: 500 }
    )
  }
}
