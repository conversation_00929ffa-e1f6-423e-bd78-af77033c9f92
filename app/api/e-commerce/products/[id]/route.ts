// Individual Product API Routes
// GET /api/e-commerce/products/[id] - Get product by ID
// PUT /api/e-commerce/products/[id] - Update product
// DELETE /api/e-commerce/products/[id] - Delete product

import { NextRequest, NextResponse } from 'next/server'
import { productService, handleEcommerceError } from '@/lib/ecommerce'
import { UpdateProductInput } from '@/lib/ecommerce/types'

interface RouteParams {
  params: {
    id: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params

    const result = await productService().getProductById(id)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Get product API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    const body = await request.json()

    const input: UpdateProductInput = {
      id,
      title: body.title,
      description: body.description,
      descriptionHtml: body.descriptionHtml,
      vendor: body.vendor,
      productType: body.productType,
      price: body.price ? {
        amount: parseFloat(body.price),
        currency: body.currency || 'ZAR'
      } : undefined,
      compareAtPrice: body.compareAtPrice ? {
        amount: parseFloat(body.compareAtPrice),
        currency: body.currency || 'ZAR'
      } : undefined,
      costPerItem: body.costPerItem ? {
        amount: parseFloat(body.costPerItem),
        currency: body.currency || 'ZAR'
      } : undefined,
      trackQuantity: body.trackQuantity,
      continueSellingWhenOutOfStock: body.continueSellingWhenOutOfStock,
      inventoryQuantity: body.inventoryQuantity ? parseInt(body.inventoryQuantity) : undefined,
      weight: body.weight ? parseFloat(body.weight) : undefined,
      weightUnit: body.weightUnit,
      dimensions: body.dimensions ? {
        length: parseFloat(body.dimensions.length),
        width: parseFloat(body.dimensions.width),
        height: parseFloat(body.dimensions.height),
        unit: body.dimensions.unit
      } : undefined,
      seo: body.seo ? {
        title: body.seo.title,
        description: body.seo.description,
        keywords: body.seo.keywords
      } : undefined,
      metafields: body.metafields,
      isGiftCard: body.isGiftCard,
      requiresShipping: body.requiresShipping,
      isTaxable: body.isTaxable,
      status: body.status,
      isVisible: body.isVisible
    }

    const result = await productService().updateProduct(input)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Update product API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params

    const result = await productService().deleteProduct(id)

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Product deleted successfully'
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Delete product API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
