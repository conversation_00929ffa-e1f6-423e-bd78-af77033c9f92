// Product Duplication API Route
// POST /api/e-commerce/products/[id]/duplicate - Duplicate a product

import { NextRequest, NextResponse } from 'next/server'
import { productService, handleEcommerceError } from '@/lib/ecommerce'

interface RouteParams {
  params: {
    id: string
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    const body = await request.json().catch(() => ({}))
    
    // Optional customization for the duplicated product
    const options = {
      titleSuffix: body.titleSuffix || ' (Copy)',
      includeInventory: body.includeInventory !== false, // Default to true
      includeImages: body.includeImages !== false, // Default to true
      includeVariants: body.includeVariants !== false, // Default to true
      includeCategories: body.includeCategories !== false, // Default to true
      includeCollections: body.includeCollections !== false, // Default to true
      status: body.status || 'draft', // Default to draft for safety
      customFields: body.customFields || {}
    }

    // Duplicate the product
    const result = await productService().duplicateProduct(id, options)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      }, { status: 201 })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Duplicate product API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
