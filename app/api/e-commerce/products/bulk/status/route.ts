// Bulk Product Status Update API Route
// PUT /api/e-commerce/products/bulk/status - Update status for multiple products

import { NextRequest, NextResponse } from 'next/server'
import { productService, handleEcommerceError } from '@/lib/ecommerce'

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!Array.isArray(body.productIds) || body.productIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Product IDs array is required and cannot be empty' },
        { status: 400 }
      )
    }

    if (!body.status) {
      return NextResponse.json(
        { success: false, error: 'Status is required' },
        { status: 400 }
      )
    }

    // Validate status value
    const validStatuses = ['active', 'draft', 'archived']
    if (!validStatuses.includes(body.status)) {
      return NextResponse.json(
        { success: false, error: `Status must be one of: ${validStatuses.join(', ')}` },
        { status: 400 }
      )
    }

    // Update products in bulk
    const result = await productService().bulkUpdateStatus(body.productIds, body.status)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: {
          updatedCount: result.data?.updatedCount || 0,
          productIds: body.productIds
        }
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Bulk status update API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
