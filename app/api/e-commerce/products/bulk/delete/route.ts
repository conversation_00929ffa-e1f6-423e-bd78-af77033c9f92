// Bulk Product Delete API Route
// DELETE /api/e-commerce/products/bulk/delete - Delete multiple products

import { NextRequest, NextResponse } from 'next/server'
import { productService, handleEcommerceError } from '@/lib/ecommerce'

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!Array.isArray(body.productIds) || body.productIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Product IDs array is required and cannot be empty' },
        { status: 400 }
      )
    }

    // Validate maximum number of products to delete at once
    if (body.productIds.length > 100) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete more than 100 products at once' },
        { status: 400 }
      )
    }

    // Delete products in bulk
    const result = await productService().bulkDeleteProducts(body.productIds, {
      force: body.force || false,
      reason: body.reason || 'Bulk delete operation'
    })

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: {
          deletedCount: result.data?.deletedCount || 0,
          skippedCount: result.data?.skippedCount || 0,
          errors: result.data?.errors || [],
          productIds: body.productIds
        }
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Bulk delete API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
