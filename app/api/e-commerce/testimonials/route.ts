import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Query parameters schema
const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  featured: z.string().optional().transform(val => val === 'true'),
  rating: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  status: z.enum(['active', 'inactive', 'pending']).optional(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = querySchema.parse(Object.fromEntries(searchParams))
    
    const { page, limit, featured, rating, status } = query
    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (featured !== undefined) {
      where.featured = featured
    }
    
    if (rating !== undefined) {
      where.rating = { gte: rating }
    }
    
    if (status) {
      where.status = status
    } else {
      // Default to active testimonials only
      where.status = 'active'
    }

    // Get testimonials with pagination
    const [testimonials, total] = await Promise.all([
      prisma.testimonial.findMany({
        where,
        orderBy: [
          { featured: 'desc' },
          { rating: 'desc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit,
        select: {
          id: true,
          name: true,
          role: true,
          content: true,
          rating: true,
          avatar: true,
          featured: true,
          status: true,
          createdAt: true,
        }
      }),
      prisma.testimonial.count({ where })
    ])

    // Transform data for frontend
    const transformedTestimonials = testimonials.map(testimonial => ({
      id: testimonial.id,
      name: testimonial.name,
      role: testimonial.role,
      content: testimonial.content,
      rating: testimonial.rating,
      avatar: testimonial.avatar || `https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=100&h=100&auto=format&fit=crop`,
      featured: testimonial.featured,
      createdAt: testimonial.createdAt,
    }))

    return NextResponse.json({
      success: true,
      data: transformedTestimonials,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Error fetching testimonials:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to fetch testimonials' },
      { status: 500 }
    )
  }
}

// Create testimonial schema
const createTestimonialSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  role: z.string().optional(),
  content: z.string().min(10, 'Content must be at least 10 characters'),
  rating: z.number().min(1).max(5),
  avatar: z.string().url().optional(),
  productId: z.string().optional(),
  orderId: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = createTestimonialSchema.parse(body)

    // Check if customer has already submitted a testimonial for this product/order
    if (validatedData.productId || validatedData.orderId) {
      const existingTestimonial = await prisma.testimonial.findFirst({
        where: {
          email: validatedData.email,
          ...(validatedData.productId && { productId: validatedData.productId }),
          ...(validatedData.orderId && { orderId: validatedData.orderId }),
        }
      })

      if (existingTestimonial) {
        return NextResponse.json(
          { success: false, error: 'You have already submitted a testimonial for this item' },
          { status: 400 }
        )
      }
    }

    // Create testimonial (pending approval by default)
    const testimonial = await prisma.testimonial.create({
      data: {
        ...validatedData,
        status: 'pending', // Requires admin approval
        featured: false,
        submittedAt: new Date(),
        ipAddress: request.ip || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      }
    })

    // TODO: Send notification to admin about new testimonial
    // await notifyAdminNewTestimonial(testimonial)

    return NextResponse.json({
      success: true,
      message: 'Testimonial submitted successfully and is pending approval',
      data: {
        id: testimonial.id,
        status: testimonial.status
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating testimonial:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid testimonial data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to submit testimonial' },
      { status: 500 }
    )
  }
}
