// User Logout API Route
// POST /api/e-commerce/auth/logout - Logout user

import { NextRequest, NextResponse } from 'next/server'
import { authService, handleEcommerceError } from '@/lib/ecommerce'

// Get service instance
const authServiceInstance = authService()

export async function POST(request: NextRequest) {
  try {
    // Get token from cookie or header
    const token = request.cookies.get('auth-token')?.value || 
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'No authentication token found' },
        { status: 401 }
      )
    }

    const result = await authServiceInstance.logout(token)

    if (result.success) {
      // Clear cookies
      const response = NextResponse.json({
        success: true,
        message: 'Logged out successfully'
      })

      response.cookies.delete('auth-token')
      response.cookies.delete('refresh-token')

      return response
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Logout API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
