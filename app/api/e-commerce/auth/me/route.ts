// Current User API Route
// GET /api/e-commerce/auth/me - Get current user
// PUT /api/e-commerce/auth/me - Update current user profile

import { NextRequest, NextResponse } from 'next/server'
import { authService, handleEcommerceError } from '@/lib/ecommerce'
import { UpdateUserInput } from '@/lib/ecommerce/types'

// Get service instance
const authServiceInstance = authService()

export async function GET(request: NextRequest) {
  try {
    // Get token from cookie or header
    const token = request.cookies.get('auth-token')?.value || 
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const result = await authServiceInstance.getCurrentUser(token)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Get current user API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get token from cookie or header
    const token = request.cookies.get('auth-token')?.value || 
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get current user to extract user ID
    const currentUserResult = await authServiceInstance.getCurrentUser(token)
    if (!currentUserResult.success || !currentUserResult.data) {
      return NextResponse.json(
        { success: false, error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    const body = await request.json()

    const input: UpdateUserInput = {
      id: currentUserResult.data.id,
      email: body.email,
      firstName: body.firstName,
      lastName: body.lastName,
      displayName: body.displayName,
      phone: body.phone,
      dateOfBirth: body.dateOfBirth ? new Date(body.dateOfBirth) : undefined,
      gender: body.gender,
      acceptsMarketing: body.acceptsMarketing,
      preferredLanguage: body.preferredLanguage,
      preferredCurrency: body.preferredCurrency,
      timezone: body.timezone,
      avatar: body.avatar,
      bio: body.bio,
      metafields: body.metafields,
      tags: body.tags,
      notes: body.notes
    }

    const result = await authServiceInstance.updateProfile(input)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Update profile API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
