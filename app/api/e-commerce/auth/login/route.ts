// User Login API Route
// POST /api/e-commerce/auth/login - Login user

import { NextRequest, NextResponse } from 'next/server'
import { authService, handleEcommerceError } from '@/lib/ecommerce'
import { LoginCredentials } from '@/lib/ecommerce/types'

// Get service instance
const authServiceInstance = authService()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.email || !body.password) {
      return NextResponse.json(
        { success: false, error: 'Email and password are required' },
        { status: 400 }
      )
    }

    const credentials: LoginCredentials = {
      email: body.email.toLowerCase().trim(),
      password: body.password,
      rememberMe: body.rememberMe || false
    }

    const result = await authServiceInstance.login(credentials)

    if (result.success) {
      // Set HTTP-only cookie for the token
      const response = NextResponse.json({
        success: true,
        data: {
          user: result.user,
          expiresAt: result.expiresAt
        }
      })

      // Set secure cookies
      if (result.token) {
        const maxAge = credentials.rememberMe 
          ? 60 * 60 * 24 * 30 // 30 days if remember me
          : 60 * 60 * 24 * 7  // 7 days default

        response.cookies.set('auth-token', result.token, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge
        })
      }

      if (result.refreshToken) {
        response.cookies.set('refresh-token', result.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 60 * 60 * 24 * 30 // 30 days
        })
      }

      return response
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Login API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
