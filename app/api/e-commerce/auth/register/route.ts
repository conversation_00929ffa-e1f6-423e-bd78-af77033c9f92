// User Registration API Route
// POST /api/e-commerce/auth/register - Register a new user

import { NextRequest, NextResponse } from 'next/server'
import { authService, handleEcommerceError } from '@/lib/ecommerce'
import { RegisterCredentials } from '@/lib/ecommerce/types'

// Get service instance
const authServiceInstance = authService()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.email || !body.password) {
      return NextResponse.json(
        { success: false, error: 'Email and password are required' },
        { status: 400 }
      )
    }

    if (body.password.length < 8) {
      return NextResponse.json(
        { success: false, error: 'Password must be at least 8 characters long' },
        { status: 400 }
      )
    }

    const credentials: RegisterCredentials = {
      email: body.email.toLowerCase().trim(),
      password: body.password,
      firstName: body.firstName?.trim(),
      lastName: body.lastName?.trim(),
      phone: body.phone?.trim(),
      acceptsMarketing: body.acceptsMarketing || false
    }

    const result = await authServiceInstance.register(credentials)

    if (result.success) {
      // Set HTTP-only cookie for the token
      const response = NextResponse.json({
        success: true,
        data: {
          user: result.user,
          expiresAt: result.expiresAt
        }
      }, { status: 201 })

      // Set secure cookies
      if (result.token) {
        response.cookies.set('auth-token', result.token, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 60 * 60 * 24 * 7 // 7 days
        })
      }

      if (result.refreshToken) {
        response.cookies.set('refresh-token', result.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 60 * 60 * 24 * 30 // 30 days
        })
      }

      return response
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Register API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
