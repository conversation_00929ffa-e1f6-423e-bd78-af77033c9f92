// Categories API Routes
// GET /api/e-commerce/categories - List categories
// POST /api/e-commerce/categories - Create a new category

import { NextRequest, NextResponse } from 'next/server'
import { handleEcommerceError } from '@/lib/ecommerce'
import { prisma } from '@/lib/ecommerce/config/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeChildren = searchParams.get('includeChildren') === 'true'
    const parentId = searchParams.get('parentId')
    const isVisible = searchParams.get('isVisible')

    // Build where clause
    const where: any = {}
    if (parentId !== null) {
      where.parentId = parentId === 'null' ? null : parentId
    }
    if (isVisible !== null) {
      where.isVisible = isVisible === 'true'
    }

    // Fetch categories
    const categories = await prisma.productCategory.findMany({
      where,
      include: {
        children: includeChildren ? {
          include: {
            children: true,
            _count: {
              select: { products: true }
            }
          }
        } : false,
        _count: {
          select: { products: true }
        }
      },
      orderBy: [
        { position: 'asc' },
        { name: 'asc' }
      ]
    })

    // Transform data to include product count
    const transformedCategories = categories.map(category => ({
      ...category,
      productCount: category._count.products,
      children: category.children?.map(child => ({
        ...child,
        productCount: child._count?.products || 0
      }))
    }))

    return NextResponse.json({
      success: true,
      data: transformedCategories
    })
  } catch (error) {
    console.error('Categories API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { success: false, error: 'Category name is required' },
        { status: 400 }
      )
    }

    // Generate slug if not provided
    const slug = body.slug || body.name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')

    // Check if slug already exists
    const existingCategory = await prisma.productCategory.findUnique({
      where: { slug }
    })

    if (existingCategory) {
      return NextResponse.json(
        { success: false, error: `Category with slug '${slug}' already exists` },
        { status: 400 }
      )
    }

    // Create category
    const category = await prisma.productCategory.create({
      data: {
        name: body.name,
        slug,
        description: body.description,
        image: body.image,
        parentId: body.parentId,
        position: body.position || 0,
        isVisible: body.isVisible !== false,
        seoTitle: body.seoTitle,
        seoDescription: body.seoDescription
      },
      include: {
        _count: {
          select: { products: true }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        ...category,
        productCount: category._count.products
      }
    }, { status: 201 })
  } catch (error) {
    console.error('Create category API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
