// Analytics API Routes
// GET /api/e-commerce/analytics - Get comprehensive analytics data

import { NextRequest, NextResponse } from 'next/server'
import { AnalyticsService } from '@/lib/ecommerce/modules/analytics/analytics-service'
import { handleEcommerceError } from '@/lib/ecommerce'
import { AnalyticsTimeRange } from '@/lib/ecommerce/types'

const analyticsService = new AnalyticsService()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const timeRange = (searchParams.get('timeRange') as AnalyticsTimeRange) || '30d'
    
    // Validate time range
    const validTimeRanges: AnalyticsTimeRange[] = ['7d', '30d', '90d', '1y']
    if (!validTimeRanges.includes(timeRange)) {
      return NextResponse.json(
        { success: false, error: 'Invalid time range. Must be one of: 7d, 30d, 90d, 1y' },
        { status: 400 }
      )
    }

    const result = await analyticsService.getAnalytics(timeRange)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Analytics API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
