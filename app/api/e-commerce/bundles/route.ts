// Product Bundles API Routes
// GET /api/e-commerce/bundles - Get all bundles
// POST /api/e-commerce/bundles - Create a new bundle

import { NextRequest, NextResponse } from 'next/server'
import { ProductBundleService } from '@/lib/ecommerce/modules/products/product-bundle-service'
import { handleEcommerceError } from '@/lib/ecommerce'
import { CreateBundleInput } from '@/lib/ecommerce/types'

const bundleService = new ProductBundleService()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const params = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      isActive: searchParams.get('isActive') === 'true' ? true : 
                searchParams.get('isActive') === 'false' ? false : undefined,
      search: searchParams.get('search') || undefined
    }

    const result = await bundleService.getBundles(params)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data?.data || [],
        pagination: result.data?.pagination
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Get bundles API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.title) {
      return NextResponse.json(
        { success: false, error: 'Bundle title is required' },
        { status: 400 }
      )
    }

    if (!body.price) {
      return NextResponse.json(
        { success: false, error: 'Bundle price is required' },
        { status: 400 }
      )
    }

    if (!body.items || !Array.isArray(body.items) || body.items.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Bundle must contain at least one item' },
        { status: 400 }
      )
    }

    const input: CreateBundleInput = {
      title: body.title,
      slug: body.slug,
      description: body.description,
      price: {
        amount: parseFloat(body.price.amount || body.price),
        currency: body.price.currency || 'ZAR'
      },
      compareAtPrice: body.compareAtPrice ? {
        amount: parseFloat(body.compareAtPrice.amount || body.compareAtPrice),
        currency: body.compareAtPrice.currency || 'ZAR'
      } : undefined,
      isActive: body.isActive ?? true,
      items: body.items.map((item: any, index: number) => ({
        productId: item.productId,
        variantId: item.variantId,
        quantity: parseInt(item.quantity) || 1,
        discount: item.discount ? parseFloat(item.discount) : undefined,
        position: item.position ?? index
      }))
    }

    const result = await bundleService.createBundle(input)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      }, { status: 201 })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Create bundle API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
