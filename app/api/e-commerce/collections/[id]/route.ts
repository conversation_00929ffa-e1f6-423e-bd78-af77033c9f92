// Individual Collection API Routes
// GET /api/e-commerce/collections/[id] - Get a specific collection
// PUT /api/e-commerce/collections/[id] - Update a collection
// DELETE /api/e-commerce/collections/[id] - Delete a collection

import { NextRequest, NextResponse } from 'next/server'
import { handleEcommerceError } from '@/lib/ecommerce'
import { prisma } from '@/lib/ecommerce/config/database'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const collection = await prisma.productCollection.findUnique({
      where: { id: params.id },
      include: {
        products: {
          include: {
            product: {
              include: {
                images: true,
                categories: true
              }
            }
          }
        }
      }
    })

    if (!collection) {
      return NextResponse.json(
        { success: false, error: 'Collection not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        ...collection,
        productCount: collection.products.length
      }
    })
  } catch (error) {
    console.error('Get collection API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()

    // Check if collection exists
    const existingCollection = await prisma.productCollection.findUnique({
      where: { id: params.id }
    })

    if (!existingCollection) {
      return NextResponse.json(
        { success: false, error: 'Collection not found' },
        { status: 404 }
      )
    }

    // Generate slug if title is being updated
    let slug = body.slug
    if (body.title && !slug) {
      slug = body.title.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '')
    }

    // Check if new slug conflicts with existing collections (excluding current one)
    if (slug && slug !== existingCollection.slug) {
      const conflictingCollection = await prisma.productCollection.findUnique({
        where: { slug }
      })

      if (conflictingCollection && conflictingCollection.id !== params.id) {
        return NextResponse.json(
          { success: false, error: `Collection with slug '${slug}' already exists` },
          { status: 400 }
        )
      }
    }

    // Update collection
    const collection = await prisma.productCollection.update({
      where: { id: params.id },
      data: {
        title: body.title,
        slug: slug || existingCollection.slug,
        description: body.description,
        image: body.image,
        sortOrder: body.sortOrder,
        isVisible: body.isVisible,
        seoTitle: body.seoTitle,
        seoDescription: body.seoDescription
      },
      include: {
        products: true
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        ...collection,
        productCount: collection.products.length
      }
    })
  } catch (error) {
    console.error('Update collection API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if collection exists
    const existingCollection = await prisma.productCollection.findUnique({
      where: { id: params.id },
      include: {
        products: true
      }
    })

    if (!existingCollection) {
      return NextResponse.json(
        { success: false, error: 'Collection not found' },
        { status: 404 }
      )
    }

    // Check if collection has products
    if (existingCollection.products.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Cannot delete collection with ${existingCollection.products.length} products. Remove products first.` 
        },
        { status: 400 }
      )
    }

    // Delete collection
    await prisma.productCollection.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      success: true,
      message: 'Collection deleted successfully'
    })
  } catch (error) {
    console.error('Delete collection API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}