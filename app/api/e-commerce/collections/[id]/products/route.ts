// Collection Products Management API Routes
// POST /api/e-commerce/collections/[id]/products - Add products to collection
// DELETE /api/e-commerce/collections/[id]/products - Remove products from collection

import { NextRequest, NextResponse } from 'next/server'
import { handleEcommerceError } from '@/lib/ecommerce'
import { prisma } from '@/lib/ecommerce/config/database'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { productIds } = body

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Product IDs array is required' },
        { status: 400 }
      )
    }

    // Check if collection exists
    const collection = await prisma.productCollection.findUnique({
      where: { id: params.id }
    })

    if (!collection) {
      return NextResponse.json(
        { success: false, error: 'Collection not found' },
        { status: 404 }
      )
    }

    // Verify all products exist
    const products = await prisma.product.findMany({
      where: { id: { in: productIds } }
    })

    if (products.length !== productIds.length) {
      const foundIds = products.map(p => p.id)
      const missingIds = productIds.filter(id => !foundIds.includes(id))
      return NextResponse.json(
        { success: false, error: `Products not found: ${missingIds.join(', ')}` },
        { status: 400 }
      )
    }

    // Add products to collection using the junction table
    const createData = productIds.map((productId, index) => ({
      productId,
      collectionId: params.id,
      position: index
    }))

    // Create the relationships (ignore duplicates)
    await prisma.productCollectionRelation.createMany({
      data: createData,
      skipDuplicates: true
    })

    // Fetch updated collection
    const updatedCollection = await prisma.productCollection.findUnique({
      where: { id: params.id },
      include: {
        products: {
          include: {
            product: {
              include: {
                images: true,
                categories: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        ...updatedCollection,
        productCount: updatedCollection?.products.length || 0
      },
      message: `${productIds.length} product(s) added to collection`
    })
  } catch (error) {
    console.error('Add products to collection API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { productIds } = body

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Product IDs array is required' },
        { status: 400 }
      )
    }

    // Check if collection exists
    const collection = await prisma.productCollection.findUnique({
      where: { id: params.id }
    })

    if (!collection) {
      return NextResponse.json(
        { success: false, error: 'Collection not found' },
        { status: 404 }
      )
    }

    // Remove products from collection using the junction table
    await prisma.productCollectionRelation.deleteMany({
      where: {
        collectionId: params.id,
        productId: { in: productIds }
      }
    })

    // Fetch updated collection
    const updatedCollection = await prisma.productCollection.findUnique({
      where: { id: params.id },
      include: {
        products: {
          include: {
            product: {
              include: {
                images: true,
                categories: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        ...updatedCollection,
        productCount: updatedCollection?.products.length || 0
      },
      message: `${productIds.length} product(s) removed from collection`
    })
  } catch (error) {
    console.error('Remove products from collection API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}