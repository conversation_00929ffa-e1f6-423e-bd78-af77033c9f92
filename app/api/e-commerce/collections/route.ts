// Collections API Routes
// GET /api/e-commerce/collections - List collections
// POST /api/e-commerce/collections - Create a new collection

import { NextRequest, NextResponse } from 'next/server'
import { handleEcommerceError } from '@/lib/ecommerce'
import { prisma } from '@/lib/ecommerce/config/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const isVisible = searchParams.get('isVisible')
    const sortOrder = searchParams.get('sortOrder')

    // Build where clause
    const where: any = {}
    if (isVisible !== null) {
      where.isVisible = isVisible === 'true'
    }
    if (sortOrder) {
      where.sortOrder = sortOrder
    }

    // Fetch collections
    const collections = await prisma.productCollection.findMany({
      where,
      include: {
        products: {
          include: {
            product: {
              select: {
                id: true,
                title: true
              }
            }
          }
        }
      },
      orderBy: [
        { createdAt: 'desc' },
        { title: 'asc' }
      ]
    })

    // Transform data to include product count
    const transformedCollections = collections.map(collection => ({
      ...collection,
      productCount: collection.products.length
    }))

    return NextResponse.json({
      success: true,
      data: transformedCollections
    })
  } catch (error) {
    console.error('Collections API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.title) {
      return NextResponse.json(
        { success: false, error: 'Collection title is required' },
        { status: 400 }
      )
    }

    // Generate slug if not provided
    const slug = body.slug || body.title.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')

    // Check if slug already exists
    const existingCollection = await prisma.productCollection.findUnique({
      where: { slug }
    })

    if (existingCollection) {
      return NextResponse.json(
        { success: false, error: `Collection with slug '${slug}' already exists` },
        { status: 400 }
      )
    }

    // Create collection
    const collection = await prisma.productCollection.create({
      data: {
        title: body.title,
        slug,
        description: body.description,
        image: body.image,
        sortOrder: body.sortOrder || 'manual',
        isVisible: body.isVisible !== false,
        seoTitle: body.seoTitle,
        seoDescription: body.seoDescription
      },
      include: {
        products: true
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        ...collection,
        productCount: collection.products.length
      }
    }, { status: 201 })
  } catch (error) {
    console.error('Create collection API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}