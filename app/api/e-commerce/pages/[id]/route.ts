// Individual Page API Routes
// GET /api/e-commerce/pages/[id] - Get a specific page
// PUT /api/e-commerce/pages/[id] - Update a specific page
// DELETE /api/e-commerce/pages/[id] - Delete a specific page

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { handleEcommerceError } from '@/lib/ecommerce'

interface RouteParams {
  params: {
    id: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params

    const page = await prisma.page.findUnique({
      where: { id },
      include: {
        blocks: {
          orderBy: { position: 'asc' }
        },
        versions: {
          orderBy: { versionNumber: 'desc' },
          take: 5 // Get last 5 versions
        }
      }
    })

    if (!page) {
      return NextResponse.json(
        { success: false, error: 'Page not found' },
        { status: 404 }
      )
    }

    // Transform response
    const transformedPage = {
      id: page.id,
      title: page.title,
      slug: page.slug,
      description: page.description,
      status: page.status,
      type: page.type,
      template: page.template,
      seoTitle: page.seoTitle,
      seoDescription: page.seoDescription,
      seoKeywords: page.seoKeywords,
      ogImage: page.ogImage,
      publishedAt: page.publishedAt,
      scheduledAt: page.scheduledAt,
      expiresAt: page.expiresAt,
      isHomePage: page.isHomePage,
      isLandingPage: page.isLandingPage,
      requiresAuth: page.requiresAuth,
      allowComments: page.allowComments,
      viewCount: page.viewCount,
      shareCount: page.shareCount,
      metadata: page.metadata,
      customCss: page.customCss,
      customJs: page.customJs,
      createdBy: page.createdBy,
      updatedBy: page.updatedBy,
      createdAt: page.createdAt,
      updatedAt: page.updatedAt,
      blocks: page.blocks.map(block => ({
        id: block.id,
        type: block.blockType,
        position: block.position,
        isVisible: block.isVisible,
        configuration: block.configuration,
        content: block.content,
        styling: block.styling,
        responsive: block.responsive,
        animation: block.animation,
        conditions: block.conditions,
        createdAt: block.createdAt,
        updatedAt: block.updatedAt,
      })),
      versions: page.versions.map(version => ({
        id: version.id,
        versionNumber: version.versionNumber,
        title: version.title,
        description: version.description,
        isPublished: version.isPublished,
        publishedAt: version.publishedAt,
        createdBy: version.createdBy,
        createdAt: version.createdAt,
      })),
    }

    return NextResponse.json({
      success: true,
      data: transformedPage
    })

  } catch (error) {
    console.error('Error fetching page:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params
    const body = await request.json()

    const {
      title,
      slug,
      description,
      status,
      type,
      template,
      seoTitle,
      seoDescription,
      seoKeywords,
      ogImage,
      publishedAt,
      scheduledAt,
      expiresAt,
      isHomePage,
      isLandingPage,
      requiresAuth,
      allowComments,
      metadata,
      customCss,
      customJs,
      updatedBy,
      blocks,
      createVersion = false,
    } = body

    // Check if page exists
    const existingPage = await prisma.page.findUnique({
      where: { id },
      include: { blocks: true }
    })

    if (!existingPage) {
      return NextResponse.json(
        { success: false, error: 'Page not found' },
        { status: 404 }
      )
    }

    // Check slug uniqueness if changed
    if (slug && slug !== existingPage.slug) {
      const slugExists = await prisma.page.findUnique({
        where: { slug }
      })

      if (slugExists) {
        return NextResponse.json(
          { success: false, error: 'A page with this slug already exists' },
          { status: 400 }
        )
      }
    }

    // Update page with blocks in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create version if requested
      if (createVersion) {
        const latestVersion = await tx.pageVersion.findFirst({
          where: { pageId: id },
          orderBy: { versionNumber: 'desc' }
        })

        const nextVersionNumber = (latestVersion?.versionNumber || 0) + 1

        await tx.pageVersion.create({
          data: {
            pageId: id,
            versionNumber: nextVersionNumber,
            title: existingPage.title,
            description: existingPage.description,
            configuration: {
              blocks: existingPage.blocks,
              settings: {
                title: existingPage.title,
                description: existingPage.description,
                seoTitle: existingPage.seoTitle,
                seoDescription: existingPage.seoDescription,
                seoKeywords: existingPage.seoKeywords,
                ogImage: existingPage.ogImage,
                customCss: existingPage.customCss,
                customJs: existingPage.customJs,
                requiresAuth: existingPage.requiresAuth,
                allowComments: existingPage.allowComments,
              }
            },
            createdBy: updatedBy,
          }
        })
      }

      // Update the page
      const updatedPage = await tx.page.update({
        where: { id },
        data: {
          ...(title !== undefined && { title }),
          ...(slug !== undefined && { slug }),
          ...(description !== undefined && { description }),
          ...(status !== undefined && { status }),
          ...(type !== undefined && { type }),
          ...(template !== undefined && { template }),
          ...(seoTitle !== undefined && { seoTitle }),
          ...(seoDescription !== undefined && { seoDescription }),
          ...(seoKeywords !== undefined && { seoKeywords }),
          ...(ogImage !== undefined && { ogImage }),
          ...(publishedAt !== undefined && { publishedAt: publishedAt ? new Date(publishedAt) : null }),
          ...(scheduledAt !== undefined && { scheduledAt: scheduledAt ? new Date(scheduledAt) : null }),
          ...(expiresAt !== undefined && { expiresAt: expiresAt ? new Date(expiresAt) : null }),
          ...(isHomePage !== undefined && { isHomePage }),
          ...(isLandingPage !== undefined && { isLandingPage }),
          ...(requiresAuth !== undefined && { requiresAuth }),
          ...(allowComments !== undefined && { allowComments }),
          ...(metadata !== undefined && { metadata }),
          ...(customCss !== undefined && { customCss }),
          ...(customJs !== undefined && { customJs }),
          ...(updatedBy !== undefined && { updatedBy }),
        }
      })

      // Update blocks if provided
      if (blocks !== undefined) {
        // Delete existing blocks
        await tx.pageBlock.deleteMany({
          where: { pageId: id }
        })

        // Create new blocks
        if (blocks.length > 0) {
          await tx.pageBlock.createMany({
            data: blocks.map((block: any, index: number) => ({
              pageId: id,
              blockType: block.type,
              position: block.position ?? index,
              isVisible: block.isVisible ?? true,
              configuration: block.configuration || {},
              content: block.content || {},
              styling: block.styling || {},
              responsive: block.responsive || {},
              animation: block.animation || {},
              conditions: block.conditions || {},
            }))
          })
        }
      }

      // Fetch the complete updated page
      return await tx.page.findUnique({
        where: { id },
        include: {
          blocks: {
            orderBy: { position: 'asc' }
          }
        }
      })
    })

    if (!result) {
      throw new Error('Failed to update page')
    }

    // Transform response
    const transformedPage = {
      id: result.id,
      title: result.title,
      slug: result.slug,
      description: result.description,
      status: result.status,
      type: result.type,
      template: result.template,
      seoTitle: result.seoTitle,
      seoDescription: result.seoDescription,
      seoKeywords: result.seoKeywords,
      ogImage: result.ogImage,
      publishedAt: result.publishedAt,
      scheduledAt: result.scheduledAt,
      expiresAt: result.expiresAt,
      isHomePage: result.isHomePage,
      isLandingPage: result.isLandingPage,
      requiresAuth: result.requiresAuth,
      allowComments: result.allowComments,
      viewCount: result.viewCount,
      shareCount: result.shareCount,
      metadata: result.metadata,
      customCss: result.customCss,
      customJs: result.customJs,
      createdBy: result.createdBy,
      updatedBy: result.updatedBy,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      blocks: result.blocks.map(block => ({
        id: block.id,
        type: block.blockType,
        position: block.position,
        isVisible: block.isVisible,
        configuration: block.configuration,
        content: block.content,
        styling: block.styling,
        responsive: block.responsive,
        animation: block.animation,
        conditions: block.conditions,
        createdAt: block.createdAt,
        updatedAt: block.updatedAt,
      })),
    }

    return NextResponse.json({
      success: true,
      data: transformedPage
    })

  } catch (error) {
    console.error('Error updating page:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params

    // Check if page exists
    const existingPage = await prisma.page.findUnique({
      where: { id }
    })

    if (!existingPage) {
      return NextResponse.json(
        { success: false, error: 'Page not found' },
        { status: 404 }
      )
    }

    // Delete page (blocks will be deleted due to cascade)
    await prisma.page.delete({
      where: { id }
    })

    return NextResponse.json({
      success: true,
      message: 'Page deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting page:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
