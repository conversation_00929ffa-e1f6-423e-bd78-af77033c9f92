// Slug Validation API Route
// GET /api/e-commerce/pages/validate-slug?slug=example&excludeId=123

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { handleEcommerceError } from '@/lib/ecommerce'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const slug = searchParams.get('slug')
    const excludeId = searchParams.get('excludeId')

    if (!slug) {
      return NextResponse.json(
        { success: false, error: 'Slug parameter is required' },
        { status: 400 }
      )
    }

    // Check if slug exists
    const existingPage = await prisma.page.findUnique({
      where: { slug },
      select: { id: true }
    })

    // If no existing page found, slug is unique
    if (!existingPage) {
      return NextResponse.json({
        success: true,
        isUnique: true
      })
    }

    // If excludeId is provided and matches the existing page, slug is still unique for this page
    if (excludeId && existingPage.id === excludeId) {
      return NextResponse.json({
        success: true,
        isUnique: true
      })
    }

    // Slug is not unique
    return NextResponse.json({
      success: true,
      isUnique: false
    })

  } catch (error) {
    console.error('Error validating slug:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
