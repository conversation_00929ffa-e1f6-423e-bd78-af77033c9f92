// Pages API Routes
// GET /api/e-commerce/pages - List pages with filtering and pagination
// POST /api/e-commerce/pages - Create a new page

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { handleEcommerceError } from '@/lib/ecommerce'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50)
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const type = searchParams.get('type') || ''
    const sortBy = searchParams.get('sortBy') || 'updatedAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { slug: { contains: search, mode: 'insensitive' } },
      ]
    }
    
    if (status) {
      where.status = status
    }
    
    if (type) {
      where.type = type
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Fetch pages with pagination
    const [pages, total] = await Promise.all([
      prisma.page.findMany({
        where,
        include: {
          blocks: {
            orderBy: { position: 'asc' }
          },
          _count: {
            select: { blocks: true }
          }
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
      }),
      prisma.page.count({ where })
    ])

    // Transform data
    const transformedPages = pages.map(page => ({
      id: page.id,
      title: page.title,
      slug: page.slug,
      description: page.description,
      status: page.status,
      type: page.type,
      template: page.template,
      seoTitle: page.seoTitle,
      seoDescription: page.seoDescription,
      seoKeywords: page.seoKeywords,
      ogImage: page.ogImage,
      publishedAt: page.publishedAt,
      scheduledAt: page.scheduledAt,
      expiresAt: page.expiresAt,
      isHomePage: page.isHomePage,
      isLandingPage: page.isLandingPage,
      requiresAuth: page.requiresAuth,
      allowComments: page.allowComments,
      viewCount: page.viewCount,
      shareCount: page.shareCount,
      metadata: page.metadata,
      customCss: page.customCss,
      customJs: page.customJs,
      createdBy: page.createdBy,
      updatedBy: page.updatedBy,
      createdAt: page.createdAt,
      updatedAt: page.updatedAt,
      blockCount: page._count.blocks,
      blocks: page.blocks.map(block => ({
        id: block.id,
        type: block.blockType,
        position: block.position,
        isVisible: block.isVisible,
        configuration: block.configuration,
        content: block.content,
        styling: block.styling,
        responsive: block.responsive,
        animation: block.animation,
        conditions: block.conditions,
        createdAt: block.createdAt,
        updatedAt: block.updatedAt,
      })),
    }))

    return NextResponse.json({
      success: true,
      data: {
        pages: transformedPages,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        }
      }
    })

  } catch (error) {
    console.error('Error fetching pages:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const {
      title,
      slug,
      description,
      status = 'draft',
      type = 'custom',
      template,
      seoTitle,
      seoDescription,
      seoKeywords = [],
      ogImage,
      publishedAt,
      scheduledAt,
      expiresAt,
      isHomePage = false,
      isLandingPage = false,
      requiresAuth = false,
      allowComments = false,
      metadata,
      customCss,
      customJs,
      createdBy,
      blocks = [],
    } = body

    // Validate required fields
    if (!title || !slug) {
      return NextResponse.json(
        { success: false, error: 'Title and slug are required' },
        { status: 400 }
      )
    }

    // Check if slug is unique
    const existingPage = await prisma.page.findUnique({
      where: { slug }
    })

    if (existingPage) {
      return NextResponse.json(
        { success: false, error: 'A page with this slug already exists' },
        { status: 400 }
      )
    }

    // Create page with blocks in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the page
      const page = await tx.page.create({
        data: {
          title,
          slug,
          description,
          status,
          type,
          template,
          seoTitle,
          seoDescription,
          seoKeywords,
          ogImage,
          publishedAt: publishedAt ? new Date(publishedAt) : null,
          scheduledAt: scheduledAt ? new Date(scheduledAt) : null,
          expiresAt: expiresAt ? new Date(expiresAt) : null,
          isHomePage,
          isLandingPage,
          requiresAuth,
          allowComments,
          metadata,
          customCss,
          customJs,
          createdBy,
          updatedBy: createdBy,
        }
      })

      // Create blocks if provided
      if (blocks.length > 0) {
        await tx.pageBlock.createMany({
          data: blocks.map((block: any, index: number) => ({
            pageId: page.id,
            blockType: block.type,
            position: block.position ?? index,
            isVisible: block.isVisible ?? true,
            configuration: block.configuration || {},
            content: block.content || {},
            styling: block.styling || {},
            responsive: block.responsive || {},
            animation: block.animation || {},
            conditions: block.conditions || {},
          }))
        })
      }

      // Fetch the complete page with blocks
      return await tx.page.findUnique({
        where: { id: page.id },
        include: {
          blocks: {
            orderBy: { position: 'asc' }
          }
        }
      })
    })

    if (!result) {
      throw new Error('Failed to create page')
    }

    // Transform response
    const transformedPage = {
      id: result.id,
      title: result.title,
      slug: result.slug,
      description: result.description,
      status: result.status,
      type: result.type,
      template: result.template,
      seoTitle: result.seoTitle,
      seoDescription: result.seoDescription,
      seoKeywords: result.seoKeywords,
      ogImage: result.ogImage,
      publishedAt: result.publishedAt,
      scheduledAt: result.scheduledAt,
      expiresAt: result.expiresAt,
      isHomePage: result.isHomePage,
      isLandingPage: result.isLandingPage,
      requiresAuth: result.requiresAuth,
      allowComments: result.allowComments,
      viewCount: result.viewCount,
      shareCount: result.shareCount,
      metadata: result.metadata,
      customCss: result.customCss,
      customJs: result.customJs,
      createdBy: result.createdBy,
      updatedBy: result.updatedBy,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      blocks: result.blocks.map(block => ({
        id: block.id,
        type: block.blockType,
        position: block.position,
        isVisible: block.isVisible,
        configuration: block.configuration,
        content: block.content,
        styling: block.styling,
        responsive: block.responsive,
        animation: block.animation,
        conditions: block.conditions,
        createdAt: block.createdAt,
        updatedAt: block.updatedAt,
      })),
    }

    return NextResponse.json({
      success: true,
      data: transformedPage
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating page:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
