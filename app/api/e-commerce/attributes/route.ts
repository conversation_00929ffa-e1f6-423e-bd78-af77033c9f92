// Product Attributes API Routes
// GET /api/e-commerce/attributes - Get all attributes
// POST /api/e-commerce/attributes - Create a new attribute

import { NextRequest, NextResponse } from 'next/server'
import { ProductAttributeService } from '@/lib/ecommerce/modules/products/product-attribute-service'
import { handleEcommerceError } from '@/lib/ecommerce'
import { CreateAttributeInput } from '@/lib/ecommerce/types'

const attributeService = new ProductAttributeService()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const params = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '50'),
      type: searchParams.get('type') || undefined,
      isVariant: searchParams.get('isVariant') === 'true' ? true : 
                 searchParams.get('isVariant') === 'false' ? false : undefined,
      isFilter: searchParams.get('isFilter') === 'true' ? true : 
                searchParams.get('isFilter') === 'false' ? false : undefined
    }

    const result = await attributeService.getAttributes(params)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data?.data || [],
        pagination: result.data?.pagination
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Get attributes API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { success: false, error: 'Attribute name is required' },
        { status: 400 }
      )
    }

    if (!body.type) {
      return NextResponse.json(
        { success: false, error: 'Attribute type is required' },
        { status: 400 }
      )
    }

    const input: CreateAttributeInput = {
      name: body.name,
      slug: body.slug,
      type: body.type,
      description: body.description,
      isRequired: body.isRequired ?? false,
      isVariant: body.isVariant ?? false,
      isFilter: body.isFilter ?? false,
      position: body.position ?? 0,
      options: body.options || [],
      validation: body.validation
    }

    const result = await attributeService.createAttribute(input)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      }, { status: 201 })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Create attribute API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
