// Individual Product Attribute API Routes
// GET /api/e-commerce/attributes/[id] - Get attribute by ID
// PUT /api/e-commerce/attributes/[id] - Update attribute
// DELETE /api/e-commerce/attributes/[id] - Delete attribute

import { NextRequest, NextResponse } from 'next/server'
import { ProductAttributeService } from '@/lib/ecommerce/modules/products/product-attribute-service'
import { handleEcommerceError } from '@/lib/ecommerce'
import { UpdateAttributeInput } from '@/lib/ecommerce/types'

interface RouteParams {
  params: {
    id: string
  }
}

const attributeService = new ProductAttributeService()

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params

    const result = await attributeService.getAttributeById(id)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Get attribute API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    const body = await request.json()

    const input: UpdateAttributeInput = {
      id,
      name: body.name,
      slug: body.slug,
      type: body.type,
      description: body.description,
      isRequired: body.isRequired,
      isVariant: body.isVariant,
      isFilter: body.isFilter,
      position: body.position,
      options: body.options,
      validation: body.validation
    }

    const result = await attributeService.updateAttribute(input)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Update attribute API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params

    const result = await attributeService.deleteAttribute(id)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Delete attribute API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
