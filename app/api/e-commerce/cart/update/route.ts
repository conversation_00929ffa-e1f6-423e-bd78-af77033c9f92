// Update Cart Item API Route
// PUT /api/e-commerce/cart/update - Update cart item quantity

import { NextRequest, NextResponse } from 'next/server'
import { cartService, handleEcommerceError } from '@/lib/ecommerce'
import { UpdateCartItemInput } from '@/lib/ecommerce/types'

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { cartItemId, quantity } = body

    if (!cartItemId || quantity === undefined) {
      return NextResponse.json(
        { success: false, error: 'Cart item ID and quantity are required' },
        { status: 400 }
      )
    }

    const updateInput: UpdateCartItemInput = {
      cartItemId,
      quantity
    }

    const result = await cartService.updateCartItem(updateInput)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Update cart item API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
