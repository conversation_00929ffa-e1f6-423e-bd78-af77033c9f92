// Cart Items API Routes
// POST /api/e-commerce/cart/items - Add item to cart

import { NextRequest, NextResponse } from 'next/server'
import { cartService, handleEcommerceError } from '@/lib/ecommerce'
import { AddToCartInput } from '@/lib/ecommerce/types'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.cartId || !body.productId || !body.quantity) {
      return NextResponse.json(
        { success: false, error: 'Cart ID, product ID, and quantity are required' },
        { status: 400 }
      )
    }

    if (body.quantity <= 0) {
      return NextResponse.json(
        { success: false, error: 'Quantity must be greater than 0' },
        { status: 400 }
      )
    }

    const input: AddToCartInput = {
      productId: body.productId,
      variantId: body.variantId,
      quantity: parseInt(body.quantity),
      customAttributes: body.customAttributes,
      personalizedMessage: body.personalizedMessage,
      giftWrap: body.giftWrap || false
    }

    const result = await cartService.addToCart(body.cartId, input)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Add to cart API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
