// Cart Item API Routes
// PUT /api/e-commerce/cart/items/[itemId] - Update cart item
// DELETE /api/e-commerce/cart/items/[itemId] - Remove cart item

import { NextRequest, NextResponse } from 'next/server'
import { cartService, handleEcommerceError } from '@/lib/ecommerce'
import { UpdateCartItemInput, RemoveFromCartInput } from '@/lib/ecommerce/types'

interface RouteParams {
  params: {
    itemId: string
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { itemId } = params
    const body = await request.json()
    
    if (!body.cartId) {
      return NextResponse.json(
        { success: false, error: 'Cart ID is required' },
        { status: 400 }
      )
    }

    const input: UpdateCartItemInput = {
      itemId,
      quantity: body.quantity !== undefined ? parseInt(body.quantity) : undefined,
      customAttributes: body.customAttributes,
      personalizedMessage: body.personalizedMessage,
      giftWrap: body.giftWrap
    }

    const result = await cartService.updateCartItem(body.cartId, input)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Update cart item API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { itemId } = params
    const { searchParams } = new URL(request.url)
    const cartId = searchParams.get('cartId')
    
    if (!cartId) {
      return NextResponse.json(
        { success: false, error: 'Cart ID is required' },
        { status: 400 }
      )
    }

    const input: RemoveFromCartInput = {
      itemId
    }

    const result = await cartService.removeFromCart(cartId, input)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Remove cart item API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
