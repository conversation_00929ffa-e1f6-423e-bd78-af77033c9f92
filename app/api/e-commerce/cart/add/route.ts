// Add to Cart API Route
// POST /api/e-commerce/cart/add - Add item to cart

import { NextRequest, NextResponse } from 'next/server'
import { cartService, handleEcommerceError } from '@/lib/ecommerce'
import { AddToCartInput } from '@/lib/ecommerce/types'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { productId, quantity, variantId, variantOptions, userId, sessionId } = body

    if (!productId || !quantity) {
      return NextResponse.json(
        { success: false, error: 'Product ID and quantity are required' },
        { status: 400 }
      )
    }

    if (!userId && !sessionId) {
      return NextResponse.json(
        { success: false, error: 'User ID or session ID is required' },
        { status: 400 }
      )
    }

    // Get or create cart first
    const cartResult = await cartService.getOrCreateCart(userId, sessionId)
    
    if (!cartResult.success || !cartResult.data) {
      const error = handleEcommerceError(cartResult.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }

    const cartId = cartResult.data.id

    // Add item to cart
    const addToCartInput: AddToCartInput = {
      productId,
      quantity,
      variantId,
      variantOptions
    }

    const result = await cartService.addToCart(cartId, addToCartInput)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Add to cart API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
