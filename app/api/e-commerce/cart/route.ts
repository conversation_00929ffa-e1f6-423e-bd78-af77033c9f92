// Cart API Routes
// GET /api/e-commerce/cart - Get or create cart
// POST /api/e-commerce/cart/items - Add item to cart
// DELETE /api/e-commerce/cart - Clear cart

import { NextRequest, NextResponse } from 'next/server'
import { cartService, handleEcommerceError } from '@/lib/ecommerce'
import { AddToCartInput } from '@/lib/ecommerce/types'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Get user ID from auth header or session
    const userId = request.headers.get('x-user-id') || undefined
    const sessionId = searchParams.get('sessionId') || undefined

    if (!userId && !sessionId) {
      return NextResponse.json(
        { success: false, error: 'User ID or session ID is required' },
        { status: 400 }
      )
    }

    const result = await cartService.getOrCreateCart(userId, sessionId)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Get cart API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const cartId = searchParams.get('cartId')

    if (!cartId) {
      return NextResponse.json(
        { success: false, error: 'Cart ID is required' },
        { status: 400 }
      )
    }

    const result = await cartService.clearCart(cartId)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Clear cart API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
