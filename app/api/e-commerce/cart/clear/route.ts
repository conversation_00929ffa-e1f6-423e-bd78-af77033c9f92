// Clear Cart API Route
// DELETE /api/e-commerce/cart/clear - Clear entire cart

import { NextRequest, NextResponse } from 'next/server'
import { cartService, handleEcommerceError } from '@/lib/ecommerce'

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, sessionId } = body

    if (!userId && !sessionId) {
      return NextResponse.json(
        { success: false, error: 'User ID or session ID is required' },
        { status: 400 }
      )
    }

    // Get cart first
    const cartResult = await cartService.getOrCreateCart(userId, sessionId)
    
    if (!cartResult.success || !cartResult.data) {
      return NextResponse.json({
        success: true,
        message: 'No cart to clear'
      })
    }

    const cartId = cartResult.data.id
    const result = await cartService.clearCart(cartId)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Clear cart API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
