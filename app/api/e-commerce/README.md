# E-commerce API Routes

This directory contains all the API routes for the custom e-commerce library. All routes are prefixed with `/api/e-commerce/`.

## Authentication

Most routes require authentication via JWT token. The token can be provided in:
- HTTP-only cookie: `auth-token`
- Authorization header: `Bearer <token>`

## Products API

### `GET /api/e-commerce/products`
Search and list products with filtering, sorting, and pagination.

**Query Parameters:**
- `query` - Text search across title, description, vendor, productType
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)
- `categories` - Comma-separated category IDs
- `tags` - Comma-separated tag IDs
- `vendor` - Filter by vendor
- `productType` - Filter by product type
- `status` - Comma-separated status values (active, draft, archived)
- `isVisible` - Filter by visibility (true/false)
- `inStock` - Filter by stock availability (true/false)
- `onSale` - Filter by sale status (true/false)
- `minPrice` - Minimum price filter
- `maxPrice` - Maximum price filter
- `sortBy` - Sort field (title, price, createdAt, updatedAt, averageRating)
- `sortOrder` - Sort direction (asc, desc)

### `POST /api/e-commerce/products`
Create a new product.

**Required Fields:**
- `title` - Product title
- `description` - Product description
- `price` - Product price (number)

### `GET /api/e-commerce/products/[id]`
Get a specific product by ID.

### `PUT /api/e-commerce/products/[id]`
Update a specific product.

### `DELETE /api/e-commerce/products/[id]`
Delete a specific product.

### `GET /api/e-commerce/products/slug/[slug]`
Get a product by its slug.

### `GET /api/e-commerce/products/[id]/related`
Get related products for a specific product.

**Query Parameters:**
- `limit` - Number of related products to return (default: 4)

## Cart API

### `GET /api/e-commerce/cart`
Get or create a cart for the current user/session.

**Query Parameters:**
- `sessionId` - Session ID for guest users

**Headers:**
- `x-user-id` - User ID for authenticated users

### `DELETE /api/e-commerce/cart`
Clear all items from a cart.

**Query Parameters:**
- `cartId` - Cart ID to clear

### `POST /api/e-commerce/cart/items`
Add an item to the cart.

**Required Fields:**
- `cartId` - Cart ID
- `productId` - Product ID
- `quantity` - Quantity to add

**Optional Fields:**
- `variantId` - Product variant ID
- `customAttributes` - Custom attributes object
- `personalizedMessage` - Personalization message
- `giftWrap` - Gift wrap option (boolean)

### `PUT /api/e-commerce/cart/items/[itemId]`
Update a cart item.

**Required Fields:**
- `cartId` - Cart ID

**Optional Fields:**
- `quantity` - New quantity (0 to remove)
- `customAttributes` - Custom attributes object
- `personalizedMessage` - Personalization message
- `giftWrap` - Gift wrap option (boolean)

### `DELETE /api/e-commerce/cart/items/[itemId]`
Remove an item from the cart.

**Query Parameters:**
- `cartId` - Cart ID

### `POST /api/e-commerce/cart/validate`
Validate cart before checkout.

**Required Fields:**
- `cartId` - Cart ID to validate

## Authentication API

### `POST /api/e-commerce/auth/register`
Register a new user account.

**Required Fields:**
- `email` - User email
- `password` - Password (minimum 8 characters)

**Optional Fields:**
- `firstName` - First name
- `lastName` - Last name
- `phone` - Phone number
- `acceptsMarketing` - Marketing consent (boolean)

### `POST /api/e-commerce/auth/login`
Login with email and password.

**Required Fields:**
- `email` - User email
- `password` - User password

**Optional Fields:**
- `rememberMe` - Extended session (boolean)

### `POST /api/e-commerce/auth/logout`
Logout the current user.

### `GET /api/e-commerce/auth/me`
Get current user profile.

### `PUT /api/e-commerce/auth/me`
Update current user profile.

**Optional Fields:**
- `email` - Email address
- `firstName` - First name
- `lastName` - Last name
- `displayName` - Display name
- `phone` - Phone number
- `dateOfBirth` - Date of birth
- `gender` - Gender
- `acceptsMarketing` - Marketing consent
- `preferredLanguage` - Preferred language
- `preferredCurrency` - Preferred currency
- `timezone` - Timezone
- `avatar` - Avatar URL
- `bio` - Biography

## Orders API

### `GET /api/e-commerce/orders`
Get orders for the current user.

**Query Parameters:**
- `query` - Text search
- `page` - Page number
- `limit` - Items per page
- `status` - Order status filter
- `paymentStatus` - Payment status filter
- `fulfillmentStatus` - Fulfillment status filter
- `orderNumber` - Order number filter
- `createdAfter` - Created after date
- `createdBefore` - Created before date
- `totalMin` - Minimum total amount
- `totalMax` - Maximum total amount
- `sortBy` - Sort field
- `sortOrder` - Sort direction

### `POST /api/e-commerce/orders`
Create a new order.

**Required Fields:**
- `customer.email` - Customer email
- `billingAddress` - Billing address object
- `shippingAddress` - Shipping address object
- `items` - Array of order items

### `GET /api/e-commerce/orders/[id]`
Get a specific order by ID.

### `PUT /api/e-commerce/orders/[id]`
Update a specific order (limited fields for customers).

### `PATCH /api/e-commerce/orders/[id]/status`
Update order status with proper workflow progression.

**Required Fields:**
- `status` - New order status

**Optional Fields:**
- `reason` - Status change reason
- `notifyCustomer` - Whether to notify customer
- `internalNote` - Internal note for the change

### `POST /api/e-commerce/orders/bulk`
Perform bulk operations on multiple orders.

**Required Fields:**
- `orderIds` - Array of order IDs
- `action` - Action to perform (updateStatus, updatePaymentStatus, addTags, removeTags, cancel)

**Optional Fields:**
- `data` - Action-specific data

### `POST /api/e-commerce/orders/export`
Export orders in various formats.

**Optional Fields:**
- `orderIds` - Specific orders to export
- `format` - Export format (csv, xlsx, json)
- `filters` - Filter criteria
- `fields` - Specific fields to include

### `GET /api/e-commerce/orders/stats`
Get order statistics and analytics.

**Query Parameters:**
- `period` - Time period (7d, 30d, 90d, 1y)

## Customers API

### `GET /api/e-commerce/customers`
Search and list customers with filtering, sorting, and pagination.

**Query Parameters:**
- `query` - Text search across email, firstName, lastName, displayName
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)
- `isActive` - Filter by active status (true/false)
- `isBlocked` - Filter by blocked status (true/false)
- `emailVerified` - Filter by email verification status (true/false)
- `loyaltyTier` - Comma-separated loyalty tier values
- `totalSpentMin` - Minimum total spent filter
- `totalSpentMax` - Maximum total spent filter
- `orderCountMin` - Minimum order count filter
- `orderCountMax` - Maximum order count filter
- `createdAfter` - Created after date
- `createdBefore` - Created before date
- `lastOrderAfter` - Last order after date
- `lastOrderBefore` - Last order before date
- `tags` - Comma-separated tag values
- `sortBy` - Sort field (email, firstName, lastName, createdAt, totalSpent, orderCount, lastOrderAt)
- `sortOrder` - Sort direction (asc, desc)

### `POST /api/e-commerce/customers`
Create a new customer.

**Required Fields:**
- `email` - Customer email

**Optional Fields:**
- `firstName` - First name
- `lastName` - Last name
- `displayName` - Display name
- `phone` - Phone number
- `dateOfBirth` - Date of birth
- `gender` - Gender
- `acceptsMarketing` - Marketing consent
- `preferredLanguage` - Preferred language
- `preferredCurrency` - Preferred currency
- `timezone` - Timezone
- `avatar` - Avatar URL
- `bio` - Biography
- `tags` - Array of tags
- `notes` - Notes
- `metafields` - Custom metadata

### `GET /api/e-commerce/customers/[id]`
Get a specific customer by ID.

### `PUT /api/e-commerce/customers/[id]`
Update a specific customer.

### `DELETE /api/e-commerce/customers/[id]`
Delete a specific customer.

## Payments API

### `POST /api/e-commerce/payments?action=intent`
Create a payment intent.

**Required Fields:**
- `amount` - Payment amount (number)

**Optional Fields:**
- `currency` - Currency code (default: ZAR)
- `paymentMethodId` - Payment method ID
- `orderId` - Associated order ID
- `description` - Payment description
- `receiptEmail` - Receipt email

### `POST /api/e-commerce/payments?action=confirm`
Confirm a payment intent.

**Required Fields:**
- `paymentIntentId` - Payment intent ID

**Optional Fields:**
- `paymentMethodId` - Payment method ID
- `returnUrl` - Return URL for 3D Secure

### `GET /api/e-commerce/payments/methods`
Get customer payment methods.

### `POST /api/e-commerce/payments/methods`
Create a new payment method.

**Required Fields:**
- `type` - Payment method type

**Optional Fields (based on type):**
- `card` - Card details object
- `bankAccount` - Bank account details
- `digitalWallet` - Digital wallet details
- `billingAddress` - Billing address
- `nickname` - Payment method nickname

### `POST /api/e-commerce/payments/methods/[id]/default`
Set a payment method as default.

## Response Format

All API responses follow this format:

```json
{
  "success": true|false,
  "data": <response_data>,
  "error": "<error_message>",
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (access denied)
- `404` - Not Found
- `500` - Internal Server Error

## Authentication Flow

1. Register: `POST /api/e-commerce/auth/register`
2. Login: `POST /api/e-commerce/auth/login`
3. Use authenticated endpoints with token
4. Logout: `POST /api/e-commerce/auth/logout`

## Shopping Flow

1. Browse products: `GET /api/e-commerce/products`
2. Get/create cart: `GET /api/e-commerce/cart`
3. Add items: `POST /api/e-commerce/cart/items`
4. Validate cart: `POST /api/e-commerce/cart/validate`
5. Create order: `POST /api/e-commerce/orders`
6. Process payment: `POST /api/e-commerce/payments`

## Currency

All monetary values are in South African Rand (ZAR) by default. The system supports multiple currencies but defaults to ZAR for the South African market.
