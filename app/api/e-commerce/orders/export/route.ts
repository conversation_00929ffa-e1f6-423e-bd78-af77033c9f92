import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { z } from 'zod'

const prisma = new PrismaClient()

const exportSchema = z.object({
  orderIds: z.array(z.string()).optional(),
  format: z.enum(['csv', 'xlsx', 'json']).default('csv'),
  filters: z.object({
    status: z.array(z.string()).optional(),
    paymentStatus: z.array(z.string()).optional(),
    createdAfter: z.string().optional(),
    createdBefore: z.string().optional(),
    customerEmail: z.string().optional(),
    totalMin: z.number().optional(),
    totalMax: z.number().optional()
  }).optional(),
  fields: z.array(z.string()).optional()
})

function formatOrderForExport(order: any) {
  return {
    orderNumber: order.orderNumber,
    customerEmail: order.customerEmail,
    customerName: `${order.customerFirstName} ${order.customerLastName}`,
    customerPhone: order.customerPhone,
    status: order.status,
    paymentStatus: order.paymentStatus,
    fulfillmentStatus: order.fulfillmentStatus,
    itemCount: order.itemCount,
    subtotal: order.subtotal,
    totalTax: order.totalTax,
    totalShipping: order.totalShipping,
    total: order.total,
    currency: order.currency,
    source: order.source,
    tags: Array.isArray(order.tags) ? order.tags.join(', ') : '',
    customerNote: order.customerNote,
    internalNotes: Array.isArray(order.internalNotes) ? order.internalNotes.join('; ') : '',
    createdAt: order.createdAt?.toISOString(),
    confirmedAt: order.confirmedAt?.toISOString(),
    processedAt: order.processedAt?.toISOString(),
    shippedAt: order.shippedAt?.toISOString(),
    deliveredAt: order.deliveredAt?.toISOString(),
    cancelledAt: order.cancelledAt?.toISOString(),
    billingAddress: order.billingAddress ? JSON.stringify(order.billingAddress) : '',
    shippingAddress: order.shippingAddress ? JSON.stringify(order.shippingAddress) : ''
  }
}

function generateCSV(orders: any[]) {
  if (orders.length === 0) return ''
  
  const headers = Object.keys(orders[0])
  const csvContent = [
    headers.join(','),
    ...orders.map(order => 
      headers.map(header => {
        const value = order[header]
        // Escape commas and quotes in CSV
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`
        }
        return value || ''
      }).join(',')
    )
  ].join('\n')
  
  return csvContent
}

export async function POST(request: NextRequest) {
  try {
    // TODO: Add admin authentication check
    // const isAdmin = await checkAdminAuth(request)
    // if (!isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    // }

    const body = await request.json()
    const validatedData = exportSchema.parse(body)

    const { orderIds, format, filters, fields } = validatedData

    // Build query conditions
    const whereConditions: any = {}

    if (orderIds && orderIds.length > 0) {
      whereConditions.id = { in: orderIds }
    }

    if (filters) {
      if (filters.status && filters.status.length > 0) {
        whereConditions.status = { in: filters.status }
      }
      if (filters.paymentStatus && filters.paymentStatus.length > 0) {
        whereConditions.paymentStatus = { in: filters.paymentStatus }
      }
      if (filters.customerEmail) {
        whereConditions.customerEmail = { contains: filters.customerEmail, mode: 'insensitive' }
      }
      if (filters.createdAfter || filters.createdBefore) {
        whereConditions.createdAt = {}
        if (filters.createdAfter) {
          whereConditions.createdAt.gte = new Date(filters.createdAfter)
        }
        if (filters.createdBefore) {
          whereConditions.createdAt.lte = new Date(filters.createdBefore)
        }
      }
      if (filters.totalMin !== undefined || filters.totalMax !== undefined) {
        whereConditions.total = {}
        if (filters.totalMin !== undefined) {
          whereConditions.total.gte = filters.totalMin
        }
        if (filters.totalMax !== undefined) {
          whereConditions.total.lte = filters.totalMax
        }
      }
    }

    // Fetch orders
    const orders = await prisma.order.findMany({
      where: whereConditions,
      include: {
        items: true
      },
      orderBy: { createdAt: 'desc' }
    })

    // Format orders for export
    const formattedOrders = orders.map(formatOrderForExport)

    // Filter fields if specified
    const exportData = fields && fields.length > 0 
      ? formattedOrders.map(order => {
          const filteredOrder: any = {}
          fields.forEach(field => {
            if (field in order) {
              filteredOrder[field] = order[field]
            }
          })
          return filteredOrder
        })
      : formattedOrders

    // Generate response based on format
    switch (format) {
      case 'csv':
        const csvContent = generateCSV(exportData)
        return new NextResponse(csvContent, {
          headers: {
            'Content-Type': 'text/csv',
            'Content-Disposition': `attachment; filename="orders-export-${new Date().toISOString().split('T')[0]}.csv"`
          }
        })

      case 'xlsx':
        // For XLSX, we'll return JSON with instructions to convert client-side
        return NextResponse.json({
          success: true,
          data: exportData,
          format: 'xlsx',
          filename: `orders-export-${new Date().toISOString().split('T')[0]}.xlsx`,
          message: 'Use client-side library to convert to XLSX'
        })

      case 'json':
      default:
        return NextResponse.json({
          success: true,
          data: exportData,
          summary: {
            totalOrders: exportData.length,
            totalValue: exportData.reduce((sum, order) => sum + (parseFloat(order.total) || 0), 0),
            exportedAt: new Date().toISOString(),
            filters: filters || {},
            fields: fields || 'all'
          }
        })
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid export parameters',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Export orders error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to export orders'
    }, { status: 500 })
  }
}
