import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { z } from 'zod'

const prisma = new PrismaClient()

interface RouteParams {
  params: {
    id: string
  }
}

const updateStatusSchema = z.object({
  status: z.enum(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled']),
  reason: z.string().optional(),
  notifyCustomer: z.boolean().default(false),
  internalNote: z.string().optional()
})

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    // TODO: Add admin authentication check
    // const isAdmin = await checkAdminAuth(request)
    // if (!isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    // }

    const { id } = params
    const body = await request.json()
    
    const validatedData = updateStatusSchema.parse(body)
    const { status, reason, notifyCustomer, internalNote } = validatedData

    // Check if order exists
    const existingOrder = await prisma.order.findUnique({
      where: { id },
      include: { items: true }
    })

    if (!existingOrder) {
      return NextResponse.json({
        success: false,
        error: 'Order not found'
      }, { status: 404 })
    }

    // Prepare update data
    const updateData: any = {
      status,
      updatedAt: new Date()
    }

    // Set appropriate timestamps based on status
    const now = new Date()
    switch (status) {
      case 'confirmed':
        if (!existingOrder.confirmedAt) {
          updateData.confirmedAt = now
        }
        break
      case 'processing':
        if (!existingOrder.processedAt) {
          updateData.processedAt = now
        }
        if (!existingOrder.confirmedAt) {
          updateData.confirmedAt = now
        }
        break
      case 'shipped':
        if (!existingOrder.shippedAt) {
          updateData.shippedAt = now
        }
        if (!existingOrder.processedAt) {
          updateData.processedAt = now
        }
        if (!existingOrder.confirmedAt) {
          updateData.confirmedAt = now
        }
        break
      case 'delivered':
        if (!existingOrder.deliveredAt) {
          updateData.deliveredAt = now
        }
        if (!existingOrder.shippedAt) {
          updateData.shippedAt = now
        }
        if (!existingOrder.processedAt) {
          updateData.processedAt = now
        }
        if (!existingOrder.confirmedAt) {
          updateData.confirmedAt = now
        }
        break
      case 'cancelled':
        if (!existingOrder.cancelledAt) {
          updateData.cancelledAt = now
        }
        break
    }

    // Add internal note if provided
    if (internalNote || reason) {
      const currentNotes = existingOrder.internalNotes || []
      const noteText = internalNote || `Status changed to ${status}${reason ? `: ${reason}` : ''}`
      updateData.internalNotes = [...currentNotes, noteText]
    }

    // Update the order
    const updatedOrder = await prisma.order.update({
      where: { id },
      data: updateData,
      include: { items: true }
    })

    // TODO: Send notification to customer if requested
    if (notifyCustomer) {
      // Implement email notification logic here
      console.log(`Would notify customer ${updatedOrder.customerEmail} about status change to ${status}`)
    }

    // TODO: Handle inventory updates based on status change
    // For example, when order is cancelled, release inventory reservations

    return NextResponse.json({
      success: true,
      data: {
        id: updatedOrder.id,
        orderNumber: updatedOrder.orderNumber,
        status: updatedOrder.status,
        previousStatus: existingOrder.status,
        updatedAt: updatedOrder.updatedAt,
        confirmedAt: updatedOrder.confirmedAt,
        processedAt: updatedOrder.processedAt,
        shippedAt: updatedOrder.shippedAt,
        deliveredAt: updatedOrder.deliveredAt,
        cancelledAt: updatedOrder.cancelledAt
      },
      message: `Order status updated to ${status}`
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid status update data',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Update order status error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to update order status'
    }, { status: 500 })
  }
}
