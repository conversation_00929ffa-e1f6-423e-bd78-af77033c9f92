// Cancel Order API Route
// POST /api/e-commerce/orders/[id]/cancel - Cancel an order

import { NextRequest, NextResponse } from 'next/server'
import { orderService, authService, handleEcommerceError } from '@/lib/ecommerce'

interface RouteParams {
  params: {
    id: string
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Get token from cookie or header
    const token = request.cookies.get('auth-token')?.value || 
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get current user
    const userResult = await authService.getCurrentUser(token)
    if (!userResult.success || !userResult.data) {
      return NextResponse.json(
        { success: false, error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    const { id } = params
    const body = await request.json()

    // First check if order exists and user owns it
    const existingOrderResult = await orderService.getOrderById(id)
    if (!existingOrderResult.success) {
      const error = handleEcommerceError(existingOrderResult.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }

    if (existingOrderResult.data?.userId !== userResult.data.id) {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      )
    }

    const result = await orderService.cancelOrder(id, body.reason)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data,
        message: 'Order cancelled successfully'
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Cancel order API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
