import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Query parameters schema
const searchSchema = z.object({
  orderNumber: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchSchema.parse(Object.fromEntries(searchParams))
    
    const { orderNumber, email, phone } = query

    // At least one search parameter is required
    if (!orderNumber && !email && !phone) {
      return NextResponse.json(
        { success: false, error: 'At least one search parameter is required (orderNumber, email, or phone)' },
        { status: 400 }
      )
    }

    // Build where clause for order search
    const where: any = {
      AND: []
    }

    if (orderNumber) {
      where.AND.push({
        orderNumber: {
          contains: orderNumber,
          mode: 'insensitive'
        }
      })
    }

    if (email) {
      where.AND.push({
        OR: [
          {
            customer: {
              email: {
                equals: email,
                mode: 'insensitive'
              }
            }
          },
          {
            billingAddress: {
              email: {
                equals: email,
                mode: 'insensitive'
              }
            }
          },
          {
            shippingAddress: {
              email: {
                equals: email,
                mode: 'insensitive'
              }
            }
          }
        ]
      })
    }

    if (phone) {
      where.AND.push({
        OR: [
          {
            customer: {
              phone: {
                contains: phone,
                mode: 'insensitive'
              }
            }
          },
          {
            billingAddress: {
              phone: {
                contains: phone,
                mode: 'insensitive'
              }
            }
          },
          {
            shippingAddress: {
              phone: {
                contains: phone,
                mode: 'insensitive'
              }
            }
          }
        ]
      })
    }

    // Search for orders
    const orders = await prisma.order.findMany({
      where,
      include: {
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                title: true,
                handle: true,
                images: {
                  take: 1,
                  select: {
                    url: true,
                    alt: true
                  }
                }
              }
            }
          }
        },
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        shippingAddress: true,
        billingAddress: true,
        total: true,
        subtotal: true,
        tax: true,
        shipping: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10 // Limit results for security
    })

    // Transform orders for frontend
    const transformedOrders = orders.map(order => ({
      id: order.id,
      orderNumber: order.orderNumber,
      status: order.status,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      total: order.total,
      subtotal: order.subtotal,
      tax: order.tax,
      shipping: order.shipping,
      customer: order.customer ? {
        name: `${order.customer.firstName} ${order.customer.lastName}`.trim(),
        email: order.customer.email
      } : null,
      orderItems: order.orderItems.map(item => ({
        id: item.id,
        quantity: item.quantity,
        price: item.price,
        productTitle: item.productTitle,
        variant: item.variant,
        product: item.product ? {
          id: item.product.id,
          title: item.product.title,
          handle: item.product.handle,
          image: item.product.images[0] || null
        } : null
      })),
      shippingAddress: order.shippingAddress,
      billingAddress: order.billingAddress,
      trackingNumber: order.trackingNumber,
      trackingUrl: order.trackingUrl,
      notes: order.notes
    }))

    return NextResponse.json({
      success: true,
      data: transformedOrders,
      count: transformedOrders.length
    })

  } catch (error) {
    console.error('Order search error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid search parameters', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to search orders' },
      { status: 500 }
    )
  }
}

// POST method for more complex searches (with request body)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const searchCriteria = z.object({
      orderNumber: z.string().optional(),
      email: z.string().email().optional(),
      phone: z.string().optional(),
      dateRange: z.object({
        from: z.string().datetime().optional(),
        to: z.string().datetime().optional()
      }).optional(),
      status: z.array(z.string()).optional(),
      minAmount: z.number().optional(),
      maxAmount: z.number().optional()
    }).parse(body)

    const { orderNumber, email, phone, dateRange, status, minAmount, maxAmount } = searchCriteria

    // Build complex where clause
    const where: any = {
      AND: []
    }

    if (orderNumber) {
      where.AND.push({
        orderNumber: {
          contains: orderNumber,
          mode: 'insensitive'
        }
      })
    }

    if (email) {
      where.AND.push({
        OR: [
          { customer: { email: { equals: email, mode: 'insensitive' } } },
          { billingAddress: { email: { equals: email, mode: 'insensitive' } } },
          { shippingAddress: { email: { equals: email, mode: 'insensitive' } } }
        ]
      })
    }

    if (phone) {
      where.AND.push({
        OR: [
          { customer: { phone: { contains: phone, mode: 'insensitive' } } },
          { billingAddress: { phone: { contains: phone, mode: 'insensitive' } } },
          { shippingAddress: { phone: { contains: phone, mode: 'insensitive' } } }
        ]
      })
    }

    if (dateRange?.from || dateRange?.to) {
      const dateFilter: any = {}
      if (dateRange.from) dateFilter.gte = new Date(dateRange.from)
      if (dateRange.to) dateFilter.lte = new Date(dateRange.to)
      where.AND.push({ createdAt: dateFilter })
    }

    if (status && status.length > 0) {
      where.AND.push({
        status: {
          in: status
        }
      })
    }

    if (minAmount !== undefined || maxAmount !== undefined) {
      const amountFilter: any = {}
      if (minAmount !== undefined) amountFilter.gte = minAmount
      if (maxAmount !== undefined) amountFilter.lte = maxAmount
      where.AND.push({
        total: {
          amount: amountFilter
        }
      })
    }

    // Execute search with same logic as GET method
    const orders = await prisma.order.findMany({
      where,
      include: {
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                title: true,
                handle: true,
                images: { take: 1, select: { url: true, alt: true } }
              }
            }
          }
        },
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        shippingAddress: true,
        billingAddress: true,
        total: true,
        subtotal: true,
        tax: true,
        shipping: true
      },
      orderBy: { createdAt: 'desc' },
      take: 50 // Higher limit for POST searches
    })

    // Transform and return results (same as GET method)
    const transformedOrders = orders.map(order => ({
      id: order.id,
      orderNumber: order.orderNumber,
      status: order.status,
      createdAt: order.createdAt,
      total: order.total,
      customer: order.customer ? {
        name: `${order.customer.firstName} ${order.customer.lastName}`.trim(),
        email: order.customer.email
      } : null,
      orderItems: order.orderItems.map(item => ({
        id: item.id,
        quantity: item.quantity,
        price: item.price,
        productTitle: item.productTitle,
        variant: item.variant,
        product: item.product
      }))
    }))

    return NextResponse.json({
      success: true,
      data: transformedOrders,
      count: transformedOrders.length
    })

  } catch (error) {
    console.error('Advanced order search error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid search criteria', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to perform advanced search' },
      { status: 500 }
    )
  }
}
