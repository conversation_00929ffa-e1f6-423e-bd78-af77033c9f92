import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    // TODO: Add admin authentication check
    // const isAdmin = await checkAdminAuth(request)
    // if (!isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    // }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30d' // 7d, 30d, 90d, 1y

    // Calculate date range based on period
    const now = new Date()
    let startDate: Date

    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      case '30d':
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
    }

    // Get basic order statistics
    const [
      totalOrders,
      totalRevenue,
      averageOrderValue,
      ordersByStatus,
      ordersByPaymentStatus,
      recentOrders,
      topCustomers,
      dailyStats
    ] = await Promise.all([
      // Total orders in period
      prisma.order.count({
        where: {
          createdAt: { gte: startDate }
        }
      }),

      // Total revenue (paid orders only)
      prisma.order.aggregate({
        where: {
          createdAt: { gte: startDate },
          paymentStatus: 'paid'
        },
        _sum: { total: true }
      }),

      // Average order value
      prisma.order.aggregate({
        where: {
          createdAt: { gte: startDate }
        },
        _avg: { total: true }
      }),

      // Orders by status
      prisma.order.groupBy({
        by: ['status'],
        where: {
          createdAt: { gte: startDate }
        },
        _count: { status: true }
      }),

      // Orders by payment status
      prisma.order.groupBy({
        by: ['paymentStatus'],
        where: {
          createdAt: { gte: startDate }
        },
        _count: { paymentStatus: true }
      }),

      // Recent orders
      prisma.order.findMany({
        where: {
          createdAt: { gte: startDate }
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
        select: {
          id: true,
          orderNumber: true,
          customerEmail: true,
          customerFirstName: true,
          customerLastName: true,
          status: true,
          paymentStatus: true,
          total: true,
          currency: true,
          createdAt: true
        }
      }),

      // Top customers by order value
      prisma.order.groupBy({
        by: ['customerEmail'],
        where: {
          createdAt: { gte: startDate },
          paymentStatus: 'paid'
        },
        _sum: { total: true },
        _count: { customerEmail: true },
        orderBy: { _sum: { total: 'desc' } },
        take: 10
      }),

      // Get all orders for daily stats calculation
      prisma.order.findMany({
        where: {
          createdAt: { gte: startDate }
        },
        select: {
          createdAt: true,
          total: true,
          paymentStatus: true
        },
        orderBy: { createdAt: 'asc' }
      })
    ])

    // Calculate growth rates (compare with previous period)
    const previousPeriodStart = new Date(startDate.getTime() - (now.getTime() - startDate.getTime()))
    
    const [previousTotalOrders, previousRevenue] = await Promise.all([
      prisma.order.count({
        where: {
          createdAt: { gte: previousPeriodStart, lt: startDate }
        }
      }),
      prisma.order.aggregate({
        where: {
          createdAt: { gte: previousPeriodStart, lt: startDate },
          paymentStatus: 'paid'
        },
        _sum: { total: true }
      })
    ])

    // Calculate growth percentages
    const orderGrowth = previousTotalOrders > 0 
      ? ((totalOrders - previousTotalOrders) / previousTotalOrders) * 100 
      : 0

    const revenueGrowth = (previousRevenue._sum.total || 0) > 0 
      ? (((totalRevenue._sum.total || 0) - (previousRevenue._sum.total || 0)) / (previousRevenue._sum.total || 0)) * 100 
      : 0

    // Format response
    const stats = {
      overview: {
        totalOrders,
        totalRevenue: totalRevenue._sum.total || 0,
        averageOrderValue: averageOrderValue._avg.total || 0,
        orderGrowth: Math.round(orderGrowth * 100) / 100,
        revenueGrowth: Math.round(revenueGrowth * 100) / 100,
        period
      },
      ordersByStatus: ordersByStatus.reduce((acc, item) => {
        acc[item.status] = item._count.status
        return acc
      }, {} as Record<string, number>),
      ordersByPaymentStatus: ordersByPaymentStatus.reduce((acc, item) => {
        acc[item.paymentStatus] = item._count.paymentStatus
        return acc
      }, {} as Record<string, number>),
      recentOrders: recentOrders.map(order => ({
        ...order,
        customerName: `${order.customerFirstName} ${order.customerLastName}`
      })),
      topCustomers: topCustomers.map(customer => ({
        email: customer.customerEmail,
        totalSpent: customer._sum.total || 0,
        orderCount: customer._count.customerEmail
      })),
      dailyStats: (() => {
        // Group orders by date
        const dailyGroups: Record<string, any[]> = {}

        dailyStats.forEach((order: any) => {
          const date = order.createdAt.toISOString().split('T')[0]
          if (!dailyGroups[date]) {
            dailyGroups[date] = []
          }
          dailyGroups[date].push(order)
        })

        // Calculate stats for each day
        return Object.entries(dailyGroups).map(([date, orders]) => ({
          date,
          orderCount: orders.length,
          revenue: orders
            .filter(order => order.paymentStatus === 'paid')
            .reduce((sum, order) => sum + Number(order.total), 0),
          avgOrderValue: orders.reduce((sum, order) => sum + Number(order.total), 0) / orders.length
        })).sort((a, b) => a.date.localeCompare(b.date))
      })()
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('Order stats API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch order statistics'
    }, { status: 500 })
  }
}
