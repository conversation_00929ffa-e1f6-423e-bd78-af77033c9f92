import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { z } from 'zod'

const prisma = new PrismaClient()

const bulkUpdateSchema = z.object({
  orderIds: z.array(z.string()).min(1),
  action: z.enum(['updateStatus', 'updatePaymentStatus', 'addTags', 'removeTags', 'cancel']),
  data: z.object({
    status: z.enum(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled']).optional(),
    paymentStatus: z.enum(['pending', 'paid', 'failed', 'refunded', 'partially_refunded']).optional(),
    tags: z.array(z.string()).optional(),
    reason: z.string().optional()
  }).optional()
})

export async function POST(request: NextRequest) {
  try {
    // TODO: Add admin authentication check
    // const isAdmin = await checkAdminAuth(request)
    // if (!isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    // }

    const body = await request.json()
    const validatedData = bulkUpdateSchema.parse(body)

    const { orderIds, action, data } = validatedData
    const results = []

    switch (action) {
      case 'updateStatus':
        if (!data?.status) {
          return NextResponse.json({
            success: false,
            error: 'Status is required for updateStatus action'
          }, { status: 400 })
        }

        const statusUpdateData: any = {
          status: data.status,
          updatedAt: new Date()
        }

        // Set appropriate timestamps based on status
        if (data.status === 'confirmed') {
          statusUpdateData.confirmedAt = new Date()
        } else if (data.status === 'processing') {
          statusUpdateData.processedAt = new Date()
        } else if (data.status === 'shipped') {
          statusUpdateData.shippedAt = new Date()
        } else if (data.status === 'delivered') {
          statusUpdateData.deliveredAt = new Date()
        } else if (data.status === 'cancelled') {
          statusUpdateData.cancelledAt = new Date()
        }

        const statusUpdated = await prisma.order.updateMany({
          where: { id: { in: orderIds } },
          data: statusUpdateData
        })

        results.push({
          action: 'updateStatus',
          affected: statusUpdated.count,
          status: data.status
        })
        break

      case 'updatePaymentStatus':
        if (!data?.paymentStatus) {
          return NextResponse.json({
            success: false,
            error: 'Payment status is required for updatePaymentStatus action'
          }, { status: 400 })
        }

        const paymentUpdated = await prisma.order.updateMany({
          where: { id: { in: orderIds } },
          data: {
            paymentStatus: data.paymentStatus,
            updatedAt: new Date()
          }
        })

        results.push({
          action: 'updatePaymentStatus',
          affected: paymentUpdated.count,
          paymentStatus: data.paymentStatus
        })
        break

      case 'addTags':
        if (!data?.tags || data.tags.length === 0) {
          return NextResponse.json({
            success: false,
            error: 'Tags are required for addTags action'
          }, { status: 400 })
        }

        // Get current orders to merge tags
        const ordersToAddTags = await prisma.order.findMany({
          where: { id: { in: orderIds } },
          select: { id: true, tags: true }
        })

        for (const order of ordersToAddTags) {
          const currentTags = order.tags || []
          const newTags = [...new Set([...currentTags, ...data.tags])]
          
          await prisma.order.update({
            where: { id: order.id },
            data: {
              tags: newTags,
              updatedAt: new Date()
            }
          })
        }

        results.push({
          action: 'addTags',
          affected: ordersToAddTags.length,
          tags: data.tags
        })
        break

      case 'removeTags':
        if (!data?.tags || data.tags.length === 0) {
          return NextResponse.json({
            success: false,
            error: 'Tags are required for removeTags action'
          }, { status: 400 })
        }

        // Get current orders to filter tags
        const ordersToRemoveTags = await prisma.order.findMany({
          where: { id: { in: orderIds } },
          select: { id: true, tags: true }
        })

        for (const order of ordersToRemoveTags) {
          const currentTags = order.tags || []
          const filteredTags = currentTags.filter(tag => !data.tags.includes(tag))
          
          await prisma.order.update({
            where: { id: order.id },
            data: {
              tags: filteredTags,
              updatedAt: new Date()
            }
          })
        }

        results.push({
          action: 'removeTags',
          affected: ordersToRemoveTags.length,
          tags: data.tags
        })
        break

      case 'cancel':
        const cancelledOrders = await prisma.order.updateMany({
          where: { 
            id: { in: orderIds },
            status: { notIn: ['delivered', 'cancelled', 'refunded'] }
          },
          data: {
            status: 'cancelled',
            cancelledAt: new Date(),
            updatedAt: new Date(),
            internalNotes: {
              push: `Order cancelled via bulk action${data?.reason ? `: ${data.reason}` : ''}`
            }
          }
        })

        results.push({
          action: 'cancel',
          affected: cancelledOrders.count,
          reason: data?.reason
        })
        break

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action'
        }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      message: 'Bulk operation completed successfully',
      results,
      totalProcessed: orderIds.length
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid bulk operation data',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Bulk orders operation error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to perform bulk operation'
    }, { status: 500 })
  }
}
