// API route for handling file uploads via Appwrite

import { NextRequest, NextResponse } from 'next/server'
import { storageService, isAppwriteConfigured } from '@/lib/appwrite'

export async function POST(request: NextRequest) {
  try {
    // Check if Appwrite is configured
    if (!isAppwriteConfigured()) {
      return NextResponse.json(
        { error: 'Appwrite is not configured' },
        { status: 503 }
      )
    }

    // Get form data
    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const bucketId = formData.get('bucketId') as string

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      )
    }

    // Validate files
    const validFiles: File[] = []
    const errors: string[] = []

    for (const file of files) {
      if (!(file instanceof File)) {
        errors.push('Invalid file format')
        continue
      }

      // Validate file type (images only)
      if (!file.type.startsWith('image/')) {
        errors.push(`${file.name}: Invalid file type. Only images are allowed.`)
        continue
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        errors.push(`${file.name}: File too large. Maximum size is 5MB.`)
        continue
      }

      validFiles.push(file)
    }

    if (validFiles.length === 0) {
      return NextResponse.json(
        { error: 'No valid files to upload', details: errors },
        { status: 400 }
      )
    }

    // Upload files to Appwrite
    const uploadResults = []
    const uploadErrors = []

    for (const file of validFiles) {
      try {
        const result = await storageService.uploadFile(file, {
          bucketId: bucketId || undefined
        })
        
        uploadResults.push({
          fileId: result.fileId,
          url: result.url,
          downloadUrl: result.downloadUrl,
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type
        })
      } catch (error) {
        console.error(`Failed to upload ${file.name}:`, error)
        uploadErrors.push({
          fileName: file.name,
          error: error instanceof Error ? error.message : 'Upload failed'
        })
      }
    }

    // Return results
    const response = {
      success: uploadResults.length > 0,
      uploaded: uploadResults.length,
      failed: uploadErrors.length,
      results: uploadResults,
      errors: uploadErrors.length > 0 ? uploadErrors : undefined,
      validationErrors: errors.length > 0 ? errors : undefined
    }

    if (uploadResults.length === 0) {
      return NextResponse.json(response, { status: 500 })
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Upload API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check if Appwrite is configured
    if (!isAppwriteConfigured()) {
      return NextResponse.json(
        { error: 'Appwrite is not configured' },
        { status: 503 }
      )
    }

    const { searchParams } = new URL(request.url)
    const fileId = searchParams.get('fileId')
    const bucketId = searchParams.get('bucketId')

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID is required' },
        { status: 400 }
      )
    }

    if (!bucketId) {
      return NextResponse.json(
        { error: 'Bucket ID is required' },
        { status: 400 }
      )
    }

    // Delete file from Appwrite
    await storageService.deleteFile(bucketId, fileId)

    return NextResponse.json({ 
      success: true,
      message: 'File deleted successfully'
    })

  } catch (error) {
    console.error('Delete API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to delete file',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Get file information
export async function GET(request: NextRequest) {
  try {
    // Check if Appwrite is configured
    if (!isAppwriteConfigured()) {
      return NextResponse.json(
        { error: 'Appwrite is not configured' },
        { status: 503 }
      )
    }

    const { searchParams } = new URL(request.url)
    const fileId = searchParams.get('fileId')
    const bucketId = searchParams.get('bucketId')

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID is required' },
        { status: 400 }
      )
    }

    if (!bucketId) {
      return NextResponse.json(
        { error: 'Bucket ID is required' },
        { status: 400 }
      )
    }

    // Get file information from Appwrite
    const file = await storageService.getFile(bucketId, fileId)

    return NextResponse.json({
      fileId: file.$id,
      name: file.name,
      size: file.sizeOriginal,
      mimeType: file.mimeType,
      createdAt: file.$createdAt,
      updatedAt: file.$updatedAt
    })

  } catch (error) {
    console.error('Get file API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to get file information',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
