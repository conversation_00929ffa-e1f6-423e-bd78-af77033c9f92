import { NextRequest, NextResponse } from 'next/server'
import { subscribeToNewsletter } from '@/lib/ecommerce/services/newsletter-service'
import { z } from 'zod'

const subscribeSchema = z.object({
  email: z.string().email('Invalid email address'),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  preferences: z.object({
    newArrivals: z.boolean().optional(),
    sales: z.boolean().optional(),
    editorial: z.boolean().optional()
  }).optional()
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate request body
    const validatedData = subscribeSchema.parse(body)

    // Subscribe to newsletter
    const subscriber = await subscribeToNewsletter(validatedData)

    return NextResponse.json({
      success: true,
      message: 'Successfully subscribed to newsletter',
      data: {
        id: subscriber.id,
        email: subscriber.email,
        subscribedAt: subscriber.subscribedAt
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Newsletter subscription error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        message: 'Invalid request data',
        errors: error.errors
      }, { status: 400 })
    }

    if (error instanceof Error) {
      if (error.message.includes('already subscribed')) {
        return NextResponse.json({
          success: false,
          message: 'Email is already subscribed to newsletter'
        }, { status: 409 })
      }

      return NextResponse.json({
        success: false,
        message: error.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: false,
      message: 'Failed to subscribe to newsletter'
    }, { status: 500 })
  }
}
