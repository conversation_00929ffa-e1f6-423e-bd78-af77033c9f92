// Set Default Payment Method API Route
// POST /api/e-commerce/payments/methods/[id]/default - Set payment method as default

import { NextRequest, NextResponse } from 'next/server'
import { paymentService, authService, handleEcommerceError } from '@/lib/ecommerce'

interface RouteParams {
  params: {
    id: string
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Get token from cookie or header
    const token = request.cookies.get('auth-token')?.value || 
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get current user
    const userResult = await authService.getCurrentUser(token)
    if (!userResult.success || !userResult.data) {
      return NextResponse.json(
        { success: false, error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    const { id } = params

    const result = await paymentService.setDefaultPaymentMethod(userResult.data.id, id)

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Default payment method updated successfully'
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Set default payment method API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
