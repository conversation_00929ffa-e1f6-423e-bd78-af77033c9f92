// Payment Methods API Routes
// GET /api/e-commerce/payments/methods - Get customer payment methods
// POST /api/e-commerce/payments/methods - Create payment method

import { NextRequest, NextResponse } from 'next/server'
import { 
  paymentService, 
  authService, 
  handleEcommerceError,
  createSuccessResponse,
  createAuthErrorResponse,
  createValidationErrorResponse,
  handleApiError
} from '@/lib/ecommerce'
import { CreatePaymentMethodInput } from '@/lib/ecommerce/types'

export async function GET(request: NextRequest) {
  try {
    // Get token from cookie or header
    const token = request.cookies.get('auth-token')?.value || 
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        createAuthErrorResponse(),
        { status: 401 }
      )
    }

    // Get current user
    const userResult = await authService.getCurrentUser(token)
    if (!userResult.success || !userResult.data) {
      return NextResponse.json(
        createAuthErrorResponse('Invalid authentication token'),
        { status: 401 }
      )
    }

    const result = await paymentService.getCustomerPaymentMethods(userResult.data.id)

    if (result.success) {
      return NextResponse.json(createSuccessResponse(result.data))
    } else {
      const { response, statusCode } = handleApiError(result.error)
      return NextResponse.json(response, { status: statusCode })
    }
  } catch (error) {
    console.error('Get payment methods API error:', error)
    const { response, statusCode } = handleApiError(error)
    return NextResponse.json(response, { status: statusCode })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get token from cookie or header
    const token = request.cookies.get('auth-token')?.value || 
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get current user
    const userResult = await authService.getCurrentUser(token)
    if (!userResult.success || !userResult.data) {
      return NextResponse.json(
        { success: false, error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    const body = await request.json()
    
    // Validate required fields
    if (!body.type) {
      return NextResponse.json(
        { success: false, error: 'Payment method type is required' },
        { status: 400 }
      )
    }

    const input: CreatePaymentMethodInput = {
      type: body.type,
      customerId: userResult.data.id,
      card: body.card ? {
        number: body.card.number,
        expiryMonth: parseInt(body.card.expiryMonth),
        expiryYear: parseInt(body.card.expiryYear),
        cvc: body.card.cvc
      } : undefined,
      bankAccount: body.bankAccount ? {
        accountNumber: body.bankAccount.accountNumber,
        routingNumber: body.bankAccount.routingNumber,
        accountType: body.bankAccount.accountType
      } : undefined,
      digitalWallet: body.digitalWallet ? {
        provider: body.digitalWallet.provider,
        token: body.digitalWallet.token
      } : undefined,
      billingAddress: body.billingAddress,
      nickname: body.nickname,
      metadata: body.metadata
    }

    const result = await paymentService.createPaymentMethod(input)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      }, { status: 201 })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Create payment method API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
