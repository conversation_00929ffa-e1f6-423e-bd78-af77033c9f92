// Payments API Routes
// POST /api/e-commerce/payments/intent - Create payment intent
// POST /api/e-commerce/payments/confirm - Confirm payment intent

import { NextRequest, NextResponse } from 'next/server'
import { paymentService, authService, handleEcommerceError } from '@/lib/ecommerce'
import { CreatePaymentIntentInput, ConfirmPaymentIntentInput } from '@/lib/ecommerce/types'

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'intent') {
      return handleCreatePaymentIntent(request)
    } else if (action === 'confirm') {
      return handleConfirmPaymentIntent(request)
    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid action. Use ?action=intent or ?action=confirm' },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Payments API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

async function handleCreatePaymentIntent(request: NextRequest) {
  try {
    // Get token from cookie or header
    const token = request.cookies.get('auth-token')?.value || 
                  request.headers.get('authorization')?.replace('Bearer ', '')

    let customerId: string | undefined

    if (token) {
      // Get current user if authenticated
      const userResult = await authService.getCurrentUser(token)
      if (userResult.success && userResult.data) {
        customerId = userResult.data.id
      }
    }

    const body = await request.json()
    
    // Validate required fields
    if (!body.amount || body.amount <= 0) {
      return NextResponse.json(
        { success: false, error: 'Valid amount is required' },
        { status: 400 }
      )
    }

    const input: CreatePaymentIntentInput = {
      amount: {
        amount: parseFloat(body.amount),
        currency: body.currency || 'ZAR'
      },
      customerId,
      paymentMethodId: body.paymentMethodId,
      paymentMethodTypes: body.paymentMethodTypes || ['card'],
      captureMethod: body.captureMethod || 'automatic',
      confirmationMethod: body.confirmationMethod || 'automatic',
      orderId: body.orderId,
      description: body.description,
      statementDescriptor: body.statementDescriptor,
      receiptEmail: body.receiptEmail,
      metadata: body.metadata
    }

    const result = await paymentService.createPaymentIntent(input)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Create payment intent API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

async function handleConfirmPaymentIntent(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.paymentIntentId) {
      return NextResponse.json(
        { success: false, error: 'Payment intent ID is required' },
        { status: 400 }
      )
    }

    const input: ConfirmPaymentIntentInput = {
      paymentIntentId: body.paymentIntentId,
      paymentMethodId: body.paymentMethodId,
      returnUrl: body.returnUrl,
      metadata: body.metadata
    }

    const result = await paymentService.confirmPaymentIntent(input)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Confirm payment intent API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
