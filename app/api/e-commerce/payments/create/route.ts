import { NextRequest, NextResponse } from 'next/server'
import { PaymentService } from '@/lib/ecommerce/services/payment-service'
import { z } from 'zod'

const createPaymentSchema = z.object({
  orderId: z.string(),
  amount: z.number().min(0),
  currency: z.literal('ZAR'),
  customerEmail: z.string().email(),
  customerName: z.string(),
  description: z.string(),
  paymentMethod: z.enum(['payfast', 'ozow']),
  returnUrl: z.string().url(),
  cancelUrl: z.string().url(),
  notifyUrl: z.string().url()
})

const paymentService = new PaymentService()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate request body
    const validatedData = createPaymentSchema.parse(body)

    // Create payment
    const payment = await paymentService.createPayment(validatedData.paymentMethod, {
      orderId: validatedData.orderId,
      amount: validatedData.amount,
      currency: validatedData.currency,
      customerEmail: validatedData.customerEmail,
      customerName: validatedData.customerName,
      description: validatedData.description,
      returnUrl: validatedData.returnUrl,
      cancelUrl: validatedData.cancelUrl,
      notifyUrl: validatedData.notifyUrl
    })

    if (payment.success) {
      return NextResponse.json({
        success: true,
        message: 'Payment created successfully',
        data: {
          paymentId: payment.paymentId,
          redirectUrl: payment.redirectUrl
        }
      }, { status: 201 })
    } else {
      return NextResponse.json({
        success: false,
        message: payment.error || 'Failed to create payment'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Create payment error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        message: 'Invalid request data',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: 'Failed to create payment'
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const paymentMethods = await paymentService.getAvailablePaymentMethods()

    return NextResponse.json({
      success: true,
      data: paymentMethods
    })

  } catch (error) {
    console.error('Get payment methods error:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to retrieve payment methods'
    }, { status: 500 })
  }
}
