import { NextRequest, NextResponse } from 'next/server'
import { PaymentService } from '@/lib/ecommerce/services/payment-service'
import { OrderService } from '@/lib/ecommerce/services/order-service'

const paymentService = new PaymentService()
const orderService = new OrderService()

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const formData = new URLSearchParams(body)
    const data: Record<string, any> = {}
    
    // Convert form data to object
    for (const [key, value] of formData.entries()) {
      data[key] = value
    }

    console.log('Payment webhook received:', data)

    // Determine payment provider
    let provider: string
    let orderId: string
    let paymentStatus: string

    if (data.m_payment_id || data.merchant_id) {
      // PayFast webhook
      provider = 'payfast'
      orderId = data.m_payment_id || data.custom_str1
      
      // Verify PayFast signature
      const isValid = await paymentService.verifyPayment('payfast', data)
      if (!isValid) {
        console.error('PayFast signature verification failed')
        return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
      }

      // Map PayFast status
      paymentStatus = data.payment_status === 'COMPLETE' ? 'paid' : 'failed'
      
    } else if (data.SiteCode || data.TransactionReference) {
      // Ozow webhook
      provider = 'ozow'
      orderId = data.TransactionReference?.replace(/^TXN_/, '').replace(/_\d+$/, '') || ''
      
      // Verify Ozow signature
      const isValid = await paymentService.verifyPayment('ozow', data)
      if (!isValid) {
        console.error('Ozow signature verification failed')
        return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
      }

      // Map Ozow status
      paymentStatus = data.Status === 'Complete' ? 'paid' : 'failed'
      
    } else {
      console.error('Unknown payment provider in webhook')
      return NextResponse.json({ error: 'Unknown payment provider' }, { status: 400 })
    }

    if (!orderId) {
      console.error('No order ID found in webhook data')
      return NextResponse.json({ error: 'No order ID found' }, { status: 400 })
    }

    // Update payment status
    await paymentService.updatePaymentStatus(
      data.TransactionReference || data.m_payment_id,
      paymentStatus,
      data
    )

    // Update order payment status
    await orderService.updatePaymentStatus(orderId, paymentStatus as any)

    console.log(`Payment ${paymentStatus} for order ${orderId}`)

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Payment webhook error:', error)
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 })
  }
}

// Handle GET requests for webhook verification (some providers send GET requests)
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  
  // PayFast sends a GET request for verification
  if (searchParams.has('m_payment_id')) {
    return NextResponse.json({ success: true })
  }
  
  return NextResponse.json({ error: 'Invalid request' }, { status: 400 })
}
