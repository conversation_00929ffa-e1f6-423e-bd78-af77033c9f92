// Checkout API Route
// POST /api/e-commerce/checkout - Process complete checkout

import { NextRequest, NextResponse } from 'next/server'
import { 
  orderService, 
  paymentService, 
  cartService, 
  handleEcommerceError,
  createSuccessResponse,
  createErrorResponse,
  createValidationErrorResponse,
  handleApiError
} from '@/lib/ecommerce'
import { CreateOrderInput } from '@/lib/ecommerce/types'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      customerEmail,
      customerPhone,
      customerName,
      shippingAddress,
      billingAddress,
      shippingMethod,
      paymentMethod,
      items,
      couponCode,
      notes,
      userId,
      sessionId
    } = body

    // Validate required fields
    if (!customerEmail || !customerName || !shippingAddress || !shippingMethod || !paymentMethod || !items || items.length === 0) {
      return NextResponse.json(
        createValidationErrorResponse('Missing required checkout information'),
        { status: 400 }
      )
    }

    // Calculate totals
    const subtotal = items.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0)
    
    // Calculate shipping cost based on method
    let shippingCost = 0
    switch (shippingMethod) {
      case 'standard':
        shippingCost = 99
        break
      case 'express':
        shippingCost = 199
        break
      case 'collection':
        shippingCost = 0
        break
      default:
        shippingCost = 99
    }

    // Calculate tax (15% VAT for South Africa)
    const taxRate = 0.15
    const taxAmount = subtotal * taxRate

    // Calculate discount if coupon provided
    let discountAmount = 0
    if (couponCode) {
      // TODO: Implement coupon validation and discount calculation
      // For now, apply a simple 10% discount for demo
      if (couponCode.toLowerCase() === 'welcome10') {
        discountAmount = subtotal * 0.1
      }
    }

    const total = subtotal + shippingCost + taxAmount - discountAmount

    // Create order input
    const orderInput: CreateOrderInput = {
      customerEmail,
      customerPhone,
      items: items.map((item: any) => ({
        productId: item.productId,
        variantId: item.variantId,
        quantity: item.quantity,
        unitPrice: {
          amount: item.price,
          currency: 'ZAR'
        },
        productTitle: item.name,
        productImage: item.image,
        variantOptions: [
          ...(item.color ? [{ name: 'color', value: item.color }] : []),
          ...(item.size ? [{ name: 'size', value: item.size }] : [])
        ]
      })),
      shippingAddress: {
        firstName: shippingAddress.firstName,
        lastName: shippingAddress.lastName,
        address1: shippingAddress.address,
        address2: shippingAddress.apartment,
        city: shippingAddress.city,
        province: shippingAddress.province,
        postalCode: shippingAddress.postalCode,
        country: shippingAddress.country || 'ZA',
        phone: customerPhone
      },
      billingAddress: billingAddress ? {
        firstName: billingAddress.firstName,
        lastName: billingAddress.lastName,
        address1: billingAddress.address,
        address2: billingAddress.apartment,
        city: billingAddress.city,
        province: billingAddress.province,
        postalCode: billingAddress.postalCode,
        country: billingAddress.country || 'ZA',
        phone: customerPhone
      } : undefined,
      shippingMethod,
      paymentMethod,
      subtotal: {
        amount: subtotal,
        currency: 'ZAR'
      },
      shippingCost: {
        amount: shippingCost,
        currency: 'ZAR'
      },
      taxAmount: {
        amount: taxAmount,
        currency: 'ZAR'
      },
      discountAmount: {
        amount: discountAmount,
        currency: 'ZAR'
      },
      total: {
        amount: total,
        currency: 'ZAR'
      },
      currency: 'ZAR',
      notes
    }

    // Create order
    const orderResult = await orderService.createOrder(orderInput)

    if (!orderResult.success || !orderResult.data) {
      const { response, statusCode } = handleApiError(orderResult.error)
      return NextResponse.json(response, { status: statusCode })
    }

    const order = orderResult.data

    // Clear cart after successful order creation
    if (userId || sessionId) {
      try {
        const cartResult = await cartService.getOrCreateCart(userId, sessionId)
        if (cartResult.success && cartResult.data) {
          await cartService.clearCart(cartResult.data.id)
        }
      } catch (error) {
        console.error('Failed to clear cart after order creation:', error)
        // Don't fail the checkout if cart clearing fails
      }
    }

    // For cash on collection, no payment processing needed
    if (paymentMethod === 'cash') {
      return NextResponse.json(
        createSuccessResponse({
          success: true,
          orderId: order.id,
          orderNumber: order.handle || order.id,
          total: total,
          paymentMethod: 'cash'
        })
      )
    }

    // Create payment for other methods
    try {
      const paymentRequest = {
        orderId: order.id,
        amount: total,
        currency: 'ZAR' as const,
        customerEmail,
        customerName,
        description: `Order ${order.handle || order.id} - Coco Milk Kids`,
        returnUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/frontend/orders/${order.id}?payment=success`,
        cancelUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/frontend/checkout?payment=cancelled`,
        notifyUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/api/e-commerce/payments/webhook`
      }

      // Determine payment gateway based on method
      let gateway = 'payfast' // default
      if (paymentMethod === 'eft' || paymentMethod === 'ozow') {
        gateway = 'ozow'
      }

      const paymentResult = await paymentService.createPayment(gateway, paymentRequest)

      if (paymentResult.success && paymentResult.redirectUrl) {
        return NextResponse.json(
          createSuccessResponse({
            success: true,
            orderId: order.id,
            orderNumber: order.handle || order.id,
            paymentUrl: paymentResult.redirectUrl,
            paymentId: paymentResult.paymentId
          })
        )
      } else {
        // Payment creation failed, but order was created
        return NextResponse.json(
          createSuccessResponse({
            success: true,
            orderId: order.id,
            orderNumber: order.handle || order.id,
            error: 'Payment processing failed. Please contact support.',
            paymentError: paymentResult.error
          })
        )
      }
    } catch (paymentError) {
      console.error('Payment creation error:', paymentError)
      
      // Return order success but payment failure
      return NextResponse.json(
        createSuccessResponse({
          success: true,
          orderId: order.id,
          orderNumber: order.handle || order.id,
          error: 'Payment processing failed. Please contact support.',
          paymentError: paymentError instanceof Error ? paymentError.message : 'Unknown payment error'
        })
      )
    }

  } catch (error) {
    console.error('Checkout API error:', error)
    const { response, statusCode } = handleApiError(error)
    return NextResponse.json(response, { status: statusCode })
  }
}
