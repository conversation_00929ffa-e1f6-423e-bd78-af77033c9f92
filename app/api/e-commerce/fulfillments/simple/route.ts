// Simple Fulfillment API for Testing
// POST /api/e-commerce/fulfillments/simple - Create simple fulfillment

import { NextRequest, NextResponse } from 'next/server'
import { SimpleFulfillmentService } from '@/lib/ecommerce/services/simple-fulfillment-service'

const fulfillmentService = new SimpleFulfillmentService()

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = request.headers.get('x-admin-request') === 'true'
    if (!isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const body = await request.json()

    // Validate required fields
    if (!body.orderId || !body.items || !Array.isArray(body.items) || body.items.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: orderId, items' },
        { status: 400 }
      )
    }

    if (!body.carrier || !body.service) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: carrier, service' },
        { status: 400 }
      )
    }

    // Create fulfillment request
    const fulfillmentRequest = {
      orderId: body.orderId,
      items: body.items,
      carrier: body.carrier,
      service: body.service,
      notes: body.notes
    }

    // Create fulfillment
    const result = await fulfillmentService.createFulfillment(fulfillmentRequest)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data,
        message: 'Fulfillment created successfully'
      })
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Create simple fulfillment error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create fulfillment' },
      { status: 500 }
    )
  }
}
