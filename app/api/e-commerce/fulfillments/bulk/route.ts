// Bulk Fulfillment Operations API Route
// POST /api/e-commerce/fulfillments/bulk - Bulk process fulfillments

import { NextRequest, NextResponse } from 'next/server'
import { FulfillmentService } from '@/lib/ecommerce/services/fulfillment-service'

const fulfillmentService = new FulfillmentService()

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = request.headers.get('x-admin-request') === 'true'
    if (!isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { action, fulfillmentRequests, fulfillmentIds, data } = body

    if (action === 'create' && fulfillmentRequests) {
      // Bulk create fulfillments
      if (!Array.isArray(fulfillmentRequests)) {
        return NextResponse.json(
          { success: false, error: 'fulfillmentRequests must be an array' },
          { status: 400 }
        )
      }

      const result = await fulfillmentService.bulkProcessFulfillments(fulfillmentRequests)
      
      return NextResponse.json({
        success: true,
        data: result,
        message: `Processed ${result.processed} fulfillments. ${result.successful} successful, ${result.failed} failed.`
      })
    }

    if (action === 'updateStatus' && fulfillmentIds && data?.status) {
      // Bulk update fulfillment status
      if (!Array.isArray(fulfillmentIds)) {
        return NextResponse.json(
          { success: false, error: 'fulfillmentIds must be an array' },
          { status: 400 }
        )
      }

      const results = []
      for (const fulfillmentId of fulfillmentIds) {
        try {
          const fulfillment = await fulfillmentService.updateFulfillmentStatus(
            fulfillmentId,
            data.status,
            data.notes
          )
          results.push({ fulfillmentId, success: true, data: fulfillment })
        } catch (error) {
          results.push({
            fulfillmentId,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      const successful = results.filter(r => r.success).length
      const failed = results.filter(r => !r.success).length

      return NextResponse.json({
        success: true,
        data: {
          processed: results.length,
          successful,
          failed,
          results
        },
        message: `Updated ${successful} fulfillments. ${failed} failed.`
      })
    }

    if (action === 'cancel' && fulfillmentIds) {
      // Bulk cancel fulfillments
      if (!Array.isArray(fulfillmentIds)) {
        return NextResponse.json(
          { success: false, error: 'fulfillmentIds must be an array' },
          { status: 400 }
        )
      }

      const reason = data?.reason || 'Bulk cancellation'
      const results = []

      for (const fulfillmentId of fulfillmentIds) {
        try {
          const result = await fulfillmentService.cancelFulfillment(fulfillmentId, reason)
          results.push({ fulfillmentId, success: true, message: result.message })
        } catch (error) {
          results.push({
            fulfillmentId,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      const successful = results.filter(r => r.success).length
      const failed = results.filter(r => !r.success).length

      return NextResponse.json({
        success: true,
        data: {
          processed: results.length,
          successful,
          failed,
          results
        },
        message: `Cancelled ${successful} fulfillments. ${failed} failed.`
      })
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action or missing required fields' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Bulk fulfillment operation error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to process bulk operation' },
      { status: 500 }
    )
  }
}
