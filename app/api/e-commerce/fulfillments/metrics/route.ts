// Fulfillment Metrics API Route
// GET /api/e-commerce/fulfillments/metrics - Get fulfillment analytics and metrics

import { NextRequest, NextResponse } from 'next/server'
import { SimpleFulfillmentService } from '@/lib/ecommerce/services/simple-fulfillment-service'

const fulfillmentService = new SimpleFulfillmentService()

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = request.headers.get('x-admin-request') === 'true'
    if (!isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30d'
    
    // Calculate date range based on period
    const endDate = new Date()
    const startDate = new Date()
    
    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7)
        break
      case '30d':
        startDate.setDate(endDate.getDate() - 30)
        break
      case '90d':
        startDate.setDate(endDate.getDate() - 90)
        break
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1)
        break
      default:
        startDate.setDate(endDate.getDate() - 30)
    }

    // Get fulfillment metrics
    const metrics = await fulfillmentService.getFulfillmentMetrics(startDate, endDate)

    // Calculate additional metrics
    const additionalMetrics = {
      averageFulfillmentTimeHours: Math.round(metrics.averageFulfillmentTime / (1000 * 60 * 60) * 100) / 100,
      fulfillmentRate: metrics.totalFulfillments > 0 ? 
        ((metrics.statusBreakdown.delivered || 0) / metrics.totalFulfillments * 100).toFixed(2) : '0.00',
      pendingFulfillments: metrics.statusBreakdown.pending || 0,
      inTransitFulfillments: (metrics.statusBreakdown.open || 0) + (metrics.statusBreakdown.in_transit || 0),
      deliveredFulfillments: metrics.statusBreakdown.delivered || 0,
      cancelledFulfillments: metrics.statusBreakdown.cancelled || 0
    }

    return NextResponse.json({
      success: true,
      data: {
        period,
        dateRange: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        },
        overview: {
          totalFulfillments: metrics.totalFulfillments,
          totalItemsFulfilled: metrics.totalItemsFulfilled,
          averageFulfillmentTimeHours: additionalMetrics.averageFulfillmentTimeHours,
          onTimeDeliveryRate: metrics.onTimeDeliveryRate.toFixed(2),
          fulfillmentRate: additionalMetrics.fulfillmentRate
        },
        statusBreakdown: {
          pending: additionalMetrics.pendingFulfillments,
          inTransit: additionalMetrics.inTransitFulfillments,
          delivered: additionalMetrics.deliveredFulfillments,
          cancelled: additionalMetrics.cancelledFulfillments,
          ...metrics.statusBreakdown
        },
        carrierBreakdown: metrics.fulfillmentsByCarrier,
        performance: {
          averageFulfillmentTime: metrics.averageFulfillmentTime,
          averageFulfillmentTimeHours: additionalMetrics.averageFulfillmentTimeHours,
          onTimeDeliveryRate: metrics.onTimeDeliveryRate,
          fulfillmentSuccessRate: parseFloat(additionalMetrics.fulfillmentRate)
        }
      }
    })

  } catch (error) {
    console.error('Get fulfillment metrics error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch fulfillment metrics' },
      { status: 500 }
    )
  }
}
