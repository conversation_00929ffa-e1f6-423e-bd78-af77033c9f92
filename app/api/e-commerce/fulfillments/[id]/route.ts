// Individual Fulfillment API Routes
// GET /api/e-commerce/fulfillments/[id] - Get fulfillment by ID
// PATCH /api/e-commerce/fulfillments/[id] - Update fulfillment
// DELETE /api/e-commerce/fulfillments/[id] - Cancel fulfillment

import { NextRequest, NextResponse } from 'next/server'
import { SimpleFulfillmentService } from '@/lib/ecommerce/services/simple-fulfillment-service'

const fulfillmentService = new SimpleFulfillmentService()

interface RouteParams {
  params: {
    id: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check admin authentication
    const isAdmin = request.headers.get('x-admin-request') === 'true'
    if (!isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const { id } = params
    const fulfillment = await fulfillmentService.getFulfillment(id)

    if (!fulfillment) {
      return NextResponse.json(
        { success: false, error: 'Fulfillment not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: fulfillment
    })

  } catch (error) {
    console.error('Get fulfillment error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch fulfillment' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    // Check admin authentication
    const isAdmin = request.headers.get('x-admin-request') === 'true'
    if (!isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const { id } = params
    const body = await request.json()

    // Update fulfillment status
    if (body.status) {
      const fulfillment = await fulfillmentService.updateFulfillmentStatus(
        id,
        body.status,
        body.notes
      )

      return NextResponse.json({
        success: true,
        data: fulfillment,
        message: `Fulfillment status updated to ${body.status}`
      })
    }

    // If no status update, return error
    return NextResponse.json(
      { success: false, error: 'No valid update fields provided' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Update fulfillment error:', error)
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Failed to update fulfillment' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Check admin authentication
    const isAdmin = request.headers.get('x-admin-request') === 'true'
    if (!isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const { id } = params
    const body = await request.json()
    const reason = body.reason || 'Cancelled by admin'

    const result = await fulfillmentService.cancelFulfillment(id, reason)

    return NextResponse.json({
      success: true,
      message: result.message
    })

  } catch (error) {
    console.error('Cancel fulfillment error:', error)
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Failed to cancel fulfillment' },
      { status: 500 }
    )
  }
}
