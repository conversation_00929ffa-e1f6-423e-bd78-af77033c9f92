// Fulfillment Tracking API Route
// GET /api/e-commerce/fulfillments/[id]/track - Track fulfillment status

import { NextRequest, NextResponse } from 'next/server'
import { FulfillmentService } from '@/lib/ecommerce/services/fulfillment-service'

const fulfillmentService = new FulfillmentService()

interface RouteParams {
  params: {
    id: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    
    // Track fulfillment
    const trackingResult = await fulfillmentService.trackFulfillment(id)

    if (trackingResult.success) {
      return NextResponse.json({
        success: true,
        data: {
          fulfillmentId: id,
          trackingNumber: trackingResult.trackingNumber,
          status: trackingResult.status,
          events: trackingResult.events,
          estimatedDelivery: trackingResult.estimatedDelivery,
          actualDelivery: trackingResult.actualDelivery
        }
      })
    } else {
      return NextResponse.json(
        { success: false, error: trackingResult.error },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Track fulfillment error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to track fulfillment' },
      { status: 500 }
    )
  }
}
