import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/lib/auth'

/**
 * PATCH /api/notifications/read-all
 * Mark all notifications as read for a user
 */
export async function PATCH(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { userId } = body

    // Validate userId matches session or user is admin
    if (userId !== session.user.id && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      )
    }

    const now = new Date()

    // Get unread notifications count before update
    const unreadCount = await prisma.notification.count({
      where: {
        recipientId: userId,
        readAt: null,
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: now } }
        ]
      }
    })

    if (unreadCount === 0) {
      return NextResponse.json({
        success: true,
        message: 'No unread notifications to mark as read',
        markedCount: 0
      })
    }

    // Update all unread notifications to read
    const updateResult = await prisma.notification.updateMany({
      where: {
        recipientId: userId,
        readAt: null,
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: now } }
        ]
      },
      data: {
        readAt: now,
        status: 'READ',
        updatedAt: now
      }
    })

    // Create a bulk interaction log entry
    await prisma.notificationInteraction.create({
      data: {
        notificationId: 'bulk_read_all',
        type: 'OPENED',
        userAgent: request.headers.get('user-agent'),
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
        data: {
          action: 'bulk_read_all',
          userId,
          count: updateResult.count,
          timestamp: now.toISOString()
        }
      }
    })

    // Log the bulk action
    console.log('Bulk mark as read:', {
      userId,
      markedCount: updateResult.count,
      timestamp: now.toISOString()
    })

    return NextResponse.json({
      success: true,
      markedCount: updateResult.count,
      message: `${updateResult.count} notifications marked as read`
    })

  } catch (error) {
    console.error('PATCH /api/notifications/read-all error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/notifications/read-all
 * Mark all notifications as unread for a user (admin only)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Authenticate admin user
    const session = await auth()
    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { userId } = body

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'userId is required' },
        { status: 400 }
      )
    }

    const now = new Date()

    // Get read notifications count before update
    const readCount = await prisma.notification.count({
      where: {
        recipientId: userId,
        readAt: { not: null },
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: now } }
        ]
      }
    })

    if (readCount === 0) {
      return NextResponse.json({
        success: true,
        message: 'No read notifications to mark as unread',
        markedCount: 0
      })
    }

    // Update all read notifications to unread
    const updateResult = await prisma.notification.updateMany({
      where: {
        recipientId: userId,
        readAt: { not: null },
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: now } }
        ]
      },
      data: {
        readAt: null,
        status: 'DELIVERED',
        updatedAt: now
      }
    })

    // Log the bulk action
    console.log('Bulk mark as unread (admin):', {
      userId,
      markedCount: updateResult.count,
      adminId: session.user.id,
      timestamp: now.toISOString()
    })

    return NextResponse.json({
      success: true,
      markedCount: updateResult.count,
      message: `${updateResult.count} notifications marked as unread`
    })

  } catch (error) {
    console.error('DELETE /api/notifications/read-all error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}
