import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/lib/auth'
import { z } from 'zod'

// Validation schema for notification preferences
const preferencesSchema = z.object({
  emailEnabled: z.boolean(),
  smsEnabled: z.boolean(),
  pushEnabled: z.boolean(),
  inAppEnabled: z.boolean(),
  frequency: z.enum(['IMMEDIATE', 'HOURLY', 'DAILY', 'WEEKLY']),
  quietHoursStart: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  quietHoursEnd: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  timezone: z.string().default('Africa/Johannesburg'),
  language: z.string().default('en'),
  categories: z.object({
    orders: z.boolean(),
    payments: z.boolean(),
    shipping: z.boolean(),
    promotions: z.boolean(),
    system: z.boolean(),
    security: z.boolean()
  })
})

/**
 * GET /api/notifications/preferences
 * Get notification preferences for a user
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const url = new URL(request.url)
    const userId = url.searchParams.get('userId')

    // Validate user access
    if (userId !== session.user.id && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      )
    }

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'userId parameter is required' },
        { status: 400 }
      )
    }

    // Get user preferences
    const preferences = await prisma.notificationPreference.findUnique({
      where: {
        userId_userType: {
          userId,
          userType: 'CUSTOMER'
        }
      }
    })

    // Return default preferences if none exist
    if (!preferences) {
      const defaultPreferences = {
        emailEnabled: true,
        smsEnabled: true,
        pushEnabled: true,
        inAppEnabled: true,
        frequency: 'IMMEDIATE',
        quietHoursStart: '22:00',
        quietHoursEnd: '08:00',
        timezone: 'Africa/Johannesburg',
        language: 'en',
        categories: {
          orders: true,
          payments: true,
          shipping: true,
          promotions: false,
          system: true,
          security: true
        }
      }

      return NextResponse.json({
        success: true,
        preferences: defaultPreferences
      })
    }

    // Format preferences for response
    const formattedPreferences = {
      emailEnabled: preferences.emailEnabled,
      smsEnabled: preferences.smsEnabled,
      pushEnabled: preferences.pushEnabled,
      inAppEnabled: preferences.inAppEnabled,
      frequency: preferences.frequency,
      quietHoursStart: preferences.quietHoursStart,
      quietHoursEnd: preferences.quietHoursEnd,
      timezone: preferences.timezone,
      language: preferences.language,
      categories: preferences.categories as Record<string, boolean>
    }

    return NextResponse.json({
      success: true,
      preferences: formattedPreferences
    })

  } catch (error) {
    console.error('GET /api/notifications/preferences error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/notifications/preferences
 * Update notification preferences for a user
 */
export async function PUT(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { userId, preferences } = body

    // Validate user access
    if (userId !== session.user.id && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      )
    }

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'userId is required' },
        { status: 400 }
      )
    }

    // Validate preferences
    const validation = preferencesSchema.safeParse(preferences)
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid preferences data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const validatedPreferences = validation.data

    // Upsert preferences
    const updatedPreferences = await prisma.notificationPreference.upsert({
      where: {
        userId_userType: {
          userId,
          userType: 'CUSTOMER'
        }
      },
      update: {
        emailEnabled: validatedPreferences.emailEnabled,
        smsEnabled: validatedPreferences.smsEnabled,
        pushEnabled: validatedPreferences.pushEnabled,
        inAppEnabled: validatedPreferences.inAppEnabled,
        frequency: validatedPreferences.frequency,
        quietHoursStart: validatedPreferences.quietHoursStart,
        quietHoursEnd: validatedPreferences.quietHoursEnd,
        timezone: validatedPreferences.timezone,
        language: validatedPreferences.language,
        categories: validatedPreferences.categories,
        updatedAt: new Date()
      },
      create: {
        userId,
        userType: 'CUSTOMER',
        emailEnabled: validatedPreferences.emailEnabled,
        smsEnabled: validatedPreferences.smsEnabled,
        pushEnabled: validatedPreferences.pushEnabled,
        inAppEnabled: validatedPreferences.inAppEnabled,
        frequency: validatedPreferences.frequency,
        quietHoursStart: validatedPreferences.quietHoursStart,
        quietHoursEnd: validatedPreferences.quietHoursEnd,
        timezone: validatedPreferences.timezone,
        language: validatedPreferences.language,
        categories: validatedPreferences.categories
      }
    })

    console.log('Notification preferences updated:', {
      userId,
      updatedBy: session.user.id,
      timestamp: new Date().toISOString()
    })

    return NextResponse.json({
      success: true,
      preferences: {
        emailEnabled: updatedPreferences.emailEnabled,
        smsEnabled: updatedPreferences.smsEnabled,
        pushEnabled: updatedPreferences.pushEnabled,
        inAppEnabled: updatedPreferences.inAppEnabled,
        frequency: updatedPreferences.frequency,
        quietHoursStart: updatedPreferences.quietHoursStart,
        quietHoursEnd: updatedPreferences.quietHoursEnd,
        timezone: updatedPreferences.timezone,
        language: updatedPreferences.language,
        categories: updatedPreferences.categories as Record<string, boolean>
      }
    })

  } catch (error) {
    console.error('PUT /api/notifications/preferences error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/notifications/preferences
 * Reset notification preferences to defaults for a user
 */
export async function DELETE(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { userId } = body

    // Validate user access
    if (userId !== session.user.id && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      )
    }

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'userId is required' },
        { status: 400 }
      )
    }

    // Delete existing preferences (will fall back to defaults)
    await prisma.notificationPreference.deleteMany({
      where: {
        userId,
        userType: 'CUSTOMER'
      }
    })

    console.log('Notification preferences reset to defaults:', {
      userId,
      resetBy: session.user.id,
      timestamp: new Date().toISOString()
    })

    return NextResponse.json({
      success: true,
      message: 'Preferences reset to defaults'
    })

  } catch (error) {
    console.error('DELETE /api/notifications/preferences error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}
