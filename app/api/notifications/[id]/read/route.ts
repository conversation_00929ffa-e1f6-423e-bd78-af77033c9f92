import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/lib/auth'

interface RouteParams {
  params: {
    id: string
  }
}

/**
 * PATCH /api/notifications/[id]/read
 * Mark a specific notification as read
 */
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = params
    const body = await request.json()
    const { userId } = body

    // Validate userId matches session or user is admin
    if (userId !== session.user.id && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      )
    }

    // Check if notification exists and belongs to user
    const notification = await prisma.notification.findFirst({
      where: {
        id,
        recipientId: userId
      },
      select: {
        id: true,
        readAt: true,
        status: true
      }
    })

    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      )
    }

    // If already read, return success
    if (notification.readAt) {
      return NextResponse.json({
        success: true,
        message: 'Notification already marked as read',
        notification
      })
    }

    const now = new Date()

    // Update notification as read
    const updatedNotification = await prisma.notification.update({
      where: { id },
      data: {
        readAt: now,
        status: 'READ',
        updatedAt: now
      }
    })

    // Log the interaction
    await prisma.notificationInteraction.create({
      data: {
        notificationId: id,
        type: 'OPENED',
        userAgent: request.headers.get('user-agent'),
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
        data: {
          readBy: userId,
          readAt: now.toISOString()
        }
      }
    })

    // Log the event
    await prisma.notificationLog.create({
      data: {
        notificationId: id,
        event: 'read',
        details: `Notification marked as read by user ${userId}`,
        metadata: {
          userId,
          userAgent: request.headers.get('user-agent'),
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
        }
      }
    })

    return NextResponse.json({
      success: true,
      notification: updatedNotification
    })

  } catch (error) {
    console.error('PATCH /api/notifications/[id]/read error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/notifications/[id]/read
 * Mark a specific notification as unread
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = params
    const body = await request.json()
    const { userId } = body

    // Validate userId matches session or user is admin
    if (userId !== session.user.id && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      )
    }

    // Check if notification exists and belongs to user
    const notification = await prisma.notification.findFirst({
      where: {
        id,
        recipientId: userId
      },
      select: {
        id: true,
        readAt: true,
        status: true
      }
    })

    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      )
    }

    // If already unread, return success
    if (!notification.readAt) {
      return NextResponse.json({
        success: true,
        message: 'Notification already marked as unread',
        notification
      })
    }

    const now = new Date()

    // Update notification as unread
    const updatedNotification = await prisma.notification.update({
      where: { id },
      data: {
        readAt: null,
        status: 'DELIVERED',
        updatedAt: now
      }
    })

    // Log the event
    await prisma.notificationLog.create({
      data: {
        notificationId: id,
        event: 'unread',
        details: `Notification marked as unread by user ${userId}`,
        metadata: {
          userId,
          userAgent: request.headers.get('user-agent'),
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
        }
      }
    })

    return NextResponse.json({
      success: true,
      notification: updatedNotification
    })

  } catch (error) {
    console.error('DELETE /api/notifications/[id]/read error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}
