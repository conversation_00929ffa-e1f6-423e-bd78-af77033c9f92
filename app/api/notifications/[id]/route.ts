import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/lib/auth'

interface RouteParams {
  params: {
    id: string
  }
}

/**
 * GET /api/notifications/[id]
 * Get a specific notification by ID
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = params

    // Fetch notification
    const notification = await prisma.notification.findUnique({
      where: { id },
      include: {
        template: {
          select: {
            name: true,
            category: true
          }
        },
        logs: {
          orderBy: { timestamp: 'desc' },
          take: 10
        },
        interactions: {
          orderBy: { timestamp: 'desc' },
          take: 10
        }
      }
    })

    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      )
    }

    // Check authorization
    if (notification.recipientId !== session.user.id && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      notification
    })

  } catch (error) {
    console.error('GET /api/notifications/[id] error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}

/**
 * PATCH /api/notifications/[id]
 * Update a notification (mark as read, etc.)
 */
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = params
    const body = await request.json()

    // Fetch notification to check ownership
    const notification = await prisma.notification.findUnique({
      where: { id },
      select: { recipientId: true, readAt: true }
    })

    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      )
    }

    // Check authorization
    if (notification.recipientId !== session.user.id && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      )
    }

    // Prepare update data
    const updateData: any = {}
    const now = new Date()

    if (body.markAsRead && !notification.readAt) {
      updateData.readAt = now
      updateData.status = 'READ'
    }

    if (body.markAsUnread && notification.readAt) {
      updateData.readAt = null
      updateData.status = 'DELIVERED'
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, error: 'No valid updates provided' },
        { status: 400 }
      )
    }

    updateData.updatedAt = now

    // Update notification
    const updatedNotification = await prisma.notification.update({
      where: { id },
      data: updateData
    })

    // Log the interaction
    if (body.markAsRead) {
      await prisma.notificationInteraction.create({
        data: {
          notificationId: id,
          type: 'OPENED',
          userAgent: request.headers.get('user-agent'),
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
        }
      })
    }

    return NextResponse.json({
      success: true,
      notification: updatedNotification
    })

  } catch (error) {
    console.error('PATCH /api/notifications/[id] error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/notifications/[id]
 * Delete a specific notification
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = params

    // Fetch notification to check ownership
    const notification = await prisma.notification.findUnique({
      where: { id },
      select: { recipientId: true }
    })

    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      )
    }

    // Check authorization
    if (notification.recipientId !== session.user.id && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      )
    }

    // Delete notification and related records
    await prisma.$transaction([
      prisma.notificationInteraction.deleteMany({
        where: { notificationId: id }
      }),
      prisma.notificationLog.deleteMany({
        where: { notificationId: id }
      }),
      prisma.notification.delete({
        where: { id }
      })
    ])

    return NextResponse.json({
      success: true,
      message: 'Notification deleted successfully'
    })

  } catch (error) {
    console.error('DELETE /api/notifications/[id] error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}
