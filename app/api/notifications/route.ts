import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/lib/auth'
import { z } from 'zod'

// Request validation schemas
const getNotificationsSchema = z.object({
  userId: z.string(),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 50),
  offset: z.string().optional().transform(val => val ? parseInt(val) : 0),
  type: z.string().optional(),
  channel: z.string().optional(),
  unreadOnly: z.string().optional().transform(val => val === 'true'),
  search: z.string().optional()
})

/**
 * GET /api/notifications
 * Fetch notifications for a user with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse and validate query parameters
    const url = new URL(request.url)
    const params = Object.fromEntries(url.searchParams.entries())
    
    const validation = getNotificationsSchema.safeParse(params)
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid parameters',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const { userId, limit, offset, type, channel, unreadOnly, search } = validation.data

    // Check authorization - users can only access their own notifications
    if (userId !== session.user.id && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      )
    }

    // Build where clause
    const where: any = {
      recipientId: userId,
      // Filter out expired notifications
      OR: [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } }
      ]
    }

    if (type) {
      where.type = type
    }

    if (channel) {
      where.channel = channel
    }

    if (unreadOnly) {
      where.readAt = null
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Fetch notifications with pagination
    const [notifications, totalCount, unreadCount] = await Promise.all([
      prisma.notification.findMany({
        where,
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ],
        take: limit,
        skip: offset,
        include: {
          template: {
            select: {
              name: true,
              category: true
            }
          }
        }
      }),
      prisma.notification.count({ where }),
      prisma.notification.count({
        where: {
          recipientId: userId,
          readAt: null,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } }
          ]
        }
      })
    ])

    return NextResponse.json({
      success: true,
      notifications,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      },
      unreadCount
    })

  } catch (error) {
    console.error('GET /api/notifications error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/notifications
 * Create a new notification (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate admin user
    const session = await auth()
    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const body = await request.json()

    // Validate required fields
    const requiredFields = ['type', 'channel', 'title', 'content']
    const missingFields = requiredFields.filter(field => !body[field])
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Missing required fields: ${missingFields.join(', ')}` 
        },
        { status: 400 }
      )
    }

    // Create notification
    const notification = await prisma.notification.create({
      data: {
        type: body.type,
        channel: body.channel,
        title: body.title,
        content: body.content,
        data: body.data || {},
        recipientId: body.recipientId,
        recipientType: body.recipientType || 'CUSTOMER',
        recipientEmail: body.recipientEmail,
        recipientPhone: body.recipientPhone,
        status: 'PENDING',
        priority: body.priority || 'NORMAL',
        templateId: body.templateId,
        campaignId: body.campaignId,
        scheduledAt: body.scheduledAt ? new Date(body.scheduledAt) : null,
        expiresAt: body.expiresAt ? new Date(body.expiresAt) : null,
        metadata: body.metadata || {}
      }
    })

    return NextResponse.json({
      success: true,
      notification
    })

  } catch (error) {
    console.error('POST /api/notifications error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/notifications
 * Bulk delete notifications (admin only)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Authenticate admin user
    const session = await auth()
    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { notificationIds, userId, olderThan } = body

    let where: any = {}

    if (notificationIds && Array.isArray(notificationIds)) {
      where.id = { in: notificationIds }
    } else if (userId) {
      where.recipientId = userId
    } else if (olderThan) {
      where.createdAt = { lt: new Date(olderThan) }
    } else {
      return NextResponse.json(
        { success: false, error: 'No deletion criteria provided' },
        { status: 400 }
      )
    }

    const result = await prisma.notification.deleteMany({ where })

    return NextResponse.json({
      success: true,
      deletedCount: result.count
    })

  } catch (error) {
    console.error('DELETE /api/notifications error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}
