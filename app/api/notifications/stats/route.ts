import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/lib/auth'

/**
 * GET /api/notifications/stats
 * Get notification statistics for a user or system-wide (admin)
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const url = new URL(request.url)
    const userId = url.searchParams.get('userId')
    const systemWide = url.searchParams.get('systemWide') === 'true'
    const startDate = url.searchParams.get('startDate')
    const endDate = url.searchParams.get('endDate')

    // Authorization checks
    if (systemWide && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required for system-wide stats' },
        { status: 403 }
      )
    }

    if (userId && userId !== session.user.id && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Cannot access other user statistics' },
        { status: 403 }
      )
    }

    // Build base where clause
    const baseWhere: any = {}
    
    if (!systemWide && userId) {
      baseWhere.recipientId = userId
    }

    if (startDate) {
      baseWhere.createdAt = { gte: new Date(startDate) }
    }

    if (endDate) {
      baseWhere.createdAt = {
        ...baseWhere.createdAt,
        lte: new Date(endDate)
      }
    }

    // Get basic counts
    const [
      totalNotifications,
      unreadNotifications,
      readNotifications,
      failedNotifications
    ] = await Promise.all([
      prisma.notification.count({ where: baseWhere }),
      prisma.notification.count({ 
        where: { ...baseWhere, readAt: null } 
      }),
      prisma.notification.count({ 
        where: { ...baseWhere, readAt: { not: null } } 
      }),
      prisma.notification.count({ 
        where: { ...baseWhere, status: 'FAILED' } 
      })
    ])

    // Get statistics by type
    const typeStats = await prisma.notification.groupBy({
      by: ['type'],
      where: baseWhere,
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    })

    // Get statistics by channel
    const channelStats = await prisma.notification.groupBy({
      by: ['channel'],
      where: baseWhere,
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    })

    // Get statistics by priority
    const priorityStats = await prisma.notification.groupBy({
      by: ['priority'],
      where: baseWhere,
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    })

    // Get statistics by status
    const statusStats = await prisma.notification.groupBy({
      by: ['status'],
      where: baseWhere,
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    })

    // Get daily statistics for the last 30 days
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const dailyStats = await prisma.$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as total,
        COUNT(CASE WHEN read_at IS NOT NULL THEN 1 END) as read,
        COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed
      FROM notifications 
      WHERE created_at >= ${thirtyDaysAgo}
        ${userId ? prisma.$queryRaw`AND recipient_id = ${userId}` : prisma.$queryRaw``}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `

    // Calculate rates
    const readRate = totalNotifications > 0 ? (readNotifications / totalNotifications) * 100 : 0
    const failureRate = totalNotifications > 0 ? (failedNotifications / totalNotifications) * 100 : 0
    const deliveryRate = 100 - failureRate

    // Format statistics
    const stats = {
      overview: {
        total: totalNotifications,
        unread: unreadNotifications,
        read: readNotifications,
        failed: failedNotifications,
        readRate: Math.round(readRate * 100) / 100,
        failureRate: Math.round(failureRate * 100) / 100,
        deliveryRate: Math.round(deliveryRate * 100) / 100
      },
      byType: typeStats.reduce((acc, stat) => {
        acc[stat.type] = stat._count.id
        return acc
      }, {} as Record<string, number>),
      byChannel: channelStats.reduce((acc, stat) => {
        acc[stat.channel] = stat._count.id
        return acc
      }, {} as Record<string, number>),
      byPriority: priorityStats.reduce((acc, stat) => {
        acc[stat.priority] = stat._count.id
        return acc
      }, {} as Record<string, number>),
      byStatus: statusStats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.id
        return acc
      }, {} as Record<string, number>),
      daily: dailyStats,
      period: {
        startDate: startDate || thirtyDaysAgo.toISOString(),
        endDate: endDate || new Date().toISOString()
      }
    }

    // Add system-wide specific stats for admins
    if (systemWide && session.user.isAdmin) {
      const [
        totalUsers,
        activeUsers,
        totalTemplates,
        activeCampaigns
      ] = await Promise.all([
        prisma.user.count(),
        prisma.notification.groupBy({
          by: ['recipientId'],
          where: {
            createdAt: {
              gte: thirtyDaysAgo
            }
          }
        }).then(result => result.length),
        prisma.notificationTemplate.count(),
        prisma.notificationCampaign.count({
          where: {
            status: 'RUNNING'
          }
        })
      ])

      stats.system = {
        totalUsers,
        activeUsers,
        totalTemplates,
        activeCampaigns,
        averageNotificationsPerUser: totalUsers > 0 ? Math.round(totalNotifications / totalUsers) : 0
      }
    }

    return NextResponse.json({
      success: true,
      stats
    })

  } catch (error) {
    console.error('GET /api/notifications/stats error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}
