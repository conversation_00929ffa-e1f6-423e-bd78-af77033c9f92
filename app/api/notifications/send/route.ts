import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { notificationManager } from '@/lib/notifications'
import { z } from 'zod'
import { ratelimit } from '@/lib/ratelimit'

// Request validation schema
const sendNotificationSchema = z.object({
  type: z.string(),
  channel: z.enum(['EMAIL', 'SMS', 'PUSH', 'IN_APP', 'WEBHOOK']),
  title: z.string().min(1).max(255),
  content: z.string().min(1).max(10000),
  recipientId: z.string().optional(),
  recipientType: z.enum(['CUSTOMER', 'ADMIN', 'STAFF', 'SYSTEM']).optional(),
  recipientEmail: z.string().email().optional(),
  recipientPhone: z.string().optional(),
  data: z.record(z.any()).optional(),
  templateId: z.string().optional(),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']).optional(),
  scheduledAt: z.string().datetime().optional(),
  expiresAt: z.string().datetime().optional(),
  metadata: z.record(z.any()).optional()
})

/**
 * POST /api/notifications/send
 * Send a notification through the notification manager
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Rate limiting
    const identifier = session.user.isAdmin ? `admin:${session.user.id}` : `user:${session.user.id}`
    const { success: rateLimitSuccess } = await ratelimit.limit(identifier)
    
    if (!rateLimitSuccess) {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validation = sendNotificationSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid request data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const notificationRequest = validation.data

    // Authorization checks
    if (!session.user.isAdmin) {
      // Non-admin users can only send to themselves
      if (notificationRequest.recipientId && notificationRequest.recipientId !== session.user.id) {
        return NextResponse.json(
          { success: false, error: 'Cannot send notifications to other users' },
          { status: 403 }
        )
      }

      // Non-admin users can only send certain types
      const allowedTypes = ['CUSTOM', 'FEEDBACK', 'SUPPORT_REQUEST']
      if (!allowedTypes.includes(notificationRequest.type)) {
        return NextResponse.json(
          { success: false, error: 'Notification type not allowed' },
          { status: 403 }
        )
      }

      // Limit channels for non-admin users
      const allowedChannels = ['IN_APP']
      if (!allowedChannels.includes(notificationRequest.channel)) {
        return NextResponse.json(
          { success: false, error: 'Notification channel not allowed' },
          { status: 403 }
        )
      }
    }

    // Channel-specific validation
    if (notificationRequest.channel === 'EMAIL' && !notificationRequest.recipientEmail) {
      return NextResponse.json(
        { success: false, error: 'recipientEmail is required for email notifications' },
        { status: 400 }
      )
    }

    if (notificationRequest.channel === 'SMS' && !notificationRequest.recipientPhone) {
      return NextResponse.json(
        { success: false, error: 'recipientPhone is required for SMS notifications' },
        { status: 400 }
      )
    }

    if (['PUSH', 'IN_APP'].includes(notificationRequest.channel) && !notificationRequest.recipientId) {
      return NextResponse.json(
        { success: false, error: 'recipientId is required for push and in-app notifications' },
        { status: 400 }
      )
    }

    // Add metadata
    const enrichedRequest = {
      ...notificationRequest,
      metadata: {
        ...notificationRequest.metadata,
        sentBy: session.user.id,
        sentAt: new Date().toISOString(),
        userAgent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
      }
    }

    // Send notification
    let result
    if (notificationRequest.scheduledAt) {
      // Schedule for later
      result = await notificationManager.schedule(enrichedRequest)
    } else {
      // Send immediately
      result = await notificationManager.send(enrichedRequest)
    }

    // Log the attempt
    console.log('Notification send attempt:', {
      success: result.success,
      type: notificationRequest.type,
      channel: notificationRequest.channel,
      recipient: notificationRequest.recipientEmail || notificationRequest.recipientPhone || notificationRequest.recipientId,
      sentBy: session.user.id,
      error: result.error
    })

    return NextResponse.json(result)

  } catch (error) {
    console.error('POST /api/notifications/send error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/notifications/send/bulk
 * Send multiple notifications (admin only)
 */
export async function PUT(request: NextRequest) {
  try {
    // Authenticate admin user
    const session = await auth()
    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { notifications } = body

    if (!Array.isArray(notifications) || notifications.length === 0) {
      return NextResponse.json(
        { success: false, error: 'notifications array is required' },
        { status: 400 }
      )
    }

    if (notifications.length > 1000) {
      return NextResponse.json(
        { success: false, error: 'Maximum 1000 notifications per batch' },
        { status: 400 }
      )
    }

    // Validate each notification
    const validationErrors: string[] = []
    const validatedNotifications = notifications.map((notification, index) => {
      const validation = sendNotificationSchema.safeParse(notification)
      if (!validation.success) {
        validationErrors.push(`Notification ${index}: ${validation.error.errors.map(e => e.message).join(', ')}`)
        return null
      }
      return {
        ...validation.data,
        metadata: {
          ...validation.data.metadata,
          sentBy: session.user.id,
          sentAt: new Date().toISOString(),
          batchId: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        }
      }
    }).filter(Boolean)

    if (validationErrors.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation errors',
          details: validationErrors
        },
        { status: 400 }
      )
    }

    // Send bulk notifications
    const results = await notificationManager.sendBulk(validatedNotifications)

    const successCount = results.filter(r => r.success).length
    const failureCount = results.length - successCount

    console.log('Bulk notification send completed:', {
      total: results.length,
      success: successCount,
      failed: failureCount,
      sentBy: session.user.id
    })

    return NextResponse.json({
      success: true,
      results,
      summary: {
        total: results.length,
        success: successCount,
        failed: failureCount
      }
    })

  } catch (error) {
    console.error('PUT /api/notifications/send error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}
