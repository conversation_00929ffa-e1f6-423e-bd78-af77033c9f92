import { NextRequest, NextResponse } from 'next/server'
import { analyticsManager } from '@/lib/analytics/manager'
import { wordpressAuth } from '@/lib/wordpress'
import { rateLimit } from '@/lib/rate-limit'
import { generateDateRange } from '@/lib/analytics/utils'
import type { AnalyticsPeriod } from '@/lib/analytics/types'
// Simple console logger for analytics
const logger = {
  error: (message: string, meta?: any) => {
    console.error(`[Analytics API Error] ${message}`, meta)
  }
}

// Rate limiting
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500,
})

export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 30, 'ANALYTICS_API')
    } catch {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Check authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const isValidToken = await wordpressAuth.validateToken(token)
    
    if (!isValidToken) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Get current user and check permissions
    const currentUser = await wordpressAuth.getCurrentUser(token)
    
    if (!wordpressAuth.hasRole(currentUser, 'administrator') && 
        !wordpressAuth.hasRole(currentUser, 'shop_manager')) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'overview'
    const period = (searchParams.get('period') || 'month') as AnalyticsPeriod
    const dateFrom = searchParams.get('dateFrom') ? new Date(searchParams.get('dateFrom')!) : undefined
    const dateTo = searchParams.get('dateTo') ? new Date(searchParams.get('dateTo')!) : undefined

    // Generate date range
    const dateRange = generateDateRange(period, 
      dateFrom && dateTo ? { from: dateFrom, to: dateTo } : undefined
    )

    switch (type) {
      case 'sales':
        const salesAnalytics = await analyticsManager.getSalesAnalytics(
          dateRange.from,
          dateRange.to,
          { from: dateRange.previousFrom, to: dateRange.previousTo }
        )
        
        return NextResponse.json({
          success: true,
          data: salesAnalytics,
          period: {
            from: dateRange.from,
            to: dateRange.to,
            period,
          },
        })

      case 'products':
        const limit = parseInt(searchParams.get('limit') || '50')
        const productMetrics = await analyticsManager.getProductMetrics(
          dateRange.from,
          dateRange.to,
          limit
        )
        
        return NextResponse.json({
          success: true,
          data: productMetrics,
          period: {
            from: dateRange.from,
            to: dateRange.to,
            period,
          },
        })

      case 'customers':
        const customerLimit = parseInt(searchParams.get('limit') || '100')
        const customerMetrics = await analyticsManager.getCustomerMetrics(
          dateRange.from,
          dateRange.to,
          customerLimit
        )
        
        return NextResponse.json({
          success: true,
          data: customerMetrics,
          period: {
            from: dateRange.from,
            to: dateRange.to,
            period,
          },
        })

      case 'inventory':
        const inventoryAnalytics = await analyticsManager.getInventoryAnalytics()
        
        return NextResponse.json({
          success: true,
          data: inventoryAnalytics,
        })

      case 'realtime':
        const realTimeMetrics = await analyticsManager.getRealTimeMetrics()
        
        return NextResponse.json({
          success: true,
          data: realTimeMetrics,
          timestamp: new Date(),
        })

      case 'overview':
      default:
        // Get overview data combining multiple analytics
        const [sales, inventory, realtime] = await Promise.all([
          analyticsManager.getSalesAnalytics(dateRange.from, dateRange.to),
          analyticsManager.getInventoryAnalytics(),
          analyticsManager.getRealTimeMetrics(),
        ])

        const overview = {
          sales: {
            totalRevenue: sales.totalRevenue,
            totalOrders: sales.totalOrders,
            averageOrderValue: sales.averageOrderValue,
            conversionRate: sales.conversionRate,
          },
          inventory: {
            totalItems: inventory.totalItems,
            totalValue: inventory.totalValue,
            lowStockItems: inventory.lowStockItems,
            outOfStockItems: inventory.outOfStockItems,
          },
          realtime: {
            activeUsers: realtime.activeUsers,
            currentOrders: realtime.currentOrders,
            revenueToday: realtime.revenueToday,
            alerts: realtime.alerts.length,
          },
        }

        return NextResponse.json({
          success: true,
          data: overview,
          period: {
            from: dateRange.from,
            to: dateRange.to,
            period,
          },
        })
    }

  } catch (error) {
    logger.error('Analytics API error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    })

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 10, 'ANALYTICS_QUERY')
    } catch {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Check authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const isValidToken = await wordpressAuth.validateToken(token)
    
    if (!isValidToken) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Get current user and check permissions
    const currentUser = await wordpressAuth.getCurrentUser(token)
    
    if (!wordpressAuth.hasRole(currentUser, 'administrator') && 
        !wordpressAuth.hasRole(currentUser, 'shop_manager')) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { action, query } = body

    switch (action) {
      case 'custom_query':
        // Handle custom analytics queries
        // This would implement a flexible query system
        return NextResponse.json({
          success: true,
          data: [],
          message: 'Custom queries not yet implemented',
        })

      case 'export_data':
        // Handle data export requests
        const { type, format, dateRange } = query
        
        // This would generate and return export data
        return NextResponse.json({
          success: true,
          data: {
            downloadUrl: '/api/analytics/export/placeholder.csv',
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
          },
          message: 'Export prepared successfully',
        })

      case 'schedule_report':
        // Handle report scheduling
        const { reportType, schedule, recipients } = query
        
        // This would set up scheduled reports
        return NextResponse.json({
          success: true,
          data: {
            scheduleId: `schedule_${Date.now()}`,
            nextRun: new Date(Date.now() + 24 * 60 * 60 * 1000),
          },
          message: 'Report scheduled successfully',
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    logger.error('Analytics POST API error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    })

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
