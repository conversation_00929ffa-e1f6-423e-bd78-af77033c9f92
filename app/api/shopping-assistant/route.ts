import { openai } from "@ai-sdk/openai"
import { streamText, tool } from "ai"
import { NextResponse } from "next/server"
import { getProducts } from "@/lib/products"
import { z } from "zod"

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

export async function POST(req: Request) {
  try {
    const { messages } = await req.json()

    const system = `You are a helpful shopping assistant for Coco Milk Kids, a proudly South African premium children's clothing store, powered by SoImagine AI.
    Help customers with product information, sizing, materials, care instructions, and shopping recommendations specifically for South African families.

    Key South African context:
    - Prices are in South African Rand (ZAR)
    - Consider South African climate (hot summers, mild winters)
    - Understand local lifestyle (braais, outdoor activities, school uniforms)
    - Be aware of Heritage Day and local celebrations
    - Consider budget-conscious South African families
    - Shipping across South Africa (free shipping over R1,500)
    - Collection available from Sandton store

    Be friendly, use South African expressions where appropriate, and be helpful. If you don't know specific product details,
    suggest general guidance and recommend contacting customer service (+27 11 123 4567 or <EMAIL>).
    Our brand focuses on comfort, quality, and style for South African children aged 2-12.
    Use the provided tools to get product information and make recommendations.`

    const result = streamText({
      model: openai("gpt-4o"),
      system,
      messages: messages.map((msg: any) => ({
        role: msg.role,
        content: msg.content,
      })),
      tools: {
        getProductInfo: tool({
          description: "Get information about products",
          parameters: z.object({
            category: z.string().optional().describe("Optional category to filter by (e.g., tops, bottoms, dresses, outerwear, accessories)"),
            priceRange: z.string().optional().describe("Optional price range to filter by (e.g., 'under R500', 'R500-R800', 'over R800')"),
            ageGroup: z.string().optional().describe("Optional age group to filter by (e.g., '2-3', '4-5', '6-7', '8-9', '10-12')"),
          }),
          execute: async ({ category, priceRange, ageGroup }) => {
            const products = await getProducts();

            let filtered = products;

            // Filter by category if provided
            if (category) {
              filtered = filtered.filter(p => p.categoryId.toLowerCase() === category.toLowerCase());
            }

            // Filter by price range if provided
            if (priceRange) {
              if (priceRange.toLowerCase().includes('under r500') || priceRange.toLowerCase().includes('under 500')) {
                filtered = filtered.filter(p => p.price < 500);
              } else if (priceRange.toLowerCase().includes('r500-r800') || priceRange.toLowerCase().includes('500-800')) {
                filtered = filtered.filter(p => p.price >= 500 && p.price <= 800);
              } else if (priceRange.toLowerCase().includes('over r800') || priceRange.toLowerCase().includes('over 800')) {
                filtered = filtered.filter(p => p.price > 800);
              }
            }

            // Map age groups to sizes
            let sizeFilter;
            if (ageGroup) {
              if (ageGroup === '2-3') sizeFilter = 'XS';
              else if (ageGroup === '4-5') sizeFilter = 'S';
              else if (ageGroup === '6-7') sizeFilter = 'M';
              else if (ageGroup === '8-9') sizeFilter = 'L';
              else if (ageGroup === '10-12') sizeFilter = 'XL';

              if (sizeFilter) {
                filtered = filtered.filter(p => p.sizes.includes(sizeFilter));
              }
            }

            return filtered.map(p => ({
              id: p.id,
              name: p.name,
              price: p.price,
              category: p.categoryId,
              isNew: p.isNew,
              isSale: p.isSale,
              sizes: p.sizes,
              description: p.description.substring(0, 100) + '...'
            }));
          }
        }),
        getSizeInformation: tool({
          description: "Get size information for children's clothing",
          parameters: z.object({}),
          execute: async () => {
            return {
              sizes: [
                { size: "XS", ageRange: "2-3 years", heightRange: "85-95 cm", weightRange: "12-15 kg" },
                { size: "S", ageRange: "4-5 years", heightRange: "95-110 cm", weightRange: "15-19 kg" },
                { size: "M", ageRange: "6-7 years", heightRange: "110-125 cm", weightRange: "19-25 kg" },
                { size: "L", ageRange: "8-9 years", heightRange: "125-140 cm", weightRange: "25-35 kg" },
                { size: "XL", ageRange: "10-12 years", heightRange: "140-155 cm", weightRange: "35-45 kg" },
              ]
            }
          }
        }),
        getMaterialsAndCare: tool({
          description: "Get information about materials and care instructions",
          parameters: z.object({}),
          execute: async () => {
            return {
              materials: [
                { type: "Cotton", description: "100% organic cotton, soft and breathable, OEKO-TEX certified" },
                { type: "Denim", description: "98% cotton, 2% elastane for comfort and flexibility" },
                { type: "Knitwear", description: "Soft acrylic and cotton blend, gentle on sensitive skin" },
              ],
              careInstructions: [
                { category: "Cotton Items", instructions: "Machine wash at 30°C, tumble dry low or air dry (great for SA sunshine!). Iron on medium heat if needed. Perfect for our hot climate." },
                { category: "Denim", instructions: "Machine wash cold, inside out. Air dry to prevent shrinking - use SA's sunny weather to your advantage! Iron on low if needed." },
                { category: "Knitwear", instructions: "Hand wash in cold water, lay flat to dry in shade. Perfect for SA's mild winters. Do not iron." },
                { category: "Sun Protection", instructions: "All our fabrics offer UPF protection. Wash regularly to maintain protective qualities, especially after beach or outdoor activities." },
              ]
            }
          }
        })
      }
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error("Shopping assistant error:", error)
    return NextResponse.json({ error: "Failed to connect to shopping assistant" }, { status: 500 })
  }
}
