import { openai } from '@ai-sdk/openai'
import { streamText } from 'ai'

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

export async function POST(req: Request) {
  try {
    const { messages } = await req.json()

    const result = streamText({
      model: openai('gpt-4o'),
      messages,
      system: `You are a helpful AI assistant for a page builder application. You help users create, modify, and optimize page blocks and layouts.

You can help with:
- Understanding different block types and their uses
- Suggesting design improvements
- Providing content recommendations
- Explaining page builder features
- Troubleshooting layout issues
- Best practices for web design and UX

Be conversational, helpful, and provide specific actionable advice. When discussing blocks or layouts, be specific about configuration options and styling recommendations.

Available block types:
- Hero: Large banner sections with titles, subtitles, and CTAs
- Text: Rich text content blocks
- Image: Image display with various styling options
- Button: Call-to-action buttons with customizable styling
- Spacer: Spacing elements for layout control
- Product Grid: E-commerce product listings with filters
- Cart Widget: Shopping cart functionality
- Product Showcase: Featured product displays
- Layout Container: Flexible container for organizing other blocks

Always consider:
- Mobile responsiveness
- Accessibility
- Performance
- User experience
- Conversion optimization`,
      temperature: 0.7,
      maxTokens: 1000,
    })

    return result.toDataStreamResponse({
      getErrorMessage: (error) => {
        if (error == null) {
          return 'An unknown error occurred.'
        }

        if (typeof error === 'string') {
          return error
        }

        if (error instanceof Error) {
          return error.message
        }

        return 'An error occurred during the conversation.'
      },
    })
  } catch (error) {
    console.error('AI Chat Error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to process chat request',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}
