import { openai } from '@ai-sdk/openai'
import { streamText } from 'ai'
import { 
  generateBlockTool, 
  generateLayoutTool, 
  optimizeBlockTool, 
  generateContentTool, 
  analyzePageTool 
} from '@/lib/ai-block-generator/tools/block-generation-tools'

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

export async function POST(req: Request) {
  try {
    const { messages } = await req.json()

    const result = streamText({
      model: openai('gpt-4o'),
      messages,
      system: `You are an expert page builder AI assistant specializing in creating and optimizing web page blocks and layouts. You help users:

1. Generate individual blocks (hero, text, image, product grids, etc.)
2. Create complete page layouts with multiple coordinated blocks
3. Optimize existing blocks for performance, accessibility, and user experience
4. Generate compelling content and copy for blocks
5. Analyze pages and suggest improvements

Key principles:
- Always consider mobile responsiveness
- Prioritize accessibility and inclusive design
- Focus on conversion optimization and user experience
- Use modern design patterns and best practices
- Provide specific, actionable recommendations

When generating blocks or layouts:
- Ask clarifying questions if requirements are unclear
- Suggest complementary blocks that work well together
- Consider the overall page flow and user journey
- Provide styling recommendations that align with modern trends
- Include performance optimizations by default

Available block types: hero, text, image, button, spacer, product-grid, cart-widget, product-showcase, layout-container

For e-commerce sites, focus on:
- Clear product presentation
- Trust signals and social proof
- Streamlined checkout flows
- Mobile-first design
- Fast loading times`,
      tools: {
        generateBlock: generateBlockTool,
        generateLayout: generateLayoutTool,
        optimizeBlock: optimizeBlockTool,
        generateContent: generateContentTool,
        analyzePage: analyzePageTool,
      },
      maxSteps: 5,
      toolChoice: 'auto',
    })

    return result.toDataStreamResponse({
      getErrorMessage: (error) => {
        if (error == null) {
          return 'An unknown error occurred while generating blocks.'
        }

        if (typeof error === 'string') {
          return error
        }

        if (error instanceof Error) {
          return error.message
        }

        return 'An error occurred during block generation.'
      },
    })
  } catch (error) {
    console.error('AI Block Generation Error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to process AI block generation request',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}
