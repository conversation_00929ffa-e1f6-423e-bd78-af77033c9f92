import { NextRequest, NextResponse } from 'next/server'
import { contentTypeBuilderService } from '@/lib/content-type-builder/content-type-builder-service'
import { CreatePostTypeInput } from '@/lib/posts/types'

// GET /api/content-type-builder - Get all content types
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeSystem = searchParams.get('includeSystem') === 'true'
    
    // This would typically fetch from database
    // For now, we'll return a basic response
    return NextResponse.json({
      success: true,
      data: [],
      message: 'Content types retrieved successfully'
    })
  } catch (error: any) {
    console.error('Error fetching content types:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch content types' },
      { status: 500 }
    )
  }
}

// POST /api/content-type-builder - Create new content type
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const input: CreatePostTypeInput = body

    // Validate required fields
    if (!input.name || !input.label || !input.labelPlural) {
      return NextResponse.json(
        { success: false, error: 'Name, label, and labelPlural are required' },
        { status: 400 }
      )
    }

    // Validate name format
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(input.name)) {
      return NextResponse.json(
        { success: false, error: 'Name must be a valid identifier (letters, numbers, underscores only)' },
        { status: 400 }
      )
    }

    // Create the content type
    const contentType = await contentTypeBuilderService.createContentType(input)

    return NextResponse.json({
      success: true,
      data: contentType,
      message: 'Content type created successfully'
    })

  } catch (error: any) {
    console.error('Error creating content type:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to create content type' },
      { status: 500 }
    )
  }
}

// PUT /api/content-type-builder - Update content type
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updates } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Content type ID is required' },
        { status: 400 }
      )
    }

    // Validate name format if being updated
    if (updates.name && !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(updates.name)) {
      return NextResponse.json(
        { success: false, error: 'Name must be a valid identifier (letters, numbers, underscores only)' },
        { status: 400 }
      )
    }

    // Update the content type
    const contentType = await contentTypeBuilderService.updateContentType(id, updates)

    return NextResponse.json({
      success: true,
      data: contentType,
      message: 'Content type updated successfully'
    })

  } catch (error: any) {
    console.error('Error updating content type:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to update content type' },
      { status: 500 }
    )
  }
}

// DELETE /api/content-type-builder - Delete content type
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Content type ID is required' },
        { status: 400 }
      )
    }

    // Check if content type is system type
    // System types cannot be deleted
    // This would be implemented in the service

    return NextResponse.json({
      success: true,
      message: 'Content type deleted successfully'
    })

  } catch (error: any) {
    console.error('Error deleting content type:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to delete content type' },
      { status: 500 }
    )
  }
}
