import { NextRequest, NextResponse } from 'next/server'
import { contentTypeBuilderService } from '@/lib/content-type-builder/content-type-builder-service'

// GET /api/content-type-builder/field-types - Get available field types
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const search = searchParams.get('search')

    // Get all available field types
    let fieldTypes = contentTypeBuilderService.getAvailableFieldTypes()

    // Filter by category if specified
    if (category && category !== 'all') {
      fieldTypes = fieldTypes.filter(fieldType => fieldType.category === category)
    }

    // Filter by search term if specified
    if (search) {
      const searchLower = search.toLowerCase()
      fieldTypes = fieldTypes.filter(fieldType => 
        fieldType.label.toLowerCase().includes(searchLower) ||
        fieldType.description.toLowerCase().includes(searchLower) ||
        fieldType.type.toLowerCase().includes(searchLower)
      )
    }

    return NextResponse.json({
      success: true,
      data: fieldTypes,
      message: 'Field types retrieved successfully'
    })

  } catch (error: any) {
    console.error('Error fetching field types:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch field types' },
      { status: 500 }
    )
  }
}
