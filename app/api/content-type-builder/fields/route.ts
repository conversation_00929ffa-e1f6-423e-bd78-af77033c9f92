import { NextRequest, NextResponse } from 'next/server'
import { contentTypeBuilderService } from '@/lib/content-type-builder/content-type-builder-service'
import { CustomField } from '@/lib/posts/types'

// POST /api/content-type-builder/fields - Add custom field to content type
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { postTypeId, field } = body

    if (!postTypeId) {
      return NextResponse.json(
        { success: false, error: 'Post type ID is required' },
        { status: 400 }
      )
    }

    if (!field) {
      return NextResponse.json(
        { success: false, error: 'Field data is required' },
        { status: 400 }
      )
    }

    // Validate field data
    if (!field.name || !field.label || !field.type) {
      return NextResponse.json(
        { success: false, error: 'Field must have name, label, and type' },
        { status: 400 }
      )
    }

    // Add the field
    const updatedContentType = await contentTypeBuilderService.addCustomField(postTypeId, field)

    return NextResponse.json({
      success: true,
      data: updatedContentType,
      message: 'Field added successfully'
    })

  } catch (error: any) {
    console.error('Error adding field:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to add field' },
      { status: 500 }
    )
  }
}

// PUT /api/content-type-builder/fields - Update custom field
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { postTypeId, fieldId, updates } = body

    if (!postTypeId || !fieldId) {
      return NextResponse.json(
        { success: false, error: 'Post type ID and field ID are required' },
        { status: 400 }
      )
    }

    if (!updates) {
      return NextResponse.json(
        { success: false, error: 'Field updates are required' },
        { status: 400 }
      )
    }

    // Validate field name if being updated
    if (updates.name && !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(updates.name)) {
      return NextResponse.json(
        { success: false, error: 'Field name must be a valid identifier' },
        { status: 400 }
      )
    }

    // Update the field
    const updatedContentType = await contentTypeBuilderService.updateCustomField(
      postTypeId, 
      fieldId, 
      updates
    )

    return NextResponse.json({
      success: true,
      data: updatedContentType,
      message: 'Field updated successfully'
    })

  } catch (error: any) {
    console.error('Error updating field:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to update field' },
      { status: 500 }
    )
  }
}

// DELETE /api/content-type-builder/fields - Remove custom field
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const postTypeId = searchParams.get('postTypeId')
    const fieldId = searchParams.get('fieldId')

    if (!postTypeId || !fieldId) {
      return NextResponse.json(
        { success: false, error: 'Post type ID and field ID are required' },
        { status: 400 }
      )
    }

    // Remove the field
    const updatedContentType = await contentTypeBuilderService.removeCustomField(
      postTypeId, 
      fieldId
    )

    return NextResponse.json({
      success: true,
      data: updatedContentType,
      message: 'Field removed successfully'
    })

  } catch (error: any) {
    console.error('Error removing field:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to remove field' },
      { status: 500 }
    )
  }
}

// PATCH /api/content-type-builder/fields - Reorder fields
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { postTypeId, fieldOrder } = body

    if (!postTypeId) {
      return NextResponse.json(
        { success: false, error: 'Post type ID is required' },
        { status: 400 }
      )
    }

    if (!fieldOrder || !Array.isArray(fieldOrder)) {
      return NextResponse.json(
        { success: false, error: 'Field order array is required' },
        { status: 400 }
      )
    }

    // Reorder the fields
    const updatedContentType = await contentTypeBuilderService.reorderCustomFields(
      postTypeId, 
      fieldOrder
    )

    return NextResponse.json({
      success: true,
      data: updatedContentType,
      message: 'Fields reordered successfully'
    })

  } catch (error: any) {
    console.error('Error reordering fields:', error)
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to reorder fields' },
      { status: 500 }
    )
  }
}
