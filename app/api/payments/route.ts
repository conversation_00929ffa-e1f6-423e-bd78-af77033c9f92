import { NextRequest, NextResponse } from 'next/server'
import { rateLimit } from '@/lib/rate-limit'
import { wordpressAuth } from '@/lib/wordpress'
import type { PaymentRequest } from '@/lib/payments/types'
import { PaymentGateway, PaymentErrorCode, PaymentMethod } from '@/lib/payments/types'

// Lazy import functions to avoid circular dependencies
let paymentManagerInstance: any = null
const getPaymentManager = async () => {
  if (!paymentManagerInstance) {
    try {
      const { paymentManager } = await import('@/lib/payments/gateway-factory')
      paymentManagerInstance = paymentManager
    } catch (error) {
      console.error('Failed to import payment manager', error)
      // Return placeholder if import fails
      paymentManagerInstance = {
        processPayment: async () => ({ success: false, error: { message: 'Payment service unavailable' } }),
        getAvailablePaymentMethods: () => []
      }
    }
  }
  return paymentManagerInstance
}

let validatePaymentRequestFn: any = null
const getValidatePaymentRequest = async () => {
  if (!validatePaymentRequestFn) {
    try {
      const { validatePaymentRequest } = await import('@/lib/payments/utils')
      validatePaymentRequestFn = validatePaymentRequest
    } catch (error) {
      console.error('Failed to import payment validation', error)
      validatePaymentRequestFn = () => ({ isValid: true, errors: [] })
    }
  }
  return validatePaymentRequestFn
}

// Dedicated logger for payments API to avoid circular dependencies
const logger = {
  info: (message: string, meta?: any) => {
    console.log(`[Payments API] ${message}`, meta ? JSON.stringify(meta, null, 2) : '')
  },
  error: (message: string, meta?: any) => {
    console.error(`[Payments API Error] ${message}`, meta ? JSON.stringify(meta, null, 2) : '')
  }
}

const paymentLogger = {
  logPaymentFailed: (data: any) => {
    console.error('[Payment Failed]', JSON.stringify(data, null, 2))
  }
}

// Rate limiting
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500, // Limit each IP to 500 requests per minute
})

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 10, 'PAYMENT_API') // 10 requests per minute per IP
    } catch {
      return NextResponse.json(
        {
          success: false,
          error: 'Rate limit exceeded. Please try again later.',
        },
        { status: 429 }
      )
    }

    const body = await request.json()
    const { 
      amount, 
      customer, 
      items, 
      metadata, 
      returnUrl, 
      cancelUrl, 
      notifyUrl, 
      reference, 
      description,
      preferredGateway,
      preferredMethod 
    } = body

    // Validate required fields
    if (!amount || !customer || !items || !metadata) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields: amount, customer, items, metadata',
        },
        { status: 400 }
      )
    }

    // Check for authentication (optional for guest checkout)
    const authHeader = request.headers.get('authorization')
    let customerId: string | undefined

    if (authHeader) {
      const token = authHeader.replace('Bearer ', '')
      const isValidToken = await wordpressAuth.validateToken(token)
      
      if (isValidToken) {
        const currentUser = await wordpressAuth.getCurrentUser(token)
        customerId = currentUser.id.toString()
      }
    }

    // Create payment request object
    const paymentRequest: PaymentRequest = {
      amount: {
        amount: parseFloat(amount.amount),
        currency: amount.currency || 'ZAR',
        formatted: amount.formatted,
      },
      customer: {
        id: customerId,
        email: customer.email,
        firstName: customer.firstName,
        lastName: customer.lastName,
        phone: customer.phone,
        address: customer.address,
      },
      items: items.map((item: any) => ({
        id: item.id,
        name: item.name,
        description: item.description,
        quantity: parseInt(item.quantity),
        unitPrice: parseFloat(item.unitPrice),
        totalPrice: parseFloat(item.totalPrice),
        sku: item.sku,
        category: item.category,
      })),
      metadata: {
        orderId: metadata.orderId,
        customerId: customerId,
        source: metadata.source || 'web',
        ...metadata,
      },
      returnUrl: returnUrl || `${process.env.NEXTAUTH_URL}/payment/success`,
      cancelUrl: cancelUrl || `${process.env.NEXTAUTH_URL}/payment/cancelled`,
      notifyUrl: notifyUrl || `${process.env.NEXTAUTH_URL}/api/webhooks/payment`,
      reference: reference || `PAY_${Date.now()}`,
      description: description || 'Online purchase',
    }

    // Validate payment request
    const validatePaymentRequest = await getValidatePaymentRequest()
    const validation = validatePaymentRequest(paymentRequest)
    if (!validation.isValid) {
      paymentLogger.logPaymentFailed({
        gateway: preferredGateway || PaymentGateway.PAYFAST,
        reference: paymentRequest.reference,
        amount: paymentRequest.amount.amount,
        currency: paymentRequest.amount.currency,
        customerId: paymentRequest.metadata.customerId,
        orderId: paymentRequest.metadata.orderId,
        errorCode: PaymentErrorCode.INVALID_AMOUNT,
        errorMessage: validation.errors.join(', '),
      })

      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: validation.errors,
        },
        { status: 400 }
      )
    }

    // Process payment
    const paymentManager = await getPaymentManager()
    const paymentResponse = await paymentManager.processPayment(
      paymentRequest,
      preferredGateway as PaymentGateway,
      preferredMethod as PaymentMethod
    )

    // Log the request
    logger.info('Payment request processed', {
      reference: paymentRequest.reference,
      amount: paymentRequest.amount.amount,
      currency: paymentRequest.amount.currency,
      gateway: preferredGateway,
      success: paymentResponse.success,
    })

    if (paymentResponse.success) {
      return NextResponse.json({
        success: true,
        data: {
          paymentUrl: paymentResponse.paymentUrl,
          transactionId: paymentResponse.transactionId,
          reference: paymentResponse.reference,
          status: paymentResponse.status,
          qrCode: paymentResponse.qrCode,
        },
        message: 'Payment initiated successfully',
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: paymentResponse.error?.message || 'Payment failed',
          code: paymentResponse.error?.code,
        },
        { status: 400 }
      )
    }

  } catch (error) {
    logger.error('Payment API error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    })

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    )
  }
}

export async function GET(_request: NextRequest) {
  try {
    // Get available payment methods
    const paymentManager = await getPaymentManager()
    const availableMethods = paymentManager.getAvailablePaymentMethods()

    return NextResponse.json({
      success: true,
      data: {
        paymentMethods: availableMethods,
        supportedCurrencies: ['ZAR', 'USD', 'EUR', 'GBP'],
      },
    })

  } catch (error) {
    logger.error('Payment methods API error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    })

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get payment methods',
      },
      { status: 500 }
    )
  }
}
