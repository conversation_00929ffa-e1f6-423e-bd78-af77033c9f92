import { NextRequest, NextResponse } from 'next/server'
import { wordpressAuth } from '@/lib/wordpress'
import type { PaymentGateway } from '@/lib/payments/types'

// Lazy import function to avoid circular dependencies
let paymentManagerInstance: any = null
const getPaymentManager = async () => {
  if (!paymentManagerInstance) {
    try {
      const { paymentManager } = await import('@/lib/payments/gateway-factory')
      paymentManagerInstance = paymentManager
    } catch (error) {
      console.error('Failed to import payment manager', error)
      // Return placeholder if import fails
      paymentManagerInstance = {
        getPaymentStatus: async () => 'failed'
      }
    }
  }
  return paymentManagerInstance
}

// Dedicated logger for payment status API to avoid circular dependencies
const logger = {
  info: (message: string, meta?: any) => {
    console.log(`[Payment Status API] ${message}`, meta ? JSON.stringify(meta, null, 2) : '')
  },
  error: (message: string, meta?: any) => {
    console.error(`[Payment Status API Error] ${message}`, meta ? JSON.stringify(meta, null, 2) : '')
  }
}

interface RouteParams {
  params: {
    transactionId: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { transactionId } = params
    const { searchParams } = new URL(request.url)
    const gateway = searchParams.get('gateway') as PaymentGateway

    if (!transactionId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Transaction ID is required',
        },
        { status: 400 }
      )
    }

    if (!gateway) {
      return NextResponse.json(
        {
          success: false,
          error: 'Gateway parameter is required',
        },
        { status: 400 }
      )
    }

    // Check for authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
        },
        { status: 401 }
      )
    }

    // Validate token
    const token = authHeader.replace('Bearer ', '')
    const isValidToken = await wordpressAuth.validateToken(token)
    
    if (!isValidToken) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid or expired token',
        },
        { status: 401 }
      )
    }

    // Get payment status
    const paymentManager = await getPaymentManager()
    const status = await paymentManager.getPaymentStatus(transactionId, gateway)

    logger.info('Payment status checked', {
      transactionId,
      gateway,
      status,
    })

    return NextResponse.json({
      success: true,
      data: {
        transactionId,
        gateway,
        status,
        timestamp: new Date().toISOString(),
      },
    })

  } catch (error) {
    logger.error('Payment status API error', {
      transactionId: params.transactionId,
      error: error instanceof Error ? error.message : 'Unknown error',
    })

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get payment status',
      },
      { status: 500 }
    )
  }
}
