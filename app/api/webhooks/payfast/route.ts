import { NextRequest, NextResponse } from 'next/server'
import { wooCommerceClient } from '@/lib/wordpress'
import crypto from 'crypto'

// Production-ready constants
const PAYFAST_GATEWAY = 'PAYFAST'
const WEBHOOK_EVENTS = {
  PAYMENT_COMPLETED: 'PAYMENT_COMPLETED',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  PAYMENT_CANCELLED: 'PAYMENT_CANCELLED',
  PAYMENT_PENDING: 'PAYMENT_PENDING'
} as const

// Production-ready logger with structured logging
const logger = {
  info: (message: string, meta?: Record<string, any>) => {
    const logEntry = {
      level: 'info',
      timestamp: new Date().toISOString(),
      service: 'payfast-webhook',
      message,
      ...meta
    }
    console.log(JSON.stringify(logEntry))
  },
  error: (message: string, meta?: Record<string, any>) => {
    const logEntry = {
      level: 'error',
      timestamp: new Date().toISOString(),
      service: 'payfast-webhook',
      message,
      ...meta
    }
    console.error(JSON.stringify(logEntry))
  },
  warn: (message: string, meta?: Record<string, any>) => {
    const logEntry = {
      level: 'warn',
      timestamp: new Date().toISOString(),
      service: 'payfast-webhook',
      message,
      ...meta
    }
    console.warn(JSON.stringify(logEntry))
  }
}

// Production-ready payment logger
const paymentLogger = {
  logWebhookReceived: (data: Record<string, any>) => {
    logger.info('Webhook received', {
      gateway: PAYFAST_GATEWAY,
      transactionId: data.pf_payment_id,
      reference: data.m_payment_id,
      status: data.payment_status,
      verified: data.verified || false
    })
  },
  logWebhookError: (error: string, data?: Record<string, any>) => {
    logger.error('Webhook processing error', {
      gateway: PAYFAST_GATEWAY,
      error,
      transactionId: data?.pf_payment_id,
      reference: data?.m_payment_id
    })
  },
  logPaymentCompleted: (data: Record<string, any>) => {
    logger.info('Payment completed', {
      gateway: PAYFAST_GATEWAY,
      transactionId: data.transactionId,
      reference: data.reference,
      amount: data.amount,
      currency: data.currency,
      orderId: data.orderId
    })
  },
  logPaymentFailed: (data: Record<string, any>) => {
    logger.error('Payment failed', {
      gateway: PAYFAST_GATEWAY,
      transactionId: data.transactionId,
      reference: data.reference,
      amount: data.amount,
      currency: data.currency,
      orderId: data.orderId,
      errorCode: data.errorCode,
      errorMessage: data.errorMessage
    })
  },
  logPaymentCancelled: (data: Record<string, any>) => {
    logger.info('Payment cancelled', {
      gateway: PAYFAST_GATEWAY,
      transactionId: data.transactionId,
      reference: data.reference,
      amount: data.amount,
      currency: data.currency,
      orderId: data.orderId
    })
  }
}

// Production-ready data masking utility
const maskSensitiveData = (data: Record<string, any>): Record<string, any> => {
  const sensitiveFields = ['signature', 'passphrase', 'merchant_id', 'merchant_key']
  const masked = { ...data }

  sensitiveFields.forEach(field => {
    if (masked[field]) {
      masked[field] = '***MASKED***'
    }
  })

  return masked
}

// Production-ready PayFast signature verification
const verifyPayFastSignature = (data: Record<string, any>, providedSignature: string): boolean => {
  try {
    const passphrase = process.env.PAYFAST_PASSPHRASE

    // Create parameter string for signature verification
    const paramString = Object.keys(data)
      .filter(key => key !== 'signature' && data[key] !== '')
      .sort()
      .map(key => `${key}=${encodeURIComponent(data[key])}`)
      .join('&')

    // Add passphrase if provided
    const stringToHash = passphrase ? `${paramString}&passphrase=${passphrase}` : paramString

    // Generate MD5 hash
    const calculatedSignature = crypto.createHash('md5').update(stringToHash).digest('hex')

    return calculatedSignature === providedSignature
  } catch (error) {
    logger.error('Error verifying PayFast signature', { error: error instanceof Error ? error.message : 'Unknown error' })
    return false
  }
}

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  let transactionId = 'unknown'

  try {
    // Parse the webhook payload (PayFast sends form data)
    const formData = await request.formData()
    const data: Record<string, any> = {}

    for (const [key, value] of formData.entries()) {
      data[key] = value.toString()
    }

    transactionId = data.pf_payment_id || 'unknown'

    // Validate required fields
    if (!data.pf_payment_id || !data.payment_status) {
      logger.error('Invalid webhook payload - missing required fields', {
        hasPaymentId: !!data.pf_payment_id,
        hasPaymentStatus: !!data.payment_status
      })
      return NextResponse.json(
        { error: 'Invalid payload - missing required fields' },
        { status: 400 }
      )
    }

    // Log webhook received
    paymentLogger.logWebhookReceived({ ...data, verified: false })

    // Verify the webhook signature if signature is provided
    if (data.signature) {
      const isValid = verifyPayFastSignature(data, data.signature)
      if (!isValid) {
        paymentLogger.logWebhookError('Invalid webhook signature', data)
        return NextResponse.json(
          { error: 'Invalid signature' },
          { status: 400 }
        )
      }
    } else {
      logger.warn('No signature provided in PayFast webhook', { transactionId })
    }

    // Log verified webhook
    paymentLogger.logWebhookReceived({ ...data, verified: true })

    // Determine webhook event based on payment status
    let webhookEvent: string
    switch (data.payment_status) {
      case 'COMPLETE':
        webhookEvent = WEBHOOK_EVENTS.PAYMENT_COMPLETED
        break
      case 'FAILED':
        webhookEvent = WEBHOOK_EVENTS.PAYMENT_FAILED
        break
      case 'CANCELLED':
        webhookEvent = WEBHOOK_EVENTS.PAYMENT_CANCELLED
        break
      default:
        webhookEvent = WEBHOOK_EVENTS.PAYMENT_PENDING
    }

    // Update order in WooCommerce based on payment status
    await updateWooCommerceOrder(data)

    // Log successful processing
    logger.info('PayFast webhook processed successfully', {
      gateway: PAYFAST_GATEWAY,
      event: webhookEvent,
      transactionId: data.pf_payment_id,
      reference: data.m_payment_id,
      status: data.payment_status,
      processingTime: Date.now() - startTime
    })

    // PayFast expects a 200 OK response
    return NextResponse.json({
      status: 'OK',
      transactionId: data.pf_payment_id,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    const processingTime = Date.now() - startTime

    logger.error('PayFast webhook processing failed', {
      transactionId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      processingTime
    })

    paymentLogger.logWebhookError(
      error instanceof Error ? error.message : 'Unknown error',
      { transactionId }
    )

    return NextResponse.json(
      {
        error: 'Webhook processing failed',
        transactionId,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

/**
 * Production-ready WooCommerce order update function for PayFast
 */
async function updateWooCommerceOrder(data: Record<string, any>): Promise<void> {
  const startTime = Date.now()
  let orderId: string | null = null

  try {
    orderId = data.custom_str1 // Order ID stored in custom field
    if (!orderId) {
      logger.warn('No order ID found in PayFast webhook', {
        transactionId: data.pf_payment_id,
        reference: data.m_payment_id,
        maskedData: maskSensitiveData(data)
      })
      return
    }

    // Validate order ID format
    const orderIdNum = parseInt(orderId)
    if (isNaN(orderIdNum) || orderIdNum <= 0) {
      logger.error('Invalid order ID format', { orderId, transactionId: data.pf_payment_id })
      return
    }

    // Get the order from WooCommerce with retry logic
    let order = null
    let retryCount = 0
    const maxRetries = 3

    while (retryCount < maxRetries && !order) {
      try {
        order = await wooCommerceClient.getOrder(orderIdNum)
        break
      } catch (error) {
        retryCount++
        if (retryCount < maxRetries) {
          logger.warn(`Failed to get order, retrying (${retryCount}/${maxRetries})`, {
            orderId,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)) // Exponential backoff
        } else {
          throw error
        }
      }
    }

    if (!order) {
      logger.error('Order not found in WooCommerce after retries', {
        orderId,
        transactionId: data.pf_payment_id,
        retryCount
      })
      return
    }

    let newStatus: string
    const amount = parseFloat(data.amount_gross) || 0
    const currency = 'ZAR'
    const customerId = data.custom_str2 || null

    switch (data.payment_status) {
      case 'COMPLETE':
        newStatus = 'processing'

        paymentLogger.logPaymentCompleted({
          gateway: PAYFAST_GATEWAY,
          reference: data.m_payment_id,
          transactionId: data.pf_payment_id,
          amount,
          currency,
          customerId,
          orderId
        })
        break

      case 'FAILED':
        newStatus = 'failed'

        paymentLogger.logPaymentFailed({
          gateway: PAYFAST_GATEWAY,
          reference: data.m_payment_id,
          transactionId: data.pf_payment_id,
          amount,
          currency,
          customerId,
          orderId,
          errorCode: 'PAYMENT_FAILED',
          errorMessage: 'Payment failed at gateway'
        })
        break

      case 'CANCELLED':
        newStatus = 'cancelled'

        paymentLogger.logPaymentCancelled({
          gateway: PAYFAST_GATEWAY,
          reference: data.m_payment_id,
          transactionId: data.pf_payment_id,
          amount,
          currency,
          customerId,
          orderId
        })
        break

      default:
        logger.warn('Unknown PayFast payment status', {
          status: data.payment_status,
          orderId,
          transactionId: data.pf_payment_id
        })
        return
    }

    // Update order status in WooCommerce with retry logic
    retryCount = 0
    while (retryCount < maxRetries) {
      try {
        await wooCommerceClient.updateOrderStatus(orderIdNum, newStatus)
        break
      } catch (error) {
        retryCount++
        if (retryCount < maxRetries) {
          logger.warn(`Failed to update order status, retrying (${retryCount}/${maxRetries})`, {
            orderId,
            newStatus,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
        } else {
          throw error
        }
      }
    }

    const processingTime = Date.now() - startTime
    logger.info('WooCommerce order updated successfully', {
      orderId,
      newStatus,
      transactionId: data.pf_payment_id,
      gateway: PAYFAST_GATEWAY,
      processingTime
    })

  } catch (error) {
    const processingTime = Date.now() - startTime
    logger.error('Failed to update WooCommerce order', {
      orderId: orderId || data.custom_str1,
      transactionId: data.pf_payment_id,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      processingTime,
      maskedData: maskSensitiveData(data)
    })

    // Don't throw error to prevent webhook retry loops
    // The payment gateway will handle the transaction state
  }
}

// Handle GET requests for webhook validation/health check
export async function GET(_request: NextRequest) {
  return NextResponse.json({
    status: 'OK',
    service: 'payfast-webhook',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  })
}
