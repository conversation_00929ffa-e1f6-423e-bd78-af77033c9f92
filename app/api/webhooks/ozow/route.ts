import { NextRequest, NextResponse } from 'next/server'
import { wooCommerceClient } from '@/lib/wordpress'
import crypto from 'crypto'

// Production-ready constants
const OZOW_GATEWAY = 'OZOW'
const WEBHOOK_EVENTS = {
  PAYMENT_COMPLETED: 'PAYMENT_COMPLETED',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  PAYMENT_CANCELLED: 'PAYMENT_CANCELLED',
  PAYMENT_PENDING: 'PAYMENT_PENDING'
} as const

// Production-ready logger with structured logging
const logger = {
  info: (message: string, meta?: Record<string, any>) => {
    const logEntry = {
      level: 'info',
      timestamp: new Date().toISOString(),
      service: 'ozow-webhook',
      message,
      ...meta
    }
    console.log(JSON.stringify(logEntry))
  },
  error: (message: string, meta?: Record<string, any>) => {
    const logEntry = {
      level: 'error',
      timestamp: new Date().toISOString(),
      service: 'ozow-webhook',
      message,
      ...meta
    }
    console.error(JSON.stringify(logEntry))
  },
  warn: (message: string, meta?: Record<string, any>) => {
    const logEntry = {
      level: 'warn',
      timestamp: new Date().toISOString(),
      service: 'ozow-webhook',
      message,
      ...meta
    }
    console.warn(JSON.stringify(logEntry))
  }
}

// Production-ready payment logger
const paymentLogger = {
  logWebhookReceived: (data: Record<string, any>) => {
    logger.info('Webhook received', {
      gateway: OZOW_GATEWAY,
      transactionId: data.TransactionId,
      reference: data.TransactionReference,
      status: data.Status,
      verified: data.verified || false
    })
  },
  logWebhookError: (error: string, data?: Record<string, any>) => {
    logger.error('Webhook processing error', {
      gateway: OZOW_GATEWAY,
      error,
      transactionId: data?.TransactionId,
      reference: data?.TransactionReference
    })
  },
  logPaymentCompleted: (data: Record<string, any>) => {
    logger.info('Payment completed', {
      gateway: OZOW_GATEWAY,
      transactionId: data.transactionId,
      reference: data.reference,
      amount: data.amount,
      currency: data.currency,
      orderId: data.orderId
    })
  },
  logPaymentFailed: (data: Record<string, any>) => {
    logger.error('Payment failed', {
      gateway: OZOW_GATEWAY,
      transactionId: data.transactionId,
      reference: data.reference,
      amount: data.amount,
      currency: data.currency,
      orderId: data.orderId,
      errorCode: data.errorCode,
      errorMessage: data.errorMessage
    })
  },
  logPaymentCancelled: (data: Record<string, any>) => {
    logger.info('Payment cancelled', {
      gateway: OZOW_GATEWAY,
      transactionId: data.transactionId,
      reference: data.reference,
      amount: data.amount,
      currency: data.currency,
      orderId: data.orderId
    })
  }
}

// Production-ready data masking utility
const maskSensitiveData = (data: Record<string, any>): Record<string, any> => {
  const sensitiveFields = ['HashCheck', 'SiteCode', 'PrivateKey', 'ApiKey']
  const masked = { ...data }

  sensitiveFields.forEach(field => {
    if (masked[field]) {
      masked[field] = '***MASKED***'
    }
  })

  return masked
}

// Production-ready webhook signature verification
const verifyOzowSignature = (data: Record<string, any>, providedHash: string): boolean => {
  try {
    // Ozow signature verification logic
    // This is a simplified version - in production, use the actual Ozow signature verification
    const siteCode = process.env.OZOW_SITE_CODE
    const privateKey = process.env.OZOW_PRIVATE_KEY

    if (!siteCode || !privateKey) {
      logger.warn('Missing Ozow credentials for signature verification')
      return false
    }

    // Create hash string according to Ozow documentation
    const hashString = `${siteCode}${data.TransactionId}${data.Amount}${data.Status}${privateKey}`
    const calculatedHash = crypto.createHash('sha512').update(hashString).digest('hex').toLowerCase()

    return calculatedHash === providedHash?.toLowerCase()
  } catch (error) {
    logger.error('Error verifying Ozow signature', { error: error instanceof Error ? error.message : 'Unknown error' })
    return false
  }
}

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  let transactionId = 'unknown'

  try {
    // Parse the webhook payload with validation
    const data = await request.json()
    transactionId = data.TransactionId || 'unknown'

    // Validate required fields
    if (!data.TransactionId || !data.Status || !data.HashCheck) {
      logger.error('Invalid webhook payload - missing required fields', {
        hasTransactionId: !!data.TransactionId,
        hasStatus: !!data.Status,
        hasHashCheck: !!data.HashCheck
      })
      return NextResponse.json(
        { error: 'Invalid payload - missing required fields' },
        { status: 400 }
      )
    }

    // Log webhook received
    paymentLogger.logWebhookReceived({ ...data, verified: false })

    // Verify the webhook signature
    const isValid = verifyOzowSignature(data, data.HashCheck)
    if (!isValid) {
      paymentLogger.logWebhookError('Invalid webhook signature', data)
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      )
    }

    // Log verified webhook
    paymentLogger.logWebhookReceived({ ...data, verified: true })

    // Determine webhook event based on transaction status
    let webhookEvent: string
    switch (data.Status?.toLowerCase()) {
      case 'complete':
        webhookEvent = WEBHOOK_EVENTS.PAYMENT_COMPLETED
        break
      case 'error':
      case 'abandoned':
        webhookEvent = WEBHOOK_EVENTS.PAYMENT_FAILED
        break
      case 'cancelled':
        webhookEvent = WEBHOOK_EVENTS.PAYMENT_CANCELLED
        break
      case 'pendinginvestigation':
        webhookEvent = WEBHOOK_EVENTS.PAYMENT_PENDING
        break
      default:
        webhookEvent = WEBHOOK_EVENTS.PAYMENT_PENDING
    }

    // Update order in WooCommerce based on transaction status
    await updateWooCommerceOrder(data)

    // Log successful processing
    logger.info('Ozow webhook processed successfully', {
      gateway: OZOW_GATEWAY,
      event: webhookEvent,
      transactionId: data.TransactionId,
      reference: data.TransactionReference,
      status: data.Status,
      processingTime: Date.now() - startTime
    })

    // Ozow expects a 200 OK response
    return NextResponse.json({
      status: 'success',
      transactionId: data.TransactionId,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    const processingTime = Date.now() - startTime

    logger.error('Ozow webhook processing failed', {
      transactionId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      processingTime
    })

    paymentLogger.logWebhookError(
      error instanceof Error ? error.message : 'Unknown error',
      { transactionId }
    )

    return NextResponse.json(
      {
        error: 'Webhook processing failed',
        transactionId,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

/**
 * Production-ready WooCommerce order update function
 */
async function updateWooCommerceOrder(data: Record<string, any>): Promise<void> {
  const startTime = Date.now()
  let orderId: string | null = null

  try {
    orderId = data.Optional1 // Order ID stored in Optional1 field
    if (!orderId) {
      logger.warn('No order ID found in Ozow webhook', {
        transactionId: data.TransactionId,
        reference: data.TransactionReference,
        maskedData: maskSensitiveData(data)
      })
      return
    }

    // Validate order ID format
    const orderIdNum = parseInt(orderId)
    if (isNaN(orderIdNum) || orderIdNum <= 0) {
      logger.error('Invalid order ID format', { orderId, transactionId: data.TransactionId })
      return
    }

    // Get the order from WooCommerce with retry logic
    let order = null
    let retryCount = 0
    const maxRetries = 3

    while (retryCount < maxRetries && !order) {
      try {
        order = await wooCommerceClient.getOrder(orderIdNum)
        break
      } catch (error) {
        retryCount++
        if (retryCount < maxRetries) {
          logger.warn(`Failed to get order, retrying (${retryCount}/${maxRetries})`, {
            orderId,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)) // Exponential backoff
        } else {
          throw error
        }
      }
    }

    if (!order) {
      logger.error('Order not found in WooCommerce after retries', {
        orderId,
        transactionId: data.TransactionId,
        retryCount
      })
      return
    }

    let newStatus: string
    const amount = parseFloat(data.Amount) || 0
    const currency = 'ZAR'
    const customerId = data.Optional2 || null

    switch (data.Status?.toLowerCase()) {
      case 'complete':
        newStatus = 'processing'

        paymentLogger.logPaymentCompleted({
          gateway: OZOW_GATEWAY,
          reference: data.TransactionReference,
          transactionId: data.TransactionId,
          amount,
          currency,
          customerId,
          orderId
        })
        break

      case 'error':
      case 'abandoned':
        newStatus = 'failed'

        paymentLogger.logPaymentFailed({
          gateway: OZOW_GATEWAY,
          reference: data.TransactionReference,
          transactionId: data.TransactionId,
          amount,
          currency,
          customerId,
          orderId,
          errorCode: 'PAYMENT_FAILED',
          errorMessage: data.StatusMessage || 'Payment failed at gateway'
        })
        break

      case 'cancelled':
        newStatus = 'cancelled'

        paymentLogger.logPaymentCancelled({
          gateway: OZOW_GATEWAY,
          reference: data.TransactionReference,
          transactionId: data.TransactionId,
          amount,
          currency,
          customerId,
          orderId
        })
        break

      case 'pendinginvestigation':
        newStatus = 'on-hold'

        logger.info('Ozow payment pending investigation', {
          gateway: OZOW_GATEWAY,
          transactionId: data.TransactionId,
          reference: data.TransactionReference,
          orderId
        })
        break

      default:
        logger.warn('Unknown Ozow transaction status', {
          status: data.Status,
          orderId,
          transactionId: data.TransactionId
        })
        return
    }

    // Update order status in WooCommerce with retry logic
    retryCount = 0
    while (retryCount < maxRetries) {
      try {
        await wooCommerceClient.updateOrderStatus(orderIdNum, newStatus)
        break
      } catch (error) {
        retryCount++
        if (retryCount < maxRetries) {
          logger.warn(`Failed to update order status, retrying (${retryCount}/${maxRetries})`, {
            orderId,
            newStatus,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
        } else {
          throw error
        }
      }
    }

    const processingTime = Date.now() - startTime
    logger.info('WooCommerce order updated successfully', {
      orderId,
      newStatus,
      transactionId: data.TransactionId,
      gateway: OZOW_GATEWAY,
      processingTime
    })

  } catch (error) {
    const processingTime = Date.now() - startTime
    logger.error('Failed to update WooCommerce order', {
      orderId: orderId || data.Optional1,
      transactionId: data.TransactionId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      processingTime,
      maskedData: maskSensitiveData(data)
    })

    // Don't throw error to prevent webhook retry loops
    // The payment gateway will handle the transaction state
  }
}

// Handle GET requests for webhook validation/health check
export async function GET(_request: NextRequest) {
  return NextResponse.json({
    status: 'OK',
    service: 'ozow-webhook',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  })
}
