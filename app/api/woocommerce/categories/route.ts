import { NextRequest, NextResponse } from 'next/server'
import { wooCommerceClient } from '@/lib/wordpress'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Extract query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const per_page = parseInt(searchParams.get('per_page') || '100')
    const parent = searchParams.get('parent') ? parseInt(searchParams.get('parent')!) : undefined
    const hide_empty = searchParams.get('hide_empty') !== 'false' // Default to true

    // Fetch categories from WooCommerce
    const wcCategories = await wooCommerceClient.getCategories({
      page,
      per_page,
      parent,
      hide_empty,
    })

    // Convert to internal format
    const categories = wcCategories.map(category => ({
      id: category.id.toString(),
      name: category.name,
      slug: category.slug,
      description: category.description || '',
      image: category.image?.src || '',
      parent: category.parent || 0,
      count: category.count || 0,
    }))

    return NextResponse.json({
      success: true,
      data: categories,
      pagination: {
        page,
        per_page,
        total: categories.length,
      },
    })

  } catch (error) {
    console.error('Error fetching categories:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch categories',
      },
      { status: 500 }
    )
  }
}
