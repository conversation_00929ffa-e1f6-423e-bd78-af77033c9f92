import { NextRequest, NextResponse } from 'next/server'
import { wooCommerceClient, wooCommerceUtils } from '@/lib/wordpress'

interface RouteParams {
  params: {
    id: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    
    // Check if ID is numeric (WooCommerce product ID) or slug
    const isNumeric = /^\d+$/.test(id)
    
    let wcProduct
    
    if (isNumeric) {
      // Fetch by ID
      wcProduct = await wooCommerceClient.getProduct(parseInt(id))
    } else {
      // Fetch by slug
      wcProduct = await wooCommerceClient.getProductBySlug(id)
      
      if (!wcProduct) {
        return NextResponse.json(
          {
            success: false,
            error: 'Product not found',
          },
          { status: 404 }
        )
      }
    }

    // Convert to internal format
    const baseProduct = wooCommerceUtils.convertToInternalProduct(wcProduct)
    const { colors, sizes } = wooCommerceUtils.extractProductAttributes(wcProduct)
    
    const product = {
      ...baseProduct,
      colors,
      sizes,
      // Add additional fields
      rating: parseFloat(wcProduct.average_rating) || 0,
      reviewCount: wcProduct.rating_count || 0,
      tags: wcProduct.tags.map(tag => tag.name),
      categories: wcProduct.categories.map(cat => ({
        id: cat.id,
        name: cat.name,
        slug: cat.slug,
      })),
      variations: wcProduct.variations || [],
      attributes: wcProduct.attributes,
      meta_data: wcProduct.meta_data,
      // Additional WooCommerce specific data
      woocommerce: {
        id: wcProduct.id,
        sku: wcProduct.sku,
        weight: wcProduct.weight,
        dimensions: wcProduct.dimensions,
        shipping_required: wcProduct.shipping_required,
        tax_status: wcProduct.tax_status,
        tax_class: wcProduct.tax_class,
        manage_stock: wcProduct.manage_stock,
        stock_quantity: wcProduct.stock_quantity,
        backorders: wcProduct.backorders,
        sold_individually: wcProduct.sold_individually,
        purchase_note: wcProduct.purchase_note,
        related_ids: wcProduct.related_ids,
        upsell_ids: wcProduct.upsell_ids,
        cross_sell_ids: wcProduct.cross_sell_ids,
      },
    }

    // Fetch related products if available
    let relatedProducts = []
    if (wcProduct.related_ids.length > 0) {
      try {
        const wcRelatedProducts = await wooCommerceClient.getRelatedProducts(wcProduct.id, 4)
        relatedProducts = wcRelatedProducts.map(relatedProduct => 
          wooCommerceUtils.convertToInternalProduct(relatedProduct)
        )
      } catch (error) {
        console.warn('Failed to fetch related products:', error)
      }
    }

    // Fetch product variations if it's a variable product
    let variations = []
    if (wcProduct.type === 'variable' && wcProduct.variations.length > 0) {
      try {
        variations = await wooCommerceClient.getProductVariations(wcProduct.id)
      } catch (error) {
        console.warn('Failed to fetch product variations:', error)
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        product,
        relatedProducts,
        variations,
      },
    })

  } catch (error) {
    console.error('Error fetching product:', error)
    
    const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch product',
      },
      { status: statusCode }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    // This would be for updating products (admin functionality)
    // For now, return method not allowed
    return NextResponse.json(
      {
        success: false,
        error: 'Product update not implemented',
      },
      { status: 501 }
    )
  } catch (error) {
    console.error('Error updating product:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update product',
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // This would be for deleting products (admin functionality)
    // For now, return method not allowed
    return NextResponse.json(
      {
        success: false,
        error: 'Product deletion not implemented',
      },
      { status: 501 }
    )
  } catch (error) {
    console.error('Error deleting product:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete product',
      },
      { status: 500 }
    )
  }
}
