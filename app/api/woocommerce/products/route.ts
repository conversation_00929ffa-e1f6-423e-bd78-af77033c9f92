import { NextRequest, NextResponse } from 'next/server'
import { wooCommerceClient, wooCommerceUtils } from '@/lib/wordpress'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Extract query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const per_page = parseInt(searchParams.get('per_page') || '20')
    const search = searchParams.get('search') || undefined
    const category = searchParams.get('category') || undefined
    const tag = searchParams.get('tag') || undefined
    const featured = searchParams.get('featured') === 'true' ? true : undefined
    const on_sale = searchParams.get('on_sale') === 'true' ? true : undefined
    const min_price = searchParams.get('min_price') ? parseFloat(searchParams.get('min_price')!) : undefined
    const max_price = searchParams.get('max_price') ? parseFloat(searchParams.get('max_price')!) : undefined
    const stock_status = searchParams.get('stock_status') as 'instock' | 'outofstock' | 'onbackorder' | undefined
    const orderby = searchParams.get('orderby') as 'date' | 'id' | 'include' | 'title' | 'slug' | 'price' | 'popularity' | 'rating' | undefined
    const order = searchParams.get('order') as 'asc' | 'desc' | undefined

    // Fetch products from WooCommerce
    const wcProducts = await wooCommerceClient.getProducts({
      page,
      per_page,
      search,
      category,
      tag,
      featured,
      on_sale,
      min_price,
      max_price,
      stock_status,
      orderby,
      order,
    })

    // Convert to internal format
    const products = wcProducts.map(wcProduct => {
      const baseProduct = wooCommerceUtils.convertToInternalProduct(wcProduct)
      const { colors, sizes } = wooCommerceUtils.extractProductAttributes(wcProduct)
      
      return {
        ...baseProduct,
        colors,
        sizes,
        // Add additional fields for compatibility
        rating: parseFloat(wcProduct.average_rating) || 0,
        reviewCount: wcProduct.rating_count || 0,
        tags: wcProduct.tags.map(tag => tag.name),
        categories: wcProduct.categories.map(cat => ({
          id: cat.id,
          name: cat.name,
          slug: cat.slug,
        })),
        variations: wcProduct.variations || [],
        attributes: wcProduct.attributes,
        meta_data: wcProduct.meta_data,
      }
    })

    return NextResponse.json({
      success: true,
      data: products,
      pagination: {
        page,
        per_page,
        total: products.length, // Note: WooCommerce API doesn't return total in response headers by default
      },
    })

  } catch (error) {
    console.error('Error fetching products:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch products',
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // This would be for creating products (admin functionality)
    // For now, return method not allowed
    return NextResponse.json(
      {
        success: false,
        error: 'Product creation not implemented',
      },
      { status: 501 }
    )
  } catch (error) {
    console.error('Error creating product:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create product',
      },
      { status: 500 }
    )
  }
}
