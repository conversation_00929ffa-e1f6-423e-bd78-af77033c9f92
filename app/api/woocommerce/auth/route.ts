import { NextRequest, NextResponse } from 'next/server'
import { wordpressAuth, wooCommerceClient, authUtils } from '@/lib/wordpress'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, username, password, email, first_name, last_name } = body

    switch (action) {
      case 'login':
        return await handleLogin(username || email, password)
      
      case 'register':
        return await handleRegister({ username, email, password, first_name, last_name })
      
      case 'validate':
        return await handleValidateToken(request)
      
      case 'logout':
        return await handleLogout(request)
      
      case 'refresh':
        return await handleRefreshToken(request)
      
      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid action. Supported actions: login, register, validate, logout, refresh',
          },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Error in auth API:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication error',
      },
      { status: 500 }
    )
  }
}

async function handleLogin(usernameOrEmail: string, password: string) {
  if (!usernameOrEmail || !password) {
    return NextResponse.json(
      {
        success: false,
        error: 'Username/email and password are required',
      },
      { status: 400 }
    )
  }

  try {
    // Authenticate with WordPress
    const authResponse = await wordpressAuth.login(usernameOrEmail, password)
    
    // Get user information
    const user = await wordpressAuth.getCurrentUser(authResponse.token)
    
    // Get customer information from WooCommerce
    let customer = null
    try {
      customer = await wooCommerceClient.getCustomerByEmail(user.email)
    } catch (error) {
      console.warn('Customer not found in WooCommerce:', error)
    }

    return NextResponse.json({
      success: true,
      data: {
        token: authResponse.token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          display_name: user.name,
          roles: user.roles,
          avatar_url: user.avatar_urls['96'] || '',
        },
        customer: customer ? {
          id: customer.id,
          billing: customer.billing,
          shipping: customer.shipping,
          is_paying_customer: customer.is_paying_customer,
        } : null,
      },
      message: 'Login successful',
    })

  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Invalid credentials',
      },
      { status: 401 }
    )
  }
}

async function handleRegister(userData: {
  username?: string
  email: string
  password: string
  first_name?: string
  last_name?: string
}) {
  const { email, password, first_name, last_name } = userData

  if (!email || !password) {
    return NextResponse.json(
      {
        success: false,
        error: 'Email and password are required',
      },
      { status: 400 }
    )
  }

  // Validate email format
  if (!authUtils.isValidEmail(email)) {
    return NextResponse.json(
      {
        success: false,
        error: 'Invalid email format',
      },
      { status: 400 }
    )
  }

  // Validate password strength
  const passwordValidation = authUtils.isValidPassword(password)
  if (!passwordValidation.isValid) {
    return NextResponse.json(
      {
        success: false,
        error: 'Password validation failed',
        details: passwordValidation.errors,
      },
      { status: 400 }
    )
  }

  try {
    // Generate username if not provided
    const username = userData.username || authUtils.generateUsernameFromEmail(email)

    // Create WordPress user
    const user = await wordpressAuth.createUser({
      username,
      email,
      password,
      first_name: first_name || '',
      last_name: last_name || '',
    })

    // Create WooCommerce customer
    let customer = null
    try {
      customer = await wooCommerceClient.createCustomer({
        email,
        first_name: first_name || '',
        last_name: last_name || '',
        username,
      })
    } catch (error) {
      console.warn('Failed to create WooCommerce customer:', error)
    }

    // Login the user to get token
    const authResponse = await wordpressAuth.login(username, password)

    return NextResponse.json({
      success: true,
      data: {
        token: authResponse.token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          display_name: user.name,
          roles: user.roles,
        },
        customer: customer ? {
          id: customer.id,
          billing: customer.billing,
          shipping: customer.shipping,
        } : null,
      },
      message: 'Account created successfully',
    })

  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create account',
      },
      { status: 500 }
    )
  }
}

async function handleValidateToken(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  
  if (!authHeader) {
    return NextResponse.json(
      {
        success: false,
        error: 'Authorization header required',
      },
      { status: 401 }
    )
  }

  const token = authUtils.extractTokenFromHeader(authHeader)
  
  if (!token) {
    return NextResponse.json(
      {
        success: false,
        error: 'Invalid authorization header format',
      },
      { status: 401 }
    )
  }

  try {
    const isValid = await wordpressAuth.validateToken(token)
    
    if (!isValid) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid or expired token',
        },
        { status: 401 }
      )
    }

    // Get current user
    const user = await wordpressAuth.getCurrentUser(token)

    return NextResponse.json({
      success: true,
      data: {
        valid: true,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          display_name: user.name,
          roles: user.roles,
        },
      },
    })

  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: 'Token validation failed',
      },
      { status: 401 }
    )
  }
}

async function handleLogout(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  
  if (!authHeader) {
    return NextResponse.json({
      success: true,
      message: 'Logout successful',
    })
  }

  const token = authUtils.extractTokenFromHeader(authHeader)
  
  if (token) {
    try {
      await wordpressAuth.logout(token)
    } catch (error) {
      console.warn('Logout error:', error)
    }
  }

  return NextResponse.json({
    success: true,
    message: 'Logout successful',
  })
}

async function handleRefreshToken(request: NextRequest) {
  // WordPress JWT Auth plugin doesn't support refresh tokens by default
  // This would need to be implemented with a custom solution
  return NextResponse.json(
    {
      success: false,
      error: 'Token refresh not implemented',
    },
    { status: 501 }
  )
}
