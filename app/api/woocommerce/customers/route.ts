import { NextRequest, NextResponse } from 'next/server'
import { wooCommerceClient, wordpressAuth, authUtils } from '@/lib/wordpress'

export async function GET(request: NextRequest) {
  try {
    // Check for authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
        },
        { status: 401 }
      )
    }

    // Extract and validate token
    const token = authHeader.replace('Bearer ', '')
    const isValidToken = await wordpressAuth.validateToken(token)
    
    if (!isValidToken) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid or expired token',
        },
        { status: 401 }
      )
    }

    // Get current user
    const currentUser = await wordpressAuth.getCurrentUser(token)
    
    // Get customer data from WooCommerce
    const customer = await wooCommerceClient.getCustomerByEmail(currentUser.email)
    
    if (!customer) {
      return NextResponse.json(
        {
          success: false,
          error: 'Customer not found',
        },
        { status: 404 }
      )
    }

    // Return customer data (excluding sensitive information)
    const customerData = {
      id: customer.id,
      email: customer.email,
      first_name: customer.first_name,
      last_name: customer.last_name,
      username: customer.username,
      billing: customer.billing,
      shipping: customer.shipping,
      is_paying_customer: customer.is_paying_customer,
      avatar_url: customer.avatar_url,
      date_created: customer.date_created,
      date_modified: customer.date_modified,
    }

    return NextResponse.json({
      success: true,
      data: customerData,
    })

  } catch (error) {
    console.error('Error fetching customer:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch customer',
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const { email, first_name, last_name, password } = body
    
    if (!email || !first_name || !last_name || !password) {
      return NextResponse.json(
        {
          success: false,
          error: 'Email, first name, last name, and password are required',
        },
        { status: 400 }
      )
    }

    // Validate email format
    if (!authUtils.isValidEmail(email)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email format',
        },
        { status: 400 }
      )
    }

    // Validate password strength
    const passwordValidation = authUtils.isValidPassword(password)
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        {
          success: false,
          error: 'Password validation failed',
          details: passwordValidation.errors,
        },
        { status: 400 }
      )
    }

    // Check if customer already exists
    const existingCustomer = await wooCommerceClient.getCustomerByEmail(email)
    if (existingCustomer) {
      return NextResponse.json(
        {
          success: false,
          error: 'Customer with this email already exists',
        },
        { status: 409 }
      )
    }

    // Generate username from email if not provided
    const username = body.username || authUtils.generateUsernameFromEmail(email)

    // Create customer data
    const customerData = {
      email,
      first_name,
      last_name,
      username,
      password,
      billing: body.billing || {},
      shipping: body.shipping || {},
    }

    // Create customer in WooCommerce
    const customer = await wooCommerceClient.createCustomer(customerData)

    // Also create WordPress user for authentication
    try {
      await wordpressAuth.createUser({
        username,
        email,
        password,
        first_name,
        last_name,
      })
    } catch (error) {
      console.warn('Failed to create WordPress user:', error)
      // Continue even if WordPress user creation fails
    }

    // Return customer data (excluding sensitive information)
    const responseData = {
      id: customer.id,
      email: customer.email,
      first_name: customer.first_name,
      last_name: customer.last_name,
      username: customer.username,
      billing: customer.billing,
      shipping: customer.shipping,
      date_created: customer.date_created,
    }

    return NextResponse.json({
      success: true,
      data: responseData,
      message: 'Customer account created successfully',
    })

  } catch (error) {
    console.error('Error creating customer:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create customer account',
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Check for authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
        },
        { status: 401 }
      )
    }

    // Extract and validate token
    const token = authHeader.replace('Bearer ', '')
    const isValidToken = await wordpressAuth.validateToken(token)
    
    if (!isValidToken) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid or expired token',
        },
        { status: 401 }
      )
    }

    // Get current user
    const currentUser = await wordpressAuth.getCurrentUser(token)
    
    // Get customer data from WooCommerce
    const customer = await wooCommerceClient.getCustomerByEmail(currentUser.email)
    
    if (!customer) {
      return NextResponse.json(
        {
          success: false,
          error: 'Customer not found',
        },
        { status: 404 }
      )
    }

    const body = await request.json()
    
    // Validate email if provided
    if (body.email && !authUtils.isValidEmail(body.email)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email format',
        },
        { status: 400 }
      )
    }

    // Update customer in WooCommerce
    const updatedCustomer = await wooCommerceClient.updateCustomer(customer.id, body)

    // Return updated customer data (excluding sensitive information)
    const responseData = {
      id: updatedCustomer.id,
      email: updatedCustomer.email,
      first_name: updatedCustomer.first_name,
      last_name: updatedCustomer.last_name,
      username: updatedCustomer.username,
      billing: updatedCustomer.billing,
      shipping: updatedCustomer.shipping,
      date_modified: updatedCustomer.date_modified,
    }

    return NextResponse.json({
      success: true,
      data: responseData,
      message: 'Customer information updated successfully',
    })

  } catch (error) {
    console.error('Error updating customer:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update customer information',
      },
      { status: 500 }
    )
  }
}
