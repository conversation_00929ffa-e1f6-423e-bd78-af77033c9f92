import { NextRequest, NextResponse } from 'next/server'
import { wooCommerceClient, wordpressAuth } from '@/lib/wordpress'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Check for authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
        },
        { status: 401 }
      )
    }

    // Extract and validate token
    const token = authHeader.replace('Bearer ', '')
    const isValidToken = await wordpressAuth.validateToken(token)
    
    if (!isValidToken) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid or expired token',
        },
        { status: 401 }
      )
    }

    // Get current user
    const currentUser = await wordpressAuth.getCurrentUser(token)
    
    // Extract query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const per_page = parseInt(searchParams.get('per_page') || '10')
    const status = searchParams.get('status') || undefined
    const customer_id = searchParams.get('customer_id') ? parseInt(searchParams.get('customer_id')!) : undefined

    // If customer_id is not provided and user is not admin, use current user's customer ID
    let customerId = customer_id
    if (!customerId && !wordpressAuth.hasRole(currentUser, 'administrator')) {
      // Get customer by email
      const customer = await wooCommerceClient.getCustomerByEmail(currentUser.email)
      if (customer) {
        customerId = customer.id
      }
    }

    // Fetch orders
    let orders
    if (customerId) {
      orders = await wooCommerceClient.getCustomerOrders(customerId, {
        page,
        per_page,
        status,
      })
    } else if (wordpressAuth.hasRole(currentUser, 'administrator')) {
      // Admin can see all orders
      orders = [] // Would implement admin order fetching here
    } else {
      orders = []
    }

    return NextResponse.json({
      success: true,
      data: orders,
      pagination: {
        page,
        per_page,
        total: orders.length,
      },
    })

  } catch (error) {
    console.error('Error fetching orders:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch orders',
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const { line_items, billing } = body
    
    if (!line_items || !Array.isArray(line_items) || line_items.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Line items are required',
        },
        { status: 400 }
      )
    }

    if (!billing) {
      return NextResponse.json(
        {
          success: false,
          error: 'Billing information is required',
        },
        { status: 400 }
      )
    }

    // Check for authentication (optional for guest checkout)
    const authHeader = request.headers.get('authorization')
    let customerId: number | undefined

    if (authHeader) {
      const token = authHeader.replace('Bearer ', '')
      const isValidToken = await wordpressAuth.validateToken(token)
      
      if (isValidToken) {
        const currentUser = await wordpressAuth.getCurrentUser(token)
        const customer = await wooCommerceClient.getCustomerByEmail(currentUser.email)
        if (customer) {
          customerId = customer.id
        }
      }
    }

    // Validate line items and check stock
    for (const item of line_items) {
      if (!item.product_id || !item.quantity) {
        return NextResponse.json(
          {
            success: false,
            error: 'Each line item must have product_id and quantity',
          },
          { status: 400 }
        )
      }

      // Check if product exists and is in stock
      try {
        const product = await wooCommerceClient.getProduct(item.product_id)
        
        if (!wooCommerceClient.isProductInStock(product)) {
          return NextResponse.json(
            {
              success: false,
              error: `Product "${product.name}" is out of stock`,
            },
            { status: 400 }
          )
        }

        // Check stock quantity if managed
        if (product.manage_stock && product.stock_quantity !== null) {
          if (item.quantity > product.stock_quantity) {
            return NextResponse.json(
              {
                success: false,
                error: `Insufficient stock for "${product.name}". Available: ${product.stock_quantity}`,
              },
              { status: 400 }
            )
          }
        }
      } catch (error) {
        return NextResponse.json(
          {
            success: false,
            error: `Product with ID ${item.product_id} not found`,
          },
          { status: 400 }
        )
      }
    }

    // Create order data
    const orderData = {
      customer_id: customerId,
      billing,
      shipping: body.shipping || billing,
      line_items,
      shipping_lines: body.shipping_lines || [],
      payment_method: body.payment_method || '',
      payment_method_title: body.payment_method_title || '',
      customer_note: body.customer_note || '',
    }

    // Create order in WooCommerce
    const order = await wooCommerceClient.createOrder(orderData)

    return NextResponse.json({
      success: true,
      data: order,
      message: 'Order created successfully',
    })

  } catch (error) {
    console.error('Error creating order:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create order',
      },
      { status: 500 }
    )
  }
}
