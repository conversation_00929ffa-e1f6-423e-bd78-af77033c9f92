import { openai } from '@ai-sdk/openai'
import { streamText } from 'ai'
import { z } from 'zod'

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

// Define tools for page design operations
const updatePageTool = {
  description: 'Update the entire page structure and content',
  parameters: z.object({
    pageData: z.object({
      title: z.string().describe('Page title'),
      slug: z.string().describe('Page URL slug'),
      description: z.string().describe('Page description'),
      blocks: z.array(z.any()).describe('Array of page blocks'),
      settings: z.object({
        title: z.string(),
        description: z.string().optional(),
        theme: z.string().optional(),
        layout: z.string().optional(),
      }).describe('Page settings')
    }).describe('Complete page data structure')
  }),
  execute: async ({ pageData }: { pageData: any }) => {
    return {
      success: true,
      action: 'updatePage',
      pageData,
      message: 'Page structure updated successfully'
    }
  }
}

const addBlockTool = {
  description: 'Add a new block to the page',
  parameters: z.object({
    blockData: z.object({
      id: z.string().describe('Unique block ID'),
      type: z.string().describe('Block type (hero, text, image, product, etc.)'),
      content: z.any().describe('Block content and configuration'),
      style: z.object({}).optional().describe('Block styling options'),
      position: z.number().optional().describe('Block position in the page')
    }).describe('Block data to add')
  }),
  execute: async ({ blockData }: { blockData: any }) => {
    return {
      success: true,
      action: 'addBlock',
      blockData,
      message: `Added ${blockData.type} block successfully`
    }
  }
}

const updateBlockTool = {
  description: 'Update an existing block on the page',
  parameters: z.object({
    blockId: z.string().describe('ID of the block to update'),
    blockData: z.object({
      type: z.string().optional().describe('Block type'),
      content: z.any().optional().describe('Block content and configuration'),
      style: z.object({}).optional().describe('Block styling options')
    }).describe('Block data to update')
  }),
  execute: async ({ blockId, blockData }: { blockId: string; blockData: any }) => {
    return {
      success: true,
      action: 'updateBlock',
      blockId,
      blockData,
      message: `Updated block ${blockId} successfully`
    }
  }
}

const updatePageSettingsTool = {
  description: 'Update page-level settings like theme, layout, SEO, etc.',
  parameters: z.object({
    settings: z.object({
      title: z.string().optional().describe('Page title'),
      description: z.string().optional().describe('Page description'),
      theme: z.string().optional().describe('Page theme (light, dark, colorful, minimal, etc.)'),
      layout: z.string().optional().describe('Page layout type'),
      colors: z.object({
        primary: z.string().optional(),
        secondary: z.string().optional(),
        accent: z.string().optional()
      }).optional().describe('Color scheme'),
      typography: z.object({
        headingFont: z.string().optional(),
        bodyFont: z.string().optional(),
        fontSize: z.string().optional()
      }).optional().describe('Typography settings'),
      spacing: z.string().optional().describe('Page spacing (tight, normal, loose)'),
      animations: z.boolean().optional().describe('Enable/disable animations')
    }).describe('Page settings to update')
  }),
  execute: async ({ settings }: { settings: any }) => {
    return {
      success: true,
      action: 'updatePageSettings',
      settings,
      message: 'Page settings updated successfully'
    }
  }
}

export async function POST(req: Request) {
  try {
    const { messages, pageData } = await req.json()

    const result = streamText({
      model: openai('gpt-4o'),
      messages,
      system: `You are an expert AI Page Designer for a kids clothing e-commerce store called "Coco Milk Kids". You help create beautiful, engaging, and conversion-optimized pages.

BRAND CONTEXT:
- Target audience: Parents shopping for children's clothing (ages 0-12)
- Brand personality: Playful, colorful, safe, high-quality, South African
- Design style: Modern, clean, child-friendly with bright colors
- Currency: South African Rand (ZAR)

DESIGN PRINCIPLES:
1. Child-friendly aesthetics with bright, cheerful colors
2. Clear, easy navigation for busy parents
3. Mobile-first responsive design
4. Fast loading and accessible
5. Trust-building elements (reviews, security badges)
6. Clear call-to-actions for purchasing

AVAILABLE BLOCK TYPES:
- hero: Hero sections with images, text, and CTAs
- text: Text content blocks with rich formatting
- image: Image blocks with captions and styling
- product: Product showcase and grid blocks
- testimonial: Customer review and testimonial blocks
- cta: Call-to-action blocks
- gallery: Image galleries and carousels
- video: Video embed blocks
- contact: Contact forms and information
- newsletter: Email signup blocks
- social: Social media integration blocks

CURRENT PAGE DATA: ${JSON.stringify(pageData, null, 2)}

When designing:
1. Always consider the kids clothing context
2. Use appropriate colors (pastels, bright colors, but not overwhelming)
3. Include trust signals and social proof
4. Make CTAs clear and compelling for parents
5. Ensure mobile responsiveness
6. Add engaging content that appeals to both kids and parents
7. Include product-focused elements when relevant
8. Use South African context where appropriate

Respond conversationally and explain what you're creating or modifying. Use the tools to make actual changes to the page.`,
      tools: {
        updatePage: updatePageTool,
        addBlock: addBlockTool,
        updateBlock: updateBlockTool,
        updatePageSettings: updatePageSettingsTool,
      },
      maxSteps: 5,
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error('AI Page Designer error:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to process AI page design request' }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    )
  }
}
