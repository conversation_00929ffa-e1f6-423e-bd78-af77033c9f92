// WordPress-Style Individual Post API Routes
// RESTful API endpoints for managing individual posts

import { NextRequest, NextResponse } from 'next/server'
import { PostService } from '@/lib/posts/services/post-service'
import { UpdatePostInput } from '@/lib/posts/types'
import { validatePostData } from '@/lib/posts/utils'

const postService = new PostService()

/**
 * GET /api/posts/[id] - Get a single post by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const include = searchParams.get('include')?.split(',') || []
    
    const result = await postService.getPostById(params.id, include)
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Post not found' ? 404 : 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data
    })

  } catch (error) {
    console.error('Error in GET /api/posts/[id]:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/posts/[id] - Update a post
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    
    // Validate input data
    const validation = validatePostData({ ...body, id: params.id })
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validation.errors 
        },
        { status: 400 }
      )
    }

    const input: UpdatePostInput = {
      id: params.id,
      title: body.title,
      slug: body.slug,
      content: body.content,
      excerpt: body.excerpt,
      status: body.status,
      postType: body.postType,
      parentId: body.parentId,
      menuOrder: body.menuOrder,
      featuredImage: body.featuredImage,
      featuredImageAlt: body.featuredImageAlt,
      template: body.template,
      password: body.password,
      publishedAt: body.publishedAt ? new Date(body.publishedAt) : undefined,
      scheduledAt: body.scheduledAt ? new Date(body.scheduledAt) : undefined,
      authorId: body.authorId,
      authorName: body.authorName,
      authorEmail: body.authorEmail,
      seoTitle: body.seoTitle,
      seoDescription: body.seoDescription,
      seoKeywords: body.seoKeywords,
      ogImage: body.ogImage,
      ogTitle: body.ogTitle,
      ogDescription: body.ogDescription,
      twitterCard: body.twitterCard,
      canonicalUrl: body.canonicalUrl,
      metaRobots: body.metaRobots,
      allowComments: body.allowComments,
      allowPingbacks: body.allowPingbacks,
      isSticky: body.isSticky,
      isFeatured: body.isFeatured,
      usePageBuilder: body.usePageBuilder,
      pageBuilderData: body.pageBuilderData,
      customFields: body.customFields,
      metadata: body.metadata,
      taxonomyTerms: body.taxonomyTerms,
      blocks: body.blocks,
    }

    // Remove undefined values
    Object.keys(input).forEach(key => {
      if (input[key as keyof UpdatePostInput] === undefined) {
        delete input[key as keyof UpdatePostInput]
      }
    })

    const result = await postService.updatePost(input)
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Post not found' ? 404 : 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: result.message
    })

  } catch (error) {
    console.error('Error in PUT /api/posts/[id]:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/posts/[id] - Delete a post
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const force = searchParams.get('force') === 'true'
    
    const result = await postService.deletePost(params.id, force)
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Post not found' ? 404 : 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: result.message
    })

  } catch (error) {
    console.error('Error in DELETE /api/posts/[id]:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * PATCH /api/posts/[id] - Partial update of a post
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    
    // For PATCH, we only validate the fields that are being updated
    const fieldsToUpdate = Object.keys(body)
    const validationData = { ...body, id: params.id }
    
    // Only validate if required fields are being updated
    if (fieldsToUpdate.includes('title') || fieldsToUpdate.includes('postType')) {
      const validation = validatePostData(validationData)
      if (!validation.isValid) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Validation failed',
            details: validation.errors 
          },
          { status: 400 }
        )
      }
    }

    const input: UpdatePostInput = {
      id: params.id,
      ...body
    }

    // Convert date strings to Date objects
    if (body.publishedAt) {
      input.publishedAt = new Date(body.publishedAt)
    }
    if (body.scheduledAt) {
      input.scheduledAt = new Date(body.scheduledAt)
    }

    const result = await postService.updatePost(input)
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Post not found' ? 404 : 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: result.message
    })

  } catch (error) {
    console.error('Error in PATCH /api/posts/[id]:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
