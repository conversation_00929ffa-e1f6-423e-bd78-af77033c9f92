// Posts System Initialization API
// API endpoint to initialize the WordPress-style posts system

import { NextRequest, NextResponse } from 'next/server'
import { initializePostsSystem, initializePostTypes, initializeTaxonomies } from '@/lib/posts/init'

/**
 * POST /api/posts/init - Initialize the posts system
 */
export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'full'

    switch (action) {
      case 'full':
        // Initialize everything
        await initializePostsSystem()
        return NextResponse.json({
          success: true,
          message: 'Posts system initialized successfully with sample data'
        })

      case 'post-types':
        // Initialize only post types
        await initializePostTypes()
        return NextResponse.json({
          success: true,
          message: 'Post types initialized successfully'
        })

      case 'taxonomies':
        // Initialize only taxonomies
        await initializeTaxonomies()
        return NextResponse.json({
          success: true,
          message: 'Taxonomies initialized successfully'
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action parameter' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Error initializing posts system:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to initialize posts system',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/posts/init - Get initialization status
 */
export async function GET(request: NextRequest) {
  try {
    // Check if system is already initialized by checking for default post types
    const response = await fetch(`${request.nextUrl.origin}/api/post-types`)
    const data = await response.json()

    const hasDefaultPostTypes = data.success && data.data?.some((pt: any) => 
      ['post', 'page', 'product'].includes(pt.name)
    )

    return NextResponse.json({
      success: true,
      data: {
        isInitialized: hasDefaultPostTypes,
        message: hasDefaultPostTypes 
          ? 'Posts system is already initialized'
          : 'Posts system needs initialization'
      }
    })

  } catch (error) {
    console.error('Error checking initialization status:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to check initialization status' },
      { status: 500 }
    )
  }
}
