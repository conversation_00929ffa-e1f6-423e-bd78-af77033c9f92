// WordPress-Style Posts API Routes
// RESTful API endpoints for managing posts

import { NextRequest, NextResponse } from 'next/server'
import { PostService } from '@/lib/posts/services/post-service'
import { CreatePostInput, PostQueryParams } from '@/lib/posts/types'
import { validatePostData } from '@/lib/posts/utils'

const postService = new PostService()

/**
 * GET /api/posts - Query posts with filters and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const params: PostQueryParams = {
      postType: searchParams.get('postType') || undefined,
      status: searchParams.get('status') as any || 'published',
      authorId: searchParams.get('authorId') || undefined,
      parentId: searchParams.get('parentId') || undefined,
      search: searchParams.get('search') || undefined,
      orderBy: searchParams.get('orderBy') as any || 'createdAt',
      order: searchParams.get('order') as any || 'desc',
      limit: parseInt(searchParams.get('limit') || '10'),
      offset: parseInt(searchParams.get('offset') || '0'),
      include: searchParams.get('include')?.split(',') as any || [],
    }

    // Parse taxonomy filters
    const taxonomyParams: Record<string, string[]> = {}
    for (const [key, value] of searchParams.entries()) {
      if (key.startsWith('taxonomy_')) {
        const taxonomyName = key.replace('taxonomy_', '')
        taxonomyParams[taxonomyName] = value.split(',')
      }
    }
    if (Object.keys(taxonomyParams).length > 0) {
      params.taxonomies = taxonomyParams
    }

    // Parse meta filters
    const metaParams: Record<string, any> = {}
    for (const [key, value] of searchParams.entries()) {
      if (key.startsWith('meta_')) {
        const metaKey = key.replace('meta_', '')
        metaParams[metaKey] = value
      }
    }
    if (Object.keys(metaParams).length > 0) {
      params.meta = metaParams
    }

    const result = await postService.queryPosts(params)
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: {
        total: result.data?.total,
        page: result.data?.page,
        limit: result.data?.limit,
        totalPages: result.data?.totalPages,
        hasNext: result.data?.hasNext,
        hasPrev: result.data?.hasPrev,
      }
    })

  } catch (error) {
    console.error('Error in GET /api/posts:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/posts - Create a new post
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input data
    const validation = validatePostData(body)
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validation.errors 
        },
        { status: 400 }
      )
    }

    const input: CreatePostInput = {
      title: body.title,
      slug: body.slug,
      content: body.content,
      excerpt: body.excerpt,
      status: body.status || 'draft',
      postType: body.postType,
      parentId: body.parentId,
      menuOrder: body.menuOrder,
      featuredImage: body.featuredImage,
      featuredImageAlt: body.featuredImageAlt,
      template: body.template,
      password: body.password,
      publishedAt: body.publishedAt ? new Date(body.publishedAt) : undefined,
      scheduledAt: body.scheduledAt ? new Date(body.scheduledAt) : undefined,
      authorId: body.authorId,
      authorName: body.authorName,
      authorEmail: body.authorEmail,
      seoTitle: body.seoTitle,
      seoDescription: body.seoDescription,
      seoKeywords: body.seoKeywords || [],
      ogImage: body.ogImage,
      ogTitle: body.ogTitle,
      ogDescription: body.ogDescription,
      twitterCard: body.twitterCard,
      canonicalUrl: body.canonicalUrl,
      metaRobots: body.metaRobots,
      allowComments: body.allowComments,
      allowPingbacks: body.allowPingbacks,
      isSticky: body.isSticky,
      isFeatured: body.isFeatured,
      usePageBuilder: body.usePageBuilder,
      pageBuilderData: body.pageBuilderData,
      customFields: body.customFields,
      metadata: body.metadata,
      taxonomyTerms: body.taxonomyTerms,
      blocks: body.blocks,
    }

    const result = await postService.createPost(input)
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: result.message
    }, { status: 201 })

  } catch (error) {
    console.error('Error in POST /api/posts:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/posts - Bulk update posts
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, postIds, updates } = body

    if (!action || !postIds || !Array.isArray(postIds)) {
      return NextResponse.json(
        { success: false, error: 'Invalid bulk action request' },
        { status: 400 }
      )
    }

    // Handle different bulk actions
    switch (action) {
      case 'publish':
        // Bulk publish posts
        // Implementation would go here
        break
      case 'unpublish':
        // Bulk unpublish posts
        // Implementation would go here
        break
      case 'trash':
        // Bulk move to trash
        // Implementation would go here
        break
      case 'delete':
        // Bulk delete posts
        // Implementation would go here
        break
      case 'update':
        // Bulk update with custom data
        // Implementation would go here
        break
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid bulk action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: `Bulk ${action} completed successfully`,
      affected: postIds.length
    })

  } catch (error) {
    console.error('Error in PUT /api/posts:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/posts - Bulk delete posts
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const postIds = searchParams.get('ids')?.split(',') || []
    const force = searchParams.get('force') === 'true'

    if (postIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No post IDs provided' },
        { status: 400 }
      )
    }

    // Implementation for bulk delete would go here
    // If force=true, permanently delete
    // Otherwise, move to trash

    return NextResponse.json({
      success: true,
      message: `${postIds.length} posts ${force ? 'deleted' : 'moved to trash'} successfully`,
      affected: postIds.length
    })

  } catch (error) {
    console.error('Error in DELETE /api/posts:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
