import { generateText } from "ai"
import { models, systemPrompts } from "@/lib/ai-config"
import { NextResponse } from "next/server"

export async function POST(req: Request) {
  try {
    const { query } = await req.json()

    const prompt = `Convert the following natural language search query into structured search parameters using SoImagine AI:
    "${query}"

    Extract and return a JSON object with these fields (if present in the query):
    - productType (e.g., t-shirt, dress, pants)
    - colors (array of colors)
    - sizes (array of sizes)
    - priceRange (object with min and max)
    - occasion (e.g., casual, formal, school)
    - season (e.g., summer, winter)
    - ageGroup (e.g., toddler, preschool, school-age)

    Only include fields that are explicitly or implicitly mentioned in the query.`

    const { text } = await generateText({
      model: models.gpt3,
      system: systemPrompts.searchEnhancer,
      prompt,
    })

    // Parse the JSON response
    let searchParams
    try {
      searchParams = JSON.parse(text)
    } catch (e) {
      // If parsing fails, return the raw text
      searchParams = { rawResponse: text }
    }

    return NextResponse.json({ searchParams })
  } catch (error) {
    console.error("Smart search error:", error)
    return NextResponse.json({ error: "Failed to process search query" }, { status: 500 })
  }
}
