import { openai } from "@ai-sdk/openai"
import { streamText, tool } from "ai"
import { NextResponse } from "next/server"
import { z } from "zod"

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

export async function POST(req: Request) {
  try {
    const { childInfo } = await req.json()

    const system = `You are a size recommendation assistant for Coco Milk Kids clothing store, powered by SoImagine AI.
    Help parents find the right size for their children based on age, height, weight, and body type.
    Be precise and helpful, explaining why you recommend a particular size.
    Our size ranges are: XS (2-3 years), S (4-5 years), M (6-7 years), L (8-9 years), XL (10-12 years).`

    const prompt = `Based on the following information about a child, recommend the best size from our collection:
    Age: ${childInfo.age}
    Height: ${childInfo.height} cm
    Weight: ${childInfo.weight} kg
    Body Type: ${childInfo.bodyType || "Average"}

    Please provide a size recommendation (XS, S, M, L, XL) with a brief explanation.`

    const result = streamText({
      model: openai("gpt-4o"),
      system,
      prompt,
      tools: {
        getSizeChart: tool({
          description: "Get the size chart for children's clothing",
          parameters: z.object({}),
          execute: async () => {
            return {
              sizes: [
                { size: "XS", ageRange: "2-3 years", heightRange: "85-95 cm", weightRange: "12-15 kg" },
                { size: "S", ageRange: "4-5 years", heightRange: "95-110 cm", weightRange: "15-19 kg" },
                { size: "M", ageRange: "6-7 years", heightRange: "110-125 cm", weightRange: "19-25 kg" },
                { size: "L", ageRange: "8-9 years", heightRange: "125-140 cm", weightRange: "25-35 kg" },
                { size: "XL", ageRange: "10-12 years", heightRange: "140-155 cm", weightRange: "35-45 kg" },
              ]
            }
          }
        }),
        calculateRecommendedSize: tool({
          description: "Calculate the recommended size based on child's measurements",
          parameters: z.object({
            age: z.number().describe("Child's age in years"),
            height: z.number().describe("Child's height in cm"),
            weight: z.number().describe("Child's weight in kg"),
            bodyType: z.string().describe("Child's body type (Slim, Average, Solid)"),
          }),
          execute: async ({ age, height, weight, bodyType }) => {
            // Simple size calculation logic
            let sizeByAge = "";
            let sizeByHeight = "";
            let sizeByWeight = "";

            // Determine size by age
            if (age >= 2 && age <= 3) sizeByAge = "XS";
            else if (age >= 4 && age <= 5) sizeByAge = "S";
            else if (age >= 6 && age <= 7) sizeByAge = "M";
            else if (age >= 8 && age <= 9) sizeByAge = "L";
            else if (age >= 10 && age <= 12) sizeByAge = "XL";

            // Determine size by height
            if (height >= 85 && height < 95) sizeByHeight = "XS";
            else if (height >= 95 && height < 110) sizeByHeight = "S";
            else if (height >= 110 && height < 125) sizeByHeight = "M";
            else if (height >= 125 && height < 140) sizeByHeight = "L";
            else if (height >= 140 && height <= 155) sizeByHeight = "XL";

            // Determine size by weight
            if (weight >= 12 && weight < 15) sizeByWeight = "XS";
            else if (weight >= 15 && weight < 19) sizeByWeight = "S";
            else if (weight >= 19 && weight < 25) sizeByWeight = "M";
            else if (weight >= 25 && weight < 35) sizeByWeight = "L";
            else if (weight >= 35 && weight <= 45) sizeByWeight = "XL";

            // Adjust based on body type
            let adjustment = 0;
            if (bodyType === "Slim") adjustment = -1;
            else if (bodyType === "Solid") adjustment = 1;

            return {
              sizeByAge,
              sizeByHeight,
              sizeByWeight,
              bodyType,
              adjustment
            }
          }
        })
      }
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error("Size assistant error:", error)
    return NextResponse.json({ error: "Failed to generate size recommendation" }, { status: 500 })
  }
}
