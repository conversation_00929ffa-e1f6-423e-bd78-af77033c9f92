"use client"

import { useState, useEffect } from "react"
import { ProductCard } from "@/components/storefront/products/product-card"
// import { ProductFilters } from "@/components/product-filters"
import { ProductSort } from "@/components/storefront/products/product-sort"
import { Skeleton } from "@/components/ui/skeleton"
import { getProducts, type Product } from "@/lib/products"
import { Sun, Waves } from "lucide-react"

export default function SummerCollectionPage() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [sortBy, setSortBy] = useState("featured")
  const [filters, setFilters] = useState({
    category: "",
    color: "",
    size: "",
  })

  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true)
      try {
        const allProducts = await getProducts({ sort: sortBy, ...filters })
        // Filter for summer-appropriate products
        const summerProducts = allProducts.filter(product => 
          product.name.toLowerCase().includes('dress') ||
          product.name.toLowerCase().includes('shorts') ||
          product.name.toLowerCase().includes('t-shirt')
        )
        setProducts(summerProducts.length > 0 ? summerProducts : allProducts)
      } catch (error) {
        console.error("Failed to fetch products:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [sortBy, filters])

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters)
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <Sun className="h-6 w-6 text-yellow-500" />
          <h1 className="text-2xl font-normal">Summer Collection</h1>
        </div>
        <p className="text-gray-600 text-sm mb-4">
          Light, breathable clothing perfect for South African summers
        </p>
        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Waves className="h-4 w-4 text-blue-500" />
            <span className="font-medium text-sm">Summer Ready</span>
          </div>
          <p className="text-sm text-gray-700">
            From Cape Town beaches to Johannesburg playgrounds - our summer collection 
            keeps your little ones cool and comfortable all season long.
          </p>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Filters Sidebar */}
        <div className="lg:w-64 flex-shrink-0">
          {/* <ProductFilters onFilterChange={handleFilterChange} /> */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Filters</h3>
              <p className="text-sm text-gray-500">Filters temporarily disabled during build optimization</p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {/* Sort and Results Count */}
          <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-100">
            <p className="text-sm text-gray-600">
              {loading ? "Loading..." : `${products.length} summer products`}
            </p>
            <ProductSort value={sortBy} onValueChange={setSortBy} />
          </div>

          {/* Products Grid */}
          {loading ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="space-y-3">
                  <Skeleton className="aspect-[3/4] w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              ))}
            </div>
          ) : products.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <Sun className="h-16 w-16 text-yellow-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No summer products found</h3>
              <p className="text-gray-600">
                Try adjusting your filters to see more products.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Summer Care Tips */}
      <div className="mt-16 bg-blue-50 rounded-lg p-8">
        <h2 className="text-xl font-medium mb-4">South African Summer Care Tips</h2>
        <div className="grid md:grid-cols-3 gap-6 text-sm text-gray-700">
          <div>
            <h3 className="font-medium mb-2 flex items-center gap-2">
              <Sun className="h-4 w-4 text-yellow-500" />
              Sun Protection
            </h3>
            <p>
              Our summer fabrics offer UPF protection while keeping your little ones cool. 
              Perfect for those hot Gauteng afternoons or Western Cape beach days.
            </p>
          </div>
          <div>
            <h3 className="font-medium mb-2">Breathable Fabrics</h3>
            <p>
              Made from lightweight, moisture-wicking materials that handle South Africa's 
              summer humidity while maintaining comfort during active play.
            </p>
          </div>
          <div>
            <h3 className="font-medium mb-2">Easy Care</h3>
            <p>
              Machine washable and quick-dry fabrics mean less time doing laundry 
              and more time enjoying the South African summer.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
