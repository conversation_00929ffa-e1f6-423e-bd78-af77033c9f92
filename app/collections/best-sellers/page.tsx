"use client"

import { useState, useEffect } from "react"
import { ProductCard } from "@/components/storefront/products/product-card"
import { ProductFilters } from "@/components/storefront/products/product-filters"
import { ProductSort } from "@/components/storefront/products/product-sort"
import { Skeleton } from "@/components/ui/skeleton"
import { getProducts, type Product } from "@/lib/products"
import { TrendingUp } from "lucide-react"

export default function BestSellersPage() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [sortBy, setSortBy] = useState("featured")
  const [filters, setFilters] = useState({
    category: "",
    color: "",
    size: "",
  })

  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true)
      try {
        const allProducts = await getProducts({ sort: sortBy, ...filters })
        // For demo purposes, we'll show all products as "best sellers"
        // In a real app, you'd have a bestSeller flag or sales data
        setProducts(allProducts)
      } catch (error) {
        console.error("Failed to fetch products:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [sortBy, filters])

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters)
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-2">
          <TrendingUp className="h-5 w-5 text-[#012169]" />
          <h1 className="text-2xl md:text-3xl font-bold font-montserrat">Best Sellers</h1>
        </div>
        <p className="text-muted-foreground font-light">
          Our most popular children's clothing - loved by families everywhere
        </p>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Filters Sidebar */}
        <div className="lg:w-64 flex-shrink-0">
          <ProductFilters onFilterChange={handleFilterChange} />
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {/* Sort and Results Count */}
          <div className="flex items-center justify-between mb-6">
            <p className="text-sm text-muted-foreground font-light">
              {loading ? "Loading..." : `${products.length} best selling products`}
            </p>
            <ProductSort value={sortBy} onValueChange={setSortBy} />
          </div>

          {/* Products Grid */}
          {loading ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="space-y-3">
                  <Skeleton className="aspect-[3/4] w-full rounded-none" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              ))}
            </div>
          ) : products.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <TrendingUp className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No products found</h3>
              <p className="text-muted-foreground">
                Try adjusting your filters to see more products.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
