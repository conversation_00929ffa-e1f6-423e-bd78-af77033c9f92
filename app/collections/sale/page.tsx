"use client"

import { useState, useEffect } from "react"
import { ProductCard } from "@/components/storefront/products/product-card"
import { ProductFilters } from "@/components/storefront/products/product-filters"
import { ProductSort } from "@/components/storefront/products/product-sort"
import { MobileFilters } from "@/components/mobile-filters"
import { Skeleton } from "@/components/ui/skeleton"
import { getProducts, type Product } from "@/lib/products"
import { Tag } from "lucide-react"

export default function SalePage() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [sortBy, setSortBy] = useState("featured")
  const [filters, setFilters] = useState({
    category: "",
    color: "",
    size: "",
  })

  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true)
      try {
        const allProducts = await getProducts({ sort: sortBy, ...filters })
        // Filter for sale items
        const saleProducts = allProducts.filter(product => product.isSale)
        setProducts(saleProducts)
      } catch (error) {
        console.error("Failed to fetch products:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [sortBy, filters])

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters)
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      {/* Clean header */}
      <div className="mb-8">
        <h1 className="text-2xl font-normal mb-2">Sale</h1>
        <p className="text-gray-600 text-sm">
          {products.length} products on sale
        </p>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Desktop Filters Sidebar - Hidden on Mobile */}
        <div className="hidden lg:block lg:w-64 flex-shrink-0">
          <ProductFilters onFilterChange={handleFilterChange} />
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {/* Sort and Results Count */}
          <div className="flex items-center justify-between mb-6">
            <p className="text-sm text-muted-foreground font-light">
              {loading ? "Loading..." : `${products.length} products on sale`}
            </p>
            <div className="flex items-center gap-2">
              {/* Mobile Filter Button */}
              <div className="lg:hidden">
                <MobileFilters
                  selectedCategory={filters.category}
                  selectedColor={filters.color}
                  selectedSize={filters.size}
                />
              </div>
              <ProductSort value={sortBy} onValueChange={setSortBy} />
            </div>
          </div>

          {/* Products Grid */}
          {loading ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="space-y-3">
                  <Skeleton className="aspect-[3/4] w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              ))}
            </div>
          ) : products.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No sale items found</h3>
              <p className="text-gray-600">
                Try adjusting your filters or check back soon for new deals.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
