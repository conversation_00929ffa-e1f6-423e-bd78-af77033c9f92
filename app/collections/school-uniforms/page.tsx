"use client"

import { useState, useEffect } from "react"
import { ProductCard } from "@/components/storefront/products/product-card"
import { ProductFilters } from "@/components/storefront/products/product-filters"
import { ProductSort } from "@/components/storefront/products/product-sort"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { getProducts, type Product } from "@/lib/products"
import { GraduationCap, Calendar, Phone } from "lucide-react"

export default function SchoolUniformsPage() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [sortBy, setSortBy] = useState("featured")
  const [filters, setFilters] = useState({
    category: "",
    color: "",
    size: "",
  })

  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true)
      try {
        const allProducts = await getProducts({ sort: sortBy, ...filters })
        // Filter for school-appropriate products
        setProducts(allProducts)
      } catch (error) {
        console.error("Failed to fetch products:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [sortBy, filters])

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters)
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <GraduationCap className="h-6 w-6 text-[#012169]" />
          <h1 className="text-2xl font-normal">School Uniforms</h1>
        </div>
        <p className="text-gray-600 text-sm mb-6">
          Quality school uniforms for South African schools - durable, comfortable, and affordable
        </p>

        {/* School Uniform Services */}
        <div className="grid md:grid-cols-3 gap-4 mb-8">
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="h-4 w-4 text-[#012169]" />
                <span className="font-medium text-sm">Back to School Special</span>
              </div>
              <p className="text-xs text-gray-600">
                15% off all school uniforms during January. Perfect for the new school year.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-sm">📏</span>
                <span className="font-medium text-sm">Custom Embroidery</span>
              </div>
              <p className="text-xs text-gray-600">
                Add your school logo or child's name. Available for most uniform items.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center gap-2 mb-2">
                <Phone className="h-4 w-4 text-[#012169]" />
                <span className="font-medium text-sm">Bulk Orders</span>
              </div>
              <p className="text-xs text-gray-600">
                Special pricing for schools and bulk orders. Contact us for quotes.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Filters Sidebar */}
        <div className="lg:w-64 flex-shrink-0">
          <ProductFilters onFilterChange={handleFilterChange} />
          
          {/* School Uniform Categories */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-sm">Uniform Categories</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="ghost" size="sm" className="w-full justify-start text-xs">
                School Shirts
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-xs">
                School Pants & Skirts
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-xs">
                School Dresses
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-xs">
                School Jerseys
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-xs">
                School Shoes
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-xs">
                School Accessories
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {/* Sort and Results Count */}
          <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-100">
            <p className="text-sm text-gray-600">
              {loading ? "Loading..." : `${products.length} uniform items`}
            </p>
            <ProductSort value={sortBy} onValueChange={setSortBy} />
          </div>

          {/* Products Grid */}
          {loading ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="space-y-3">
                  <Skeleton className="aspect-[3/4] w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              ))}
            </div>
          ) : products.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <GraduationCap className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No uniform items found</h3>
              <p className="text-gray-600">
                Try adjusting your filters to see more products.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* School Uniform Information */}
      <div className="mt-16 bg-gray-50 rounded-lg p-8">
        <h2 className="text-xl font-medium mb-6">School Uniform Services</h2>
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <h3 className="font-medium mb-3">Why Choose Our School Uniforms?</h3>
            <ul className="space-y-2 text-sm text-gray-700">
              <li>• Durable fabrics that withstand daily wear</li>
              <li>• Comfortable fits for active children</li>
              <li>• Easy-care, machine washable materials</li>
              <li>• Competitive pricing for South African families</li>
              <li>• Fast delivery across South Africa</li>
              <li>• Custom embroidery services available</li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium mb-3">Bulk Order Information</h3>
            <div className="space-y-3 text-sm text-gray-700">
              <p>
                We offer special pricing for schools, aftercare centers, and bulk orders. 
                Contact our team for custom quotes and embroidery options.
              </p>
              <div className="bg-white p-4 rounded border">
                <p className="font-medium mb-1">Contact for Bulk Orders:</p>
                <p>Email: <EMAIL></p>
                <p>Phone: +27 11 123 4567</p>
                <p className="text-xs text-gray-500 mt-2">
                  Minimum order quantities apply for bulk pricing
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
