"use client";

import { ErrorComponent } from "@/components/ui/error";

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <html>
      <body>
        <div className="min-h-screen flex items-center justify-center p-4 bg-slate-50 dark:bg-slate-950">
          <ErrorComponent 
            error={error} 
            reset={reset}
            title="Application Error"
            description="We're sorry, but something went wrong with the application."
            showHomeButton={true}
            showReportButton={true}
            showStackTrace={process.env.NODE_ENV === "development"}
          />
        </div>
      </body>
    </html>
  );
}