import { Logo } from "@/components/logo"

export default function BrandPage() {
  return (
    <div className="container py-12">
      <h1 className="text-3xl font-light mb-8 tracking-wide">Brand Assets</h1>

      <div className="grid gap-12">
        <section>
          <h2 className="text-xl font-light mb-6 tracking-wide">Standard Logo</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 p-8 border rounded-lg">
            <div className="flex flex-col items-center gap-4">
              <div className="bg-white p-8 rounded-lg shadow-sm">
                <Logo theme="dark" />
              </div>
              <span className="text-sm font-light">Dark</span>
            </div>
            <div className="flex flex-col items-center gap-4">
              <div className="bg-[#0D0D0D] p-8 rounded-lg shadow-sm">
                <Logo theme="light" />
              </div>
              <span className="text-sm font-light">Light</span>
            </div>
            <div className="flex flex-col items-center gap-4">
              <div className="bg-white p-8 rounded-lg shadow-sm">
                <Logo theme="color" />
              </div>
              <span className="text-sm font-light">Color</span>
            </div>
          </div>
        </section>

        <section>
          <h2 className="text-xl font-light mb-6 tracking-wide">Horizontal Logo</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 p-8 border rounded-lg">
            <div className="flex flex-col items-center gap-4">
              <div className="bg-white p-8 rounded-lg shadow-sm">
                <Logo variant="horizontal" theme="dark" />
              </div>
              <span className="text-sm font-light">Dark</span>
            </div>
            <div className="flex flex-col items-center gap-4">
              <div className="bg-[#0D0D0D] p-8 rounded-lg shadow-sm">
                <Logo variant="horizontal" theme="light" />
              </div>
              <span className="text-sm font-light">Light</span>
            </div>
            <div className="flex flex-col items-center gap-4">
              <div className="bg-white p-8 rounded-lg shadow-sm">
                <Logo variant="horizontal" theme="color" />
              </div>
              <span className="text-sm font-light">Color</span>
            </div>
          </div>
        </section>

        <section>
          <h2 className="text-xl font-light mb-6 tracking-wide">Square Logo</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 p-8 border rounded-lg">
            <div className="flex flex-col items-center gap-4">
              <div className="bg-white p-8 rounded-lg shadow-sm">
                <Logo variant="square" theme="dark" />
              </div>
              <span className="text-sm font-light">Dark</span>
            </div>
            <div className="flex flex-col items-center gap-4">
              <div className="bg-[#0D0D0D] p-8 rounded-lg shadow-sm">
                <Logo variant="square" theme="light" />
              </div>
              <span className="text-sm font-light">Light</span>
            </div>
            <div className="flex flex-col items-center gap-4">
              <div className="bg-white p-8 rounded-lg shadow-sm">
                <Logo variant="square" theme="color" />
              </div>
              <span className="text-sm font-light">Color</span>
            </div>
          </div>
        </section>

        <section>
          <h2 className="text-xl font-light mb-6 tracking-wide">Icon Only</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 p-8 border rounded-lg">
            <div className="flex flex-col items-center gap-4">
              <div className="bg-white p-8 rounded-lg shadow-sm">
                <Logo variant="icon-only" theme="dark" width={60} height={60} />
              </div>
              <span className="text-sm font-light">Dark</span>
            </div>
            <div className="flex flex-col items-center gap-4">
              <div className="bg-[#0D0D0D] p-8 rounded-lg shadow-sm">
                <Logo variant="icon-only" theme="light" width={60} height={60} />
              </div>
              <span className="text-sm font-light">Light</span>
            </div>
            <div className="flex flex-col items-center gap-4">
              <div className="bg-white p-8 rounded-lg shadow-sm">
                <Logo variant="icon-only" theme="color" width={60} height={60} />
              </div>
              <span className="text-sm font-light">Color</span>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}
