"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Package, Truck, CheckCircle, Clock, AlertCircle, Search } from "lucide-react"
import Link from "next/link"

interface OrderStatus {
  status: "processing" | "shipped" | "delivered" | "delayed" | "cancelled"
  orderNumber: string
  trackingNumber?: string
  carrier?: string
  estimatedDelivery?: string
  lastUpdated: string
  items: {
    id: string
    name: string
    quantity: number
    price: number
    image: string
  }[]
  shippingAddress: {
    name: string
    street: string
    city: string
    state: string
    zip: string
    country: string
  }
  timeline: {
    status: string
    date: string
    location?: string
    description: string
  }[]
}

export default function TrackOrderPage() {
  const [orderNumber, setOrderNumber] = useState("")
  const [email, setEmail] = useState("")
  const [orderStatus, setOrderStatus] = useState<OrderStatus | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const handleTrackOrder = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500))

    // Mock order data
    if (orderNumber === "CM12345" || orderNumber === "CM67890") {
      const mockOrder: OrderStatus = {
        status: orderNumber === "CM12345" ? "shipped" : "processing",
        orderNumber,
        trackingNumber: orderNumber === "CM12345" ? "TRK9876543210" : undefined,
        carrier: orderNumber === "CM12345" ? "FedEx" : undefined,
        estimatedDelivery: orderNumber === "CM12345" ? "May 28, 2025" : "June 2, 2025",
        lastUpdated: "May 23, 2025, 10:30 AM",
        items: [
          {
            id: "1",
            name: "Striped Cotton T-Shirt",
            quantity: 2,
            price: 24.99,
            image: "/placeholder.svg?height=80&width=80",
          },
          {
            id: "2",
            name: "Comfort Fit Jeans",
            quantity: 1,
            price: 39.99,
            image: "/placeholder.svg?height=80&width=80",
          },
        ],
        shippingAddress: {
          name: "John Doe",
          street: "123 Main St",
          city: "Anytown",
          state: "CA",
          zip: "12345",
          country: "United States",
        },
        timeline:
          orderNumber === "CM12345"
            ? [
                {
                  status: "Order Placed",
                  date: "May 20, 2025, 2:30 PM",
                  description: "Your order has been received and is being processed.",
                },
                {
                  status: "Payment Confirmed",
                  date: "May 20, 2025, 2:35 PM",
                  description: "Payment has been successfully processed.",
                },
                {
                  status: "Order Processed",
                  date: "May 21, 2025, 9:15 AM",
                  description: "Your order has been prepared and is ready for shipping.",
                },
                {
                  status: "Shipped",
                  date: "May 22, 2025, 11:45 AM",
                  location: "Distribution Center, Los Angeles, CA",
                  description: "Your order has been shipped via FedEx.",
                },
                {
                  status: "In Transit",
                  date: "May 23, 2025, 8:20 AM",
                  location: "Sorting Facility, San Francisco, CA",
                  description: "Your package is in transit to the delivery address.",
                },
              ]
            : [
                {
                  status: "Order Placed",
                  date: "May 22, 2025, 4:15 PM",
                  description: "Your order has been received and is being processed.",
                },
                {
                  status: "Payment Confirmed",
                  date: "May 22, 2025, 4:20 PM",
                  description: "Payment has been successfully processed.",
                },
                {
                  status: "Processing",
                  date: "May 23, 2025, 10:30 AM",
                  description: "Your order is being prepared for shipping.",
                },
              ],
      }
      setOrderStatus(mockOrder)
    } else {
      setError("Order not found. Please check your order number and email address.")
    }

    setIsLoading(false)
  }

  const getStatusIcon = (status: OrderStatus["status"]) => {
    switch (status) {
      case "processing":
        return <Clock className="h-6 w-6 text-blue-500" />
      case "shipped":
        return <Truck className="h-6 w-6 text-green-500" />
      case "delivered":
        return <CheckCircle className="h-6 w-6 text-green-600" />
      case "delayed":
        return <AlertCircle className="h-6 w-6 text-orange-500" />
      case "cancelled":
        return <AlertCircle className="h-6 w-6 text-red-500" />
      default:
        return <Package className="h-6 w-6 text-gray-500" />
    }
  }

  const getStatusText = (status: OrderStatus["status"]) => {
    switch (status) {
      case "processing":
        return "Processing"
      case "shipped":
        return "Shipped"
      case "delivered":
        return "Delivered"
      case "delayed":
        return "Delayed"
      case "cancelled":
        return "Cancelled"
      default:
        return "Unknown"
    }
  }

  const getStatusColor = (status: OrderStatus["status"]) => {
    switch (status) {
      case "processing":
        return "text-blue-700 bg-blue-50 border-blue-200"
      case "shipped":
        return "text-green-700 bg-green-50 border-green-200"
      case "delivered":
        return "text-green-800 bg-green-50 border-green-200"
      case "delayed":
        return "text-orange-700 bg-orange-50 border-orange-200"
      case "cancelled":
        return "text-red-700 bg-red-50 border-red-200"
      default:
        return "text-gray-700 bg-gray-50 border-gray-200"
    }
  }

  return (
    <div className="container px-4 md:px-6 py-8 md:py-12">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl md:text-3xl font-bold font-montserrat mb-6">Track Your Order</h1>

        <Tabs defaultValue="order-number" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="order-number">Track by Order Number</TabsTrigger>
            <TabsTrigger value="tracking-number">Track by Tracking Number</TabsTrigger>
          </TabsList>

          <TabsContent value="order-number">
            <Card>
              <CardHeader>
                <CardTitle>Enter Order Details</CardTitle>
                <CardDescription>
                  Please enter your order number and the email address used for the order.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleTrackOrder} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label htmlFor="order-number" className="text-sm font-medium">
                        Order Number
                      </label>
                      <Input
                        id="order-number"
                        placeholder="e.g., CM12345"
                        value={orderNumber}
                        onChange={(e) => setOrderNumber(e.target.value)}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="email" className="text-sm font-medium">
                        Email Address
                      </label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                      />
                    </div>
                  </div>
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? "Tracking..." : "Track Order"}
                  </Button>

                  {error && <p className="text-red-600 text-sm mt-2">{error}</p>}

                  <div className="text-sm text-muted-foreground mt-4">
                    <p>For testing, use order numbers: CM12345 or CM67890</p>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tracking-number">
            <Card>
              <CardHeader>
                <CardTitle>Enter Tracking Details</CardTitle>
                <CardDescription>Please enter your tracking number and select the carrier.</CardDescription>
              </CardHeader>
              <CardContent>
                <form className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label htmlFor="tracking-number" className="text-sm font-medium">
                        Tracking Number
                      </label>
                      <Input id="tracking-number" placeholder="e.g., TRK9876543210" required />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="carrier" className="text-sm font-medium">
                        Carrier
                      </label>
                      <select
                        id="carrier"
                        className="w-full h-10 px-3 rounded-md border border-input bg-background"
                        required
                      >
                        <option value="">Select Carrier</option>
                        <option value="fedex">FedEx</option>
                        <option value="ups">UPS</option>
                        <option value="usps">USPS</option>
                        <option value="dhl">DHL</option>
                      </select>
                    </div>
                  </div>
                  <Button type="submit" className="w-full">
                    Track Package
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {orderStatus && (
          <div className="mt-8 space-y-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>Order #{orderStatus.orderNumber}</CardTitle>
                    <CardDescription>Last updated: {orderStatus.lastUpdated}</CardDescription>
                  </div>
                  <div
                    className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(
                      orderStatus.status,
                    )}`}
                  >
                    {getStatusText(orderStatus.status)}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium mb-2">Shipping Details</h3>
                    <div className="text-sm space-y-1">
                      {orderStatus.trackingNumber && (
                        <p>
                          <span className="text-muted-foreground">Tracking Number:</span>{" "}
                          <span className="font-medium">{orderStatus.trackingNumber}</span>
                        </p>
                      )}
                      {orderStatus.carrier && (
                        <p>
                          <span className="text-muted-foreground">Carrier:</span>{" "}
                          <span className="font-medium">{orderStatus.carrier}</span>
                        </p>
                      )}
                      <p>
                        <span className="text-muted-foreground">Estimated Delivery:</span>{" "}
                        <span className="font-medium">{orderStatus.estimatedDelivery}</span>
                      </p>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium mb-2">Shipping Address</h3>
                    <div className="text-sm space-y-1">
                      <p className="font-medium">{orderStatus.shippingAddress.name}</p>
                      <p>{orderStatus.shippingAddress.street}</p>
                      <p>
                        {orderStatus.shippingAddress.city}, {orderStatus.shippingAddress.state}{" "}
                        {orderStatus.shippingAddress.zip}
                      </p>
                      <p>{orderStatus.shippingAddress.country}</p>
                    </div>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-sm font-medium mb-4">Order Items</h3>
                  <div className="space-y-4">
                    {orderStatus.items.map((item) => (
                      <div key={item.id} className="flex items-center space-x-4">
                        <div className="relative w-16 h-16 bg-muted rounded-md overflow-hidden flex-shrink-0">
                          <img
                            src={item.image || "/placeholder.svg"}
                            alt={item.name}
                            className="object-cover w-full h-full"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium">{item.name}</p>
                          <p className="text-sm text-muted-foreground">Quantity: {item.quantity}</p>
                        </div>
                        <div className="text-sm font-medium">${item.price.toFixed(2)}</div>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-sm font-medium mb-4">Tracking Timeline</h3>
                  <div className="relative">
                    {orderStatus.timeline.map((event, index) => (
                      <div key={index} className="flex mb-6 last:mb-0">
                        <div className="flex flex-col items-center mr-4">
                          <div className="rounded-full h-10 w-10 flex items-center justify-center bg-primary text-primary-foreground">
                            {index === 0 ? (
                              getStatusIcon(orderStatus.status)
                            ) : (
                              <div className="h-3 w-3 rounded-full bg-primary-foreground" />
                            )}
                          </div>
                          {index < orderStatus.timeline.length - 1 && (
                            <div className="w-px h-full bg-border flex-grow mt-2" />
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-1">
                            <h4 className="font-medium">{event.status}</h4>
                            <span className="text-sm text-muted-foreground">{event.date}</span>
                          </div>
                          {event.location && <p className="text-sm text-muted-foreground mb-1">{event.location}</p>}
                          <p className="text-sm">{event.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-between">
              <Button variant="outline" asChild>
                <Link href="/orders">View All Orders</Link>
              </Button>
              <Button variant="outline">
                <Search className="h-4 w-4 mr-2" />
                Track Another Order
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
