"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { Package, Search, ShoppingBag, Truck, CheckCircle, Clock } from "lucide-react"

import { useOrders } from '@/lib/ecommerce/hooks/use-orders'
import { useAuth } from '@/lib/ecommerce/hooks/use-auth'

export default function OrdersPage() {
  const [orderNumber, setOrderNumber] = useState("")
  const [email, setEmail] = useState("")

  // Real data hooks
  const { user } = useAuth()
  const { orders, loading, error, refetch } = useOrders({
    customerId: user?.id,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })
  const [searchResults, setSearchResults] = useState<any[] | null>(null)
  const [isSearching, setIsSearching] = useState(false)
  const isLoggedIn = !!user

  const handleOrderLookup = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!orderNumber && !email) {
      toast({
        title: "Missing information",
        description: "Please enter either an order number or email address.",
        variant: "destructive",
      })
      return
    }

    setIsSearching(true)

    try {
      // Search for orders using the API
      const params = new URLSearchParams()
      if (orderNumber) params.append('orderNumber', orderNumber)
      if (email) params.append('email', email)

      const response = await fetch(`/api/e-commerce/orders/search?${params}`)
      const data = await response.json()

      if (data.success) {
        setSearchResults(data.data || [])

        if (data.data.length === 0) {
          toast({
            title: "No orders found",
            description: "Please check your order number or email address and try again.",
            variant: "destructive",
          })
        } else {
          toast({
            title: "Orders found",
            description: `Found ${data.data.length} order(s)`,
          })
        }
      } else {
        throw new Error(data.error || 'Search failed')
      }
    } catch (error) {
      console.error('Order search error:', error)
      toast({
        title: "Search failed",
        description: "Unable to search for orders. Please try again.",
        variant: "destructive",
      })
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "processing":
        return <Clock className="h-4 w-4" />
      case "shipped":
        return <Truck className="h-4 w-4" />
      case "delivered":
        return <CheckCircle className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "processing":
        return "bg-yellow-100 text-yellow-800"
      case "shipped":
        return "bg-blue-100 text-blue-800"
      case "delivered":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      {/* Header */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center gap-2 mb-4">
          <Package className="h-6 w-6 text-[#012169]" />
          <h1 className="text-3xl md:text-4xl font-bold font-montserrat">Order Tracking</h1>
        </div>
        <p className="text-lg text-muted-foreground font-light max-w-2xl mx-auto">
          Track your orders and view your order history. Enter your order number or email to get started.
        </p>
      </div>

      {/* Logged-in User Orders */}
      {isLoggedIn && (
        <div className="max-w-4xl mx-auto space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold font-montserrat">Your Orders</h2>
            <Button onClick={() => refetch()} variant="outline" size="sm">
              Refresh
            </Button>
          </div>

          {loading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="animate-pulse space-y-4">
                      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : error ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-red-600">Error loading orders: {error}</p>
                <Button onClick={() => refetch()} className="mt-2">
                  Try Again
                </Button>
              </CardContent>
            </Card>
          ) : orders.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <ShoppingBag className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="font-medium mb-2">No orders yet</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Start shopping to see your orders here.
                </p>
                <Button asChild>
                  <Link href="/products">Start Shopping</Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {orders.map((order) => (
                <Card key={order.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="font-montserrat">Order {order.orderNumber}</CardTitle>
                        <CardDescription>
                          Placed on {new Date(order.createdAt).toLocaleDateString()}
                        </CardDescription>
                      </div>
                      <Badge className={`${getStatusColor(order.status)} flex items-center gap-1`}>
                        {getStatusIcon(order.status)}
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <h4 className="font-medium">Items Ordered</h4>
                      {(order.orderItems || []).map((item: any, index: number) => (
                        <div key={index} className="flex justify-between text-sm">
                          <span>
                            {item.product?.title || item.productTitle}
                            {item.variant && ` (${item.variant})`}
                            x {item.quantity}
                          </span>
                          <span>R{(item.price?.amount * item.quantity).toFixed(2)}</span>
                        </div>
                      ))}
                    </div>

                    <div className="border-t pt-4 flex justify-between items-center">
                      <span className="font-medium">
                        Total: R{order.total?.amount.toFixed(2)}
                      </span>
                      <div className="flex gap-2">
                        <Button asChild variant="outline" size="sm">
                          <Link href={`/orders/${order.id}`}>
                            View Details
                          </Link>
                        </Button>
                        {order.status === 'shipped' && (
                          <Button asChild variant="outline" size="sm">
                            <Link href={`/orders/track/${order.id}`}>
                              Track Package
                            </Link>
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}

      {!isLoggedIn && (
        <div className="max-w-2xl mx-auto mb-12">
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Search className="h-5 w-5 text-[#012169]" />
                <CardTitle className="font-montserrat">Track Your Order</CardTitle>
              </div>
              <CardDescription>
                Enter your order information to track your package or view order details.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleOrderLookup} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="orderNumber">Order Number</Label>
                    <Input
                      id="orderNumber"
                      value={orderNumber}
                      onChange={(e) => setOrderNumber(e.target.value)}
                      placeholder="ORD-2024-001"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                
                <Button
                  type="submit"
                  disabled={isSearching}
                  className="w-full bg-[#012169] hover:bg-[#012169]/90 font-medium"
                >
                  {isSearching ? "Searching..." : "Track Order"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Order Results */}
      {searchResults && (
        <div className="max-w-4xl mx-auto space-y-6">
          <h2 className="text-xl font-bold font-montserrat">
            {searchResults.length === 1 ? "Order Found" : `${searchResults.length} Orders Found`}
          </h2>
          
          {searchResults.map((order) => (
            <Card key={order.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="font-montserrat">Order {order.orderNumber || order.id}</CardTitle>
                    <CardDescription>
                      Placed on {new Date(order.createdAt || order.date).toLocaleDateString()}
                    </CardDescription>
                  </div>
                  <Badge className={`${getStatusColor(order.status)} flex items-center gap-1`}>
                    {getStatusIcon(order.status)}
                    {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <h4 className="font-medium">Items Ordered</h4>
                  {(order.items || order.orderItems || []).map((item: any, index: number) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span>
                        {item.product?.title || item.name}
                        {item.variant && ` (${item.variant})`}
                        {item.size && ` (Size: ${item.size})`}
                        x {item.quantity}
                      </span>
                      <span>R{((item.price?.amount || item.price || 0) * item.quantity).toFixed(2)}</span>
                    </div>
                  ))}
                </div>

                <div className="border-t pt-4 flex justify-between items-center">
                  <span className="font-medium">
                    Total: R{(order.total?.amount || order.total || 0).toFixed(2)}
                  </span>
                  <Button asChild variant="outline" size="sm">
                    <Link href={`/orders/${order.id}`}>
                      View Details
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Login Prompt */}
      {!isLoggedIn && !searchResults && (
        <div className="text-center mt-12">
          <div className="bg-gray-50 rounded-lg p-8 max-w-md mx-auto">
            <ShoppingBag className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="font-medium mb-2">Want to see all your orders?</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Create an account or sign in to view your complete order history and track all your packages.
            </p>
            <div className="space-y-2">
              <Button asChild className="w-full bg-[#012169] hover:bg-[#012169]/90">
                <Link href="/auth/login">Sign In</Link>
              </Button>
              <Button asChild variant="outline" className="w-full">
                <Link href="/auth/register">Create Account</Link>
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
