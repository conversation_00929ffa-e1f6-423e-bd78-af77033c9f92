"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, ChevronUp, HelpCircle } from "lucide-react"
import { cn } from "@/lib/utils"

const faqData = [
  {
    category: "Orders & Shipping",
    questions: [
      {
        question: "How long does shipping take?",
        answer: "Standard shipping takes 5-7 business days, while express shipping takes 2-3 business days. Orders are typically processed within 1-2 business days before shipping."
      },
      {
        question: "Do you offer free shipping?",
        answer: "Yes! We offer free standard shipping on all orders over $75 within the United States."
      },
      {
        question: "Can I track my order?",
        answer: "Absolutely! Once your order ships, you'll receive a tracking number via email. You can also track your order in your account dashboard."
      },
      {
        question: "Can I change or cancel my order?",
        answer: "You can modify or cancel your order within 2 hours of placing it. After that, orders enter our fulfillment process and cannot be changed."
      }
    ]
  },
  {
    category: "Sizing & Fit",
    questions: [
      {
        question: "How do I find the right size for my child?",
        answer: "We recommend using our size chart and AI-powered size assistant. Our sizes run slightly oversized for comfort. When in doubt, size up for growing room."
      },
      {
        question: "What if the size doesn't fit?",
        answer: "No worries! We offer free exchanges within 30 days. Just contact our customer service team to initiate an exchange."
      },
      {
        question: "Do your clothes shrink after washing?",
        answer: "Our clothes are pre-shrunk and made from high-quality materials. Follow our care instructions to maintain the original fit and quality."
      }
    ]
  },
  {
    category: "Returns & Exchanges",
    questions: [
      {
        question: "What is your return policy?",
        answer: "We accept returns within 30 days of delivery. Items must be unworn, with original tags, and in original packaging."
      },
      {
        question: "How do I return an item?",
        answer: "Contact our customer service team to receive a return authorization and prepaid shipping label. Package your items securely and ship them back to us."
      },
      {
        question: "How long do refunds take?",
        answer: "Refunds are processed within 5-7 business days after we receive your return. You'll receive an email confirmation once processed."
      },
      {
        question: "Can I exchange for a different size or color?",
        answer: "Yes! Exchanges are free and easy. Contact us to initiate an exchange, and we'll send you the new item once we receive the original."
      }
    ]
  },
  {
    category: "Product Care",
    questions: [
      {
        question: "How should I care for my Coco Milk Kids clothing?",
        answer: "Most items can be machine washed in cold water and tumble dried on low. Check the care label on each item for specific instructions."
      },
      {
        question: "Are your clothes safe for sensitive skin?",
        answer: "Yes! All our fabrics are OEKO-TEX certified and free from harmful chemicals. We use soft, breathable materials that are gentle on sensitive skin."
      },
      {
        question: "What materials do you use?",
        answer: "We primarily use 100% organic cotton, cotton blends, and other natural fibers. All materials are carefully selected for comfort, durability, and safety."
      }
    ]
  }
]

export default function FAQPage() {
  const [openItems, setOpenItems] = useState<string[]>([])

  const toggleItem = (id: string) => {
    setOpenItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      {/* Header */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center gap-2 mb-4">
          <HelpCircle className="h-6 w-6 text-[#012169]" />
          <h1 className="text-3xl md:text-4xl font-bold font-montserrat">Frequently Asked Questions</h1>
        </div>
        <p className="text-lg text-muted-foreground font-light max-w-2xl mx-auto">
          Find answers to common questions about our products, shipping, returns, and more.
        </p>
      </div>

      {/* FAQ Categories */}
      <div className="space-y-8">
        {faqData.map((category, categoryIndex) => (
          <Card key={categoryIndex}>
            <CardHeader>
              <CardTitle className="font-montserrat">{category.category}</CardTitle>
              <CardDescription>
                Common questions about {category.category.toLowerCase()}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {category.questions.map((faq, questionIndex) => {
                const itemId = `${categoryIndex}-${questionIndex}`
                const isOpen = openItems.includes(itemId)

                return (
                  <Collapsible key={questionIndex} open={isOpen} onOpenChange={() => toggleItem(itemId)}>
                    <CollapsibleTrigger className="flex w-full items-center justify-between py-3 px-4 text-left bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                      <span className="font-medium">{faq.question}</span>
                      {isOpen ? (
                        <ChevronUp className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      )}
                    </CollapsibleTrigger>
                    <CollapsibleContent className="px-4 py-3 text-muted-foreground">
                      {faq.answer}
                    </CollapsibleContent>
                  </Collapsible>
                )
              })}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Contact Section */}
      <div className="mt-12 text-center bg-gray-50 rounded-lg p-8">
        <h2 className="text-xl font-bold font-montserrat mb-4">Still Have Questions?</h2>
        <p className="text-muted-foreground mb-4">
          Can't find what you're looking for? Our customer service team is here to help.
        </p>
        <p className="text-sm">
          Email us at <a href="mailto:<EMAIL>" className="text-[#012169] hover:underline"><EMAIL></a> or
          call <a href="tel:+27111234567" className="text-[#012169] hover:underline">+27 11 123 4567</a>
        </p>
      </div>
    </div>
  )
}
