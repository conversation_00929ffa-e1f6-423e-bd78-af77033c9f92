"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"
import { Mail, Gift, Sparkles, TrendingUp, Users } from "lucide-react"

export default function NewsletterPage() {
  const [email, setEmail] = useState("")
  const [preferences, setPreferences] = useState({
    newArrivals: true,
    sales: true,
    styling: false,
    events: false,
  })
  const [isSubscribed, setIsSubscribed] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!email) {
      toast({
        title: "Email required",
        description: "Please enter your email address.",
        variant: "destructive",
      })
      return
    }

    // In a real app, you would send this data to your backend
    setIsSubscribed(true)
    toast({
      title: "Welcome to our newsletter!",
      description: "You'll receive your first email soon with a special welcome offer.",
    })
  }

  const handlePreferenceChange = (key: keyof typeof preferences) => {
    setPreferences(prev => ({ ...prev, [key]: !prev[key] }))
  }

  if (isSubscribed) {
    return (
      <div className="container px-4 md:px-6 py-16">
        <div className="max-w-2xl mx-auto text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Mail className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold font-montserrat mb-4">Welcome to the Family!</h1>
          <p className="text-lg text-muted-foreground mb-8">
            Thank you for subscribing to our newsletter. You'll be the first to know about new arrivals,
            exclusive sales, and styling tips for your little ones.
          </p>
          <div className="bg-[#012169]/5 border border-[#012169]/20 rounded-lg p-6">
            <Gift className="h-8 w-8 text-[#012169] mx-auto mb-4" />
            <h3 className="font-medium mb-2">Welcome Gift</h3>
            <p className="text-sm text-muted-foreground">
              Check your email for a special 15% off coupon code to use on your first order!
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      {/* Header */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center gap-2 mb-4">
          <Mail className="h-6 w-6 text-[#012169]" />
          <h1 className="text-3xl md:text-4xl font-bold font-montserrat">Join Our Newsletter</h1>
        </div>
        <p className="text-lg text-muted-foreground font-light max-w-2xl mx-auto">
          Stay in the loop with the latest from Coco Milk Kids. Get exclusive access to new arrivals,
          special offers, and styling tips for your little South Africans.
        </p>
      </div>

      <div className="grid lg:grid-cols-3 gap-8 mb-12">
        {/* Benefits */}
        <div className="lg:col-span-2 space-y-6">
          <h2 className="text-xl font-bold font-montserrat mb-6">What You'll Get</h2>

          <div className="grid md:grid-cols-2 gap-6">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center space-x-3 mb-3">
                  <Sparkles className="h-5 w-5 text-[#012169]" />
                  <h3 className="font-medium">New Arrivals First</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  Be the first to shop our latest collections before they're available to the public.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center space-x-3 mb-3">
                  <TrendingUp className="h-5 w-5 text-[#012169]" />
                  <h3 className="font-medium">Exclusive Sales</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  Get access to subscriber-only sales and special discount codes.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center space-x-3 mb-3">
                  <Users className="h-5 w-5 text-[#012169]" />
                  <h3 className="font-medium">Styling Tips</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  Expert advice on how to style your kids' outfits for every occasion.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center space-x-3 mb-3">
                  <Gift className="h-5 w-5 text-[#012169]" />
                  <h3 className="font-medium">Birthday Surprises</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  Special birthday offers and surprises for your little ones.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Subscription Form */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="font-montserrat">Subscribe Now</CardTitle>
              <CardDescription>
                Join thousands of parents who trust Coco Milk Kids for their children's wardrobe.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div className="space-y-4">
                  <Label>Email Preferences</Label>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="newArrivals"
                        checked={preferences.newArrivals}
                        onCheckedChange={() => handlePreferenceChange('newArrivals')}
                      />
                      <Label htmlFor="newArrivals" className="text-sm">New arrivals & collections</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="sales"
                        checked={preferences.sales}
                        onCheckedChange={() => handlePreferenceChange('sales')}
                      />
                      <Label htmlFor="sales" className="text-sm">Sales & special offers</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="styling"
                        checked={preferences.styling}
                        onCheckedChange={() => handlePreferenceChange('styling')}
                      />
                      <Label htmlFor="styling" className="text-sm">Styling tips & guides</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="events"
                        checked={preferences.events}
                        onCheckedChange={() => handlePreferenceChange('events')}
                      />
                      <Label htmlFor="events" className="text-sm">Events & community updates</Label>
                    </div>
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full bg-[#012169] hover:bg-[#012169]/90 font-medium"
                >
                  Subscribe & Get 15% Off
                </Button>

                <p className="text-xs text-muted-foreground text-center">
                  By subscribing, you agree to our privacy policy. You can unsubscribe at any time.
                </p>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Social Proof */}
      <div className="text-center bg-gray-50 rounded-lg p-8">
        <h3 className="font-medium mb-2">Join 5,000+ Happy South African Families</h3>
        <p className="text-sm text-muted-foreground">
          "I love getting the newsletter! The styling tips are so helpful and I never miss a sale. Plus, free shipping to Cape Town!" - Sarah M., Cape Town
        </p>
      </div>
    </div>
  )
}
