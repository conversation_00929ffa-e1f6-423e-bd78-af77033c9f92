import { PageRenderer } from '@/lib/page-builder/components/page-renderer'
import { PageData } from '@/lib/page-builder/types'

export default function ContactPage() {
  // Create a page configuration using page builder blocks
  const pageData: PageData = {
    id: 'contact',
    title: 'Contact Us',
    slug: 'contact',
    description: 'Get in touch with our friendly team',
    status: 'published',
    type: 'custom',
    blocks: [
      // Hero Section
      {
        id: 'contact-hero-1',
        type: 'hero',
        position: 0,
        isVisible: true,
        configuration: {
          title: 'Contact Us',
          subtitle: 'Have a question or need help? We\'re here to assist you. Get in touch with our friendly team.',
          alignment: 'center',
          backgroundType: 'color',
          backgroundColor: '#ffffff',
          showCta: false,
          padding: { top: '3rem', bottom: '3rem' }
        },
        content: {},
        styling: {},
        responsive: {
          desktop: { isVisible: true },
          tablet: { isVisible: true },
          mobile: { isVisible: true }
        }
      },

      // Contact Info Section
      {
        id: 'contact-info-1',
        type: 'contact-info',
        position: 1,
        isVisible: true,
        configuration: {
          layout: 'cards',
          contacts: [
            {
              icon: 'Mail',
              title: 'Email',
              value: '<EMAIL>',
              description: 'Send us an email anytime'
            },
            {
              icon: 'Phone',
              title: 'Phone',
              value: '+27 11 123 4567',
              description: 'Mon-Fri from 8am to 5pm'
            },
            {
              icon: 'MapPin',
              title: 'Address',
              value: '123 Sandton Drive, Sandton, Johannesburg 2196',
              description: 'Visit our showroom'
            },
            {
              icon: 'Clock',
              title: 'Business Hours',
              value: 'Mon-Fri: 8am-5pm, Sat: 9am-2pm',
              description: 'We\'re here to help'
            }
          ],
          spacing: 'normal'
        },
        content: {},
        styling: {},
        responsive: {
          desktop: { isVisible: true },
          tablet: { isVisible: true },
          mobile: { isVisible: true }
        }
      },

      // Contact Form Section
      {
        id: 'contact-form-1',
        type: 'contact-form',
        position: 2,
        isVisible: true,
        configuration: {
          title: 'Send us a Message',
          description: 'Fill out the form below and we\'ll get back to you as soon as possible.',
          fields: [
            { name: 'name', label: 'Name', type: 'text', required: true, placeholder: 'Your full name' },
            { name: 'email', label: 'Email', type: 'email', required: true, placeholder: '<EMAIL>' },
            {
              name: 'subject',
              label: 'Subject',
              type: 'select',
              required: true,
              options: [
                { value: 'general', label: 'General Inquiry' },
                { value: 'order', label: 'Order Support' },
                { value: 'returns', label: 'Returns & Exchanges' },
                { value: 'sizing', label: 'Sizing Help' },
                { value: 'wholesale', label: 'Wholesale Inquiry' },
                { value: 'other', label: 'Other' }
              ]
            },
            { name: 'message', label: 'Message', type: 'textarea', required: true, placeholder: 'Tell us how we can help you...', rows: 6 }
          ],
          submitText: 'Send Message',
          successMessage: 'Message sent! We\'ll get back to you within 24 hours.',
          layout: 'single-column',
          spacing: 'normal'
        },
        content: {},
        styling: {},
        responsive: {
          desktop: { isVisible: true },
          tablet: { isVisible: true },
          mobile: { isVisible: true }
        }
      }
    ],
    settings: {
      title: 'Contact Us',
      description: 'Get in touch with our friendly team',
      seoTitle: 'Contact Us - Coco Milk Kids',
      seoDescription: 'Get in touch with Coco Milk Kids. We\'re here to help with any questions about our premium children\'s clothing.',
      requiresAuth: false,
      allowComments: false
    }
  }

  return <PageRenderer page={pageData} />
}
