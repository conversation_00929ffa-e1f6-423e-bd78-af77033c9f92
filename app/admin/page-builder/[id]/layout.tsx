"use client"

import { PageBuilderProvider } from "@/lib/page-builder/context"
import { PageBuilderSidebar } from "@/components/admin/page-builder-sidebar"
import { PageBuilderPropertiesSidebar } from "@/components/admin/page-builder-properties-sidebar"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { useRouter } from "next/navigation"

export default function PageBuilderLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()

  const handleBackToAdmin = () => {
    router.push('/admin/page-builder')
  }

  return (
    <PageBuilderProvider>
      <SidebarProvider>
        <PageBuilderSidebar onBackToAdmin={handleBackToAdmin} />
        <SidebarInset>
          {/* <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 h-4" />
              <AdminBreadcrumb />
            </div>
            <div className="ml-auto flex items-center gap-2 px-4">
              <NotificationCenter />
              <div className="text-sm text-muted-foreground">
                Page Builder Editor
              </div>
            </div>
          </header> */}
          <div className="flex flex-1 overflow-hidden">
            <div className="flex-1 overflow-hidden">
              {children}
            </div>
            <PageBuilderPropertiesSidebar />
          </div>
        </SidebarInset>
      </SidebarProvider>
    </PageBuilderProvider>
  )
}
