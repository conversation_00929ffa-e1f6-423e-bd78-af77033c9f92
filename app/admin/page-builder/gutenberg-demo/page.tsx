'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Plus, Save, Eye, Settings } from 'lucide-react'
import { G<PERSON>nbergBlockWrapper } from '@/lib/page-builder/components/block-wrapper/gutenberg-block-wrapper'
import { CompactBlockSettings } from '@/lib/page-builder/components/properties-panel/compact-block-settings'
import { GutenbergBlockInserter, FloatingAddButton } from '@/lib/page-builder/components/block-inserter/gutenberg-block-inserter'
import { BlockNavigation, CompactBlockNavigation } from '@/lib/page-builder/components/navigation/block-navigation'
import { BlockPatterns } from '@/lib/page-builder/components/patterns/block-patterns'
import { BlockFormatToolbar } from '@/lib/page-builder/components/block-toolbar/block-format-toolbar'
import { blockRegistry, registerDefaultBlocks } from '@/lib/page-builder/blocks/registry'

// Initialize blocks
registerDefaultBlocks()

interface DemoBlock {
  id: string
  type: string
  configuration: any
  hidden?: boolean
}

export default function GutenbergDemoPage() {
  const [blocks, setBlocks] = useState<DemoBlock[]>([
    {
      id: '1',
      type: 'text',
      configuration: {
        content: '<p>Welcome to our <strong>Gutenberg-style</strong> page builder! This text block demonstrates the new dropdown and popover interface.</p>',
        textAlign: 'left',
        fontSize: 'medium',
        textColor: '#333333',
        backgroundColor: 'transparent'
      }
    },
    {
      id: '2',
      type: 'button',
      configuration: {
        text: 'Get Started',
        url: { url: '/get-started', text: 'Get Started', target: '_self' },
        variant: 'primary',
        size: 'medium',
        backgroundColor: '#007bff',
        textColor: '#ffffff'
      }
    },
    {
      id: '3',
      type: 'image',
      configuration: {
        src: '/api/placeholder/600/300',
        alt: 'Demo image',
        caption: 'This is a demo image with caption',
        width: '100%',
        height: 'auto',
        objectFit: 'cover'
      }
    }
  ])

  const [selectedBlockId, setSelectedBlockId] = useState<string | null>(null)
  const [hoveredBlockId, setHoveredBlockId] = useState<string | null>(null)

  const updateBlock = (blockId: string, updates: any) => {
    setBlocks(prev => prev.map(block => 
      block.id === blockId 
        ? { ...block, configuration: { ...block.configuration, ...updates } }
        : block
    ))
  }

  const duplicateBlock = (blockId: string) => {
    const block = blocks.find(b => b.id === blockId)
    if (block) {
      const newBlock = {
        ...block,
        id: Date.now().toString(),
      }
      const index = blocks.findIndex(b => b.id === blockId)
      setBlocks(prev => [
        ...prev.slice(0, index + 1),
        newBlock,
        ...prev.slice(index + 1)
      ])
    }
  }

  const deleteBlock = (blockId: string) => {
    setBlocks(prev => prev.filter(b => b.id !== blockId))
    if (selectedBlockId === blockId) {
      setSelectedBlockId(null)
    }
  }

  const moveBlock = (blockId: string, direction: 'up' | 'down') => {
    const index = blocks.findIndex(b => b.id === blockId)
    if (index === -1) return

    const newIndex = direction === 'up' ? index - 1 : index + 1
    if (newIndex < 0 || newIndex >= blocks.length) return

    const newBlocks = [...blocks]
    const [movedBlock] = newBlocks.splice(index, 1)
    newBlocks.splice(newIndex, 0, movedBlock)
    setBlocks(newBlocks)
  }

  const toggleBlockVisibility = (blockId: string) => {
    setBlocks(prev => prev.map(block => 
      block.id === blockId 
        ? { ...block, hidden: !block.hidden }
        : block
    ))
  }

  const addBlock = (type: string, afterBlockId?: string) => {
    const blockType = blockRegistry.getBlockType(type)
    if (!blockType) return

    const newBlock: DemoBlock = {
      id: Date.now().toString(),
      type,
      configuration: blockType.defaultConfig || {}
    }

    if (afterBlockId) {
      const index = blocks.findIndex(b => b.id === afterBlockId)
      setBlocks(prev => [
        ...prev.slice(0, index + 1),
        newBlock,
        ...prev.slice(index + 1)
      ])
    } else {
      setBlocks(prev => [...prev, newBlock])
    }
  }

  const addPattern = (pattern: any) => {
    const newBlocks = pattern.blocks.map((block: any) => ({
      id: Date.now().toString() + Math.random(),
      type: block.type,
      configuration: block.configuration || {}
    }))
    setBlocks(prev => [...prev, ...newBlocks])
  }

  const renderBlock = (block: DemoBlock) => {
    const blockType = blockRegistry.getBlockType(block.type)
    if (!blockType) return <div>Unknown block type: {block.type}</div>

    // Simple block renderers for demo
    switch (block.type) {
      case 'text':
        return (
          <div 
            style={{
              textAlign: block.configuration.textAlign,
              fontSize: block.configuration.fontSize === 'small' ? '14px' : 
                       block.configuration.fontSize === 'large' ? '18px' :
                       block.configuration.fontSize === 'xl' ? '24px' : '16px',
              color: block.configuration.textColor,
              backgroundColor: block.configuration.backgroundColor,
              padding: '16px',
              lineHeight: block.configuration.lineHeight === 'tight' ? '1.2' :
                         block.configuration.lineHeight === 'relaxed' ? '1.8' : '1.5'
            }}
            dangerouslySetInnerHTML={{ __html: block.configuration.content || '' }}
          />
        )

      case 'button':
        return (
          <div style={{ padding: '16px', textAlign: 'center' }}>
            <button
              style={{
                backgroundColor: block.configuration.backgroundColor,
                color: block.configuration.textColor,
                padding: block.configuration.size === 'small' ? '8px 16px' :
                        block.configuration.size === 'large' ? '16px 32px' : '12px 24px',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: block.configuration.size === 'small' ? '14px' :
                        block.configuration.size === 'large' ? '18px' : '16px',
                width: block.configuration.fullWidth ? '100%' : 'auto'
              }}
            >
              {block.configuration.text || 'Button'}
            </button>
          </div>
        )

      case 'image':
        return (
          <div style={{ padding: '16px' }}>
            <img
              src={block.configuration.src}
              alt={block.configuration.alt}
              style={{
                width: block.configuration.width,
                height: block.configuration.height,
                objectFit: block.configuration.objectFit,
                borderRadius: block.configuration.borderRadius,
                display: 'block',
                margin: '0 auto'
              }}
            />
            {block.configuration.caption && (
              <p style={{ 
                textAlign: 'center', 
                fontSize: '14px', 
                color: '#666', 
                marginTop: '8px' 
              }}>
                {block.configuration.caption}
              </p>
            )}
          </div>
        )

      default:
        return <div>Block type: {block.type}</div>
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div>
              <h1 className="text-xl font-semibold">Gutenberg-Style Page Builder</h1>
              <p className="text-sm text-gray-600 mt-1">
                Professional block editor with dropdowns and popovers
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Navigation */}
            <BlockNavigation
              blocks={blocks}
              selectedBlockId={selectedBlockId}
              onSelectBlock={setSelectedBlockId}
              onUpdateBlock={updateBlock}
              onDuplicateBlock={duplicateBlock}
              onDeleteBlock={deleteBlock}
              onMoveBlock={moveBlock}
            />

            {/* Patterns */}
            <BlockPatterns onInsertPattern={addPattern} />

            {/* Actions */}
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>
            <Button size="sm">
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
          </div>
        </div>

        {/* Secondary Toolbar */}
        <div className="flex items-center justify-between mt-4 pt-4 border-t">
          <div className="flex items-center gap-2">
            <CompactBlockNavigation
              blocks={blocks}
              selectedBlockId={selectedBlockId}
              onSelectBlock={setSelectedBlockId}
            />

            {selectedBlockId && (() => {
              const selectedBlock = blocks.find(b => b.id === selectedBlockId)
              const blockType = selectedBlock ? blockRegistry.getBlockType(selectedBlock.type) : null

              return selectedBlock && blockType ? (
                <BlockFormatToolbar
                  block={selectedBlock}
                  blockType={blockType}
                  onUpdate={(updates) => updateBlock(selectedBlockId, updates)}
                />
              ) : null
            })()}
          </div>

          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {blocks.length} block{blocks.length !== 1 ? 's' : ''}
            </Badge>
            {selectedBlockId && (
              <Badge variant="outline" className="text-xs">
                Block #{blocks.findIndex(b => b.id === selectedBlockId) + 1} selected
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto py-8 px-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Interactive Block Editor
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {blocks.map((block) => (
              <GutenbergBlockWrapper
                key={block.id}
                block={block}
                isSelected={selectedBlockId === block.id}
                isHovered={hoveredBlockId === block.id}
                onSelect={() => setSelectedBlockId(block.id)}
                onUpdate={(updates) => updateBlock(block.id, updates)}
                onDuplicate={() => duplicateBlock(block.id)}
                onDelete={() => deleteBlock(block.id)}
                onMoveUp={() => moveBlock(block.id, 'up')}
                onMoveDown={() => moveBlock(block.id, 'down')}
                onToggleVisibility={() => toggleBlockVisibility(block.id)}
                onInsertBefore={() => {
                  // Show block inserter for before
                  const index = blocks.findIndex(b => b.id === block.id)
                  // For demo, just add a text block
                  addBlock('text', blocks[index - 1]?.id)
                }}
                onInsertAfter={() => {
                  // Show block inserter for after
                  addBlock('text', block.id)
                }}
                onMouseEnter={() => setHoveredBlockId(block.id)}
                onMouseLeave={() => setHoveredBlockId(null)}
              >
                {renderBlock(block)}
              </GutenbergBlockWrapper>
            ))}

            {/* Add Block Button */}
            <div className="text-center py-8">
              <GutenbergBlockInserter
                onInsertBlock={(blockType) => addBlock(blockType)}
                trigger={
                  <Button
                    variant="outline"
                    className="border-dashed"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Block
                  </Button>
                }
              />
            </div>
          </CardContent>
        </Card>

        {/* Features Info */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>✨ Complete Gutenberg-Style Editor Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  🎯 Block Interaction
                </h4>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li>• Hover-activated toolbars</li>
                  <li>• Click for detailed settings</li>
                  <li>• Quick property dropdowns</li>
                  <li>• Drag handles for reordering</li>
                  <li>• Visual selection states</li>
                  <li>• Floating insert buttons</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  ⚡ Advanced Features
                </h4>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li>• Block navigation sidebar</li>
                  <li>• Pattern library browser</li>
                  <li>• Format toolbar for text</li>
                  <li>• Compact block navigation</li>
                  <li>• Schema-driven properties</li>
                  <li>• Real-time validation</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  🎨 User Experience
                </h4>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li>• WordPress-familiar interface</li>
                  <li>• Progressive disclosure</li>
                  <li>• Keyboard shortcuts</li>
                  <li>• Responsive design</li>
                  <li>• Professional styling</li>
                  <li>• Smooth animations</li>
                </ul>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">🚀 Try These Features:</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                <div>
                  <strong>Block Management:</strong>
                  <ul className="mt-1 space-y-1">
                    <li>• Hover over blocks to see toolbars</li>
                    <li>• Use the navigation sidebar</li>
                    <li>• Browse and insert patterns</li>
                  </ul>
                </div>
                <div>
                  <strong>Editing:</strong>
                  <ul className="mt-1 space-y-1">
                    <li>• Click settings icons for properties</li>
                    <li>• Use format toolbar for text blocks</li>
                    <li>• Try quick property dropdowns</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
