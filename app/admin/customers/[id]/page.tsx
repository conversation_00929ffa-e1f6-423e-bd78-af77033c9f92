'use client'

import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import { 
  <PERSON><PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import { 
  ArrowLeft, 
  Edit, 
  Mail, 
  Phone, 
  MapPin,
  Calendar,
  ShoppingBag,
  DollarSign,
  TrendingUp,
  User,
  Star,
  Gift
} from 'lucide-react'
import Link from 'next/link'
import { useZarFormatter } from '@/components/admin/zar-price-input'
import { useCustomer } from '@/lib/ecommerce/hooks/use-customers'
import type { User } from '@/lib/ecommerce/types'

export default function CustomerDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { formatPrice } = useZarFormatter()
  const customerId = params.id as string

  const { customer, loading, error } = useCustomer({ customerId })

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase()
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/customers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Customers
            </Link>
          </Button>
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error || !customer) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/customers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Customers
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <User className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Customer not found</h3>
              <p className="mt-1 text-sm text-gray-500">
                The customer you're looking for doesn't exist or has been deleted.
              </p>
              <div className="mt-6">
                <Button asChild>
                  <Link href="/admin/customers">
                    Back to Customers
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const getLoyaltyBadge = (tier: string) => {
    switch (tier) {
      case 'VIP':
        return <Badge variant="default" className="bg-purple-600">VIP</Badge>
      case 'Gold':
        return <Badge variant="default" className="bg-yellow-600">Gold</Badge>
      case 'Silver':
        return <Badge variant="secondary">Silver</Badge>
      case 'Bronze':
        return <Badge variant="outline">Bronze</Badge>
      default:
        return <Badge variant="secondary">Standard</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/customers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Customers
            </Link>
          </Button>
          <div className="flex items-center space-x-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={customer.avatar} alt={`${customer.firstName} ${customer.lastName}`} />
              <AvatarFallback className="text-lg">
                {getInitials(customer.firstName || '', customer.lastName || '')}
              </AvatarFallback>
            </Avatar>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {customer.firstName} {customer.lastName}
              </h1>
              <p className="text-muted-foreground">
                Customer since {customer.customerSince ? new Date(customer.customerSince).toLocaleDateString() : 'N/A'}
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Mail className="mr-2 h-4 w-4" />
            Send Email
          </Button>
          <Button variant="outline">
            <Edit className="mr-2 h-4 w-4" />
            Edit Customer
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Spent</p>
                <p className="text-2xl font-bold">{formatPrice(customer.totalSpent || 0)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Orders</p>
                <p className="text-2xl font-bold">{customer.orderCount || 0}</p>
              </div>
              <ShoppingBag className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Order Value</p>
                <p className="text-2xl font-bold">
                  {formatPrice(customer.orderCount > 0 ? (customer.totalSpent || 0) / customer.orderCount : 0)}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Loyalty Tier</p>
                {getLoyaltyBadge(customer.loyaltyTier || 'Standard')}
              </div>
              <Star className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="addresses">Addresses</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{customer.email}</p>
                    <p className="text-sm text-muted-foreground">Email</p>
                  </div>
                </div>
                
                {customer.phone && (
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{customer.phone}</p>
                      <p className="text-sm text-muted-foreground">Phone</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">
                      {customer.dateOfBirth ? new Date(customer.dateOfBirth).toLocaleDateString() : 'Not provided'}
                    </p>
                    <p className="text-sm text-muted-foreground">Date of Birth</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Account Details */}
            <Card>
              <CardHeader>
                <CardTitle>Account Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Account Status</label>
                  <div className="mt-1">
                    <Badge variant={customer.isActive ? "default" : "destructive"}>
                      {customer.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Email Verified</label>
                  <div className="mt-1">
                    <Badge variant={customer.emailVerified ? "default" : "outline"}>
                      {customer.emailVerified ? "Verified" : "Unverified"}
                    </Badge>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Preferred Currency</label>
                  <p className="mt-1">{customer.preferredCurrency || 'ZAR'}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Last Order</label>
                  <p className="mt-1">
                    {customer.lastOrderAt ? new Date(customer.lastOrderAt).toLocaleDateString() : 'No orders yet'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Marketing Preferences */}
          <Card>
            <CardHeader>
              <CardTitle>Marketing Preferences</CardTitle>
              <CardDescription>
                Customer's communication and marketing preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Email Marketing</label>
                  <div className="mt-1">
                    <Badge variant={customer.acceptsMarketing ? "default" : "outline"}>
                      {customer.acceptsMarketing ? "Subscribed" : "Unsubscribed"}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">SMS Marketing</label>
                  <div className="mt-1">
                    <Badge variant="outline">Not Set</Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Newsletter</label>
                  <div className="mt-1">
                    <Badge variant="outline">Not Set</Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Promotions</label>
                  <div className="mt-1">
                    <Badge variant="outline">Not Set</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="orders" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Order History</CardTitle>
              <CardDescription>
                All orders placed by this customer
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <ShoppingBag className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Order History Coming Soon</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Customer order history will be displayed here.
                </p>
                <div className="mt-6">
                  <Button asChild>
                    <Link href={`/admin/orders?customer=${customerId}`}>
                      View All Orders
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="addresses" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Saved Addresses</CardTitle>
              <CardDescription>
                Customer's billing and shipping addresses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <MapPin className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No Addresses Found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  This customer hasn't saved any addresses yet.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Activity</CardTitle>
              <CardDescription>
                Recent activity and engagement history
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                      <User className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>
                  <div>
                    <p className="font-medium">Account Created</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(customer.createdAt).toLocaleString()}
                    </p>
                  </div>
                </div>

                {customer.customerSince && (
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                        <Gift className="h-4 w-4 text-green-600" />
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">Became Customer</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(customer.customerSince).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}

                {customer.lastOrderAt && (
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                        <ShoppingBag className="h-4 w-4 text-purple-600" />
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">Last Order</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(customer.lastOrderAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                      <Calendar className="h-4 w-4 text-gray-600" />
                    </div>
                  </div>
                  <div>
                    <p className="font-medium">Last Updated</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(customer.updatedAt).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
