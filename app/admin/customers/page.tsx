'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { DataTable, DataTableColumn, DataTableAction } from '@/components/admin/data-table'
import { useZarFormatter } from '@/components/admin/zar-price-input'
import {
  Users,
  Eye,
  Mail,
  MapPin,
  ShoppingBag,
  TrendingUp
} from 'lucide-react'
import { useCustomers } from '@/lib/ecommerce/hooks/use-customers'
import type { User } from '@/lib/ecommerce/types'

export default function CustomersPage() {
  const { formatPrice } = useZarFormatter()
  const { customers, loading, error, searchCustomers } = useCustomers({
    initialParams: {
      page: 1,
      limit: 100,
      sort: { field: 'totalSpent', direction: 'desc' }
    }
  })

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never'
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getCustomerTier = (totalSpent: number) => {
    if (totalSpent >= 5000) return { label: 'VIP', variant: 'default' as const }
    if (totalSpent >= 2000) return { label: 'Gold', variant: 'secondary' as const }
    if (totalSpent >= 500) return { label: 'Silver', variant: 'outline' as const }
    return { label: 'Bronze', variant: 'outline' as const }
  }

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
  }

  const columns: DataTableColumn<User>[] = [
    {
      key: 'name',
      title: 'Customer',
      searchable: true,
      sortable: true,
      render: (customer) => (
        <div className="flex items-center space-x-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={customer.avatar} alt={`${customer.firstName} ${customer.lastName}`} />
            <AvatarFallback>
              {getInitials(customer.firstName || '', customer.lastName || '')}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">
              {customer.firstName} {customer.lastName}
            </div>
            <div className="text-sm text-muted-foreground flex items-center">
              <Mail className="mr-1 h-3 w-3" />
              {customer.email}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'location',
      title: 'Location',
      render: (customer) => (
        <div className="flex items-center text-sm text-muted-foreground">
          <MapPin className="mr-1 h-3 w-3" />
          <span>
            {customer.preferredCurrency || 'ZAR'}
          </span>
        </div>
      )
    },
    {
      key: 'orderCount',
      title: 'Orders',
      sortable: true,
      render: (customer) => (
        <div className="text-center">
          <div className="font-medium">{customer.orderCount}</div>
          <div className="text-sm text-muted-foreground">
            Last: {formatDate(customer.lastOrderAt?.toISOString())}
          </div>
        </div>
      )
    },
    {
      key: 'totalSpent',
      title: 'Total Spent',
      sortable: true,
      render: (customer) => (
        <div className="text-right">
          <div className="font-medium">{formatPrice(customer.totalSpent)}</div>
          <div className="text-sm text-muted-foreground">
            Avg: {formatPrice(customer.averageOrderValue)}
          </div>
        </div>
      )
    },
    {
      key: 'tier',
      title: 'Tier',
      render: (customer) => {
        const tier = getCustomerTier(customer.totalSpent)
        return (
          <Badge variant={tier.variant}>
            {tier.label}
          </Badge>
        )
      }
    },
    {
      key: 'createdAt',
      title: 'Joined',
      sortable: true,
      render: (customer) => (
        <div className="text-sm text-muted-foreground">
          {formatDate(customer.createdAt.toISOString())}
        </div>
      )
    }
  ]

  const actions: DataTableAction<User>[] = [
    {
      label: 'View Profile',
      icon: Eye,
      onClick: (customer) => {
        // Navigate to customer details page
        window.location.href = `/admin/customers/${customer.id}`
      }
    },
    {
      label: 'View Orders',
      icon: ShoppingBag,
      onClick: (customer) => {
        // Navigate to orders filtered by customer
        window.location.href = `/admin/orders?customer=${customer.id}`
      }
    }
  ]

  const customerStats = {
    total: customers.length,
    vip: customers.filter(c => getCustomerTier(c.totalSpent).label === 'VIP').length,
    gold: customers.filter(c => getCustomerTier(c.totalSpent).label === 'Gold').length,
    silver: customers.filter(c => getCustomerTier(c.totalSpent).label === 'Silver').length,
    bronze: customers.filter(c => getCustomerTier(c.totalSpent).label === 'Bronze').length,
    totalRevenue: customers.reduce((sum, customer) => sum + customer.totalSpent, 0),
    averageOrderValue: customers.length > 0
      ? customers.reduce((sum, customer) => sum + customer.totalSpent, 0) /
        customers.reduce((sum, customer) => sum + customer.orderCount, 0)
      : 0
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Customers</h1>
          <p className="text-muted-foreground">
            Manage customer relationships and track loyalty
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-4 w-4 text-muted-foreground" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Total Customers</p>
                <p className="text-2xl font-bold">{customerStats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">VIP</p>
              <p className="text-2xl font-bold text-purple-600">{customerStats.vip}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Gold</p>
              <p className="text-2xl font-bold text-yellow-600">{customerStats.gold}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Silver</p>
              <p className="text-2xl font-bold text-gray-600">{customerStats.silver}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Bronze</p>
              <p className="text-2xl font-bold text-orange-600">{customerStats.bronze}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Avg Order</p>
              <p className="text-lg font-bold">{formatPrice(customerStats.averageOrderValue)}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Customers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Management</CardTitle>
          <CardDescription>
            View customer profiles, order history, and loyalty tiers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            data={customers}
            columns={columns}
            actions={actions}
            loading={loading}
            searchPlaceholder="Search customers by name or email..."
            emptyMessage="No customers found."
            emptyIcon={Users}
            pageSize={20}
          />
        </CardContent>
      </Card>
    </div>
  )
}
