'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { DataTable, DataTableColumn, DataTableAction } from '@/components/admin/data-table'
import {
  Plus,
  Palette,
  Eye,
  Edit,
  Copy,
  Trash2,
  Download,
  Upload,
  Wand2,
  Settings,
  Zap
} from 'lucide-react'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { ThemeGeneratorOptions } from '@/lib/theme-generator/types'

interface Theme {
  id: string
  name: string
  description: string
  category: string
  tags: string[]
  preview: string
  isDefault: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
  config: any
}

export default function ThemesPage() {
  const [themes, setThemes] = useState<Theme[]>([])
  const [loading, setLoading] = useState(true)
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [showGeneratorDialog, setShowGeneratorDialog] = useState(false)
  const [showPresetDialog, setShowPresetDialog] = useState(false)
  const [presets, setPresets] = useState<any[]>([])
  const [generating, setGenerating] = useState(false)

  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors }
  } = useForm<ThemeGeneratorOptions>()

  // Load themes on mount
  useEffect(() => {
    loadThemes()
    loadPresets()
  }, [])

  const loadThemes = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/theme-generator/themes')
      if (response.ok) {
        const result = await response.json()
        setThemes(result.data || [])
      }
    } catch (error) {
      console.error('Error loading themes:', error)
      toast.error('Failed to load themes')
    } finally {
      setLoading(false)
    }
  }

  const loadPresets = async () => {
    try {
      const response = await fetch('/api/theme-generator/themes', { method: 'OPTIONS' })
      if (response.ok) {
        const result = await response.json()
        setPresets(result.data || [])
      }
    } catch (error) {
      console.error('Error loading presets:', error)
    }
  }

  const onGenerateTheme = async (data: ThemeGeneratorOptions) => {
    try {
      setGenerating(true)
      
      const response = await fetch('/api/theme-generator/themes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          generate: true,
          options: data,
          createdBy: 'admin'
        })
      })

      if (response.ok) {
        const result = await response.json()
        toast.success('Theme generated successfully!')
        setShowGeneratorDialog(false)
        reset()
        loadThemes()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to generate theme')
      }
    } catch (error) {
      console.error('Error generating theme:', error)
      toast.error('Failed to generate theme')
    } finally {
      setGenerating(false)
    }
  }

  const handleActivateTheme = async (theme: Theme) => {
    try {
      const response = await fetch(`/api/theme-generator/themes/${theme.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'activate' })
      })

      if (response.ok) {
        toast.success(`${theme.name} activated successfully!`)
        loadThemes()
      } else {
        toast.error('Failed to activate theme')
      }
    } catch (error) {
      console.error('Error activating theme:', error)
      toast.error('Failed to activate theme')
    }
  }

  const handleDuplicateTheme = async (theme: Theme) => {
    try {
      const response = await fetch(`/api/theme-generator/themes/${theme.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          action: 'duplicate',
          createdBy: 'admin'
        })
      })

      if (response.ok) {
        toast.success(`${theme.name} duplicated successfully!`)
        loadThemes()
      } else {
        toast.error('Failed to duplicate theme')
      }
    } catch (error) {
      console.error('Error duplicating theme:', error)
      toast.error('Failed to duplicate theme')
    }
  }

  const handleDeleteTheme = async (theme: Theme) => {
    if (theme.isDefault) {
      toast.error('Cannot delete default theme')
      return
    }

    if (theme.isActive) {
      toast.error('Cannot delete active theme. Please activate another theme first.')
      return
    }

    try {
      const response = await fetch(`/api/theme-generator/themes/${theme.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast.success(`${theme.name} deleted successfully!`)
        loadThemes()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to delete theme')
      }
    } catch (error) {
      console.error('Error deleting theme:', error)
      toast.error('Failed to delete theme')
    }
  }

  const handlePresetSelect = (preset: any) => {
    setValue('baseColor', preset.options.baseColor)
    setValue('style', preset.options.style)
    setValue('contrast', preset.options.contrast)
    setValue('saturation', preset.options.saturation)
    setValue('borderRadius', preset.options.borderRadius)
    setValue('fontPairing', preset.options.fontPairing)
    setValue('spacing', preset.options.spacing)
    setShowPresetDialog(false)
    setShowGeneratorDialog(true)
  }

  const filteredThemes = themes.filter(theme => 
    categoryFilter === 'all' || theme.category === categoryFilter
  )

  const columns: DataTableColumn<Theme>[] = [
    {
      key: 'name',
      title: 'Theme',
      searchable: true,
      sortable: true,
      render: (theme) => (
        <div className="flex items-center space-x-3">
          <div 
            className="w-8 h-8 rounded border"
            style={{ backgroundColor: theme.config?.colors?.primary?.['500'] || '#3b82f6' }}
          />
          <div>
            <div className="font-medium">{theme.name}</div>
            <div className="text-sm text-muted-foreground">{theme.description}</div>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      title: 'Category',
      sortable: true,
      render: (theme) => (
        <Badge variant="outline">
          {theme.category.replace('-', ' ').toUpperCase()}
        </Badge>
      )
    },
    {
      key: 'status',
      title: 'Status',
      render: (theme) => (
        <div className="flex space-x-1">
          {theme.isActive && (
            <Badge variant="default">Active</Badge>
          )}
          {theme.isDefault && (
            <Badge variant="secondary">Default</Badge>
          )}
        </div>
      )
    },
    {
      key: 'tags',
      title: 'Tags',
      render: (theme) => (
        <div className="flex flex-wrap gap-1">
          {theme.tags.slice(0, 3).map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {theme.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{theme.tags.length - 3}
            </Badge>
          )}
        </div>
      )
    },
    {
      key: 'updatedAt',
      title: 'Last Updated',
      sortable: true,
      render: (theme) => (
        <div className="text-sm">
          {new Date(theme.updatedAt).toLocaleDateString()}
        </div>
      )
    }
  ]

  const actions: DataTableAction<Theme>[] = [
    {
      label: 'Preview',
      icon: Eye,
      onClick: (theme) => {
        window.open(`/api/theme-generator/preview/${theme.id}`, '_blank')
      }
    },
    {
      label: 'Activate',
      icon: Zap,
      onClick: handleActivateTheme,
      condition: (theme) => !theme.isActive
    },
    {
      label: 'Edit',
      icon: Edit,
      onClick: (theme) => {
        window.location.href = `/admin/themes/${theme.id}/edit`
      }
    },
    {
      label: 'Duplicate',
      icon: Copy,
      onClick: handleDuplicateTheme
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: handleDeleteTheme,
      condition: (theme) => !theme.isDefault && !theme.isActive,
      variant: 'destructive'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Theme Generator</h1>
          <p className="text-muted-foreground">
            Create and manage custom themes for your Shadcn components
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => setShowPresetDialog(true)}>
            <Palette className="mr-2 h-4 w-4" />
            Presets
          </Button>
          <Dialog open={showGeneratorDialog} onOpenChange={setShowGeneratorDialog}>
            <DialogTrigger asChild>
              <Button>
                <Wand2 className="mr-2 h-4 w-4" />
                Generate Theme
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Generate Custom Theme</DialogTitle>
                <DialogDescription>
                  Create a new theme by customizing the options below
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit(onGenerateTheme)} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="baseColor">Base Color</Label>
                    <Input
                      id="baseColor"
                      type="color"
                      {...register('baseColor', { required: 'Base color is required' })}
                      defaultValue="#3b82f6"
                    />
                    {errors.baseColor && (
                      <p className="text-sm text-red-600">{errors.baseColor.message}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="style">Style</Label>
                    <Select onValueChange={(value) => setValue('style', value as any)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select style" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="minimal">Minimal</SelectItem>
                        <SelectItem value="bold">Bold</SelectItem>
                        <SelectItem value="elegant">Elegant</SelectItem>
                        <SelectItem value="modern">Modern</SelectItem>
                        <SelectItem value="playful">Playful</SelectItem>
                        <SelectItem value="professional">Professional</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="contrast">Contrast</Label>
                    <Select onValueChange={(value) => setValue('contrast', value as any)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select contrast" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="saturation">Saturation</Label>
                    <Select onValueChange={(value) => setValue('saturation', value as any)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select saturation" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="muted">Muted</SelectItem>
                        <SelectItem value="normal">Normal</SelectItem>
                        <SelectItem value="vibrant">Vibrant</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="borderRadius">Border Radius</Label>
                    <Select onValueChange={(value) => setValue('borderRadius', value as any)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select radius" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="sharp">Sharp</SelectItem>
                        <SelectItem value="rounded">Rounded</SelectItem>
                        <SelectItem value="pill">Pill</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="fontPairing">Font Pairing</Label>
                    <Select onValueChange={(value) => setValue('fontPairing', value as any)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select fonts" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="classic">Classic</SelectItem>
                        <SelectItem value="modern">Modern</SelectItem>
                        <SelectItem value="creative">Creative</SelectItem>
                        <SelectItem value="technical">Technical</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="spacing">Spacing</Label>
                    <Select onValueChange={(value) => setValue('spacing', value as any)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select spacing" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="compact">Compact</SelectItem>
                        <SelectItem value="normal">Normal</SelectItem>
                        <SelectItem value="spacious">Spacious</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowGeneratorDialog(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={generating}>
                    {generating ? 'Generating...' : 'Generate Theme'}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Presets Dialog */}
      <Dialog open={showPresetDialog} onOpenChange={setShowPresetDialog}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>Theme Presets</DialogTitle>
            <DialogDescription>
              Choose from pre-designed theme templates
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {presets.map((preset) => (
              <Card 
                key={preset.id} 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handlePresetSelect(preset)}
              >
                <CardHeader className="pb-2">
                  <div 
                    className="w-full h-20 rounded border mb-2"
                    style={{ backgroundColor: preset.options.baseColor }}
                  />
                  <CardTitle className="text-sm">{preset.name}</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-xs text-muted-foreground">{preset.description}</p>
                  <Badge variant="outline" className="mt-2 text-xs">
                    {preset.category}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </DialogContent>
      </Dialog>

      {/* Themes Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Custom Themes</CardTitle>
              <CardDescription>
                Manage your generated and custom themes
              </CardDescription>
            </div>
            
            {/* Category Filter */}
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="business">Business</SelectItem>
                <SelectItem value="creative">Creative</SelectItem>
                <SelectItem value="minimal">Minimal</SelectItem>
                <SelectItem value="bold">Bold</SelectItem>
                <SelectItem value="elegant">Elegant</SelectItem>
                <SelectItem value="modern">Modern</SelectItem>
                <SelectItem value="classic">Classic</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            data={filteredThemes}
            columns={columns}
            actions={actions}
            loading={loading}
            searchPlaceholder="Search themes by name or description..."
            emptyMessage="No themes found. Create your first theme!"
            emptyIcon={Palette}
            pageSize={20}
          />
        </CardContent>
      </Card>
    </div>
  )
}
