'use client'

// Posts System Initialization Page
// Admin interface for initializing the WordPress-style posts system

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Loader2, 
  Database, 
  FileText, 
  Tag, 
  Zap,
  RefreshCw,
  Play
} from 'lucide-react'
import { toast } from 'sonner'

interface InitializationStatus {
  isInitialized: boolean
  postTypes: boolean
  taxonomies: boolean
  sampleData: boolean
  message: string
}

export default function InitPostsPage() {
  const [status, setStatus] = useState<InitializationStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [initializing, setInitializing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState('')

  // Check initialization status
  const checkStatus = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/posts/init')
      const data = await response.json()

      if (data.success) {
        // Check individual components
        const [postTypesRes, taxonomiesRes] = await Promise.all([
          fetch('/api/post-types'),
          fetch('/api/taxonomies')
        ])

        const postTypesData = await postTypesRes.json()
        const taxonomiesData = await taxonomiesRes.json()

        const hasDefaultPostTypes = postTypesData.success && 
          postTypesData.data?.some((pt: any) => ['post', 'page', 'product'].includes(pt.name))
        
        const hasDefaultTaxonomies = taxonomiesData.success && 
          taxonomiesData.data?.some((t: any) => ['category', 'tag'].includes(t.name))

        setStatus({
          isInitialized: data.data.isInitialized,
          postTypes: hasDefaultPostTypes,
          taxonomies: hasDefaultTaxonomies,
          sampleData: false, // We'll check this separately if needed
          message: data.data.message
        })
      }
    } catch (error) {
      console.error('Error checking status:', error)
      toast.error('Failed to check initialization status')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    checkStatus()
  }, [])

  // Initialize the system
  const initializeSystem = async (action: 'full' | 'post-types' | 'taxonomies') => {
    try {
      setInitializing(true)
      setProgress(0)
      setCurrentStep('Starting initialization...')

      const response = await fetch(`/api/posts/init?action=${action}`, {
        method: 'POST'
      })

      const data = await response.json()

      if (data.success) {
        // Simulate progress for better UX
        const steps = [
          'Creating database tables...',
          'Registering post types...',
          'Setting up taxonomies...',
          'Creating sample data...',
          'Finalizing setup...'
        ]

        for (let i = 0; i < steps.length; i++) {
          setCurrentStep(steps[i])
          setProgress((i + 1) * 20)
          await new Promise(resolve => setTimeout(resolve, 500))
        }

        toast.success(data.message)
        await checkStatus() // Refresh status
      } else {
        toast.error(data.error || 'Initialization failed')
      }
    } catch (error) {
      console.error('Error initializing system:', error)
      toast.error('Failed to initialize system')
    } finally {
      setInitializing(false)
      setProgress(0)
      setCurrentStep('')
    }
  }

  const StatusIcon = ({ status }: { status: boolean }) => {
    if (status) {
      return <CheckCircle className="h-5 w-5 text-green-500" />
    }
    return <XCircle className="h-5 w-5 text-red-500" />
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Initialize Posts System</h1>
          <p className="text-muted-foreground">
            Set up the WordPress-style posts system
          </p>
        </div>
        
        <Card>
          <CardContent className="p-8 text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Checking system status...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Initialize Posts System</h1>
        <p className="text-muted-foreground">
          Set up the WordPress-style posts system with default post types, taxonomies, and sample data
        </p>
      </div>

      {/* Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            System Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          {status?.isInitialized ? (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Posts system is initialized and ready to use!
              </AlertDescription>
            </Alert>
          ) : (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Posts system needs to be initialized before you can create content.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Initialization Progress */}
      {initializing && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              Initializing System
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Progress value={progress} className="w-full" />
            <p className="text-sm text-muted-foreground">{currentStep}</p>
          </CardContent>
        </Card>
      )}

      {/* Component Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Post Types
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-muted-foreground">
                  Default post types (post, page, product)
                </div>
              </div>
              <StatusIcon status={status?.postTypes || false} />
            </div>
            {!status?.postTypes && (
              <Button 
                size="sm" 
                className="w-full mt-3"
                onClick={() => initializeSystem('post-types')}
                disabled={initializing}
              >
                Initialize Post Types
              </Button>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Tag className="h-4 w-4" />
              Taxonomies
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-muted-foreground">
                  Default taxonomies (categories, tags)
                </div>
              </div>
              <StatusIcon status={status?.taxonomies || false} />
            </div>
            {!status?.taxonomies && (
              <Button 
                size="sm" 
                className="w-full mt-3"
                onClick={() => initializeSystem('taxonomies')}
                disabled={initializing}
              >
                Initialize Taxonomies
              </Button>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Sample Data
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-muted-foreground">
                  Sample posts and categories
                </div>
              </div>
              <StatusIcon status={status?.sampleData || false} />
            </div>
            <Button 
              size="sm" 
              className="w-full mt-3"
              onClick={() => initializeSystem('full')}
              disabled={initializing}
            >
              Create Sample Data
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Initialization Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Quick Setup</h4>
              <p className="text-sm text-muted-foreground">
                Initialize everything with default settings and sample data
              </p>
              <Button 
                onClick={() => initializeSystem('full')}
                disabled={initializing}
                className="w-full"
              >
                <Play className="h-4 w-4 mr-2" />
                Full Initialization
              </Button>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Refresh Status</h4>
              <p className="text-sm text-muted-foreground">
                Check the current initialization status
              </p>
              <Button 
                variant="outline"
                onClick={checkStatus}
                disabled={loading || initializing}
                className="w-full"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Status
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Information */}
      <Card>
        <CardHeader>
          <CardTitle>What gets initialized?</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Post Types</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• <strong>Post</strong> - Blog posts and articles</li>
                <li>• <strong>Page</strong> - Static pages with page builder support</li>
                <li>• <strong>Product</strong> - E-commerce products</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">Taxonomies</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• <strong>Category</strong> - Hierarchical post categories</li>
                <li>• <strong>Tag</strong> - Flat post tags</li>
                <li>• <strong>Product Category</strong> - Product organization</li>
                <li>• <strong>Product Tag</strong> - Product tags</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">Sample Data</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Welcome blog post</li>
                <li>• Sample categories (Technology, Lifestyle, Business)</li>
                <li>• Sample tags (tutorial, guide, tips, review)</li>
                <li>• Product categories (Kids Clothing, Accessories)</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
