'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Copy, 
  Eye,
  FileText,
  Settings,
  Database,
  Layers
} from 'lucide-react'
import { ContentTypeBuilder } from '@/lib/content-type-builder/components/content-type-builder'
import { CreatePostTypeInput, PostType } from '@/lib/posts/types'
import { toast } from 'sonner'

export default function ContentTypeBuilderPage() {
  const [contentTypes, setContentTypes] = useState<PostType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedContentType, setSelectedContentType] = useState<PostType | null>(null)
  const [isBuilderOpen, setIsBuilderOpen] = useState(false)
  const [isCreating, setIsCreating] = useState(false)

  useEffect(() => {
    fetchContentTypes()
  }, [])

  const fetchContentTypes = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/content-type-builder')
      const result = await response.json()
      
      if (result.success) {
        setContentTypes(result.data || [])
      } else {
        toast.error('Failed to fetch content types')
      }
    } catch (error) {
      console.error('Error fetching content types:', error)
      toast.error('Failed to fetch content types')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateNew = () => {
    setSelectedContentType(null)
    setIsCreating(true)
    setIsBuilderOpen(true)
  }

  const handleEdit = (contentType: PostType) => {
    setSelectedContentType(contentType)
    setIsCreating(false)
    setIsBuilderOpen(true)
  }

  const handleSave = async (data: CreatePostTypeInput) => {
    try {
      const url = isCreating ? '/api/content-type-builder' : '/api/content-type-builder'
      const method = isCreating ? 'POST' : 'PUT'
      const body = isCreating ? data : { id: selectedContentType?.id, ...data }

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      })

      const result = await response.json()

      if (result.success) {
        toast.success(isCreating ? 'Content type created successfully' : 'Content type updated successfully')
        setIsBuilderOpen(false)
        fetchContentTypes()
      } else {
        toast.error(result.error || 'Failed to save content type')
      }
    } catch (error) {
      console.error('Error saving content type:', error)
      toast.error('Failed to save content type')
    }
  }

  const handleDelete = async (contentType: PostType) => {
    if (!confirm(`Are you sure you want to delete "${contentType.label}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/content-type-builder?id=${contentType.id}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Content type deleted successfully')
        fetchContentTypes()
      } else {
        toast.error(result.error || 'Failed to delete content type')
      }
    } catch (error) {
      console.error('Error deleting content type:', error)
      toast.error('Failed to delete content type')
    }
  }

  const handleDuplicate = async (contentType: PostType) => {
    const duplicateData: CreatePostTypeInput = {
      name: `${contentType.name}_copy`,
      label: `${contentType.label} Copy`,
      labelPlural: `${contentType.labelPlural} Copy`,
      description: contentType.description,
      icon: contentType.icon,
      isPublic: contentType.isPublic,
      isHierarchical: contentType.isHierarchical,
      hasArchive: contentType.hasArchive,
      supportsTitle: contentType.supportsTitle,
      supportsContent: contentType.supportsContent,
      supportsExcerpt: contentType.supportsExcerpt,
      supportsThumbnail: contentType.supportsThumbnail,
      supportsComments: contentType.supportsComments,
      supportsRevisions: contentType.supportsRevisions,
      supportsPageBuilder: contentType.supportsPageBuilder,
      menuPosition: contentType.menuPosition,
      taxonomies: [...contentType.taxonomies],
      customFields: [...contentType.customFields],
      templates: [...contentType.templates],
    }

    await handleSave(duplicateData)
  }

  const filteredContentTypes = contentTypes.filter(contentType =>
    contentType.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contentType.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contentType.description?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  if (isBuilderOpen) {
    return (
      <ContentTypeBuilder
        initialData={selectedContentType}
        onSave={handleSave}
        onPreview={(data) => {
          console.log('Preview:', data)
          toast.info('Preview functionality coming soon')
        }}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Content Type Builder</h1>
          <p className="text-gray-600">
            Create and manage custom content types with visual field builder
          </p>
        </div>
        <Button onClick={handleCreateNew}>
          <Plus className="h-4 w-4 mr-2" />
          Create Content Type
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search content types..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Database className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Types</p>
                <p className="text-2xl font-bold">{contentTypes.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Eye className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Public Types</p>
                <p className="text-2xl font-bold">
                  {contentTypes.filter(ct => ct.isPublic).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Layers className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Custom Fields</p>
                <p className="text-2xl font-bold">
                  {contentTypes.reduce((total, ct) => total + (ct.customFields?.length || 0), 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">System Types</p>
                <p className="text-2xl font-bold">
                  {contentTypes.filter(ct => ct.isSystem).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Content Types Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          // Loading skeletons
          Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : filteredContentTypes.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery ? 'No content types found' : 'No content types yet'}
            </h3>
            <p className="text-gray-500 mb-4">
              {searchQuery 
                ? 'Try adjusting your search terms'
                : 'Create your first content type to get started'
              }
            </p>
            {!searchQuery && (
              <Button onClick={handleCreateNew}>
                <Plus className="h-4 w-4 mr-2" />
                Create Content Type
              </Button>
            )}
          </div>
        ) : (
          filteredContentTypes.map((contentType) => (
            <Card key={contentType.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl">{contentType.icon || '📄'}</span>
                    <div>
                      <CardTitle className="text-lg">{contentType.label}</CardTitle>
                      <p className="text-sm text-gray-500 font-mono">{contentType.name}</p>
                    </div>
                  </div>
                  <div className="flex space-x-1">
                    {contentType.isSystem && (
                      <Badge variant="secondary">System</Badge>
                    )}
                    {contentType.isPublic && (
                      <Badge variant="outline">Public</Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                  {contentType.description || 'No description provided'}
                </p>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Custom Fields:</span>
                    <span className="font-medium">{contentType.customFields?.length || 0}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Taxonomies:</span>
                    <span className="font-medium">{contentType.taxonomies?.length || 0}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Page Builder:</span>
                    <span className="font-medium">
                      {contentType.supportsPageBuilder ? 'Yes' : 'No'}
                    </span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEdit(contentType)}
                    className="flex-1"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDuplicate(contentType)}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                  {!contentType.isSystem && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDelete(contentType)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
