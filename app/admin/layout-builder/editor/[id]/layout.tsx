"use client"

import { LayoutBuilderProvider } from "@/lib/layout-builder/context"
import { LayoutBuilderSidebar } from "@/components/admin/layout-builder-sidebar"
import { LayoutBuilderPropertiesSidebar } from "@/components/admin/layout-builder-properties-sidebar"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { useRouter } from "next/navigation"

export default function LayoutBuilderEditorLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()

  const handleBackToAdmin = () => {
    router.push('/admin/layout-builder')
  }

  return (
    <LayoutBuilderProvider>
      <SidebarProvider>
        <LayoutBuilderSidebar onBackToAdmin={handleBackToAdmin} />
        <SidebarInset>
          <div className="flex flex-1 overflow-hidden">
            <div className="flex-1 overflow-hidden">
              {children}
            </div>
            <LayoutBuilderPropertiesSidebar />
          </div>
        </SidebarInset>
      </SidebarProvider>
    </LayoutBuilderProvider>
  )
}
