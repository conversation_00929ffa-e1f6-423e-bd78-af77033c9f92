'use client'

import React, { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { useLayoutBuilder } from '@/lib/layout-builder/context'
import { LayoutBuilderEditor } from '@/lib/layout-builder/components/layout-builder-editor'
import { Layout } from '@/lib/layout-builder/types'
import { toast } from 'sonner'

export default function LayoutBuilderEditorPage() {
  const params = useParams()
  const router = useRouter()
  const { updateLayout, state, dispatch } = useLayoutBuilder()
  const [loading, setLoading] = useState(true)

  const layoutId = params.id as string

  // Load layout data
  useEffect(() => {
    const loadLayout = async () => {
      try {
        setLoading(true)

        if (layoutId === 'new') {
          // Create a new layout
          const newLayout: Layout = {
            id: '',
            name: 'New Layout',
            description: '',
            type: 'page',
            category: 'custom',
            structure: {
              main: {
                id: 'main-section',
                type: 'main',
                name: 'Main Content',
                position: 1,
                blocks: [],
                configuration: {
                  layout: 'block',
                  alignment: 'left',
                  spacing: { top: '0', right: '0', bottom: '0', left: '0' },
                  background: { type: 'color', color: 'transparent' },
                  container: {
                    maxWidth: '1200px',
                    padding: { top: '1rem', right: '1rem', bottom: '1rem', left: '1rem' },
                    margin: { top: '0', right: 'auto', bottom: '0', left: 'auto' },
                    centered: true
                  }
                },
                styling: {
                  background: { type: 'color', color: 'transparent' },
                  border: { width: '0', style: 'none', color: 'transparent', radius: '0' },
                  spacing: { top: '0', right: '0', bottom: '0', left: '0' },
                  shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' }
                },
                responsive: {
                  mobile: {
                    display: 'block',
                    width: '100%',
                    height: 'auto',
                    spacing: { top: '0', right: '0', bottom: '0', left: '0' },
                    typography: {
                      fontFamily: 'inherit',
                      fontSize: '16px',
                      fontWeight: 'normal',
                      lineHeight: '1.5',
                      letterSpacing: '0',
                      textAlign: 'left',
                      textTransform: 'none'
                    }
                  },
                  tablet: {
                    display: 'block',
                    width: '100%',
                    height: 'auto',
                    spacing: { top: '0', right: '0', bottom: '0', left: '0' },
                    typography: {
                      fontFamily: 'inherit',
                      fontSize: '16px',
                      fontWeight: 'normal',
                      lineHeight: '1.5',
                      letterSpacing: '0',
                      textAlign: 'left',
                      textTransform: 'none'
                    }
                  },
                  desktop: {
                    display: 'block',
                    width: '100%',
                    height: 'auto',
                    spacing: { top: '0', right: '0', bottom: '0', left: '0' },
                    typography: {
                      fontFamily: 'inherit',
                      fontSize: '16px',
                      fontWeight: 'normal',
                      lineHeight: '1.5',
                      letterSpacing: '0',
                      textAlign: 'left',
                      textTransform: 'none'
                    }
                  },
                  large: {
                    display: 'block',
                    width: '100%',
                    height: 'auto',
                    spacing: { top: '0', right: '0', bottom: '0', left: '0' },
                    typography: {
                      fontFamily: 'inherit',
                      fontSize: '16px',
                      fontWeight: 'normal',
                      lineHeight: '1.5',
                      letterSpacing: '0',
                      textAlign: 'left',
                      textTransform: 'none'
                    }
                  }
                },
                isVisible: true
              }
            },
            styling: {
              theme: 'default',
              colorScheme: 'light',
              typography: {
                fontFamily: 'Inter, sans-serif',
                fontSize: '16px',
                lineHeight: '1.5',
                fontWeight: 'normal',
                letterSpacing: '0',
                textAlign: 'left',
                textTransform: 'none'
              },
              spacing: { top: '0', right: '0', bottom: '0', left: '0' },
              colors: {
                primary: '#000000',
                secondary: '#666666',
                accent: '#3b82f6',
                background: '#ffffff',
                text: '#000000',
                border: '#e5e5e5'
              }
            },
            responsive: {
              mobile: {
                display: 'block',
                width: '100%',
                height: 'auto',
                spacing: { top: '0', right: '0', bottom: '0', left: '0' },
                typography: {
                  fontFamily: 'inherit',
                  fontSize: '14px',
                  fontWeight: 'normal',
                  lineHeight: '1.5',
                  letterSpacing: '0',
                  textAlign: 'left',
                  textTransform: 'none'
                }
              },
              tablet: {
                display: 'block',
                width: '100%',
                height: 'auto',
                spacing: { top: '0', right: '0', bottom: '0', left: '0' },
                typography: {
                  fontFamily: 'inherit',
                  fontSize: '15px',
                  fontWeight: 'normal',
                  lineHeight: '1.5',
                  letterSpacing: '0',
                  textAlign: 'left',
                  textTransform: 'none'
                }
              },
              desktop: {
                display: 'block',
                width: '100%',
                height: 'auto',
                spacing: { top: '0', right: '0', bottom: '0', left: '0' },
                typography: {
                  fontFamily: 'inherit',
                  fontSize: '16px',
                  fontWeight: 'normal',
                  lineHeight: '1.5',
                  letterSpacing: '0',
                  textAlign: 'left',
                  textTransform: 'none'
                }
              },
              large: {
                display: 'block',
                width: '100%',
                height: 'auto',
                spacing: { top: '0', right: '0', bottom: '0', left: '0' },
                typography: {
                  fontFamily: 'inherit',
                  fontSize: '16px',
                  fontWeight: 'normal',
                  lineHeight: '1.5',
                  letterSpacing: '0',
                  textAlign: 'left',
                  textTransform: 'none'
                }
              }
            },
            conditions: {},
            isTemplate: false,
            isSystem: false,
            isActive: true,
            usageCount: 0,
            tags: [],
            createdAt: new Date(),
            updatedAt: new Date()
          }

          updateLayout(newLayout)
        } else {
          // Load existing layout
          const response = await fetch(`/api/layout-builder/layouts/${layoutId}`)
          if (!response.ok) {
            throw new Error('Failed to load layout')
          }

          const data = await response.json()
          if (data.success) {
            updateLayout(data.data)
          } else {
            throw new Error(data.error || 'Failed to load layout')
          }
        }
      } catch (error) {
        console.error('Error loading layout:', error)
        toast.error('Failed to load layout')
        router.push('/admin/layout-builder')
      } finally {
        setLoading(false)
      }
    }

    loadLayout()
  }, [layoutId, updateLayout, router])

  // Save layout
  const handleSave = async () => {
    try {
      dispatch({ type: 'SET_SAVING', payload: true })

      const layoutData = {
        ...state.layout,
        updatedAt: new Date()
      }

      let response
      if (layoutId === 'new' || !state.layout.id) {
        // Create new layout
        response = await fetch('/api/layout-builder/layouts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(layoutData),
        })
      } else {
        // Update existing layout
        response = await fetch(`/api/layout-builder/layouts/${layoutId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(layoutData),
        })
      }

      if (!response.ok) {
        throw new Error('Failed to save layout')
      }

      const data = await response.json()
      if (data.success) {
        updateLayout(data.data)
        toast.success('Layout saved successfully')

        // If this was a new layout, redirect to the edit page with the new ID
        if (layoutId === 'new' && data.data.id) {
          router.replace(`/admin/layout-builder/editor/${data.data.id}`)
        }
      } else {
        throw new Error(data.error || 'Failed to save layout')
      }
    } catch (error) {
      console.error('Error saving layout:', error)
      toast.error('Failed to save layout')
    } finally {
      dispatch({ type: 'SET_SAVING', payload: false })
    }
  }

  // Handle preview
  const handlePreview = () => {
    // Open preview in new tab
    if (state.layout.id) {
      window.open(`/preview/layout/${state.layout.id}`, '_blank')
    } else {
      toast.error('Please save the layout first to preview')
    }
  }

  // Handle back to admin
  const handleBack = () => {
    if (state.hasUnsavedChanges) {
      const confirmed = window.confirm(
        'You have unsaved changes. Are you sure you want to leave?'
      )
      if (!confirmed) return
    }

    router.push('/admin/layout-builder')
  }

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-sm text-muted-foreground">Loading layout...</p>
        </div>
      </div>
    )
  }

  return (
    <LayoutBuilderEditor
      initialLayout={state.layout}
      onSave={handleSave}
      onPreview={handlePreview}
      onBack={handleBack}
    />
  )
}