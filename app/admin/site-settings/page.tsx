'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { toast } from '@/components/ui/use-toast'
import { Home, Settings, Globe, Shield, BarChart3, RefreshCw, ExternalLink } from 'lucide-react'

interface SiteSettings {
  id: string
  homepageId?: string
  homepageSlug?: string
  siteName: string
  siteDescription: string
  siteUrl: string
  logoUrl?: string
  faviconUrl?: string
  socialMedia: {
    facebook?: string
    instagram?: string
    twitter?: string
    youtube?: string
  }
  seo: {
    defaultTitle: string
    defaultDescription: string
    defaultKeywords: string[]
    ogImage?: string
  }
  ecommerce: {
    currency: string
    taxRate: number
    freeShippingThreshold?: number
    defaultShippingCost: number
  }
  maintenance: {
    enabled: boolean
    message?: string
    allowedIps?: string[]
  }
  analytics: {
    googleAnalyticsId?: string
    facebookPixelId?: string
    hotjarId?: string
  }
}

interface AvailablePage {
  id: string
  title: string
  slug: string
  type: string
}

export default function SiteSettingsPage() {
  const [settings, setSettings] = useState<SiteSettings | null>(null)
  const [availablePages, setAvailablePages] = useState<AvailablePage[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    fetchSettings()
    fetchHomepageOptions()
  }, [])

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/site-settings')
      if (response.ok) {
        const data = await response.json()
        setSettings(data.data)
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error)
      toast({
        title: 'Error',
        description: 'Failed to load site settings',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchHomepageOptions = async () => {
    try {
      const response = await fetch('/api/admin/site-settings/homepage')
      if (response.ok) {
        const data = await response.json()
        setAvailablePages(data.data.availablePages || [])
      }
    } catch (error) {
      console.error('Failed to fetch homepage options:', error)
    }
  }

  const updateSettings = async (updatedSettings: Partial<SiteSettings>) => {
    try {
      setSaving(true)
      const response = await fetch('/api/admin/site-settings', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updatedSettings)
      })

      if (response.ok) {
        const data = await response.json()
        setSettings(data.data)
        toast({
          title: 'Success',
          description: 'Site settings updated successfully'
        })
      } else {
        throw new Error('Failed to update settings')
      }
    } catch (error) {
      console.error('Failed to update settings:', error)
      toast({
        title: 'Error',
        description: 'Failed to update site settings',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  const setHomepage = async (pageId: string) => {
    try {
      const response = await fetch('/api/admin/site-settings/homepage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ pageId })
      })

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Homepage set successfully'
        })
        fetchSettings()
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Failed to set homepage')
      }
    } catch (error) {
      console.error('Failed to set homepage:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to set homepage',
        variant: 'destructive'
      })
    }
  }

  const clearHomepage = async () => {
    try {
      const response = await fetch('/api/admin/site-settings/homepage', {
        method: 'DELETE'
      })

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Homepage cleared successfully'
        })
        fetchSettings()
      } else {
        throw new Error('Failed to clear homepage')
      }
    } catch (error) {
      console.error('Failed to clear homepage:', error)
      toast({
        title: 'Error',
        description: 'Failed to clear homepage',
        variant: 'destructive'
      })
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading site settings...</div>
      </div>
    )
  }

  if (!settings) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Failed to load site settings</div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Site Settings</h1>
          <p className="text-muted-foreground">Manage your website configuration and homepage</p>
        </div>
        <Button onClick={fetchSettings} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="homepage" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="homepage" className="flex items-center space-x-2">
            <Home className="h-4 w-4" />
            <span>Homepage</span>
          </TabsTrigger>
          <TabsTrigger value="general" className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span>General</span>
          </TabsTrigger>
          <TabsTrigger value="seo" className="flex items-center space-x-2">
            <Globe className="h-4 w-4" />
            <span>SEO</span>
          </TabsTrigger>
          <TabsTrigger value="maintenance" className="flex items-center space-x-2">
            <Shield className="h-4 w-4" />
            <span>Maintenance</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="homepage">
          <Card>
            <CardHeader>
              <CardTitle>Homepage Configuration</CardTitle>
              <CardDescription>
                Choose which page should be displayed as your website's homepage
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Current Homepage */}
              <div className="space-y-2">
                <Label>Current Homepage</Label>
                {settings.homepageId ? (
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">
                        {availablePages.find(p => p.id === settings.homepageId)?.title || 'Unknown Page'}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        /{settings.homepageSlug}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="default">Active</Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open('/', '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearHomepage}
                      >
                        Clear
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="p-3 border rounded-lg text-muted-foreground">
                    No custom homepage set. Using default route.
                  </div>
                )}
              </div>

              {/* Available Pages */}
              <div className="space-y-2">
                <Label>Set Homepage</Label>
                <Select onValueChange={setHomepage}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a page to set as homepage" />
                  </SelectTrigger>
                  <SelectContent>
                    {availablePages.map((page) => (
                      <SelectItem key={page.id} value={page.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{page.title}</span>
                          <Badge variant="outline" className="ml-2">
                            {page.type}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Only published pages are available for selection
                </p>
              </div>

              {/* Homepage Preview */}
              {settings.homepageId && (
                <div className="space-y-2">
                  <Label>Preview</Label>
                  <div className="border rounded-lg p-4 bg-muted/50">
                    <p className="text-sm text-muted-foreground mb-2">
                      Your homepage will be accessible at:
                    </p>
                    <div className="flex items-center space-x-2">
                      <code className="bg-background px-2 py-1 rounded text-sm">
                        {settings.siteUrl}/
                      </code>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open('/', '_blank')}
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Visit
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>Basic site information and branding</CardDescription>
            </CardHeader>
            <CardContent>
              <form className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="siteName">Site Name</Label>
                    <Input
                      id="siteName"
                      value={settings.siteName}
                      onChange={(e) => setSettings(prev => prev ? { ...prev, siteName: e.target.value } : null)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="siteUrl">Site URL</Label>
                    <Input
                      id="siteUrl"
                      value={settings.siteUrl}
                      onChange={(e) => setSettings(prev => prev ? { ...prev, siteUrl: e.target.value } : null)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="siteDescription">Site Description</Label>
                  <Textarea
                    id="siteDescription"
                    value={settings.siteDescription}
                    onChange={(e) => setSettings(prev => prev ? { ...prev, siteDescription: e.target.value } : null)}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="logoUrl">Logo URL</Label>
                    <Input
                      id="logoUrl"
                      value={settings.logoUrl || ''}
                      onChange={(e) => setSettings(prev => prev ? { ...prev, logoUrl: e.target.value } : null)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="faviconUrl">Favicon URL</Label>
                    <Input
                      id="faviconUrl"
                      value={settings.faviconUrl || ''}
                      onChange={(e) => setSettings(prev => prev ? { ...prev, faviconUrl: e.target.value } : null)}
                    />
                  </div>
                </div>

                <Button onClick={() => updateSettings(settings)} disabled={saving}>
                  {saving ? 'Saving...' : 'Save Changes'}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="maintenance">
          <Card>
            <CardHeader>
              <CardTitle>Maintenance Mode</CardTitle>
              <CardDescription>Control site accessibility during maintenance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="maintenanceEnabled">Enable Maintenance Mode</Label>
                    <p className="text-sm text-muted-foreground">
                      When enabled, only allowed IPs can access the site
                    </p>
                  </div>
                  <Switch
                    id="maintenanceEnabled"
                    checked={settings.maintenance.enabled}
                    onCheckedChange={(checked) => 
                      setSettings(prev => prev ? {
                        ...prev,
                        maintenance: { ...prev.maintenance, enabled: checked }
                      } : null)
                    }
                  />
                </div>

                {settings.maintenance.enabled && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="maintenanceMessage">Maintenance Message</Label>
                      <Textarea
                        id="maintenanceMessage"
                        value={settings.maintenance.message || ''}
                        onChange={(e) => setSettings(prev => prev ? {
                          ...prev,
                          maintenance: { ...prev.maintenance, message: e.target.value }
                        } : null)}
                        placeholder="We're currently performing maintenance. Please check back soon."
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="allowedIps">Allowed IP Addresses</Label>
                      <Textarea
                        id="allowedIps"
                        value={(settings.maintenance.allowedIps || []).join('\n')}
                        onChange={(e) => setSettings(prev => prev ? {
                          ...prev,
                          maintenance: { 
                            ...prev.maintenance, 
                            allowedIps: e.target.value.split('\n').filter(ip => ip.trim())
                          }
                        } : null)}
                        placeholder="Enter IP addresses (one per line)"
                      />
                    </div>
                  </>
                )}

                <Button onClick={() => updateSettings(settings)} disabled={saving}>
                  {saving ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Add other tabs content here */}
      </Tabs>
    </div>
  )
}
