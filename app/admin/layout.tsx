"use client";

import { usePathname } from "next/navigation";
import { NotificationCenter } from "@/components/admin/notification-center";
import { AdminSidebar } from "@/components/admin/admin-sidebar";
import { AdminBreadcrumb } from "@/components/admin/admin-breadcrumb";
import { AdminAuthProvider } from "@/components/admin/admin-auth-provider";
import { EditorLayout } from "@/components/admin/editor-layout";
import {
  SidebarProvider,
  SidebarInset,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { useAdminUI } from "@/stores/use-admin-ui";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const { isEditorMode } = useAdminUI();

  // Check if we're on the login page
  const isLoginPage = pathname === "/admin/login";

  // Check if we're in a dynamic Page Builder editor route (which has its own layout)
  const isPageBuilderEditorRoute = pathname?.match(
    /^\/admin\/page-builder\/[^\/]+$/
  );

  // If it's the login page, render without authentication wrapper
  if (isLoginPage) {
    return <>{children}</>;
  }

  // If it's a Page Builder editor route, don't render the admin sidebar
  // The Page Builder layout will handle its own sidebar
  if (isPageBuilderEditorRoute) {
    return <AdminAuthProvider>{children}</AdminAuthProvider>;
  }

  return (
    <AdminAuthProvider>
      <SidebarProvider>
        <AdminSidebar />
        {isEditorMode ? (
          <EditorLayout
            leftPanel={
              <div className="flex h-full flex-col">
                {/* Add your left panel content here */}
                <div className="flex-1 overflow-auto p-4">
                  Left Panel Content
                </div>
              </div>
            }
            rightPanel={
              <div className="flex h-full flex-col">
                {/* Add your right panel content here */}
                <div className="flex-1 overflow-auto p-4">
                  Right Panel Content
                </div>
              </div>
            }
          >
            <div className="flex flex-1 flex-col">{children}</div>
          </EditorLayout>
        ) : (
          <>
            <Separator orientation="horizontal" />
            <main className="flex min-h-screen flex-1">
              <SidebarInset>
                <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
                  <div className="flex items-center gap-2 px-4">
                    <SidebarTrigger className="-ml-1" />
                    <Separator orientation="vertical" className="mr-2 h-4" />
                    <AdminBreadcrumb />
                  </div>
                  <div className="ml-auto flex items-center gap-2 px-4">
                    <NotificationCenter />
                    <div className="text-sm text-muted-foreground">
                      Coco Milk Kids Admin
                    </div>
                  </div>
                </header>
                <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
                  {children}
                </div>
              </SidebarInset>
            </main>
          </>
        )}
      </SidebarProvider>
    </AdminAuthProvider>
  );
}
