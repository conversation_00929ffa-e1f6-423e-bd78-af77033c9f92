'use client'

// Edit Blog Post Page
// Interface for editing existing blog posts

import React, { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { PostEditor } from '@/lib/posts/components/post-editor'
import { Post, UpdatePostInput } from '@/lib/posts/types'
import { toast } from 'sonner'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

export default function EditBlogPostPage() {
  const router = useRouter()
  const params = useParams()
  const [post, setPost] = useState<Post | null>(null)
  const [loading, setLoading] = useState(true)
  const [isLoading, setIsLoading] = useState(false)

  const postId = params.id as string

  // Fetch post data
  useEffect(() => {
    const fetchPost = async () => {
      try {
        const response = await fetch(`/api/posts/${postId}?include=meta,taxonomyTerms,blocks`)
        const result = await response.json()

        if (result.success) {
          setPost(result.data)
        } else {
          toast.error('Post not found')
          router.push('/admin/blog')
        }
      } catch (error) {
        console.error('Error fetching post:', error)
        toast.error('Failed to load post')
        router.push('/admin/blog')
      } finally {
        setLoading(false)
      }
    }

    if (postId) {
      fetchPost()
    }
  }, [postId, router])

  const handleSave = async (data: UpdatePostInput) => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/posts/${postId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Post updated successfully!')
        setPost(result.data) // Update local state
      } else {
        toast.error(result.error || 'Failed to update post')
      }
    } catch (error) {
      console.error('Error updating post:', error)
      toast.error('Failed to update post')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePreview = () => {
    if (post) {
      // Open post preview in new tab
      window.open(`/blog/${post.slug}?preview=true`, '_blank')
    }
  }

  const handleDelete = async () => {
    if (!post) return

    const confirmed = confirm(
      `Are you sure you want to delete "${post.title}"? This action cannot be undone.`
    )

    if (confirmed) {
      try {
        setIsLoading(true)

        const response = await fetch(`/api/posts/${postId}`, {
          method: 'DELETE',
        })

        const result = await response.json()

        if (result.success) {
          toast.success('Post deleted successfully!')
          router.push('/admin/blog')
        } else {
          toast.error(result.error || 'Failed to delete post')
        }
      } catch (error) {
        console.error('Error deleting post:', error)
        toast.error('Failed to delete post')
      } finally {
        setIsLoading(false)
      }
    }
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-28" />
            <Skeleton className="h-10 w-20" />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Content Skeleton */}
          <div className="lg:col-span-3">
            <Card>
              <CardContent className="p-6 space-y-6">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-64 w-full" />
                <Skeleton className="h-24 w-full" />
              </CardContent>
            </Card>
          </div>

          {/* Sidebar Skeleton */}
          <div className="space-y-6">
            <Card>
              <CardContent className="p-4 space-y-4">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 space-y-4">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-32 w-full" />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  if (!post) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-2">Post Not Found</h2>
            <p className="text-muted-foreground mb-4">
              The post you're looking for doesn't exist or has been deleted.
            </p>
            <button
              onClick={() => router.push('/admin/blog')}
              className="text-primary hover:underline"
            >
              Back to Blog Posts
            </button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div>
      <PostEditor
        post={post}
        postType="post"
        onSave={handleSave}
        onPreview={handlePreview}
        onDelete={handleDelete}
        isLoading={isLoading}
      />
    </div>
  )
}
