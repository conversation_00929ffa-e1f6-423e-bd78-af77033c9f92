'use client'

// New Blog Post Creation Page
// Interface for creating new blog posts using the WordPress-style editor

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { PostEditor } from '@/lib/posts/components/post-editor'
import { CreatePostInput } from '@/lib/posts/types'
import { toast } from 'sonner'

export default function NewBlogPostPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSave = async (data: CreatePostInput) => {
    try {
      setIsLoading(true)
      
      // Add author information (in a real app, this would come from auth)
      const postData = {
        ...data,
        authorName: 'Admin User', // This should come from authenticated user
        authorEmail: '<EMAIL>', // This should come from authenticated user
      }

      const response = await fetch('/api/posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(postData),
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Post created successfully!')
        
        // Redirect to edit page or back to list
        if (data.status === 'published') {
          router.push('/admin/blog')
        } else {
          router.push(`/admin/blog/edit/${result.data.id}`)
        }
      } else {
        toast.error(result.error || 'Failed to create post')
      }
    } catch (error) {
      console.error('Error creating post:', error)
      toast.error('Failed to create post')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePreview = () => {
    // In a real implementation, this would open a preview modal or new tab
    toast.info('Preview functionality would be implemented here')
  }

  return (
    <div>
      <PostEditor
        postType="post"
        onSave={handleSave}
        onPreview={handlePreview}
        isLoading={isLoading}
      />
    </div>
  )
}
