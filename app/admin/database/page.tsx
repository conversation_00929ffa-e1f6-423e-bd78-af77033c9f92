'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Copy, 
  Eye,
  Database,
  Table,
  Download,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'
import { SchemaDesigner } from '@/lib/database/components/schema-designer'
import { DatabaseSchema } from '@/lib/posts/types'
import { generateId } from '@/lib/page-builder/utils'
import { toast } from 'sonner'

export default function DatabasePage() {
  const [schemas, setSchemas] = useState<DatabaseSchema[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedSchema, setSelectedSchema] = useState<DatabaseSchema | null>(null)
  const [isDesignerO<PERSON>, setIsDesignerOpen] = useState(false)
  const [isCreating, setIsCreating] = useState(false)

  useEffect(() => {
    fetchSchemas()
  }, [])

  const fetchSchemas = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/database/schemas')
      const result = await response.json()
      
      if (result.success) {
        setSchemas(result.data || [])
      } else {
        toast.error('Failed to fetch schemas')
      }
    } catch (error) {
      console.error('Error fetching schemas:', error)
      toast.error('Failed to fetch schemas')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateNew = () => {
    const newSchema: DatabaseSchema = {
      id: generateId(),
      name: 'New Schema',
      version: '1.0.0',
      description: '',
      tables: [],
      views: [],
      functions: [],
      triggers: [],
      migrations: [],
      metadata: {
        version: '1.0.0',
        compatibility: [],
        features: [],
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    setSelectedSchema(newSchema)
    setIsCreating(true)
    setIsDesignerOpen(true)
  }

  const handleEdit = (schema: DatabaseSchema) => {
    setSelectedSchema(schema)
    setIsCreating(false)
    setIsDesignerOpen(true)
  }

  const handleSave = async (schema: DatabaseSchema) => {
    try {
      const url = isCreating ? '/api/database/schemas' : `/api/database/schemas/${schema.id}`
      const method = isCreating ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(schema)
      })

      const result = await response.json()

      if (result.success) {
        toast.success(isCreating ? 'Schema created successfully' : 'Schema updated successfully')
        setIsDesignerOpen(false)
        fetchSchemas()
      } else {
        toast.error(result.error || 'Failed to save schema')
      }
    } catch (error) {
      console.error('Error saving schema:', error)
      toast.error('Failed to save schema')
    }
  }

  const handleDelete = async (schema: DatabaseSchema) => {
    if (!confirm(`Are you sure you want to delete "${schema.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/database/schemas/${schema.id}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Schema deleted successfully')
        fetchSchemas()
      } else {
        toast.error(result.error || 'Failed to delete schema')
      }
    } catch (error) {
      console.error('Error deleting schema:', error)
      toast.error('Failed to delete schema')
    }
  }

  const handleDuplicate = async (schema: DatabaseSchema) => {
    const duplicateData = {
      ...schema,
      id: generateId(),
      name: `${schema.name} Copy`,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    await handleSave(duplicateData)
  }

  const handleExport = async (schema: DatabaseSchema, format: string) => {
    try {
      const response = await fetch('/api/database/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          schema,
          options: {
            target: format,
            includeComments: true,
            includeIndexes: true,
            includeConstraints: true,
            includeSeeds: false,
            formatOutput: true,
          }
        })
      })

      const result = await response.json()

      if (result.success) {
        // Download the generated code
        const blob = new Blob([result.data.code], { type: 'text/plain' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${schema.name}.${format === 'typescript' ? 'ts' : format}`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        
        toast.success(`Schema exported as ${format.toUpperCase()}`)
      } else {
        toast.error(result.error || 'Failed to export schema')
      }
    } catch (error) {
      console.error('Error exporting schema:', error)
      toast.error('Failed to export schema')
    }
  }

  const filteredSchemas = schemas.filter(schema =>
    schema.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    schema.description?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  if (isDesignerOpen && selectedSchema) {
    return (
      <SchemaDesigner
        initialSchema={selectedSchema}
        onSave={handleSave}
        onExport={handleExport}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Database Schema Generator</h1>
          <p className="text-gray-600">
            Visual database design and schema generation tool
          </p>
        </div>
        <Button onClick={handleCreateNew}>
          <Plus className="h-4 w-4 mr-2" />
          Create Schema
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search schemas..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Database className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Schemas</p>
                <p className="text-2xl font-bold">{schemas.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Table className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Tables</p>
                <p className="text-2xl font-bold">
                  {schemas.reduce((total, schema) => total + schema.tables.length, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Valid Schemas</p>
                <p className="text-2xl font-bold">
                  {schemas.filter(schema => schema.tables.length > 0).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Download className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Exports</p>
                <p className="text-2xl font-bold">0</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Schemas Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          // Loading skeletons
          Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : filteredSchemas.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <Database className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery ? 'No schemas found' : 'No schemas yet'}
            </h3>
            <p className="text-gray-500 mb-4">
              {searchQuery 
                ? 'Try adjusting your search terms'
                : 'Create your first database schema to get started'
              }
            </p>
            {!searchQuery && (
              <Button onClick={handleCreateNew}>
                <Plus className="h-4 w-4 mr-2" />
                Create Schema
              </Button>
            )}
          </div>
        ) : (
          filteredSchemas.map((schema) => (
            <Card key={schema.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{schema.name}</CardTitle>
                    <p className="text-sm text-gray-500">v{schema.version}</p>
                  </div>
                  <Badge variant="outline">
                    {schema.tables.length} tables
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent>
                <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                  {schema.description || 'No description provided'}
                </p>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Tables:</span>
                    <span className="font-medium">{schema.tables.length}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Views:</span>
                    <span className="font-medium">{schema.views.length}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Last Updated:</span>
                    <span className="font-medium">
                      {new Date(schema.updatedAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEdit(schema)}
                    className="flex-1"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDuplicate(schema)}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleExport(schema, 'prisma')}
                  >
                    <Download className="h-3 w-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDelete(schema)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
