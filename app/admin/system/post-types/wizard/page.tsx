'use client'

// Post Type Creation Wizard Page
// Guided step-by-step interface for creating custom post types

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { PostTypeWizard } from '@/lib/posts/components/post-type-wizard'
import { CreatePostTypeInput } from '@/lib/posts/types'
import { toast } from 'sonner'

export default function PostTypeWizardPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleComplete = async (data: CreatePostTypeInput) => {
    try {
      setIsLoading(true)
      
      // Create the post type
      const response = await fetch('/api/post-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (result.success) {
        // Create suggested taxonomies if they don't exist
        const taxonomiesToCreate = [
          {
            name: `${data.name}_category`,
            label: `${data.label} Category`,
            labelPlural: `${data.label} Categories`,
            description: `Categories for ${data.labelPlural?.toLowerCase()}`,
            isHierarchical: true,
            postTypes: [data.name]
          },
          {
            name: `${data.name}_tag`,
            label: `${data.label} Tag`,
            labelPlural: `${data.label} Tags`,
            description: `Tags for ${data.labelPlural?.toLowerCase()}`,
            isHierarchical: false,
            postTypes: [data.name]
          }
        ]

        // Create taxonomies
        for (const taxonomy of taxonomiesToCreate) {
          try {
            await fetch('/api/taxonomies', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(taxonomy),
            })
          } catch (error) {
            console.error('Error creating taxonomy:', error)
          }
        }

        toast.success(`Post type "${data.label}" created successfully!`)
        router.push('/admin/post-types')
      } else {
        toast.error(result.error || 'Failed to create post type')
      }
    } catch (error) {
      console.error('Error creating post type:', error)
      toast.error('Failed to create post type')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    router.push('/admin/post-types')
  }

  return (
    <div>
      <PostTypeWizard
        onComplete={handleComplete}
        onCancel={handleCancel}
        isLoading={isLoading}
      />
    </div>
  )
}
