'use client'

// New Post Type Creation Page
// Interface for creating new custom post types

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { PostTypeBuilder } from '@/lib/posts/components/post-type-builder'
import { CreatePostTypeInput } from '@/lib/posts/types'
import { toast } from 'sonner'

export default function NewPostTypePage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSave = async (data: CreatePostTypeInput) => {
    try {
      setIsLoading(true)
      
      const response = await fetch('/api/post-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Post type created successfully!')
        router.push('/admin/post-types')
      } else {
        toast.error(result.error || 'Failed to create post type')
      }
    } catch (error) {
      console.error('Error creating post type:', error)
      toast.error('Failed to create post type')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePreview = () => {
    // In a real implementation, this would show a preview of how the post type would look
    toast.info('Preview functionality would show how this post type will appear in the admin')
  }

  return (
    <div>
      <PostTypeBuilder
        onSave={handleSave}
        onPreview={handlePreview}
        isLoading={isLoading}
      />
    </div>
  )
}
