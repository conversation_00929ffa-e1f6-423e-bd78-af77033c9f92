'use client'

// Edit Post Type Page
// Interface for editing existing custom post types

import React, { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { PostTypeBuilder } from '@/lib/posts/components/post-type-builder'
import { PostType, CreatePostTypeInput } from '@/lib/posts/types'
import { toast } from 'sonner'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

export default function EditPostTypePage() {
  const router = useRouter()
  const params = useParams()
  const [postType, setPostType] = useState<PostType | null>(null)
  const [loading, setLoading] = useState(true)
  const [isLoading, setIsLoading] = useState(false)

  const postTypeName = params.name as string

  // Fetch post type data
  useEffect(() => {
    const fetchPostType = async () => {
      try {
        const response = await fetch(`/api/post-types/${postTypeName}`)
        const result = await response.json()

        if (result.success) {
          setPostType(result.data)
        } else {
          toast.error('Post type not found')
          router.push('/admin/post-types')
        }
      } catch (error) {
        console.error('Error fetching post type:', error)
        toast.error('Failed to load post type')
        router.push('/admin/post-types')
      } finally {
        setLoading(false)
      }
    }

    if (postTypeName) {
      fetchPostType()
    }
  }, [postTypeName, router])

  const handleSave = async (data: CreatePostTypeInput) => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/post-types/${postTypeName}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Post type updated successfully!')
        setPostType(result.data) // Update local state
      } else {
        toast.error(result.error || 'Failed to update post type')
      }
    } catch (error) {
      console.error('Error updating post type:', error)
      toast.error('Failed to update post type')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePreview = () => {
    if (postType) {
      // Open preview of posts of this type
      window.open(`/admin/posts?postType=${postType.name}`, '_blank')
    }
  }

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-32" />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content Skeleton */}
          <div className="lg:col-span-2">
            <Card>
              <CardContent className="p-6 space-y-6">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-10 w-full" />
                <div className="grid grid-cols-2 gap-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar Skeleton */}
          <div className="space-y-6">
            <Card>
              <CardContent className="p-4 space-y-4">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 space-y-4">
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-16 w-full" />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  if (!postType) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-2">Post Type Not Found</h2>
            <p className="text-muted-foreground mb-4">
              The post type you're looking for doesn't exist or has been deleted.
            </p>
            <button
              onClick={() => router.push('/admin/post-types')}
              className="text-primary hover:underline"
            >
              Back to Post Types
            </button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Check if this is a system post type
  if (postType.isSystem) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-2">System Post Type</h2>
            <p className="text-muted-foreground mb-4">
              System post types cannot be edited to maintain application stability.
            </p>
            <div className="flex items-center justify-center gap-4">
              <button
                onClick={() => router.push('/admin/post-types')}
                className="text-primary hover:underline"
              >
                Back to Post Types
              </button>
              <button
                onClick={() => window.open(`/admin/posts?postType=${postType.name}`, '_blank')}
                className="text-primary hover:underline"
              >
                View Posts
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div>
      <PostTypeBuilder
        postType={postType}
        onSave={handleSave}
        onPreview={handlePreview}
        isLoading={isLoading}
      />
    </div>
  )
}
