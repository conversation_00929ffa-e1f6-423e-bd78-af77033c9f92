'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Plus, FileText, Eye, Edit, Trash2 } from 'lucide-react'
import { toast } from 'sonner'
import Link from 'next/link'

interface PageListItem {
  id: string
  title: string
  slug: string
  status: string
  type: string
  blockCount: number
  updatedAt: string
}

export default function PageBuilderPage() {
  const [pages, setPages] = useState<PageListItem[]>([])
  const [loading, setLoading] = useState(true)
  const [showNewPageDialog, setShowNewPageDialog] = useState(false)

  // New page form state
  const [newPageTitle, setNewPageTitle] = useState('')
  const [newPageSlug, setNewPageSlug] = useState('')
  const [newPageType, setNewPageType] = useState('custom')
  const [newPageDescription, setNewPageDescription] = useState('')

  // Fetch pages list
  const fetchPages = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/e-commerce/pages')
      const data = await response.json()

      if (data.success) {
        setPages(data.data.pages)
      } else {
        toast.error('Failed to fetch pages')
      }
    } catch (error) {
      console.error('Error fetching pages:', error)
      toast.error('Failed to fetch pages')
    } finally {
      setLoading(false)
    }
  }

  // Navigate to page editor
  const loadPage = (pageId: string) => {
    window.location.href = `/admin/page-builder/${pageId}`
  }

  // Create new page
  const createNewPage = async () => {
    if (!newPageTitle || !newPageSlug) {
      toast.error('Title and slug are required')
      return
    }

    try {
      const response = await fetch('/api/e-commerce/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: newPageTitle,
          slug: newPageSlug,
          description: newPageDescription,
          type: newPageType,
          status: 'draft',
        }),
      })

      const data = await response.json()

      if (data.success) {
        setShowNewPageDialog(false)
        setNewPageTitle('')
        setNewPageSlug('')
        setNewPageDescription('')
        setNewPageType('custom')
        toast.success('Page created successfully')
        fetchPages()
        // Navigate to the new page editor
        window.location.href = `/admin/page-builder/${data.data.id}`
      } else {
        toast.error(data.error || 'Failed to create page')
      }
    } catch (error) {
      console.error('Error creating page:', error)
      toast.error('Failed to create page')
    }
  }



  // Delete page
  const deletePage = async (pageId: string) => {
    if (!confirm('Are you sure you want to delete this page?')) return

    try {
      const response = await fetch(`/api/e-commerce/pages/${pageId}`, {
        method: 'DELETE',
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Page deleted successfully')
        fetchPages()
      } else {
        toast.error(data.error || 'Failed to delete page')
      }
    } catch (error) {
      console.error('Error deleting page:', error)
      toast.error('Failed to delete page')
    }
  }

  // Generate slug from title
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  useEffect(() => {
    fetchPages()
  }, [])

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold">Page Builder</h1>
          <p className="text-muted-foreground">
            Create and manage custom pages for your e-commerce store
          </p>
        </div>

        <Dialog open={showNewPageDialog} onOpenChange={setShowNewPageDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Page
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Page</DialogTitle>
              <DialogDescription>
                Create a new custom page using the visual page builder.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="title">Page Title</Label>
                <Input
                  id="title"
                  value={newPageTitle}
                  onChange={(e) => {
                    setNewPageTitle(e.target.value)
                    setNewPageSlug(generateSlug(e.target.value))
                  }}
                  placeholder="Enter page title"
                />
              </div>
              <div>
                <Label htmlFor="slug">Page Slug</Label>
                <Input
                  id="slug"
                  value={newPageSlug}
                  onChange={(e) => setNewPageSlug(e.target.value)}
                  placeholder="page-slug"
                />
              </div>
              <div>
                <Label htmlFor="type">Page Type</Label>
                <Select value={newPageType} onValueChange={setNewPageType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="custom">Custom Page</SelectItem>
                    <SelectItem value="landing">Landing Page</SelectItem>
                    <SelectItem value="product">Product Page</SelectItem>
                    <SelectItem value="category">Category Page</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="description">Description (Optional)</Label>
                <Input
                  id="description"
                  value={newPageDescription}
                  onChange={(e) => setNewPageDescription(e.target.value)}
                  placeholder="Brief description of the page"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setShowNewPageDialog(false)}
                >
                  Cancel
                </Button>
                <Button onClick={createNewPage}>
                  Create Page
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Pages List */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : pages.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No pages yet</h3>
            <p className="text-muted-foreground mb-4">
              Create your first custom page to get started.
            </p>
            <Button onClick={() => setShowNewPageDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create First Page
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {pages.map((page) => (
            <Card key={page.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="truncate">{page.title}</span>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    page.status === 'published' 
                      ? 'bg-green-100 text-green-800'
                      : page.status === 'draft'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {page.status}
                  </span>
                </CardTitle>
                <CardDescription>
                  /{page.slug} • {page.type} • {page.blockCount} blocks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    Updated {new Date(page.updatedAt).toLocaleDateString()}
                  </span>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => loadPage(page.id)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      asChild
                    >
                      <Link href={`/preview/${page.slug}`} target="_blank">
                        <Eye className="h-4 w-4" />
                      </Link>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deletePage(page.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
