'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Layout, 
  Plus, 
  Edit, 
  Copy, 
  Trash2, 
  Eye, 
  Settings, 
  Palette,
  Monitor,
  Smartphone,
  Tablet
} from 'lucide-react'

interface LayoutItem {
  id: string
  name: string
  description?: string
  type: string
  category: string
  isTemplate: boolean
  isSystem: boolean
  isActive: boolean
  usageCount: number
  thumbnail?: string
  tags: string[]
  createdAt: string
  updatedAt: string
}

export default function LayoutBuilderPage() {
  const router = useRouter()
  const [layouts, setLayouts] = useState<LayoutItem[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')

  useEffect(() => {
    fetchLayouts()
  }, [])

  const fetchLayouts = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/layout-builder/layouts?includeTemplates=true&includeInactive=false')
      const result = await response.json()

      if (result.success) {
        setLayouts(result.data)
      } else {
        console.error('Error fetching layouts:', result.error)
        // Fallback to empty array
        setLayouts([])
      }
    } catch (error) {
      console.error('Error fetching layouts:', error)
      // Fallback to empty array
      setLayouts([])
    } finally {
      setLoading(false)
    }
  }

  const filteredLayouts = layouts.filter(layout => {
    const matchesSearch = layout.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         layout.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         layout.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = selectedCategory === 'all' || layout.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  const categories = ['all', ...Array.from(new Set(layouts.map(l => l.category)))]

  return (
    <div className="container mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Layout className="h-8 w-8" />
            Layout Builder
          </h1>
          <p className="text-muted-foreground mt-2">
            Create and manage layouts for your website structure
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push('/admin/layout-builder/templates')}
          >
            Browse Templates
          </Button>
          <Button className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Create Layout
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1">
          <Input
            placeholder="Search layouts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
          <TabsList>
            {categories.map(category => (
              <TabsTrigger key={category} value={category} className="capitalize">
                {category === 'all' ? 'All' : category}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      {/* Layout Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </CardHeader>
              <CardContent>
                <div className="h-32 bg-gray-200 rounded mb-4"></div>
                <div className="flex gap-2">
                  <div className="h-6 bg-gray-200 rounded w-16"></div>
                  <div className="h-6 bg-gray-200 rounded w-20"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredLayouts.map((layout) => (
            <LayoutCard
              key={layout.id}
              layout={layout}
              onEdit={() => router.push(`/admin/layout-builder/editor/${layout.id}`)}
              onPreview={() => window.open(`/?layout=${layout.id}`, '_blank')}
            />
          ))}
        </div>
      )}

      {/* Empty State */}
      {!loading && filteredLayouts.length === 0 && (
        <div className="text-center py-12">
          <Layout className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No layouts found</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm || selectedCategory !== 'all' 
              ? 'Try adjusting your search or filters'
              : 'Create your first layout to get started'
            }
          </p>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Layout
          </Button>
        </div>
      )}

      {/* Quick Stats */}
      <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{layouts.length}</div>
            <div className="text-sm text-muted-foreground">Total Layouts</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {layouts.filter(l => l.isActive).length}
            </div>
            <div className="text-sm text-muted-foreground">Active Layouts</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {layouts.filter(l => l.isTemplate).length}
            </div>
            <div className="text-sm text-muted-foreground">Templates</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {layouts.reduce((sum, l) => sum + l.usageCount, 0)}
            </div>
            <div className="text-sm text-muted-foreground">Total Usage</div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

function LayoutCard({
  layout,
  onEdit,
  onPreview
}: {
  layout: LayoutItem
  onEdit: () => void
  onPreview: () => void
}) {
  return (
    <Card className="group hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg flex items-center gap-2">
              {layout.name}
              {layout.isSystem && (
                <Badge variant="secondary" className="text-xs">
                  System
                </Badge>
              )}
              {layout.isTemplate && (
                <Badge variant="outline" className="text-xs">
                  Template
                </Badge>
              )}
            </CardTitle>
            <CardDescription className="mt-1">
              {layout.description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Layout Preview */}
        <div className="bg-gray-50 rounded-lg p-4 mb-4 min-h-[120px] flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <Layout className="h-8 w-8 mx-auto mb-2" />
            <div className="text-sm">Layout Preview</div>
            <div className="text-xs">{layout.type} • {layout.category}</div>
          </div>
        </div>

        {/* Tags */}
        <div className="flex flex-wrap gap-1 mb-4">
          {layout.tags.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
          {layout.tags.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{layout.tags.length - 3}
            </Badge>
          )}
        </div>

        {/* Usage Stats */}
        <div className="text-sm text-muted-foreground mb-4">
          Used {layout.usageCount} times
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <Button size="sm" variant="outline" className="flex-1" onClick={onPreview}>
            <Eye className="h-4 w-4 mr-1" />
            Preview
          </Button>
          <Button size="sm" variant="outline" onClick={onEdit}>
            <Edit className="h-4 w-4" />
          </Button>
          <Button size="sm" variant="outline">
            <Copy className="h-4 w-4" />
          </Button>
          {!layout.isSystem && (
            <Button size="sm" variant="outline">
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Device Icons */}
        <div className="flex justify-center gap-2 mt-4 pt-4 border-t">
          <Monitor className="h-4 w-4 text-muted-foreground" />
          <Tablet className="h-4 w-4 text-muted-foreground" />
          <Smartphone className="h-4 w-4 text-muted-foreground" />
        </div>
      </CardContent>
    </Card>
  )
}
