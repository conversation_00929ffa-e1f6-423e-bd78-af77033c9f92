'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Layout, 
  ArrowLeft, 
  Search, 
  Eye,
  Download,
  Star,
  Users,
  Zap,
  Monitor,
  Smartphone,
  Tablet,
  ShoppingCart,
  FileText,
  Briefcase,
  Camera
} from 'lucide-react'
import { toast } from 'sonner'

interface LayoutTemplate {
  id: string
  name: string
  description: string
  category: string
  preview: string
  thumbnail: string
  features: string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  rating: number
  downloads: number
  isPremium: boolean
  tags: string[]
  structure: any
  styling: any
  responsive: any
}

const TEMPLATE_CATEGORIES = [
  { id: 'all', name: 'All Templates', icon: Layout },
  { id: 'ecommerce', name: 'E-commerce', icon: ShoppingCart },
  { id: 'blog', name: 'Blog & Magazine', icon: FileText },
  { id: 'portfolio', name: 'Portfolio', icon: Camera },
  { id: 'business', name: 'Business', icon: Briefcase },
  { id: 'landing', name: 'Landing Pages', icon: Zap },
  { id: 'personal', name: 'Personal', icon: Users }
]



export default function LayoutTemplatesPage() {
  const router = useRouter()
  const [templates, setTemplates] = useState<LayoutTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [sortBy, setSortBy] = useState<'popular' | 'rating' | 'newest'>('popular')

  useEffect(() => {
    fetchTemplates()
  }, [selectedCategory])

  const fetchTemplates = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/layout-builder/templates?category=${selectedCategory}`)
      const result = await response.json()

      if (result.success) {
        setTemplates(result.data)
      } else {
        console.error('Error fetching templates:', result.error)
        toast.error('Failed to load templates')
      }
    } catch (error) {
      console.error('Error fetching templates:', error)
      toast.error('Failed to load templates')
    } finally {
      setLoading(false)
    }
  }

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory
    
    return matchesSearch && matchesCategory
  }).sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.downloads - a.downloads
      case 'rating':
        return b.rating - a.rating
      case 'newest':
        return 0 // Would sort by creation date
      default:
        return 0
    }
  })

  const useTemplate = async (template: LayoutTemplate) => {
    try {
      setLoading(true)

      // Get template structure from API
      const templateResponse = await fetch(`/api/layout-builder/templates?id=${template.id}`)
      const templateResult = await templateResponse.json()

      if (!templateResult.success) {
        toast.error('Failed to load template structure')
        return
      }

      // Create new layout from template
      const response = await fetch('/api/layout-builder/layouts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: `${template.name} (Copy)`,
          description: template.description,
          type: 'page',
          category: template.category,
          structure: templateResult.data.structure,
          styling: templateResult.data.styling,
          responsive: templateResult.data.responsive,
          isTemplate: false,
          tags: [...template.tags, 'from-template']
        })
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Template applied successfully!')
        router.push(`/admin/layout-builder/editor/${result.data.id}`)
      } else {
        toast.error('Failed to apply template')
      }
    } catch (error) {
      console.error('Error applying template:', error)
      toast.error('Failed to apply template')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Button 
            variant="ghost" 
            onClick={() => router.push('/admin/layout-builder')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Layout Builder
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Layout className="h-8 w-8" />
              Layout Templates
            </h1>
            <p className="text-muted-foreground mt-2">
              Choose from professionally designed layout templates
            </p>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col lg:flex-row gap-4 mb-8">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <div className="flex gap-2">
          <select 
            value={sortBy} 
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border rounded-md"
          >
            <option value="popular">Most Popular</option>
            <option value="rating">Highest Rated</option>
            <option value="newest">Newest</option>
          </select>
        </div>
      </div>

      {/* Category Tabs */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="mb-8">
        <TabsList className="grid grid-cols-7 w-full">
          {TEMPLATE_CATEGORIES.map(category => {
            const Icon = category.icon
            return (
              <TabsTrigger key={category.id} value={category.id} className="flex items-center gap-2">
                <Icon className="h-4 w-4" />
                <span className="hidden sm:inline">{category.name}</span>
              </TabsTrigger>
            )
          })}
        </TabsList>
      </Tabs>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => (
          <TemplateCard 
            key={template.id} 
            template={template} 
            onUse={() => useTemplate(template)}
            loading={loading}
          />
        ))}
      </div>

      {/* Empty State */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <Layout className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No templates found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search or filters
          </p>
        </div>
      )}

      {/* Stats */}
      <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{templates.length}</div>
            <div className="text-sm text-muted-foreground">Total Templates</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">
              {templates.filter(t => !t.isPremium).length}
            </div>
            <div className="text-sm text-muted-foreground">Free Templates</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">
              {templates.reduce((sum, t) => sum + t.downloads, 0).toLocaleString()}
            </div>
            <div className="text-sm text-muted-foreground">Total Downloads</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">
              {(templates.reduce((sum, t) => sum + t.rating, 0) / templates.length).toFixed(1)}
            </div>
            <div className="text-sm text-muted-foreground">Average Rating</div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

function TemplateCard({ 
  template, 
  onUse, 
  loading 
}: { 
  template: LayoutTemplate
  onUse: () => void
  loading: boolean
}) {
  return (
    <Card className="group hover:shadow-lg transition-shadow overflow-hidden">
      {/* Template Preview */}
      <div className="relative h-48 bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <Layout className="h-12 w-12 mx-auto mb-2" />
            <div className="text-sm font-medium">{template.name}</div>
            <div className="text-xs">{template.category}</div>
          </div>
        </div>
        
        {/* Premium Badge */}
        {template.isPremium && (
          <Badge className="absolute top-2 right-2 bg-yellow-500">
            Premium
          </Badge>
        )}

        {/* Difficulty Badge */}
        <Badge 
          variant="secondary" 
          className="absolute top-2 left-2"
        >
          {template.difficulty}
        </Badge>
      </div>

      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg">{template.name}</CardTitle>
            <CardDescription className="mt-1">
              {template.description}
            </CardDescription>
          </div>
        </div>

        {/* Rating and Downloads */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span>{template.rating}</span>
          </div>
          <div className="flex items-center gap-1">
            <Download className="h-4 w-4" />
            <span>{template.downloads.toLocaleString()}</span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Features */}
        <div className="mb-4">
          <div className="text-sm font-medium mb-2">Features:</div>
          <div className="flex flex-wrap gap-1">
            {template.features.slice(0, 3).map((feature) => (
              <Badge key={feature} variant="outline" className="text-xs">
                {feature}
              </Badge>
            ))}
            {template.features.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{template.features.length - 3} more
              </Badge>
            )}
          </div>
        </div>

        {/* Tags */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-1">
            {template.tags.slice(0, 4).map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="flex-1"
          >
            <Eye className="h-4 w-4 mr-1" />
            Preview
          </Button>
          <Button 
            onClick={onUse}
            disabled={loading}
            size="sm"
            className="flex-1"
          >
            {loading ? 'Applying...' : 'Use Template'}
          </Button>
        </div>

        {/* Device Icons */}
        <div className="flex justify-center gap-2 mt-4 pt-4 border-t">
          <Monitor className="h-4 w-4 text-muted-foreground" />
          <Tablet className="h-4 w-4 text-muted-foreground" />
          <Smartphone className="h-4 w-4 text-muted-foreground" />
        </div>
      </CardContent>
    </Card>
  )
}
