'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import {
  Bell,
  Mail,
  Smartphone,
  Save,
  RefreshCw,
  Settings,
  Users,
  ShoppingCart,
  Package,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { toast } from 'sonner'
import { NotificationHistory } from '@/components/admin/notifications/notification-history'

interface NotificationSettings {
  emailEnabled: boolean
  smsEnabled: boolean
  pushEnabled: boolean
  adminNotifications: {
    newOrder: boolean
    lowStock: boolean
    newCustomer: boolean
    systemAlerts: boolean
  }
  customerNotifications: {
    orderConfirmation: boolean
    orderStatusUpdate: boolean
    shipmentTracking: boolean
    promotionalEmails: boolean
  }
}

export default function NotificationsSettingsPage() {
  const [settings, setSettings] = useState<NotificationSettings>({
    emailEnabled: true,
    smsEnabled: false,
    pushEnabled: true,
    adminNotifications: {
      newOrder: true,
      lowStock: true,
      newCustomer: false,
      systemAlerts: true
    },
    customerNotifications: {
      orderConfirmation: true,
      orderStatusUpdate: true,
      shipmentTracking: true,
      promotionalEmails: false
    }
  })

  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500))
    } catch (error) {
      console.error('Error loading settings:', error)
      toast.error('Failed to load notification settings')
    } finally {
      setLoading(false)
    }
  }

  const saveSettings = async () => {
    try {
      setSaving(true)
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Notification settings saved successfully')
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Failed to save notification settings')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Notification Settings</h1>
        </div>
        <div className="grid gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Notification Settings</h1>
          <p className="text-muted-foreground">
            Configure email, SMS, and push notification preferences
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadSettings} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={saveSettings} disabled={saving}>
            {saving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Notification Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Mail className="h-4 w-4 text-blue-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Email</p>
                <p className="text-2xl font-bold">
                  {settings.emailEnabled ? (
                    <span className="text-green-600">Enabled</span>
                  ) : (
                    <span className="text-red-600">Disabled</span>
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Smartphone className="h-4 w-4 text-green-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">SMS</p>
                <p className="text-2xl font-bold">
                  {settings.smsEnabled ? (
                    <span className="text-green-600">Enabled</span>
                  ) : (
                    <span className="text-red-600">Disabled</span>
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Bell className="h-4 w-4 text-purple-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Push</p>
                <p className="text-2xl font-bold">
                  {settings.pushEnabled ? (
                    <span className="text-green-600">Enabled</span>
                  ) : (
                    <span className="text-red-600">Disabled</span>
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Settings className="h-4 w-4 text-orange-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Active</p>
                <p className="text-2xl font-bold">
                  {Object.values(settings.adminNotifications).filter(Boolean).length + 
                   Object.values(settings.customerNotifications).filter(Boolean).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* General Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>General Settings</span>
          </CardTitle>
          <CardDescription>
            Enable or disable notification channels
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Email Notifications</Label>
              <p className="text-sm text-muted-foreground">
                Send notifications via email
              </p>
            </div>
            <Switch
              checked={settings.emailEnabled}
              onCheckedChange={(checked) => setSettings(prev => ({ ...prev, emailEnabled: checked }))}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>SMS Notifications</Label>
              <p className="text-sm text-muted-foreground">
                Send notifications via SMS (requires SMS provider)
              </p>
            </div>
            <Switch
              checked={settings.smsEnabled}
              onCheckedChange={(checked) => setSettings(prev => ({ ...prev, smsEnabled: checked }))}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Push Notifications</Label>
              <p className="text-sm text-muted-foreground">
                Send browser push notifications
              </p>
            </div>
            <Switch
              checked={settings.pushEnabled}
              onCheckedChange={(checked) => setSettings(prev => ({ ...prev, pushEnabled: checked }))}
            />
          </div>
        </CardContent>
      </Card>

      {/* Email Configuration */}
      {settings.emailEnabled && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Mail className="h-5 w-5" />
              <span>Email Configuration</span>
            </CardTitle>
            <CardDescription>
              Configure SMTP settings for email delivery
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>SMTP Host</Label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border rounded-md"
                  placeholder="smtp.gmail.com"
                  defaultValue="smtp.gmail.com"
                />
              </div>
              <div className="space-y-2">
                <Label>SMTP Port</Label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border rounded-md"
                  placeholder="587"
                  defaultValue="587"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Username</Label>
                <input
                  type="email"
                  className="w-full px-3 py-2 border rounded-md"
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="space-y-2">
                <Label>Password</Label>
                <input
                  type="password"
                  className="w-full px-3 py-2 border rounded-md"
                  placeholder="••••••••"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                Test Connection
              </Button>
              <Badge variant="outline">Not Tested</Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Notification Types */}
      <Tabs defaultValue="admin" className="space-y-4">
        <TabsList>
          <TabsTrigger value="admin">Admin Notifications</TabsTrigger>
          <TabsTrigger value="customer">Customer Notifications</TabsTrigger>
          <TabsTrigger value="templates">Email Templates</TabsTrigger>
          <TabsTrigger value="history">Notification History</TabsTrigger>
        </TabsList>

        <TabsContent value="admin">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Admin Notifications</span>
              </CardTitle>
              <CardDescription>
                Notifications sent to administrators
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <ShoppingCart className="h-4 w-4 text-blue-600" />
                  <div className="space-y-0.5">
                    <Label>New Order</Label>
                    <p className="text-sm text-muted-foreground">
                      Notify when a new order is placed
                    </p>
                  </div>
                </div>
                <Switch
                  checked={settings.adminNotifications.newOrder}
                  onCheckedChange={(checked) => setSettings(prev => ({
                    ...prev,
                    adminNotifications: { ...prev.adminNotifications, newOrder: checked }
                  }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Package className="h-4 w-4 text-orange-600" />
                  <div className="space-y-0.5">
                    <Label>Low Stock</Label>
                    <p className="text-sm text-muted-foreground">
                      Notify when product stock is low
                    </p>
                  </div>
                </div>
                <Switch
                  checked={settings.adminNotifications.lowStock}
                  onCheckedChange={(checked) => setSettings(prev => ({
                    ...prev,
                    adminNotifications: { ...prev.adminNotifications, lowStock: checked }
                  }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Users className="h-4 w-4 text-green-600" />
                  <div className="space-y-0.5">
                    <Label>New Customer</Label>
                    <p className="text-sm text-muted-foreground">
                      Notify when a new customer registers
                    </p>
                  </div>
                </div>
                <Switch
                  checked={settings.adminNotifications.newCustomer}
                  onCheckedChange={(checked) => setSettings(prev => ({
                    ...prev,
                    adminNotifications: { ...prev.adminNotifications, newCustomer: checked }
                  }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <div className="space-y-0.5">
                    <Label>System Alerts</Label>
                    <p className="text-sm text-muted-foreground">
                      Notify about system errors and alerts
                    </p>
                  </div>
                </div>
                <Switch
                  checked={settings.adminNotifications.systemAlerts}
                  onCheckedChange={(checked) => setSettings(prev => ({
                    ...prev,
                    adminNotifications: { ...prev.adminNotifications, systemAlerts: checked }
                  }))}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customer">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bell className="h-5 w-5" />
                <span>Customer Notifications</span>
              </CardTitle>
              <CardDescription>
                Notifications sent to customers
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <div className="space-y-0.5">
                    <Label>Order Confirmation</Label>
                    <p className="text-sm text-muted-foreground">
                      Send confirmation when order is placed
                    </p>
                  </div>
                </div>
                <Switch
                  checked={settings.customerNotifications.orderConfirmation}
                  onCheckedChange={(checked) => setSettings(prev => ({
                    ...prev,
                    customerNotifications: { ...prev.customerNotifications, orderConfirmation: checked }
                  }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Package className="h-4 w-4 text-blue-600" />
                  <div className="space-y-0.5">
                    <Label>Order Status Updates</Label>
                    <p className="text-sm text-muted-foreground">
                      Notify when order status changes
                    </p>
                  </div>
                </div>
                <Switch
                  checked={settings.customerNotifications.orderStatusUpdate}
                  onCheckedChange={(checked) => setSettings(prev => ({
                    ...prev,
                    customerNotifications: { ...prev.customerNotifications, orderStatusUpdate: checked }
                  }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Package className="h-4 w-4 text-purple-600" />
                  <div className="space-y-0.5">
                    <Label>Shipment Tracking</Label>
                    <p className="text-sm text-muted-foreground">
                      Send tracking information when order ships
                    </p>
                  </div>
                </div>
                <Switch
                  checked={settings.customerNotifications.shipmentTracking}
                  onCheckedChange={(checked) => setSettings(prev => ({
                    ...prev,
                    customerNotifications: { ...prev.customerNotifications, shipmentTracking: checked }
                  }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-orange-600" />
                  <div className="space-y-0.5">
                    <Label>Promotional Emails</Label>
                    <p className="text-sm text-muted-foreground">
                      Send marketing and promotional emails
                    </p>
                  </div>
                </div>
                <Switch
                  checked={settings.customerNotifications.promotionalEmails}
                  onCheckedChange={(checked) => setSettings(prev => ({
                    ...prev,
                    customerNotifications: { ...prev.customerNotifications, promotionalEmails: checked }
                  }))}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Mail className="h-5 w-5" />
                <span>Email Templates</span>
              </CardTitle>
              <CardDescription>
                Customize email templates for different notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { id: 'order-confirmation', name: 'Order Confirmation', description: 'Sent when customer places an order' },
                  { id: 'order-shipped', name: 'Order Shipped', description: 'Sent when order is shipped' },
                  { id: 'order-delivered', name: 'Order Delivered', description: 'Sent when order is delivered' },
                  { id: 'password-reset', name: 'Password Reset', description: 'Sent when customer requests password reset' },
                  { id: 'welcome-email', name: 'Welcome Email', description: 'Sent to new customers' },
                  { id: 'low-stock-alert', name: 'Low Stock Alert', description: 'Sent to admins when stock is low' }
                ].map((template) => (
                  <div key={template.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{template.name}</h4>
                      <p className="text-sm text-muted-foreground">{template.description}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">Active</Badge>
                      <Button variant="outline" size="sm">
                        Edit Template
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history">
          <NotificationHistory />
        </TabsContent>
      </Tabs>
    </div>
  )
}
