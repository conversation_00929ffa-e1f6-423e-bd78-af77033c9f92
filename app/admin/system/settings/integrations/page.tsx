'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  <PERSON><PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import {
  Zap,
  Globe,
  Webhook,
  Key,
  Save,
  RefreshCw,
  ExternalLink,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Settings,
  BarChart3,
  Mail,
  MessageSquare,
  Truck,
  CreditCard
} from 'lucide-react'
import { toast } from 'sonner'
import { WebhookManager } from '@/components/admin/integrations/webhook-manager'

interface Integration {
  id: string
  name: string
  description: string
  category: 'analytics' | 'marketing' | 'shipping' | 'payment' | 'communication' | 'other'
  icon: any
  enabled: boolean
  configured: boolean
  status: 'connected' | 'disconnected' | 'error' | 'testing'
  settings: Record<string, any>
  webhookUrl?: string
  lastSync?: string
}

export default function IntegrationsSettingsPage() {
  const [integrations, setIntegrations] = useState<Integration[]>([
    {
      id: 'google-analytics',
      name: 'Google Analytics',
      description: 'Track website traffic and user behavior',
      category: 'analytics',
      icon: BarChart3,
      enabled: true,
      configured: true,
      status: 'connected',
      settings: {
        trackingId: 'GA-XXXXXXXXX',
        enhancedEcommerce: true
      },
      lastSync: '2024-01-15T10:30:00Z'
    },
    {
      id: 'facebook-pixel',
      name: 'Facebook Pixel',
      description: 'Track conversions and optimize Facebook ads',
      category: 'marketing',
      icon: Globe,
      enabled: false,
      configured: false,
      status: 'disconnected',
      settings: {
        pixelId: '',
        accessToken: ''
      }
    },
    {
      id: 'mailchimp',
      name: 'Mailchimp',
      description: 'Email marketing and automation',
      category: 'marketing',
      icon: Mail,
      enabled: true,
      configured: true,
      status: 'connected',
      settings: {
        apiKey: 'xxxxxxxx-us1',
        listId: 'abc123'
      },
      lastSync: '2024-01-15T09:15:00Z'
    },
    {
      id: 'whatsapp-business',
      name: 'WhatsApp Business',
      description: 'Send order updates via WhatsApp',
      category: 'communication',
      icon: MessageSquare,
      enabled: false,
      configured: false,
      status: 'disconnected',
      settings: {
        phoneNumberId: '',
        accessToken: ''
      }
    },
    {
      id: 'aramex',
      name: 'Aramex',
      description: 'Shipping and logistics integration',
      category: 'shipping',
      icon: Truck,
      enabled: true,
      configured: true,
      status: 'connected',
      settings: {
        username: 'testuser',
        password: 'testpass',
        accountNumber: '123456',
        sandbox: true
      },
      lastSync: '2024-01-15T08:45:00Z'
    },
    {
      id: 'zapier',
      name: 'Zapier',
      description: 'Connect with 5000+ apps and automate workflows',
      category: 'other',
      icon: Zap,
      enabled: false,
      configured: false,
      status: 'disconnected',
      settings: {
        webhookUrl: ''
      }
    }
  ])

  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    loadIntegrations()
  }, [])

  const loadIntegrations = async () => {
    try {
      setLoading(true)
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500))
    } catch (error) {
      console.error('Error loading integrations:', error)
      toast.error('Failed to load integrations')
    } finally {
      setLoading(false)
    }
  }

  const saveIntegrations = async () => {
    try {
      setSaving(true)
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Integration settings saved successfully')
    } catch (error) {
      console.error('Error saving integrations:', error)
      toast.error('Failed to save integration settings')
    } finally {
      setSaving(false)
    }
  }

  const toggleIntegration = (integrationId: string) => {
    setIntegrations(prev => prev.map(integration => 
      integration.id === integrationId 
        ? { ...integration, enabled: !integration.enabled }
        : integration
    ))
  }

  const testConnection = async (integrationId: string) => {
    const integration = integrations.find(i => i.id === integrationId)
    if (!integration) return

    setIntegrations(prev => prev.map(i => 
      i.id === integrationId ? { ...i, status: 'testing' } : i
    ))

    try {
      // Mock connection test
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Randomly succeed or fail for demo
      const success = Math.random() > 0.3
      
      setIntegrations(prev => prev.map(i => 
        i.id === integrationId 
          ? { 
              ...i, 
              status: success ? 'connected' : 'error',
              lastSync: success ? new Date().toISOString() : i.lastSync
            } 
          : i
      ))

      if (success) {
        toast.success(`${integration.name} connection test successful`)
      } else {
        toast.error(`${integration.name} connection test failed`)
      }
    } catch (error) {
      setIntegrations(prev => prev.map(i => 
        i.id === integrationId ? { ...i, status: 'error' } : i
      ))
      toast.error(`${integration.name} connection test failed`)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'disconnected':
        return <XCircle className="h-4 w-4 text-gray-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'testing':
        return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge variant="default">Connected</Badge>
      case 'disconnected':
        return <Badge variant="outline">Disconnected</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
      case 'testing':
        return <Badge variant="secondary">Testing...</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'analytics':
        return <BarChart3 className="h-4 w-4" />
      case 'marketing':
        return <Globe className="h-4 w-4" />
      case 'shipping':
        return <Truck className="h-4 w-4" />
      case 'payment':
        return <CreditCard className="h-4 w-4" />
      case 'communication':
        return <MessageSquare className="h-4 w-4" />
      default:
        return <Zap className="h-4 w-4" />
    }
  }

  const groupedIntegrations = integrations.reduce((acc, integration) => {
    if (!acc[integration.category]) {
      acc[integration.category] = []
    }
    acc[integration.category].push(integration)
    return acc
  }, {} as Record<string, Integration[]>)

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Integrations</h1>
        </div>
        <div className="grid gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Integrations</h1>
          <p className="text-muted-foreground">
            Connect with third-party services and automate your workflows
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadIntegrations} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={saveIntegrations} disabled={saving}>
            {saving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Integration Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Zap className="h-4 w-4 text-blue-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Total Integrations</p>
                <p className="text-2xl font-bold">{integrations.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Connected</p>
                <p className="text-2xl font-bold text-green-600">
                  {integrations.filter(i => i.status === 'connected').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Settings className="h-4 w-4 text-purple-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Enabled</p>
                <p className="text-2xl font-bold text-purple-600">
                  {integrations.filter(i => i.enabled).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <XCircle className="h-4 w-4 text-red-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Errors</p>
                <p className="text-2xl font-bold text-red-600">
                  {integrations.filter(i => i.status === 'error').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Integrations by Category */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Integrations</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="marketing">Marketing</TabsTrigger>
          <TabsTrigger value="shipping">Shipping</TabsTrigger>
          <TabsTrigger value="communication">Communication</TabsTrigger>
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <div className="space-y-6">
            {Object.entries(groupedIntegrations).map(([category, categoryIntegrations]) => (
              <Card key={category}>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 capitalize">
                    {getCategoryIcon(category)}
                    <span>{category}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {categoryIntegrations.map((integration) => (
                      <div
                        key={integration.id}
                        className={`p-4 border rounded-lg transition-colors ${
                          integration.enabled ? 'border-green-200 bg-green-50' : 'border-gray-200'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            <integration.icon className="h-5 w-5" />
                            <span className="font-medium">{integration.name}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(integration.status)}
                            <Switch
                              checked={integration.enabled}
                              onCheckedChange={() => toggleIntegration(integration.id)}
                            />
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">
                          {integration.description}
                        </p>
                        <div className="flex items-center justify-between">
                          {getStatusBadge(integration.status)}
                          <div className="flex items-center space-x-2">
                            {integration.enabled && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => testConnection(integration.id)}
                                disabled={integration.status === 'testing'}
                              >
                                {integration.status === 'testing' ? 'Testing...' : 'Test'}
                              </Button>
                            )}
                            <Button variant="outline" size="sm">
                              Configure
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Analytics Integrations</span>
              </CardTitle>
              <CardDescription>
                Track and analyze your store performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {integrations
                  .filter(i => i.category === 'analytics')
                  .map((integration) => (
                    <div
                      key={integration.id}
                      className={`p-4 border rounded-lg ${
                        integration.enabled ? 'border-green-200 bg-green-50' : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <integration.icon className="h-5 w-5" />
                          <span className="font-medium">{integration.name}</span>
                        </div>
                        <Switch
                          checked={integration.enabled}
                          onCheckedChange={() => toggleIntegration(integration.id)}
                        />
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        {integration.description}
                      </p>
                      <div className="flex items-center justify-between">
                        {getStatusBadge(integration.status)}
                        <Button variant="outline" size="sm">
                          Configure
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="marketing">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Globe className="h-5 w-5" />
                <span>Marketing Integrations</span>
              </CardTitle>
              <CardDescription>
                Connect with marketing and advertising platforms
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {integrations
                  .filter(i => i.category === 'marketing')
                  .map((integration) => (
                    <div
                      key={integration.id}
                      className={`p-4 border rounded-lg ${
                        integration.enabled ? 'border-green-200 bg-green-50' : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <integration.icon className="h-5 w-5" />
                          <span className="font-medium">{integration.name}</span>
                        </div>
                        <Switch
                          checked={integration.enabled}
                          onCheckedChange={() => toggleIntegration(integration.id)}
                        />
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        {integration.description}
                      </p>
                      <div className="flex items-center justify-between">
                        {getStatusBadge(integration.status)}
                        <Button variant="outline" size="sm">
                          Configure
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="shipping">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Truck className="h-5 w-5" />
                <span>Shipping Integrations</span>
              </CardTitle>
              <CardDescription>
                Connect with shipping and logistics providers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {integrations
                  .filter(i => i.category === 'shipping')
                  .map((integration) => (
                    <div
                      key={integration.id}
                      className={`p-4 border rounded-lg ${
                        integration.enabled ? 'border-green-200 bg-green-50' : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <integration.icon className="h-5 w-5" />
                          <span className="font-medium">{integration.name}</span>
                        </div>
                        <Switch
                          checked={integration.enabled}
                          onCheckedChange={() => toggleIntegration(integration.id)}
                        />
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        {integration.description}
                      </p>
                      <div className="flex items-center justify-between">
                        {getStatusBadge(integration.status)}
                        <Button variant="outline" size="sm">
                          Configure
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="communication">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MessageSquare className="h-5 w-5" />
                <span>Communication Integrations</span>
              </CardTitle>
              <CardDescription>
                Connect with messaging and communication platforms
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {integrations
                  .filter(i => i.category === 'communication')
                  .map((integration) => (
                    <div
                      key={integration.id}
                      className={`p-4 border rounded-lg ${
                        integration.enabled ? 'border-green-200 bg-green-50' : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <integration.icon className="h-5 w-5" />
                          <span className="font-medium">{integration.name}</span>
                        </div>
                        <Switch
                          checked={integration.enabled}
                          onCheckedChange={() => toggleIntegration(integration.id)}
                        />
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        {integration.description}
                      </p>
                      <div className="flex items-center justify-between">
                        {getStatusBadge(integration.status)}
                        <Button variant="outline" size="sm">
                          Configure
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="webhooks">
          <WebhookManager />
        </TabsContent>
      </Tabs>
    </div>
  )
}
