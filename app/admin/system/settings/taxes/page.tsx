'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Receipt,
  Plus,
  Edit,
  Trash2,
  Save,
  RefreshCw,
  Calculator,
  MapPin,
  Percent,
  Info
} from 'lucide-react'
import { toast } from 'sonner'

interface TaxRate {
  id: string
  name: string
  rate: number
  type: 'percentage' | 'fixed'
  country: string
  state?: string
  city?: string
  postcode?: string
  priority: number
  compound: boolean
  shipping: boolean
  enabled: boolean
}

interface TaxClass {
  id: string
  name: string
  description: string
  rates: string[] // Tax rate IDs
}

interface TaxSettings {
  enableTaxes: boolean
  pricesIncludeTax: boolean
  calculateTaxBased: 'shipping' | 'billing' | 'store'
  shippingTaxClass: string
  roundingMode: 'round' | 'up' | 'down'
  displayPricesInShop: 'excluding' | 'including' | 'both'
  displayPricesInCart: 'excluding' | 'including' | 'both'
  displayTaxTotals: 'single' | 'itemized'
  enableTaxReports: boolean
}

export default function TaxesSettingsPage() {
  const [settings, setSettings] = useState<TaxSettings>({
    enableTaxes: true,
    pricesIncludeTax: true,
    calculateTaxBased: 'shipping',
    shippingTaxClass: 'standard',
    roundingMode: 'round',
    displayPricesInShop: 'including',
    displayPricesInCart: 'including',
    displayTaxTotals: 'itemized',
    enableTaxReports: true
  })

  const [taxRates, setTaxRates] = useState<TaxRate[]>([
    {
      id: 'vat-za',
      name: 'South Africa VAT',
      rate: 15,
      type: 'percentage',
      country: 'ZA',
      priority: 1,
      compound: false,
      shipping: true,
      enabled: true
    },
    {
      id: 'vat-za-zero',
      name: 'South Africa VAT (Zero Rate)',
      rate: 0,
      type: 'percentage',
      country: 'ZA',
      priority: 1,
      compound: false,
      shipping: false,
      enabled: true
    },
    {
      id: 'us-sales-tax',
      name: 'US Sales Tax',
      rate: 8.5,
      type: 'percentage',
      country: 'US',
      state: 'CA',
      priority: 1,
      compound: false,
      shipping: true,
      enabled: false
    }
  ])

  const [taxClasses, setTaxClasses] = useState<TaxClass[]>([
    {
      id: 'standard',
      name: 'Standard Rate',
      description: 'Standard VAT rate for most products',
      rates: ['vat-za']
    },
    {
      id: 'zero-rate',
      name: 'Zero Rate',
      description: 'Zero-rated products (books, basic foods, etc.)',
      rates: ['vat-za-zero']
    },
    {
      id: 'reduced-rate',
      name: 'Reduced Rate',
      description: 'Reduced VAT rate for specific products',
      rates: []
    }
  ])

  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [editingRate, setEditingRate] = useState<TaxRate | null>(null)
  const [editingClass, setEditingClass] = useState<TaxClass | null>(null)
  const [showRateDialog, setShowRateDialog] = useState(false)
  const [showClassDialog, setShowClassDialog] = useState(false)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      // In production, this would fetch from your API
      await new Promise(resolve => setTimeout(resolve, 500))
    } catch (error) {
      console.error('Error loading settings:', error)
      toast.error('Failed to load tax settings')
    } finally {
      setLoading(false)
    }
  }

  const saveSettings = async () => {
    try {
      setSaving(true)
      // In production, this would save to your API
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Tax settings saved successfully')
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Failed to save tax settings')
    } finally {
      setSaving(false)
    }
  }

  const toggleRateEnabled = (rateId: string) => {
    setTaxRates(prev => prev.map(rate => 
      rate.id === rateId ? { ...rate, enabled: !rate.enabled } : rate
    ))
  }

  const deleteRate = (rateId: string) => {
    if (confirm('Are you sure you want to delete this tax rate?')) {
      setTaxRates(prev => prev.filter(rate => rate.id !== rateId))
      toast.success('Tax rate deleted successfully')
    }
  }

  const deleteClass = (classId: string) => {
    if (confirm('Are you sure you want to delete this tax class?')) {
      setTaxClasses(prev => prev.filter(cls => cls.id !== classId))
      toast.success('Tax class deleted successfully')
    }
  }

  const formatRate = (rate: TaxRate) => {
    return rate.type === 'percentage' ? `${rate.rate}%` : `R${rate.rate}`
  }

  const getLocationString = (rate: TaxRate) => {
    const parts = [rate.country]
    if (rate.state) parts.push(rate.state)
    if (rate.city) parts.push(rate.city)
    if (rate.postcode) parts.push(rate.postcode)
    return parts.join(', ')
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Tax Settings</h1>
        </div>
        <div className="grid gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Tax Settings</h1>
          <p className="text-muted-foreground">
            Configure tax rates, classes, and calculation methods
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadSettings} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={saveSettings} disabled={saving}>
            {saving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Tax Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Receipt className="h-4 w-4 text-blue-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Tax Rates</p>
                <p className="text-2xl font-bold">{taxRates.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calculator className="h-4 w-4 text-green-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Active Rates</p>
                <p className="text-2xl font-bold text-green-600">
                  {taxRates.filter(rate => rate.enabled).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Percent className="h-4 w-4 text-purple-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Tax Classes</p>
                <p className="text-2xl font-bold">{taxClasses.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <MapPin className="h-4 w-4 text-orange-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Countries</p>
                <p className="text-2xl font-bold">
                  {new Set(taxRates.map(rate => rate.country)).size}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* General Tax Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calculator className="h-5 w-5" />
            <span>General Settings</span>
          </CardTitle>
          <CardDescription>
            Configure basic tax calculation and display preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Taxes</Label>
              <p className="text-sm text-muted-foreground">
                Calculate and display taxes on products and orders
              </p>
            </div>
            <Switch
              checked={settings.enableTaxes}
              onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enableTaxes: checked }))}
            />
          </div>

          {settings.enableTaxes && (
            <>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Prices Include Tax</Label>
                  <p className="text-sm text-muted-foreground">
                    Product prices entered include tax
                  </p>
                </div>
                <Switch
                  checked={settings.pricesIncludeTax}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, pricesIncludeTax: checked }))}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>Calculate Tax Based On</Label>
                  <Select 
                    value={settings.calculateTaxBased} 
                    onValueChange={(value: 'shipping' | 'billing' | 'store') => 
                      setSettings(prev => ({ ...prev, calculateTaxBased: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="shipping">Customer Shipping Address</SelectItem>
                      <SelectItem value="billing">Customer Billing Address</SelectItem>
                      <SelectItem value="store">Store Address</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Rounding Mode</Label>
                  <Select 
                    value={settings.roundingMode} 
                    onValueChange={(value: 'round' | 'up' | 'down') => 
                      setSettings(prev => ({ ...prev, roundingMode: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="round">Round to nearest cent</SelectItem>
                      <SelectItem value="up">Round up</SelectItem>
                      <SelectItem value="down">Round down</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>Display Prices in Shop</Label>
                  <Select 
                    value={settings.displayPricesInShop} 
                    onValueChange={(value: 'excluding' | 'including' | 'both') => 
                      setSettings(prev => ({ ...prev, displayPricesInShop: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="excluding">Excluding tax</SelectItem>
                      <SelectItem value="including">Including tax</SelectItem>
                      <SelectItem value="both">Both (including and excluding)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Display Prices in Cart</Label>
                  <Select 
                    value={settings.displayPricesInCart} 
                    onValueChange={(value: 'excluding' | 'including' | 'both') => 
                      setSettings(prev => ({ ...prev, displayPricesInCart: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="excluding">Excluding tax</SelectItem>
                      <SelectItem value="including">Including tax</SelectItem>
                      <SelectItem value="both">Both (including and excluding)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Tax Rates */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Percent className="h-5 w-5" />
                <span>Tax Rates</span>
              </CardTitle>
              <CardDescription>
                Configure tax rates for different locations and products
              </CardDescription>
            </div>
            <Button onClick={() => setShowRateDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Rate
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Rate</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Shipping</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {taxRates.map((rate) => (
                <TableRow key={rate.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{rate.name}</div>
                      {rate.compound && (
                        <Badge variant="outline" className="text-xs mt-1">
                          Compound
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="font-mono">{formatRate(rate)}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <MapPin className="h-3 w-3" />
                      <span className="text-sm">{getLocationString(rate)}</span>
                    </div>
                  </TableCell>
                  <TableCell>{rate.priority}</TableCell>
                  <TableCell>
                    {rate.shipping ? (
                      <Badge variant="default" className="text-xs">Yes</Badge>
                    ) : (
                      <Badge variant="outline" className="text-xs">No</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <Switch
                      checked={rate.enabled}
                      onCheckedChange={() => toggleRateEnabled(rate.id)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => deleteRate(rate.id)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Tax Classes */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Receipt className="h-5 w-5" />
                <span>Tax Classes</span>
              </CardTitle>
              <CardDescription>
                Group tax rates into classes for easy product assignment
              </CardDescription>
            </div>
            <Button onClick={() => setShowClassDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Class
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {taxClasses.map((taxClass) => (
              <div key={taxClass.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h4 className="font-medium">{taxClass.name}</h4>
                      <Badge variant="outline">
                        {taxClass.rates.length} rate{taxClass.rates.length !== 1 ? 's' : ''}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {taxClass.description}
                    </p>
                    {taxClass.rates.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {taxClass.rates.map((rateId) => {
                          const rate = taxRates.find(r => r.id === rateId)
                          return rate ? (
                            <Badge key={rateId} variant="secondary" className="text-xs">
                              {rate.name} ({formatRate(rate)})
                            </Badge>
                          ) : null
                        })}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm">
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => deleteClass(taxClass.id)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
