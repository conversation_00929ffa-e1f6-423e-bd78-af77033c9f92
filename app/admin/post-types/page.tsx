'use client'

// Custom Post Types Management Page
// Admin interface for managing all post types

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Eye, 
  Trash2,
  Settings,
  FileText,
  Archive,
  Shield,
  Grid3X3
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { PostType } from '@/lib/posts/types'
import { toast } from 'sonner'

export default function PostTypesManagementPage() {
  const router = useRouter()
  const [postTypes, setPostTypes] = useState<PostType[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  // Fetch post types
  const fetchPostTypes = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/post-types?includeInactive=true')
      const data = await response.json()

      if (data.success) {
        setPostTypes(data.data)
      }
    } catch (error) {
      console.error('Error fetching post types:', error)
      toast.error('Failed to fetch post types')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPostTypes()
  }, [])

  // Filter post types based on search
  const filteredPostTypes = postTypes.filter(postType =>
    postType.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    postType.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    postType.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Handle post type actions
  const handleEdit = (postType: PostType) => {
    router.push(`/admin/post-types/edit/${postType.name}`)
  }

  const handleView = (postType: PostType) => {
    // Navigate to posts of this type
    router.push(`/admin/posts?postType=${postType.name}`)
  }

  const handleDeactivate = async (postType: PostType) => {
    if (postType.isSystem) {
      toast.error('Cannot deactivate system post types')
      return
    }

    if (confirm(`Are you sure you want to deactivate "${postType.label}"?`)) {
      try {
        const response = await fetch(`/api/post-types/${postType.name}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ isActive: false })
        })

        if (response.ok) {
          toast.success('Post type deactivated')
          fetchPostTypes()
        } else {
          toast.error('Failed to deactivate post type')
        }
      } catch (error) {
        console.error('Error deactivating post type:', error)
        toast.error('Failed to deactivate post type')
      }
    }
  }

  const getStatusBadge = (postType: PostType) => {
    if (!postType.isActive) {
      return <Badge variant="secondary">Inactive</Badge>
    }
    if (postType.isSystem) {
      return <Badge variant="outline">System</Badge>
    }
    return <Badge variant="default">Active</Badge>
  }

  const getFeatureIcons = (postType: PostType) => {
    const features = []
    if (postType.supportsContent) features.push({ icon: FileText, label: 'Content' })
    if (postType.supportsThumbnail) features.push({ icon: Eye, label: 'Featured Image' })
    if (postType.supportsComments) features.push({ icon: Settings, label: 'Comments' })
    if (postType.supportsPageBuilder) features.push({ icon: Grid3X3, label: 'Page Builder' })
    if (postType.hasArchive) features.push({ icon: Archive, label: 'Archive' })
    if (postType.isHierarchical) features.push({ icon: Shield, label: 'Hierarchical' })
    
    return features.slice(0, 4) // Show only first 4
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Post Types</h1>
          <p className="text-muted-foreground">
            Manage content types for your application
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => router.push('/admin/post-types/templates')}
          >
            <Plus className="h-4 w-4 mr-2" />
            From Template
          </Button>
          <Button onClick={() => router.push('/admin/post-types/wizard')}>
            <Plus className="h-4 w-4 mr-2" />
            Create Custom
          </Button>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search post types..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Post Types Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Post Types ({filteredPostTypes.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : filteredPostTypes.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No post types found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm 
                  ? 'Try adjusting your search terms' 
                  : 'Get started by creating your first custom post type'
                }
              </p>
              {!searchTerm && (
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    onClick={() => router.push('/admin/post-types/templates')}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    From Template
                  </Button>
                  <Button onClick={() => router.push('/admin/post-types/wizard')}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Custom
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Post Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Features</TableHead>
                  <TableHead>Taxonomies</TableHead>
                  <TableHead>Menu Position</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPostTypes.map((postType) => (
                  <TableRow key={postType.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <span className="text-lg">{postType.icon}</span>
                        <div>
                          <div className="font-medium">{postType.label}</div>
                          <div className="text-sm text-muted-foreground">
                            {postType.name}
                          </div>
                          {postType.description && (
                            <div className="text-xs text-muted-foreground max-w-md truncate">
                              {postType.description}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(postType)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {getFeatureIcons(postType).map((feature, index) => (
                          <div
                            key={index}
                            className="p-1 rounded bg-muted"
                            title={feature.label}
                          >
                            <feature.icon className="h-3 w-3" />
                          </div>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {postType.taxonomies.slice(0, 2).map((taxonomy) => (
                          <Badge key={taxonomy} variant="outline" className="text-xs">
                            {taxonomy}
                          </Badge>
                        ))}
                        {postType.taxonomies.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{postType.taxonomies.length - 2}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary" className="text-xs">
                        {postType.menuPosition}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEdit(postType)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleView(postType)}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Posts
                          </DropdownMenuItem>
                          {!postType.isSystem && (
                            <DropdownMenuItem 
                              onClick={() => handleDeactivate(postType)}
                              className="text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Deactivate
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {postTypes.filter(pt => pt.isActive).length}
            </div>
            <div className="text-sm text-muted-foreground">Active Post Types</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {postTypes.filter(pt => pt.isSystem).length}
            </div>
            <div className="text-sm text-muted-foreground">System Post Types</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {postTypes.filter(pt => !pt.isSystem).length}
            </div>
            <div className="text-sm text-muted-foreground">Custom Post Types</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {postTypes.filter(pt => pt.supportsPageBuilder).length}
            </div>
            <div className="text-sm text-muted-foreground">Page Builder Enabled</div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
