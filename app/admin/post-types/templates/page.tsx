'use client'

// Post Type Templates Page
// Template-based post type creation interface

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { PostTypeTemplateSelector } from '@/lib/posts/components/post-type-template-selector'
import { PostTypeBuilder } from '@/lib/posts/components/post-type-builder'
import { PostTypeTemplate } from '@/lib/posts/templates/post-type-templates'
import { CreatePostTypeInput } from '@/lib/posts/types'
import { toast } from 'sonner'

type ViewMode = 'templates' | 'builder'

export default function PostTypeTemplatesPage() {
  const router = useRouter()
  const [viewMode, setViewMode] = useState<ViewMode>('templates')
  const [selectedTemplate, setSelectedTemplate] = useState<PostTypeTemplate | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const handleSelectTemplate = (template: PostTypeTemplate) => {
    setSelectedTemplate(template)
    setViewMode('builder')
  }

  const handleCreateCustom = () => {
    setSelectedTemplate(null)
    setViewMode('builder')
  }

  const handleSave = async (data: CreatePostTypeInput) => {
    try {
      setIsLoading(true)
      
      // Create the post type
      const response = await fetch('/api/post-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (result.success) {
        // If using a template, create suggested taxonomies and fields
        if (selectedTemplate) {
          await createSuggestedTaxonomies(selectedTemplate, data.name)
          await createSuggestedFields(selectedTemplate, result.data.id)
        }

        toast.success(`Post type "${data.label}" created successfully!`)
        router.push('/admin/post-types')
      } else {
        toast.error(result.error || 'Failed to create post type')
      }
    } catch (error) {
      console.error('Error creating post type:', error)
      toast.error('Failed to create post type')
    } finally {
      setIsLoading(false)
    }
  }

  const createSuggestedTaxonomies = async (template: PostTypeTemplate, postTypeName: string) => {
    if (!template.suggestedTaxonomies) return

    for (const taxonomy of template.suggestedTaxonomies) {
      try {
        await fetch('/api/taxonomies', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...taxonomy,
            postTypes: [postTypeName]
          }),
        })
      } catch (error) {
        console.error('Error creating taxonomy:', error)
      }
    }
  }

  const createSuggestedFields = async (template: PostTypeTemplate, postTypeId: string) => {
    if (!template.suggestedFields) return

    // This would integrate with a custom fields system
    // For now, we'll just log the suggested fields
    console.log('Suggested fields for post type:', template.suggestedFields)
  }

  const handleBack = () => {
    if (viewMode === 'builder') {
      setViewMode('templates')
      setSelectedTemplate(null)
    } else {
      router.push('/admin/post-types')
    }
  }

  const handlePreview = () => {
    if (selectedTemplate) {
      toast.info(`Preview of ${selectedTemplate.name} template functionality`)
    } else {
      toast.info('Preview functionality for custom post type')
    }
  }

  if (viewMode === 'templates') {
    return (
      <PostTypeTemplateSelector
        onSelectTemplate={handleSelectTemplate}
        onCreateCustom={handleCreateCustom}
      />
    )
  }

  return (
    <div>
      {/* Template Info Banner */}
      {selectedTemplate && (
        <div className="bg-primary/5 border border-primary/20 rounded-lg p-4 mb-6 mx-6">
          <div className="flex items-center gap-3">
            <span className="text-2xl">{selectedTemplate.icon}</span>
            <div>
              <h3 className="font-medium">Using {selectedTemplate.name} Template</h3>
              <p className="text-sm text-muted-foreground">
                {selectedTemplate.description}
              </p>
            </div>
          </div>
          
          {(selectedTemplate.suggestedTaxonomies || selectedTemplate.suggestedFields) && (
            <div className="mt-3 pt-3 border-t border-primary/20">
              <div className="text-sm">
                <strong>Will be created:</strong>
                {selectedTemplate.suggestedTaxonomies && (
                  <span className="ml-2">
                    {selectedTemplate.suggestedTaxonomies.length} taxonomies
                  </span>
                )}
                {selectedTemplate.suggestedFields && (
                  <span className="ml-2">
                    {selectedTemplate.suggestedFields.length} custom fields
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      <PostTypeBuilder
        postType={selectedTemplate ? {
          ...selectedTemplate.config,
          id: '',
          isSystem: false,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        } : undefined}
        onSave={handleSave}
        onPreview={handlePreview}
        isLoading={isLoading}
      />
    </div>
  )
}
