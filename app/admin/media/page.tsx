'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Upload,
  Search,
  Grid3X3,
  List,
  Filter,
  MoreHorizontal,
  Eye,
  Download,
  Edit,
  Trash2,
  Image,
  Video,
  Music,
  FileText,
  Database,
  RefreshCw,
  SortAsc,
  SortDesc
} from 'lucide-react'
import { useMedia, MediaFile, MediaFilters } from '@/hooks/use-media'
import { MediaUploadDialog } from '@/components/admin/media/media-upload-dialog'
import { MediaGrid } from '@/components/admin/media/media-grid'
import { MediaList } from '@/components/admin/media/media-list'
import { MediaPreviewDialog } from '@/components/admin/media/media-preview-dialog'
import { formatFileSize } from '@/lib/utils'

export default function MediaPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showUploadDialog, setShowUploadDialog] = useState(false)
  const [selectedFile, setSelectedFile] = useState<MediaFile | null>(null)
  const [showPreviewDialog, setShowPreviewDialog] = useState(false)

  const {
    files,
    pagination,
    loading,
    error,
    filters,
    setFilters,
    setPage,
    refetch,
    uploadFiles,
    deleteFile,
    updateFile
  } = useMedia({
    autoFetch: true,
    initialLimit: 24
  })

  const handleFilterChange = (key: keyof MediaFilters, value: string) => {
    setFilters({
      ...filters,
      [key]: value === 'all' ? undefined : value
    })
    setPage(1) // Reset to first page when filtering
  }

  const handleSearch = (search: string) => {
    setFilters({
      ...filters,
      search: search || undefined
    })
    setPage(1)
  }

  const handleFilePreview = (file: MediaFile) => {
    setSelectedFile(file)
    setShowPreviewDialog(true)
  }

  const handleFileDelete = async (file: MediaFile) => {
    if (confirm(`Are you sure you want to delete "${file.name}"?`)) {
      try {
        await deleteFile(file.id)
      } catch (error) {
        // Error is handled by the hook
      }
    }
  }

  const handleFileDownload = (file: MediaFile) => {
    window.open(file.downloadUrl, '_blank')
  }

  const getFileTypeIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <Image className="h-4 w-4" />
      case 'video':
        return <Video className="h-4 w-4" />
      case 'audio':
        return <Music className="h-4 w-4" />
      case 'document':
        return <FileText className="h-4 w-4" />
      default:
        return <Database className="h-4 w-4" />
    }
  }

  const getFileTypeColor = (type: string) => {
    switch (type) {
      case 'image':
        return 'text-green-600'
      case 'video':
        return 'text-blue-600'
      case 'audio':
        return 'text-purple-600'
      case 'document':
        return 'text-orange-600'
      default:
        return 'text-gray-600'
    }
  }

  // Calculate stats
  const stats = {
    total: pagination.total,
    images: files.filter(f => f.type === 'image').length,
    videos: files.filter(f => f.type === 'video').length,
    documents: files.filter(f => f.type === 'document').length,
    totalSize: files.reduce((sum, file) => sum + file.size, 0)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Media Library</h1>
          <p className="text-muted-foreground">
            Manage your files, images, and media assets
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={refetch} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
            <DialogTrigger asChild>
              <Button>
                <Upload className="mr-2 h-4 w-4" />
                Upload Files
              </Button>
            </DialogTrigger>
            <MediaUploadDialog
              onUpload={uploadFiles}
              onClose={() => setShowUploadDialog(false)}
            />
          </Dialog>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Database className="h-4 w-4 text-muted-foreground" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Total Files</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Image className="h-4 w-4 text-green-600" />
              </div>
              <p className="text-sm font-medium text-muted-foreground">Images</p>
              <p className="text-2xl font-bold text-green-600">{stats.images}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Video className="h-4 w-4 text-blue-600" />
              </div>
              <p className="text-sm font-medium text-muted-foreground">Videos</p>
              <p className="text-2xl font-bold text-blue-600">{stats.videos}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <FileText className="h-4 w-4 text-orange-600" />
              </div>
              <p className="text-sm font-medium text-muted-foreground">Documents</p>
              <p className="text-2xl font-bold text-orange-600">{stats.documents}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Storage Used</p>
              <p className="text-lg font-bold">{formatFileSize(stats.totalSize)}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Media Files</CardTitle>
              <CardDescription>
                Browse and manage your uploaded files
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search files..."
                  className="pl-10"
                  value={filters.search || ''}
                  onChange={(e) => handleSearch(e.target.value)}
                />
              </div>
            </div>
            
            <Select
              value={filters.type || 'all'}
              onValueChange={(value) => handleFilterChange('type', value)}
            >
              <SelectTrigger className="w-[140px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="image">Images</SelectItem>
                <SelectItem value="video">Videos</SelectItem>
                <SelectItem value="audio">Audio</SelectItem>
                <SelectItem value="document">Documents</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={`${filters.sortBy || 'createdAt'}-${filters.sortOrder || 'desc'}`}
              onValueChange={(value) => {
                const [sortBy, sortOrder] = value.split('-')
                setFilters({
                  ...filters,
                  sortBy: sortBy as any,
                  sortOrder: sortOrder as any
                })
              }}
            >
              <SelectTrigger className="w-[140px]">
                {filters.sortOrder === 'asc' ? (
                  <SortAsc className="mr-2 h-4 w-4" />
                ) : (
                  <SortDesc className="mr-2 h-4 w-4" />
                )}
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="createdAt-desc">Newest First</SelectItem>
                <SelectItem value="createdAt-asc">Oldest First</SelectItem>
                <SelectItem value="name-asc">Name A-Z</SelectItem>
                <SelectItem value="name-desc">Name Z-A</SelectItem>
                <SelectItem value="size-desc">Largest First</SelectItem>
                <SelectItem value="size-asc">Smallest First</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* File Display */}
          {viewMode === 'grid' ? (
            <MediaGrid
              files={files}
              loading={loading}
              onPreview={handleFilePreview}
              onDelete={handleFileDelete}
              onDownload={handleFileDownload}
            />
          ) : (
            <MediaList
              files={files}
              loading={loading}
              onPreview={handleFilePreview}
              onDelete={handleFileDelete}
              onDownload={handleFileDownload}
            />
          )}

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <p className="text-sm text-muted-foreground">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                {pagination.total} files
              </p>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                >
                  Previous
                </Button>
                <span className="text-sm">
                  Page {pagination.page} of {pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(pagination.page + 1)}
                  disabled={pagination.page >= pagination.totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Preview Dialog */}
      {selectedFile && (
        <MediaPreviewDialog
          file={selectedFile}
          open={showPreviewDialog}
          onOpenChange={setShowPreviewDialog}
          onUpdate={updateFile}
          onDelete={handleFileDelete}
          onDownload={handleFileDownload}
        />
      )}
    </div>
  )
}
