'use client'

import { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { DataTable, DataTableColumn, DataTableAction } from '@/components/admin/data-table'
import { OrderFilters, type OrderFilters as OrderFiltersType } from '@/components/admin/order-filters'
import { OrderBulkActions } from '@/components/admin/order-bulk-actions'
import { OrderStatus } from '@/components/admin/order-status'
import { useZarFormatter } from '@/components/admin/zar-price-input'
import {
  ShoppingCart,
  Eye,
  Download,
  Filter,
  Calendar,
  Plus,
  RefreshCw,
  MoreHorizontal,
  Edit,
  Trash2
} from 'lucide-react'
import Link from 'next/link'
import { useOrders } from '@/lib/ecommerce/hooks/use-orders'

// Import the actual Order type from the ecommerce library
import { Order } from '@/lib/ecommerce/types'

export default function OrdersPage() {
  const [filters, setFilters] = useState<OrderFiltersType>({})
  const [selectedOrders, setSelectedOrders] = useState<Order[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const { formatPrice } = useZarFormatter()

  const { orders, loading, error, refetch } = useOrders({
    initialParams: {
      page: 1,
      limit: 100,
      sort: {
        field: 'createdAt',
        direction: 'desc'
      }
    },
    autoFetch: true
  })

  // Transform orders to ensure compatibility with UI expectations
  const transformedOrders: Order[] = orders.map(order => ({
    ...order,
    // Ensure total is a proper Money object
    total: typeof order.total === 'object' && order.total !== null && 'amount' in order.total 
      ? order.total 
      : { amount: typeof order.total === 'number' ? order.total : 0, currency: 'ZAR' },
    // Ensure dates are Date objects
    createdAt: order.createdAt instanceof Date ? order.createdAt : new Date(order.createdAt),
    updatedAt: order.updatedAt instanceof Date ? order.updatedAt : new Date(order.updatedAt)
  }))

  // Filter orders based on current filters
  const filteredOrders = transformedOrders.filter(order => {
    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      const matchesSearch =
        order.orderNumber.toLowerCase().includes(searchLower) ||
        order.customer?.email?.toLowerCase().includes(searchLower) ||
        order.customer?.firstName?.toLowerCase().includes(searchLower) ||
        order.customer?.lastName?.toLowerCase().includes(searchLower)

      if (!matchesSearch) return false
    }

    // Status filter
    if (filters.status && filters.status.length > 0) {
      if (!filters.status.includes(order.status)) return false
    }

    // Payment status filter
    if (filters.paymentStatus && filters.paymentStatus.length > 0) {
      if (!filters.paymentStatus.includes(order.paymentStatus)) return false
    }

    // Date range filter
    if (filters.dateRange?.from || filters.dateRange?.to) {
      const orderDate = new Date(order.createdAt)
      if (filters.dateRange.from && orderDate < filters.dateRange.from) return false
      if (filters.dateRange.to && orderDate > filters.dateRange.to) return false
    }

    // Customer email filter
    if (filters.customerEmail) {
      if (!order.customer?.email?.toLowerCase().includes(filters.customerEmail.toLowerCase())) return false
    }

    // Order number filter
    if (filters.orderNumber) {
      if (!order.orderNumber.toLowerCase().includes(filters.orderNumber.toLowerCase())) return false
    }

    // Amount filters
    const totalAmount = order.total.amount
    if (filters.minAmount && totalAmount < filters.minAmount) return false
    if (filters.maxAmount && totalAmount > filters.maxAmount) return false

    // Shipping method filter
    if (filters.shippingMethod && order.shippingMethod?.id !== filters.shippingMethod) return false

    return true
  })

  const formatDate = (date: Date | string) => {
    const dateObj = date instanceof Date ? date : new Date(date)
    return dateObj.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Handle bulk actions
  const handleBulkAction = useCallback(async (action: string, data?: any) => {
    if (selectedOrders.length === 0) return

    setIsLoading(true)
    try {
      const orderIds = selectedOrders.map(order => order.id)

      const response = await fetch('/api/e-commerce/orders/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-request': 'true'
        },
        body: JSON.stringify({
          action: action,
          orderIds,
          data
        })
      })

      const result = await response.json()

      if (result.success) {
        // Refresh orders data
        await refetch()
        setSelectedOrders([])
        // Show success message
        console.log('Bulk action completed:', result.message)
      } else {
        console.error('Bulk action failed:', result.error)
      }
    } catch (error) {
      console.error('Bulk action error:', error)
    } finally {
      setIsLoading(false)
    }
  }, [selectedOrders, refetch])

  // Handle order status change
  const handleStatusChange = useCallback(async (orderId: string, newStatus: Order['status']) => {
    try {
      const response = await fetch(`/api/e-commerce/orders/${orderId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-request': 'true'
        },
        body: JSON.stringify({ status: newStatus })
      })

      const result = await response.json()

      if (result.success) {
        await refetch()
        console.log('Order status updated successfully')
      } else {
        console.error('Failed to update order status:', result.error)
      }
    } catch (error) {
      console.error('Status update error:', error)
    }
  }, [refetch])

  // Handle payment status change
  const handlePaymentStatusChange = useCallback(async (orderId: string, newStatus: Order['paymentStatus']) => {
    try {
      const response = await fetch('/api/e-commerce/orders/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-request': 'true'
        },
        body: JSON.stringify({
          action: 'updatePaymentStatus',
          orderIds: [orderId],
          data: { paymentStatus: newStatus }
        })
      })

      const result = await response.json()

      if (result.success) {
        await refetch()
        console.log('Payment status updated successfully')
      } else {
        console.error('Failed to update payment status:', result.error)
      }
    } catch (error) {
      console.error('Payment status update error:', error)
    }
  }, [refetch])

  // Handle export
  const handleExport = useCallback(async () => {
    try {
      const response = await fetch('/api/e-commerce/orders/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-request': 'true'
        },
        body: JSON.stringify({
          format: 'csv',
          filters: filters
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `orders_export_${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        console.error('Export failed')
      }
    } catch (error) {
      console.error('Export error:', error)
    }
  }, [filters])

  const columns: DataTableColumn<Order>[] = [
    {
      key: 'select',
      title: '',
      render: (order) => {
        // Find the original order for selection
        const originalOrder = orders.find(o => o.id === order.id)
        return (
          <Checkbox
            checked={selectedOrders.some(selected => selected.id === order.id)}
            onCheckedChange={(checked) => {
              if (checked && originalOrder) {
                setSelectedOrders([...selectedOrders, originalOrder])
              } else {
                setSelectedOrders(selectedOrders.filter(selected => selected.id !== order.id))
              }
            }}
          />
        )
      }
    },
    {
      key: 'orderNumber',
      title: 'Order',
      searchable: true,
      sortable: true,
      render: (order) => (
        <div>
          <div className="font-medium">#{order.orderNumber}</div>
          <div className="text-sm text-muted-foreground">
            {formatDate(order.createdAt)}
          </div>
        </div>
      )
    },
    {
      key: 'customer',
      title: 'Customer',
      searchable: true,
      render: (order) => (
        <div>
          <div className="font-medium">
            {order.customer?.firstName} {order.customer?.lastName}
          </div>
          <div className="text-sm text-muted-foreground">
            {order.customer?.email}
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      render: (order) => (
        <OrderStatus
          order={order}
          onStatusChange={handleStatusChange}
          onPaymentStatusChange={handlePaymentStatusChange}
          compact={true}
          showActions={false}
        />
      )
    },
    {
      key: 'total',
      title: 'Total',
      sortable: true,
      render: (order) => {
        return (
          <div className="text-right">
            <div className="font-medium">{formatPrice(order.total.amount || 0)}</div>
            <div className="text-sm text-muted-foreground">
              {typeof order.paymentMethod === 'string' ? order.paymentMethod : order.paymentMethod?.type || 'Unknown'}
            </div>
          </div>
        )
      }
    },
    {
      key: 'items',
      title: 'Items',
      render: (order) => (
        <div className="text-center">
          <div className="font-medium">{order.items?.length || 0}</div>
          <div className="text-sm text-muted-foreground">
            {order.items?.reduce((sum, item) => sum + item.quantity, 0) || 0} qty
          </div>
        </div>
      )
    }
  ]

  const actions: DataTableAction<Order>[] = [
    {
      label: 'View Details',
      icon: Eye,
      onClick: (order) => {
        window.location.href = `/admin/orders/${order.id}`
      }
    },
    {
      label: 'Edit Order',
      icon: Edit,
      onClick: (order) => {
        window.location.href = `/admin/orders/${order.id}/edit`
      }
    },
    {
      label: 'Download Invoice',
      icon: Download,
      onClick: (order) => {
        console.log('Download invoice for order:', order.id)
      }
    }
  ]

  const orderStats = {
    total: filteredOrders.length,
    pending: filteredOrders.filter(o => o.status === 'pending').length,
    processing: filteredOrders.filter(o => o.status === 'processing').length,
    shipped: filteredOrders.filter(o => o.status === 'shipped').length,
    delivered: filteredOrders.filter(o => o.status === 'delivered').length,
    cancelled: filteredOrders.filter(o => o.status === 'cancelled').length,
    totalRevenue: filteredOrders
      .filter(o => o.paymentStatus === 'paid')
      .reduce((sum, order) => {
        return sum + (order.total.amount || 0)
      }, 0)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Orders</h1>
          <p className="text-muted-foreground">
            Manage customer orders and track sales
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleExport} disabled={isLoading}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button asChild>
            <Link href="/admin/orders/new">
              <Plus className="mr-2 h-4 w-4" />
              New Order
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Total</p>
                <p className="text-2xl font-bold">{orderStats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">{orderStats.pending}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Processing</p>
              <p className="text-2xl font-bold text-blue-600">{orderStats.processing}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Shipped</p>
              <p className="text-2xl font-bold text-purple-600">{orderStats.shipped}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Delivered</p>
              <p className="text-2xl font-bold text-green-600">{orderStats.delivered}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Revenue</p>
              <p className="text-lg font-bold">{formatPrice(orderStats.totalRevenue)}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <OrderFilters
        filters={filters}
        onFiltersChange={setFilters}
        onExport={handleExport}
        isLoading={isLoading}
      />

      {/* Bulk Actions */}
      <OrderBulkActions
        selectedOrders={selectedOrders}
        onBulkAction={handleBulkAction}
        onClearSelection={() => setSelectedOrders([])}
        isLoading={isLoading}
      />

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Order Management</CardTitle>
              <CardDescription>
                {filteredOrders.length} order{filteredOrders.length !== 1 ? 's' : ''} found
                {selectedOrders.length > 0 && ` • ${selectedOrders.length} selected`}
              </CardDescription>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                disabled={loading}
              >
                <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            data={filteredOrders}
            columns={columns}
            actions={actions}
            loading={loading}
            searchPlaceholder="Search orders by number, customer name, or email..."
            emptyMessage="No orders found matching your criteria."
            emptyIcon={ShoppingCart}
            pageSize={20}
          />
        </CardContent>
      </Card>
    </div>
  )
}
