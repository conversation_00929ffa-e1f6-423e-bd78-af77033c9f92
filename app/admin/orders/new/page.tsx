'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { OrderForm } from '@/components/admin/order-form'
import type { Order } from '@/lib/ecommerce/types'

export default function NewOrderPage() {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleSubmit = async (data: any) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/e-commerce/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-request': 'true'
        },
        body: JSON.stringify(data)
      })

      const result = await response.json()

      if (result.success) {
        // Redirect to the new order's detail page
        router.push(`/admin/orders/${result.data.id}`)
      } else {
        console.error('Failed to create order:', result.error)
        // Show error message to user
      }
    } catch (error) {
      console.error('Order creation error:', error)
      // Show error message to user
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    router.push('/admin/orders')
  }

  return (
    <div className="container mx-auto py-6">
      <OrderForm
        mode="create"
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isLoading={isLoading}
      />
    </div>
  )
}
