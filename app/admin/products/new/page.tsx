'use client'

import { useRouter } from 'next/navigation'
import { ProductForm } from '@/components/admin/products/product-form'
import { useProductMutations } from '@/lib/ecommerce/hooks/use-products'
import { toast } from 'sonner'
import type { CreateProductInput } from '@/lib/ecommerce/types'

export default function NewProductPage() {
  const router = useRouter()
  const { createProduct, loading: isCreating } = useProductMutations()

  const handleSubmit = async (data: CreateProductInput) => {
    try {
      const product = await createProduct(data)

      if (product) {
        toast.success('Product created successfully')
        router.push(`/admin/products/${product.id}`)
      }
    } catch (error) {
      toast.error('Failed to create product')
      console.error('Error creating product:', error)
    }
  }

  const handleCancel = () => {
    router.push('/admin/products')
  }

  return (
    <ProductForm
      onSubmit={handleSubmit}
      onCancel={handleCancel}
      loading={isCreating}
    />
  )
}
