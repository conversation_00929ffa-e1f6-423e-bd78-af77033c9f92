'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Plus, 
  Edit, 
  Trash2, 
  MoreHorizontal, 
  FolderOpen, 
  Eye, 
  EyeOff,
  Save,
  X,
  Alert<PERSON>riangle,
  Loader2
} from 'lucide-react'
import { useCategories, useCategoryMutations } from '@/lib/ecommerce/hooks/use-categories'
import { toast } from 'sonner'
import type { ProductCategory, CreateCategoryInput, UpdateCategoryInput } from '@/lib/ecommerce/hooks/use-categories'

export default function CategoriesPage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<ProductCategory | null>(null)
  const [searchQuery, setSearchQuery] = useState('')

  const { categories, loading, error, refetch } = useCategories()
  const { 
    createCategory, 
    updateCategory, 
    deleteCategory, 
    loading: mutationLoading, 
    error: mutationError,
    clearError 
  } = useCategoryMutations()

  // Form state
  const [formData, setFormData] = useState<CreateCategoryInput>({
    name: '',
    slug: '',
    description: '',
    image: '',
    parentId: '',
    position: 0,
    isVisible: true,
    seoTitle: '',
    seoDescription: ''
  })

  // Filter categories based on search
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.description?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const resetForm = () => {
    setFormData({
      name: '',
      slug: '',
      description: '',
      image: '',
      parentId: '',
      position: 0,
      isVisible: true,
      seoTitle: '',
      seoDescription: ''
    })
  }

  const handleCreate = async () => {
    try {
      const result = await createCategory(formData)
      if (result) {
        toast.success('Category created successfully')
        setIsCreateDialogOpen(false)
        resetForm()
        refetch()
      }
    } catch (error) {
      toast.error('Failed to create category')
    }
  }

  const handleEdit = (category: ProductCategory) => {
    setSelectedCategory(category)
    setFormData({
      name: category.name,
      slug: category.slug,
      description: category.description || '',
      image: category.image || '',
      parentId: category.parentId || '',
      position: category.position,
      isVisible: category.isVisible,
      seoTitle: category.seoTitle || '',
      seoDescription: category.seoDescription || ''
    })
    setIsEditDialogOpen(true)
  }

  const handleUpdate = async () => {
    if (!selectedCategory) return

    try {
      const result = await updateCategory({
        id: selectedCategory.id,
        ...formData
      })
      if (result) {
        toast.success('Category updated successfully')
        setIsEditDialogOpen(false)
        setSelectedCategory(null)
        resetForm()
        refetch()
      }
    } catch (error) {
      toast.error('Failed to update category')
    }
  }

  const handleDelete = async (category: ProductCategory) => {
    if (!confirm(`Are you sure you want to delete "${category.name}"?`)) return

    try {
      const result = await deleteCategory(category.id)
      if (result) {
        toast.success('Category deleted successfully')
        refetch()
      }
    } catch (error) {
      toast.error('Failed to delete category')
    }
  }

  const handleInputChange = (field: keyof CreateCategoryInput, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Auto-generate slug from name
    if (field === 'name' && typeof value === 'string') {
      const slug = value
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()

      setFormData(prev => ({
        ...prev,
        slug
      }))
    }
  }

  const CategoryForm = ({ isEdit = false }: { isEdit?: boolean }) => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Category Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="Enter category name"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="slug">URL Slug</Label>
          <Input
            id="slug"
            value={formData.slug}
            onChange={(e) => handleInputChange('slug', e.target.value)}
            placeholder="category-slug"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="Category description"
          rows={3}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="position">Position</Label>
          <Input
            id="position"
            type="number"
            value={formData.position}
            onChange={(e) => handleInputChange('position', parseInt(e.target.value) || 0)}
            placeholder="0"
          />
        </div>
        <div className="flex items-center space-x-2 pt-6">
          <Switch
            id="isVisible"
            checked={formData.isVisible}
            onCheckedChange={(checked) => handleInputChange('isVisible', checked)}
          />
          <Label htmlFor="isVisible">Visible</Label>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="seoTitle">SEO Title</Label>
          <Input
            id="seoTitle"
            value={formData.seoTitle}
            onChange={(e) => handleInputChange('seoTitle', e.target.value)}
            placeholder="SEO optimized title"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="seoDescription">SEO Description</Label>
          <Input
            id="seoDescription"
            value={formData.seoDescription}
            onChange={(e) => handleInputChange('seoDescription', e.target.value)}
            placeholder="SEO meta description"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button 
          variant="outline" 
          onClick={() => {
            if (isEdit) {
              setIsEditDialogOpen(false)
              setSelectedCategory(null)
            } else {
              setIsCreateDialogOpen(false)
            }
            resetForm()
          }}
        >
          <X className="mr-2 h-4 w-4" />
          Cancel
        </Button>
        <Button 
          onClick={isEdit ? handleUpdate : handleCreate}
          disabled={mutationLoading || !formData.name.trim()}
        >
          {mutationLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <Save className="mr-2 h-4 w-4" />
          {isEdit ? 'Update' : 'Create'} Category
        </Button>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Categories</h1>
          <p className="text-muted-foreground">
            Organize your products into categories for better navigation
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Category
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Category</DialogTitle>
              <DialogDescription>
                Add a new category to organize your products
              </DialogDescription>
            </DialogHeader>
            <CategoryForm />
          </DialogContent>
        </Dialog>
      </div>

      {/* Error Display */}
      {(error || mutationError) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error || mutationError}
            <Button 
              variant="outline" 
              size="sm" 
              className="ml-2"
              onClick={() => {
                clearError()
                refetch()
              }}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Category Management</CardTitle>
          <CardDescription>
            View and manage all your product categories
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Input
              placeholder="Search categories..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
          </div>

          {/* Categories Table */}
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading categories...</span>
            </div>
          ) : filteredCategories.length === 0 ? (
            <div className="text-center py-8">
              <FolderOpen className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-semibold">No categories found</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {searchQuery ? 'No categories match your search.' : 'Get started by creating your first category.'}
              </p>
              {!searchQuery && (
                <Button className="mt-4" onClick={() => setIsCreateDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Category
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Slug</TableHead>
                  <TableHead>Products</TableHead>
                  <TableHead>Position</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-[70px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCategories.map((category) => (
                  <TableRow key={category.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{category.name}</div>
                        {category.description && (
                          <div className="text-sm text-muted-foreground">
                            {category.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {category.slug}
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {category.productCount || 0} products
                      </Badge>
                    </TableCell>
                    <TableCell>{category.position}</TableCell>
                    <TableCell>
                      {category.isVisible ? (
                        <Badge variant="default">
                          <Eye className="mr-1 h-3 w-3" />
                          Visible
                        </Badge>
                      ) : (
                        <Badge variant="secondary">
                          <EyeOff className="mr-1 h-3 w-3" />
                          Hidden
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEdit(category)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDelete(category)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Category</DialogTitle>
            <DialogDescription>
              Update category information and settings
            </DialogDescription>
          </DialogHeader>
          <CategoryForm isEdit />
        </DialogContent>
      </Dialog>
    </div>
  )
}
