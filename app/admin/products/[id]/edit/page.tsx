'use client'

import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Package } from 'lucide-react'
import Link from 'next/link'
import { useProduct, useProductMutations } from '@/lib/ecommerce/hooks/use-products'
import { ProductForm } from '@/components/admin/products/product-form'
import { toast } from 'sonner'
import type { UpdateProductInput } from '@/lib/ecommerce/types'

export default function ProductEditPage() {
  const params = useParams()
  const router = useRouter()
  const productId = params.id as string

  const { product, loading, error } = useProduct({ productId })
  const { updateProduct, loading: isUpdating } = useProductMutations()

  const handleSubmit = async (data: UpdateProductInput) => {
    try {
      const result = await updateProduct(data)
      if (result) {
        toast.success('Product updated successfully')
        router.push(`/admin/products/${productId}`)
      }
    } catch (error) {
      toast.error('Failed to update product')
      console.error('Error updating product:', error)
    }
  }

  const handleCancel = () => {
    router.push(`/admin/products/${productId}`)
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Link>
          </Button>
        </div>
        <div className="grid gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <Package className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Product not found</h3>
              <p className="mt-1 text-sm text-gray-500">
                The product you're trying to edit doesn't exist or has been deleted.
              </p>
              <div className="mt-6">
                <Button asChild>
                  <Link href="/admin/products">
                    Back to Products
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <ProductForm
      product={product}
      onSubmit={handleSubmit}
      onCancel={handleCancel}
      loading={isUpdating}
    />
  )
}
