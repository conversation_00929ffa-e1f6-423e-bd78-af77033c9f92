'use client'

import { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { DataTable, DataTableColumn, DataTableAction } from '@/components/admin/data-table'
import { useZarFormatter } from '@/components/admin/zar-price-input'
import {
  Package,
  AlertTriangle,
  TrendingDown,
  TrendingUp,
  Edit,
  Plus,
  Minus,
  BarChart3,
  Filter
} from 'lucide-react'
import Link from 'next/link'
import { useProducts } from '@/lib/ecommerce/hooks/use-products'
import type { Product } from '@/lib/ecommerce/types'

interface InventoryItem extends Product {
  stockValue: number
  stockStatus: 'in-stock' | 'low-stock' | 'out-of-stock' | 'not-tracked'
}

export default function InventoryPage() {
  const [stockFilter, setStockFilter] = useState<string>('all')
  const { formatPrice } = useZarFormatter()

  const { products, loading, error } = useProducts({
    initialParams: {
      page: 1,
      limit: 100,
      filters: { status: ['active'] }
    }
  })

  // Transform products into inventory items with calculated values
  const inventory: InventoryItem[] = useMemo(() => {
    return products.map(product => {
      const price = product.price?.amount || 0
      const stockQuantity = product.inventoryQuantity || 0
      const stockValue = price * stockQuantity

      let stockStatus: InventoryItem['stockStatus']
      if (!product.trackQuantity) {
        stockStatus = 'not-tracked'
      } else if (stockQuantity === 0) {
        stockStatus = 'out-of-stock'
      } else if (stockQuantity <= 5) {
        stockStatus = 'low-stock'
      } else {
        stockStatus = 'in-stock'
      }

      return {
        ...product,
        stockValue,
        stockStatus
      }
    })
  }, [products])

  const getStockBadgeVariant = (status: InventoryItem['stockStatus']) => {
    switch (status) {
      case 'in-stock':
        return 'default'
      case 'low-stock':
        return 'secondary'
      case 'out-of-stock':
        return 'destructive'
      case 'not-tracked':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const getStockStatusLabel = (status: InventoryItem['stockStatus']) => {
    switch (status) {
      case 'in-stock':
        return 'In Stock'
      case 'low-stock':
        return 'Low Stock'
      case 'out-of-stock':
        return 'Out of Stock'
      case 'not-tracked':
        return 'Not Tracked'
      default:
        return 'Unknown'
    }
  }

  // Filter inventory based on stock status
  const filteredInventory = inventory.filter(item => {
    if (stockFilter === 'all') return true
    return item.stockStatus === stockFilter
  })

  const columns: DataTableColumn<InventoryItem>[] = [
    {
      key: 'name',
      title: 'Product',
      searchable: true,
      sortable: true,
      render: (item) => (
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 rounded-md bg-gray-100 flex items-center justify-center">
            {item.images && item.images.length > 0 ? (
              <img
                src={item.images[0].url}
                alt={item.title}
                className="h-10 w-10 rounded-md object-cover"
              />
            ) : (
              <Package className="h-5 w-5 text-gray-400" />
            )}
          </div>
          <div>
            <div className="font-medium">{item.title}</div>
            <div className="text-sm text-muted-foreground">
              Handle: {item.handle || 'N/A'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'price',
      title: 'Price',
      sortable: true,
      render: (item) => (
        <div className="text-right">
          <div className="font-medium">{formatPrice(item.price?.amount || 0)}</div>
          {item.compareAtPrice && item.compareAtPrice.amount > item.price?.amount && (
            <div className="text-sm text-muted-foreground line-through">
              {formatPrice(item.compareAtPrice.amount)}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'inventoryQuantity',
      title: 'Stock Qty',
      sortable: true,
      render: (item) => (
        <div className="text-center">
          {item.trackQuantity ? (
            <div className="flex items-center justify-center space-x-2">
              <span className="font-medium">{item.inventoryQuantity || 0}</span>
              {item.stockStatus === 'low-stock' && (
                <AlertTriangle className="h-4 w-4 text-orange-500" />
              )}
            </div>
          ) : (
            <span className="text-muted-foreground">Not tracked</span>
          )}
        </div>
      )
    },
    {
      key: 'stockValue',
      title: 'Stock Value',
      sortable: true,
      render: (item) => (
        <div className="text-right">
          {item.trackQuantity ? (
            <span className="font-medium">{formatPrice(item.stockValue)}</span>
          ) : (
            <span className="text-muted-foreground">-</span>
          )}
        </div>
      )
    },
    {
      key: 'stockStatus',
      title: 'Status',
      sortable: true,
      render: (item) => (
        <Badge variant={getStockBadgeVariant(item.stockStatus)}>
          {getStockStatusLabel(item.stockStatus)}
        </Badge>
      )
    }
  ]

  const actions: DataTableAction<InventoryItem>[] = [
    {
      label: 'Edit Product',
      icon: Edit,
      onClick: (item) => {
        window.location.href = `/admin/products/${item.id}/edit`
      }
    },
    {
      label: 'Adjust Stock',
      icon: BarChart3,
      onClick: (item) => {
        // Open stock adjustment modal
        console.log('Adjust stock for:', item.title)
      }
    }
  ]

  const inventoryStats = {
    totalProducts: inventory.length,
    inStock: inventory.filter(item => item.stockStatus === 'in-stock').length,
    lowStock: inventory.filter(item => item.stockStatus === 'low-stock').length,
    outOfStock: inventory.filter(item => item.stockStatus === 'out-of-stock').length,
    totalValue: inventory
      .filter(item => item.trackQuantity)
      .reduce((sum, item) => sum + item.stockValue, 0),
    totalQuantity: inventory
      .filter(item => item.trackQuantity)
      .reduce((sum, item) => sum + (item.inventoryQuantity || 0), 0)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Inventory</h1>
          <p className="text-muted-foreground">
            Track stock levels and manage product inventory
          </p>
        </div>
        <Link href="/admin/products/new">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Product
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Package className="h-4 w-4 text-muted-foreground" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Total Products</p>
                <p className="text-2xl font-bold">{inventoryStats.totalProducts}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">In Stock</p>
              <p className="text-2xl font-bold text-green-600">{inventoryStats.inStock}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Low Stock</p>
              <p className="text-2xl font-bold text-orange-600">{inventoryStats.lowStock}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Out of Stock</p>
              <p className="text-2xl font-bold text-red-600">{inventoryStats.outOfStock}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Total Qty</p>
              <p className="text-2xl font-bold">{inventoryStats.totalQuantity}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-muted-foreground">Stock Value</p>
              <p className="text-lg font-bold">{formatPrice(inventoryStats.totalValue)}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Inventory Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Inventory Management</CardTitle>
              <CardDescription>
                Monitor stock levels and manage product availability
              </CardDescription>
            </div>
            
            {/* Stock Status Filter */}
            <Select value={stockFilter} onValueChange={setStockFilter}>
              <SelectTrigger className="w-[160px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Products</SelectItem>
                <SelectItem value="in-stock">In Stock</SelectItem>
                <SelectItem value="low-stock">Low Stock</SelectItem>
                <SelectItem value="out-of-stock">Out of Stock</SelectItem>
                <SelectItem value="not-tracked">Not Tracked</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            data={filteredInventory}
            columns={columns}
            actions={actions}
            loading={loading}
            searchPlaceholder="Search products by name or SKU..."
            emptyMessage="No products found."
            emptyIcon={Package}
            pageSize={20}
          />
        </CardContent>
      </Card>
    </div>
  )
}
