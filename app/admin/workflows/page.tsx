'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { DataTable, DataTableColumn, DataTableAction } from '@/components/admin/data-table'
import {
  Plus,
  Workflow,
  Play,
  Pause,
  Eye,
  Edit,
  Copy,
  Trash2,
  BarChart3,
  Zap,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'
import Link from 'next/link'
import { toast } from 'sonner'

interface WorkflowData {
  id: string
  name: string
  description: string
  category: string
  isActive: boolean
  tags: string[]
  createdAt: string
  updatedAt: string
  trigger: {
    type: string
    event?: string
  }
  steps: any[]
  metadata: any
}

interface WorkflowStats {
  totalWorkflows: number
  activeWorkflows: number
  totalExecutions: number
  categories: Record<string, number>
}

export default function WorkflowsPage() {
  const [workflows, setWorkflows] = useState<WorkflowData[]>([])
  const [stats, setStats] = useState<WorkflowStats | null>(null)
  const [templates, setTemplates] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [showTemplateDialog, setShowTemplateDialog] = useState(false)
  const [showTriggerDialog, setShowTriggerDialog] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null)

  // Load workflows and stats on mount
  useEffect(() => {
    loadWorkflows()
    loadStats()
    loadTemplates()
  }, [categoryFilter, statusFilter])

  const loadWorkflows = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (categoryFilter !== 'all') params.set('category', categoryFilter)
      if (statusFilter !== 'all') params.set('isActive', statusFilter)

      const response = await fetch(`/api/workflows?${params}`)
      if (response.ok) {
        const result = await response.json()
        setWorkflows(result.data || [])
      }
    } catch (error) {
      console.error('Error loading workflows:', error)
      toast.error('Failed to load workflows')
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await fetch('/api/workflows?action=stats', { method: 'OPTIONS' })
      if (response.ok) {
        const result = await response.json()
        setStats(result.data)
      }
    } catch (error) {
      console.error('Error loading stats:', error)
    }
  }

  const loadTemplates = async () => {
    try {
      const response = await fetch('/api/workflows?action=templates', { method: 'OPTIONS' })
      if (response.ok) {
        const result = await response.json()
        setTemplates(result.data || [])
      }
    } catch (error) {
      console.error('Error loading templates:', error)
    }
  }

  const handleCreateFromTemplate = async (template: any) => {
    try {
      const response = await fetch('/api/workflows', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fromTemplate: true,
          templateId: template.id,
          createdBy: 'admin'
        })
      })

      if (response.ok) {
        const result = await response.json()
        toast.success(`Workflow "${result.data.name}" created successfully!`)
        setShowTemplateDialog(false)
        loadWorkflows()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to create workflow')
      }
    } catch (error) {
      console.error('Error creating workflow:', error)
      toast.error('Failed to create workflow')
    }
  }

  const handleToggleActive = async (workflow: WorkflowData) => {
    try {
      const response = await fetch(`/api/workflows/${workflow.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: workflow.isActive ? 'deactivate' : 'activate'
        })
      })

      if (response.ok) {
        toast.success(`Workflow ${workflow.isActive ? 'deactivated' : 'activated'} successfully!`)
        loadWorkflows()
      } else {
        toast.error('Failed to update workflow')
      }
    } catch (error) {
      console.error('Error updating workflow:', error)
      toast.error('Failed to update workflow')
    }
  }

  const handleDuplicateWorkflow = async (workflow: WorkflowData) => {
    try {
      const response = await fetch(`/api/workflows/${workflow.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'duplicate',
          createdBy: 'admin'
        })
      })

      if (response.ok) {
        toast.success(`Workflow "${workflow.name}" duplicated successfully!`)
        loadWorkflows()
      } else {
        toast.error('Failed to duplicate workflow')
      }
    } catch (error) {
      console.error('Error duplicating workflow:', error)
      toast.error('Failed to duplicate workflow')
    }
  }

  const handleDeleteWorkflow = async (workflow: WorkflowData) => {
    if (!confirm(`Are you sure you want to delete "${workflow.name}"?`)) return

    try {
      const response = await fetch(`/api/workflows/${workflow.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast.success(`Workflow "${workflow.name}" deleted successfully!`)
        loadWorkflows()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to delete workflow')
      }
    } catch (error) {
      console.error('Error deleting workflow:', error)
      toast.error('Failed to delete workflow')
    }
  }

  const handleTriggerWorkflow = async (eventType: string, eventData: any) => {
    try {
      const response = await fetch('/api/workflows', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          eventType,
          eventData,
          context: { userId: 'admin', source: 'manual' }
        })
      })

      if (response.ok) {
        const result = await response.json()
        toast.success(`Triggered ${result.data.executionIds.length} workflow(s)!`)
        setShowTriggerDialog(false)
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to trigger workflow')
      }
    } catch (error) {
      console.error('Error triggering workflow:', error)
      toast.error('Failed to trigger workflow')
    }
  }

  const filteredWorkflows = workflows.filter(workflow => {
    if (categoryFilter !== 'all' && workflow.category !== categoryFilter) return false
    if (statusFilter !== 'all') {
      const isActive = statusFilter === 'true'
      if (workflow.isActive !== isActive) return false
    }
    return true
  })

  const columns: DataTableColumn<WorkflowData>[] = [
    {
      key: 'name',
      title: 'Workflow',
      searchable: true,
      sortable: true,
      render: (workflow) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <Workflow className="h-8 w-8 text-blue-500" />
          </div>
          <div>
            <div className="font-medium">{workflow.name}</div>
            <div className="text-sm text-muted-foreground">{workflow.description}</div>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      title: 'Category',
      sortable: true,
      render: (workflow) => (
        <Badge variant="outline">
          {workflow.category.replace('-', ' ').toUpperCase()}
        </Badge>
      )
    },
    {
      key: 'trigger',
      title: 'Trigger',
      render: (workflow) => (
        <div className="text-sm">
          <div className="font-medium">{workflow.trigger.type}</div>
          {workflow.trigger.event && (
            <div className="text-muted-foreground">{workflow.trigger.event}</div>
          )}
        </div>
      )
    },
    {
      key: 'steps',
      title: 'Steps',
      render: (workflow) => (
        <div className="text-sm">
          {workflow.steps.length} step{workflow.steps.length !== 1 ? 's' : ''}
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      render: (workflow) => (
        <div className="flex items-center space-x-2">
          {workflow.isActive ? (
            <>
              <CheckCircle className="h-4 w-4 text-green-500" />
              <Badge variant="default">Active</Badge>
            </>
          ) : (
            <>
              <XCircle className="h-4 w-4 text-gray-400" />
              <Badge variant="secondary">Inactive</Badge>
            </>
          )}
        </div>
      )
    },
    {
      key: 'tags',
      title: 'Tags',
      render: (workflow) => (
        <div className="flex flex-wrap gap-1">
          {workflow.tags.slice(0, 2).map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {workflow.tags.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{workflow.tags.length - 2}
            </Badge>
          )}
        </div>
      )
    },
    {
      key: 'updatedAt',
      title: 'Last Updated',
      sortable: true,
      render: (workflow) => (
        <div className="text-sm">
          {new Date(workflow.updatedAt).toLocaleDateString()}
        </div>
      )
    }
  ]

  const actions: DataTableAction<WorkflowData>[] = [
    {
      label: 'View Details',
      icon: Eye,
      onClick: (workflow) => {
        window.location.href = `/admin/workflows/${workflow.id}`
      }
    },
    {
      label: 'Analytics',
      icon: BarChart3,
      onClick: (workflow) => {
        window.location.href = `/admin/workflows/${workflow.id}/analytics`
      }
    },
    {
      label: workflow => workflow.isActive ? 'Deactivate' : 'Activate',
      icon: workflow => workflow.isActive ? Pause : Play,
      onClick: handleToggleActive
    },
    {
      label: 'Duplicate',
      icon: Copy,
      onClick: handleDuplicateWorkflow
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: handleDeleteWorkflow,
      variant: 'destructive'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">E-commerce Workflows</h1>
          <p className="text-muted-foreground">
            Automate your business processes with intelligent workflows
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => setShowTriggerDialog(true)}>
            <Zap className="mr-2 h-4 w-4" />
            Trigger Event
          </Button>
          <Dialog open={showTemplateDialog} onOpenChange={setShowTemplateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create Workflow
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[800px]">
              <DialogHeader>
                <DialogTitle>Create New Workflow</DialogTitle>
                <DialogDescription>
                  Choose from pre-built templates or create a custom workflow
                </DialogDescription>
              </DialogHeader>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                {templates.map((template) => (
                  <Card 
                    key={template.id} 
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => handleCreateFromTemplate(template)}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex items-center space-x-2">
                        <Workflow className="h-5 w-5 text-blue-500" />
                        <CardTitle className="text-sm">{template.name}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-xs text-muted-foreground mb-2">{template.description}</p>
                      <div className="flex items-center justify-between">
                        <Badge variant="outline" className="text-xs">
                          {template.category}
                        </Badge>
                        <div className="flex flex-wrap gap-1">
                          {template.tags.slice(0, 2).map((tag: string, index: number) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Workflows</CardTitle>
              <Workflow className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalWorkflows}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Workflows</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeWorkflows}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Executions</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalExecutions}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Categories</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{Object.keys(stats.categories).length}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Workflows Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Workflow Management</CardTitle>
              <CardDescription>
                Manage and monitor your automated business workflows
              </CardDescription>
            </div>
            
            {/* Filters */}
            <div className="flex items-center space-x-2">
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="customer">Customer</SelectItem>
                  <SelectItem value="order">Order</SelectItem>
                  <SelectItem value="inventory">Inventory</SelectItem>
                  <SelectItem value="marketing">Marketing</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="true">Active</SelectItem>
                  <SelectItem value="false">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            data={filteredWorkflows}
            columns={columns}
            actions={actions}
            loading={loading}
            searchPlaceholder="Search workflows by name or description..."
            emptyMessage="No workflows found. Create your first workflow!"
            emptyIcon={Workflow}
            pageSize={20}
          />
        </CardContent>
      </Card>

      {/* Trigger Event Dialog */}
      <Dialog open={showTriggerDialog} onOpenChange={setShowTriggerDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Trigger Workflow Event</DialogTitle>
            <DialogDescription>
              Manually trigger workflows by simulating events
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Event Type</Label>
              <Select onValueChange={(value) => console.log('Selected event:', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select event type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="customer.registered">Customer Registered</SelectItem>
                  <SelectItem value="order.paid">Order Paid</SelectItem>
                  <SelectItem value="cart.abandoned">Cart Abandoned</SelectItem>
                  <SelectItem value="inventory.low_stock">Low Stock</SelectItem>
                  <SelectItem value="order.shipped">Order Shipped</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Event Data (JSON)</Label>
              <textarea
                className="w-full h-32 p-2 border rounded-md"
                placeholder='{"customer": {"id": "123", "email": "<EMAIL>"}}'
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowTriggerDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => handleTriggerWorkflow('customer.registered', {})}>
              Trigger Event
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
