'use client'

import { useState, useCallback } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable'
import {
  Save,
  Play,
  ArrowLeft,
  Workflow
} from 'lucide-react'
import { toast } from 'sonner'
import Link from 'next/link'

// Import our visual flow components
import VisualFlowDesigner from '@/components/workflows/visual-flow-designer'
import { NodePalette } from '@/components/workflows/node-palette'
import { WorkflowDefinition } from '@/lib/workflows/types'

export default function WorkflowBuilderPage() {
  const [currentWorkflow, setCurrentWorkflow] = useState<WorkflowDefinition | null>(null)

  const handleSaveWorkflow = useCallback(async (workflow: WorkflowDefinition) => {
    try {
      const response = await fetch('/api/workflows', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(workflow)
      })

      if (response.ok) {
        const result = await response.json()
        toast.success('Workflow saved successfully!')
        setCurrentWorkflow(result.data)
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to save workflow')
      }
    } catch (error) {
      console.error('Error saving workflow:', error)
      toast.error('Failed to save workflow')
    }
  }, [])

  const handleTestWorkflow = useCallback(async (workflow: WorkflowDefinition) => {
    try {
      const testData = {
        customer: { id: 'test-123', email: '<EMAIL>', firstName: 'Test' },
        order: { id: 'order-123', total: 299.99 }
      }

      const response = await fetch('/api/workflows', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          eventType: 'customer.registered',
          eventData: testData,
          context: { userId: 'admin', source: 'builder-test' }
        })
      })

      if (response.ok) {
        const result = await response.json()
        toast.success(`Test triggered ${result.data.executionIds.length} workflow(s)!`)
      } else {
        toast.error('Failed to test workflow')
      }
    } catch (error) {
      console.error('Error testing workflow:', error)
      toast.error('Failed to test workflow')
    }
  }, [])

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-background">
        <div className="flex items-center space-x-4">
          <Link href="/admin/workflows">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Workflows
            </Button>
          </Link>
          <div className="flex items-center space-x-2">
            <Workflow className="h-5 w-5" />
            <h1 className="text-xl font-semibold">Visual Workflow Builder</h1>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => currentWorkflow && handleTestWorkflow(currentWorkflow)}
            disabled={!currentWorkflow}
          >
            <Play className="h-4 w-4 mr-2" />
            Test
          </Button>
          <Button
            size="sm"
            onClick={() => currentWorkflow && handleSaveWorkflow(currentWorkflow)}
            disabled={!currentWorkflow}
          >
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1">
        <ResizablePanelGroup direction="horizontal">
          {/* Node Palette */}
          <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
            <NodePalette />
          </ResizablePanel>

          <ResizableHandle />

          {/* Visual Flow Designer */}
          <ResizablePanel defaultSize={80}>
            <VisualFlowDesigner
              initialWorkflow={currentWorkflow}
              onSave={handleSaveWorkflow}
              onTest={handleTestWorkflow}
            />
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  )
}