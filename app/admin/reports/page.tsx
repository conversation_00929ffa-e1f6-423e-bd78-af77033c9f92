'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  <PERSON><PERSON>,
  <PERSON>bsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts'
import { 
  FileText, 
  Download, 
  Calendar, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Target,
  Award,
  Clock,
  MapPin
} from 'lucide-react'
import { useZarFormatter } from '@/components/admin/zar-price-input'
import { useReports, useReportsTimeRange } from '@/lib/ecommerce/hooks/use-reports'
import type { AnalyticsTimeRange } from '@/lib/ecommerce/types'

interface ReportData {
  salesReport: {
    totalRevenue: number
    totalOrders: number
    averageOrderValue: number
    conversionRate: number
    topSellingProducts: Array<{
      name: string
      quantity: number
      revenue: number
    }>
    salesByMonth: Array<{
      month: string
      revenue: number
      orders: number
    }>
  }
  customerReport: {
    totalCustomers: number
    newCustomers: number
    returningCustomers: number
    customerLifetimeValue: number
    customersByRegion: Array<{
      region: string
      customers: number
      percentage: number
    }>
    customerAcquisition: Array<{
      month: string
      new: number
      returning: number
    }>
  }
  inventoryReport: {
    totalProducts: number
    lowStockItems: number
    outOfStockItems: number
    inventoryValue: number
    topCategories: Array<{
      category: string
      products: number
      value: number
    }>
    stockMovement: Array<{
      month: string
      inbound: number
      outbound: number
    }>
  }
}

export default function ReportsPage() {
  const { formatPrice } = useZarFormatter()
  const { timeRange, setTimeRange, timeRangeOptions } = useReportsTimeRange('30d')
  const { reports, loading, error, refetch, exportReport } = useReports({
    timeRange,
    autoFetch: true
  })
  const [reportType, setReportType] = useState('sales')

  // Handle time range changes
  const handleTimeRangeChange = (newTimeRange: string) => {
    const range = newTimeRange as AnalyticsTimeRange
    setTimeRange(range)
    refetch(range)
  }

  // Handle export
  const handleExport = (format: 'pdf' | 'excel' | 'csv') => {
    exportReport(format, reportType)
  }

  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1']

  if (loading || !reports) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-red-600">Error loading reports: {error}</p>
              <Button onClick={() => refetch()} className="mt-4">
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
          <p className="text-muted-foreground">
            Comprehensive business reports and insights
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={handleTimeRangeChange}>
            <SelectTrigger className="w-[140px]">
              <Calendar className="mr-2 h-4 w-4" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {timeRangeOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={reportType} onValueChange={setReportType}>
            <SelectTrigger className="w-[140px]">
              <FileText className="mr-2 h-4 w-4" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="sales">Sales Report</SelectItem>
              <SelectItem value="customer">Customer Report</SelectItem>
              <SelectItem value="inventory">Inventory Report</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={() => handleExport('pdf')}>
            <Download className="mr-2 h-4 w-4" />
            Export PDF
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sales">Sales</TabsTrigger>
          <TabsTrigger value="customers">Customers</TabsTrigger>
          <TabsTrigger value="inventory">Inventory</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <div className="ml-2">
                    <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                    <div className="flex items-center">
                      <p className="text-2xl font-bold">{formatPrice(reports.salesReport.totalRevenue)}</p>
                      <TrendingUp className="ml-1 h-4 w-4 text-green-600" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                  <div className="ml-2">
                    <p className="text-sm font-medium text-muted-foreground">Total Orders</p>
                    <div className="flex items-center">
                      <p className="text-2xl font-bold">{reports.salesReport.totalOrders}</p>
                      <TrendingUp className="ml-1 h-4 w-4 text-green-600" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <div className="ml-2">
                    <p className="text-sm font-medium text-muted-foreground">Total Customers</p>
                    <div className="flex items-center">
                      <p className="text-2xl font-bold">{reports.customerReport.totalCustomers}</p>
                      <TrendingUp className="ml-1 h-4 w-4 text-green-600" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Target className="h-4 w-4 text-muted-foreground" />
                  <div className="ml-2">
                    <p className="text-sm font-medium text-muted-foreground">Conversion Rate</p>
                    <div className="flex items-center">
                      <p className="text-2xl font-bold">{reports.salesReport.conversionRate.toFixed(1)}%</p>
                      <TrendingUp className="ml-1 h-4 w-4 text-green-600" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Insights */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Performance Summary</CardTitle>
                <CardDescription>Key performance indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Average Order Value</span>
                  <span className="font-medium">{formatPrice(reports.salesReport.averageOrderValue)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Customer Lifetime Value</span>
                  <span className="font-medium">{formatPrice(reports.customerReport.customerLifetimeValue)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Inventory Value</span>
                  <span className="font-medium">{formatPrice(reports.inventoryReport.inventoryValue)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Stock Health</span>
                  <div className="flex items-center space-x-2">
                    <Progress value={reports.inventoryReport.stockHealth} className="w-16" />
                    <span className="text-sm font-medium">{reports.inventoryReport.stockHealth}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Performing Products</CardTitle>
                <CardDescription>Best sellers by revenue</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {reports.salesReport.topSellingProducts.slice(0, 5).map((product: any, index: number) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
                          {index + 1}
                        </Badge>
                        <span className="text-sm font-medium">{product.title}</span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">{formatPrice(product.revenue)}</div>
                        <div className="text-xs text-muted-foreground">{product.quantity} sold</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sales" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sales Trend</CardTitle>
              <CardDescription>Monthly sales performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={reports.salesReport.salesByPeriod}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value, name) => [
                      name === 'revenue' ? formatPrice(value as number) : value,
                      name === 'revenue' ? 'Revenue' : 'Orders'
                    ]} />
                    <Legend />
                    <Bar dataKey="revenue" fill="#8884d8" />
                    <Bar dataKey="orders" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customers" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Customer Distribution by Region</CardTitle>
                <CardDescription>Geographic breakdown of customers</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={reports.customerReport.customersByRegion}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ region, percentage }) => `${region} (${percentage}%)`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="customers"
                      >
                        {reports.customerReport.customersByRegion.map((_: any, index: number) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Customer Acquisition</CardTitle>
                <CardDescription>New vs returning customers</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={reports.customerReport.customerAcquisition}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="new" stroke="#8884d8" strokeWidth={2} />
                      <Line type="monotone" dataKey="returning" stroke="#82ca9d" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="inventory" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Inventory by Category</CardTitle>
                <CardDescription>Product distribution and value</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {reports.inventoryReport.topCategories.map((category: any, index: number) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{category.category}</span>
                        <span className="text-sm text-muted-foreground">
                          {category.products} products • {formatPrice(category.value)}
                        </span>
                      </div>
                      <Progress 
                        value={(category.products / reports.inventoryReport.totalProducts) * 100}
                        className="h-2"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Stock Movement</CardTitle>
                <CardDescription>Inbound vs outbound inventory</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={reports.inventoryReport.stockMovement}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="inbound" fill="#82ca9d" />
                      <Bar dataKey="outbound" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
