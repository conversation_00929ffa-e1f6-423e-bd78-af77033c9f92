'use client'

import React, { useState } from 'react'
import { CodeMirrorEditor } from '@/lib/page-builder/components/code-editor/codemirror-editor'
import { CodeBlock } from '@/lib/page-builder/blocks/code-block'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'

export default function CodeEditorDemoPage() {
  const [htmlCode, setHtmlCode] = useState(`<div class="demo-container">
  <h1>Hello CodeMirror!</h1>
  <p>This is a demonstration of the CodeMirror editor integration.</p>
  <button onclick="showAlert()">Click me!</button>
</div>`)

  const [cssCode, setCssCode] = useState(`.demo-container {
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.demo-container h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.demo-container p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.demo-container button {
  background: white;
  color: #667eea;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s;
}

.demo-container button:hover {
  transform: translateY(-2px);
}`)

  const [jsCode, setJsCode] = useState(`function showAlert() {
  alert('Hello from the CodeMirror editor! 🎉');
}

// Add some interactive functionality
document.addEventListener('DOMContentLoaded', function() {
  console.log('CodeMirror demo loaded successfully!');
  
  // Add hover effects
  const container = document.querySelector('.demo-container');
  if (container) {
    container.addEventListener('mouseenter', function() {
      this.style.transform = 'scale(1.02)';
      this.style.transition = 'transform 0.3s ease';
    });
    
    container.addEventListener('mouseleave', function() {
      this.style.transform = 'scale(1)';
    });
  }
});`)

  const [codeBlockConfig, setCodeBlockConfig] = useState({
    code: {
      html: htmlCode,
      css: cssCode,
      javascript: jsCode
    },
    activeLanguage: 'html' as const,
    showPreview: true,
    enabledLanguages: ['html', 'css', 'javascript'] as const,
    theme: 'light' as const,
    height: '300px',
    readOnly: false,
    showLineNumbers: true,
    enableSearch: true,
    enableAutocompletion: true
  })

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          CodeMirror Editor Demo
        </h1>
        <p className="text-gray-600">
          Test the powerful CodeMirror editor integration with syntax highlighting and live preview
        </p>
      </div>

      <Tabs defaultValue="individual" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="individual">Individual Editors</TabsTrigger>
          <TabsTrigger value="block">Code Block Component</TabsTrigger>
        </TabsList>

        <TabsContent value="individual" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* HTML Editor */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  🌐 HTML Editor
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CodeMirrorEditor
                  value={htmlCode}
                  onChange={setHtmlCode}
                  language="html"
                  height="300px"
                  placeholder="Enter HTML code..."
                />
              </CardContent>
            </Card>

            {/* CSS Editor */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  🎨 CSS Editor
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CodeMirrorEditor
                  value={cssCode}
                  onChange={setCssCode}
                  language="css"
                  height="300px"
                  placeholder="Enter CSS code..."
                />
              </CardContent>
            </Card>

            {/* JavaScript Editor */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  ⚡ JavaScript Editor
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CodeMirrorEditor
                  value={jsCode}
                  onChange={setJsCode}
                  language="javascript"
                  height="300px"
                  placeholder="Enter JavaScript code..."
                />
              </CardContent>
            </Card>
          </div>

          {/* Live Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">👁️ Live Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <iframe
                  srcDoc={`
                    <!DOCTYPE html>
                    <html>
                      <head>
                        <style>${cssCode}</style>
                      </head>
                      <body>
                        ${htmlCode}
                        <script>${jsCode}</script>
                      </body>
                    </html>
                  `}
                  className="w-full h-96 border-0"
                  title="Live Preview"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="block" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">🧩 Code Block Component</CardTitle>
              <p className="text-sm text-gray-600">
                This demonstrates the CodeBlock component as it would appear in the page builder
              </p>
            </CardHeader>
            <CardContent>
              <CodeBlock
                configuration={codeBlockConfig}
                isSelected={false}
                isEditing={true}
                onUpdate={(updates) => {
                  setCodeBlockConfig(prev => ({
                    ...prev,
                    ...updates
                  }))
                }}
              />
            </CardContent>
          </Card>

          {/* Theme Toggle */}
          <div className="flex justify-center">
            <Button
              onClick={() => {
                setCodeBlockConfig(prev => ({
                  ...prev,
                  theme: prev.theme === 'light' ? 'dark' : 'light'
                }))
              }}
              variant="outline"
            >
              Toggle Theme (Current: {codeBlockConfig.theme})
            </Button>
          </div>
        </TabsContent>
      </Tabs>

      {/* Features List */}
      <Card>
        <CardHeader>
          <CardTitle>✨ Features Demonstrated</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div className="space-y-2">
              <h4 className="font-semibold">Editor Features</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Syntax highlighting</li>
                <li>• Auto-completion</li>
                <li>• Line numbers</li>
                <li>• Code folding</li>
                <li>• Search & replace</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold">Language Support</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• HTML</li>
                <li>• CSS</li>
                <li>• JavaScript</li>
                <li>• TypeScript</li>
                <li>• JSX/TSX</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold">Integration</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Page builder blocks</li>
                <li>• Live preview</li>
                <li>• Theme switching</li>
                <li>• Export functionality</li>
                <li>• Responsive design</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
