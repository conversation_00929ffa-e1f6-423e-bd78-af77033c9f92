'use client'

// Taxonomies Management Page
// Admin interface for managing all taxonomies (categories, tags, custom taxonomies)

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Eye, 
  Trash2,
  Tag,
  Folder,
  Hash,
  TreePine
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { Taxonomy } from '@/lib/posts/types'
import { toast } from 'sonner'

export default function TaxonomiesManagementPage() {
  const router = useRouter()
  const [taxonomies, setTaxonomies] = useState<Taxonomy[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  // Fetch taxonomies
  const fetchTaxonomies = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/taxonomies?includeInactive=true')
      const data = await response.json()

      if (data.success) {
        setTaxonomies(data.data)
      }
    } catch (error) {
      console.error('Error fetching taxonomies:', error)
      toast.error('Failed to fetch taxonomies')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTaxonomies()
  }, [])

  // Filter taxonomies based on search
  const filteredTaxonomies = taxonomies.filter(taxonomy =>
    taxonomy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    taxonomy.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    taxonomy.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Handle taxonomy actions
  const handleEdit = (taxonomy: Taxonomy) => {
    router.push(`/admin/taxonomies/edit/${taxonomy.name}`)
  }

  const handleViewTerms = (taxonomy: Taxonomy) => {
    router.push(`/admin/taxonomies/${taxonomy.name}`)
  }

  const handleDeactivate = async (taxonomy: Taxonomy) => {
    if (taxonomy.isSystem) {
      toast.error('Cannot deactivate system taxonomies')
      return
    }

    if (confirm(`Are you sure you want to deactivate "${taxonomy.label}"?`)) {
      try {
        const response = await fetch(`/api/taxonomies/${taxonomy.name}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ isActive: false })
        })

        if (response.ok) {
          toast.success('Taxonomy deactivated')
          fetchTaxonomies()
        } else {
          toast.error('Failed to deactivate taxonomy')
        }
      } catch (error) {
        console.error('Error deactivating taxonomy:', error)
        toast.error('Failed to deactivate taxonomy')
      }
    }
  }

  const getStatusBadge = (taxonomy: Taxonomy) => {
    if (!taxonomy.isActive) {
      return <Badge variant="secondary">Inactive</Badge>
    }
    if (taxonomy.isSystem) {
      return <Badge variant="outline">System</Badge>
    }
    return <Badge variant="default">Active</Badge>
  }

  const getTaxonomyIcon = (taxonomy: Taxonomy) => {
    if (taxonomy.isHierarchical) {
      return <TreePine className="h-4 w-4" />
    }
    return <Hash className="h-4 w-4" />
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Taxonomies</h1>
          <p className="text-muted-foreground">
            Manage categories, tags, and custom taxonomies
          </p>
        </div>
        
        <Button onClick={() => router.push('/admin/taxonomies/new')}>
          <Plus className="h-4 w-4 mr-2" />
          New Taxonomy
        </Button>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search taxonomies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Quick Access */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push('/admin/taxonomies/category')}>
          <CardContent className="p-4 text-center">
            <Folder className="h-8 w-8 mx-auto mb-2 text-primary" />
            <div className="font-medium">Categories</div>
            <div className="text-sm text-muted-foreground">Hierarchical organization</div>
          </CardContent>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push('/admin/taxonomies/tag')}>
          <CardContent className="p-4 text-center">
            <Hash className="h-8 w-8 mx-auto mb-2 text-primary" />
            <div className="font-medium">Tags</div>
            <div className="text-sm text-muted-foreground">Flat organization</div>
          </CardContent>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push('/admin/taxonomies/product_category')}>
          <CardContent className="p-4 text-center">
            <Folder className="h-8 w-8 mx-auto mb-2 text-primary" />
            <div className="font-medium">Product Categories</div>
            <div className="text-sm text-muted-foreground">E-commerce organization</div>
          </CardContent>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push('/admin/taxonomies/new')}>
          <CardContent className="p-4 text-center">
            <Plus className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
            <div className="font-medium">Create New</div>
            <div className="text-sm text-muted-foreground">Custom taxonomy</div>
          </CardContent>
        </Card>
      </div>

      {/* Taxonomies Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Tag className="h-5 w-5" />
            Taxonomies ({filteredTaxonomies.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : filteredTaxonomies.length === 0 ? (
            <div className="text-center py-8">
              <Tag className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No taxonomies found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm 
                  ? 'Try adjusting your search terms' 
                  : 'Get started by creating your first custom taxonomy'
                }
              </p>
              {!searchTerm && (
                <Button onClick={() => router.push('/admin/taxonomies/new')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Taxonomy
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Taxonomy</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Post Types</TableHead>
                  <TableHead>Terms</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTaxonomies.map((taxonomy) => (
                  <TableRow key={taxonomy.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        {getTaxonomyIcon(taxonomy)}
                        <div>
                          <div className="font-medium">{taxonomy.label}</div>
                          <div className="text-sm text-muted-foreground">
                            {taxonomy.name}
                          </div>
                          {taxonomy.description && (
                            <div className="text-xs text-muted-foreground max-w-md truncate">
                              {taxonomy.description}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {taxonomy.isHierarchical ? 'Hierarchical' : 'Flat'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(taxonomy)}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {taxonomy.postTypes.slice(0, 2).map((postType) => (
                          <Badge key={postType} variant="secondary" className="text-xs">
                            {postType}
                          </Badge>
                        ))}
                        {taxonomy.postTypes.length > 2 && (
                          <Badge variant="secondary" className="text-xs">
                            +{taxonomy.postTypes.length - 2}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {taxonomy.terms?.length || 0} terms
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewTerms(taxonomy)}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Terms
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEdit(taxonomy)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          {!taxonomy.isSystem && (
                            <DropdownMenuItem 
                              onClick={() => handleDeactivate(taxonomy)}
                              className="text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Deactivate
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {taxonomies.filter(t => t.isActive).length}
            </div>
            <div className="text-sm text-muted-foreground">Active Taxonomies</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {taxonomies.filter(t => t.isSystem).length}
            </div>
            <div className="text-sm text-muted-foreground">System Taxonomies</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {taxonomies.filter(t => t.isHierarchical).length}
            </div>
            <div className="text-sm text-muted-foreground">Hierarchical</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {taxonomies.reduce((acc, t) => acc + (t.terms?.length || 0), 0)}
            </div>
            <div className="text-sm text-muted-foreground">Total Terms</div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
