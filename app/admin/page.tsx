'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import {
  Package,
  ShoppingCart,
  Users,
  TrendingUp,
  Plus,
  Eye,
  AlertTriangle,
  BarChart3,
  DollarSign
} from 'lucide-react'
import Link from 'next/link'
import { DashboardOverviewWidget } from '@/components/admin/dashboard/widgets/dashboard-overview-widget'
import { RevenueChartWidget } from '@/components/admin/dashboard/widgets/revenue-chart-widget'
import { OrdersAnalyticsWidget } from '@/components/admin/dashboard/widgets/orders-analytics-widget'
import { TopProductsWidget } from '@/components/admin/dashboard/widgets/top-products-widget'

export default function AdminDashboard() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back! Here's what's happening with your store.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Link href="/admin/products/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </Link>
          <Link href="/admin/orders">
            <Button variant="outline">
              <ShoppingCart className="mr-2 h-4 w-4" />
              View Orders
            </Button>
          </Link>
        </div>
      </div>

      {/* Dashboard Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="revenue" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Revenue
          </TabsTrigger>
          <TabsTrigger value="orders" className="flex items-center gap-2">
            <ShoppingCart className="h-4 w-4" />
            Orders
          </TabsTrigger>
          <TabsTrigger value="products" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Products
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <DashboardOverviewWidget />
        </TabsContent>

        <TabsContent value="revenue" className="space-y-4">
          <RevenueChartWidget />
        </TabsContent>

        <TabsContent value="orders" className="space-y-4">
          <OrdersAnalyticsWidget />
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          <TopProductsWidget />
        </TabsContent>
      </Tabs>
    </div>
  )
}