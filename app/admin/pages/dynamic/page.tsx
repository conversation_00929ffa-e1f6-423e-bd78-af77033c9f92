'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { DataTable, DataTableColumn, DataTableAction } from '@/components/admin/data-table'
import { useDynamicPages, usePageGeneration, usePageCache } from '@/lib/page-builder/hooks/use-dynamic-page'
import {
  Plus,
  Eye,
  Edit,
  Copy,
  Trash2,
  Globe,
  Clock,
  BarChart3,
  <PERSON>freshC<PERSON>,
  Zap
} from 'lucide-react'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

interface PageFormData {
  title: string
  slug: string
  description: string
  type: string
  template: string
  status: string
}

export default function DynamicPagesPage() {
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [templates, setTemplates] = useState<any[]>([])

  const { pages, loading, error, pagination, refetch } = useDynamicPages({
    status: statusFilter === 'all' ? undefined : statusFilter,
    type: typeFilter === 'all' ? undefined : typeFilter,
    limit: 20,
    sortBy: 'updatedAt',
    sortOrder: 'desc'
  })

  const { generatePage, duplicatePage, generating } = usePageGeneration()
  const { stats: cacheStats, clearCache, invalidatePage } = usePageCache()

  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors }
  } = useForm<PageFormData>()

  // Load templates on mount
  useEffect(() => {
    const loadTemplates = async () => {
      try {
        const response = await fetch('/api/page-builder/generate?action=templates')
        const result = await response.json()
        if (result.success) {
          setTemplates(result.data)
        }
      } catch (error) {
        console.error('Error loading templates:', error)
      }
    }
    loadTemplates()
  }, [])

  // Auto-generate slug from title
  const watchedTitle = watch('title')
  useEffect(() => {
    if (watchedTitle) {
      const slug = watchedTitle
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
      setValue('slug', slug)
    }
  }, [watchedTitle, setValue])

  const onSubmit = async (data: PageFormData) => {
    try {
      await generatePage({
        title: data.title,
        slug: data.slug,
        description: data.description,
        type: data.type,
        template: data.template
      })

      toast.success('Page created successfully!')
      setShowCreateDialog(false)
      reset()
      refetch()
    } catch (error: any) {
      toast.error(error.message || 'Failed to create page')
    }
  }

  const handleDuplicate = async (page: any) => {
    try {
      const newSlug = `${page.slug}-copy-${Date.now()}`
      await duplicatePage(page.id, newSlug, `${page.title} (Copy)`)
      toast.success('Page duplicated successfully!')
      refetch()
    } catch (error: any) {
      toast.error(error.message || 'Failed to duplicate page')
    }
  }

  const handleClearCache = async () => {
    try {
      await clearCache()
      toast.success('Cache cleared successfully!')
    } catch (error) {
      toast.error('Failed to clear cache')
    }
  }

  const columns: DataTableColumn<any>[] = [
    {
      key: 'title',
      title: 'Page',
      searchable: true,
      sortable: true,
      render: (page) => (
        <div>
          <div className="font-medium">{page.title}</div>
          <div className="text-sm text-muted-foreground">/{page.slug}</div>
        </div>
      )
    },
    {
      key: 'type',
      title: 'Type',
      sortable: true,
      render: (page) => (
        <Badge variant="outline">
          {page.type.replace('-', ' ').toUpperCase()}
        </Badge>
      )
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      render: (page) => (
        <Badge variant={page.status === 'published' ? 'default' : 'secondary'}>
          {page.status.toUpperCase()}
        </Badge>
      )
    },
    {
      key: 'blocks',
      title: 'Blocks',
      render: (page) => (
        <div className="text-center">
          <span className="font-medium">{page.blocks?.length || 0}</span>
        </div>
      )
    },
    {
      key: 'updatedAt',
      title: 'Last Updated',
      sortable: true,
      render: (page) => (
        <div className="text-sm">
          {new Date(page.updatedAt).toLocaleDateString()}
        </div>
      )
    }
  ]

  const actions: DataTableAction<any>[] = [
    {
      label: 'View',
      icon: Eye,
      onClick: (page) => {
        window.open(`/frontend/preview/${page.slug}?token=preview123`, '_blank')
      }
    },
    {
      label: 'Edit',
      icon: Edit,
      onClick: (page) => {
        window.location.href = `/admin/page-builder/${page.id}`
      }
    },
    {
      label: 'Duplicate',
      icon: Copy,
      onClick: handleDuplicate
    },
    {
      label: 'Clear Cache',
      icon: RefreshCw,
      onClick: (page) => {
        invalidatePage(page.id, page.slug)
        toast.success('Page cache cleared!')
      }
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dynamic Pages</h1>
          <p className="text-muted-foreground">
            Manage dynamically rendered pages created with the page builder
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleClearCache}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Clear Cache
          </Button>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create Page
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Create New Page</DialogTitle>
                <DialogDescription>
                  Generate a new page using our page builder templates
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      {...register('title', { required: 'Title is required' })}
                      placeholder="Enter page title"
                    />
                    {errors.title && (
                      <p className="text-sm text-red-600">{errors.title.message}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="slug">Slug</Label>
                    <Input
                      id="slug"
                      {...register('slug', { required: 'Slug is required' })}
                      placeholder="page-slug"
                    />
                    {errors.slug && (
                      <p className="text-sm text-red-600">{errors.slug.message}</p>
                    )}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    {...register('description')}
                    placeholder="Enter page description"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="type">Page Type</Label>
                    <Select onValueChange={(value) => setValue('type', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="custom">Custom</SelectItem>
                        <SelectItem value="home">Home</SelectItem>
                        <SelectItem value="landing">Landing</SelectItem>
                        <SelectItem value="product">Product</SelectItem>
                        <SelectItem value="category">Category</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="template">Template</Label>
                    <Select onValueChange={(value) => setValue('template', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select template" />
                      </SelectTrigger>
                      <SelectContent>
                        {templates.map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            {template.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowCreateDialog(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={generating}>
                    {generating ? 'Creating...' : 'Create Page'}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Cache Stats */}
      {cacheStats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Zap className="mr-2 h-5 w-5" />
              Cache Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{cacheStats.totalEntries}</div>
                <div className="text-sm text-muted-foreground">Cached Pages</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{cacheStats.hitRate}%</div>
                <div className="text-sm text-muted-foreground">Hit Rate</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{cacheStats.totalHits}</div>
                <div className="text-sm text-muted-foreground">Cache Hits</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{cacheStats.memoryUsage}KB</div>
                <div className="text-sm text-muted-foreground">Memory Usage</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pages Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Dynamic Pages</CardTitle>
              <CardDescription>
                Pages created and managed through the page builder system
              </CardDescription>
            </div>
            
            {/* Filters */}
            <div className="flex items-center space-x-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                  <SelectItem value="home">Home</SelectItem>
                  <SelectItem value="landing">Landing</SelectItem>
                  <SelectItem value="product">Product</SelectItem>
                  <SelectItem value="category">Category</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            data={pages}
            columns={columns}
            actions={actions}
            loading={loading}
            searchPlaceholder="Search pages by title or slug..."
            emptyMessage="No dynamic pages found."
            emptyIcon={Globe}
            pageSize={20}
          />
        </CardContent>
      </Card>
    </div>
  )
}
