'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import { 
  Settings, 
  Store, 
  CreditCard, 
  Truck, 
  Globe,
  Save,
  TestTube,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react'

export default function SettingsPage() {
  const [loading, setLoading] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'testing'>('connected')

  // Store Settings
  const [storeSettings, setStoreSettings] = useState({
    storeName: 'Coco Milk Kids Store',
    storeDescription: 'Premium kids clothing for South African families',
    storeAddress: '123 Main Street, Cape Town, South Africa',
    storePhone: '+27 21 123 4567',
    storeEmail: '<EMAIL>',
    currency: 'ZAR',
    timezone: 'Africa/Johannesburg',
    language: 'en_ZA'
  })

  // WooCommerce Settings
  const [wooSettings, setWooSettings] = useState({
    baseUrl: process.env.NEXT_PUBLIC_WORDPRESS_URL || '',
    consumerKey: '',
    consumerSecret: '',
    syncEnabled: true,
    syncInterval: '5',
    autoSync: true
  })

  // Payment Settings
  const [paymentSettings, setPaymentSettings] = useState({
    payfast: {
      enabled: true,
      merchantId: '',
      merchantKey: '',
      passphrase: '',
      sandbox: false
    },
    ozow: {
      enabled: true,
      siteCode: '',
      privateKey: '',
      apiKey: '',
      sandbox: false
    },
    cod: {
      enabled: true,
      title: 'Cash on Delivery',
      description: 'Pay when you receive your order'
    }
  })

  // Shipping Settings
  const [shippingSettings, setShippingSettings] = useState({
    freeShippingThreshold: 500,
    standardShipping: 99,
    expressShipping: 199,
    courierGuy: {
      enabled: true,
      username: '',
      password: ''
    },
    postnet: {
      enabled: false,
      apiKey: ''
    }
  })

  const testWooCommerceConnection = async () => {
    setConnectionStatus('testing')
    setLoading(true)
    
    try {
      // Simulate API test
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // In a real implementation, you would test the actual connection
      const success = Math.random() > 0.3 // 70% success rate for demo
      
      setConnectionStatus(success ? 'connected' : 'disconnected')
    } catch (error) {
      setConnectionStatus('disconnected')
    } finally {
      setLoading(false)
    }
  }

  const saveSettings = async (section: string) => {
    setLoading(true)
    
    try {
      // Simulate save operation
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // In a real implementation, you would save to your backend
      console.log(`Saving ${section} settings`)
      
      // Show success message
      alert(`${section} settings saved successfully!`)
    } catch (error) {
      alert(`Error saving ${section} settings`)
    } finally {
      setLoading(false)
    }
  }

  const getConnectionStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'disconnected':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'testing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
    }
  }

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Connected'
      case 'disconnected':
        return 'Disconnected'
      case 'testing':
        return 'Testing...'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">
            Configure your store, integrations, and preferences
          </p>
        </div>
      </div>

      <Tabs defaultValue="store" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="store" className="flex items-center space-x-2">
            <Store className="h-4 w-4" />
            <span>Store</span>
          </TabsTrigger>
          <TabsTrigger value="woocommerce" className="flex items-center space-x-2">
            <Globe className="h-4 w-4" />
            <span>WooCommerce</span>
          </TabsTrigger>
          <TabsTrigger value="payments" className="flex items-center space-x-2">
            <CreditCard className="h-4 w-4" />
            <span>Payments</span>
          </TabsTrigger>
          <TabsTrigger value="shipping" className="flex items-center space-x-2">
            <Truck className="h-4 w-4" />
            <span>Shipping</span>
          </TabsTrigger>
        </TabsList>

        {/* Store Settings */}
        <TabsContent value="store">
          <Card>
            <CardHeader>
              <CardTitle>Store Information</CardTitle>
              <CardDescription>
                Basic information about your kids clothing store
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="storeName">Store Name</Label>
                  <Input
                    id="storeName"
                    value={storeSettings.storeName}
                    onChange={(e) => setStoreSettings({...storeSettings, storeName: e.target.value})}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="storeEmail">Store Email</Label>
                  <Input
                    id="storeEmail"
                    type="email"
                    value={storeSettings.storeEmail}
                    onChange={(e) => setStoreSettings({...storeSettings, storeEmail: e.target.value})}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="storeDescription">Store Description</Label>
                <Textarea
                  id="storeDescription"
                  value={storeSettings.storeDescription}
                  onChange={(e) => setStoreSettings({...storeSettings, storeDescription: e.target.value})}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="storePhone">Phone Number</Label>
                  <Input
                    id="storePhone"
                    value={storeSettings.storePhone}
                    onChange={(e) => setStoreSettings({...storeSettings, storePhone: e.target.value})}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <Select value={storeSettings.currency} onValueChange={(value) => setStoreSettings({...storeSettings, currency: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ZAR">South African Rand (ZAR)</SelectItem>
                      <SelectItem value="USD">US Dollar (USD)</SelectItem>
                      <SelectItem value="EUR">Euro (EUR)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="storeAddress">Store Address</Label>
                <Textarea
                  id="storeAddress"
                  value={storeSettings.storeAddress}
                  onChange={(e) => setStoreSettings({...storeSettings, storeAddress: e.target.value})}
                  rows={2}
                />
              </div>

              <div className="flex justify-end">
                <Button onClick={() => saveSettings('Store')} disabled={loading}>
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  <Save className="mr-2 h-4 w-4" />
                  Save Store Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* WooCommerce Settings */}
        <TabsContent value="woocommerce">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>WooCommerce Integration</span>
                <div className="flex items-center space-x-2">
                  {getConnectionStatusIcon()}
                  <Badge variant={connectionStatus === 'connected' ? 'default' : 'destructive'}>
                    {getConnectionStatusText()}
                  </Badge>
                </div>
              </CardTitle>
              <CardDescription>
                Connect your WordPress/WooCommerce store for seamless integration
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="baseUrl">WordPress/WooCommerce URL</Label>
                <Input
                  id="baseUrl"
                  placeholder="https://yourstore.com"
                  value={wooSettings.baseUrl}
                  onChange={(e) => setWooSettings({...wooSettings, baseUrl: e.target.value})}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="consumerKey">Consumer Key</Label>
                  <Input
                    id="consumerKey"
                    type="password"
                    placeholder="ck_..."
                    value={wooSettings.consumerKey}
                    onChange={(e) => setWooSettings({...wooSettings, consumerKey: e.target.value})}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="consumerSecret">Consumer Secret</Label>
                  <Input
                    id="consumerSecret"
                    type="password"
                    placeholder="cs_..."
                    value={wooSettings.consumerSecret}
                    onChange={(e) => setWooSettings({...wooSettings, consumerSecret: e.target.value})}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Auto Sync</Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically sync products, orders, and customers
                    </p>
                  </div>
                  <Switch
                    checked={wooSettings.autoSync}
                    onCheckedChange={(checked) => setWooSettings({...wooSettings, autoSync: checked})}
                  />
                </div>

                {wooSettings.autoSync && (
                  <div className="space-y-2">
                    <Label htmlFor="syncInterval">Sync Interval (minutes)</Label>
                    <Select value={wooSettings.syncInterval} onValueChange={(value) => setWooSettings({...wooSettings, syncInterval: value})}>
                      <SelectTrigger className="w-[200px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">Every minute</SelectItem>
                        <SelectItem value="5">Every 5 minutes</SelectItem>
                        <SelectItem value="15">Every 15 minutes</SelectItem>
                        <SelectItem value="30">Every 30 minutes</SelectItem>
                        <SelectItem value="60">Every hour</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-4">
                <Button onClick={testWooCommerceConnection} disabled={loading} variant="outline">
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  <TestTube className="mr-2 h-4 w-4" />
                  Test Connection
                </Button>
                
                <Button onClick={() => saveSettings('WooCommerce')} disabled={loading}>
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  <Save className="mr-2 h-4 w-4" />
                  Save WooCommerce Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Payment Settings */}
        <TabsContent value="payments">
          <div className="space-y-6">
            {/* PayFast */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>PayFast</span>
                  <Switch
                    checked={paymentSettings.payfast.enabled}
                    onCheckedChange={(checked) => setPaymentSettings({
                      ...paymentSettings,
                      payfast: {...paymentSettings.payfast, enabled: checked}
                    })}
                  />
                </CardTitle>
                <CardDescription>
                  South African payment gateway for online transactions
                </CardDescription>
              </CardHeader>
              {paymentSettings.payfast.enabled && (
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Merchant ID</Label>
                      <Input
                        type="password"
                        placeholder="10000100"
                        value={paymentSettings.payfast.merchantId}
                        onChange={(e) => setPaymentSettings({
                          ...paymentSettings,
                          payfast: {...paymentSettings.payfast, merchantId: e.target.value}
                        })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Merchant Key</Label>
                      <Input
                        type="password"
                        placeholder="46f0cd694581a"
                        value={paymentSettings.payfast.merchantKey}
                        onChange={(e) => setPaymentSettings({
                          ...paymentSettings,
                          payfast: {...paymentSettings.payfast, merchantKey: e.target.value}
                        })}
                      />
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={paymentSettings.payfast.sandbox}
                      onCheckedChange={(checked) => setPaymentSettings({
                        ...paymentSettings,
                        payfast: {...paymentSettings.payfast, sandbox: checked}
                      })}
                    />
                    <Label>Sandbox Mode</Label>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Ozow */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Ozow</span>
                  <Switch
                    checked={paymentSettings.ozow.enabled}
                    onCheckedChange={(checked) => setPaymentSettings({
                      ...paymentSettings,
                      ozow: {...paymentSettings.ozow, enabled: checked}
                    })}
                  />
                </CardTitle>
                <CardDescription>
                  Instant EFT payments for South African customers
                </CardDescription>
              </CardHeader>
              {paymentSettings.ozow.enabled && (
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Site Code</Label>
                      <Input
                        placeholder="Your Ozow site code"
                        value={paymentSettings.ozow.siteCode}
                        onChange={(e) => setPaymentSettings({
                          ...paymentSettings,
                          ozow: {...paymentSettings.ozow, siteCode: e.target.value}
                        })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label>API Key</Label>
                      <Input
                        type="password"
                        placeholder="Your Ozow API key"
                        value={paymentSettings.ozow.apiKey}
                        onChange={(e) => setPaymentSettings({
                          ...paymentSettings,
                          ozow: {...paymentSettings.ozow, apiKey: e.target.value}
                        })}
                      />
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>

            <div className="flex justify-end">
              <Button onClick={() => saveSettings('Payment')} disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Save className="mr-2 h-4 w-4" />
                Save Payment Settings
              </Button>
            </div>
          </div>
        </TabsContent>

        {/* Shipping Settings */}
        <TabsContent value="shipping">
          <Card>
            <CardHeader>
              <CardTitle>Shipping Configuration</CardTitle>
              <CardDescription>
                Configure shipping rates and courier integrations for South Africa
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Free Shipping Threshold (ZAR)</Label>
                  <Input
                    type="number"
                    value={shippingSettings.freeShippingThreshold}
                    onChange={(e) => setShippingSettings({
                      ...shippingSettings,
                      freeShippingThreshold: parseInt(e.target.value)
                    })}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Standard Shipping (ZAR)</Label>
                  <Input
                    type="number"
                    value={shippingSettings.standardShipping}
                    onChange={(e) => setShippingSettings({
                      ...shippingSettings,
                      standardShipping: parseInt(e.target.value)
                    })}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Express Shipping (ZAR)</Label>
                  <Input
                    type="number"
                    value={shippingSettings.expressShipping}
                    onChange={(e) => setShippingSettings({
                      ...shippingSettings,
                      expressShipping: parseInt(e.target.value)
                    })}
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={() => saveSettings('Shipping')} disabled={loading}>
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  <Save className="mr-2 h-4 w-4" />
                  Save Shipping Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
