'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Store,
  MapPin,
  Phone,
  Mail,
  Globe,
  DollarSign,
  Package,
  Truck,
  Save,
  RefreshCw,
  CreditCard,
  Receipt,
  ShoppingCart
} from 'lucide-react'
import { toast } from 'sonner'

interface StoreSettings {
  // Basic Information
  storeName: string
  storeDescription: string
  storeTagline: string
  
  // Contact Information
  email: string
  phone: string
  address: {
    street: string
    city: string
    state: string
    postalCode: string
    country: string
  }
  
  // Business Information
  vatNumber: string
  registrationNumber: string
  
  // Currency & Pricing
  currency: string
  currencyPosition: 'before' | 'after'
  currencySymbol: string
  decimalPlaces: number
  thousandSeparator: string
  decimalSeparator: string
  
  // Tax Settings
  pricesIncludeTax: boolean
  taxCalculation: 'exclusive' | 'inclusive'
  displayPricesWithTax: boolean
  
  // Inventory
  manageStock: boolean
  stockNotifications: boolean
  lowStockThreshold: number
  outOfStockVisibility: 'visible' | 'hidden' | 'catalog_only'
  
  // Orders
  orderNumberPrefix: string
  orderNumberSuffix: string
  orderNumberLength: number
  enableGuestCheckout: boolean
  requireAccountForPurchase: boolean
  
  // Reviews
  enableReviews: boolean
  reviewsRequireApproval: boolean
  enableReviewRatings: boolean
  onlyVerifiedPurchasers: boolean
}

export default function StoreSettingsPage() {
  const [settings, setSettings] = useState<StoreSettings>({
    storeName: 'Coco Milk Kids',
    storeDescription: 'Premium children\'s clothing and accessories designed for comfort, style, and adventure.',
    storeTagline: 'Where Style Meets Adventure',
    
    email: '<EMAIL>',
    phone: '+27 21 123 4567',
    address: {
      street: '123 Main Street',
      city: 'Cape Town',
      state: 'Western Cape',
      postalCode: '8001',
      country: 'ZA'
    },
    
    vatNumber: '**********',
    registrationNumber: '2023/123456/07',
    
    currency: 'ZAR',
    currencyPosition: 'before',
    currencySymbol: 'R',
    decimalPlaces: 2,
    thousandSeparator: ',',
    decimalSeparator: '.',
    
    pricesIncludeTax: true,
    taxCalculation: 'inclusive',
    displayPricesWithTax: true,
    
    manageStock: true,
    stockNotifications: true,
    lowStockThreshold: 5,
    outOfStockVisibility: 'hidden',
    
    orderNumberPrefix: 'CM',
    orderNumberSuffix: '',
    orderNumberLength: 6,
    enableGuestCheckout: true,
    requireAccountForPurchase: false,
    
    enableReviews: true,
    reviewsRequireApproval: false,
    enableReviewRatings: true,
    onlyVerifiedPurchasers: true
  })

  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      // In production, this would fetch from your API
      await new Promise(resolve => setTimeout(resolve, 500))
    } catch (error) {
      console.error('Error loading settings:', error)
      toast.error('Failed to load store settings')
    } finally {
      setLoading(false)
    }
  }

  const saveSettings = async () => {
    try {
      setSaving(true)
      // In production, this would save to your API
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Store settings saved successfully')
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Failed to save store settings')
    } finally {
      setSaving(false)
    }
  }

  const currencies = [
    { value: 'ZAR', label: 'South African Rand (R)', symbol: 'R' },
    { value: 'USD', label: 'US Dollar ($)', symbol: '$' },
    { value: 'EUR', label: 'Euro (€)', symbol: '€' },
    { value: 'GBP', label: 'British Pound (£)', symbol: '£' }
  ]

  const countries = [
    { value: 'ZA', label: 'South Africa' },
    { value: 'US', label: 'United States' },
    { value: 'GB', label: 'United Kingdom' },
    { value: 'AU', label: 'Australia' }
  ]

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Store Settings</h1>
        </div>
        <div className="grid gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Store Settings</h1>
          <p className="text-muted-foreground">
            Configure your e-commerce store information and preferences
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadSettings} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={saveSettings} disabled={saving}>
            {saving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Store Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Store className="h-5 w-5" />
            <span>Store Information</span>
          </CardTitle>
          <CardDescription>
            Basic information about your store
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="storeName">Store Name</Label>
              <Input
                id="storeName"
                value={settings.storeName}
                onChange={(e) => setSettings(prev => ({ ...prev, storeName: e.target.value }))}
                placeholder="Your store name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="storeTagline">Store Tagline</Label>
              <Input
                id="storeTagline"
                value={settings.storeTagline}
                onChange={(e) => setSettings(prev => ({ ...prev, storeTagline: e.target.value }))}
                placeholder="A catchy tagline"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="storeDescription">Store Description</Label>
            <Textarea
              id="storeDescription"
              value={settings.storeDescription}
              onChange={(e) => setSettings(prev => ({ ...prev, storeDescription: e.target.value }))}
              placeholder="Describe your store and what you sell"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Mail className="h-5 w-5" />
            <span>Contact Information</span>
          </CardTitle>
          <CardDescription>
            How customers can reach you
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={settings.email}
                onChange={(e) => setSettings(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                value={settings.phone}
                onChange={(e) => setSettings(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="+27 21 123 4567"
              />
            </div>
          </div>

          <div className="space-y-4">
            <Label>Store Address</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="street">Street Address</Label>
                <Input
                  id="street"
                  value={settings.address.street}
                  onChange={(e) => setSettings(prev => ({ 
                    ...prev, 
                    address: { ...prev.address, street: e.target.value }
                  }))}
                  placeholder="123 Main Street"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={settings.address.city}
                  onChange={(e) => setSettings(prev => ({ 
                    ...prev, 
                    address: { ...prev.address, city: e.target.value }
                  }))}
                  placeholder="Cape Town"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="state">State/Province</Label>
                <Input
                  id="state"
                  value={settings.address.state}
                  onChange={(e) => setSettings(prev => ({ 
                    ...prev, 
                    address: { ...prev.address, state: e.target.value }
                  }))}
                  placeholder="Western Cape"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="postalCode">Postal Code</Label>
                <Input
                  id="postalCode"
                  value={settings.address.postalCode}
                  onChange={(e) => setSettings(prev => ({ 
                    ...prev, 
                    address: { ...prev.address, postalCode: e.target.value }
                  }))}
                  placeholder="8001"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <Select 
                  value={settings.address.country} 
                  onValueChange={(value) => setSettings(prev => ({ 
                    ...prev, 
                    address: { ...prev.address, country: value }
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map((country) => (
                      <SelectItem key={country.value} value={country.value}>
                        {country.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Business Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Receipt className="h-5 w-5" />
            <span>Business Information</span>
          </CardTitle>
          <CardDescription>
            Legal business details for invoicing and compliance
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="vatNumber">VAT Number</Label>
              <Input
                id="vatNumber"
                value={settings.vatNumber}
                onChange={(e) => setSettings(prev => ({ ...prev, vatNumber: e.target.value }))}
                placeholder="**********"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="registrationNumber">Company Registration Number</Label>
              <Input
                id="registrationNumber"
                value={settings.registrationNumber}
                onChange={(e) => setSettings(prev => ({ ...prev, registrationNumber: e.target.value }))}
                placeholder="2023/123456/07"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Currency Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>Currency & Pricing</span>
          </CardTitle>
          <CardDescription>
            Configure how prices are displayed and calculated
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="currency">Currency</Label>
              <Select 
                value={settings.currency} 
                onValueChange={(value) => {
                  const currency = currencies.find(c => c.value === value)
                  setSettings(prev => ({ 
                    ...prev, 
                    currency: value,
                    currencySymbol: currency?.symbol || value
                  }))
                }}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {currencies.map((currency) => (
                    <SelectItem key={currency.value} value={currency.value}>
                      {currency.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="currencyPosition">Currency Position</Label>
              <Select 
                value={settings.currencyPosition} 
                onValueChange={(value: 'before' | 'after') => setSettings(prev => ({ ...prev, currencyPosition: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="before">Before amount (R100.00)</SelectItem>
                  <SelectItem value="after">After amount (100.00R)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="decimalPlaces">Decimal Places</Label>
              <Select 
                value={settings.decimalPlaces.toString()} 
                onValueChange={(value) => setSettings(prev => ({ ...prev, decimalPlaces: parseInt(value) }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">0 (100)</SelectItem>
                  <SelectItem value="2">2 (100.00)</SelectItem>
                  <SelectItem value="3">3 (100.000)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="thousandSeparator">Thousand Separator</Label>
              <Select 
                value={settings.thousandSeparator} 
                onValueChange={(value) => setSettings(prev => ({ ...prev, thousandSeparator: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value=",">, (1,000)</SelectItem>
                  <SelectItem value=".">. (1.000)</SelectItem>
                  <SelectItem value=" ">Space (1 000)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="decimalSeparator">Decimal Separator</Label>
              <Select 
                value={settings.decimalSeparator} 
                onValueChange={(value) => setSettings(prev => ({ ...prev, decimalSeparator: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value=".">. (100.50)</SelectItem>
                  <SelectItem value=",">, (100,50)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
