'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import {
  Settings,
  Store,
  CreditCard,
  Truck,
  Receipt,
  Shield,
  Zap,
  Wifi,
  Globe,
  Database,
  Users,
  FileText,
  Bell,
  Lock,
  HardDrive,
  Palette,
  Mail,
  Smartphone,
  Code,
  BarChart3
} from 'lucide-react'

interface SettingsSection {
  id: string
  title: string
  description: string
  icon: any
  href: string
  badge?: string
  status?: 'complete' | 'incomplete' | 'warning'
}

const settingsSections: SettingsSection[] = [
  {
    id: 'general',
    title: 'General',
    description: 'Basic site information and preferences',
    icon: Settings,
    href: '/admin/settings/general',
    status: 'complete'
  },
  {
    id: 'store',
    title: 'Store',
    description: 'Store information, currency, and business details',
    icon: Store,
    href: '/admin/settings/store',
    status: 'complete'
  },
  {
    id: 'payments',
    title: 'Payments',
    description: 'Payment gateways and processing options',
    icon: CreditCard,
    href: '/admin/settings/payments',
    status: 'complete'
  },
  {
    id: 'shipping',
    title: 'Shipping',
    description: 'Shipping zones, methods, and delivery options',
    icon: Truck,
    href: '/admin/settings/shipping',
    status: 'complete'
  },
  {
    id: 'taxes',
    title: 'Taxes',
    description: 'Tax rates, classes, and calculation settings',
    icon: Receipt,
    href: '/admin/settings/taxes',
    status: 'incomplete'
  },
  {
    id: 'security',
    title: 'Security',
    description: 'Security policies, authentication, and access control',
    icon: Shield,
    href: '/admin/settings/security',
    status: 'complete'
  },
  {
    id: 'notifications',
    title: 'Notifications',
    description: 'Email templates, SMS, and notification preferences',
    icon: Bell,
    href: '/admin/settings/notifications',
    status: 'incomplete'
  },
  {
    id: 'integrations',
    title: 'Integrations',
    description: 'Third-party services and API connections',
    icon: Zap,
    href: '/admin/settings/integrations',
    status: 'incomplete'
  },
  {
    id: 'appearance',
    title: 'Appearance',
    description: 'Themes, branding, and visual customization',
    icon: Palette,
    href: '/admin/settings/appearance',
    status: 'incomplete'
  },
  {
    id: 'seo',
    title: 'SEO',
    description: 'Search engine optimization and meta settings',
    icon: BarChart3,
    href: '/admin/settings/seo',
    status: 'incomplete'
  },
  {
    id: 'api',
    title: 'API',
    description: 'API keys, webhooks, and developer settings',
    icon: Code,
    href: '/admin/settings/api',
    status: 'incomplete'
  },
  {
    id: 'backup',
    title: 'Backup',
    description: 'Data backup and restore options',
    icon: HardDrive,
    href: '/admin/settings/backup',
    status: 'incomplete'
  }
]

export default function SettingsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  const isSettingsOverview = pathname === '/admin/settings'

  if (isSettingsOverview) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">
            Configure your store settings and preferences
          </p>
        </div>

        {/* Settings Overview Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {settingsSections.map((section) => (
            <Link key={section.id} href={section.href}>
              <Card className="h-full transition-all hover:shadow-md hover:border-blue-300 cursor-pointer group">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
                      <section.icon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold text-lg group-hover:text-blue-600 transition-colors">
                          {section.title}
                        </h3>
                        <div className="flex items-center space-x-2">
                          {section.badge && (
                            <Badge variant="secondary" className="text-xs">
                              {section.badge}
                            </Badge>
                          )}
                          {section.status === 'complete' && (
                            <div className="w-2 h-2 bg-green-500 rounded-full" />
                          )}
                          {section.status === 'incomplete' && (
                            <div className="w-2 h-2 bg-gray-300 rounded-full" />
                          )}
                          {section.status === 'warning' && (
                            <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                          )}
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {section.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Settings className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Total Settings</p>
                  <p className="text-2xl font-bold">{settingsSections.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Shield className="h-4 w-4 text-green-600" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Configured</p>
                  <p className="text-2xl font-bold text-green-600">
                    {settingsSections.filter(s => s.status === 'complete').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Globe className="h-4 w-4 text-blue-600" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Pending</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {settingsSections.filter(s => s.status === 'incomplete').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Zap className="h-4 w-4 text-purple-600" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Completion</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {Math.round((settingsSections.filter(s => s.status === 'complete').length / settingsSections.length) * 100)}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-full">
      {/* Settings Sidebar */}
      <div className={cn(
        "border-r bg-gray-50/40 transition-all duration-300",
        sidebarCollapsed ? "w-16" : "w-64"
      )}>
        <div className="p-4">
          <div className="flex items-center justify-between mb-6">
            {!sidebarCollapsed && (
              <h2 className="font-semibold text-lg">Settings</h2>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>

          <nav className="space-y-2">
            {settingsSections.map((section) => {
              const isActive = pathname === section.href
              return (
                <Link key={section.id} href={section.href}>
                  <div className={cn(
                    "flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors",
                    isActive 
                      ? "bg-blue-100 text-blue-700" 
                      : "hover:bg-gray-100 text-gray-700"
                  )}>
                    <section.icon className="h-4 w-4 flex-shrink-0" />
                    {!sidebarCollapsed && (
                      <>
                        <span className="font-medium">{section.title}</span>
                        <div className="ml-auto">
                          {section.status === 'complete' && (
                            <div className="w-2 h-2 bg-green-500 rounded-full" />
                          )}
                          {section.status === 'incomplete' && (
                            <div className="w-2 h-2 bg-gray-300 rounded-full" />
                          )}
                          {section.status === 'warning' && (
                            <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                          )}
                        </div>
                      </>
                    )}
                  </div>
                </Link>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          {children}
        </div>
      </div>
    </div>
  )
}
