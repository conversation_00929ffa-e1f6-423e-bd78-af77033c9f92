'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Shield,
  Lock,
  Key,
  Eye,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Save,
  RefreshCw,
  Download,
  Trash2,
  Clock,
  Globe,
  Smartphone,
  Computer,
  Info
} from 'lucide-react'
import { toast } from 'sonner'

interface SecuritySettings {
  // Password Policy
  minPasswordLength: number
  requireUppercase: boolean
  requireLowercase: boolean
  requireNumbers: boolean
  requireSpecialChars: boolean
  passwordExpiry: number // days, 0 = never
  preventPasswordReuse: number // number of previous passwords
  
  // Two-Factor Authentication
  enable2FA: boolean
  force2FA: boolean
  allowedMethods: string[]
  
  // Session Management
  sessionTimeout: number // minutes
  maxConcurrentSessions: number
  logoutInactiveSessions: boolean
  
  // Login Security
  maxLoginAttempts: number
  lockoutDuration: number // minutes
  enableCaptcha: boolean
  enableIPWhitelist: boolean
  allowedIPs: string[]
  
  // Security Headers
  enableCSP: boolean
  enableHSTS: boolean
  enableXFrameOptions: boolean
  enableXSSProtection: boolean
  
  // Audit & Monitoring
  enableAuditLog: boolean
  logFailedLogins: boolean
  logPasswordChanges: boolean
  logPermissionChanges: boolean
  retentionPeriod: number // days
}

interface LoginAttempt {
  id: string
  email: string
  ip: string
  userAgent: string
  success: boolean
  timestamp: string
  location?: string
}

interface ActiveSession {
  id: string
  userId: string
  email: string
  ip: string
  userAgent: string
  device: string
  location: string
  lastActivity: string
  current: boolean
}

export default function SecuritySettingsPage() {
  const [settings, setSettings] = useState<SecuritySettings>({
    minPasswordLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
    passwordExpiry: 90,
    preventPasswordReuse: 5,
    
    enable2FA: false,
    force2FA: false,
    allowedMethods: ['totp', 'sms'],
    
    sessionTimeout: 60,
    maxConcurrentSessions: 3,
    logoutInactiveSessions: true,
    
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    enableCaptcha: true,
    enableIPWhitelist: false,
    allowedIPs: [],
    
    enableCSP: true,
    enableHSTS: true,
    enableXFrameOptions: true,
    enableXSSProtection: true,
    
    enableAuditLog: true,
    logFailedLogins: true,
    logPasswordChanges: true,
    logPermissionChanges: true,
    retentionPeriod: 365
  })

  const [recentAttempts, setRecentAttempts] = useState<LoginAttempt[]>([
    {
      id: '1',
      email: '<EMAIL>',
      ip: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      success: true,
      timestamp: '2024-01-15T10:30:00Z',
      location: 'Cape Town, ZA'
    },
    {
      id: '2',
      email: '<EMAIL>',
      ip: '************',
      userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
      success: false,
      timestamp: '2024-01-15T09:15:00Z',
      location: 'Unknown'
    }
  ])

  const [activeSessions, setActiveSessions] = useState<ActiveSession[]>([
    {
      id: '1',
      userId: 'admin-1',
      email: '<EMAIL>',
      ip: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      device: 'Windows Desktop',
      location: 'Cape Town, ZA',
      lastActivity: '2024-01-15T10:30:00Z',
      current: true
    },
    {
      id: '2',
      userId: 'admin-1',
      email: '<EMAIL>',
      ip: '*************',
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)',
      device: 'iPhone',
      location: 'Cape Town, ZA',
      lastActivity: '2024-01-15T08:45:00Z',
      current: false
    }
  ])

  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      // In production, this would fetch from your API
      await new Promise(resolve => setTimeout(resolve, 500))
    } catch (error) {
      console.error('Error loading settings:', error)
      toast.error('Failed to load security settings')
    } finally {
      setLoading(false)
    }
  }

  const saveSettings = async () => {
    try {
      setSaving(true)
      // In production, this would save to your API
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Security settings saved successfully')
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Failed to save security settings')
    } finally {
      setSaving(false)
    }
  }

  const terminateSession = async (sessionId: string) => {
    try {
      setActiveSessions(prev => prev.filter(session => session.id !== sessionId))
      toast.success('Session terminated successfully')
    } catch (error) {
      toast.error('Failed to terminate session')
    }
  }

  const exportAuditLog = async () => {
    try {
      // In production, this would download the audit log
      toast.success('Audit log export started')
    } catch (error) {
      toast.error('Failed to export audit log')
    }
  }

  const getPasswordStrength = () => {
    let strength = 0
    if (settings.minPasswordLength >= 8) strength += 20
    if (settings.requireUppercase) strength += 20
    if (settings.requireLowercase) strength += 20
    if (settings.requireNumbers) strength += 20
    if (settings.requireSpecialChars) strength += 20
    return strength
  }

  const getStrengthLabel = (strength: number) => {
    if (strength < 40) return { label: 'Weak', color: 'text-red-600' }
    if (strength < 60) return { label: 'Fair', color: 'text-yellow-600' }
    if (strength < 80) return { label: 'Good', color: 'text-blue-600' }
    return { label: 'Strong', color: 'text-green-600' }
  }

  const passwordStrength = getPasswordStrength()
  const strengthInfo = getStrengthLabel(passwordStrength)

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Security Settings</h1>
        </div>
        <div className="grid gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Security Settings</h1>
          <p className="text-muted-foreground">
            Configure security policies and monitor access to your system
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadSettings} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={saveSettings} disabled={saving}>
            {saving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Security Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Shield className="h-4 w-4 text-green-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Security Score</p>
                <p className="text-2xl font-bold text-green-600">85%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Lock className="h-4 w-4 text-blue-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Active Sessions</p>
                <p className="text-2xl font-bold">{activeSessions.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">Failed Logins (24h)</p>
                <p className="text-2xl font-bold">3</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Key className="h-4 w-4 text-purple-600" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">2FA Enabled</p>
                <p className="text-2xl font-bold">{settings.enable2FA ? 'Yes' : 'No'}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Password Policy */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Lock className="h-5 w-5" />
            <span>Password Policy</span>
          </CardTitle>
          <CardDescription>
            Configure password requirements and security rules
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Minimum Password Length</Label>
                <Select 
                  value={settings.minPasswordLength.toString()} 
                  onValueChange={(value) => setSettings(prev => ({ ...prev, minPasswordLength: parseInt(value) }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="6">6 characters</SelectItem>
                    <SelectItem value="8">8 characters</SelectItem>
                    <SelectItem value="10">10 characters</SelectItem>
                    <SelectItem value="12">12 characters</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>Require Uppercase Letters</Label>
                  <Switch
                    checked={settings.requireUppercase}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, requireUppercase: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label>Require Lowercase Letters</Label>
                  <Switch
                    checked={settings.requireLowercase}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, requireLowercase: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label>Require Numbers</Label>
                  <Switch
                    checked={settings.requireNumbers}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, requireNumbers: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label>Require Special Characters</Label>
                  <Switch
                    checked={settings.requireSpecialChars}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, requireSpecialChars: checked }))}
                  />
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label>Password Strength</Label>
                <div className="mt-2">
                  <Progress value={passwordStrength} className="h-2" />
                  <div className="flex justify-between mt-1">
                    <span className="text-sm text-muted-foreground">Weak</span>
                    <span className={`text-sm font-medium ${strengthInfo.color}`}>
                      {strengthInfo.label}
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Password Expiry (days)</Label>
                <Select 
                  value={settings.passwordExpiry.toString()} 
                  onValueChange={(value) => setSettings(prev => ({ ...prev, passwordExpiry: parseInt(value) }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Never</SelectItem>
                    <SelectItem value="30">30 days</SelectItem>
                    <SelectItem value="60">60 days</SelectItem>
                    <SelectItem value="90">90 days</SelectItem>
                    <SelectItem value="180">180 days</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Prevent Password Reuse</Label>
                <Select 
                  value={settings.preventPasswordReuse.toString()} 
                  onValueChange={(value) => setSettings(prev => ({ ...prev, preventPasswordReuse: parseInt(value) }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Disabled</SelectItem>
                    <SelectItem value="3">Last 3 passwords</SelectItem>
                    <SelectItem value="5">Last 5 passwords</SelectItem>
                    <SelectItem value="10">Last 10 passwords</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Two-Factor Authentication */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Smartphone className="h-5 w-5" />
            <span>Two-Factor Authentication</span>
          </CardTitle>
          <CardDescription>
            Add an extra layer of security to user accounts
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Two-Factor Authentication</Label>
              <p className="text-sm text-muted-foreground">
                Allow users to enable 2FA on their accounts
              </p>
            </div>
            <Switch
              checked={settings.enable2FA}
              onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enable2FA: checked }))}
            />
          </div>

          {settings.enable2FA && (
            <>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Force 2FA for All Users</Label>
                  <p className="text-sm text-muted-foreground">
                    Require all users to set up 2FA
                  </p>
                </div>
                <Switch
                  checked={settings.force2FA}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, force2FA: checked }))}
                />
              </div>

              <div className="space-y-2">
                <Label>Allowed 2FA Methods</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="totp"
                      checked={settings.allowedMethods.includes('totp')}
                      onChange={(e) => {
                        const methods = e.target.checked
                          ? [...settings.allowedMethods, 'totp']
                          : settings.allowedMethods.filter(m => m !== 'totp')
                        setSettings(prev => ({ ...prev, allowedMethods: methods }))
                      }}
                    />
                    <Label htmlFor="totp">Authenticator App (TOTP)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="sms"
                      checked={settings.allowedMethods.includes('sms')}
                      onChange={(e) => {
                        const methods = e.target.checked
                          ? [...settings.allowedMethods, 'sms']
                          : settings.allowedMethods.filter(m => m !== 'sms')
                        setSettings(prev => ({ ...prev, allowedMethods: methods }))
                      }}
                    />
                    <Label htmlFor="sms">SMS</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="email"
                      checked={settings.allowedMethods.includes('email')}
                      onChange={(e) => {
                        const methods = e.target.checked
                          ? [...settings.allowedMethods, 'email']
                          : settings.allowedMethods.filter(m => m !== 'email')
                        setSettings(prev => ({ ...prev, allowedMethods: methods }))
                      }}
                    />
                    <Label htmlFor="email">Email</Label>
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Login Security */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Login Security</span>
          </CardTitle>
          <CardDescription>
            Protect against brute force attacks and unauthorized access
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Max Login Attempts</Label>
                <Select 
                  value={settings.maxLoginAttempts.toString()} 
                  onValueChange={(value) => setSettings(prev => ({ ...prev, maxLoginAttempts: parseInt(value) }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="3">3 attempts</SelectItem>
                    <SelectItem value="5">5 attempts</SelectItem>
                    <SelectItem value="10">10 attempts</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Lockout Duration (minutes)</Label>
                <Select 
                  value={settings.lockoutDuration.toString()} 
                  onValueChange={(value) => setSettings(prev => ({ ...prev, lockoutDuration: parseInt(value) }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 minutes</SelectItem>
                    <SelectItem value="15">15 minutes</SelectItem>
                    <SelectItem value="30">30 minutes</SelectItem>
                    <SelectItem value="60">1 hour</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Enable CAPTCHA</Label>
                  <p className="text-sm text-muted-foreground">
                    Show CAPTCHA after failed login attempts
                  </p>
                </div>
                <Switch
                  checked={settings.enableCaptcha}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enableCaptcha: checked }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>IP Whitelist</Label>
                  <p className="text-sm text-muted-foreground">
                    Only allow login from specific IP addresses
                  </p>
                </div>
                <Switch
                  checked={settings.enableIPWhitelist}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enableIPWhitelist: checked }))}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Active Sessions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Computer className="h-5 w-5" />
                <span>Active Sessions</span>
              </CardTitle>
              <CardDescription>
                Monitor and manage active user sessions
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Device</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Last Activity</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {activeSessions.map((session) => (
                <TableRow key={session.id}>
                  <TableCell>{session.email}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {session.device.includes('iPhone') ? (
                        <Smartphone className="h-4 w-4" />
                      ) : (
                        <Computer className="h-4 w-4" />
                      )}
                      <span>{session.device}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Globe className="h-3 w-3" />
                      <span className="text-sm">{session.location}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-3 w-3" />
                      <span className="text-sm">
                        {new Date(session.lastActivity).toLocaleString()}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {session.current ? (
                      <Badge variant="default">Current</Badge>
                    ) : (
                      <Badge variant="outline">Active</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {!session.current && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => terminateSession(session.id)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Recent Login Attempts */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5" />
                <span>Recent Login Attempts</span>
              </CardTitle>
              <CardDescription>
                Monitor login attempts and security events
              </CardDescription>
            </div>
            <Button variant="outline" onClick={exportAuditLog}>
              <Download className="mr-2 h-4 w-4" />
              Export Log
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>IP Address</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentAttempts.map((attempt) => (
                <TableRow key={attempt.id}>
                  <TableCell>{attempt.email}</TableCell>
                  <TableCell>{attempt.ip}</TableCell>
                  <TableCell>{attempt.location}</TableCell>
                  <TableCell>
                    {new Date(attempt.timestamp).toLocaleString()}
                  </TableCell>
                  <TableCell>
                    {attempt.success ? (
                      <div className="flex items-center space-x-1">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-green-600">Success</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-1">
                        <XCircle className="h-4 w-4 text-red-500" />
                        <span className="text-red-600">Failed</span>
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
