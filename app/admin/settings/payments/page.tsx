'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert'
import {
  CreditCard,
  Banknote,
  Smartphone,
  Building,
  Save,
  RefreshCw,
  TestTube,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye,
  EyeOff,
  Info,
  ExternalLink
} from 'lucide-react'
import { toast } from 'sonner'

interface PaymentMethod {
  id: string
  name: string
  description: string
  enabled: boolean
  icon: any
  settings: Record<string, any>
  testMode: boolean
  status: 'connected' | 'disconnected' | 'testing'
}

export default function PaymentsSettingsPage() {
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({})
  
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([
    {
      id: 'payfast',
      name: 'PayFast',
      description: 'South African payment gateway supporting cards, EFT, and mobile payments',
      enabled: true,
      icon: CreditCard,
      testMode: true,
      status: 'connected',
      settings: {
        merchantId: '********',
        merchantKey: '46f0cd694581a',
        passphrase: '',
        returnUrl: '',
        cancelUrl: '',
        notifyUrl: ''
      }
    },
    {
      id: 'ozow',
      name: 'Ozow',
      description: 'Instant EFT payments for South African customers',
      enabled: true,
      icon: Smartphone,
      testMode: true,
      status: 'connected',
      settings: {
        siteCode: 'TEST-SITE',
        privateKey: '',
        apiKey: '',
        isTest: true,
        bankReference: 'Coco Milk Kids'
      }
    },
    {
      id: 'stripe',
      name: 'Stripe',
      description: 'Global payment processing for cards and digital wallets',
      enabled: false,
      icon: CreditCard,
      testMode: true,
      status: 'disconnected',
      settings: {
        publishableKey: '',
        secretKey: '',
        webhookSecret: '',
        currency: 'ZAR'
      }
    },
    {
      id: 'paypal',
      name: 'PayPal',
      description: 'Accept PayPal payments and credit cards',
      enabled: false,
      icon: Building,
      testMode: true,
      status: 'disconnected',
      settings: {
        clientId: '',
        clientSecret: '',
        mode: 'sandbox'
      }
    },
    {
      id: 'cod',
      name: 'Cash on Delivery',
      description: 'Accept payment when the order is delivered',
      enabled: true,
      icon: Banknote,
      testMode: false,
      status: 'connected',
      settings: {
        title: 'Cash on Delivery',
        description: 'Pay when you receive your order',
        instructions: 'Please have the exact amount ready when the courier arrives.',
        enableForShippingMethods: ['standard', 'express']
      }
    }
  ])

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      // In production, this would fetch from your API
      await new Promise(resolve => setTimeout(resolve, 500))
    } catch (error) {
      console.error('Error loading settings:', error)
      toast.error('Failed to load payment settings')
    } finally {
      setLoading(false)
    }
  }

  const saveSettings = async () => {
    try {
      setSaving(true)
      // In production, this would save to your API
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Payment settings saved successfully')
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Failed to save payment settings')
    } finally {
      setSaving(false)
    }
  }

  const testConnection = async (methodId: string) => {
    const method = paymentMethods.find(m => m.id === methodId)
    if (!method) return

    setPaymentMethods(prev => prev.map(m => 
      m.id === methodId ? { ...m, status: 'testing' } : m
    ))

    try {
      // Simulate API test
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Mock success/failure
      const success = Math.random() > 0.3
      
      setPaymentMethods(prev => prev.map(m => 
        m.id === methodId ? { ...m, status: success ? 'connected' : 'disconnected' } : m
      ))

      if (success) {
        toast.success(`${method.name} connection test successful`)
      } else {
        toast.error(`${method.name} connection test failed`)
      }
    } catch (error) {
      setPaymentMethods(prev => prev.map(m => 
        m.id === methodId ? { ...m, status: 'disconnected' } : m
      ))
      toast.error(`${method.name} connection test failed`)
    }
  }

  const toggleMethod = (methodId: string) => {
    setPaymentMethods(prev => prev.map(m => 
      m.id === methodId ? { ...m, enabled: !m.enabled } : m
    ))
  }

  const toggleTestMode = (methodId: string) => {
    setPaymentMethods(prev => prev.map(m => 
      m.id === methodId ? { ...m, testMode: !m.testMode } : m
    ))
  }

  const updateMethodSetting = (methodId: string, key: string, value: any) => {
    setPaymentMethods(prev => prev.map(m => 
      m.id === methodId ? { 
        ...m, 
        settings: { ...m.settings, [key]: value }
      } : m
    ))
  }

  const toggleShowSecret = (fieldId: string) => {
    setShowSecrets(prev => ({ ...prev, [fieldId]: !prev[fieldId] }))
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'disconnected':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'testing':
        return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge variant="default">Connected</Badge>
      case 'disconnected':
        return <Badge variant="destructive">Disconnected</Badge>
      case 'testing':
        return <Badge variant="secondary">Testing...</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Payment Settings</h1>
        </div>
        <div className="grid gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Payment Settings</h1>
          <p className="text-muted-foreground">
            Configure payment methods and gateways for your store
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadSettings} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={saveSettings} disabled={saving}>
            {saving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Payment Methods Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Methods Overview</CardTitle>
          <CardDescription>
            Manage your payment gateways and processing options
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {paymentMethods.map((method) => (
              <div
                key={method.id}
                className={`p-4 border rounded-lg transition-colors ${
                  method.enabled ? 'border-green-200 bg-green-50' : 'border-gray-200'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <method.icon className="h-5 w-5" />
                    <span className="font-medium">{method.name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(method.status)}
                    <Switch
                      checked={method.enabled}
                      onCheckedChange={() => toggleMethod(method.id)}
                    />
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  {method.description}
                </p>
                <div className="flex items-center justify-between">
                  {getStatusBadge(method.status)}
                  {method.testMode && (
                    <Badge variant="outline" className="text-xs">
                      Test Mode
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Individual Payment Method Settings */}
      {paymentMethods.map((method) => (
        <Card key={method.id} className={!method.enabled ? 'opacity-60' : ''}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <method.icon className="h-6 w-6" />
                <div>
                  <CardTitle>{method.name}</CardTitle>
                  <CardDescription>{method.description}</CardDescription>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {getStatusIcon(method.status)}
                {getStatusBadge(method.status)}
                <Switch
                  checked={method.enabled}
                  onCheckedChange={() => toggleMethod(method.id)}
                />
              </div>
            </div>
          </CardHeader>

          {method.enabled && (
            <CardContent className="space-y-6">
              {/* Test Mode Toggle */}
              {method.id !== 'cod' && (
                <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <TestTube className="h-4 w-4 text-yellow-600" />
                    <div>
                      <Label className="text-yellow-800">Test Mode</Label>
                      <p className="text-sm text-yellow-700">
                        Use sandbox/test environment for this payment method
                      </p>
                    </div>
                  </div>
                  <Switch
                    checked={method.testMode}
                    onCheckedChange={() => toggleTestMode(method.id)}
                  />
                </div>
              )}

              {/* PayFast Settings */}
              {method.id === 'payfast' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Merchant ID</Label>
                      <Input
                        type={showSecrets[`${method.id}-merchantId`] ? 'text' : 'password'}
                        value={method.settings.merchantId}
                        onChange={(e) => updateMethodSetting(method.id, 'merchantId', e.target.value)}
                        placeholder="********"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Merchant Key</Label>
                      <div className="relative">
                        <Input
                          type={showSecrets[`${method.id}-merchantKey`] ? 'text' : 'password'}
                          value={method.settings.merchantKey}
                          onChange={(e) => updateMethodSetting(method.id, 'merchantKey', e.target.value)}
                          placeholder="46f0cd694581a"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3"
                          onClick={() => toggleShowSecret(`${method.id}-merchantKey`)}
                        >
                          {showSecrets[`${method.id}-merchantKey`] ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Passphrase (Optional)</Label>
                    <Input
                      type={showSecrets[`${method.id}-passphrase`] ? 'text' : 'password'}
                      value={method.settings.passphrase}
                      onChange={(e) => updateMethodSetting(method.id, 'passphrase', e.target.value)}
                      placeholder="Leave empty if not using passphrase"
                    />
                  </div>

                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertTitle>PayFast Integration</AlertTitle>
                    <AlertDescription>
                      Make sure to configure your PayFast merchant account with the correct return, cancel, and notify URLs.
                      <Button variant="link" className="p-0 h-auto" asChild>
                        <a href="https://www.payfast.co.za" target="_blank" rel="noopener noreferrer">
                          Visit PayFast <ExternalLink className="ml-1 h-3 w-3" />
                        </a>
                      </Button>
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              {/* Ozow Settings */}
              {method.id === 'ozow' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Site Code</Label>
                      <Input
                        value={method.settings.siteCode}
                        onChange={(e) => updateMethodSetting(method.id, 'siteCode', e.target.value)}
                        placeholder="TEST-SITE"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Bank Reference</Label>
                      <Input
                        value={method.settings.bankReference}
                        onChange={(e) => updateMethodSetting(method.id, 'bankReference', e.target.value)}
                        placeholder="Your Store Name"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Private Key</Label>
                      <Input
                        type={showSecrets[`${method.id}-privateKey`] ? 'text' : 'password'}
                        value={method.settings.privateKey}
                        onChange={(e) => updateMethodSetting(method.id, 'privateKey', e.target.value)}
                        placeholder="Your Ozow private key"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>API Key</Label>
                      <Input
                        type={showSecrets[`${method.id}-apiKey`] ? 'text' : 'password'}
                        value={method.settings.apiKey}
                        onChange={(e) => updateMethodSetting(method.id, 'apiKey', e.target.value)}
                        placeholder="Your Ozow API key"
                      />
                    </div>
                  </div>

                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertTitle>Ozow Integration</AlertTitle>
                    <AlertDescription>
                      Ozow provides instant EFT payments for South African customers. Contact Ozow to get your credentials.
                      <Button variant="link" className="p-0 h-auto" asChild>
                        <a href="https://www.ozow.com" target="_blank" rel="noopener noreferrer">
                          Visit Ozow <ExternalLink className="ml-1 h-3 w-3" />
                        </a>
                      </Button>
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              {/* Cash on Delivery Settings */}
              {method.id === 'cod' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Title</Label>
                      <Input
                        value={method.settings.title}
                        onChange={(e) => updateMethodSetting(method.id, 'title', e.target.value)}
                        placeholder="Cash on Delivery"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Description</Label>
                      <Input
                        value={method.settings.description}
                        onChange={(e) => updateMethodSetting(method.id, 'description', e.target.value)}
                        placeholder="Pay when you receive your order"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Instructions</Label>
                    <Input
                      value={method.settings.instructions}
                      onChange={(e) => updateMethodSetting(method.id, 'instructions', e.target.value)}
                      placeholder="Payment instructions for customers"
                    />
                  </div>
                </div>
              )}

              {/* Test Connection Button */}
              {method.id !== 'cod' && (
                <div className="flex items-center space-x-4 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => testConnection(method.id)}
                    disabled={method.status === 'testing'}
                  >
                    {method.status === 'testing' ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Testing...
                      </>
                    ) : (
                      <>
                        <TestTube className="mr-2 h-4 w-4" />
                        Test Connection
                      </>
                    )}
                  </Button>
                  
                  <div className="text-sm text-muted-foreground">
                    Last tested: Never
                  </div>
                </div>
              )}
            </CardContent>
          )}
        </Card>
      ))}
    </div>
  )
}
