'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert'
import {
  Settings,
  Globe,
  Save,
  RefreshCw,
  Info,
  Upload,
  Image,
  ExternalLink,
  Clock,
  MapPin,
  Mail,
  Phone
} from 'lucide-react'
import { MediaPicker } from '@/components/admin/media/media-picker'
import { MediaFile } from '@/hooks/use-media'
import { toast } from 'sonner'

interface GeneralSettings {
  siteName: string
  siteDescription: string
  siteUrl: string
  adminEmail: string
  timezone: string
  dateFormat: string
  timeFormat: string
  weekStartsOn: string
  language: string
  logoUrl?: string
  faviconUrl?: string
  maintenanceMode: boolean
  maintenanceMessage: string
  registrationEnabled: boolean
  defaultUserRole: string
  enableComments: boolean
  moderateComments: boolean
  enableGravatar: boolean
}

export default function GeneralSettingsPage() {
  const [settings, setSettings] = useState<GeneralSettings>({
    siteName: 'Coco Milk Kids',
    siteDescription: 'Premium children\'s clothing and accessories for South African families',
    siteUrl: 'https://cocomilkkids.co.za',
    adminEmail: '<EMAIL>',
    timezone: 'Africa/Johannesburg',
    dateFormat: 'Y-m-d',
    timeFormat: 'H:i',
    weekStartsOn: '1',
    language: 'en_ZA',
    logoUrl: '',
    faviconUrl: '',
    maintenanceMode: false,
    maintenanceMessage: 'We are currently performing scheduled maintenance. Please check back soon.',
    registrationEnabled: true,
    defaultUserRole: 'customer',
    enableComments: true,
    moderateComments: true,
    enableGravatar: true
  })

  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [logoFiles, setLogoFiles] = useState<MediaFile[]>([])
  const [faviconFiles, setFaviconFiles] = useState<MediaFile[]>([])

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      // In production, this would fetch from your API
      // const response = await fetch('/api/admin/settings/general')
      // const data = await response.json()
      // setSettings(data.settings)
      
      // Mock loading delay
      await new Promise(resolve => setTimeout(resolve, 500))
    } catch (error) {
      console.error('Error loading settings:', error)
      toast.error('Failed to load general settings')
    } finally {
      setLoading(false)
    }
  }

  const saveSettings = async () => {
    try {
      setSaving(true)
      
      const settingsToSave = {
        ...settings,
        logoUrl: logoFiles[0]?.url || '',
        faviconUrl: faviconFiles[0]?.url || ''
      }

      // In production, this would save to your API
      // const response = await fetch('/api/admin/settings/general', {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(settingsToSave)
      // })
      
      // Mock save delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast.success('General settings saved successfully')
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Failed to save general settings')
    } finally {
      setSaving(false)
    }
  }

  const timezones = [
    { value: 'Africa/Johannesburg', label: 'South Africa (SAST)' },
    { value: 'UTC', label: 'UTC' },
    { value: 'America/New_York', label: 'Eastern Time (EST/EDT)' },
    { value: 'America/Los_Angeles', label: 'Pacific Time (PST/PDT)' },
    { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },
    { value: 'Europe/Paris', label: 'Central European Time (CET)' },
    { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },
    { value: 'Australia/Sydney', label: 'Australian Eastern Time (AEST)' }
  ]

  const languages = [
    { value: 'en_ZA', label: 'English (South Africa)' },
    { value: 'en_US', label: 'English (United States)' },
    { value: 'en_GB', label: 'English (United Kingdom)' },
    { value: 'af_ZA', label: 'Afrikaans' },
    { value: 'zu_ZA', label: 'Zulu' },
    { value: 'xh_ZA', label: 'Xhosa' }
  ]

  const userRoles = [
    { value: 'customer', label: 'Customer' },
    { value: 'subscriber', label: 'Subscriber' },
    { value: 'contributor', label: 'Contributor' },
    { value: 'author', label: 'Author' },
    { value: 'editor', label: 'Editor' }
  ]

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">General Settings</h1>
        </div>
        <div className="grid gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">General Settings</h1>
          <p className="text-muted-foreground">
            Configure basic site information and preferences
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadSettings} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={saveSettings} disabled={saving}>
            {saving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Site Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>Site Information</span>
          </CardTitle>
          <CardDescription>
            Basic information about your website
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="siteName">Site Name</Label>
              <Input
                id="siteName"
                value={settings.siteName}
                onChange={(e) => setSettings(prev => ({ ...prev, siteName: e.target.value }))}
                placeholder="Your site name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="siteUrl">Site URL</Label>
              <Input
                id="siteUrl"
                value={settings.siteUrl}
                onChange={(e) => setSettings(prev => ({ ...prev, siteUrl: e.target.value }))}
                placeholder="https://yoursite.com"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="siteDescription">Site Description</Label>
            <Textarea
              id="siteDescription"
              value={settings.siteDescription}
              onChange={(e) => setSettings(prev => ({ ...prev, siteDescription: e.target.value }))}
              placeholder="A brief description of your site"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="adminEmail">Administrator Email</Label>
            <Input
              id="adminEmail"
              type="email"
              value={settings.adminEmail}
              onChange={(e) => setSettings(prev => ({ ...prev, adminEmail: e.target.value }))}
              placeholder="<EMAIL>"
            />
          </div>
        </CardContent>
      </Card>

      {/* Site Identity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Image className="h-5 w-5" />
            <span>Site Identity</span>
          </CardTitle>
          <CardDescription>
            Upload your site logo and favicon
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label>Site Logo</Label>
              <MediaPicker
                value={logoFiles}
                onChange={setLogoFiles}
                accept="image"
                multiple={false}
                label=""
                description="Upload your site logo (recommended: 200x60px)"
              />
            </div>
            
            <div className="space-y-2">
              <Label>Site Favicon</Label>
              <MediaPicker
                value={faviconFiles}
                onChange={setFaviconFiles}
                accept="image"
                multiple={false}
                label=""
                description="Upload your site favicon (recommended: 32x32px)"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Localization */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5" />
            <span>Localization</span>
          </CardTitle>
          <CardDescription>
            Configure timezone, date formats, and language settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="timezone">Timezone</Label>
              <Select value={settings.timezone} onValueChange={(value) => setSettings(prev => ({ ...prev, timezone: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timezones.map((tz) => (
                    <SelectItem key={tz.value} value={tz.value}>
                      {tz.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="language">Language</Label>
              <Select value={settings.language} onValueChange={(value) => setSettings(prev => ({ ...prev, language: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((lang) => (
                    <SelectItem key={lang.value} value={lang.value}>
                      {lang.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="dateFormat">Date Format</Label>
              <Select value={settings.dateFormat} onValueChange={(value) => setSettings(prev => ({ ...prev, dateFormat: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Y-m-d">2024-01-15</SelectItem>
                  <SelectItem value="d/m/Y">15/01/2024</SelectItem>
                  <SelectItem value="m/d/Y">01/15/2024</SelectItem>
                  <SelectItem value="F j, Y">January 15, 2024</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="timeFormat">Time Format</Label>
              <Select value={settings.timeFormat} onValueChange={(value) => setSettings(prev => ({ ...prev, timeFormat: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="H:i">24-hour (14:30)</SelectItem>
                  <SelectItem value="g:i A">12-hour (2:30 PM)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="weekStartsOn">Week Starts On</Label>
              <Select value={settings.weekStartsOn} onValueChange={(value) => setSettings(prev => ({ ...prev, weekStartsOn: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">Sunday</SelectItem>
                  <SelectItem value="1">Monday</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* User Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>User Settings</span>
          </CardTitle>
          <CardDescription>
            Configure user registration and default settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>User Registration</Label>
                <p className="text-sm text-muted-foreground">
                  Allow new users to register on your site
                </p>
              </div>
              <Switch
                checked={settings.registrationEnabled}
                onCheckedChange={(checked) => setSettings(prev => ({ ...prev, registrationEnabled: checked }))}
              />
            </div>

            {settings.registrationEnabled && (
              <div className="space-y-2">
                <Label htmlFor="defaultUserRole">Default User Role</Label>
                <Select value={settings.defaultUserRole} onValueChange={(value) => setSettings(prev => ({ ...prev, defaultUserRole: value }))}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {userRoles.map((role) => (
                      <SelectItem key={role.value} value={role.value}>
                        {role.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          <Separator />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Comments</Label>
                <p className="text-sm text-muted-foreground">
                  Allow users to comment on posts and pages
                </p>
              </div>
              <Switch
                checked={settings.enableComments}
                onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enableComments: checked }))}
              />
            </div>

            {settings.enableComments && (
              <>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Moderate Comments</Label>
                    <p className="text-sm text-muted-foreground">
                      Comments must be approved before appearing
                    </p>
                  </div>
                  <Switch
                    checked={settings.moderateComments}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, moderateComments: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Enable Gravatar</Label>
                    <p className="text-sm text-muted-foreground">
                      Show user avatars from Gravatar service
                    </p>
                  </div>
                  <Switch
                    checked={settings.enableGravatar}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enableGravatar: checked }))}
                  />
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Maintenance Mode */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Maintenance Mode</span>
          </CardTitle>
          <CardDescription>
            Temporarily disable your site for maintenance
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Maintenance Mode</Label>
              <p className="text-sm text-muted-foreground">
                Show maintenance page to visitors
              </p>
            </div>
            <Switch
              checked={settings.maintenanceMode}
              onCheckedChange={(checked) => setSettings(prev => ({ ...prev, maintenanceMode: checked }))}
            />
          </div>

          {settings.maintenanceMode && (
            <>
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Maintenance Mode Active</AlertTitle>
                <AlertDescription>
                  Your site is currently in maintenance mode. Only administrators can access the site.
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <Label htmlFor="maintenanceMessage">Maintenance Message</Label>
                <Textarea
                  id="maintenanceMessage"
                  value={settings.maintenanceMessage}
                  onChange={(e) => setSettings(prev => ({ ...prev, maintenanceMessage: e.target.value }))}
                  placeholder="Message to show visitors during maintenance"
                  rows={3}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
