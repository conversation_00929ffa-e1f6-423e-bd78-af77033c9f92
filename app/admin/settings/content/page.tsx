'use client'

// Content Settings Page
// Admin interface for configuring content-related settings

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Save, 
  FileText, 
  MessageSquare, 
  Eye, 
  Shield,
  Search,
  Globe,
  Zap
} from 'lucide-react'
import { toast } from 'sonner'

export default function ContentSettingsPage() {
  const [settings, setSettings] = useState({
    // Blog Settings
    postsPerPage: 10,
    blogTitle: 'Our Blog',
    blogDescription: 'Latest news, insights, and updates',
    showAuthor: true,
    showDate: true,
    showExcerpt: true,
    showReadingTime: true,
    
    // Comments Settings
    allowComments: true,
    requireApproval: true,
    requireRegistration: false,
    closeCommentsAfter: 30,
    maxCommentDepth: 5,
    
    // SEO Settings
    enableSEO: true,
    defaultMetaDescription: 'Your default meta description for pages without custom descriptions',
    enableOpenGraph: true,
    enableTwitterCards: true,
    enableStructuredData: true,
    
    // Content Settings
    enableRevisions: true,
    maxRevisions: 10,
    enableAutoSave: true,
    autoSaveInterval: 60,
    enablePageBuilder: true,
    
    // Media Settings
    enableFeaturedImages: true,
    defaultImageSize: 'large',
    enableImageOptimization: true,
    enableLazyLoading: true,
    
    // Archive Settings
    enableArchives: true,
    archiveLayout: 'grid',
    showArchiveFilters: true,
    enableSearch: true,
  })

  const [saving, setSaving] = useState(false)

  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      
      // In a real implementation, this would save to your settings API
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
      
      toast.success('Content settings saved successfully!')
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Failed to save settings')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Content Settings</h1>
          <p className="text-muted-foreground">
            Configure how content is displayed and managed
          </p>
        </div>
        
        <Button onClick={handleSave} disabled={saving}>
          <Save className="h-4 w-4 mr-2" />
          {saving ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>

      <Tabs defaultValue="blog" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="blog">Blog</TabsTrigger>
          <TabsTrigger value="comments">Comments</TabsTrigger>
          <TabsTrigger value="seo">SEO</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="media">Media</TabsTrigger>
          <TabsTrigger value="archives">Archives</TabsTrigger>
        </TabsList>

        <TabsContent value="blog" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Blog Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="blogTitle">Blog Title</Label>
                  <Input
                    id="blogTitle"
                    value={settings.blogTitle}
                    onChange={(e) => updateSetting('blogTitle', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="postsPerPage">Posts Per Page</Label>
                  <Input
                    id="postsPerPage"
                    type="number"
                    value={settings.postsPerPage}
                    onChange={(e) => updateSetting('postsPerPage', parseInt(e.target.value))}
                    min="1"
                    max="50"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="blogDescription">Blog Description</Label>
                <Textarea
                  id="blogDescription"
                  value={settings.blogDescription}
                  onChange={(e) => updateSetting('blogDescription', e.target.value)}
                  rows={3}
                />
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Display Options</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="showAuthor">Show Author</Label>
                    <Switch
                      id="showAuthor"
                      checked={settings.showAuthor}
                      onCheckedChange={(checked) => updateSetting('showAuthor', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="showDate">Show Date</Label>
                    <Switch
                      id="showDate"
                      checked={settings.showDate}
                      onCheckedChange={(checked) => updateSetting('showDate', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="showExcerpt">Show Excerpt</Label>
                    <Switch
                      id="showExcerpt"
                      checked={settings.showExcerpt}
                      onCheckedChange={(checked) => updateSetting('showExcerpt', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="showReadingTime">Show Reading Time</Label>
                    <Switch
                      id="showReadingTime"
                      checked={settings.showReadingTime}
                      onCheckedChange={(checked) => updateSetting('showReadingTime', checked)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="comments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Comment Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="allowComments">Allow Comments</Label>
                  <p className="text-sm text-muted-foreground">Enable commenting on posts</p>
                </div>
                <Switch
                  id="allowComments"
                  checked={settings.allowComments}
                  onCheckedChange={(checked) => updateSetting('allowComments', checked)}
                />
              </div>

              {settings.allowComments && (
                <>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="requireApproval">Require Approval</Label>
                      <p className="text-sm text-muted-foreground">Comments must be approved before appearing</p>
                    </div>
                    <Switch
                      id="requireApproval"
                      checked={settings.requireApproval}
                      onCheckedChange={(checked) => updateSetting('requireApproval', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="requireRegistration">Require Registration</Label>
                      <p className="text-sm text-muted-foreground">Users must be logged in to comment</p>
                    </div>
                    <Switch
                      id="requireRegistration"
                      checked={settings.requireRegistration}
                      onCheckedChange={(checked) => updateSetting('requireRegistration', checked)}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="closeCommentsAfter">Close Comments After (days)</Label>
                      <Input
                        id="closeCommentsAfter"
                        type="number"
                        value={settings.closeCommentsAfter}
                        onChange={(e) => updateSetting('closeCommentsAfter', parseInt(e.target.value))}
                        min="0"
                      />
                    </div>
                    <div>
                      <Label htmlFor="maxCommentDepth">Max Comment Depth</Label>
                      <Input
                        id="maxCommentDepth"
                        type="number"
                        value={settings.maxCommentDepth}
                        onChange={(e) => updateSetting('maxCommentDepth', parseInt(e.target.value))}
                        min="1"
                        max="10"
                      />
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="seo" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                SEO Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="enableSEO">Enable SEO Features</Label>
                  <p className="text-sm text-muted-foreground">Enable SEO meta tags and optimization</p>
                </div>
                <Switch
                  id="enableSEO"
                  checked={settings.enableSEO}
                  onCheckedChange={(checked) => updateSetting('enableSEO', checked)}
                />
              </div>

              {settings.enableSEO && (
                <>
                  <div>
                    <Label htmlFor="defaultMetaDescription">Default Meta Description</Label>
                    <Textarea
                      id="defaultMetaDescription"
                      value={settings.defaultMetaDescription}
                      onChange={(e) => updateSetting('defaultMetaDescription', e.target.value)}
                      rows={3}
                    />
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium">Social Media</h4>
                    
                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableOpenGraph">Enable Open Graph</Label>
                      <Switch
                        id="enableOpenGraph"
                        checked={settings.enableOpenGraph}
                        onCheckedChange={(checked) => updateSetting('enableOpenGraph', checked)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableTwitterCards">Enable Twitter Cards</Label>
                      <Switch
                        id="enableTwitterCards"
                        checked={settings.enableTwitterCards}
                        onCheckedChange={(checked) => updateSetting('enableTwitterCards', checked)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableStructuredData">Enable Structured Data</Label>
                      <Switch
                        id="enableStructuredData"
                        checked={settings.enableStructuredData}
                        onCheckedChange={(checked) => updateSetting('enableStructuredData', checked)}
                      />
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Content Features
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="enableRevisions">Enable Revisions</Label>
                  <p className="text-sm text-muted-foreground">Keep track of content changes</p>
                </div>
                <Switch
                  id="enableRevisions"
                  checked={settings.enableRevisions}
                  onCheckedChange={(checked) => updateSetting('enableRevisions', checked)}
                />
              </div>

              {settings.enableRevisions && (
                <div>
                  <Label htmlFor="maxRevisions">Max Revisions to Keep</Label>
                  <Input
                    id="maxRevisions"
                    type="number"
                    value={settings.maxRevisions}
                    onChange={(e) => updateSetting('maxRevisions', parseInt(e.target.value))}
                    min="1"
                    max="50"
                  />
                </div>
              )}

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="enableAutoSave">Enable Auto-Save</Label>
                  <p className="text-sm text-muted-foreground">Automatically save drafts while editing</p>
                </div>
                <Switch
                  id="enableAutoSave"
                  checked={settings.enableAutoSave}
                  onCheckedChange={(checked) => updateSetting('enableAutoSave', checked)}
                />
              </div>

              {settings.enableAutoSave && (
                <div>
                  <Label htmlFor="autoSaveInterval">Auto-Save Interval (seconds)</Label>
                  <Input
                    id="autoSaveInterval"
                    type="number"
                    value={settings.autoSaveInterval}
                    onChange={(e) => updateSetting('autoSaveInterval', parseInt(e.target.value))}
                    min="30"
                    max="300"
                  />
                </div>
              )}

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="enablePageBuilder">Enable Page Builder</Label>
                  <p className="text-sm text-muted-foreground">Allow visual page building for supported post types</p>
                </div>
                <Switch
                  id="enablePageBuilder"
                  checked={settings.enablePageBuilder}
                  onCheckedChange={(checked) => updateSetting('enablePageBuilder', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="media" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Media Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="enableFeaturedImages">Enable Featured Images</Label>
                  <p className="text-sm text-muted-foreground">Allow posts to have featured images</p>
                </div>
                <Switch
                  id="enableFeaturedImages"
                  checked={settings.enableFeaturedImages}
                  onCheckedChange={(checked) => updateSetting('enableFeaturedImages', checked)}
                />
              </div>

              <div>
                <Label htmlFor="defaultImageSize">Default Image Size</Label>
                <Select value={settings.defaultImageSize} onValueChange={(value) => updateSetting('defaultImageSize', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="small">Small (300px)</SelectItem>
                    <SelectItem value="medium">Medium (600px)</SelectItem>
                    <SelectItem value="large">Large (1200px)</SelectItem>
                    <SelectItem value="full">Full Size</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="enableImageOptimization">Enable Image Optimization</Label>
                <Switch
                  id="enableImageOptimization"
                  checked={settings.enableImageOptimization}
                  onCheckedChange={(checked) => updateSetting('enableImageOptimization', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="enableLazyLoading">Enable Lazy Loading</Label>
                <Switch
                  id="enableLazyLoading"
                  checked={settings.enableLazyLoading}
                  onCheckedChange={(checked) => updateSetting('enableLazyLoading', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="archives" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Archive Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="enableArchives">Enable Archive Pages</Label>
                  <p className="text-sm text-muted-foreground">Create listing pages for post types</p>
                </div>
                <Switch
                  id="enableArchives"
                  checked={settings.enableArchives}
                  onCheckedChange={(checked) => updateSetting('enableArchives', checked)}
                />
              </div>

              {settings.enableArchives && (
                <>
                  <div>
                    <Label htmlFor="archiveLayout">Archive Layout</Label>
                    <Select value={settings.archiveLayout} onValueChange={(value) => updateSetting('archiveLayout', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="grid">Grid Layout</SelectItem>
                        <SelectItem value="list">List Layout</SelectItem>
                        <SelectItem value="masonry">Masonry Layout</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="showArchiveFilters">Show Archive Filters</Label>
                    <Switch
                      id="showArchiveFilters"
                      checked={settings.showArchiveFilters}
                      onCheckedChange={(checked) => updateSetting('showArchiveFilters', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="enableSearch">Enable Search</Label>
                    <Switch
                      id="enableSearch"
                      checked={settings.enableSearch}
                      onCheckedChange={(checked) => updateSetting('enableSearch', checked)}
                    />
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
