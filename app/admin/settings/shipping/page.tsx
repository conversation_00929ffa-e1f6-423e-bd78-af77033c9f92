'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Truck,
  Package,
  MapPin,
  Plus,
  Edit,
  Trash2,
  Save,
  RefreshCw,
  Clock,
  DollarSign,
  Ruler,
  Weight,
  Globe
} from 'lucide-react'
import { toast } from 'sonner'

interface ShippingZone {
  id: string
  name: string
  regions: string[]
  methods: ShippingMethod[]
}

interface ShippingMethod {
  id: string
  name: string
  description: string
  type: 'flat_rate' | 'free_shipping' | 'local_pickup' | 'calculated'
  cost: number
  minAmount?: number
  maxAmount?: number
  enabled: boolean
  estimatedDays: string
}

interface ShippingSettings {
  enableShipping: boolean
  enableShippingCalculator: boolean
  hideShippingUntilAddress: boolean
  enableLocalPickup: boolean
  pickupLocations: string[]
  defaultDimensions: {
    length: number
    width: number
    height: number
    weight: number
  }
  weightUnit: 'kg' | 'g' | 'lbs' | 'oz'
  dimensionUnit: 'cm' | 'm' | 'in' | 'ft'
}

export default function ShippingSettingsPage() {
  const [settings, setSettings] = useState<ShippingSettings>({
    enableShipping: true,
    enableShippingCalculator: true,
    hideShippingUntilAddress: false,
    enableLocalPickup: true,
    pickupLocations: ['Cape Town Store', 'Johannesburg Store'],
    defaultDimensions: {
      length: 20,
      width: 15,
      height: 5,
      weight: 0.5
    },
    weightUnit: 'kg',
    dimensionUnit: 'cm'
  })

  const [shippingZones, setShippingZones] = useState<ShippingZone[]>([
    {
      id: 'south-africa',
      name: 'South Africa',
      regions: ['Western Cape', 'Gauteng', 'KwaZulu-Natal', 'Eastern Cape', 'Free State', 'Limpopo', 'Mpumalanga', 'Northern Cape', 'North West'],
      methods: [
        {
          id: 'standard',
          name: 'Standard Shipping',
          description: 'Regular delivery within 3-5 business days',
          type: 'flat_rate',
          cost: 99,
          enabled: true,
          estimatedDays: '3-5'
        },
        {
          id: 'express',
          name: 'Express Shipping',
          description: 'Fast delivery within 1-2 business days',
          type: 'flat_rate',
          cost: 199,
          enabled: true,
          estimatedDays: '1-2'
        },
        {
          id: 'free',
          name: 'Free Shipping',
          description: 'Free delivery on orders over R500',
          type: 'free_shipping',
          cost: 0,
          minAmount: 500,
          enabled: true,
          estimatedDays: '3-5'
        }
      ]
    },
    {
      id: 'international',
      name: 'International',
      regions: ['Rest of World'],
      methods: [
        {
          id: 'international-standard',
          name: 'International Standard',
          description: 'International delivery within 7-14 business days',
          type: 'flat_rate',
          cost: 299,
          enabled: true,
          estimatedDays: '7-14'
        },
        {
          id: 'international-express',
          name: 'International Express',
          description: 'Fast international delivery within 3-5 business days',
          type: 'flat_rate',
          cost: 599,
          enabled: false,
          estimatedDays: '3-5'
        }
      ]
    }
  ])

  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [editingZone, setEditingZone] = useState<ShippingZone | null>(null)
  const [editingMethod, setEditingMethod] = useState<ShippingMethod | null>(null)
  const [showZoneDialog, setShowZoneDialog] = useState(false)
  const [showMethodDialog, setShowMethodDialog] = useState(false)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      // In production, this would fetch from your API
      await new Promise(resolve => setTimeout(resolve, 500))
    } catch (error) {
      console.error('Error loading settings:', error)
      toast.error('Failed to load shipping settings')
    } finally {
      setLoading(false)
    }
  }

  const saveSettings = async () => {
    try {
      setSaving(true)
      // In production, this would save to your API
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Shipping settings saved successfully')
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Failed to save shipping settings')
    } finally {
      setSaving(false)
    }
  }

  const toggleMethodEnabled = (zoneId: string, methodId: string) => {
    setShippingZones(prev => prev.map(zone => 
      zone.id === zoneId ? {
        ...zone,
        methods: zone.methods.map(method => 
          method.id === methodId ? { ...method, enabled: !method.enabled } : method
        )
      } : zone
    ))
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount)
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Shipping Settings</h1>
        </div>
        <div className="grid gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Shipping Settings</h1>
          <p className="text-muted-foreground">
            Configure shipping zones, methods, and delivery options
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadSettings} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={saveSettings} disabled={saving}>
            {saving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>

      {/* General Shipping Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Truck className="h-5 w-5" />
            <span>General Settings</span>
          </CardTitle>
          <CardDescription>
            Configure basic shipping preferences and calculations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Shipping</Label>
                <p className="text-sm text-muted-foreground">
                  Allow customers to choose shipping options
                </p>
              </div>
              <Switch
                checked={settings.enableShipping}
                onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enableShipping: checked }))}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Shipping Calculator</Label>
                <p className="text-sm text-muted-foreground">
                  Show shipping calculator on cart and product pages
                </p>
              </div>
              <Switch
                checked={settings.enableShippingCalculator}
                onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enableShippingCalculator: checked }))}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Hide Shipping Until Address</Label>
                <p className="text-sm text-muted-foreground">
                  Only show shipping options after customer enters address
                </p>
              </div>
              <Switch
                checked={settings.hideShippingUntilAddress}
                onCheckedChange={(checked) => setSettings(prev => ({ ...prev, hideShippingUntilAddress: checked }))}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Local Pickup</Label>
                <p className="text-sm text-muted-foreground">
                  Allow customers to collect orders from your store
                </p>
              </div>
              <Switch
                checked={settings.enableLocalPickup}
                onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enableLocalPickup: checked }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Package Dimensions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>Package Dimensions</span>
          </CardTitle>
          <CardDescription>
            Default package dimensions for shipping calculations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Weight Unit</Label>
              <Select 
                value={settings.weightUnit} 
                onValueChange={(value: 'kg' | 'g' | 'lbs' | 'oz') => setSettings(prev => ({ ...prev, weightUnit: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="kg">Kilograms (kg)</SelectItem>
                  <SelectItem value="g">Grams (g)</SelectItem>
                  <SelectItem value="lbs">Pounds (lbs)</SelectItem>
                  <SelectItem value="oz">Ounces (oz)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Dimension Unit</Label>
              <Select 
                value={settings.dimensionUnit} 
                onValueChange={(value: 'cm' | 'm' | 'in' | 'ft') => setSettings(prev => ({ ...prev, dimensionUnit: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cm">Centimeters (cm)</SelectItem>
                  <SelectItem value="m">Meters (m)</SelectItem>
                  <SelectItem value="in">Inches (in)</SelectItem>
                  <SelectItem value="ft">Feet (ft)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>Length ({settings.dimensionUnit})</Label>
              <Input
                type="number"
                value={settings.defaultDimensions.length}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  defaultDimensions: { ...prev.defaultDimensions, length: parseFloat(e.target.value) || 0 }
                }))}
              />
            </div>

            <div className="space-y-2">
              <Label>Width ({settings.dimensionUnit})</Label>
              <Input
                type="number"
                value={settings.defaultDimensions.width}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  defaultDimensions: { ...prev.defaultDimensions, width: parseFloat(e.target.value) || 0 }
                }))}
              />
            </div>

            <div className="space-y-2">
              <Label>Height ({settings.dimensionUnit})</Label>
              <Input
                type="number"
                value={settings.defaultDimensions.height}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  defaultDimensions: { ...prev.defaultDimensions, height: parseFloat(e.target.value) || 0 }
                }))}
              />
            </div>

            <div className="space-y-2">
              <Label>Weight ({settings.weightUnit})</Label>
              <Input
                type="number"
                step="0.1"
                value={settings.defaultDimensions.weight}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  defaultDimensions: { ...prev.defaultDimensions, weight: parseFloat(e.target.value) || 0 }
                }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Shipping Zones */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <MapPin className="h-5 w-5" />
                <span>Shipping Zones</span>
              </CardTitle>
              <CardDescription>
                Configure shipping methods for different regions
              </CardDescription>
            </div>
            <Button onClick={() => setShowZoneDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Zone
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {shippingZones.map((zone) => (
              <Card key={zone.id} className="border-l-4 border-l-blue-500">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{zone.name}</CardTitle>
                      <CardDescription>
                        {zone.regions.join(', ')}
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => setShowMethodDialog(true)}>
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Method</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Cost</TableHead>
                        <TableHead>Delivery Time</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {zone.methods.map((method) => (
                        <TableRow key={method.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{method.name}</div>
                              <div className="text-sm text-muted-foreground">{method.description}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {method.type.replace('_', ' ').toUpperCase()}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {method.type === 'free_shipping' ? (
                              <div>
                                <div className="font-medium">Free</div>
                                {method.minAmount && (
                                  <div className="text-sm text-muted-foreground">
                                    Min: {formatCurrency(method.minAmount)}
                                  </div>
                                )}
                              </div>
                            ) : (
                              formatCurrency(method.cost)
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span className="text-sm">{method.estimatedDays} days</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Switch
                              checked={method.enabled}
                              onCheckedChange={() => toggleMethodEnabled(zone.id, method.id)}
                            />
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Button variant="ghost" size="sm">
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
