import { notFound } from 'next/navigation'
import { PageRenderer } from '@/lib/page-builder/components/page-renderer'
import { prisma } from '@/lib/ecommerce/config/database'
import { Metadata } from 'next'

interface DynamicPageProps {
  params: {
    slug: string
  }
}

// Generate metadata for SEO
export async function generateMetadata({ params }: DynamicPageProps): Promise<Metadata> {
  const resolvedParams = await params
  const page = await prisma.page.findUnique({
    where: {
      slug: resolvedParams.slug,
      status: 'published'
    },
    select: {
      title: true,
      seoTitle: true,
      seoDescription: true,
      seoKeywords: true,
      ogImage: true
    }
  })

  if (!page) {
    return {
      title: 'Page Not Found'
    }
  }

  const seoTitle = page.seoTitle || page.title
  const seoDescription = page.seoDescription || ''
  const seoKeywords = page.seoKeywords || []

  return {
    title: seoTitle,
    description: seoDescription,
    keywords: seoKeywords,
    openGraph: {
      title: seoTitle,
      description: seoDescription,
      type: 'website',
      ...(page.ogImage && { images: [page.ogImage] })
    },
    twitter: {
      card: 'summary_large_image',
      title: seoTitle,
      description: seoDescription
    }
  }
}

// Generate static params for static generation (optional)
export async function generateStaticParams() {
  try {
    const pages = await prisma.page.findMany({
      where: {
        status: 'published'
      },
      select: {
        slug: true
      }
    })

    return pages.map((page) => ({
      slug: page.slug
    }))
  } catch (error) {
    console.error('Error generating static params:', error)
    return []
  }
}

export default async function DynamicPage({ params }: DynamicPageProps) {
  try {
    const resolvedParams = await params
    // Fetch the page data
    const page = await prisma.page.findUnique({
      where: {
        slug: resolvedParams.slug,
        status: 'published'
      },
      include: {
        blocks: {
          where: { isVisible: true },
          orderBy: { position: 'asc' }
        }
      }
    })

    if (!page) {
      notFound()
    }

    // Convert to PageData format expected by PageRenderer
    const pageData = {
      id: page.id,
      title: page.title,
      slug: page.slug,
      description: page.description,
      status: page.status,
      type: page.type,
      blocks: page.blocks.map(block => ({
        id: block.id,
        type: block.blockType,
        position: block.position,
        isVisible: block.isVisible,
        configuration: block.configuration as any,
        content: block.content as any,
        styling: block.styling as any,
        responsive: block.responsive as any
      })),
      settings: {
        seoTitle: page.seoTitle,
        seoDescription: page.seoDescription,
        seoKeywords: page.seoKeywords,
        ogImage: page.ogImage,
        customCss: page.customCss,
        customJs: page.customJs,
        requiresAuth: page.requiresAuth,
        allowComments: page.allowComments
      }
    }

    return (
      <div className="min-h-screen">
        <PageRenderer page={pageData} />
      </div>
    )
  } catch (error) {
    console.error('Error rendering dynamic page:', error)
    notFound()
  }
}
