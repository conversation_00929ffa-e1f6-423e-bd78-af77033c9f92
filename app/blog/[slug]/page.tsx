// Individual Blog Post Page
// Dynamic page for displaying single blog posts

import React from 'react'
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { BlogPost } from '@/lib/posts/components/blog-post'
import { BlogList } from '@/lib/posts/components/blog-list'
import { PostService } from '@/lib/posts/services/post-service'
import { generateSeoMeta } from '@/lib/posts/utils'
import { Post } from '@/lib/posts/types'

interface BlogPostPageProps {
  params: { slug: string }
  searchParams: { preview?: string }
}

async function getPost(slug: string): Promise<Post | null> {
  try {
    const postService = new PostService()
    const result = await postService.getPostBySlug(slug, [
      'taxonomyTerms', 
      'comments', 
      'meta'
    ])

    return result.success ? result.data || null : null
  } catch (error) {
    console.error('Error fetching post:', error)
    return null
  }
}

async function getRelatedPosts(post: Post): Promise<Post[]> {
  try {
    const postService = new PostService()
    
    // Get posts from the same categories
    const categories = post.taxonomyTerms?.filter(
      term => term.term?.taxonomy?.name === 'category'
    ) || []

    if (categories.length === 0) {
      // If no categories, get recent posts
      const result = await postService.queryPosts({
        postType: 'post',
        status: 'published',
        orderBy: 'publishedAt',
        order: 'desc',
        limit: 3,
        include: ['taxonomyTerms']
      })
      
      return result.success ? (result.data?.posts || []).filter(p => p.id !== post.id) : []
    }

    // Get posts with similar categories
    const categoryNames = categories.map(c => c.term?.slug).filter(Boolean) as string[]
    const result = await postService.queryPosts({
      postType: 'post',
      status: 'published',
      taxonomies: {
        category: categoryNames
      },
      orderBy: 'publishedAt',
      order: 'desc',
      limit: 4,
      include: ['taxonomyTerms']
    })

    return result.success 
      ? (result.data?.posts || []).filter(p => p.id !== post.id).slice(0, 3)
      : []
  } catch (error) {
    console.error('Error fetching related posts:', error)
    return []
  }
}

// Generate metadata for SEO
export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = await getPost(params.slug)
  
  if (!post) {
    return {
      title: 'Post Not Found',
      description: 'The requested blog post could not be found.'
    }
  }

  const seoMeta = generateSeoMeta(post, 'Your Store Name')

  return {
    title: seoMeta.title,
    description: seoMeta.description,
    keywords: seoMeta.keywords,
    authors: post.authorName ? [{ name: post.authorName }] : undefined,
    openGraph: {
      title: seoMeta.openGraph.title,
      description: seoMeta.openGraph.description,
      images: seoMeta.openGraph.image ? [seoMeta.openGraph.image] : undefined,
      url: seoMeta.canonical,
      type: 'article',
      publishedTime: seoMeta.openGraph.publishedTime,
      modifiedTime: seoMeta.openGraph.modifiedTime,
      authors: seoMeta.openGraph.author ? [seoMeta.openGraph.author] : undefined,
    },
    twitter: {
      card: seoMeta.twitter.card as any,
      title: seoMeta.twitter.title,
      description: seoMeta.twitter.description,
      images: seoMeta.twitter.image ? [seoMeta.twitter.image] : undefined,
    },
    alternates: {
      canonical: seoMeta.canonical
    },
    robots: seoMeta.robots
  }
}

export default async function BlogPostPage({ params, searchParams }: BlogPostPageProps) {
  const post = await getPost(params.slug)
  
  if (!post) {
    notFound()
  }

  // Check if post should be visible
  const isPreview = searchParams.preview === 'true'
  if (!isPreview && post.status !== 'published') {
    notFound()
  }

  const relatedPosts = await getRelatedPosts(post)

  // Handle post interactions (these would typically be client-side)
  const handleLike = async () => {
    // Implementation for liking a post
    console.log('Like post:', post.id)
  }

  const handleShare = async () => {
    // Implementation for sharing a post
    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt || post.title,
          url: window.location.href,
        })
      } catch (error) {
        // Fallback to copying URL
        navigator.clipboard.writeText(window.location.href)
      }
    }
  }

  const handleComment = () => {
    // Scroll to comments section or open comment form
    const commentsSection = document.getElementById('comments')
    if (commentsSection) {
      commentsSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Preview Banner */}
      {isPreview && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-800 px-4 py-3 rounded mb-6">
          <strong>Preview Mode:</strong> This is a preview of an unpublished post.
        </div>
      )}

      <div className="max-w-4xl mx-auto">
        {/* Main Post */}
        <BlogPost
          post={post}
          showFullContent={true}
          showMeta={true}
          showActions={true}
          showComments={true}
          onLike={handleLike}
          onShare={handleShare}
          onComment={handleComment}
        />

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <div className="mt-16 pt-8 border-t">
            <h2 className="text-2xl font-bold mb-8">Related Posts</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {relatedPosts.map((relatedPost) => (
                <BlogPost
                  key={relatedPost.id}
                  post={relatedPost}
                  showFullContent={false}
                  showMeta={true}
                  showActions={false}
                  showComments={false}
                  className="border rounded-lg p-4"
                />
              ))}
            </div>
          </div>
        )}

        {/* Comments Section */}
        <div id="comments" className="mt-16 pt-8 border-t">
          <h2 className="text-2xl font-bold mb-6">Comments</h2>
          
          {post.allowComments ? (
            <div className="space-y-6">
              {/* Comment Form */}
              <div className="bg-muted/50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold mb-4">Leave a Comment</h3>
                <form className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium mb-1">
                        Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        required
                        className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium mb-1">
                        Email *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        required
                        className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                      />
                    </div>
                  </div>
                  <div>
                    <label htmlFor="comment" className="block text-sm font-medium mb-1">
                      Comment *
                    </label>
                    <textarea
                      id="comment"
                      name="comment"
                      rows={4}
                      required
                      className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                      placeholder="Share your thoughts..."
                    />
                  </div>
                  <button
                    type="submit"
                    className="bg-primary text-primary-foreground px-6 py-2 rounded-md hover:bg-primary/90 transition-colors"
                  >
                    Post Comment
                  </button>
                </form>
              </div>

              {/* Existing Comments */}
              {post.comments && post.comments.length > 0 && (
                <div className="space-y-4">
                  {post.comments.map((comment) => (
                    <div key={comment.id} className="bg-background border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="font-medium">{comment.authorName}</div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(comment.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div 
                        className="prose prose-sm max-w-none"
                        dangerouslySetInnerHTML={{ __html: comment.content }}
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <p className="text-muted-foreground">Comments are disabled for this post.</p>
          )}
        </div>
      </div>
    </div>
  )
}
