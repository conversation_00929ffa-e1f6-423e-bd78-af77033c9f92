'use client'

// Public Blog Page
// Main blog listing page for frontend users

import React, { useState, useEffect } from 'react'
import { BlogList } from '@/lib/posts/components/blog-list'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle, Database, RefreshCw } from 'lucide-react'

export default function BlogPage() {
  const [initialPosts, setInitialPosts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [systemInitialized, setSystemInitialized] = useState(false)

  const checkSystemStatus = async () => {
    try {
      const response = await fetch('/api/posts/init')
      const data = await response.json()
      return data.success && data.data?.isInitialized
    } catch (error) {
      console.error('Error checking system status:', error)
      return false
    }
  }

  const initializeSystem = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/posts/init', { method: 'POST' })
      const data = await response.json()

      if (data.success) {
        setSystemInitialized(true)
        await loadPosts()
      } else {
        setError('Failed to initialize posts system')
      }
    } catch (error) {
      console.error('Error initializing system:', error)
      setError('Failed to initialize posts system')
    } finally {
      setLoading(false)
    }
  }

  const loadPosts = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/posts?postType=post&status=published&limit=9&include=taxonomyTerms')
      const data = await response.json()

      if (data.success) {
        setInitialPosts(data.data?.posts || [])
      } else {
        setError('Failed to load posts')
      }
    } catch (error) {
      console.error('Error loading posts:', error)
      setError('Failed to load posts')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    const initializePage = async () => {
      const isInitialized = await checkSystemStatus()
      setSystemInitialized(isInitialized)

      if (isInitialized) {
        await loadPosts()
      } else {
        setLoading(false)
      }
    }

    initializePage()
  }, [])

  // Loading state
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-2">Loading Blog...</h2>
          <p className="text-muted-foreground">Please wait while we load the blog posts.</p>
        </div>
      </div>
    )
  }

  // System not initialized state
  if (!systemInitialized) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Our Blog</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Discover insights, tips, and stories from our team.
          </p>
        </div>

        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Blog System Setup Required
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-6">
              The blog system needs to be initialized before you can view posts.
              This will create the necessary database tables and sample content.
            </p>
            <Button onClick={initializeSystem} disabled={loading}>
              <Database className="h-4 w-4 mr-2" />
              Initialize Blog System
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Our Blog</h1>
        </div>

        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              Error Loading Blog
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-muted-foreground mb-6">{error}</p>
            <Button onClick={loadPosts} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Normal state
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold mb-4">
          Our Blog
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Discover insights, tips, and stories from our team. Stay updated with the latest trends and news.
        </p>
      </div>

      {/* Blog List */}
      <BlogList
        initialPosts={initialPosts}
        showSearch={true}
        showFilters={true}
        showPagination={true}
        layout="grid"
        postsPerPage={9}
        baseUrl="/blog"
      />
    </div>
  )
}
