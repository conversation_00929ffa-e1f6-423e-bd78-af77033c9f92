import { <PERSON><PERSON>enderer } from '@/lib/page-builder/components/page-renderer'
import { PageData } from '@/lib/page-builder/types'

// This demonstrates how the hardcoded checkout page can be converted to use the page builder
// The page will render exactly the same as the hardcoded version but using the page builder system

export default function CheckoutDynamicPage() {
  // Create a page configuration that uses the Checkout Block
  const pageData: PageData = {
    id: 'checkout-dynamic',
    title: 'Checkout',
    slug: 'checkout-dynamic',
    description: 'Complete your purchase',
    status: 'published',
    type: 'custom',
    blocks: [
      {
        id: 'checkout-1',
        type: 'checkout',
        position: 0,
        isVisible: true,
        configuration: {},
        content: {},
        styling: {},
        responsive: {
          desktop: { isVisible: true },
          tablet: { isVisible: true },
          mobile: { isVisible: true }
        }
      }
    ],
    settings: {
      title: 'Checkout',
      description: 'Complete your purchase',
      seoTitle: 'Checkout - Coco Milk Kids',
      seoDescription: 'Complete your purchase securely.',
      requiresAuth: false,
      allowComments: false
    }
  }

  return (
    <div>
      {/* This will render exactly like the hardcoded checkout page */}
      <PageRenderer page={pageData} />
    </div>
  )
}
