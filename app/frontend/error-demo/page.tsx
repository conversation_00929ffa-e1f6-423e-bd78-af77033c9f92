"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export default function ErrorDemoPage() {
  const [count, setCount] = useState(0);

  // Function that throws a client-side error
  const triggerClientError = () => {
    throw new Error("This is a simulated client-side error");
  };

  // Function that causes a React state error
  const triggerStateError = () => {
    // @ts-ignore - Intentionally causing an error
    setCount(null);
  };

  // Function that causes a promise rejection
  const triggerAsyncError = async () => {
    try {
      await new Promise((_, reject) => {
        setTimeout(() => reject(new Error("Async operation failed")), 500);
      });
    } catch (error) {
      throw error;
    }
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Error Component Demo</h1>
      <p className="text-gray-600 dark:text-gray-400 mb-8">
        This page demonstrates the error handling capabilities of our application.
        Click the buttons below to trigger different types of errors.
      </p>

      <Tabs defaultValue="client" className="w-full max-w-3xl">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="client">Client Errors</TabsTrigger>
          <TabsTrigger value="state">State Errors</TabsTrigger>
          <TabsTrigger value="async">Async Errors</TabsTrigger>
        </TabsList>
        
        <TabsContent value="client" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Client-Side Error</CardTitle>
              <CardDescription>
                Trigger a standard JavaScript error that will be caught by the error boundary.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                This will throw a simple Error object that will be caught by the nearest error boundary.
              </p>
            </CardContent>
            <CardFooter>
              <Button onClick={triggerClientError} variant="destructive">
                Trigger Client Error
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="state" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>React State Error</CardTitle>
              <CardDescription>
                Trigger an error by setting an invalid state value.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                Current count: {count}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                This will attempt to set the count state to null, which will cause a React error.
              </p>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button onClick={() => setCount(count + 1)} variant="outline">
                Increment Count
              </Button>
              <Button onClick={triggerStateError} variant="destructive">
                Trigger State Error
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="async" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Async Error</CardTitle>
              <CardDescription>
                Trigger an error in an asynchronous operation.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                This will simulate a failed API call or other asynchronous operation.
              </p>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={() => {
                  triggerAsyncError().catch(error => {
                    throw error;
                  });
                }} 
                variant="destructive"
              >
                Trigger Async Error
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}