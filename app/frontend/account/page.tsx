'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { toast } from '@/components/ui/use-toast'
import { User, Package, MapPin, Heart, Bell, Settings, Eye, Download } from 'lucide-react'
import Link from 'next/link'

interface CustomerOrder {
  id: string
  orderNumber: string
  status: string
  paymentStatus: string
  total: number
  currency: string
  createdAt: string
  items: Array<{
    name: string
    quantity: number
    price: number
    image?: string
  }>
  trackingNumber?: string
}

interface CustomerProfile {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  dateOfBirth?: string
  preferences: {
    newsletter: boolean
    smsNotifications: boolean
    emailNotifications: boolean
  }
}

interface Address {
  id: string
  type: 'shipping' | 'billing'
  firstName: string
  lastName: string
  address1: string
  address2?: string
  city: string
  province: string
  postalCode: string
  country: string
  isDefault: boolean
}

export default function CustomerAccountPage() {
  const [profile, setProfile] = useState<CustomerProfile | null>(null)
  const [orders, setOrders] = useState<CustomerOrder[]>([])
  const [addresses, setAddresses] = useState<Address[]>([])
  const [wishlistItems, setWishlistItems] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    fetchCustomerData()
  }, [])

  const fetchCustomerData = async () => {
    try {
      setLoading(true)
      
      // Fetch customer profile
      const profileResponse = await fetch('/api/customer/profile')
      if (profileResponse.ok) {
        const profileData = await profileResponse.json()
        setProfile(profileData.profile)
      }

      // Fetch customer orders
      const ordersResponse = await fetch('/api/e-commerce/orders')
      if (ordersResponse.ok) {
        const ordersData = await ordersResponse.json()
        setOrders(ordersData.data || [])
      }

      // Fetch customer addresses
      const addressesResponse = await fetch('/api/customer/addresses')
      if (addressesResponse.ok) {
        const addressesData = await addressesResponse.json()
        setAddresses(addressesData.addresses || [])
      }

      // Fetch wishlist
      const wishlistResponse = await fetch('/api/customer/wishlist')
      if (wishlistResponse.ok) {
        const wishlistData = await wishlistResponse.json()
        setWishlistItems(wishlistData.items || [])
      }

    } catch (error) {
      console.error('Failed to fetch customer data:', error)
      toast({
        title: 'Error',
        description: 'Failed to load account information',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const updateProfile = async (updatedProfile: Partial<CustomerProfile>) => {
    try {
      const response = await fetch('/api/customer/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updatedProfile)
      })

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Profile updated successfully'
        })
        fetchCustomerData()
      } else {
        throw new Error('Failed to update profile')
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update profile',
        variant: 'destructive'
      })
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'Pending', variant: 'secondary' as const },
      confirmed: { label: 'Confirmed', variant: 'default' as const },
      processing: { label: 'Processing', variant: 'default' as const },
      shipped: { label: 'Shipped', variant: 'default' as const },
      delivered: { label: 'Delivered', variant: 'default' as const },
      cancelled: { label: 'Cancelled', variant: 'destructive' as const }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, variant: 'secondary' as const }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Welcome Section */}
      <Card>
        <CardHeader>
          <CardTitle>Welcome back, {profile?.firstName}!</CardTitle>
          <CardDescription>
            Here's a summary of your account activity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-muted rounded-lg">
              <Package className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">{orders.length}</div>
              <div className="text-sm text-muted-foreground">Total Orders</div>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <Heart className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">{wishlistItems.length}</div>
              <div className="text-sm text-muted-foreground">Wishlist Items</div>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <MapPin className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">{addresses.length}</div>
              <div className="text-sm text-muted-foreground">Saved Addresses</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Orders */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Recent Orders</CardTitle>
              <CardDescription>Your latest order activity</CardDescription>
            </div>
            <Button variant="outline" asChild>
              <Link href="#" onClick={() => setActiveTab('orders')}>
                View All Orders
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {orders.slice(0, 3).map((order) => (
            <div key={order.id} className="flex justify-between items-center py-3 border-b last:border-b-0">
              <div>
                <div className="font-medium">#{order.orderNumber}</div>
                <div className="text-sm text-muted-foreground">
                  {new Date(order.createdAt).toLocaleDateString()} • {order.items.length} items
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium">R{order.total.toFixed(2)}</div>
                <div>{getStatusBadge(order.status)}</div>
              </div>
            </div>
          ))}
          {orders.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No orders yet. <Link href="/products" className="text-primary hover:underline">Start shopping</Link>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )

  const renderOrders = () => (
    <Card>
      <CardHeader>
        <CardTitle>Order History</CardTitle>
        <CardDescription>View and track all your orders</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {orders.map((order) => (
            <Card key={order.id}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="font-semibold">Order #{order.orderNumber}</h3>
                    <p className="text-sm text-muted-foreground">
                      Placed on {new Date(order.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">R{order.total.toFixed(2)}</div>
                    <div className="mt-1">{getStatusBadge(order.status)}</div>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  {order.items.map((item, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span>{item.name} × {item.quantity}</span>
                      <span>R{(item.price * item.quantity).toFixed(2)}</span>
                    </div>
                  ))}
                </div>

                {order.trackingNumber && (
                  <div className="mb-4 p-3 bg-muted rounded-lg">
                    <div className="text-sm font-medium">Tracking Number</div>
                    <div className="text-sm text-muted-foreground">{order.trackingNumber}</div>
                  </div>
                )}

                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Download Invoice
                  </Button>
                  {order.trackingNumber && (
                    <Button variant="outline" size="sm">
                      Track Package
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
          {orders.length === 0 && (
            <div className="text-center py-8">
              <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">No orders yet</h3>
              <p className="text-muted-foreground mb-4">Start shopping to see your orders here</p>
              <Button asChild>
                <Link href="/products">Browse Products</Link>
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )

  const renderProfile = () => (
    <Card>
      <CardHeader>
        <CardTitle>Profile Information</CardTitle>
        <CardDescription>Manage your personal information</CardDescription>
      </CardHeader>
      <CardContent>
        <form className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                value={profile?.firstName || ''}
                onChange={(e) => setProfile(prev => prev ? { ...prev, firstName: e.target.value } : null)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                value={profile?.lastName || ''}
                onChange={(e) => setProfile(prev => prev ? { ...prev, lastName: e.target.value } : null)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              value={profile?.email || ''}
              onChange={(e) => setProfile(prev => prev ? { ...prev, email: e.target.value } : null)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              type="tel"
              value={profile?.phone || ''}
              onChange={(e) => setProfile(prev => prev ? { ...prev, phone: e.target.value } : null)}
            />
          </div>

          <Separator />

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Notification Preferences</h3>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Email Notifications</div>
                  <div className="text-sm text-muted-foreground">Receive order updates via email</div>
                </div>
                <input
                  type="checkbox"
                  checked={profile?.preferences.emailNotifications || false}
                  onChange={(e) => setProfile(prev => prev ? {
                    ...prev,
                    preferences: { ...prev.preferences, emailNotifications: e.target.checked }
                  } : null)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Newsletter</div>
                  <div className="text-sm text-muted-foreground">Receive promotional emails and updates</div>
                </div>
                <input
                  type="checkbox"
                  checked={profile?.preferences.newsletter || false}
                  onChange={(e) => setProfile(prev => prev ? {
                    ...prev,
                    preferences: { ...prev.preferences, newsletter: e.target.checked }
                  } : null)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">SMS Notifications</div>
                  <div className="text-sm text-muted-foreground">Receive order updates via SMS</div>
                </div>
                <input
                  type="checkbox"
                  checked={profile?.preferences.smsNotifications || false}
                  onChange={(e) => setProfile(prev => prev ? {
                    ...prev,
                    preferences: { ...prev.preferences, smsNotifications: e.target.checked }
                  } : null)}
                />
              </div>
            </div>
          </div>

          <Button onClick={() => profile && updateProfile(profile)} className="w-full">
            Save Changes
          </Button>
        </form>
      </CardContent>
    </Card>
  )

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading account information...</div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">My Account</h1>
        <p className="text-muted-foreground">Manage your account settings and view your order history</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <User className="h-4 w-4" />
            <span>Overview</span>
          </TabsTrigger>
          <TabsTrigger value="orders" className="flex items-center space-x-2">
            <Package className="h-4 w-4" />
            <span>Orders</span>
          </TabsTrigger>
          <TabsTrigger value="profile" className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span>Profile</span>
          </TabsTrigger>
          <TabsTrigger value="addresses" className="flex items-center space-x-2">
            <MapPin className="h-4 w-4" />
            <span>Addresses</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          {renderOverview()}
        </TabsContent>

        <TabsContent value="orders">
          {renderOrders()}
        </TabsContent>

        <TabsContent value="profile">
          {renderProfile()}
        </TabsContent>

        <TabsContent value="addresses">
          <Card>
            <CardHeader>
              <CardTitle>Saved Addresses</CardTitle>
              <CardDescription>Manage your shipping and billing addresses</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Address management coming soon
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
