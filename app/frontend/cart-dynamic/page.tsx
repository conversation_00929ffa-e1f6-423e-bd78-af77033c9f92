import { <PERSON><PERSON>enderer } from '@/lib/page-builder/components/page-renderer'
import { PageData } from '@/lib/page-builder/types'

// This demonstrates how the hardcoded cart page can be converted to use the page builder
// The page will render exactly the same as the hardcoded version but using the page builder system

export default function CartDynamicPage() {
  // Create a page configuration that uses the Cart Block
  const pageData: PageData = {
    id: 'cart-dynamic',
    title: 'Shopping Cart',
    slug: 'cart-dynamic',
    description: 'Your shopping cart',
    status: 'published',
    type: 'custom',
    blocks: [
      {
        id: 'cart-1',
        type: 'cart',
        position: 0,
        isVisible: true,
        configuration: {},
        content: {},
        styling: {},
        responsive: {
          desktop: { isVisible: true },
          tablet: { isVisible: true },
          mobile: { isVisible: true }
        }
      }
    ],
    settings: {
      title: 'Shopping Cart',
      description: 'Your shopping cart',
      seoTitle: 'Shopping Cart - Coco Milk Kids',
      seoDescription: 'Review and manage items in your shopping cart.',
      requiresAuth: false,
      allowComments: false
    }
  }

  return (
    <div>
      {/* This will render exactly like the hardcoded cart page */}
      <PageRenderer page={pageData} />
    </div>
  )
}
