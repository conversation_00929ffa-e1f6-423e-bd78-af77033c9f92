import { <PERSON><PERSON>enderer } from '@/lib/page-builder/components/page-renderer'
import { PageData } from '@/lib/page-builder/types'

// This demonstrates how the hardcoded landing page can be converted to use the page builder
// The page will render exactly the same as the hardcoded version but using the page builder system

export default function LandingDynamicPage() {
  // Create a page configuration that replicates the hardcoded landing page
  const pageData: PageData = {
    id: 'landing-dynamic',
    title: 'Coco Milk Kids - Premium Children\'s Clothing',
    slug: 'landing-dynamic',
    description: 'Discover our premium collection of children\'s clothing',
    status: 'published',
    type: 'custom',
    blocks: [
      // Hero Section - exactly like hardcoded
      {
        id: 'hero-1',
        type: 'hero-section',
        position: 0,
        isVisible: true,
        configuration: {
          title: 'KIDS COLLECTION',
          backgroundImage: '/assets/images/cocomilk_kids-20210912_114630-3065525289.jpg',
          overlay: { enabled: true, color: '#000000', opacity: 0.2 },
          ctaButton: { text: 'SHOP NOW', url: '/products', style: 'minimal' },
          contentPosition: 'bottom-left',
          height: 'viewport',
          textColor: '#ffffff',
          animation: { enabled: true, type: 'fade', duration: 1, delay: 0 },
          style: 'zara'
        },
        content: {},
        styling: {},
        responsive: {
          desktop: { isVisible: true },
          tablet: { isVisible: true },
          mobile: { isVisible: true }
        }
      },

      // Featured Categories - exactly like hardcoded
      {
        id: 'featured-categories-1',
        type: 'featured-categories',
        position: 1,
        isVisible: true,
        configuration: {
          title: 'Shop by Category',
          subtitle: 'Discover our curated collections',
          layout: 'grid',
          columns: { desktop: 4, tablet: 2, mobile: 2 },
          categories: [
            {
              id: 'girls',
              name: 'Girls Collection',
              description: 'Stylish & Comfortable',
              image: '/assets/images/cocomilk_kids-20220819_100135-2187605151.jpg',
              link: '/products?category=girls',
              textPosition: 'bottom-left',
              size: 'small',
              overlay: { enabled: true, color: '#000000', opacity: 20 }
            },
            {
              id: 'boys',
              name: 'Boys Collection',
              description: 'Adventure Ready',
              image: '/assets/images/cocomilk_kids-20220822_112525-1393039322.jpg',
              link: '/products?category=boys',
              textPosition: 'bottom-left',
              size: 'small',
              overlay: { enabled: true, color: '#000000', opacity: 20 }
            },
            {
              id: 'new-arrivals',
              name: 'New Arrivals',
              description: 'Latest Trends',
              image: '/assets/images/cocomilk_kids-20221028_102959-387306553.jpg',
              link: '/products?category=new-arrivals',
              textPosition: 'bottom-left',
              size: 'small',
              overlay: { enabled: true, color: '#000000', opacity: 20 }
            },
            {
              id: 'sale',
              name: 'Sale',
              description: 'Up to 50% Off',
              image: '/assets/images/cocomilk_kids-20220912_082247-3005247592.jpg',
              link: '/products?category=sale',
              textPosition: 'bottom-left',
              size: 'small',
              overlay: { enabled: true, color: '#000000', opacity: 20 }
            }
          ]
        },
        content: {},
        styling: {},
        responsive: {
          desktop: { isVisible: true },
          tablet: { isVisible: true },
          mobile: { isVisible: true }
        }
      },

      // New Arrivals Section - exactly like hardcoded
      {
        id: 'new-arrivals-1',
        type: 'new-arrivals',
        position: 2,
        isVisible: true,
        configuration: {
          title: 'NEW IN',
          limit: 6,
          showViewAllLink: true,
          viewAllText: 'VIEW ALL NEW IN',
          viewAllUrl: '/collections/new-arrivals',
          layout: 'grid',
          columns: { desktop: 3, tablet: 2, mobile: 2 },
          spacing: 'normal',
          style: 'zara'
        },
        content: {},
        styling: {},
        responsive: {
          desktop: { isVisible: true },
          tablet: { isVisible: true },
          mobile: { isVisible: true }
        }
      },

      // Editorial Section - exactly like hardcoded
      {
        id: 'editorial-1',
        type: 'editorial-section',
        position: 3,
        isVisible: true,
        configuration: {
          items: [
            {
              id: 'spring-essentials',
              title: 'SPRING ESSENTIALS',
              description: 'Discover the season\'s must-have pieces',
              image: '/assets/images/cocomilk_kids-20220829_111232-3780725228.jpg',
              link: '/collections/editorial',
              size: 'large'
            },
            {
              id: 'accessories',
              title: 'ACCESSORIES',
              description: 'Complete the look',
              image: '/assets/images/cocomilk_kids-20220824_102244-4040552385.jpg',
              link: '/collections/accessories',
              size: 'small'
            },
            {
              id: 'shoes',
              title: 'SHOES',
              description: 'Step into style',
              image: '/assets/images/cocomilk_kids-20220901_072641-574408837.jpg',
              link: '/collections/shoes',
              size: 'small'
            }
          ],
          layout: 'asymmetric',
          style: 'zara',
          spacing: 'normal',
          animation: { enabled: true, type: 'slide', stagger: 0.1 },
          backgroundColor: '#ffffff'
        },
        content: {},
        styling: {},
        responsive: {
          desktop: { isVisible: true },
          tablet: { isVisible: true },
          mobile: { isVisible: true }
        }
      },

      // Special Offers Banner - exactly like hardcoded
      {
        id: 'special-offers-1',
        type: 'special-offers-banner',
        position: 4,
        isVisible: true,
        configuration: {
          title: 'SPECIAL OFFER',
          subtitle: 'Limited Time Only',
          description: 'Get 20% off on all new arrivals. Use code NEWKIDS20',
          ctaButton: { text: 'SHOP NOW', url: '/products?category=new-arrivals', style: 'primary' },
          backgroundImage: '/assets/images/cocomilk_kids-20220912_082247-3005247592.jpg',
          overlay: { enabled: true, color: '#000000', opacity: 0.4 },
          textColor: '#ffffff',
          alignment: 'center',
          style: 'modern'
        },
        content: {},
        styling: {},
        responsive: {
          desktop: { isVisible: true },
          tablet: { isVisible: true },
          mobile: { isVisible: true }
        }
      },

      // Newsletter Signup - exactly like hardcoded
      {
        id: 'newsletter-1',
        type: 'newsletter-signup',
        position: 5,
        isVisible: true,
        configuration: {
          title: 'Stay Updated',
          description: 'Subscribe to our newsletter for the latest updates and exclusive offers.',
          placeholder: 'Email address',
          buttonText: 'SUBSCRIBE',
          successMessage: 'Thank you for subscribing',
          style: 'zara',
          layout: 'vertical',
          backgroundColor: 'transparent',
          textColor: '#000000',
          showIcon: true,
          animation: { enabled: true, type: 'fade' },
          privacy: {
            enabled: true,
            text: 'By subscribing, you agree to our',
            linkText: 'Privacy Policy',
            linkUrl: '/privacy-policy'
          }
        },
        content: {},
        styling: {},
        responsive: {
          desktop: { isVisible: true },
          tablet: { isVisible: true },
          mobile: { isVisible: true }
        }
      }
    ],
    settings: {
      title: 'Coco Milk Kids - Premium Children\'s Clothing',
      description: 'Discover our premium collection of children\'s clothing designed for comfort, style, and adventure.',
      seoTitle: 'Coco Milk Kids - Premium Children\'s Clothing & Accessories',
      seoDescription: 'Shop premium children\'s clothing at Coco Milk Kids. Discover our latest collections of stylish and comfortable kids\' fashion.',
      requiresAuth: false,
      allowComments: false
    }
  }

  return (
    <div>
      {/* This will render exactly like the hardcoded landing page */}
      <PageRenderer page={pageData} />
    </div>
  )
}
