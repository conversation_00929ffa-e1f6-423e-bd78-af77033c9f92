import { <PERSON><PERSON>enderer } from '@/lib/page-builder/components/page-renderer'
import { PageData } from '@/lib/page-builder/types'

// This demonstrates how the hardcoded wishlist page can be converted to use the page builder
// The page will render exactly the same as the hardcoded version but using the page builder system

export default function WishlistDynamicPage() {
  // Create a page configuration that uses the Wishlist Block
  const pageData: PageData = {
    id: 'wishlist-dynamic',
    title: 'My Wishlist',
    slug: 'wishlist-dynamic',
    description: 'Your saved products',
    status: 'published',
    type: 'custom',
    blocks: [
      {
        id: 'wishlist-1',
        type: 'wishlist',
        position: 0,
        isVisible: true,
        configuration: {},
        content: {},
        styling: {},
        responsive: {
          desktop: { isVisible: true },
          tablet: { isVisible: true },
          mobile: { isVisible: true }
        }
      }
    ],
    settings: {
      title: 'My Wishlist',
      description: 'Your saved products',
      seoTitle: 'My Wishlist - Coco Milk Kids',
      seoDescription: 'View and manage your saved products.',
      requiresAuth: false,
      allowComments: false
    }
  }

  return (
    <div>
      {/* This will render exactly like the hardcoded wishlist page */}
      <PageRenderer page={pageData} />
    </div>
  )
}
