import { SiteSettingsService } from '@/lib/site-settings/site-settings-service'
import { Wrench, Clock, Mail } from 'lucide-react'

export default async function MaintenancePage() {
  let maintenanceMessage = "We're currently performing maintenance on our website. Please check back soon."
  let siteName = "Coco Milk Kids"

  try {
    const siteSettingsService = new SiteSettingsService()
    const settings = await siteSettingsService.getSiteSettings()
    
    siteName = settings.siteName
    if (settings.maintenance.message) {
      maintenanceMessage = settings.maintenance.message
    }
  } catch (error) {
    console.error('Error loading maintenance settings:', error)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {/* Icon */}
        <div className="flex justify-center mb-6">
          <div className="bg-orange-100 p-4 rounded-full">
            <Wrench className="h-12 w-12 text-orange-600" />
          </div>
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Under Maintenance
        </h1>

        {/* Site Name */}
        <p className="text-lg text-gray-600 mb-6">
          {siteName}
        </p>

        {/* Message */}
        <p className="text-gray-600 mb-8 leading-relaxed">
          {maintenanceMessage}
        </p>

        {/* Status Indicators */}
        <div className="space-y-4 mb-8">
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
            <Clock className="h-4 w-4" />
            <span>Estimated completion: Soon</span>
          </div>
          
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
            <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
            <span>Systems are being updated</span>
          </div>
        </div>

        {/* Contact Information */}
        <div className="border-t pt-6">
          <p className="text-sm text-gray-500 mb-3">
            Need immediate assistance?
          </p>
          <div className="flex items-center justify-center space-x-2 text-sm">
            <Mail className="h-4 w-4 text-gray-400" />
            <a 
              href="mailto:<EMAIL>" 
              className="text-blue-600 hover:text-blue-800 transition-colors"
            >
              <EMAIL>
            </a>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 pt-6 border-t">
          <p className="text-xs text-gray-400">
            Thank you for your patience
          </p>
        </div>
      </div>
    </div>
  )
}
