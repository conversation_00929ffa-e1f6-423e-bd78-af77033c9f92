"use client"

import { useState } from "react"
import { useCart } from "@/hooks/use-cart"
import { CheckoutForm } from "@/components/storefront/checkout/checkout-form"
import { CheckoutSummary } from "@/components/storefront/checkout/checkout-summary"
import { OrderConfirmation } from "@/components/order-confirmation"
import { Button } from "@/components/ui/button"
import { ShoppingBag } from "lucide-react"
import Link from "next/link"

export default function CheckoutPage() {
  const { isEmpty } = useCart()
  const [step, setStep] = useState<"shipping" | "payment">("shipping")
  const [shippingMethod, setShippingMethod] = useState("standard")
  const [orderComplete, setOrderComplete] = useState(false)

  // Generate a random order ID - moved before any conditional returns
  const generateOrderId = () => {
    return Math.floor(Math.random() * 10000000).toString().padStart(7, '0');
  };

  const [orderId, setOrderId] = useState(generateOrderId());

  if (isEmpty) {
    return (
      <div className="container px-4 md:px-6 py-16">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <ShoppingBag className="h-16 w-16 text-muted-foreground" />
          <h1 className="text-2xl font-bold font-montserrat">Your cart is empty</h1>
          <p className="text-muted-foreground">You need to add items to your cart before checking out.</p>
          <Button asChild>
            <Link href="/products">Continue Shopping</Link>
          </Button>
        </div>
      </div>
    )
  }

  const handleCompleteOrder = (orderId: string, paymentUrl?: string) => {
    setOrderComplete(true);
    // Store order ID for confirmation page
    setOrderId(orderId);

    // If there's a payment URL, the checkout form will handle the redirect
    // Otherwise, show the order confirmation page
    if (!paymentUrl) {
      // Order completed without external payment (e.g., cash on collection)
      console.log('Order completed:', orderId);
    }
  };

  // If order is complete, show the order confirmation page
  if (orderComplete) {
    return (
      <OrderConfirmation
        orderId={orderId}
        shippingMethod={shippingMethod}
      />
    );
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      <h1 className="text-2xl md:text-3xl font-bold font-montserrat mb-6">Checkout</h1>
      <div className="grid lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <CheckoutForm
            step={step}
            setStep={setStep}
            shippingMethod={shippingMethod}
            setShippingMethod={setShippingMethod}
            onCompleteOrder={handleCompleteOrder}
          />
        </div>
        <div>
          <CheckoutSummary
            shippingMethod={shippingMethod}
            step={step}
          />
        </div>
      </div>
    </div>
  )
}
