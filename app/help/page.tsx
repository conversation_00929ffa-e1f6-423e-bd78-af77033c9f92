import { CustomerServiceHub } from "@/components/customer-service-hub"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Shield, 
  Truck, 
  RotateCcw, 
  CreditCard, 
  Users, 
  Ruler,
  Gift,
  Star
} from "lucide-react"

export default function HelpPage() {
  const helpTopics = [
    {
      icon: Shield,
      title: "Size Guide",
      description: "Find the perfect fit for your child",
      href: "/size-guide",
      popular: true
    },
    {
      icon: Truck,
      title: "Shipping Info",
      description: "Delivery options and tracking",
      href: "/shipping",
      popular: true
    },
    {
      icon: RotateCcw,
      title: "Returns & Exchanges",
      description: "Easy returns within 30 days",
      href: "/returns",
      popular: true
    },
    {
      icon: CreditCard,
      title: "Payment Options",
      description: "Secure payment methods",
      href: "/payment",
      popular: false
    },
    {
      icon: Users,
      title: "Account Help",
      description: "Manage your account settings",
      href: "/account-help",
      popular: false
    },
    {
      icon: Gift,
      title: "Gift Cards",
      description: "Purchase and redeem gift cards",
      href: "/gift-cards",
      popular: false
    },
    {
      icon: Star,
      title: "Rewards Program",
      description: "Earn points with every purchase",
      href: "/rewards",
      popular: false
    },
    {
      icon: Ruler,
      title: "Care Instructions",
      description: "Keep clothes looking their best",
      href: "/care",
      popular: false
    }
  ]

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold font-montserrat mb-4">How Can We Help You?</h1>
          <p className="text-muted-foreground text-lg">
            Find answers to your questions or get in touch with our support team
          </p>
        </div>

        {/* Quick Help Topics */}
        <div className="mb-12">
          <h2 className="text-xl font-semibold mb-6">Popular Help Topics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {helpTopics.map((topic) => (
              <Card key={topic.title} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-6 text-center">
                  <div className="relative">
                    <topic.icon className="h-8 w-8 mx-auto mb-3 text-primary" />
                    {topic.popular && (
                      <Badge variant="secondary" className="absolute -top-2 -right-2 text-xs">
                        Popular
                      </Badge>
                    )}
                  </div>
                  <h3 className="font-medium mb-2">{topic.title}</h3>
                  <p className="text-sm text-muted-foreground">{topic.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Customer Service Hub */}
        <CustomerServiceHub />

        {/* Additional Resources */}
        <div className="mt-12">
          <h2 className="text-xl font-semibold mb-6">Additional Resources</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Size Guide</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Our comprehensive size guide helps you find the perfect fit for your child.
                </p>
                <a href="/size-guide" className="text-primary hover:underline text-sm font-medium">
                  View Size Guide →
                </a>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Care Instructions</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Learn how to properly care for your Coco Milk Kids clothing.
                </p>
                <a href="/care" className="text-primary hover:underline text-sm font-medium">
                  Care Guide →
                </a>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Store Locator</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Find a Coco Milk Kids store near you for in-person shopping.
                </p>
                <a href="/stores" className="text-primary hover:underline text-sm font-medium">
                  Find Stores →
                </a>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Contact Information */}
        <div className="mt-12 bg-gray-50 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Still Need Help?</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div>
              <h3 className="font-medium mb-2">Phone Support</h3>
              <p className="text-sm text-muted-foreground mb-1">1-800-COCO-KIDS</p>
              <p className="text-xs text-muted-foreground">Monday - Friday, 9AM - 6PM EST</p>
            </div>
            <div>
              <h3 className="font-medium mb-2">Email Support</h3>
              <p className="text-sm text-muted-foreground mb-1"><EMAIL></p>
              <p className="text-xs text-muted-foreground">24-48 hour response time</p>
            </div>
            <div>
              <h3 className="font-medium mb-2">Live Chat</h3>
              <p className="text-sm text-muted-foreground mb-1">Available on our website</p>
              <p className="text-xs text-muted-foreground">Monday - Friday, 9AM - 6PM EST</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
