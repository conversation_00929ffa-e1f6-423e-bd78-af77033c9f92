import { openai } from "@ai-sdk/openai"
import { createAI, getMutableAIState } from "ai/rsc"
import { getProducts, getProduct } from "@/lib/products"

// Define our AI models
export const models = {
  gpt4: openai("gpt-4o"),
  gpt3: openai("gpt-3.5-turbo"),
}

// System prompts for different AI features
export const systemPrompts = {
  stylist: `You are an expert kids fashion stylist for Coco Milk Kids, powered by SoImagine AI.
  Your goal is to suggest outfit combinations based on the user's preferences and needs.
  Focus on age-appropriate, comfortable, and stylish outfits for children.
  Always recommend items from our collection categories: tops, bottoms, dresses, outerwear, and accessories.
  Keep suggestions concise and friendly.`,

  sizeAssistant: `You are a size recommendation assistant for Coco Milk Kids clothing store, powered by SoImagine AI.
  Help parents find the right size for their children based on age, height, weight, and body type.
  Be precise and helpful, explaining why you recommend a particular size.
  Our size ranges are: XS (2-3 years), S (4-5 years), M (6-7 years), L (8-9 years), XL (10-12 years).`,

  searchEnhancer: `You are a search enhancement tool for Coco Milk Kids, powered by SoImagine AI.
  Your job is to understand natural language search queries and convert them to structured search parameters.
  Extract key information like product type, color, size, style, occasion, season, and price range.
  Return only the structured data without explanations.`,

  shoppingAssistant: `You are a helpful shopping assistant for Coco Milk Kids, a premium children's clothing store, powered by SoImagine AI.
  Help customers with product information, sizing, materials, care instructions, and shopping recommendations.
  Be friendly, concise, and helpful. If you don't know specific product details, suggest general guidance
  and recommend contacting customer service for more specific information.
  Our brand focuses on comfort, quality, and style for children aged 2-12.`,
}

// Define AI tools
export const tools = {
  // Tool to get all products
  getAllProducts: {
    description: "Get all products from the store",
    parameters: {},
    execute: async () => {
      const products = await getProducts();
      return products;
    }
  },

  // Tool to get a specific product by slug
  getProductBySlug: {
    description: "Get a specific product by its slug",
    parameters: {
      slug: {
        type: "string",
        description: "The slug of the product to retrieve"
      }
    },
    execute: async ({ slug }: { slug: string }) => {
      const product = await getProduct(slug);
      return product;
    }
  },

  // Tool to get products by category
  getProductsByCategory: {
    description: "Get products filtered by category",
    parameters: {
      category: {
        type: "string",
        description: "The category to filter by (e.g., tops, bottoms, dresses, outerwear, accessories)"
      }
    },
    execute: async ({ category }: { category: string }) => {
      const products = await getProducts({ category });
      return products;
    }
  },

  // Tool to recommend products based on age and preferences
  recommendProducts: {
    description: "Recommend products based on age and preferences",
    parameters: {
      age: {
        type: "string",
        description: "The age of the child"
      },
      gender: {
        type: "string",
        description: "The gender of the child (Boy, Girl, or Any)"
      },
      preferences: {
        type: "string",
        description: "Any specific preferences (e.g., colors, styles, occasions)"
      }
    },
    execute: async ({ age, gender, preferences }: { age: string, gender: string, preferences: string }) => {
      // This would typically call a recommendation engine
      // For now, we'll just return all products and let the AI filter them
      const allProducts = await getProducts();
      return {
        products: allProducts,
        message: `Here are some recommendations for a ${age} year old ${gender.toLowerCase()} with preferences: ${preferences}`
      };
    }
  },

  // Tool to get size information
  getSizeInformation: {
    description: "Get size information for children's clothing",
    parameters: {},
    execute: async () => {
      return {
        sizes: [
          { size: "XS", ageRange: "2-3 years", heightRange: "85-95 cm", weightRange: "12-15 kg" },
          { size: "S", ageRange: "4-5 years", heightRange: "95-110 cm", weightRange: "15-19 kg" },
          { size: "M", ageRange: "6-7 years", heightRange: "110-125 cm", weightRange: "19-25 kg" },
          { size: "L", ageRange: "8-9 years", heightRange: "125-140 cm", weightRange: "25-35 kg" },
          { size: "XL", ageRange: "10-12 years", heightRange: "140-155 cm", weightRange: "35-45 kg" },
        ]
      }
    }
  },

  // Tool to get materials and care instructions
  getMaterialsAndCare: {
    description: "Get information about materials and care instructions",
    parameters: {},
    execute: async () => {
      return {
        materials: [
          { type: "Cotton", description: "100% organic cotton, soft and breathable, OEKO-TEX certified" },
          { type: "Denim", description: "98% cotton, 2% elastane for comfort and flexibility" },
          { type: "Knitwear", description: "Soft acrylic and cotton blend, gentle on sensitive skin" },
        ],
        careInstructions: [
          { category: "Cotton Items", instructions: "Machine wash at 30°C (86°F), tumble dry low, iron on medium heat if needed" },
          { category: "Denim", instructions: "Machine wash cold, inside out. Air dry to prevent shrinking. Iron on low if needed." },
          { category: "Knitwear", instructions: "Hand wash in cold water, lay flat to dry, do not iron" },
        ]
      }
    }
  }
}

// Define the AI state interface
export interface AIState {
  messages: {
    role: "user" | "assistant" | "system" | "function";
    content: string;
    id?: string;
    name?: string;
  }[];
  productRecommendations: any[];
  currentQuery: string;
  searchResults: any[];
}

// Initialize the AI state
export const initialAIState: AIState = {
  messages: [],
  productRecommendations: [],
  currentQuery: "",
  searchResults: []
}

// Create a function to get the mutable AI state
export function getAIState() {
  // Use type assertion to work around the constraint issue
  return getMutableAIState() as any;
}

// Create the AI instance
export const AI = createAI({
  actions: {
    // Get product recommendations
    getRecommendations: async (query: string) => {
      const aiState = getAIState();

      // Update the AI state with the query
      aiState.update((draft: AIState) => {
        draft.currentQuery = query;
        draft.messages.push({
          role: "user",
          content: query
        });
      });

      // Get all products
      const products = await getProducts();

      // Let the AI respond with a simple text completion
      const responseText = `Here are some outfit recommendations based on your query: "${query}"

      1. Casual Everyday Look: Striped cotton t-shirt with comfortable denim jeans and a light cardigan
      2. Special Occasion: Floral print dress with a matching hair accessory and comfortable ballet flats
      3. Playtime: Graphic t-shirt with elastic waist shorts and slip-on sneakers
      4. Seasonal: Light cotton hoodie with jogger pants and canvas shoes

      All these items are available in our collection and designed for comfort and style!`;

      // Update the AI state with the response
      aiState.update((draft: AIState) => {
        draft.messages.push({
          role: "assistant",
          content: responseText
        });
        draft.productRecommendations = products.slice(0, 4); // Just show first 4 products for demo
      });

      // Return the response
      return {
        text: responseText,
        products: products.slice(0, 4)
      };
    },

    // Get size recommendations
    getSizeRecommendation: async (childInfo: { age: string, height: string, weight: string, bodyType: string }) => {
      const aiState = getAIState();

      // Create the prompt
      const prompt = `Based on the following information about a child, recommend the best size from our collection:
      Age: ${childInfo.age}
      Height: ${childInfo.height} cm
      Weight: ${childInfo.weight} kg
      Body Type: ${childInfo.bodyType || "Average"}

      Please provide a size recommendation (XS, S, M, L, XL) with a brief explanation.`;

      // Update the AI state
      aiState.update((draft: AIState) => {
        draft.messages.push({
          role: "user",
          content: prompt
        });
      });

      // Get the size information
      const sizeInfo = await tools.getSizeInformation.execute();

      // Generate a size recommendation response
      const responseText = `Based on the information provided:

      Age: ${childInfo.age}
      Height: ${childInfo.height} cm
      Weight: ${childInfo.weight} kg
      Body Type: ${childInfo.bodyType || "Average"}

      I recommend size ${parseInt(childInfo.age) > 8 ? "L" : parseInt(childInfo.age) > 6 ? "M" : parseInt(childInfo.age) > 4 ? "S" : "XS"} for your child.

      This size should provide a comfortable fit with some room to grow. If your child is between sizes or has a larger/smaller build than average, you might want to size up/down accordingly.`;

      // Update the AI state with the response
      aiState.update((draft: AIState) => {
        draft.messages.push({
          role: "assistant",
          content: responseText
        });
      });

      // Return the response
      return {
        recommendation: responseText,
        sizes: sizeInfo.sizes
      };
    },

    // Search products
    searchProducts: async (query: string) => {
      const aiState = getAIState();

      // Update the AI state
      aiState.update((draft: AIState) => {
        draft.currentQuery = query;
        draft.messages.push({
          role: "user",
          content: `Search for: ${query}`
        });
      });

      // Get all products
      const products = await getProducts();

      // Generate an enhanced search query
      const enhancedQuery = `Enhanced search for: ${query}
      Type: ${query.toLowerCase().includes('dress') ? 'dress' : query.toLowerCase().includes('shirt') ? 'top' : 'all'}
      Color: ${query.toLowerCase().includes('blue') ? 'blue' : query.toLowerCase().includes('red') ? 'red' : 'any'}
      Size: ${query.toLowerCase().includes('small') ? 'S' : query.toLowerCase().includes('large') ? 'L' : 'any'}`;

      // For demo purposes, just return some products
      // In a real app, you would parse the AI response and filter products accordingly
      const searchResults = products.slice(0, 6);

      // Update the AI state
      aiState.update((draft: AIState) => {
        draft.searchResults = searchResults;
      });

      // Return the search results
      return {
        results: searchResults,
        enhancedQuery: enhancedQuery
      };
    }
  },
  initialAIState: initialAIState
})
