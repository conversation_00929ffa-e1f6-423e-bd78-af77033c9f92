import { NextRequest } from 'next/server'
import { RateLimiterMemory } from 'rate-limiter-flexible'

interface RateLimitOptions {
  interval: number // Time window in milliseconds
  uniqueTokenPerInterval: number // Max unique tokens per interval
}

interface RateLimiter {
  check: (request: NextRequest, limit: number, token?: string) => Promise<void>
}

// In-memory rate limiter for development
// In production, you should use Redis or another persistent store
const rateLimiters = new Map<string, RateLimiterMemory>()

export function rateLimit(options: RateLimitOptions): RateLimiter {
  return {
    check: async (request: NextRequest, limit: number, token?: string) => {
      const identifier = token || getClientIdentifier(request)
      const key = `${identifier}_${options.interval}`
      
      let limiter = rateLimiters.get(key)
      
      if (!limiter) {
        limiter = new RateLimiterMemory({
          keyGenerator: () => identifier,
          points: limit,
          duration: Math.ceil(options.interval / 1000), // Convert to seconds
        })
        rateLimiters.set(key, limiter)
      }

      try {
        await limiter.consume(identifier)
      } catch (rejRes) {
        throw new Error('Rate limit exceeded')
      }
    },
  }
}

function getClientIdentifier(request: NextRequest): string {
  // Try to get IP from various headers
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const cfConnectingIp = request.headers.get('cf-connecting-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIp) {
    return realIp
  }
  
  if (cfConnectingIp) {
    return cfConnectingIp
  }
  
  // Fallback to a default identifier
  return 'unknown'
}
