// Inventory Management Workflow Templates
// Predefined workflows for inventory operations

import { WorkflowTemplate, WorkflowDefinition } from '../types'
import { generateId } from '../../page-builder/utils'

export const inventoryWorkflowTemplates: WorkflowTemplate[] = [
  {
    id: 'low-stock-alert',
    name: 'Low Stock Alert Workflow',
    description: 'Automated alerts and reordering for low stock items',
    category: 'inventory',
    icon: 'AlertTriangle',
    isBuiltIn: true,
    tags: ['stock', 'alert', 'reorder'],
    definition: {
      name: 'Low Stock Alert Workflow',
      description: 'Automated workflow for handling low stock situations',
      version: '1.0.0',
      category: 'inventory',
      trigger: {
        type: 'event',
        event: 'inventory.low_stock',
        conditions: [
          {
            field: 'product.currentStock',
            operator: 'less_than',
            value: '{{product.reorderPoint}}'
          }
        ],
        config: {}
      },
      steps: [
        {
          id: 'check-reorder-settings',
          name: 'Check Auto-Reorder Settings',
          description: 'Verify if product has auto-reorder enabled',
          type: 'condition',
          status: 'pending',
          config: {
            conditions: [
              {
                field: 'product.autoReorder',
                operator: 'equals',
                value: true
              }
            ]
          }
        },
        {
          id: 'calculate-reorder-quantity',
          name: 'Calculate Reorder Quantity',
          description: 'Determine optimal reorder quantity',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'api_call',
            endpoint: '/api/inventory/calculate-reorder',
            method: 'POST',
            data: {
              productId: '{{product.id}}',
              currentStock: '{{product.currentStock}}',
              reorderPoint: '{{product.reorderPoint}}',
              leadTime: '{{supplier.leadTime}}',
              salesVelocity: '{{product.salesVelocity}}'
            }
          },
          dependencies: ['check-reorder-settings']
        },
        {
          id: 'create-purchase-order',
          name: 'Create Purchase Order',
          description: 'Generate purchase order for supplier',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'api_call',
            endpoint: '/api/purchasing/create-order',
            method: 'POST',
            data: {
              supplierId: '{{product.supplierId}}',
              items: [{
                productId: '{{product.id}}',
                quantity: '{{reorder.quantity}}',
                unitCost: '{{product.costPrice}}'
              }],
              priority: 'normal',
              requestedDeliveryDate: '{{reorder.requestedDate}}'
            }
          },
          dependencies: ['calculate-reorder-quantity']
        },
        {
          id: 'send-supplier-notification',
          name: 'Send Supplier Notification',
          description: 'Notify supplier of new purchase order',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'purchase-order-notification',
            recipient: '{{supplier.email}}',
            data: {
              supplierName: '{{supplier.name}}',
              purchaseOrderNumber: '{{purchaseOrder.number}}',
              items: '{{purchaseOrder.items}}',
              totalAmount: '{{purchaseOrder.total}}',
              requestedDeliveryDate: '{{purchaseOrder.requestedDeliveryDate}}'
            }
          },
          dependencies: ['create-purchase-order']
        },
        {
          id: 'notify-inventory-team',
          name: 'Notify Inventory Team',
          description: 'Alert inventory team of low stock and reorder',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'low-stock-alert',
            recipient: '<EMAIL>',
            data: {
              productName: '{{product.name}}',
              sku: '{{product.sku}}',
              currentStock: '{{product.currentStock}}',
              reorderPoint: '{{product.reorderPoint}}',
              reorderQuantity: '{{reorder.quantity}}',
              purchaseOrderNumber: '{{purchaseOrder.number}}'
            }
          },
          dependencies: ['send-supplier-notification']
        },
        {
          id: 'update-product-status',
          name: 'Update Product Status',
          description: 'Mark product as pending restock',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'update_inventory',
            productId: '{{product.id}}',
            status: 'pending_restock',
            lastReorderDate: '{{now}}',
            pendingQuantity: '{{reorder.quantity}}'
          },
          dependencies: ['notify-inventory-team']
        }
      ],
      isActive: true,
      tags: ['stock', 'alert', 'reorder'],
      metadata: {
        automationLevel: 'high',
        estimatedLeadTime: '7-14 days',
        costSavings: 'Prevents stockouts and lost sales'
      }
    }
  },

  {
    id: 'stock-received-workflow',
    name: 'Stock Received Workflow',
    description: 'Process incoming inventory and update stock levels',
    category: 'inventory',
    icon: 'Package',
    isBuiltIn: true,
    tags: ['receiving', 'stock', 'quality'],
    definition: {
      name: 'Stock Received Workflow',
      description: 'Automated workflow for processing received inventory',
      version: '1.0.0',
      category: 'inventory',
      trigger: {
        type: 'event',
        event: 'inventory.stock_received',
        config: {}
      },
      steps: [
        {
          id: 'create-receiving-task',
          name: 'Create Receiving Task',
          description: 'Create task for warehouse team to process shipment',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'create_task',
            taskType: 'inventory_receiving',
            assignTo: 'warehouse_team',
            priority: 'high',
            data: {
              shipmentId: '{{shipment.id}}',
              purchaseOrderNumber: '{{purchaseOrder.number}}',
              expectedItems: '{{shipment.items}}',
              supplier: '{{supplier.name}}'
            }
          }
        },
        {
          id: 'quality-inspection',
          name: 'Quality Inspection',
          description: 'Inspect received items for quality and damage',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'create_task',
            taskType: 'quality_inspection',
            assignTo: 'quality_team',
            data: {
              shipmentId: '{{shipment.id}}',
              items: '{{shipment.items}}',
              inspectionCriteria: '{{product.qualityStandards}}'
            }
          },
          dependencies: ['create-receiving-task']
        },
        {
          id: 'update-inventory-levels',
          name: 'Update Inventory Levels',
          description: 'Add received items to available inventory',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'update_inventory',
            operation: 'add_stock',
            items: '{{inspection.approvedItems}}',
            location: '{{warehouse.defaultLocation}}'
          },
          dependencies: ['quality-inspection']
        },
        {
          id: 'handle-damaged-items',
          name: 'Handle Damaged Items',
          description: 'Process any damaged or rejected items',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'create_task',
            taskType: 'damage_claim',
            assignTo: 'purchasing_team',
            data: {
              damagedItems: '{{inspection.damagedItems}}',
              supplier: '{{supplier.name}}',
              claimAmount: '{{damage.totalValue}}'
            }
          },
          dependencies: ['update-inventory-levels']
        },
        {
          id: 'update-purchase-order',
          name: 'Update Purchase Order Status',
          description: 'Mark purchase order as received',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'api_call',
            endpoint: '/api/purchasing/update-order',
            method: 'PUT',
            data: {
              purchaseOrderId: '{{purchaseOrder.id}}',
              status: 'received',
              receivedDate: '{{now}}',
              receivedQuantity: '{{inspection.approvedQuantity}}'
            }
          },
          dependencies: ['handle-damaged-items']
        },
        {
          id: 'notify-stakeholders',
          name: 'Notify Stakeholders',
          description: 'Inform relevant teams of stock receipt',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'stock-received-notification',
            recipient: '<EMAIL>,<EMAIL>',
            data: {
              purchaseOrderNumber: '{{purchaseOrder.number}}',
              supplier: '{{supplier.name}}',
              receivedItems: '{{inspection.approvedItems}}',
              damagedItems: '{{inspection.damagedItems}}',
              totalValue: '{{shipment.totalValue}}'
            }
          },
          dependencies: ['update-purchase-order']
        },
        {
          id: 'check-backorders',
          name: 'Check for Backorders',
          description: 'Check if any backorders can now be fulfilled',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'api_call',
            endpoint: '/api/orders/check-backorders',
            method: 'POST',
            data: {
              productIds: '{{shipment.productIds}}',
              availableQuantities: '{{inventory.newQuantities}}'
            }
          },
          dependencies: ['notify-stakeholders']
        }
      ],
      isActive: true,
      tags: ['receiving', 'stock', 'quality'],
      metadata: {
        estimatedDuration: '2-4 hours',
        requiresManualSteps: true,
        qualityCheckRequired: true
      }
    }
  },

  {
    id: 'inventory-audit-workflow',
    name: 'Inventory Audit Workflow',
    description: 'Scheduled inventory audits and discrepancy resolution',
    category: 'inventory',
    icon: 'ClipboardCheck',
    isBuiltIn: true,
    tags: ['audit', 'count', 'accuracy'],
    definition: {
      name: 'Inventory Audit Workflow',
      description: 'Automated workflow for conducting inventory audits',
      version: '1.0.0',
      category: 'inventory',
      trigger: {
        type: 'schedule',
        schedule: '0 2 1 * *', // First day of every month at 2 AM
        config: {}
      },
      steps: [
        {
          id: 'generate-audit-list',
          name: 'Generate Audit List',
          description: 'Create list of items to audit based on criteria',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'api_call',
            endpoint: '/api/inventory/generate-audit-list',
            method: 'POST',
            data: {
              criteria: {
                highValue: true,
                fastMoving: true,
                lastAuditDate: '6 months ago',
                discrepancyHistory: true
              },
              sampleSize: 100
            }
          }
        },
        {
          id: 'create-audit-tasks',
          name: 'Create Audit Tasks',
          description: 'Assign audit tasks to warehouse team',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'create_task',
            taskType: 'inventory_audit',
            assignTo: 'warehouse_team',
            data: {
              auditId: '{{audit.id}}',
              itemsToCount: '{{audit.items}}',
              deadline: '{{audit.deadline}}',
              instructions: '{{audit.instructions}}'
            }
          },
          dependencies: ['generate-audit-list']
        },
        {
          id: 'wait-for-counts',
          name: 'Wait for Physical Counts',
          description: 'Wait for warehouse team to complete counts',
          type: 'condition',
          status: 'pending',
          config: {
            conditions: [
              {
                field: 'audit.status',
                operator: 'equals',
                value: 'counts_completed'
              }
            ],
            timeout: 604800000 // 7 days
          },
          dependencies: ['create-audit-tasks']
        },
        {
          id: 'analyze-discrepancies',
          name: 'Analyze Discrepancies',
          description: 'Compare physical counts with system records',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'api_call',
            endpoint: '/api/inventory/analyze-discrepancies',
            method: 'POST',
            data: {
              auditId: '{{audit.id}}',
              physicalCounts: '{{audit.physicalCounts}}',
              systemCounts: '{{audit.systemCounts}}'
            }
          },
          dependencies: ['wait-for-counts']
        },
        {
          id: 'investigate-major-discrepancies',
          name: 'Investigate Major Discrepancies',
          description: 'Create investigation tasks for significant variances',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'create_task',
            taskType: 'discrepancy_investigation',
            assignTo: 'inventory_manager',
            data: {
              discrepancies: '{{audit.majorDiscrepancies}}',
              threshold: '{{audit.investigationThreshold}}',
              priority: 'high'
            }
          },
          dependencies: ['analyze-discrepancies']
        },
        {
          id: 'adjust-inventory',
          name: 'Adjust Inventory Records',
          description: 'Update system records based on physical counts',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'update_inventory',
            operation: 'audit_adjustment',
            adjustments: '{{audit.approvedAdjustments}}',
            reason: 'inventory_audit',
            auditId: '{{audit.id}}'
          },
          dependencies: ['investigate-major-discrepancies']
        },
        {
          id: 'generate-audit-report',
          name: 'Generate Audit Report',
          description: 'Create comprehensive audit report',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'api_call',
            endpoint: '/api/reports/generate-audit-report',
            method: 'POST',
            data: {
              auditId: '{{audit.id}}',
              includeDiscrepancies: true,
              includeAdjustments: true,
              includeRecommendations: true
            }
          },
          dependencies: ['adjust-inventory']
        },
        {
          id: 'send-audit-summary',
          name: 'Send Audit Summary',
          description: 'Email audit summary to management',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'inventory-audit-summary',
            recipient: '<EMAIL>,<EMAIL>',
            data: {
              auditDate: '{{audit.completedDate}}',
              itemsAudited: '{{audit.itemCount}}',
              accuracyRate: '{{audit.accuracyRate}}',
              totalAdjustments: '{{audit.totalAdjustments}}',
              reportUrl: '{{audit.reportUrl}}'
            }
          },
          dependencies: ['generate-audit-report']
        }
      ],
      isActive: true,
      tags: ['audit', 'count', 'accuracy'],
      metadata: {
        frequency: 'monthly',
        estimatedDuration: '7 days',
        accuracyTarget: '98%'
      }
    }
  }
]

export function getInventoryWorkflowTemplate(templateId: string): WorkflowTemplate | undefined {
  return inventoryWorkflowTemplates.find(template => template.id === templateId)
}

export function createInventoryWorkflowFromTemplate(templateId: string, customizations?: Partial<WorkflowDefinition>): WorkflowDefinition {
  const template = getInventoryWorkflowTemplate(templateId)
  if (!template) {
    throw new Error(`Template ${templateId} not found`)
  }

  const workflow: WorkflowDefinition = {
    id: generateId(),
    ...template.definition,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'system',
    ...customizations
  }

  return workflow
}
