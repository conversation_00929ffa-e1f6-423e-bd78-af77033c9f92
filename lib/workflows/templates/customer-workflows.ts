// Customer Journey Workflow Templates
// Predefined workflows for customer lifecycle management

import { WorkflowTemplate, WorkflowDefinition } from '../types'
import { generateId } from '../../page-builder/utils'

export const customerWorkflowTemplates: WorkflowTemplate[] = [
  {
    id: 'customer-welcome-series',
    name: 'Customer Welcome Series',
    description: 'Automated welcome email series for new customers',
    category: 'customer',
    icon: 'UserPlus',
    isBuiltIn: true,
    tags: ['welcome', 'onboarding', 'email'],
    definition: {
      name: 'Customer Welcome Series',
      description: 'Send a series of welcome emails to new customers',
      version: '1.0.0',
      category: 'customer',
      trigger: {
        type: 'event',
        event: 'customer.registered',
        config: {}
      },
      steps: [
        {
          id: 'welcome-email-1',
          name: 'Send Welcome Email',
          description: 'Send immediate welcome email',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'customer-welcome',
            recipient: '{{customer.email}}',
            data: {
              customerName: '{{customer.firstName}}',
              welcomeOffer: '10% off your first order'
            }
          }
        },
        {
          id: 'delay-24h',
          name: 'Wait 24 Hours',
          description: 'Wait 24 hours before next email',
          type: 'delay',
          status: 'pending',
          config: {
            delay: 86400000 // 24 hours in milliseconds
          }
        },
        {
          id: 'welcome-email-2',
          name: 'Send Product Showcase Email',
          description: 'Send email showcasing popular products',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'product-showcase',
            recipient: '{{customer.email}}',
            data: {
              customerName: '{{customer.firstName}}',
              featuredProducts: '{{products.featured}}'
            }
          },
          dependencies: ['delay-24h']
        },
        {
          id: 'delay-72h',
          name: 'Wait 72 Hours',
          description: 'Wait 72 hours before final email',
          type: 'delay',
          status: 'pending',
          config: {
            delay: 259200000 // 72 hours in milliseconds
          },
          dependencies: ['welcome-email-2']
        },
        {
          id: 'welcome-email-3',
          name: 'Send Care Guide Email',
          description: 'Send email with care instructions and tips',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'care-guide',
            recipient: '{{customer.email}}',
            data: {
              customerName: '{{customer.firstName}}',
              careGuideUrl: '{{site.url}}/care-guide'
            }
          },
          dependencies: ['delay-72h']
        }
      ],
      isActive: true,
      tags: ['welcome', 'onboarding', 'email'],
      metadata: {
        estimatedDuration: '72 hours',
        emailCount: 3,
        conversionGoal: 'first_purchase'
      }
    }
  },

  {
    id: 'cart-abandonment-recovery',
    name: 'Cart Abandonment Recovery',
    description: 'Recover abandoned carts with targeted emails',
    category: 'customer',
    icon: 'ShoppingCart',
    isBuiltIn: true,
    tags: ['cart', 'abandonment', 'recovery', 'email'],
    definition: {
      name: 'Cart Abandonment Recovery',
      description: 'Automated sequence to recover abandoned shopping carts',
      version: '1.0.0',
      category: 'customer',
      trigger: {
        type: 'event',
        event: 'cart.abandoned',
        conditions: [
          {
            field: 'cart.itemCount',
            operator: 'greater_than',
            value: 0
          },
          {
            field: 'cart.total',
            operator: 'greater_than',
            value: 100
          }
        ],
        config: {}
      },
      steps: [
        {
          id: 'delay-1h',
          name: 'Wait 1 Hour',
          description: 'Wait 1 hour to see if customer returns',
          type: 'delay',
          status: 'pending',
          config: {
            delay: 3600000 // 1 hour
          }
        },
        {
          id: 'check-cart-status',
          name: 'Check Cart Status',
          description: 'Check if cart is still abandoned',
          type: 'condition',
          status: 'pending',
          config: {
            conditions: [
              {
                field: 'cart.status',
                operator: 'equals',
                value: 'abandoned'
              }
            ]
          },
          dependencies: ['delay-1h']
        },
        {
          id: 'abandonment-email-1',
          name: 'Send First Recovery Email',
          description: 'Send gentle reminder about items in cart',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'cart-abandonment-1',
            recipient: '{{customer.email}}',
            data: {
              customerName: '{{customer.firstName}}',
              cartItems: '{{cart.items}}',
              cartTotal: '{{cart.total}}',
              cartUrl: '{{cart.recoveryUrl}}'
            }
          },
          dependencies: ['check-cart-status']
        },
        {
          id: 'delay-24h',
          name: 'Wait 24 Hours',
          description: 'Wait 24 hours before second email',
          type: 'delay',
          status: 'pending',
          config: {
            delay: 86400000 // 24 hours
          },
          dependencies: ['abandonment-email-1']
        },
        {
          id: 'abandonment-email-2',
          name: 'Send Discount Offer Email',
          description: 'Send email with discount incentive',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'cart-abandonment-2',
            recipient: '{{customer.email}}',
            data: {
              customerName: '{{customer.firstName}}',
              cartItems: '{{cart.items}}',
              discountCode: 'COMEBACK10',
              discountPercent: 10,
              cartUrl: '{{cart.recoveryUrl}}'
            }
          },
          dependencies: ['delay-24h']
        },
        {
          id: 'delay-72h',
          name: 'Wait 72 Hours',
          description: 'Wait 72 hours before final email',
          type: 'delay',
          status: 'pending',
          config: {
            delay: 259200000 // 72 hours
          },
          dependencies: ['abandonment-email-2']
        },
        {
          id: 'abandonment-email-3',
          name: 'Send Final Recovery Email',
          description: 'Send final email with urgency message',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'cart-abandonment-3',
            recipient: '{{customer.email}}',
            data: {
              customerName: '{{customer.firstName}}',
              cartItems: '{{cart.items}}',
              urgencyMessage: 'Items in your cart are selling fast!',
              cartUrl: '{{cart.recoveryUrl}}'
            }
          },
          dependencies: ['delay-72h']
        }
      ],
      isActive: true,
      tags: ['cart', 'abandonment', 'recovery', 'email'],
      metadata: {
        estimatedDuration: '96 hours',
        emailCount: 3,
        averageRecoveryRate: '15%'
      }
    }
  },

  {
    id: 'customer-reactivation',
    name: 'Customer Reactivation Campaign',
    description: 'Re-engage inactive customers with personalized offers',
    category: 'customer',
    icon: 'RefreshCw',
    isBuiltIn: true,
    tags: ['reactivation', 'retention', 'email'],
    definition: {
      name: 'Customer Reactivation Campaign',
      description: 'Automated campaign to re-engage customers who haven\'t purchased in 90 days',
      version: '1.0.0',
      category: 'customer',
      trigger: {
        type: 'schedule',
        schedule: '0 9 * * 1', // Every Monday at 9 AM
        conditions: [
          {
            field: 'customer.lastOrderDate',
            operator: 'less_than',
            value: '90 days ago'
          },
          {
            field: 'customer.totalOrders',
            operator: 'greater_than',
            value: 0
          }
        ],
        config: {}
      },
      steps: [
        {
          id: 'segment-customers',
          name: 'Segment Inactive Customers',
          description: 'Identify and segment inactive customers',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'database_query',
            query: 'SELECT * FROM customers WHERE last_order_date < NOW() - INTERVAL 90 DAY'
          }
        },
        {
          id: 'personalize-offers',
          name: 'Generate Personalized Offers',
          description: 'Create personalized offers based on purchase history',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'api_call',
            endpoint: '/api/personalization/offers',
            method: 'POST'
          },
          dependencies: ['segment-customers']
        },
        {
          id: 'reactivation-email',
          name: 'Send Reactivation Email',
          description: 'Send personalized reactivation email',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'customer-reactivation',
            recipient: '{{customer.email}}',
            data: {
              customerName: '{{customer.firstName}}',
              lastPurchase: '{{customer.lastOrderDate}}',
              personalizedOffer: '{{offer.details}}',
              recommendedProducts: '{{recommendations.products}}'
            }
          },
          dependencies: ['personalize-offers']
        },
        {
          id: 'track-engagement',
          name: 'Track Email Engagement',
          description: 'Track opens, clicks, and conversions',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'update_customer',
            fields: {
              lastEngagementDate: '{{now}}',
              reactivationCampaignSent: true
            }
          },
          dependencies: ['reactivation-email']
        }
      ],
      isActive: true,
      tags: ['reactivation', 'retention', 'email'],
      metadata: {
        targetAudience: 'inactive_customers',
        expectedReactivationRate: '8%',
        campaignFrequency: 'weekly'
      }
    }
  },

  {
    id: 'birthday-campaign',
    name: 'Birthday Campaign',
    description: 'Send birthday wishes with special offers',
    category: 'customer',
    icon: 'Gift',
    isBuiltIn: true,
    tags: ['birthday', 'celebration', 'email', 'discount'],
    definition: {
      name: 'Birthday Campaign',
      description: 'Automated birthday wishes with special discount offers',
      version: '1.0.0',
      category: 'customer',
      trigger: {
        type: 'schedule',
        schedule: '0 8 * * *', // Daily at 8 AM
        conditions: [
          {
            field: 'customer.dateOfBirth',
            operator: 'equals',
            value: 'today'
          }
        ],
        config: {}
      },
      steps: [
        {
          id: 'generate-birthday-code',
          name: 'Generate Birthday Discount Code',
          description: 'Create unique birthday discount code',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'api_call',
            endpoint: '/api/discounts/generate',
            method: 'POST',
            data: {
              type: 'percentage',
              value: 20,
              prefix: 'BIRTHDAY',
              expiresIn: '30 days'
            }
          }
        },
        {
          id: 'birthday-email',
          name: 'Send Birthday Email',
          description: 'Send personalized birthday email with discount',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'customer-birthday',
            recipient: '{{customer.email}}',
            data: {
              customerName: '{{customer.firstName}}',
              birthdayMessage: 'Happy Birthday! 🎉',
              discountCode: '{{discountCode}}',
              discountPercent: 20,
              expiryDate: '{{discountExpiry}}'
            }
          },
          dependencies: ['generate-birthday-code']
        },
        {
          id: 'update-customer-record',
          name: 'Update Customer Record',
          description: 'Mark birthday campaign as sent',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'update_customer',
            fields: {
              lastBirthdayCampaign: '{{now}}',
              birthdayDiscountSent: true
            }
          },
          dependencies: ['birthday-email']
        }
      ],
      isActive: true,
      tags: ['birthday', 'celebration', 'email', 'discount'],
      metadata: {
        discountPercent: 20,
        validityPeriod: '30 days',
        averageConversionRate: '25%'
      }
    }
  }
]

export function getCustomerWorkflowTemplate(templateId: string): WorkflowTemplate | undefined {
  return customerWorkflowTemplates.find(template => template.id === templateId)
}

export function createWorkflowFromTemplate(templateId: string, customizations?: Partial<WorkflowDefinition>): WorkflowDefinition {
  const template = getCustomerWorkflowTemplate(templateId)
  if (!template) {
    throw new Error(`Template ${templateId} not found`)
  }

  const workflow: WorkflowDefinition = {
    id: generateId(),
    ...template.definition,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'system',
    ...customizations
  }

  return workflow
}
