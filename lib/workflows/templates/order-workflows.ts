// Order Management Workflow Templates
// Predefined workflows for order lifecycle management

import { WorkflowTemplate, WorkflowDefinition } from '../types'
import { generateId } from '../../page-builder/utils'

export const orderWorkflowTemplates: WorkflowTemplate[] = [
  {
    id: 'order-fulfillment-standard',
    name: 'Standard Order Fulfillment',
    description: 'Complete order fulfillment process from payment to delivery',
    category: 'order',
    icon: 'Package',
    isBuiltIn: true,
    tags: ['fulfillment', 'shipping', 'tracking'],
    definition: {
      name: 'Standard Order Fulfillment',
      description: 'Automated order fulfillment workflow for standard orders',
      version: '1.0.0',
      category: 'order',
      trigger: {
        type: 'event',
        event: 'order.paid',
        conditions: [
          {
            field: 'order.paymentStatus',
            operator: 'equals',
            value: 'paid'
          }
        ],
        config: {}
      },
      steps: [
        {
          id: 'validate-inventory',
          name: 'Validate Inventory',
          description: 'Check if all items are in stock',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'update_inventory',
            operation: 'validate_stock',
            orderItems: '{{order.items}}'
          }
        },
        {
          id: 'reserve-inventory',
          name: 'Reserve Inventory',
          description: 'Reserve items for this order',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'update_inventory',
            operation: 'reserve_stock',
            orderItems: '{{order.items}}'
          },
          dependencies: ['validate-inventory']
        },
        {
          id: 'update-order-status',
          name: 'Update Order Status to Processing',
          description: 'Mark order as processing',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'update_order',
            orderId: '{{order.id}}',
            status: 'processing',
            fulfillmentStatus: 'processing'
          },
          dependencies: ['reserve-inventory']
        },
        {
          id: 'send-confirmation-email',
          name: 'Send Order Confirmation',
          description: 'Send order confirmation email to customer',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'order-confirmation',
            recipient: '{{order.customerEmail}}',
            data: {
              orderNumber: '{{order.orderNumber}}',
              customerName: '{{order.customerFirstName}}',
              orderItems: '{{order.items}}',
              orderTotal: '{{order.total}}',
              estimatedDelivery: '{{shipping.estimatedDelivery}}'
            }
          },
          dependencies: ['update-order-status']
        },
        {
          id: 'create-pick-list',
          name: 'Generate Pick List',
          description: 'Create warehouse pick list',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'create_task',
            taskType: 'warehouse_picking',
            assignTo: 'warehouse_team',
            data: {
              orderId: '{{order.id}}',
              items: '{{order.items}}',
              priority: 'normal'
            }
          },
          dependencies: ['send-confirmation-email']
        },
        {
          id: 'wait-for-picking',
          name: 'Wait for Picking Completion',
          description: 'Wait for warehouse team to complete picking',
          type: 'condition',
          status: 'pending',
          config: {
            conditions: [
              {
                field: 'order.fulfillmentStatus',
                operator: 'equals',
                value: 'picked'
              }
            ],
            timeout: 86400000 // 24 hours
          },
          dependencies: ['create-pick-list']
        },
        {
          id: 'create-shipping-label',
          name: 'Create Shipping Label',
          description: 'Generate shipping label and tracking number',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'create_shipment',
            carrier: '{{order.shippingMethod}}',
            fromAddress: '{{warehouse.address}}',
            toAddress: '{{order.shippingAddress}}',
            packages: '{{order.packages}}'
          },
          dependencies: ['wait-for-picking']
        },
        {
          id: 'update-order-shipped',
          name: 'Update Order Status to Shipped',
          description: 'Mark order as shipped',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'update_order',
            orderId: '{{order.id}}',
            status: 'shipped',
            fulfillmentStatus: 'shipped',
            trackingNumber: '{{shipment.trackingNumber}}',
            shippedAt: '{{now}}'
          },
          dependencies: ['create-shipping-label']
        },
        {
          id: 'send-shipping-notification',
          name: 'Send Shipping Notification',
          description: 'Notify customer that order has shipped',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'order-shipped',
            recipient: '{{order.customerEmail}}',
            data: {
              orderNumber: '{{order.orderNumber}}',
              customerName: '{{order.customerFirstName}}',
              trackingNumber: '{{shipment.trackingNumber}}',
              trackingUrl: '{{shipment.trackingUrl}}',
              carrier: '{{shipment.carrier}}'
            }
          },
          dependencies: ['update-order-shipped']
        },
        {
          id: 'schedule-delivery-check',
          name: 'Schedule Delivery Check',
          description: 'Schedule check for delivery confirmation',
          type: 'delay',
          status: 'pending',
          config: {
            delay: 604800000 // 7 days
          },
          dependencies: ['send-shipping-notification']
        },
        {
          id: 'check-delivery-status',
          name: 'Check Delivery Status',
          description: 'Check if order has been delivered',
          type: 'integration',
          status: 'pending',
          config: {
            integration: 'shipping_carrier',
            action: 'track_shipment',
            trackingNumber: '{{shipment.trackingNumber}}'
          },
          dependencies: ['schedule-delivery-check']
        },
        {
          id: 'update-order-delivered',
          name: 'Update Order Status to Delivered',
          description: 'Mark order as delivered if confirmed',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'update_order',
            orderId: '{{order.id}}',
            status: 'delivered',
            fulfillmentStatus: 'delivered',
            deliveredAt: '{{tracking.deliveredAt}}'
          },
          dependencies: ['check-delivery-status']
        },
        {
          id: 'send-delivery-confirmation',
          name: 'Send Delivery Confirmation',
          description: 'Confirm delivery with customer',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'order-delivered',
            recipient: '{{order.customerEmail}}',
            data: {
              orderNumber: '{{order.orderNumber}}',
              customerName: '{{order.customerFirstName}}',
              deliveredAt: '{{tracking.deliveredAt}}',
              reviewUrl: '{{site.url}}/reviews/{{order.id}}'
            }
          },
          dependencies: ['update-order-delivered']
        }
      ],
      isActive: true,
      tags: ['fulfillment', 'shipping', 'tracking'],
      metadata: {
        estimatedDuration: '7-10 days',
        automationLevel: 'high',
        touchpoints: 4
      }
    }
  },

  {
    id: 'order-cancellation-workflow',
    name: 'Order Cancellation Workflow',
    description: 'Handle order cancellations and refunds',
    category: 'order',
    icon: 'XCircle',
    isBuiltIn: true,
    tags: ['cancellation', 'refund', 'inventory'],
    definition: {
      name: 'Order Cancellation Workflow',
      description: 'Automated workflow for processing order cancellations',
      version: '1.0.0',
      category: 'order',
      trigger: {
        type: 'event',
        event: 'order.cancelled',
        config: {}
      },
      steps: [
        {
          id: 'check-order-status',
          name: 'Check Order Status',
          description: 'Verify order can be cancelled',
          type: 'condition',
          status: 'pending',
          config: {
            conditions: [
              {
                field: 'order.status',
                operator: 'in',
                value: ['pending', 'confirmed', 'processing']
              }
            ]
          }
        },
        {
          id: 'release-inventory',
          name: 'Release Reserved Inventory',
          description: 'Return reserved items to available stock',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'update_inventory',
            operation: 'release_reserved',
            orderItems: '{{order.items}}'
          },
          dependencies: ['check-order-status']
        },
        {
          id: 'process-refund',
          name: 'Process Refund',
          description: 'Initiate refund if payment was processed',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'process_payment',
            operation: 'refund',
            paymentId: '{{order.paymentId}}',
            amount: '{{order.total}}',
            reason: 'order_cancelled'
          },
          dependencies: ['release-inventory']
        },
        {
          id: 'update-order-status',
          name: 'Update Order Status',
          description: 'Mark order as cancelled',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'update_order',
            orderId: '{{order.id}}',
            status: 'cancelled',
            cancelledAt: '{{now}}',
            cancellationReason: '{{cancellation.reason}}'
          },
          dependencies: ['process-refund']
        },
        {
          id: 'send-cancellation-email',
          name: 'Send Cancellation Confirmation',
          description: 'Notify customer of cancellation',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'order-cancelled',
            recipient: '{{order.customerEmail}}',
            data: {
              orderNumber: '{{order.orderNumber}}',
              customerName: '{{order.customerFirstName}}',
              refundAmount: '{{refund.amount}}',
              refundMethod: '{{refund.method}}',
              estimatedRefundTime: '3-5 business days'
            }
          },
          dependencies: ['update-order-status']
        },
        {
          id: 'create-admin-task',
          name: 'Create Admin Review Task',
          description: 'Create task for admin to review cancellation',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'create_task',
            taskType: 'order_review',
            assignTo: 'customer_service',
            priority: 'normal',
            data: {
              orderId: '{{order.id}}',
              cancellationReason: '{{cancellation.reason}}',
              refundAmount: '{{refund.amount}}'
            }
          },
          dependencies: ['send-cancellation-email']
        }
      ],
      isActive: true,
      tags: ['cancellation', 'refund', 'inventory'],
      metadata: {
        estimatedDuration: '1-2 hours',
        refundProcessingTime: '3-5 business days',
        requiresManualReview: false
      }
    }
  },

  {
    id: 'order-return-workflow',
    name: 'Order Return Workflow',
    description: 'Handle product returns and exchanges',
    category: 'order',
    icon: 'RotateCcw',
    isBuiltIn: true,
    tags: ['return', 'exchange', 'refund'],
    definition: {
      name: 'Order Return Workflow',
      description: 'Automated workflow for processing product returns',
      version: '1.0.0',
      category: 'order',
      trigger: {
        type: 'event',
        event: 'return.requested',
        config: {}
      },
      steps: [
        {
          id: 'validate-return-eligibility',
          name: 'Validate Return Eligibility',
          description: 'Check if return is within policy timeframe',
          type: 'condition',
          status: 'pending',
          config: {
            conditions: [
              {
                field: 'order.deliveredAt',
                operator: 'greater_than',
                value: '30 days ago'
              },
              {
                field: 'return.reason',
                operator: 'in',
                value: ['defective', 'wrong_item', 'not_as_described', 'changed_mind']
              }
            ]
          }
        },
        {
          id: 'generate-return-label',
          name: 'Generate Return Shipping Label',
          description: 'Create prepaid return shipping label',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'create_shipment',
            type: 'return',
            fromAddress: '{{order.shippingAddress}}',
            toAddress: '{{warehouse.address}}',
            items: '{{return.items}}'
          },
          dependencies: ['validate-return-eligibility']
        },
        {
          id: 'send-return-instructions',
          name: 'Send Return Instructions',
          description: 'Email return instructions and label to customer',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'return-instructions',
            recipient: '{{order.customerEmail}}',
            data: {
              orderNumber: '{{order.orderNumber}}',
              customerName: '{{order.customerFirstName}}',
              returnItems: '{{return.items}}',
              returnLabel: '{{return.shippingLabel}}',
              returnInstructions: '{{return.instructions}}'
            }
          },
          dependencies: ['generate-return-label']
        },
        {
          id: 'wait-for-return-shipment',
          name: 'Wait for Return Shipment',
          description: 'Monitor return shipment tracking',
          type: 'condition',
          status: 'pending',
          config: {
            conditions: [
              {
                field: 'return.status',
                operator: 'equals',
                value: 'received'
              }
            ],
            timeout: 1209600000 // 14 days
          },
          dependencies: ['send-return-instructions']
        },
        {
          id: 'inspect-returned-items',
          name: 'Inspect Returned Items',
          description: 'Create task for quality inspection',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'create_task',
            taskType: 'quality_inspection',
            assignTo: 'quality_team',
            data: {
              returnId: '{{return.id}}',
              items: '{{return.items}}',
              returnReason: '{{return.reason}}'
            }
          },
          dependencies: ['wait-for-return-shipment']
        },
        {
          id: 'process-return-refund',
          name: 'Process Return Refund',
          description: 'Issue refund for approved return',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'process_payment',
            operation: 'refund',
            paymentId: '{{order.paymentId}}',
            amount: '{{return.refundAmount}}',
            reason: 'product_return'
          },
          dependencies: ['inspect-returned-items']
        },
        {
          id: 'update-inventory',
          name: 'Update Inventory',
          description: 'Return items to inventory if in good condition',
          type: 'action',
          status: 'pending',
          config: {
            actionType: 'update_inventory',
            operation: 'return_to_stock',
            items: '{{return.approvedItems}}'
          },
          dependencies: ['process-return-refund']
        },
        {
          id: 'send-refund-confirmation',
          name: 'Send Refund Confirmation',
          description: 'Notify customer of processed refund',
          type: 'notification',
          status: 'pending',
          config: {
            type: 'email',
            template: 'return-refund-processed',
            recipient: '{{order.customerEmail}}',
            data: {
              orderNumber: '{{order.orderNumber}}',
              customerName: '{{order.customerFirstName}}',
              refundAmount: '{{return.refundAmount}}',
              refundMethod: '{{refund.method}}',
              estimatedRefundTime: '3-5 business days'
            }
          },
          dependencies: ['update-inventory']
        }
      ],
      isActive: true,
      tags: ['return', 'exchange', 'refund'],
      metadata: {
        estimatedDuration: '7-14 days',
        returnWindow: '30 days',
        requiresInspection: true
      }
    }
  }
]

export function getOrderWorkflowTemplate(templateId: string): WorkflowTemplate | undefined {
  return orderWorkflowTemplates.find(template => template.id === templateId)
}

export function createOrderWorkflowFromTemplate(templateId: string, customizations?: Partial<WorkflowDefinition>): WorkflowDefinition {
  const template = getOrderWorkflowTemplate(templateId)
  if (!template) {
    throw new Error(`Template ${templateId} not found`)
  }

  const workflow: WorkflowDefinition = {
    id: generateId(),
    ...template.definition,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'system',
    ...customizations
  }

  return workflow
}
