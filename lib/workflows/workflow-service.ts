// Workflow Management Service
// Service for managing workflow definitions, executions, and templates

import { PrismaClient } from '@prisma/client'
import { WorkflowEngine } from './engine'
import {
  WorkflowDefinition,
  WorkflowExecution,
  WorkflowTemplate,
  WorkflowEventType,
  WorkflowAnalytics
} from './types'
import { customerWorkflowTemplates } from './templates/customer-workflows'
import { orderWorkflowTemplates } from './templates/order-workflows'
import { inventoryWorkflowTemplates } from './templates/inventory-workflows'
import { generateId } from '../page-builder/utils'

export class WorkflowService {
  private db: PrismaClient
  private engine: WorkflowEngine
  private templates: Map<string, WorkflowTemplate> = new Map()

  constructor(database?: PrismaClient) {
    this.db = database || new PrismaClient()
    this.engine = new WorkflowEngine(this.db)
    this.loadBuiltInTemplates()
  }

  /**
   * Initialize the workflow service
   */
  async initialize(): Promise<void> {
    await this.engine.start()
    await this.loadWorkflowsFromDatabase()
  }

  /**
   * Shutdown the workflow service
   */
  async shutdown(): Promise<void> {
    await this.engine.stop()
  }

  /**
   * Trigger a workflow by event
   */
  async triggerWorkflow(
    eventType: WorkflowEventType,
    eventData: Record<string, any>,
    context: Record<string, any> = {}
  ): Promise<string[]> {
    return await this.engine.triggerWorkflow(eventType, eventData, context)
  }

  /**
   * Create a new workflow from template
   */
  async createWorkflowFromTemplate(
    templateId: string,
    customizations?: Partial<WorkflowDefinition>
  ): Promise<WorkflowDefinition> {
    const template = this.templates.get(templateId)
    if (!template) {
      throw new Error(`Template ${templateId} not found`)
    }

    const workflow: WorkflowDefinition = {
      id: generateId(),
      ...template.definition,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: customizations?.createdBy || 'system',
      ...customizations
    }

    await this.saveWorkflow(workflow)
    return workflow
  }

  /**
   * Create a custom workflow
   */
  async createWorkflow(workflow: Omit<WorkflowDefinition, 'id' | 'createdAt' | 'updatedAt'>): Promise<WorkflowDefinition> {
    const newWorkflow: WorkflowDefinition = {
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date(),
      ...workflow
    }

    await this.saveWorkflow(newWorkflow)
    return newWorkflow
  }

  /**
   * Get workflow by ID
   */
  async getWorkflow(workflowId: string): Promise<WorkflowDefinition | null> {
    try {
      const workflow = await this.db.workflow.findUnique({
        where: { id: workflowId }
      })

      if (!workflow) return null

      return {
        id: workflow.id,
        name: workflow.name,
        description: workflow.description || '',
        version: workflow.version,
        category: workflow.category as any,
        trigger: JSON.parse(workflow.trigger as string),
        steps: JSON.parse(workflow.steps as string),
        isActive: workflow.isActive,
        createdAt: workflow.createdAt,
        updatedAt: workflow.updatedAt,
        createdBy: workflow.createdBy,
        tags: workflow.tags,
        metadata: JSON.parse(workflow.metadata as string || '{}')
      }
    } catch (error) {
      console.error('Error getting workflow:', error)
      return null
    }
  }

  /**
   * List workflows with filtering
   */
  async listWorkflows(filters: {
    category?: string
    isActive?: boolean
    tags?: string[]
    search?: string
    limit?: number
    offset?: number
  } = {}): Promise<{
    workflows: WorkflowDefinition[]
    total: number
  }> {
    try {
      const where: any = {}

      if (filters.category) {
        where.category = filters.category
      }

      if (filters.isActive !== undefined) {
        where.isActive = filters.isActive
      }

      if (filters.tags && filters.tags.length > 0) {
        where.tags = { hasSome: filters.tags }
      }

      if (filters.search) {
        where.OR = [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } }
        ]
      }

      const [workflows, total] = await Promise.all([
        this.db.workflow.findMany({
          where,
          orderBy: { updatedAt: 'desc' },
          take: filters.limit || 50,
          skip: filters.offset || 0
        }),
        this.db.workflow.count({ where })
      ])

      const transformedWorkflows = workflows.map(workflow => ({
        id: workflow.id,
        name: workflow.name,
        description: workflow.description || '',
        version: workflow.version,
        category: workflow.category as any,
        trigger: JSON.parse(workflow.trigger as string),
        steps: JSON.parse(workflow.steps as string),
        isActive: workflow.isActive,
        createdAt: workflow.createdAt,
        updatedAt: workflow.updatedAt,
        createdBy: workflow.createdBy,
        tags: workflow.tags,
        metadata: JSON.parse(workflow.metadata as string || '{}')
      }))

      return { workflows: transformedWorkflows, total }
    } catch (error) {
      console.error('Error listing workflows:', error)
      return { workflows: [], total: 0 }
    }
  }

  /**
   * Update workflow
   */
  async updateWorkflow(workflowId: string, updates: Partial<WorkflowDefinition>): Promise<WorkflowDefinition | null> {
    try {
      const workflow = await this.db.workflow.update({
        where: { id: workflowId },
        data: {
          name: updates.name,
          description: updates.description,
          version: updates.version,
          category: updates.category,
          trigger: updates.trigger ? JSON.stringify(updates.trigger) : undefined,
          steps: updates.steps ? JSON.stringify(updates.steps) : undefined,
          isActive: updates.isActive,
          tags: updates.tags,
          metadata: updates.metadata ? JSON.stringify(updates.metadata) : undefined,
          updatedAt: new Date()
        }
      })

      return await this.getWorkflow(workflow.id)
    } catch (error) {
      console.error('Error updating workflow:', error)
      return null
    }
  }

  /**
   * Delete workflow
   */
  async deleteWorkflow(workflowId: string): Promise<boolean> {
    try {
      await this.db.workflow.delete({
        where: { id: workflowId }
      })
      return true
    } catch (error) {
      console.error('Error deleting workflow:', error)
      return false
    }
  }

  /**
   * Get workflow execution
   */
  async getExecution(executionId: string): Promise<WorkflowExecution | null> {
    try {
      const execution = await this.db.workflowExecution.findUnique({
        where: { id: executionId }
      })

      if (!execution) return null

      return {
        id: execution.id,
        workflowId: execution.workflowId,
        workflowVersion: execution.workflowVersion,
        status: execution.status as any,
        triggeredBy: execution.triggeredBy,
        triggerData: JSON.parse(execution.triggerData as string),
        context: JSON.parse(execution.context as string),
        steps: JSON.parse(execution.steps as string),
        startedAt: execution.startedAt,
        completedAt: execution.completedAt || undefined,
        duration: execution.duration || undefined,
        error: execution.error || undefined,
        result: execution.result ? JSON.parse(execution.result as string) : undefined
      }
    } catch (error) {
      console.error('Error getting execution:', error)
      return null
    }
  }

  /**
   * List workflow executions
   */
  async listExecutions(filters: {
    workflowId?: string
    status?: string
    triggeredBy?: string
    limit?: number
    offset?: number
  } = {}): Promise<{
    executions: WorkflowExecution[]
    total: number
  }> {
    try {
      const where: any = {}

      if (filters.workflowId) {
        where.workflowId = filters.workflowId
      }

      if (filters.status) {
        where.status = filters.status
      }

      if (filters.triggeredBy) {
        where.triggeredBy = filters.triggeredBy
      }

      const [executions, total] = await Promise.all([
        this.db.workflowExecution.findMany({
          where,
          orderBy: { startedAt: 'desc' },
          take: filters.limit || 50,
          skip: filters.offset || 0
        }),
        this.db.workflowExecution.count({ where })
      ])

      const transformedExecutions = executions.map(execution => ({
        id: execution.id,
        workflowId: execution.workflowId,
        workflowVersion: execution.workflowVersion,
        status: execution.status as any,
        triggeredBy: execution.triggeredBy,
        triggerData: JSON.parse(execution.triggerData as string),
        context: JSON.parse(execution.context as string),
        steps: JSON.parse(execution.steps as string),
        startedAt: execution.startedAt,
        completedAt: execution.completedAt || undefined,
        duration: execution.duration || undefined,
        error: execution.error || undefined,
        result: execution.result ? JSON.parse(execution.result as string) : undefined
      }))

      return { executions: transformedExecutions, total }
    } catch (error) {
      console.error('Error listing executions:', error)
      return { executions: [], total: 0 }
    }
  }

  /**
   * Get workflow analytics
   */
  async getWorkflowAnalytics(workflowId: string, period: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<WorkflowAnalytics | null> {
    try {
      // Calculate date range based on period
      const now = new Date()
      const startDate = new Date()
      
      switch (period) {
        case 'day':
          startDate.setDate(now.getDate() - 1)
          break
        case 'week':
          startDate.setDate(now.getDate() - 7)
          break
        case 'month':
          startDate.setMonth(now.getMonth() - 1)
          break
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1)
          break
      }

      const executions = await this.db.workflowExecution.findMany({
        where: {
          workflowId,
          startedAt: {
            gte: startDate,
            lte: now
          }
        }
      })

      const totalExecutions = executions.length
      const successfulExecutions = executions.filter(e => e.status === 'completed').length
      const failedExecutions = executions.filter(e => e.status === 'failed').length
      
      const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0
      const errorRate = totalExecutions > 0 ? (failedExecutions / totalExecutions) * 100 : 0

      const completedExecutions = executions.filter(e => e.duration !== null)
      const averageDuration = completedExecutions.length > 0
        ? completedExecutions.reduce((sum, e) => sum + (e.duration || 0), 0) / completedExecutions.length
        : 0

      const errorCounts: Record<string, number> = {}
      executions.filter(e => e.error).forEach(e => {
        const error = e.error!
        errorCounts[error] = (errorCounts[error] || 0) + 1
      })

      const mostCommonErrors = Object.entries(errorCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([error]) => error)

      return {
        workflowId,
        period,
        executions: totalExecutions,
        successRate,
        averageDuration,
        errorRate,
        mostCommonErrors,
        performanceMetrics: {
          totalExecutions,
          successfulExecutions,
          failedExecutions,
          averageDuration
        }
      }
    } catch (error) {
      console.error('Error getting workflow analytics:', error)
      return null
    }
  }

  /**
   * Get all workflow templates
   */
  getTemplates(): WorkflowTemplate[] {
    return Array.from(this.templates.values())
  }

  /**
   * Get template by ID
   */
  getTemplate(templateId: string): WorkflowTemplate | undefined {
    return this.templates.get(templateId)
  }

  // Private methods
  private async saveWorkflow(workflow: WorkflowDefinition): Promise<void> {
    await this.db.workflow.upsert({
      where: { id: workflow.id },
      create: {
        id: workflow.id,
        name: workflow.name,
        description: workflow.description,
        version: workflow.version,
        category: workflow.category,
        trigger: JSON.stringify(workflow.trigger),
        steps: JSON.stringify(workflow.steps),
        isActive: workflow.isActive,
        createdAt: workflow.createdAt,
        updatedAt: workflow.updatedAt,
        createdBy: workflow.createdBy,
        tags: workflow.tags,
        metadata: JSON.stringify(workflow.metadata)
      },
      update: {
        name: workflow.name,
        description: workflow.description,
        version: workflow.version,
        category: workflow.category,
        trigger: JSON.stringify(workflow.trigger),
        steps: JSON.stringify(workflow.steps),
        isActive: workflow.isActive,
        updatedAt: workflow.updatedAt,
        tags: workflow.tags,
        metadata: JSON.stringify(workflow.metadata)
      }
    })
  }

  private loadBuiltInTemplates(): void {
    // Load customer workflow templates
    customerWorkflowTemplates.forEach(template => {
      this.templates.set(template.id, template)
    })

    // Load order workflow templates
    orderWorkflowTemplates.forEach(template => {
      this.templates.set(template.id, template)
    })

    // Load inventory workflow templates
    inventoryWorkflowTemplates.forEach(template => {
      this.templates.set(template.id, template)
    })
  }

  private async loadWorkflowsFromDatabase(): Promise<void> {
    try {
      const workflows = await this.db.workflow.findMany({
        where: { isActive: true }
      })

      console.log(`Loaded ${workflows.length} active workflows from database`)
    } catch (error) {
      console.error('Error loading workflows from database:', error)
    }
  }
}
