// Custom Workflow Examples
// Practical examples showing how to build workflows with the system

import { WorkflowDefinition } from '../types'
import { generateId } from '../../page-builder/utils'

/**
 * Example 1: VIP Customer Workflow
 * Triggered when a customer makes their 5th purchase
 */
export const vipCustomerWorkflow: WorkflowDefinition = {
  id: generateId(),
  name: 'VIP Customer Upgrade',
  description: 'Automatically upgrade customers to VIP status after 5 purchases and send special perks',
  version: '1.0.0',
  category: 'customer',
  trigger: {
    type: 'event',
    event: 'order.completed',
    conditions: [
      {
        field: 'customer.orderCount',
        operator: 'equals',
        value: 5
      }
    ],
    config: {}
  },
  steps: [
    {
      id: 'check-eligibility',
      name: 'Check VIP Eligibility',
      description: 'Verify customer has exactly 5 completed orders',
      type: 'condition',
      status: 'pending',
      config: {
        conditions: [
          {
            field: 'customer.orderCount',
            operator: 'equals',
            value: 5
          },
          {
            field: 'customer.status',
            operator: 'not_equals',
            value: 'vip'
          }
        ]
      },
      retryCount: 0,
      maxRetries: 1
    },
    {
      id: 'upgrade-customer',
      name: 'Upgrade to VIP Status',
      description: 'Update customer status to VIP in database',
      type: 'action',
      status: 'pending',
      config: {
        actionType: 'update_customer',
        customerId: '{{customer.id}}',
        updates: {
          status: 'vip',
          vipSince: '{{now}}',
          discountPercentage: 15
        }
      },
      dependencies: ['check-eligibility'],
      retryCount: 0,
      maxRetries: 3
    },
    {
      id: 'generate-vip-code',
      name: 'Generate VIP Discount Code',
      description: 'Create a personalized 20% discount code',
      type: 'action',
      status: 'pending',
      config: {
        actionType: 'api_call',
        endpoint: '/api/e-commerce/coupons',
        method: 'POST',
        data: {
          code: 'VIP{{customer.id}}',
          type: 'percentage',
          value: 20,
          customerId: '{{customer.id}}',
          expiresAt: '{{date.add(30, "days")}}',
          description: 'VIP Welcome Discount'
        }
      },
      dependencies: ['upgrade-customer'],
      retryCount: 0,
      maxRetries: 3
    },
    {
      id: 'send-vip-welcome',
      name: 'Send VIP Welcome Email',
      description: 'Send congratulations email with VIP perks',
      type: 'notification',
      status: 'pending',
      config: {
        type: 'email',
        template: 'vip-welcome',
        recipient: '{{customer.email}}',
        data: {
          customerName: '{{customer.firstName}}',
          vipCode: '{{coupon.code}}',
          discountAmount: '20%',
          perks: [
            'Free shipping on all orders',
            'Early access to sales',
            'Exclusive VIP-only products',
            'Priority customer support'
          ]
        }
      },
      dependencies: ['generate-vip-code'],
      retryCount: 0,
      maxRetries: 3
    },
    {
      id: 'create-vip-task',
      name: 'Create Customer Service Task',
      description: 'Notify customer service team about new VIP',
      type: 'action',
      status: 'pending',
      config: {
        actionType: 'create_task',
        taskType: 'customer_service',
        assignTo: 'vip_team',
        priority: 'normal',
        data: {
          customerId: '{{customer.id}}',
          customerName: '{{customer.firstName}} {{customer.lastName}}',
          action: 'new_vip_customer',
          message: 'New VIP customer - ensure excellent service'
        }
      },
      dependencies: ['send-vip-welcome'],
      retryCount: 0,
      maxRetries: 2
    }
  ],
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  createdBy: 'system',
  tags: ['vip', 'customer-loyalty', 'automation'],
  metadata: {
    estimatedDuration: '5 minutes',
    businessImpact: 'Increases customer loyalty and lifetime value',
    automationLevel: 'high'
  }
}

/**
 * Example 2: Seasonal Sale Workflow
 * Scheduled workflow for seasonal promotions
 */
export const seasonalSaleWorkflow: WorkflowDefinition = {
  id: generateId(),
  name: 'Summer Sale Campaign',
  description: 'Automated summer sale campaign with progressive discounts',
  version: '1.0.0',
  category: 'marketing',
  trigger: {
    type: 'schedule',
    schedule: '0 9 1 6 *', // June 1st at 9 AM
    config: {}
  },
  steps: [
    {
      id: 'create-sale-products',
      name: 'Mark Sale Products',
      description: 'Apply sale tags to summer collection',
      type: 'action',
      status: 'pending',
      config: {
        actionType: 'api_call',
        endpoint: '/api/e-commerce/products/bulk-update',
        method: 'POST',
        data: {
          filter: { category: 'summer-collection' },
          updates: {
            onSale: true,
            salePercentage: 25,
            saleStartDate: '{{now}}',
            saleEndDate: '{{date.add(30, "days")}}'
          }
        }
      },
      retryCount: 0,
      maxRetries: 3
    },
    {
      id: 'send-announcement',
      name: 'Send Sale Announcement',
      description: 'Email all customers about the summer sale',
      type: 'notification',
      status: 'pending',
      config: {
        type: 'email',
        template: 'summer-sale-announcement',
        recipient: 'all_customers',
        segmentation: {
          includeVip: true,
          includeActive: true,
          excludeUnsubscribed: true
        },
        data: {
          salePercentage: '25%',
          saleEndDate: '{{date.add(30, "days")}}',
          featuredProducts: '{{products.summer.featured}}'
        }
      },
      dependencies: ['create-sale-products'],
      retryCount: 0,
      maxRetries: 2
    },
    {
      id: 'wait-week-1',
      name: 'Wait 1 Week',
      description: 'Wait one week before first reminder',
      type: 'delay',
      status: 'pending',
      config: {
        delay: 604800000 // 7 days in milliseconds
      },
      dependencies: ['send-announcement'],
      retryCount: 0,
      maxRetries: 1
    },
    {
      id: 'send-reminder-1',
      name: 'Send First Reminder',
      description: 'Remind customers about ongoing sale',
      type: 'notification',
      status: 'pending',
      config: {
        type: 'email',
        template: 'sale-reminder',
        recipient: 'customers_no_purchase',
        data: {
          reminderNumber: 1,
          daysLeft: 23,
          urgencyMessage: 'Don\'t miss out on 25% off summer styles!'
        }
      },
      dependencies: ['wait-week-1'],
      retryCount: 0,
      maxRetries: 2
    },
    {
      id: 'wait-week-2',
      name: 'Wait Another Week',
      description: 'Wait another week before increasing discount',
      type: 'delay',
      status: 'pending',
      config: {
        delay: 604800000 // 7 days
      },
      dependencies: ['send-reminder-1'],
      retryCount: 0,
      maxRetries: 1
    },
    {
      id: 'increase-discount',
      name: 'Increase Discount to 35%',
      description: 'Boost discount for final push',
      type: 'action',
      status: 'pending',
      config: {
        actionType: 'api_call',
        endpoint: '/api/e-commerce/products/bulk-update',
        method: 'POST',
        data: {
          filter: { onSale: true, category: 'summer-collection' },
          updates: {
            salePercentage: 35
          }
        }
      },
      dependencies: ['wait-week-2'],
      retryCount: 0,
      maxRetries: 3
    },
    {
      id: 'send-final-push',
      name: 'Send Final Sale Push',
      description: 'Last chance email with increased discount',
      type: 'notification',
      status: 'pending',
      config: {
        type: 'email',
        template: 'sale-final-push',
        recipient: 'customers_no_purchase',
        data: {
          newDiscount: '35%',
          daysLeft: 9,
          urgencyMessage: 'FINAL DAYS - Now 35% off!'
        }
      },
      dependencies: ['increase-discount'],
      retryCount: 0,
      maxRetries: 2
    }
  ],
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  createdBy: 'marketing_team',
  tags: ['seasonal', 'sale', 'marketing', 'automated'],
  metadata: {
    estimatedDuration: '30 days',
    expectedRevenue: 'R50,000+',
    targetAudience: 'All active customers'
  }
}

/**
 * Example 3: Product Review Request Workflow
 * Triggered after successful delivery
 */
export const reviewRequestWorkflow: WorkflowDefinition = {
  id: generateId(),
  name: 'Product Review Request',
  description: 'Request product reviews from customers after delivery',
  version: '1.0.0',
  category: 'customer',
  trigger: {
    type: 'event',
    event: 'order.delivered',
    config: {}
  },
  steps: [
    {
      id: 'wait-satisfaction-period',
      name: 'Wait for Customer Satisfaction',
      description: 'Wait 3 days for customer to try products',
      type: 'delay',
      status: 'pending',
      config: {
        delay: 259200000 // 3 days
      },
      retryCount: 0,
      maxRetries: 1
    },
    {
      id: 'check-review-eligibility',
      name: 'Check Review Eligibility',
      description: 'Ensure customer hasn\'t already reviewed',
      type: 'condition',
      status: 'pending',
      config: {
        conditions: [
          {
            field: 'order.reviewStatus',
            operator: 'equals',
            value: 'pending'
          },
          {
            field: 'customer.reviewOptOut',
            operator: 'equals',
            value: false
          }
        ]
      },
      dependencies: ['wait-satisfaction-period'],
      retryCount: 0,
      maxRetries: 2
    },
    {
      id: 'generate-review-tokens',
      name: 'Generate Review Tokens',
      description: 'Create secure tokens for each product',
      type: 'action',
      status: 'pending',
      config: {
        actionType: 'api_call',
        endpoint: '/api/e-commerce/reviews/generate-tokens',
        method: 'POST',
        data: {
          orderId: '{{order.id}}',
          customerId: '{{customer.id}}',
          products: '{{order.items}}'
        }
      },
      dependencies: ['check-review-eligibility'],
      retryCount: 0,
      maxRetries: 3
    },
    {
      id: 'send-review-request',
      name: 'Send Review Request Email',
      description: 'Ask customer to review their purchase',
      type: 'notification',
      status: 'pending',
      config: {
        type: 'email',
        template: 'review-request',
        recipient: '{{customer.email}}',
        data: {
          customerName: '{{customer.firstName}}',
          orderNumber: '{{order.orderNumber}}',
          products: '{{order.items}}',
          reviewTokens: '{{review.tokens}}',
          incentive: '10% off next order'
        }
      },
      dependencies: ['generate-review-tokens'],
      retryCount: 0,
      maxRetries: 3
    },
    {
      id: 'wait-response-period',
      name: 'Wait for Response',
      description: 'Give customer 1 week to respond',
      type: 'delay',
      status: 'pending',
      config: {
        delay: 604800000 // 7 days
      },
      dependencies: ['send-review-request'],
      retryCount: 0,
      maxRetries: 1
    },
    {
      id: 'check-review-completion',
      name: 'Check if Reviews Submitted',
      description: 'Check if customer has submitted reviews',
      type: 'condition',
      status: 'pending',
      config: {
        conditions: [
          {
            field: 'order.reviewStatus',
            operator: 'equals',
            value: 'pending'
          }
        ]
      },
      dependencies: ['wait-response-period'],
      retryCount: 0,
      maxRetries: 2
    },
    {
      id: 'send-gentle-reminder',
      name: 'Send Gentle Reminder',
      description: 'One gentle reminder with increased incentive',
      type: 'notification',
      status: 'pending',
      config: {
        type: 'email',
        template: 'review-reminder',
        recipient: '{{customer.email}}',
        data: {
          customerName: '{{customer.firstName}}',
          orderNumber: '{{order.orderNumber}}',
          incentive: '15% off next order',
          message: 'We\'d love to hear about your experience!'
        }
      },
      dependencies: ['check-review-completion'],
      retryCount: 0,
      maxRetries: 2
    }
  ],
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  createdBy: 'customer_success',
  tags: ['reviews', 'feedback', 'customer-engagement'],
  metadata: {
    estimatedDuration: '10 days',
    expectedReviewRate: '25%',
    businessValue: 'Increases social proof and SEO'
  }
}

// Export all examples
export const customWorkflowExamples = [
  vipCustomerWorkflow,
  seasonalSaleWorkflow,
  reviewRequestWorkflow
]

// Helper function to create workflow from example
export function createWorkflowFromExample(exampleId: string): WorkflowDefinition | null {
  const example = customWorkflowExamples.find(w => w.id === exampleId)
  if (!example) return null

  return {
    ...example,
    id: generateId(),
    createdAt: new Date(),
    updatedAt: new Date()
  }
}
