// E-commerce Workflow Types
// Comprehensive workflow definitions for all e-commerce operations

export interface WorkflowStep {
  id: string
  name: string
  description: string
  type: 'action' | 'condition' | 'notification' | 'integration' | 'delay'
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped'
  config: Record<string, any>
  dependencies?: string[]
  timeout?: number
  retryCount?: number
  maxRetries?: number
  executedAt?: Date
  completedAt?: Date
  error?: string
  result?: any
}

export interface WorkflowDefinition {
  id: string
  name: string
  description: string
  version: string
  category: 'customer' | 'order' | 'inventory' | 'marketing' | 'admin'
  trigger: WorkflowTrigger
  steps: WorkflowStep[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  createdBy: string
  tags: string[]
  metadata: Record<string, any>
}

export interface WorkflowTrigger {
  type: 'event' | 'schedule' | 'manual' | 'webhook' | 'api'
  event?: string
  schedule?: string
  conditions?: WorkflowCondition[]
  config: Record<string, any>
}

export interface WorkflowCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'not_contains' | 'in' | 'not_in'
  value: any
  logicalOperator?: 'and' | 'or'
}

export interface WorkflowExecution {
  id: string
  workflowId: string
  workflowVersion: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  triggeredBy: string
  triggerData: Record<string, any>
  context: Record<string, any>
  steps: WorkflowStepExecution[]
  startedAt: Date
  completedAt?: Date
  duration?: number
  error?: string
  result?: any
}

export interface WorkflowStepExecution {
  stepId: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped'
  startedAt?: Date
  completedAt?: Date
  duration?: number
  retryCount: number
  error?: string
  result?: any
  logs: WorkflowLog[]
}

export interface WorkflowLog {
  timestamp: Date
  level: 'info' | 'warn' | 'error' | 'debug'
  message: string
  data?: Record<string, any>
}

export interface WorkflowTemplate {
  id: string
  name: string
  description: string
  category: string
  icon: string
  definition: Omit<WorkflowDefinition, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>
  isBuiltIn: boolean
  tags: string[]
}

// Customer Journey Workflows
export interface CustomerJourneyStage {
  id: string
  name: string
  description: string
  triggers: string[]
  actions: string[]
  nextStages: string[]
  conditions: WorkflowCondition[]
}

export interface CustomerJourney {
  id: string
  customerId: string
  currentStage: string
  stages: CustomerJourneyStage[]
  startedAt: Date
  lastUpdated: Date
  metadata: Record<string, any>
}

// Order Workflow States
export interface OrderWorkflowState {
  orderId: string
  currentStatus: string
  previousStatus?: string
  statusHistory: OrderStatusChange[]
  pendingActions: string[]
  blockedReasons: string[]
  estimatedCompletion?: Date
  metadata: Record<string, any>
}

export interface OrderStatusChange {
  from: string
  to: string
  timestamp: Date
  triggeredBy: string
  reason?: string
  metadata?: Record<string, any>
}

// Inventory Workflow Types
export interface InventoryWorkflowEvent {
  type: 'stock_low' | 'stock_out' | 'reorder_point' | 'stock_received' | 'stock_adjustment'
  productId: string
  sku: string
  currentStock: number
  previousStock?: number
  threshold?: number
  metadata: Record<string, any>
}

// Marketing Workflow Types
export interface MarketingCampaign {
  id: string
  name: string
  type: 'email' | 'sms' | 'push' | 'social' | 'retargeting'
  status: 'draft' | 'scheduled' | 'running' | 'paused' | 'completed'
  targetAudience: CustomerSegment
  content: CampaignContent
  schedule: CampaignSchedule
  metrics: CampaignMetrics
}

export interface CustomerSegment {
  id: string
  name: string
  conditions: WorkflowCondition[]
  customerCount: number
  lastUpdated: Date
}

export interface CampaignContent {
  subject?: string
  message: string
  template?: string
  variables: Record<string, any>
  attachments?: string[]
}

export interface CampaignSchedule {
  startDate: Date
  endDate?: Date
  frequency?: 'once' | 'daily' | 'weekly' | 'monthly'
  timezone: string
}

export interface CampaignMetrics {
  sent: number
  delivered: number
  opened: number
  clicked: number
  converted: number
  unsubscribed: number
  bounced: number
  revenue: number
}

// Notification Types
export interface NotificationTemplate {
  id: string
  name: string
  type: 'email' | 'sms' | 'push' | 'in_app'
  subject?: string
  content: string
  variables: string[]
  isActive: boolean
}

export interface NotificationEvent {
  id: string
  type: string
  recipientId: string
  recipientType: 'customer' | 'admin' | 'staff'
  templateId: string
  data: Record<string, any>
  status: 'pending' | 'sent' | 'delivered' | 'failed'
  sentAt?: Date
  deliveredAt?: Date
  error?: string
}

// Integration Types
export interface IntegrationConfig {
  id: string
  name: string
  type: 'payment' | 'shipping' | 'inventory' | 'crm' | 'analytics' | 'marketing'
  provider: string
  credentials: Record<string, any>
  settings: Record<string, any>
  isActive: boolean
  lastSync?: Date
}

// Workflow Analytics
export interface WorkflowAnalytics {
  workflowId: string
  period: 'day' | 'week' | 'month' | 'year'
  executions: number
  successRate: number
  averageDuration: number
  errorRate: number
  mostCommonErrors: string[]
  performanceMetrics: Record<string, number>
}

// Export all workflow event types
export type WorkflowEventType = 
  | 'customer.registered'
  | 'customer.login'
  | 'customer.profile_updated'
  | 'order.created'
  | 'order.paid'
  | 'order.shipped'
  | 'order.delivered'
  | 'order.cancelled'
  | 'order.refunded'
  | 'cart.abandoned'
  | 'cart.recovered'
  | 'product.viewed'
  | 'product.added_to_cart'
  | 'product.purchased'
  | 'inventory.low_stock'
  | 'inventory.out_of_stock'
  | 'inventory.reorder_point'
  | 'payment.succeeded'
  | 'payment.failed'
  | 'shipping.label_created'
  | 'shipping.dispatched'
  | 'shipping.delivered'
  | 'review.submitted'
  | 'support.ticket_created'
  | 'marketing.campaign_sent'
  | 'marketing.email_opened'
  | 'marketing.email_clicked'

export type WorkflowActionType =
  | 'send_email'
  | 'send_sms'
  | 'send_push_notification'
  | 'update_customer'
  | 'update_order'
  | 'create_task'
  | 'update_inventory'
  | 'process_payment'
  | 'create_shipment'
  | 'send_webhook'
  | 'delay'
  | 'condition'
  | 'loop'
  | 'parallel'
  | 'api_call'
  | 'database_query'
  | 'file_operation'
  | 'integration_sync'
