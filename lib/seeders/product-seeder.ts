/**
 * Product Seeder for Coco Milk Kids
 * 
 * This seeder creates products based on the available images and their metadata.
 * It generates realistic product data that matches the image content and brand aesthetic.
 */

import { imageSeeds, ImageMetadata } from '@/lib/seeders/data/image-seed'
import { Product, ProductVariant } from '@/lib/seeders/data/product-seed'

// Product name templates based on image categories and moods
const productNameTemplates = {
  'girls': [
    'Princess {mood} Dress',
    'Sweet {mood} Outfit',
    'Lovely {mood} Collection',
    'Charming {mood} Set',
    'Graceful {mood} Ensemble'
  ],
  'boys': [
    'Adventure {mood} Set',
    'Explorer {mood} Outfit',
    'Champion {mood} Collection',
    'Hero {mood} Ensemble',
    'Brave {mood} Set'
  ],
  'unisex': [
    '{mood} Wonder Collection',
    'Little {mood} Set',
    'Cozy {mood} Outfit',
    'Happy {mood} Ensemble',
    'Playful {mood} Collection'
  ],
  'summer': [
    'Sunshine {mood} Collection',
    'Beach {mood} Set',
    'Tropical {mood} Outfit',
    'Summer {mood} Ensemble'
  ],
  'winter': [
    'Cozy {mood} Collection',
    'Winter {mood} Set',
    'Warm {mood} Outfit',
    'Snowy {mood} Ensemble'
  ],
  'spring': [
    'Fresh {mood} Collection',
    'Bloom {mood} Set',
    'Garden {mood} Outfit',
    'Spring {mood} Ensemble'
  ],
  'autumn': [
    'Harvest {mood} Collection',
    'Autumn {mood} Set',
    'Golden {mood} Outfit',
    'Fall {mood} Ensemble'
  ]
}

// Price ranges based on category and subcategory
const priceRanges = {
  'tops': { min: 149, max: 299 },
  'bottoms': { min: 179, max: 349 },
  'dresses': { min: 249, max: 449 },
  'outerwear': { min: 399, max: 699 },
  'shoes': { min: 299, max: 499 },
  'accessories': { min: 99, max: 299 },
  'special-occasion': { min: 349, max: 599 },
  'premium-quality': { min: 449, max: 799 },
  'sale': { min: 99, max: 249 }
}

// Size options based on age group
const sizeOptions = {
  'baby': ['0-3M', '3-6M', '6-9M', '9-12M', '12-18M'],
  'toddler': ['12M', '18M', '2T', '3T'],
  'child': ['2T', '3T', '4T', '5T', '6T', '7T', '8T']
}

// Color palettes based on image colors and moods
const colorPalettes = {
  'feminine': ['Pink', 'Lavender', 'Mint', 'Peach', 'Cream'],
  'bold': ['Navy', 'Red', 'Royal Blue', 'Forest Green', 'Black'],
  'neutral': ['Beige', 'Grey', 'White', 'Taupe', 'Ivory'],
  'vibrant': ['Coral', 'Turquoise', 'Sunshine Yellow', 'Lime', 'Orange'],
  'earth-tones': ['Khaki', 'Olive', 'Rust', 'Camel', 'Chocolate'],
  'pastel': ['Baby Blue', 'Soft Pink', 'Pale Yellow', 'Light Green', 'Lilac']
}

// Material options based on product type and season
const materialOptions = {
  'summer': ['100% Cotton', '95% Cotton 5% Elastane', 'Linen Blend', 'Bamboo Cotton'],
  'winter': ['Wool Blend', 'Fleece', 'Cotton Flannel', 'Thermal Cotton'],
  'spring': ['Cotton Blend', 'Jersey Cotton', 'Organic Cotton', 'Cotton Poplin'],
  'autumn': ['Corduroy', 'Cotton Twill', 'Brushed Cotton', 'Cotton Knit'],
  'all-season': ['100% Cotton', 'Cotton Blend', 'Jersey Cotton', 'Organic Cotton']
}

function generateProductName(image: ImageMetadata): string {
  const templates = []
  
  // Add gender-specific templates
  if (image.gender && productNameTemplates[image.gender]) {
    templates.push(...productNameTemplates[image.gender])
  }
  
  // Add season-specific templates
  if (image.season && image.season !== 'all-season' && productNameTemplates[image.season]) {
    templates.push(...productNameTemplates[image.season])
  }
  
  // Fallback to unisex templates
  if (templates.length === 0) {
    templates.push(...productNameTemplates['unisex'])
  }
  
  const template = templates[Math.floor(Math.random() * templates.length)]
  const mood = image.mood[0] || 'Comfortable'
  
  return template.replace('{mood}', mood.charAt(0).toUpperCase() + mood.slice(1))
}

function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

function generatePrice(image: ImageMetadata): { price: number; compareAtPrice?: number } {
  const subcategory = image.subcategory || 'tops'
  const range = priceRanges[subcategory] || priceRanges['tops']
  
  const basePrice = Math.floor(Math.random() * (range.max - range.min + 1)) + range.min
  const price = Math.round(basePrice / 10) * 10 - 0.01 // Round to nearest 10 and subtract 1 cent
  
  // 30% chance of having a compare at price (sale item)
  const compareAtPrice = Math.random() < 0.3 
    ? Math.round(price * (1.2 + Math.random() * 0.3)) 
    : undefined
  
  return { price, compareAtPrice }
}

function generateVariants(image: ImageMetadata, basePrice: number): ProductVariant[] {
  const sizes = sizeOptions[image.ageGroup || 'child']
  const colorKey = image.colors[0] || 'neutral'
  const colors = colorPalettes[colorKey] || colorPalettes['neutral']
  
  const variants: ProductVariant[] = []
  let variantId = 1
  
  // Generate 3-5 size variants
  const selectedSizes = sizes.slice(0, 3 + Math.floor(Math.random() * 3))
  
  // Generate 2-3 color variants
  const selectedColors = colors.slice(0, 2 + Math.floor(Math.random() * 2))
  
  selectedSizes.forEach(size => {
    selectedColors.forEach(color => {
      variants.push({
        id: `v${variantId}`,
        size,
        color,
        sku: `COCO${String(variantId).padStart(3, '0')}-${size}-${color.replace(/\s+/g, '').toUpperCase()}`,
        price: basePrice,
        stock: 5 + Math.floor(Math.random() * 20) // 5-24 stock per variant
      })
      variantId++
    })
  })
  
  return variants
}

function generateMaterials(image: ImageMetadata): string[] {
  const season = image.season || 'all-season'
  const materials = materialOptions[season] || materialOptions['all-season']
  
  // Return 1-2 materials
  const count = 1 + Math.floor(Math.random() * 2)
  return materials.slice(0, count)
}

function generateCareInstructions(materials: string[]): string[] {
  const instructions = ['Machine wash cold', 'Tumble dry low']
  
  if (materials.some(m => m.includes('Wool'))) {
    return ['Hand wash cold', 'Lay flat to dry', 'Do not wring']
  }
  
  if (materials.some(m => m.includes('Delicate') || m.includes('Silk'))) {
    return ['Hand wash cold', 'Hang to dry', 'Iron on low heat']
  }
  
  instructions.push('Iron on medium heat')
  return instructions
}

export function generateProductFromImage(image: ImageMetadata, index: number): Product {
  const name = generateProductName(image)
  const slug = generateSlug(name)
  const { price, compareAtPrice } = generatePrice(image)
  const variants = generateVariants(image, price)
  const materials = generateMaterials(image)
  const careInstructions = generateCareInstructions(materials)
  
  // Determine category and subcategory
  let category = 'clothing'
  let subcategory = image.subcategory || 'tops'
  
  if (image.subcategory === 'shoes' || image.subcategory === 'sneakers') {
    category = 'footwear'
    subcategory = 'sneakers'
  } else if (image.subcategory === 'accessories' || image.subcategory === 'bags') {
    category = 'accessories'
    subcategory = image.subcategory
  }
  
  const product: Product = {
    id: `coco-${String(index + 1).padStart(3, '0')}`,
    name,
    slug,
    description: `${image.description} Crafted with attention to detail and designed for comfort, this piece embodies the Coco Milk Kids commitment to quality and style.`,
    shortDescription: image.description.split('.')[0],
    price,
    compareAtPrice,
    currency: 'ZAR',
    sku: `COCO${String(index + 1).padStart(3, '0')}`,
    category,
    subcategory,
    tags: [...image.tags, category, subcategory],
    images: [image.path],
    variants,
    sizes: [...new Set(variants.map(v => v.size))],
    colors: [...new Set(variants.map(v => v.color))],
    materials,
    careInstructions,
    ageGroup: image.ageGroup || 'child',
    gender: image.gender || 'unisex',
    season: image.season || 'all-season',
    isNew: Math.random() < 0.4, // 40% chance of being new
    isFeatured: Math.random() < 0.3, // 30% chance of being featured
    isOnSale: !!compareAtPrice, // On sale if has compare at price
    stock: variants.reduce((sum, v) => sum + v.stock, 0),
    weight: 0.2 + Math.random() * 0.6, // 0.2-0.8 kg
    dimensions: {
      length: 20 + Math.floor(Math.random() * 20),
      width: 15 + Math.floor(Math.random() * 15),
      height: 2 + Math.floor(Math.random() * 8)
    },
    seoTitle: `${name} | Premium Kids Fashion | Coco Milk Kids`,
    seoDescription: `${image.description.split('.')[0]}. Premium quality children's clothing designed for comfort and style.`,
    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(), // Random date within last 30 days
    updatedAt: new Date().toISOString()
  }
  
  return product
}

export function seedProducts(): Product[] {
  console.log('🌱 Starting product seeding...')
  
  // Filter images that are suitable for products
  const productImages = imageSeeds.filter(image => 
    image.category === 'product' || 
    image.category === 'collection' ||
    (image.category === 'editorial' && image.subcategory !== 'brand-essence')
  )
  
  console.log(`📸 Found ${productImages.length} suitable images for product generation`)
  
  const products = productImages.map((image, index) => {
    const product = generateProductFromImage(image, index)
    console.log(`✅ Generated product: ${product.name} (${product.id})`)
    return product
  })
  
  console.log(`🎉 Successfully generated ${products.length} products`)
  console.log(`💰 Price range: R${Math.min(...products.map(p => p.price))} - R${Math.max(...products.map(p => p.price))}`)
  console.log(`📦 Total stock: ${products.reduce((sum, p) => sum + p.stock, 0)} items`)
  console.log(`🆕 New products: ${products.filter(p => p.isNew).length}`)
  console.log(`⭐ Featured products: ${products.filter(p => p.isFeatured).length}`)
  console.log(`🏷️ Sale products: ${products.filter(p => p.isOnSale).length}`)
  
  return products
}
