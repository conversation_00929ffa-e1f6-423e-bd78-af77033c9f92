# Page Builder Seeder

This seeder creates a complete set of pages using the Page Builder system with all 15 custom blocks. It generates production-ready content for a children's clothing e-commerce website.

## 🚀 Quick Start

Run the seeder to create all pages:

```bash
pnpm run seed:pages
```

## 📄 Generated Pages

The seeder creates 11 complete pages with realistic content:

### 1. **Home Page** (`/`)
- **Hero Block**: Main banner with call-to-action
- **Special Offers Banner**: Promotional banner with urgency
- **Featured Categories**: 2x2 grid of product categories
- **Editorial Grid**: Asymmetric content layout
- **Product Grid**: Featured products showcase
- **Newsletter**: Subscription form

### 2. **About Page** (`/about`)
- **Story Section**: Company story with image
- **Values Grid**: 4 core company values
- **Mission Statement**: Highlighted mission content

### 3. **Contact Page** (`/contact`)
- **Contact Info**: Business details and hours
- **Contact Form**: Multi-field contact form

### 4. **FAQ Page** (`/faq`)
- **FAQ Accordion**: 6 common questions with search
- Categories: Products, Returns, Shipping, Materials, Care

### 5. **Brand Page** (`/brand`)
- **Brand Assets**: Logo, colors, typography showcase

### 6. **Help Page** (`/help`)
- **Help Topics Grid**: 6 help categories with article counts
- Search functionality and popular topics

### 7. **Privacy Policy** (`/privacy`)
- **Legal Content**: Structured privacy policy
- Table of contents and contact information

### 8. **Terms of Service** (`/terms`)
- **Legal Content**: Terms and conditions
- Structured sections with navigation

### 9. **Shipping Page** (`/shipping`)
- **Shipping Info Cards**: 4 shipping options
- Standard, Express, Overnight, Store Collection

### 10. **Stores Page** (`/stores`)
- **Store Locator**: 3 store locations
- Sandton City (Flagship), Canal Walk, Gateway

### 11. **Newsletter Page** (`/newsletter`)
- **Newsletter Benefits**: 6 subscription benefits
- **Newsletter Form**: Subscription with additional fields

## 🎨 Content Features

### Realistic Content
- South African context (ZAR currency, SA locations)
- Children's clothing industry focus
- Professional copywriting
- SEO-optimized metadata

### Visual Elements
- High-quality Unsplash images
- Consistent Zara-style design
- Proper image sizing and optimization
- Responsive layouts

### Interactive Features
- Search functionality in FAQ and Help
- Contact forms with validation
- Store locator with ratings
- Newsletter subscription

## 🔧 Technical Details

### Database Structure
- Creates `Page` records with proper metadata
- Creates `PageBlock` records with full configurations
- Maintains proper relationships and positioning
- Includes SEO settings for each page

### Block Types Used
All 15 custom blocks are utilized:
- `hero` - Hero sections
- `story-section` - About content
- `values-grid` - Company values
- `mission-statement` - Mission content
- `contact-info` - Contact details
- `contact-form` - Contact forms
- `faq-accordion` - FAQ sections
- `brand-assets` - Brand showcase
- `help-topics-grid` - Help navigation
- `legal-content` - Legal documents
- `shipping-info-cards` - Shipping options
- `store-locator` - Store finder
- `newsletter-benefits` - Subscription benefits
- `featured-categories` - Category grids
- `editorial-grid` - Editorial content
- `special-offers-banner` - Promotional banners
- `newsletter` - Newsletter forms
- `product-grid` - Product showcases

### Configuration Examples
Each block includes comprehensive configuration:
- Layout options (grid, list, cards)
- Styling options (colors, spacing, effects)
- Content arrays with realistic data
- Interactive features (search, forms)
- Responsive settings

## 🎯 Usage Instructions

### 1. Run the Seeder
```bash
pnpm run seed:pages
```

### 2. Access Pages
Visit the generated pages:
- Home: `http://localhost:3090/`
- About: `http://localhost:3090/about`
- Contact: `http://localhost:3090/contact`
- FAQ: `http://localhost:3090/faq`
- Brand: `http://localhost:3090/brand`
- Help: `http://localhost:3090/help`
- Privacy: `http://localhost:3090/privacy`
- Terms: `http://localhost:3090/terms`
- Shipping: `http://localhost:3090/shipping`
- Stores: `http://localhost:3090/stores`
- Newsletter: `http://localhost:3090/newsletter`

### 3. Manage Pages
Use the Page Builder admin:
- Admin: `http://localhost:3090/admin/page-builder`
- Edit any page content
- Add/remove blocks
- Customize configurations

### 4. Update Navigation
Update your navigation menus to link to the new pages:
```tsx
// Example navigation update
const navigation = [
  { name: 'Home', href: '/' },
  { name: 'About', href: '/about' },
  { name: 'Contact', href: '/contact' },
  { name: 'FAQ', href: '/faq' },
  { name: 'Help', href: '/help' },
  { name: 'Shipping', href: '/shipping' },
  { name: 'Stores', href: '/stores' },
]
```

## ⚠️ Important Notes

### Data Replacement
- **Clears existing pages**: The seeder removes all existing pages and blocks
- **Production data**: Use with caution in production environments
- **Backup recommended**: Backup your database before running

### Customization Required
- **Images**: Replace Unsplash URLs with your actual product images
- **Contact info**: Update phone numbers, addresses, and emails
- **Store locations**: Modify store information for your actual locations
- **Legal content**: Review and update privacy policy and terms

### SEO Optimization
- **Meta titles**: Customized for each page
- **Descriptions**: SEO-friendly descriptions
- **Keywords**: Relevant keyword targeting
- **Open Graph**: Social media optimization

## 🔄 Re-running the Seeder

To update or reset pages:

```bash
# Re-run the seeder
pnpm run seed:pages
```

This will:
1. Clear all existing pages and blocks
2. Create fresh pages with updated content
3. Maintain all block configurations
4. Preserve the Page Builder structure

## 📈 Next Steps

After running the seeder:

1. **Test all pages** in your browser
2. **Customize content** to match your brand
3. **Update images** with your actual assets
4. **Configure forms** to handle submissions
5. **Set up analytics** tracking
6. **Update navigation** menus
7. **Test responsive design** on mobile devices
8. **Review SEO** settings and metadata

## 🎉 Benefits

- **Instant website**: Complete website in seconds
- **Professional content**: Production-ready copy and structure
- **Best practices**: SEO, accessibility, and UX optimized
- **Customizable**: Easy to modify through Page Builder admin
- **Scalable**: Add more pages and blocks as needed
