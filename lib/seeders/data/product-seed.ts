/**
 * Coco Milk Kids Product Seed Data
 * 
 * This file contains comprehensive product data for the Coco Milk Kids e-commerce store.
 * Products are based on the available images and typical children's clothing categories.
 */

export interface Product {
  id: string
  name: string
  slug: string
  description: string
  shortDescription: string
  price: number
  compareAtPrice?: number
  currency: string
  sku: string
  category: string
  subcategory: string
  tags: string[]
  images: string[]
  variants: ProductVariant[]
  sizes: string[]
  colors: string[]
  materials: string[]
  careInstructions: string[]
  ageGroup: 'baby' | 'toddler' | 'child'
  gender: 'boys' | 'girls' | 'unisex'
  season: 'spring' | 'summer' | 'autumn' | 'winter' | 'all-season'
  isNew: boolean
  isFeatured: boolean
  isOnSale: boolean
  stock: number
  weight: number
  dimensions: {
    length: number
    width: number
    height: number
  }
  seoTitle: string
  seoDescription: string
  createdAt: string
  updatedAt: string
}

export interface ProductVariant {
  id: string
  size: string
  color: string
  sku: string
  price: number
  stock: number
  image?: string
}

export const productSeeds: Product[] = [
  {
    id: "coco-001",
    name: "Summer Breeze Cotton Dress",
    slug: "summer-breeze-cotton-dress",
    description: "A delightful summer dress crafted from premium organic cotton. Features a comfortable A-line silhouette with playful patterns that capture the essence of childhood joy. Perfect for warm days, playground adventures, and special occasions.",
    shortDescription: "Organic cotton summer dress with playful patterns",
    price: 299.99,
    compareAtPrice: 399.99,
    currency: "ZAR",
    sku: "COCODRESS001",
    category: "clothing",
    subcategory: "dresses",
    tags: ["summer", "cotton", "organic", "dress", "girls", "comfortable"],
    images: [
      "/assets/images/cocomilk_kids-20220927_125643-1359487094.jpg",
      "/assets/images/cocomilk_kids-20220819_100135-2187605151.jpg"
    ],
    variants: [
      { id: "v1", size: "2T", color: "Pink", sku: "COCODRESS001-2T-PINK", price: 299.99, stock: 15 },
      { id: "v2", size: "3T", color: "Pink", sku: "COCODRESS001-3T-PINK", price: 299.99, stock: 12 },
      { id: "v3", size: "4T", color: "Blue", sku: "COCODRESS001-4T-BLUE", price: 299.99, stock: 8 }
    ],
    sizes: ["2T", "3T", "4T", "5T", "6T"],
    colors: ["Pink", "Blue", "Yellow", "White"],
    materials: ["100% Organic Cotton"],
    careInstructions: ["Machine wash cold", "Tumble dry low", "Iron on low heat"],
    ageGroup: "child",
    gender: "girls",
    season: "summer",
    isNew: true,
    isFeatured: true,
    isOnSale: true,
    stock: 35,
    weight: 0.2,
    dimensions: { length: 25, width: 20, height: 2 },
    seoTitle: "Summer Breeze Cotton Dress for Girls | Coco Milk Kids",
    seoDescription: "Premium organic cotton summer dress for girls. Comfortable, stylish, and perfect for warm weather adventures.",
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-20T14:30:00Z"
  },
  {
    id: "coco-002",
    name: "Adventure Explorer Cargo Shorts",
    slug: "adventure-explorer-cargo-shorts",
    description: "Durable cargo shorts designed for active boys who love adventure. Multiple pockets for treasures, reinforced knees for playground action, and comfortable elastic waistband for all-day play.",
    shortDescription: "Durable cargo shorts with multiple pockets for active boys",
    price: 199.99,
    currency: "ZAR",
    sku: "COCOSHORTS001",
    category: "clothing",
    subcategory: "bottoms",
    tags: ["shorts", "cargo", "boys", "adventure", "durable", "pockets"],
    images: [
      "/assets/images/cocomilk_kids-20220921_110351-1857198841.jpg",
      "/assets/images/cocomilk_kids-20220822_112525-1393039322.jpg"
    ],
    variants: [
      { id: "v4", size: "3T", color: "Khaki", sku: "COCOSHORTS001-3T-KHAKI", price: 199.99, stock: 20 },
      { id: "v5", size: "4T", color: "Navy", sku: "COCOSHORTS001-4T-NAVY", price: 199.99, stock: 18 },
      { id: "v6", size: "5T", color: "Olive", sku: "COCOSHORTS001-5T-OLIVE", price: 199.99, stock: 15 }
    ],
    sizes: ["2T", "3T", "4T", "5T", "6T", "7T"],
    colors: ["Khaki", "Navy", "Olive", "Black"],
    materials: ["65% Cotton", "35% Polyester"],
    careInstructions: ["Machine wash warm", "Tumble dry medium", "Do not bleach"],
    ageGroup: "child",
    gender: "boys",
    season: "all-season",
    isNew: false,
    isFeatured: true,
    isOnSale: false,
    stock: 53,
    weight: 0.3,
    dimensions: { length: 30, width: 25, height: 3 },
    seoTitle: "Adventure Explorer Cargo Shorts for Boys | Coco Milk Kids",
    seoDescription: "Durable cargo shorts for active boys. Multiple pockets, reinforced design, perfect for playground adventures.",
    createdAt: "2024-01-10T09:00:00Z",
    updatedAt: "2024-01-18T11:15:00Z"
  },
  {
    id: "coco-003",
    name: "Cozy Cloud Knit Sweater",
    slug: "cozy-cloud-knit-sweater",
    description: "Ultra-soft knit sweater that feels like a warm hug. Made from premium merino wool blend, perfect for cooler days and layering. Features a classic crew neck design with ribbed cuffs and hem.",
    shortDescription: "Ultra-soft merino wool blend sweater for comfort and warmth",
    price: 449.99,
    compareAtPrice: 549.99,
    currency: "ZAR",
    sku: "COCOSWEATER001",
    category: "clothing",
    subcategory: "tops",
    tags: ["sweater", "knit", "wool", "cozy", "warm", "unisex"],
    images: [
      "/assets/images/cocomilk_kids-20220820_161754-1684440538.jpg",
      "/assets/images/cocomilk_kids-20221021_091054-2430450557.jpg"
    ],
    variants: [
      { id: "v7", size: "2T", color: "Cream", sku: "COCOSWEATER001-2T-CREAM", price: 449.99, stock: 10 },
      { id: "v8", size: "3T", color: "Grey", sku: "COCOSWEATER001-3T-GREY", price: 449.99, stock: 12 },
      { id: "v9", size: "4T", color: "Navy", sku: "COCOSWEATER001-4T-NAVY", price: 449.99, stock: 8 }
    ],
    sizes: ["2T", "3T", "4T", "5T", "6T"],
    colors: ["Cream", "Grey", "Navy", "Dusty Pink"],
    materials: ["70% Merino Wool", "30% Cotton"],
    careInstructions: ["Hand wash cold", "Lay flat to dry", "Do not wring"],
    ageGroup: "child",
    gender: "unisex",
    season: "autumn",
    isNew: true,
    isFeatured: false,
    isOnSale: true,
    stock: 30,
    weight: 0.4,
    dimensions: { length: 35, width: 30, height: 4 },
    seoTitle: "Cozy Cloud Knit Sweater | Premium Kids Knitwear | Coco Milk Kids",
    seoDescription: "Premium merino wool blend sweater for kids. Ultra-soft, warm, and perfect for layering in cooler weather.",
    createdAt: "2024-01-12T08:30:00Z",
    updatedAt: "2024-01-19T16:45:00Z"
  },
  {
    id: "coco-004",
    name: "Little Explorer Sneakers",
    slug: "little-explorer-sneakers",
    description: "Comfortable and durable sneakers designed for active little feet. Features breathable mesh panels, cushioned sole for all-day comfort, and easy velcro straps for independent dressing.",
    shortDescription: "Comfortable sneakers with velcro straps for active kids",
    price: 349.99,
    currency: "ZAR",
    sku: "COCOSHOES001",
    category: "footwear",
    subcategory: "sneakers",
    tags: ["sneakers", "shoes", "comfortable", "velcro", "active", "breathable"],
    images: [
      "/assets/images/cocomilk_kids-20220901_072641-574408837.jpg",
      "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"
    ],
    variants: [
      { id: "v10", size: "6", color: "White", sku: "COCOSHOES001-6-WHITE", price: 349.99, stock: 15 },
      { id: "v11", size: "7", color: "Navy", sku: "COCOSHOES001-7-NAVY", price: 349.99, stock: 12 },
      { id: "v12", size: "8", color: "Pink", sku: "COCOSHOES001-8-PINK", price: 349.99, stock: 10 }
    ],
    sizes: ["5", "6", "7", "8", "9", "10", "11", "12"],
    colors: ["White", "Navy", "Pink", "Black", "Grey"],
    materials: ["Synthetic Upper", "Rubber Sole", "Mesh Lining"],
    careInstructions: ["Wipe clean with damp cloth", "Air dry", "Do not machine wash"],
    ageGroup: "child",
    gender: "unisex",
    season: "all-season",
    isNew: false,
    isFeatured: true,
    isOnSale: false,
    stock: 37,
    weight: 0.5,
    dimensions: { length: 20, width: 15, height: 8 },
    seoTitle: "Little Explorer Sneakers for Kids | Comfortable Kids Shoes | Coco Milk Kids",
    seoDescription: "Comfortable and durable sneakers for active kids. Easy velcro straps, breathable design, perfect for everyday adventures.",
    createdAt: "2024-01-08T12:00:00Z",
    updatedAt: "2024-01-17T09:20:00Z"
  },
  {
    id: "coco-005",
    name: "Rainbow Adventure Backpack",
    slug: "rainbow-adventure-backpack",
    description: "Colorful and practical backpack perfect for school, day trips, or storing treasures. Features multiple compartments, padded straps for comfort, and water-resistant material.",
    shortDescription: "Colorful backpack with multiple compartments for kids",
    price: 249.99,
    compareAtPrice: 299.99,
    currency: "ZAR",
    sku: "COCOBAG001",
    category: "accessories",
    subcategory: "bags",
    tags: ["backpack", "school", "colorful", "practical", "water-resistant"],
    images: [
      "/assets/images/cocomilk_kids-20220824_102244-4040552385.jpg",
      "/assets/images/cocomilk_kids-20221005_080805-1368653969.jpg"
    ],
    variants: [
      { id: "v13", size: "One Size", color: "Rainbow", sku: "COCOBAG001-OS-RAINBOW", price: 249.99, stock: 25 },
      { id: "v14", size: "One Size", color: "Blue", sku: "COCOBAG001-OS-BLUE", price: 249.99, stock: 20 },
      { id: "v15", size: "One Size", color: "Pink", sku: "COCOBAG001-OS-PINK", price: 249.99, stock: 18 }
    ],
    sizes: ["One Size"],
    colors: ["Rainbow", "Blue", "Pink", "Green", "Purple"],
    materials: ["Water-resistant Polyester", "Padded Straps"],
    careInstructions: ["Spot clean only", "Air dry", "Do not machine wash"],
    ageGroup: "child",
    gender: "unisex",
    season: "all-season",
    isNew: true,
    isFeatured: false,
    isOnSale: true,
    stock: 63,
    weight: 0.6,
    dimensions: { length: 30, width: 20, height: 40 },
    seoTitle: "Rainbow Adventure Backpack for Kids | School Bags | Coco Milk Kids",
    seoDescription: "Colorful and practical backpack for kids. Water-resistant, multiple compartments, perfect for school and adventures.",
    createdAt: "2024-01-14T14:15:00Z",
    updatedAt: "2024-01-21T10:30:00Z"
  },
  {
    id: "coco-006",
    name: "Sunshine Playsuit",
    slug: "sunshine-playsuit",
    description: "Adorable one-piece playsuit perfect for summer adventures. Made from lightweight, breathable cotton with snap closures for easy dressing. Features cheerful prints that capture the joy of childhood.",
    shortDescription: "Lightweight cotton playsuit with cheerful summer prints",
    price: 179.99,
    currency: "ZAR",
    sku: "COCOPLAY001",
    category: "clothing",
    subcategory: "playsuits",
    tags: ["playsuit", "summer", "cotton", "lightweight", "cheerful", "easy-dressing"],
    images: [
      "/assets/images/cocomilk_kids-20210819_155000-1416316218.jpg",
      "/assets/images/cocomilk_kids-20220805_074556-1017553403.jpg"
    ],
    variants: [
      { id: "v16", size: "12M", color: "Yellow", sku: "COCOPLAY001-12M-YELLOW", price: 179.99, stock: 15 },
      { id: "v17", size: "18M", color: "Coral", sku: "COCOPLAY001-18M-CORAL", price: 179.99, stock: 12 },
      { id: "v18", size: "2T", color: "Mint", sku: "COCOPLAY001-2T-MINT", price: 179.99, stock: 10 }
    ],
    sizes: ["6M", "12M", "18M", "2T", "3T"],
    colors: ["Yellow", "Coral", "Mint", "White", "Lavender"],
    materials: ["100% Cotton"],
    careInstructions: ["Machine wash cold", "Tumble dry low", "Iron on medium"],
    ageGroup: "toddler",
    gender: "unisex",
    season: "summer",
    isNew: true,
    isFeatured: true,
    isOnSale: false,
    stock: 37,
    weight: 0.15,
    dimensions: { length: 20, width: 15, height: 2 },
    seoTitle: "Sunshine Playsuit for Toddlers | Summer Baby Clothes | Coco Milk Kids",
    seoDescription: "Adorable cotton playsuit for toddlers. Lightweight, breathable, with cheerful prints perfect for summer play.",
    createdAt: "2024-01-16T11:45:00Z",
    updatedAt: "2024-01-22T15:20:00Z"
  },
  {
    id: "coco-007",
    name: "Winter Wonderland Coat",
    slug: "winter-wonderland-coat",
    description: "Warm and stylish winter coat designed to keep little ones cozy during cold weather. Features water-resistant outer shell, soft fleece lining, and practical hood with faux fur trim.",
    shortDescription: "Warm winter coat with fleece lining and hood",
    price: 599.99,
    compareAtPrice: 749.99,
    currency: "ZAR",
    sku: "COCOCOAT001",
    category: "clothing",
    subcategory: "outerwear",
    tags: ["coat", "winter", "warm", "water-resistant", "hood", "fleece"],
    images: [
      "/assets/images/cocomilk_kids-20221216_070828-1200554041.jpg",
      "/assets/images/cocomilk_kids-20221021_091054-2430450557.jpg"
    ],
    variants: [
      { id: "v19", size: "2T", color: "Navy", sku: "COCOCOAT001-2T-NAVY", price: 599.99, stock: 8 },
      { id: "v20", size: "3T", color: "Pink", sku: "COCOCOAT001-3T-PINK", price: 599.99, stock: 6 },
      { id: "v21", size: "4T", color: "Grey", sku: "COCOCOAT001-4T-GREY", price: 599.99, stock: 5 }
    ],
    sizes: ["2T", "3T", "4T", "5T", "6T"],
    colors: ["Navy", "Pink", "Grey", "Black", "Burgundy"],
    materials: ["Water-resistant Polyester", "Fleece Lining", "Faux Fur Trim"],
    careInstructions: ["Machine wash cold", "Tumble dry low", "Do not iron trim"],
    ageGroup: "child",
    gender: "unisex",
    season: "winter",
    isNew: false,
    isFeatured: true,
    isOnSale: true,
    stock: 19,
    weight: 0.8,
    dimensions: { length: 40, width: 35, height: 5 },
    seoTitle: "Winter Wonderland Coat for Kids | Warm Winter Jackets | Coco Milk Kids",
    seoDescription: "Warm and stylish winter coat for kids. Water-resistant, fleece-lined, with hood for ultimate cold weather protection.",
    createdAt: "2024-01-05T10:30:00Z",
    updatedAt: "2024-01-20T13:45:00Z"
  },
  {
    id: "coco-008",
    name: "Holiday Magic Dress",
    slug: "holiday-magic-dress",
    description: "Elegant dress perfect for special occasions and holiday celebrations. Features sparkly details, comfortable fit, and timeless design that makes every little girl feel like a princess.",
    shortDescription: "Elegant holiday dress with sparkly details for special occasions",
    price: 399.99,
    currency: "ZAR",
    sku: "COCOHOLIDAY001",
    category: "clothing",
    subcategory: "special-occasion",
    tags: ["dress", "holiday", "special-occasion", "elegant", "sparkly", "princess"],
    images: [
      "/assets/images/cocomilk_kids-20221221_105615-2775996687.jpg",
      "/assets/images/cocomilk_kids-20210903_003540-4185833907.jpg"
    ],
    variants: [
      { id: "v22", size: "2T", color: "Gold", sku: "COCOHOLIDAY001-2T-GOLD", price: 399.99, stock: 12 },
      { id: "v23", size: "3T", color: "Silver", sku: "COCOHOLIDAY001-3T-SILVER", price: 399.99, stock: 10 },
      { id: "v24", size: "4T", color: "Rose Gold", sku: "COCOHOLIDAY001-4T-ROSEGOLD", price: 399.99, stock: 8 }
    ],
    sizes: ["2T", "3T", "4T", "5T", "6T"],
    colors: ["Gold", "Silver", "Rose Gold", "Burgundy", "Navy"],
    materials: ["Polyester", "Tulle", "Sequin Details"],
    careInstructions: ["Hand wash cold", "Lay flat to dry", "Do not iron sequins"],
    ageGroup: "child",
    gender: "girls",
    season: "all-season",
    isNew: true,
    isFeatured: false,
    isOnSale: false,
    stock: 30,
    weight: 0.3,
    dimensions: { length: 30, width: 25, height: 3 },
    seoTitle: "Holiday Magic Dress for Girls | Special Occasion Dresses | Coco Milk Kids",
    seoDescription: "Elegant holiday dress for girls with sparkly details. Perfect for special occasions and celebrations.",
    createdAt: "2024-01-18T09:15:00Z",
    updatedAt: "2024-01-23T11:30:00Z"
  },
  {
    id: "coco-009",
    name: "Fresh Start Spring Cardigan",
    slug: "fresh-start-spring-cardigan",
    description: "Light and airy cardigan perfect for spring layering. Made from soft cotton blend with delicate button details and comfortable fit for transitional weather.",
    shortDescription: "Light cotton blend cardigan perfect for spring layering",
    price: 279.99,
    currency: "ZAR",
    sku: "COCOCARD001",
    category: "clothing",
    subcategory: "cardigans",
    tags: ["cardigan", "spring", "layering", "cotton", "light", "buttons"],
    images: [
      "/assets/images/cocomilk_kids-20220829_111232-3780725228.jpg",
      "/assets/images/cocomilk_kids-20230112_110126-3728995334.jpg"
    ],
    variants: [
      { id: "v25", size: "2T", color: "Mint", sku: "COCOCARD001-2T-MINT", price: 279.99, stock: 15 },
      { id: "v26", size: "3T", color: "Peach", sku: "COCOCARD001-3T-PEACH", price: 279.99, stock: 12 },
      { id: "v27", size: "4T", color: "Lavender", sku: "COCOCARD001-4T-LAVENDER", price: 279.99, stock: 10 }
    ],
    sizes: ["2T", "3T", "4T", "5T", "6T"],
    colors: ["Mint", "Peach", "Lavender", "Cream", "Light Blue"],
    materials: ["60% Cotton", "40% Acrylic"],
    careInstructions: ["Machine wash cold", "Tumble dry low", "Iron on low heat"],
    ageGroup: "child",
    gender: "unisex",
    season: "spring",
    isNew: true,
    isFeatured: true,
    isOnSale: false,
    stock: 37,
    weight: 0.25,
    dimensions: { length: 32, width: 28, height: 3 },
    seoTitle: "Fresh Start Spring Cardigan | Kids Layering Pieces | Coco Milk Kids",
    seoDescription: "Light cotton blend cardigan for kids. Perfect for spring layering with comfortable fit and delicate details.",
    createdAt: "2024-01-19T14:20:00Z",
    updatedAt: "2024-01-24T16:10:00Z"
  }
]

// Helper functions for working with product data
export const getProductsByCategory = (category: string) => {
  return productSeeds.filter(product => product.category === category)
}

export const getProductsBySubcategory = (subcategory: string) => {
  return productSeeds.filter(product => product.subcategory === subcategory)
}

export const getProductsByGender = (gender: Product['gender']) => {
  return productSeeds.filter(product => product.gender === gender || product.gender === 'unisex')
}

export const getProductsBySeason = (season: Product['season']) => {
  return productSeeds.filter(product => product.season === season || product.season === 'all-season')
}

export const getProductsByAgeGroup = (ageGroup: Product['ageGroup']) => {
  return productSeeds.filter(product => product.ageGroup === ageGroup)
}

export const getNewProducts = () => {
  return productSeeds.filter(product => product.isNew)
}

export const getFeaturedProducts = () => {
  return productSeeds.filter(product => product.isFeatured)
}

export const getSaleProducts = () => {
  return productSeeds.filter(product => product.isOnSale)
}

export const getProductsByPriceRange = (minPrice: number, maxPrice: number) => {
  return productSeeds.filter(product => product.price >= minPrice && product.price <= maxPrice)
}

export const getProductsByTag = (tag: string) => {
  return productSeeds.filter(product => product.tags.includes(tag))
}

export const getProductById = (id: string) => {
  return productSeeds.find(product => product.id === id)
}

export const getProductBySlug = (slug: string) => {
  return productSeeds.find(product => product.slug === slug)
}

export const getRandomProducts = (count: number = 4) => {
  const shuffled = [...productSeeds].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

export const getRelatedProducts = (productId: string, count: number = 4) => {
  const product = getProductById(productId)
  if (!product) return []

  const related = productSeeds.filter(p =>
    p.id !== productId &&
    (p.category === product.category ||
     p.subcategory === product.subcategory ||
     p.gender === product.gender ||
     p.ageGroup === product.ageGroup)
  )

  return related.slice(0, count)
}

export const searchProducts = (query: string) => {
  const lowercaseQuery = query.toLowerCase()
  return productSeeds.filter(product =>
    product.name.toLowerCase().includes(lowercaseQuery) ||
    product.description.toLowerCase().includes(lowercaseQuery) ||
    product.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
    product.category.toLowerCase().includes(lowercaseQuery) ||
    product.subcategory.toLowerCase().includes(lowercaseQuery)
  )
}

// Product statistics
export const getProductStats = () => {
  return {
    total: productSeeds.length,
    categories: [...new Set(productSeeds.map(p => p.category))].length,
    subcategories: [...new Set(productSeeds.map(p => p.subcategory))].length,
    newProducts: getNewProducts().length,
    featuredProducts: getFeaturedProducts().length,
    saleProducts: getSaleProducts().length,
    averagePrice: productSeeds.reduce((sum, p) => sum + p.price, 0) / productSeeds.length,
    priceRange: {
      min: Math.min(...productSeeds.map(p => p.price)),
      max: Math.max(...productSeeds.map(p => p.price))
    },
    totalStock: productSeeds.reduce((sum, p) => sum + p.stock, 0)
  }
}

// Category and filter options
export const getCategories = () => {
  return [...new Set(productSeeds.map(p => p.category))]
}

export const getSubcategories = () => {
  return [...new Set(productSeeds.map(p => p.subcategory))]
}

export const getAllSizes = () => {
  return [...new Set(productSeeds.flatMap(p => p.sizes))]
}

export const getAllColors = () => {
  return [...new Set(productSeeds.flatMap(p => p.colors))]
}

export const getAllTags = () => {
  return [...new Set(productSeeds.flatMap(p => p.tags))]
}

export const getAllMaterials = () => {
  return [...new Set(productSeeds.flatMap(p => p.materials))]
}

// Quick access functions
export const getClothingProducts = () => getProductsByCategory('clothing')
export const getFootwearProducts = () => getProductsByCategory('footwear')
export const getAccessoryProducts = () => getProductsByCategory('accessories')
export const getBoysProducts = () => getProductsByGender('boys')
export const getGirlsProducts = () => getProductsByGender('girls')
export const getUnisexProducts = () => getProductsByGender('unisex')
export const getBabyProducts = () => getProductsByAgeGroup('baby')
export const getToddlerProducts = () => getProductsByAgeGroup('toddler')
export const getChildProducts = () => getProductsByAgeGroup('child')
