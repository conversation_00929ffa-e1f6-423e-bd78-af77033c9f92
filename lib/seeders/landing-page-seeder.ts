import { prisma } from '@/lib/prisma'
import { SiteSettingsService } from '@/lib/site-settings/site-settings-service'

/**
 * Seeds the dynamic landing page that replicates the hardcoded landing page exactly
 * This will become the new homepage using the page builder system
 */
export async function seedLandingPage() {
  console.log('🌱 Seeding dynamic landing page...')

  try {
    // Check if landing page already exists
    const existingPage = await prisma.page.findUnique({
      where: { slug: 'home' },
      include: {
        blocks: true
      }
    })

    if (existingPage) {
      console.log('✅ Landing page already exists')

      // Check if it has blocks
      if (existingPage.blocks.length === 0) {
        console.log('🔄 Page has no blocks, adding them...')
        // Delete the existing page and recreate it with blocks
        await prisma.page.delete({
          where: { id: existingPage.id }
        })
        console.log('🗑️ Deleted existing empty page')
      } else {
        console.log(`✅ Page already has ${existingPage.blocks.length} blocks, setting as homepage...`)
        // Set this page as the homepage
        const siteSettingsService = new SiteSettingsService()
        await siteSettingsService.setHomepage(existingPage.id)
        console.log('✅ Set existing page as homepage')
        return existingPage
      }
    }

    // Create the dynamic landing page
    const landingPage = await prisma.page.create({
      data: {
        title: 'Coco Milk Kids - Premium Children\'s Clothing',
        slug: 'home',
        description: 'Discover our premium collection of children\'s clothing designed for comfort, style, and adventure.',
        status: 'published',
        type: 'home',
        seoTitle: 'Coco Milk Kids - Premium Children\'s Clothing & Accessories',
        seoDescription: 'Shop premium children\'s clothing at Coco Milk Kids. Discover our latest collections of stylish and comfortable kids\' fashion.',
        seoKeywords: ['kids clothing', 'children fashion', 'premium kids wear', 'comfortable kids clothes'],
        isHomePage: true,
        requiresAuth: false,
        allowComments: false,
        metadata: {
          title: 'Coco Milk Kids - Premium Children\'s Clothing',
          description: 'Discover our premium collection of children\'s clothing designed for comfort, style, and adventure.',
          seoTitle: 'Coco Milk Kids - Premium Children\'s Clothing & Accessories',
          seoDescription: 'Shop premium children\'s clothing at Coco Milk Kids. Discover our latest collections of stylish and comfortable kids\' fashion.',
          requiresAuth: false,
          allowComments: false
        }
      }
    })

    console.log(`✅ Created landing page: ${landingPage.title}`)

    // Create all the blocks that replicate the hardcoded landing page
    const blocks = [
      // 1. Hero Section Block - exactly like HeroSection component
      {
        pageId: landingPage.id,
        blockType: 'hero-section',
        position: 0,
        isVisible: true,
        configuration: {
          title: 'KIDS\nCOLLECTION',
          backgroundImage: '/assets/images/cocomilk_kids-20210912_114630-3065525289.jpg',
          overlay: { enabled: true, color: '#000000', opacity: 0.2 },
          ctaButton: { text: 'SHOP NOW', url: '/products', style: 'minimal' },
          contentPosition: 'bottom-left',
          height: 'viewport',
          textColor: '#ffffff',
          animation: { enabled: true, type: 'fade', duration: 1, delay: 0 },
          style: 'zara'
        },
        content: {},
        styling: {},
        responsive: {},
        animation: {},
        conditions: {}
      },

      // 2. Featured Categories Block - exactly like FeaturedCategories component
      {
        pageId: landingPage.id,
        blockType: 'featured-categories',
        position: 1,
        isVisible: true,
        configuration: {
          categories: [
            {
              name: 'Girls Collection',
              subtitle: 'Stylish & Comfortable',
              image: '/assets/images/cocomilk_kids-20220819_100135-2187605151.jpg',
              link: '/products?category=girls',
              overlay: { enabled: false, color: '#000000', opacity: 0 }
            },
            {
              name: 'Boys Collection',
              subtitle: 'Adventure Ready',
              image: '/assets/images/cocomilk_kids-20220822_112525-1393039322.jpg',
              link: '/products?category=boys',
              overlay: { enabled: false, color: '#000000', opacity: 0 }
            },
            {
              name: 'New Arrivals',
              subtitle: 'Latest Trends',
              image: '/assets/images/cocomilk_kids-20221028_102959-387306553.jpg',
              link: '/products?category=new-arrivals',
              overlay: { enabled: false, color: '#000000', opacity: 0 }
            },
            {
              name: 'Sale',
              subtitle: 'Up to 50% Off',
              image: '/assets/images/cocomilk_kids-20220912_082247-3005247592.jpg',
              link: '/products?category=sale',
              overlay: { enabled: false, color: '#000000', opacity: 0 }
            }
          ],
          layout: '2x2',
          aspectRatio: 'square',
          gap: 'medium',
          borderRadius: 'none',
          hoverEffect: 'zoom',
          textStyle: 'minimal',
          spacing: 'normal',
          backgroundColor: 'transparent',
          showDescriptions: true
        },
        content: {},
        styling: {},
        responsive: {},
        animation: {},
        conditions: {}
      },

      // 3. New Arrivals Block - exactly like NewArrivalsSection component
      {
        pageId: landingPage.id,
        blockType: 'new-arrivals',
        position: 2,
        isVisible: true,
        configuration: {
          title: 'NEW IN',
          limit: 6,
          showViewAllLink: true,
          viewAllText: 'VIEW ALL NEW IN',
          viewAllUrl: '/collections/new-arrivals',
          layout: 'grid',
          columns: { desktop: 3, tablet: 2, mobile: 2 },
          spacing: 'normal',
          style: 'zara'
        },
        content: {},
        styling: {},
        responsive: {},
        animation: {},
        conditions: {}
      },

      // 4. Editorial Grid Block - exactly like EditorialSection component
      {
        pageId: landingPage.id,
        blockType: 'editorial-grid',
        position: 3,
        isVisible: true,
        configuration: {
          items: [
            {
              title: 'Summer Collection',
              subtitle: 'Light & Breezy',
              description: 'Perfect for warm weather adventures',
              image: '/assets/images/cocomilk_kids-20220819_100135-2187605151.jpg',
              link: '/collections/summer',
              buttonText: 'Explore',
              size: 'large'
            },
            {
              title: 'Comfort First',
              subtitle: 'All Day Play',
              description: 'Designed for active kids',
              image: '/assets/images/cocomilk_kids-20220822_112525-1393039322.jpg',
              link: '/collections/comfort',
              buttonText: 'Shop Now',
              size: 'medium'
            },
            {
              title: 'Style Guide',
              subtitle: 'Mix & Match',
              description: 'Create perfect outfits',
              image: '/assets/images/cocomilk_kids-20221028_102959-387306553.jpg',
              link: '/style-guide',
              buttonText: 'Learn More',
              size: 'medium'
            }
          ],
          layout: 'asymmetric',
          aspectRatio: 'mixed',
          gap: 'medium',
          borderRadius: 'none',
          hoverEffect: 'zoom',
          textStyle: 'minimal',
          spacing: 'normal',
          backgroundColor: 'transparent',
          showButtons: true,
          buttonStyle: 'outline'
        },
        content: {},
        styling: {},
        responsive: {},
        animation: {},
        conditions: {}
      },

      // 5. Editorial Section Block - exactly like EditorialSection component
      {
        pageId: landingPage.id,
        blockType: 'editorial-section',
        position: 4,
        isVisible: true,
        configuration: {
          title: '',
          subtitle: '',
          items: [
            {
              id: 'spring-essentials',
              title: 'SPRING ESSENTIALS',
              description: 'Discover the season\'s must-have pieces',
              image: '/assets/images/cocomilk_kids-20220829_111232-3780725228.jpg',
              link: '/collections/editorial',
              size: 'large'
            },
            {
              id: 'accessories',
              title: 'ACCESSORIES',
              description: 'Complete the look',
              image: '/assets/images/cocomilk_kids-20220824_102244-4040552385.jpg',
              link: '/collections/accessories',
              size: 'small'
            },
            {
              id: 'shoes',
              title: 'SHOES',
              description: 'Step into style',
              image: '/assets/images/cocomilk_kids-20220901_072641-574408837.jpg',
              link: '/collections/shoes',
              size: 'small'
            }
          ],
          layout: 'asymmetric',
          style: 'zara',
          spacing: 'normal',
          animation: { enabled: true, type: 'slide', stagger: 0.1 },
          backgroundColor: '#ffffff'
        },
        content: {},
        styling: {},
        responsive: {},
        animation: {},
        conditions: {}
      },

      // 6. Special Offers Banner Block - exactly like SpecialOffersBanner component
      {
        pageId: landingPage.id,
        blockType: 'special-offers-banner',
        position: 5,
        isVisible: true,
        configuration: {
          title: 'SALE',
          subtitle: '',
          description: 'Up to 50% off selected items',
          buttonText: 'SHOP SALE',
          buttonLink: '/collections/sale',
          image: {
            url: '/assets/images/cocomilk_kids-20220906_082318-3283935526.jpg',
            alt: 'Sale collection'
          },
          layout: 'split',
          style: 'zara',
          backgroundColor: '#000000',
          textColor: '#ffffff',
          spacing: 'normal',
          animation: { enabled: true, type: 'slide', stagger: 0.1 }
        },
        content: {},
        styling: {},
        responsive: {},
        animation: {},
        conditions: {}
      },

      // 7. Newsletter Signup Block - exactly like NewsletterSignup component
      {
        pageId: landingPage.id,
        blockType: 'newsletter-signup',
        position: 6,
        isVisible: true,
        configuration: {
          title: 'NEWSLETTER',
          subtitle: '',
          description: 'Subscribe to receive updates on new arrivals and exclusive offers',
          placeholder: 'Email address',
          buttonText: 'SUBSCRIBE',
          successMessage: 'Thank you for subscribing',
          style: 'zara',
          layout: 'vertical',
          backgroundColor: '#f9fafb',
          textColor: '#000000',
          showIcon: true,
          animation: { enabled: true, type: 'fade' },
          privacy: {
            enabled: true,
            text: 'By subscribing, you agree to our',
            linkText: 'Privacy Policy',
            linkUrl: '/privacy-policy'
          }
        },
        content: {},
        styling: {},
        responsive: {},
        animation: {},
        conditions: {}
      }
    ]

    // Create all blocks
    for (const blockData of blocks) {
      await prisma.pageBlock.create({ data: blockData })
    }

    console.log(`✅ Created ${blocks.length} blocks for landing page`)

    // Set this page as the homepage
    const siteSettingsService = new SiteSettingsService()
    await siteSettingsService.setHomepage(landingPage.id)

    console.log('✅ Set dynamic landing page as homepage')
    console.log('🎉 Landing page seeding completed successfully!')

    return landingPage

  } catch (error) {
    console.error('❌ Error seeding landing page:', error)
    throw error
  }
}

// Export for use in other seeders
export default seedLandingPage
