import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function seedPageBuilderPages() {
  console.log('🌱 Starting Page Builder seeding...')

  try {
    // Clear existing pages and blocks
    await prisma.pageBlock.deleteMany()
    await prisma.page.deleteMany()

    console.log('🗑️ Cleared existing pages and blocks')

    // Create Home Page
    await createHomePage()

    // Create About Page
    await createAboutPage()

    // Create Contact Page
    await createContactPage()

    console.log('✅ Page Builder seeding completed successfully!')

  } catch (error) {
    console.error('❌ Error seeding Page Builder pages:', error)
    throw error
  }
}

async function createHomePage() {
  console.log('📄 Creating Home page...')
  
  const page = await prisma.page.create({
    data: {
      title: 'Home',
      slug: 'home',
      status: 'published',
      type: 'custom',
      seoTitle: 'Coco Milk Kids - Premium Children\'s Clothing',
      seoDescription: 'Discover comfortable, stylish clothing for children. Premium quality, sustainable materials, and designs that let kids be kids.',
      seoKeywords: ['children clothing', 'kids fashion', 'comfortable clothes', 'sustainable kids wear'],
      ogImage: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?q=80&w=1200&h=630&auto=format&fit=crop',
      isHomePage: true,
      publishedAt: new Date()
    }
  })

  // Hero Block
  await prisma.pageBlock.create({
    data: {
      pageId: page.id,
      blockType: 'hero',
      position: 0,
      configuration: {
        title: 'Comfort Meets Style',
        subtitle: 'Premium children\'s clothing designed for every adventure',
        description: 'Discover our collection of comfortable, stylish clothing that lets kids be kids while looking their best.',
        backgroundImage: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?q=80&w=1920&h=1080&auto=format&fit=crop',
        ctaText: 'Shop Collection',
        ctaLink: '/collections/all',
        secondaryCtaText: 'Learn More',
        secondaryCtaLink: '/about',
        alignment: 'center',
        textColor: '#ffffff',
        overlayOpacity: 40
      }
    }
  })

  // Featured Categories Block
  await prisma.pageBlock.create({
    data: {
      pageId: page.id,
      blockType: 'featured-categories',
      position: 1,
      configuration: {
        categories: [
          {
            id: 'girls',
            name: 'Girls',
            description: 'Stylish and comfortable clothing for girls',
            image: 'https://images.unsplash.com/photo-1518831959646-742c3a14ebf7?q=80&w=800&h=800&auto=format&fit=crop',
            link: '/collections/girls',
            textPosition: 'bottom-left',
            size: 'small',
            overlay: { enabled: true, color: '#000000', opacity: 20 }
          },
          {
            id: 'boys',
            name: 'Boys',
            description: 'Cool and comfortable styles for boys',
            image: 'https://images.unsplash.com/photo-1503919545889-aef636e10ad4?q=80&w=800&h=800&auto=format&fit=crop',
            link: '/collections/boys',
            textPosition: 'bottom-left',
            size: 'small',
            overlay: { enabled: true, color: '#000000', opacity: 20 }
          }
        ],
        layout: '2x2',
        aspectRatio: 'square',
        gap: 'small',
        hoverEffect: 'zoom'
      }
    }
  })

  // Newsletter Block
  await prisma.pageBlock.create({
    data: {
      pageId: page.id,
      blockType: 'newsletter',
      position: 2,
      configuration: {
        title: 'Stay in the Loop',
        description: 'Get the latest updates on new arrivals, exclusive offers, and parenting tips.',
        placeholder: 'Enter your email address',
        buttonText: 'Subscribe',
        showPrivacyNote: true,
        privacyText: 'We respect your privacy. Unsubscribe at any time.',
        backgroundColor: '#f9fafb'
      }
    }
  })

  console.log('✅ Home page created')
}

async function createAboutPage() {
  console.log('📄 Creating About page...')

  const page = await prisma.page.create({
    data: {
      title: 'About Us',
      slug: 'about',
      status: 'published',
      type: 'custom',
      seoTitle: 'About Coco Milk Kids - Our Story',
      seoDescription: 'Learn about our mission to create comfortable, stylish clothing for children with sustainable materials and ethical production.',
      seoKeywords: ['about coco milk kids', 'children clothing company', 'sustainable kids fashion', 'our story'],
      publishedAt: new Date()
    }
  })

  // Story Section Block
  await prisma.pageBlock.create({
    data: {
      pageId: page.id,
      blockType: 'story-section',
      position: 0,
      configuration: {
        title: 'Our Story',
        content: [
          'Founded in the heart of Johannesburg with a simple belief that South African children deserve clothing that\'s as comfortable as it is stylish, Coco Milk Kids was born from the idea that kids should be free to play, explore, and express themselves without compromise.',
          'Our journey began when we noticed a gap in the South African market for high-quality, oversized children\'s clothing that could withstand our diverse climate while prioritizing both comfort and contemporary design.',
          'Today, we\'re proud to be a proudly South African brand offering a carefully curated collection of premium children\'s clothing that celebrates individuality while ensuring every little South African feels comfortable and confident.'
        ],
        image: {
          src: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?q=80&w=800&h=800&auto=format&fit=crop',
          alt: 'Children playing in comfortable clothing'
        },
        layout: 'image-right',
        imageAspect: 'square'
      }
    }
  })

  // Values Grid Block
  await prisma.pageBlock.create({
    data: {
      pageId: page.id,
      blockType: 'values-grid',
      position: 1,
      configuration: {
        title: 'Our Values',
        values: [
          {
            icon: 'heart',
            title: 'Comfort First',
            description: 'Every piece is designed with your child\'s comfort in mind, using soft, breathable fabrics.'
          },
          {
            icon: 'shield',
            title: 'Quality Promise',
            description: 'Premium materials and expert craftsmanship ensure our clothes last through all adventures.'
          },
          {
            icon: 'leaf',
            title: 'Sustainable Choice',
            description: 'Environmentally conscious production methods and materials for a better tomorrow.'
          },
          {
            icon: 'users',
            title: 'Family Focused',
            description: 'Designed by parents, for parents who want the best for their children.'
          }
        ],
        columns: 4,
        iconStyle: 'circle'
      }
    }
  })

  // Mission Statement Block
  await prisma.pageBlock.create({
    data: {
      pageId: page.id,
      blockType: 'mission-statement',
      position: 2,
      configuration: {
        title: 'Our Mission',
        content: 'To create clothing that empowers children to be themselves while providing parents with the peace of mind that comes from knowing their kids are wearing high-quality, comfortable, and stylish pieces that will last through all of life\'s adventures.',
        alignment: 'center',
        backgroundStyle: 'light',
        padding: 'normal'
      }
    }
  })

  console.log('✅ About page created')
}

async function createContactPage() {
  console.log('📄 Creating Contact page...')

  const page = await prisma.page.create({
    data: {
      title: 'Contact Us',
      slug: 'contact',
      status: 'published',
      type: 'custom',
      seoTitle: 'Contact Coco Milk Kids',
      seoDescription: 'Get in touch with our team. We\'re here to help with any questions about our products or services.',
      seoKeywords: ['contact coco milk kids', 'customer service', 'support', 'get in touch'],
      publishedAt: new Date()
    }
  })

  // Contact Info Block
  await prisma.pageBlock.create({
    data: {
      pageId: page.id,
      blockType: 'contact-info',
      position: 0,
      configuration: {
        title: 'Get in Touch',
        description: 'We\'d love to hear from you. Send us a message and we\'ll respond as soon as possible.',
        contacts: [
          {
            type: 'email',
            label: 'Email',
            value: '<EMAIL>',
            link: '<EMAIL>'
          },
          {
            type: 'phone',
            label: 'Phone',
            value: '+27 11 123 4567',
            link: '+***********'
          },
          {
            type: 'address',
            label: 'Address',
            value: '123 Sandton Drive, Sandton, Johannesburg 2196, South Africa'
          },
          {
            type: 'hours',
            label: 'Business Hours',
            value: 'Monday - Friday: 9:00 AM - 5:00 PM'
          }
        ],
        layout: 'vertical',
        cardStyle: true
      }
    }
  })

  // Contact Form Block
  await prisma.pageBlock.create({
    data: {
      pageId: page.id,
      blockType: 'contact-form',
      position: 1,
      configuration: {
        title: 'Send us a Message',
        description: 'Fill out the form below and we\'ll get back to you as soon as possible.',
        fields: [
          {
            name: 'name',
            label: 'Name',
            type: 'text',
            required: true,
            placeholder: 'Your full name',
            width: 'half'
          },
          {
            name: 'email',
            label: 'Email',
            type: 'email',
            required: true,
            placeholder: '<EMAIL>',
            width: 'half'
          },
          {
            name: 'subject',
            label: 'Subject',
            type: 'select',
            required: true,
            options: [
              'General Inquiry',
              'Product Question',
              'Order Support',
              'Returns & Exchanges',
              'Wholesale Inquiry',
              'Press & Media',
              'Other'
            ],
            width: 'full'
          },
          {
            name: 'message',
            label: 'Message',
            type: 'textarea',
            required: true,
            placeholder: 'Tell us how we can help you...',
            width: 'full'
          }
        ],
        submitButtonText: 'Send Message',
        cardStyle: true
      }
    }
  })

  console.log('✅ Contact page created')
}
