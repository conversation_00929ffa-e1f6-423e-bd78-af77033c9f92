/**
 * Notification System Test Script
 * 
 * Tests the core functionality of the notification system
 */

import { notificationManager } from './manager'
import { NotificationType, NotificationChannel, NotificationPriority } from './types'

async function testNotificationSystem() {
  console.log('🧪 Testing Notification System...')

  try {
    // Test 1: Send a simple email notification
    console.log('\n📧 Test 1: Sending email notification...')
    const emailResult = await notificationManager.send({
      type: NotificationType.WELCOME,
      channel: NotificationChannel.EMAIL,
      title: 'Welcome to Coco Milk Kids!',
      content: 'Thank you for joining our family!',
      recipientEmail: '<EMAIL>',
      priority: NotificationPriority.NORMAL
    })
    console.log('✅ Email notification sent:', emailResult.success)

    // Test 2: Send SMS notification
    console.log('\n📱 Test 2: Sending SMS notification...')
    const smsResult = await notificationManager.send({
      type: NotificationType.ORDER_CONFIRMATION,
      channel: NotificationChannel.SMS,
      title: 'Order Confirmed',
      content: 'Your order #12345 has been confirmed!',
      recipientPhone: '+27123456789',
      priority: NotificationPriority.HIGH
    })
    console.log('✅ SMS notification sent:', smsResult.success)

    // Test 3: Send in-app notification
    console.log('\n🔔 Test 3: Sending in-app notification...')
    const inAppResult = await notificationManager.send({
      type: NotificationType.PROMOTIONAL,
      channel: NotificationChannel.IN_APP,
      title: 'Special Offer!',
      content: 'Get 20% off your next order!',
      recipientId: 'user123',
      priority: NotificationPriority.NORMAL
    })
    console.log('✅ In-app notification sent:', inAppResult.success)

    // Test 4: Send bulk notifications
    console.log('\n📬 Test 4: Sending bulk notifications...')
    const bulkNotifications = [
      {
        type: NotificationType.NEWSLETTER,
        channel: NotificationChannel.EMAIL,
        title: 'Monthly Newsletter',
        content: 'Check out our latest collection!',
        recipientEmail: '<EMAIL>'
      },
      {
        type: NotificationType.NEWSLETTER,
        channel: NotificationChannel.EMAIL,
        title: 'Monthly Newsletter',
        content: 'Check out our latest collection!',
        recipientEmail: '<EMAIL>'
      }
    ]

    const bulkResults = await notificationManager.sendBulk(bulkNotifications)
    console.log('✅ Bulk notifications sent:', bulkResults.length, 'notifications')

    // Test 5: Get analytics
    console.log('\n📊 Test 5: Getting analytics...')
    const analytics = await notificationManager.getAnalytics()
    console.log('✅ Analytics retrieved:', {
      totalSent: analytics.totalSent,
      totalDelivered: analytics.totalDelivered,
      deliveryRate: analytics.deliveryRate
    })

    console.log('\n🎉 All tests completed successfully!')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run tests if called directly
if (require.main === module) {
  testNotificationSystem()
}

export { testNotificationSystem }
