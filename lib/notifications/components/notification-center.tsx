'use client'

import React, { useState } from 'react'
import { 
  Bell, 
  Search, 
  Filter, 
  MoreVertical, 
  CheckCheck, 
  Trash2, 
  <PERSON><PERSON>resh<PERSON><PERSON>,
  <PERSON>tings,
  <PERSON>
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { cn } from '@/lib/utils'
import { useNotificationCenter } from '../hooks'
import { NotificationItem, NotificationItemSkeleton, NotificationItemList } from './notification-item'
import { NotificationSettings } from './notification-settings'
import { 
  NotificationChannel, 
  NotificationType, 
  NotificationPriority 
} from '../types'

interface NotificationCenterProps {
  userId?: string
  enableRealTime?: boolean
  className?: string
  maxHeight?: string
  showSettings?: boolean
}

/**
 * NotificationCenter Component
 * 
 * Complete notification management interface with filtering, search, and actions
 */
export function NotificationCenter({
  userId,
  enableRealTime = true,
  className,
  maxHeight = '600px',
  showSettings = true
}: NotificationCenterProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [showFilters, setShowFilters] = useState(false)

  const {
    groupedNotifications,
    unreadCount,
    totalCount,
    loading,
    error,
    connected,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refresh,
    activeFilters,
    setTypeFilter,
    setChannelFilter,
    setPriorityFilter,
    setUnreadOnlyFilter,
    setSearchFilter,
    clearFilters,
    stats
  } = useNotificationCenter({
    userId,
    enableRealTime
  })

  const handleSearch = (value: string) => {
    setSearchQuery(value)
    setSearchFilter(value)
  }

  const handleMarkAllAsRead = async () => {
    await markAllAsRead()
  }

  const handleRefresh = async () => {
    await refresh()
  }

  const hasActiveFilters = Boolean(
    activeFilters.type || 
    activeFilters.channel || 
    activeFilters.priority || 
    activeFilters.unreadOnly ||
    activeFilters.search
  )

  return (
    <Card className={cn('w-full max-w-2xl', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notifications
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2">
                {unreadCount}
              </Badge>
            )}
          </CardTitle>

          <div className="flex items-center gap-2">
            {/* Real-time connection status */}
            {enableRealTime && (
              <div 
                className={cn(
                  'h-2 w-2 rounded-full',
                  connected ? 'bg-green-500' : 'bg-red-500'
                )}
                title={connected ? 'Connected' : 'Disconnected'}
              />
            )}

            {/* Actions dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                
                <DropdownMenuItem onClick={handleRefresh} disabled={loading}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </DropdownMenuItem>
                
                {unreadCount > 0 && (
                  <DropdownMenuItem onClick={handleMarkAllAsRead}>
                    <CheckCheck className="h-4 w-4 mr-2" />
                    Mark all as read
                  </DropdownMenuItem>
                )}
                
                {hasActiveFilters && (
                  <DropdownMenuItem onClick={clearFilters}>
                    <X className="h-4 w-4 mr-2" />
                    Clear filters
                  </DropdownMenuItem>
                )}

                {showSettings && (
                  <>
                    <DropdownMenuSeparator />
                    <Dialog>
                      <DialogTrigger asChild>
                        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                          <Settings className="h-4 w-4 mr-2" />
                          Settings
                        </DropdownMenuItem>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>Notification Settings</DialogTitle>
                          <DialogDescription>
                            Manage your notification preferences and channels
                          </DialogDescription>
                        </DialogHeader>
                        <NotificationSettings userId={userId} />
                      </DialogContent>
                    </Dialog>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Search and filters */}
        <div className="space-y-3">
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search notifications..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-9"
              />
            </div>
            
            <Button
              variant={showFilters ? "default" : "outline"}
              size="icon"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4" />
            </Button>
          </div>

          {/* Filter controls */}
          {showFilters && (
            <div className="flex flex-wrap gap-2 p-3 bg-muted/50 rounded-lg">
              {/* Type filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    Type {activeFilters.type && `(${activeFilters.type})`}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuCheckboxItem
                    checked={!activeFilters.type}
                    onCheckedChange={() => setTypeFilter(null)}
                  >
                    All Types
                  </DropdownMenuCheckboxItem>
                  {Object.values(NotificationType).map((type) => (
                    <DropdownMenuCheckboxItem
                      key={type}
                      checked={activeFilters.type === type}
                      onCheckedChange={() => setTypeFilter(type)}
                    >
                      {type.replace(/_/g, ' ')}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Channel filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    Channel {activeFilters.channel && `(${activeFilters.channel})`}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuCheckboxItem
                    checked={!activeFilters.channel}
                    onCheckedChange={() => setChannelFilter(null)}
                  >
                    All Channels
                  </DropdownMenuCheckboxItem>
                  {Object.values(NotificationChannel).map((channel) => (
                    <DropdownMenuCheckboxItem
                      key={channel}
                      checked={activeFilters.channel === channel}
                      onCheckedChange={() => setChannelFilter(channel)}
                    >
                      {channel}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Priority filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    Priority {activeFilters.priority && `(${activeFilters.priority})`}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuCheckboxItem
                    checked={!activeFilters.priority}
                    onCheckedChange={() => setPriorityFilter(null)}
                  >
                    All Priorities
                  </DropdownMenuCheckboxItem>
                  {Object.values(NotificationPriority).map((priority) => (
                    <DropdownMenuCheckboxItem
                      key={priority}
                      checked={activeFilters.priority === priority}
                      onCheckedChange={() => setPriorityFilter(priority)}
                    >
                      {priority}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Unread only toggle */}
              <Button
                variant={activeFilters.unreadOnly ? "default" : "outline"}
                size="sm"
                onClick={() => setUnreadOnlyFilter(!activeFilters.unreadOnly)}
              >
                Unread Only
              </Button>
            </div>
          )}

          {/* Stats summary */}
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>{totalCount} total</span>
            {unreadCount > 0 && <span>{unreadCount} unread</span>}
            <span>{stats.readRate}% read rate</span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <ScrollArea style={{ height: maxHeight }}>
          <div className="p-4">
            {/* Error state */}
            {error && (
              <div className="text-center py-8">
                <div className="text-destructive mb-2">Error loading notifications</div>
                <p className="text-sm text-muted-foreground mb-4">{error}</p>
                <Button onClick={handleRefresh} variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </div>
            )}

            {/* Loading state */}
            {loading && (
              <NotificationItemList>
                {Array.from({ length: 5 }).map((_, i) => (
                  <NotificationItemSkeleton key={i} />
                ))}
              </NotificationItemList>
            )}

            {/* Empty state */}
            {!loading && !error && groupedNotifications.length === 0 && (
              <div className="text-center py-12">
                <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No notifications</h3>
                <p className="text-muted-foreground mb-4">
                  {hasActiveFilters 
                    ? 'No notifications match your current filters'
                    : 'You\'re all caught up!'
                  }
                </p>
                {hasActiveFilters && (
                  <Button onClick={clearFilters} variant="outline" size="sm">
                    Clear filters
                  </Button>
                )}
              </div>
            )}

            {/* Notifications grouped by date */}
            {!loading && !error && groupedNotifications.map((group, groupIndex) => (
              <div key={group.date} className="mb-6">
                <div className="flex items-center gap-2 mb-3">
                  <h3 className="font-medium text-sm text-muted-foreground">
                    {group.date}
                  </h3>
                  <Separator className="flex-1" />
                  <Badge variant="outline" className="text-xs">
                    {group.notifications.length}
                  </Badge>
                </div>
                
                <NotificationItemList>
                  {group.notifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                      onMarkAsRead={markAsRead}
                      onDelete={deleteNotification}
                    />
                  ))}
                </NotificationItemList>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

/**
 * NotificationCenterModal Component
 * 
 * NotificationCenter wrapped in a modal dialog
 */
interface NotificationCenterModalProps extends NotificationCenterProps {
  trigger?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function NotificationCenterModal({
  trigger,
  open,
  onOpenChange,
  ...props
}: NotificationCenterModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {trigger && (
        <DialogTrigger asChild>
          {trigger}
        </DialogTrigger>
      )}
      <DialogContent className="max-w-4xl max-h-[90vh] p-0">
        <NotificationCenter {...props} maxHeight="70vh" />
      </DialogContent>
    </Dialog>
  )
}

/**
 * NotificationCenterDrawer Component
 * 
 * NotificationCenter in a slide-out drawer (mobile-friendly)
 */
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'

interface NotificationCenterDrawerProps extends NotificationCenterProps {
  trigger?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
  side?: 'left' | 'right' | 'top' | 'bottom'
}

export function NotificationCenterDrawer({
  trigger,
  open,
  onOpenChange,
  side = 'right',
  ...props
}: NotificationCenterDrawerProps) {
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      {trigger && (
        <SheetTrigger asChild>
          {trigger}
        </SheetTrigger>
      )}
      <SheetContent side={side} className="w-full sm:w-96 p-0">
        <NotificationCenter {...props} maxHeight="calc(100vh - 2rem)" />
      </SheetContent>
    </Sheet>
  )
}
