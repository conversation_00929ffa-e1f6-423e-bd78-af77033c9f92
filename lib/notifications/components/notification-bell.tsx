'use client'

import React from 'react'
import { <PERSON>, BellRing } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { useNotificationCenter } from '../hooks'

interface NotificationBellProps {
  userId?: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'ghost' | 'outline'
  showBadge?: boolean
  maxBadgeCount?: number
  enableRealTime?: boolean
  onClick?: () => void
}

/**
 * NotificationBell Component
 * 
 * A bell icon with unread notification badge that integrates with the notification center
 */
export function NotificationBell({
  userId,
  className,
  size = 'md',
  variant = 'ghost',
  showBadge = true,
  maxBadgeCount = 99,
  enableRealTime = true,
  onClick
}: NotificationBellProps) {
  const {
    unreadCount,
    loading,
    error,
    toggle,
    connected
  } = useNotificationCenter({
    userId,
    enableRealTime
  })

  const handleClick = () => {
    if (onClick) {
      onClick()
    } else {
      toggle()
    }
  }

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  }

  const buttonSizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-9 w-9',
    lg: 'h-10 w-10'
  }

  const badgeText = unreadCount > maxBadgeCount ? `${maxBadgeCount}+` : unreadCount.toString()
  const hasUnread = unreadCount > 0
  const isConnected = !enableRealTime || connected

  return (
    <div className="relative">
      <Button
        variant={variant}
        size="icon"
        onClick={handleClick}
        disabled={loading}
        className={cn(
          buttonSizeClasses[size],
          'relative transition-colors',
          !isConnected && 'opacity-50',
          className
        )}
        aria-label={`Notifications${hasUnread ? ` (${unreadCount} unread)` : ''}`}
      >
        {hasUnread ? (
          <BellRing 
            className={cn(
              sizeClasses[size],
              'text-primary animate-pulse'
            )} 
          />
        ) : (
          <Bell 
            className={cn(
              sizeClasses[size],
              'text-muted-foreground'
            )} 
          />
        )}
        
        {/* Connection status indicator */}
        {enableRealTime && (
          <div 
            className={cn(
              'absolute -top-0.5 -right-0.5 h-2 w-2 rounded-full',
              connected ? 'bg-green-500' : 'bg-red-500'
            )}
            title={connected ? 'Connected' : 'Disconnected'}
          />
        )}
      </Button>

      {/* Unread badge */}
      {showBadge && hasUnread && (
        <Badge
          variant="destructive"
          className={cn(
            'absolute -top-1 -right-1 h-5 min-w-[1.25rem] px-1 text-xs font-medium',
            'flex items-center justify-center rounded-full',
            'animate-in zoom-in-50 duration-200'
          )}
        >
          {badgeText}
        </Badge>
      )}

      {/* Error indicator */}
      {error && (
        <div 
          className="absolute -bottom-1 -right-1 h-2 w-2 rounded-full bg-yellow-500"
          title={`Error: ${error}`}
        />
      )}
    </div>
  )
}

/**
 * NotificationBellWithTooltip Component
 * 
 * NotificationBell with tooltip showing unread count and status
 */
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface NotificationBellWithTooltipProps extends NotificationBellProps {
  showTooltip?: boolean
}

export function NotificationBellWithTooltip({
  showTooltip = true,
  ...props
}: NotificationBellWithTooltipProps) {
  const { unreadCount, connected, error, enableRealTime } = useNotificationCenter({
    userId: props.userId,
    enableRealTime: props.enableRealTime
  })

  if (!showTooltip) {
    return <NotificationBell {...props} />
  }

  const getTooltipContent = () => {
    if (error) {
      return `Notifications error: ${error}`
    }
    
    if (enableRealTime && !connected) {
      return 'Notifications disconnected'
    }
    
    if (unreadCount === 0) {
      return 'No unread notifications'
    }
    
    return `${unreadCount} unread notification${unreadCount === 1 ? '' : 's'}`
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div>
            <NotificationBell {...props} />
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{getTooltipContent()}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

/**
 * NotificationBellMenu Component
 * 
 * NotificationBell that opens a dropdown menu with recent notifications
 */
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { formatDistanceToNow } from 'date-fns'

interface NotificationBellMenuProps extends Omit<NotificationBellProps, 'onClick'> {
  maxMenuItems?: number
  onNotificationClick?: (notificationId: string) => void
  onViewAllClick?: () => void
}

export function NotificationBellMenu({
  maxMenuItems = 5,
  onNotificationClick,
  onViewAllClick,
  ...bellProps
}: NotificationBellMenuProps) {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead
  } = useNotificationCenter({
    userId: bellProps.userId,
    enableRealTime: bellProps.enableRealTime
  })

  const recentNotifications = notifications.slice(0, maxMenuItems)

  const handleNotificationClick = async (notificationId: string) => {
    await markAsRead(notificationId)
    if (onNotificationClick) {
      onNotificationClick(notificationId)
    }
  }

  const handleMarkAllRead = async () => {
    await markAllAsRead()
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div>
          <NotificationBell {...bellProps} />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Notifications</span>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMarkAllRead}
              className="h-auto p-1 text-xs"
            >
              Mark all read
            </Button>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {recentNotifications.length === 0 ? (
          <DropdownMenuItem disabled>
            <div className="flex flex-col items-center py-4 text-center">
              <Bell className="h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground">No notifications</p>
            </div>
          </DropdownMenuItem>
        ) : (
          <>
            {recentNotifications.map((notification) => (
              <DropdownMenuItem
                key={notification.id}
                onClick={() => handleNotificationClick(notification.id)}
                className={cn(
                  'flex flex-col items-start p-3 cursor-pointer',
                  !notification.readAt && 'bg-muted/50'
                )}
              >
                <div className="flex w-full items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {notification.title}
                    </p>
                    <p className="text-xs text-muted-foreground line-clamp-2 mt-1">
                      {notification.content}
                    </p>
                  </div>
                  {!notification.readAt && (
                    <div className="h-2 w-2 rounded-full bg-primary ml-2 mt-1 flex-shrink-0" />
                  )}
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                </p>
              </DropdownMenuItem>
            ))}
            
            {notifications.length > maxMenuItems && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={onViewAllClick}>
                  <div className="w-full text-center">
                    <Button variant="ghost" size="sm" className="w-full">
                      View all notifications
                    </Button>
                  </div>
                </DropdownMenuItem>
              </>
            )}
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
