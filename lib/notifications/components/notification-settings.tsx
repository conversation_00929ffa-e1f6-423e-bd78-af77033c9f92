'use client'

import React, { useState } from 'react'
import { 
  Bell, 
  Mail, 
  Smartphone, 
  MessageSquare, 
  Volume2, 
  VolumeX,
  Clock,
  Save,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { cn } from '@/lib/utils'
import { useNotificationPreferences, useBrowserNotificationPermission } from '../hooks'

interface NotificationSettingsProps {
  userId?: string
  className?: string
  onSave?: (preferences: any) => void
}

/**
 * NotificationSettings Component
 * 
 * Comprehensive notification preferences management interface
 */
export function NotificationSettings({
  userId,
  className,
  onSave
}: NotificationSettingsProps) {
  const {
    preferences,
    loading,
    saving,
    error,
    updatePreferences
  } = useNotificationPreferences(userId)

  const {
    permission,
    requesting,
    requestPermission,
    isSupported
  } = useBrowserNotificationPermission()

  const [localPreferences, setLocalPreferences] = useState(preferences)
  const [hasChanges, setHasChanges] = useState(false)

  // Update local state when preferences change
  React.useEffect(() => {
    setLocalPreferences(preferences)
    setHasChanges(false)
  }, [preferences])

  const handlePreferenceChange = (key: string, value: any) => {
    const newPreferences = { ...localPreferences, [key]: value }
    setLocalPreferences(newPreferences)
    setHasChanges(true)
  }

  const handleCategoryChange = (category: string, value: boolean) => {
    const newCategories = { ...localPreferences.categories, [category]: value }
    handlePreferenceChange('categories', newCategories)
  }

  const handleSave = async () => {
    const success = await updatePreferences(localPreferences)
    if (success) {
      setHasChanges(false)
      if (onSave) {
        onSave(localPreferences)
      }
    }
  }

  const handleReset = () => {
    setLocalPreferences(preferences)
    setHasChanges(false)
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="h-5 bg-muted rounded animate-pulse" />
              <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Array.from({ length: 3 }).map((_, j) => (
                  <div key={j} className="flex items-center justify-between">
                    <div className="h-4 bg-muted rounded animate-pulse w-1/2" />
                    <div className="h-6 w-12 bg-muted rounded animate-pulse" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Error alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Browser notifications */}
      {isSupported && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Browser Notifications
            </CardTitle>
            <CardDescription>
              Enable browser notifications to receive real-time alerts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Browser notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Status: {permission === 'granted' ? 'Enabled' : permission === 'denied' ? 'Blocked' : 'Not enabled'}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Badge 
                  variant={permission === 'granted' ? 'default' : 'secondary'}
                  className={cn(
                    permission === 'granted' && 'bg-green-500',
                    permission === 'denied' && 'bg-red-500'
                  )}
                >
                  {permission}
                </Badge>
                {permission !== 'granted' && (
                  <Button
                    onClick={requestPermission}
                    disabled={requesting || permission === 'denied'}
                    size="sm"
                  >
                    {requesting ? 'Requesting...' : 'Enable'}
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Channel preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Notification Channels
          </CardTitle>
          <CardDescription>
            Choose how you want to receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <div>
                <Label>Email notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive notifications via email
                </p>
              </div>
            </div>
            <Switch
              checked={localPreferences.emailEnabled}
              onCheckedChange={(checked) => handlePreferenceChange('emailEnabled', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Smartphone className="h-4 w-4 text-muted-foreground" />
              <div>
                <Label>SMS notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive notifications via SMS
                </p>
              </div>
            </div>
            <Switch
              checked={localPreferences.smsEnabled}
              onCheckedChange={(checked) => handlePreferenceChange('smsEnabled', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Bell className="h-4 w-4 text-muted-foreground" />
              <div>
                <Label>Push notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive push notifications on your devices
                </p>
              </div>
            </div>
            <Switch
              checked={localPreferences.pushEnabled}
              onCheckedChange={(checked) => handlePreferenceChange('pushEnabled', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
              <div>
                <Label>In-app notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Show notifications within the application
                </p>
              </div>
            </div>
            <Switch
              checked={localPreferences.inAppEnabled}
              onCheckedChange={(checked) => handlePreferenceChange('inAppEnabled', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Notification categories */}
      <Card>
        <CardHeader>
          <CardTitle>Notification Categories</CardTitle>
          <CardDescription>
            Choose which types of notifications you want to receive
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries(localPreferences.categories).map(([category, enabled]) => (
            <div key={category} className="flex items-center justify-between">
              <div>
                <Label className="capitalize">
                  {category.replace(/([A-Z])/g, ' $1').trim()}
                </Label>
                <p className="text-sm text-muted-foreground">
                  {getCategoryDescription(category)}
                </p>
              </div>
              <Switch
                checked={enabled}
                onCheckedChange={(checked) => handleCategoryChange(category, checked)}
              />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Timing preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Timing & Frequency
          </CardTitle>
          <CardDescription>
            Control when and how often you receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Notification frequency</Label>
            <Select
              value={localPreferences.frequency}
              onValueChange={(value) => handlePreferenceChange('frequency', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="IMMEDIATE">Immediate</SelectItem>
                <SelectItem value="HOURLY">Hourly digest</SelectItem>
                <SelectItem value="DAILY">Daily digest</SelectItem>
                <SelectItem value="WEEKLY">Weekly digest</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Quiet hours start</Label>
              <Select
                value={localPreferences.quietHoursStart}
                onValueChange={(value) => handlePreferenceChange('quietHoursStart', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {generateTimeOptions().map((time) => (
                    <SelectItem key={time} value={time}>
                      {time}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Quiet hours end</Label>
              <Select
                value={localPreferences.quietHoursEnd}
                onValueChange={(value) => handlePreferenceChange('quietHoursEnd', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {generateTimeOptions().map((time) => (
                    <SelectItem key={time} value={time}>
                      {time}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sound preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {localPreferences.sound ? (
              <Volume2 className="h-5 w-5" />
            ) : (
              <VolumeX className="h-5 w-5" />
            )}
            Sound Settings
          </CardTitle>
          <CardDescription>
            Configure notification sounds and alerts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <Label>Notification sounds</Label>
              <p className="text-sm text-muted-foreground">
                Play sound when receiving notifications
              </p>
            </div>
            <Switch
              checked={localPreferences.sound}
              onCheckedChange={(checked) => handlePreferenceChange('sound', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Save actions */}
      <div className="flex items-center justify-between pt-4 border-t">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          {hasChanges && (
            <>
              <AlertCircle className="h-4 w-4" />
              You have unsaved changes
            </>
          )}
        </div>

        <div className="flex items-center gap-2">
          {hasChanges && (
            <Button variant="outline" onClick={handleReset}>
              Reset
            </Button>
          )}
          
          <Button 
            onClick={handleSave} 
            disabled={!hasChanges || saving}
            className="min-w-[100px]"
          >
            {saving ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}

/**
 * Helper function to get category descriptions
 */
function getCategoryDescription(category: string): string {
  const descriptions: Record<string, string> = {
    orders: 'Order confirmations, updates, and shipping notifications',
    payments: 'Payment confirmations, failures, and refund notifications',
    shipping: 'Shipping updates, delivery confirmations, and tracking info',
    promotions: 'Special offers, discounts, and marketing communications',
    system: 'System maintenance, updates, and important announcements',
    security: 'Security alerts, login notifications, and account changes'
  }
  
  return descriptions[category] || 'Notification category'
}

/**
 * Helper function to generate time options
 */
function generateTimeOptions(): string[] {
  const times: string[] = []
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
      times.push(timeString)
    }
  }
  return times
}
