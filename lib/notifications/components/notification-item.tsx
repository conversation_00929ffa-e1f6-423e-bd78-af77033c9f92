'use client'

import React from 'react'
import { formatDistanceToNow } from 'date-fns'
import { 
  Mail, 
  MessageSquare, 
  Smartphone, 
  Bell, 
  Trash2, 
  Eye, 
  EyeOff,
  ExternalLink,
  AlertCircle,
  CheckCircle,
  Clock,
  X
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { 
  NotificationRecord, 
  NotificationChannel, 
  NotificationPriority,
  NotificationStatus 
} from '../types'

interface NotificationItemProps {
  notification: NotificationRecord
  onMarkAsRead?: (notificationId: string) => void
  onDelete?: (notificationId: string) => void
  onClick?: (notification: NotificationRecord) => void
  showActions?: boolean
  compact?: boolean
  className?: string
}

/**
 * NotificationItem Component
 * 
 * Displays a single notification with actions and status indicators
 */
export function NotificationItem({
  notification,
  onMarkAsRead,
  onDelete,
  onClick,
  showActions = true,
  compact = false,
  className
}: NotificationItemProps) {
  const isUnread = !notification.readAt
  const isUrgent = notification.priority === NotificationPriority.URGENT
  const isHigh = notification.priority === NotificationPriority.HIGH

  const handleClick = () => {
    if (onClick) {
      onClick(notification)
    } else if (isUnread && onMarkAsRead) {
      onMarkAsRead(notification.id)
    }
  }

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onMarkAsRead) {
      onMarkAsRead(notification.id)
    }
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onDelete) {
      onDelete(notification.id)
    }
  }

  const getChannelIcon = (channel: NotificationChannel) => {
    const iconClass = compact ? 'h-3 w-3' : 'h-4 w-4'
    
    switch (channel) {
      case NotificationChannel.EMAIL:
        return <Mail className={iconClass} />
      case NotificationChannel.SMS:
        return <Smartphone className={iconClass} />
      case NotificationChannel.PUSH:
        return <Bell className={iconClass} />
      case NotificationChannel.IN_APP:
        return <MessageSquare className={iconClass} />
      default:
        return <Bell className={iconClass} />
    }
  }

  const getStatusIcon = (status: NotificationStatus) => {
    const iconClass = compact ? 'h-3 w-3' : 'h-4 w-4'
    
    switch (status) {
      case NotificationStatus.DELIVERED:
      case NotificationStatus.SENT:
        return <CheckCircle className={cn(iconClass, 'text-green-500')} />
      case NotificationStatus.FAILED:
        return <AlertCircle className={cn(iconClass, 'text-red-500')} />
      case NotificationStatus.PENDING:
      case NotificationStatus.QUEUED:
        return <Clock className={cn(iconClass, 'text-yellow-500')} />
      case NotificationStatus.READ:
        return <Eye className={cn(iconClass, 'text-blue-500')} />
      default:
        return null
    }
  }

  const getPriorityBadge = (priority: NotificationPriority) => {
    switch (priority) {
      case NotificationPriority.URGENT:
        return (
          <Badge variant="destructive" className={compact ? 'text-xs px-1' : ''}>
            Urgent
          </Badge>
        )
      case NotificationPriority.HIGH:
        return (
          <Badge variant="secondary" className={compact ? 'text-xs px-1' : ''}>
            High
          </Badge>
        )
      default:
        return null
    }
  }

  const formatTime = (date: Date) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true })
  }

  if (compact) {
    return (
      <div
        onClick={handleClick}
        className={cn(
          'flex items-center gap-2 p-2 rounded-md cursor-pointer transition-colors',
          'hover:bg-muted/50',
          isUnread && 'bg-muted/30 border-l-2 border-l-primary',
          isUrgent && 'border-l-red-500',
          className
        )}
      >
        {/* Channel icon */}
        <div className="flex-shrink-0 text-muted-foreground">
          {getChannelIcon(notification.channel)}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <p className="text-sm font-medium truncate">
              {notification.title}
            </p>
            {getPriorityBadge(notification.priority)}
          </div>
          <p className="text-xs text-muted-foreground truncate">
            {notification.content}
          </p>
        </div>

        {/* Status and time */}
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          {getStatusIcon(notification.status)}
          <span>{formatTime(notification.createdAt)}</span>
        </div>

        {/* Unread indicator */}
        {isUnread && (
          <div className="h-2 w-2 rounded-full bg-primary flex-shrink-0" />
        )}
      </div>
    )
  }

  return (
    <Card 
      className={cn(
        'transition-all duration-200 cursor-pointer',
        'hover:shadow-md hover:border-primary/20',
        isUnread && 'border-l-4 border-l-primary bg-muted/30',
        isUrgent && 'border-l-red-500 shadow-sm',
        className
      )}
      onClick={handleClick}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between gap-3">
          {/* Main content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              {/* Channel icon */}
              <div className="text-muted-foreground">
                {getChannelIcon(notification.channel)}
              </div>
              
              {/* Title */}
              <h4 className="font-medium text-sm truncate">
                {notification.title}
              </h4>
              
              {/* Priority badge */}
              {getPriorityBadge(notification.priority)}
              
              {/* Unread indicator */}
              {isUnread && (
                <div className="h-2 w-2 rounded-full bg-primary flex-shrink-0" />
              )}
            </div>

            {/* Content */}
            <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
              {notification.content}
            </p>

            {/* Metadata */}
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                {getStatusIcon(notification.status)}
                <span className="capitalize">{notification.status.toLowerCase()}</span>
              </div>
              
              <span>{formatTime(notification.createdAt)}</span>
              
              {notification.readAt && (
                <span>Read {formatTime(notification.readAt)}</span>
              )}
              
              <Badge variant="outline" className="text-xs">
                {notification.type.replace(/_/g, ' ')}
              </Badge>
            </div>
          </div>

          {/* Actions */}
          {showActions && (
            <div className="flex items-center gap-1">
              {isUnread && onMarkAsRead && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleMarkAsRead}
                  className="h-8 w-8"
                  title="Mark as read"
                >
                  <EyeOff className="h-4 w-4" />
                </Button>
              )}
              
              {notification.data?.clickAction && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation()
                    window.open(notification.data?.clickAction, '_blank')
                  }}
                  className="h-8 w-8"
                  title="Open link"
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              )}
              
              {onDelete && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleDelete}
                  className="h-8 w-8 text-destructive hover:text-destructive"
                  title="Delete notification"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * NotificationItemSkeleton Component
 * 
 * Loading skeleton for notification items
 */
export function NotificationItemSkeleton({ compact = false }: { compact?: boolean }) {
  if (compact) {
    return (
      <div className="flex items-center gap-2 p-2">
        <div className="h-4 w-4 bg-muted rounded animate-pulse" />
        <div className="flex-1 space-y-1">
          <div className="h-4 bg-muted rounded animate-pulse" />
          <div className="h-3 bg-muted rounded animate-pulse w-3/4" />
        </div>
        <div className="h-3 w-16 bg-muted rounded animate-pulse" />
      </div>
    )
  }

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 space-y-3">
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 bg-muted rounded animate-pulse" />
              <div className="h-4 bg-muted rounded animate-pulse flex-1" />
            </div>
            <div className="space-y-2">
              <div className="h-3 bg-muted rounded animate-pulse" />
              <div className="h-3 bg-muted rounded animate-pulse w-3/4" />
            </div>
            <div className="flex items-center gap-4">
              <div className="h-3 w-16 bg-muted rounded animate-pulse" />
              <div className="h-3 w-20 bg-muted rounded animate-pulse" />
              <div className="h-5 w-16 bg-muted rounded animate-pulse" />
            </div>
          </div>
          <div className="flex gap-1">
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * NotificationItemList Component
 * 
 * List container for notification items with proper spacing
 */
interface NotificationItemListProps {
  children: React.ReactNode
  compact?: boolean
  className?: string
}

export function NotificationItemList({ 
  children, 
  compact = false, 
  className 
}: NotificationItemListProps) {
  return (
    <div 
      className={cn(
        'space-y-2',
        compact && 'space-y-1',
        className
      )}
    >
      {children}
    </div>
  )
}
