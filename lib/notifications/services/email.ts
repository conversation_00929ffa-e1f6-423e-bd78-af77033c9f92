import { 
  NotificationService, 
  NotificationRequest, 
  NotificationResult, 
  DeliveryStatus,
  NotificationChannel,
  NotificationStatus
} from '../types'
import { notificationConfig } from '../config'
import { logger, validators, errorUtils } from '../utils'
import * as nodemailer from 'nodemailer'

/**
 * Email Notification Service
 * Handles email delivery through multiple providers
 */
export class EmailService implements NotificationService {
  private transporter: nodemailer.Transporter | null = null

  constructor() {
    this.initializeTransporter()
  }

  /**
   * Initialize email transporter based on configuration
   */
  private async initializeTransporter(): Promise<void> {
    try {
      const emailConfig = notificationConfig.getEmailConfig()
      const preferredProvider = notificationConfig.getPreferredEmailProvider()

      if (!preferredProvider) {
        logger.warn('No email provider configured')
        return
      }

      switch (preferredProvider) {
        case 'smtp':
          if (emailConfig.smtp) {
            this.transporter = nodemailer.createTransport({
              host: emailConfig.smtp.host,
              port: emailConfig.smtp.port,
              secure: emailConfig.smtp.secure,
              auth: {
                user: emailConfig.smtp.username,
                pass: emailConfig.smtp.password
              }
            })
          }
          break

        case 'sendgrid':
          if (emailConfig.sendgrid) {
            this.transporter = nodemailer.createTransport({
              service: 'SendGrid',
              auth: {
                user: 'apikey',
                pass: emailConfig.sendgrid.apiKey
              }
            })
          }
          break

        case 'mailgun':
          if (emailConfig.mailgun) {
            // Mailgun SMTP configuration
            this.transporter = nodemailer.createTransport({
              host: 'smtp.mailgun.org',
              port: 587,
              secure: false,
              auth: {
                user: `postmaster@${emailConfig.mailgun.domain}`,
                pass: emailConfig.mailgun.apiKey
              }
            })
          }
          break
      }

      if (this.transporter) {
        // Verify transporter configuration
        await this.transporter.verify()
        logger.info(`Email service initialized with ${preferredProvider} provider`)
      }

    } catch (error) {
      logger.error('Failed to initialize email transporter', { error })
      this.transporter = null
    }
  }

  /**
   * Send email notification
   */
  async send(notification: NotificationRequest): Promise<NotificationResult> {
    try {
      // Validate notification
      const validation = this.validateEmailNotification(notification)
      if (!validation.isValid) {
        return {
          success: false,
          error: `Email validation failed: ${validation.errors.join(', ')}`
        }
      }

      // Check if transporter is available
      if (!this.transporter) {
        await this.initializeTransporter()
        if (!this.transporter) {
          return {
            success: false,
            error: 'Email service not configured'
          }
        }
      }

      // Prepare email options
      const emailOptions = this.prepareEmailOptions(notification)

      // Send email
      const result = await this.transporter.sendMail(emailOptions)

      logger.info('Email sent successfully', {
        messageId: result.messageId,
        recipient: notification.recipientEmail
      })

      return {
        success: true,
        messageId: result.messageId,
        metadata: {
          provider: notificationConfig.getPreferredEmailProvider(),
          response: result.response
        }
      }

    } catch (error) {
      const errorMessage = errorUtils.createErrorMessage(error)
      logger.error('Email send failed', { error: errorMessage, notification })

      return {
        success: false,
        error: errorMessage,
        metadata: {
          provider: notificationConfig.getPreferredEmailProvider(),
          retryable: errorUtils.isRetryableError(errorMessage)
        }
      }
    }
  }

  /**
   * Validate email notification
   */
  private validateEmailNotification(notification: NotificationRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (notification.channel !== NotificationChannel.EMAIL) {
      errors.push('Invalid channel for email service')
    }

    if (!notification.recipientEmail) {
      errors.push('Recipient email is required')
    } else if (!validators.isValidEmail(notification.recipientEmail)) {
      errors.push('Invalid recipient email format')
    }

    if (!notification.title && !notification.content) {
      errors.push('Either title or content is required')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Prepare email options for nodemailer
   */
  private prepareEmailOptions(notification: NotificationRequest): nodemailer.SendMailOptions {
    const emailConfig = notificationConfig.getEmailConfig()
    const preferredProvider = notificationConfig.getPreferredEmailProvider()

    let fromEmail = notificationConfig.getDefaultFromEmail()
    let fromName = notificationConfig.getDefaultFromName()

    // Override with provider-specific settings
    if (preferredProvider === 'sendgrid' && emailConfig.sendgrid) {
      fromEmail = emailConfig.sendgrid.fromEmail
      fromName = emailConfig.sendgrid.fromName
    } else if (preferredProvider === 'mailgun' && emailConfig.mailgun) {
      fromEmail = emailConfig.mailgun.fromEmail
      fromName = emailConfig.mailgun.fromName
    }

    const options: nodemailer.SendMailOptions = {
      from: `"${fromName}" <${fromEmail}>`,
      to: notification.recipientEmail,
      subject: notification.title,
      text: notification.content
    }

    // Add HTML content if available
    if (notification.data?.htmlContent) {
      options.html = notification.data.htmlContent
    }

    // Add attachments if available
    if (notification.data?.attachments) {
      options.attachments = notification.data.attachments
    }

    // Add custom headers
    if (notification.data?.headers) {
      options.headers = notification.data.headers
    }

    // Add tracking headers for analytics
    options.headers = {
      ...options.headers,
      'X-Notification-ID': notification.data?.notificationId || 'unknown',
      'X-Notification-Type': notification.type,
      'X-Notification-Channel': notification.channel
    }

    return options
  }

  /**
   * Validate email service configuration
   */
  async validateConfig(): Promise<boolean> {
    try {
      if (!this.transporter) {
        await this.initializeTransporter()
      }

      if (!this.transporter) {
        return false
      }

      await this.transporter.verify()
      return true

    } catch (error) {
      logger.error('Email config validation failed', { error })
      return false
    }
  }

  /**
   * Get delivery status for email
   */
  async getDeliveryStatus(messageId: string): Promise<DeliveryStatus> {
    // Email delivery status tracking would require webhook integration
    // with email providers. This is a placeholder implementation.
    
    try {
      // In a real implementation, you would:
      // 1. Query the email provider's API for delivery status
      // 2. Check webhook data stored in database
      // 3. Return actual delivery status

      return {
        status: NotificationStatus.SENT,
        timestamp: new Date(),
        metadata: {
          messageId,
          provider: notificationConfig.getPreferredEmailProvider()
        }
      }

    } catch (error) {
      logger.error('Failed to get email delivery status', { error, messageId })
      return {
        status: NotificationStatus.FAILED,
        error: errorUtils.createErrorMessage(error)
      }
    }
  }

  /**
   * Send test email
   */
  async sendTest(recipientEmail: string): Promise<NotificationResult> {
    const testNotification: NotificationRequest = {
      type: 'CUSTOM' as any,
      channel: NotificationChannel.EMAIL,
      title: 'Test Email from Coco Milk Kids',
      content: `
        This is a test email to verify your email configuration.
        
        If you received this email, your email service is working correctly!
        
        Sent at: ${new Date().toISOString()}
        Provider: ${notificationConfig.getPreferredEmailProvider()}
      `,
      recipientEmail,
      data: {
        htmlContent: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Test Email from Coco Milk Kids</h2>
            <p>This is a test email to verify your email configuration.</p>
            <p>If you received this email, your email service is working correctly!</p>
            <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <strong>Details:</strong><br>
              Sent at: ${new Date().toISOString()}<br>
              Provider: ${notificationConfig.getPreferredEmailProvider()}
            </div>
            <p style="color: #666; font-size: 12px;">
              This is an automated test email from the Coco Milk Kids notification system.
            </p>
          </div>
        `
      }
    }

    return this.send(testNotification)
  }

  /**
   * Get email service statistics
   */
  async getStats(): Promise<{
    provider: string | null
    configured: boolean
    lastTest?: Date
    totalSent?: number
  }> {
    return {
      provider: notificationConfig.getPreferredEmailProvider(),
      configured: await this.validateConfig(),
      // In a real implementation, these would come from database
      totalSent: 0
    }
  }
}
