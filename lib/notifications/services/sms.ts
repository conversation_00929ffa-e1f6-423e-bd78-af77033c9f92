import { 
  NotificationService, 
  NotificationRequest, 
  NotificationResult, 
  DeliveryStatus,
  NotificationChannel,
  NotificationStatus
} from '../types'
import { notificationConfig } from '../config'
import { logger, validators, formatters, errorUtils } from '../utils'
import axios from 'axios'

/**
 * SMS Notification Service
 * Handles SMS delivery through multiple South African and international providers
 */
export class SMSService implements NotificationService {
  
  constructor() {
    // Initialize any required connections
  }

  /**
   * Send SMS notification
   */
  async send(notification: NotificationRequest): Promise<NotificationResult> {
    try {
      // Validate notification
      const validation = this.validateSMSNotification(notification)
      if (!validation.isValid) {
        return {
          success: false,
          error: `SMS validation failed: ${validation.errors.join(', ')}`
        }
      }

      const smsConfig = notificationConfig.getSMSConfig()
      const preferredProvider = notificationConfig.getPreferredSMSProvider()

      if (!preferredProvider) {
        return {
          success: false,
          error: 'No SMS provider configured'
        }
      }

      // Format phone number
      const formattedPhone = formatters.formatSAPhoneNumber(notification.recipientPhone!)

      // Format content for SMS
      const formattedContent = formatters.formatContentForChannel(
        notification.content, 
        NotificationChannel.SMS
      )

      let result: NotificationResult

      switch (preferredProvider) {
        case 'clickatell':
          result = await this.sendViaClickatell(formattedPhone, formattedContent, smsConfig.clickatell!)
          break
        case 'bulkSms':
          result = await this.sendViaBulkSMS(formattedPhone, formattedContent, smsConfig.bulkSms!)
          break
        case 'twilio':
          result = await this.sendViaTwilio(formattedPhone, formattedContent, smsConfig.twilio!)
          break
        default:
          return {
            success: false,
            error: `Unsupported SMS provider: ${preferredProvider}`
          }
      }

      if (result.success) {
        logger.info('SMS sent successfully', {
          messageId: result.messageId,
          recipient: formattedPhone,
          provider: preferredProvider
        })
      }

      return result

    } catch (error) {
      const errorMessage = errorUtils.createErrorMessage(error)
      logger.error('SMS send failed', { error: errorMessage, notification })

      return {
        success: false,
        error: errorMessage,
        metadata: {
          provider: notificationConfig.getPreferredSMSProvider(),
          retryable: errorUtils.isRetryableError(errorMessage)
        }
      }
    }
  }

  /**
   * Send SMS via Clickatell (South African provider)
   */
  private async sendViaClickatell(
    phone: string, 
    content: string, 
    config: { apiKey: string; fromNumber: string }
  ): Promise<NotificationResult> {
    try {
      const response = await axios.post('https://platform.clickatell.com/messages', {
        to: [phone],
        content,
        from: config.fromNumber
      }, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.data.messages && response.data.messages[0]) {
        const message = response.data.messages[0]
        
        if (message.accepted) {
          return {
            success: true,
            messageId: message.apiMessageId,
            metadata: {
              provider: 'clickatell',
              to: message.to,
              from: message.from
            }
          }
        } else {
          return {
            success: false,
            error: message.error?.description || 'Message not accepted',
            metadata: {
              provider: 'clickatell',
              errorCode: message.error?.code
            }
          }
        }
      }

      return {
        success: false,
        error: 'Invalid response from Clickatell'
      }

    } catch (error) {
      if (axios.isAxiosError(error)) {
        return {
          success: false,
          error: error.response?.data?.error?.description || error.message,
          metadata: {
            provider: 'clickatell',
            statusCode: error.response?.status
          }
        }
      }
      throw error
    }
  }

  /**
   * Send SMS via Bulk SMS (South African provider)
   */
  private async sendViaBulkSMS(
    phone: string, 
    content: string, 
    config: { username: string; password: string; fromNumber: string }
  ): Promise<NotificationResult> {
    try {
      const response = await axios.post('https://bulksms.vsms.net/eapi/submission/send_sms/2/2.0', 
        `username=${encodeURIComponent(config.username)}&password=${encodeURIComponent(config.password)}&message=${encodeURIComponent(content)}&msisdn=${encodeURIComponent(phone)}`,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      )

      // Bulk SMS returns status codes
      const statusCode = response.data.split('|')[0]
      
      if (statusCode === '0') {
        // Success
        const messageId = response.data.split('|')[1]
        return {
          success: true,
          messageId,
          metadata: {
            provider: 'bulkSms',
            response: response.data
          }
        }
      } else {
        // Error
        const errorMessages: { [key: string]: string } = {
          '22': 'Internal fatal error',
          '23': 'Authentication failure',
          '24': 'Data validation failed',
          '25': 'Insufficient credits',
          '26': 'Upstream credits not available',
          '27': 'Daily quota exceeded',
          '28': 'Upstream quota exceeded',
          '40': 'Temporarily unavailable'
        }

        return {
          success: false,
          error: errorMessages[statusCode] || `Unknown error code: ${statusCode}`,
          metadata: {
            provider: 'bulkSms',
            statusCode,
            response: response.data
          }
        }
      }

    } catch (error) {
      if (axios.isAxiosError(error)) {
        return {
          success: false,
          error: error.response?.data || error.message,
          metadata: {
            provider: 'bulkSms',
            statusCode: error.response?.status
          }
        }
      }
      throw error
    }
  }

  /**
   * Send SMS via Twilio (International provider)
   */
  private async sendViaTwilio(
    phone: string, 
    content: string, 
    config: { accountSid: string; authToken: string; fromNumber: string }
  ): Promise<NotificationResult> {
    try {
      const auth = Buffer.from(`${config.accountSid}:${config.authToken}`).toString('base64')
      
      const response = await axios.post(
        `https://api.twilio.com/2010-04-01/Accounts/${config.accountSid}/Messages.json`,
        new URLSearchParams({
          To: phone,
          From: config.fromNumber,
          Body: content
        }),
        {
          headers: {
            'Authorization': `Basic ${auth}`,
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      )

      if (response.data.sid) {
        return {
          success: true,
          messageId: response.data.sid,
          metadata: {
            provider: 'twilio',
            status: response.data.status,
            to: response.data.to,
            from: response.data.from
          }
        }
      }

      return {
        success: false,
        error: 'Invalid response from Twilio'
      }

    } catch (error) {
      if (axios.isAxiosError(error)) {
        const twilioError = error.response?.data
        return {
          success: false,
          error: twilioError?.message || error.message,
          metadata: {
            provider: 'twilio',
            errorCode: twilioError?.code,
            statusCode: error.response?.status
          }
        }
      }
      throw error
    }
  }

  /**
   * Validate SMS notification
   */
  private validateSMSNotification(notification: NotificationRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (notification.channel !== NotificationChannel.SMS) {
      errors.push('Invalid channel for SMS service')
    }

    if (!notification.recipientPhone) {
      errors.push('Recipient phone number is required')
    } else if (!validators.isValidPhoneNumber(notification.recipientPhone)) {
      errors.push('Invalid recipient phone number format')
    }

    if (!notification.content) {
      errors.push('Content is required for SMS')
    } else if (notification.content.length > 160) {
      // Warning, not error - content will be truncated
      logger.warn('SMS content exceeds 160 characters and will be truncated', {
        length: notification.content.length
      })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Validate SMS service configuration
   */
  async validateConfig(): Promise<boolean> {
    const smsConfig = notificationConfig.getSMSConfig()
    const preferredProvider = notificationConfig.getPreferredSMSProvider()

    if (!preferredProvider) {
      return false
    }

    switch (preferredProvider) {
      case 'clickatell':
        return !!(smsConfig.clickatell?.apiKey && smsConfig.clickatell?.fromNumber)
      case 'bulkSms':
        return !!(smsConfig.bulkSms?.username && smsConfig.bulkSms?.password)
      case 'twilio':
        return !!(smsConfig.twilio?.accountSid && smsConfig.twilio?.authToken && smsConfig.twilio?.fromNumber)
      default:
        return false
    }
  }

  /**
   * Get delivery status for SMS
   */
  async getDeliveryStatus(messageId: string): Promise<DeliveryStatus> {
    try {
      const preferredProvider = notificationConfig.getPreferredSMSProvider()
      
      if (!preferredProvider) {
        return {
          status: NotificationStatus.FAILED,
          error: 'No SMS provider configured'
        }
      }

      // In a real implementation, you would query the provider's API
      // for delivery status. This is a placeholder.
      
      return {
        status: NotificationStatus.SENT,
        timestamp: new Date(),
        metadata: {
          messageId,
          provider: preferredProvider
        }
      }

    } catch (error) {
      logger.error('Failed to get SMS delivery status', { error, messageId })
      return {
        status: NotificationStatus.FAILED,
        error: errorUtils.createErrorMessage(error)
      }
    }
  }

  /**
   * Send test SMS
   */
  async sendTest(recipientPhone: string): Promise<NotificationResult> {
    const testNotification: NotificationRequest = {
      type: 'CUSTOM' as any,
      channel: NotificationChannel.SMS,
      title: 'Test SMS',
      content: `Test SMS from Coco Milk Kids. Provider: ${notificationConfig.getPreferredSMSProvider()}. Time: ${new Date().toLocaleTimeString()}`,
      recipientPhone
    }

    return this.send(testNotification)
  }

  /**
   * Get SMS service statistics
   */
  async getStats(): Promise<{
    provider: string | null
    configured: boolean
    lastTest?: Date
    totalSent?: number
  }> {
    return {
      provider: notificationConfig.getPreferredSMSProvider(),
      configured: await this.validateConfig(),
      // In a real implementation, these would come from database
      totalSent: 0
    }
  }
}
