import { 
  NotificationService, 
  NotificationRequest, 
  NotificationResult, 
  DeliveryStatus,
  NotificationChannel,
  NotificationStatus,
  NotificationRecord
} from '../types'
import { logger, errorUtils } from '../utils'

/**
 * In-App Notification Service
 * Handles in-app notifications that are displayed within the application
 */
export class InAppService implements NotificationService {
  private notifications: Map<string, NotificationRecord[]> = new Map()
  private subscribers: Map<string, ((notifications: NotificationRecord[]) => void)[]> = new Map()

  constructor() {
    // Initialize in-memory storage for in-app notifications
    // In a real implementation, this would use a database
  }

  /**
   * Send in-app notification
   */
  async send(notification: NotificationRequest): Promise<NotificationResult> {
    try {
      // Validate notification
      const validation = this.validateInAppNotification(notification)
      if (!validation.isValid) {
        return {
          success: false,
          error: `In-app validation failed: ${validation.errors.join(', ')}`
        }
      }

      // Create notification record
      const notificationRecord: NotificationRecord = {
        id: `inapp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: notification.type,
        channel: NotificationChannel.IN_APP,
        title: notification.title,
        content: notification.content,
        data: notification.data,
        recipientId: notification.recipientId!,
        recipientType: notification.recipientType,
        status: NotificationStatus.DELIVERED, // In-app notifications are immediately delivered
        priority: notification.priority || 'normal' as any,
        templateId: notification.templateId,
        sentAt: new Date(),
        deliveredAt: new Date(),
        retryCount: 0,
        maxRetries: 0, // In-app notifications don't need retries
        metadata: {
          ...notification.metadata,
          channel: 'in_app'
        },
        expiresAt: notification.expiresAt || this.getDefaultExpiryDate(),
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // Store notification
      await this.storeNotification(notificationRecord)

      // Notify subscribers (real-time updates)
      this.notifySubscribers(notification.recipientId!, notificationRecord)

      logger.info('In-app notification delivered', {
        notificationId: notificationRecord.id,
        recipient: notification.recipientId
      })

      return {
        success: true,
        notificationId: notificationRecord.id,
        messageId: notificationRecord.id,
        metadata: {
          provider: 'in_app',
          deliveredAt: notificationRecord.deliveredAt
        }
      }

    } catch (error) {
      const errorMessage = errorUtils.createErrorMessage(error)
      logger.error('In-app notification send failed', { error: errorMessage, notification })

      return {
        success: false,
        error: errorMessage,
        metadata: {
          provider: 'in_app'
        }
      }
    }
  }

  /**
   * Store notification in memory/database
   */
  private async storeNotification(notification: NotificationRecord): Promise<void> {
    const userId = notification.recipientId!
    
    if (!this.notifications.has(userId)) {
      this.notifications.set(userId, [])
    }

    const userNotifications = this.notifications.get(userId)!
    userNotifications.unshift(notification) // Add to beginning

    // Keep only the latest 100 notifications per user
    if (userNotifications.length > 100) {
      userNotifications.splice(100)
    }

    this.notifications.set(userId, userNotifications)
  }

  /**
   * Get notifications for a user
   */
  async getNotifications(
    userId: string, 
    options: {
      limit?: number
      offset?: number
      unreadOnly?: boolean
      type?: string
    } = {}
  ): Promise<NotificationRecord[]> {
    try {
      const userNotifications = this.notifications.get(userId) || []
      let filtered = [...userNotifications]

      // Filter by read status
      if (options.unreadOnly) {
        filtered = filtered.filter(n => !n.readAt)
      }

      // Filter by type
      if (options.type) {
        filtered = filtered.filter(n => n.type === options.type)
      }

      // Filter out expired notifications
      const now = new Date()
      filtered = filtered.filter(n => !n.expiresAt || n.expiresAt > now)

      // Apply pagination
      const offset = options.offset || 0
      const limit = options.limit || 50
      
      return filtered.slice(offset, offset + limit)

    } catch (error) {
      logger.error('Failed to get notifications', { error, userId })
      return []
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(userId: string, notificationId: string): Promise<boolean> {
    try {
      const userNotifications = this.notifications.get(userId) || []
      const notification = userNotifications.find(n => n.id === notificationId)

      if (notification) {
        notification.readAt = new Date()
        notification.status = NotificationStatus.READ
        notification.updatedAt = new Date()

        // Notify subscribers of the update
        this.notifySubscribers(userId)

        logger.info('Notification marked as read', { notificationId, userId })
        return true
      }

      return false

    } catch (error) {
      logger.error('Failed to mark notification as read', { error, notificationId, userId })
      return false
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: string): Promise<number> {
    try {
      const userNotifications = this.notifications.get(userId) || []
      let markedCount = 0

      userNotifications.forEach(notification => {
        if (!notification.readAt) {
          notification.readAt = new Date()
          notification.status = NotificationStatus.READ
          notification.updatedAt = new Date()
          markedCount++
        }
      })

      if (markedCount > 0) {
        // Notify subscribers of the update
        this.notifySubscribers(userId)
        logger.info('All notifications marked as read', { userId, count: markedCount })
      }

      return markedCount

    } catch (error) {
      logger.error('Failed to mark all notifications as read', { error, userId })
      return 0
    }
  }

  /**
   * Delete notification
   */
  async deleteNotification(userId: string, notificationId: string): Promise<boolean> {
    try {
      const userNotifications = this.notifications.get(userId) || []
      const index = userNotifications.findIndex(n => n.id === notificationId)

      if (index !== -1) {
        userNotifications.splice(index, 1)
        this.notifications.set(userId, userNotifications)

        // Notify subscribers of the update
        this.notifySubscribers(userId)

        logger.info('Notification deleted', { notificationId, userId })
        return true
      }

      return false

    } catch (error) {
      logger.error('Failed to delete notification', { error, notificationId, userId })
      return false
    }
  }

  /**
   * Get unread count for a user
   */
  async getUnreadCount(userId: string): Promise<number> {
    try {
      const userNotifications = this.notifications.get(userId) || []
      const now = new Date()
      
      return userNotifications.filter(n => 
        !n.readAt && 
        (!n.expiresAt || n.expiresAt > now)
      ).length

    } catch (error) {
      logger.error('Failed to get unread count', { error, userId })
      return 0
    }
  }

  /**
   * Subscribe to real-time notification updates
   */
  subscribe(userId: string, callback: (notifications: NotificationRecord[]) => void): () => void {
    if (!this.subscribers.has(userId)) {
      this.subscribers.set(userId, [])
    }

    const userSubscribers = this.subscribers.get(userId)!
    userSubscribers.push(callback)

    // Return unsubscribe function
    return () => {
      const index = userSubscribers.indexOf(callback)
      if (index !== -1) {
        userSubscribers.splice(index, 1)
      }
    }
  }

  /**
   * Notify subscribers of notification updates
   */
  private notifySubscribers(userId: string, newNotification?: NotificationRecord): void {
    const subscribers = this.subscribers.get(userId) || []
    
    if (subscribers.length > 0) {
      const notifications = this.notifications.get(userId) || []
      subscribers.forEach(callback => {
        try {
          callback(notifications)
        } catch (error) {
          logger.error('Subscriber callback error', { error, userId })
        }
      })
    }
  }

  /**
   * Validate in-app notification
   */
  private validateInAppNotification(notification: NotificationRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (notification.channel !== NotificationChannel.IN_APP) {
      errors.push('Invalid channel for in-app service')
    }

    if (!notification.recipientId) {
      errors.push('Recipient ID is required for in-app notifications')
    }

    if (!notification.title) {
      errors.push('Title is required for in-app notifications')
    }

    if (!notification.content) {
      errors.push('Content is required for in-app notifications')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Get default expiry date (30 days from now)
   */
  private getDefaultExpiryDate(): Date {
    const date = new Date()
    date.setDate(date.getDate() + 30)
    return date
  }

  /**
   * Validate in-app service configuration
   */
  async validateConfig(): Promise<boolean> {
    // In-app notifications don't require external configuration
    return true
  }

  /**
   * Get delivery status for in-app notification
   */
  async getDeliveryStatus(messageId: string): Promise<DeliveryStatus> {
    try {
      // Find notification across all users
      for (const [userId, notifications] of Array.from(this.notifications.entries())) {
        const notification = notifications.find(n => n.id === messageId)
        if (notification) {
          return {
            status: notification.status,
            timestamp: notification.deliveredAt || notification.sentAt,
            metadata: {
              messageId,
              provider: 'in_app',
              readAt: notification.readAt
            }
          }
        }
      }

      return {
        status: NotificationStatus.FAILED,
        error: 'Notification not found'
      }

    } catch (error) {
      logger.error('Failed to get in-app delivery status', { error, messageId })
      return {
        status: NotificationStatus.FAILED,
        error: errorUtils.createErrorMessage(error)
      }
    }
  }

  /**
   * Clean up expired notifications
   */
  async cleanupExpired(): Promise<number> {
    try {
      let cleanedCount = 0
      const now = new Date()

      for (const [userId, notifications] of Array.from(this.notifications.entries())) {
        const validNotifications = notifications.filter(n => !n.expiresAt || n.expiresAt > now)
        const removedCount = notifications.length - validNotifications.length

        if (removedCount > 0) {
          this.notifications.set(userId, validNotifications)
          cleanedCount += removedCount
          
          // Notify subscribers of the update
          this.notifySubscribers(userId)
        }
      }

      if (cleanedCount > 0) {
        logger.info('Expired notifications cleaned up', { count: cleanedCount })
      }

      return cleanedCount

    } catch (error) {
      logger.error('Failed to cleanup expired notifications', { error })
      return 0
    }
  }

  /**
   * Get in-app service statistics
   */
  async getStats(): Promise<{
    provider: string
    configured: boolean
    totalNotifications: number
    totalUsers: number
    unreadCount: number
  }> {
    let totalNotifications = 0
    let unreadCount = 0

    for (const notifications of Array.from(this.notifications.values())) {
      totalNotifications += notifications.length
      unreadCount += notifications.filter(n => !n.readAt).length
    }

    return {
      provider: 'in_app',
      configured: true,
      totalNotifications,
      totalUsers: this.notifications.size,
      unreadCount
    }
  }
}
