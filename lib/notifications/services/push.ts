import { 
  NotificationService, 
  NotificationRequest, 
  NotificationResult, 
  DeliveryStatus,
  NotificationChannel,
  NotificationStatus
} from '../types'
import { notificationConfig } from '../config'
import { logger, formatters, errorUtils } from '../utils'
import axios from 'axios'

/**
 * Push Notification Service
 * Handles push notifications through FCM and APNS
 */
export class PushService implements NotificationService {
  
  constructor() {
    // Initialize any required connections
  }

  /**
   * Send push notification
   */
  async send(notification: NotificationRequest): Promise<NotificationResult> {
    try {
      // Validate notification
      const validation = this.validatePushNotification(notification)
      if (!validation.isValid) {
        return {
          success: false,
          error: `Push validation failed: ${validation.errors.join(', ')}`
        }
      }

      const pushConfig = notificationConfig.getPushConfig()
      const preferredProvider = notificationConfig.getPreferredPushProvider()

      if (!preferredProvider) {
        return {
          success: false,
          error: 'No push notification provider configured'
        }
      }

      // Format content for push notifications
      const formattedContent = formatters.formatContentForChannel(
        notification.content, 
        NotificationChannel.PUSH
      )

      let result: NotificationResult

      switch (preferredProvider) {
        case 'fcm':
          result = await this.sendViaFCM(notification, formattedContent, pushConfig.fcm!)
          break
        case 'apns':
          result = await this.sendViaAPNS(notification, formattedContent, pushConfig.apns!)
          break
        default:
          return {
            success: false,
            error: `Unsupported push provider: ${preferredProvider}`
          }
      }

      if (result.success) {
        logger.info('Push notification sent successfully', {
          messageId: result.messageId,
          recipient: notification.recipientId,
          provider: preferredProvider
        })
      }

      return result

    } catch (error) {
      const errorMessage = errorUtils.createErrorMessage(error)
      logger.error('Push notification send failed', { error: errorMessage, notification })

      return {
        success: false,
        error: errorMessage,
        metadata: {
          provider: notificationConfig.getPreferredPushProvider(),
          retryable: errorUtils.isRetryableError(errorMessage)
        }
      }
    }
  }

  /**
   * Send push notification via Firebase Cloud Messaging (FCM)
   */
  private async sendViaFCM(
    notification: NotificationRequest,
    content: string,
    config: { serverKey: string; senderId: string }
  ): Promise<NotificationResult> {
    try {
      // Get device tokens for the user
      const deviceTokens = await this.getDeviceTokens(notification.recipientId!)
      
      if (!deviceTokens || deviceTokens.length === 0) {
        return {
          success: false,
          error: 'No device tokens found for user'
        }
      }

      const payload = {
        registration_ids: deviceTokens,
        notification: {
          title: notification.title,
          body: content,
          icon: '/icon-192x192.png',
          badge: '/icon-192x192.png',
          click_action: notification.data?.clickAction || '/',
          tag: notification.data?.tag || 'default'
        },
        data: {
          notificationId: notification.data?.notificationId || '',
          type: notification.type,
          timestamp: new Date().toISOString(),
          ...notification.data?.customData
        },
        android: {
          priority: 'high',
          notification: {
            sound: 'default',
            color: '#FF6B35'
          }
        },
        apns: {
          headers: {
            'apns-priority': '10'
          },
          payload: {
            aps: {
              sound: 'default',
              badge: 1
            }
          }
        }
      }

      const response = await axios.post(
        'https://fcm.googleapis.com/fcm/send',
        payload,
        {
          headers: {
            'Authorization': `key=${config.serverKey}`,
            'Content-Type': 'application/json'
          }
        }
      )

      if (response.data.success > 0) {
        return {
          success: true,
          messageId: response.data.multicast_id?.toString(),
          metadata: {
            provider: 'fcm',
            successCount: response.data.success,
            failureCount: response.data.failure,
            results: response.data.results
          }
        }
      } else {
        return {
          success: false,
          error: 'All push notifications failed',
          metadata: {
            provider: 'fcm',
            results: response.data.results
          }
        }
      }

    } catch (error) {
      if (axios.isAxiosError(error)) {
        return {
          success: false,
          error: error.response?.data?.error || error.message,
          metadata: {
            provider: 'fcm',
            statusCode: error.response?.status
          }
        }
      }
      throw error
    }
  }

  /**
   * Send push notification via Apple Push Notification Service (APNS)
   */
  private async sendViaAPNS(
    notification: NotificationRequest,
    content: string,
    config: { keyId: string; teamId: string; bundleId: string; privateKey: string; production: boolean }
  ): Promise<NotificationResult> {
    try {
      // Get device tokens for iOS devices
      const deviceTokens = await this.getIOSDeviceTokens(notification.recipientId!)
      
      if (!deviceTokens || deviceTokens.length === 0) {
        return {
          success: false,
          error: 'No iOS device tokens found for user'
        }
      }

      // APNS requires individual requests for each device token
      const results = await Promise.allSettled(
        deviceTokens.map(token => this.sendAPNSToDevice(token, notification, content, config))
      )

      const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length
      const failureCount = results.length - successCount

      if (successCount > 0) {
        return {
          success: true,
          messageId: `apns_${Date.now()}`,
          metadata: {
            provider: 'apns',
            successCount,
            failureCount,
            results: results.map(r => r.status === 'fulfilled' ? r.value : { success: false, error: 'Promise rejected' })
          }
        }
      } else {
        return {
          success: false,
          error: 'All APNS notifications failed',
          metadata: {
            provider: 'apns',
            results
          }
        }
      }

    } catch (error) {
      throw error
    }
  }

  /**
   * Send APNS notification to a single device
   */
  private async sendAPNSToDevice(
    deviceToken: string,
    notification: NotificationRequest,
    content: string,
    config: { keyId: string; teamId: string; bundleId: string; privateKey: string; production: boolean }
  ): Promise<NotificationResult> {
    try {
      // In a real implementation, you would:
      // 1. Generate JWT token using the private key
      // 2. Send HTTP/2 request to APNS
      // 3. Handle the response
      
      // This is a simplified placeholder implementation
      const payload = {
        aps: {
          alert: {
            title: notification.title,
            body: content
          },
          badge: 1,
          sound: 'default'
        },
        customData: notification.data?.customData || {}
      }

      // Placeholder for actual APNS HTTP/2 request
      logger.info('Would send APNS notification', {
        deviceToken,
        payload,
        production: config.production
      })

      return {
        success: true,
        messageId: `apns_${deviceToken}_${Date.now()}`,
        metadata: {
          provider: 'apns',
          deviceToken,
          production: config.production
        }
      }

    } catch (error) {
      return {
        success: false,
        error: errorUtils.createErrorMessage(error),
        metadata: {
          provider: 'apns',
          deviceToken
        }
      }
    }
  }

  /**
   * Get device tokens for a user (placeholder implementation)
   */
  private async getDeviceTokens(userId: string): Promise<string[]> {
    // In a real implementation, this would query the database
    // for device tokens associated with the user
    
    // Placeholder implementation
    return []
  }

  /**
   * Get iOS device tokens for a user (placeholder implementation)
   */
  private async getIOSDeviceTokens(userId: string): Promise<string[]> {
    // In a real implementation, this would query the database
    // for iOS device tokens associated with the user
    
    // Placeholder implementation
    return []
  }

  /**
   * Validate push notification
   */
  private validatePushNotification(notification: NotificationRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (notification.channel !== NotificationChannel.PUSH) {
      errors.push('Invalid channel for push service')
    }

    if (!notification.recipientId) {
      errors.push('Recipient ID is required for push notifications')
    }

    if (!notification.title) {
      errors.push('Title is required for push notifications')
    }

    if (!notification.content) {
      errors.push('Content is required for push notifications')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Validate push service configuration
   */
  async validateConfig(): Promise<boolean> {
    const pushConfig = notificationConfig.getPushConfig()
    const preferredProvider = notificationConfig.getPreferredPushProvider()

    if (!preferredProvider) {
      return false
    }

    switch (preferredProvider) {
      case 'fcm':
        return !!(pushConfig.fcm?.serverKey && pushConfig.fcm?.senderId)
      case 'apns':
        return !!(
          pushConfig.apns?.keyId && 
          pushConfig.apns?.teamId && 
          pushConfig.apns?.bundleId && 
          pushConfig.apns?.privateKey
        )
      default:
        return false
    }
  }

  /**
   * Get delivery status for push notification
   */
  async getDeliveryStatus(messageId: string): Promise<DeliveryStatus> {
    try {
      // Push notification delivery status would require
      // tracking through the respective provider's APIs
      
      return {
        status: NotificationStatus.SENT,
        timestamp: new Date(),
        metadata: {
          messageId,
          provider: notificationConfig.getPreferredPushProvider()
        }
      }

    } catch (error) {
      logger.error('Failed to get push delivery status', { error, messageId })
      return {
        status: NotificationStatus.FAILED,
        error: errorUtils.createErrorMessage(error)
      }
    }
  }

  /**
   * Send test push notification
   */
  async sendTest(recipientId: string): Promise<NotificationResult> {
    const testNotification: NotificationRequest = {
      type: 'CUSTOM' as any,
      channel: NotificationChannel.PUSH,
      title: 'Test Push Notification',
      content: `Test push from Coco Milk Kids. Provider: ${notificationConfig.getPreferredPushProvider()}`,
      recipientId,
      data: {
        test: true,
        timestamp: new Date().toISOString(),
        clickAction: '/admin/notifications'
      }
    }

    return this.send(testNotification)
  }

  /**
   * Register device token for push notifications
   */
  async registerDeviceToken(userId: string, deviceToken: string, platform: 'ios' | 'android'): Promise<boolean> {
    try {
      // In a real implementation, this would save the device token
      // to the database associated with the user
      
      logger.info('Device token registered', {
        userId,
        platform,
        tokenPreview: deviceToken.substring(0, 10) + '...'
      })

      return true

    } catch (error) {
      logger.error('Failed to register device token', { error, userId, platform })
      return false
    }
  }

  /**
   * Unregister device token
   */
  async unregisterDeviceToken(userId: string, deviceToken: string): Promise<boolean> {
    try {
      // In a real implementation, this would remove the device token
      // from the database
      
      logger.info('Device token unregistered', {
        userId,
        tokenPreview: deviceToken.substring(0, 10) + '...'
      })

      return true

    } catch (error) {
      logger.error('Failed to unregister device token', { error, userId })
      return false
    }
  }

  /**
   * Get push service statistics
   */
  async getStats(): Promise<{
    provider: string | null
    configured: boolean
    lastTest?: Date
    totalSent?: number
    registeredDevices?: number
  }> {
    return {
      provider: notificationConfig.getPreferredPushProvider(),
      configured: await this.validateConfig(),
      // In a real implementation, these would come from database
      totalSent: 0,
      registeredDevices: 0
    }
  }
}
