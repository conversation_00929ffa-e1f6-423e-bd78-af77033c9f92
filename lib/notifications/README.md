# Coco Milk Kids - Notification System

A comprehensive, production-grade notification system built for the South African e-commerce market, supporting multiple channels, real-time updates, and advanced management features.

## 🌟 Features

### Multi-Channel Support
- **Email**: SMTP, SendGrid, Mailgun
- **SMS**: Clickatell (SA), Bulk SMS (SA), Twilio
- **Push Notifications**: FCM, APNS
- **In-App Notifications**: Real-time with SSE
- **Webhooks**: Custom integrations

### Advanced Capabilities
- ✅ Template engine with variable substitution
- ✅ Background queue processing with retry logic
- ✅ Real-time notifications via Server-Sent Events
- ✅ User preference management
- ✅ Campaign management and analytics
- ✅ Rate limiting and security
- ✅ South African provider optimization
- ✅ Comprehensive logging and monitoring

## 📁 Project Structure

```
lib/notifications/
├── components/           # React components
│   ├── notification-bell.tsx
│   ├── notification-center.tsx
│   ├── notification-item.tsx
│   ├── notification-settings.tsx
│   └── index.ts
├── hooks/               # React hooks
│   ├── use-notifications.ts
│   ├── use-real-time-notifications.ts
│   ├── use-notification-center.ts
│   └── index.ts
├── services/            # Notification services
│   ├── email.ts
│   ├── sms.ts
│   ├── push.ts
│   └── in-app.ts
├── templates/           # Template engine
│   └── engine.ts
├── queue/              # Background processing
│   └── processor.ts
├── prisma/             # Database schema
│   └── schema.prisma
├── manager.ts          # Core manager
├── config.ts           # Configuration
├── types.ts            # TypeScript types
├── utils.ts            # Utilities
├── index.ts            # Main exports
└── README.md           # This file
```

## 🚀 Quick Start

### 1. Installation

Add the notification system to your project:

```bash
# Install dependencies
pnpm add @prisma/client date-fns lucide-react
pnpm add -D prisma

# Add the notification schema to your Prisma schema
# Copy contents from lib/notifications/prisma/schema.prisma
```

### 2. Environment Configuration

Create or update your `.env` file:

```env
# Database
DATABASE_URL="your-database-url"

# Email Configuration (choose one)
# SendGrid (Recommended)
SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME="Coco Milk Kids"

# SMTP Alternative
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# SMS Configuration (South African providers)
# Clickatell (Recommended for SA)
CLICKATELL_API_KEY=your-clickatell-api-key
CLICKATELL_FROM_NUMBER=+***********

# Bulk SMS (Alternative SA provider)
BULK_SMS_USERNAME=your-bulk-sms-username
BULK_SMS_PASSWORD=your-bulk-sms-password
BULK_SMS_FROM_NUMBER=+***********

# Push Notifications
FCM_SERVER_KEY=your-fcm-server-key
FCM_SENDER_ID=your-fcm-sender-id

# APNS (iOS)
APNS_KEY_ID=your-apns-key-id
APNS_TEAM_ID=your-apns-team-id
APNS_BUNDLE_ID=com.cocomilkkids.app
APNS_PRIVATE_KEY=your-apns-private-key
APNS_PRODUCTION=false

# Notification Settings
NOTIFICATION_FROM_EMAIL=<EMAIL>
NOTIFICATION_FROM_NAME="Coco Milk Kids"
NOTIFICATION_FROM_NUMBER=+***********
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_BATCH_SIZE=100
NOTIFICATION_RATE_LIMIT=1000
```

### 3. Database Setup

Run Prisma migrations:

```bash
npx prisma db push
npx prisma generate
```

### 4. Basic Usage

#### Send a Simple Notification

```typescript
import { notificationManager, NotificationChannel, NotificationType } from '@/lib/notifications'

// Send email notification
await notificationManager.send({
  type: NotificationType.ORDER_CONFIRMATION,
  channel: NotificationChannel.EMAIL,
  title: 'Order Confirmed',
  content: 'Your order #12345 has been confirmed!',
  recipientEmail: '<EMAIL>'
})
```

#### Use React Components

```tsx
import { NotificationBell, NotificationCenter } from '@/lib/notifications'

function Header() {
  return (
    <div className="header">
      <NotificationBell 
        userId="user123" 
        enableRealTime={true}
      />
    </div>
  )
}

function Dashboard() {
  return (
    <NotificationCenter 
      userId="user123"
      enableRealTime={true}
    />
  )
}
```

#### Use Hooks

```tsx
import { useNotifications, useNotificationCenter } from '@/lib/notifications'

function NotificationsList() {
  const {
    notifications,
    unreadCount,
    markAsRead,
    loading
  } = useNotifications({ userId: 'user123' })

  if (loading) return <div>Loading...</div>

  return (
    <div>
      <h2>Notifications ({unreadCount} unread)</h2>
      {notifications.map(notification => (
        <div 
          key={notification.id}
          onClick={() => markAsRead(notification.id)}
        >
          <h3>{notification.title}</h3>
          <p>{notification.content}</p>
        </div>
      ))}
    </div>
  )
}
```

## 📧 Email Configuration

### SendGrid (Recommended)

1. Sign up at [SendGrid](https://sendgrid.com)
2. Create an API key
3. Verify your sender domain
4. Add to environment variables

### SMTP (Alternative)

Works with Gmail, Outlook, or any SMTP provider:

```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

## 📱 SMS Configuration (South African Providers)

### Clickatell (Recommended)

Clickatell is a leading South African SMS provider:

1. Sign up at [Clickatell](https://www.clickatell.com)
2. Create an API integration
3. Get your API key
4. Add to environment variables

```env
CLICKATELL_API_KEY=your-api-key
CLICKATELL_FROM_NUMBER=+***********
```

### Bulk SMS (Alternative)

Another popular South African provider:

1. Sign up at [Bulk SMS](https://www.bulksms.com)
2. Get your username and password
3. Add to environment variables

```env
BULK_SMS_USERNAME=your-username
BULK_SMS_PASSWORD=your-password
BULK_SMS_FROM_NUMBER=+***********
```

## 🔔 Push Notifications

### Firebase Cloud Messaging (FCM)

1. Create a Firebase project
2. Enable Cloud Messaging
3. Get your server key and sender ID
4. Add to environment variables

### Apple Push Notification Service (APNS)

1. Create an Apple Developer account
2. Generate an APNS key
3. Get your key ID, team ID, and bundle ID
4. Add to environment variables

## 🎨 Components

### NotificationBell

A bell icon with unread count badge:

```tsx
<NotificationBell 
  userId="user123"
  enableRealTime={true}
  showBadge={true}
  maxBadgeCount={99}
/>
```

### NotificationCenter

Complete notification management interface:

```tsx
<NotificationCenter 
  userId="user123"
  enableRealTime={true}
  showSettings={true}
  maxHeight="600px"
/>
```

### NotificationItem

Individual notification display:

```tsx
<NotificationItem 
  notification={notification}
  onMarkAsRead={handleMarkAsRead}
  onDelete={handleDelete}
  showActions={true}
/>
```

## 🎣 Hooks

### useNotifications

Main hook for notification management:

```tsx
const {
  notifications,
  unreadCount,
  loading,
  error,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  refresh
} = useNotifications({ userId: 'user123' })
```

### useRealTimeNotifications

Real-time updates via Server-Sent Events:

```tsx
const {
  notifications,
  unreadCount,
  connected,
  error
} = useRealTimeNotifications({ 
  userId: 'user123',
  enabled: true 
})
```

### useNotificationCenter

Advanced notification center with filtering:

```tsx
const {
  notifications,
  groupedNotifications,
  unreadCount,
  activeFilters,
  setTypeFilter,
  setChannelFilter,
  stats
} = useNotificationCenter({ userId: 'user123' })
```

## 📊 API Endpoints

### Core Endpoints

- `GET /api/notifications` - List notifications
- `POST /api/notifications/send` - Send notification
- `PATCH /api/notifications/[id]/read` - Mark as read
- `DELETE /api/notifications/[id]` - Delete notification
- `GET /api/notifications/stream` - Real-time SSE stream
- `GET /api/notifications/stats` - Statistics
- `GET /api/notifications/preferences` - User preferences

### Example API Usage

```typescript
// Send notification via API
const response = await fetch('/api/notifications/send', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    type: 'ORDER_CONFIRMATION',
    channel: 'EMAIL',
    title: 'Order Confirmed',
    content: 'Your order has been confirmed!',
    recipientEmail: '<EMAIL>'
  })
})

const result = await response.json()
```

## 🔧 Advanced Configuration

### Template System

Create custom templates:

```typescript
import { TemplateEngine } from '@/lib/notifications'

const templateEngine = new TemplateEngine()

await templateEngine.saveTemplate({
  id: 'order_shipped',
  name: 'Order Shipped',
  type: 'SHIPPING_UPDATE',
  channel: 'EMAIL',
  subject: 'Your order {{order.number}} has shipped!',
  content: 'Hi {{customer.firstName}}, your order is on its way!',
  variables: ['order.number', 'customer.firstName']
})
```

### Queue Processing

The system includes automatic background processing with retry logic:

```typescript
import { NotificationQueue } from '@/lib/notifications'

const queue = new NotificationQueue()

// Queue will automatically process notifications
// with exponential backoff retry logic
```

### Real-time Updates

Server-Sent Events provide real-time notifications:

```typescript
// Client automatically connects to /api/notifications/stream
// and receives real-time updates for new notifications,
// read status changes, and deletions
```

## 🔒 Security Features

- Rate limiting per user/admin
- Authentication required for all endpoints
- Authorization checks for user data access
- Input validation with Zod schemas
- SQL injection protection via Prisma
- XSS protection in templates

## 📈 Monitoring & Analytics

### Built-in Analytics

- Delivery rates by channel
- Read rates by notification type
- Failed delivery tracking
- User engagement metrics
- Campaign performance

### Health Checks

```typescript
import { healthCheck } from '@/lib/notifications'

const health = await healthCheck()
console.log(health.services) // Service status
```

## 🐛 Troubleshooting

### Common Issues

1. **Email not sending**
   - Check SMTP/SendGrid credentials
   - Verify sender domain
   - Check spam folders

2. **SMS not delivering**
   - Verify phone number format (+27...)
   - Check provider credits
   - Validate API credentials

3. **Real-time not working**
   - Check SSE endpoint accessibility
   - Verify authentication
   - Check browser console for errors

4. **Database errors**
   - Run `npx prisma db push`
   - Check database connection
   - Verify schema is up to date

### Debug Mode

Enable debug logging:

```env
NODE_ENV=development
DEBUG=notifications:*
```

## 🤝 Contributing

1. Follow existing code patterns
2. Add tests for new features
3. Update documentation
4. Use TypeScript strictly
5. Follow South African compliance requirements

## 📄 License

This notification system is part of the Coco Milk Kids e-commerce platform.

---

For support or questions, contact the development team.
