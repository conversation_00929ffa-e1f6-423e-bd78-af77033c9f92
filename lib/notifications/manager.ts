import {
  NotificationRequest,
  NotificationResult,
  NotificationChannel,
  NotificationStatus,
  NotificationRecord,
  NotificationService,
  NotificationAnalytics,
  NotificationPriority,
  RecipientType
} from './types'
import { EmailService } from './services/email'
import { SMSService } from './services/sms'
import { PushService } from './services/push'
import { InAppService } from './services/in-app'
import { TemplateEngine } from './templates/engine'
import { NotificationQueue } from './queue/processor'
import { notificationConfig } from './config'
import { logger, validators, priorityUtils, errorUtils } from './utils'
import { prisma } from '../prisma'

/**
 * Central Notification Manager
 * Handles all notification operations across different channels
 */
export class NotificationManager {
  private services: Map<NotificationChannel, NotificationService>
  private templateEngine: TemplateEngine
  private queue: NotificationQueue
  private config = notificationConfig

  constructor() {
    this.services = new Map()
    this.templateEngine = new TemplateEngine()
    this.queue = new NotificationQueue()
    this.initializeServices()
  }

  /**
   * Initialize notification services for each channel
   */
  private initializeServices(): void {
    this.services.set(NotificationChannel.EMAIL, new EmailService())
    this.services.set(NotificationChannel.SMS, new SMSService())
    this.services.set(NotificationChannel.PUSH, new PushService())
    this.services.set(NotificationChannel.IN_APP, new InAppService())
  }

  /**
   * Send a notification immediately
   */
  async send(request: NotificationRequest): Promise<NotificationResult> {
    try {
      logger.info('Sending notification', { 
        type: request.type, 
        channel: request.channel,
        recipient: request.recipientEmail || request.recipientPhone || request.recipientId
      })

      // Validate request
      const validation = await this.validateRequest(request)
      if (!validation.isValid) {
        return {
          success: false,
          error: `Validation failed: ${validation.errors.join(', ')}`
        }
      }

      // Get service for channel
      const service = this.services.get(request.channel)
      if (!service) {
        return {
          success: false,
          error: `No service available for channel: ${request.channel}`
        }
      }

      // Process template if templateId provided
      let processedRequest = request
      if (request.templateId) {
        processedRequest = await this.processTemplate(request)
      }

      // Send notification
      const result = await service.send(processedRequest)
      
      // Log result
      if (result.success) {
        logger.info('Notification sent successfully', { 
          notificationId: result.notificationId,
          messageId: result.messageId
        })
      } else {
        logger.error('Notification failed', { 
          error: result.error,
          request: processedRequest
        })
      }

      return result

    } catch (error) {
      logger.error('Notification manager error', { error, request })
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Schedule a notification for later delivery
   */
  async schedule(request: NotificationRequest): Promise<NotificationResult> {
    try {
      if (!request.scheduledAt) {
        return {
          success: false,
          error: 'scheduledAt is required for scheduled notifications'
        }
      }

      // Create notification record
      const notification = await this.createNotificationRecord(request)
      
      // Add to queue
      await this.queue.enqueue(notification)

      logger.info('Notification scheduled', { 
        notificationId: notification.id,
        scheduledAt: request.scheduledAt
      })

      return {
        success: true,
        notificationId: notification.id
      }

    } catch (error) {
      logger.error('Schedule notification error', { error, request })
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Send bulk notifications
   */
  async sendBulk(requests: NotificationRequest[]): Promise<NotificationResult[]> {
    const results: NotificationResult[] = []
    const batchSize = this.config.getBatchSize()

    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize)
      const batchPromises = batch.map(request => this.send(request))
      const batchResults = await Promise.allSettled(batchPromises)
      
      batchResults.forEach(result => {
        if (result.status === 'fulfilled') {
          results.push(result.value)
        } else {
          results.push({
            success: false,
            error: result.reason?.message || 'Unknown error'
          })
        }
      })

      // Rate limiting delay between batches
      if (i + batchSize < requests.length) {
        await this.delay(this.config.getBatchDelay())
      }
    }

    return results
  }

  /**
   * Cancel a scheduled notification
   */
  async cancel(notificationId: string): Promise<boolean> {
    try {
      await this.queue.cancel(notificationId)
      logger.info('Notification cancelled', { notificationId })
      return true
    } catch (error) {
      logger.error('Cancel notification error', { error, notificationId })
      return false
    }
  }

  /**
   * Retry a failed notification
   */
  async retry(notificationId: string): Promise<NotificationResult> {
    try {
      await this.queue.retry(notificationId)
      return { success: true, notificationId }
    } catch (error) {
      logger.error('Retry notification error', { error, notificationId })
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get notification analytics
   */
  async getAnalytics(filters?: {
    startDate?: Date
    endDate?: Date
    channel?: NotificationChannel
    type?: string
  }): Promise<NotificationAnalytics> {
    // Implementation would query database for analytics
    // This is a placeholder implementation
    return {
      totalSent: 0,
      totalDelivered: 0,
      totalOpened: 0,
      totalClicked: 0,
      totalBounced: 0,
      deliveryRate: 0,
      openRate: 0,
      clickRate: 0,
      bounceRate: 0,
      byChannel: [],
      byTemplate: [],
      byTimeOfDay: []
    }
  }

  /**
   * Validate notification request
   */
  private async validateRequest(request: NotificationRequest): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = []

    if (!request.channel) {
      errors.push('Channel is required')
    }

    if (!request.title && !request.templateId) {
      errors.push('Title or templateId is required')
    }

    if (!request.content && !request.templateId) {
      errors.push('Content or templateId is required')
    }

    // Channel-specific validation
    if (request.channel === NotificationChannel.EMAIL && !request.recipientEmail) {
      errors.push('recipientEmail is required for email notifications')
    }

    if (request.channel === NotificationChannel.SMS && !request.recipientPhone) {
      errors.push('recipientPhone is required for SMS notifications')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Process template and merge with request data
   */
  private async processTemplate(request: NotificationRequest): Promise<NotificationRequest> {
    if (!request.templateId) return request

    const template = await this.templateEngine.getTemplate(request.templateId)
    if (!template) {
      throw new Error(`Template not found: ${request.templateId}`)
    }

    const rendered = await this.templateEngine.render(template, request.data || {})
    
    return {
      ...request,
      title: rendered.subject || request.title,
      content: rendered.content
    }
  }

  /**
   * Create notification record in database
   */
  private async createNotificationRecord(request: NotificationRequest): Promise<NotificationRecord> {
    try {
      const notification = await prisma.notification.create({
        data: {
          type: request.type as any,
          channel: request.channel as any,
          title: request.title,
          content: request.content,
          data: request.data || {},
          recipientId: request.recipientId,
          recipientType: (request.recipientType as any) || 'CUSTOMER',
          recipientEmail: request.recipientEmail,
          recipientPhone: request.recipientPhone,
          status: NotificationStatus.PENDING,
          priority: request.priority || NotificationPriority.NORMAL,
          templateId: request.templateId,
          scheduledAt: request.scheduledAt,
          retryCount: 0,
          maxRetries: 3,
          metadata: request.metadata || {},
          expiresAt: request.expiresAt
        }
      })

      return notification as NotificationRecord
    } catch (error) {
      logger.error('Failed to create notification record', { error, request })
      throw new Error('Failed to create notification record')
    }
  }

  /**
   * Update notification status in database
   */
  private async updateNotificationStatus(
    notificationId: string,
    status: NotificationStatus,
    error?: string,
    messageId?: string
  ): Promise<void> {
    try {
      const updateData: any = {
        status,
        updatedAt: new Date()
      }

      if (status === NotificationStatus.SENT) {
        updateData.sentAt = new Date()
      } else if (status === NotificationStatus.DELIVERED) {
        updateData.deliveredAt = new Date()
      } else if (status === NotificationStatus.FAILED) {
        updateData.failedAt = new Date()
        updateData.error = error
      }

      if (messageId) {
        updateData.metadata = {
          messageId
        }
      }

      await prisma.notification.update({
        where: { id: notificationId },
        data: updateData
      })

      // Log the status change
      await prisma.notificationLog.create({
        data: {
          notificationId,
          event: status.toLowerCase(),
          details: error || `Status changed to ${status}`,
          metadata: { messageId }
        }
      })

    } catch (dbError) {
      logger.error('Failed to update notification status', {
        error: dbError,
        notificationId,
        status
      })
    }
  }

  /**
   * Get notification by ID from database
   */
  private async getNotificationById(notificationId: string): Promise<NotificationRecord | null> {
    try {
      const notification = await prisma.notification.findUnique({
        where: { id: notificationId },
        include: {
          template: {
            select: {
              name: true,
              category: true
            }
          }
        }
      })

      return notification as NotificationRecord | null
    } catch (error) {
      logger.error('Failed to get notification by ID', { error, notificationId })
      return null
    }
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Export singleton instance
export const notificationManager = new NotificationManager()
