/**
 * Coco Milk Kids - Notification System
 *
 * A comprehensive, production-grade notification system supporting:
 * - Email notifications (SMTP, SendGrid, Mailgun)
 * - SMS notifications (<PERSON><PERSON>atell, Bulk SMS, Twilio)
 * - Push notifications (FCM, APNS)
 * - In-app notifications
 * - Template engine with variable substitution
 * - Background queue processing
 * - Real-time updates
 * - Analytics and monitoring
 */

// Core Manager
export { NotificationManager, notificationManager } from './manager'

// Configuration
export { NotificationConfig, notificationConfig } from './config'

// Services
export { EmailService } from './services/email'
export { SMSService } from './services/sms'
export { PushService } from './services/push'
export { InAppService } from './services/in-app'

// Template Engine
export { TemplateEngine } from './templates/engine'

// Queue Processor
export { NotificationQueue } from './queue/processor'

// React Hooks
export * from './hooks'

// React Components
export * from './components'

// Utilities
export {
  logger,
  validators,
  formatters,
  templateUtils,
  priorityUtils,
  statusUtils,
  retryUtils,
  channelUtils,
  typeUtils,
  errorUtils
} from './utils'

// Types and Interfaces
export type {
  // Core Types
  NotificationRequest,
  NotificationResult,
  NotificationRecord,
  NotificationTemplateRecord,
  NotificationCampaignRecord,
  NotificationPreferenceRecord,
  
  // Service Interfaces
  NotificationService,
  TemplateEngine as ITemplateEngine,
  NotificationQueue as INotificationQueue,
  
  // Configuration Types
  EmailProviderConfig,
  SMSProviderConfig,
  PushProviderConfig,
  NotificationConfig as INotificationConfig,
  
  // Utility Types
  RenderedTemplate,
  ValidationResult,
  QueueStats,
  DeliveryStatus,
  NotificationAnalytics,
  
  // Legacy Types (for backward compatibility)
  EmailRequest,
  EmailLog,
  SMSRequest,
  SMSLog,
  PushNotificationRequest,
  PushNotificationLog,
  InAppNotification,
  NotificationTemplate,
  NotificationEvent,
  NotificationRule,
  NotificationCondition,
  NotificationAction,
  NotificationPreferences
} from './types'

// Enums
export {
  // Core Enums
  NotificationType,
  NotificationChannel,
  NotificationStatus,
  NotificationPriority,
  RecipientType,
  CampaignType,
  CampaignStatus,
  
  // Legacy Enums (for backward compatibility)
  EmailStatus,
  EmailProvider,
  SMSStatus,
  SMSProvider,
  SMSPriority,
  PushNotificationStatus,
  PushNotificationProvider,
  InAppNotificationStatus,
  NotificationEventType,
  NotificationEventStatus,
  NotificationFrequency,
  ConditionOperator,
  NotificationActionType
} from './types'

/**
 * Quick Start Examples
 */

// Example 1: Send a simple email notification
/*
import { notificationManager, NotificationChannel, NotificationType } from '@/lib/notifications'

await notificationManager.send({
  type: NotificationType.ORDER_CONFIRMATION,
  channel: NotificationChannel.EMAIL,
  title: 'Order Confirmation',
  content: 'Your order has been confirmed!',
  recipientEmail: '<EMAIL>'
})
*/

// Example 2: Send using a template
/*
import { notificationManager, NotificationChannel, NotificationType } from '@/lib/notifications'

await notificationManager.send({
  type: NotificationType.ORDER_CONFIRMATION,
  channel: NotificationChannel.EMAIL,
  templateId: 'order_confirmation_email',
  recipientEmail: '<EMAIL>',
  data: {
    customer: { firstName: 'John' },
    order: { number: 'ORD-12345', total: 'R 299.99' }
  }
})
*/

// Example 3: Schedule a notification
/*
import { notificationManager, NotificationChannel, NotificationType } from '@/lib/notifications'

await notificationManager.schedule({
  type: NotificationType.PROMOTIONAL,
  channel: NotificationChannel.EMAIL,
  title: 'Special Offer!',
  content: 'Don\'t miss our weekend sale!',
  recipientEmail: '<EMAIL>',
  scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
})
*/

// Example 4: Send bulk notifications
/*
import { notificationManager, NotificationChannel, NotificationType } from '@/lib/notifications'

const notifications = customers.map(customer => ({
  type: NotificationType.NEWSLETTER,
  channel: NotificationChannel.EMAIL,
  templateId: 'monthly_newsletter',
  recipientEmail: customer.email,
  data: { customer }
}))

await notificationManager.sendBulk(notifications)
*/

// Example 5: In-app notifications with real-time updates
/*
import { InAppService } from '@/lib/notifications'

const inAppService = new InAppService()

// Subscribe to real-time updates
const unsubscribe = inAppService.subscribe(userId, (notifications) => {
  console.log('New notifications:', notifications)
})

// Send in-app notification
await inAppService.send({
  type: NotificationType.SYSTEM_ALERT,
  channel: NotificationChannel.IN_APP,
  title: 'System Maintenance',
  content: 'Scheduled maintenance will begin in 30 minutes.',
  recipientId: userId
})

// Get unread count
const unreadCount = await inAppService.getUnreadCount(userId)

// Mark as read
await inAppService.markAsRead(userId, notificationId)
*/

// Example 6: Template management
/*
import { TemplateEngine } from '@/lib/notifications'

const templateEngine = new TemplateEngine()

// Create a new template
const template = {
  id: 'custom_template',
  name: 'Custom Template',
  type: NotificationType.CUSTOM,
  channel: NotificationChannel.EMAIL,
  subject: 'Hello {{customer.firstName}}!',
  content: 'Welcome to our store, {{customer.firstName}}!',
  variables: ['customer.firstName'],
  isActive: true,
  isSystem: false,
  category: 'Marketing',
  tags: ['welcome'],
  metadata: {},
  createdAt: new Date(),
  updatedAt: new Date()
}

await templateEngine.saveTemplate(template)

// Render template
const rendered = await templateEngine.render(template, {
  customer: { firstName: 'John' }
})
*/

/**
 * Configuration Examples
 */

// Environment Variables for Email (SMTP)
/*
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
*/

// Environment Variables for Email (SendGrid)
/*
SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Coco Milk Kids
*/

// Environment Variables for SMS (Clickatell - South African)
/*
CLICKATELL_API_KEY=your-clickatell-api-key
CLICKATELL_FROM_NUMBER=+27123456789
*/

// Environment Variables for Push Notifications (FCM)
/*
FCM_SERVER_KEY=your-fcm-server-key
FCM_SENDER_ID=your-fcm-sender-id
*/

/**
 * Default Configuration
 */
export const defaultConfig = {
  retryAttempts: 3,
  retryDelay: 60000, // 1 minute
  batchSize: 100,
  rateLimitPerMinute: 1000,
  defaultFromEmail: '<EMAIL>',
  defaultFromName: 'Coco Milk Kids',
  defaultFromNumber: '+27123456789'
}

/**
 * Utility Functions for Common Operations
 */

/**
 * Send order confirmation notification
 */
export async function sendOrderConfirmation(order: any, customer: any) {
  return notificationManager.send({
    type: NotificationType.ORDER_CONFIRMATION,
    channel: NotificationChannel.EMAIL,
    templateId: 'order_confirmation_email',
    recipientEmail: customer.email,
    data: { order, customer }
  })
}

/**
 * Send welcome notification to new customer
 */
export async function sendWelcomeNotification(customer: any) {
  return notificationManager.send({
    type: NotificationType.WELCOME,
    channel: NotificationChannel.EMAIL,
    templateId: 'welcome_email',
    recipientEmail: customer.email,
    data: { customer }
  })
}

/**
 * Send password reset notification
 */
export async function sendPasswordReset(email: string, resetToken: string) {
  return notificationManager.send({
    type: NotificationType.PASSWORD_RESET,
    channel: NotificationChannel.EMAIL,
    title: 'Password Reset Request',
    content: `Click here to reset your password: ${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${resetToken}`,
    recipientEmail: email,
    data: { resetToken, resetUrl: `${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${resetToken}` }
  })
}

/**
 * Send inventory alert to admin
 */
export async function sendInventoryAlert(product: any, currentStock: number, threshold: number) {
  return notificationManager.send({
    type: NotificationType.INVENTORY_ALERT,
    channel: NotificationChannel.EMAIL,
    title: 'Low Stock Alert',
    content: `Product "${product.name}" is running low. Current stock: ${currentStock}, Threshold: ${threshold}`,
    recipientEmail: process.env.ADMIN_EMAIL || '<EMAIL>',
    data: { product, currentStock, threshold }
  })
}

/**
 * Health check for notification system
 */
export async function healthCheck() {
  const config = notificationConfig.getConfig()
  
  return {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      email: {
        configured: notificationConfig.isEmailConfigured(),
        provider: notificationConfig.getPreferredEmailProvider()
      },
      sms: {
        configured: notificationConfig.isSMSConfigured(),
        provider: notificationConfig.getPreferredSMSProvider()
      },
      push: {
        configured: notificationConfig.isPushConfigured(),
        provider: notificationConfig.getPreferredPushProvider()
      },
      inApp: {
        configured: true,
        provider: 'in_app'
      }
    },
    config: {
      retryAttempts: config.retryAttempts,
      batchSize: config.batchSize,
      rateLimitPerMinute: config.rateLimitPerMinute
    }
  }
}
