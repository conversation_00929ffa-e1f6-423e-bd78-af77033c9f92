import { 
  NotificationQueue as INotificationQueue,
  NotificationRecord,
  QueueStats,
  NotificationStatus,
  NotificationPriority
} from '../types'
import { notificationManager } from '../manager'
import { logger, priorityUtils, retryUtils, errorUtils } from '../utils'

interface QueueItem {
  id: string
  notificationId: string
  notification: NotificationRecord
  priority: number
  attempts: number
  maxAttempts: number
  nextAttemptAt: Date
  lockedAt?: Date
  lockedBy?: string
  error?: string
  createdAt: Date
}

/**
 * Notification Queue Processor
 * Handles background processing of scheduled notifications
 */
export class NotificationQueue implements INotificationQueue {
  private queue: QueueItem[] = []
  private processing: boolean = false
  private processingInterval: NodeJS.Timeout | null = null
  private lockId: string
  private maxConcurrentJobs: number = 5
  private processingJobs: Set<string> = new Set()

  constructor() {
    this.lockId = `processor_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    this.startProcessing()
  }

  /**
   * Add notification to queue
   */
  async enqueue(notification: NotificationRecord): Promise<void> {
    try {
      const queueItem: QueueItem = {
        id: `queue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        notificationId: notification.id,
        notification,
        priority: priorityUtils.getPriorityWeight(notification.priority),
        attempts: 0,
        maxAttempts: notification.maxRetries,
        nextAttemptAt: notification.scheduledAt || new Date(),
        createdAt: new Date()
      }

      // Insert in priority order
      this.insertByPriority(queueItem)

      logger.info('Notification queued', {
        queueId: queueItem.id,
        notificationId: notification.id,
        priority: notification.priority,
        scheduledAt: notification.scheduledAt
      })

    } catch (error) {
      logger.error('Failed to enqueue notification', { error, notificationId: notification.id })
      throw error
    }
  }

  /**
   * Get next notification from queue
   */
  async dequeue(): Promise<NotificationRecord | null> {
    try {
      const now = new Date()
      
      // Find next available item
      const availableItem = this.queue.find(item => 
        !item.lockedAt && 
        item.nextAttemptAt <= now &&
        !this.processingJobs.has(item.id)
      )

      if (!availableItem) {
        return null
      }

      // Lock the item
      availableItem.lockedAt = now
      availableItem.lockedBy = this.lockId
      this.processingJobs.add(availableItem.id)

      logger.debug('Notification dequeued', {
        queueId: availableItem.id,
        notificationId: availableItem.notificationId
      })

      return availableItem.notification

    } catch (error) {
      logger.error('Failed to dequeue notification', { error })
      return null
    }
  }

  /**
   * Retry failed notification
   */
  async retry(notificationId: string): Promise<void> {
    try {
      const queueItem = this.queue.find(item => item.notificationId === notificationId)
      
      if (!queueItem) {
        throw new Error(`Queue item not found for notification: ${notificationId}`)
      }

      if (queueItem.attempts >= queueItem.maxAttempts) {
        throw new Error(`Maximum retry attempts exceeded for notification: ${notificationId}`)
      }

      // Reset for retry
      queueItem.lockedAt = undefined
      queueItem.lockedBy = undefined
      queueItem.nextAttemptAt = new Date(Date.now() + retryUtils.calculateRetryDelay(queueItem.attempts))
      queueItem.error = undefined
      this.processingJobs.delete(queueItem.id)

      // Re-sort queue by priority
      this.sortByPriority()

      logger.info('Notification queued for retry', {
        queueId: queueItem.id,
        notificationId,
        attempt: queueItem.attempts + 1,
        nextAttemptAt: queueItem.nextAttemptAt
      })

    } catch (error) {
      logger.error('Failed to retry notification', { error, notificationId })
      throw error
    }
  }

  /**
   * Cancel queued notification
   */
  async cancel(notificationId: string): Promise<void> {
    try {
      const index = this.queue.findIndex(item => item.notificationId === notificationId)
      
      if (index === -1) {
        throw new Error(`Queue item not found for notification: ${notificationId}`)
      }

      const queueItem = this.queue[index]
      
      // Remove from processing jobs if it's being processed
      this.processingJobs.delete(queueItem.id)
      
      // Remove from queue
      this.queue.splice(index, 1)

      logger.info('Notification cancelled', {
        queueId: queueItem.id,
        notificationId
      })

    } catch (error) {
      logger.error('Failed to cancel notification', { error, notificationId })
      throw error
    }
  }

  /**
   * Get queue statistics
   */
  async getStats(): Promise<QueueStats> {
    const now = new Date()
    
    const pending = this.queue.filter(item => 
      !item.lockedAt && item.nextAttemptAt <= now
    ).length

    const processing = this.processingJobs.size

    const scheduled = this.queue.filter(item => 
      !item.lockedAt && item.nextAttemptAt > now
    ).length

    const failed = this.queue.filter(item => 
      item.attempts >= item.maxAttempts
    ).length

    return {
      pending,
      processing,
      completed: 0, // Would come from database in real implementation
      failed
    }
  }

  /**
   * Start background processing
   */
  private startProcessing(): void {
    if (this.processing) {
      return
    }

    this.processing = true
    this.processingInterval = setInterval(() => {
      this.processQueue()
    }, 5000) // Process every 5 seconds

    logger.info('Queue processor started', { lockId: this.lockId })
  }

  /**
   * Stop background processing
   */
  stopProcessing(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval)
      this.processingInterval = null
    }
    
    this.processing = false
    logger.info('Queue processor stopped', { lockId: this.lockId })
  }

  /**
   * Process queue items
   */
  private async processQueue(): Promise<void> {
    try {
      // Don't exceed max concurrent jobs
      if (this.processingJobs.size >= this.maxConcurrentJobs) {
        return
      }

      const notification = await this.dequeue()
      if (!notification) {
        return
      }

      // Process notification in background
      this.processNotification(notification).catch(error => {
        logger.error('Background notification processing failed', { 
          error, 
          notificationId: notification.id 
        })
      })

    } catch (error) {
      logger.error('Queue processing error', { error })
    }
  }

  /**
   * Process individual notification
   */
  private async processNotification(notification: NotificationRecord): Promise<void> {
    const queueItem = this.queue.find(item => item.notificationId === notification.id)
    if (!queueItem) {
      logger.error('Queue item not found during processing', { notificationId: notification.id })
      return
    }

    try {
      queueItem.attempts++

      // Convert to notification request format
      const request = {
        type: notification.type,
        channel: notification.channel,
        title: notification.title,
        content: notification.content,
        data: notification.data,
        recipientId: notification.recipientId,
        recipientType: notification.recipientType,
        recipientEmail: notification.recipientEmail,
        recipientPhone: notification.recipientPhone,
        templateId: notification.templateId,
        priority: notification.priority,
        metadata: notification.metadata
      }

      // Send notification
      const result = await notificationManager.send(request)

      if (result.success) {
        // Success - remove from queue
        this.removeFromQueue(queueItem.id)
        logger.info('Queued notification processed successfully', {
          queueId: queueItem.id,
          notificationId: notification.id,
          messageId: result.messageId
        })
      } else {
        // Failed - handle retry or final failure
        await this.handleFailure(queueItem, result.error || 'Unknown error')
      }

    } catch (error) {
      // Exception - handle retry or final failure
      await this.handleFailure(queueItem, errorUtils.createErrorMessage(error))
    }
  }

  /**
   * Handle notification processing failure
   */
  private async handleFailure(queueItem: QueueItem, error: string): Promise<void> {
    queueItem.error = error
    queueItem.lockedAt = undefined
    queueItem.lockedBy = undefined
    this.processingJobs.delete(queueItem.id)

    if (queueItem.attempts >= queueItem.maxAttempts) {
      // Max attempts reached - mark as failed
      logger.error('Notification failed after max attempts', {
        queueId: queueItem.id,
        notificationId: queueItem.notificationId,
        attempts: queueItem.attempts,
        error
      })
      
      // Keep in queue for monitoring but don't retry
      queueItem.nextAttemptAt = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year in future
    } else {
      // Schedule retry
      const retryDelay = retryUtils.calculateRetryDelay(queueItem.attempts - 1)
      queueItem.nextAttemptAt = new Date(Date.now() + retryDelay)
      
      logger.warn('Notification failed, scheduling retry', {
        queueId: queueItem.id,
        notificationId: queueItem.notificationId,
        attempt: queueItem.attempts,
        nextAttemptAt: queueItem.nextAttemptAt,
        error
      })
    }

    // Re-sort queue
    this.sortByPriority()
  }

  /**
   * Remove item from queue
   */
  private removeFromQueue(queueId: string): void {
    const index = this.queue.findIndex(item => item.id === queueId)
    if (index !== -1) {
      const queueItem = this.queue[index]
      this.processingJobs.delete(queueItem.id)
      this.queue.splice(index, 1)
    }
  }

  /**
   * Insert item in queue by priority
   */
  private insertByPriority(queueItem: QueueItem): void {
    let insertIndex = 0
    
    for (let i = 0; i < this.queue.length; i++) {
      if (this.queue[i].priority < queueItem.priority) {
        insertIndex = i
        break
      }
      insertIndex = i + 1
    }
    
    this.queue.splice(insertIndex, 0, queueItem)
  }

  /**
   * Sort queue by priority
   */
  private sortByPriority(): void {
    this.queue.sort((a, b) => {
      // First by priority (higher first)
      if (a.priority !== b.priority) {
        return b.priority - a.priority
      }
      
      // Then by next attempt time (earlier first)
      return a.nextAttemptAt.getTime() - b.nextAttemptAt.getTime()
    })
  }

  /**
   * Clean up old completed/failed items
   */
  async cleanup(olderThanDays: number = 7): Promise<number> {
    try {
      const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000)
      const initialLength = this.queue.length
      
      this.queue = this.queue.filter(item => {
        // Keep items that are not old failures
        return !(
          item.attempts >= item.maxAttempts && 
          item.createdAt < cutoffDate
        )
      })

      const cleanedCount = initialLength - this.queue.length
      
      if (cleanedCount > 0) {
        logger.info('Queue cleanup completed', { 
          cleanedCount, 
          remainingCount: this.queue.length 
        })
      }

      return cleanedCount

    } catch (error) {
      logger.error('Queue cleanup failed', { error })
      return 0
    }
  }

  /**
   * Get queue items for monitoring
   */
  getQueueItems(filters?: {
    status?: 'pending' | 'processing' | 'failed' | 'scheduled'
    limit?: number
  }): QueueItem[] {
    let items = [...this.queue]
    const now = new Date()

    if (filters?.status) {
      switch (filters.status) {
        case 'pending':
          items = items.filter(item => !item.lockedAt && item.nextAttemptAt <= now)
          break
        case 'processing':
          items = items.filter(item => !!item.lockedAt)
          break
        case 'failed':
          items = items.filter(item => item.attempts >= item.maxAttempts)
          break
        case 'scheduled':
          items = items.filter(item => !item.lockedAt && item.nextAttemptAt > now)
          break
      }
    }

    if (filters?.limit) {
      items = items.slice(0, filters.limit)
    }

    return items
  }
}
