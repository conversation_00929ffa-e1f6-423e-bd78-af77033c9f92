'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { NotificationRecord, NotificationPriority } from '../types'

interface UseRealTimeNotificationsOptions {
  userId?: string
  enabled?: boolean
  maxRetries?: number
  retryDelay?: number
  enableBrowserNotifications?: boolean
  enableSound?: boolean
}

interface UseRealTimeNotificationsReturn {
  notifications: NotificationRecord[]
  unreadCount: number
  connected: boolean
  error: string | null
  
  // Actions
  connect: () => void
  disconnect: () => void
  markAsRead: (notificationId: string) => void
  clearAll: () => void
}

/**
 * Production real-time notifications hook using Server-Sent Events
 */
export function useRealTimeNotifications(
  options: UseRealTimeNotificationsOptions = {}
): UseRealTimeNotificationsReturn {
  const {
    userId,
    enabled = true,
    maxRetries = 5,
    retryDelay = 5000,
    enableBrowserNotifications = true,
    enableSound = true
  } = options

  // State
  const [notifications, setNotifications] = useState<NotificationRecord[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [connected, setConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Refs
  const eventSourceRef = useRef<EventSource | null>(null)
  const retryCountRef = useRef(0)
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  /**
   * Initialize notification sound
   */
  useEffect(() => {
    if (enableSound && typeof window !== 'undefined') {
      audioRef.current = new Audio('/sounds/notification.mp3')
      audioRef.current.volume = 0.5
    }
    
    return () => {
      if (audioRef.current) {
        audioRef.current.pause()
        audioRef.current = null
      }
    }
  }, [enableSound])

  /**
   * Play notification sound
   */
  const playNotificationSound = useCallback(() => {
    if (enableSound && audioRef.current) {
      audioRef.current.currentTime = 0
      audioRef.current.play().catch(err => {
        console.warn('Failed to play notification sound:', err)
      })
    }
  }, [enableSound])

  /**
   * Show browser notification
   */
  const showBrowserNotification = useCallback((notification: NotificationRecord) => {
    if (!enableBrowserNotifications || typeof window === 'undefined') return
    
    if ('Notification' in window && Notification.permission === 'granted') {
      try {
        const browserNotification = new Notification(notification.title, {
          body: notification.content,
          icon: '/icon-192x192.png',
          badge: '/icon-192x192.png',
          tag: notification.id,
          requireInteraction: notification.priority === NotificationPriority.URGENT,
          data: {
            notificationId: notification.id,
            url: notification.data?.clickAction || '/'
          }
        })

        browserNotification.onclick = () => {
          window.focus()
          
          // Navigate to URL if specified
          if (notification.data?.clickAction) {
            window.location.href = notification.data.clickAction
          }
          
          browserNotification.close()
        }

        // Auto-close after 5 seconds for non-urgent notifications
        if (notification.priority !== NotificationPriority.URGENT) {
          setTimeout(() => {
            browserNotification.close()
          }, 5000)
        }

      } catch (err) {
        console.error('Failed to show browser notification:', err)
      }
    }
  }, [enableBrowserNotifications])

  /**
   * Connect to real-time notifications via SSE
   */
  const connect = useCallback(() => {
    if (!userId || !enabled || eventSourceRef.current) {
      return
    }

    try {
      const url = `/api/notifications/stream?userId=${encodeURIComponent(userId)}`
      const eventSource = new EventSource(url)
      eventSourceRef.current = eventSource

      eventSource.onopen = () => {
        setConnected(true)
        setError(null)
        retryCountRef.current = 0
        console.log('Real-time notifications connected')
      }

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          
          switch (data.type) {
            case 'notification':
              // New notification received
              const newNotification = data.notification as NotificationRecord
              setNotifications(prev => [newNotification, ...prev.slice(0, 99)]) // Keep max 100
              setUnreadCount(prev => prev + 1)
              
              // Show browser notification and play sound
              showBrowserNotification(newNotification)
              playNotificationSound()
              break

            case 'read':
              // Notification marked as read
              setNotifications(prev => 
                prev.map(n => 
                  n.id === data.notificationId 
                    ? { ...n, readAt: new Date(), status: 'READ' as any }
                    : n
                )
              )
              setUnreadCount(prev => Math.max(0, prev - 1))
              break

            case 'delete':
              // Notification deleted
              const deletedNotification = notifications.find(n => n.id === data.notificationId)
              setNotifications(prev => prev.filter(n => n.id !== data.notificationId))
              
              if (deletedNotification && !deletedNotification.readAt) {
                setUnreadCount(prev => Math.max(0, prev - 1))
              }
              break

            case 'bulk_read':
              // All notifications marked as read
              setNotifications(prev => 
                prev.map(n => 
                  !n.readAt 
                    ? { ...n, readAt: new Date(), status: 'READ' as any }
                    : n
                )
              )
              setUnreadCount(0)
              break

            case 'sync':
              // Full sync of notifications
              setNotifications(data.notifications || [])
              setUnreadCount(data.unreadCount || 0)
              break

            case 'heartbeat':
              // Keep connection alive
              break

            default:
              console.warn('Unknown real-time notification event:', data.type)
          }
        } catch (err) {
          console.error('Failed to parse real-time notification data:', err)
        }
      }

      eventSource.onerror = (event) => {
        console.error('Real-time notifications error:', event)
        setConnected(false)
        
        // Attempt to reconnect with exponential backoff
        if (retryCountRef.current < maxRetries) {
          const delay = retryDelay * Math.pow(2, retryCountRef.current)
          retryCountRef.current++
          
          setError(`Connection lost. Retrying in ${Math.ceil(delay / 1000)}s... (${retryCountRef.current}/${maxRetries})`)
          
          retryTimeoutRef.current = setTimeout(() => {
            disconnect()
            connect()
          }, delay)
        } else {
          setError('Failed to connect to real-time notifications. Please refresh the page.')
        }
      }

    } catch (err) {
      console.error('Failed to connect to real-time notifications:', err)
      setError('Failed to establish real-time connection')
    }
  }, [userId, enabled, maxRetries, retryDelay, notifications, showBrowserNotification, playNotificationSound])

  /**
   * Disconnect from real-time notifications
   */
  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
      eventSourceRef.current = null
    }

    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current)
      retryTimeoutRef.current = null
    }

    setConnected(false)
    retryCountRef.current = 0
  }, [])

  /**
   * Mark notification as read (optimistic update)
   */
  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, readAt: new Date(), status: 'READ' as any }
          : notification
      )
    )
    setUnreadCount(prev => Math.max(0, prev - 1))
  }, [])

  /**
   * Clear all notifications
   */
  const clearAll = useCallback(() => {
    setNotifications([])
    setUnreadCount(0)
  }, [])

  // Connect/disconnect based on options
  useEffect(() => {
    if (userId && enabled) {
      connect()
    } else {
      disconnect()
    }

    return () => {
      disconnect()
    }
  }, [userId, enabled, connect, disconnect])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  // Handle page visibility changes to reconnect when page becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && userId && enabled && !connected) {
        connect()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [userId, enabled, connected, connect])

  return {
    notifications,
    unreadCount,
    connected,
    error,
    connect,
    disconnect,
    markAsRead,
    clearAll
  }
}

/**
 * Hook for managing browser notification permission
 */
export function useBrowserNotificationPermission() {
  const [permission, setPermission] = useState<NotificationPermission>('default')
  const [requesting, setRequesting] = useState(false)

  useEffect(() => {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      setPermission(Notification.permission)
    }
  }, [])

  const requestPermission = useCallback(async (): Promise<NotificationPermission> => {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      return 'denied'
    }

    if (Notification.permission === 'granted') {
      return 'granted'
    }

    setRequesting(true)

    try {
      const result = await Notification.requestPermission()
      setPermission(result)
      return result
    } catch (err) {
      console.error('Failed to request notification permission:', err)
      return 'denied'
    } finally {
      setRequesting(false)
    }
  }, [])

  return {
    permission,
    requesting,
    requestPermission,
    isSupported: typeof window !== 'undefined' && 'Notification' in window
  }
}

/**
 * Hook for notification preferences with database persistence
 */
export function useNotificationPreferences(userId?: string) {
  const [preferences, setPreferences] = useState({
    emailEnabled: true,
    smsEnabled: true,
    pushEnabled: true,
    inAppEnabled: true,
    browserNotifications: true,
    sound: true,
    frequency: 'IMMEDIATE' as const,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00',
    categories: {
      orders: true,
      payments: true,
      shipping: true,
      promotions: false,
      system: true,
      security: true
    }
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchPreferences = useCallback(async () => {
    if (!userId) return

    try {
      setError(null)
      
      const response = await fetch(`/api/notifications/preferences?userId=${encodeURIComponent(userId)}`, {
        headers: {
          'Content-Type': 'application/json',
        }
      })
      
      if (response.ok) {
        const result = await response.json()
        if (result.success && result.preferences) {
          setPreferences(prev => ({ ...prev, ...result.preferences }))
        }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch preferences'
      setError(errorMessage)
      console.error('Fetch preferences error:', err)
    } finally {
      setLoading(false)
    }
  }, [userId])

  const savePreferences = useCallback(async (newPreferences: typeof preferences): Promise<boolean> => {
    if (!userId) return false

    setSaving(true)
    setError(null)

    try {
      const response = await fetch('/api/notifications/preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          preferences: newPreferences
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setPreferences(newPreferences)
        return true
      } else {
        throw new Error(result.error || 'Failed to save preferences')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save preferences'
      setError(errorMessage)
      console.error('Save preferences error:', err)
      return false
    } finally {
      setSaving(false)
    }
  }, [userId])

  useEffect(() => {
    fetchPreferences()
  }, [fetchPreferences])

  return {
    preferences,
    loading,
    saving,
    error,
    updatePreferences: savePreferences,
    refresh: fetchPreferences
  }
}
