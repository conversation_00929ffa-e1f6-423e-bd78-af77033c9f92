'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { 
  NotificationRecord, 
  NotificationRequest,
  NotificationResult,
  NotificationChannel,
  NotificationType
} from '../types'

interface UseNotificationsOptions {
  userId?: string
  autoRefresh?: boolean
  refreshInterval?: number
  limit?: number
}

interface UseNotificationsReturn {
  notifications: NotificationRecord[]
  unreadCount: number
  loading: boolean
  error: string | null
  
  // Actions
  sendNotification: (request: NotificationRequest) => Promise<NotificationResult>
  markAsRead: (notificationId: string) => Promise<boolean>
  markAllAsRead: () => Promise<number>
  deleteNotification: (notificationId: string) => Promise<boolean>
  refresh: () => Promise<void>
  
  // Filters
  filterByType: (type: NotificationType | null) => void
  filterByChannel: (channel: NotificationChannel | null) => void
  showUnreadOnly: (unreadOnly: boolean) => void
  
  // State
  currentFilter: {
    type: NotificationType | null
    channel: NotificationChannel | null
    unreadOnly: boolean
  }
}

/**
 * Main notifications hook with production database integration
 */
export function useNotifications(options: UseNotificationsOptions = {}): UseNotificationsReturn {
  const {
    userId,
    autoRefresh = true,
    refreshInterval = 30000,
    limit = 50
  } = options

  // State
  const [notifications, setNotifications] = useState<NotificationRecord[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Filters
  const [currentFilter, setCurrentFilter] = useState({
    type: null as NotificationType | null,
    channel: null as NotificationChannel | null,
    unreadOnly: false
  })

  // Refs
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  /**
   * Fetch notifications from API with production error handling
   */
  const fetchNotifications = useCallback(async () => {
    if (!userId) return

    try {
      setError(null)
      
      // Cancel previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      abortControllerRef.current = new AbortController()

      const params = new URLSearchParams({
        userId,
        limit: limit.toString(),
        ...(currentFilter.type && { type: currentFilter.type }),
        ...(currentFilter.channel && { channel: currentFilter.channel }),
        ...(currentFilter.unreadOnly && { unreadOnly: 'true' })
      })

      const response = await fetch(`/api/notifications?${params}`, {
        signal: abortControllerRef.current.signal,
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      
      if (data.success) {
        setNotifications(data.notifications || [])
        setUnreadCount(data.unreadCount || 0)
      } else {
        throw new Error(data.error || 'Failed to fetch notifications')
      }

    } catch (err) {
      if (err instanceof Error && err.name !== 'AbortError') {
        const errorMessage = err.message || 'Failed to fetch notifications'
        setError(errorMessage)
        console.error('Fetch notifications error:', err)
      }
    } finally {
      setLoading(false)
    }
  }, [userId, limit, currentFilter])

  /**
   * Send notification with production error handling
   */
  const sendNotification = useCallback(async (request: NotificationRequest): Promise<NotificationResult> => {
    try {
      const response = await fetch('/api/notifications/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}: ${response.statusText}`)
      }
      
      // Refresh notifications if it's an in-app notification for current user
      if (request.channel === NotificationChannel.IN_APP && request.recipientId === userId) {
        await fetchNotifications()
      }

      return result

    } catch (err) {
      const error = err instanceof Error ? err.message : 'Unknown error'
      console.error('Send notification error:', err)
      return {
        success: false,
        error
      }
    }
  }, [userId, fetchNotifications])

  /**
   * Mark notification as read with optimistic updates
   */
  const markAsRead = useCallback(async (notificationId: string): Promise<boolean> => {
    if (!userId) return false

    // Optimistic update
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, readAt: new Date(), status: 'READ' as any }
          : notification
      )
    )
    setUnreadCount(prev => Math.max(0, prev - 1))

    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ userId })
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        // Revert optimistic update on failure
        setNotifications(prev => 
          prev.map(notification => 
            notification.id === notificationId 
              ? { ...notification, readAt: undefined, status: 'DELIVERED' as any }
              : notification
          )
        )
        setUnreadCount(prev => prev + 1)
        
        throw new Error(result.error || 'Failed to mark as read')
      }

      return true

    } catch (err) {
      console.error('Mark as read error:', err)
      return false
    }
  }, [userId])

  /**
   * Mark all notifications as read
   */
  const markAllAsRead = useCallback(async (): Promise<number> => {
    if (!userId) return 0

    const unreadNotifications = notifications.filter(n => !n.readAt)
    const unreadCount = unreadNotifications.length

    // Optimistic update
    setNotifications(prev => 
      prev.map(notification => 
        !notification.readAt 
          ? { ...notification, readAt: new Date(), status: 'READ' as any }
          : notification
      )
    )
    setUnreadCount(0)

    try {
      const response = await fetch('/api/notifications/read-all', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ userId })
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        // Revert optimistic update on failure
        setNotifications(prev => 
          prev.map(notification => {
            const wasUnread = unreadNotifications.find(n => n.id === notification.id)
            return wasUnread 
              ? { ...notification, readAt: undefined, status: 'DELIVERED' as any }
              : notification
          })
        )
        setUnreadCount(unreadCount)
        
        throw new Error(result.error || 'Failed to mark all as read')
      }

      return result.markedCount || unreadCount

    } catch (err) {
      console.error('Mark all as read error:', err)
      return 0
    }
  }, [userId, notifications])

  /**
   * Delete notification with optimistic updates
   */
  const deleteNotification = useCallback(async (notificationId: string): Promise<boolean> => {
    if (!userId) return false

    const deletedNotification = notifications.find(n => n.id === notificationId)
    
    // Optimistic update
    setNotifications(prev => prev.filter(n => n.id !== notificationId))
    if (deletedNotification && !deletedNotification.readAt) {
      setUnreadCount(prev => Math.max(0, prev - 1))
    }

    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ userId })
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        // Revert optimistic update on failure
        if (deletedNotification) {
          setNotifications(prev => [deletedNotification, ...prev])
          if (!deletedNotification.readAt) {
            setUnreadCount(prev => prev + 1)
          }
        }
        
        throw new Error(result.error || 'Failed to delete notification')
      }

      return true

    } catch (err) {
      console.error('Delete notification error:', err)
      return false
    }
  }, [userId, notifications])

  /**
   * Refresh notifications
   */
  const refresh = useCallback(async () => {
    setLoading(true)
    await fetchNotifications()
  }, [fetchNotifications])

  /**
   * Filter by type
   */
  const filterByType = useCallback((type: NotificationType | null) => {
    setCurrentFilter(prev => ({ ...prev, type }))
  }, [])

  /**
   * Filter by channel
   */
  const filterByChannel = useCallback((channel: NotificationChannel | null) => {
    setCurrentFilter(prev => ({ ...prev, channel }))
  }, [])

  /**
   * Show unread only
   */
  const showUnreadOnly = useCallback((unreadOnly: boolean) => {
    setCurrentFilter(prev => ({ ...prev, unreadOnly }))
  }, [])

  // Initial fetch and auto-refresh setup
  useEffect(() => {
    if (userId) {
      fetchNotifications()

      if (autoRefresh) {
        refreshIntervalRef.current = setInterval(fetchNotifications, refreshInterval)
      }
    }

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current)
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [userId, autoRefresh, refreshInterval, fetchNotifications])

  // Refetch when filters change
  useEffect(() => {
    if (userId) {
      fetchNotifications()
    }
  }, [currentFilter, fetchNotifications])

  return {
    notifications,
    unreadCount,
    loading,
    error,
    sendNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refresh,
    filterByType,
    filterByChannel,
    showUnreadOnly,
    currentFilter
  }
}

/**
 * Hook for sending notifications with enhanced error handling
 */
export function useSendNotification() {
  const [sending, setSending] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const send = useCallback(async (request: NotificationRequest): Promise<NotificationResult> => {
    setSending(true)
    setError(null)

    try {
      const response = await fetch('/api/notifications/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}: ${response.statusText}`)
      }
      
      if (!result.success) {
        setError(result.error || 'Failed to send notification')
      }

      return result

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('Send notification error:', err)
      
      return {
        success: false,
        error: errorMessage
      }
    } finally {
      setSending(false)
    }
  }, [])

  return {
    send,
    sending,
    error,
    clearError: () => setError(null)
  }
}

/**
 * Hook for notification statistics with caching
 */
export function useNotificationStats(userId?: string) {
  const [stats, setStats] = useState({
    total: 0,
    unread: 0,
    byType: {} as Record<string, number>,
    byChannel: {} as Record<string, number>,
    byPriority: {} as Record<string, number>,
    readRate: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchStats = useCallback(async () => {
    if (!userId) return

    try {
      setError(null)
      
      const response = await fetch(`/api/notifications/stats?userId=${userId}`, {
        headers: {
          'Content-Type': 'application/json',
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (result.success) {
        setStats(result.stats)
      } else {
        throw new Error(result.error || 'Failed to fetch stats')
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('Fetch stats error:', err)
    } finally {
      setLoading(false)
    }
  }, [userId])

  useEffect(() => {
    fetchStats()
  }, [fetchStats])

  return {
    stats,
    loading,
    error,
    refresh: fetchStats
  }
}
