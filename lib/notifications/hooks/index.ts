/**
 * Notification Hooks
 * 
 * Production-ready React hooks for managing notifications in the Coco Milk Kids application
 */

// Main notification hooks
export { 
  useNotifications, 
  useSendNotification, 
  useNotificationStats 
} from './use-notifications'

export { 
  useRealTimeNotifications,
  useBrowserNotificationPermission,
  useNotificationPreferences
} from './use-real-time-notifications'

export { 
  useNotificationCenter 
} from './use-notification-center'

// Re-export types for convenience
export type {
  NotificationRecord,
  NotificationRequest,
  NotificationResult,
  NotificationChannel,
  NotificationType,
  NotificationPriority,
  NotificationStatus,
  RecipientType
} from '../types'
