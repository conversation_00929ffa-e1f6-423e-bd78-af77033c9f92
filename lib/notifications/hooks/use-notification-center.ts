'use client'

import { useState, useCallback, useMemo } from 'react'
import { useNotifications } from './use-notifications'
import { useRealTimeNotifications } from './use-real-time-notifications'
import { 
  NotificationRecord, 
  NotificationChannel, 
  NotificationType,
  NotificationPriority 
} from '../types'

interface UseNotificationCenterOptions {
  userId?: string
  enableRealTime?: boolean
  autoRefresh?: boolean
  maxNotifications?: number
}

interface NotificationGroup {
  date: string
  notifications: NotificationRecord[]
}

interface UseNotificationCenterReturn {
  // Data
  notifications: NotificationRecord[]
  groupedNotifications: NotificationGroup[]
  unreadCount: number
  totalCount: number
  
  
  // State
  loading: boolean
  error: string | null
  connected: boolean
  isOpen: boolean
  enableRealTime: boolean
  
  // Actions
  open: () => void
  close: () => void
  toggle: () => void
  markAsRead: (notificationId: string) => Promise<boolean>
  markAllAsRead: () => Promise<number>
  deleteNotification: (notificationId: string) => Promise<boolean>
  refresh: () => Promise<void>
  
  // Filters
  activeFilters: {
    type: NotificationType | null
    channel: NotificationChannel | null
    priority: NotificationPriority | null
    unreadOnly: boolean
    search: string
  }
  setTypeFilter: (type: NotificationType | null) => void
  setChannelFilter: (channel: NotificationChannel | null) => void
  setPriorityFilter: (priority: NotificationPriority | null) => void
  setUnreadOnlyFilter: (unreadOnly: boolean) => void
  setSearchFilter: (search: string) => void
  clearFilters: () => void
  
  // Sorting
  sortBy: 'date' | 'priority' | 'type'
  sortOrder: 'asc' | 'desc'
  setSorting: (sortBy: 'date' | 'priority' | 'type', sortOrder: 'asc' | 'desc') => void
  
  // Pagination
  currentPage: number
  totalPages: number
  hasMore: boolean
  loadMore: () => Promise<void>
  goToPage: (page: number) => void
  
  // Statistics
  stats: {
    byType: Record<string, number>
    byChannel: Record<string, number>
    byPriority: Record<string, number>
    readRate: number
  }
}

/**
 * Production notification center hook with advanced filtering and management
 */
export function useNotificationCenter(
  options: UseNotificationCenterOptions = {}
): UseNotificationCenterReturn {
  const {
    userId,
    enableRealTime = true,
    autoRefresh = true,
    maxNotifications = 100
  } = options

  // State
  const [isOpen, setIsOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [sortBy, setSortBy] = useState<'date' | 'priority' | 'type'>('date')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  
  // Filters
  const [activeFilters, setActiveFilters] = useState({
    type: null as NotificationType | null,
    channel: null as NotificationChannel | null,
    priority: null as NotificationPriority | null,
    unreadOnly: false,
    search: ''
  })

  // Hooks
  const {
    notifications: staticNotifications,
    unreadCount: staticUnreadCount,
    loading,
    error,
    markAsRead: staticMarkAsRead,
    markAllAsRead: staticMarkAllAsRead,
    deleteNotification: staticDeleteNotification,
    refresh,
    filterByType,
    filterByChannel,
    showUnreadOnly
  } = useNotifications({
    userId,
    autoRefresh: autoRefresh && !enableRealTime,
    limit: maxNotifications
  })

  const {
    notifications: realTimeNotifications,
    unreadCount: realTimeUnreadCount,
    connected,
    markAsRead: realTimeMarkAsRead
  } = useRealTimeNotifications({
    userId,
    enabled: enableRealTime
  })

  // Determine which notifications to use
  const notifications = enableRealTime ? realTimeNotifications : staticNotifications
  const unreadCount = enableRealTime ? realTimeUnreadCount : staticUnreadCount

  /**
   * Filter and sort notifications with production-grade performance
   */
  const filteredAndSortedNotifications = useMemo(() => {
    let filtered = [...notifications]

    // Apply filters
    if (activeFilters.type) {
      filtered = filtered.filter(n => n.type === activeFilters.type)
    }

    if (activeFilters.channel) {
      filtered = filtered.filter(n => n.channel === activeFilters.channel)
    }

    if (activeFilters.priority) {
      filtered = filtered.filter(n => n.priority === activeFilters.priority)
    }

    if (activeFilters.unreadOnly) {
      filtered = filtered.filter(n => !n.readAt)
    }

    if (activeFilters.search) {
      const searchLower = activeFilters.search.toLowerCase()
      filtered = filtered.filter(n => 
        n.title.toLowerCase().includes(searchLower) ||
        n.content.toLowerCase().includes(searchLower) ||
        n.type.toLowerCase().includes(searchLower)
      )
    }

    // Apply sorting with stable sort
    filtered.sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'date':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          break
        case 'priority':
          const priorityOrder = { URGENT: 4, HIGH: 3, NORMAL: 2, LOW: 1 }
          comparison = (priorityOrder[a.priority as 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'] || 2) -
                      (priorityOrder[b.priority as 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'] || 2)
          break
        case 'type':
          comparison = a.type.localeCompare(b.type)
          break
      }

      // Secondary sort by date for stable sorting
      if (comparison === 0) {
        comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      }

      return sortOrder === 'desc' ? -comparison : comparison
    })

    return filtered
  }, [notifications, activeFilters, sortBy, sortOrder])

  /**
   * Group notifications by date with localized formatting
   */
  const groupedNotifications = useMemo(() => {
    const groups: Record<string, NotificationRecord[]> = {}
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    
    filteredAndSortedNotifications.forEach(notification => {
      const notificationDate = new Date(notification.createdAt)
      let dateKey: string

      if (notificationDate.toDateString() === today.toDateString()) {
        dateKey = 'Today'
      } else if (notificationDate.toDateString() === yesterday.toDateString()) {
        dateKey = 'Yesterday'
      } else {
        dateKey = notificationDate.toLocaleDateString('en-ZA', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      }

      if (!groups[dateKey]) {
        groups[dateKey] = []
      }
      groups[dateKey].push(notification)
    })

    return Object.entries(groups)
      .map(([date, notifications]) => ({ date, notifications }))
      .sort((a, b) => {
        // Sort groups by most recent first
        if (a.date === 'Today') return -1
        if (b.date === 'Today') return 1
        if (a.date === 'Yesterday') return -1
        if (b.date === 'Yesterday') return 1
        
        const dateA = new Date(a.notifications[0]?.createdAt || 0)
        const dateB = new Date(b.notifications[0]?.createdAt || 0)
        return dateB.getTime() - dateA.getTime()
      })
  }, [filteredAndSortedNotifications])

  /**
   * Calculate comprehensive statistics
   */
  const stats = useMemo(() => {
    const byType: Record<string, number> = {}
    const byChannel: Record<string, number> = {}
    const byPriority: Record<string, number> = {}
    let readCount = 0

    notifications.forEach(notification => {
      // Count by type
      byType[notification.type] = (byType[notification.type] || 0) + 1
      
      // Count by channel
      byChannel[notification.channel] = (byChannel[notification.channel] || 0) + 1
      
      // Count by priority
      byPriority[notification.priority] = (byPriority[notification.priority] || 0) + 1
      
      // Count read notifications
      if (notification.readAt) {
        readCount++
      }
    })

    const readRate = notifications.length > 0 ? Math.round((readCount / notifications.length) * 100) : 0

    return {
      byType,
      byChannel,
      byPriority,
      readRate
    }
  }, [notifications])

  // Pagination
  const itemsPerPage = 20
  const totalPages = Math.ceil(filteredAndSortedNotifications.length / itemsPerPage)
  const hasMore = currentPage < totalPages

  const paginatedNotifications = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return filteredAndSortedNotifications.slice(startIndex, endIndex)
  }, [filteredAndSortedNotifications, currentPage])

  // Actions
  const open = useCallback(() => setIsOpen(true), [])
  const close = useCallback(() => setIsOpen(false), [])
  const toggle = useCallback(() => setIsOpen(prev => !prev), [])

  const markAsRead = useCallback(async (notificationId: string): Promise<boolean> => {
    if (enableRealTime) {
      realTimeMarkAsRead(notificationId)
      // Also call API to persist the change
      return staticMarkAsRead(notificationId)
    } else {
      return staticMarkAsRead(notificationId)
    }
  }, [enableRealTime, realTimeMarkAsRead, staticMarkAsRead])

  const markAllAsRead = useCallback(async (): Promise<number> => {
    return staticMarkAllAsRead()
  }, [staticMarkAllAsRead])

  const deleteNotification = useCallback(async (notificationId: string): Promise<boolean> => {
    return staticDeleteNotification(notificationId)
  }, [staticDeleteNotification])

  // Filter setters with validation
  const setTypeFilter = useCallback((type: NotificationType | null) => {
    setActiveFilters(prev => ({ ...prev, type }))
    filterByType(type)
    setCurrentPage(1)
  }, [filterByType])

  const setChannelFilter = useCallback((channel: NotificationChannel | null) => {
    setActiveFilters(prev => ({ ...prev, channel }))
    filterByChannel(channel)
    setCurrentPage(1)
  }, [filterByChannel])

  const setPriorityFilter = useCallback((priority: NotificationPriority | null) => {
    setActiveFilters(prev => ({ ...prev, priority }))
    setCurrentPage(1)
  }, [])

  const setUnreadOnlyFilter = useCallback((unreadOnly: boolean) => {
    setActiveFilters(prev => ({ ...prev, unreadOnly }))
    showUnreadOnly(unreadOnly)
    setCurrentPage(1)
  }, [showUnreadOnly])

  const setSearchFilter = useCallback((search: string) => {
    setActiveFilters(prev => ({ ...prev, search: search.trim() }))
    setCurrentPage(1)
  }, [])

  const clearFilters = useCallback(() => {
    setActiveFilters({
      type: null,
      channel: null,
      priority: null,
      unreadOnly: false,
      search: ''
    })
    filterByType(null)
    filterByChannel(null)
    showUnreadOnly(false)
    setCurrentPage(1)
  }, [filterByType, filterByChannel, showUnreadOnly])

  // Sorting
  const setSorting = useCallback((newSortBy: 'date' | 'priority' | 'type', newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy)
    setSortOrder(newSortOrder)
    setCurrentPage(1)
  }, [])

  // Pagination
  const loadMore = useCallback(async () => {
    if (hasMore) {
      setCurrentPage(prev => prev + 1)
    }
  }, [hasMore])

  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page)
    }
  }, [totalPages])

  return {
    // Data
    notifications: paginatedNotifications,
    groupedNotifications,
    unreadCount,
    totalCount: filteredAndSortedNotifications.length,
    enableRealTime,
    
    // State
    loading,
    error,
    connected,
    isOpen,
    
    // Actions
    open,
    close,
    toggle,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refresh,
    
    // Filters
    activeFilters,
    setTypeFilter,
    setChannelFilter,
    setPriorityFilter,
    setUnreadOnlyFilter,
    setSearchFilter,
    clearFilters,
    
    // Sorting
    sortBy,
    sortOrder,
    setSorting,
    
    // Pagination
    currentPage,
    totalPages,
    hasMore,
    loadMore,
    goToPage,
    
    // Statistics
    stats
  }
}
