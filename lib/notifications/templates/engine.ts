import {
  TemplateEngine as ITemplateEngine,
  NotificationTemplateRecord,
  RenderedTemplate,
  ValidationResult,
  NotificationChannel
} from '../types'
import { templateUtils, logger, errorUtils } from '../utils'

/**
 * Notification Template Engine
 * Handles template rendering with variable substitution
 */
export class TemplateEngine implements ITemplateEngine {
  private templates: Map<string, NotificationTemplateRecord> = new Map()

  constructor() {
    this.loadDefaultTemplates()
  }

  /**
   * Render template with variables
   */
  async render(template: NotificationTemplateRecord, variables: Record<string, any>): Promise<RenderedTemplate> {
    try {
      // Validate template variables
      const validation = templateUtils.validateVariables(template.content, variables)
      if (!validation.isValid) {
        logger.warn('Template has missing variables', {
          templateId: template.id,
          missingVariables: validation.missingVariables
        })
      }

      // Add default variables
      const allVariables = {
        ...this.getDefaultVariables(),
        ...variables
      }

      // Render content
      const renderedContent = templateUtils.replaceVariables(template.content, allVariables)
      
      // Render HTML content if available
      let renderedHtmlContent: string | undefined
      if (template.htmlContent) {
        renderedHtmlContent = templateUtils.replaceVariables(template.htmlContent, allVariables)
      }

      // Render subject if available
      let renderedSubject: string | undefined
      if (template.subject) {
        renderedSubject = templateUtils.replaceVariables(template.subject, allVariables)
      }

      return {
        subject: renderedSubject,
        content: renderedContent,
        htmlContent: renderedHtmlContent
      }

    } catch (error) {
      logger.error('Template rendering failed', { error, templateId: template.id })
      throw new Error(`Template rendering failed: ${errorUtils.createErrorMessage(error)}`)
    }
  }

  /**
   * Validate template
   */
  async validate(template: NotificationTemplateRecord): Promise<ValidationResult> {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // Check required fields
      if (!template.name) {
        errors.push('Template name is required')
      }

      if (!template.content) {
        errors.push('Template content is required')
      }

      if (!template.type) {
        errors.push('Template type is required')
      }

      if (!template.channel) {
        errors.push('Template channel is required')
      }

      // Validate content syntax
      if (template.content) {
        const contentVariables = templateUtils.extractVariables(template.content)
        
        // Check for malformed variables
        const malformedVariables = this.findMalformedVariables(template.content)
        if (malformedVariables.length > 0) {
          errors.push(`Malformed variables found: ${malformedVariables.join(', ')}`)
        }

        // Update template variables
        template.variables = contentVariables
      }

      // Validate HTML content if present
      if (template.htmlContent) {
        const htmlVariables = templateUtils.extractVariables(template.htmlContent)
        const contentVariables = templateUtils.extractVariables(template.content)
        
        // Check if HTML variables match content variables
        const missingInHtml = contentVariables.filter(v => !htmlVariables.includes(v))
        const extraInHtml = htmlVariables.filter(v => !contentVariables.includes(v))
        
        if (missingInHtml.length > 0) {
          warnings.push(`Variables missing in HTML content: ${missingInHtml.join(', ')}`)
        }
        
        if (extraInHtml.length > 0) {
          warnings.push(`Extra variables in HTML content: ${extraInHtml.join(', ')}`)
        }
      }

      // Validate subject if present
      if (template.subject) {
        const subjectVariables = templateUtils.extractVariables(template.subject)
        const contentVariables = templateUtils.extractVariables(template.content)
        
        const extraInSubject = subjectVariables.filter(v => !contentVariables.includes(v))
        if (extraInSubject.length > 0) {
          warnings.push(`Variables in subject not found in content: ${extraInSubject.join(', ')}`)
        }
      }

      // Channel-specific validation
      if (template.channel === NotificationChannel.SMS && template.content.length > 160) {
        warnings.push('SMS template content exceeds 160 characters')
      }

      if (template.channel === NotificationChannel.PUSH && template.content.length > 100) {
        warnings.push('Push notification content exceeds 100 characters')
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings
      }

    } catch (error) {
      logger.error('Template validation failed', { error, templateId: template.id })
      return {
        isValid: false,
        errors: [`Validation error: ${errorUtils.createErrorMessage(error)}`],
        warnings
      }
    }
  }

  /**
   * Get variables from template content
   */
  getVariables(content: string): string[] {
    return templateUtils.extractVariables(content)
  }

  /**
   * Get template by ID
   */
  async getTemplate(templateId: string): Promise<NotificationTemplateRecord | null> {
    return this.templates.get(templateId) || null
  }

  /**
   * Save template
   */
  async saveTemplate(template: NotificationTemplateRecord): Promise<void> {
    // Validate template before saving
    const validation = await this.validate(template)
    if (!validation.isValid) {
      throw new Error(`Template validation failed: ${validation.errors.join(', ')}`)
    }

    this.templates.set(template.id, {
      ...template,
      updatedAt: new Date()
    })

    logger.info('Template saved', { templateId: template.id, name: template.name })
  }

  /**
   * Delete template
   */
  async deleteTemplate(templateId: string): Promise<boolean> {
    const deleted = this.templates.delete(templateId)
    if (deleted) {
      logger.info('Template deleted', { templateId })
    }
    return deleted
  }

  /**
   * List all templates
   */
  async listTemplates(filters?: {
    type?: string
    channel?: string
    category?: string
    isActive?: boolean
  }): Promise<NotificationTemplateRecord[]> {
    let templates = Array.from(this.templates.values())

    if (filters) {
      if (filters.type) {
        templates = templates.filter(t => t.type === filters.type)
      }
      if (filters.channel) {
        templates = templates.filter(t => t.channel === filters.channel)
      }
      if (filters.category) {
        templates = templates.filter(t => t.category === filters.category)
      }
      if (filters.isActive !== undefined) {
        templates = templates.filter(t => t.isActive === filters.isActive)
      }
    }

    return templates.sort((a, b) => a.name.localeCompare(b.name))
  }

  /**
   * Find malformed variables in content
   */
  private findMalformedVariables(content: string): string[] {
    const malformed: string[] = []
    
    // Find single braces
    const singleBraceRegex = /\{[^{}]*\}/g
    const singleBraces = content.match(singleBraceRegex) || []
    
    singleBraces.forEach(match => {
      if (!match.startsWith('{{') || !match.endsWith('}}')) {
        malformed.push(match)
      }
    })

    // Find unclosed braces
    const openBraces = (content.match(/\{\{/g) || []).length
    const closeBraces = (content.match(/\}\}/g) || []).length
    
    if (openBraces !== closeBraces) {
      malformed.push('Unclosed template variables')
    }

    return malformed
  }

  /**
   * Get default variables available in all templates
   */
  private getDefaultVariables(): Record<string, any> {
    const now = new Date()
    
    return {
      // Date/Time variables
      'date.now': now.toISOString(),
      'date.today': now.toDateString(),
      'date.year': now.getFullYear(),
      'date.month': now.getMonth() + 1,
      'date.day': now.getDate(),
      'time.now': now.toTimeString(),
      'time.hour': now.getHours(),
      'time.minute': now.getMinutes(),
      
      // Company variables
      'company.name': 'Coco Milk Kids',
      'company.email': '<EMAIL>',
      'company.phone': '+27 ***********',
      'company.website': 'https://cocomilkkids.com',
      'company.address': 'Cape Town, South Africa',
      
      // System variables
      'system.environment': process.env.NODE_ENV || 'development',
      'system.version': '1.0.0'
    }
  }

  /**
   * Load default system templates
   */
  private loadDefaultTemplates(): void {
    const defaultTemplates: NotificationTemplateRecord[] = [
      {
        id: 'welcome_email',
        name: 'Welcome Email',
        description: 'Welcome email for new customers',
        type: 'WELCOME' as any,
        channel: 'EMAIL' as any,
        subject: 'Welcome to {{company.name}}!',
        content: `Hi {{customer.firstName}},

Welcome to {{company.name}}! We're excited to have you join our family.

Your account has been created successfully. You can now:
- Browse our amazing collection of kids' clothing
- Track your orders
- Manage your preferences

If you have any questions, feel free to contact us at {{company.email}}.

Best regards,
The {{company.name}} Team`,
        htmlContent: `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h2 style="color: #333;">Welcome to {{company.name}}!</h2>
  <p>Hi {{customer.firstName}},</p>
  <p>Welcome to {{company.name}}! We're excited to have you join our family.</p>
  <p>Your account has been created successfully. You can now:</p>
  <ul>
    <li>Browse our amazing collection of kids' clothing</li>
    <li>Track your orders</li>
    <li>Manage your preferences</li>
  </ul>
  <p>If you have any questions, feel free to contact us at <a href="mailto:{{company.email}}">{{company.email}}</a>.</p>
  <p>Best regards,<br>The {{company.name}} Team</p>
</div>`,
        variables: ['customer.firstName', 'company.name', 'company.email'],
        isActive: true,
        isSystem: true,
        category: 'Account',
        tags: ['welcome', 'onboarding'],
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'order_confirmation_email',
        name: 'Order Confirmation Email',
        description: 'Email sent when an order is confirmed',
        type: 'ORDER_CONFIRMATION' as any,
        channel: 'EMAIL' as any,
        subject: 'Order Confirmation - #{{order.number}}',
        content: `Hi {{customer.firstName}},

Thank you for your order! We've received your order and it's being processed.

Order Details:
- Order Number: {{order.number}}
- Order Date: {{order.date}}
- Total Amount: {{order.total}}

Items Ordered:
{{order.items}}

Shipping Address:
{{order.shippingAddress}}

We'll send you another email when your order ships.

Thank you for shopping with {{company.name}}!`,
        variables: ['customer.firstName', 'order.number', 'order.date', 'order.total', 'order.items', 'order.shippingAddress', 'company.name'],
        isActive: true,
        isSystem: true,
        category: 'Orders',
        tags: ['order', 'confirmation'],
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'order_confirmation_sms',
        name: 'Order Confirmation SMS',
        description: 'SMS sent when an order is confirmed',
        type: 'ORDER_CONFIRMATION' as any,
        channel: 'SMS' as any,
        content: 'Hi {{customer.firstName}}! Your order #{{order.number}} for {{order.total}} has been confirmed. Track: {{order.trackingUrl}}',
        variables: ['customer.firstName', 'order.number', 'order.total', 'order.trackingUrl'],
        isActive: true,
        isSystem: true,
        category: 'Orders',
        tags: ['order', 'confirmation', 'sms'],
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]

    defaultTemplates.forEach(template => {
      this.templates.set(template.id, template)
    })

    logger.info('Default templates loaded', { count: defaultTemplates.length })
  }

  /**
   * Preview template with sample data
   */
  async preview(templateId: string, sampleData?: Record<string, any>): Promise<RenderedTemplate> {
    const template = await this.getTemplate(templateId)
    if (!template) {
      throw new Error(`Template not found: ${templateId}`)
    }

    const defaultSampleData = {
      customer: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      },
      order: {
        number: 'ORD-12345',
        date: new Date().toDateString(),
        total: 'R 299.99',
        items: '2x Kids T-Shirt, 1x Kids Shorts',
        shippingAddress: '123 Main St, Cape Town, 8001',
        trackingUrl: 'https://cocomilkkids.com/track/12345'
      },
      ...sampleData
    }

    return this.render(template, defaultSampleData)
  }
}
