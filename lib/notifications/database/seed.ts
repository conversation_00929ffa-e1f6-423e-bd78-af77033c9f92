/**
 * Notification System Database Seeder
 * 
 * Seeds the database with default notification templates and test data
 */

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

/**
 * Default notification templates for Coco Milk Kids
 */
const defaultTemplates = [
  {
    id: 'welcome_email',
    name: 'Welcome Email',
    description: 'Welcome email for new customers',
    type: 'WELCOME',
    channel: 'EMAIL',
    subject: 'Welcome to {{company.name}}! 🎉',
    content: `Hi {{customer.firstName}},

Welcome to {{company.name}}! We're thrilled to have you join our family of happy parents and adorable kids.

Your account has been created successfully. Here's what you can do now:

✨ Browse our amazing collection of kids' clothing
📦 Track your orders in real-time
💝 Manage your preferences and wishlist
🎁 Get exclusive access to sales and promotions

If you have any questions, our friendly support team is here to help at {{company.email}} or {{company.phone}}.

Happy shopping!
The {{company.name}} Team

P.S. Follow us on social media for the latest updates and adorable kids' fashion inspiration!`,
    htmlContent: `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
  <div style="text-align: center; margin-bottom: 30px;">
    <h1 style="color: #FF6B35; margin: 0;">Welcome to {{company.name}}! 🎉</h1>
  </div>
  
  <p style="font-size: 16px; color: #333;">Hi {{customer.firstName}},</p>
  
  <p style="font-size: 16px; color: #333; line-height: 1.6;">
    Welcome to {{company.name}}! We're thrilled to have you join our family of happy parents and adorable kids.
  </p>
  
  <p style="font-size: 16px; color: #333; line-height: 1.6;">
    Your account has been created successfully. Here's what you can do now:
  </p>
  
  <ul style="font-size: 16px; color: #333; line-height: 1.8;">
    <li>✨ Browse our amazing collection of kids' clothing</li>
    <li>📦 Track your orders in real-time</li>
    <li>💝 Manage your preferences and wishlist</li>
    <li>🎁 Get exclusive access to sales and promotions</li>
  </ul>
  
  <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <p style="margin: 0; color: #666;">
      If you have any questions, our friendly support team is here to help at 
      <a href="mailto:{{company.email}}" style="color: #FF6B35;">{{company.email}}</a> 
      or {{company.phone}}.
    </p>
  </div>
  
  <p style="font-size: 16px; color: #333;">Happy shopping!</p>
  <p style="font-size: 16px; color: #333; font-weight: bold;">The {{company.name}} Team</p>
  
  <p style="font-size: 14px; color: #666; font-style: italic;">
    P.S. Follow us on social media for the latest updates and adorable kids' fashion inspiration!
  </p>
</div>`,
    variables: ['customer.firstName', 'company.name', 'company.email', 'company.phone'],
    isActive: true,
    isSystem: true,
    category: 'Account',
    tags: ['welcome', 'onboarding', 'customer']
  },
  {
    id: 'order_confirmation_email',
    name: 'Order Confirmation Email',
    description: 'Email sent when an order is confirmed',
    type: 'ORDER_CONFIRMATION',
    channel: 'EMAIL',
    subject: 'Order Confirmation - #{{order.number}} 📦',
    content: `Hi {{customer.firstName}},

Thank you for your order! We've received your order and it's being processed.

ORDER DETAILS
Order Number: {{order.number}}
Order Date: {{order.date}}
Total Amount: {{order.total}}

ITEMS ORDERED
{{order.items}}

SHIPPING ADDRESS
{{order.shippingAddress}}

BILLING ADDRESS
{{order.billingAddress}}

We'll send you another email with tracking information when your order ships. You can also track your order anytime by logging into your account.

Thank you for choosing {{company.name}}!

Best regards,
The {{company.name}} Team`,
    variables: [
      'customer.firstName', 
      'order.number', 
      'order.date', 
      'order.total', 
      'order.items', 
      'order.shippingAddress',
      'order.billingAddress',
      'company.name'
    ],
    isActive: true,
    isSystem: true,
    category: 'Orders',
    tags: ['order', 'confirmation', 'transactional']
  },
  {
    id: 'order_confirmation_sms',
    name: 'Order Confirmation SMS',
    description: 'SMS sent when an order is confirmed',
    type: 'ORDER_CONFIRMATION',
    channel: 'SMS',
    content: 'Hi {{customer.firstName}}! Your order #{{order.number}} for {{order.total}} has been confirmed. Track: {{order.trackingUrl}}',
    variables: ['customer.firstName', 'order.number', 'order.total', 'order.trackingUrl'],
    isActive: true,
    isSystem: true,
    category: 'Orders',
    tags: ['order', 'confirmation', 'sms']
  },
  {
    id: 'shipping_update_email',
    name: 'Shipping Update Email',
    description: 'Email sent when order ships',
    type: 'SHIPPING_UPDATE',
    channel: 'EMAIL',
    subject: 'Your order #{{order.number}} is on its way! 🚚',
    content: `Hi {{customer.firstName}},

Great news! Your order #{{order.number}} has been shipped and is on its way to you.

SHIPPING DETAILS
Tracking Number: {{shipping.trackingNumber}}
Carrier: {{shipping.carrier}}
Estimated Delivery: {{shipping.estimatedDelivery}}

You can track your package here: {{shipping.trackingUrl}}

Your order will be delivered to:
{{order.shippingAddress}}

If you have any questions about your shipment, please don't hesitate to contact us.

Thank you for shopping with {{company.name}}!`,
    variables: [
      'customer.firstName',
      'order.number',
      'shipping.trackingNumber',
      'shipping.carrier',
      'shipping.estimatedDelivery',
      'shipping.trackingUrl',
      'order.shippingAddress',
      'company.name'
    ],
    isActive: true,
    isSystem: true,
    category: 'Shipping',
    tags: ['shipping', 'tracking', 'delivery']
  },
  {
    id: 'payment_failed_email',
    name: 'Payment Failed Email',
    description: 'Email sent when payment fails',
    type: 'PAYMENT_FAILED',
    channel: 'EMAIL',
    subject: 'Payment Issue - Order #{{order.number}} ⚠️',
    content: `Hi {{customer.firstName}},

We encountered an issue processing the payment for your order #{{order.number}}.

ORDER DETAILS
Order Number: {{order.number}}
Total Amount: {{order.total}}
Payment Method: {{payment.method}}

WHAT HAPPENED
{{payment.errorMessage}}

NEXT STEPS
Please update your payment information or try a different payment method to complete your order.

Update Payment: {{payment.updateUrl}}

Your order will be held for 48 hours. If payment is not completed within this time, the order will be automatically cancelled.

If you need assistance, please contact our support team at {{company.email}}.

Thank you,
The {{company.name}} Team`,
    variables: [
      'customer.firstName',
      'order.number',
      'order.total',
      'payment.method',
      'payment.errorMessage',
      'payment.updateUrl',
      'company.email',
      'company.name'
    ],
    isActive: true,
    isSystem: true,
    category: 'Payments',
    tags: ['payment', 'failed', 'urgent']
  },
  {
    id: 'promotional_email',
    name: 'Promotional Email Template',
    description: 'Template for promotional campaigns',
    type: 'PROMOTIONAL',
    channel: 'EMAIL',
    subject: '{{promotion.title}} - Special Offer Inside! 🎉',
    content: `Hi {{customer.firstName}},

{{promotion.description}}

{{promotion.details}}

Use code: {{promotion.code}}
Valid until: {{promotion.expiryDate}}

Shop Now: {{promotion.shopUrl}}

Don't miss out on this amazing deal!

Happy shopping,
The {{company.name}} Team`,
    variables: [
      'customer.firstName',
      'promotion.title',
      'promotion.description',
      'promotion.details',
      'promotion.code',
      'promotion.expiryDate',
      'promotion.shopUrl',
      'company.name'
    ],
    isActive: true,
    isSystem: false,
    category: 'Marketing',
    tags: ['promotion', 'marketing', 'discount']
  }
]

/**
 * Seed notification templates
 */
async function seedTemplates() {
  console.log('🌱 Seeding notification templates...')

  for (const template of defaultTemplates) {
    try {
      await prisma.notificationTemplate.upsert({
        where: { id: template.id },
        update: {
          name: template.name,
          description: template.description,
          subject: template.subject,
          content: template.content,
          htmlContent: template.htmlContent,
          variables: template.variables,
          isActive: template.isActive,
          isSystem: template.isSystem,
          category: template.category,
          tags: template.tags,
          updatedAt: new Date()
        },
        create: {
          id: template.id,
          name: template.name,
          description: template.description,
          type: template.type as any,
          channel: template.channel as any,
          subject: template.subject,
          content: template.content,
          htmlContent: template.htmlContent,
          variables: template.variables,
          isActive: template.isActive,
          isSystem: template.isSystem,
          category: template.category,
          tags: template.tags,
          metadata: {},
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
      console.log(`✅ Template created/updated: ${template.name}`)
    } catch (error) {
      console.error(`❌ Failed to create template ${template.name}:`, error)
    }
  }
}

/**
 * Seed test notification preferences
 */
async function seedPreferences() {
  console.log('🌱 Seeding test notification preferences...')

  const testUsers = [
    'user_test_1',
    'user_test_2',
    'admin_test_1'
  ]

  for (const userId of testUsers) {
    try {
      await prisma.notificationPreference.upsert({
        where: {
          userId_userType: {
            userId,
            userType: 'CUSTOMER'
          }
        },
        update: {
          updatedAt: new Date()
        },
        create: {
          userId,
          userType: 'CUSTOMER',
          emailEnabled: true,
          smsEnabled: true,
          pushEnabled: true,
          inAppEnabled: true,
          frequency: 'IMMEDIATE',
          quietHoursStart: '22:00',
          quietHoursEnd: '08:00',
          timezone: 'Africa/Johannesburg',
          language: 'en',
          categories: {
            orders: true,
            payments: true,
            shipping: true,
            promotions: false,
            system: true,
            security: true
          },
          metadata: {},
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
      console.log(`✅ Preferences created for user: ${userId}`)
    } catch (error) {
      console.error(`❌ Failed to create preferences for ${userId}:`, error)
    }
  }
}

/**
 * Main seeder function
 */
async function main() {
  try {
    console.log('🚀 Starting notification system database seeding...')
    
    await seedTemplates()
    await seedPreferences()
    
    console.log('✅ Notification system seeding completed successfully!')
  } catch (error) {
    console.error('❌ Seeding failed:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Run seeder if called directly
if (require.main === module) {
  main()
}

export { main as seedNotificationSystem }
