'use client'

import { useState, useEffect, useCallback } from 'react'
import { CMSContent, PostType } from '../types'

interface UseCMSContentOptions {
  postType?: string
  status?: string
  authorId?: string
  search?: string
  limit?: number
  orderBy?: 'title' | 'publishedAt' | 'updatedAt' | 'menuOrder'
  orderDirection?: 'asc' | 'desc'
  enableCache?: boolean
}

interface UseCMSContentResult {
  content: CMSContent[]
  total: number
  loading: boolean
  error: string | null
  refresh: () => Promise<void>
  loadMore: () => Promise<void>
  hasMore: boolean
}

export function useCMSContent(options: UseCMSContentOptions = {}): UseCMSContentResult {
  const [content, setContent] = useState<CMSContent[]>([])
  const [total, setTotal] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [offset, setOffset] = useState(0)

  const {
    postType,
    status = 'published',
    authorId,
    search,
    limit = 20,
    orderBy = 'updatedAt',
    orderDirection = 'desc',
    enableCache = true
  } = options

  const fetchContent = useCallback(async (reset = false) => {
    try {
      setLoading(true)
      setError(null)

      const currentOffset = reset ? 0 : offset
      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: currentOffset.toString(),
        orderBy,
        orderDirection
      })

      if (postType) params.append('postType', postType)
      if (status) params.append('status', status)
      if (authorId) params.append('authorId', authorId)
      if (search) params.append('search', search)

      const response = await fetch(`/api/cms/content?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch content: ${response.statusText}`)
      }

      const data = await response.json()

      if (reset) {
        setContent(data.content)
        setOffset(limit)
      } else {
        setContent(prev => [...prev, ...data.content])
        setOffset(prev => prev + limit)
      }

      setTotal(data.total)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch content')
    } finally {
      setLoading(false)
    }
  }, [postType, status, authorId, search, limit, orderBy, orderDirection, offset])

  const refresh = useCallback(async () => {
    setOffset(0)
    await fetchContent(true)
  }, [fetchContent])

  const loadMore = useCallback(async () => {
    if (!loading && hasMore) {
      await fetchContent(false)
    }
  }, [fetchContent, loading])

  const hasMore = content.length < total

  useEffect(() => {
    fetchContent(true)
  }, [postType, status, authorId, search, orderBy, orderDirection])

  return {
    content,
    total,
    loading,
    error,
    refresh,
    loadMore,
    hasMore
  }
}

interface UseSingleContentOptions {
  id?: string
  slug?: string
  enableCache?: boolean
}

interface UseSingleContentResult {
  content: CMSContent | null
  loading: boolean
  error: string | null
  refresh: () => Promise<void>
}

export function useSingleContent(options: UseSingleContentOptions): UseSingleContentResult {
  const [content, setContent] = useState<CMSContent | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const { id, slug, enableCache = true } = options

  const fetchContent = useCallback(async () => {
    if (!id && !slug) {
      setError('Either id or slug is required')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const endpoint = id ? `/api/cms/content/${id}` : `/api/cms/content/slug/${slug}`
      const response = await fetch(endpoint)
      
      if (!response.ok) {
        if (response.status === 404) {
          setContent(null)
          return
        }
        throw new Error(`Failed to fetch content: ${response.statusText}`)
      }

      const data = await response.json()
      setContent(data.content)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch content')
    } finally {
      setLoading(false)
    }
  }, [id, slug])

  const refresh = useCallback(async () => {
    await fetchContent()
  }, [fetchContent])

  useEffect(() => {
    fetchContent()
  }, [fetchContent])

  return {
    content,
    loading,
    error,
    refresh
  }
}

interface UseContentMutationOptions {
  onSuccess?: (content: CMSContent) => void
  onError?: (error: string) => void
}

interface UseContentMutationResult {
  createContent: (data: Partial<CMSContent>) => Promise<CMSContent | null>
  updateContent: (id: string, data: Partial<CMSContent>) => Promise<CMSContent | null>
  deleteContent: (id: string) => Promise<boolean>
  publishContent: (id: string) => Promise<CMSContent | null>
  unpublishContent: (id: string) => Promise<CMSContent | null>
  loading: boolean
  error: string | null
}

export function useContentMutation(options: UseContentMutationOptions = {}): UseContentMutationResult {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const { onSuccess, onError } = options

  const createContent = useCallback(async (data: Partial<CMSContent>): Promise<CMSContent | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/cms/content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        throw new Error(`Failed to create content: ${response.statusText}`)
      }

      const result = await response.json()
      onSuccess?.(result.content)
      return result.content
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create content'
      setError(errorMessage)
      onError?.(errorMessage)
      return null
    } finally {
      setLoading(false)
    }
  }, [onSuccess, onError])

  const updateContent = useCallback(async (id: string, data: Partial<CMSContent>): Promise<CMSContent | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/cms/content/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        throw new Error(`Failed to update content: ${response.statusText}`)
      }

      const result = await response.json()
      onSuccess?.(result.content)
      return result.content
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update content'
      setError(errorMessage)
      onError?.(errorMessage)
      return null
    } finally {
      setLoading(false)
    }
  }, [onSuccess, onError])

  const deleteContent = useCallback(async (id: string): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/cms/content/${id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error(`Failed to delete content: ${response.statusText}`)
      }

      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete content'
      setError(errorMessage)
      onError?.(errorMessage)
      return false
    } finally {
      setLoading(false)
    }
  }, [onError])

  const publishContent = useCallback(async (id: string): Promise<CMSContent | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/cms/content/${id}/publish`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error(`Failed to publish content: ${response.statusText}`)
      }

      const result = await response.json()
      onSuccess?.(result.content)
      return result.content
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to publish content'
      setError(errorMessage)
      onError?.(errorMessage)
      return null
    } finally {
      setLoading(false)
    }
  }, [onSuccess, onError])

  const unpublishContent = useCallback(async (id: string): Promise<CMSContent | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/cms/content/${id}/unpublish`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error(`Failed to unpublish content: ${response.statusText}`)
      }

      const result = await response.json()
      onSuccess?.(result.content)
      return result.content
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to unpublish content'
      setError(errorMessage)
      onError?.(errorMessage)
      return null
    } finally {
      setLoading(false)
    }
  }, [onSuccess, onError])

  return {
    createContent,
    updateContent,
    deleteContent,
    publishContent,
    unpublishContent,
    loading,
    error
  }
}
