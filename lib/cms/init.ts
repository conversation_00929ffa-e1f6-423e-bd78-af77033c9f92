/**
 * CMS System Initialization
 * 
 * This module initializes the core CMS system including:
 * - <PERSON><PERSON><PERSON> post types
 * - Default taxonomies
 * - Plugin system
 * - Hook system
 * - Component registry
 */

import { PostTypeService } from './services/post-type-service'
import { TaxonomyService } from './services/taxonomy-service'
import { PluginManager } from './plugins/plugin-manager'
import { HookSystem } from './plugins/hook-system'
import { ComponentRegistry } from './rendering/component-registry'
import { DEFAULT_POST_TYPES, DEFAULT_TAXONOMIES } from './constants'

export class CMSInitializer {
  private static initialized = false

  /**
   * Initialize the CMS system
   */
  static async initialize(): Promise<void> {
    if (this.initialized) {
      console.log('CMS system already initialized')
      return
    }

    console.log('Initializing CMS system...')

    try {
      // Initialize hook system first
      HookSystem.initialize()
      console.log('✓ Hook system initialized')

      // Initialize plugin manager
      const pluginManager = PluginManager.getInstance()
      await pluginManager.initialize()
      console.log('✓ Plugin system initialized')

      // Initialize component registry
      ComponentRegistry.initializeDefaults()
      console.log('✓ Component registry initialized')

      // Initialize default post types
      await this.initializePostTypes()
      console.log('✓ Post types initialized')

      // Initialize default taxonomies
      await this.initializeTaxonomies()
      console.log('✓ Taxonomies initialized')

      // Register core hooks
      this.registerCoreHooks()
      console.log('✓ Core hooks registered')

      this.initialized = true
      console.log('🎉 CMS system initialization complete')

    } catch (error) {
      console.error('❌ CMS system initialization failed:', error)
      throw error
    }
  }

  /**
   * Initialize default post types
   */
  private static async initializePostTypes(): Promise<void> {
    for (const postTypeData of DEFAULT_POST_TYPES) {
      try {
        const existing = await PostTypeService.getPostType(postTypeData.name!)
        if (!existing) {
          await PostTypeService.registerPostType(postTypeData)
          console.log(`  ✓ Registered post type: ${postTypeData.name}`)
        }
      } catch (error) {
        console.error(`  ❌ Failed to register post type ${postTypeData.name}:`, error)
      }
    }
  }

  /**
   * Initialize default taxonomies
   */
  private static async initializeTaxonomies(): Promise<void> {
    for (const taxonomyData of DEFAULT_TAXONOMIES) {
      try {
        const existing = await TaxonomyService.getTaxonomy(taxonomyData.name)
        if (!existing) {
          await TaxonomyService.registerTaxonomy(taxonomyData)
          console.log(`  ✓ Registered taxonomy: ${taxonomyData.name}`)
        }
      } catch (error) {
        console.error(`  ❌ Failed to register taxonomy ${taxonomyData.name}:`, error)
      }
    }
  }

  /**
   * Register core CMS hooks
   */
  private static registerCoreHooks(): void {
    // Content hooks
    HookSystem.addAction('cms_content_created', async (content) => {
      console.log(`Content created: ${content.title} (${content.id})`)
    })

    HookSystem.addAction('cms_content_updated', async (content, previousContent) => {
      console.log(`Content updated: ${content.title} (${content.id})`)
    })

    HookSystem.addAction('cms_content_deleted', async (content) => {
      console.log(`Content deleted: ${content.title} (${content.id})`)
    })

    HookSystem.addAction('cms_content_published', async (content) => {
      console.log(`Content published: ${content.title} (${content.id})`)
    })

    // Post type hooks
    HookSystem.addAction('cms_post_type_registered', async (postType) => {
      console.log(`Post type registered: ${postType.name}`)
    })

    // Plugin hooks
    HookSystem.addAction('cms_plugin_activated', async (plugin) => {
      console.log(`Plugin activated: ${plugin.name}`)
    })

    HookSystem.addAction('cms_plugin_deactivated', async (plugin) => {
      console.log(`Plugin deactivated: ${plugin.name}`)
    })

    // Filter hooks
    HookSystem.addFilter('cms_content_filter', (content) => {
      // Apply default content filtering
      return content
    })

    HookSystem.addFilter('cms_template_hierarchy', (hierarchy) => {
      // Allow modification of template hierarchy
      return hierarchy
    })
  }

  /**
   * Check if CMS is initialized
   */
  static isInitialized(): boolean {
    return this.initialized
  }

  /**
   * Reset initialization state (for testing)
   */
  static reset(): void {
    this.initialized = false
  }

  /**
   * Get initialization status
   */
  static getStatus(): {
    initialized: boolean
    components: {
      hookSystem: boolean
      pluginManager: boolean
      componentRegistry: boolean
    }
  } {
    return {
      initialized: this.initialized,
      components: {
        hookSystem: true, // Always available
        pluginManager: true, // Always available
        componentRegistry: ComponentRegistry.getAllTemplates().length > 0
      }
    }
  }
}

/**
 * Auto-initialize CMS system when module is imported
 * This ensures the CMS is ready when the application starts
 */
export async function initializeCMS(): Promise<void> {
  if (typeof window === 'undefined') {
    // Only initialize on server side
    await CMSInitializer.initialize()
  }
}

// Auto-initialize if not in test environment
if (process.env.NODE_ENV !== 'test') {
  initializeCMS().catch(console.error)
}

export default CMSInitializer
