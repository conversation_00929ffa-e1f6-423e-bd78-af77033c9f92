# WordPress-Inspired CMS System

A comprehensive, extensible Content Management System built for Next.js 15 with TypeScript, Prisma, and Shadcn UI components.

## 🌟 Features

### Core CMS Architecture
- **WordPress-inspired Post Types** - Flexible content type system
- **Custom Fields** - Advanced Custom Fields (ACF) inspired field system
- **Taxonomy Management** - Categories, tags, and custom taxonomies
- **Plugin Architecture** - Extensible plugin system with hooks and filters
- **Dynamic Rendering** - Component-based template system
- **AI Integration** - AI-powered content generation and extension

### Content Management
- **Hierarchical Content** - Support for parent-child relationships
- **Content Revisions** - Track content changes over time
- **SEO Optimization** - Built-in SEO fields and meta management
- **Media Management** - Image and file handling
- **Comments System** - Built-in commenting functionality

### Developer Experience
- **TypeScript First** - Full type safety throughout
- **React Components** - Shadcn UI component integration
- **Hook System** - WordPress-style actions and filters
- **Template Hierarchy** - Flexible template resolution
- **API Routes** - RESTful API for content management

## 🚀 Quick Start

### 1. Initialize the CMS System

```typescript
import { CMSInitializer } from '@/lib/cms/init'

// Initialize CMS (automatically done on import)
await CMSInitializer.initialize()
```

### 2. Create Content

```typescript
import { ContentService } from '@/lib/cms/services/content-service'

const content = await ContentService.createContent({
  title: 'My First Post',
  content: 'This is the content of my first post.',
  postType: 'post',
  status: 'published'
})
```

### 3. Render Content

```typescript
import { CMSRenderer } from '@/lib/cms/rendering/cms-renderer'

function MyPage({ content, postType }) {
  return (
    <CMSRenderer
      content={content}
      postType={postType}
    />
  )
}
```

## 📚 Core Concepts

### Post Types

Post types define the structure and behavior of different content types:

```typescript
import { PostTypeService } from '@/lib/cms/services/post-type-service'

await PostTypeService.registerPostType({
  name: 'product',
  label: 'Product',
  labelPlural: 'Products',
  isPublic: true,
  supportsTitle: true,
  supportsContent: true,
  supportsCustomFields: true,
  taxonomies: ['product_category', 'product_tag']
})
```

### Custom Fields

Create flexible field groups for enhanced content:

```typescript
import { CustomFieldService } from '@/lib/cms/services/custom-field-service'

// Create field group
const group = await CustomFieldService.createFieldGroup({
  title: 'Product Details',
  key: 'product_details',
  postTypes: ['product']
})

// Add fields to group
await CustomFieldService.addField('product_details', {
  label: 'Price',
  name: 'price',
  type: 'number',
  required: true
})
```

### Taxonomies

Organize content with categories and tags:

```typescript
import { TaxonomyService } from '@/lib/cms/services/taxonomy-service'

await TaxonomyService.registerTaxonomy({
  name: 'product_category',
  label: 'Product Category',
  isHierarchical: true,
  postTypes: ['product']
})
```

## 🔌 Plugin System

### Creating a Plugin

```typescript
import { PluginManager } from '@/lib/cms/plugins/plugin-manager'

const plugin = await PluginManager.registerPlugin({
  name: 'My Custom Plugin',
  slug: 'my-custom-plugin',
  version: '1.0.0',
  description: 'A custom plugin for my site',
  author: 'Your Name'
})
```

### Adding Hooks

```typescript
import { HookSystem } from '@/lib/cms/plugins/hook-system'

// Add action hook
HookSystem.addAction('cms_content_created', (content) => {
  console.log('New content created:', content.title)
})

// Add filter hook
HookSystem.addFilter('cms_content_filter', (content) => {
  // Modify content before rendering
  return {
    ...content,
    title: content.title.toUpperCase()
  }
})
```

### Registering Components

```typescript
import { ComponentRegistry } from '@/lib/cms/rendering/component-registry'

// Register template component
ComponentRegistry.registerTemplate('my-template', ({ content, postType }) => {
  return (
    <div>
      <h1>{content.title}</h1>
      <div>{content.content}</div>
    </div>
  )
})

// Register field renderer
ComponentRegistry.registerFieldRenderer('price', ({ value }) => {
  return <span>${value}</span>
}, {}, ['number'])
```

## 🎨 Template System

### Template Hierarchy

The CMS follows a WordPress-inspired template hierarchy:

```
single-{post_type}-{slug}.tsx
single-{post_type}.tsx
single.tsx
singular.tsx
index.tsx
```

### Custom Templates

```typescript
import { TemplateResolver } from '@/lib/cms/rendering/template-resolver'

// Get template hierarchy for content
const hierarchy = TemplateResolver.getTemplateHierarchy(postType, content)

// Resolve template
const template = TemplateResolver.resolveTemplate(content, postType)
```

## 🔧 API Routes

### Content API

```typescript
// GET /api/cms/content - List content
// POST /api/cms/content - Create content
// GET /api/cms/content/[id] - Get content by ID
// PUT /api/cms/content/[id] - Update content
// DELETE /api/cms/content/[id] - Delete content
```

### Using the API

```typescript
// Fetch content
const response = await fetch('/api/cms/content?postType=post&status=published')
const { content, total } = await response.json()

// Create content
const response = await fetch('/api/cms/content', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    title: 'New Post',
    content: 'Post content',
    postType: 'post'
  })
})
```

## 🪝 React Hooks

### Content Hooks

```typescript
import { useCMSContent, useSingleContent, useContentMutation } from '@/lib/cms/hooks/use-cms-content'

// List content
const { content, loading, error, loadMore } = useCMSContent({
  postType: 'post',
  status: 'published'
})

// Single content
const { content, loading, error } = useSingleContent({
  slug: 'my-post-slug'
})

// Content mutations
const { createContent, updateContent, deleteContent } = useContentMutation({
  onSuccess: (content) => console.log('Content saved:', content.title)
})
```

## 🔍 Advanced Features

### AI Integration

The CMS integrates with your existing AI systems for content generation:

```typescript
// AI-powered content generation is handled through the existing
// AI block generator and Vercel AI SDK integration
```

### Performance Optimization

- **Server-side Rendering** - Full SSR support with Next.js 15
- **Caching** - Built-in caching for content and templates
- **Lazy Loading** - Component-level lazy loading
- **Database Optimization** - Efficient Prisma queries

### Security

- **Input Sanitization** - Content sanitization for XSS prevention
- **Authentication** - Integration with existing auth systems
- **Role-based Access** - Granular permission system
- **CSRF Protection** - Built-in CSRF protection

## 📖 Examples

Check the `/examples` directory for complete implementation examples:

- Basic blog setup
- E-commerce product catalog
- Custom post types
- Plugin development
- Template customization

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This CMS system is part of the Coco Milk Kids project and follows the same licensing terms.

## 🆘 Support

For support and questions:
- Check the documentation
- Review example implementations
- Open an issue on GitHub
- Contact the development team

---

**Built with ❤️ for the Next.js ecosystem**
