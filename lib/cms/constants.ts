import { PostType, FieldType } from './types'

// Default Post Types
export const DEFAULT_POST_TYPES: Partial<PostType>[] = [
  {
    name: 'page',
    label: 'Page',
    labelPlural: 'Pages',
    description: 'Static pages for your website',
    icon: 'file-text',
    isPublic: true,
    isHierarchical: true,
    hasArchive: false,
    supportsTitle: true,
    supportsContent: true,
    supportsExcerpt: false,
    supportsThumbnail: true,
    supportsComments: false,
    supportsRevisions: true,
    supportsPageBuilder: true,
    supportsCustomFields: true,
    menuPosition: 20,
    taxonomies: [],
    templates: ['page.tsx', 'page-{slug}.tsx', 'page-{id}.tsx'],
    singleTemplate: 'page.tsx',
    rewriteSlug: '',
    rewriteWithFront: false,
    canExport: true,
    showInRest: true,
    restBase: 'pages',
    isSystem: true,
    isActive: true
  },
  {
    name: 'post',
    label: 'Post',
    labelPlural: 'Posts',
    description: 'Blog posts and articles',
    icon: 'edit',
    isPublic: true,
    isHierarchical: false,
    hasArchive: true,
    supportsTitle: true,
    supportsContent: true,
    supportsExcerpt: true,
    supportsThumbnail: true,
    supportsComments: true,
    supportsRevisions: true,
    supportsPageBuilder: true,
    supportsCustomFields: true,
    menuPosition: 5,
    taxonomies: ['category', 'post_tag'],
    templates: ['single.tsx', 'single-post.tsx', 'single-{slug}.tsx'],
    archiveTemplate: 'archive.tsx',
    singleTemplate: 'single.tsx',
    rewriteSlug: 'blog',
    rewriteWithFront: true,
    canExport: true,
    showInRest: true,
    restBase: 'posts',
    isSystem: true,
    isActive: true
  },
  {
    name: 'product',
    label: 'Product',
    labelPlural: 'Products',
    description: 'E-commerce products',
    icon: 'shopping-bag',
    isPublic: true,
    isHierarchical: false,
    hasArchive: true,
    supportsTitle: true,
    supportsContent: true,
    supportsExcerpt: true,
    supportsThumbnail: true,
    supportsComments: false,
    supportsRevisions: true,
    supportsPageBuilder: false,
    supportsCustomFields: true,
    menuPosition: 10,
    taxonomies: ['product_category', 'product_tag'],
    templates: ['single-product.tsx', 'product.tsx'],
    archiveTemplate: 'archive-product.tsx',
    singleTemplate: 'single-product.tsx',
    rewriteSlug: 'products',
    rewriteWithFront: true,
    canExport: true,
    showInRest: true,
    restBase: 'products',
    isSystem: false,
    isActive: true
  }
]

// Default Taxonomies
export const DEFAULT_TAXONOMIES = [
  {
    name: 'category',
    label: 'Category',
    labelPlural: 'Categories',
    description: 'Post categories',
    isHierarchical: true,
    isPublic: true,
    showInMenu: true,
    showInRest: true,
    postTypes: ['post'],
    isSystem: true,
    isActive: true
  },
  {
    name: 'post_tag',
    label: 'Tag',
    labelPlural: 'Tags',
    description: 'Post tags',
    isHierarchical: false,
    isPublic: true,
    showInMenu: true,
    showInRest: true,
    postTypes: ['post'],
    isSystem: true,
    isActive: true
  },
  {
    name: 'product_category',
    label: 'Product Category',
    labelPlural: 'Product Categories',
    description: 'Product categories',
    isHierarchical: true,
    isPublic: true,
    showInMenu: true,
    showInRest: true,
    postTypes: ['product'],
    isSystem: false,
    isActive: true
  },
  {
    name: 'product_tag',
    label: 'Product Tag',
    labelPlural: 'Product Tags',
    description: 'Product tags',
    isHierarchical: false,
    isPublic: true,
    showInMenu: true,
    showInRest: true,
    postTypes: ['product'],
    isSystem: false,
    isActive: true
  }
]

// Field Types Configuration
export const FIELD_TYPES: Record<FieldType, {
  label: string
  category: string
  description: string
  defaultConfig: Record<string, any>
}> = {
  text: {
    label: 'Text',
    category: 'Basic',
    description: 'Single line text input',
    defaultConfig: {
      placeholder: '',
      maxLength: 255,
      readonly: false,
      disabled: false
    }
  },
  textarea: {
    label: 'Textarea',
    category: 'Basic',
    description: 'Multi-line text input',
    defaultConfig: {
      placeholder: '',
      rows: 4,
      maxLength: 1000,
      readonly: false,
      disabled: false
    }
  },
  number: {
    label: 'Number',
    category: 'Basic',
    description: 'Numeric input',
    defaultConfig: {
      min: null,
      max: null,
      step: 1,
      placeholder: ''
    }
  },
  email: {
    label: 'Email',
    category: 'Basic',
    description: 'Email address input',
    defaultConfig: {
      placeholder: '<EMAIL>'
    }
  },
  url: {
    label: 'URL',
    category: 'Basic',
    description: 'URL input',
    defaultConfig: {
      placeholder: 'https://example.com'
    }
  },
  password: {
    label: 'Password',
    category: 'Basic',
    description: 'Password input',
    defaultConfig: {
      placeholder: ''
    }
  },
  wysiwyg: {
    label: 'WYSIWYG Editor',
    category: 'Content',
    description: 'Rich text editor',
    defaultConfig: {
      toolbar: 'full',
      mediaUpload: true,
      height: 300
    }
  },
  image: {
    label: 'Image',
    category: 'Media',
    description: 'Single image upload',
    defaultConfig: {
      returnFormat: 'array',
      previewSize: 'medium',
      library: 'all'
    }
  },
  gallery: {
    label: 'Gallery',
    category: 'Media',
    description: 'Multiple image upload',
    defaultConfig: {
      returnFormat: 'array',
      previewSize: 'medium',
      library: 'all',
      min: 0,
      max: 0
    }
  },
  select: {
    label: 'Select',
    category: 'Choice',
    description: 'Dropdown selection',
    defaultConfig: {
      choices: {},
      allowNull: false,
      multiple: false,
      ui: true,
      ajax: false,
      placeholder: 'Select...'
    }
  },
  checkbox: {
    label: 'Checkbox',
    category: 'Choice',
    description: 'Multiple checkboxes',
    defaultConfig: {
      choices: {},
      layout: 'vertical',
      toggle: false,
      allowOther: false
    }
  },
  radio: {
    label: 'Radio Button',
    category: 'Choice',
    description: 'Single choice radio buttons',
    defaultConfig: {
      choices: {},
      layout: 'vertical',
      allowOther: false
    }
  },
  button_group: {
    label: 'Button Group',
    category: 'Choice',
    description: 'Button group selection',
    defaultConfig: {
      choices: {},
      allowNull: false,
      layout: 'horizontal'
    }
  },
  true_false: {
    label: 'True/False',
    category: 'Choice',
    description: 'Boolean toggle',
    defaultConfig: {
      message: '',
      ui: true,
      uiOn: 'Yes',
      uiOff: 'No'
    }
  },
  date_picker: {
    label: 'Date Picker',
    category: 'Date & Time',
    description: 'Date selection',
    defaultConfig: {
      displayFormat: 'd/m/Y',
      returnFormat: 'Y-m-d',
      firstDay: 1
    }
  },
  color_picker: {
    label: 'Color Picker',
    category: 'Basic',
    description: 'Color selection',
    defaultConfig: {
      enableOpacity: false,
      returnFormat: 'string'
    }
  },
  repeater: {
    label: 'Repeater',
    category: 'Layout',
    description: 'Repeatable group of fields',
    defaultConfig: {
      collapsed: '',
      min: 0,
      max: 0,
      layout: 'table',
      buttonLabel: 'Add Row'
    }
  },
  flexible_content: {
    label: 'Flexible Content',
    category: 'Layout',
    description: 'Flexible content layouts',
    defaultConfig: {
      layouts: {},
      min: 0,
      max: 0,
      buttonLabel: 'Add Row'
    }
  },
  group: {
    label: 'Group',
    category: 'Layout',
    description: 'Group of fields',
    defaultConfig: {
      layout: 'block'
    }
  },
  clone: {
    label: 'Clone',
    category: 'Layout',
    description: 'Clone existing fields',
    defaultConfig: {
      clone: [],
      display: 'seamless',
      layout: 'block',
      prefix_label: false,
      prefix_name: false
    }
  },
  link: {
    label: 'Link',
    category: 'Relational',
    description: 'Link object',
    defaultConfig: {
      returnFormat: 'array'
    }
  },
  post_object: {
    label: 'Post Object',
    category: 'Relational',
    description: 'Select posts',
    defaultConfig: {
      postType: [],
      taxonomy: [],
      allowNull: false,
      multiple: false,
      returnFormat: 'object',
      ui: true
    }
  },
  page_link: {
    label: 'Page Link',
    category: 'Relational',
    description: 'Select page links',
    defaultConfig: {
      postType: [],
      taxonomy: [],
      allowNull: false,
      allowArchives: true,
      multiple: false
    }
  },
  relationship: {
    label: 'Relationship',
    category: 'Relational',
    description: 'Bidirectional relationship',
    defaultConfig: {
      postType: [],
      taxonomy: [],
      filters: ['search', 'post_type', 'taxonomy'],
      elements: [],
      min: 0,
      max: 0,
      returnFormat: 'object'
    }
  },
  taxonomy: {
    label: 'Taxonomy',
    category: 'Relational',
    description: 'Select taxonomy terms',
    defaultConfig: {
      taxonomy: 'category',
      fieldType: 'checkbox',
      allowNull: false,
      addTerm: true,
      saveTerms: false,
      loadTerms: false,
      returnFormat: 'id',
      multiple: false
    }
  },
  user: {
    label: 'User',
    category: 'Relational',
    description: 'Select users',
    defaultConfig: {
      role: [],
      allowNull: false,
      multiple: false,
      returnFormat: 'array'
    }
  },
  google_map: {
    label: 'Google Map',
    category: 'Advanced',
    description: 'Google Maps integration',
    defaultConfig: {
      center_lat: '',
      center_lng: '',
      zoom: 14,
      height: 400
    }
  },
  date_time_picker: {
    label: 'Date Time Picker',
    category: 'Date & Time',
    description: 'Date and time selection',
    defaultConfig: {
      displayFormat: 'd/m/Y g:i a',
      returnFormat: 'Y-m-d H:i:s',
      firstDay: 1
    }
  },
  time_picker: {
    label: 'Time Picker',
    category: 'Date & Time',
    description: 'Time selection',
    defaultConfig: {
      displayFormat: 'g:i a',
      returnFormat: 'H:i:s'
    }
  },
  range: {
    label: 'Range',
    category: 'Basic',
    description: 'Range slider',
    defaultConfig: {
      min: 0,
      max: 100,
      step: 1,
      prepend: '',
      append: ''
    }
  },
  accordion: {
    label: 'Accordion',
    category: 'Layout',
    description: 'Collapsible content section',
    defaultConfig: {
      open: false,
      multiExpand: false,
      endpoint: false
    }
  },
  tab: {
    label: 'Tab',
    category: 'Layout',
    description: 'Tab container',
    defaultConfig: {
      placement: 'top',
      endpoint: false
    }
  },
  message: {
    label: 'Message',
    category: 'Layout',
    description: 'Display message',
    defaultConfig: {
      message: '',
      newLines: 'wpautop',
      escapeHtml: false
    }
  }
}

// Hook Priorities
export const HOOK_PRIORITIES = {
  HIGHEST: 1,
  HIGH: 5,
  DEFAULT: 10,
  LOW: 15,
  LOWEST: 20
}

// Template Hierarchy
export const TEMPLATE_HIERARCHY = {
  page: [
    'page-{slug}.tsx',
    'page-{id}.tsx',
    'page.tsx',
    'singular.tsx',
    'index.tsx'
  ],
  post: [
    'single-post-{slug}.tsx',
    'single-post-{id}.tsx',
    'single-post.tsx',
    'single.tsx',
    'singular.tsx',
    'index.tsx'
  ],
  archive: [
    'archive-{post_type}.tsx',
    'archive.tsx',
    'index.tsx'
  ],
  taxonomy: [
    'taxonomy-{taxonomy}-{term}.tsx',
    'taxonomy-{taxonomy}.tsx',
    'taxonomy.tsx',
    'archive.tsx',
    'index.tsx'
  ],
  search: [
    'search.tsx',
    'index.tsx'
  ],
  '404': [
    '404.tsx',
    'index.tsx'
  ]
}

// Post Statuses
export const POST_STATUSES = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  PRIVATE: 'private',
  PENDING: 'pending',
  TRASH: 'trash'
} as const

// Comment Statuses
export const COMMENT_STATUSES = {
  APPROVED: 'approved',
  PENDING: 'pending',
  SPAM: 'spam',
  TRASH: 'trash'
} as const
