import { PrismaClient } from '@prisma/client'
import { PostType } from '../types'
import { DEFAULT_POST_TYPES } from '../constants'
// import { generateSlug, validatePostType } from '../utils'

const prisma = new PrismaClient()

export class PostTypeService {
  /**
   * Register a new post type
   */
  static async registerPostType(postTypeData: Partial<PostType>): Promise<PostType> {
    // Validate required fields
    if (!postTypeData.name || !postTypeData.label) {
      throw new Error('Post type name and label are required')
    }

    // Check if post type already exists
    const existing = await prisma.postType.findUnique({
      where: { name: postTypeData.name! }
    })

    if (existing) {
      throw new Error(`Post type '${postTypeData.name}' already exists`)
    }

    // Generate slug if not provided
    const slug = postTypeData.rewriteSlug || postTypeData.name!.toLowerCase().replace(/[^a-z0-9]/g, '-')

    // Create the post type
    const postType = await prisma.postType.create({
      data: {
        name: postTypeData.name!,
        label: postTypeData.label!,
        labelPlural: postTypeData.labelPlural || `${postTypeData.label}s`,
        description: postTypeData.description,
        icon: postTypeData.icon,
        isPublic: postTypeData.isPublic ?? true,
        isHierarchical: postTypeData.isHierarchical ?? false,
        hasArchive: postTypeData.hasArchive ?? true,
        supportsTitle: postTypeData.supportsTitle ?? true,
        supportsContent: postTypeData.supportsContent ?? true,
        supportsExcerpt: postTypeData.supportsExcerpt ?? false,
        supportsThumbnail: postTypeData.supportsThumbnail ?? false,
        supportsComments: postTypeData.supportsComments ?? false,
        supportsRevisions: postTypeData.supportsRevisions ?? true,
        supportsPageBuilder: postTypeData.supportsPageBuilder ?? false,
        supportsCustomFields: postTypeData.supportsCustomFields ?? true,
        menuPosition: postTypeData.menuPosition,
        capabilities: postTypeData.capabilities || {},
        taxonomies: postTypeData.taxonomies || [],
        customFields: postTypeData.customFields || [],
        templates: postTypeData.templates || [],
        archiveTemplate: postTypeData.archiveTemplate,
        singleTemplate: postTypeData.singleTemplate,
        rewriteSlug: slug,
        rewriteWithFront: postTypeData.rewriteWithFront ?? true,
        queryVar: postTypeData.queryVar,
        canExport: postTypeData.canExport ?? true,
        showInRest: postTypeData.showInRest ?? true,
        restBase: postTypeData.restBase || postTypeData.name,
        restControllerClass: postTypeData.restControllerClass,
        isSystem: postTypeData.isSystem ?? false,
        isActive: postTypeData.isActive ?? true,
        registeredBy: postTypeData.registeredBy
      }
    })

    return postType as PostType
  }

  /**
   * Get all post types
   */
  static async getPostTypes(includeInactive = false): Promise<PostType[]> {
    const where = includeInactive ? {} : { isActive: true }
    
    const postTypes = await prisma.postType.findMany({
      where,
      orderBy: [
        { menuPosition: 'asc' },
        { label: 'asc' }
      ]
    })

    return postTypes as PostType[]
  }

  /**
   * Get a post type by name
   */
  static async getPostType(name: string): Promise<PostType | null> {
    const postType = await prisma.postType.findUnique({
      where: { name }
    })

    return postType as PostType | null
  }

  /**
   * Update a post type
   */
  static async updatePostType(name: string, updates: Partial<PostType>): Promise<PostType> {
    const postType = await prisma.postType.update({
      where: { name },
      data: {
        ...updates,
        updatedAt: new Date()
      }
    })

    return postType as PostType
  }

  /**
   * Delete a post type
   */
  static async deletePostType(name: string): Promise<void> {
    // Check if it's a system post type
    const postType = await this.getPostType(name)
    if (postType?.isSystem) {
      throw new Error(`Cannot delete system post type '${name}'`)
    }

    // Check if there are posts using this post type
    const postCount = await prisma.post.count({
      where: { postType: name }
    })

    if (postCount > 0) {
      throw new Error(`Cannot delete post type '${name}' because it has ${postCount} posts`)
    }

    await prisma.postType.delete({
      where: { name }
    })
  }

  /**
   * Activate a post type
   */
  static async activatePostType(name: string): Promise<PostType> {
    return this.updatePostType(name, { isActive: true })
  }

  /**
   * Deactivate a post type
   */
  static async deactivatePostType(name: string): Promise<PostType> {
    const postType = await this.getPostType(name)
    if (postType?.isSystem) {
      throw new Error(`Cannot deactivate system post type '${name}'`)
    }

    return this.updatePostType(name, { isActive: false })
  }

  /**
   * Get post types that support a specific feature
   */
  static async getPostTypesByFeature(feature: keyof PostType): Promise<PostType[]> {
    const postTypes = await this.getPostTypes()
    return postTypes.filter(postType => postType[feature] === true)
  }

  /**
   * Get public post types
   */
  static async getPublicPostTypes(): Promise<PostType[]> {
    const postTypes = await prisma.postType.findMany({
      where: {
        isPublic: true,
        isActive: true
      },
      orderBy: [
        { menuPosition: 'asc' },
        { label: 'asc' }
      ]
    })

    return postTypes as PostType[]
  }

  /**
   * Get hierarchical post types
   */
  static async getHierarchicalPostTypes(): Promise<PostType[]> {
    const postTypes = await prisma.postType.findMany({
      where: {
        isHierarchical: true,
        isActive: true
      },
      orderBy: [
        { menuPosition: 'asc' },
        { label: 'asc' }
      ]
    })

    return postTypes as PostType[]
  }

  /**
   * Initialize default post types
   */
  static async initializeDefaultPostTypes(): Promise<void> {
    for (const postTypeData of DEFAULT_POST_TYPES) {
      try {
        const existing = await this.getPostType(postTypeData.name!)
        if (!existing) {
          await this.registerPostType(postTypeData)
          console.log(`Initialized post type: ${postTypeData.name}`)
        }
      } catch (error) {
        console.error(`Failed to initialize post type ${postTypeData.name}:`, error)
      }
    }
  }

  /**
   * Get post type capabilities
   */
  static getPostTypeCapabilities(postType: PostType): Record<string, string> {
    const name = postType.name
    const plural = name + 's'

    return {
      [`edit_${name}`]: `edit_${name}`,
      [`read_${name}`]: `read_${name}`,
      [`delete_${name}`]: `delete_${name}`,
      [`edit_${plural}`]: `edit_${plural}`,
      [`edit_others_${plural}`]: `edit_others_${plural}`,
      [`publish_${plural}`]: `publish_${plural}`,
      [`read_private_${plural}`]: `read_private_${plural}`,
      [`delete_${plural}`]: `delete_${plural}`,
      [`delete_private_${plural}`]: `delete_private_${plural}`,
      [`delete_published_${plural}`]: `delete_published_${plural}`,
      [`delete_others_${plural}`]: `delete_others_${plural}`,
      [`edit_private_${plural}`]: `edit_private_${plural}`,
      [`edit_published_${plural}`]: `edit_published_${plural}`,
      ...postType.capabilities
    }
  }

  /**
   * Check if a post type supports a feature
   */
  static postTypeSupports(postType: PostType, feature: string): boolean {
    const supportMap: Record<string, keyof PostType> = {
      'title': 'supportsTitle',
      'content': 'supportsContent',
      'excerpt': 'supportsExcerpt',
      'thumbnail': 'supportsThumbnail',
      'comments': 'supportsComments',
      'revisions': 'supportsRevisions',
      'page-builder': 'supportsPageBuilder',
      'custom-fields': 'supportsCustomFields'
    }

    const supportKey = supportMap[feature]
    return supportKey ? Boolean(postType[supportKey]) : false
  }

  /**
   * Get template hierarchy for a post type
   */
  static getTemplateHierarchy(postType: PostType, context: 'single' | 'archive' = 'single'): string[] {
    const templates: string[] = []
    
    if (context === 'single') {
      if (postType.singleTemplate) {
        templates.push(postType.singleTemplate)
      }
      templates.push(`single-${postType.name}.tsx`)
      templates.push('single.tsx')
      templates.push('singular.tsx')
    } else {
      if (postType.archiveTemplate) {
        templates.push(postType.archiveTemplate)
      }
      templates.push(`archive-${postType.name}.tsx`)
      templates.push('archive.tsx')
    }
    
    templates.push('index.tsx')
    return templates
  }
}
