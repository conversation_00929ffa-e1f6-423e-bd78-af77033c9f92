import { PrismaClient } from '@prisma/client'
import { CMSContent, PostWithRelations } from '../types'
import { PostTypeService } from './post-type-service'
import { CustomFieldService } from './custom-field-service'
import { POST_STATUSES } from '../constants'

const prisma = new PrismaClient()

export class ContentService {
  /**
   * Create new content
   */
  static async createContent(contentData: Partial<CMSContent>): Promise<CMSContent> {
    if (!contentData.title || !contentData.postType) {
      throw new Error('Title and post type are required')
    }

    // Verify post type exists
    const postType = await PostTypeService.getPostType(contentData.postType)
    if (!postType) {
      throw new Error(`Post type '${contentData.postType}' not found`)
    }

    // Generate slug if not provided
    const slug = contentData.slug || this.generateSlug(contentData.title)

    // Check slug uniqueness
    const existingSlug = await prisma.post.findUnique({
      where: { slug }
    })

    if (existingSlug) {
      throw new Error(`Content with slug '${slug}' already exists`)
    }

    // Create the content
    const content = await prisma.post.create({
      data: {
        title: contentData.title,
        slug,
        content: contentData.content,
        contentHtml: contentData.contentHtml,
        excerpt: contentData.excerpt,
        status: contentData.status || POST_STATUSES.DRAFT,
        postType: contentData.postType,
        parentId: contentData.parentId,
        menuOrder: contentData.menuOrder || 0,
        featuredImage: contentData.featuredImage,
        featuredImageAlt: contentData.featuredImageAlt,
        template: contentData.template,
        password: contentData.password,
        publishedAt: contentData.status === POST_STATUSES.PUBLISHED ? new Date() : contentData.publishedAt,
        scheduledAt: contentData.scheduledAt,
        authorId: contentData.authorId,
        authorName: contentData.authorName,
        authorEmail: contentData.authorEmail,
        seoTitle: contentData.seoTitle,
        seoDescription: contentData.seoDescription,
        seoKeywords: contentData.seoKeywords || [],
        ogImage: contentData.ogImage,
        ogTitle: contentData.ogTitle,
        ogDescription: contentData.ogDescription,
        twitterCard: contentData.twitterCard,
        canonicalUrl: contentData.canonicalUrl,
        metaRobots: contentData.metaRobots,
        usePageBuilder: contentData.usePageBuilder || false,
        pageBuilderData: contentData.pageBuilderData,
        allowComments: contentData.allowComments ?? true,
        allowPingbacks: contentData.allowPingbacks ?? true,
        isSticky: contentData.isSticky || false,
        isFeatured: contentData.isFeatured || false,
        customFields: contentData.customFields,
        metadata: contentData.metadata
      },
      include: {
        blocks: true,
        comments: true,
        meta: true,
        revisions: true,
        taxonomyTerms: {
          include: {
            term: {
              include: {
                taxonomy: true
              }
            }
          }
        },
        customFieldValues: {
          include: {
            field: {
              include: {
                group: true
              }
            }
          }
        },
        parent: true,
        children: true,
        postTypeRef: true
      }
    })

    // Save custom field values if provided
    if (contentData.customFields) {
      await CustomFieldService.saveFieldValues(content.id, contentData.customFields)
    }

    return this.transformPostToContent(content)
  }

  /**
   * Get content by ID
   */
  static async getContent(id: string): Promise<CMSContent | null> {
    const content = await prisma.post.findUnique({
      where: { id },
      include: {
        blocks: true,
        comments: true,
        meta: true,
        revisions: true,
        taxonomyTerms: {
          include: {
            term: {
              include: {
                taxonomy: true
              }
            }
          }
        },
        customFieldValues: {
          include: {
            field: {
              include: {
                group: true
              }
            }
          }
        },
        parent: true,
        children: true,
        postTypeRef: true
      }
    })

    if (!content) return null

    return this.transformPostToContent(content)
  }

  /**
   * Get content by slug
   */
  static async getContentBySlug(slug: string): Promise<CMSContent | null> {
    const content = await prisma.post.findUnique({
      where: { slug },
      include: {
        blocks: true,
        comments: true,
        meta: true,
        revisions: true,
        taxonomyTerms: {
          include: {
            term: {
              include: {
                taxonomy: true
              }
            }
          }
        },
        customFieldValues: {
          include: {
            field: {
              include: {
                group: true
              }
            }
          }
        },
        parent: true,
        children: true,
        postTypeRef: true
      }
    })

    if (!content) return null

    return this.transformPostToContent(content)
  }

  /**
   * Get content list with filters
   */
  static async getContentList(options: {
    postType?: string
    status?: string
    authorId?: string
    parentId?: string
    search?: string
    limit?: number
    offset?: number
    orderBy?: 'title' | 'publishedAt' | 'updatedAt' | 'menuOrder'
    orderDirection?: 'asc' | 'desc'
  } = {}): Promise<{ content: CMSContent[], total: number }> {
    const {
      postType,
      status,
      authorId,
      parentId,
      search,
      limit = 20,
      offset = 0,
      orderBy = 'updatedAt',
      orderDirection = 'desc'
    } = options

    const where: any = {}

    if (postType) where.postType = postType
    if (status) where.status = status
    if (authorId) where.authorId = authorId
    if (parentId) where.parentId = parentId
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } },
        { excerpt: { contains: search, mode: 'insensitive' } }
      ]
    }

    const [content, total] = await Promise.all([
      prisma.post.findMany({
        where,
        include: {
          blocks: true,
          comments: true,
          meta: true,
          revisions: true,
          taxonomyTerms: {
            include: {
              term: {
                include: {
                  taxonomy: true
                }
              }
            }
          },
          customFieldValues: {
            include: {
              field: {
                include: {
                  group: true
                }
              }
            }
          },
          parent: true,
          children: true,
          postTypeRef: true
        },
        orderBy: { [orderBy]: orderDirection },
        take: limit,
        skip: offset
      }),
      prisma.post.count({ where })
    ])

    return {
      content: content.map(this.transformPostToContent),
      total
    }
  }

  /**
   * Update content
   */
  static async updateContent(id: string, updates: Partial<CMSContent>): Promise<CMSContent> {
    const content = await prisma.post.update({
      where: { id },
      data: {
        ...updates,
        updatedAt: new Date()
      },
      include: {
        blocks: true,
        comments: true,
        meta: true,
        revisions: true,
        taxonomyTerms: {
          include: {
            term: {
              include: {
                taxonomy: true
              }
            }
          }
        },
        customFieldValues: {
          include: {
            field: {
              include: {
                group: true
              }
            }
          }
        },
        parent: true,
        children: true,
        postTypeRef: true
      }
    })

    // Update custom field values if provided
    if (updates.customFields) {
      await CustomFieldService.saveFieldValues(id, updates.customFields)
    }

    return this.transformPostToContent(content)
  }

  /**
   * Delete content
   */
  static async deleteContent(id: string): Promise<void> {
    // Delete custom field values first
    await CustomFieldService.deleteFieldValues(id)

    // Delete the content
    await prisma.post.delete({
      where: { id }
    })
  }

  /**
   * Publish content
   */
  static async publishContent(id: string): Promise<CMSContent> {
    return this.updateContent(id, {
      status: POST_STATUSES.PUBLISHED,
      publishedAt: new Date()
    })
  }

  /**
   * Unpublish content
   */
  static async unpublishContent(id: string): Promise<CMSContent> {
    return this.updateContent(id, {
      status: POST_STATUSES.DRAFT,
      publishedAt: undefined
    })
  }

  /**
   * Generate slug from title
   */
  private static generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  /**
   * Transform Prisma Post to CMSContent
   */
  private static transformPostToContent(post: PostWithRelations): CMSContent {
    // Extract custom field values
    const customFields: Record<string, any> = {}
    for (const value of post.customFieldValues) {
      const fieldName = value.field.name
      let parsedValue = value.value

      if (parsedValue) {
        try {
          parsedValue = JSON.parse(parsedValue)
        } catch {
          // Keep as string if not valid JSON
        }
      }

      customFields[fieldName] = parsedValue
    }

    // Extract taxonomy terms
    const taxonomyTerms = post.taxonomyTerms.map(tt => tt.term)

    return {
      id: post.id,
      title: post.title,
      slug: post.slug,
      content: post.content || undefined,
      contentHtml: post.contentHtml || undefined,
      excerpt: post.excerpt || undefined,
      status: post.status as any,
      postType: post.postType,
      parentId: post.parentId || undefined,
      menuOrder: post.menuOrder,
      featuredImage: post.featuredImage || undefined,
      featuredImageAlt: post.featuredImageAlt || undefined,
      template: post.template || undefined,
      password: post.password || undefined,
      publishedAt: post.publishedAt || undefined,
      scheduledAt: post.scheduledAt || undefined,
      authorId: post.authorId || undefined,
      authorName: post.authorName || undefined,
      authorEmail: post.authorEmail || undefined,
      seoTitle: post.seoTitle || undefined,
      seoDescription: post.seoDescription || undefined,
      seoKeywords: post.seoKeywords,
      ogImage: post.ogImage || undefined,
      ogTitle: post.ogTitle || undefined,
      ogDescription: post.ogDescription || undefined,
      twitterCard: post.twitterCard || undefined,
      canonicalUrl: post.canonicalUrl || undefined,
      metaRobots: post.metaRobots || undefined,
      viewCount: post.viewCount,
      shareCount: post.shareCount,
      likeCount: post.likeCount,
      commentCount: post.commentCount,
      usePageBuilder: post.usePageBuilder,
      pageBuilderData: post.pageBuilderData,
      allowComments: post.allowComments,
      allowPingbacks: post.allowPingbacks,
      isSticky: post.isSticky,
      isFeatured: post.isFeatured,
      customFields,
      metadata: post.metadata as Record<string, any> || undefined,
      taxonomyTerms,
      customFieldValues: post.customFieldValues
    }
  }
}
