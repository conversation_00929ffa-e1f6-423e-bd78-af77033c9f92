import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

interface TaxonomyData {
  name: string
  label: string
  labelPlural: string
  description?: string
  isHierarchical: boolean
  isPublic: boolean
  showInMenu: boolean
  showInRest: boolean
  postTypes: string[]
  capabilities?: Record<string, any>
  metaBoxCallback?: string
  isSystem: boolean
  isActive: boolean
}

export class TaxonomyService {
  /**
   * Register a new taxonomy
   */
  static async registerTaxonomy(taxonomyData: Partial<TaxonomyData>): Promise<any> {
    if (!taxonomyData.name || !taxonomyData.label) {
      throw new Error('Taxonomy name and label are required')
    }

    // Check if taxonomy already exists
    const existing = await prisma.taxonomy.findUnique({
      where: { name: taxonomyData.name }
    })

    if (existing) {
      throw new Error(`Taxonomy '${taxonomyData.name}' already exists`)
    }

    // Create the taxonomy
    const taxonomy = await prisma.taxonomy.create({
      data: {
        name: taxonomyData.name,
        label: taxonomyData.label,
        labelPlural: taxonomyData.labelPlural || `${taxonomyData.label}s`,
        description: taxonomyData.description,
        isHierarchical: taxonomyData.isHierarchical ?? false,
        isPublic: taxonomyData.isPublic ?? true,
        showInMenu: taxonomyData.showInMenu ?? true,
        showInRest: taxonomyData.showInRest ?? true,
        postTypes: taxonomyData.postTypes || [],
        capabilities: taxonomyData.capabilities || {},
        metaBoxCallback: taxonomyData.metaBoxCallback,
        isSystem: taxonomyData.isSystem ?? false,
        isActive: taxonomyData.isActive ?? true
      }
    })

    return taxonomy
  }

  /**
   * Get all taxonomies
   */
  static async getTaxonomies(includeInactive = false): Promise<any[]> {
    const where = includeInactive ? {} : { isActive: true }
    
    const taxonomies = await prisma.taxonomy.findMany({
      where,
      include: {
        terms: {
          where: includeInactive ? {} : {},
          orderBy: { name: 'asc' }
        }
      },
      orderBy: { label: 'asc' }
    })

    return taxonomies
  }

  /**
   * Get a taxonomy by name
   */
  static async getTaxonomy(name: string): Promise<any | null> {
    const taxonomy = await prisma.taxonomy.findUnique({
      where: { name },
      include: {
        terms: {
          orderBy: { name: 'asc' }
        }
      }
    })

    return taxonomy
  }

  /**
   * Update a taxonomy
   */
  static async updateTaxonomy(name: string, updates: Partial<TaxonomyData>): Promise<any> {
    const taxonomy = await prisma.taxonomy.update({
      where: { name },
      data: {
        ...updates,
        updatedAt: new Date()
      }
    })

    return taxonomy
  }

  /**
   * Delete a taxonomy
   */
  static async deleteTaxonomy(name: string): Promise<void> {
    // Check if it's a system taxonomy
    const taxonomy = await this.getTaxonomy(name)
    if (taxonomy?.isSystem) {
      throw new Error(`Cannot delete system taxonomy '${name}'`)
    }

    // Check if there are terms using this taxonomy
    const termCount = await prisma.taxonomyTerm.count({
      where: { taxonomyId: taxonomy.id }
    })

    if (termCount > 0) {
      throw new Error(`Cannot delete taxonomy '${name}' because it has ${termCount} terms`)
    }

    await prisma.taxonomy.delete({
      where: { name }
    })
  }

  /**
   * Create a taxonomy term
   */
  static async createTerm(taxonomyName: string, termData: {
    name: string
    slug?: string
    description?: string
    parentId?: string
    color?: string
    image?: string
    metadata?: Record<string, any>
  }): Promise<any> {
    const taxonomy = await this.getTaxonomy(taxonomyName)
    if (!taxonomy) {
      throw new Error(`Taxonomy '${taxonomyName}' not found`)
    }

    // Generate slug if not provided
    const slug = termData.slug || termData.name.toLowerCase().replace(/[^a-z0-9]/g, '-')

    // Check slug uniqueness within taxonomy
    const existingTerm = await prisma.taxonomyTerm.findFirst({
      where: {
        taxonomyId: taxonomy.id,
        slug
      }
    })

    if (existingTerm) {
      throw new Error(`Term with slug '${slug}' already exists in taxonomy '${taxonomyName}'`)
    }

    const term = await prisma.taxonomyTerm.create({
      data: {
        name: termData.name,
        slug,
        description: termData.description,
        taxonomyId: taxonomy.id,
        parentId: termData.parentId,
        color: termData.color,
        image: termData.image,
        metadata: termData.metadata || {}
      }
    })

    return term
  }

  /**
   * Get terms for a taxonomy
   */
  static async getTerms(taxonomyName: string, parentId?: string): Promise<any[]> {
    const taxonomy = await this.getTaxonomy(taxonomyName)
    if (!taxonomy) {
      throw new Error(`Taxonomy '${taxonomyName}' not found`)
    }

    const where: any = { taxonomyId: taxonomy.id }
    if (parentId !== undefined) {
      where.parentId = parentId
    }

    const terms = await prisma.taxonomyTerm.findMany({
      where,
      include: {
        parent: true,
        children: true,
        posts: {
          include: {
            post: true
          }
        }
      },
      orderBy: { name: 'asc' }
    })

    return terms
  }

  /**
   * Get a term by slug
   */
  static async getTerm(taxonomyName: string, slug: string): Promise<any | null> {
    const taxonomy = await this.getTaxonomy(taxonomyName)
    if (!taxonomy) {
      return null
    }

    const term = await prisma.taxonomyTerm.findFirst({
      where: {
        taxonomyId: taxonomy.id,
        slug
      },
      include: {
        parent: true,
        children: true,
        posts: {
          include: {
            post: true
          }
        }
      }
    })

    return term
  }

  /**
   * Update a term
   */
  static async updateTerm(taxonomyName: string, slug: string, updates: any): Promise<any> {
    const taxonomy = await this.getTaxonomy(taxonomyName)
    if (!taxonomy) {
      throw new Error(`Taxonomy '${taxonomyName}' not found`)
    }

    const term = await prisma.taxonomyTerm.findFirst({
      where: {
        taxonomyId: taxonomy.id,
        slug
      }
    })

    if (!term) {
      throw new Error(`Term '${slug}' not found in taxonomy '${taxonomyName}'`)
    }

    const updatedTerm = await prisma.taxonomyTerm.update({
      where: { id: term.id },
      data: {
        ...updates,
        updatedAt: new Date()
      }
    })

    return updatedTerm
  }

  /**
   * Delete a term
   */
  static async deleteTerm(taxonomyName: string, slug: string): Promise<void> {
    const taxonomy = await this.getTaxonomy(taxonomyName)
    if (!taxonomy) {
      throw new Error(`Taxonomy '${taxonomyName}' not found`)
    }

    const term = await prisma.taxonomyTerm.findFirst({
      where: {
        taxonomyId: taxonomy.id,
        slug
      }
    })

    if (!term) {
      throw new Error(`Term '${slug}' not found in taxonomy '${taxonomyName}'`)
    }

    // Check if term has children (for hierarchical taxonomies)
    if (taxonomy.isHierarchical) {
      const childCount = await prisma.taxonomyTerm.count({
        where: { parentId: term.id }
      })

      if (childCount > 0) {
        throw new Error(`Cannot delete term '${slug}' because it has ${childCount} child terms`)
      }
    }

    await prisma.taxonomyTerm.delete({
      where: { id: term.id }
    })
  }

  /**
   * Assign terms to content
   */
  static async assignTermsToContent(contentId: string, termIds: string[]): Promise<void> {
    // Remove existing term assignments
    await prisma.postTaxonomyTerm.deleteMany({
      where: { postId: contentId }
    })

    // Add new term assignments
    if (termIds.length > 0) {
      await prisma.postTaxonomyTerm.createMany({
        data: termIds.map(termId => ({
          postId: contentId,
          termId
        }))
      })
    }
  }

  /**
   * Get content by taxonomy term
   */
  static async getContentByTerm(taxonomyName: string, termSlug: string, options: {
    limit?: number
    offset?: number
    status?: string
  } = {}): Promise<{ content: any[], total: number }> {
    const { limit = 20, offset = 0, status = 'published' } = options

    const term = await this.getTerm(taxonomyName, termSlug)
    if (!term) {
      return { content: [], total: 0 }
    }

    const where: any = {
      taxonomyTerms: {
        some: {
          termId: term.id
        }
      }
    }

    if (status) {
      where.status = status
    }

    const [content, total] = await Promise.all([
      prisma.post.findMany({
        where,
        include: {
          taxonomyTerms: {
            include: {
              term: {
                include: {
                  taxonomy: true
                }
              }
            }
          }
        },
        orderBy: { publishedAt: 'desc' },
        take: limit,
        skip: offset
      }),
      prisma.post.count({ where })
    ])

    return { content, total }
  }

  /**
   * Get taxonomies for a specific post type
   */
  static async getTaxonomiesForPostType(postType: string): Promise<Taxonomy[]> {
    const taxonomies = await prisma.taxonomy.findMany({
      where: {
        postTypes: {
          has: postType
        },
        isActive: true
      },
      include: {
        terms: {
          where: { isActive: true },
          orderBy: { name: 'asc' }
        }
      },
      orderBy: { name: 'asc' }
    })

    return taxonomies as Taxonomy[]
  }
}
