import { PrismaClient } from '@prisma/client'
import { CustomField, CustomFieldGroup, CustomFieldValue, LocationRule } from '../types'

const prisma = new PrismaClient()

export class CustomFieldService {
  /**
   * Create a custom field group
   */
  static async createFieldGroup(groupData: Partial<CustomFieldGroup>): Promise<CustomFieldGroup> {
    if (!groupData.title || !groupData.key) {
      throw new Error('Field group title and key are required')
    }

    // Check if key already exists
    const existing = await prisma.customFieldGroup.findUnique({
      where: { key: groupData.key }
    })

    if (existing) {
      throw new Error(`Field group with key '${groupData.key}' already exists`)
    }

    const group = await prisma.customFieldGroup.create({
      data: {
        title: groupData.title,
        key: groupData.key,
        description: groupData.description,
        position: groupData.position || 'normal',
        priority: groupData.priority || 'default',
        postTypes: groupData.postTypes || [],
        location: groupData.location || [],
        hideOnScreen: groupData.hideOnScreen || [],
        isActive: groupData.isActive ?? true,
        menuOrder: groupData.menuOrder || 0,
        instruction: groupData.instruction,
        registeredBy: groupData.registeredBy
      },
      include: {
        fields: true
      }
    })

    return group as CustomFieldGroup
  }

  /**
   * Get all field groups
   */
  static async getFieldGroups(includeInactive = false): Promise<CustomFieldGroup[]> {
    const where = includeInactive ? {} : { isActive: true }
    
    const groups = await prisma.customFieldGroup.findMany({
      where,
      include: {
        fields: {
          where: includeInactive ? {} : { isActive: true },
          orderBy: { menuOrder: 'asc' }
        }
      },
      orderBy: { menuOrder: 'asc' }
    })

    return groups as CustomFieldGroup[]
  }

  /**
   * Get field groups for a specific post type
   */
  static async getFieldGroupsForPostType(postType: string): Promise<CustomFieldGroup[]> {
    const groups = await prisma.customFieldGroup.findMany({
      where: {
        isActive: true,
        postTypes: {
          has: postType
        }
      },
      include: {
        fields: {
          where: { isActive: true },
          orderBy: { menuOrder: 'asc' }
        }
      },
      orderBy: { menuOrder: 'asc' }
    })

    return groups as CustomFieldGroup[]
  }

  /**
   * Get a field group by key
   */
  static async getFieldGroup(key: string): Promise<CustomFieldGroup | null> {
    const group = await prisma.customFieldGroup.findUnique({
      where: { key },
      include: {
        fields: {
          orderBy: { menuOrder: 'asc' }
        }
      }
    })

    return group as CustomFieldGroup | null
  }

  /**
   * Update a field group
   */
  static async updateFieldGroup(key: string, updates: Partial<CustomFieldGroup>): Promise<CustomFieldGroup> {
    const group = await prisma.customFieldGroup.update({
      where: { key },
      data: {
        ...updates,
        updatedAt: new Date()
      },
      include: {
        fields: {
          orderBy: { menuOrder: 'asc' }
        }
      }
    })

    return group as CustomFieldGroup
  }

  /**
   * Delete a field group
   */
  static async deleteFieldGroup(key: string): Promise<void> {
    await prisma.customFieldGroup.delete({
      where: { key }
    })
  }

  /**
   * Add a field to a group
   */
  static async addField(groupKey: string, fieldData: Partial<CustomField>): Promise<CustomField> {
    if (!fieldData.label || !fieldData.name || !fieldData.type) {
      throw new Error('Field label, name, and type are required')
    }

    const group = await this.getFieldGroup(groupKey)
    if (!group) {
      throw new Error(`Field group '${groupKey}' not found`)
    }

    // Check if field name already exists in the group
    const existing = await prisma.customField.findFirst({
      where: {
        groupId: group.id,
        name: fieldData.name
      }
    })

    if (existing) {
      throw new Error(`Field with name '${fieldData.name}' already exists in group '${groupKey}'`)
    }

    const field = await prisma.customField.create({
      data: {
        groupId: group.id,
        label: fieldData.label,
        name: fieldData.name,
        type: fieldData.type,
        instructions: fieldData.instructions,
        required: fieldData.required || false,
        conditionalLogic: fieldData.conditionalLogic,
        wrapper: fieldData.wrapper,
        defaultValue: fieldData.defaultValue,
        placeholder: fieldData.placeholder,
        prepend: fieldData.prepend,
        append: fieldData.append,
        formatting: fieldData.formatting,
        maxLength: fieldData.maxLength,
        readonly: fieldData.readonly || false,
        disabled: fieldData.disabled || false,
        choices: fieldData.choices,
        allowOther: fieldData.allowOther || false,
        saveOther: fieldData.saveOther,
        defaultOther: fieldData.defaultOther,
        layout: fieldData.layout,
        toggle: fieldData.toggle || false,
        allowNull: fieldData.allowNull || false,
        multiple: fieldData.multiple || false,
        ui: fieldData.ui ?? true,
        ajax: fieldData.ajax || false,
        returnFormat: fieldData.returnFormat,
        libraryType: fieldData.libraryType,
        minSize: fieldData.minSize,
        maxSize: fieldData.maxSize,
        mimeTypes: fieldData.mimeTypes,
        minWidth: fieldData.minWidth,
        maxWidth: fieldData.maxWidth,
        minHeight: fieldData.minHeight,
        maxHeight: fieldData.maxHeight,
        previewSize: fieldData.previewSize,
        insertType: fieldData.insertType,
        buttonLabel: fieldData.buttonLabel,
        minRows: fieldData.minRows,
        maxRows: fieldData.maxRows,
        subFields: fieldData.subFields,
        layouts: fieldData.layouts,
        min: fieldData.min,
        max: fieldData.max,
        collapsed: fieldData.collapsed,
        menuOrder: fieldData.menuOrder || 0,
        isActive: fieldData.isActive ?? true,
        registeredBy: fieldData.registeredBy
      }
    })

    return field as CustomField
  }

  /**
   * Update a field
   */
  static async updateField(fieldId: string, updates: Partial<CustomField>): Promise<CustomField> {
    const field = await prisma.customField.update({
      where: { id: fieldId },
      data: {
        ...updates,
        updatedAt: new Date()
      }
    })

    return field as CustomField
  }

  /**
   * Delete a field
   */
  static async deleteField(fieldId: string): Promise<void> {
    await prisma.customField.delete({
      where: { id: fieldId }
    })
  }

  /**
   * Get field values for a post
   */
  static async getFieldValues(postId: string): Promise<Record<string, any>> {
    const values = await prisma.customFieldValue.findMany({
      where: { postId },
      include: {
        field: {
          include: {
            group: true
          }
        }
      }
    })

    const result: Record<string, any> = {}
    
    for (const value of values) {
      const fieldName = value.field.name
      let parsedValue = value.value

      // Parse JSON values
      if (parsedValue) {
        try {
          parsedValue = JSON.parse(parsedValue)
        } catch {
          // Keep as string if not valid JSON
        }
      }

      result[fieldName] = parsedValue
    }

    return result
  }

  /**
   * Save field values for a post
   */
  static async saveFieldValues(postId: string, values: Record<string, any>): Promise<void> {
    // Get all fields for this post's post type
    const post = await prisma.post.findUnique({
      where: { id: postId },
      select: { postType: true }
    })

    if (!post) {
      throw new Error(`Post with id '${postId}' not found`)
    }

    const fieldGroups = await this.getFieldGroupsForPostType(post.postType)
    const fieldMap = new Map<string, CustomField>()

    // Build field map
    for (const group of fieldGroups) {
      for (const field of group.fields) {
        fieldMap.set(field.name, field)
      }
    }

    // Save each field value
    for (const [fieldName, value] of Object.entries(values)) {
      const field = fieldMap.get(fieldName)
      if (!field) continue

      const stringValue = typeof value === 'string' ? value : JSON.stringify(value)

      await prisma.customFieldValue.upsert({
        where: {
          postId_fieldId: {
            postId,
            fieldId: field.id
          }
        },
        update: {
          value: stringValue,
          updatedAt: new Date()
        },
        create: {
          postId,
          fieldId: field.id,
          value: stringValue
        }
      })
    }
  }

  /**
   * Delete field values for a post
   */
  static async deleteFieldValues(postId: string): Promise<void> {
    await prisma.customFieldValue.deleteMany({
      where: { postId }
    })
  }

  /**
   * Check if location rules match
   */
  static checkLocationRules(rules: LocationRule[], context: Record<string, any>): boolean {
    if (!rules || rules.length === 0) return true

    return rules.some(rule => {
      const contextValue = context[rule.param]
      
      switch (rule.operator) {
        case '==':
          return contextValue === rule.value
        case '!=':
          return contextValue !== rule.value
        case 'contains':
          return String(contextValue).includes(rule.value)
        case 'not_contains':
          return !String(contextValue).includes(rule.value)
        default:
          return false
      }
    })
  }

  /**
   * Get fields that should be shown for a specific context
   */
  static async getFieldsForContext(context: Record<string, any>): Promise<CustomFieldGroup[]> {
    const allGroups = await this.getFieldGroups()
    
    return allGroups.filter(group => 
      this.checkLocationRules(group.location as LocationRule[], context)
    )
  }
}
