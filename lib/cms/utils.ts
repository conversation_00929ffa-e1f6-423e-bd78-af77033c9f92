import { PostT<PERSON>, CustomField, CMSContent } from './types'

/**
 * Generate a URL-friendly slug from a string
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
}

/**
 * Sanitize content for safe display
 */
export function sanitizeContent(content: string): string {
  // Basic HTML sanitization - in production, use a proper library like DOMPurify
  return content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
}

/**
 * Validate post type data
 */
export function validatePostType(postType: Partial<PostType>): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  if (!postType.name) {
    errors.push('Post type name is required')
  } else if (!/^[a-z_][a-z0-9_]*$/i.test(postType.name)) {
    errors.push('Post type name must contain only letters, numbers, and underscores')
  }

  if (!postType.label) {
    errors.push('Post type label is required')
  }

  if (postType.name && postType.name.length > 20) {
    errors.push('Post type name must be 20 characters or less')
  }

  if (postType.menuPosition && (postType.menuPosition < 0 || postType.menuPosition > 100)) {
    errors.push('Menu position must be between 0 and 100')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validate custom field data
 */
export function validateCustomField(field: Partial<CustomField>): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  if (!field.label) {
    errors.push('Field label is required')
  }

  if (!field.name) {
    errors.push('Field name is required')
  } else if (!/^[a-z_][a-z0-9_]*$/i.test(field.name)) {
    errors.push('Field name must contain only letters, numbers, and underscores')
  }

  if (!field.type) {
    errors.push('Field type is required')
  }

  if (field.name && field.name.length > 64) {
    errors.push('Field name must be 64 characters or less')
  }

  if (field.maxLength && field.maxLength < 0) {
    errors.push('Max length must be a positive number')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Format field value for display
 */
export function formatFieldValue(value: any, fieldType: string): string {
  if (value === null || value === undefined) {
    return ''
  }

  switch (fieldType) {
    case 'true_false':
      return value ? 'Yes' : 'No'
    
    case 'date_picker':
    case 'date_time_picker':
      if (value instanceof Date) {
        return value.toLocaleDateString()
      }
      if (typeof value === 'string') {
        const date = new Date(value)
        return isNaN(date.getTime()) ? value : date.toLocaleDateString()
      }
      return String(value)
    
    case 'number':
    case 'range':
      return typeof value === 'number' ? value.toLocaleString() : String(value)
    
    case 'email':
      return `<a href="mailto:${value}">${value}</a>`
    
    case 'url':
      return `<a href="${value}" target="_blank" rel="noopener noreferrer">${value}</a>`
    
    case 'image':
      if (typeof value === 'object' && value.url) {
        return `<img src="${value.url}" alt="${value.alt || ''}" />`
      }
      return String(value)
    
    case 'gallery':
      if (Array.isArray(value)) {
        return value.map(img => 
          typeof img === 'object' && img.url 
            ? `<img src="${img.url}" alt="${img.alt || ''}" />`
            : String(img)
        ).join('')
      }
      return String(value)
    
    case 'link':
      if (typeof value === 'object' && value.url) {
        return `<a href="${value.url}" target="${value.target || '_self'}">${value.title || value.url}</a>`
      }
      return String(value)
    
    case 'select':
    case 'checkbox':
    case 'radio':
      if (Array.isArray(value)) {
        return value.join(', ')
      }
      return String(value)
    
    case 'repeater':
    case 'flexible_content':
      if (Array.isArray(value)) {
        return `${value.length} items`
      }
      return String(value)
    
    default:
      if (typeof value === 'object') {
        return JSON.stringify(value, null, 2)
      }
      return String(value)
  }
}

/**
 * Parse field value from string
 */
export function parseFieldValue(value: string, fieldType: string): any {
  if (!value) {
    return null
  }

  switch (fieldType) {
    case 'true_false':
      return value === 'true' || value === '1' || value === 'yes'
    
    case 'number':
    case 'range':
      const num = parseFloat(value)
      return isNaN(num) ? null : num
    
    case 'date_picker':
    case 'date_time_picker':
      const date = new Date(value)
      return isNaN(date.getTime()) ? null : date
    
    case 'checkbox':
    case 'select':
      try {
        const parsed = JSON.parse(value)
        return Array.isArray(parsed) ? parsed : [value]
      } catch {
        return [value]
      }
    
    case 'image':
    case 'gallery':
    case 'link':
    case 'repeater':
    case 'flexible_content':
    case 'group':
      try {
        return JSON.parse(value)
      } catch {
        return value
      }
    
    default:
      return value
  }
}

/**
 * Extract excerpt from content
 */
export function extractExcerpt(content: string, length: number = 150): string {
  if (!content) return ''
  
  // Remove HTML tags
  const textContent = content.replace(/<[^>]*>/g, '')
  
  // Trim and truncate
  const trimmed = textContent.trim()
  if (trimmed.length <= length) {
    return trimmed
  }
  
  // Find the last space before the length limit
  const truncated = trimmed.substring(0, length)
  const lastSpace = truncated.lastIndexOf(' ')
  
  if (lastSpace > 0) {
    return truncated.substring(0, lastSpace) + '...'
  }
  
  return truncated + '...'
}

/**
 * Calculate reading time for content
 */
export function calculateReadingTime(content: string): number {
  if (!content) return 0
  
  // Remove HTML tags and count words
  const textContent = content.replace(/<[^>]*>/g, '')
  const words = textContent.trim().split(/\s+/).length
  
  // Average reading speed is 200-250 words per minute
  const wordsPerMinute = 225
  const minutes = Math.ceil(words / wordsPerMinute)
  
  return Math.max(1, minutes)
}

/**
 * Generate SEO-friendly meta description
 */
export function generateMetaDescription(content: CMSContent): string {
  if (content.seoDescription) {
    return content.seoDescription
  }
  
  if (content.excerpt) {
    return content.excerpt.length > 160 
      ? content.excerpt.substring(0, 157) + '...'
      : content.excerpt
  }
  
  if (content.content) {
    return extractExcerpt(content.content, 160)
  }
  
  return ''
}

/**
 * Generate breadcrumb trail
 */
export function generateBreadcrumbs(content: CMSContent, postType: PostType): Array<{
  title: string
  url: string
}> {
  const breadcrumbs = [
    { title: 'Home', url: '/' }
  ]
  
  // Add post type archive if it has one
  if (postType.hasArchive && postType.name !== 'page') {
    breadcrumbs.push({
      title: postType.labelPlural,
      url: `/${postType.rewriteSlug || postType.name}`
    })
  }
  
  // Add parent pages for hierarchical content
  if (postType.isHierarchical && content.parentId) {
    // In a real implementation, you would fetch parent content
    // breadcrumbs.push(...getParentBreadcrumbs(content.parentId))
  }
  
  // Add current content
  breadcrumbs.push({
    title: content.title,
    url: `/${postType.rewriteSlug || postType.name}/${content.slug}`
  })
  
  return breadcrumbs
}

/**
 * Check if user can perform action on content
 */
export function userCan(action: string, content: CMSContent, userId?: string): boolean {
  // Basic permission check - in a real implementation, this would check user roles and capabilities
  if (!userId) {
    return false
  }
  
  // Content author can edit their own content
  if (action === 'edit' && content.authorId === userId) {
    return true
  }
  
  // For now, allow all actions for authenticated users
  // In production, implement proper role-based access control
  return true
}

/**
 * Get content URL
 */
export function getContentUrl(content: CMSContent, postType: PostType): string {
  const baseSlug = postType.rewriteSlug || postType.name
  
  if (postType.name === 'page') {
    return `/${content.slug}`
  }
  
  if (postType.rewriteWithFront) {
    return `/${baseSlug}/${content.slug}`
  }
  
  return `/${content.slug}`
}

/**
 * Get archive URL for post type
 */
export function getArchiveUrl(postType: PostType): string {
  if (!postType.hasArchive) {
    return ''
  }
  
  return `/${postType.rewriteSlug || postType.name}`
}

/**
 * Validate slug uniqueness
 */
export function isValidSlug(slug: string): boolean {
  // Check if slug is valid format
  if (!/^[a-z0-9-]+$/.test(slug)) {
    return false
  }
  
  // Check length
  if (slug.length < 1 || slug.length > 200) {
    return false
  }
  
  // Check for reserved words
  const reserved = [
    'admin', 'api', 'app', 'assets', 'blog', 'cms', 'dashboard',
    'edit', 'login', 'logout', 'preview', 'search', 'sitemap',
    'wp-admin', 'wp-content', 'wp-includes'
  ]
  
  if (reserved.includes(slug)) {
    return false
  }
  
  return true
}

/**
 * Deep merge objects
 */
export function deepMerge(target: any, source: any): any {
  const result = { ...target }
  
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = deepMerge(result[key] || {}, source[key])
    } else {
      result[key] = source[key]
    }
  }
  
  return result
}
