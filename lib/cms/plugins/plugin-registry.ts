import { PrismaClient } from '@prisma/client'
import { PostTypeService } from '../services/post-type-service'
import { CustomFieldService } from '../services/custom-field-service'
import { TaxonomyService } from '../services/taxonomy-service'
import { ComponentRegistry } from '../rendering/component-registry'

const prisma = new PrismaClient()

export class PluginRegistry {
  /**
   * Register a component from a plugin
   */
  static async registerComponent(
    type: string,
    name: string,
    configuration: any,
    pluginSlug: string
  ): Promise<void> {
    try {
      switch (type) {
        case 'post_type':
          await this.registerPostType(name, configuration, pluginSlug)
          break
        
        case 'taxonomy':
          await this.registerTaxonomy(name, configuration, pluginSlug)
          break
        
        case 'custom_field_group':
          await this.registerCustomFieldGroup(name, configuration, pluginSlug)
          break
        
        case 'block_type':
          await this.registerBlockType(name, configuration, pluginSlug)
          break
        
        case 'template':
          await this.registerTemplate(name, configuration, pluginSlug)
          break
        
        default:
          console.warn(`Unknown component type: ${type}`)
      }

      // Record the registration in the database
      await prisma.pluginRegistration.create({
        data: {
          pluginId: await this.getPluginId(pluginSlug),
          type,
          name,
          configuration,
          isActive: true
        }
      })

      console.log(`Registered ${type} '${name}' from plugin '${pluginSlug}'`)
    } catch (error) {
      console.error(`Failed to register ${type} '${name}' from plugin '${pluginSlug}':`, error)
      throw error
    }
  }

  /**
   * Unregister a component from a plugin
   */
  static async unregisterComponent(
    type: string,
    name: string,
    pluginSlug: string
  ): Promise<void> {
    try {
      switch (type) {
        case 'post_type':
          await PostTypeService.deletePostType(name)
          break
        
        case 'taxonomy':
          await TaxonomyService.deleteTaxonomy(name)
          break
        
        case 'custom_field_group':
          await CustomFieldService.deleteFieldGroup(name)
          break
        
        case 'block_type':
          ComponentRegistry.unregisterBlockComponent(name)
          break
        
        case 'template':
          ComponentRegistry.unregisterTemplate(name)
          break
      }

      // Remove the registration from the database
      const pluginId = await this.getPluginId(pluginSlug)
      await prisma.pluginRegistration.deleteMany({
        where: {
          pluginId,
          type,
          name
        }
      })

      console.log(`Unregistered ${type} '${name}' from plugin '${pluginSlug}'`)
    } catch (error) {
      console.error(`Failed to unregister ${type} '${name}' from plugin '${pluginSlug}':`, error)
      throw error
    }
  }

  /**
   * Register a post type from a plugin
   */
  private static async registerPostType(
    name: string,
    configuration: any,
    pluginSlug: string
  ): Promise<void> {
    await PostTypeService.registerPostType({
      ...configuration,
      name,
      registeredBy: pluginSlug,
      isSystem: false
    })
  }

  /**
   * Register a taxonomy from a plugin
   */
  private static async registerTaxonomy(
    name: string,
    configuration: any,
    pluginSlug: string
  ): Promise<void> {
    await TaxonomyService.registerTaxonomy({
      ...configuration,
      name,
      isSystem: false
    })
  }

  /**
   * Register a custom field group from a plugin
   */
  private static async registerCustomFieldGroup(
    key: string,
    configuration: any,
    pluginSlug: string
  ): Promise<void> {
    await CustomFieldService.createFieldGroup({
      ...configuration,
      key,
      registeredBy: pluginSlug
    })
  }

  /**
   * Register a block type from a plugin
   */
  private static async registerBlockType(
    name: string,
    configuration: any,
    pluginSlug: string
  ): Promise<void> {
    // Create block type in database
    await prisma.blockType.create({
      data: {
        name,
        displayName: configuration.displayName || name,
        description: configuration.description,
        category: configuration.category || 'custom',
        icon: configuration.icon,
        thumbnail: configuration.thumbnail,
        defaultConfig: configuration.defaultConfig || {},
        configSchema: configuration.configSchema || {},
        isActive: true,
        isSystem: false,
        version: configuration.version || '1.0.0',
        dependencies: configuration.dependencies || [],
        tags: configuration.tags || []
      }
    })

    // Register component if provided
    if (configuration.component) {
      ComponentRegistry.registerBlockComponent(name, configuration.component)
    }
  }

  /**
   * Register a template from a plugin
   */
  private static async registerTemplate(
    name: string,
    configuration: any,
    pluginSlug: string
  ): Promise<void> {
    // Create template in database
    await prisma.pageTemplate.create({
      data: {
        name,
        description: configuration.description,
        category: configuration.category || 'custom',
        thumbnail: configuration.thumbnail,
        configuration: configuration.templateConfig || {},
        isPublic: configuration.isPublic ?? true,
        isSystem: false,
        tags: configuration.tags || [],
        createdBy: pluginSlug
      }
    })

    // Register component if provided
    if (configuration.component) {
      ComponentRegistry.registerTemplate(name, configuration.component, {
        postTypes: configuration.postTypes,
        priority: configuration.priority
      })
    }
  }

  /**
   * Get plugin ID by slug
   */
  private static async getPluginId(pluginSlug: string): Promise<string> {
    const plugin = await prisma.plugin.findUnique({
      where: { slug: pluginSlug },
      select: { id: true }
    })

    if (!plugin) {
      throw new Error(`Plugin '${pluginSlug}' not found`)
    }

    return plugin.id
  }

  /**
   * Get all registrations for a plugin
   */
  static async getPluginRegistrations(pluginSlug: string): Promise<any[]> {
    const pluginId = await this.getPluginId(pluginSlug)
    
    const registrations = await prisma.pluginRegistration.findMany({
      where: { pluginId },
      orderBy: { createdAt: 'asc' }
    })

    return registrations
  }

  /**
   * Activate all registrations for a plugin
   */
  static async activatePluginRegistrations(pluginSlug: string): Promise<void> {
    const registrations = await this.getPluginRegistrations(pluginSlug)
    
    for (const registration of registrations) {
      if (!registration.isActive) {
        await this.registerComponent(
          registration.type,
          registration.name,
          registration.configuration,
          pluginSlug
        )
      }
    }
  }

  /**
   * Deactivate all registrations for a plugin
   */
  static async deactivatePluginRegistrations(pluginSlug: string): Promise<void> {
    const registrations = await this.getPluginRegistrations(pluginSlug)
    
    for (const registration of registrations) {
      if (registration.isActive) {
        await this.unregisterComponent(
          registration.type,
          registration.name,
          pluginSlug
        )
      }
    }
  }

  /**
   * Get registration statistics
   */
  static async getRegistrationStats(): Promise<{
    totalRegistrations: number
    byType: Record<string, number>
    byPlugin: Record<string, number>
  }> {
    const registrations = await prisma.pluginRegistration.findMany({
      include: {
        plugin: {
          select: { slug: true }
        }
      }
    })

    const byType: Record<string, number> = {}
    const byPlugin: Record<string, number> = {}

    for (const registration of registrations) {
      // Count by type
      byType[registration.type] = (byType[registration.type] || 0) + 1
      
      // Count by plugin
      const pluginSlug = registration.plugin.slug
      byPlugin[pluginSlug] = (byPlugin[pluginSlug] || 0) + 1
    }

    return {
      totalRegistrations: registrations.length,
      byType,
      byPlugin
    }
  }

  /**
   * Validate component configuration
   */
  static validateComponentConfiguration(type: string, configuration: any): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    switch (type) {
      case 'post_type':
        if (!configuration.label) {
          errors.push('Post type label is required')
        }
        break
      
      case 'taxonomy':
        if (!configuration.label) {
          errors.push('Taxonomy label is required')
        }
        break
      
      case 'custom_field_group':
        if (!configuration.title) {
          errors.push('Field group title is required')
        }
        break
      
      case 'block_type':
        if (!configuration.displayName) {
          errors.push('Block type display name is required')
        }
        break
      
      case 'template':
        if (!configuration.description) {
          errors.push('Template description is required')
        }
        break
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}
