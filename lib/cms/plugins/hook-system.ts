/**
 * WordPress-inspired Hook System for CMS
 * Provides actions and filters for extensibility
 */

type HookCallback = (...args: any[]) => any
type FilterCallback = (value: any, ...args: any[]) => any

interface Hook {
  callback: HookCallback | FilterCallback
  priority: number
  acceptedArgs: number
  tag: string
}

export class HookSystem {
  private static actions = new Map<string, Hook[]>()
  private static filters = new Map<string, Hook[]>()
  private static currentFilter: string | null = null
  private static currentFilterValue: any = null

  /**
   * Initialize the hook system
   */
  static initialize(): void {
    console.log('Hook system initialized')
  }

  /**
   * Add an action hook
   */
  static addAction(
    tag: string,
    callback: HookCallback,
    priority: number = 10,
    acceptedArgs: number = 1
  ): void {
    if (!this.actions.has(tag)) {
      this.actions.set(tag, [])
    }

    const hooks = this.actions.get(tag)!
    hooks.push({
      callback,
      priority,
      acceptedArgs,
      tag
    })

    // Sort by priority
    hooks.sort((a, b) => a.priority - b.priority)
  }

  /**
   * Remove an action hook
   */
  static removeAction(tag: string, callback: HookCallback): boolean {
    const hooks = this.actions.get(tag)
    if (!hooks) return false

    const index = hooks.findIndex(hook => hook.callback === callback)
    if (index === -1) return false

    hooks.splice(index, 1)
    return true
  }

  /**
   * Remove all action hooks for a tag
   */
  static removeAllActions(tag: string): void {
    this.actions.delete(tag)
  }

  /**
   * Check if an action hook exists
   */
  static hasAction(tag: string, callback?: HookCallback): boolean {
    const hooks = this.actions.get(tag)
    if (!hooks) return false

    if (callback) {
      return hooks.some(hook => hook.callback === callback)
    }

    return hooks.length > 0
  }

  /**
   * Execute action hooks
   */
  static async doAction(tag: string, ...args: any[]): Promise<void> {
    const hooks = this.actions.get(tag)
    if (!hooks || hooks.length === 0) return

    for (const hook of hooks) {
      try {
        const callbackArgs = args.slice(0, hook.acceptedArgs)
        await hook.callback(...callbackArgs)
      } catch (error) {
        console.error(`Error executing action hook '${tag}':`, error)
      }
    }
  }

  /**
   * Add a filter hook
   */
  static addFilter(
    tag: string,
    callback: FilterCallback,
    priority: number = 10,
    acceptedArgs: number = 1
  ): void {
    if (!this.filters.has(tag)) {
      this.filters.set(tag, [])
    }

    const hooks = this.filters.get(tag)!
    hooks.push({
      callback,
      priority,
      acceptedArgs,
      tag
    })

    // Sort by priority
    hooks.sort((a, b) => a.priority - b.priority)
  }

  /**
   * Remove a filter hook
   */
  static removeFilter(tag: string, callback: FilterCallback): boolean {
    const hooks = this.filters.get(tag)
    if (!hooks) return false

    const index = hooks.findIndex(hook => hook.callback === callback)
    if (index === -1) return false

    hooks.splice(index, 1)
    return true
  }

  /**
   * Remove all filter hooks for a tag
   */
  static removeAllFilters(tag: string): void {
    this.filters.delete(tag)
  }

  /**
   * Check if a filter hook exists
   */
  static hasFilter(tag: string, callback?: FilterCallback): boolean {
    const hooks = this.filters.get(tag)
    if (!hooks) return false

    if (callback) {
      return hooks.some(hook => hook.callback === callback)
    }

    return hooks.length > 0
  }

  /**
   * Apply filter hooks
   */
  static async applyFilters(tag: string, value: any, ...args: any[]): Promise<any> {
    const hooks = this.filters.get(tag)
    if (!hooks || hooks.length === 0) return value

    let filteredValue = value
    this.currentFilter = tag
    this.currentFilterValue = value

    for (const hook of hooks) {
      try {
        const callbackArgs = [filteredValue, ...args.slice(0, hook.acceptedArgs - 1)]
        filteredValue = await hook.callback(...callbackArgs)
      } catch (error) {
        console.error(`Error executing filter hook '${tag}':`, error)
      }
    }

    this.currentFilter = null
    this.currentFilterValue = null

    return filteredValue
  }

  /**
   * Get current filter being executed
   */
  static getCurrentFilter(): string | null {
    return this.currentFilter
  }

  /**
   * Get current filter value
   */
  static getCurrentFilterValue(): any {
    return this.currentFilterValue
  }

  /**
   * Get all registered actions
   */
  static getActions(): Map<string, Hook[]> {
    return new Map(this.actions)
  }

  /**
   * Get all registered filters
   */
  static getFilters(): Map<string, Hook[]> {
    return new Map(this.filters)
  }

  /**
   * Get hooks for a specific tag
   */
  static getHooks(tag: string, type: 'action' | 'filter' = 'action'): Hook[] {
    const hooks = type === 'action' ? this.actions.get(tag) : this.filters.get(tag)
    return hooks ? [...hooks] : []
  }

  /**
   * Get hook count for a tag
   */
  static getHookCount(tag: string, type: 'action' | 'filter' = 'action'): number {
    const hooks = type === 'action' ? this.actions.get(tag) : this.filters.get(tag)
    return hooks ? hooks.length : 0
  }

  /**
   * Clear all hooks
   */
  static clearAll(): void {
    this.actions.clear()
    this.filters.clear()
  }

  /**
   * Get hook statistics
   */
  static getStats(): {
    totalActions: number
    totalFilters: number
    actionTags: number
    filterTags: number
  } {
    let totalActions = 0
    let totalFilters = 0

    for (const hooks of this.actions.values()) {
      totalActions += hooks.length
    }

    for (const hooks of this.filters.values()) {
      totalFilters += hooks.length
    }

    return {
      totalActions,
      totalFilters,
      actionTags: this.actions.size,
      filterTags: this.filters.size
    }
  }
}

// Common CMS hooks
export const CMS_HOOKS = {
  // Content hooks
  CONTENT_CREATED: 'cms_content_created',
  CONTENT_UPDATED: 'cms_content_updated',
  CONTENT_DELETED: 'cms_content_deleted',
  CONTENT_PUBLISHED: 'cms_content_published',
  CONTENT_UNPUBLISHED: 'cms_content_unpublished',

  // Post type hooks
  POST_TYPE_REGISTERED: 'cms_post_type_registered',
  POST_TYPE_UPDATED: 'cms_post_type_updated',
  POST_TYPE_DELETED: 'cms_post_type_deleted',

  // Custom field hooks
  FIELD_GROUP_CREATED: 'cms_field_group_created',
  FIELD_GROUP_UPDATED: 'cms_field_group_updated',
  FIELD_GROUP_DELETED: 'cms_field_group_deleted',
  FIELD_CREATED: 'cms_field_created',
  FIELD_UPDATED: 'cms_field_updated',
  FIELD_DELETED: 'cms_field_deleted',
  FIELD_VALUE_SAVED: 'cms_field_value_saved',

  // Plugin hooks
  PLUGIN_ACTIVATED: 'cms_plugin_activated',
  PLUGIN_DEACTIVATED: 'cms_plugin_deactivated',
  PLUGIN_INSTALLED: 'cms_plugin_installed',
  PLUGIN_UNINSTALLED: 'cms_plugin_uninstalled',

  // Rendering hooks
  BEFORE_RENDER: 'cms_before_render',
  AFTER_RENDER: 'cms_after_render',
  TEMPLATE_RESOLVED: 'cms_template_resolved',

  // Filter hooks
  CONTENT_FILTER: 'cms_content_filter',
  TEMPLATE_HIERARCHY: 'cms_template_hierarchy',
  FIELD_VALUE_FILTER: 'cms_field_value_filter',
  QUERY_FILTER: 'cms_query_filter'
} as const

// Convenience functions for common hooks
export const cmsHooks = {
  /**
   * Add action for content created
   */
  onContentCreated: (callback: (content: any) => void, priority = 10) => {
    HookSystem.addAction(CMS_HOOKS.CONTENT_CREATED, callback, priority)
  },

  /**
   * Add action for content updated
   */
  onContentUpdated: (callback: (content: any) => void, priority = 10) => {
    HookSystem.addAction(CMS_HOOKS.CONTENT_UPDATED, callback, priority)
  },

  /**
   * Add action for plugin activated
   */
  onPluginActivated: (callback: (plugin: any) => void, priority = 10) => {
    HookSystem.addAction(CMS_HOOKS.PLUGIN_ACTIVATED, callback, priority)
  },

  /**
   * Filter content before rendering
   */
  filterContent: (callback: (content: any) => any, priority = 10) => {
    HookSystem.addFilter(CMS_HOOKS.CONTENT_FILTER, callback, priority)
  },

  /**
   * Filter template hierarchy
   */
  filterTemplateHierarchy: (callback: (hierarchy: string[]) => string[], priority = 10) => {
    HookSystem.addFilter(CMS_HOOKS.TEMPLATE_HIERARCHY, callback, priority)
  },

  /**
   * Execute content created action
   */
  doContentCreated: (content: any) => {
    return HookSystem.doAction(CMS_HOOKS.CONTENT_CREATED, content)
  },

  /**
   * Apply content filter
   */
  applyContentFilter: (content: any) => {
    return HookSystem.applyFilters(CMS_HOOKS.CONTENT_FILTER, content)
  }
}
