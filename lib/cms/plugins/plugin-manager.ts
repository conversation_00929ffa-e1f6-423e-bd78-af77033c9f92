import { PrismaClient } from '@prisma/client'
import { Plugin, PluginHook } from '../types'
import { HookSystem } from './hook-system'
import { PluginRegistry } from './plugin-registry'

const prisma = new PrismaClient()

export class PluginManager {
  private static instance: PluginManager
  private activePlugins = new Map<string, Plugin>()
  private pluginDependencies = new Map<string, string[]>()

  private constructor() {}

  static getInstance(): PluginManager {
    if (!this.instance) {
      this.instance = new PluginManager()
    }
    return this.instance
  }

  /**
   * Initialize the plugin system
   */
  async initialize(): Promise<void> {
    console.log('Initializing plugin system...')
    
    // Load active plugins from database
    await this.loadActivePlugins()
    
    // Initialize hook system
    HookSystem.initialize()
    
    // Register plugin hooks
    await this.registerPluginHooks()
    
    console.log(`Plugin system initialized with ${this.activePlugins.size} active plugins`)
  }

  /**
   * Register a new plugin
   */
  async registerPlugin(pluginData: Partial<Plugin>): Promise<Plugin> {
    if (!pluginData.name || !pluginData.slug || !pluginData.version) {
      throw new Error('Plugin name, slug, and version are required')
    }

    // Check if plugin already exists
    const existing = await prisma.plugin.findUnique({
      where: { slug: pluginData.slug }
    })

    if (existing) {
      throw new Error(`Plugin with slug '${pluginData.slug}' already exists`)
    }

    // Validate dependencies
    if (pluginData.dependencies && pluginData.dependencies.length > 0) {
      await this.validateDependencies(pluginData.dependencies)
    }

    // Create the plugin
    const plugin = await prisma.plugin.create({
      data: {
        name: pluginData.name,
        slug: pluginData.slug,
        version: pluginData.version,
        description: pluginData.description,
        author: pluginData.author,
        authorUri: pluginData.authorUri,
        pluginUri: pluginData.pluginUri,
        textDomain: pluginData.textDomain,
        domainPath: pluginData.domainPath,
        requiresWp: pluginData.requiresWp,
        requiresPhp: pluginData.requiresPhp,
        network: pluginData.network || false,
        license: pluginData.license,
        licenseUri: pluginData.licenseUri,
        updateUri: pluginData.updateUri,
        isActive: false, // Plugins start inactive
        isInstalled: true,
        isSystem: pluginData.isSystem || false,
        autoUpdate: pluginData.autoUpdate || false,
        activationHooks: pluginData.activationHooks,
        deactivationHooks: pluginData.deactivationHooks,
        uninstallHooks: pluginData.uninstallHooks,
        dependencies: pluginData.dependencies || [],
        conflicts: pluginData.conflicts || [],
        settings: pluginData.settings,
        metadata: pluginData.metadata,
        installPath: pluginData.installPath,
        mainFile: pluginData.mainFile
      },
      include: {
        hooks: true,
        registrations: true
      }
    })

    console.log(`Plugin '${plugin.name}' registered successfully`)
    return plugin as Plugin
  }

  /**
   * Activate a plugin
   */
  async activatePlugin(slug: string): Promise<void> {
    const plugin = await this.getPlugin(slug)
    if (!plugin) {
      throw new Error(`Plugin '${slug}' not found`)
    }

    if (plugin.isActive) {
      throw new Error(`Plugin '${slug}' is already active`)
    }

    // Check dependencies
    if (plugin.dependencies.length > 0) {
      await this.validateDependencies(plugin.dependencies)
    }

    // Check conflicts
    if (plugin.conflicts.length > 0) {
      await this.checkConflicts(plugin.conflicts)
    }

    // Run activation hooks
    if (plugin.activationHooks) {
      await this.runHooks(plugin.activationHooks as PluginHook[], { plugin })
    }

    // Activate the plugin
    await prisma.plugin.update({
      where: { slug },
      data: { isActive: true }
    })

    // Load plugin into memory
    const updatedPlugin = await this.getPlugin(slug)
    if (updatedPlugin) {
      this.activePlugins.set(slug, updatedPlugin)
      
      // Register plugin hooks
      await this.registerPluginHooks(updatedPlugin)
      
      // Register plugin components
      await this.registerPluginComponents(updatedPlugin)
    }

    console.log(`Plugin '${plugin.name}' activated successfully`)
  }

  /**
   * Deactivate a plugin
   */
  async deactivatePlugin(slug: string): Promise<void> {
    const plugin = await this.getPlugin(slug)
    if (!plugin) {
      throw new Error(`Plugin '${slug}' not found`)
    }

    if (!plugin.isActive) {
      throw new Error(`Plugin '${slug}' is not active`)
    }

    if (plugin.isSystem) {
      throw new Error(`Cannot deactivate system plugin '${slug}'`)
    }

    // Check if other plugins depend on this one
    const dependentPlugins = await this.getDependentPlugins(slug)
    if (dependentPlugins.length > 0) {
      const names = dependentPlugins.map(p => p.name).join(', ')
      throw new Error(`Cannot deactivate plugin '${slug}' because these plugins depend on it: ${names}`)
    }

    // Run deactivation hooks
    if (plugin.deactivationHooks) {
      await this.runHooks(plugin.deactivationHooks as PluginHook[], { plugin })
    }

    // Deactivate the plugin
    await prisma.plugin.update({
      where: { slug },
      data: { isActive: false }
    })

    // Remove from memory
    this.activePlugins.delete(slug)

    // Unregister plugin hooks
    await this.unregisterPluginHooks(plugin)

    console.log(`Plugin '${plugin.name}' deactivated successfully`)
  }

  /**
   * Uninstall a plugin
   */
  async uninstallPlugin(slug: string): Promise<void> {
    const plugin = await this.getPlugin(slug)
    if (!plugin) {
      throw new Error(`Plugin '${slug}' not found`)
    }

    if (plugin.isSystem) {
      throw new Error(`Cannot uninstall system plugin '${slug}'`)
    }

    // Deactivate first if active
    if (plugin.isActive) {
      await this.deactivatePlugin(slug)
    }

    // Run uninstall hooks
    if (plugin.uninstallHooks) {
      await this.runHooks(plugin.uninstallHooks as PluginHook[], { plugin })
    }

    // Delete plugin data
    await prisma.plugin.delete({
      where: { slug }
    })

    console.log(`Plugin '${plugin.name}' uninstalled successfully`)
  }

  /**
   * Get a plugin by slug
   */
  async getPlugin(slug: string): Promise<Plugin | null> {
    const plugin = await prisma.plugin.findUnique({
      where: { slug },
      include: {
        hooks: true,
        registrations: true
      }
    })

    return plugin as Plugin | null
  }

  /**
   * Get all plugins
   */
  async getPlugins(includeInactive = false): Promise<Plugin[]> {
    const where = includeInactive ? {} : { isActive: true }
    
    const plugins = await prisma.plugin.findMany({
      where,
      include: {
        hooks: true,
        registrations: true
      },
      orderBy: { name: 'asc' }
    })

    return plugins as Plugin[]
  }

  /**
   * Get active plugins
   */
  getActivePlugins(): Plugin[] {
    return Array.from(this.activePlugins.values())
  }

  /**
   * Check if a plugin is active
   */
  isPluginActive(slug: string): boolean {
    return this.activePlugins.has(slug)
  }

  /**
   * Load active plugins from database
   */
  private async loadActivePlugins(): Promise<void> {
    const plugins = await this.getPlugins(false)
    
    for (const plugin of plugins) {
      this.activePlugins.set(plugin.slug, plugin)
    }
  }

  /**
   * Register hooks for all active plugins
   */
  private async registerPluginHooks(plugin?: Plugin): Promise<void> {
    const plugins = plugin ? [plugin] : this.getActivePlugins()
    
    for (const plugin of plugins) {
      for (const hook of plugin.hooks || []) {
        if (hook.isActive) {
          HookSystem.addAction(hook.hookName, hook.callback, hook.priority, hook.acceptedArgs)
        }
      }
    }
  }

  /**
   * Unregister hooks for a plugin
   */
  private async unregisterPluginHooks(plugin: Plugin): Promise<void> {
    for (const hook of plugin.hooks || []) {
      HookSystem.removeAction(hook.hookName, hook.callback)
    }
  }

  /**
   * Register components for a plugin
   */
  private async registerPluginComponents(plugin: Plugin): Promise<void> {
    for (const registration of plugin.registrations || []) {
      if (registration.isActive) {
        await PluginRegistry.registerComponent(
          registration.type,
          registration.name,
          registration.configuration,
          plugin.slug
        )
      }
    }
  }

  /**
   * Validate plugin dependencies
   */
  private async validateDependencies(dependencies: string[]): Promise<void> {
    for (const dependency of dependencies) {
      const dependencyPlugin = await this.getPlugin(dependency)
      if (!dependencyPlugin) {
        throw new Error(`Required plugin '${dependency}' is not installed`)
      }
      if (!dependencyPlugin.isActive) {
        throw new Error(`Required plugin '${dependency}' is not active`)
      }
    }
  }

  /**
   * Check for plugin conflicts
   */
  private async checkConflicts(conflicts: string[]): Promise<void> {
    for (const conflict of conflicts) {
      const conflictPlugin = await this.getPlugin(conflict)
      if (conflictPlugin && conflictPlugin.isActive) {
        throw new Error(`Plugin conflicts with active plugin '${conflict}'`)
      }
    }
  }

  /**
   * Get plugins that depend on a specific plugin
   */
  private async getDependentPlugins(slug: string): Promise<Plugin[]> {
    const allPlugins = await this.getPlugins(false)
    return allPlugins.filter(plugin => plugin.dependencies.includes(slug))
  }

  /**
   * Run plugin hooks
   */
  private async runHooks(hooks: PluginHook[], context: Record<string, any>): Promise<void> {
    for (const hook of hooks) {
      try {
        // In a real implementation, you would dynamically load and execute the hook callback
        console.log(`Running hook: ${hook.hookName} -> ${hook.callback}`)
        // await this.executeHookCallback(hook.callback, context)
      } catch (error) {
        console.error(`Error running hook ${hook.hookName}:`, error)
      }
    }
  }
}
