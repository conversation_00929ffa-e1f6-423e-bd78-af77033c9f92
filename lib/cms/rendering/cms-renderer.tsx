'use client'

import React from 'react'
import { CMSContent, PostType } from '../types'
import { PageBuilderRenderer } from '@/lib/page-builder/components/page-builder-renderer'
import { TemplateResolver } from './template-resolver'
import { ComponentRegistry } from './component-registry'

interface CMSRendererProps {
  content: CMSContent
  postType: PostType
  template?: string
  context?: Record<string, any>
}

export function CMSRenderer({ content, postType, template, context = {} }: CMSRendererProps) {
  // If content uses page builder, render with page builder
  if (content.usePageBuilder && content.pageBuilderData) {
    return (
      <PageBuilderRenderer
        pageData={{
          title: content.title,
          slug: content.slug,
          status: content.status,
          type: content.postType,
          blocks: content.pageBuilderData.blocks || [],
          settings: content.pageBuilderData.settings || {}
        }}
      />
    )
  }

  // Otherwise, use template-based rendering
  const resolvedTemplate = template || TemplateResolver.resolveTemplate(content, postType)
  const TemplateComponent = ComponentRegistry.getTemplate(resolvedTemplate)

  if (TemplateComponent) {
    return (
      <TemplateComponent
        content={content}
        postType={postType}
        context={context}
      />
    )
  }

  // Fallback to default content rendering
  return <DefaultContentRenderer content={content} postType={postType} />
}

interface DefaultContentRendererProps {
  content: CMSContent
  postType: PostType
}

function DefaultContentRenderer({ content, postType }: DefaultContentRendererProps) {
  return (
    <article className="cms-content">
      {/* Header */}
      <header className="cms-content-header">
        <h1 className="cms-content-title">{content.title}</h1>
        
        {content.excerpt && (
          <div className="cms-content-excerpt">
            {content.excerpt}
          </div>
        )}

        {/* Meta information */}
        <div className="cms-content-meta">
          {content.publishedAt && (
            <time dateTime={content.publishedAt.toISOString()}>
              {content.publishedAt.toLocaleDateString()}
            </time>
          )}
          
          {content.authorName && (
            <span className="cms-content-author">
              by {content.authorName}
            </span>
          )}
        </div>
      </header>

      {/* Featured Image */}
      {content.featuredImage && (
        <div className="cms-content-featured-image">
          <img
            src={content.featuredImage}
            alt={content.featuredImageAlt || content.title}
            className="w-full h-auto"
          />
        </div>
      )}

      {/* Content */}
      <div className="cms-content-body">
        {content.contentHtml ? (
          <div
            dangerouslySetInnerHTML={{ __html: content.contentHtml }}
            className="prose prose-lg max-w-none"
          />
        ) : content.content ? (
          <div className="prose prose-lg max-w-none whitespace-pre-wrap">
            {content.content}
          </div>
        ) : null}
      </div>

      {/* Custom Fields */}
      {content.customFields && Object.keys(content.customFields).length > 0 && (
        <div className="cms-content-custom-fields">
          <CustomFieldsRenderer
            fields={content.customFields}
            postType={postType}
          />
        </div>
      )}

      {/* Taxonomy Terms */}
      {content.taxonomyTerms && content.taxonomyTerms.length > 0 && (
        <div className="cms-content-taxonomies">
          <TaxonomyRenderer terms={content.taxonomyTerms} />
        </div>
      )}

      {/* Comments */}
      {content.allowComments && (
        <div className="cms-content-comments">
          <CommentsRenderer contentId={content.id} />
        </div>
      )}
    </article>
  )
}

interface CustomFieldsRendererProps {
  fields: Record<string, any>
  postType: PostType
}

function CustomFieldsRenderer({ fields, postType }: CustomFieldsRendererProps) {
  return (
    <div className="custom-fields">
      {Object.entries(fields).map(([fieldName, value]) => (
        <div key={fieldName} className="custom-field">
          <CustomFieldRenderer
            name={fieldName}
            value={value}
            postType={postType}
          />
        </div>
      ))}
    </div>
  )
}

interface CustomFieldRendererProps {
  name: string
  value: any
  postType: PostType
}

function CustomFieldRenderer({ name, value, postType }: CustomFieldRendererProps) {
  // Get field configuration from registry
  const fieldConfig = ComponentRegistry.getFieldRenderer(name)
  
  if (fieldConfig) {
    const FieldComponent = fieldConfig.component
    return <FieldComponent value={value} config={fieldConfig.config} />
  }

  // Default field rendering based on value type
  if (value === null || value === undefined) {
    return null
  }

  if (typeof value === 'boolean') {
    return <span>{value ? 'Yes' : 'No'}</span>
  }

  if (typeof value === 'object') {
    if (Array.isArray(value)) {
      return (
        <ul>
          {value.map((item, index) => (
            <li key={index}>
              <CustomFieldRenderer
                name={`${name}[${index}]`}
                value={item}
                postType={postType}
              />
            </li>
          ))}
        </ul>
      )
    }

    // Handle image objects
    if (value.url) {
      return (
        <img
          src={value.url}
          alt={value.alt || name}
          className="custom-field-image"
        />
      )
    }

    // Handle link objects
    if (value.url && value.title) {
      return (
        <a
          href={value.url}
          target={value.target || '_self'}
          className="custom-field-link"
        >
          {value.title}
        </a>
      )
    }

    return <pre>{JSON.stringify(value, null, 2)}</pre>
  }

  return <span>{String(value)}</span>
}

interface TaxonomyRendererProps {
  terms: any[]
}

function TaxonomyRenderer({ terms }: TaxonomyRendererProps) {
  const groupedTerms = terms.reduce((acc, term) => {
    const taxonomyName = term.taxonomy?.name || 'unknown'
    if (!acc[taxonomyName]) {
      acc[taxonomyName] = []
    }
    acc[taxonomyName].push(term)
    return acc
  }, {} as Record<string, any[]>)

  return (
    <div className="taxonomy-terms">
      {Object.entries(groupedTerms).map(([taxonomyName, taxonomyTerms]) => (
        <div key={taxonomyName} className="taxonomy-group">
          <h4 className="taxonomy-label">
            {taxonomyName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}:
          </h4>
          <div className="taxonomy-terms-list">
            {taxonomyTerms.map((term, index) => (
              <span key={term.id} className="taxonomy-term">
                {term.name}
                {index < taxonomyTerms.length - 1 && ', '}
              </span>
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}

interface CommentsRendererProps {
  contentId: string
}

function CommentsRenderer({ contentId }: CommentsRendererProps) {
  // This would integrate with your comments system
  return (
    <div className="comments-section">
      <h3>Comments</h3>
      <p>Comments for content {contentId} would be rendered here.</p>
    </div>
  )
}

// Archive renderer for post type archives
interface ArchiveRendererProps {
  postType: PostType
  content: CMSContent[]
  pagination?: {
    currentPage: number
    totalPages: number
    totalItems: number
  }
}

export function ArchiveRenderer({ postType, content, pagination }: ArchiveRendererProps) {
  const ArchiveTemplate = ComponentRegistry.getArchiveTemplate(postType.name)

  if (ArchiveTemplate) {
    return (
      <ArchiveTemplate
        postType={postType}
        content={content}
        pagination={pagination}
      />
    )
  }

  // Default archive rendering
  return (
    <div className="cms-archive">
      <header className="cms-archive-header">
        <h1 className="cms-archive-title">{postType.labelPlural}</h1>
        {postType.description && (
          <p className="cms-archive-description">{postType.description}</p>
        )}
      </header>

      <div className="cms-archive-content">
        {content.length > 0 ? (
          <div className="cms-archive-grid">
            {content.map((item) => (
              <div key={item.id} className="cms-archive-item">
                <CMSRenderer
                  content={item}
                  postType={postType}
                  template="archive-item"
                />
              </div>
            ))}
          </div>
        ) : (
          <p className="cms-archive-empty">
            No {postType.labelPlural.toLowerCase()} found.
          </p>
        )}
      </div>

      {pagination && pagination.totalPages > 1 && (
        <div className="cms-archive-pagination">
          <PaginationRenderer pagination={pagination} />
        </div>
      )}
    </div>
  )
}

interface PaginationRendererProps {
  pagination: {
    currentPage: number
    totalPages: number
    totalItems: number
  }
}

function PaginationRenderer({ pagination }: PaginationRendererProps) {
  const { currentPage, totalPages } = pagination

  return (
    <nav className="pagination">
      {currentPage > 1 && (
        <a href={`?page=${currentPage - 1}`} className="pagination-prev">
          Previous
        </a>
      )}
      
      <span className="pagination-info">
        Page {currentPage} of {totalPages}
      </span>
      
      {currentPage < totalPages && (
        <a href={`?page=${currentPage + 1}`} className="pagination-next">
          Next
        </a>
      )}
    </nav>
  )
}
