import React from 'react'
import { C<PERSON><PERSON>ontent, PostType, CustomField } from '../types'

// Template component interface
export interface TemplateComponent {
  (props: {
    content: CMSContent
    postType: PostType
    context?: Record<string, any>
  }): React.ReactElement
}

// Archive template component interface
export interface ArchiveTemplateComponent {
  (props: {
    postType: PostType
    content: CMSContent[]
    pagination?: {
      currentPage: number
      totalPages: number
      totalItems: number
    }
  }): React.ReactElement
}

// Field renderer component interface
export interface FieldRendererComponent {
  (props: {
    value: any
    config: Record<string, any>
    field?: CustomField
  }): React.ReactElement
}

// Registry interfaces
interface TemplateRegistration {
  component: TemplateComponent
  postTypes?: string[]
  priority?: number
}

interface ArchiveTemplateRegistration {
  component: ArchiveTemplateComponent
  postTypes?: string[]
  priority?: number
}

interface FieldRendererRegistration {
  component: FieldRendererComponent
  config: Record<string, any>
  fieldTypes?: string[]
}

export class ComponentRegistry {
  private static templates = new Map<string, TemplateRegistration>()
  private static archiveTemplates = new Map<string, ArchiveTemplateRegistration>()
  private static fieldRenderers = new Map<string, FieldRendererRegistration>()
  private static blockComponents = new Map<string, React.ComponentType<any>>()

  /**
   * Register a template component
   */
  static registerTemplate(
    name: string,
    component: TemplateComponent,
    options: {
      postTypes?: string[]
      priority?: number
    } = {}
  ): void {
    this.templates.set(name, {
      component,
      postTypes: options.postTypes,
      priority: options.priority || 10
    })
  }

  /**
   * Register an archive template component
   */
  static registerArchiveTemplate(
    name: string,
    component: ArchiveTemplateComponent,
    options: {
      postTypes?: string[]
      priority?: number
    } = {}
  ): void {
    this.archiveTemplates.set(name, {
      component,
      postTypes: options.postTypes,
      priority: options.priority || 10
    })
  }

  /**
   * Register a field renderer component
   */
  static registerFieldRenderer(
    name: string,
    component: FieldRendererComponent,
    config: Record<string, any> = {},
    fieldTypes?: string[]
  ): void {
    this.fieldRenderers.set(name, {
      component,
      config,
      fieldTypes
    })
  }

  /**
   * Register a block component
   */
  static registerBlockComponent(
    name: string,
    component: React.ComponentType<any>
  ): void {
    this.blockComponents.set(name, component)
  }

  /**
   * Get a template component
   */
  static getTemplate(name: string): TemplateComponent | null {
    const registration = this.templates.get(name)
    return registration?.component || null
  }

  /**
   * Get an archive template component
   */
  static getArchiveTemplate(postType: string): ArchiveTemplateComponent | null {
    // Try post type specific archive template first
    const postTypeTemplate = this.archiveTemplates.get(`archive-${postType}`)
    if (postTypeTemplate) {
      return postTypeTemplate.component
    }

    // Try generic archive template
    const genericTemplate = this.archiveTemplates.get('archive')
    if (genericTemplate) {
      return genericTemplate.component
    }

    return null
  }

  /**
   * Get a field renderer
   */
  static getFieldRenderer(name: string): FieldRendererRegistration | null {
    return this.fieldRenderers.get(name) || null
  }

  /**
   * Get a block component
   */
  static getBlockComponent(name: string): React.ComponentType<any> | null {
    return this.blockComponents.get(name) || null
  }

  /**
   * Get templates for a specific post type
   */
  static getTemplatesForPostType(postType: string): string[] {
    const templates: string[] = []
    
    for (const [name, registration] of this.templates.entries()) {
      if (!registration.postTypes || registration.postTypes.includes(postType)) {
        templates.push(name)
      }
    }
    
    return templates.sort((a, b) => {
      const aPriority = this.templates.get(a)?.priority || 10
      const bPriority = this.templates.get(b)?.priority || 10
      return aPriority - bPriority
    })
  }

  /**
   * Get field renderers for a specific field type
   */
  static getFieldRenderersForType(fieldType: string): string[] {
    const renderers: string[] = []
    
    for (const [name, registration] of this.fieldRenderers.entries()) {
      if (!registration.fieldTypes || registration.fieldTypes.includes(fieldType)) {
        renderers.push(name)
      }
    }
    
    return renderers
  }

  /**
   * Check if a template is registered
   */
  static hasTemplate(name: string): boolean {
    return this.templates.has(name)
  }

  /**
   * Check if an archive template is registered
   */
  static hasArchiveTemplate(name: string): boolean {
    return this.archiveTemplates.has(name)
  }

  /**
   * Check if a field renderer is registered
   */
  static hasFieldRenderer(name: string): boolean {
    return this.fieldRenderers.has(name)
  }

  /**
   * Check if a block component is registered
   */
  static hasBlockComponent(name: string): boolean {
    return this.blockComponents.has(name)
  }

  /**
   * Get all registered templates
   */
  static getAllTemplates(): string[] {
    return Array.from(this.templates.keys())
  }

  /**
   * Get all registered archive templates
   */
  static getAllArchiveTemplates(): string[] {
    return Array.from(this.archiveTemplates.keys())
  }

  /**
   * Get all registered field renderers
   */
  static getAllFieldRenderers(): string[] {
    return Array.from(this.fieldRenderers.keys())
  }

  /**
   * Get all registered block components
   */
  static getAllBlockComponents(): string[] {
    return Array.from(this.blockComponents.keys())
  }

  /**
   * Unregister a template
   */
  static unregisterTemplate(name: string): boolean {
    return this.templates.delete(name)
  }

  /**
   * Unregister an archive template
   */
  static unregisterArchiveTemplate(name: string): boolean {
    return this.archiveTemplates.delete(name)
  }

  /**
   * Unregister a field renderer
   */
  static unregisterFieldRenderer(name: string): boolean {
    return this.fieldRenderers.delete(name)
  }

  /**
   * Unregister a block component
   */
  static unregisterBlockComponent(name: string): boolean {
    return this.blockComponents.delete(name)
  }

  /**
   * Clear all registrations
   */
  static clearAll(): void {
    this.templates.clear()
    this.archiveTemplates.clear()
    this.fieldRenderers.clear()
    this.blockComponents.clear()
  }

  /**
   * Get registration info for debugging
   */
  static getRegistrationInfo(): {
    templates: number
    archiveTemplates: number
    fieldRenderers: number
    blockComponents: number
  } {
    return {
      templates: this.templates.size,
      archiveTemplates: this.archiveTemplates.size,
      fieldRenderers: this.fieldRenderers.size,
      blockComponents: this.blockComponents.size
    }
  }

  /**
   * Initialize default components
   */
  static initializeDefaults(): void {
    // Register default templates
    this.registerDefaultTemplates()
    
    // Register default field renderers
    this.registerDefaultFieldRenderers()
    
    // Register default block components
    this.registerDefaultBlockComponents()
  }

  /**
   * Register default templates
   */
  private static registerDefaultTemplates(): void {
    // Default single template
    this.registerTemplate('single', ({ content, postType }) => {
      return React.createElement('div', { className: 'single-template' }, [
        React.createElement('h1', { key: 'title' }, content.title),
        React.createElement('div', { 
          key: 'content',
          dangerouslySetInnerHTML: { __html: content.contentHtml || content.content || '' }
        })
      ])
    })

    // Default page template
    this.registerTemplate('page', ({ content }) => {
      return React.createElement('div', { className: 'page-template' }, [
        React.createElement('h1', { key: 'title' }, content.title),
        React.createElement('div', { 
          key: 'content',
          dangerouslySetInnerHTML: { __html: content.contentHtml || content.content || '' }
        })
      ])
    }, { postTypes: ['page'] })

    // Default archive template
    this.registerArchiveTemplate('archive', ({ postType, content }) => {
      return React.createElement('div', { className: 'archive-template' }, [
        React.createElement('h1', { key: 'title' }, postType.labelPlural),
        React.createElement('div', { key: 'content' }, 
          content.map(item => 
            React.createElement('div', { key: item.id, className: 'archive-item' }, [
              React.createElement('h2', { key: 'title' }, item.title),
              React.createElement('p', { key: 'excerpt' }, item.excerpt)
            ])
          )
        )
      ])
    })
  }

  /**
   * Register default field renderers
   */
  private static registerDefaultFieldRenderers(): void {
    // Text field renderer
    this.registerFieldRenderer('text', ({ value }) => {
      return React.createElement('span', {}, String(value || ''))
    }, {}, ['text', 'textarea', 'email', 'url'])

    // Image field renderer
    this.registerFieldRenderer('image', ({ value }) => {
      if (!value || !value.url) return null
      return React.createElement('img', {
        src: value.url,
        alt: value.alt || '',
        className: 'custom-field-image'
      })
    }, {}, ['image'])

    // Link field renderer
    this.registerFieldRenderer('link', ({ value }) => {
      if (!value || !value.url) return null
      return React.createElement('a', {
        href: value.url,
        target: value.target || '_self',
        className: 'custom-field-link'
      }, value.title || value.url)
    }, {}, ['link'])
  }

  /**
   * Register default block components
   */
  private static registerDefaultBlockComponents(): void {
    // Text block
    this.registerBlockComponent('text', ({ content }) => {
      return React.createElement('div', { 
        className: 'text-block',
        dangerouslySetInnerHTML: { __html: content || '' }
      })
    })

    // Image block
    this.registerBlockComponent('image', ({ src, alt, className }) => {
      return React.createElement('img', {
        src,
        alt: alt || '',
        className: `image-block ${className || ''}`
      })
    })
  }
}
