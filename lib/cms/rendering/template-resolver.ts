import { CMSContent, PostType, TemplateHierarchy } from '../types'
import { TEMPLATE_HIERARCHY } from '../constants'

export class TemplateResolver {
  /**
   * Resolve the template to use for rendering content
   */
  static resolveTemplate(content: CMSContent, postType: PostType, context: 'single' | 'archive' = 'single'): string {
    // Check if content has a specific template assigned
    if (content.template) {
      return content.template
    }

    // Get template hierarchy for the post type
    const hierarchy = this.getTemplateHierarchy(postType, content, context)
    
    // Return the first template in the hierarchy
    // In a real implementation, you would check if each template file exists
    return hierarchy[0] || 'index.tsx'
  }

  /**
   * Get template hierarchy for a post type and content
   */
  static getTemplateHierarchy(postType: PostType, content: CMSContent, context: 'single' | 'archive' = 'single'): string[] {
    const templates: string[] = []
    
    if (context === 'single') {
      // Single content templates
      
      // Post type specific templates with slug
      templates.push(`single-${postType.name}-${content.slug}.tsx`)
      
      // Post type specific templates with ID
      templates.push(`single-${postType.name}-${content.id}.tsx`)
      
      // Post type specific template
      templates.push(`single-${postType.name}.tsx`)
      
      // Custom single template from post type
      if (postType.singleTemplate) {
        templates.push(postType.singleTemplate)
      }
      
      // Generic single template
      templates.push('single.tsx')
      
      // Singular template (for pages and posts)
      if (postType.name === 'page' || postType.name === 'post') {
        templates.push('singular.tsx')
      }
      
      // Page specific templates
      if (postType.name === 'page') {
        templates.push(`page-${content.slug}.tsx`)
        templates.push(`page-${content.id}.tsx`)
        templates.push('page.tsx')
      }
      
    } else {
      // Archive templates
      
      // Custom archive template from post type
      if (postType.archiveTemplate) {
        templates.push(postType.archiveTemplate)
      }
      
      // Post type specific archive
      templates.push(`archive-${postType.name}.tsx`)
      
      // Generic archive
      templates.push('archive.tsx')
    }
    
    // Fallback to index
    templates.push('index.tsx')
    
    return templates
  }

  /**
   * Get taxonomy template hierarchy
   */
  static getTaxonomyTemplateHierarchy(taxonomyName: string, termSlug?: string): string[] {
    const templates: string[] = []
    
    if (termSlug) {
      templates.push(`taxonomy-${taxonomyName}-${termSlug}.tsx`)
    }
    
    templates.push(`taxonomy-${taxonomyName}.tsx`)
    templates.push('taxonomy.tsx')
    templates.push('archive.tsx')
    templates.push('index.tsx')
    
    return templates
  }

  /**
   * Get search template hierarchy
   */
  static getSearchTemplateHierarchy(): string[] {
    return ['search.tsx', 'index.tsx']
  }

  /**
   * Get 404 template hierarchy
   */
  static get404TemplateHierarchy(): string[] {
    return ['404.tsx', 'index.tsx']
  }

  /**
   * Get home page template hierarchy
   */
  static getHomeTemplateHierarchy(): string[] {
    return [
      'front-page.tsx',
      'home.tsx',
      'index.tsx'
    ]
  }

  /**
   * Get template hierarchy for a specific context
   */
  static getTemplateHierarchyForContext(context: {
    type: 'single' | 'archive' | 'taxonomy' | 'search' | '404' | 'home'
    postType?: PostType
    content?: CMSContent
    taxonomyName?: string
    termSlug?: string
  }): string[] {
    switch (context.type) {
      case 'single':
        if (context.postType && context.content) {
          return this.getTemplateHierarchy(context.postType, context.content, 'single')
        }
        return ['single.tsx', 'index.tsx']
        
      case 'archive':
        if (context.postType && context.content) {
          return this.getTemplateHierarchy(context.postType, context.content, 'archive')
        }
        return ['archive.tsx', 'index.tsx']
        
      case 'taxonomy':
        return this.getTaxonomyTemplateHierarchy(context.taxonomyName!, context.termSlug)
        
      case 'search':
        return this.getSearchTemplateHierarchy()
        
      case '404':
        return this.get404TemplateHierarchy()
        
      case 'home':
        return this.getHomeTemplateHierarchy()
        
      default:
        return ['index.tsx']
    }
  }

  /**
   * Check if a template supports a specific post type
   */
  static templateSupportsPostType(template: string, postType: PostType): boolean {
    // Extract post type from template name
    const templatePostType = this.extractPostTypeFromTemplate(template)
    
    if (!templatePostType) {
      // Generic templates support all post types
      return true
    }
    
    return templatePostType === postType.name
  }

  /**
   * Extract post type from template name
   */
  static extractPostTypeFromTemplate(template: string): string | null {
    const patterns = [
      /^single-([^-]+)(?:-.*)?\.tsx$/,
      /^archive-([^-]+)\.tsx$/,
      /^taxonomy-([^-]+)(?:-.*)?\.tsx$/
    ]
    
    for (const pattern of patterns) {
      const match = template.match(pattern)
      if (match) {
        return match[1]
      }
    }
    
    return null
  }

  /**
   * Get template variables for rendering
   */
  static getTemplateVariables(content: CMSContent, postType: PostType, context: Record<string, any> = {}): Record<string, any> {
    return {
      content,
      postType,
      post: content, // WordPress compatibility
      post_type: postType, // WordPress compatibility
      is_single: true,
      is_page: postType.name === 'page',
      is_post: postType.name === 'post',
      is_archive: false,
      is_home: false,
      is_front_page: false,
      is_search: false,
      is_404: false,
      ...context
    }
  }

  /**
   * Get archive template variables
   */
  static getArchiveTemplateVariables(postType: PostType, content: CMSContent[], context: Record<string, any> = {}): Record<string, any> {
    return {
      postType,
      content,
      posts: content, // WordPress compatibility
      post_type: postType, // WordPress compatibility
      is_single: false,
      is_page: false,
      is_post: false,
      is_archive: true,
      is_home: postType.name === 'post',
      is_front_page: false,
      is_search: false,
      is_404: false,
      ...context
    }
  }

  /**
   * Get taxonomy template variables
   */
  static getTaxonomyTemplateVariables(taxonomyName: string, termSlug: string, content: CMSContent[], context: Record<string, any> = {}): Record<string, any> {
    return {
      taxonomy: taxonomyName,
      term: termSlug,
      content,
      posts: content, // WordPress compatibility
      is_single: false,
      is_page: false,
      is_post: false,
      is_archive: true,
      is_taxonomy: true,
      is_category: taxonomyName === 'category',
      is_tag: taxonomyName === 'post_tag',
      is_home: false,
      is_front_page: false,
      is_search: false,
      is_404: false,
      ...context
    }
  }

  /**
   * Resolve template path
   */
  static resolveTemplatePath(template: string, basePath = '/templates'): string {
    // Remove .tsx extension if present
    const cleanTemplate = template.replace(/\.tsx$/, '')
    
    // Add base path
    return `${basePath}/${cleanTemplate}`
  }

  /**
   * Get all possible templates for a post type
   */
  static getAllTemplatesForPostType(postType: PostType): string[] {
    const templates = new Set<string>()
    
    // Single templates
    templates.add(`single-${postType.name}.tsx`)
    templates.add('single.tsx')
    templates.add('singular.tsx')
    
    // Archive templates
    if (postType.hasArchive) {
      templates.add(`archive-${postType.name}.tsx`)
      templates.add('archive.tsx')
    }
    
    // Page specific templates
    if (postType.name === 'page') {
      templates.add('page.tsx')
      templates.add('front-page.tsx')
      templates.add('home.tsx')
    }
    
    // Custom templates from post type
    if (postType.templates) {
      postType.templates.forEach(template => templates.add(template))
    }
    
    // Fallback
    templates.add('index.tsx')
    
    return Array.from(templates)
  }
}
