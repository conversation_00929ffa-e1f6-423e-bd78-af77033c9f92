import { Prisma } from '@prisma/client'

// Core CMS Types
export interface PostType {
  id: string
  name: string
  label: string
  labelPlural: string
  description?: string
  icon?: string
  isPublic: boolean
  isHierarchical: boolean
  hasArchive: boolean
  supportsTitle: boolean
  supportsContent: boolean
  supportsExcerpt: boolean
  supportsThumbnail: boolean
  supportsComments: boolean
  supportsRevisions: boolean
  supportsPageBuilder: boolean
  supportsCustomFields: boolean
  menuPosition?: number
  capabilities?: Record<string, any>
  taxonomies: string[]
  customFields: any[]
  templates: string[]
  archiveTemplate?: string
  singleTemplate?: string
  rewriteSlug?: string
  rewriteWithFront: boolean
  queryVar?: string
  canExport: boolean
  showInRest: boolean
  restBase?: string
  restControllerClass?: string
  isSystem: boolean
  isActive: boolean
  registeredBy?: string
  createdAt: Date
  updatedAt: Date
}

export interface CustomFieldGroup {
  id: string
  title: string
  key: string
  description?: string
  position: 'normal' | 'side' | 'advanced'
  priority: 'high' | 'core' | 'default' | 'low'
  postTypes: string[]
  location: LocationRule[]
  hideOnScreen: string[]
  isActive: boolean
  menuOrder: number
  instruction?: string
  registeredBy?: string
  fields: CustomField[]
}

export interface CustomField {
  id: string
  groupId: string
  label: string
  name: string
  type: FieldType
  instructions?: string
  required: boolean
  conditionalLogic?: ConditionalLogic
  wrapper?: WrapperSettings
  defaultValue?: string
  placeholder?: string
  prepend?: string
  append?: string
  formatting?: 'none' | 'html'
  maxLength?: number
  readonly: boolean
  disabled: boolean
  choices?: Record<string, string>
  allowOther: boolean
  saveOther?: 'append' | 'replace'
  defaultOther?: string
  layout?: 'vertical' | 'horizontal'
  toggle: boolean
  allowNull: boolean
  multiple: boolean
  ui: boolean
  ajax: boolean
  returnFormat?: 'value' | 'label' | 'array' | 'object'
  libraryType?: 'all' | 'uploadedTo'
  minSize?: string
  maxSize?: string
  mimeTypes?: string
  minWidth?: number
  maxWidth?: number
  minHeight?: number
  maxHeight?: number
  previewSize?: 'thumbnail' | 'medium' | 'large' | 'full'
  insertType?: 'append' | 'prepend' | 'replace'
  buttonLabel?: string
  minRows?: number
  maxRows?: number
  subFields?: CustomField[]
  layouts?: FlexibleContentLayout[]
  min?: number
  max?: number
  collapsed?: string
  menuOrder: number
  isActive: boolean
  registeredBy?: string
}

export type FieldType = 
  | 'text'
  | 'textarea'
  | 'number'
  | 'email'
  | 'url'
  | 'password'
  | 'wysiwyg'
  | 'image'
  | 'gallery'
  | 'select'
  | 'checkbox'
  | 'radio'
  | 'button_group'
  | 'true_false'
  | 'date_picker'
  | 'color_picker'
  | 'repeater'
  | 'flexible_content'
  | 'group'
  | 'clone'
  | 'link'
  | 'post_object'
  | 'page_link'
  | 'relationship'
  | 'taxonomy'
  | 'user'
  | 'google_map'
  | 'date_time_picker'
  | 'time_picker'
  | 'range'
  | 'accordion'
  | 'tab'
  | 'message'

export interface LocationRule {
  param: string
  operator: '==' | '!=' | 'contains' | 'not_contains'
  value: string
}

export interface ConditionalLogic {
  rules: ConditionalRule[]
  operator: 'and' | 'or'
}

export interface ConditionalRule {
  field: string
  operator: '==' | '!=' | 'contains' | 'not_contains' | '>' | '<' | '>=' | '<='
  value: string
}

export interface WrapperSettings {
  width?: string
  class?: string
  id?: string
}

export interface FlexibleContentLayout {
  key: string
  name: string
  label: string
  display: 'block' | 'table' | 'row'
  subFields: CustomField[]
  min?: number
  max?: number
}

export interface TaxonomyTerm {
  id: string
  name: string
  slug: string
  description?: string
  taxonomyId: string
  parentId?: string
  count: number
  color?: string
  image?: string
  metadata?: Record<string, any>
  parent?: TaxonomyTerm
  children: TaxonomyTerm[]
}

export interface Plugin {
  id: string
  name: string
  slug: string
  version: string
  description?: string
  author?: string
  authorUri?: string
  pluginUri?: string
  textDomain?: string
  domainPath?: string
  requiresWp?: string
  requiresPhp?: string
  network: boolean
  license?: string
  licenseUri?: string
  updateUri?: string
  isActive: boolean
  isInstalled: boolean
  isSystem: boolean
  autoUpdate: boolean
  activationHooks?: PluginHook[]
  deactivationHooks?: PluginHook[]
  uninstallHooks?: PluginHook[]
  dependencies: string[]
  conflicts: string[]
  settings?: Record<string, any>
  metadata?: Record<string, any>
  installPath?: string
  mainFile?: string
}

export interface PluginHook {
  id: string
  pluginId: string
  hookName: string
  callback: string
  priority: number
  acceptedArgs: number
  isActive: boolean
}

export interface CMSContent {
  id: string
  title: string
  slug: string
  content?: string
  contentHtml?: string
  excerpt?: string
  status: 'draft' | 'published' | 'private' | 'pending' | 'trash'
  postType: string
  parentId?: string
  menuOrder: number
  featuredImage?: string
  featuredImageAlt?: string
  template?: string
  password?: string
  publishedAt?: Date
  scheduledAt?: Date
  authorId?: string
  authorName?: string
  authorEmail?: string
  seoTitle?: string
  seoDescription?: string
  seoKeywords: string[]
  ogImage?: string
  ogTitle?: string
  ogDescription?: string
  twitterCard?: string
  canonicalUrl?: string
  metaRobots?: string
  viewCount: number
  shareCount: number
  likeCount: number
  commentCount: number
  usePageBuilder: boolean
  pageBuilderData?: any
  allowComments: boolean
  allowPingbacks: boolean
  isSticky: boolean
  isFeatured: boolean
  customFields?: Record<string, any>
  metadata?: Record<string, any>
  taxonomyTerms: TaxonomyTerm[]
  customFieldValues: CustomFieldValue[]
}

export interface CustomFieldValue {
  id: string
  postId: string
  fieldId: string
  value?: string
  field: CustomField
}

export interface TemplateHierarchy {
  postType: string
  templates: string[]
  fallback: string
}

// Prisma types with relations
export type PostWithRelations = Prisma.PostGetPayload<{
  include: {
    blocks: true
    comments: true
    meta: true
    revisions: true
    taxonomyTerms: {
      include: {
        term: {
          include: {
            taxonomy: true
          }
        }
      }
    }
    customFieldValues: {
      include: {
        field: {
          include: {
            group: true
          }
        }
      }
    }
    parent: true
    children: true
    postTypeRef: true
  }
}>

export type CustomFieldGroupWithFields = Prisma.CustomFieldGroupGetPayload<{
  include: {
    fields: true
  }
}>

export type PluginWithHooks = Prisma.PluginGetPayload<{
  include: {
    hooks: true
    registrations: true
  }
}>
