/**
 * WooCommerce API Integration Library
 *
 * Supports Basic and JWT authentication methods.
 */

export type WooCommerceConfig = {
  baseUrl: string;
  consumerKey: string;
  consumerSecret: string;
  authType?: 'basic' | 'jwt';
};

export class WooCommerceAPI {
  private config: WooCommerceConfig;
  private jwtToken: string | null;

  constructor(config: WooCommerceConfig) {
    this.config = config;
    this.jwtToken = null;
  }

  /**
   * Generates the Basic Auth header using consumer key and secret.
   */
  private getBasicAuthHeader(): string {
    const credentials = `${this.config.consumerKey}:${this.config.consumerSecret}`;
    // For Node.js environments, Buffer is available.
    const encoded = Buffer.from(credentials).toString('base64');
    return `Basic ${encoded}`;
  }

  /**
   * Fetches the JWT token from the WooCommerce endpoint.
   * Assumes the endpoint is at /wp-json/jwt-auth/v1/token.
   */
  public async fetchJWTToken(): Promise<string> {
    const url = `${this.config.baseUrl}/wp-json/jwt-auth/v1/token`;
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: this.config.consumerKey,
        password: this.config.consumerSecret
      })
    });

    if (!response.ok) {
      throw new Error(`JWT Authentication failed with status: ${response.status}`);
    }

    const data = await response.json();
    if (!data.token) {
      throw new Error('JWT token not found in response');
    }

    this.jwtToken = data.token;
    return data.token;
  }

  /**
   * Executes an API request.
   * @param endpoint - API endpoint (e.g., "/wp-json/wc/v3/products")
   * @param method - HTTP method ("GET", "POST", "PUT", "DELETE")
   * @param data - Optional JSON payload for POST/PUT requests
   */
  public async request(endpoint: string, method: string = 'GET', data?: any): Promise<any> {
    const url = `${this.config.baseUrl}${endpoint}`;
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (this.config.authType === 'basic') {
      headers['Authorization'] = this.getBasicAuthHeader();
    } else if (this.config.authType === 'jwt') {
      if (!this.jwtToken) {
        await this.fetchJWTToken();
      }
      headers['Authorization'] = `Bearer ${this.jwtToken}`;
    }

    const options: RequestInit = {
      method,
      headers
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(url, options);
    if (!response.ok) {
      throw new Error(`Request to ${url} failed with status ${response.status}`);
    }
    return await response.json();
  }
}
