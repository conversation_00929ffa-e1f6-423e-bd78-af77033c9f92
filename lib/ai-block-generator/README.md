# AI Block Generator Module

A production-ready AI-powered system for generating, optimizing, and managing page builder blocks using the Vercel AI SDK.

## Features

### 🤖 AI Block Generation
- Generate individual blocks based on natural language descriptions
- Create complete page layouts with multiple coordinated blocks
- Support for all block types (hero, text, image, button, product-grid, etc.)
- Intelligent configuration based on context and requirements

### ⚡ Block Optimization
- Performance optimization (lazy loading, image optimization, etc.)
- Accessibility improvements (ARIA labels, keyboard navigation, etc.)
- Mobile optimization (touch targets, responsive design, etc.)
- SEO enhancements (structured data, meta tags, etc.)
- UX improvements (micro-interactions, user guidance, etc.)

### ✍️ Content Generation
- Headlines and titles
- Descriptions and copy
- Call-to-action text
- Feature lists
- Testimonials
- Product descriptions

### 💬 AI Assistant Chat
- Interactive help and guidance
- Best practices recommendations
- Design advice and feedback
- Troubleshooting support

## Components

### AIBlockGenerator
Main component for generating blocks and layouts.

```tsx
import { AIBlockGenerator } from '@/lib/ai-block-generator'

function PageBuilder() {
  return (
    <div>
      <AIBlockGenerator />
    </div>
  )
}
```

### AIBlockOptimizer
Component for optimizing existing blocks.

```tsx
import { AIBlockOptimizer } from '@/lib/ai-block-generator'

function BlockEditor({ selectedBlockId }: { selectedBlockId: string }) {
  return (
    <AIBlockOptimizer selectedBlockId={selectedBlockId} />
  )
}
```

### AIContentGenerator
Component for generating content for blocks.

```tsx
import { AIContentGenerator } from '@/lib/ai-block-generator'

function ContentEditor() {
  return (
    <AIContentGenerator />
  )
}
```

### AIBlockChat
Interactive AI assistant for help and guidance.

```tsx
import { AIBlockChat } from '@/lib/ai-block-generator'

function HelpPanel() {
  return (
    <AIBlockChat />
  )
}
```

## Hooks

### useAIBlockGenerator
Hook for programmatic block generation.

```tsx
import { useAIBlockGenerator } from '@/lib/ai-block-generator'

function CustomGenerator() {
  const {
    generateBlock,
    generateLayout,
    generatedBlocks,
    addGeneratedBlock,
    isGenerating,
  } = useAIBlockGenerator({
    onBlockGenerated: (block) => {
      console.log('Generated:', block)
    }
  })

  const handleGenerate = async () => {
    await generateBlock('hero', 'Create a hero section for an e-commerce store')
  }

  return (
    <button onClick={handleGenerate} disabled={isGenerating}>
      Generate Hero Block
    </button>
  )
}
```

### useAIBlockOptimizer
Hook for block optimization.

```tsx
import { useAIBlockOptimizer } from '@/lib/ai-block-generator'

function BlockOptimizer({ block }: { block: any }) {
  const {
    optimizeBlock,
    optimizationResults,
    applyOptimization,
    isOptimizing,
  } = useAIBlockOptimizer()

  const handleOptimize = async () => {
    await optimizeBlock(block, 'performance', 'Improve loading speed')
  }

  return (
    <div>
      <button onClick={handleOptimize} disabled={isOptimizing}>
        Optimize for Performance
      </button>
      {optimizationResults && (
        <button onClick={applyOptimization}>
          Apply Optimization
        </button>
      )}
    </div>
  )
}
```

### useAIContentGenerator
Hook for content generation.

```tsx
import { useAIContentGenerator } from '@/lib/ai-block-generator'

function ContentGenerator() {
  const {
    generateContent,
    generatedContent,
    isGenerating,
  } = useAIContentGenerator()

  const handleGenerate = async () => {
    await generateContent(
      'headline',
      'E-commerce clothing store',
      'professional',
      'medium',
      ['fashion', 'quality', 'style']
    )
  }

  return (
    <div>
      <button onClick={handleGenerate} disabled={isGenerating}>
        Generate Headline
      </button>
      {generatedContent && (
        <p>{generatedContent.content}</p>
      )}
    </div>
  )
}
```

### useAIBlockChat
Hook for AI assistant functionality.

```tsx
import { useAIBlockChat } from '@/lib/ai-block-generator'

function AIAssistant() {
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    askQuestion,
    isLoading,
  } = useAIBlockChat()

  const handleQuickQuestion = () => {
    askQuestion('How can I improve my page conversion rate?')
  }

  return (
    <div>
      <div>
        {messages.map(message => (
          <div key={message.id}>
            {message.content}
          </div>
        ))}
      </div>
      <form onSubmit={handleSubmit}>
        <input
          value={input}
          onChange={handleInputChange}
          placeholder="Ask me anything..."
        />
        <button type="submit" disabled={isLoading}>
          Send
        </button>
      </form>
      <button onClick={handleQuickQuestion}>
        Quick Question
      </button>
    </div>
  )
}
```

## API Routes

The module includes production-ready API routes:

- `/api/ai-blocks/generate` - Main AI generation endpoint with tool calling
- `/api/ai-blocks/chat` - Chat interface for AI assistance

## Configuration

### Environment Variables

```env
# Required for OpenAI integration
OPENAI_API_KEY=your_openai_api_key

# Optional: Custom model configuration
AI_MODEL=gpt-4o
AI_MAX_TOKENS=1000
AI_TEMPERATURE=0.7
```

### Dependencies

The module requires these dependencies:

```json
{
  "ai": "^3.0.0",
  "@ai-sdk/react": "^0.0.0",
  "@ai-sdk/openai": "^0.0.0",
  "zod": "^3.22.0"
}
```

## Advanced Usage

### Custom Tools

You can use the AI tools directly for custom implementations:

```tsx
import { generateBlockTool, optimizeBlockTool } from '@/lib/ai-block-generator'

// Use tools in custom AI workflows
const result = await generateBlockTool.execute({
  blockType: 'hero',
  requirements: 'Modern hero section with CTA',
  style: 'minimal'
})
```

### Analysis Functions

Use the analysis functions for custom optimization logic:

```tsx
import { 
  analyzeAccessibilityScore,
  generatePerformanceSuggestions 
} from '@/lib/ai-block-generator'

const accessibilityScore = analyzeAccessibilityScore(blocks)
const suggestions = generatePerformanceSuggestions(blocks, 'improve loading speed')
```

## Best Practices

1. **Error Handling**: Always implement proper error handling for AI operations
2. **Loading States**: Show loading indicators during AI generation
3. **User Feedback**: Provide clear feedback on AI operations
4. **Validation**: Validate AI-generated content before applying
5. **Fallbacks**: Implement fallbacks for when AI services are unavailable

## Production Considerations

- **Rate Limiting**: Implement rate limiting for AI API calls
- **Caching**: Cache frequently generated content
- **Monitoring**: Monitor AI usage and costs
- **Security**: Validate and sanitize all AI-generated content
- **Performance**: Use streaming responses for better UX

## License

This module is part of the page builder system and follows the same license terms.
