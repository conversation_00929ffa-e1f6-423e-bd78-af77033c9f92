import { z } from 'zod'
import React from 'react'

// Enhanced Block Schema with dynamic capabilities
export const DynamicBlockSchema = z.object({
  id: z.string(),
  type: z.string(),
  version: z.string().default('1.0'),
  
  // Core configuration
  configuration: z.record(z.any()),
  styling: z.record(z.any()).optional(),
  responsive: z.record(z.any()).optional(),
  accessibility: z.record(z.any()).optional(),
  
  // Dynamic features
  jsx: z.string().optional(), // Custom JSX template
  logic: z.string().optional(), // JavaScript logic
  events: z.record(z.string()).optional(), // Event handlers
  dependencies: z.array(z.string()).optional(), // External dependencies
  
  // AI metadata
  aiGenerated: z.boolean().optional(),
  aiPrompt: z.string().optional(),
  aiVersion: z.string().optional(),
  lastOptimized: z.string().optional(),
  
  // Execution context
  context: z.record(z.any()).optional(),
  state: z.record(z.any()).optional(),
  
  // Security and validation
  sandbox: z.boolean().default(true),
  allowedAPIs: z.array(z.string()).optional(),
  permissions: z.record(z.boolean()).optional(),
})

export type DynamicBlockData = z.infer<typeof DynamicBlockSchema>

// Block execution context
export interface BlockExecutionContext {
  blockId: string
  props: Record<string, any>
  state: Record<string, any>
  setState: (newState: Record<string, any>) => void
  emit: (event: string, data?: any) => void
  api: BlockAPI
  utils: BlockUtils
}

// Block API interface
export interface BlockAPI {
  // Data operations
  getData: (key: string) => Promise<any>
  setData: (key: string, value: any) => Promise<void>
  
  // HTTP requests (sandboxed)
  fetch: (url: string, options?: RequestInit) => Promise<Response>
  
  // Local storage (scoped to block)
  getStorage: (key: string) => string | null
  setStorage: (key: string, value: string) => void
  
  // Event system
  on: (event: string, handler: Function) => void
  off: (event: string, handler: Function) => void
  emit: (event: string, data?: any) => void
  
  // UI utilities
  showToast: (message: string, type?: 'success' | 'error' | 'warning' | 'info') => void
  showModal: (content: React.ReactNode, options?: any) => void
  
  // Analytics
  track: (event: string, properties?: Record<string, any>) => void
}

// Block utilities
export interface BlockUtils {
  // String utilities
  formatText: (text: string, format: string) => string
  truncate: (text: string, length: number) => string
  
  // Date utilities
  formatDate: (date: Date | string, format: string) => string
  timeAgo: (date: Date | string) => string
  
  // Number utilities
  formatNumber: (num: number, options?: Intl.NumberFormatOptions) => string
  formatCurrency: (amount: number, currency?: string) => string
  
  // Validation
  validate: (data: any, schema: z.ZodSchema) => { success: boolean; errors?: string[] }
  
  // Color utilities
  hexToRgb: (hex: string) => { r: number; g: number; b: number } | null
  rgbToHex: (r: number, g: number, b: number) => string
  
  // CSS utilities
  cn: (...classes: string[]) => string // className utility
  
  // Animation utilities
  animate: (element: HTMLElement, keyframes: Keyframe[], options?: KeyframeAnimationOptions) => Animation
}

// Event system for blocks
export class BlockEventSystem {
  private listeners = new Map<string, Set<Function>>()
  private globalListeners = new Map<string, Set<Function>>()

  // Block-scoped events
  on(blockId: string, event: string, handler: Function): void {
    const key = `${blockId}:${event}`
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set())
    }
    this.listeners.get(key)!.add(handler)
  }

  off(blockId: string, event: string, handler: Function): void {
    const key = `${blockId}:${event}`
    const handlers = this.listeners.get(key)
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.listeners.delete(key)
      }
    }
  }

  emit(blockId: string, event: string, data?: any): void {
    // Emit block-scoped event
    const key = `${blockId}:${event}`
    const handlers = this.listeners.get(key)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`Error in event handler for ${key}:`, error)
        }
      })
    }

    // Emit global event
    const globalHandlers = this.globalListeners.get(event)
    if (globalHandlers) {
      globalHandlers.forEach(handler => {
        try {
          handler({ blockId, data })
        } catch (error) {
          console.error(`Error in global event handler for ${event}:`, error)
        }
      })
    }
  }

  // Global events (cross-block communication)
  onGlobal(event: string, handler: Function): void {
    if (!this.globalListeners.has(event)) {
      this.globalListeners.set(event, new Set())
    }
    this.globalListeners.get(event)!.add(handler)
  }

  offGlobal(event: string, handler: Function): void {
    const handlers = this.globalListeners.get(event)
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.globalListeners.delete(event)
      }
    }
  }

  // Cleanup for block
  cleanup(blockId: string): void {
    const keysToDelete: string[] = []
    for (const key of this.listeners.keys()) {
      if (key.startsWith(`${blockId}:`)) {
        keysToDelete.push(key)
      }
    }
    keysToDelete.forEach(key => this.listeners.delete(key))
  }
}

// Sandbox for safe code execution
export class BlockSandbox {
  private allowedGlobals = new Set([
    'console', 'Math', 'Date', 'JSON', 'Object', 'Array', 'String', 'Number', 'Boolean',
    'parseInt', 'parseFloat', 'isNaN', 'isFinite', 'encodeURIComponent', 'decodeURIComponent'
  ])

  private allowedAPIs = new Set([
    'fetch', 'localStorage', 'sessionStorage', 'setTimeout', 'clearTimeout',
    'setInterval', 'clearInterval'
  ])

  execute(code: string, context: BlockExecutionContext, permissions: Record<string, boolean> = {}): any {
    try {
      // Create restricted global scope
      const restrictedGlobals: Record<string, any> = {}
      
      // Add allowed globals
      this.allowedGlobals.forEach(global => {
        if (typeof window !== 'undefined' && (window as any)[global]) {
          restrictedGlobals[global] = (window as any)[global]
        }
      })

      // Add context APIs if permitted
      if (permissions.allowFetch !== false) {
        restrictedGlobals.fetch = context.api.fetch
      }
      
      if (permissions.allowStorage !== false) {
        restrictedGlobals.localStorage = {
          getItem: context.api.getStorage,
          setItem: context.api.setStorage,
        }
      }

      // Add block context
      restrictedGlobals.props = context.props
      restrictedGlobals.state = context.state
      restrictedGlobals.setState = context.setState
      restrictedGlobals.emit = context.emit
      restrictedGlobals.api = context.api
      restrictedGlobals.utils = context.utils

      // Create function with restricted scope
      const func = new Function(
        ...Object.keys(restrictedGlobals),
        `"use strict"; ${code}`
      )

      // Execute with restricted globals
      return func(...Object.values(restrictedGlobals))
    } catch (error) {
      console.error('Sandbox execution error:', error)
      throw new Error(`Code execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  validateCode(code: string): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    // Check for dangerous patterns
    const dangerousPatterns = [
      /eval\s*\(/,
      /Function\s*\(/,
      /window\./,
      /document\./,
      /global\./,
      /process\./,
      /require\s*\(/,
      /import\s+/,
      /export\s+/,
      /__proto__/,
      /constructor/,
      /prototype/,
    ]

    dangerousPatterns.forEach((pattern, index) => {
      if (pattern.test(code)) {
        errors.push(`Dangerous pattern detected: ${pattern.source}`)
      }
    })

    // Basic syntax validation
    try {
      new Function(code)
    } catch (error) {
      errors.push(`Syntax error: ${error instanceof Error ? error.message : 'Invalid syntax'}`)
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }
}

// State management for blocks
export class BlockStateManager {
  private states = new Map<string, Record<string, any>>()
  private subscribers = new Map<string, Set<Function>>()

  getState(blockId: string): Record<string, any> {
    return this.states.get(blockId) || {}
  }

  setState(blockId: string, newState: Record<string, any>): void {
    const currentState = this.getState(blockId)
    const updatedState = { ...currentState, ...newState }
    this.states.set(blockId, updatedState)
    
    // Notify subscribers
    const blockSubscribers = this.subscribers.get(blockId)
    if (blockSubscribers) {
      blockSubscribers.forEach(callback => {
        try {
          callback(updatedState, currentState)
        } catch (error) {
          console.error(`Error in state subscriber for block ${blockId}:`, error)
        }
      })
    }
  }

  subscribe(blockId: string, callback: Function): () => void {
    if (!this.subscribers.has(blockId)) {
      this.subscribers.set(blockId, new Set())
    }
    this.subscribers.get(blockId)!.add(callback)

    // Return unsubscribe function
    return () => {
      const blockSubscribers = this.subscribers.get(blockId)
      if (blockSubscribers) {
        blockSubscribers.delete(callback)
        if (blockSubscribers.size === 0) {
          this.subscribers.delete(blockId)
        }
      }
    }
  }

  cleanup(blockId: string): void {
    this.states.delete(blockId)
    this.subscribers.delete(blockId)
  }
}

// Main Block Engine
export class BlockEngine {
  private eventSystem = new BlockEventSystem()
  private sandbox = new BlockSandbox()
  private stateManager = new BlockStateManager()
  private renderCache = new Map<string, React.ReactElement>()

  constructor() {
    // Initialize engine
  }

  // Validate block data
  validateBlock(blockData: any): { valid: boolean; errors: string[] } {
    try {
      DynamicBlockSchema.parse(blockData)
      
      const errors: string[] = []
      
      // Validate custom logic if present
      if (blockData.logic) {
        const codeValidation = this.sandbox.validateCode(blockData.logic)
        if (!codeValidation.valid) {
          errors.push(...codeValidation.errors)
        }
      }
      
      return { valid: errors.length === 0, errors }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          valid: false,
          errors: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
        }
      }
      return {
        valid: false,
        errors: [error instanceof Error ? error.message : 'Unknown validation error']
      }
    }
  }

  // Get event system
  getEventSystem(): BlockEventSystem {
    return this.eventSystem
  }

  // Get state manager
  getStateManager(): BlockStateManager {
    return this.stateManager
  }

  // Cleanup block resources
  cleanup(blockId: string): void {
    this.eventSystem.cleanup(blockId)
    this.stateManager.cleanup(blockId)
    this.renderCache.delete(blockId)
  }

  // Clear all caches
  clearCache(): void {
    this.renderCache.clear()
  }
}

// Export singleton instance
export const blockEngine = new BlockEngine()
