'use client'

import React, { createElement, Fragment, useMemo, useCallback } from 'react'
import { z } from 'zod'

// Import ALL Shadcn components for dynamic rendering
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Slider } from '@/components/ui/slider'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { Calendar } from '@/components/ui/calendar'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Command, CommandDialog, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, CommandSeparator, CommandShortcut } from '@/components/ui/command'
import { ContextMenu, ContextMenuCheckboxItem, ContextMenuContent, ContextMenuItem, ContextMenuLabel, ContextMenuRadioGroup, ContextMenuRadioItem, ContextMenuSeparator, ContextMenuShortcut, ContextMenuSub, ContextMenuSubContent, ContextMenuSubTrigger, ContextMenuTrigger } from '@/components/ui/context-menu'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuPortal, DropdownMenuRadioGroup, DropdownMenuRadioItem, DropdownMenuSeparator, DropdownMenuShortcut, DropdownMenuSub, DropdownMenuSubContent, DropdownMenuSubTrigger, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card'
import { Menubar, MenubarCheckboxItem, MenubarContent, MenubarItem, MenubarLabel, MenubarMenu, MenubarRadioGroup, MenubarRadioItem, MenubarSeparator, MenubarShortcut, MenubarSub, MenubarSubContent, MenubarSubTrigger, MenubarTrigger } from '@/components/ui/menubar'
import { NavigationMenu, NavigationMenuContent, NavigationMenuIndicator, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuTrigger, NavigationMenuViewport } from '@/components/ui/navigation-menu'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Sheet, SheetContent, SheetDescription, SheetFooter, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { Skeleton } from '@/components/ui/skeleton'
import { Table, TableBody, TableCaption, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Toast, ToastAction, ToastClose, ToastDescription, ToastProvider, ToastTitle, ToastViewport } from '@/components/ui/toast'
import { Toggle } from '@/components/ui/toggle'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

// Import Lucide icons for dynamic rendering
import * as LucideIcons from 'lucide-react'

// Component registry for safe dynamic rendering - ALL SHADCN COMPONENTS
const COMPONENT_REGISTRY = {
  // Core Shadcn UI Components
  Button,
  Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter,
  Input, Label, Textarea,
  Badge, Progress, Separator, Switch,
  Tabs, TabsContent, TabsList, TabsTrigger,
  ScrollArea,
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue,

  // Form Components
  Checkbox,
  RadioGroup, RadioGroupItem,
  Slider,
  Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage,

  // Display Components
  Avatar, AvatarFallback, AvatarImage,
  Alert, AlertDescription, AlertTitle,
  AspectRatio,
  Calendar,
  Skeleton,

  // Layout Components
  Collapsible, CollapsibleContent, CollapsibleTrigger,

  // Navigation Components
  NavigationMenu, NavigationMenuContent, NavigationMenuIndicator,
  NavigationMenuItem, NavigationMenuLink, NavigationMenuList,
  NavigationMenuTrigger, NavigationMenuViewport,
  Menubar, MenubarCheckboxItem, MenubarContent, MenubarItem, MenubarLabel,
  MenubarMenu, MenubarRadioGroup, MenubarRadioItem, MenubarSeparator,
  MenubarShortcut, MenubarSub, MenubarSubContent, MenubarSubTrigger, MenubarTrigger,

  // Overlay Components
  Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger,
  AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent,
  AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger,
  Sheet, SheetContent, SheetDescription, SheetFooter, SheetHeader, SheetTitle, SheetTrigger,
  Popover, PopoverContent, PopoverTrigger,
  HoverCard, HoverCardContent, HoverCardTrigger,
  Tooltip, TooltipContent, TooltipProvider, TooltipTrigger,

  // Menu Components
  DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuLabel, DropdownMenuPortal, DropdownMenuRadioGroup, DropdownMenuRadioItem,
  DropdownMenuSeparator, DropdownMenuShortcut, DropdownMenuSub, DropdownMenuSubContent,
  DropdownMenuSubTrigger, DropdownMenuTrigger,
  ContextMenu, ContextMenuCheckboxItem, ContextMenuContent, ContextMenuItem,
  ContextMenuLabel, ContextMenuRadioGroup, ContextMenuRadioItem, ContextMenuSeparator,
  ContextMenuShortcut, ContextMenuSub, ContextMenuSubContent, ContextMenuSubTrigger, ContextMenuTrigger,

  // Command Components
  Command, CommandDialog, CommandEmpty, CommandGroup, CommandInput,
  CommandItem, CommandList, CommandSeparator, CommandShortcut,

  // Data Display Components
  Table, TableBody, TableCaption, TableCell, TableFooter, TableHead, TableHeader, TableRow,

  // Feedback Components
  Toast, ToastAction, ToastClose, ToastDescription, ToastProvider, ToastTitle, ToastViewport,

  // Toggle Components
  Toggle,
  ToggleGroup, ToggleGroupItem,

  // HTML Elements
  div: 'div', span: 'span', p: 'p',
  h1: 'h1', h2: 'h2', h3: 'h3', h4: 'h4', h5: 'h5', h6: 'h6',
  img: 'img', a: 'a',
  ul: 'ul', ol: 'ol', li: 'li',
  section: 'section', article: 'article', header: 'header', footer: 'footer', nav: 'nav', main: 'main',
  form: 'form', fieldset: 'fieldset', legend: 'legend',
  table: 'table', thead: 'thead', tbody: 'tbody', tfoot: 'tfoot', tr: 'tr', td: 'td', th: 'th',

  // React built-ins
  Fragment,

  // Lucide Icons (dynamically added)
  ...Object.fromEntries(
    Object.entries(LucideIcons).map(([name, component]) => [name, component])
  ),
}

// JSX AST node types
interface JSXNode {
  type: 'element' | 'text' | 'expression'
  tag?: string
  props?: Record<string, any>
  children?: JSXNode[]
  content?: string
  expression?: string
}

// Props schema for validation (exported for external use)
export const PropsSchema = z.record(z.union([
  z.string(),
  z.number(),
  z.boolean(),
  z.array(z.any()),
  z.record(z.any()),
  z.function(),
]))

export class JSXParser {
  private componentRegistry: Record<string, any>
  private allowedProps: Set<string>
  private eventHandlers: Record<string, Function>

  constructor(
    customComponents: Record<string, any> = {},
    eventHandlers: Record<string, Function> = {}
  ) {
    this.componentRegistry = { ...COMPONENT_REGISTRY, ...customComponents }
    this.eventHandlers = eventHandlers
    
    // Define allowed props for security
    this.allowedProps = new Set([
      'className', 'style', 'id', 'key', 'ref',
      'onClick', 'onChange', 'onSubmit', 'onFocus', 'onBlur',
      'onMouseEnter', 'onMouseLeave', 'onKeyDown', 'onKeyUp',
      'disabled', 'placeholder', 'value', 'defaultValue',
      'type', 'name', 'required', 'min', 'max', 'step',
      'src', 'alt', 'href', 'target', 'rel',
      'variant', 'size', 'asChild', 'children',
      'title', 'description', 'content', 'label',
      'checked', 'defaultChecked', 'orientation',
      'direction', 'side', 'align', 'sideOffset',
    ])
  }

  // Parse JSX string to AST
  parseJSX(jsxString: string): JSXNode {
    try {
      // Remove whitespace and normalize
      const normalized = jsxString.trim()
      
      // Simple JSX parser (for production, consider using a proper parser like @babel/parser)
      return this.parseElement(normalized)
    } catch (error) {
      throw new Error(`JSX parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // Parse a single element
  private parseElement(jsx: string): JSXNode {
    jsx = jsx.trim()
    
    // Handle text content
    if (!jsx.startsWith('<')) {
      return {
        type: 'text',
        content: jsx
      }
    }

    // Handle expressions
    if (jsx.startsWith('{') && jsx.endsWith('}')) {
      return {
        type: 'expression',
        expression: jsx.slice(1, -1)
      }
    }

    // Parse element
    const tagMatch = jsx.match(/^<(\w+)([^>]*)>/)
    if (!tagMatch) {
      throw new Error('Invalid JSX syntax')
    }

    const [, tagName, propsString] = tagMatch
    const props = this.parseProps(propsString)
    
    // Handle self-closing tags
    if (jsx.endsWith('/>')) {
      return {
        type: 'element',
        tag: tagName,
        props,
        children: []
      }
    }

    // Find closing tag
    const closingTag = `</${tagName}>`
    const closingIndex = jsx.lastIndexOf(closingTag)
    
    if (closingIndex === -1) {
      throw new Error(`Missing closing tag for <${tagName}>`)
    }

    // Extract children
    const childrenString = jsx.slice(tagMatch[0].length, closingIndex)
    const children = this.parseChildren(childrenString)

    return {
      type: 'element',
      tag: tagName,
      props,
      children
    }
  }

  // Parse props from string
  private parseProps(propsString: string): Record<string, any> {
    const props: Record<string, any> = {}
    
    if (!propsString.trim()) {
      return props
    }

    // Simple prop parser (for production, use a proper parser)
    const propRegex = /(\w+)=(?:{([^}]+)}|"([^"]+)"|'([^']+)')/g
    let match

    while ((match = propRegex.exec(propsString)) !== null) {
      const [, propName, expression, doubleQuoted, singleQuoted] = match
      
      // Security check
      if (!this.allowedProps.has(propName)) {
        console.warn(`Prop "${propName}" is not allowed and will be ignored`)
        continue
      }

      if (expression) {
        // Handle expressions
        try {
          props[propName] = this.evaluateExpression(expression)
        } catch (error) {
          console.warn(`Failed to evaluate expression for prop "${propName}":`, error)
        }
      } else {
        // Handle string values
        props[propName] = doubleQuoted || singleQuoted
      }
    }

    return props
  }

  // Parse children elements
  private parseChildren(childrenString: string): JSXNode[] {
    if (!childrenString.trim()) {
      return []
    }

    const children: JSXNode[] = []
    let current = ''
    let inExpression = false
    
    for (let i = 0; i < childrenString.length; i++) {
      const char = childrenString[i]
      
      if (char === '{' && !inExpression) {
        if (current.trim()) {
          children.push({
            type: 'text',
            content: current.trim()
          })
          current = ''
        }
        inExpression = true
        current += char
      } else if (char === '}' && inExpression) {
        current += char
        inExpression = false
        children.push({
          type: 'expression',
          expression: current.slice(1, -1)
        })
        current = ''
      } else if (char === '<' && !inExpression) {
        if (current.trim()) {
          children.push({
            type: 'text',
            content: current.trim()
          })
          current = ''
        }
        
        // Find the complete element
        let elementEnd = i
        let elementDepth = 0
        
        while (elementEnd < childrenString.length) {
          if (childrenString[elementEnd] === '<') {
            elementDepth++
          } else if (childrenString[elementEnd] === '>') {
            elementDepth--
            if (elementDepth === 0) {
              break
            }
          }
          elementEnd++
        }
        
        const element = childrenString.slice(i, elementEnd + 1)
        children.push(this.parseElement(element))
        i = elementEnd
      } else {
        current += char
      }
    }
    
    if (current.trim()) {
      children.push({
        type: 'text',
        content: current.trim()
      })
    }

    return children
  }

  // Evaluate JavaScript expressions safely
  private evaluateExpression(expression: string): any {
    try {
      // Simple expression evaluator (for production, use a proper evaluator)
      // This is a basic implementation - in production, use a proper sandbox
      
      // Handle common patterns
      if (expression === 'true') return true
      if (expression === 'false') return false
      if (expression === 'null') return null
      if (expression === 'undefined') return undefined
      
      // Handle numbers
      if (/^\d+(\.\d+)?$/.test(expression)) {
        return parseFloat(expression)
      }
      
      // Handle strings
      if (/^["'].*["']$/.test(expression)) {
        return expression.slice(1, -1)
      }
      
      // Handle arrays
      if (expression.startsWith('[') && expression.endsWith(']')) {
        return JSON.parse(expression)
      }
      
      // Handle objects
      if (expression.startsWith('{') && expression.endsWith('}')) {
        return JSON.parse(expression)
      }
      
      // Handle event handlers
      if (expression.startsWith('handleEvent(') || expression.includes('()')) {
        const eventName = expression.replace(/[()]/g, '').replace('handleEvent', '')
        return this.eventHandlers[eventName] || (() => {})
      }
      
      return expression
    } catch (error) {
      console.warn('Expression evaluation failed:', error)
      return expression
    }
  }

  // Render JSX AST to React elements
  renderAST(node: JSXNode, key?: string | number): React.ReactNode {
    switch (node.type) {
      case 'text':
        return node.content

      case 'expression':
        try {
          return this.evaluateExpression(node.expression || '')
        } catch (error) {
          console.warn('Expression rendering failed:', error)
          return `{${node.expression}}`
        }

      case 'element':
        const Component = this.componentRegistry[node.tag || '']
        
        if (!Component) {
          console.warn(`Component "${node.tag}" not found in registry`)
          return null
        }

        const props = { ...node.props }
        if (key !== undefined) {
          props.key = key
        }

        // Render children
        const children = node.children?.map((child, index) => 
          this.renderAST(child, index)
        )

        // Handle event props
        Object.keys(props).forEach(propName => {
          if (propName.startsWith('on') && typeof props[propName] === 'string') {
            const eventHandler = this.eventHandlers[props[propName]]
            if (eventHandler) {
              props[propName] = eventHandler
            }
          }
        })

        return createElement(Component, props, ...children || [])

      default:
        return null
    }
  }

  // Main render method
  render(jsxString: string): React.ReactNode {
    try {
      const ast = this.parseJSX(jsxString)
      return this.renderAST(ast)
    } catch (error) {
      console.error('JSX rendering failed:', error)
      return (
        <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
          <p className="text-red-600 font-medium">JSX Rendering Error</p>
          <p className="text-red-500 text-sm mt-1">
            {error instanceof Error ? error.message : 'Unknown error'}
          </p>
          <details className="mt-2">
            <summary className="text-red-600 text-xs cursor-pointer">JSX Source</summary>
            <pre className="text-xs bg-red-100 p-2 rounded mt-1 overflow-auto">
              {jsxString}
            </pre>
          </details>
        </div>
      )
    }
  }

  // Validate JSX string
  validate(jsxString: string): { valid: boolean; errors: string[] } {
    try {
      this.parseJSX(jsxString)
      return { valid: true, errors: [] }
    } catch (error) {
      return {
        valid: false,
        errors: [error instanceof Error ? error.message : 'Unknown validation error']
      }
    }
  }

  // Add custom component to registry
  addComponent(name: string, component: any): void {
    this.componentRegistry[name] = component
  }

  // Add event handler
  addEventHandler(name: string, handler: Function): void {
    this.eventHandlers[name] = handler
  }

  // Get available components
  getAvailableComponents(): string[] {
    return Object.keys(this.componentRegistry)
  }
}

// React hook for JSX parsing
export function useJSXParser(
  customComponents: Record<string, any> = {},
  eventHandlers: Record<string, Function> = {}
) {
  const parser = useMemo(
    () => new JSXParser(customComponents, eventHandlers),
    [customComponents, eventHandlers]
  )

  const render = useCallback(
    (jsxString: string) => parser.render(jsxString),
    [parser]
  )

  const validate = useCallback(
    (jsxString: string) => parser.validate(jsxString),
    [parser]
  )

  return {
    parser,
    render,
    validate,
    addComponent: parser.addComponent.bind(parser),
    addEventHandler: parser.addEventHandler.bind(parser),
    getAvailableComponents: parser.getAvailableComponents.bind(parser),
  }
}
