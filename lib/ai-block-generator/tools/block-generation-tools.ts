import { tool } from 'ai'
import { z } from 'zod'
import {
  analyzeConversionScore,
  generateConversionSuggestions,
  analyzeConversionDetails,
  analyzeAccessibilityScore,
  generateAccessibilitySuggestions,
  analyzeAccessibilityDetails,
  analyzePerformanceScore,
  generatePerformanceSuggestions,
  analyzePerformanceDetails,
  analyzeSEOScore,
  generateSEOSuggestions,
  analyzeSEODetails,
} from './analysis-functions'

// Schema for block configuration
const BlockConfigSchema = z.object({
  type: z.string().describe('The type of block to generate'),
  configuration: z.record(z.any()).describe('Block configuration object'),
  styling: z.record(z.any()).optional().describe('Block styling configuration'),
  responsive: z.record(z.any()).optional().describe('Responsive settings'),
  animation: z.record(z.any()).optional().describe('Animation settings'),
  accessibility: z.record(z.any()).optional().describe('Accessibility settings'),
})

// Schema for layout structure (used for validation)
export const LayoutSchema = z.object({
  blocks: z.array(BlockConfigSchema).describe('Array of blocks in the layout'),
  metadata: z.object({
    title: z.string().optional(),
    description: z.string().optional(),
    theme: z.string().optional(),
    pageType: z.string().optional(),
    target: z.string().optional(),
    sectionsCount: z.number().optional(),
    generatedAt: z.string().optional(),
  }).optional(),
})

// Tool for generating individual blocks
export const generateBlockTool = tool({
  description: 'Generate a single page builder block based on user requirements',
  parameters: z.object({
    blockType: z.string().describe('Type of block to generate (hero, product-grid, text, image, etc.)'),
    requirements: z.string().describe('Detailed requirements for the block'),
    context: z.string().optional().describe('Additional context about the page or section'),
    style: z.string().optional().describe('Styling preferences (modern, minimal, bold, etc.)'),
  }),
  execute: async ({ blockType, requirements, context, style }) => {
    // Get available block types
    const availableTypes = ['hero', 'text', 'image', 'button', 'spacer', 'product-grid', 'cart-widget', 'product-showcase', 'layout-container']

    if (!availableTypes.includes(blockType)) {
      throw new Error(`Block type "${blockType}" not found. Available types: ${availableTypes.join(', ')}`)
    }

    // Generate block configuration based on type and requirements
    const blockConfig = generateBlockConfiguration(blockType, requirements, context, style)

    return {
      success: true,
      block: blockConfig,
      blockType: { name: blockType, displayName: blockType },
      message: `Generated ${blockType} block successfully`
    }
  }
})

// Tool for generating complete page layouts
export const generateLayoutTool = tool({
  description: 'Generate a complete page layout with multiple blocks',
  parameters: z.object({
    pageType: z.string().describe('Type of page (landing, product, about, contact, etc.)'),
    requirements: z.string().describe('Detailed requirements for the page'),
    sections: z.array(z.string()).describe('List of sections needed (hero, features, testimonials, etc.)'),
    style: z.string().optional().describe('Overall styling theme'),
    target: z.string().optional().describe('Target audience or use case'),
  }),
  execute: async ({ pageType, requirements, sections, style, target }) => {
    const layout = generatePageLayout(pageType, requirements, sections, style, target)
    
    return {
      success: true,
      layout,
      message: `Generated ${pageType} page layout with ${layout.blocks.length} blocks`
    }
  }
})

// Tool for optimizing existing blocks
export const optimizeBlockTool = tool({
  description: 'Optimize an existing block for better performance, accessibility, or user experience',
  parameters: z.object({
    blockData: z.record(z.any()).describe('Current block configuration'),
    optimizationType: z.enum(['performance', 'accessibility', 'ux', 'seo', 'mobile']).describe('Type of optimization'),
    goals: z.string().describe('Specific optimization goals'),
  }),
  execute: async ({ blockData, optimizationType, goals }) => {
    const optimizedBlock = optimizeBlock(blockData, optimizationType, goals)
    
    return {
      success: true,
      originalBlock: blockData,
      optimizedBlock,
      improvements: getOptimizationImprovements(blockData, optimizedBlock, optimizationType),
      message: `Optimized block for ${optimizationType}`
    }
  }
})

// Tool for generating content for blocks
export const generateContentTool = tool({
  description: 'Generate content (text, copy, descriptions) for page builder blocks',
  parameters: z.object({
    contentType: z.enum(['headline', 'description', 'cta', 'features', 'testimonial', 'product-copy']).describe('Type of content to generate'),
    context: z.string().describe('Context about the business, product, or service'),
    tone: z.string().optional().describe('Tone of voice (professional, casual, friendly, etc.)'),
    length: z.enum(['short', 'medium', 'long']).optional().describe('Desired content length'),
    keywords: z.array(z.string()).optional().describe('Keywords to include'),
  }),
  execute: async ({ contentType, context, tone = 'professional', length = 'medium', keywords = [] }) => {
    const content = generateBlockContent(contentType, context, tone, length, keywords)
    
    return {
      success: true,
      content,
      contentType,
      metadata: {
        tone,
        length,
        keywords,
        wordCount: content.split(' ').length
      },
      message: `Generated ${contentType} content`
    }
  }
})

// Tool for analyzing and suggesting improvements
export const analyzePageTool = tool({
  description: 'Analyze a page layout and suggest improvements',
  parameters: z.object({
    pageData: z.record(z.any()).describe('Current page configuration'),
    analysisType: z.enum(['ux', 'conversion', 'accessibility', 'performance', 'seo']).describe('Type of analysis'),
    goals: z.string().optional().describe('Specific goals or metrics to optimize for'),
  }),
  execute: async ({ pageData, analysisType, goals }) => {
    const analysis = analyzePageLayout(pageData, analysisType, goals)
    
    return {
      success: true,
      analysis,
      suggestions: analysis.suggestions,
      score: analysis.score,
      message: `Completed ${analysisType} analysis`
    }
  }
})

// Helper functions for block generation
function generateBlockConfiguration(blockType: string, requirements: string, context?: string, style?: string) {
  const baseConfig = {
    id: `ai-block-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
    type: blockType,
    position: 0,
    isVisible: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    configuration: {},
    styling: getStyleConfiguration(style),
    responsive: getResponsiveConfiguration(),
    accessibility: getAccessibilityConfiguration(),
  }

  // Generate configuration based on block type and requirements analysis
  const parsedRequirements = parseRequirements(requirements, context)

  switch (blockType) {
    case 'hero':
      return {
        ...baseConfig,
        configuration: {
          title: parsedRequirements.title || extractMainHeading(requirements),
          subtitle: parsedRequirements.subtitle || extractSubheading(requirements),
          ctaText: parsedRequirements.ctaText || extractCallToAction(requirements),
          ctaLink: parsedRequirements.ctaLink || '#',
          backgroundType: parsedRequirements.backgroundType || inferBackgroundType(requirements, style),
          alignment: parsedRequirements.alignment || inferAlignment(requirements),
          showCta: parsedRequirements.showCta !== false,
          backgroundImage: parsedRequirements.backgroundImage,
          overlayOpacity: parsedRequirements.overlayOpacity || 0.5,
        }
      }

    case 'text':
      return {
        ...baseConfig,
        configuration: {
          content: cleanAndFormatText(requirements),
          tag: inferTextTag(requirements),
          fontSize: inferFontSize(requirements),
          textAlign: inferTextAlignment(requirements),
          maxWidth: parsedRequirements.maxWidth || '100%',
        }
      }

    case 'product-grid':
      return {
        ...baseConfig,
        configuration: {
          columns: parsedRequirements.columns || inferColumnCount(requirements),
          showFilters: parsedRequirements.showFilters !== false,
          showSorting: parsedRequirements.showSorting !== false,
          productsPerPage: parsedRequirements.productsPerPage || 12,
          filterOptions: generateFilterOptions(requirements),
          sortOptions: generateSortOptions(),
          gridGap: parsedRequirements.gridGap || '1rem',
          showPagination: parsedRequirements.showPagination !== false,
        }
      }

    case 'image':
      return {
        ...baseConfig,
        configuration: {
          src: parsedRequirements.src || '',
          alt: parsedRequirements.alt || generateAltText(requirements),
          width: parsedRequirements.width || 'auto',
          height: parsedRequirements.height || 'auto',
          objectFit: parsedRequirements.objectFit || 'cover',
          lazy: parsedRequirements.lazy !== false,
          caption: parsedRequirements.caption,
          borderRadius: parsedRequirements.borderRadius || '0.5rem',
        }
      }

    case 'button':
      return {
        ...baseConfig,
        configuration: {
          text: parsedRequirements.text || extractCallToAction(requirements),
          href: parsedRequirements.href || '#',
          variant: parsedRequirements.variant || inferButtonVariant(requirements, style),
          size: parsedRequirements.size || inferButtonSize(requirements),
          fullWidth: parsedRequirements.fullWidth || false,
          openInNewTab: parsedRequirements.openInNewTab || false,
          icon: parsedRequirements.icon,
          iconPosition: parsedRequirements.iconPosition || 'left',
        }
      }

    case 'layout-container':
      return {
        ...baseConfig,
        configuration: {
          direction: parsedRequirements.direction || 'column',
          gap: parsedRequirements.gap || '1rem',
          padding: parsedRequirements.padding || '2rem',
          maxWidth: parsedRequirements.maxWidth || '1200px',
          centerContent: parsedRequirements.centerContent !== false,
          backgroundColor: parsedRequirements.backgroundColor,
          borderRadius: parsedRequirements.borderRadius || '0.5rem',
        }
      }

    default:
      return {
        ...baseConfig,
        configuration: {
          content: requirements,
          customProperties: parsedRequirements,
        }
      }
  }
}

// Production-grade helper functions
function getStyleConfiguration(style?: string) {
  const styleConfigs = {
    modern: {
      borderRadius: '0.75rem',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      fontFamily: 'Inter, system-ui, sans-serif',
      colorScheme: 'light',
    },
    minimal: {
      borderRadius: '0.25rem',
      boxShadow: 'none',
      fontFamily: 'system-ui, sans-serif',
      colorScheme: 'light',
    },
    bold: {
      borderRadius: '0.5rem',
      boxShadow: '0 8px 25px -8px rgba(0, 0, 0, 0.25)',
      fontFamily: 'Inter, system-ui, sans-serif',
      fontWeight: '600',
    },
    elegant: {
      borderRadius: '0.5rem',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
      fontFamily: 'Georgia, serif',
      colorScheme: 'light',
    },
  }

  return styleConfigs[style as keyof typeof styleConfigs] || styleConfigs.modern
}

function getResponsiveConfiguration() {
  return {
    mobile: {
      padding: { top: '1rem', bottom: '1rem', left: '1rem', right: '1rem' },
      fontSize: '0.875rem',
      hideOnMobile: false,
    },
    tablet: {
      padding: { top: '1.5rem', bottom: '1.5rem', left: '1.5rem', right: '1.5rem' },
      fontSize: '1rem',
    },
    desktop: {
      padding: { top: '2rem', bottom: '2rem', left: '2rem', right: '2rem' },
      fontSize: '1rem',
    },
  }
}

function getAccessibilityConfiguration() {
  return {
    focusable: true,
    ariaLabel: '',
    respectMotionPreference: true,
    highContrast: false,
    keyboardNavigation: true,
  }
}

function parseRequirements(requirements: string, _context?: string) {
  const parsed: any = {}
  const lowerReq = requirements.toLowerCase()

  // Extract common patterns
  if (lowerReq.includes('center') || lowerReq.includes('centered')) {
    parsed.alignment = 'center'
    parsed.textAlign = 'center'
  }

  if (lowerReq.includes('left')) {
    parsed.alignment = 'left'
    parsed.textAlign = 'left'
  }

  if (lowerReq.includes('right')) {
    parsed.alignment = 'right'
    parsed.textAlign = 'right'
  }

  // Extract column information
  const columnMatch = requirements.match(/(\d+)\s*column/i)
  if (columnMatch) {
    parsed.columns = parseInt(columnMatch[1])
  }

  // Extract size information
  if (lowerReq.includes('large') || lowerReq.includes('big')) {
    parsed.size = 'lg'
    parsed.fontSize = '1.25rem'
  } else if (lowerReq.includes('small') || lowerReq.includes('compact')) {
    parsed.size = 'sm'
    parsed.fontSize = '0.875rem'
  }

  // Extract color information
  const colorMatch = requirements.match(/(?:color|background):\s*([#\w]+)/i)
  if (colorMatch) {
    parsed.backgroundColor = colorMatch[1]
  }

  // Extract URL patterns
  const urlMatch = requirements.match(/https?:\/\/[^\s]+/i)
  if (urlMatch) {
    parsed.src = urlMatch[0]
    parsed.href = urlMatch[0]
  }

  return parsed
}

function extractMainHeading(requirements: string): string {
  // Extract the first sentence or phrase that looks like a heading
  const sentences = requirements.split(/[.!?]/)
  const firstSentence = sentences[0]?.trim()

  if (firstSentence && firstSentence.length > 0) {
    // Capitalize first letter and clean up
    return firstSentence.charAt(0).toUpperCase() + firstSentence.slice(1)
  }

  return 'Welcome to Our Platform'
}

function extractSubheading(requirements: string): string {
  const sentences = requirements.split(/[.!?]/)
  const secondSentence = sentences[1]?.trim()

  if (secondSentence && secondSentence.length > 0) {
    return secondSentence.charAt(0).toUpperCase() + secondSentence.slice(1)
  }

  return 'Discover amazing features and capabilities'
}

function extractCallToAction(requirements: string): string {
  const ctaPatterns = [
    /(?:button|cta|call.to.action):\s*["']([^"']+)["']/i,
    /(?:click|button|action):\s*([^.,!?]+)/i,
    /(get started|learn more|sign up|contact us|buy now|shop now)/i,
  ]

  for (const pattern of ctaPatterns) {
    const match = requirements.match(pattern)
    if (match) {
      return match[1].trim()
    }
  }

  return 'Get Started'
}

function inferBackgroundType(requirements: string, style?: string): string {
  const lowerReq = requirements.toLowerCase()

  if (lowerReq.includes('gradient')) return 'gradient'
  if (lowerReq.includes('image') || lowerReq.includes('photo')) return 'image'
  if (lowerReq.includes('video')) return 'video'
  if (lowerReq.includes('solid') || lowerReq.includes('color')) return 'solid'

  // Default based on style
  if (style === 'bold' || style === 'modern') return 'gradient'
  return 'solid'
}

function inferAlignment(requirements: string): string {
  const lowerReq = requirements.toLowerCase()

  if (lowerReq.includes('center') || lowerReq.includes('centered')) return 'center'
  if (lowerReq.includes('left')) return 'left'
  if (lowerReq.includes('right')) return 'right'

  return 'center'
}

function cleanAndFormatText(text: string): string {
  return text
    .trim()
    .replace(/\s+/g, ' ')
    .replace(/([.!?])\s*([A-Z])/g, '$1 $2')
}

function inferTextTag(requirements: string): string {
  const lowerReq = requirements.toLowerCase()

  if (lowerReq.includes('heading') || lowerReq.includes('title')) {
    if (lowerReq.includes('main') || lowerReq.includes('h1')) return 'h1'
    if (lowerReq.includes('h2')) return 'h2'
    if (lowerReq.includes('h3')) return 'h3'
    return 'h2'
  }

  if (lowerReq.includes('paragraph') || lowerReq.includes('description')) return 'p'
  if (lowerReq.includes('span') || lowerReq.includes('inline')) return 'span'

  return 'p'
}

function inferFontSize(requirements: string): string {
  const lowerReq = requirements.toLowerCase()

  if (lowerReq.includes('large') || lowerReq.includes('big')) return '1.25rem'
  if (lowerReq.includes('small')) return '0.875rem'
  if (lowerReq.includes('tiny')) return '0.75rem'
  if (lowerReq.includes('huge') || lowerReq.includes('xl')) return '1.5rem'

  return '1rem'
}

function inferTextAlignment(requirements: string): string {
  return inferAlignment(requirements)
}

function inferColumnCount(requirements: string): number {
  const lowerReq = requirements.toLowerCase()

  if (lowerReq.includes('1 column') || lowerReq.includes('single column')) return 1
  if (lowerReq.includes('2 column') || lowerReq.includes('two column')) return 2
  if (lowerReq.includes('3 column') || lowerReq.includes('three column')) return 3
  if (lowerReq.includes('4 column') || lowerReq.includes('four column')) return 4
  if (lowerReq.includes('5 column') || lowerReq.includes('five column')) return 5
  if (lowerReq.includes('6 column') || lowerReq.includes('six column')) return 6

  // Extract number from text
  const match = requirements.match(/(\d+)\s*column/i)
  if (match) {
    const num = parseInt(match[1])
    return Math.min(Math.max(num, 1), 6) // Clamp between 1-6
  }

  return 3 // Default
}

function generateFilterOptions(requirements: string): string[] {
  const lowerReq = requirements.toLowerCase()
  const filters: string[] = []

  // Common e-commerce filters
  if (lowerReq.includes('category') || lowerReq.includes('categories')) {
    filters.push('category')
  }
  if (lowerReq.includes('price') || lowerReq.includes('cost')) {
    filters.push('price')
  }
  if (lowerReq.includes('brand') || lowerReq.includes('brands')) {
    filters.push('brand')
  }
  if (lowerReq.includes('size') || lowerReq.includes('sizes')) {
    filters.push('size')
  }
  if (lowerReq.includes('color') || lowerReq.includes('colors')) {
    filters.push('color')
  }
  if (lowerReq.includes('rating') || lowerReq.includes('review')) {
    filters.push('rating')
  }

  // Default filters if none specified
  if (filters.length === 0) {
    filters.push('category', 'price', 'brand')
  }

  return filters
}

function generateSortOptions(): Array<{ label: string; value: string }> {
  return [
    { label: 'Featured', value: 'featured' },
    { label: 'Price: Low to High', value: 'price_asc' },
    { label: 'Price: High to Low', value: 'price_desc' },
    { label: 'Newest', value: 'newest' },
    { label: 'Best Selling', value: 'best_selling' },
    { label: 'Customer Rating', value: 'rating' },
    { label: 'Name: A to Z', value: 'name_asc' },
    { label: 'Name: Z to A', value: 'name_desc' },
  ]
}

function generateAltText(requirements: string): string {
  const lowerReq = requirements.toLowerCase()

  if (lowerReq.includes('product')) return 'Product image'
  if (lowerReq.includes('hero') || lowerReq.includes('banner')) return 'Hero banner image'
  if (lowerReq.includes('logo')) return 'Company logo'
  if (lowerReq.includes('team') || lowerReq.includes('person')) return 'Team member photo'
  if (lowerReq.includes('feature')) return 'Feature illustration'

  return 'Image'
}

function inferButtonVariant(requirements: string, style?: string): string {
  const lowerReq = requirements.toLowerCase()

  if (lowerReq.includes('primary') || lowerReq.includes('main')) return 'default'
  if (lowerReq.includes('secondary')) return 'secondary'
  if (lowerReq.includes('outline') || lowerReq.includes('border')) return 'outline'
  if (lowerReq.includes('ghost') || lowerReq.includes('subtle')) return 'ghost'
  if (lowerReq.includes('destructive') || lowerReq.includes('danger')) return 'destructive'
  if (lowerReq.includes('link')) return 'link'

  // Default based on style
  if (style === 'bold') return 'default'
  if (style === 'minimal') return 'ghost'

  return 'default'
}

function inferButtonSize(requirements: string): string {
  const lowerReq = requirements.toLowerCase()

  if (lowerReq.includes('large') || lowerReq.includes('big')) return 'lg'
  if (lowerReq.includes('small') || lowerReq.includes('compact')) return 'sm'
  if (lowerReq.includes('icon only')) return 'icon'

  return 'default'
}

function generatePageLayout(pageType: string, requirements: string, sections: string[], style?: string, target?: string) {
  const blocks: any[] = []

  sections.forEach((section, index) => {
    const blockType = mapSectionToBlockType(section)
    const sectionRequirements = `${section} section for ${pageType} page. ${requirements}`
    const blockConfig = generateBlockConfiguration(blockType, sectionRequirements, requirements, style)
    blockConfig.position = index
    blocks.push(blockConfig)
  })

  return {
    blocks,
    metadata: {
      title: `${pageType.charAt(0).toUpperCase() + pageType.slice(1)} Page`,
      description: requirements,
      theme: style || 'modern',
      target,
      pageType,
      sectionsCount: sections.length,
      generatedAt: new Date().toISOString(),
    }
  }
}

function optimizeBlock(blockData: any, optimizationType: string, goals: string) {
  // Clone the block data
  const optimized = JSON.parse(JSON.stringify(blockData))

  switch (optimizationType) {
    case 'accessibility':
      optimized.accessibility = {
        ...optimized.accessibility,
        ariaLabel: generateAriaLabel(blockData),
        focusable: true,
        respectMotionPreference: true,
        highContrast: goals.toLowerCase().includes('contrast'),
        keyboardNavigation: true,
        screenReaderOptimized: true,
      }

      // Add semantic HTML improvements
      if (blockData.type === 'text' && blockData.configuration?.content) {
        optimized.configuration.semanticMarkup = true
      }

      // Improve color contrast if needed
      if (goals.toLowerCase().includes('contrast')) {
        optimized.styling = {
          ...optimized.styling,
          colorContrast: 'high',
          focusIndicator: 'enhanced',
        }
      }
      break

    case 'performance':
      optimized.configuration = {
        ...optimized.configuration,
        lazy: true,
        preload: false,
      }

      if (blockData.type === 'image') {
        optimized.configuration = {
          ...optimized.configuration,
          loading: 'lazy',
          decoding: 'async',
          fetchPriority: 'low',
          sizes: 'auto',
        }
      }

      if (blockData.type === 'product-grid') {
        optimized.configuration = {
          ...optimized.configuration,
          virtualScrolling: true,
          imageOptimization: true,
          lazyLoadProducts: true,
        }
      }

      // Add performance monitoring
      optimized.performance = {
        trackMetrics: true,
        optimizeImages: true,
        minifyCSS: true,
      }
      break

    case 'mobile':
      optimized.responsive = {
        ...optimized.responsive,
        mobile: {
          padding: { top: '1rem', bottom: '1rem', left: '0.75rem', right: '0.75rem' },
          fontSize: '0.875rem',
          hideOnMobile: false,
          touchOptimized: true,
          swipeGestures: blockData.type === 'product-grid',
        },
        tablet: {
          padding: { top: '1.5rem', bottom: '1.5rem', left: '1rem', right: '1rem' },
          fontSize: '1rem',
        },
      }

      // Mobile-specific optimizations
      if (blockData.type === 'hero') {
        optimized.configuration = {
          ...optimized.configuration,
          mobileLayout: 'stacked',
          mobileImageOptimization: true,
        }
      }

      if (blockData.type === 'button') {
        optimized.configuration = {
          ...optimized.configuration,
          minTouchTarget: '44px',
          mobileFullWidth: goals.toLowerCase().includes('full width'),
        }
      }
      break

    case 'seo':
      optimized.seo = {
        structuredData: true,
        semanticHTML: true,
        metaOptimized: true,
      }

      if (blockData.type === 'text') {
        optimized.configuration = {
          ...optimized.configuration,
          headingStructure: 'optimized',
          keywordDensity: 'balanced',
        }
      }

      if (blockData.type === 'image') {
        optimized.configuration = {
          ...optimized.configuration,
          alt: optimized.configuration.alt || generateSEOAltText(blockData, goals),
          title: generateSEOTitle(blockData, goals),
        }
      }
      break

    case 'ux':
      optimized.ux = {
        interactionFeedback: true,
        loadingStates: true,
        errorHandling: true,
        userGuidance: true,
      }

      if (blockData.type === 'button') {
        optimized.configuration = {
          ...optimized.configuration,
          hoverEffects: true,
          clickFeedback: true,
          loadingState: true,
        }
      }

      if (blockData.type === 'product-grid') {
        optimized.configuration = {
          ...optimized.configuration,
          quickView: true,
          wishlistIntegration: true,
          compareFeature: true,
          filterPersistence: true,
        }
      }

      // Add micro-interactions
      optimized.animations = {
        ...optimized.animations,
        entrance: 'fadeInUp',
        hover: 'subtle',
        respectMotionPreference: true,
      }
      break
  }

  return optimized
}

// Helper functions for optimization
function generateAriaLabel(blockData: any): string {
  const { type, configuration } = blockData

  switch (type) {
    case 'hero':
      return `Hero section: ${configuration?.title || 'Main banner'}`
    case 'button':
      return `Button: ${configuration?.text || 'Action button'}`
    case 'image':
      return configuration?.alt || `${type} image`
    case 'product-grid':
      return 'Product grid: Browse our products'
    case 'text':
      return `Text content: ${configuration?.content?.substring(0, 50) || 'Text block'}...`
    default:
      return `${type} block`
  }
}

function generateSEOAltText(blockData: any, goals: string): string {
  const { configuration } = blockData
  const context = goals.toLowerCase()

  if (context.includes('product')) {
    return `${configuration?.title || 'Product'} - High quality image`
  }
  if (context.includes('hero')) {
    return `${configuration?.title || 'Hero'} banner - Main page image`
  }
  if (context.includes('feature')) {
    return `Feature illustration - ${configuration?.title || 'Key feature'}`
  }

  return configuration?.alt || 'Optimized image for better SEO'
}

function generateSEOTitle(blockData: any, goals: string): string {
  const { configuration } = blockData
  const context = goals.toLowerCase()

  if (context.includes('product')) {
    return `${configuration?.title || 'Product'} | Premium Quality`
  }
  if (context.includes('hero')) {
    return `${configuration?.title || 'Welcome'} | Our Platform`
  }

  return configuration?.title || 'Optimized content'
}

function generateBlockContent(contentType: string, context: string, tone: string, length: string, keywords: string[]): string {
  // Production-grade content generation based on type, context, tone, length, and keywords
  const toneModifiers = {
    professional: { prefix: '', suffix: '', style: 'formal' },
    casual: { prefix: '', suffix: '', style: 'conversational' },
    friendly: { prefix: '', suffix: '', style: 'warm' },
    authoritative: { prefix: '', suffix: '', style: 'confident' },
    playful: { prefix: '', suffix: '', style: 'fun' },
  }

  const lengthModifiers = {
    short: { maxWords: 15, sentences: 1 },
    medium: { maxWords: 50, sentences: 2-3 },
    long: { maxWords: 150, sentences: 4-6 },
  }

  // Get tone and length configurations for content generation
  const toneConfig = toneModifiers[tone as keyof typeof toneModifiers] || toneModifiers.professional
  const currentLength = lengthModifiers[length as keyof typeof lengthModifiers] || lengthModifiers.medium

  // Incorporate keywords naturally
  const keywordPhrase = keywords.length > 0 ? keywords.slice(0, 3).join(', ') : ''

  // Apply tone-specific modifications
  const applyTone = (text: string): string => {
    switch (toneConfig.style) {
      case 'conversational':
        return text.replace(/\b(we|our)\b/gi, 'we').replace(/\.$/, '!')
      case 'confident':
        return text.replace(/\b(can|may)\b/gi, 'will').replace(/\b(help)\b/gi, 'guarantee')
      case 'fun':
        return text.replace(/\.$/, '! 🎉').replace(/\b(amazing|great)\b/gi, 'awesome')
      default:
        return text
    }
  }

  switch (contentType) {
    case 'headline':
      let headline = currentLength.maxWords <= 15
        ? `Transform Your ${context}${keywordPhrase ? ` with ${keywords[0]}` : ''}`
        : `Transform Your ${context} Experience${keywordPhrase ? ` - Featuring ${keywordPhrase}` : ''}`
      return applyTone(headline)

    case 'description':
      const baseDescription = `Discover how our innovative ${context} solutions can help you achieve your goals.`
      let description = keywords.length > 0
        ? `${baseDescription} Specializing in ${keywordPhrase}, we deliver exceptional results that exceed expectations.`
        : baseDescription
      return applyTone(description)

    case 'cta':
      const ctaOptions = {
        short: ['Get Started', 'Learn More', 'Shop Now', 'Contact Us'],
        medium: ['Get Started Today', 'Learn More About Us', 'Shop Our Collection', 'Contact Our Team'],
        long: ['Get Started with Our Solutions', 'Learn More About Our Services', 'Shop Our Premium Collection', 'Contact Our Expert Team'],
      }
      const options = ctaOptions[length as keyof typeof ctaOptions] || ctaOptions.medium
      return options[Math.floor(Math.random() * options.length)]

    case 'features':
      const features = [
        `Premium ${context} quality`,
        `Expert ${context} support`,
        `Advanced ${context} technology`,
        `Reliable ${context} performance`,
      ]

      if (keywords.length > 0) {
        features.unshift(`Industry-leading ${keywords[0]}`)
      }

      if (currentLength.maxWords <= 50) {
        return features.slice(0, 3).join(', ')
      }
      return `Key features that make our ${context} exceptional: ${features.join(', ')}.`

    case 'testimonial':
      const testimonialTemplates = [
        `"This ${context} solution exceeded our expectations and delivered amazing results."`,
        `"Outstanding ${context} service with incredible attention to detail."`,
        `"The best ${context} experience we've had. Highly recommended!"`,
        `"Professional, reliable, and effective ${context} solutions."`,
      ]

      let testimonial = testimonialTemplates[Math.floor(Math.random() * testimonialTemplates.length)]

      if (keywords.length > 0 && currentLength.maxWords > 50) {
        testimonial += ` Their expertise in ${keywords[0]} really shows.`
      }

      return testimonial

    case 'product-copy':
      let productCopy = `Premium ${context} designed for modern needs.`

      if (keywords.length > 0) {
        productCopy = `Premium ${context} featuring ${keywords[0]} technology.`
      }

      if (currentLength.maxWords > 50) {
        productCopy += ` Crafted with attention to detail and built to last, our ${context} delivers exceptional performance and value.`
      }

      if (keywords.length > 1 && currentLength.maxWords > 100) {
        productCopy += ` Experience the difference with ${keywords.slice(1).join(' and ')}.`
      }

      return productCopy

    default:
      return `High-quality ${context} content${keywordPhrase ? ` featuring ${keywordPhrase}` : ''}.`
  }
}

function analyzePageLayout(pageData: any, analysisType: string, goals?: string) {
  const { blocks = [], metadata = {} } = pageData
  const analysis = {
    score: 0,
    suggestions: [] as string[],
    details: {
      strengths: [] as string[],
      weaknesses: [] as string[],
      opportunities: [] as string[],
      metrics: {} as Record<string, any>
    }
  }

  switch (analysisType) {
    case 'ux':
      analysis.score = analyzeUXScore(blocks)
      analysis.suggestions = generateUXSuggestions(blocks, goals)
      analysis.details = analyzeUXDetails(blocks)
      break

    case 'conversion':
      analysis.score = analyzeConversionScore(blocks)
      analysis.suggestions = generateConversionSuggestions(blocks, goals)
      analysis.details = analyzeConversionDetails(blocks)
      break

    case 'accessibility':
      analysis.score = analyzeAccessibilityScore(blocks)
      analysis.suggestions = generateAccessibilitySuggestions(blocks, goals)
      analysis.details = analyzeAccessibilityDetails(blocks)
      break

    case 'performance':
      analysis.score = analyzePerformanceScore(blocks)
      analysis.suggestions = generatePerformanceSuggestions(blocks, goals)
      analysis.details = analyzePerformanceDetails(blocks)
      break

    case 'seo':
      analysis.score = analyzeSEOScore(blocks, metadata)
      analysis.suggestions = generateSEOSuggestions(blocks, metadata, goals)
      analysis.details = analyzeSEODetails(blocks, metadata)
      break

    default:
      // General analysis
      analysis.score = Math.round((
        analyzeUXScore(blocks) +
        analyzeAccessibilityScore(blocks) +
        analyzePerformanceScore(blocks)
      ) / 3)
      analysis.suggestions = [
        ...generateUXSuggestions(blocks, goals).slice(0, 2),
        ...generateAccessibilitySuggestions(blocks, goals).slice(0, 2),
        ...generatePerformanceSuggestions(blocks, goals).slice(0, 2),
      ]
      break
  }

  return analysis
}

// UX Analysis Functions
function analyzeUXScore(blocks: any[]): number {
  let score = 100

  // Check for hero block
  const hasHero = blocks.some(block => block.type === 'hero')
  if (!hasHero) score -= 15

  // Check for clear CTAs
  const ctaBlocks = blocks.filter(block =>
    block.type === 'button' ||
    (block.type === 'hero' && block.configuration?.ctaText)
  )
  if (ctaBlocks.length === 0) score -= 20
  if (ctaBlocks.length < 2) score -= 10

  // Check for content hierarchy
  const textBlocks = blocks.filter(block => block.type === 'text')
  const hasHeadings = textBlocks.some(block =>
    ['h1', 'h2', 'h3'].includes(block.configuration?.tag)
  )
  if (!hasHeadings) score -= 15

  // Check for visual balance
  if (blocks.length < 3) score -= 10
  if (blocks.length > 15) score -= 5

  return Math.max(0, Math.min(100, score))
}

function generateUXSuggestions(blocks: any[], goals?: string): string[] {
  const suggestions: string[] = []

  const hasHero = blocks.some(block => block.type === 'hero')
  if (!hasHero) {
    suggestions.push('Add a hero section to create a strong first impression')
  }

  const ctaCount = blocks.filter(block =>
    block.type === 'button' ||
    (block.type === 'hero' && block.configuration?.ctaText)
  ).length

  if (ctaCount === 0) {
    suggestions.push('Add clear call-to-action buttons to guide user actions')
  } else if (ctaCount === 1) {
    suggestions.push('Consider adding secondary CTAs to provide multiple conversion paths')
  }

  const hasProductGrid = blocks.some(block => block.type === 'product-grid')
  if (!hasProductGrid && goals?.toLowerCase().includes('ecommerce')) {
    suggestions.push('Add a product grid to showcase your offerings')
  }

  const textBlocks = blocks.filter(block => block.type === 'text')
  if (textBlocks.length > 5) {
    suggestions.push('Consider breaking up long text sections with visual elements')
  }

  suggestions.push('Ensure consistent spacing and alignment across all blocks')
  suggestions.push('Add micro-interactions to enhance user engagement')

  return suggestions.slice(0, 5)
}

function analyzeUXDetails(blocks: any[]) {
  const strengths: string[] = []
  const weaknesses: string[] = []
  const opportunities: string[] = []

  // Analyze strengths
  if (blocks.some(block => block.type === 'hero')) {
    strengths.push('Strong hero section for first impressions')
  }

  const ctaCount = blocks.filter(block => block.type === 'button').length
  if (ctaCount >= 2) {
    strengths.push('Multiple call-to-action opportunities')
  }

  // Analyze weaknesses
  if (blocks.length < 3) {
    weaknesses.push('Limited content variety')
  }

  const hasImages = blocks.some(block => block.type === 'image')
  if (!hasImages) {
    weaknesses.push('Lack of visual elements')
  }

  // Analyze opportunities
  opportunities.push('Add animations for better engagement')
  opportunities.push('Implement progressive disclosure for complex content')
  opportunities.push('Add social proof elements')

  return {
    strengths,
    weaknesses,
    opportunities,
    metrics: {
      totalBlocks: blocks.length,
      ctaCount,
      hasHero: blocks.some(block => block.type === 'hero'),
      hasImages,
    }
  }
}

// Helper functions for section mapping

function mapSectionToBlockType(section: string): string {
  const mapping: Record<string, string> = {
    hero: 'hero',
    features: 'layout-container',
    testimonials: 'layout-container',
    products: 'product-grid',
    about: 'text',
    contact: 'layout-container',
    footer: 'layout-container'
  }
  
  return mapping[section.toLowerCase()] || 'text'
}

function getOptimizationImprovements(original: any, optimized: any, _type: string): string[] {
  const improvements: string[] = []
  
  if (optimized.accessibility && !original.accessibility) {
    improvements.push('Added accessibility attributes')
  }
  
  if (optimized.responsive && !original.responsive) {
    improvements.push('Added responsive design settings')
  }
  
  if (optimized.configuration?.lazy && !original.configuration?.lazy) {
    improvements.push('Enabled lazy loading for better performance')
  }
  
  return improvements
}
