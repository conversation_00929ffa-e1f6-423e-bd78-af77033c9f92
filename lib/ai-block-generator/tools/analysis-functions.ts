// Production-grade analysis functions for page layout optimization

// Conversion Analysis Functions
export function analyzeConversionScore(blocks: any[]): number {
  let score = 100
  
  // Check for clear value proposition
  const hasHero = blocks.some(block => block.type === 'hero')
  if (!hasHero) score -= 25
  
  // Check for CTAs
  const ctaBlocks = blocks.filter(block => 
    block.type === 'button' || 
    (block.type === 'hero' && block.configuration?.ctaText)
  )
  if (ctaBlocks.length === 0) score -= 30
  if (ctaBlocks.length === 1) score -= 10
  
  // Check for social proof
  const hasSocialProof = blocks.some(block => 
    block.type === 'testimonial' || 
    (block.type === 'text' && block.configuration?.content?.includes('testimonial'))
  )
  if (!hasSocialProof) score -= 15
  
  // Check for product showcase
  const hasProducts = blocks.some(block => 
    block.type === 'product-grid' || block.type === 'product-showcase'
  )
  if (!hasProducts) score -= 10
  
  // Check for trust signals
  const hasTrustSignals = blocks.some(block =>
    block.configuration?.content?.toLowerCase().includes('guarantee') ||
    block.configuration?.content?.toLowerCase().includes('secure') ||
    block.configuration?.content?.toLowerCase().includes('certified')
  )
  if (!hasTrustSignals) score -= 10
  
  return Math.max(0, Math.min(100, score))
}

export function generateConversionSuggestions(blocks: any[], goals?: string): string[] {
  const suggestions: string[] = []
  
  const ctaCount = blocks.filter(block => 
    block.type === 'button' || 
    (block.type === 'hero' && block.configuration?.ctaText)
  ).length
  
  if (ctaCount === 0) {
    suggestions.push('Add prominent call-to-action buttons above the fold')
  } else if (ctaCount === 1) {
    suggestions.push('Add secondary CTAs to capture different user intents')
  }
  
  const hasHero = blocks.some(block => block.type === 'hero')
  if (!hasHero) {
    suggestions.push('Create a compelling hero section with clear value proposition')
  }
  
  const hasSocialProof = blocks.some(block => 
    block.type === 'testimonial' || 
    block.configuration?.content?.includes('testimonial')
  )
  if (!hasSocialProof) {
    suggestions.push('Add customer testimonials or reviews to build trust')
  }
  
  const hasUrgency = blocks.some(block =>
    block.configuration?.content?.toLowerCase().includes('limited') ||
    block.configuration?.content?.toLowerCase().includes('offer')
  )
  if (!hasUrgency && goals?.toLowerCase().includes('sales')) {
    suggestions.push('Add urgency elements like limited-time offers')
  }
  
  suggestions.push('Optimize button colors and text for higher click-through rates')
  suggestions.push('Add progress indicators for multi-step processes')
  
  return suggestions.slice(0, 5)
}

export function analyzeConversionDetails(blocks: any[]) {
  const strengths: string[] = []
  const weaknesses: string[] = []
  const opportunities: string[] = []
  
  const ctaCount = blocks.filter(block => block.type === 'button').length
  const hasHero = blocks.some(block => block.type === 'hero')
  const hasProducts = blocks.some(block => 
    block.type === 'product-grid' || block.type === 'product-showcase'
  )
  
  // Analyze strengths
  if (hasHero) {
    strengths.push('Clear value proposition in hero section')
  }
  if (ctaCount >= 2) {
    strengths.push('Multiple conversion opportunities')
  }
  if (hasProducts) {
    strengths.push('Product showcase for direct sales')
  }
  
  // Analyze weaknesses
  if (ctaCount === 0) {
    weaknesses.push('No clear call-to-action buttons')
  }
  if (!hasHero) {
    weaknesses.push('Missing compelling hero section')
  }
  
  // Analyze opportunities
  opportunities.push('A/B test different CTA button colors and text')
  opportunities.push('Add exit-intent popups for lead capture')
  opportunities.push('Implement social proof elements')
  opportunities.push('Add scarcity indicators for products')
  
  return {
    strengths,
    weaknesses,
    opportunities,
    metrics: {
      ctaCount,
      hasHero,
      hasProducts,
      conversionFunnelSteps: calculateFunnelSteps(blocks),
    }
  }
}

// Accessibility Analysis Functions
export function analyzeAccessibilityScore(blocks: any[]): number {
  let score = 100
  
  // Check for alt text on images
  const imageBlocks = blocks.filter(block => block.type === 'image')
  const imagesWithoutAlt = imageBlocks.filter(block => !block.configuration?.alt)
  if (imagesWithoutAlt.length > 0) {
    score -= (imagesWithoutAlt.length / imageBlocks.length) * 20
  }
  
  // Check for heading structure
  const textBlocks = blocks.filter(block => block.type === 'text')
  const hasProperHeadings = textBlocks.some(block => 
    ['h1', 'h2', 'h3'].includes(block.configuration?.tag)
  )
  if (!hasProperHeadings) score -= 15
  
  // Check for color contrast
  const hasHighContrast = blocks.some(block => 
    block.accessibility?.highContrast || block.styling?.colorContrast === 'high'
  )
  if (!hasHighContrast) score -= 10
  
  // Check for keyboard navigation
  const interactiveBlocks = blocks.filter(block => 
    block.type === 'button' || block.type === 'product-grid'
  )
  const keyboardAccessible = interactiveBlocks.filter(block => 
    block.accessibility?.keyboardNavigation !== false
  )
  if (keyboardAccessible.length < interactiveBlocks.length) {
    score -= 15
  }
  
  // Check for ARIA labels
  const hasAriaLabels = blocks.some(block => block.accessibility?.ariaLabel)
  if (!hasAriaLabels) score -= 10
  
  return Math.max(0, Math.min(100, score))
}

export function generateAccessibilitySuggestions(blocks: any[], _goals?: string): string[] {
  const suggestions: string[] = []
  
  const imageBlocks = blocks.filter(block => block.type === 'image')
  const imagesWithoutAlt = imageBlocks.filter(block => !block.configuration?.alt)
  if (imagesWithoutAlt.length > 0) {
    suggestions.push(`Add alt text to ${imagesWithoutAlt.length} image(s)`)
  }
  
  const textBlocks = blocks.filter(block => block.type === 'text')
  const hasProperHeadings = textBlocks.some(block => 
    ['h1', 'h2', 'h3'].includes(block.configuration?.tag)
  )
  if (!hasProperHeadings) {
    suggestions.push('Implement proper heading hierarchy (H1, H2, H3)')
  }
  
  const hasHighContrast = blocks.some(block => 
    block.styling?.colorContrast === 'high'
  )
  if (!hasHighContrast) {
    suggestions.push('Improve color contrast for better readability')
  }
  
  const hasAriaLabels = blocks.some(block => block.accessibility?.ariaLabel)
  if (!hasAriaLabels) {
    suggestions.push('Add ARIA labels for screen reader compatibility')
  }
  
  suggestions.push('Ensure all interactive elements are keyboard accessible')
  suggestions.push('Add focus indicators for better navigation')
  
  return suggestions.slice(0, 5)
}

export function analyzeAccessibilityDetails(blocks: any[]) {
  const imageBlocks = blocks.filter(block => block.type === 'image')
  const imagesWithAlt = imageBlocks.filter(block => block.configuration?.alt)
  const interactiveBlocks = blocks.filter(block => 
    block.type === 'button' || block.type === 'product-grid'
  )
  
  const strengths: string[] = []
  const weaknesses: string[] = []
  const opportunities: string[] = []
  
  // Analyze strengths
  if (imagesWithAlt.length === imageBlocks.length && imageBlocks.length > 0) {
    strengths.push('All images have alt text')
  }
  
  const hasKeyboardNav = interactiveBlocks.some(block => 
    block.accessibility?.keyboardNavigation !== false
  )
  if (hasKeyboardNav) {
    strengths.push('Keyboard navigation support')
  }
  
  // Analyze weaknesses
  if (imagesWithAlt.length < imageBlocks.length) {
    weaknesses.push(`${imageBlocks.length - imagesWithAlt.length} images missing alt text`)
  }
  
  const hasAriaLabels = blocks.some(block => block.accessibility?.ariaLabel)
  if (!hasAriaLabels) {
    weaknesses.push('Missing ARIA labels for screen readers')
  }
  
  // Analyze opportunities
  opportunities.push('Add skip navigation links')
  opportunities.push('Implement high contrast mode toggle')
  opportunities.push('Add screen reader announcements for dynamic content')
  opportunities.push('Ensure proper tab order throughout the page')
  
  return {
    strengths,
    weaknesses,
    opportunities,
    metrics: {
      imageBlocks: imageBlocks.length,
      imagesWithAlt: imagesWithAlt.length,
      interactiveBlocks: interactiveBlocks.length,
      accessibilityScore: analyzeAccessibilityScore(blocks),
    }
  }
}

// Performance Analysis Functions
export function analyzePerformanceScore(blocks: any[]): number {
  let score = 100
  
  // Check for lazy loading
  const imageBlocks = blocks.filter(block => block.type === 'image')
  const lazyImages = imageBlocks.filter(block => block.configuration?.lazy !== false)
  if (lazyImages.length < imageBlocks.length) {
    score -= ((imageBlocks.length - lazyImages.length) / imageBlocks.length) * 15
  }
  
  // Check for optimized product grids
  const productGrids = blocks.filter(block => block.type === 'product-grid')
  const optimizedGrids = productGrids.filter(block => 
    block.configuration?.virtualScrolling || block.configuration?.lazyLoadProducts
  )
  if (optimizedGrids.length < productGrids.length) {
    score -= 10
  }
  
  // Check for excessive blocks
  if (blocks.length > 20) {
    score -= 10
  }
  
  // Check for performance monitoring
  const hasPerformanceTracking = blocks.some(block => 
    block.performance?.trackMetrics
  )
  if (!hasPerformanceTracking) score -= 5
  
  return Math.max(0, Math.min(100, score))
}

export function generatePerformanceSuggestions(blocks: any[], _goals?: string): string[] {
  const suggestions: string[] = []
  
  const imageBlocks = blocks.filter(block => block.type === 'image')
  const nonLazyImages = imageBlocks.filter(block => block.configuration?.lazy === false)
  if (nonLazyImages.length > 0) {
    suggestions.push(`Enable lazy loading for ${nonLazyImages.length} image(s)`)
  }
  
  const productGrids = blocks.filter(block => block.type === 'product-grid')
  if (productGrids.length > 0) {
    suggestions.push('Implement virtual scrolling for product grids')
  }
  
  if (blocks.length > 15) {
    suggestions.push('Consider splitting content across multiple pages')
  }
  
  suggestions.push('Optimize images with next-gen formats (WebP, AVIF)')
  suggestions.push('Implement critical CSS for above-the-fold content')
  suggestions.push('Add performance monitoring and Core Web Vitals tracking')
  
  return suggestions.slice(0, 5)
}

export function analyzePerformanceDetails(blocks: any[]) {
  const imageBlocks = blocks.filter(block => block.type === 'image')
  const lazyImages = imageBlocks.filter(block => block.configuration?.lazy !== false)
  const productGrids = blocks.filter(block => block.type === 'product-grid')
  
  const strengths: string[] = []
  const weaknesses: string[] = []
  const opportunities: string[] = []
  
  // Analyze strengths
  if (lazyImages.length === imageBlocks.length && imageBlocks.length > 0) {
    strengths.push('All images use lazy loading')
  }
  
  if (blocks.length <= 10) {
    strengths.push('Optimal number of blocks for fast loading')
  }
  
  // Analyze weaknesses
  if (lazyImages.length < imageBlocks.length) {
    weaknesses.push(`${imageBlocks.length - lazyImages.length} images not lazy loaded`)
  }
  
  if (blocks.length > 20) {
    weaknesses.push('High number of blocks may impact performance')
  }
  
  // Analyze opportunities
  opportunities.push('Implement image optimization and compression')
  opportunities.push('Add service worker for caching')
  opportunities.push('Use CDN for static assets')
  opportunities.push('Implement code splitting for large components')
  
  return {
    strengths,
    weaknesses,
    opportunities,
    metrics: {
      totalBlocks: blocks.length,
      imageBlocks: imageBlocks.length,
      lazyImages: lazyImages.length,
      productGrids: productGrids.length,
      performanceScore: analyzePerformanceScore(blocks),
    }
  }
}

// SEO Analysis Functions
export function analyzeSEOScore(blocks: any[], metadata: any): number {
  let score = 100

  // Check for H1 tag
  const hasH1 = blocks.some(block =>
    block.type === 'text' && block.configuration?.tag === 'h1'
  ) || blocks.some(block =>
    block.type === 'hero' && block.configuration?.title
  )
  if (!hasH1) score -= 20

  // Check for meta description
  if (!metadata.description) score -= 15

  // Check for proper heading hierarchy
  const headingBlocks = blocks.filter(block =>
    block.type === 'text' && ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(block.configuration?.tag)
  )
  if (headingBlocks.length < 2) score -= 10

  // Check for alt text on images
  const imageBlocks = blocks.filter(block => block.type === 'image')
  const imagesWithoutAlt = imageBlocks.filter(block => !block.configuration?.alt)
  if (imagesWithoutAlt.length > 0) {
    score -= (imagesWithoutAlt.length / imageBlocks.length) * 15
  }

  // Check for structured data
  const hasStructuredData = blocks.some(block =>
    block.seo?.structuredData || block.type === 'product-grid'
  )
  if (!hasStructuredData) score -= 10

  // Check for internal linking
  const hasInternalLinks = blocks.some(block =>
    block.configuration?.content?.includes('href') ||
    block.type === 'button'
  )
  if (!hasInternalLinks) score -= 10

  return Math.max(0, Math.min(100, score))
}

export function generateSEOSuggestions(blocks: any[], metadata: any, goals?: string): string[] {
  const suggestions: string[] = []

  const hasH1 = blocks.some(block =>
    (block.type === 'text' && block.configuration?.tag === 'h1') ||
    (block.type === 'hero' && block.configuration?.title)
  )
  if (!hasH1) {
    suggestions.push('Add an H1 heading for better SEO structure')
  }

  if (!metadata.description) {
    suggestions.push('Add a meta description for better search results')
  }

  const imageBlocks = blocks.filter(block => block.type === 'image')
  const imagesWithoutAlt = imageBlocks.filter(block => !block.configuration?.alt)
  if (imagesWithoutAlt.length > 0) {
    suggestions.push(`Add alt text to ${imagesWithoutAlt.length} image(s) for SEO`)
  }

  const hasStructuredData = blocks.some(block => block.seo?.structuredData)
  if (!hasStructuredData) {
    suggestions.push('Implement structured data markup for rich snippets')
  }

  const productBlocks = blocks.filter(block =>
    block.type === 'product-grid' || block.type === 'product-showcase'
  )
  if (productBlocks.length > 0 && goals?.toLowerCase().includes('ecommerce')) {
    suggestions.push('Add product schema markup for better product visibility')
  }

  suggestions.push('Optimize page loading speed for better search rankings')
  suggestions.push('Add internal links to improve site navigation')

  return suggestions.slice(0, 5)
}

export function analyzeSEODetails(blocks: any[], metadata: any) {
  const headingBlocks = blocks.filter(block =>
    block.type === 'text' && ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(block.configuration?.tag)
  )
  const imageBlocks = blocks.filter(block => block.type === 'image')
  const imagesWithAlt = imageBlocks.filter(block => block.configuration?.alt)

  const strengths: string[] = []
  const weaknesses: string[] = []
  const opportunities: string[] = []

  // Analyze strengths
  const hasH1 = blocks.some(block =>
    (block.type === 'text' && block.configuration?.tag === 'h1') ||
    (block.type === 'hero' && block.configuration?.title)
  )
  if (hasH1) {
    strengths.push('Proper H1 heading structure')
  }

  if (metadata.description) {
    strengths.push('Meta description present')
  }

  if (imagesWithAlt.length === imageBlocks.length && imageBlocks.length > 0) {
    strengths.push('All images have SEO-friendly alt text')
  }

  // Analyze weaknesses
  if (!hasH1) {
    weaknesses.push('Missing H1 heading')
  }

  if (!metadata.description) {
    weaknesses.push('Missing meta description')
  }

  if (headingBlocks.length < 2) {
    weaknesses.push('Insufficient heading hierarchy')
  }

  // Analyze opportunities
  opportunities.push('Add FAQ schema for common questions')
  opportunities.push('Implement breadcrumb navigation')
  opportunities.push('Add social media meta tags (Open Graph, Twitter Cards)')
  opportunities.push('Optimize for local SEO if applicable')

  return {
    strengths,
    weaknesses,
    opportunities,
    metrics: {
      headingCount: headingBlocks.length,
      imageBlocks: imageBlocks.length,
      imagesWithAlt: imagesWithAlt.length,
      hasMetaDescription: !!metadata.description,
      seoScore: analyzeSEOScore(blocks, metadata),
    }
  }
}

// Helper function
function calculateFunnelSteps(blocks: any[]): number {
  let steps = 0

  if (blocks.some(block => block.type === 'hero')) steps++
  if (blocks.some(block => block.type === 'product-grid')) steps++
  if (blocks.some(block => block.type === 'button')) steps++
  if (blocks.some(block => block.type === 'cart-widget')) steps++

  return steps
}
