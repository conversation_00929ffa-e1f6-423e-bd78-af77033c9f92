// AI Block Generator Module
// Production-ready AI-powered block generation, optimization, and content creation

// Components
export {
  AI<PERSON>lockGenerator,
  AIBlockOptimizer,
  AIContentGenerator,
  AIBlockChat,
  DynamicAIBlock,
  AIBlockAnalytics,
} from './components'

// Hooks
export {
  useA<PERSON>lockGenerator,
  useAIBlockOptimizer,
  useAIContentGenerator,
  useAIBlockChat,
} from './hooks'

// Types
export type {
  OptimizationType,
  ContentType,
  ContentLength,
} from './hooks'

export type {
  DynamicAIBlockData,
} from './components'

// Services
export { aiBlockService, AIBlockService } from './services/aiBlockService'

// Tools (for advanced usage)
export {
  generateBlockTool,
  generateLayoutTool,
  optimizeBlockTool,
  generateContentTool,
  analyzePageTool,
} from './tools/block-generation-tools'

// Analysis Functions (for custom implementations)
export {
  analyzeConversionScore,
  generateConversionSuggestions,
  analyzeConversionDetails,
  analyzeAccessibilityScore,
  generateAccessibilitySuggestions,
  analyzeAccessibilityDetails,
  analyzePerformanceScore,
  generatePerformanceSuggestions,
  analyzePerformanceDetails,
  analyzeSEOScore,
  generateSEOSuggestions,
  analyzeSEODetails,
} from './tools/analysis-functions'
