// Comprehensive Shadcn/UI component examples for AI Block Generator
export const shadcnComponentExamples = {
  // Core UI Components
  heroWithAllComponents: {
    name: 'Complete Hero Section',
    description: 'Hero section showcasing multiple Shadcn components',
    jsx: `<div className="container mx-auto px-4 py-16">
  <div className="text-center space-y-8">
    <div className="space-y-4">
      <Badge variant="secondary" className="mb-4">
        <Sparkles className="h-3 w-3 mr-1" />
        New Release
      </Badge>
      <h1 className="text-4xl font-bold tracking-tight">
        Build Amazing UIs with Shadcn/UI
      </h1>
      <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
        Beautifully designed components built with Radix UI and Tailwind CSS.
      </p>
    </div>
    
    <div className="flex flex-col sm:flex-row gap-4 justify-center">
      <Button size="lg">
        Get Started
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
      <Button variant="outline" size="lg">
        View Components
      </Button>
    </div>
    
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Fast
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Built for performance and speed.</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Accessible
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">WAI-ARIA compliant components.</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Customizable
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Fully customizable with CSS variables.</p>
        </CardContent>
      </Card>
    </div>
  </div>
</div>`
  },

  // Form Components Showcase
  completeForm: {
    name: 'Complete Form Example',
    description: 'Comprehensive form with all Shadcn form components',
    jsx: `<Card className="w-full max-w-2xl mx-auto">
  <CardHeader>
    <CardTitle>User Profile</CardTitle>
    <CardDescription>Update your profile information</CardDescription>
  </CardHeader>
  <CardContent className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label htmlFor="firstName">First Name</Label>
        <Input id="firstName" placeholder="John" />
      </div>
      <div className="space-y-2">
        <Label htmlFor="lastName">Last Name</Label>
        <Input id="lastName" placeholder="Doe" />
      </div>
    </div>
    
    <div className="space-y-2">
      <Label htmlFor="email">Email</Label>
      <Input id="email" type="email" placeholder="<EMAIL>" />
    </div>
    
    <div className="space-y-2">
      <Label htmlFor="bio">Bio</Label>
      <Textarea id="bio" placeholder="Tell us about yourself..." />
    </div>
    
    <div className="space-y-3">
      <Label>Preferences</Label>
      <div className="flex items-center space-x-2">
        <Checkbox id="newsletter" />
        <Label htmlFor="newsletter">Subscribe to newsletter</Label>
      </div>
      <div className="flex items-center space-x-2">
        <Switch id="notifications" />
        <Label htmlFor="notifications">Enable notifications</Label>
      </div>
    </div>
    
    <div className="space-y-3">
      <Label>Account Type</Label>
      <RadioGroup defaultValue="personal">
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="personal" id="personal" />
          <Label htmlFor="personal">Personal</Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="business" id="business" />
          <Label htmlFor="business">Business</Label>
        </div>
      </RadioGroup>
    </div>
    
    <div className="space-y-3">
      <Label>Country</Label>
      <Select>
        <SelectTrigger>
          <SelectValue placeholder="Select a country" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="us">United States</SelectItem>
          <SelectItem value="uk">United Kingdom</SelectItem>
          <SelectItem value="ca">Canada</SelectItem>
          <SelectItem value="za">South Africa</SelectItem>
        </SelectContent>
      </Select>
    </div>
    
    <div className="space-y-3">
      <Label>Experience Level</Label>
      <Slider defaultValue={[3]} max={5} step={1} className="w-full" />
      <div className="flex justify-between text-sm text-muted-foreground">
        <span>Beginner</span>
        <span>Expert</span>
      </div>
    </div>
    
    <Separator />
    
    <div className="flex justify-end space-x-2">
      <Button variant="outline">Cancel</Button>
      <Button>Save Changes</Button>
    </div>
  </CardContent>
</Card>`
  },

  // Navigation Components
  navigationShowcase: {
    name: 'Navigation Components',
    description: 'Complete navigation with tabs, menus, and breadcrumbs',
    jsx: `<div className="space-y-6">
  <NavigationMenu>
    <NavigationMenuList>
      <NavigationMenuItem>
        <NavigationMenuTrigger>Getting started</NavigationMenuTrigger>
        <NavigationMenuContent>
          <div className="grid gap-3 p-6 md:w-[400px] lg:w-[500px]">
            <div className="row-span-3">
              <NavigationMenuLink asChild>
                <a className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md">
                  <div className="mb-2 mt-4 text-lg font-medium">
                    shadcn/ui
                  </div>
                  <p className="text-sm leading-tight text-muted-foreground">
                    Beautifully designed components built with Radix UI and Tailwind CSS.
                  </p>
                </a>
              </NavigationMenuLink>
            </div>
          </div>
        </NavigationMenuContent>
      </NavigationMenuItem>
      <NavigationMenuItem>
        <NavigationMenuTrigger>Components</NavigationMenuTrigger>
        <NavigationMenuContent>
          <div className="grid w-[400px] gap-3 p-4">
            <NavigationMenuLink>Alert Dialog</NavigationMenuLink>
            <NavigationMenuLink>Button</NavigationMenuLink>
            <NavigationMenuLink>Card</NavigationMenuLink>
          </div>
        </NavigationMenuContent>
      </NavigationMenuItem>
    </NavigationMenuList>
  </NavigationMenu>
  
  <Tabs defaultValue="overview" className="w-full">
    <TabsList className="grid w-full grid-cols-4">
      <TabsTrigger value="overview">Overview</TabsTrigger>
      <TabsTrigger value="analytics">Analytics</TabsTrigger>
      <TabsTrigger value="reports">Reports</TabsTrigger>
      <TabsTrigger value="notifications">Notifications</TabsTrigger>
    </TabsList>
    <TabsContent value="overview" className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Overview</CardTitle>
          <CardDescription>Your account overview and recent activity.</CardDescription>
        </CardHeader>
        <CardContent>
          <p>Welcome to your dashboard overview.</p>
        </CardContent>
      </Card>
    </TabsContent>
    <TabsContent value="analytics">
      <Card>
        <CardHeader>
          <CardTitle>Analytics</CardTitle>
          <CardDescription>View your analytics and insights.</CardDescription>
        </CardHeader>
        <CardContent>
          <p>Analytics data will be displayed here.</p>
        </CardContent>
      </Card>
    </TabsContent>
  </Tabs>
</div>`
  },

  // Data Display Components
  dataDisplay: {
    name: 'Data Display Components',
    description: 'Tables, avatars, alerts, and progress indicators',
    jsx: `<div className="space-y-6">
  <div className="flex items-center space-x-4">
    <Avatar className="h-12 w-12">
      <AvatarImage src="https://github.com/shadcn.png" />
      <AvatarFallback>CN</AvatarFallback>
    </Avatar>
    <div>
      <p className="text-sm font-medium">shadcn</p>
      <p className="text-sm text-muted-foreground">@shadcn</p>
    </div>
  </div>
  
  <Alert>
    <AlertCircle className="h-4 w-4" />
    <AlertTitle>Heads up!</AlertTitle>
    <AlertDescription>
      You can add components to your app using the cli.
    </AlertDescription>
  </Alert>
  
  <div className="space-y-2">
    <div className="flex justify-between text-sm">
      <span>Progress</span>
      <span>75%</span>
    </div>
    <Progress value={75} className="w-full" />
  </div>
  
  <Table>
    <TableCaption>A list of your recent invoices.</TableCaption>
    <TableHeader>
      <TableRow>
        <TableHead>Invoice</TableHead>
        <TableHead>Status</TableHead>
        <TableHead>Method</TableHead>
        <TableHead className="text-right">Amount</TableHead>
      </TableRow>
    </TableHeader>
    <TableBody>
      <TableRow>
        <TableCell className="font-medium">INV001</TableCell>
        <TableCell>
          <Badge variant="outline">Paid</Badge>
        </TableCell>
        <TableCell>Credit Card</TableCell>
        <TableCell className="text-right">$250.00</TableCell>
      </TableRow>
      <TableRow>
        <TableCell className="font-medium">INV002</TableCell>
        <TableCell>
          <Badge variant="secondary">Pending</Badge>
        </TableCell>
        <TableCell>PayPal</TableCell>
        <TableCell className="text-right">$150.00</TableCell>
      </TableRow>
    </TableBody>
  </Table>
</div>`
  },

  // Interactive Components
  interactiveComponents: {
    name: 'Interactive Components',
    description: 'Dialogs, popovers, tooltips, and dropdowns',
    jsx: `<div className="flex flex-wrap gap-4">
  <Dialog>
    <DialogTrigger asChild>
      <Button variant="outline">Open Dialog</Button>
    </DialogTrigger>
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Are you sure?</DialogTitle>
        <DialogDescription>
          This action cannot be undone. This will permanently delete your account.
        </DialogDescription>
      </DialogHeader>
      <div className="flex justify-end space-x-2">
        <Button variant="outline">Cancel</Button>
        <Button variant="destructive">Delete</Button>
      </div>
    </DialogContent>
  </Dialog>
  
  <Popover>
    <PopoverTrigger asChild>
      <Button variant="outline">Open Popover</Button>
    </PopoverTrigger>
    <PopoverContent className="w-80">
      <div className="grid gap-4">
        <div className="space-y-2">
          <h4 className="font-medium leading-none">Dimensions</h4>
          <p className="text-sm text-muted-foreground">
            Set the dimensions for the layer.
          </p>
        </div>
        <div className="grid gap-2">
          <div className="grid grid-cols-3 items-center gap-4">
            <Label htmlFor="width">Width</Label>
            <Input id="width" defaultValue="100%" className="col-span-2 h-8" />
          </div>
          <div className="grid grid-cols-3 items-center gap-4">
            <Label htmlFor="height">Height</Label>
            <Input id="height" defaultValue="25px" className="col-span-2 h-8" />
          </div>
        </div>
      </div>
    </PopoverContent>
  </Popover>
  
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant="outline">Hover me</Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>Add to library</p>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
  
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button variant="outline">Open Menu</Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent className="w-56">
      <DropdownMenuLabel>My Account</DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuItem>
        <User className="mr-2 h-4 w-4" />
        <span>Profile</span>
      </DropdownMenuItem>
      <DropdownMenuItem>
        <CreditCard className="mr-2 h-4 w-4" />
        <span>Billing</span>
      </DropdownMenuItem>
      <DropdownMenuItem>
        <Settings className="mr-2 h-4 w-4" />
        <span>Settings</span>
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</div>`
  },

  // E-commerce Product Card
  ecommerceCard: {
    name: 'E-commerce Product Card',
    description: 'Complete product card with all Shadcn components',
    jsx: `<Card className="w-full max-w-sm">
  <div className="relative">
    <AspectRatio ratio={16 / 9}>
      <img
        src="https://images.unsplash.com/photo-*************-c2dcdb7f1dcd?w=800&dpr=2&q=80"
        alt="Photo by Drew Beamer"
        className="rounded-t-lg object-cover w-full h-full"
      />
    </AspectRatio>
    <Badge className="absolute top-2 right-2" variant="secondary">
      New
    </Badge>
  </div>
  <CardHeader>
    <div className="flex justify-between items-start">
      <div>
        <CardTitle className="text-lg">Premium Headphones</CardTitle>
        <CardDescription>High-quality wireless headphones</CardDescription>
      </div>
      <div className="text-right">
        <p className="text-2xl font-bold">R1,299</p>
        <p className="text-sm text-muted-foreground line-through">R1,599</p>
      </div>
    </div>
  </CardHeader>
  <CardContent className="space-y-4">
    <div className="flex items-center space-x-2">
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star key={star} className="h-4 w-4 fill-current text-yellow-500" />
        ))}
      </div>
      <span className="text-sm text-muted-foreground">(128 reviews)</span>
    </div>
    
    <div className="space-y-2">
      <Label>Color</Label>
      <div className="flex space-x-2">
        <Button variant="outline" size="sm" className="w-8 h-8 p-0 bg-black"></Button>
        <Button variant="outline" size="sm" className="w-8 h-8 p-0 bg-white"></Button>
        <Button variant="outline" size="sm" className="w-8 h-8 p-0 bg-blue-500"></Button>
      </div>
    </div>
    
    <div className="space-y-2">
      <Label>Quantity</Label>
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="sm">-</Button>
        <span className="px-3 py-1 border rounded">1</span>
        <Button variant="outline" size="sm">+</Button>
      </div>
    </div>
    
    <Separator />
    
    <div className="flex space-x-2">
      <Button className="flex-1">
        <ShoppingCart className="mr-2 h-4 w-4" />
        Add to Cart
      </Button>
      <Button variant="outline" size="icon">
        <Heart className="h-4 w-4" />
      </Button>
    </div>
  </CardContent>
</Card>`
  }
}

// Export individual component JSX templates
export const componentTemplates = {
  button: `<Button>Click me</Button>`,
  card: `<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
  </CardHeader>
  <CardContent>
    <p>Card content goes here.</p>
  </CardContent>
</Card>`,
  input: `<div className="space-y-2">
  <Label htmlFor="input">Label</Label>
  <Input id="input" placeholder="Enter text..." />
</div>`,
  select: `<Select>
  <SelectTrigger>
    <SelectValue placeholder="Select an option" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
    <SelectItem value="option2">Option 2</SelectItem>
  </SelectContent>
</Select>`,
  checkbox: `<div className="flex items-center space-x-2">
  <Checkbox id="checkbox" />
  <Label htmlFor="checkbox">Check me</Label>
</div>`,
  badge: `<Badge>New</Badge>`,
  alert: `<Alert>
  <AlertCircle className="h-4 w-4" />
  <AlertTitle>Heads up!</AlertTitle>
  <AlertDescription>
    This is an alert message.
  </AlertDescription>
</Alert>`,
  progress: `<Progress value={60} className="w-full" />`,
  avatar: `<Avatar>
  <AvatarImage src="https://github.com/shadcn.png" />
  <AvatarFallback>CN</AvatarFallback>
</Avatar>`,
  separator: `<Separator />`,
  tabs: `<Tabs defaultValue="tab1">
  <TabsList>
    <TabsTrigger value="tab1">Tab 1</TabsTrigger>
    <TabsTrigger value="tab2">Tab 2</TabsTrigger>
  </TabsList>
  <TabsContent value="tab1">Content 1</TabsContent>
  <TabsContent value="tab2">Content 2</TabsContent>
</Tabs>`
}
