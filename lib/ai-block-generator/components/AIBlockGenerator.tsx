'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import {
  Wand2,
  Plus,
  Loader2,
  Sparkles,
  Layout,
  MessageSquare,
  CheckCircle,
  XCircle,
  Copy,
  Download,
  Settings,
  Eye,
  Save,
  RefreshCw
} from 'lucide-react'
import { useAIBlockGenerator } from '../hooks/useAIBlockGenerator'
// Available block types for the AI generator

export function AIBlockGenerator() {
  const [activeTab, setActiveTab] = useState('single')
  const [blockType, setBlockType] = useState('')
  const [requirements, setRequirements] = useState('')
  const [context, setContext] = useState('')
  const [style, setStyle] = useState('')
  const [pageType, setPageType] = useState('')
  const [sections, setSections] = useState<string[]>([])
  const [target, setTarget] = useState('')
  const [previewMode, setPreviewMode] = useState(false)
  const [savedTemplates, setSavedTemplates] = useState<any[]>([])
  const [templateName, setTemplateName] = useState('')

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
    generateBlock,
    generateLayout,
    generatedBlocks,
    addGeneratedBlock,
    addAllGeneratedBlocks,
    clearGeneratedBlocks,
    // startNewConversation, // Commented out as it's not used in this component
    hasGeneratedBlocks,
  } = useAIBlockGenerator({
    onBlockGenerated: (block) => {
      console.log('Block generated:', block)
    },
    onLayoutGenerated: (layout) => {
      console.log('Layout generated:', layout)
    },
    onError: (error) => {
      console.error('AI Generation Error:', error)
    },
  })

  const availableBlockTypes = [
    { name: 'hero', displayName: 'Hero Section' },
    { name: 'text', displayName: 'Text Block' },
    { name: 'image', displayName: 'Image Block' },
    { name: 'button', displayName: 'Button' },
    { name: 'spacer', displayName: 'Spacer' },
    { name: 'product-grid', displayName: 'Product Grid' },
    { name: 'cart-widget', displayName: 'Cart Widget' },
    { name: 'product-showcase', displayName: 'Product Showcase' },
    { name: 'layout-container', displayName: 'Layout Container' },
  ]
  const availablePageTypes = ['landing', 'product', 'about', 'contact', 'blog', 'portfolio', 'pricing']
  const availableSections = ['hero', 'features', 'testimonials', 'products', 'about', 'contact', 'footer', 'gallery', 'pricing', 'faq']
  const availableStyles = ['modern', 'minimal', 'bold', 'elegant', 'playful', 'professional', 'creative']

  const handleGenerateBlock = async () => {
    if (!blockType || !requirements) return
    await generateBlock(blockType, requirements, context, style)
  }

  const handleGenerateLayout = async () => {
    if (!pageType || !requirements || sections.length === 0) return
    await generateLayout(pageType, requirements, sections, style, target)
  }

  const toggleSection = (section: string) => {
    setSections(prev =>
      prev.includes(section)
        ? prev.filter(s => s !== section)
        : [...prev, section]
    )
  }

  const saveAsTemplate = () => {
    if (!templateName.trim()) return
    const template = {
      id: Date.now(),
      name: templateName,
      pageType,
      sections,
      style,
      target,
      createdAt: new Date().toISOString(),
    }
    setSavedTemplates(prev => [...prev, template])
    setTemplateName('')
  }

  const loadTemplate = (template: any) => {
    setPageType(template.pageType)
    setSections(template.sections)
    setStyle(template.style)
    setTarget(template.target)
  }

  const copyBlockToClipboard = async (block: any) => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(block, null, 2))
    } catch (err) {
      console.error('Failed to copy block:', err)
    }
  }

  const exportBlocks = () => {
    const dataStr = JSON.stringify(generatedBlocks, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    const exportFileDefaultName = `ai-generated-blocks-${Date.now()}.json`

    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5" />
            AI Block Generator
          </CardTitle>
          <CardDescription>
            Generate page blocks and layouts using AI. Describe what you need and let AI create it for you.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="single">Single Block</TabsTrigger>
              <TabsTrigger value="layout">Page Layout</TabsTrigger>
              <TabsTrigger value="chat">AI Chat</TabsTrigger>
            </TabsList>

            <TabsContent value="single" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Block Type</Label>
                  <Select value={blockType} onValueChange={setBlockType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select block type" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableBlockTypes.map((type) => (
                        <SelectItem key={type.name} value={type.name}>
                          {type.displayName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Style</Label>
                  <Select value={style} onValueChange={setStyle}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select style (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableStyles.map((styleOption) => (
                        <SelectItem key={styleOption} value={styleOption}>
                          {styleOption.charAt(0).toUpperCase() + styleOption.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label>Requirements</Label>
                <Textarea
                  value={requirements}
                  onChange={(e) => setRequirements(e.target.value)}
                  placeholder="Describe what you want this block to do and how it should look..."
                  rows={3}
                />
              </div>

              <div>
                <Label>Context (Optional)</Label>
                <Input
                  value={context}
                  onChange={(e) => setContext(e.target.value)}
                  placeholder="Additional context about your page or business..."
                />
              </div>

              <Button 
                onClick={handleGenerateBlock}
                disabled={!blockType || !requirements || isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating Block...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate Block
                  </>
                )}
              </Button>
            </TabsContent>

            <TabsContent value="layout" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Page Type</Label>
                  <Select value={pageType} onValueChange={setPageType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select page type" />
                    </SelectTrigger>
                    <SelectContent>
                      {availablePageTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type.charAt(0).toUpperCase() + type.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Style</Label>
                  <Select value={style} onValueChange={setStyle}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select style (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableStyles.map((styleOption) => (
                        <SelectItem key={styleOption} value={styleOption}>
                          {styleOption.charAt(0).toUpperCase() + styleOption.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label>Sections</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {availableSections.map((section) => (
                    <Badge
                      key={section}
                      variant={sections.includes(section) ? 'default' : 'outline'}
                      className="cursor-pointer"
                      onClick={() => toggleSection(section)}
                    >
                      {section.charAt(0).toUpperCase() + section.slice(1)}
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <Label>Requirements</Label>
                <Textarea
                  value={requirements}
                  onChange={(e) => setRequirements(e.target.value)}
                  placeholder="Describe your page goals, target audience, and specific requirements..."
                  rows={3}
                />
              </div>

              <div>
                <Label>Target Audience (Optional)</Label>
                <Input
                  value={target}
                  onChange={(e) => setTarget(e.target.value)}
                  placeholder="Who is this page for? (e.g., young professionals, families, etc.)"
                />
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={handleGenerateLayout}
                  disabled={!pageType || !requirements || sections.length === 0 || isLoading}
                  className="flex-1"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating Layout...
                    </>
                  ) : (
                    <>
                      <Layout className="h-4 w-4 mr-2" />
                      Generate Layout
                    </>
                  )}
                </Button>

                <Button
                  variant="outline"
                  onClick={() => setPreviewMode(!previewMode)}
                  disabled={!hasGeneratedBlocks}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
              </div>

              {/* Template Management */}
              <div className="border-t pt-4">
                <div className="flex items-center gap-2 mb-3">
                  <Settings className="h-4 w-4" />
                  <span className="font-medium">Template Management</span>
                </div>

                <div className="space-y-3">
                  <div className="flex gap-2">
                    <Input
                      value={templateName}
                      onChange={(e) => setTemplateName(e.target.value)}
                      placeholder="Template name..."
                      className="flex-1"
                    />
                    <Button
                      onClick={saveAsTemplate}
                      disabled={!templateName.trim() || !pageType}
                      size="sm"
                    >
                      <Save className="h-4 w-4 mr-1" />
                      Save
                    </Button>
                  </div>

                  {savedTemplates.length > 0 && (
                    <div>
                      <Label className="text-xs">Saved Templates</Label>
                      <div className="grid grid-cols-1 gap-2 mt-2">
                        {savedTemplates.map((template) => (
                          <div key={template.id} className="flex items-center justify-between p-2 border rounded">
                            <div>
                              <span className="text-sm font-medium">{template.name}</span>
                              <p className="text-xs text-muted-foreground">
                                {template.pageType} • {template.sections.length} sections
                              </p>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => loadTemplate(template)}
                            >
                              <RefreshCw className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="chat" className="space-y-4">
              <div className="border rounded-lg">
                <ScrollArea className="h-64 p-4">
                  {messages.length === 0 ? (
                    <div className="text-center text-muted-foreground">
                      <MessageSquare className="h-8 w-8 mx-auto mb-2" />
                      <p>Start a conversation with the AI assistant</p>
                      <p className="text-sm">Ask questions about page building, design, or get help with blocks</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {messages.map((message) => (
                        <div
                          key={message.id}
                          className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                        >
                          <div
                            className={`max-w-[80%] rounded-lg px-3 py-2 ${
                              message.role === 'user'
                                ? 'bg-primary text-primary-foreground'
                                : 'bg-muted'
                            }`}
                          >
                            {message.parts.map((part, index) => {
                              if (part.type === 'text') {
                                return <p key={index} className="text-sm">{part.text}</p>
                              }
                              return null
                            })}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
                <Separator />
                <form onSubmit={handleSubmit} className="p-4">
                  <div className="flex gap-2">
                    <Input
                      value={input}
                      onChange={handleInputChange}
                      placeholder="Ask me anything about page building..."
                      disabled={isLoading}
                    />
                    <Button type="submit" disabled={isLoading || !input.trim()}>
                      {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        'Send'
                      )}
                    </Button>
                  </div>
                </form>
              </div>
            </TabsContent>
          </Tabs>

          {error && (
            <div className="mt-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <div className="flex items-center gap-2 text-destructive">
                <XCircle className="h-4 w-4" />
                <span className="text-sm font-medium">Error</span>
              </div>
              <p className="text-sm text-destructive/80 mt-1">{error.message}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {hasGeneratedBlocks && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Generated Blocks ({generatedBlocks.length})
            </CardTitle>
            <CardDescription>
              Review and add the generated blocks to your page
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex gap-2">
                <Button onClick={addAllGeneratedBlocks} className="flex-1">
                  <Plus className="h-4 w-4 mr-2" />
                  Add All Blocks
                </Button>
                <Button variant="outline" onClick={exportBlocks}>
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
                <Button variant="outline" onClick={clearGeneratedBlocks}>
                  Clear
                </Button>
              </div>
              
              <div className="space-y-2">
                {generatedBlocks.map((block, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">{block.type} Block</p>
                      <p className="text-sm text-muted-foreground">
                        {block.configuration?.title || block.configuration?.content || 'Generated block'}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyBlockToClipboard(block)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => addGeneratedBlock(block)}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
