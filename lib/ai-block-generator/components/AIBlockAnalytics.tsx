'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Eye,
  Zap,
  Accessibility,
  Search,
  Smartphone,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  MousePointer,
  RefreshCw
} from 'lucide-react'
import {
  analyzeConversionScore,
  analyzeAccessibilityScore,
  analyzePerformanceScore,
  analyzeSEOScore,
  generateConversionSuggestions,
  generateAccessibilitySuggestions,
  generatePerformanceSuggestions,
  generateSEOSuggestions,
} from '../tools/analysis-functions'

interface AIBlockAnalyticsProps {
  blocks: any[]
  pageMetadata?: any
  onOptimizationSuggestion?: (suggestion: string, blockId?: string) => void
  realTimeAnalysis?: boolean
}

export function AIBlockAnalytics({
  blocks,
  pageMetadata = {},
  onOptimizationSuggestion,
  realTimeAnalysis = true,
}: AIBlockAnalyticsProps) {
  const [analysisData, setAnalysisData] = useState<any>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [lastAnalyzed, setLastAnalyzed] = useState<Date | null>(null)
  const [selectedMetric, setSelectedMetric] = useState<string>('overview')

  // Perform analysis
  const performAnalysis = async () => {
    setIsAnalyzing(true)
    
    try {
      const conversionScore = analyzeConversionScore(blocks)
      const accessibilityScore = analyzeAccessibilityScore(blocks)
      const performanceScore = analyzePerformanceScore(blocks)
      const seoScore = analyzeSEOScore(blocks, pageMetadata)
      
      const overallScore = Math.round((conversionScore + accessibilityScore + performanceScore + seoScore) / 4)
      
      const analysis = {
        overall: {
          score: overallScore,
          grade: getGrade(overallScore),
          trend: getTrend(overallScore),
        },
        metrics: {
          conversion: {
            score: conversionScore,
            suggestions: generateConversionSuggestions(blocks),
            priority: conversionScore < 70 ? 'high' : conversionScore < 85 ? 'medium' : 'low',
          },
          accessibility: {
            score: accessibilityScore,
            suggestions: generateAccessibilitySuggestions(blocks),
            priority: accessibilityScore < 70 ? 'high' : accessibilityScore < 85 ? 'medium' : 'low',
          },
          performance: {
            score: performanceScore,
            suggestions: generatePerformanceSuggestions(blocks),
            priority: performanceScore < 70 ? 'high' : performanceScore < 85 ? 'medium' : 'low',
          },
          seo: {
            score: seoScore,
            suggestions: generateSEOSuggestions(blocks, pageMetadata),
            priority: seoScore < 70 ? 'high' : seoScore < 85 ? 'medium' : 'low',
          },
        },
        blockAnalysis: analyzeIndividualBlocks(blocks),
        recommendations: generatePriorityRecommendations(blocks),
        timestamp: new Date().toISOString(),
      }
      
      setAnalysisData(analysis)
      setLastAnalyzed(new Date())
    } catch (error) {
      console.error('Analysis failed:', error)
    } finally {
      setIsAnalyzing(false)
    }
  }

  // Real-time analysis when blocks change
  useEffect(() => {
    if (realTimeAnalysis && blocks.length > 0) {
      const debounceTimer = setTimeout(() => {
        performAnalysis()
      }, 1000)
      
      return () => clearTimeout(debounceTimer)
    }
  }, [blocks, realTimeAnalysis])

  // Initial analysis
  useEffect(() => {
    if (blocks.length > 0) {
      performAnalysis()
    }
  }, [])

  const getGrade = (score: number): string => {
    if (score >= 90) return 'A+'
    if (score >= 85) return 'A'
    if (score >= 80) return 'B+'
    if (score >= 75) return 'B'
    if (score >= 70) return 'C+'
    if (score >= 65) return 'C'
    if (score >= 60) return 'D'
    return 'F'
  }

  const getTrend = (score: number): 'up' | 'down' | 'stable' => {
    // In a real implementation, this would compare with previous scores
    return score >= 80 ? 'up' : score >= 60 ? 'stable' : 'down'
  }

  const analyzeIndividualBlocks = (blocks: any[]) => {
    return blocks.map(block => ({
      id: block.id,
      type: block.type,
      score: Math.floor(Math.random() * 40) + 60, // Simulated score
      issues: getBlockIssues(block),
      suggestions: getBlockSuggestions(block),
    }))
  }

  const getBlockIssues = (block: any): string[] => {
    const issues: string[] = []
    
    if (block.type === 'image' && !block.configuration?.alt) {
      issues.push('Missing alt text')
    }
    
    if (block.type === 'button' && !block.configuration?.text) {
      issues.push('Missing button text')
    }
    
    if (!block.accessibility?.ariaLabel) {
      issues.push('Missing ARIA label')
    }
    
    return issues
  }

  const getBlockSuggestions = (block: any): string[] => {
    const suggestions: string[] = []
    
    if (block.type === 'hero' && !block.configuration?.ctaText) {
      suggestions.push('Add a call-to-action button')
    }
    
    if (block.type === 'text' && block.configuration?.content?.length > 500) {
      suggestions.push('Consider breaking up long text')
    }
    
    return suggestions
  }

  const generatePriorityRecommendations = (blocks: any[]): Array<{
    priority: 'high' | 'medium' | 'low'
    category: string
    description: string
    impact: string
  }> => {
    const recommendations = []
    
    const imageBlocks = blocks.filter(b => b.type === 'image')
    const imagesWithoutAlt = imageBlocks.filter(b => !b.configuration?.alt)
    
    if (imagesWithoutAlt.length > 0) {
      recommendations.push({
        priority: 'high' as const,
        category: 'Accessibility',
        description: `Add alt text to ${imagesWithoutAlt.length} image(s)`,
        impact: 'Improves screen reader compatibility and SEO',
      })
    }
    
    const hasHero = blocks.some(b => b.type === 'hero')
    if (!hasHero) {
      recommendations.push({
        priority: 'medium' as const,
        category: 'Conversion',
        description: 'Add a hero section to improve first impressions',
        impact: 'Increases user engagement and conversion rates',
      })
    }
    
    return recommendations
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200'
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'low': return 'text-green-600 bg-green-50 border-green-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  if (!analysisData) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">
              {isAnalyzing ? 'Analyzing your blocks...' : 'No analysis data available'}
            </p>
            {!isAnalyzing && (
              <Button onClick={performAnalysis} className="mt-4">
                Start Analysis
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Overview Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                AI Block Analytics
              </CardTitle>
              <CardDescription>
                Real-time analysis and optimization recommendations
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {lastAnalyzed && (
                <div className="text-xs text-muted-foreground flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {lastAnalyzed.toLocaleTimeString()}
                </div>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={performAnalysis}
                disabled={isAnalyzing}
              >
                {isAnalyzing ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            {/* Overall Score */}
            <div className="md:col-span-2">
              <div className="text-center">
                <div className="text-4xl font-bold mb-2 flex items-center justify-center gap-2">
                  {analysisData.overall.score}
                  <span className="text-lg text-muted-foreground">/ 100</span>
                  {analysisData.overall.trend === 'up' && <TrendingUp className="h-6 w-6 text-green-500" />}
                  {analysisData.overall.trend === 'down' && <TrendingDown className="h-6 w-6 text-red-500" />}
                </div>
                <div className="text-2xl font-semibold text-primary mb-2">
                  Grade {analysisData.overall.grade}
                </div>
                <Progress value={analysisData.overall.score} className="w-full" />
              </div>
            </div>
            
            {/* Metric Scores */}
            <div className="md:col-span-3 grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Eye className="h-4 w-4 text-blue-500" />
                  <span className="text-sm font-medium">Conversion</span>
                </div>
                <div className="text-2xl font-bold">{analysisData.metrics.conversion.score}</div>
                <Badge variant={analysisData.metrics.conversion.priority === 'high' ? 'destructive' : 'secondary'}>
                  {analysisData.metrics.conversion.priority}
                </Badge>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Accessibility className="h-4 w-4 text-green-500" />
                  <span className="text-sm font-medium">Accessibility</span>
                </div>
                <div className="text-2xl font-bold">{analysisData.metrics.accessibility.score}</div>
                <Badge variant={analysisData.metrics.accessibility.priority === 'high' ? 'destructive' : 'secondary'}>
                  {analysisData.metrics.accessibility.priority}
                </Badge>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Zap className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm font-medium">Performance</span>
                </div>
                <div className="text-2xl font-bold">{analysisData.metrics.performance.score}</div>
                <Badge variant={analysisData.metrics.performance.priority === 'high' ? 'destructive' : 'secondary'}>
                  {analysisData.metrics.performance.priority}
                </Badge>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Search className="h-4 w-4 text-purple-500" />
                  <span className="text-sm font-medium">SEO</span>
                </div>
                <div className="text-2xl font-bold">{analysisData.metrics.seo.score}</div>
                <Badge variant={analysisData.metrics.seo.priority === 'high' ? 'destructive' : 'secondary'}>
                  {analysisData.metrics.seo.priority}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Analysis & Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedMetric} onValueChange={setSelectedMetric}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="conversion">Conversion</TabsTrigger>
              <TabsTrigger value="accessibility">Accessibility</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="seo">SEO</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div>
                <h3 className="font-semibold mb-3">Priority Recommendations</h3>
                <div className="space-y-3">
                  {analysisData.recommendations.map((rec: any, index: number) => (
                    <div key={index} className={`p-3 rounded-lg border ${getPriorityColor(rec.priority)}`}>
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <Badge variant="outline" className="text-xs">
                              {rec.category}
                            </Badge>
                            <Badge variant={rec.priority === 'high' ? 'destructive' : 'secondary'} className="text-xs">
                              {rec.priority} priority
                            </Badge>
                          </div>
                          <p className="font-medium">{rec.description}</p>
                          <p className="text-sm opacity-80 mt-1">{rec.impact}</p>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onOptimizationSuggestion?.(rec.description)}
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>

            {['conversion', 'accessibility', 'performance', 'seo'].map(metric => (
              <TabsContent key={metric} value={metric} className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold capitalize">{metric} Analysis</h3>
                    <div className="text-2xl font-bold">
                      {analysisData.metrics[metric].score}/100
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    {analysisData.metrics[metric].suggestions.map((suggestion: string, index: number) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">{suggestion}</span>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onOptimizationSuggestion?.(suggestion)}
                        >
                          Apply
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
