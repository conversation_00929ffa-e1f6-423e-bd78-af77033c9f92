'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
// Badge component removed as it's not used in this component
import { 
  MessageSquare, 
  Send, 
  Loader2, 
  HelpCircle,
  Lightbulb,
  Palette,
  Smartphone,
  Zap,
  Search,
  Trash2
} from 'lucide-react'
import { useAIBlockChat } from '../hooks/useAIBlockChat'

export function AIBlockChat() {
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
    askQuestion,
    askAboutBlock,
    askForDesignAdvice,
    askAboutBestPractices,
    getAccessibilityAdvice,
    askAboutPerformance,
    getMobileOptimizationTips,
    clearConversation,
    hasMessages,
  } = useAIBlockChat({
    onResponse: (message) => {
      console.log('AI Response:', message)
    },
    onError: (error) => {
      console.error('Chat Error:', error)
    },
  })

  const quickQuestions = [
    {
      category: 'Design',
      icon: <Palette className="h-4 w-4" />,
      questions: [
        'What are the latest design trends for e-commerce sites?',
        'How can I improve the visual hierarchy of my page?',
        'What color schemes work best for conversion?',
        'How do I create a cohesive design system?'
      ]
    },
    {
      category: 'UX/UI',
      icon: <Lightbulb className="h-4 w-4" />,
      questions: [
        'How can I improve user engagement on my landing page?',
        'What are the best practices for call-to-action buttons?',
        'How do I reduce bounce rate with better UX?',
        'What micro-interactions should I add?'
      ]
    },
    {
      category: 'Mobile',
      icon: <Smartphone className="h-4 w-4" />,
      questions: [
        'How do I optimize my page for mobile devices?',
        'What are the best mobile navigation patterns?',
        'How can I improve mobile page speed?',
        'What touch gestures should I implement?'
      ]
    },
    {
      category: 'Performance',
      icon: <Zap className="h-4 w-4" />,
      questions: [
        'How can I improve my page loading speed?',
        'What are Core Web Vitals and how do I optimize them?',
        'How do I implement lazy loading effectively?',
        'What images formats should I use for best performance?'
      ]
    },
    {
      category: 'SEO',
      icon: <Search className="h-4 w-4" />,
      questions: [
        'How do I optimize my page for search engines?',
        'What structured data should I implement?',
        'How can I improve my page\'s SEO score?',
        'What are the most important meta tags?'
      ]
    }
  ]

  const handleQuickQuestion = async (question: string) => {
    await askQuestion(question)
  }

  const handleSpecializedQuestion = async (type: string, context: string) => {
    switch (type) {
      case 'block':
        await askAboutBlock('hero', context)
        break
      case 'design':
        await askForDesignAdvice(context, 'improve user engagement')
        break
      case 'accessibility':
        await getAccessibilityAdvice(context)
        break
      case 'performance':
        await askAboutPerformance(context)
        break
      case 'mobile':
        await getMobileOptimizationTips(context)
        break
      default:
        await askQuestion(context)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            AI Page Builder Assistant
          </CardTitle>
          <CardDescription>
            Get expert advice on page building, design, UX, performance, and more. Ask questions or choose from quick suggestions.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Chat Messages */}
          <div className="border rounded-lg mb-4">
            <ScrollArea className="h-96 p-4">
              {!hasMessages ? (
                <div className="text-center text-muted-foreground py-8">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <h3 className="font-medium mb-2">Welcome to AI Assistant</h3>
                  <p className="text-sm">
                    I'm here to help you with page building, design decisions, optimization tips, and best practices.
                  </p>
                  <p className="text-sm mt-2">
                    Ask me anything or choose from the quick questions below!
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[85%] rounded-lg px-4 py-3 ${
                          message.role === 'user'
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted'
                        }`}
                      >
                        {message.parts.map((part, index) => {
                          if (part.type === 'text') {
                            return (
                              <div key={index} className="prose prose-sm max-w-none">
                                {part.text.split('\n').map((line, lineIndex) => (
                                  <p key={lineIndex} className="mb-2 last:mb-0">
                                    {line}
                                  </p>
                                ))}
                              </div>
                            )
                          }
                          return null
                        })}
                      </div>
                    </div>
                  ))}
                  {isLoading && (
                    <div className="flex justify-start">
                      <div className="bg-muted rounded-lg px-4 py-3">
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span className="text-sm">AI is thinking...</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </ScrollArea>
            
            <Separator />
            
            {/* Chat Input */}
            <form onSubmit={handleSubmit} className="p-4">
              <div className="flex gap-2">
                <Input
                  value={input}
                  onChange={handleInputChange}
                  placeholder="Ask me anything about page building, design, or optimization..."
                  disabled={isLoading}
                  className="flex-1"
                />
                <Button type="submit" disabled={isLoading || !input.trim()}>
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
                {hasMessages && (
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={clearConversation}
                    disabled={isLoading}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </form>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg mb-4">
              <div className="flex items-center gap-2 text-destructive">
                <HelpCircle className="h-4 w-4" />
                <span className="text-sm font-medium">Error</span>
              </div>
              <p className="text-sm text-destructive/80 mt-1">{error.message}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Questions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            Quick Questions
          </CardTitle>
          <CardDescription>
            Get instant answers to common page building questions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {quickQuestions.map((category) => (
              <div key={category.category}>
                <div className="flex items-center gap-2 mb-3">
                  {category.icon}
                  <h3 className="font-medium">{category.category}</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {category.questions.map((question, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      className="justify-start h-auto p-3 text-left"
                      onClick={() => handleQuickQuestion(question)}
                      disabled={isLoading}
                    >
                      <span className="text-sm">{question}</span>
                    </Button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Specialized Help */}
      <Card>
        <CardHeader>
          <CardTitle>Specialized Help</CardTitle>
          <CardDescription>
            Get targeted assistance for specific areas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-start gap-2"
              onClick={() => handleSpecializedQuestion('design', 'my e-commerce landing page')}
              disabled={isLoading}
            >
              <Palette className="h-5 w-5" />
              <div className="text-left">
                <div className="font-medium">Design Review</div>
                <div className="text-sm text-muted-foreground">Get design feedback and suggestions</div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-start gap-2"
              onClick={() => handleSpecializedQuestion('accessibility', 'my current page layout')}
              disabled={isLoading}
            >
              <HelpCircle className="h-5 w-5" />
              <div className="text-left">
                <div className="font-medium">Accessibility Audit</div>
                <div className="text-sm text-muted-foreground">Check accessibility compliance</div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-start gap-2"
              onClick={() => handleSpecializedQuestion('performance', 'my page loading speed')}
              disabled={isLoading}
            >
              <Zap className="h-5 w-5" />
              <div className="text-left">
                <div className="font-medium">Performance Tips</div>
                <div className="text-sm text-muted-foreground">Optimize loading and performance</div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-start gap-2"
              onClick={() => handleSpecializedQuestion('mobile', 'my mobile user experience')}
              disabled={isLoading}
            >
              <Smartphone className="h-5 w-5" />
              <div className="text-left">
                <div className="font-medium">Mobile Optimization</div>
                <div className="text-sm text-muted-foreground">Improve mobile experience</div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-start gap-2"
              onClick={() => handleSpecializedQuestion('block', 'I need help with hero block configuration')}
              disabled={isLoading}
            >
              <Lightbulb className="h-5 w-5" />
              <div className="text-left">
                <div className="font-medium">Block Help</div>
                <div className="text-sm text-muted-foreground">Get help with specific blocks</div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-start gap-2"
              onClick={() => askAboutBestPractices('e-commerce page design')}
              disabled={isLoading}
            >
              <Search className="h-5 w-5" />
              <div className="text-left">
                <div className="font-medium">Best Practices</div>
                <div className="text-sm text-muted-foreground">Learn industry standards</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
