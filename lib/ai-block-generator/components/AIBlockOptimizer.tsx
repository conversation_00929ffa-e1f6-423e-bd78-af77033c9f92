'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { 
  Zap, 
  CheckCircle, 
  XCircle, 
  Loader2, 
  TrendingUp,
  Eye,
  Smartphone,
  Search,
  Accessibility
} from 'lucide-react'
import { useAIBlockOptimizer, OptimizationType } from '../hooks/useAIBlockOptimizer'
import { usePageBuilder } from '../../page-builder/context'

interface AIBlockOptimizerProps {
  selectedBlockId?: string
}

export function AIBlockOptimizer({ selectedBlockId }: AIBlockOptimizerProps) {
  const { blocks } = usePageBuilder()
  const [optimizationType, setOptimizationType] = useState<OptimizationType>('ux')
  const [goals, setGoals] = useState('')
  const [blockToOptimize, setBlockToOptimize] = useState(selectedBlockId || '')

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
    optimizeBlock,
    getOptimizationSuggestions,
    optimizationResults,
    applyOptimization,
    rejectOptimization,
    isOptimizing,
    hasOptimizationResults,
  } = useAIBlockOptimizer({
    onBlockOptimized: (original, optimized, improvements) => {
      console.log('Block optimized:', { original, optimized, improvements })
    },
    onError: (error) => {
      console.error('Optimization Error:', error)
    },
  })

  const selectedBlock = blocks.find(block => block.id === blockToOptimize)

  const optimizationTypes: Array<{ value: OptimizationType; label: string; icon: React.ReactNode; description: string }> = [
    {
      value: 'performance',
      label: 'Performance',
      icon: <Zap className="h-4 w-4" />,
      description: 'Optimize for faster loading and better Core Web Vitals'
    },
    {
      value: 'accessibility',
      label: 'Accessibility',
      icon: <Accessibility className="h-4 w-4" />,
      description: 'Improve accessibility for users with disabilities'
    },
    {
      value: 'ux',
      label: 'User Experience',
      icon: <Eye className="h-4 w-4" />,
      description: 'Enhance user interaction and engagement'
    },
    {
      value: 'seo',
      label: 'SEO',
      icon: <Search className="h-4 w-4" />,
      description: 'Optimize for search engine visibility'
    },
    {
      value: 'mobile',
      label: 'Mobile',
      icon: <Smartphone className="h-4 w-4" />,
      description: 'Optimize for mobile devices and touch interfaces'
    },
  ]

  const handleOptimizeBlock = async () => {
    if (!selectedBlock || !goals.trim()) return
    await optimizeBlock(selectedBlock, optimizationType, goals)
  }

  const handleGetSuggestions = async () => {
    if (!selectedBlock) return
    await getOptimizationSuggestions(selectedBlock)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            AI Block Optimizer
          </CardTitle>
          <CardDescription>
            Optimize your blocks for better performance, accessibility, and user experience using AI.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Block Selection */}
          <div>
            <Label>Select Block to Optimize</Label>
            <Select value={blockToOptimize} onValueChange={setBlockToOptimize}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a block to optimize" />
              </SelectTrigger>
              <SelectContent>
                {blocks.map((block) => (
                  <SelectItem key={block.id} value={block.id}>
                    {block.type} - {block.configuration?.title || block.configuration?.content?.substring(0, 30) || 'Untitled'}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Optimization Type */}
          <div>
            <Label>Optimization Type</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mt-2">
              {optimizationTypes.map((type) => (
                <Card
                  key={type.value}
                  className={`cursor-pointer transition-colors ${
                    optimizationType === type.value
                      ? 'border-primary bg-primary/5'
                      : 'hover:border-primary/50'
                  }`}
                  onClick={() => setOptimizationType(type.value)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      {type.icon}
                      <span className="font-medium">{type.label}</span>
                    </div>
                    <p className="text-sm text-muted-foreground">{type.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Optimization Goals */}
          <div>
            <Label>Optimization Goals</Label>
            <Textarea
              value={goals}
              onChange={(e) => setGoals(e.target.value)}
              placeholder="Describe what you want to achieve with this optimization..."
              rows={3}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button 
              onClick={handleOptimizeBlock}
              disabled={!selectedBlock || !goals.trim() || isOptimizing}
              className="flex-1"
            >
              {isOptimizing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Optimizing...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Optimize Block
                </>
              )}
            </Button>
            
            <Button 
              variant="outline"
              onClick={handleGetSuggestions}
              disabled={!selectedBlock || isLoading}
            >
              Get Suggestions
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <div className="flex items-center gap-2 text-destructive">
                <XCircle className="h-4 w-4" />
                <span className="text-sm font-medium">Error</span>
              </div>
              <p className="text-sm text-destructive/80 mt-1">{error.message}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Optimization Results */}
      {hasOptimizationResults && optimizationResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Optimization Results
            </CardTitle>
            <CardDescription>
              Review the suggested optimizations for your {optimizationResults.type} improvements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Improvements List */}
              <div>
                <Label className="text-sm font-medium">Improvements Made:</Label>
                <div className="mt-2 space-y-1">
                  {optimizationResults.improvements.map((improvement, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">{improvement}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Configuration Comparison */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Original Configuration:</Label>
                  <ScrollArea className="h-32 mt-2">
                    <pre className="text-xs bg-muted p-2 rounded">
                      {JSON.stringify(optimizationResults.original.configuration, null, 2)}
                    </pre>
                  </ScrollArea>
                </div>
                <div>
                  <Label className="text-sm font-medium">Optimized Configuration:</Label>
                  <ScrollArea className="h-32 mt-2">
                    <pre className="text-xs bg-muted p-2 rounded">
                      {JSON.stringify(optimizationResults.optimized.configuration, null, 2)}
                    </pre>
                  </ScrollArea>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button onClick={applyOptimization} className="flex-1">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Apply Optimization
                </Button>
                <Button variant="outline" onClick={rejectOptimization}>
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Chat Interface for Suggestions */}
      {messages.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>AI Suggestions</CardTitle>
            <CardDescription>
              AI-powered recommendations for your block optimization
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-64">
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg px-3 py-2 ${
                        message.role === 'user'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted'
                      }`}
                    >
                      {message.parts.map((part, index) => {
                        if (part.type === 'text') {
                          return <p key={index} className="text-sm">{part.text}</p>
                        }
                        return null
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
            <Separator className="my-4" />
            <form onSubmit={handleSubmit}>
              <div className="flex gap-2">
                <input
                  value={input}
                  onChange={handleInputChange}
                  placeholder="Ask for more optimization advice..."
                  className="flex-1 px-3 py-2 border rounded-md"
                  disabled={isLoading}
                />
                <Button type="submit" disabled={isLoading || !input.trim()}>
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    'Send'
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
