'use client'

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Sparkles,
  RefreshCw,
  Settings,
  Eye,
  EyeOff,
  Loader2,
  AlertTriangle,
  CheckCircle,
  Code,
  Play,
  Terminal,
  Zap,
  Shield,
  Activity,
  Database,
  Cpu,
  Smartphone
} from 'lucide-react'
import { useAIBlockGenerator, useAIBlockOptimizer } from '../hooks'
import {
  blockEngine,
  type DynamicBlockData,
  type BlockExecutionContext,
  type BlockAPI,
  type BlockUtils
} from '../engine/BlockEngine'
import { useJSXParser } from '../engine/JSXParser'
import { cn } from '@/lib/utils'

export type DynamicAIBlockData = DynamicBlockData

interface DynamicAIBlockProps {
  blockData: DynamicAIBlockData
  onUpdate?: (updatedBlock: DynamicAIBlockData) => void
  onDelete?: () => void
  onEvent?: (event: string, data?: any) => void
  isEditing?: boolean
  showAIControls?: boolean
  autoOptimize?: boolean
  adaptiveRendering?: boolean
  enableCustomJSX?: boolean
  enableLogicExecution?: boolean
  enableEventHandling?: boolean
  isolatedExecution?: boolean
  customComponents?: Record<string, any>
  globalEventHandlers?: Record<string, Function>
}

export function DynamicAIBlock({
  blockData,
  onUpdate,
  onDelete,
  onEvent,
  isEditing = false,
  showAIControls = true,
  autoOptimize = false,
  adaptiveRendering = true,
  enableCustomJSX = true,
  enableLogicExecution = true,
  enableEventHandling = true, // Used for event system setup (future implementation)
  isolatedExecution = true,
  customComponents = {},
  globalEventHandlers = {},
}: DynamicAIBlockProps) {
  // State management
  const [isOptimizing, setIsOptimizing] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const [isVisible, setIsVisible] = useState(true)
  const [renderMode, setRenderMode] = useState<'preview' | 'edit' | 'code' | 'jsx' | 'logic'>('preview')
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([])
  const [isExecuting, setIsExecuting] = useState(false)
  const [executionLogs, setExecutionLogs] = useState<string[]>([])
  const [blockState, setBlockState] = useState<Record<string, any>>({})
  const [customJSX, setCustomJSX] = useState(blockData.jsx || '')
  const [customLogic, setCustomLogic] = useState(blockData.logic || '')
  const [performanceMetrics, setPerformanceMetrics] = useState<any>(null)

  // Refs for execution context
  const executionContextRef = useRef<BlockExecutionContext | null>(null)
  const blockRef = useRef<HTMLDivElement>(null)
  const logicExecutionRef = useRef<any>(null) // Reserved for future logic execution

  // Hooks
  const { generateBlock, isGenerating } = useAIBlockGenerator()
  const { optimizeBlock, optimizationResults, applyOptimization } = useAIBlockOptimizer()

  // Event handlers for the block
  const blockEventHandlers = useMemo(() => ({
    ...globalEventHandlers,
    handleClick: (event: any) => {
      onEvent?.('click', { blockId: blockData.id, event })
      logExecution('Click event triggered')
    },
    handleChange: (value: any) => {
      onEvent?.('change', { blockId: blockData.id, value })
      logExecution(`Change event: ${value}`)
    },
    handleSubmit: (data: any) => {
      onEvent?.('submit', { blockId: blockData.id, data })
      logExecution('Submit event triggered')
    },
    handleCustomEvent: (eventName: string, data?: any) => {
      onEvent?.(eventName, { blockId: blockData.id, data })
      logExecution(`Custom event: ${eventName}`)
    },
  }), [globalEventHandlers, onEvent, blockData.id])

  // JSX Parser with custom components and event handlers
  const { render: renderJSX, validate: validateJSX } = useJSXParser(
    customComponents,
    blockEventHandlers
  )

  // Utility functions
  const logExecution = useCallback((message: string) => {
    const timestamp = new Date().toISOString()
    setExecutionLogs(prev => [...prev, `[${timestamp}] ${message}`])
  }, [])

  const executeCustomLogic = useCallback(async () => {
    if (!enableLogicExecution || !customLogic.trim()) return

    setIsExecuting(true)
    try {
      const context: BlockExecutionContext = {
        blockId: blockData.id,
        props: blockData.configuration,
        state: blockState,
        setState: setBlockState,
        emit: (event: string, data?: any) => onEvent?.(event, { blockId: blockData.id, data }),
        api: createBlockAPI(),
        utils: createBlockUtils(),
      }

      executionContextRef.current = context
      const result = blockEngine.getStateManager().setState(blockData.id, blockState)

      logExecution('Custom logic executed successfully')
      return result
    } catch (error) {
      logExecution(`Logic execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      throw error
    } finally {
      setIsExecuting(false)
    }
  }, [enableLogicExecution, customLogic, blockData.id, blockData.configuration, blockState, onEvent])

  const createBlockAPI = useCallback((): BlockAPI => ({
    getData: async (key: string) => {
      return blockEngine.getStateManager().getState(blockData.id)[key]
    },
    setData: async (key: string, value: any) => {
      setBlockState(prev => ({ ...prev, [key]: value }))
    },
    fetch: async (url: string, options?: RequestInit) => {
      if (isolatedExecution) {
        // Sandboxed fetch with restrictions
        return fetch(url, { ...options, mode: 'cors' })
      }
      return fetch(url, options)
    },
    getStorage: (key: string) => {
      return localStorage.getItem(`block_${blockData.id}_${key}`)
    },
    setStorage: (key: string, value: string) => {
      localStorage.setItem(`block_${blockData.id}_${key}`, value)
    },
    on: (event: string, handler: Function) => {
      blockEngine.getEventSystem().on(blockData.id, event, handler)
    },
    off: (event: string, handler: Function) => {
      blockEngine.getEventSystem().off(blockData.id, event, handler)
    },
    emit: (event: string, data?: any) => {
      blockEngine.getEventSystem().emit(blockData.id, event, data)
    },
    showToast: (message: string, type = 'info') => {
      logExecution(`Toast: ${type} - ${message}`)
    },
    showModal: (_content: React.ReactNode, _options?: any) => {
      logExecution('Modal opened')
    },
    track: (event: string, properties?: Record<string, any>) => {
      logExecution(`Analytics: ${event} - ${JSON.stringify(properties)}`)
    },
  }), [blockData.id, isolatedExecution])

  const createBlockUtils = useCallback((): BlockUtils => ({
    formatText: (text: string, format: string) => {
      switch (format) {
        case 'uppercase': return text.toUpperCase()
        case 'lowercase': return text.toLowerCase()
        case 'capitalize': return text.charAt(0).toUpperCase() + text.slice(1)
        default: return text
      }
    },
    truncate: (text: string, length: number) => {
      return text.length > length ? text.substring(0, length) + '...' : text
    },
    formatDate: (date: Date | string, _format: string) => {
      const d = new Date(date)
      return d.toLocaleDateString()
    },
    timeAgo: (date: Date | string) => {
      const now = new Date()
      const past = new Date(date)
      const diff = now.getTime() - past.getTime()
      const minutes = Math.floor(diff / 60000)
      if (minutes < 60) return `${minutes}m ago`
      const hours = Math.floor(minutes / 60)
      if (hours < 24) return `${hours}h ago`
      const days = Math.floor(hours / 24)
      return `${days}d ago`
    },
    formatNumber: (num: number, options?: Intl.NumberFormatOptions) => {
      return new Intl.NumberFormat('en-US', options).format(num)
    },
    formatCurrency: (amount: number, currency = 'USD') => {
      return new Intl.NumberFormat('en-US', { style: 'currency', currency }).format(amount)
    },
    validate: (data: any, schema: any) => {
      try {
        schema.parse(data)
        return { success: true }
      } catch (error) {
        return { success: false, errors: ['Validation failed'] }
      }
    },
    hexToRgb: (hex: string) => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null
    },
    rgbToHex: (r: number, g: number, b: number) => {
      return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
    },
    cn: (...classes: string[]) => cn(...classes),
    animate: (element: HTMLElement, keyframes: Keyframe[], options?: KeyframeAnimationOptions) => {
      return element.animate(keyframes, options)
    },
  }), [])

  // Validation on mount and updates
  useEffect(() => {
    const validation = blockEngine.validateBlock(blockData)
    setValidationErrors(validation.errors)

    // Initialize logic execution context if needed
    if (enableLogicExecution && enableEventHandling) {
      logicExecutionRef.current = {
        blockId: blockData.id,
        enableEventHandling,
        initialized: true
      }
    }
  }, [blockData, enableLogicExecution, enableEventHandling])

  // Performance monitoring
  useEffect(() => {
    const startTime = performance.now()

    return () => {
      const endTime = performance.now()
      setPerformanceMetrics({
        renderTime: endTime - startTime,
        timestamp: new Date().toISOString(),
      })
    }
  }, [blockData])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      blockEngine.cleanup(blockData.id)
    }
  }, [blockData.id])

  // Auto-optimization when enabled
  const handleAutoOptimize = useCallback(async () => {
    if (!autoOptimize || !blockData.aiGenerated) return

    setIsOptimizing(true)
    try {
      await optimizeBlock(blockData, 'ux', 'Improve user experience and performance')
      if (optimizationResults) {
        const optimizedBlock = {
          ...optimizationResults.optimized,
          lastOptimized: new Date().toISOString(),
        }
        onUpdate?.(optimizedBlock)
      }
    } catch (error) {
      console.error('Auto-optimization failed:', error)
    } finally {
      setIsOptimizing(false)
    }
  }, [autoOptimize, blockData, optimizeBlock, optimizationResults, onUpdate])

  useEffect(() => {
    if (autoOptimize && blockData.aiGenerated && !blockData.lastOptimized) {
      handleAutoOptimize()
    }
  }, [autoOptimize, blockData, handleAutoOptimize])

  // Adaptive configuration based on viewport
  const adaptiveConfig = useMemo(() => {
    if (!adaptiveRendering) return blockData.configuration

    const viewport = typeof window !== 'undefined' ? {
      width: window.innerWidth,
      height: window.innerHeight,
      isMobile: window.innerWidth < 768,
      isTablet: window.innerWidth >= 768 && window.innerWidth < 1024,
    } : null

    if (!viewport) return blockData.configuration

    const adapted = { ...blockData.configuration }

    if (viewport.isMobile && blockData.responsive?.mobile) {
      Object.assign(adapted, blockData.responsive.mobile)
    } else if (viewport.isTablet && blockData.responsive?.tablet) {
      Object.assign(adapted, blockData.responsive.tablet)
    }

    return adapted
  }, [blockData, adaptiveRendering])

  // Render block content based on mode
  const renderBlockContent = useCallback(() => {
    switch (renderMode) {
      case 'preview':
        // Custom JSX rendering if available
        if (enableCustomJSX && customJSX.trim()) {
          try {
            return renderJSX(customJSX)
          } catch (error) {
            return (
              <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
                <p className="text-red-600 font-medium">JSX Rendering Error</p>
                <p className="text-red-500 text-sm">{error instanceof Error ? error.message : 'Unknown error'}</p>
              </div>
            )
          }
        }

        // Default block rendering
        return (
          <div className="p-4 border rounded-lg bg-background">
            <div className="text-center text-muted-foreground">
              <div className="text-lg font-medium">{blockData.type} Block</div>
              <div className="text-sm mt-2">
                {adaptiveConfig.title || adaptiveConfig.content || 'AI Generated Block'}
              </div>
              {blockData.aiGenerated && (
                <Badge variant="secondary" className="mt-2">
                  <Sparkles className="h-3 w-3 mr-1" />
                  AI Generated
                </Badge>
              )}
            </div>
          </div>
        )

      case 'edit':
        return (
          <div className="p-4 border rounded-lg">
            <div className="space-y-4">
              <div className="text-sm font-medium">Block Configuration</div>
              <pre className="text-xs bg-muted p-3 rounded overflow-auto max-h-40">
                {JSON.stringify(adaptiveConfig, null, 2)}
              </pre>
            </div>
          </div>
        )

      case 'code':
        return (
          <div className="p-4 border rounded-lg">
            <div className="space-y-4">
              <div className="text-sm font-medium">Full Block Data</div>
              <pre className="text-xs bg-muted p-3 rounded overflow-auto max-h-60">
                {JSON.stringify(blockData, null, 2)}
              </pre>
            </div>
          </div>
        )

      case 'jsx':
        return (
          <div className="p-4 border rounded-lg space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Custom JSX Template</Label>
              <Button
                size="sm"
                onClick={() => {
                  const validation = validateJSX(customJSX)
                  if (validation.valid) {
                    onUpdate?.({ ...blockData, jsx: customJSX })
                  } else {
                    setValidationErrors(validation.errors)
                  }
                }}
                disabled={!enableCustomJSX}
              >
                <Play className="h-4 w-4 mr-1" />
                Apply
              </Button>
            </div>
            <Textarea
              value={customJSX}
              onChange={(e) => setCustomJSX(e.target.value)}
              placeholder="Enter custom JSX template..."
              className="font-mono text-sm"
              rows={10}
              disabled={!enableCustomJSX}
            />
            {enableCustomJSX && customJSX && (
              <div className="text-xs text-muted-foreground">
                Preview: {validateJSX(customJSX).valid ? '✅ Valid JSX' : '❌ Invalid JSX'}
              </div>
            )}
          </div>
        )

      case 'logic':
        return (
          <div className="p-4 border rounded-lg space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Custom Logic</Label>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={executeCustomLogic}
                  disabled={!enableLogicExecution || isExecuting}
                >
                  {isExecuting ? (
                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  ) : (
                    <Play className="h-4 w-4 mr-1" />
                  )}
                  Execute
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    onUpdate?.({ ...blockData, logic: customLogic })
                  }}
                  disabled={!enableLogicExecution}
                >
                  Save
                </Button>
              </div>
            </div>
            <Textarea
              value={customLogic}
              onChange={(e) => setCustomLogic(e.target.value)}
              placeholder="Enter custom JavaScript logic..."
              className="font-mono text-sm"
              rows={8}
              disabled={!enableLogicExecution}
            />
            {executionLogs.length > 0 && (
              <div>
                <Label className="text-xs font-medium">Execution Logs</Label>
                <ScrollArea className="h-32 w-full border rounded p-2 mt-1">
                  <div className="space-y-1">
                    {executionLogs.map((log, index) => (
                      <div key={index} className="text-xs font-mono text-muted-foreground">
                        {log}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}
          </div>
        )

      default:
        return null
    }
  }, [
    renderMode, enableCustomJSX, customJSX, renderJSX, blockData, adaptiveConfig,
    enableLogicExecution, customLogic, executeCustomLogic, isExecuting, executionLogs,
    validateJSX, onUpdate
  ])

  // Additional handlers
  const handleRegenerateBlock = useCallback(async () => {
    if (!blockData.aiPrompt) return

    try {
      await generateBlock(
        blockData.type,
        blockData.aiPrompt,
        'Regenerate with improvements',
        'modern'
      )
    } catch (error) {
      console.error('Block regeneration failed:', error)
    }
  }, [blockData.aiPrompt, blockData.type, generateBlock])

  const handleManualOptimize = useCallback(async (type: 'performance' | 'accessibility' | 'ux' | 'seo' | 'mobile') => {
    setIsOptimizing(true)
    try {
      await optimizeBlock(blockData, type, `Optimize for ${type}`)
    } catch (error) {
      console.error('Manual optimization failed:', error)
    } finally {
      setIsOptimizing(false)
    }
  }, [blockData, optimizeBlock])

  const generateAISuggestions = useCallback(async () => {
    const suggestions = [
      'Consider adding a call-to-action button',
      'Improve color contrast for better accessibility',
      'Add loading states for better UX',
      'Optimize images for faster loading',
      'Include structured data for SEO',
    ]
    setAiSuggestions(suggestions)
  }, [])

  if (!isVisible) {
    return (
      <div className="p-2 border border-dashed rounded-lg opacity-50">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsVisible(true)}
          className="w-full"
        >
          <EyeOff className="h-4 w-4 mr-2" />
          Show {blockData.type} Block
        </Button>
      </div>
    )
  }

  return (
    <Card className={cn("relative", isEditing && "ring-2 ring-primary")} ref={blockRef}>
      {/* AI Controls Header */}
      {showAIControls && (
        <div className="flex items-center justify-between p-3 border-b bg-muted/50">
          <div className="flex items-center gap-2">
            <Sparkles className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">Dynamic AI Block</span>
            {blockData.aiGenerated && (
              <Badge variant="outline" className="text-xs">
                v{blockData.aiVersion || '1.0'}
              </Badge>
            )}
            {isolatedExecution && (
              <Badge variant="secondary" className="text-xs">
                <Shield className="h-3 w-3 mr-1" />
                Sandboxed
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-1">
            <Tabs value={renderMode} onValueChange={(value) => setRenderMode(value as any)}>
              <TabsList className="h-8">
                <TabsTrigger value="preview" className="text-xs px-2">
                  <Eye className="h-3 w-3" />
                </TabsTrigger>
                <TabsTrigger value="edit" className="text-xs px-2">
                  <Settings className="h-3 w-3" />
                </TabsTrigger>
                <TabsTrigger value="code" className="text-xs px-2">
                  <Code className="h-3 w-3" />
                </TabsTrigger>
                {enableCustomJSX && (
                  <TabsTrigger value="jsx" className="text-xs px-2">
                    <Terminal className="h-3 w-3" />
                  </TabsTrigger>
                )}
                {enableLogicExecution && (
                  <TabsTrigger value="logic" className="text-xs px-2">
                    <Cpu className="h-3 w-3" />
                  </TabsTrigger>
                )}
              </TabsList>
            </Tabs>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
            >
              <EyeOff className="h-4 w-4" />
            </Button>

            {blockData.aiPrompt && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRegenerateBlock}
                disabled={isGenerating}
                title="Regenerate block"
              >
                {isGenerating ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
            )}

            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleManualOptimize('ux')}
              disabled={isOptimizing}
              title="Optimize block"
            >
              {isOptimizing ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Zap className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      )}

      {/* Performance Metrics */}
      {performanceMetrics && isEditing && (
        <div className="px-3 py-1 bg-muted/30 border-b">
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Activity className="h-3 w-3" />
              Render: {performanceMetrics.renderTime?.toFixed(2)}ms
            </div>
            {blockState && Object.keys(blockState).length > 0 && (
              <div className="flex items-center gap-1">
                <Database className="h-3 w-3" />
                State: {Object.keys(blockState).length} keys
              </div>
            )}
          </div>
        </div>
      )}

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <div className="p-3 bg-destructive/10 border-b">
          <div className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm font-medium">Validation Errors</span>
          </div>
          <ul className="text-xs text-destructive/80 mt-1 list-disc list-inside">
            {validationErrors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Block Content */}
      <CardContent className="p-0">
        {renderBlockContent()}
      </CardContent>

      {/* Optimization Results */}
      {optimizationResults && (
        <div className="p-3 border-t bg-green-50 dark:bg-green-950/20">
          <div className="flex items-center gap-2 text-green-700 dark:text-green-400">
            <CheckCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Optimization Available</span>
          </div>
          <div className="text-xs text-green-600 dark:text-green-500 mt-1">
            {optimizationResults.improvements.join(', ')}
          </div>
          <Button
            size="sm"
            onClick={applyOptimization}
            className="mt-2"
          >
            Apply Optimization
          </Button>
        </div>
      )}

      {/* AI Suggestions */}
      {aiSuggestions.length > 0 && (
        <div className="p-3 border-t bg-blue-50 dark:bg-blue-950/20">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-blue-700 dark:text-blue-400">
              AI Suggestions
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setAiSuggestions([])}
            >
              ×
            </Button>
          </div>
          <ul className="text-xs text-blue-600 dark:text-blue-500 mt-2 space-y-1">
            {aiSuggestions.map((suggestion, index) => (
              <li key={index}>• {suggestion}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Advanced Controls */}
      {isEditing && (
        <div className="p-3 border-t bg-muted/30">
          <div className="flex flex-wrap gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={generateAISuggestions}
            >
              <Sparkles className="h-4 w-4 mr-1" />
              AI Suggestions
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={() => handleManualOptimize('accessibility')}
              disabled={isOptimizing}
            >
              <Shield className="h-4 w-4 mr-1" />
              Accessibility
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={() => handleManualOptimize('performance')}
              disabled={isOptimizing}
            >
              <Zap className="h-4 w-4 mr-1" />
              Performance
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={() => handleManualOptimize('seo')}
              disabled={isOptimizing}
            >
              <Activity className="h-4 w-4 mr-1" />
              SEO
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={() => handleManualOptimize('mobile')}
              disabled={isOptimizing}
            >
              <Smartphone className="h-4 w-4 mr-1" />
              Mobile
            </Button>

            {onDelete && (
              <Button
                size="sm"
                variant="destructive"
                onClick={onDelete}
                className="ml-auto"
              >
                Delete
              </Button>
            )}
          </div>
        </div>
      )}
    </Card>
  )
}
