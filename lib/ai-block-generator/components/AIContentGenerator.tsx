'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { 
  PenTool, 
  Copy, 
  RefreshCw, 
  Loader2, 
  CheckCircle,
  Sparkles,
  Type,
  MessageSquare,
  Star,
  ShoppingBag
} from 'lucide-react'
import { useAIContentGenerator, ContentType, ContentLength } from '../hooks/useAIContentGenerator'

export function AIContentGenerator() {
  const [contentType, setContentType] = useState<ContentType>('headline')
  const [context, setContext] = useState('')
  const [tone, setTone] = useState('professional')
  const [length, setLength] = useState<ContentLength>('medium')
  const [keywords, setKeywords] = useState('')
  const [variationCount, setVariationCount] = useState(3)

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
    generateContent,
    generateVariations,
    improveContent,
    generatedContent,
    clearGeneratedContent,
    startNewSession,
    isGenerating,
    hasGeneratedContent,
  } = useAIContentGenerator({
    onContentGenerated: (content, metadata) => {
      console.log('Content generated:', { content, metadata })
    },
    onError: (error) => {
      console.error('Content Generation Error:', error)
    },
  })

  const contentTypes: Array<{ value: ContentType; label: string; icon: React.ReactNode; description: string }> = [
    {
      value: 'headline',
      label: 'Headline',
      icon: <Type className="h-4 w-4" />,
      description: 'Compelling headlines and titles'
    },
    {
      value: 'description',
      label: 'Description',
      icon: <MessageSquare className="h-4 w-4" />,
      description: 'Detailed descriptions and explanations'
    },
    {
      value: 'cta',
      label: 'Call to Action',
      icon: <Sparkles className="h-4 w-4" />,
      description: 'Action-oriented button text and CTAs'
    },
    {
      value: 'features',
      label: 'Features',
      icon: <CheckCircle className="h-4 w-4" />,
      description: 'Feature lists and benefit descriptions'
    },
    {
      value: 'testimonial',
      label: 'Testimonial',
      icon: <Star className="h-4 w-4" />,
      description: 'Customer testimonials and reviews'
    },
    {
      value: 'product-copy',
      label: 'Product Copy',
      icon: <ShoppingBag className="h-4 w-4" />,
      description: 'Product descriptions and marketing copy'
    },
  ]

  const toneOptions = [
    { value: 'professional', label: 'Professional' },
    { value: 'casual', label: 'Casual' },
    { value: 'friendly', label: 'Friendly' },
    { value: 'authoritative', label: 'Authoritative' },
    { value: 'playful', label: 'Playful' },
  ]

  const lengthOptions: Array<{ value: ContentLength; label: string }> = [
    { value: 'short', label: 'Short (1-15 words)' },
    { value: 'medium', label: 'Medium (15-50 words)' },
    { value: 'long', label: 'Long (50+ words)' },
  ]

  const handleGenerateContent = async () => {
    if (!context.trim()) return
    const keywordArray = keywords.split(',').map(k => k.trim()).filter(k => k.length > 0)
    await generateContent(contentType, context, tone, length, keywordArray)
  }

  const handleGenerateVariations = async () => {
    if (!context.trim()) return
    await generateVariations(contentType, context, variationCount, tone)
  }

  const handleImproveContent = async (currentContent: string) => {
    const improvementGoals = `Improve this ${contentType} content for better ${tone} tone and ${length} length`
    await improveContent(currentContent, improvementGoals, contentType)
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PenTool className="h-5 w-5" />
            AI Content Generator
          </CardTitle>
          <CardDescription>
            Generate compelling content for your blocks using AI. Create headlines, descriptions, CTAs, and more.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Content Type Selection */}
          <div>
            <Label>Content Type</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mt-2">
              {contentTypes.map((type) => (
                <Card
                  key={type.value}
                  className={`cursor-pointer transition-colors ${
                    contentType === type.value
                      ? 'border-primary bg-primary/5'
                      : 'hover:border-primary/50'
                  }`}
                  onClick={() => setContentType(type.value)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      {type.icon}
                      <span className="font-medium">{type.label}</span>
                    </div>
                    <p className="text-sm text-muted-foreground">{type.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Context Input */}
          <div>
            <Label>Context & Description</Label>
            <Textarea
              value={context}
              onChange={(e) => setContext(e.target.value)}
              placeholder="Describe your business, product, or service. What should the content be about?"
              rows={3}
            />
          </div>

          {/* Content Settings */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label>Tone</Label>
              <Select value={tone} onValueChange={setTone}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {toneOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label>Length</Label>
              <Select value={length} onValueChange={(value: ContentLength) => setLength(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {lengthOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label>Variations</Label>
              <Select value={variationCount.toString()} onValueChange={(value) => setVariationCount(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 variation</SelectItem>
                  <SelectItem value="3">3 variations</SelectItem>
                  <SelectItem value="5">5 variations</SelectItem>
                  <SelectItem value="10">10 variations</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Keywords */}
          <div>
            <Label>Keywords (Optional)</Label>
            <Input
              value={keywords}
              onChange={(e) => setKeywords(e.target.value)}
              placeholder="Enter keywords separated by commas"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button 
              onClick={handleGenerateContent}
              disabled={!context.trim() || isGenerating}
              className="flex-1"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Generate Content
                </>
              )}
            </Button>
            
            <Button 
              variant="outline"
              onClick={handleGenerateVariations}
              disabled={!context.trim() || isGenerating}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Generate Variations
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <div className="flex items-center gap-2 text-destructive">
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm font-medium">Error</span>
              </div>
              <p className="text-sm text-destructive/80 mt-1">{error.message}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Generated Content */}
      {hasGeneratedContent && generatedContent && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Generated Content
            </CardTitle>
            <CardDescription>
              {generatedContent.type} content generated with {generatedContent.metadata?.tone || 'professional'} tone
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <p className="text-lg">{generatedContent.content}</p>
              </div>
              
              {/* Content Metadata */}
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">
                  Type: {generatedContent.type}
                </Badge>
                {generatedContent.metadata?.tone && (
                  <Badge variant="secondary">
                    Tone: {generatedContent.metadata.tone}
                  </Badge>
                )}
                {generatedContent.metadata?.wordCount && (
                  <Badge variant="secondary">
                    Words: {generatedContent.metadata.wordCount}
                  </Badge>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                <Button 
                  onClick={() => copyToClipboard(generatedContent.content)}
                  variant="outline"
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy
                </Button>
                <Button 
                  onClick={() => handleImproveContent(generatedContent.content)}
                  variant="outline"
                  disabled={isLoading}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Improve
                </Button>
                <Button 
                  onClick={clearGeneratedContent}
                  variant="outline"
                >
                  Clear
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Chat Interface */}
      {messages.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Content Assistance</CardTitle>
            <CardDescription>
              Chat with AI for content ideas and improvements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-64">
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg px-3 py-2 ${
                        message.role === 'user'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted'
                      }`}
                    >
                      {message.parts.map((part, index) => {
                        if (part.type === 'text') {
                          return <p key={index} className="text-sm">{part.text}</p>
                        }
                        return null
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
            <Separator className="my-4" />
            <form onSubmit={handleSubmit}>
              <div className="flex gap-2">
                <input
                  value={input}
                  onChange={handleInputChange}
                  placeholder="Ask for content ideas or improvements..."
                  className="flex-1 px-3 py-2 border rounded-md"
                  disabled={isLoading}
                />
                <Button type="submit" disabled={isLoading || !input.trim()}>
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    'Send'
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
