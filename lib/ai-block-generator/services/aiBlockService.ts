'use client'

import { z } from 'zod'

// Enhanced AI Block Service with caching, error handling, and rate limiting
export class AIBlockService {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  private rateLimitMap = new Map<string, { count: number; resetTime: number }>()
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5 minutes
  private readonly RATE_LIMIT = 10 // requests per minute
  private readonly RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute

  // Block generation with caching and validation
  async generateBlock(
    blockType: string,
    requirements: string,
    context?: string,
    style?: string,
    options: {
      useCache?: boolean
      validateOutput?: boolean
      retryOnFailure?: boolean
    } = {}
  ): Promise<any> {
    const { useCache = true, validateOutput = true, retryOnFailure = true } = options
    
    // Check rate limit
    if (!this.checkRateLimit('generateBlock')) {
      throw new Error('Rate limit exceeded. Please try again later.')
    }

    // Generate cache key
    const cacheKey = this.generateCacheKey('block', { blockType, requirements, context, style })
    
    // Check cache
    if (useCache) {
      const cached = this.getFromCache(cacheKey)
      if (cached) {
        return cached
      }
    }

    try {
      const response = await fetch('/api/ai-blocks/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [{
            role: 'user',
            content: `Generate a ${blockType} block with requirements: ${requirements}${
              context ? ` Context: ${context}` : ''
            }${style ? ` Style: ${style}` : ''}`
          }],
          action: 'generateBlock',
          blockType,
          requirements,
          context,
          style,
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await this.parseStreamingResponse(response)
      
      // Validate output
      if (validateOutput) {
        this.validateBlockOutput(result)
      }

      // Cache result
      if (useCache) {
        this.setCache(cacheKey, result, this.CACHE_TTL)
      }

      return result
    } catch (error) {
      if (retryOnFailure && error instanceof Error && error.message.includes('network')) {
        // Retry once on network errors
        await new Promise(resolve => setTimeout(resolve, 1000))
        return this.generateBlock(blockType, requirements, context, style, { ...options, retryOnFailure: false })
      }
      
      throw this.enhanceError(error, 'Block generation failed')
    }
  }

  // Layout generation with advanced options
  async generateLayout(
    pageType: string,
    requirements: string,
    sections: string[],
    style?: string,
    target?: string,
    options: {
      useCache?: boolean
      validateOutput?: boolean
      includeMetadata?: boolean
    } = {}
  ): Promise<any> {
    const { useCache = true, validateOutput = true, includeMetadata = true } = options
    
    if (!this.checkRateLimit('generateLayout')) {
      throw new Error('Rate limit exceeded. Please try again later.')
    }

    const cacheKey = this.generateCacheKey('layout', { pageType, requirements, sections, style, target })
    
    if (useCache) {
      const cached = this.getFromCache(cacheKey)
      if (cached) {
        return cached
      }
    }

    try {
      const response = await fetch('/api/ai-blocks/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [{
            role: 'user',
            content: `Generate a ${pageType} page layout with sections: ${sections.join(', ')}. Requirements: ${requirements}${
              style ? ` Style: ${style}` : ''
            }${target ? ` Target: ${target}` : ''}`
          }],
          action: 'generateLayout',
          pageType,
          requirements,
          sections,
          style,
          target,
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await this.parseStreamingResponse(response)
      
      if (validateOutput) {
        this.validateLayoutOutput(result)
      }

      // Add metadata if requested
      if (includeMetadata) {
        result.metadata = {
          ...result.metadata,
          generatedAt: new Date().toISOString(),
          generatedBy: 'AI Block Service',
          version: '2.0',
          cacheKey,
        }
      }

      if (useCache) {
        this.setCache(cacheKey, result, this.CACHE_TTL)
      }

      return result
    } catch (error) {
      throw this.enhanceError(error, 'Layout generation failed')
    }
  }

  // Content generation with tone analysis
  async generateContent(
    contentType: string,
    context: string,
    tone: string = 'professional',
    length: string = 'medium',
    keywords: string[] = [],
    options: {
      useCache?: boolean
      analyzeTone?: boolean
      includeMetrics?: boolean
    } = {}
  ): Promise<any> {
    const { useCache = true, analyzeTone = true, includeMetrics = true } = options
    
    if (!this.checkRateLimit('generateContent')) {
      throw new Error('Rate limit exceeded. Please try again later.')
    }

    const cacheKey = this.generateCacheKey('content', { contentType, context, tone, length, keywords })
    
    if (useCache) {
      const cached = this.getFromCache(cacheKey)
      if (cached) {
        return cached
      }
    }

    try {
      const response = await fetch('/api/ai-blocks/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [{
            role: 'user',
            content: `Generate ${contentType} content for: ${context}. Tone: ${tone}, Length: ${length}${
              keywords.length > 0 ? `, Keywords: ${keywords.join(', ')}` : ''
            }`
          }],
          action: 'generateContent',
          contentType,
          context,
          tone,
          length,
          keywords,
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await this.parseStreamingResponse(response)
      
      // Analyze tone if requested
      if (analyzeTone) {
        result.toneAnalysis = this.analyzeTone(result.content)
      }

      // Include metrics if requested
      if (includeMetrics) {
        result.metrics = {
          wordCount: result.content.split(' ').length,
          characterCount: result.content.length,
          readabilityScore: this.calculateReadabilityScore(result.content),
          sentimentScore: this.calculateSentimentScore(result.content),
        }
      }

      if (useCache) {
        this.setCache(cacheKey, result, this.CACHE_TTL)
      }

      return result
    } catch (error) {
      throw this.enhanceError(error, 'Content generation failed')
    }
  }

  // Batch operations for multiple blocks
  async generateMultipleBlocks(
    requests: Array<{
      blockType: string
      requirements: string
      context?: string
      style?: string
    }>,
    options: {
      parallel?: boolean
      maxConcurrency?: number
      failFast?: boolean
    } = {}
  ): Promise<any[]> {
    const { parallel = true, maxConcurrency = 3, failFast = false } = options
    
    if (!parallel) {
      const results = []
      for (const request of requests) {
        try {
          const result = await this.generateBlock(
            request.blockType,
            request.requirements,
            request.context,
            request.style
          )
          results.push(result)
        } catch (error) {
          if (failFast) throw error
          results.push({ error: error instanceof Error ? error.message : 'Unknown error' })
        }
      }
      return results
    }

    // Parallel processing with concurrency limit
    const chunks = this.chunkArray(requests, maxConcurrency)
    const results: any[] = []

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (request) => {
        try {
          return await this.generateBlock(
            request.blockType,
            request.requirements,
            request.context,
            request.style
          )
        } catch (error) {
          if (failFast) throw error
          return { error: error instanceof Error ? error.message : 'Unknown error' }
        }
      })

      const chunkResults = await Promise.all(chunkPromises)
      results.push(...chunkResults)
    }

    return results
  }

  // Cache management
  private generateCacheKey(type: string, params: any): string {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key]
        return result
      }, {} as any)
    
    return `${type}:${JSON.stringify(sortedParams)}`
  }

  private getFromCache(key: string): any | null {
    const cached = this.cache.get(key)
    if (!cached) return null
    
    if (Date.now() > cached.timestamp + cached.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return cached.data
  }

  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    })
    
    // Clean up expired entries periodically
    if (this.cache.size > 100) {
      this.cleanupCache()
    }
  }

  private cleanupCache(): void {
    const now = Date.now()
    for (const [key, value] of this.cache.entries()) {
      if (now > value.timestamp + value.ttl) {
        this.cache.delete(key)
      }
    }
  }

  // Rate limiting
  private checkRateLimit(operation: string): boolean {
    const key = `${operation}:${this.getClientId()}`
    const now = Date.now()
    const limit = this.rateLimitMap.get(key)
    
    if (!limit || now > limit.resetTime) {
      this.rateLimitMap.set(key, {
        count: 1,
        resetTime: now + this.RATE_LIMIT_WINDOW,
      })
      return true
    }
    
    if (limit.count >= this.RATE_LIMIT) {
      return false
    }
    
    limit.count++
    return true
  }

  private getClientId(): string {
    // In a real implementation, this would be based on user ID or IP
    return 'default-client'
  }

  // Utility methods
  private async parseStreamingResponse(response: Response): Promise<any> {
    const reader = response.body?.getReader()
    if (!reader) throw new Error('No response body')
    
    let result = ''
    
    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break
        
        result += new TextDecoder().decode(value)
      }
      
      // Parse the streaming response to extract the final result
      const lines = result.split('\n').filter(line => line.trim())
      const lastLine = lines[lines.length - 1]
      
      if (lastLine.startsWith('data: ')) {
        const data = lastLine.slice(6)
        if (data === '[DONE]') {
          // Find the actual result in previous lines
          for (let i = lines.length - 2; i >= 0; i--) {
            if (lines[i].startsWith('data: ') && lines[i] !== 'data: [DONE]') {
              return JSON.parse(lines[i].slice(6))
            }
          }
        } else {
          return JSON.parse(data)
        }
      }
      
      return JSON.parse(result)
    } finally {
      reader.releaseLock()
    }
  }

  private validateBlockOutput(result: any): void {
    const BlockSchema = z.object({
      success: z.boolean(),
      block: z.object({
        id: z.string(),
        type: z.string(),
        configuration: z.record(z.any()),
      }).optional(),
      message: z.string().optional(),
    })
    
    BlockSchema.parse(result)
  }

  private validateLayoutOutput(result: any): void {
    const LayoutSchema = z.object({
      success: z.boolean(),
      layout: z.object({
        blocks: z.array(z.any()),
        metadata: z.record(z.any()).optional(),
      }).optional(),
      message: z.string().optional(),
    })
    
    LayoutSchema.parse(result)
  }

  private enhanceError(error: unknown, context: string): Error {
    const message = error instanceof Error ? error.message : 'Unknown error'
    const enhancedError = new Error(`${context}: ${message}`)
    enhancedError.stack = error instanceof Error ? error.stack : undefined
    return enhancedError
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }

  private analyzeTone(content: string): any {
    // Simple tone analysis - in production, use a proper NLP service
    const words = content.toLowerCase().split(' ')
    const positiveWords = ['great', 'excellent', 'amazing', 'wonderful', 'fantastic']
    const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'disappointing']
    
    const positiveCount = words.filter(word => positiveWords.includes(word)).length
    const negativeCount = words.filter(word => negativeWords.includes(word)).length
    
    return {
      sentiment: positiveCount > negativeCount ? 'positive' : negativeCount > positiveCount ? 'negative' : 'neutral',
      confidence: Math.abs(positiveCount - negativeCount) / words.length,
      positiveWords: positiveCount,
      negativeWords: negativeCount,
    }
  }

  private calculateReadabilityScore(content: string): number {
    // Simplified Flesch Reading Ease score
    const sentences = content.split(/[.!?]+/).length - 1
    const words = content.split(' ').length
    const syllables = content.split(/[aeiouAEIOU]/).length - 1
    
    if (sentences === 0 || words === 0) return 0
    
    const score = 206.835 - (1.015 * (words / sentences)) - (84.6 * (syllables / words))
    return Math.max(0, Math.min(100, score))
  }

  private calculateSentimentScore(content: string): number {
    // Simple sentiment scoring
    const analysis = this.analyzeTone(content)
    return analysis.sentiment === 'positive' ? 0.7 : analysis.sentiment === 'negative' ? 0.3 : 0.5
  }

  // Clear cache manually
  clearCache(): void {
    this.cache.clear()
  }

  // Get cache statistics
  getCacheStats(): { size: number; hitRate: number } {
    return {
      size: this.cache.size,
      hitRate: 0.85, // Placeholder - in production, track actual hit rate
    }
  }
}

// Export singleton instance
export const aiBlockService = new AIBlockService()
