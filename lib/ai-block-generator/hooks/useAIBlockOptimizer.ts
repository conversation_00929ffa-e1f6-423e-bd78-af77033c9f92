'use client'

import { useChat } from '@ai-sdk/react'
import { useState, useCallback } from 'react'
import { usePageBuilder } from '../../page-builder/context'

export type OptimizationType = 'performance' | 'accessibility' | 'ux' | 'seo' | 'mobile'

export interface AIBlockOptimizerOptions {
  onBlockOptimized?: (originalBlock: any, optimizedBlock: any, improvements: string[]) => void
  onError?: (error: Error) => void
}

export function useAIBlockOptimizer(options: AIBlockOptimizerOptions = {}) {
  const { updateBlock } = usePageBuilder()
  const [isOptimizing, setIsOptimizing] = useState(false)
  const [optimizationResults, setOptimizationResults] = useState<{
    original: any
    optimized: any
    improvements: string[]
    type: OptimizationType
  } | null>(null)

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
    stop,
    setMessages,
  } = useChat({
    api: '/api/ai-blocks/generate',
    maxSteps: 3,
    onFinish: (message) => {
      setIsOptimizing(false)
      
      // Process tool results to extract optimization results
      const toolResults = message.parts
        .filter(part => part.type === 'tool-invocation' && part.toolInvocation.state === 'result')
        .map(part => part.toolInvocation.result)

      toolResults.forEach(result => {
        if (result.success && result.optimizedBlock) {
          const optimizationResult = {
            original: result.originalBlock,
            optimized: result.optimizedBlock,
            improvements: result.improvements || [],
            type: result.optimizationType || 'ux'
          }
          
          setOptimizationResults(optimizationResult)
          options.onBlockOptimized?.(
            result.originalBlock,
            result.optimizedBlock,
            result.improvements
          )
        }
      })
    },
    onError: (error) => {
      setIsOptimizing(false)
      options.onError?.(error)
    },
  })

  // Optimize a block
  const optimizeBlock = useCallback(async (
    blockData: any,
    optimizationType: OptimizationType,
    goals: string
  ) => {
    setIsOptimizing(true)
    setOptimizationResults(null)
    
    const prompt = `Optimize this block for ${optimizationType}. Goals: ${goals}. Block data: ${JSON.stringify(blockData, null, 2)}`

    handleSubmit(new Event('submit') as any, {
      body: { 
        messages: [{ role: 'user', content: prompt }],
        action: 'optimizeBlock',
        blockData,
        optimizationType,
        goals
      }
    })
  }, [handleSubmit])

  // Apply optimization to the actual block
  const applyOptimization = useCallback(() => {
    if (optimizationResults) {
      updateBlock(optimizationResults.original.id, optimizationResults.optimized)
      setOptimizationResults(null)
    }
  }, [optimizationResults, updateBlock])

  // Reject optimization
  const rejectOptimization = useCallback(() => {
    setOptimizationResults(null)
  }, [])

  // Get optimization suggestions for a block
  const getOptimizationSuggestions = useCallback(async (blockData: any) => {
    const prompt = `Analyze this block and suggest optimizations: ${JSON.stringify(blockData, null, 2)}`
    
    handleSubmit(new Event('submit') as any, {
      body: { 
        messages: [{ role: 'user', content: prompt }],
        action: 'analyzePage',
        pageData: { blocks: [blockData] },
        analysisType: 'ux'
      }
    })
  }, [handleSubmit])

  return {
    // Chat interface
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading: isLoading || isOptimizing,
    error,
    stop,
    setMessages,
    
    // Optimization functions
    optimizeBlock,
    getOptimizationSuggestions,
    
    // Results management
    optimizationResults,
    applyOptimization,
    rejectOptimization,
    
    // Status
    isOptimizing,
    hasOptimizationResults: !!optimizationResults,
  }
}
