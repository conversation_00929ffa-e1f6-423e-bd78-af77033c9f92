'use client'

import { useChat } from '@ai-sdk/react'
import { useCallback } from 'react'

import type { Message } from 'ai'

export interface AIBlockChatOptions {
  onResponse?: (message: string) => void
  onError?: (error: Error) => void
  initialMessages?: Message[]
}

export function useAIBlockChat(options: AIBlockChatOptions = {}) {
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    status, // Note: isLoading is deprecated but kept for compatibility
    error,
    stop,
    reload,
    setMessages,
    setInput,
    append,
  } = useChat({
    api: '/api/ai-blocks/chat',
    initialMessages: options.initialMessages,
    onFinish: (message) => {
      options.onResponse?.(message.content)
    },
    onError: (error) => {
      options.onError?.(error)
    },
  })

  // Ask a question about page building
  const askQuestion = useCallback(async (question: string) => {
    await append({
      role: 'user',
      content: question,
    })
  }, [append])

  // Ask for help with a specific block
  const askAboutBlock = useCallback(async (blockType: string, issue: string) => {
    const question = `I need help with a ${blockType} block. ${issue}`
    await askQuestion(question)
  }, [askQuestion])

  // Ask for design advice
  const askForDesignAdvice = useCallback(async (context: string, goals: string) => {
    const question = `I'm working on ${context} and want to ${goals}. What design recommendations do you have?`
    await askQuestion(question)
  }, [askQuestion])

  // Ask about best practices
  const askAboutBestPractices = useCallback(async (topic: string) => {
    const question = `What are the best practices for ${topic} in web design and page building?`
    await askQuestion(question)
  }, [askQuestion])

  // Get help with layout issues
  const getLayoutHelp = useCallback(async (layoutDescription: string, problem: string) => {
    const question = `I have a layout with ${layoutDescription}. The problem is: ${problem}. How can I fix this?`
    await askQuestion(question)
  }, [askQuestion])

  // Ask for content suggestions
  const askForContentSuggestions = useCallback(async (blockType: string, purpose: string) => {
    const question = `What content should I include in a ${blockType} block for ${purpose}?`
    await askQuestion(question)
  }, [askQuestion])

  // Get accessibility advice
  const getAccessibilityAdvice = useCallback(async (context: string) => {
    const question = `How can I make ${context} more accessible? What accessibility features should I consider?`
    await askQuestion(question)
  }, [askQuestion])

  // Ask about performance optimization
  const askAboutPerformance = useCallback(async (context: string) => {
    const question = `How can I optimize the performance of ${context}? What should I focus on?`
    await askQuestion(question)
  }, [askQuestion])

  // Get mobile optimization tips
  const getMobileOptimizationTips = useCallback(async (context: string) => {
    const question = `How can I optimize ${context} for mobile devices? What mobile-specific considerations should I keep in mind?`
    await askQuestion(question)
  }, [askQuestion])

  // Clear conversation
  const clearConversation = useCallback(() => {
    setMessages([])
  }, [setMessages])

  // Send custom message
  const sendMessage = useCallback((message: string) => {
    setInput(message)
    const syntheticEvent = new Event('submit', { bubbles: true, cancelable: true })
    handleSubmit(syntheticEvent as any)
  }, [setInput, handleSubmit])

  return {
    // Chat interface
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading: status === 'streaming',
    status,
    error,
    stop,
    reload,
    setMessages,
    setInput,
    append,
    
    // Specialized question functions
    askQuestion,
    askAboutBlock,
    askForDesignAdvice,
    askAboutBestPractices,
    getLayoutHelp,
    askForContentSuggestions,
    getAccessibilityAdvice,
    askAboutPerformance,
    getMobileOptimizationTips,
    
    // Utility functions
    clearConversation,
    sendMessage,
    
    // Status
    hasMessages: messages.length > 0,
    lastMessage: messages[messages.length - 1],
  }
}
