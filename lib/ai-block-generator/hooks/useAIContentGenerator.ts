'use client'

import { useChat } from '@ai-sdk/react'
import { useState, useCallback } from 'react'

export type ContentType = 'headline' | 'description' | 'cta' | 'features' | 'testimonial' | 'product-copy'
export type ContentLength = 'short' | 'medium' | 'long'

export interface AIContentGeneratorOptions {
  onContentGenerated?: (content: string, metadata: any) => void
  onError?: (error: Error) => void
}

export function useAIContentGenerator(options: AIContentGeneratorOptions = {}) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState<{
    content: string
    type: ContentType
    metadata: any
  } | null>(null)

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
    stop,
    setMessages,
  } = useChat({
    api: '/api/ai-blocks/generate',
    maxSteps: 2,
    onFinish: (message) => {
      setIsGenerating(false)
      
      // Process tool results to extract generated content
      const toolResults = message.parts
        .filter(part => part.type === 'tool-invocation' && part.toolInvocation.state === 'result')
        .map(part => part.toolInvocation.result)

      toolResults.forEach(result => {
        if (result.success && result.content) {
          const contentResult = {
            content: result.content,
            type: result.contentType,
            metadata: result.metadata || {}
          }
          
          setGeneratedContent(contentResult)
          options.onContentGenerated?.(result.content, result.metadata)
        }
      })
    },
    onError: (error) => {
      setIsGenerating(false)
      options.onError?.(error)
    },
  })

  // Generate content
  const generateContent = useCallback(async (
    contentType: ContentType,
    context: string,
    tone: string = 'professional',
    length: ContentLength = 'medium',
    keywords: string[] = []
  ) => {
    setIsGenerating(true)
    setGeneratedContent(null)
    
    const prompt = `Generate ${contentType} content for: ${context}. Tone: ${tone}, Length: ${length}${
      keywords.length > 0 ? `, Keywords: ${keywords.join(', ')}` : ''
    }`

    handleSubmit(new Event('submit') as any, {
      body: { 
        messages: [{ role: 'user', content: prompt }],
        action: 'generateContent',
        contentType,
        context,
        tone,
        length,
        keywords
      }
    })
  }, [handleSubmit])

  // Generate multiple content variations
  const generateVariations = useCallback(async (
    contentType: ContentType,
    context: string,
    count: number = 3,
    tone: string = 'professional'
  ) => {
    setIsGenerating(true)
    
    const prompt = `Generate ${count} different variations of ${contentType} content for: ${context}. Tone: ${tone}. Make each variation unique in style and approach.`

    handleSubmit(new Event('submit') as any, {
      body: { 
        messages: [{ role: 'user', content: prompt }],
        action: 'generateContent',
        contentType,
        context,
        tone,
        variations: count
      }
    })
  }, [handleSubmit])

  // Improve existing content
  const improveContent = useCallback(async (
    currentContent: string,
    improvementGoals: string,
    contentType?: ContentType
  ) => {
    setIsGenerating(true)
    
    const prompt = `Improve this content: "${currentContent}". Goals: ${improvementGoals}${
      contentType ? ` (Content type: ${contentType})` : ''
    }`

    handleSubmit(new Event('submit') as any, {
      body: { 
        messages: [{ role: 'user', content: prompt }],
        action: 'improveContent',
        currentContent,
        improvementGoals,
        contentType
      }
    })
  }, [handleSubmit])

  // Generate content for specific block
  const generateBlockContent = useCallback(async (
    blockType: string,
    blockConfig: any,
    context: string
  ) => {
    setIsGenerating(true)
    
    const prompt = `Generate appropriate content for a ${blockType} block with this configuration: ${JSON.stringify(blockConfig, null, 2)}. Context: ${context}`

    handleSubmit(new Event('submit') as any, {
      body: { 
        messages: [{ role: 'user', content: prompt }],
        action: 'generateBlockContent',
        blockType,
        blockConfig,
        context
      }
    })
  }, [handleSubmit])

  // Clear generated content
  const clearGeneratedContent = useCallback(() => {
    setGeneratedContent(null)
  }, [])

  // Start new content generation session
  const startNewSession = useCallback(() => {
    setMessages([])
    setGeneratedContent(null)
    setIsGenerating(false)
  }, [setMessages])

  return {
    // Chat interface
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading: isLoading || isGenerating,
    error,
    stop,
    setMessages,
    
    // Content generation functions
    generateContent,
    generateVariations,
    improveContent,
    generateBlockContent,
    
    // Generated content
    generatedContent,
    clearGeneratedContent,
    
    // Session management
    startNewSession,
    
    // Status
    isGenerating,
    hasGeneratedContent: !!generatedContent,
  }
}
