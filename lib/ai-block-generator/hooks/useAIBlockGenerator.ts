'use client'

import { useChat } from '@ai-sdk/react'
import { useState, useCallback } from 'react'
import { usePageBuilder } from '../../page-builder/context'

export interface AIBlockGeneratorOptions {
  onBlockGenerated?: (block: any) => void
  onLayoutGenerated?: (layout: any) => void
  onError?: (error: Error) => void
  maxSteps?: number
}

export function useAIBlockGenerator(options: AIBlockGeneratorOptions = {}) {
  const { addBlock, addMultipleBlocks } = usePageBuilder()
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedBlocks, setGeneratedBlocks] = useState<any[]>([])

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
    stop,
    reload,
    setMessages,
  } = useChat({
    api: '/api/ai-blocks/generate',
    maxSteps: options.maxSteps || 5,
    onFinish: (message) => {
      setIsGenerating(false)
      
      // Process tool results to extract generated blocks
      const toolResults = message.parts
        .filter(part => part.type === 'tool-invocation' && part.toolInvocation.state === 'result')
        .map(part => part.toolInvocation.result)

      toolResults.forEach(result => {
        if (result.success) {
          if (result.block) {
            // Single block generated
            setGeneratedBlocks(prev => [...prev, result.block])
            options.onBlockGenerated?.(result.block)
          } else if (result.layout) {
            // Layout with multiple blocks generated
            setGeneratedBlocks(prev => [...prev, ...result.layout.blocks])
            options.onLayoutGenerated?.(result.layout)
          }
        }
      })
    },
    onError: (error) => {
      setIsGenerating(false)
      options.onError?.(error)
    },
  })

  // Generate a single block
  const generateBlock = useCallback(async (
    blockType: string,
    requirements: string,
    context?: string,
    style?: string
  ) => {
    setIsGenerating(true)
    setGeneratedBlocks([])
    
    const prompt = `Generate a ${blockType} block with the following requirements: ${requirements}${
      context ? ` Context: ${context}` : ''
    }${style ? ` Style: ${style}` : ''}`

    handleSubmit(new Event('submit') as any, {
      body: { 
        messages: [{ role: 'user', content: prompt }],
        action: 'generateBlock',
        blockType,
        requirements,
        context,
        style
      }
    })
  }, [handleSubmit])

  // Generate a complete page layout
  const generateLayout = useCallback(async (
    pageType: string,
    requirements: string,
    sections: string[],
    style?: string,
    target?: string
  ) => {
    setIsGenerating(true)
    setGeneratedBlocks([])
    
    const prompt = `Generate a complete ${pageType} page layout with the following sections: ${sections.join(', ')}. Requirements: ${requirements}${
      style ? ` Style: ${style}` : ''
    }${target ? ` Target audience: ${target}` : ''}`

    handleSubmit(new Event('submit') as any, {
      body: { 
        messages: [{ role: 'user', content: prompt }],
        action: 'generateLayout',
        pageType,
        requirements,
        sections,
        style,
        target
      }
    })
  }, [handleSubmit])

  // Add generated block to page
  const addGeneratedBlock = useCallback((block: any) => {
    addBlock(block)
  }, [addBlock])

  // Add all generated blocks to page
  const addAllGeneratedBlocks = useCallback(() => {
    if (generatedBlocks.length > 0) {
      addMultipleBlocks(generatedBlocks)
      setGeneratedBlocks([])
    }
  }, [generatedBlocks, addMultipleBlocks])

  // Clear generated blocks
  const clearGeneratedBlocks = useCallback(() => {
    setGeneratedBlocks([])
  }, [])

  // Start a new conversation
  const startNewConversation = useCallback(() => {
    setMessages([])
    setGeneratedBlocks([])
    setIsGenerating(false)
  }, [setMessages])

  return {
    // Chat interface
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading: isLoading || isGenerating,
    error,
    stop,
    reload,
    setMessages,
    
    // Generation functions
    generateBlock,
    generateLayout,
    
    // Generated content
    generatedBlocks,
    addGeneratedBlock,
    addAllGeneratedBlocks,
    clearGeneratedBlocks,
    
    // Conversation management
    startNewConversation,
    
    // Status
    isGenerating,
    hasGeneratedBlocks: generatedBlocks.length > 0,
  }
}
