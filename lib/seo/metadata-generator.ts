import { Metadata } from 'next'
import { ResolvedPage, ResolvedPost } from '@/lib/routing/route-resolver'

interface PostType {
  id: string
  name: string
  label: string
  labelPlural: string
  description?: string | null
  isPublic: boolean
  hasArchive: boolean
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

interface SEOConfig {
  siteName: string
  siteUrl: string
  defaultTitle: string
  defaultDescription: string
  defaultImage: string
  twitterHandle?: string
  facebookAppId?: string
}

const seoConfig: SEOConfig = {
  siteName: 'Coco Milk Kids',
  siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://cocomilkkids.com',
  defaultTitle: 'Coco Milk Kids | Premium Children\'s Clothing',
  defaultDescription: 'Discover stylish, comfortable, and sustainable children\'s clothing at Coco Milk Kids. Premium quality for growing adventures.',
  defaultImage: '/images/og-default.jpg',
  twitterHandle: '@cocomilkkids',
  facebookAppId: process.env.FACEBOOK_APP_ID
}

export class MetadataGenerator {
  /**
   * Generate metadata for a page
   */
  static generatePageMetadata(page: ResolvedPage, pathname: string): Metadata {
    const title = page.seoTitle || page.title
    const description = page.seoDescription || page.description || seoConfig.defaultDescription
    const canonical = `${seoConfig.siteUrl}${pathname}`
    const ogImage = page.ogImage || seoConfig.defaultImage

    const metadata: Metadata = {
      title: title === seoConfig.defaultTitle ? title : `${title} | ${seoConfig.siteName}`,
      description,
      keywords: page.seoKeywords?.join(', '),
      authors: page.createdBy ? [{ name: page.createdBy }] : undefined,
      creator: page.createdBy,
      publisher: seoConfig.siteName,
      alternates: {
        canonical
      },
      openGraph: {
        type: 'website',
        url: canonical,
        title,
        description,
        siteName: seoConfig.siteName,
        images: [
          {
            url: ogImage.startsWith('http') ? ogImage : `${seoConfig.siteUrl}${ogImage}`,
            width: 1200,
            height: 630,
            alt: title
          }
        ]
      },
      twitter: {
        card: 'summary_large_image',
        site: seoConfig.twitterHandle,
        creator: seoConfig.twitterHandle,
        title,
        description,
        images: [ogImage.startsWith('http') ? ogImage : `${seoConfig.siteUrl}${ogImage}`]
      },
      robots: {
        index: page.status === 'published',
        follow: page.status === 'published',
        googleBot: {
          index: page.status === 'published',
          follow: page.status === 'published'
        }
      }
    }

    // Add structured data for pages
    if (page.type === 'landing' || page.type === 'product') {
      metadata.other = {
        'application/ld+json': JSON.stringify(this.generatePageStructuredData(page, canonical))
      }
    }

    return metadata
  }

  /**
   * Generate metadata for a post
   */
  static generatePostMetadata(post: ResolvedPost, pathname: string): Metadata {
    const title = post.seoTitle || post.title
    const description = post.seoDescription || post.excerpt || seoConfig.defaultDescription
    const canonical = `${seoConfig.siteUrl}${pathname}`
    const ogImage = post.ogImage || post.featuredImage || seoConfig.defaultImage
    const publishedTime = post.publishedAt?.toISOString()
    const modifiedTime = post.updatedAt?.toISOString()

    const metadata: Metadata = {
      title: `${title} | ${seoConfig.siteName}`,
      description,
      keywords: post.seoKeywords?.join(', '),
      authors: post.authorName ? [{ name: post.authorName }] : undefined,
      creator: post.authorName,
      publisher: seoConfig.siteName,
      alternates: {
        canonical
      },
      openGraph: {
        type: 'article',
        url: canonical,
        title,
        description,
        siteName: seoConfig.siteName,
        publishedTime,
        modifiedTime,
        authors: post.authorName ? [post.authorName] : undefined,
        section: this.getPostSection(post),
        tags: this.getPostTags(post),
        images: [
          {
            url: ogImage.startsWith('http') ? ogImage : `${seoConfig.siteUrl}${ogImage}`,
            width: 1200,
            height: 630,
            alt: post.featuredImageAlt || title
          }
        ]
      },
      twitter: {
        card: 'summary_large_image',
        site: seoConfig.twitterHandle,
        creator: seoConfig.twitterHandle,
        title,
        description,
        images: [ogImage.startsWith('http') ? ogImage : `${seoConfig.siteUrl}${ogImage}`]
      },
      robots: {
        index: post.status === 'published',
        follow: post.status === 'published',
        googleBot: {
          index: post.status === 'published',
          follow: post.status === 'published'
        }
      }
    }

    // Add structured data for posts
    metadata.other = {
      'application/ld+json': JSON.stringify(this.generatePostStructuredData(post, canonical))
    }

    return metadata
  }

  /**
   * Generate metadata for archive pages
   */
  static generateArchiveMetadata(
    postType: PostType, 
    pathname: string, 
    page: number = 1,
    totalPosts: number = 0
  ): Metadata {
    const title = page > 1 
      ? `${postType.labelPlural} - Page ${page}`
      : postType.labelPlural
    const description = postType.description || `Browse all ${postType.labelPlural.toLowerCase()} on ${seoConfig.siteName}`
    const canonical = `${seoConfig.siteUrl}${pathname}`

    const metadata: Metadata = {
      title: `${title} | ${seoConfig.siteName}`,
      description,
      alternates: {
        canonical
      },
      openGraph: {
        type: 'website',
        url: canonical,
        title,
        description,
        siteName: seoConfig.siteName,
        images: [
          {
            url: seoConfig.defaultImage.startsWith('http') 
              ? seoConfig.defaultImage 
              : `${seoConfig.siteUrl}${seoConfig.defaultImage}`,
            width: 1200,
            height: 630,
            alt: title
          }
        ]
      },
      twitter: {
        card: 'summary_large_image',
        site: seoConfig.twitterHandle,
        title,
        description,
        images: [seoConfig.defaultImage.startsWith('http') 
          ? seoConfig.defaultImage 
          : `${seoConfig.siteUrl}${seoConfig.defaultImage}`]
      },
      robots: {
        index: postType.isPublic,
        follow: postType.isPublic,
        googleBot: {
          index: postType.isPublic,
          follow: postType.isPublic
        }
      }
    }

    // Add structured data for archive pages
    metadata.other = {
      'application/ld+json': JSON.stringify(this.generateArchiveStructuredData(postType, canonical, totalPosts))
    }

    return metadata
  }

  /**
   * Generate structured data for pages
   */
  private static generatePageStructuredData(page: ResolvedPage, url: string) {
    const baseData = {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      name: page.title,
      description: page.description,
      url,
      inLanguage: 'en-ZA',
      isPartOf: {
        '@type': 'WebSite',
        name: seoConfig.siteName,
        url: seoConfig.siteUrl
      }
    }

    // Add specific schema based on page type
    if (page.type === 'product') {
      return {
        ...baseData,
        '@type': 'ProductPage'
      }
    }

    if (page.type === 'landing') {
      return {
        ...baseData,
        '@type': 'LandingPage'
      }
    }

    return baseData
  }

  /**
   * Generate structured data for posts
   */
  private static generatePostStructuredData(post: ResolvedPost, url: string) {
    const categories = this.getPostCategories(post)
    
    return {
      '@context': 'https://schema.org',
      '@type': 'Article',
      headline: post.title,
      description: post.excerpt,
      image: post.featuredImage ? 
        (post.featuredImage.startsWith('http') ? post.featuredImage : `${seoConfig.siteUrl}${post.featuredImage}`) : 
        undefined,
      datePublished: post.publishedAt?.toISOString(),
      dateModified: post.updatedAt?.toISOString(),
      author: post.authorName ? {
        '@type': 'Person',
        name: post.authorName
      } : undefined,
      publisher: {
        '@type': 'Organization',
        name: seoConfig.siteName,
        logo: {
          '@type': 'ImageObject',
          url: `${seoConfig.siteUrl}/images/logo.png`
        }
      },
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': url
      },
      articleSection: categories[0] || post.postType,
      keywords: this.getPostTags(post).join(', '),
      wordCount: this.estimateWordCount(post.content || ''),
      inLanguage: 'en-ZA'
    }
  }

  /**
   * Generate structured data for archive pages
   */
  private static generateArchiveStructuredData(postType: PostType, url: string, totalPosts: number) {
    return {
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      name: postType.labelPlural,
      description: postType.description,
      url,
      numberOfItems: totalPosts,
      isPartOf: {
        '@type': 'WebSite',
        name: seoConfig.siteName,
        url: seoConfig.siteUrl
      }
    }
  }

  /**
   * Helper methods
   */
  private static getPostSection(post: ResolvedPost): string {
    const categories = this.getPostCategories(post)
    return categories[0] || post.postType || 'General'
  }

  private static getPostCategories(post: ResolvedPost): string[] {
    return post.taxonomyTerms
      ?.filter(term => term.term?.taxonomy?.name === 'category')
      ?.map(term => term.term.name) || []
  }

  private static getPostTags(post: ResolvedPost): string[] {
    return post.taxonomyTerms
      ?.filter(term => term.term?.taxonomy?.name === 'tag')
      ?.map(term => term.term.name) || []
  }

  private static estimateWordCount(content: string): number {
    // Remove HTML tags and count words
    const textContent = content.replace(/<[^>]*>/g, ' ')
    const words = textContent.trim().split(/\s+/).filter(word => word.length > 0)
    return words.length
  }

  /**
   * Generate sitemap entries
   */
  static async generateSitemapEntries() {
    // This would be called by a sitemap.xml route
    const entries = await RouteResolver.generateSitemapData()
    
    return entries.map(entry => ({
      url: `${seoConfig.siteUrl}${entry.url}`,
      lastModified: entry.lastModified,
      changeFrequency: entry.changeFrequency,
      priority: entry.priority
    }))
  }

  /**
   * Generate robots.txt content
   */
  static generateRobotsTxt(): string {
    return `User-agent: *
Allow: /

# Disallow admin and preview pages
Disallow: /admin/
Disallow: /preview/
Disallow: /api/

# Sitemap
Sitemap: ${seoConfig.siteUrl}/sitemap.xml`
  }
}
