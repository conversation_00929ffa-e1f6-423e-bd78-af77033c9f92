// Appwrite Realtime service for live updates

import { Models } from 'appwrite'
import { getServices } from './client'
import { appwriteConfig, APPWRITE_COLLECTIONS, REALTIME_CHANNEL_PATTERNS } from './config'

export interface RealtimeSubscription {
  unsubscribe: () => void
}

export interface RealtimeEvent<T = any> {
  events: string[]
  channels: string[]
  timestamp: number
  payload: T
}

export type RealtimeCallback<T = any> = (event: RealtimeEvent<T>) => void

export class AppwriteRealtimeService {
  private static instance: AppwriteRealtimeService
  private subscriptions: Map<string, RealtimeSubscription> = new Map()

  static getInstance(): AppwriteRealtimeService {
    if (!AppwriteRealtimeService.instance) {
      AppwriteRealtimeService.instance = new AppwriteRealtimeService()
    }
    return AppwriteRealtimeService.instance
  }

  /**
   * Subscribe to realtime updates
   */
  subscribe<T = any>(
    channels: string | string[],
    callback: RealtimeCallback<T>
  ): RealtimeSubscription {
    try {
      const services = getServices()
      const channelArray = Array.isArray(channels) ? channels : [channels]

      // In Appwrite v18+, realtime is accessed through the client
      const unsubscribe = services.client.subscribe(channelArray, callback)

      // Store subscription for cleanup
      const subscriptionId = `${Date.now()}_${Math.random()}`
      const subscription = { unsubscribe }
      this.subscriptions.set(subscriptionId, subscription)

      return {
        unsubscribe: () => {
          try {
            unsubscribe()
            this.subscriptions.delete(subscriptionId)
          } catch (error) {
            console.warn('Error unsubscribing from realtime:', error)
          }
        }
      }
    } catch (error) {
      console.warn('Error subscribing to realtime:', error)
      // Return a dummy subscription that does nothing
      return {
        unsubscribe: () => {}
      }
    }
  }

  /**
   * Subscribe to database collection changes
   */
  subscribeToCollection<T = Models.Document>(
    collectionId: string,
    callback: RealtimeCallback<T>
  ): RealtimeSubscription {
    const channel = REALTIME_CHANNEL_PATTERNS.DOCUMENTS(appwriteConfig.databaseId, collectionId)
    return this.subscribe(channel, callback)
  }

  /**
   * Subscribe to specific document changes
   */
  subscribeToDocument<T = Models.Document>(
    collectionId: string,
    documentId: string,
    callback: RealtimeCallback<T>
  ): RealtimeSubscription {
    const channel = REALTIME_CHANNEL_PATTERNS.DOCUMENTS(appwriteConfig.databaseId, collectionId, documentId)
    return this.subscribe(channel, callback)
  }

  /**
   * Subscribe to user account changes
   */
  subscribeToAccount<T = Models.User<Models.Preferences>>(
    callback: RealtimeCallback<T>
  ): RealtimeSubscription {
    return this.subscribe(REALTIME_CHANNEL_PATTERNS.ACCOUNT, callback)
  }

  /**
   * Subscribe to storage bucket changes
   */
  subscribeToStorage<T = Models.File>(
    bucketId: string,
    callback: RealtimeCallback<T>
  ): RealtimeSubscription {
    const channel = REALTIME_CHANNEL_PATTERNS.FILES(bucketId)
    return this.subscribe(channel, callback)
  }

  /**
   * Unsubscribe from all subscriptions
   */
  unsubscribeAll(): void {
    this.subscriptions.forEach(subscription => {
      subscription.unsubscribe()
    })
    this.subscriptions.clear()
  }

  /**
   * Get active subscription count
   */
  getActiveSubscriptionCount(): number {
    return this.subscriptions.size
  }
}

// Export singleton instance
export const realtimeService = AppwriteRealtimeService.getInstance()

// Utility functions for common subscriptions

/**
 * Subscribe to product changes
 */
export function subscribeToProducts(
  callback: RealtimeCallback<Models.Document>
): RealtimeSubscription {
  return realtimeService.subscribeToCollection(APPWRITE_COLLECTIONS.PRODUCTS, callback)
}

/**
 * Subscribe to specific product changes
 */
export function subscribeToProduct(
  productId: string,
  callback: RealtimeCallback<Models.Document>
): RealtimeSubscription {
  return realtimeService.subscribeToDocument(APPWRITE_COLLECTIONS.PRODUCTS, productId, callback)
}

/**
 * Subscribe to cart changes for a user
 */
export function subscribeToUserCart(
  userId: string,
  callback: RealtimeCallback<Models.Document>
): RealtimeSubscription {
  // Note: This would require server-side filtering or custom channels
  return realtimeService.subscribeToCollection(APPWRITE_COLLECTIONS.CART_ITEMS, (event) => {
    // Filter events for specific user
    if (event.payload && event.payload.userId === userId) {
      callback(event)
    }
  })
}

/**
 * Subscribe to order changes
 */
export function subscribeToOrders(
  callback: RealtimeCallback<Models.Document>
): RealtimeSubscription {
  return realtimeService.subscribeToCollection(APPWRITE_COLLECTIONS.ORDERS, callback)
}

/**
 * Subscribe to specific order changes
 */
export function subscribeToOrder(
  orderId: string,
  callback: RealtimeCallback<Models.Document>
): RealtimeSubscription {
  return realtimeService.subscribeToDocument(APPWRITE_COLLECTIONS.ORDERS, orderId, callback)
}

/**
 * Subscribe to inventory changes
 */
export function subscribeToInventory(
  callback: RealtimeCallback<Models.Document>
): RealtimeSubscription {
  return realtimeService.subscribeToCollection(APPWRITE_COLLECTIONS.INVENTORY, callback)
}

/**
 * Subscribe to user account changes
 */
export function subscribeToUserAccount(
  callback: RealtimeCallback<Models.User<Models.Preferences>>
): RealtimeSubscription {
  return realtimeService.subscribeToAccount(callback)
}

/**
 * Subscribe to file uploads in product images bucket
 */
export function subscribeToProductImageUploads(
  callback: RealtimeCallback<Models.File>
): RealtimeSubscription {
  return realtimeService.subscribeToStorage(appwriteConfig.storageBucketId, callback)
}

// React hooks for realtime subscriptions (to be used in components)
export interface UseRealtimeOptions {
  enabled?: boolean
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: Error) => void
}

/**
 * Custom hook for realtime subscriptions in React components
 */
export function createRealtimeHook<T = any>(
  getChannels: () => string | string[],
  callback: RealtimeCallback<T>,
  options: UseRealtimeOptions = {}
) {
  return function useRealtime() {
    const { enabled = true, onConnect, onDisconnect, onError } = options

    // This would be implemented as a React hook in a separate file
    // For now, it's just a factory function
    if (enabled) {
      try {
        const subscription = realtimeService.subscribe(getChannels(), callback)
        onConnect?.()
        
        return {
          subscription,
          isConnected: true,
          disconnect: () => {
            subscription.unsubscribe()
            onDisconnect?.()
          }
        }
      } catch (error) {
        onError?.(error as Error)
        return {
          subscription: null,
          isConnected: false,
          disconnect: () => {}
        }
      }
    }

    return {
      subscription: null,
      isConnected: false,
      disconnect: () => {}
    }
  }
}

// Cleanup function for when the app unmounts
export function cleanupRealtime(): void {
  realtimeService.unsubscribeAll()
}
