// Appwrite Database service for data management

import { ID, Models, Query } from 'appwrite'
import { getServices, withAppwriteErrorHandling } from './client'
import { appwriteConfig, APPWRITE_COLLECTIONS, APPWRITE_ERRORS } from './config'

export interface DatabaseOptions {
  databaseId?: string
  permissions?: string[]
}

export interface QueryOptions {
  queries?: string[]
  orderBy?: string
  orderDirection?: 'asc' | 'desc'
  limit?: number
  offset?: number
}

export class AppwriteDatabaseService {
  private static instance: AppwriteDatabaseService

  static getInstance(): AppwriteDatabaseService {
    if (!AppwriteDatabaseService.instance) {
      AppwriteDatabaseService.instance = new AppwriteDatabaseService()
    }
    return AppwriteDatabaseService.instance
  }

  /**
   * Create a document
   */
  async createDocument<T extends Record<string, any>>(
    collectionId: string,
    data: T,
    options: DatabaseOptions = {}
  ): Promise<Models.Document> {
    return withAppwriteErrorHandling(async () => {
      const {
        databaseId = appwriteConfig.databaseId,
        permissions = []
      } = options

      const services = getServices()
      return await services.databases.createDocument(
        databaseId,
        collectionId,
        ID.unique(),
        data,
        permissions
      )
    }, APPWRITE_ERRORS.DATABASE_ERROR)
  }

  /**
   * Get a document by ID
   */
  async getDocument(
    collectionId: string,
    documentId: string,
    options: DatabaseOptions = {}
  ): Promise<Models.Document> {
    return withAppwriteErrorHandling(async () => {
      const { databaseId = appwriteConfig.databaseId } = options
      const services = getServices()
      return await services.databases.getDocument(databaseId, collectionId, documentId)
    }, APPWRITE_ERRORS.DATABASE_ERROR)
  }

  /**
   * Update a document
   */
  async updateDocument<T extends Record<string, any>>(
    collectionId: string,
    documentId: string,
    data: Partial<T>,
    options: DatabaseOptions = {}
  ): Promise<Models.Document> {
    return withAppwriteErrorHandling(async () => {
      const {
        databaseId = appwriteConfig.databaseId,
        permissions
      } = options

      const services = getServices()
      return await services.databases.updateDocument(
        databaseId,
        collectionId,
        documentId,
        data,
        permissions
      )
    }, APPWRITE_ERRORS.DATABASE_ERROR)
  }

  /**
   * Delete a document
   */
  async deleteDocument(
    collectionId: string,
    documentId: string,
    options: DatabaseOptions = {}
  ): Promise<void> {
    return withAppwriteErrorHandling(async () => {
      const { databaseId = appwriteConfig.databaseId } = options
      const services = getServices()
      await services.databases.deleteDocument(databaseId, collectionId, documentId)
    }, APPWRITE_ERRORS.DATABASE_ERROR)
  }

  /**
   * List documents with queries
   */
  async listDocuments(
    collectionId: string,
    queryOptions: QueryOptions = {},
    options: DatabaseOptions = {}
  ): Promise<Models.DocumentList<Models.Document>> {
    return withAppwriteErrorHandling(async () => {
      const { databaseId = appwriteConfig.databaseId } = options
      const {
        queries = [],
        orderBy,
        orderDirection = 'desc',
        limit = 25,
        offset = 0
      } = queryOptions

      // Build queries
      const finalQueries = [...queries]

      if (limit) {
        finalQueries.push(Query.limit(limit))
      }

      if (offset) {
        finalQueries.push(Query.offset(offset))
      }

      if (orderBy) {
        if (orderDirection === 'asc') {
          finalQueries.push(Query.orderAsc(orderBy))
        } else {
          finalQueries.push(Query.orderDesc(orderBy))
        }
      }

      const services = getServices()
      return await services.databases.listDocuments(
        databaseId,
        collectionId,
        finalQueries
      )
    }, APPWRITE_ERRORS.DATABASE_ERROR)
  }

  /**
   * Search documents
   */
  async searchDocuments(
    collectionId: string,
    searchTerm: string,
    searchFields: string[],
    queryOptions: QueryOptions = {},
    options: DatabaseOptions = {}
  ): Promise<Models.DocumentList<Models.Document>> {
    const searchQueries = searchFields.map(field => 
      Query.search(field, searchTerm)
    )

    return this.listDocuments(
      collectionId,
      {
        ...queryOptions,
        queries: [...(queryOptions.queries || []), ...searchQueries]
      },
      options
    )
  }

  /**
   * Count documents
   */
  async countDocuments(
    collectionId: string,
    queries: string[] = [],
    options: DatabaseOptions = {}
  ): Promise<number> {
    const result = await this.listDocuments(
      collectionId,
      { queries, limit: 1 },
      options
    )
    return result.total
  }

  /**
   * Batch operations
   */
  async batchCreate<T extends Record<string, any>>(
    collectionId: string,
    documents: T[],
    options: DatabaseOptions = {}
  ): Promise<Models.Document[]> {
    const results: Models.Document[] = []
    
    for (const data of documents) {
      try {
        const result = await this.createDocument(collectionId, data, options)
        results.push(result)
      } catch (error) {
        console.error('Batch create error:', error)
        throw error
      }
    }
    
    return results
  }

  /**
   * Batch delete
   */
  async batchDelete(
    collectionId: string,
    documentIds: string[],
    options: DatabaseOptions = {}
  ): Promise<void> {
    for (const documentId of documentIds) {
      try {
        await this.deleteDocument(collectionId, documentId, options)
      } catch (error) {
        console.error(`Failed to delete document ${documentId}:`, error)
      }
    }
  }
}

// Export singleton instance
export const databaseService = AppwriteDatabaseService.getInstance()

// Utility functions for specific collections
export async function createProduct(productData: any): Promise<Models.Document> {
  return databaseService.createDocument(APPWRITE_COLLECTIONS.PRODUCTS, productData)
}

export async function getProduct(productId: string): Promise<Models.Document> {
  return databaseService.getDocument(APPWRITE_COLLECTIONS.PRODUCTS, productId)
}

export async function updateProduct(productId: string, data: any): Promise<Models.Document> {
  return databaseService.updateDocument(APPWRITE_COLLECTIONS.PRODUCTS, productId, data)
}

export async function deleteProduct(productId: string): Promise<void> {
  return databaseService.deleteDocument(APPWRITE_COLLECTIONS.PRODUCTS, productId)
}

export async function listProducts(options: QueryOptions = {}): Promise<Models.DocumentList<Models.Document>> {
  return databaseService.listDocuments(APPWRITE_COLLECTIONS.PRODUCTS, options)
}

export async function searchProducts(searchTerm: string, options: QueryOptions = {}): Promise<Models.DocumentList<Models.Document>> {
  return databaseService.searchDocuments(
    APPWRITE_COLLECTIONS.PRODUCTS,
    searchTerm,
    ['title', 'description', 'tags'],
    options
  )
}

// Cart operations
export async function createCartItem(cartData: any): Promise<Models.Document> {
  return databaseService.createDocument(APPWRITE_COLLECTIONS.CART_ITEMS, cartData)
}

export async function getUserCartItems(userId: string): Promise<Models.DocumentList<Models.Document>> {
  return databaseService.listDocuments(APPWRITE_COLLECTIONS.CART_ITEMS, {
    queries: [Query.equal('userId', userId)]
  })
}

export async function updateCartItem(cartItemId: string, data: any): Promise<Models.Document> {
  return databaseService.updateDocument(APPWRITE_COLLECTIONS.CART_ITEMS, cartItemId, data)
}

export async function deleteCartItem(cartItemId: string): Promise<void> {
  return databaseService.deleteDocument(APPWRITE_COLLECTIONS.CART_ITEMS, cartItemId)
}

// Order operations
export async function createOrder(orderData: any): Promise<Models.Document> {
  return databaseService.createDocument(APPWRITE_COLLECTIONS.ORDERS, orderData)
}

export async function getOrder(orderId: string): Promise<Models.Document> {
  return databaseService.getDocument(APPWRITE_COLLECTIONS.ORDERS, orderId)
}

export async function getUserOrders(userId: string): Promise<Models.DocumentList<Models.Document>> {
  return databaseService.listDocuments(APPWRITE_COLLECTIONS.ORDERS, {
    queries: [Query.equal('userId', userId)],
    orderBy: 'createdAt',
    orderDirection: 'desc'
  })
}
