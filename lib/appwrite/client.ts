// Appwrite client setup and initialization

import { Client, Account, Databases, Storage, Functions } from 'appwrite'
import { appwriteConfig, appwriteServerConfig, validateAppwriteConfig } from './config'

// Client-side Appwrite client (for browser)
let clientInstance: Client | null = null

export function getAppwriteClient(): Client {
  if (!clientInstance) {
    if (!validateAppwriteConfig(appwriteConfig)) {
      throw new Error('Invalid Appwrite configuration. Please check your environment variables.')
    }

    clientInstance = new Client()
      .setEndpoint(appwriteConfig.endpoint)
      .setProject(appwriteConfig.projectId)

    // Set locale for South African users
    clientInstance.setLocale('en-ZA')
  }

  return clientInstance
}

// Server-side Appwrite client (for API routes)
let serverClientInstance: Client | null = null

export function getAppwriteServerClient(): Client {
  if (!serverClientInstance) {
    if (!validateAppwriteConfig(appwriteServerConfig) || !appwriteServerConfig.apiKey) {
      throw new Error('Invalid Appwrite server configuration. Please check your environment variables.')
    }

    serverClientInstance = new Client()
      .setEndpoint(appwriteServerConfig.endpoint)
      .setProject(appwriteServerConfig.projectId)

    // Set API key using headers for server-side operations
    if (appwriteServerConfig.apiKey) {
      serverClientInstance.headers['X-Appwrite-Key'] = appwriteServerConfig.apiKey
    }

    serverClientInstance.setLocale('en-ZA')
  }

  return serverClientInstance
}

// Service instances for client-side
export class AppwriteServices {
  private static instance: AppwriteServices
  public readonly client: Client
  public readonly account: Account
  public readonly databases: Databases
  public readonly storage: Storage
  public readonly functions: Functions

  private constructor() {
    this.client = getAppwriteClient()
    this.account = new Account(this.client)
    this.databases = new Databases(this.client)
    this.storage = new Storage(this.client)
    this.functions = new Functions(this.client)
  }

  static getInstance(): AppwriteServices {
    if (!AppwriteServices.instance) {
      AppwriteServices.instance = new AppwriteServices()
    }
    return AppwriteServices.instance
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      // Try to get account info - this is a simple way to check if we're connected
      await this.account.get()
      return true
    } catch (error) {
      // If account.get() fails, try a simpler check
      try {
        // For anonymous users, this will still work to test connectivity
        await this.account.createAnonymousSession()
        await this.account.deleteSession('current')
        return true
      } catch (secondError) {
        console.warn('Appwrite health check failed:', error)
        return false
      }
    }
  }
}

// Service instances for server-side
export class AppwriteServerServices {
  private static instance: AppwriteServerServices
  public readonly client: Client
  public readonly databases: Databases
  public readonly storage: Storage
  public readonly functions: Functions

  private constructor() {
    this.client = getAppwriteServerClient()
    this.databases = new Databases(this.client)
    this.storage = new Storage(this.client)
    this.functions = new Functions(this.client)
  }

  static getInstance(): AppwriteServerServices {
    if (!AppwriteServerServices.instance) {
      AppwriteServerServices.instance = new AppwriteServerServices()
    }
    return AppwriteServerServices.instance
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      // Try to list files in the configured bucket to test connectivity
      await this.storage.listFiles(appwriteConfig.storageBucketId)
      return true
    } catch (error) {
      console.warn('Appwrite server health check failed:', error)
      return false
    }
  }
}

// Utility functions
export function isClientSide(): boolean {
  return typeof window !== 'undefined'
}

export function getServices() {
  if (isClientSide()) {
    return AppwriteServices.getInstance()
  } else {
    return AppwriteServerServices.getInstance()
  }
}

// Error handling wrapper
export async function withAppwriteErrorHandling<T>(
  operation: () => Promise<T>,
  errorMessage: string = 'Appwrite operation failed'
): Promise<T> {
  try {
    return await operation()
  } catch (error: any) {
    console.error(`${errorMessage}:`, error)
    
    // Handle specific Appwrite errors
    if (error.code) {
      switch (error.code) {
        case 401:
          throw new Error('Authentication required')
        case 403:
          throw new Error('Permission denied')
        case 404:
          throw new Error('Resource not found')
        case 429:
          throw new Error('Rate limit exceeded')
        case 500:
          throw new Error('Server error')
        default:
          throw new Error(error.message || errorMessage)
      }
    }
    
    throw new Error(errorMessage)
  }
}

// Connection status
export class AppwriteConnectionManager {
  private static instance: AppwriteConnectionManager
  private isConnected: boolean = false
  private connectionListeners: Array<(connected: boolean) => void> = []

  private constructor() {
    this.checkConnection()
  }

  static getInstance(): AppwriteConnectionManager {
    if (!AppwriteConnectionManager.instance) {
      AppwriteConnectionManager.instance = new AppwriteConnectionManager()
    }
    return AppwriteConnectionManager.instance
  }

  async checkConnection(): Promise<boolean> {
    try {
      const services = getServices()
      this.isConnected = await services.healthCheck()
      this.notifyListeners()
      return this.isConnected
    } catch (error) {
      this.isConnected = false
      this.notifyListeners()
      return false
    }
  }

  onConnectionChange(listener: (connected: boolean) => void): () => void {
    this.connectionListeners.push(listener)
    
    // Return unsubscribe function
    return () => {
      const index = this.connectionListeners.indexOf(listener)
      if (index > -1) {
        this.connectionListeners.splice(index, 1)
      }
    }
  }

  private notifyListeners(): void {
    this.connectionListeners.forEach(listener => listener(this.isConnected))
  }

  get connected(): boolean {
    return this.isConnected
  }
}

// Export default instances
export const appwrite = AppwriteServices.getInstance()
export const appwriteServer = AppwriteServerServices.getInstance()
export const connectionManager = AppwriteConnectionManager.getInstance()
