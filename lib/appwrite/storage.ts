// Appwrite Storage service for file management

import { ID, Models } from 'appwrite'
import { getServices, withAppwriteErrorHandling } from './client'
import { 
  appwriteConfig, 
  STORAGE_BUCKETS, 
  APPWRITE_ERRORS,
  isValidFileType,
  isValidFileSize,
  getFileUrl,
  getFileDownloadUrl,
  generateFileId
} from './config'

export interface UploadResult {
  fileId: string
  url: string
  downloadUrl: string
  file: Models.File
}

export interface UploadOptions {
  bucketId?: string
  fileId?: string
  permissions?: string[]
  onProgress?: (progress: number) => void
}

export interface FileValidation {
  maxSize: number
  allowedTypes: string[]
}

export class AppwriteStorageService {
  private static instance: AppwriteStorageService

  static getInstance(): AppwriteStorageService {
    if (!AppwriteStorageService.instance) {
      AppwriteStorageService.instance = new AppwriteStorageService()
    }
    return AppwriteStorageService.instance
  }

  /**
   * Upload a file to Appwrite storage
   */
  async uploadFile(
    file: File,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    return withAppwriteErrorHandling(async () => {
      const {
        bucketId = appwriteConfig.storageBucketId,
        fileId = generateFileId(),
        permissions = [],
        onProgress
      } = options

      // Validate file
      this.validateFile(file, bucketId)

      const services = getServices()

      // Upload file with progress tracking
      const uploadedFile = await services.storage.createFile(
        bucketId,
        fileId,
        file,
        permissions
      )

      // Generate URLs
      const url = getFileUrl(bucketId, uploadedFile.$id)
      const downloadUrl = getFileDownloadUrl(bucketId, uploadedFile.$id)

      return {
        fileId: uploadedFile.$id,
        url,
        downloadUrl,
        file: uploadedFile
      }
    }, APPWRITE_ERRORS.UPLOAD_FAILED)
  }

  /**
   * Upload multiple files
   */
  async uploadFiles(
    files: File[],
    options: UploadOptions = {}
  ): Promise<UploadResult[]> {
    const results: UploadResult[] = []
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      const fileOptions = {
        ...options,
        onProgress: options.onProgress 
          ? (progress: number) => {
              const totalProgress = ((i * 100) + progress) / files.length
              options.onProgress!(totalProgress)
            }
          : undefined
      }
      
      try {
        const result = await this.uploadFile(file, fileOptions)
        results.push(result)
      } catch (error) {
        console.error(`Failed to upload file ${file.name}:`, error)
        throw error
      }
    }

    return results
  }

  /**
   * Get file information
   */
  async getFile(bucketId: string, fileId: string): Promise<Models.File> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      return await services.storage.getFile(bucketId, fileId)
    }, APPWRITE_ERRORS.DOWNLOAD_FAILED)
  }

  /**
   * Delete a file
   */
  async deleteFile(bucketId: string, fileId: string): Promise<void> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      await services.storage.deleteFile(bucketId, fileId)
    }, 'Failed to delete file')
  }

  /**
   * Delete multiple files
   */
  async deleteFiles(bucketId: string, fileIds: string[]): Promise<void> {
    for (const fileId of fileIds) {
      try {
        await this.deleteFile(bucketId, fileId)
      } catch (error) {
        console.error(`Failed to delete file ${fileId}:`, error)
      }
    }
  }

  /**
   * Get file preview URL
   */
  getFilePreview(
    bucketId: string,
    fileId: string,
    width?: number,
    height?: number,
    gravity?: string,
    quality?: number,
    borderWidth?: number,
    borderColor?: string,
    borderRadius?: number,
    opacity?: number,
    rotation?: number,
    background?: string,
    output?: string
  ): string {
    const services = getServices()
    return services.storage.getFilePreview(
      bucketId,
      fileId,
      width,
      height,
      gravity,
      quality,
      borderWidth,
      borderColor,
      borderRadius,
      opacity,
      rotation,
      background,
      output
    ).href
  }

  /**
   * List files in a bucket
   */
  async listFiles(
    bucketId: string,
    queries?: string[]
  ): Promise<Models.FileList> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      return await services.storage.listFiles(bucketId, queries)
    }, 'Failed to list files')
  }

  /**
   * Get file URL for viewing
   */
  getFileUrl(bucketId: string, fileId: string): string {
    return getFileUrl(bucketId, fileId)
  }

  /**
   * Get file download URL
   */
  getFileDownloadUrl(bucketId: string, fileId: string): string {
    return getFileDownloadUrl(bucketId, fileId)
  }

  /**
   * Validate file before upload
   */
  private validateFile(file: File, bucketId: string): void {
    // Get bucket configuration
    const bucketConfig = Object.values(STORAGE_BUCKETS).find(
      bucket => bucket.id === bucketId
    )

    if (!bucketConfig) {
      throw new Error(`Unknown bucket: ${bucketId}`)
    }

    // Validate file type
    if (!isValidFileType(file, bucketConfig.allowedFileTypes)) {
      throw new Error(APPWRITE_ERRORS.INVALID_FILE_TYPE)
    }

    // Validate file size
    if (!isValidFileSize(file, bucketConfig.maxFileSize)) {
      throw new Error(APPWRITE_ERRORS.FILE_TOO_LARGE)
    }
  }

  /**
   * Get optimized image URL for product images
   */
  getOptimizedImageUrl(
    fileId: string,
    width: number = 800,
    height: number = 600,
    quality: number = 80
  ): string {
    return this.getFilePreview(
      appwriteConfig.storageBucketId,
      fileId,
      width,
      height,
      'center',
      quality,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      'webp'
    )
  }

  /**
   * Get thumbnail URL for product images
   */
  getThumbnailUrl(fileId: string, size: number = 200): string {
    return this.getFilePreview(
      appwriteConfig.storageBucketId,
      fileId,
      size,
      size,
      'center',
      80,
      undefined,
      undefined,
      8, // border radius for rounded corners
      undefined,
      undefined,
      undefined,
      'webp'
    )
  }
}

// Export singleton instance
export const storageService = AppwriteStorageService.getInstance()

// Utility functions for common operations
export async function uploadProductImage(file: File): Promise<UploadResult> {
  return storageService.uploadFile(file, {
    bucketId: STORAGE_BUCKETS.PRODUCT_IMAGES.id
  })
}

export async function uploadProductImages(files: File[]): Promise<UploadResult[]> {
  return storageService.uploadFiles(files, {
    bucketId: STORAGE_BUCKETS.PRODUCT_IMAGES.id
  })
}

export async function uploadUserAvatar(file: File): Promise<UploadResult> {
  return storageService.uploadFile(file, {
    bucketId: STORAGE_BUCKETS.USER_AVATARS.id
  })
}

export function getProductImageUrl(fileId: string, optimized: boolean = true): string {
  if (optimized) {
    return storageService.getOptimizedImageUrl(fileId)
  }
  return getFileUrl(appwriteConfig.storageBucketId, fileId)
}

export function getProductThumbnailUrl(fileId: string): string {
  return storageService.getThumbnailUrl(fileId)
}
