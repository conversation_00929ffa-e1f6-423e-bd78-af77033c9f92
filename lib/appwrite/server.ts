import { Client, Storage, Databases, Permission, Role, Compression } from 'node-appwrite'

// Server-side Appwrite client with API key
const client = new Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || '')
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '')
  .setKey(process.env.NEXT_PUBLIC_APPWRITE_API_KEY || '')

const storage = new Storage(client)
const databases = new Databases(client)

export const MEDIA_BUCKET_ID = process.env.NEXT_PUBLIC_APPWRITE_MEDIA_BUCKET_ID || 'media'
export const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || 'main'
export const MEDIA_COLLECTION_ID = process.env.NEXT_PUBLIC_APPWRITE_MEDIA_COLLECTION_ID || 'media-metadata'

export interface BucketConfig {
  bucketId: string
  name: string
  permissions: string[]
  fileSecurity: boolean
  enabled: boolean
  maximumFileSize: number
  allowedFileExtensions: string[]
  compression: Compression
  encryption: boolean
  antivirus: boolean
}

export interface CollectionConfig {
  collectionId: string
  name: string
  permissions: string[]
  documentSecurity: boolean
  enabled: boolean
}

export class AppwriteServerService {
  private storage: Storage
  private databases: Databases

  constructor() {
    this.storage = storage
    this.databases = databases
  }

  /**
   * Create media storage bucket with optimal configuration
   */
  async createMediaBucket(config?: Partial<BucketConfig>): Promise<any> {
    const defaultConfig: BucketConfig = {
      bucketId: MEDIA_BUCKET_ID,
      name: 'Media Library',
      permissions: [
        Permission.read(Role.any()),
        Permission.create(Role.users()),
        Permission.update(Role.users()),
        Permission.delete(Role.users())
      ],
      fileSecurity: true,
      enabled: true,
      maximumFileSize: 52428800, // 50MB
      allowedFileExtensions: [
        // Images
        'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'tiff', 'ico',
        // Videos
        'mp4', 'webm', 'ogg', 'avi', 'mov', 'wmv', 'flv', 'mkv', '3gp',
        // Audio
        'mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a', 'wma',
        // Documents
        'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'odt', 'ods', 'odp',
        // Archives
        'zip', 'rar', '7z', 'tar', 'gz',
        // Code
        'js', 'ts', 'jsx', 'tsx', 'css', 'scss', 'html', 'json', 'xml', 'yaml', 'yml'
      ],
      compression: Compression.Gzip,
      encryption: true,
      antivirus: true
    }

    const finalConfig = { ...defaultConfig, ...config }

    try {
      // Check if bucket already exists
      try {
        const existingBucket = await this.storage.getBucket(finalConfig.bucketId)
        console.log(`✅ Media bucket '${finalConfig.bucketId}' already exists`)
        return existingBucket
      } catch (error) {
        // Bucket doesn't exist, create it
      }

      console.log(`🚀 Creating media bucket '${finalConfig.bucketId}'...`)

      const bucket = await this.storage.createBucket(
        finalConfig.bucketId,
        finalConfig.name,
        finalConfig.permissions,
        finalConfig.fileSecurity,
        finalConfig.enabled,
        finalConfig.maximumFileSize,
        finalConfig.allowedFileExtensions,
        finalConfig.compression,
        finalConfig.encryption,
        finalConfig.antivirus
      )

      console.log(`✅ Media bucket '${finalConfig.bucketId}' created successfully`)
      return bucket
    } catch (error) {
      console.error(`❌ Error creating media bucket:`, error)
      throw new Error(`Failed to create media bucket: ${error}`)
    }
  }

  /**
   * Create database for metadata storage
   */
  async createDatabase(databaseId: string = DATABASE_ID, name: string = 'Main Database'): Promise<any> {
    try {
      // Check if database already exists
      try {
        const existingDatabase = await this.databases.get(databaseId)
        console.log(`✅ Database '${databaseId}' already exists`)
        return existingDatabase
      } catch (error) {
        // Database doesn't exist, create it
      }

      console.log(`🚀 Creating database '${databaseId}'...`)

      const database = await this.databases.create(
        databaseId,
        name,
        true // enabled
      )

      console.log(`✅ Database '${databaseId}' created successfully`)
      return database
    } catch (error) {
      console.error(`❌ Error creating database:`, error)
      throw new Error(`Failed to create database: ${error}`)
    }
  }

  /**
   * Create media metadata collection
   */
  async createMediaMetadataCollection(config?: Partial<CollectionConfig>): Promise<any> {
    const defaultConfig: CollectionConfig = {
      collectionId: MEDIA_COLLECTION_ID,
      name: 'Media Metadata',
      permissions: [
        Permission.read(Role.any()),
        Permission.create(Role.users()),
        Permission.update(Role.users()),
        Permission.delete(Role.users())
      ],
      documentSecurity: true,
      enabled: true
    }

    const finalConfig = { ...defaultConfig, ...config }

    try {
      // Ensure database exists first
      await this.createDatabase()

      // Check if collection already exists
      try {
        const existingCollection = await this.databases.getCollection(DATABASE_ID, finalConfig.collectionId)
        console.log(`✅ Media metadata collection '${finalConfig.collectionId}' already exists`)
        return existingCollection
      } catch (error) {
        // Collection doesn't exist, create it
      }

      console.log(`🚀 Creating media metadata collection '${finalConfig.collectionId}'...`)

      const collection = await this.databases.createCollection(
        DATABASE_ID,
        finalConfig.collectionId,
        finalConfig.name,
        finalConfig.permissions,
        finalConfig.documentSecurity,
        finalConfig.enabled
      )

      // Create attributes for the collection
      await this.createMediaMetadataAttributes(finalConfig.collectionId)

      console.log(`✅ Media metadata collection '${finalConfig.collectionId}' created successfully`)
      return collection
    } catch (error) {
      console.error(`❌ Error creating media metadata collection:`, error)
      throw new Error(`Failed to create media metadata collection: ${error}`)
    }
  }

  /**
   * Create attributes for media metadata collection
   */
  private async createMediaMetadataAttributes(collectionId: string): Promise<void> {
    const attributes = [
      {
        key: 'fileId',
        type: 'string',
        size: 255,
        required: true,
        array: false
      },
      {
        key: 'alt',
        type: 'string',
        size: 255,
        required: false,
        array: false
      },
      {
        key: 'title',
        type: 'string',
        size: 255,
        required: false,
        array: false
      },
      {
        key: 'description',
        type: 'string',
        size: 1000,
        required: false,
        array: false
      },
      {
        key: 'tags',
        type: 'string',
        size: 100,
        required: false,
        array: true
      },
      {
        key: 'folder',
        type: 'string',
        size: 255,
        required: false,
        array: false
      },
      {
        key: 'width',
        type: 'integer',
        required: false,
        array: false
      },
      {
        key: 'height',
        type: 'integer',
        required: false,
        array: false
      },
      {
        key: 'mimeType',
        type: 'string',
        size: 100,
        required: false,
        array: false
      },
      {
        key: 'fileSize',
        type: 'integer',
        required: false,
        array: false
      }
    ]

    console.log(`🚀 Creating attributes for collection '${collectionId}'...`)

    for (const attr of attributes) {
      try {
        if (attr.type === 'string') {
          await this.databases.createStringAttribute(
            DATABASE_ID,
            collectionId,
            attr.key,
            attr.size!,
            attr.required,
            undefined, // default value
            attr.array
          )
        } else if (attr.type === 'integer') {
          await this.databases.createIntegerAttribute(
            DATABASE_ID,
            collectionId,
            attr.key,
            attr.required,
            undefined, // min
            undefined, // max
            undefined, // default
            attr.array
          )
        }

        console.log(`  ✅ Created attribute: ${attr.key}`)
      } catch (error) {
        console.log(`  ⚠️  Attribute '${attr.key}' might already exist`)
      }
    }

    // Create indexes
    await this.createMediaMetadataIndexes(collectionId)
  }

  /**
   * Create indexes for media metadata collection
   */
  private async createMediaMetadataIndexes(collectionId: string): Promise<void> {
    const indexes = [
      {
        key: 'fileId_index',
        type: 'key',
        attributes: ['fileId']
      },
      {
        key: 'folder_index',
        type: 'key',
        attributes: ['folder']
      },
      {
        key: 'mimeType_index',
        type: 'key',
        attributes: ['mimeType']
      },
      {
        key: 'tags_search',
        type: 'fulltext',
        attributes: ['tags']
      },
      {
        key: 'title_search',
        type: 'fulltext',
        attributes: ['title', 'description']
      }
    ]

    console.log(`🚀 Creating indexes for collection '${collectionId}'...`)

    for (const index of indexes) {
      try {
        await this.databases.createIndex(
          DATABASE_ID,
          collectionId,
          index.key,
          index.type as any,
          index.attributes
        )
        console.log(`  ✅ Created index: ${index.key}`)
      } catch (error) {
        console.log(`  ⚠️  Index '${index.key}' might already exist`)
      }
    }
  }

  /**
   * Setup complete media library infrastructure
   */
  async setupMediaLibrary(): Promise<{
    bucket: any
    database: any
    collection: any
  }> {
    console.log('🚀 Setting up complete media library infrastructure...')

    try {
      // Create media bucket
      const bucket = await this.createMediaBucket()

      // Create database
      const database = await this.createDatabase()

      // Create media metadata collection
      const collection = await this.createMediaMetadataCollection()

      console.log('✅ Media library infrastructure setup completed successfully!')

      return {
        bucket,
        database,
        collection
      }
    } catch (error) {
      console.error('❌ Failed to setup media library infrastructure:', error)
      throw error
    }
  }

  /**
   * Get bucket information
   */
  async getBucketInfo(bucketId: string = MEDIA_BUCKET_ID): Promise<any> {
    try {
      return await this.storage.getBucket(bucketId)
    } catch (error) {
      console.error(`❌ Error getting bucket info:`, error)
      throw error
    }
  }

  /**
   * Update bucket permissions
   */
  async updateBucketPermissions(
    bucketId: string = MEDIA_BUCKET_ID,
    permissions: string[]
  ): Promise<any> {
    try {
      const bucket = await this.storage.getBucket(bucketId)
      return await this.storage.updateBucket(
        bucketId,
        bucket.name,
        permissions,
        bucket.fileSecurity,
        bucket.enabled,
        bucket.maximumFileSize,
        bucket.allowedFileExtensions,
        bucket.compression as Compression,
        bucket.encryption,
        bucket.antivirus
      )
    } catch (error) {
      console.error(`❌ Error updating bucket permissions:`, error)
      throw error
    }
  }

  /**
   * Delete bucket (use with caution)
   */
  async deleteBucket(bucketId: string): Promise<void> {
    try {
      await this.storage.deleteBucket(bucketId)
      console.log(`✅ Bucket '${bucketId}' deleted successfully`)
    } catch (error) {
      console.error(`❌ Error deleting bucket:`, error)
      throw error
    }
  }
}

// Export singleton instance
export const appwriteServer = new AppwriteServerService()
