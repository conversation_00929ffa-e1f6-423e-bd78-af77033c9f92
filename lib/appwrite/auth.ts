// Appwrite Authentication service

import { ID, Models } from 'appwrite'
import { getServices, withAppwriteErrorHandling } from './client'
import { APPWRITE_ERRORS } from './config'

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterCredentials {
  email: string
  password: string
  name: string
}

export interface AuthUser {
  $id: string
  name: string
  email: string
  emailVerification: boolean
  phone: string
  phoneVerification: boolean
  prefs: Record<string, any>
  registration: string
  status: boolean
}

export interface AuthSession {
  $id: string
  userId: string
  expire: string
  provider: string
  providerUid: string
  providerAccessToken: string
  providerAccessTokenExpiry: string
  providerRefreshToken: string
  ip: string
  osCode: string
  osName: string
  osVersion: string
  clientType: string
  clientCode: string
  clientName: string
  clientVersion: string
  clientEngine: string
  clientEngineVersion: string
  deviceName: string
  deviceBrand: string
  deviceModel: string
  countryCode: string
  countryName: string
  current: boolean
}

export class AppwriteAuthService {
  private static instance: AppwriteAuthService

  static getInstance(): AppwriteAuthService {
    if (!AppwriteAuthService.instance) {
      AppwriteAuthService.instance = new AppwriteAuthService()
    }
    return AppwriteAuthService.instance
  }

  /**
   * Register a new user
   */
  async register(credentials: RegisterCredentials): Promise<Models.User<Models.Preferences>> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      return await services.account.create(
        ID.unique(),
        credentials.email,
        credentials.password,
        credentials.name
      )
    }, APPWRITE_ERRORS.AUTH_ERROR)
  }

  /**
   * Login user with email and password
   */
  async login(credentials: LoginCredentials): Promise<Models.Session> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      return await services.account.createEmailSession(
        credentials.email,
        credentials.password
      )
    }, APPWRITE_ERRORS.AUTH_ERROR)
  }

  /**
   * Logout current session
   */
  async logout(): Promise<void> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      await services.account.deleteSession('current')
    }, 'Logout failed')
  }

  /**
   * Logout from all sessions
   */
  async logoutAll(): Promise<void> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      await services.account.deleteSessions()
    }, 'Logout from all sessions failed')
  }

  /**
   * Get current user
   */
  async getCurrentUser(): Promise<Models.User<Models.Preferences> | null> {
    try {
      const services = getServices()
      return await services.account.get()
    } catch (error) {
      // User not authenticated
      return null
    }
  }

  /**
   * Get current session
   */
  async getCurrentSession(): Promise<Models.Session | null> {
    try {
      const services = getServices()
      return await services.account.getSession('current')
    } catch (error) {
      // No active session
      return null
    }
  }

  /**
   * Update user name
   */
  async updateName(name: string): Promise<Models.User<Models.Preferences>> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      return await services.account.updateName(name)
    }, 'Failed to update name')
  }

  /**
   * Update user email
   */
  async updateEmail(email: string, password: string): Promise<Models.User<Models.Preferences>> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      return await services.account.updateEmail(email, password)
    }, 'Failed to update email')
  }

  /**
   * Update user password
   */
  async updatePassword(newPassword: string, oldPassword: string): Promise<Models.User<Models.Preferences>> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      return await services.account.updatePassword(newPassword, oldPassword)
    }, 'Failed to update password')
  }

  /**
   * Update user preferences
   */
  async updatePreferences(prefs: Record<string, any>): Promise<Models.User<Models.Preferences>> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      return await services.account.updatePrefs(prefs)
    }, 'Failed to update preferences')
  }

  /**
   * Send email verification
   */
  async sendEmailVerification(url: string): Promise<Models.Token> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      return await services.account.createVerification(url)
    }, 'Failed to send email verification')
  }

  /**
   * Confirm email verification
   */
  async confirmEmailVerification(userId: string, secret: string): Promise<Models.Token> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      return await services.account.updateVerification(userId, secret)
    }, 'Failed to verify email')
  }

  /**
   * Send password recovery email
   */
  async sendPasswordRecovery(email: string, url: string): Promise<Models.Token> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      return await services.account.createRecovery(email, url)
    }, 'Failed to send password recovery email')
  }

  /**
   * Confirm password recovery
   */
  async confirmPasswordRecovery(
    userId: string,
    secret: string,
    password: string,
    passwordAgain: string
  ): Promise<Models.Token> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      return await services.account.updateRecovery(userId, secret, password, passwordAgain)
    }, 'Failed to reset password')
  }

  /**
   * List user sessions
   */
  async listSessions(): Promise<Models.SessionList> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      return await services.account.listSessions()
    }, 'Failed to list sessions')
  }

  /**
   * Delete a specific session
   */
  async deleteSession(sessionId: string): Promise<void> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      await services.account.deleteSession(sessionId)
    }, 'Failed to delete session')
  }

  /**
   * Create anonymous session
   */
  async createAnonymousSession(): Promise<Models.Session> {
    return withAppwriteErrorHandling(async () => {
      const services = getServices()
      return await services.account.createAnonymousSession()
    }, 'Failed to create anonymous session')
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      const user = await this.getCurrentUser()
      return user !== null
    } catch (error) {
      return false
    }
  }

  /**
   * Get user preferences with defaults
   */
  async getUserPreferences(): Promise<Record<string, any>> {
    try {
      const user = await this.getCurrentUser()
      return user?.prefs || {}
    } catch (error) {
      return {}
    }
  }

  /**
   * Set user preference
   */
  async setUserPreference(key: string, value: any): Promise<void> {
    const currentPrefs = await this.getUserPreferences()
    await this.updatePreferences({
      ...currentPrefs,
      [key]: value
    })
  }

  /**
   * Get user preference
   */
  async getUserPreference(key: string, defaultValue: any = null): Promise<any> {
    const prefs = await this.getUserPreferences()
    return prefs[key] ?? defaultValue
  }
}

// Export singleton instance
export const authService = AppwriteAuthService.getInstance()

// Utility functions
export async function requireAuth(): Promise<Models.User<Models.Preferences>> {
  const user = await authService.getCurrentUser()
  if (!user) {
    throw new Error('Authentication required')
  }
  return user
}

export async function getAuthenticatedUser(): Promise<Models.User<Models.Preferences> | null> {
  return authService.getCurrentUser()
}

export async function isUserAuthenticated(): Promise<boolean> {
  return authService.isAuthenticated()
}
