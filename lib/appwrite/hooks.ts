// React hooks for Appwrite integration

'use client'

import { useState, useEffect, useCallback } from 'react'
import { Models } from 'appwrite'
import { 
  authService, 
  storageService, 
  databaseService,
  realtimeService,
  isAppwriteConfigured,
  getAppwriteStatus,
  AppwriteStatus
} from './index'

// Hook for authentication state
export function useAppwriteAuth() {
  const [user, setUser] = useState<Models.User<Models.Preferences> | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!isAppwriteConfigured()) {
      setLoading(false)
      setError('Appwrite not configured')
      return
    }

    const checkAuth = async () => {
      try {
        const currentUser = await authService.getCurrentUser()
        setUser(currentUser)
        setError(null)
      } catch (err) {
        setUser(null)
        setError(err instanceof Error ? err.message : 'Authentication check failed')
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [])

  const login = useCallback(async (email: string, password: string) => {
    setLoading(true)
    setError(null)
    
    try {
      await authService.login({ email, password })
      const currentUser = await authService.getCurrentUser()
      setUser(currentUser)
      return { success: true }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [])

  const register = useCallback(async (email: string, password: string, name: string) => {
    setLoading(true)
    setError(null)
    
    try {
      await authService.register({ email, password, name })
      const currentUser = await authService.getCurrentUser()
      setUser(currentUser)
      return { success: true }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Registration failed'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [])

  const logout = useCallback(async () => {
    setLoading(true)
    
    try {
      await authService.logout()
      setUser(null)
      setError(null)
      return { success: true }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Logout failed'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    user,
    loading,
    error,
    isAuthenticated: !!user,
    login,
    register,
    logout
  }
}

// Hook for file uploads
export function useAppwriteStorage() {
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)

  const uploadFile = useCallback(async (file: File, bucketId?: string) => {
    if (!isAppwriteConfigured()) {
      throw new Error('Appwrite not configured')
    }

    setUploading(true)
    setUploadProgress(0)
    setError(null)

    try {
      const result = await storageService.uploadFile(file, {
        bucketId,
        onProgress: setUploadProgress
      })
      
      setUploadProgress(100)
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed'
      setError(errorMessage)
      throw err
    } finally {
      setUploading(false)
    }
  }, [])

  const uploadFiles = useCallback(async (files: File[], bucketId?: string) => {
    if (!isAppwriteConfigured()) {
      throw new Error('Appwrite not configured')
    }

    setUploading(true)
    setUploadProgress(0)
    setError(null)

    try {
      const results = await storageService.uploadFiles(files, {
        bucketId,
        onProgress: setUploadProgress
      })
      
      setUploadProgress(100)
      return results
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed'
      setError(errorMessage)
      throw err
    } finally {
      setUploading(false)
    }
  }, [])

  const deleteFile = useCallback(async (bucketId: string, fileId: string) => {
    if (!isAppwriteConfigured()) {
      throw new Error('Appwrite not configured')
    }

    try {
      await storageService.deleteFile(bucketId, fileId)
      return { success: true }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Delete failed'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    }
  }, [])

  return {
    uploading,
    uploadProgress,
    error,
    uploadFile,
    uploadFiles,
    deleteFile
  }
}

// Hook for database operations
export function useAppwriteDatabase<T = any>(collectionId: string) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createDocument = useCallback(async (data: T) => {
    if (!isAppwriteConfigured()) {
      throw new Error('Appwrite not configured')
    }

    setLoading(true)
    setError(null)

    try {
      const result = await databaseService.createDocument(collectionId, data)
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Create failed'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [collectionId])

  const updateDocument = useCallback(async (documentId: string, data: Partial<T>) => {
    if (!isAppwriteConfigured()) {
      throw new Error('Appwrite not configured')
    }

    setLoading(true)
    setError(null)

    try {
      const result = await databaseService.updateDocument(collectionId, documentId, data)
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Update failed'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [collectionId])

  const deleteDocument = useCallback(async (documentId: string) => {
    if (!isAppwriteConfigured()) {
      throw new Error('Appwrite not configured')
    }

    setLoading(true)
    setError(null)

    try {
      await databaseService.deleteDocument(collectionId, documentId)
      return { success: true }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Delete failed'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [collectionId])

  const getDocument = useCallback(async (documentId: string) => {
    if (!isAppwriteConfigured()) {
      throw new Error('Appwrite not configured')
    }

    setLoading(true)
    setError(null)

    try {
      const result = await databaseService.getDocument(collectionId, documentId)
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Get failed'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [collectionId])

  const listDocuments = useCallback(async (queries?: string[]) => {
    if (!isAppwriteConfigured()) {
      throw new Error('Appwrite not configured')
    }

    setLoading(true)
    setError(null)

    try {
      const result = await databaseService.listDocuments(collectionId, { queries })
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'List failed'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [collectionId])

  return {
    loading,
    error,
    createDocument,
    updateDocument,
    deleteDocument,
    getDocument,
    listDocuments
  }
}

// Hook for Appwrite connection status
export function useAppwriteStatus() {
  const [status, setStatus] = useState<AppwriteStatus>(AppwriteStatus.NOT_CONFIGURED)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const checkStatus = async () => {
      try {
        const currentStatus = await getAppwriteStatus()
        setStatus(currentStatus)
      } catch (error) {
        setStatus(AppwriteStatus.ERROR)
      } finally {
        setLoading(false)
      }
    }

    checkStatus()

    // Check status every 30 seconds
    const interval = setInterval(checkStatus, 30000)
    return () => clearInterval(interval)
  }, [])

  return {
    status,
    loading,
    isConfigured: status !== AppwriteStatus.NOT_CONFIGURED,
    isConnected: status === AppwriteStatus.CONNECTED,
    hasError: status === AppwriteStatus.ERROR
  }
}
