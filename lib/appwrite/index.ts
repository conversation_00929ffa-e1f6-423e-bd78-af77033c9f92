// Main Appwrite integration exports

// Configuration
export * from './config'

// Client setup
export * from './client'

// Services
export * from './storage'
export * from './database'
export * from './auth'
export * from './realtime'

// Re-export commonly used Appwrite types
export { ID, Query, Permission, Role } from 'appwrite'

// Define Models namespace for compatibility
export namespace Models {
  export interface Document {
    $id: string
    $collectionId: string
    $databaseId: string
    $createdAt: string
    $updatedAt: string
    $permissions: string[]
    [key: string]: any
  }

  export interface DocumentList<T extends Document> {
    total: number
    documents: T[]
  }

  export interface File {
    $id: string
    bucketId: string
    name: string
    signature: string
    mimeType: string
    sizeOriginal: number
    chunksTotal: number
    chunksUploaded: number
    $createdAt: string
    $updatedAt: string
  }

  export interface FileList {
    total: number
    files: File[]
  }

  export interface User<T = any> {
    $id: string
    $createdAt: string
    $updatedAt: string
    name: string
    registration: string
    status: boolean
    labels: string[]
    passwordUpdate: string
    email: string
    phone: string
    emailVerification: boolean
    phoneVerification: boolean
    mfa: boolean
    prefs: T
    targets: any[]
    accessedAt: string
  }

  export interface Preferences {
    [key: string]: any
  }

  export interface Session {
    $id: string
    $createdAt: string
    $updatedAt: string
    userId: string
    expire: string
    provider: string
    providerUid: string
    providerAccessToken: string
    providerAccessTokenExpiry: string
    providerRefreshToken: string
    ip: string
    osCode: string
    osName: string
    osVersion: string
    clientType: string
    clientCode: string
    clientName: string
    clientVersion: string
    clientEngine: string
    clientEngineVersion: string
    deviceName: string
    deviceBrand: string
    deviceModel: string
    countryCode: string
    countryName: string
    current: boolean
    factors: string[]
    secret: string
    mfaUpdatedAt: string
  }

  export interface SessionList {
    total: number
    sessions: Session[]
  }

  export interface Token {
    $id: string
    $createdAt: string
    userId: string
    secret: string
    expire: string
    phrase: string
  }
}

// Main Appwrite integration class
import { AppwriteServices, AppwriteServerServices } from './client'
import { AppwriteStorageService } from './storage'
import { AppwriteDatabaseService } from './database'
import { AppwriteAuthService } from './auth'
import { AppwriteRealtimeService } from './realtime'
import { appwriteConfig, validateAppwriteConfig } from './config'

export class AppwriteIntegration {
  private static instance: AppwriteIntegration

  public readonly storage: AppwriteStorageService
  public readonly database: AppwriteDatabaseService
  public readonly auth: AppwriteAuthService
  public readonly realtime: AppwriteRealtimeService

  private constructor() {
    this.storage = AppwriteStorageService.getInstance()
    this.database = AppwriteDatabaseService.getInstance()
    this.auth = AppwriteAuthService.getInstance()
    this.realtime = AppwriteRealtimeService.getInstance()
  }

  static getInstance(): AppwriteIntegration {
    if (!AppwriteIntegration.instance) {
      AppwriteIntegration.instance = new AppwriteIntegration()
    }
    return AppwriteIntegration.instance
  }

  /**
   * Initialize Appwrite integration
   */
  async initialize(): Promise<void> {
    if (!validateAppwriteConfig(appwriteConfig)) {
      throw new Error('Invalid Appwrite configuration. Please check your environment variables.')
    }

    try {
      // Test connection
      const services = AppwriteServices.getInstance()
      const isHealthy = await services.healthCheck()
      
      if (!isHealthy) {
        console.warn('Appwrite health check failed, but continuing...')
      }

      console.log('Appwrite integration initialized successfully')
    } catch (error) {
      console.error('Failed to initialize Appwrite integration:', error)
      throw error
    }
  }

  /**
   * Check if Appwrite is properly configured
   */
  isConfigured(): boolean {
    return validateAppwriteConfig(appwriteConfig)
  }

  /**
   * Get configuration status
   */
  getConfigStatus() {
    return {
      configured: this.isConfigured(),
      endpoint: !!appwriteConfig.endpoint,
      projectId: !!appwriteConfig.projectId,
      databaseId: !!appwriteConfig.databaseId,
      storageBucketId: !!appwriteConfig.storageBucketId,
    }
  }

  /**
   * Cleanup all resources
   */
  cleanup(): void {
    try {
      this.realtime.unsubscribeAll()
    } catch (error) {
      console.warn('Error cleaning up realtime subscriptions:', error)
    }
  }
}

// Create and export default instance
export const appwriteIntegration = AppwriteIntegration.getInstance()

// Utility functions for easy access
export async function initializeAppwrite(): Promise<void> {
  return appwriteIntegration.initialize()
}

export function isAppwriteConfigured(): boolean {
  return appwriteIntegration.isConfigured()
}

export function getAppwriteConfigStatus() {
  return appwriteIntegration.getConfigStatus()
}

// Feature detection
export const APPWRITE_FEATURES_AVAILABLE = {
  STORAGE: true,
  DATABASE: true,
  AUTH: true,
  REALTIME: true,
  FUNCTIONS: false, // Not implemented yet
  MESSAGING: false, // Not implemented yet
} as const

// Integration status
export enum AppwriteStatus {
  NOT_CONFIGURED = 'not_configured',
  CONFIGURED = 'configured',
  CONNECTED = 'connected',
  ERROR = 'error'
}

export async function getAppwriteStatus(): Promise<AppwriteStatus> {
  if (!isAppwriteConfigured()) {
    return AppwriteStatus.NOT_CONFIGURED
  }

  try {
    const services = AppwriteServices.getInstance()
    const isHealthy = await services.healthCheck()
    
    if (isHealthy) {
      return AppwriteStatus.CONNECTED
    } else {
      return AppwriteStatus.CONFIGURED
    }
  } catch (error) {
    return AppwriteStatus.ERROR
  }
}

// Error handling utilities
export function isAppwriteError(error: any): boolean {
  return error && typeof error.code === 'number' && typeof error.type === 'string'
}

export function getAppwriteErrorMessage(error: any): string {
  if (isAppwriteError(error)) {
    return error.message || `Appwrite error: ${error.type} (${error.code})`
  }
  return error?.message || 'Unknown Appwrite error'
}

// Migration utilities (for moving from other systems to Appwrite)
export interface MigrationOptions {
  batchSize?: number
  onProgress?: (progress: number, total: number) => void
  onError?: (error: Error, item: any) => void
}

export class AppwriteMigrationHelper {
  static async migrateFiles(
    files: Array<{ file: File; metadata?: any }>,
    bucketId: string,
    options: MigrationOptions = {}
  ): Promise<Array<{ success: boolean; fileId?: string; error?: Error }>> {
    const { batchSize = 10, onProgress, onError } = options
    const results: Array<{ success: boolean; fileId?: string; error?: Error }> = []
    
    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize)
      
      for (const { file, metadata } of batch) {
        try {
          const result = await appwriteIntegration.storage.uploadFile(file, {
            bucketId,
            ...metadata
          })
          
          results.push({ success: true, fileId: result.fileId })
        } catch (error) {
          const err = error as Error
          results.push({ success: false, error: err })
          onError?.(err, { file, metadata })
        }
      }
      
      onProgress?.(Math.min(i + batchSize, files.length), files.length)
    }
    
    return results
  }

  static async migrateDocuments(
    documents: Array<any>,
    collectionId: string,
    options: MigrationOptions = {}
  ): Promise<Array<{ success: boolean; documentId?: string; error?: Error }>> {
    const { batchSize = 50, onProgress, onError } = options
    const results: Array<{ success: boolean; documentId?: string; error?: Error }> = []
    
    for (let i = 0; i < documents.length; i += batchSize) {
      const batch = documents.slice(i, i + batchSize)
      
      for (const document of batch) {
        try {
          const result = await appwriteIntegration.database.createDocument(
            collectionId,
            document
          )
          
          results.push({ success: true, documentId: result.$id })
        } catch (error) {
          const err = error as Error
          results.push({ success: false, error: err })
          onError?.(err, document)
        }
      }
      
      onProgress?.(Math.min(i + batchSize, documents.length), documents.length)
    }
    
    return results
  }
}

export const migrationHelper = AppwriteMigrationHelper
