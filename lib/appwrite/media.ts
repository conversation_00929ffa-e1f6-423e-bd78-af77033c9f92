import { Client, Storage, ID, Query } from 'appwrite'

// Initialize Appwrite client
const client = new Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || '')
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '')

const storage = new Storage(client)

export const MEDIA_BUCKET_ID = process.env.NEXT_PUBLIC_APPWRITE_MEDIA_BUCKET_ID || 'media'

export interface MediaFile {
  $id: string
  name: string
  mimeType: string
  sizeOriginal: number
  $createdAt: string
  $updatedAt: string
  url?: string
  preview?: string
  metadata?: {
    width?: number
    height?: number
    alt?: string
    title?: string
    description?: string
    tags?: string[]
    folder?: string
  }
}

export interface MediaUploadOptions {
  file: File
  folder?: string
  alt?: string
  title?: string
  description?: string
  tags?: string[]
}

export interface MediaLibraryFilters {
  type?: 'image' | 'video' | 'audio' | 'document' | 'all'
  folder?: string
  search?: string
  tags?: string[]
  dateRange?: {
    start: Date
    end: Date
  }
}

export class MediaLibrary {
  private bucketId: string

  constructor(bucketId: string = MEDIA_BUCKET_ID) {
    this.bucketId = bucketId
  }

  /**
   * Upload a file to Appwrite storage
   */
  async uploadFile(options: MediaUploadOptions): Promise<MediaFile> {
    try {
      const { file, folder, alt, title, description, tags } = options
      
      // Note: Appwrite handles unique file naming automatically

      // Upload file to Appwrite
      const uploadedFile = await storage.createFile(
        this.bucketId,
        ID.unique(),
        file
      )

      // Get file URL and preview
      const fileUrl = this.getFileUrl(uploadedFile.$id)
      const previewUrl = this.isImage(file.type) 
        ? this.getFilePreview(uploadedFile.$id, 400, 300)
        : undefined

      // Extract image dimensions if it's an image
      let dimensions: { width?: number; height?: number } = {}
      if (this.isImage(file.type)) {
        dimensions = await this.getImageDimensions(file)
      }

      // Create media file object
      const mediaFile: MediaFile = {
        $id: uploadedFile.$id,
        name: uploadedFile.name,
        mimeType: uploadedFile.mimeType,
        sizeOriginal: uploadedFile.sizeOriginal,
        $createdAt: uploadedFile.$createdAt,
        $updatedAt: uploadedFile.$updatedAt,
        url: fileUrl,
        preview: previewUrl,
        metadata: {
          ...dimensions,
          alt,
          title,
          description,
          tags,
          folder
        }
      }

      return mediaFile
    } catch (error) {
      console.error('Error uploading file:', error)
      throw new Error('Failed to upload file')
    }
  }

  /**
   * Get list of files with filters
   */
  async getFiles(
    filters: MediaLibraryFilters = {},
    limit: number = 25,
    offset: number = 0
  ): Promise<{ files: MediaFile[]; total: number }> {
    try {
      const queries: string[] = [
        Query.limit(limit),
        Query.offset(offset),
        Query.orderDesc('$createdAt')
      ]

      // Add type filter
      if (filters.type && filters.type !== 'all') {
        const mimeTypes = this.getMimeTypesForFilter(filters.type)
        queries.push(Query.contains('mimeType', mimeTypes))
      }

      // Add search filter
      if (filters.search) {
        queries.push(Query.search('name', filters.search))
      }

      const result = await storage.listFiles(this.bucketId, queries)

      const files: MediaFile[] = result.files.map(file => ({
        $id: file.$id,
        name: file.name,
        mimeType: file.mimeType,
        sizeOriginal: file.sizeOriginal,
        $createdAt: file.$createdAt,
        $updatedAt: file.$updatedAt,
        url: this.getFileUrl(file.$id),
        preview: this.isImage(file.mimeType) 
          ? this.getFilePreview(file.$id, 300, 200)
          : undefined,
        metadata: {
          // Metadata would be stored separately in a database collection
          // For now, we'll use basic file info
        }
      }))

      return {
        files: this.applyClientSideFilters(files, filters),
        total: result.total
      }
    } catch (error) {
      console.error('Error fetching files:', error)
      throw new Error('Failed to fetch files')
    }
  }

  /**
   * Delete a file
   */
  async deleteFile(fileId: string): Promise<void> {
    try {
      await storage.deleteFile(this.bucketId, fileId)
    } catch (error) {
      console.error('Error deleting file:', error)
      throw new Error('Failed to delete file')
    }
  }

  /**
   * Get file URL
   */
  getFileUrl(fileId: string): string {
    return storage.getFileView(this.bucketId, fileId).toString()
  }

  /**
   * Get file preview URL
   */
  getFilePreview(
    fileId: string, 
    width: number = 400, 
    height: number = 300,
    gravity: 'center' | 'top-left' | 'top' | 'top-right' | 'left' | 'right' | 'bottom-left' | 'bottom' | 'bottom-right' = 'center',
    quality: number = 100,
    borderWidth: number = 0,
    borderColor: string = '',
    borderRadius: number = 0,
    opacity: number = 1,
    rotation: number = 0,
    background: string = '',
    output: 'jpg' | 'jpeg' | 'png' | 'gif' | 'webp' = 'webp'
  ): string {
    return storage.getFilePreview(
      this.bucketId,
      fileId,
      width,
      height,
      gravity as any, // Type assertion for Appwrite compatibility
      quality,
      borderWidth,
      borderColor,
      borderRadius,
      opacity,
      rotation,
      background,
      output as any // Type assertion for Appwrite compatibility
    ).toString()
  }

  /**
   * Check if file is an image
   */
  private isImage(mimeType: string): boolean {
    return mimeType.startsWith('image/')
  }

  /**
   * Check if file is a video
   */
  isVideo(mimeType: string): boolean {
    return mimeType.startsWith('video/')
  }

  /**
   * Check if file is audio
   */
  isAudio(mimeType: string): boolean {
    return mimeType.startsWith('audio/')
  }

  /**
   * Get MIME types for filter
   */
  private getMimeTypesForFilter(type: string): string[] {
    switch (type) {
      case 'image':
        return ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
      case 'video':
        return ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov']
      case 'audio':
        return ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/aac', 'audio/flac']
      case 'document':
        return ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
      default:
        return []
    }
  }

  /**
   * Apply client-side filters
   */
  private applyClientSideFilters(files: MediaFile[], filters: MediaLibraryFilters): MediaFile[] {
    let filteredFiles = [...files]

    // Filter by folder
    if (filters.folder) {
      filteredFiles = filteredFiles.filter(file => 
        file.metadata?.folder === filters.folder
      )
    }

    // Filter by tags
    if (filters.tags && filters.tags.length > 0) {
      filteredFiles = filteredFiles.filter(file =>
        file.metadata?.tags?.some(tag => filters.tags!.includes(tag))
      )
    }

    // Filter by date range
    if (filters.dateRange) {
      filteredFiles = filteredFiles.filter(file => {
        const fileDate = new Date(file.$createdAt)
        return fileDate >= filters.dateRange!.start && fileDate <= filters.dateRange!.end
      })
    }

    return filteredFiles
  }

  /**
   * Get image dimensions
   */
  private async getImageDimensions(file: File): Promise<{ width?: number; height?: number }> {
    return new Promise((resolve) => {
      if (!file.type.startsWith('image/')) {
        resolve({})
        return
      }

      const img = new Image()
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height
        })
      }
      img.onerror = () => resolve({})
      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * Format file size
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * Get file type icon
   */
  static getFileTypeIcon(mimeType: string): string {
    if (mimeType.startsWith('image/')) return '🖼️'
    if (mimeType.startsWith('video/')) return '🎥'
    if (mimeType.startsWith('audio/')) return '🎵'
    if (mimeType.includes('pdf')) return '📄'
    if (mimeType.includes('word') || mimeType.includes('document')) return '📝'
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return '📊'
    if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return '📈'
    return '📁'
  }
}

// Export singleton instance
export const mediaLibrary = new MediaLibrary()
