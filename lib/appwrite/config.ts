// Appwrite configuration for the e-commerce system

export interface AppwriteConfig {
  endpoint: string
  projectId: string
  apiKey?: string
  databaseId: string
  storageBucketId: string
}

// Client-side configuration (public)
export const appwriteConfig: AppwriteConfig = {
  endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
  projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '',
  databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || '',
  storageBucketId: process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID || '',
}

// Server-side configuration (includes API key)
export const appwriteServerConfig: AppwriteConfig = {
  ...appwriteConfig,
  apiKey: process.env.NEXT_PUBLIC_APPWRITE_API_KEY || '',
}

// Collection IDs for different data types
export const APPWRITE_COLLECTIONS = {
  PRODUCTS: 'products',
  USERS: 'users',
  ORDERS: 'orders',
  CART_ITEMS: 'cart_items',
  REVIEWS: 'reviews',
  INVENTORY: 'inventory',
  ANALYTICS: 'analytics',
} as const

// Storage bucket configurations
export const STORAGE_BUCKETS = {
  PRODUCT_IMAGES: {
    id: appwriteConfig.storageBucketId,
    name: 'Product Images',
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedFileTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  },
  USER_AVATARS: {
    id: 'user-avatars',
    name: 'User Avatars',
    maxFileSize: 2 * 1024 * 1024, // 2MB
    allowedFileTypes: ['image/jpeg', 'image/png', 'image/webp'],
  },
  DOCUMENTS: {
    id: 'documents',
    name: 'Documents',
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedFileTypes: ['application/pdf', 'text/plain', 'application/msword'],
  },
  MEDIA_LIBRARY: {
    id: 'media-library',
    name: 'Media Library',
    maxFileSize: 50 * 1024 * 1024, // 50MB
    allowedFileTypes: [
      // Images
      'image/jpeg', 'image/png', 'image/webp', 'image/gif', 'image/svg+xml',
      // Videos
      'video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov',
      // Audio
      'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/aac',
      // Documents
      'application/pdf', 'text/plain', 'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      // Archives
      'application/zip', 'application/x-rar-compressed'
    ],
  },
} as const

// Database permissions
export const DATABASE_PERMISSIONS = {
  PUBLIC_READ: ['read("any")'],
  USER_READ_WRITE: ['read("user")', 'write("user")'],
  ADMIN_FULL: ['read("any")', 'write("any")', 'delete("any")'],
} as const

// Real-time channel patterns (for reference)
export const REALTIME_CHANNEL_PATTERNS = {
  DATABASES: (databaseId: string) => `databases.${databaseId}`,
  COLLECTIONS: (databaseId: string, collectionId: string) => `databases.${databaseId}.collections.${collectionId}`,
  DOCUMENTS: (databaseId: string, collectionId: string, documentId?: string) =>
    documentId
      ? `databases.${databaseId}.collections.${collectionId}.documents.${documentId}`
      : `databases.${databaseId}.collections.${collectionId}.documents`,
  STORAGE: (bucketId: string) => `buckets.${bucketId}`,
  FILES: (bucketId: string, fileId?: string) =>
    fileId
      ? `buckets.${bucketId}.files.${fileId}`
      : `buckets.${bucketId}.files`,
  ACCOUNT: 'account',
} as const

// Error messages
export const APPWRITE_ERRORS = {
  INVALID_CONFIG: 'Invalid Appwrite configuration',
  UPLOAD_FAILED: 'File upload failed',
  DOWNLOAD_FAILED: 'File download failed',
  DATABASE_ERROR: 'Database operation failed',
  AUTH_ERROR: 'Authentication failed',
  PERMISSION_DENIED: 'Permission denied',
  FILE_TOO_LARGE: 'File size exceeds limit',
  INVALID_FILE_TYPE: 'Invalid file type',
  NETWORK_ERROR: 'Network error occurred',
} as const

// Validation functions
export function validateAppwriteConfig(config: Partial<AppwriteConfig>): boolean {
  return !!(
    config.endpoint &&
    config.projectId &&
    config.databaseId &&
    config.storageBucketId
  )
}

export function isValidFileType(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.includes(file.type)
}

export function isValidFileSize(file: File, maxSize: number): boolean {
  return file.size <= maxSize
}

// Helper functions
export function getFileUrl(bucketId: string, fileId: string): string {
  return `${appwriteConfig.endpoint}/storage/buckets/${bucketId}/files/${fileId}/view?project=${appwriteConfig.projectId}`
}

export function getFileDownloadUrl(bucketId: string, fileId: string): string {
  return `${appwriteConfig.endpoint}/storage/buckets/${bucketId}/files/${fileId}/download?project=${appwriteConfig.projectId}`
}

export function generateFileId(): string {
  return `file_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

// Feature flags for Appwrite integration
export const APPWRITE_FEATURES = {
  STORAGE: true,
  DATABASE: true,
  AUTH: true,
  REALTIME: true,
  FUNCTIONS: false, // Not implemented yet
} as const

// Cache configuration for Appwrite data
export const APPWRITE_CACHE = {
  FILE_URLS: {
    ttl: 60 * 60 * 1000, // 1 hour
    key: 'appwrite_file_urls',
  },
  USER_DATA: {
    ttl: 5 * 60 * 1000, // 5 minutes
    key: 'appwrite_user_data',
  },
  PRODUCT_DATA: {
    ttl: 10 * 60 * 1000, // 10 minutes
    key: 'appwrite_product_data',
  },
} as const
