import { prisma } from '@/lib/ecommerce/config/database'

export interface SiteSettings {
  id: string
  homepageId?: string
  homepageSlug?: string
  siteName: string
  siteDescription: string
  siteUrl: string
  logoUrl?: string
  faviconUrl?: string
  socialMedia: {
    facebook?: string
    instagram?: string
    twitter?: string
    youtube?: string
  }
  seo: {
    defaultTitle: string
    defaultDescription: string
    defaultKeywords: string[]
    ogImage?: string
  }
  ecommerce: {
    currency: string
    taxRate: number
    freeShippingThreshold?: number
    defaultShippingCost: number
  }
  maintenance: {
    enabled: boolean
    message?: string
    allowedIps?: string[]
  }
  analytics: {
    googleAnalyticsId?: string
    facebookPixelId?: string
    hotjarId?: string
  }
  createdAt: Date
  updatedAt: Date
}

export interface UpdateSiteSettingsRequest {
  homepageId?: string
  siteName?: string
  siteDescription?: string
  siteUrl?: string
  logoUrl?: string
  faviconUrl?: string
  socialMedia?: {
    facebook?: string
    instagram?: string
    twitter?: string
    youtube?: string
  }
  seo?: {
    defaultTitle?: string
    defaultDescription?: string
    defaultKeywords?: string[]
    ogImage?: string
  }
  ecommerce?: {
    currency?: string
    taxRate?: number
    freeShippingThreshold?: number
    defaultShippingCost?: number
  }
  maintenance?: {
    enabled?: boolean
    message?: string
    allowedIps?: string[]
  }
  analytics?: {
    googleAnalyticsId?: string
    facebookPixelId?: string
    hotjarId?: string
  }
}

export class SiteSettingsService {
  async getSiteSettings(): Promise<SiteSettings> {
    try {
      let settings = await prisma.siteSettings.findFirst()

      if (!settings) {
        // Create default settings if none exist
        settings = await this.createDefaultSettings()
      }

      // Get homepage slug if homepage is set
      let homepageSlug: string | undefined
      if (settings.homepageId) {
        const homepage = await prisma.page.findUnique({
          where: { id: settings.homepageId },
          select: { slug: true }
        })
        homepageSlug = homepage?.slug
      }

      return {
        id: settings.id,
        homepageId: settings.homepageId,
        homepageSlug,
        siteName: settings.siteName,
        siteDescription: settings.siteDescription,
        siteUrl: settings.siteUrl,
        logoUrl: settings.logoUrl,
        faviconUrl: settings.faviconUrl,
        socialMedia: settings.socialMedia as any || {},
        seo: settings.seo as any || {
          defaultTitle: settings.siteName,
          defaultDescription: settings.siteDescription,
          defaultKeywords: []
        },
        ecommerce: settings.ecommerce as any || {
          currency: 'ZAR',
          taxRate: 0.15,
          defaultShippingCost: 99
        },
        maintenance: settings.maintenance as any || {
          enabled: false
        },
        analytics: settings.analytics as any || {},
        createdAt: settings.createdAt,
        updatedAt: settings.updatedAt
      }
    } catch (error) {
      console.error('Get site settings error:', error)
      throw new Error('Failed to get site settings')
    }
  }

  async updateSiteSettings(data: UpdateSiteSettingsRequest): Promise<SiteSettings> {
    try {
      let settings = await prisma.siteSettings.findFirst()

      if (!settings) {
        settings = await this.createDefaultSettings()
      }

      // Validate homepage if provided
      if (data.homepageId) {
        const page = await prisma.page.findUnique({
          where: { id: data.homepageId, status: 'published' }
        })

        if (!page) {
          throw new Error('Invalid homepage: Page not found or not published')
        }
      }

      const updatedSettings = await prisma.siteSettings.update({
        where: { id: settings.id },
        data: {
          ...(data.homepageId !== undefined && { homepageId: data.homepageId }),
          ...(data.siteName && { siteName: data.siteName }),
          ...(data.siteDescription && { siteDescription: data.siteDescription }),
          ...(data.siteUrl && { siteUrl: data.siteUrl }),
          ...(data.logoUrl !== undefined && { logoUrl: data.logoUrl }),
          ...(data.faviconUrl !== undefined && { faviconUrl: data.faviconUrl }),
          ...(data.socialMedia && { socialMedia: data.socialMedia }),
          ...(data.seo && { seo: data.seo }),
          ...(data.ecommerce && { ecommerce: data.ecommerce }),
          ...(data.maintenance && { maintenance: data.maintenance }),
          ...(data.analytics && { analytics: data.analytics }),
          updatedAt: new Date()
        }
      })

      return this.getSiteSettings()
    } catch (error) {
      console.error('Update site settings error:', error)
      throw new Error('Failed to update site settings')
    }
  }

  async setHomepage(pageId: string): Promise<SiteSettings> {
    try {
      // Validate that the page exists and is published
      const page = await prisma.page.findUnique({
        where: { id: pageId }
      })

      if (!page) {
        throw new Error('Page not found')
      }

      if (page.status !== 'published') {
        throw new Error('Only published pages can be set as homepage')
      }

      return this.updateSiteSettings({ homepageId: pageId })
    } catch (error) {
      console.error('Set homepage error:', error)
      throw new Error('Failed to set homepage')
    }
  }

  async clearHomepage(): Promise<SiteSettings> {
    try {
      return this.updateSiteSettings({ homepageId: null })
    } catch (error) {
      console.error('Clear homepage error:', error)
      throw new Error('Failed to clear homepage')
    }
  }

  async getHomepage(): Promise<{ pageId: string; slug: string } | null> {
    try {
      const settings = await this.getSiteSettings()
      
      if (!settings.homepageId) {
        return null
      }

      const page = await prisma.page.findUnique({
        where: { 
          id: settings.homepageId,
          status: 'published'
        },
        select: { id: true, slug: true }
      })

      if (!page) {
        // Homepage was deleted or unpublished, clear it
        await this.clearHomepage()
        return null
      }

      return {
        pageId: page.id,
        slug: page.slug
      }
    } catch (error) {
      console.error('Get homepage error:', error)
      return null
    }
  }

  async getAvailablePages(): Promise<Array<{ id: string; title: string; slug: string; type: string }>> {
    try {
      const pages = await prisma.page.findMany({
        where: { status: 'published' },
        select: {
          id: true,
          title: true,
          slug: true,
          type: true
        },
        orderBy: { title: 'asc' }
      })

      return pages
    } catch (error) {
      console.error('Get available pages error:', error)
      return []
    }
  }

  private async createDefaultSettings() {
    return prisma.siteSettings.create({
      data: {
        siteName: 'Coco Milk Kids',
        siteDescription: 'Premium children\'s clothing and accessories',
        siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://cocomilkkids.co.za',
        socialMedia: {},
        seo: {
          defaultTitle: 'Coco Milk Kids - Premium Children\'s Clothing',
          defaultDescription: 'Discover our premium collection of children\'s clothing designed for comfort, style, and adventure.',
          defaultKeywords: ['kids clothing', 'children fashion', 'premium kids wear', 'south africa']
        },
        ecommerce: {
          currency: 'ZAR',
          taxRate: 0.15,
          defaultShippingCost: 99
        },
        maintenance: {
          enabled: false
        },
        analytics: {}
      }
    })
  }
}
