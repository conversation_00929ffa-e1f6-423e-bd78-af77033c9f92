# Dynamic Homepage Selection System

This system allows administrators to choose any page builder-generated page as the website's homepage, with middleware handling the routing automatically.

## 🏗️ Architecture

### Components
```
lib/site-settings/
├── site-settings-service.ts     # Core service for managing site settings
└── README.md                    # This documentation

app/api/
├── admin/site-settings/         # Admin API routes
│   ├── route.ts                 # General site settings CRUD
│   └── homepage/route.ts        # Homepage-specific operations
└── site-settings/homepage/      # Public API for homepage info
    └── route.ts

app/
├── admin/site-settings/         # Admin interface
│   └── page.tsx                 # Site settings management UI
├── dynamic-page/[slug]/         # Dynamic page renderer
│   └── page.tsx                 # Renders any page builder page
└── maintenance/                 # Maintenance mode page
    └── page.tsx

middleware.ts                    # Handles routing logic
hooks/use-site-settings.ts       # React hooks for settings management
```

## 🚀 How It Works

### 1. Homepage Selection
Administrators can choose any published page as the homepage through the admin interface:

```typescript
// Set a page as homepage
await siteSettingsService.setHomepage(pageId)

// Clear homepage (use default route)
await siteSettingsService.clearHomepage()
```

### 2. Middleware Routing
The middleware intercepts requests and handles routing:

```typescript
// Root path (/) routing logic
if (pathname === '/') {
  const settings = await prisma.siteSettings.findFirst()
  
  if (settings?.homepageId) {
    const homepage = await prisma.page.findUnique({
      where: { id: settings.homepageId, status: 'published' }
    })
    
    if (homepage) {
      // Rewrite to dynamic page route
      return NextResponse.rewrite(`/dynamic-page/${homepage.slug}`)
    }
  }
  
  // Continue to default route
  return NextResponse.next()
}
```

### 3. Dynamic Page Rendering
The dynamic page route renders any page builder page:

```typescript
// app/dynamic-page/[slug]/page.tsx
export default async function DynamicPage({ params }) {
  const page = await prisma.page.findUnique({
    where: { slug: params.slug, status: 'published' }
  })
  
  return <PageRenderer page={page} />
}
```

## 📋 Features

### Homepage Management
- **Choose Any Page**: Select any published page as homepage
- **Real-time Updates**: Changes take effect immediately
- **Fallback Handling**: Graceful fallback to default route
- **Validation**: Only published pages can be set as homepage

### Site Settings
- **General Settings**: Site name, description, URLs, branding
- **SEO Configuration**: Meta tags, Open Graph, Twitter cards
- **E-commerce Settings**: Currency, tax rates, shipping
- **Maintenance Mode**: Site-wide maintenance with IP allowlisting
- **Analytics Integration**: Google Analytics, Facebook Pixel, Hotjar

### Maintenance Mode
- **Site-wide Control**: Enable/disable maintenance mode
- **Custom Messages**: Configurable maintenance messages
- **IP Allowlisting**: Allow specific IPs during maintenance
- **Admin Access**: Admins can always access the site

## 🔧 Usage

### Admin Interface

#### Setting Homepage
```typescript
import { useSiteSettings } from '@/hooks/use-site-settings'

function HomepageSettings() {
  const { setHomepage, availablePages } = useSiteSettings()
  
  const handleSetHomepage = async (pageId: string) => {
    const result = await setHomepage(pageId)
    if (result.success) {
      toast.success('Homepage set successfully')
    }
  }
  
  return (
    <Select onValueChange={handleSetHomepage}>
      {availablePages.map(page => (
        <SelectItem key={page.id} value={page.id}>
          {page.title}
        </SelectItem>
      ))}
    </Select>
  )
}
```

#### Managing Site Settings
```typescript
function SiteSettings() {
  const { settings, updateSettings } = useSiteSettings()
  
  const handleUpdate = async (newSettings) => {
    const result = await updateSettings(newSettings)
    if (result.success) {
      toast.success('Settings updated')
    }
  }
  
  return (
    <form onSubmit={handleUpdate}>
      <Input 
        value={settings?.siteName} 
        onChange={(e) => setSiteName(e.target.value)}
      />
      <Button type="submit">Save</Button>
    </form>
  )
}
```

### API Usage

#### Get Current Homepage
```typescript
// GET /api/site-settings/homepage
const response = await fetch('/api/site-settings/homepage')
const { homepage, maintenance } = await response.json()
```

#### Set Homepage (Admin)
```typescript
// POST /api/admin/site-settings/homepage
const response = await fetch('/api/admin/site-settings/homepage', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ pageId: 'page-id' })
})
```

#### Update Site Settings (Admin)
```typescript
// PATCH /api/admin/site-settings
const response = await fetch('/api/admin/site-settings', {
  method: 'PATCH',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    siteName: 'New Site Name',
    maintenance: { enabled: true, message: 'Under maintenance' }
  })
})
```

## 🗄️ Database Schema

```sql
model SiteSettings {
  id              String   @id @default(cuid())
  homepageId      String?  -- Reference to Page model
  siteName        String
  siteDescription String
  siteUrl         String
  logoUrl         String?
  faviconUrl      String?
  socialMedia     Json     @default("{}")
  seo             Json     @default("{}")
  ecommerce       Json     @default("{}")
  maintenance     Json     @default("{}")
  analytics       Json     @default("{}")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}
```

## 🔒 Security

### Admin Authentication
All admin endpoints should include authentication checks:

```typescript
// TODO: Implement admin authentication
const isAdmin = await checkAdminAuth(request)
if (!isAdmin) {
  return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
}
```

### Maintenance Mode Security
- IP allowlisting for maintenance mode
- Admin routes always accessible
- Secure IP detection from headers

### Input Validation
All inputs are validated using Zod schemas:

```typescript
const updateSiteSettingsSchema = z.object({
  siteName: z.string().optional(),
  siteUrl: z.string().url().optional(),
  maintenance: z.object({
    enabled: z.boolean().optional(),
    allowedIps: z.array(z.string()).optional()
  }).optional()
})
```

## 🚀 Performance

### Middleware Optimization
- Direct database queries for minimal latency
- Graceful error handling with fallbacks
- Efficient routing patterns

### Caching Strategy
- Static generation for published pages
- Cache headers for dynamic content
- Revalidation on settings changes

### SEO Optimization
- Dynamic metadata generation
- Open Graph and Twitter card support
- Structured data for search engines

## 🔄 Workflow

### Setting a Homepage
1. Admin selects a published page
2. API validates page exists and is published
3. Database is updated with new homepage ID
4. Middleware immediately starts routing to new homepage
5. Users see the new homepage on next visit

### Maintenance Mode
1. Admin enables maintenance mode
2. Optional: Set custom message and allowed IPs
3. Middleware checks maintenance status
4. Non-admin users see maintenance page
5. Allowed IPs and admin routes remain accessible

### Page Updates
1. Page content is updated in page builder
2. If page is set as homepage, changes are immediately visible
3. If page is unpublished, homepage automatically falls back to default

## 🛠️ Development

### Adding New Settings
1. Update the `SiteSettings` interface
2. Add validation to Zod schema
3. Update the admin interface
4. Add database migration if needed

### Custom Homepage Logic
The system is extensible for custom homepage logic:

```typescript
// Custom homepage selection logic
const getCustomHomepage = async (userContext) => {
  // Implement custom logic (A/B testing, personalization, etc.)
  return selectedPageId
}
```

This dynamic homepage system provides a flexible, secure, and performant way to manage website homepages while maintaining the benefits of the page builder system.
