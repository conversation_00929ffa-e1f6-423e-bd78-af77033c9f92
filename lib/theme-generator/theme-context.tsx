'use client'

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { ThemeConfig, ThemeMode, ThemeContextValue, ThemeGeneratorOptions, ThemeApplication } from './types'
import { ThemeGenerator } from './theme-generator'

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined)

export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

interface ThemeProviderProps {
  children: React.ReactNode
  defaultTheme?: string
  defaultMode?: ThemeMode
  storageKey?: string
}

export function ThemeProvider({
  children,
  defaultTheme = 'default',
  defaultMode = 'light',
  storageKey = 'coco-milk-theme'
}: ThemeProviderProps) {
  const [currentTheme, setCurrentTheme] = useState<ThemeConfig | null>(null)
  const [themes, setThemes] = useState<ThemeConfig[]>([])
  const [mode, setMode] = useState<ThemeMode>(defaultMode)
  const [isLoading, setIsLoading] = useState(true)

  // Load themes and current theme on mount
  useEffect(() => {
    loadThemes()
    loadCurrentTheme()
    loadMode()
  }, [])

  // Apply theme changes to document
  useEffect(() => {
    if (currentTheme) {
      applyThemeToDocument(currentTheme, mode)
    }
  }, [currentTheme, mode])

  const loadThemes = async () => {
    try {
      const response = await fetch('/api/theme-generator/themes')
      if (response.ok) {
        const result = await response.json()
        setThemes(result.data || [])
      }
    } catch (error) {
      console.error('Error loading themes:', error)
      // Load default themes if API fails
      setThemes(getDefaultThemes())
    }
  }

  const loadCurrentTheme = async () => {
    try {
      // Try to load from localStorage first
      const stored = localStorage.getItem(storageKey)
      if (stored) {
        const themeId = JSON.parse(stored).themeId
        const response = await fetch(`/api/theme-generator/themes/${themeId}`)
        if (response.ok) {
          const result = await response.json()
          setCurrentTheme(result.data)
          setIsLoading(false)
          return
        }
      }

      // Fallback to default theme
      const defaultThemeConfig = getDefaultThemes().find(t => t.id === defaultTheme)
      if (defaultThemeConfig) {
        setCurrentTheme(defaultThemeConfig)
      }
    } catch (error) {
      console.error('Error loading current theme:', error)
      const defaultThemeConfig = getDefaultThemes().find(t => t.id === defaultTheme)
      if (defaultThemeConfig) {
        setCurrentTheme(defaultThemeConfig)
      }
    } finally {
      setIsLoading(false)
    }
  }

  const loadMode = () => {
    try {
      const stored = localStorage.getItem(storageKey)
      if (stored) {
        const { mode: storedMode } = JSON.parse(stored)
        if (storedMode) {
          setMode(storedMode)
          return
        }
      }

      // Check system preference
      if (typeof window !== 'undefined' && window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
        setMode(mediaQuery.matches ? 'dark' : 'light')
        
        // Listen for system theme changes
        const handleChange = (e: MediaQueryListEvent) => {
          if (mode === 'auto') {
            setMode(e.matches ? 'dark' : 'light')
          }
        }
        mediaQuery.addEventListener('change', handleChange)
        return () => mediaQuery.removeEventListener('change', handleChange)
      }
    } catch (error) {
      console.error('Error loading theme mode:', error)
    }
  }

  const setTheme = useCallback(async (themeId: string) => {
    try {
      setIsLoading(true)
      
      // Find theme in loaded themes first
      let theme = themes.find(t => t.id === themeId)
      
      // If not found, try to load from API
      if (!theme) {
        const response = await fetch(`/api/theme-generator/themes/${themeId}`)
        if (response.ok) {
          const result = await response.json()
          theme = result.data
        }
      }

      if (theme) {
        setCurrentTheme(theme)
        
        // Save to localStorage
        const stored = localStorage.getItem(storageKey)
        const currentData = stored ? JSON.parse(stored) : {}
        localStorage.setItem(storageKey, JSON.stringify({
          ...currentData,
          themeId: theme.id
        }))
      }
    } catch (error) {
      console.error('Error setting theme:', error)
    } finally {
      setIsLoading(false)
    }
  }, [themes, storageKey])

  const setThemeMode = useCallback((newMode: ThemeMode) => {
    setMode(newMode)
    
    // Save to localStorage
    const stored = localStorage.getItem(storageKey)
    const currentData = stored ? JSON.parse(stored) : {}
    localStorage.setItem(storageKey, JSON.stringify({
      ...currentData,
      mode: newMode
    }))
  }, [storageKey])

  const generateTheme = useCallback(async (options: ThemeGeneratorOptions): Promise<ThemeConfig> => {
    try {
      setIsLoading(true)
      
      // Generate theme locally first
      const generatedTheme = ThemeGenerator.generateTheme(options)
      
      // Save to API
      const response = await fetch('/api/theme-generator/themes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(generatedTheme)
      })

      if (response.ok) {
        const result = await response.json()
        const savedTheme = result.data
        
        // Update themes list
        setThemes(prev => [...prev, savedTheme])
        
        return savedTheme
      }

      return generatedTheme
    } catch (error) {
      console.error('Error generating theme:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [])

  const saveTheme = useCallback(async (theme: ThemeConfig) => {
    try {
      const response = await fetch('/api/theme-generator/themes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(theme)
      })

      if (response.ok) {
        const result = await response.json()
        const savedTheme = result.data
        
        // Update themes list
        setThemes(prev => {
          const existing = prev.findIndex(t => t.id === savedTheme.id)
          if (existing >= 0) {
            const updated = [...prev]
            updated[existing] = savedTheme
            return updated
          }
          return [...prev, savedTheme]
        })
      }
    } catch (error) {
      console.error('Error saving theme:', error)
      throw error
    }
  }, [])

  const deleteTheme = useCallback(async (themeId: string) => {
    try {
      const response = await fetch(`/api/theme-generator/themes/${themeId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setThemes(prev => prev.filter(t => t.id !== themeId))
        
        // If deleted theme is current, switch to default
        if (currentTheme?.id === themeId) {
          const defaultThemeConfig = themes.find(t => t.metadata.isDefault)
          if (defaultThemeConfig) {
            setCurrentTheme(defaultThemeConfig)
          }
        }
      }
    } catch (error) {
      console.error('Error deleting theme:', error)
      throw error
    }
  }, [currentTheme, themes])

  const applyTheme = useCallback(async (scope: ThemeApplication['scope'], targetId?: string) => {
    if (!currentTheme) return

    try {
      const application: Omit<ThemeApplication, 'appliedAt' | 'appliedBy'> = {
        scope,
        targetId,
        themeId: currentTheme.id,
        isActive: true
      }

      const response = await fetch('/api/theme-generator/applications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(application)
      })

      if (!response.ok) {
        throw new Error('Failed to apply theme')
      }
    } catch (error) {
      console.error('Error applying theme:', error)
      throw error
    }
  }, [currentTheme])

  const removeTheme = useCallback(async (scope: ThemeApplication['scope'], targetId?: string) => {
    try {
      const params = new URLSearchParams({ scope })
      if (targetId) params.set('targetId', targetId)

      const response = await fetch(`/api/theme-generator/applications?${params}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to remove theme')
      }
    } catch (error) {
      console.error('Error removing theme:', error)
      throw error
    }
  }, [])

  const getCSSVariables = useCallback((theme?: ThemeConfig): Record<string, string> => {
    const activeTheme = theme || currentTheme
    if (!activeTheme) return {}

    const variables: Record<string, string> = {}
    const colors = mode === 'dark' && activeTheme.darkMode.enabled 
      ? { ...activeTheme.colors, ...activeTheme.darkMode.colors }
      : activeTheme.colors

    // Convert colors to CSS variables
    Object.entries(colors).forEach(([colorName, colorScale]) => {
      if (typeof colorScale === 'object') {
        Object.entries(colorScale).forEach(([shade, value]) => {
          variables[`--color-${colorName}-${shade}`] = value
        })
      }
    })

    // Add other theme variables
    variables['--font-sans'] = activeTheme.typography.fontFamily.sans.join(', ')
    variables['--font-serif'] = activeTheme.typography.fontFamily.serif.join(', ')
    variables['--font-mono'] = activeTheme.typography.fontFamily.mono.join(', ')

    return variables
  }, [currentTheme, mode])

  const getComponentClasses = useCallback((component: string, variant?: string, size?: string): string => {
    if (!currentTheme) return ''

    const componentTheme = currentTheme.components[component as keyof typeof currentTheme.components]
    if (!componentTheme) return ''

    let classes = componentTheme.base

    if (variant && componentTheme.variants.variant) {
      const variantClass = componentTheme.variants.variant[variant]
      if (variantClass) classes += ` ${variantClass}`
    }

    if (size && componentTheme.sizes[size]) {
      classes += ` ${componentTheme.sizes[size]}`
    }

    return classes
  }, [currentTheme])

  const value: ThemeContextValue = {
    currentTheme,
    themes,
    mode,
    isLoading,
    setTheme,
    setMode: setThemeMode,
    generateTheme,
    saveTheme,
    deleteTheme,
    applyTheme,
    removeTheme,
    getCSSVariables,
    getComponentClasses
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}

// Apply theme to document
function applyThemeToDocument(theme: ThemeConfig, mode: ThemeMode) {
  if (typeof document === 'undefined') return

  const root = document.documentElement
  const variables = getCSSVariablesForDocument(theme, mode)

  // Apply CSS variables
  Object.entries(variables).forEach(([key, value]) => {
    root.style.setProperty(key, value)
  })

  // Apply theme class
  root.className = root.className.replace(/theme-\w+/g, '')
  root.classList.add(`theme-${theme.id}`)

  // Apply mode class
  root.className = root.className.replace(/\b(light|dark)\b/g, '')
  root.classList.add(mode)
}

function getCSSVariablesForDocument(theme: ThemeConfig, mode: ThemeMode): Record<string, string> {
  const variables: Record<string, string> = {}
  const colors = mode === 'dark' && theme.darkMode.enabled 
    ? { ...theme.colors, ...theme.darkMode.colors }
    : theme.colors

  // Convert colors to CSS variables
  Object.entries(colors).forEach(([colorName, colorScale]) => {
    if (typeof colorScale === 'object') {
      Object.entries(colorScale).forEach(([shade, value]) => {
        variables[`--color-${colorName}-${shade}`] = value
      })
    }
  })

  return variables
}

// Default themes
function getDefaultThemes(): ThemeConfig[] {
  return [
    ThemeGenerator.generateTheme({
      baseColor: '#3b82f6',
      style: 'modern',
      contrast: 'medium',
      saturation: 'normal',
      borderRadius: 'rounded',
      fontPairing: 'modern',
      spacing: 'normal'
    })
  ]
}
