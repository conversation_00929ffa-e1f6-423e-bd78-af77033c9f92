'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { useTheme } from '../theme-context'
import { ThemeConfig, ThemeGeneratorOptions } from '../types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import {
  Palette,
  Type,
  Layout,
  Spacing,
  BorderAll,
  Shadow,
  <PERSON>,
  <PERSON>Off,
  Refresh<PERSON>w,
  Save,
  Download,
  Upload,
  Copy,
  Check,
  Wand2,
  Sparkles,
  Monitor,
  Smartphone,
  Tablet,
  Sun,
  Moon,
  Contrast,
  Droplets,
  Zap
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

interface EnhancedThemeCustomizerProps {
  className?: string
  theme?: ThemeConfig
  onThemeChange?: (theme: ThemeConfig) => void
  showPreview?: boolean
}

export function EnhancedThemeCustomizer({ 
  className, 
  theme: initialTheme, 
  onThemeChange,
  showPreview = true 
}: EnhancedThemeCustomizerProps) {
  const { currentTheme, generateTheme, saveTheme, setTheme } = useTheme()
  const [workingTheme, setWorkingTheme] = useState<ThemeConfig | null>(initialTheme || currentTheme)
  const [activeTab, setActiveTab] = useState<'colors' | 'typography' | 'spacing' | 'effects' | 'preview'>('colors')
  const [previewMode, setPreviewMode] = useState<'light' | 'dark'>('light')
  const [previewDevice, setPreviewDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [isGenerating, setIsGenerating] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  // Color customization state
  const [primaryColor, setPrimaryColor] = useState('#3b82f6')
  const [secondaryColor, setSecondaryColor] = useState('#6b7280')
  const [accentColor, setAccentColor] = useState('#8b5cf6')
  const [backgroundColor, setBackgroundColor] = useState('#ffffff')
  const [textColor, setTextColor] = useState('#1f2937')

  // Typography state
  const [fontPairing, setFontPairing] = useState<ThemeGeneratorOptions['fontPairing']>('modern')
  const [fontSize, setFontSize] = useState([16])
  const [lineHeight, setLineHeight] = useState([1.5])
  const [letterSpacing, setLetterSpacing] = useState([0])

  // Spacing and layout state
  const [spacing, setSpacing] = useState<ThemeGeneratorOptions['spacing']>('normal')
  const [borderRadius, setBorderRadius] = useState<ThemeGeneratorOptions['borderRadius']>('rounded')
  const [containerWidth, setContainerWidth] = useState([1200])

  // Effects state
  const [shadowIntensity, setShadowIntensity] = useState([50])
  const [animationSpeed, setAnimationSpeed] = useState([300])
  const [contrast, setContrast] = useState<ThemeGeneratorOptions['contrast']>('medium')
  const [saturation, setSaturation] = useState<ThemeGeneratorOptions['saturation']>('normal')

  // Update working theme when props change
  useEffect(() => {
    if (initialTheme) {
      setWorkingTheme(initialTheme)
      updateStateFromTheme(initialTheme)
    } else if (currentTheme) {
      setWorkingTheme(currentTheme)
      updateStateFromTheme(currentTheme)
    }
  }, [initialTheme?.id, currentTheme?.id]) // Use IDs instead of entire objects to prevent infinite loops

  // Update state from theme
  const updateStateFromTheme = (theme: ThemeConfig) => {
    if (theme.colors?.primary?.DEFAULT) {
      setPrimaryColor(theme.colors.primary.DEFAULT)
    }
    if (theme.colors?.secondary?.DEFAULT) {
      setSecondaryColor(theme.colors.secondary.DEFAULT)
    }
    if (theme.colors?.background) {
      setBackgroundColor(theme.colors.background)
    }
    if (theme.colors?.foreground) {
      setTextColor(theme.colors.foreground)
    }
  }

  // Generate theme from current settings
  const generateThemeFromSettings = async () => {
    setIsGenerating(true)
    try {
      const options: ThemeGeneratorOptions = {
        baseColor: primaryColor,
        style: 'modern',
        contrast,
        saturation,
        borderRadius,
        fontPairing,
        spacing
      }

      const newTheme = await generateTheme(options)
      
      // Apply custom overrides
      const customizedTheme: ThemeConfig = {
        ...newTheme,
        colors: {
          ...newTheme.colors,
          primary: {
            DEFAULT: primaryColor,
            foreground: getContrastColor(primaryColor)
          },
          secondary: {
            DEFAULT: secondaryColor,
            foreground: getContrastColor(secondaryColor)
          },
          accent: {
            DEFAULT: accentColor,
            foreground: getContrastColor(accentColor)
          },
          background: backgroundColor,
          foreground: textColor
        },
        typography: {
          ...newTheme.typography,
          fontSize: {
            ...newTheme.typography.fontSize,
            base: `${fontSize[0]}px`
          },
          lineHeight: {
            ...newTheme.typography.lineHeight,
            normal: lineHeight[0].toString()
          },
          letterSpacing: {
            ...newTheme.typography.letterSpacing,
            normal: `${letterSpacing[0]}em`
          }
        }
      }

      setWorkingTheme(customizedTheme)
      setHasChanges(true)
      
      if (onThemeChange) {
        onThemeChange(customizedTheme)
      }
    } catch (error) {
      console.error('Theme generation error:', error)
      toast.error('Failed to generate theme')
    } finally {
      setIsGenerating(false)
    }
  }

  // Get contrast color for text
  const getContrastColor = (hexColor: string): string => {
    const r = parseInt(hexColor.slice(1, 3), 16)
    const g = parseInt(hexColor.slice(3, 5), 16)
    const b = parseInt(hexColor.slice(5, 7), 16)
    const brightness = (r * 299 + g * 587 + b * 114) / 1000
    return brightness > 128 ? '#000000' : '#ffffff'
  }

  // Save theme
  const handleSaveTheme = async () => {
    if (!workingTheme) return
    
    try {
      await saveTheme(workingTheme)
      setHasChanges(false)
      toast.success('Theme saved successfully!')
    } catch (error) {
      console.error('Save theme error:', error)
      toast.error('Failed to save theme')
    }
  }

  // Apply theme
  const handleApplyTheme = async () => {
    if (!workingTheme) return
    
    try {
      await setTheme(workingTheme.id)
      toast.success('Theme applied successfully!')
    } catch (error) {
      console.error('Apply theme error:', error)
      toast.error('Failed to apply theme')
    }
  }

  // Reset to original
  const handleReset = () => {
    if (initialTheme || currentTheme) {
      const resetTheme = initialTheme || currentTheme!
      setWorkingTheme(resetTheme)
      updateStateFromTheme(resetTheme)
      setHasChanges(false)
      
      if (onThemeChange) {
        onThemeChange(resetTheme)
      }
    }
  }

  // Auto-generate on setting changes (with debounce to prevent infinite loops)
  useEffect(() => {
    // Skip auto-generation if we're currently generating or if this is the initial load
    if (isGenerating || !workingTheme) return

    const debounceTimer = setTimeout(() => {
      generateThemeFromSettings()
    }, 500)

    return () => clearTimeout(debounceTimer)
  }, [primaryColor, secondaryColor, accentColor, backgroundColor, textColor, contrast, saturation, borderRadius, fontPairing, spacing, isGenerating, workingTheme])

  const previewStyles = useMemo(() => {
    if (!workingTheme) return {}
    
    return {
      '--primary': workingTheme.colors?.primary?.DEFAULT || primaryColor,
      '--secondary': workingTheme.colors?.secondary?.DEFAULT || secondaryColor,
      '--accent': workingTheme.colors?.accent?.DEFAULT || accentColor,
      '--background': workingTheme.colors?.background || backgroundColor,
      '--foreground': workingTheme.colors?.foreground || textColor,
      '--radius': workingTheme.borderRadius?.DEFAULT || '0.5rem',
      fontSize: `${fontSize[0]}px`,
      lineHeight: lineHeight[0],
      letterSpacing: `${letterSpacing[0]}em`
    } as React.CSSProperties
  }, [workingTheme, primaryColor, secondaryColor, accentColor, backgroundColor, textColor, fontSize, lineHeight, letterSpacing])

  if (!workingTheme) {
    return (
      <Card className={cn('h-full flex items-center justify-center', className)}>
        <CardContent className="text-center">
          <Palette className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="font-medium text-gray-900 mb-2">No Theme Selected</h3>
          <p className="text-sm text-muted-foreground">
            Select a theme to start customizing
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn('h-full flex flex-col', className)}>
      {/* Header */}
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Palette className="h-5 w-5 text-purple-600" />
                Theme Customizer
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Customize {workingTheme.name} with real-time preview
              </p>
            </div>
            <div className="flex items-center gap-2">
              {hasChanges && (
                <Badge variant="secondary" className="text-xs">
                  Unsaved Changes
                </Badge>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
                disabled={!hasChanges}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSaveTheme}
                disabled={!hasChanges}
              >
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
              <Button
                size="sm"
                onClick={handleApplyTheme}
              >
                <Check className="h-4 w-4 mr-2" />
                Apply
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="flex-1 flex gap-4">
        {/* Customization Panel */}
        <Card className="w-80 flex flex-col">
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Customization</CardTitle>
          </CardHeader>
          <CardContent className="flex-1 p-0">
            <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-5 mx-4">
                <TabsTrigger value="colors" className="text-xs">
                  <Palette className="h-3 w-3" />
                </TabsTrigger>
                <TabsTrigger value="typography" className="text-xs">
                  <Type className="h-3 w-3" />
                </TabsTrigger>
                <TabsTrigger value="spacing" className="text-xs">
                  <Layout className="h-3 w-3" />
                </TabsTrigger>
                <TabsTrigger value="effects" className="text-xs">
                  <Sparkles className="h-3 w-3" />
                </TabsTrigger>
                <TabsTrigger value="preview" className="text-xs">
                  <Eye className="h-3 w-3" />
                </TabsTrigger>
              </TabsList>

              <ScrollArea className="flex-1 px-4">
                {/* Colors Tab */}
                <TabsContent value="colors" className="mt-4 space-y-4">
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="primary-color" className="text-sm font-medium">Primary Color</Label>
                      <div className="flex gap-2 mt-1">
                        <Input
                          id="primary-color"
                          type="color"
                          value={primaryColor}
                          onChange={(e) => setPrimaryColor(e.target.value)}
                          className="w-12 h-8 p-1 border rounded"
                        />
                        <Input
                          value={primaryColor}
                          onChange={(e) => setPrimaryColor(e.target.value)}
                          className="flex-1"
                          placeholder="#3b82f6"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="secondary-color" className="text-sm font-medium">Secondary Color</Label>
                      <div className="flex gap-2 mt-1">
                        <Input
                          id="secondary-color"
                          type="color"
                          value={secondaryColor}
                          onChange={(e) => setSecondaryColor(e.target.value)}
                          className="w-12 h-8 p-1 border rounded"
                        />
                        <Input
                          value={secondaryColor}
                          onChange={(e) => setSecondaryColor(e.target.value)}
                          className="flex-1"
                          placeholder="#6b7280"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="accent-color" className="text-sm font-medium">Accent Color</Label>
                      <div className="flex gap-2 mt-1">
                        <Input
                          id="accent-color"
                          type="color"
                          value={accentColor}
                          onChange={(e) => setAccentColor(e.target.value)}
                          className="w-12 h-8 p-1 border rounded"
                        />
                        <Input
                          value={accentColor}
                          onChange={(e) => setAccentColor(e.target.value)}
                          className="flex-1"
                          placeholder="#8b5cf6"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="background-color" className="text-sm font-medium">Background</Label>
                      <div className="flex gap-2 mt-1">
                        <Input
                          id="background-color"
                          type="color"
                          value={backgroundColor}
                          onChange={(e) => setBackgroundColor(e.target.value)}
                          className="w-12 h-8 p-1 border rounded"
                        />
                        <Input
                          value={backgroundColor}
                          onChange={(e) => setBackgroundColor(e.target.value)}
                          className="flex-1"
                          placeholder="#ffffff"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="text-color" className="text-sm font-medium">Text Color</Label>
                      <div className="flex gap-2 mt-1">
                        <Input
                          id="text-color"
                          type="color"
                          value={textColor}
                          onChange={(e) => setTextColor(e.target.value)}
                          className="w-12 h-8 p-1 border rounded"
                        />
                        <Input
                          value={textColor}
                          onChange={(e) => setTextColor(e.target.value)}
                          className="flex-1"
                          placeholder="#1f2937"
                        />
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <Label className="text-sm font-medium">Contrast</Label>
                      <Select value={contrast} onValueChange={(value: any) => setContrast(value)}>
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">Low Contrast</SelectItem>
                          <SelectItem value="medium">Medium Contrast</SelectItem>
                          <SelectItem value="high">High Contrast</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Saturation</Label>
                      <Select value={saturation} onValueChange={(value: any) => setSaturation(value)}>
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="muted">Muted</SelectItem>
                          <SelectItem value="normal">Normal</SelectItem>
                          <SelectItem value="vibrant">Vibrant</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </TabsContent>

                {/* Typography Tab */}
                <TabsContent value="typography" className="mt-4 space-y-4">
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium">Font Pairing</Label>
                      <Select value={fontPairing} onValueChange={(value: any) => setFontPairing(value)}>
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="classic">Classic</SelectItem>
                          <SelectItem value="modern">Modern</SelectItem>
                          <SelectItem value="creative">Creative</SelectItem>
                          <SelectItem value="technical">Technical</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Font Size: {fontSize[0]}px</Label>
                      <Slider
                        value={fontSize}
                        onValueChange={setFontSize}
                        min={12}
                        max={24}
                        step={1}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Line Height: {lineHeight[0]}</Label>
                      <Slider
                        value={lineHeight}
                        onValueChange={setLineHeight}
                        min={1.2}
                        max={2.0}
                        step={0.1}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Letter Spacing: {letterSpacing[0]}em</Label>
                      <Slider
                        value={letterSpacing}
                        onValueChange={setLetterSpacing}
                        min={-0.05}
                        max={0.1}
                        step={0.005}
                        className="mt-2"
                      />
                    </div>
                  </div>
                </TabsContent>

                {/* Spacing Tab */}
                <TabsContent value="spacing" className="mt-4 space-y-4">
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium">Spacing Scale</Label>
                      <Select value={spacing} onValueChange={(value: any) => setSpacing(value)}>
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="compact">Compact</SelectItem>
                          <SelectItem value="normal">Normal</SelectItem>
                          <SelectItem value="spacious">Spacious</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Border Radius</Label>
                      <Select value={borderRadius} onValueChange={(value: any) => setBorderRadius(value)}>
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="sharp">Sharp (0px)</SelectItem>
                          <SelectItem value="rounded">Rounded (8px)</SelectItem>
                          <SelectItem value="pill">Pill (999px)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Container Width: {containerWidth[0]}px</Label>
                      <Slider
                        value={containerWidth}
                        onValueChange={setContainerWidth}
                        min={800}
                        max={1600}
                        step={50}
                        className="mt-2"
                      />
                    </div>
                  </div>
                </TabsContent>

                {/* Effects Tab */}
                <TabsContent value="effects" className="mt-4 space-y-4">
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium">Shadow Intensity: {shadowIntensity[0]}%</Label>
                      <Slider
                        value={shadowIntensity}
                        onValueChange={setShadowIntensity}
                        min={0}
                        max={100}
                        step={5}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Animation Speed: {animationSpeed[0]}ms</Label>
                      <Slider
                        value={animationSpeed}
                        onValueChange={setAnimationSpeed}
                        min={100}
                        max={1000}
                        step={50}
                        className="mt-2"
                      />
                    </div>

                    <Separator />

                    <div className="space-y-3">
                      <h4 className="text-sm font-medium">Quick Effects</h4>

                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-start"
                        onClick={() => {
                          setShadowIntensity([75])
                          setAnimationSpeed([200])
                          toast.success('Applied modern effects')
                        }}
                      >
                        <Zap className="h-4 w-4 mr-2" />
                        Modern Effects
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-start"
                        onClick={() => {
                          setShadowIntensity([25])
                          setAnimationSpeed([400])
                          toast.success('Applied subtle effects')
                        }}
                      >
                        <Droplets className="h-4 w-4 mr-2" />
                        Subtle Effects
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-start"
                        onClick={() => {
                          setShadowIntensity([0])
                          setAnimationSpeed([0])
                          toast.success('Disabled all effects')
                        }}
                      >
                        <EyeOff className="h-4 w-4 mr-2" />
                        No Effects
                      </Button>
                    </div>
                  </div>
                </TabsContent>

                {/* Preview Tab */}
                <TabsContent value="preview" className="mt-4 space-y-4">
                  <div className="text-center py-6">
                    <Eye className="h-12 w-12 mx-auto mb-3 text-gray-400" />
                    <h3 className="font-medium text-gray-900 mb-2">Theme Preview</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Use the preview panel to see your changes in real-time
                    </p>

                    <div className="space-y-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => {
                          navigator.clipboard.writeText(JSON.stringify(workingTheme, null, 2))
                          toast.success('Theme JSON copied to clipboard!')
                        }}
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Copy Theme JSON
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => {
                          const cssVars = Object.entries(previewStyles)
                            .map(([key, value]) => `${key}: ${value};`)
                            .join('\n')
                          navigator.clipboard.writeText(cssVars)
                          toast.success('CSS variables copied!')
                        }}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Export CSS Variables
                      </Button>
                    </div>
                  </div>
                </TabsContent>
              </ScrollArea>
            </Tabs>
          </CardContent>
        </Card>

        {/* Preview Panel */}
        {showPreview && (
          <Card className="flex-1 flex flex-col">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">Live Preview</CardTitle>
                <div className="flex items-center gap-2">
                  <Button
                    variant={previewMode === 'light' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewMode('light')}
                  >
                    <Sun className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={previewMode === 'dark' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewMode('dark')}
                  >
                    <Moon className="h-4 w-4" />
                  </Button>
                  <Separator orientation="vertical" className="h-6" />
                  <Button
                    variant={previewDevice === 'desktop' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewDevice('desktop')}
                  >
                    <Monitor className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={previewDevice === 'tablet' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewDevice('tablet')}
                  >
                    <Tablet className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={previewDevice === 'mobile' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewDevice('mobile')}
                  >
                    <Smartphone className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="flex-1 p-4">
              <div 
                className={cn(
                  'h-full rounded-lg border-2 border-dashed border-gray-300 p-4 transition-all',
                  previewDevice === 'mobile' && 'max-w-sm mx-auto',
                  previewDevice === 'tablet' && 'max-w-2xl mx-auto',
                  previewMode === 'dark' && 'bg-gray-900'
                )}
                style={previewStyles}
              >
                {/* Theme Preview Content */}
                <div className="space-y-4">
                  <div className="text-center">
                    <h1 className="text-2xl font-bold mb-2" style={{ color: 'var(--foreground)' }}>
                      Theme Preview
                    </h1>
                    <p className="text-muted-foreground">
                      See how your theme looks in real-time
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card style={{ backgroundColor: 'var(--background)', borderColor: 'var(--border)' }}>
                      <CardHeader>
                        <CardTitle style={{ color: 'var(--foreground)' }}>Sample Card</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p style={{ color: 'var(--foreground)' }}>
                          This is how cards will look with your theme.
                        </p>
                        <Button 
                          className="mt-3"
                          style={{ 
                            backgroundColor: 'var(--primary)', 
                            color: getContrastColor(primaryColor)
                          }}
                        >
                          Primary Button
                        </Button>
                      </CardContent>
                    </Card>

                    <Card style={{ backgroundColor: 'var(--background)', borderColor: 'var(--border)' }}>
                      <CardHeader>
                        <CardTitle style={{ color: 'var(--foreground)' }}>Typography</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <h3 className="text-lg font-semibold" style={{ color: 'var(--foreground)' }}>
                          Heading Example
                        </h3>
                        <p style={{ color: 'var(--foreground)' }}>
                          Body text with your custom typography settings.
                        </p>
                        <Badge style={{ backgroundColor: 'var(--accent)', color: getContrastColor(accentColor) }}>
                          Accent Badge
                        </Badge>
                      </CardContent>
                    </Card>
                  </div>

                  {isGenerating && (
                    <div className="text-center py-8">
                      <div className="inline-flex items-center gap-2 text-sm text-muted-foreground">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        Generating theme...
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
