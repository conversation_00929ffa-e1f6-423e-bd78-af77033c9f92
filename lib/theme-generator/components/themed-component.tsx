'use client'

import React, { useMemo } from 'react'
import { cn } from '@/lib/utils'
import { useTheme } from '../theme-context'
import { ThemeConfig } from '../types'

interface ThemedComponentProps {
  children: React.ReactNode
  component: string
  variant?: string
  size?: string
  className?: string
  themeId?: string // Override theme for this component
  blockId?: string // Block-specific theming
  customizations?: Record<string, any>
  style?: React.CSSProperties
}

export function ThemedComponent({
  children,
  component,
  variant,
  size,
  className,
  themeId,
  blockId,
  customizations,
  style,
  ...props
}: ThemedComponentProps) {
  const { currentTheme, getComponentClasses, getCSSVariables } = useTheme()

  // Get the theme to use (override, current, or default)
  const activeTheme = useMemo(() => {
    if (themeId && themeId !== currentTheme?.id) {
      // TODO: Load specific theme if different from current
      return currentTheme
    }
    return currentTheme
  }, [themeId, currentTheme])

  // Generate component classes based on theme
  const themedClasses = useMemo(() => {
    if (!activeTheme) return ''
    
    const baseClasses = getComponentClasses(component, variant, size)
    
    // Apply customizations if provided
    if (customizations) {
      // TODO: Apply customizations to classes
    }
    
    return baseClasses
  }, [activeTheme, component, variant, size, customizations, getComponentClasses])

  // Generate CSS variables for this component
  const cssVariables = useMemo(() => {
    if (!activeTheme) return {}
    
    const variables = getCSSVariables(activeTheme)
    
    // Add component-specific variables
    const componentTheme = activeTheme.components[component as keyof typeof activeTheme.components]
    if (componentTheme?.cssVariables) {
      Object.assign(variables, componentTheme.cssVariables)
    }
    
    // Apply customizations to variables
    if (customizations) {
      Object.entries(customizations).forEach(([key, value]) => {
        if (key.startsWith('--')) {
          variables[key] = value
        }
      })
    }
    
    return variables
  }, [activeTheme, component, customizations, getCSSVariables])

  // Combine all styles
  const combinedStyle = useMemo(() => {
    return {
      ...cssVariables,
      ...style
    }
  }, [cssVariables, style])

  // Combine all classes
  const combinedClasses = useMemo(() => {
    return cn(themedClasses, className)
  }, [themedClasses, className])

  return (
    <div
      className={combinedClasses}
      style={combinedStyle}
      data-theme-component={component}
      data-theme-id={activeTheme?.id}
      data-block-id={blockId}
      {...props}
    >
      {children}
    </div>
  )
}

// Higher-order component for theming existing components
export function withTheme<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName: string
) {
  const ThemedWrappedComponent = React.forwardRef<any, P & ThemedComponentProps>(
    (props, ref) => {
      const { 
        variant, 
        size, 
        className, 
        themeId, 
        blockId, 
        customizations, 
        style,
        ...componentProps 
      } = props

      return (
        <ThemedComponent
          component={componentName}
          variant={variant}
          size={size}
          className={className}
          themeId={themeId}
          blockId={blockId}
          customizations={customizations}
          style={style}
        >
          <WrappedComponent ref={ref} {...(componentProps as P)} />
        </ThemedComponent>
      )
    }
  )

  ThemedWrappedComponent.displayName = `withTheme(${WrappedComponent.displayName || WrappedComponent.name})`
  
  return ThemedWrappedComponent
}

// Hook for getting themed classes directly
export function useThemedClasses(
  component: string,
  variant?: string,
  size?: string,
  customizations?: Record<string, any>
) {
  const { currentTheme, getComponentClasses } = useTheme()

  return useMemo(() => {
    if (!currentTheme) return ''
    
    const baseClasses = getComponentClasses(component, variant, size)
    
    // Apply customizations if provided
    if (customizations) {
      // TODO: Apply customizations to classes
    }
    
    return baseClasses
  }, [currentTheme, component, variant, size, customizations, getComponentClasses])
}

// Hook for getting themed CSS variables
export function useThemedVariables(
  component?: string,
  customizations?: Record<string, any>
) {
  const { currentTheme, getCSSVariables } = useTheme()

  return useMemo(() => {
    if (!currentTheme) return {}
    
    const variables = getCSSVariables(currentTheme)
    
    // Add component-specific variables
    if (component) {
      const componentTheme = currentTheme.components[component as keyof typeof currentTheme.components]
      if (componentTheme?.cssVariables) {
        Object.assign(variables, componentTheme.cssVariables)
      }
    }
    
    // Apply customizations to variables
    if (customizations) {
      Object.entries(customizations).forEach(([key, value]) => {
        if (key.startsWith('--')) {
          variables[key] = value
        }
      })
    }
    
    return variables
  }, [currentTheme, component, customizations, getCSSVariables])
}

// Component for applying theme to a specific scope
export function ThemeScope({
  children,
  themeId,
  className,
  style
}: {
  children: React.ReactNode
  themeId?: string
  className?: string
  style?: React.CSSProperties
}) {
  const { currentTheme, getCSSVariables } = useTheme()

  const scopeVariables = useMemo(() => {
    if (!currentTheme) return {}
    return getCSSVariables(currentTheme)
  }, [currentTheme, getCSSVariables])

  const scopeStyle = useMemo(() => {
    return {
      ...scopeVariables,
      ...style
    }
  }, [scopeVariables, style])

  return (
    <div
      className={cn('theme-scope', className)}
      style={scopeStyle}
      data-theme-scope={themeId || currentTheme?.id}
    >
      {children}
    </div>
  )
}

// Utility component for theme preview
export function ThemePreview({
  theme,
  children,
  className
}: {
  theme: ThemeConfig
  children: React.ReactNode
  className?: string
}) {
  const variables = useMemo(() => {
    const vars: Record<string, string> = {}
    
    // Convert colors to CSS variables
    Object.entries(theme.colors).forEach(([colorName, colorScale]) => {
      if (typeof colorScale === 'object') {
        Object.entries(colorScale).forEach(([shade, value]) => {
          vars[`--color-${colorName}-${shade}`] = value
        })
      }
    })

    // Add typography variables
    vars['--font-sans'] = theme.typography.fontFamily.sans.join(', ')
    vars['--font-serif'] = theme.typography.fontFamily.serif.join(', ')
    vars['--font-mono'] = theme.typography.fontFamily.mono.join(', ')

    return vars
  }, [theme])

  return (
    <div
      className={cn('theme-preview', className)}
      style={variables}
      data-theme-preview={theme.id}
    >
      {children}
    </div>
  )
}

// Component for theme customization overlay
export function ThemeCustomizer({
  component,
  blockId,
  onCustomizationChange
}: {
  component: string
  blockId?: string
  onCustomizationChange?: (customizations: Record<string, any>) => void
}) {
  const { currentTheme } = useTheme()
  const [customizations, setCustomizations] = React.useState<Record<string, any>>({})

  const handleCustomizationChange = (key: string, value: any) => {
    const newCustomizations = { ...customizations, [key]: value }
    setCustomizations(newCustomizations)
    onCustomizationChange?.(newCustomizations)
  }

  if (!currentTheme) return null

  const componentTheme = currentTheme.components[component as keyof typeof currentTheme.components]
  if (!componentTheme) return null

  return (
    <div className="theme-customizer p-4 border rounded-lg bg-background">
      <h4 className="font-medium mb-3">Customize {component}</h4>
      
      {/* Color customizations */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Primary Color</label>
        <input
          type="color"
          value={customizations['--color-primary-500'] || currentTheme.colors.primary['500']}
          onChange={(e) => handleCustomizationChange('--color-primary-500', e.target.value)}
          className="w-full h-8 rounded border"
        />
      </div>

      {/* Variant selection */}
      {Object.keys(componentTheme.variants).length > 0 && (
        <div className="space-y-2 mt-4">
          <label className="text-sm font-medium">Variant</label>
          <select
            value={customizations.variant || 'default'}
            onChange={(e) => handleCustomizationChange('variant', e.target.value)}
            className="w-full p-2 border rounded"
          >
            {Object.keys(componentTheme.variants.variant || {}).map(variant => (
              <option key={variant} value={variant}>
                {variant}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Size selection */}
      {Object.keys(componentTheme.sizes).length > 0 && (
        <div className="space-y-2 mt-4">
          <label className="text-sm font-medium">Size</label>
          <select
            value={customizations.size || 'default'}
            onChange={(e) => handleCustomizationChange('size', e.target.value)}
            className="w-full p-2 border rounded"
          >
            {Object.keys(componentTheme.sizes).map(size => (
              <option key={size} value={size}>
                {size}
              </option>
            ))}
          </select>
        </div>
      )}
    </div>
  )
}
