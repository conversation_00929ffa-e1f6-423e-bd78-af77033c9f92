'use client'

import React from 'react'
import { Button as ShadcnButton } from '@/components/ui/button'
import { Card as ShadcnCard, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Input as ShadcnInput } from '@/components/ui/input'
import { Badge as ShadcnBadge } from '@/components/ui/badge'
import { Alert as ShadcnAlert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Dialog as ShadcnDialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { withTheme, ThemedComponent, useThemedClasses } from './themed-component'
import { cn } from '@/lib/utils'

// Themed Button
export const ThemedButton = React.forwardRef<
  React.ElementRef<typeof ShadcnButton>,
  React.ComponentPropsWithoutRef<typeof ShadcnButton> & {
    themeId?: string
    blockId?: string
    customizations?: Record<string, any>
  }
>(({ className, themeId, blockId, customizations, ...props }, ref) => {
  const themedClasses = useThemedClasses('button', props.variant, props.size, customizations)
  
  return (
    <ShadcnButton
      ref={ref}
      className={cn(themedClasses, className)}
      data-theme-component="button"
      data-theme-id={themeId}
      data-block-id={blockId}
      {...props}
    />
  )
})
ThemedButton.displayName = 'ThemedButton'

// Themed Card
export const ThemedCard = React.forwardRef<
  React.ElementRef<typeof ShadcnCard>,
  React.ComponentPropsWithoutRef<typeof ShadcnCard> & {
    themeId?: string
    blockId?: string
    customizations?: Record<string, any>
  }
>(({ className, themeId, blockId, customizations, ...props }, ref) => {
  const themedClasses = useThemedClasses('card', undefined, undefined, customizations)
  
  return (
    <ShadcnCard
      ref={ref}
      className={cn(themedClasses, className)}
      data-theme-component="card"
      data-theme-id={themeId}
      data-block-id={blockId}
      {...props}
    />
  )
})
ThemedCard.displayName = 'ThemedCard'

// Themed Card components
export const ThemedCardHeader = CardHeader
export const ThemedCardTitle = CardTitle
export const ThemedCardDescription = CardDescription
export const ThemedCardContent = CardContent
export const ThemedCardFooter = CardFooter

// Themed Input
export const ThemedInput = React.forwardRef<
  React.ElementRef<typeof ShadcnInput>,
  React.ComponentPropsWithoutRef<typeof ShadcnInput> & {
    themeId?: string
    blockId?: string
    customizations?: Record<string, any>
  }
>(({ className, themeId, blockId, customizations, ...props }, ref) => {
  const themedClasses = useThemedClasses('input', undefined, undefined, customizations)
  
  return (
    <ShadcnInput
      ref={ref}
      className={cn(themedClasses, className)}
      data-theme-component="input"
      data-theme-id={themeId}
      data-block-id={blockId}
      {...props}
    />
  )
})
ThemedInput.displayName = 'ThemedInput'

// Themed Badge
export const ThemedBadge = React.forwardRef<
  React.ElementRef<typeof ShadcnBadge>,
  React.ComponentPropsWithoutRef<typeof ShadcnBadge> & {
    themeId?: string
    blockId?: string
    customizations?: Record<string, any>
  }
>(({ className, variant, themeId, blockId, customizations, ...props }, ref) => {
  const themedClasses = useThemedClasses('badge', variant, undefined, customizations)
  
  return (
    <ShadcnBadge
      ref={ref}
      variant={variant}
      className={cn(themedClasses, className)}
      data-theme-component="badge"
      data-theme-id={themeId}
      data-block-id={blockId}
      {...props}
    />
  )
})
ThemedBadge.displayName = 'ThemedBadge'

// Themed Alert
export const ThemedAlert = React.forwardRef<
  React.ElementRef<typeof ShadcnAlert>,
  React.ComponentPropsWithoutRef<typeof ShadcnAlert> & {
    themeId?: string
    blockId?: string
    customizations?: Record<string, any>
  }
>(({ className, variant, themeId, blockId, customizations, ...props }, ref) => {
  const themedClasses = useThemedClasses('alert', variant, undefined, customizations)
  
  return (
    <ShadcnAlert
      ref={ref}
      variant={variant}
      className={cn(themedClasses, className)}
      data-theme-component="alert"
      data-theme-id={themeId}
      data-block-id={blockId}
      {...props}
    />
  )
})
ThemedAlert.displayName = 'ThemedAlert'

// Themed Alert components
export const ThemedAlertTitle = AlertTitle
export const ThemedAlertDescription = AlertDescription

// Themed Dialog
export const ThemedDialog = ShadcnDialog

export const ThemedDialogTrigger = DialogTrigger

export const ThemedDialogContent = React.forwardRef<
  React.ElementRef<typeof DialogContent>,
  React.ComponentPropsWithoutRef<typeof DialogContent> & {
    themeId?: string
    blockId?: string
    customizations?: Record<string, any>
  }
>(({ className, themeId, blockId, customizations, ...props }, ref) => {
  const themedClasses = useThemedClasses('dialog', undefined, undefined, customizations)
  
  return (
    <DialogContent
      ref={ref}
      className={cn(themedClasses, className)}
      data-theme-component="dialog"
      data-theme-id={themeId}
      data-block-id={blockId}
      {...props}
    />
  )
})
ThemedDialogContent.displayName = 'ThemedDialogContent'

export const ThemedDialogHeader = DialogHeader
export const ThemedDialogTitle = DialogTitle
export const ThemedDialogDescription = DialogDescription
export const ThemedDialogFooter = DialogFooter

// Theme-aware component factory
export function createThemedComponent<T extends React.ComponentType<any>>(
  Component: T,
  componentName: string
) {
  return React.forwardRef<
    React.ElementRef<T>,
    React.ComponentPropsWithoutRef<T> & {
      themeId?: string
      blockId?: string
      customizations?: Record<string, any>
    }
  >(({ className, themeId, blockId, customizations, ...props }, ref) => {
    const themedClasses = useThemedClasses(componentName, undefined, undefined, customizations)
    
    return (
      <Component
        ref={ref}
        className={cn(themedClasses, className)}
        data-theme-component={componentName}
        data-theme-id={themeId}
        data-block-id={blockId}
        {...props}
      />
    )
  })
}

// Block-level theme wrapper
export function ThemedBlock({
  children,
  blockId,
  themeId,
  customizations,
  className,
  style
}: {
  children: React.ReactNode
  blockId?: string
  themeId?: string
  customizations?: Record<string, any>
  className?: string
  style?: React.CSSProperties
}) {
  return (
    <ThemedComponent
      component="block"
      blockId={blockId}
      themeId={themeId}
      customizations={customizations}
      className={cn('themed-block', className)}
      style={style}
    >
      {children}
    </ThemedComponent>
  )
}

// Page-level theme wrapper
export function ThemedPage({
  children,
  pageId,
  themeId,
  customizations,
  className,
  style
}: {
  children: React.ReactNode
  pageId?: string
  themeId?: string
  customizations?: Record<string, any>
  className?: string
  style?: React.CSSProperties
}) {
  return (
    <ThemedComponent
      component="page"
      blockId={pageId}
      themeId={themeId}
      customizations={customizations}
      className={cn('themed-page', className)}
      style={style}
    >
      {children}
    </ThemedComponent>
  )
}

// Export all themed components
export const ThemedComponents = {
  Button: ThemedButton,
  Card: ThemedCard,
  CardHeader: ThemedCardHeader,
  CardTitle: ThemedCardTitle,
  CardDescription: ThemedCardDescription,
  CardContent: ThemedCardContent,
  CardFooter: ThemedCardFooter,
  Input: ThemedInput,
  Badge: ThemedBadge,
  Alert: ThemedAlert,
  AlertTitle: ThemedAlertTitle,
  AlertDescription: ThemedAlertDescription,
  Dialog: ThemedDialog,
  DialogTrigger: ThemedDialogTrigger,
  DialogContent: ThemedDialogContent,
  DialogHeader: ThemedDialogHeader,
  DialogTitle: ThemedDialogTitle,
  DialogDescription: ThemedDialogDescription,
  DialogFooter: ThemedDialogFooter,
  Block: ThemedBlock,
  Page: ThemedPage
}

// Component registry for dynamic theming
export const THEMED_COMPONENT_REGISTRY = {
  'button': ThemedButton,
  'card': ThemedCard,
  'input': ThemedInput,
  'badge': ThemedBadge,
  'alert': ThemedAlert,
  'dialog': ThemedDialogContent,
  'block': ThemedBlock,
  'page': ThemedPage
}

// Utility to get themed component by name
export function getThemedComponent(componentName: string) {
  return THEMED_COMPONENT_REGISTRY[componentName as keyof typeof THEMED_COMPONENT_REGISTRY]
}

// Hook for dynamic component theming
export function useThemedComponent(componentName: string) {
  const Component = getThemedComponent(componentName)
  const themedClasses = useThemedClasses(componentName)
  
  return {
    Component,
    themedClasses,
    isThemed: !!Component
  }
}
