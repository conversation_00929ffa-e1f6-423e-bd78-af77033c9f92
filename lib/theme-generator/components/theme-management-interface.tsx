'use client'

import React, { useState, useEffect } from 'react'
import { useTheme } from '../theme-context'
import { ThemeConfig } from '../types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import {
  Palette,
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Copy,
  Check,
  Trash2,
  Edit,
  Eye,
  Star,
  <PERSON>Off,
  <PERSON>rid,
  List,
  SortAsc,
  SortDesc,
  Calendar,
  User,
  Tag,
  Sparkles,
  Wand2,
  Brain,
  Settings,
  Share,
  Import,
  Export
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'
import { AIThemeDesigner } from './ai-theme-designer'
import { EnhancedThemeCustomizer } from './enhanced-theme-customizer'

interface ThemeManagementInterfaceProps {
  className?: string
  onThemeSelect?: (theme: ThemeConfig) => void
  showAIDesigner?: boolean
  showCustomizer?: boolean
}

export function ThemeManagementInterface({ 
  className, 
  onThemeSelect,
  showAIDesigner = true,
  showCustomizer = true
}: ThemeManagementInterfaceProps) {
  const { 
    themes, 
    currentTheme, 
    setTheme, 
    saveTheme, 
    deleteTheme,
    generateTheme,
    isLoading 
  } = useTheme()

  const [activeTab, setActiveTab] = useState<'library' | 'ai-designer' | 'customizer' | 'settings'>('library')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'author'>('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [filterCategory, setFilterCategory] = useState<'all' | 'custom' | 'generated' | 'imported'>('all')
  const [selectedTheme, setSelectedTheme] = useState<ThemeConfig | null>(null)
  const [showPreview, setShowPreview] = useState(false)
  const [favorites, setFavorites] = useState<string[]>([])

  // Load favorites from localStorage
  useEffect(() => {
    const savedFavorites = localStorage.getItem('theme-favorites')
    if (savedFavorites) {
      setFavorites(JSON.parse(savedFavorites))
    }
  }, [])

  // Save favorites to localStorage
  const toggleFavorite = (themeId: string) => {
    const newFavorites = favorites.includes(themeId)
      ? favorites.filter(id => id !== themeId)
      : [...favorites, themeId]
    
    setFavorites(newFavorites)
    localStorage.setItem('theme-favorites', JSON.stringify(newFavorites))
  }

  // Filter and sort themes
  const filteredThemes = themes
    .filter(theme => {
      // Search filter
      if (searchQuery && !theme.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !theme.description?.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false
      }

      // Category filter
      if (filterCategory !== 'all') {
        // This would need to be implemented based on theme metadata
        // For now, we'll show all themes
      }

      return true
    })
    .sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'date':
          // Would need createdAt field in theme
          comparison = 0
          break
        case 'author':
          comparison = (a.author || '').localeCompare(b.author || '')
          break
      }

      return sortOrder === 'asc' ? comparison : -comparison
    })

  const handleThemeSelect = async (theme: ThemeConfig) => {
    try {
      await setTheme(theme.id)
      setSelectedTheme(theme)
      
      if (onThemeSelect) {
        onThemeSelect(theme)
      }
      
      toast.success(`Applied ${theme.name} theme`)
    } catch (error) {
      console.error('Theme selection error:', error)
      toast.error('Failed to apply theme')
    }
  }

  const handleThemeDelete = async (themeId: string) => {
    try {
      await deleteTheme(themeId)
      toast.success('Theme deleted successfully')
    } catch (error) {
      console.error('Theme deletion error:', error)
      toast.error('Failed to delete theme')
    }
  }

  const handleThemeExport = (theme: ThemeConfig) => {
    const dataStr = JSON.stringify(theme, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = `${theme.name.replace(/\s+/g, '-').toLowerCase()}-theme.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    toast.success('Theme exported successfully')
  }

  const handleThemeImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        const themeData = JSON.parse(e.target?.result as string)
        
        // Validate theme structure
        if (!themeData.id || !themeData.name || !themeData.colors) {
          throw new Error('Invalid theme file format')
        }

        // Generate new ID to avoid conflicts
        const importedTheme: ThemeConfig = {
          ...themeData,
          id: `imported-${Date.now()}`,
          name: `${themeData.name} (Imported)`
        }

        await saveTheme(importedTheme)
        toast.success('Theme imported successfully')
      } catch (error) {
        console.error('Theme import error:', error)
        toast.error('Failed to import theme')
      }
    }
    
    reader.readAsText(file)
    event.target.value = '' // Reset input
  }

  const renderThemeCard = (theme: ThemeConfig) => {
    const isActive = currentTheme?.id === theme.id
    const isFavorite = favorites.includes(theme.id)
    
    return (
      <Card 
        key={theme.id} 
        className={cn(
          'cursor-pointer transition-all hover:shadow-md',
          isActive && 'ring-2 ring-purple-500 ring-offset-2'
        )}
        onClick={() => handleThemeSelect(theme)}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-sm font-medium truncate">
                {theme.name}
              </CardTitle>
              {theme.description && (
                <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                  {theme.description}
                </p>
              )}
            </div>
            <div className="flex items-center gap-1 ml-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  toggleFavorite(theme.id)
                }}
                className="h-6 w-6 p-0"
              >
                {isFavorite ? (
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                ) : (
                  <StarOff className="h-3 w-3" />
                )}
              </Button>
              {isActive && (
                <Badge variant="default" className="text-xs">
                  Active
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          {/* Color Preview */}
          <div className="flex gap-1 mb-3">
            {theme.colors?.primary?.DEFAULT && (
              <div 
                className="w-6 h-6 rounded border"
                style={{ backgroundColor: theme.colors.primary.DEFAULT }}
                title="Primary"
              />
            )}
            {theme.colors?.secondary?.DEFAULT && (
              <div 
                className="w-6 h-6 rounded border"
                style={{ backgroundColor: theme.colors.secondary.DEFAULT }}
                title="Secondary"
              />
            )}
            {theme.colors?.accent?.DEFAULT && (
              <div 
                className="w-6 h-6 rounded border"
                style={{ backgroundColor: theme.colors.accent.DEFAULT }}
                title="Accent"
              />
            )}
            {theme.colors?.background && (
              <div 
                className="w-6 h-6 rounded border"
                style={{ backgroundColor: theme.colors.background }}
                title="Background"
              />
            )}
          </div>

          {/* Theme Info */}
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-2">
              {theme.author && (
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  {theme.author}
                </div>
              )}
              <div className="flex items-center gap-1">
                <Tag className="h-3 w-3" />
                v{theme.version}
              </div>
            </div>
            
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  setSelectedTheme(theme)
                  setShowPreview(true)
                }}
                className="h-6 w-6 p-0"
              >
                <Eye className="h-3 w-3" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  handleThemeExport(theme)
                }}
                className="h-6 w-6 p-0"
              >
                <Download className="h-3 w-3" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  navigator.clipboard.writeText(JSON.stringify(theme, null, 2))
                  toast.success('Theme copied to clipboard')
                }}
                className="h-6 w-6 p-0"
              >
                <Copy className="h-3 w-3" />
              </Button>
              
              {!isActive && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleThemeDelete(theme.id)
                  }}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const renderThemeList = (theme: ThemeConfig) => {
    const isActive = currentTheme?.id === theme.id
    const isFavorite = favorites.includes(theme.id)
    
    return (
      <Card 
        key={theme.id} 
        className={cn(
          'cursor-pointer transition-all hover:shadow-sm',
          isActive && 'ring-2 ring-purple-500 ring-offset-1'
        )}
        onClick={() => handleThemeSelect(theme)}
      >
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            {/* Color Preview */}
            <div className="flex gap-1">
              {theme.colors?.primary?.DEFAULT && (
                <div 
                  className="w-4 h-4 rounded border"
                  style={{ backgroundColor: theme.colors.primary.DEFAULT }}
                />
              )}
              {theme.colors?.secondary?.DEFAULT && (
                <div 
                  className="w-4 h-4 rounded border"
                  style={{ backgroundColor: theme.colors.secondary.DEFAULT }}
                />
              )}
              {theme.colors?.accent?.DEFAULT && (
                <div 
                  className="w-4 h-4 rounded border"
                  style={{ backgroundColor: theme.colors.accent.DEFAULT }}
                />
              )}
            </div>

            {/* Theme Info */}
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h3 className="font-medium text-sm">{theme.name}</h3>
                {isActive && (
                  <Badge variant="default" className="text-xs">
                    Active
                  </Badge>
                )}
                {isFavorite && (
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                )}
              </div>
              {theme.description && (
                <p className="text-xs text-muted-foreground mt-1">
                  {theme.description}
                </p>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  toggleFavorite(theme.id)
                }}
                className="h-6 w-6 p-0"
              >
                {isFavorite ? (
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                ) : (
                  <StarOff className="h-3 w-3" />
                )}
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  setSelectedTheme(theme)
                  setShowPreview(true)
                }}
                className="h-6 w-6 p-0"
              >
                <Eye className="h-3 w-3" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  handleThemeExport(theme)
                }}
                className="h-6 w-6 p-0"
              >
                <Download className="h-3 w-3" />
              </Button>
              
              {!isActive && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleThemeDelete(theme.id)
                  }}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn('h-full flex flex-col', className)}>
      {/* Header */}
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Palette className="h-5 w-5 text-purple-600" />
              Theme Management
            </CardTitle>
            <div className="flex items-center gap-2">
              <input
                type="file"
                accept=".json"
                onChange={handleThemeImport}
                className="hidden"
                id="theme-import"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => document.getElementById('theme-import')?.click()}
              >
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              
              <Dialog>
                <DialogTrigger asChild>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    New Theme
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh]">
                  <DialogHeader>
                    <DialogTitle>Create New Theme</DialogTitle>
                  </DialogHeader>
                  <div className="h-[70vh]">
                    <AIThemeDesigner />
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="library">
              <Grid className="h-4 w-4 mr-2" />
              Library
            </TabsTrigger>
            {showAIDesigner && (
              <TabsTrigger value="ai-designer">
                <Brain className="h-4 w-4 mr-2" />
                AI Designer
              </TabsTrigger>
            )}
            {showCustomizer && (
              <TabsTrigger value="customizer">
                <Wand2 className="h-4 w-4 mr-2" />
                Customizer
              </TabsTrigger>
            )}
            <TabsTrigger value="settings">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </TabsTrigger>
          </TabsList>

          {/* Library Tab */}
          <TabsContent value="library" className="flex-1 flex flex-col mt-4">
            {/* Filters and Search */}
            <Card className="mb-4">
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search themes..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <Select value={filterCategory} onValueChange={(value: any) => setFilterCategory(value)}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Themes</SelectItem>
                      <SelectItem value="custom">Custom</SelectItem>
                      <SelectItem value="generated">AI Generated</SelectItem>
                      <SelectItem value="imported">Imported</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="name">Name</SelectItem>
                      <SelectItem value="date">Date</SelectItem>
                      <SelectItem value="author">Author</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  >
                    {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                  </Button>

                  <div className="flex rounded-lg border">
                    <Button
                      variant={viewMode === 'grid' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('grid')}
                      className="rounded-r-none"
                    >
                      <Grid className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === 'list' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('list')}
                      className="rounded-l-none"
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Themes Grid/List */}
            <ScrollArea className="flex-1">
              {filteredThemes.length === 0 ? (
                <Card className="p-8">
                  <div className="text-center">
                    <Palette className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <h3 className="font-medium text-gray-900 mb-2">No Themes Found</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      {searchQuery ? 'Try adjusting your search criteria' : 'Create your first theme to get started'}
                    </p>
                    <Button onClick={() => setActiveTab('ai-designer')}>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Create Theme with AI
                    </Button>
                  </div>
                </Card>
              ) : (
                <div className={cn(
                  viewMode === 'grid'
                    ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
                    : 'space-y-2'
                )}>
                  {filteredThemes.map(theme =>
                    viewMode === 'grid' ? renderThemeCard(theme) : renderThemeList(theme)
                  )}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          {/* AI Designer Tab */}
          {showAIDesigner && (
            <TabsContent value="ai-designer" className="flex-1 mt-4">
              <AIThemeDesigner
                className="h-full"
                onThemeGenerated={(theme) => {
                  toast.success('Theme generated successfully!')
                  setActiveTab('library')
                }}
              />
            </TabsContent>
          )}

          {/* Customizer Tab */}
          {showCustomizer && (
            <TabsContent value="customizer" className="flex-1 mt-4">
              {selectedTheme || currentTheme ? (
                <EnhancedThemeCustomizer
                  className="h-full"
                  theme={selectedTheme || currentTheme}
                  onThemeChange={(theme) => {
                    if (onThemeSelect) {
                      onThemeSelect(theme)
                    }
                  }}
                />
              ) : (
                <Card className="h-full flex items-center justify-center">
                  <CardContent className="text-center">
                    <Wand2 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <h3 className="font-medium text-gray-900 mb-2">No Theme Selected</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Select a theme from the library to start customizing
                    </p>
                    <Button onClick={() => setActiveTab('library')}>
                      <Palette className="h-4 w-4 mr-2" />
                      Browse Themes
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          )}

          {/* Settings Tab */}
          <TabsContent value="settings" className="flex-1 mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Theme Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Auto-apply themes</Label>
                      <p className="text-xs text-muted-foreground">
                        Automatically apply themes when selected
                      </p>
                    </div>
                    <Switch />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Theme previews</Label>
                      <p className="text-xs text-muted-foreground">
                        Show live previews in theme cards
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">AI suggestions</Label>
                      <p className="text-xs text-muted-foreground">
                        Enable AI-powered theme suggestions
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-sm font-medium">Export & Import</h4>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={() => {
                        const allThemes = JSON.stringify(themes, null, 2)
                        const blob = new Blob([allThemes], { type: 'application/json' })
                        const url = URL.createObjectURL(blob)
                        const link = document.createElement('a')
                        link.href = url
                        link.download = 'all-themes.json'
                        link.click()
                        URL.revokeObjectURL(url)
                        toast.success('All themes exported')
                      }}
                    >
                      <Export className="h-4 w-4 mr-2" />
                      Export All
                    </Button>

                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={() => document.getElementById('theme-import')?.click()}
                    >
                      <Import className="h-4 w-4 mr-2" />
                      Import Themes
                    </Button>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-sm font-medium">Theme Library</h4>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Total Themes:</span>
                      <span className="ml-2 font-medium">{themes.length}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Favorites:</span>
                      <span className="ml-2 font-medium">{favorites.length}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Active Theme:</span>
                      <span className="ml-2 font-medium">{currentTheme?.name || 'None'}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Last Modified:</span>
                      <span className="ml-2 font-medium">Today</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Theme Preview Dialog */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>
              {selectedTheme?.name} Preview
            </DialogTitle>
          </DialogHeader>
          {selectedTheme && (
            <div className="h-[70vh]">
              <EnhancedThemeCustomizer
                theme={selectedTheme}
                showPreview={true}
                onThemeChange={() => {}}
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
