'use client'

import React, { useState, useRef, useEffect } from 'react'
import { useTheme } from '../theme-context'
import { ThemeGeneratorOptions } from '../types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import {
  Sparkles,
  MessageSquare,
  Wand2,
  Brain,
  Send,
  Loader2,
  Lightbulb,
  Zap,
  Palette,
  Type,
  Layout,
  Paintbrush,
  Eye,
  Star,
  Plus,
  ChevronDown,
  ChevronUp,
  Download,
  Upload,
  Copy,
  Check
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

interface AIThemeDesignerProps {
  className?: string
  onThemeGenerated?: (theme: any) => void
}

export function AIThemeDesigner({ className, onThemeGenerated }: AIThemeDesignerProps) {
  const { generateTheme, saveTheme, themes, currentTheme } = useTheme()
  const [activeTab, setActiveTab] = useState<'chat' | 'suggestions' | 'templates' | 'advanced'>('chat')
  const [isExpanded, setIsExpanded] = useState(true)
  const [input, setInput] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [messages, setMessages] = useState<any[]>([])
  const [generatedThemes, setGeneratedThemes] = useState<any[]>([])
  const inputRef = useRef<HTMLInputElement>(null)

  // AI-powered theme suggestions
  const quickActions = [
    {
      icon: Palette,
      label: 'Modern Minimalist',
      prompt: 'Create a clean, modern minimalist theme with subtle colors and plenty of white space',
      category: 'style',
      color: 'bg-blue-500',
      options: { style: 'minimal', contrast: 'low', saturation: 'muted' }
    },
    {
      icon: Star,
      label: 'Bold & Vibrant',
      prompt: 'Design a bold, vibrant theme with high contrast and energetic colors',
      category: 'style',
      color: 'bg-red-500',
      options: { style: 'bold', contrast: 'high', saturation: 'vibrant' }
    },
    {
      icon: Type,
      label: 'Elegant Professional',
      prompt: 'Create an elegant, professional theme suitable for business and corporate use',
      category: 'style',
      color: 'bg-gray-600',
      options: { style: 'professional', contrast: 'medium', saturation: 'normal' }
    },
    {
      icon: Paintbrush,
      label: 'Creative Playful',
      prompt: 'Design a creative, playful theme with fun colors and rounded elements',
      category: 'style',
      color: 'bg-purple-500',
      options: { style: 'playful', contrast: 'medium', saturation: 'vibrant' }
    },
    {
      icon: Layout,
      label: 'E-commerce Optimized',
      prompt: 'Create a theme optimized for e-commerce with clear CTAs and product focus',
      category: 'purpose',
      color: 'bg-green-500',
      options: { style: 'modern', contrast: 'high', saturation: 'normal' }
    },
    {
      icon: Eye,
      label: 'Dark Mode Ready',
      prompt: 'Design a theme that works beautifully in both light and dark modes',
      category: 'mode',
      color: 'bg-indigo-500',
      options: { style: 'modern', contrast: 'medium', saturation: 'normal' }
    }
  ]

  // Smart theme suggestions based on current context
  const smartSuggestions = [
    {
      type: 'improvement',
      title: 'Enhance Accessibility',
      description: 'Improve color contrast and readability for better accessibility',
      action: () => handleQuickAction('Optimize current theme for better accessibility with improved contrast ratios')
    },
    {
      type: 'trend',
      title: 'Modern Gradient',
      description: 'Add subtle gradients and modern color transitions',
      action: () => handleQuickAction('Add modern gradient effects and smooth color transitions to the theme')
    },
    {
      type: 'brand',
      title: 'Brand Alignment',
      description: 'Align theme colors with brand identity',
      action: () => handleQuickAction('Adjust theme colors to better align with our brand identity and values')
    },
    {
      type: 'seasonal',
      title: 'Seasonal Update',
      description: 'Create a seasonal variation of the current theme',
      action: () => handleQuickAction('Create a seasonal variation of the current theme with appropriate colors')
    }
  ]

  // Pre-built theme templates
  const themeTemplates = [
    {
      name: 'Zara Inspired',
      description: 'Clean, minimalist theme inspired by Zara\'s design aesthetic',
      preview: '#000000',
      options: {
        baseColor: '#000000',
        style: 'minimal' as const,
        contrast: 'high' as const,
        saturation: 'muted' as const,
        borderRadius: 'sharp' as const,
        fontPairing: 'modern' as const,
        spacing: 'normal' as const
      }
    },
    {
      name: 'Selfi.co.za Style',
      description: 'Light, airy theme matching selfi.co.za aesthetic',
      preview: '#f8f9fa',
      options: {
        baseColor: '#6c757d',
        style: 'minimal' as const,
        contrast: 'low' as const,
        saturation: 'muted' as const,
        borderRadius: 'rounded' as const,
        fontPairing: 'classic' as const,
        spacing: 'spacious' as const
      }
    },
    {
      name: 'Kids Fashion',
      description: 'Playful, colorful theme perfect for children\'s clothing',
      preview: '#ff6b6b',
      options: {
        baseColor: '#ff6b6b',
        style: 'playful' as const,
        contrast: 'medium' as const,
        saturation: 'vibrant' as const,
        borderRadius: 'pill' as const,
        fontPairing: 'creative' as const,
        spacing: 'normal' as const
      }
    },
    {
      name: 'Luxury Brand',
      description: 'Sophisticated theme for premium and luxury brands',
      preview: '#2c3e50',
      options: {
        baseColor: '#2c3e50',
        style: 'elegant' as const,
        contrast: 'medium' as const,
        saturation: 'muted' as const,
        borderRadius: 'rounded' as const,
        fontPairing: 'classic' as const,
        spacing: 'spacious' as const
      }
    },
    {
      name: 'Tech Startup',
      description: 'Modern, innovative theme for technology companies',
      preview: '#3498db',
      options: {
        baseColor: '#3498db',
        style: 'modern' as const,
        contrast: 'high' as const,
        saturation: 'normal' as const,
        borderRadius: 'rounded' as const,
        fontPairing: 'technical' as const,
        spacing: 'compact' as const
      }
    },
    {
      name: 'Eco Friendly',
      description: 'Natural, earth-toned theme for sustainable brands',
      preview: '#27ae60',
      options: {
        baseColor: '#27ae60',
        style: 'modern' as const,
        contrast: 'medium' as const,
        saturation: 'normal' as const,
        borderRadius: 'rounded' as const,
        fontPairing: 'modern' as const,
        spacing: 'normal' as const
      }
    }
  ]

  const handleQuickAction = async (prompt: string, options?: Partial<ThemeGeneratorOptions>) => {
    setIsGenerating(true)
    setInput(prompt)
    
    try {
      // Use provided options or extract from prompt
      const themeOptions: ThemeGeneratorOptions = {
        baseColor: options?.baseColor || '#3b82f6',
        style: options?.style || 'modern',
        contrast: options?.contrast || 'medium',
        saturation: options?.saturation || 'normal',
        borderRadius: options?.borderRadius || 'rounded',
        fontPairing: options?.fontPairing || 'modern',
        spacing: options?.spacing || 'normal'
      }

      const theme = await generateTheme(themeOptions)
      setGeneratedThemes(prev => [theme, ...prev.slice(0, 4)]) // Keep last 5
      
      // Add to messages
      setMessages(prev => [
        ...prev,
        { role: 'user', content: prompt },
        { role: 'assistant', content: `I've generated a ${themeOptions.style} theme based on your request. The theme features ${themeOptions.contrast} contrast with ${themeOptions.saturation} saturation levels.` }
      ])

      if (onThemeGenerated) {
        onThemeGenerated(theme)
      }

      toast.success('Theme generated successfully!')
    } catch (error) {
      console.error('Quick action error:', error)
      toast.error('Failed to generate theme')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleCustomSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim()) return
    
    setIsGenerating(true)
    
    try {
      // Parse natural language input to extract theme preferences
      const themeOptions = parseNaturalLanguageInput(input)
      const theme = await generateTheme(themeOptions)
      
      setGeneratedThemes(prev => [theme, ...prev.slice(0, 4)])
      
      // Add to messages
      setMessages(prev => [
        ...prev,
        { role: 'user', content: input },
        { role: 'assistant', content: 'I\'ve analyzed your requirements and generated a custom theme that matches your specifications.' }
      ])

      setInput('')
      
      if (onThemeGenerated) {
        onThemeGenerated(theme)
      }

      toast.success('Custom theme generated!')
    } catch (error) {
      console.error('Custom submit error:', error)
      toast.error('Failed to generate custom theme')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleTemplateGenerate = async (template: any) => {
    setIsGenerating(true)
    
    try {
      const theme = await generateTheme(template.options)
      setGeneratedThemes(prev => [theme, ...prev.slice(0, 4)])
      
      toast.success(`${template.name} theme generated!`)
      
      if (onThemeGenerated) {
        onThemeGenerated(theme)
      }
    } catch (error) {
      console.error('Template generation error:', error)
      toast.error('Failed to generate template theme')
    } finally {
      setIsGenerating(false)
    }
  }

  const parseNaturalLanguageInput = (input: string): ThemeGeneratorOptions => {
    const lower = input.toLowerCase()
    
    // Extract style
    let style: ThemeGeneratorOptions['style'] = 'modern'
    if (lower.includes('minimal') || lower.includes('clean')) style = 'minimal'
    else if (lower.includes('bold') || lower.includes('strong')) style = 'bold'
    else if (lower.includes('elegant') || lower.includes('sophisticated')) style = 'elegant'
    else if (lower.includes('playful') || lower.includes('fun')) style = 'playful'
    else if (lower.includes('professional') || lower.includes('business')) style = 'professional'

    // Extract contrast
    let contrast: ThemeGeneratorOptions['contrast'] = 'medium'
    if (lower.includes('high contrast') || lower.includes('strong contrast')) contrast = 'high'
    else if (lower.includes('low contrast') || lower.includes('subtle')) contrast = 'low'

    // Extract saturation
    let saturation: ThemeGeneratorOptions['saturation'] = 'normal'
    if (lower.includes('vibrant') || lower.includes('bright')) saturation = 'vibrant'
    else if (lower.includes('muted') || lower.includes('subtle')) saturation = 'muted'

    // Extract border radius
    let borderRadius: ThemeGeneratorOptions['borderRadius'] = 'rounded'
    if (lower.includes('sharp') || lower.includes('square')) borderRadius = 'sharp'
    else if (lower.includes('pill') || lower.includes('round')) borderRadius = 'pill'

    // Extract base color (simple color detection)
    let baseColor = '#3b82f6'
    if (lower.includes('blue')) baseColor = '#3b82f6'
    else if (lower.includes('red')) baseColor = '#ef4444'
    else if (lower.includes('green')) baseColor = '#22c55e'
    else if (lower.includes('purple')) baseColor = '#a855f7'
    else if (lower.includes('orange')) baseColor = '#f97316'
    else if (lower.includes('pink')) baseColor = '#ec4899'
    else if (lower.includes('black')) baseColor = '#000000'
    else if (lower.includes('gray') || lower.includes('grey')) baseColor = '#6b7280'

    return {
      baseColor,
      style,
      contrast,
      saturation,
      borderRadius,
      fontPairing: 'modern',
      spacing: 'normal'
    }
  }

  // Focus input when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  return (
    <Card className={cn('h-full flex flex-col', className)}>
      {/* Header */}
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Brain className="h-5 w-5 text-purple-600" />
            AI Theme Designer
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>
        <p className="text-sm text-muted-foreground">
          Create beautiful themes with AI assistance and natural language
        </p>
      </CardHeader>

      {isExpanded && (
        <CardContent className="flex-1 flex flex-col p-0">
          {/* AI Input */}
          <div className="p-4 border-b">
            <form onSubmit={handleCustomSubmit} className="space-y-3">
              <div className="flex gap-2">
                <Input
                  ref={inputRef}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Describe your ideal theme... e.g., 'Create a modern, minimalist theme with blue accents and high contrast'"
                  disabled={isGenerating}
                  className="flex-1"
                />
                <Button
                  type="submit"
                  size="sm"
                  disabled={isGenerating || !input.trim()}
                  className="px-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                >
                  {isGenerating ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
              
              {isGenerating && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Loader2 className="h-3 w-3 animate-spin" />
                  AI is designing your theme...
                </div>
              )}
            </form>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-4 mx-4 mt-4">
              <TabsTrigger value="chat" className="text-xs">
                <MessageSquare className="h-3 w-3 mr-1" />
                Chat
              </TabsTrigger>
              <TabsTrigger value="suggestions" className="text-xs">
                <Lightbulb className="h-3 w-3 mr-1" />
                Smart
              </TabsTrigger>
              <TabsTrigger value="templates" className="text-xs">
                <Wand2 className="h-3 w-3 mr-1" />
                Templates
              </TabsTrigger>
              <TabsTrigger value="advanced" className="text-xs">
                <Palette className="h-3 w-3 mr-1" />
                Advanced
              </TabsTrigger>
            </TabsList>

            {/* Chat Tab */}
            <TabsContent value="chat" className="flex-1 flex flex-col mt-4">
              <ScrollArea className="flex-1 px-4">
                {messages.length === 0 ? (
                  <div className="space-y-3">
                    <div className="text-center py-6">
                      <Sparkles className="h-8 w-8 mx-auto mb-3 text-purple-500" />
                      <h3 className="font-medium mb-2">Start with AI Theme Design</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Choose a quick style or describe your perfect theme
                      </p>
                    </div>
                    
                    {/* Quick Actions Grid */}
                    <div className="grid grid-cols-1 gap-2">
                      {quickActions.map((action, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => handleQuickAction(action.prompt, action.options)}
                          disabled={isGenerating}
                          className="justify-start h-auto p-3 text-left"
                        >
                          <div className={cn('w-8 h-8 rounded-lg flex items-center justify-center mr-3', action.color)}>
                            <action.icon className="h-4 w-4 text-white" />
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-sm">{action.label}</div>
                            <div className="text-xs text-muted-foreground">{action.prompt}</div>
                          </div>
                        </Button>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {messages.map((message, index) => (
                      <div
                        key={index}
                        className={cn(
                          'p-3 rounded-lg max-w-[85%]',
                          message.role === 'user'
                            ? 'bg-purple-100 ml-auto text-purple-900'
                            : 'bg-gray-100 text-gray-900'
                        )}
                      >
                        <div className="text-sm">{message.content}</div>
                      </div>
                    ))}
                    
                    {messages.length > 0 && (
                      <div className="flex justify-center pt-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setMessages([])}
                          className="text-xs"
                        >
                          Clear Conversation
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>

            {/* Suggestions Tab */}
            <TabsContent value="suggestions" className="flex-1 mt-4">
              <ScrollArea className="h-full px-4">
                <div className="space-y-3">
                  {smartSuggestions.map((suggestion, index) => (
                    <Card key={index} className="p-3">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Lightbulb className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-sm mb-1">{suggestion.title}</h4>
                          <p className="text-xs text-muted-foreground mb-2">{suggestion.description}</p>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={suggestion.action}
                            disabled={isGenerating}
                            className="text-xs h-7"
                          >
                            <Zap className="h-3 w-3 mr-1" />
                            Apply
                          </Button>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {suggestion.type}
                        </Badge>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Templates Tab */}
            <TabsContent value="templates" className="flex-1 mt-4">
              <ScrollArea className="h-full px-4">
                <div className="space-y-3">
                  {themeTemplates.map((template, index) => (
                    <Card key={index} className="p-3">
                      <div className="space-y-3">
                        <div className="flex items-start gap-3">
                          <div
                            className="w-12 h-12 rounded-lg border-2 border-gray-200 flex-shrink-0"
                            style={{ backgroundColor: template.preview }}
                          />
                          <div className="flex-1">
                            <h4 className="font-medium text-sm mb-1">{template.name}</h4>
                            <p className="text-xs text-muted-foreground">{template.description}</p>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-1">
                          <Badge variant="outline" className="text-xs">
                            {template.options.style}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {template.options.contrast} contrast
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {template.options.saturation}
                          </Badge>
                        </div>

                        <Button
                          size="sm"
                          onClick={() => handleTemplateGenerate(template)}
                          disabled={isGenerating}
                          className="w-full text-xs h-7"
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Use Template
                        </Button>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Advanced Tab */}
            <TabsContent value="advanced" className="flex-1 mt-4">
              <ScrollArea className="h-full px-4">
                <div className="space-y-4">
                  <div className="text-center py-4">
                    <Palette className="h-8 w-8 mx-auto mb-2 text-purple-500" />
                    <h3 className="font-medium mb-1">Advanced Theme Controls</h3>
                    <p className="text-xs text-muted-foreground">
                      Fine-tune your theme with precise controls
                    </p>
                  </div>

                  {/* Generated Themes Preview */}
                  {generatedThemes.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium">Recently Generated</h4>
                      {generatedThemes.slice(0, 3).map((theme, index) => (
                        <Card key={index} className="p-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 rounded border" style={{ backgroundColor: theme.colors?.primary?.DEFAULT || '#3b82f6' }} />
                              <div>
                                <div className="text-sm font-medium">{theme.name}</div>
                                <div className="text-xs text-muted-foreground">{theme.description}</div>
                              </div>
                            </div>
                            <div className="flex gap-1">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  navigator.clipboard.writeText(JSON.stringify(theme, null, 2))
                                  toast.success('Theme copied to clipboard!')
                                }}
                                className="text-xs h-6 px-2"
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => saveTheme(theme)}
                                className="text-xs h-6 px-2"
                              >
                                <Download className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  )}

                  {/* Theme Library */}
                  {themes.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium">Saved Themes</h4>
                      {themes.slice(0, 5).map((theme, index) => (
                        <Card key={index} className="p-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 rounded border" style={{ backgroundColor: theme.colors?.primary?.DEFAULT || '#3b82f6' }} />
                              <div>
                                <div className="text-sm font-medium">{theme.name}</div>
                                <div className="text-xs text-muted-foreground">{theme.description}</div>
                              </div>
                            </div>
                            <div className="flex gap-1">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  if (onThemeGenerated) {
                                    onThemeGenerated(theme)
                                  }
                                }}
                                className="text-xs h-6 px-2"
                              >
                                <Check className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      )}
    </Card>
  )
}
