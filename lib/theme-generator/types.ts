// Theme Generator Types
// Comprehensive theming system for Shadcn components

export interface ThemeColors {
  // Primary colors
  primary: {
    50: string
    100: string
    200: string
    300: string
    400: string
    500: string
    600: string
    700: string
    800: string
    900: string
    950: string
  }
  
  // Secondary colors
  secondary: {
    50: string
    100: string
    200: string
    300: string
    400: string
    500: string
    600: string
    700: string
    800: string
    900: string
    950: string
  }
  
  // Accent colors
  accent: {
    50: string
    100: string
    200: string
    300: string
    400: string
    500: string
    600: string
    700: string
    800: string
    900: string
    950: string
  }
  
  // Neutral/Gray colors
  neutral: {
    50: string
    100: string
    200: string
    300: string
    400: string
    500: string
    600: string
    700: string
    800: string
    900: string
    950: string
  }
  
  // Semantic colors
  success: {
    50: string
    100: string
    200: string
    300: string
    400: string
    500: string
    600: string
    700: string
    800: string
    900: string
    950: string
  }
  
  warning: {
    50: string
    100: string
    200: string
    300: string
    400: string
    500: string
    600: string
    700: string
    800: string
    900: string
    950: string
  }
  
  error: {
    50: string
    100: string
    200: string
    300: string
    400: string
    500: string
    600: string
    700: string
    800: string
    900: string
    950: string
  }
  
  info: {
    50: string
    100: string
    200: string
    300: string
    400: string
    500: string
    600: string
    700: string
    800: string
    900: string
    950: string
  }
}

export interface ThemeTypography {
  fontFamily: {
    sans: string[]
    serif: string[]
    mono: string[]
    display: string[]
  }
  
  fontSize: {
    xs: [string, { lineHeight: string; letterSpacing?: string }]
    sm: [string, { lineHeight: string; letterSpacing?: string }]
    base: [string, { lineHeight: string; letterSpacing?: string }]
    lg: [string, { lineHeight: string; letterSpacing?: string }]
    xl: [string, { lineHeight: string; letterSpacing?: string }]
    '2xl': [string, { lineHeight: string; letterSpacing?: string }]
    '3xl': [string, { lineHeight: string; letterSpacing?: string }]
    '4xl': [string, { lineHeight: string; letterSpacing?: string }]
    '5xl': [string, { lineHeight: string; letterSpacing?: string }]
    '6xl': [string, { lineHeight: string; letterSpacing?: string }]
    '7xl': [string, { lineHeight: string; letterSpacing?: string }]
    '8xl': [string, { lineHeight: string; letterSpacing?: string }]
    '9xl': [string, { lineHeight: string; letterSpacing?: string }]
  }
  
  fontWeight: {
    thin: string
    extralight: string
    light: string
    normal: string
    medium: string
    semibold: string
    bold: string
    extrabold: string
    black: string
  }
}

export interface ThemeSpacing {
  px: string
  0: string
  0.5: string
  1: string
  1.5: string
  2: string
  2.5: string
  3: string
  3.5: string
  4: string
  5: string
  6: string
  7: string
  8: string
  9: string
  10: string
  11: string
  12: string
  14: string
  16: string
  20: string
  24: string
  28: string
  32: string
  36: string
  40: string
  44: string
  48: string
  52: string
  56: string
  60: string
  64: string
  72: string
  80: string
  96: string
}

export interface ThemeBorderRadius {
  none: string
  sm: string
  DEFAULT: string
  md: string
  lg: string
  xl: string
  '2xl': string
  '3xl': string
  full: string
}

export interface ThemeShadows {
  sm: string
  DEFAULT: string
  md: string
  lg: string
  xl: string
  '2xl': string
  inner: string
  none: string
}

export interface ThemeConfig {
  id: string
  name: string
  description?: string
  version: string
  author?: string
  
  // Core theme properties
  colors: ThemeColors
  typography: ThemeTypography
  spacing: ThemeSpacing
  borderRadius: ThemeBorderRadius
  shadows: ThemeShadows
  
  // Component-specific overrides
  components: {
    button: ComponentTheme
    card: ComponentTheme
    input: ComponentTheme
    badge: ComponentTheme
    alert: ComponentTheme
    dialog: ComponentTheme
    dropdown: ComponentTheme
    navigation: ComponentTheme
    form: ComponentTheme
    table: ComponentTheme
  }
  
  // Dark mode support
  darkMode: {
    enabled: boolean
    colors: Partial<ThemeColors>
    components: Partial<ThemeConfig['components']>
  }
  
  // Animation settings
  animations: {
    duration: {
      fast: string
      normal: string
      slow: string
    }
    easing: {
      linear: string
      ease: string
      easeIn: string
      easeOut: string
      easeInOut: string
    }
  }
  
  // Responsive breakpoints
  breakpoints: {
    sm: string
    md: string
    lg: string
    xl: string
    '2xl': string
  }
  
  // Theme metadata
  metadata: {
    createdAt: Date
    updatedAt: Date
    isDefault: boolean
    isActive: boolean
    category: 'business' | 'creative' | 'minimal' | 'bold' | 'elegant' | 'modern' | 'classic'
    tags: string[]
    preview: string // Preview image URL
  }
}

export interface ComponentTheme {
  base: string // Base classes
  variants: {
    [variantName: string]: {
      [variantValue: string]: string
    }
  }
  sizes: {
    [sizeName: string]: string
  }
  states: {
    default: string
    hover: string
    focus: string
    active: string
    disabled: string
  }
  // Custom CSS variables for this component
  cssVariables?: Record<string, string>
}

export interface ThemePreset {
  id: string
  name: string
  description: string
  category: ThemeConfig['metadata']['category']
  preview: string
  config: Partial<ThemeConfig>
}

export interface ThemeApplication {
  // Where the theme is applied
  scope: 'global' | 'page' | 'block' | 'component'
  targetId?: string // Page ID, Block ID, or Component ID
  
  // Theme configuration
  themeId: string
  customizations?: Partial<ThemeConfig>
  
  // Application metadata
  appliedAt: Date
  appliedBy: string
  isActive: boolean
}

export interface ThemeGeneratorOptions {
  baseColor: string // Primary color to generate theme from
  style: 'minimal' | 'bold' | 'elegant' | 'modern' | 'playful' | 'professional'
  contrast: 'low' | 'medium' | 'high'
  saturation: 'muted' | 'normal' | 'vibrant'
  borderRadius: 'sharp' | 'rounded' | 'pill'
  fontPairing: 'classic' | 'modern' | 'creative' | 'technical'
  spacing: 'compact' | 'normal' | 'spacious'
}

export type ThemeMode = 'light' | 'dark' | 'auto'

export interface ThemeContextValue {
  currentTheme: ThemeConfig | null
  themes: ThemeConfig[]
  mode: ThemeMode
  isLoading: boolean
  
  // Theme management
  setTheme: (themeId: string) => Promise<void>
  setMode: (mode: ThemeMode) => void
  generateTheme: (options: ThemeGeneratorOptions) => Promise<ThemeConfig>
  saveTheme: (theme: ThemeConfig) => Promise<void>
  deleteTheme: (themeId: string) => Promise<void>
  
  // Theme application
  applyTheme: (scope: ThemeApplication['scope'], targetId?: string) => Promise<void>
  removeTheme: (scope: ThemeApplication['scope'], targetId?: string) => Promise<void>
  
  // Utilities
  getCSSVariables: (theme?: ThemeConfig) => Record<string, string>
  getComponentClasses: (component: string, variant?: string, size?: string) => string
}
