import { prisma } from '@/lib/prisma'
import { SiteSettingsService } from '@/lib/site-settings/site-settings-service'

export interface RouteResolution {
  type: 'page' | 'post' | 'archive' | 'notfound'
  data?: any
  metadata?: {
    title?: string
    description?: string
    canonical?: string
    noindex?: boolean
  }
}

export interface ResolvedPage {
  id: string
  title: string
  slug: string
  description?: string | null
  status: string
  type: string
  template?: string | null
  seoTitle?: string | null
  seoDescription?: string | null
  seoKeywords: string[]
  ogImage?: string | null
  publishedAt?: Date | null
  scheduledAt?: Date | null
  expiresAt?: Date | null
  isHomePage: boolean
  isLandingPage: boolean
  requiresAuth: boolean
  allowComments: boolean
  metadata?: any
  customCss?: string | null
  customJs?: string | null
  createdBy?: string | null
  updatedBy?: string | null
  createdAt: Date
  updatedAt: Date
  blocks: Array<{
    id: string
    pageId: string
    blockType: string
    position: number
    isVisible: boolean
    configuration: any
    content?: any
    styling?: any
    responsive?: any
    animation?: any
    conditions?: any
    createdAt: Date
    updatedAt: Date
  }>
}

export interface ResolvedPost {
  id: string
  title: string
  slug: string
  content?: string | null
  contentHtml?: string | null
  excerpt?: string | null
  status: string
  postType: string
  parentId?: string | null
  menuOrder: number
  featuredImage?: string | null
  featuredImageAlt?: string | null
  template?: string | null
  password?: string | null
  publishedAt?: Date | null
  scheduledAt?: Date | null
  authorId?: string | null
  authorName?: string | null
  authorEmail?: string | null
  seoTitle?: string | null
  seoDescription?: string | null
  seoKeywords: string[]
  ogImage?: string | null
  ogTitle?: string | null
  ogDescription?: string | null
  twitterCard?: string | null
  canonicalUrl?: string | null
  metaRobots?: string | null
  viewCount: number
  shareCount: number
  likeCount: number
  commentCount: number
  usePageBuilder: boolean
  pageBuilderData?: any
  allowComments: boolean
  allowPingbacks: boolean
  isSticky: boolean
  isFeatured: boolean
  customFields?: any
  metadata?: any
  createdAt: Date
  updatedAt: Date
  postTypeData?: {
    id: string
    name: string
    label: string
    labelPlural: string
    description?: string | null
    icon?: string | null
    isPublic: boolean
    isHierarchical: boolean
    hasArchive: boolean
    supportsTitle: boolean
    supportsContent: boolean
    supportsExcerpt: boolean
    supportsThumbnail: boolean
    supportsComments: boolean
    supportsRevisions: boolean
    supportsPageBuilder: boolean
    menuPosition?: number | null
    capabilities?: any
    taxonomies: string[]
    customFields?: any
    templates: string[]
    isSystem: boolean
    isActive: boolean
    createdAt: Date
    updatedAt: Date
  }
  taxonomyTerms?: Array<{
    id: string
    postId: string
    termId: string
    createdAt: Date
    term: {
      id: string
      name: string
      slug: string
      description?: string | null
      parentId?: string | null
      taxonomyId: string
      metadata?: any
      createdAt: Date
      updatedAt: Date
      taxonomy: {
        id: string
        name: string
        label: string
        labelPlural: string
        description?: string | null
        isHierarchical: boolean
        isPublic: boolean
        capabilities?: any
        isSystem: boolean
        isActive: boolean
        createdAt: Date
        updatedAt: Date
      }
    }
  }>
}

export class RouteResolver {
  private static cache = new Map<string, { data: RouteResolution, timestamp: number }>()
  private static pageDataCache = new Map<string, { data: ResolvedPage, timestamp: number }>()
  private static CACHE_TTL = 5 * 60 * 1000 // 5 minutes

  /**
   * Resolve a route path to determine what content to render
   */
  static async resolve(pathname: string): Promise<RouteResolution> {
    // Check cache first
    const cached = this.cache.get(pathname)
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data
    }

    const pathSegments = pathname.split('/').filter(Boolean)
    let resolution: RouteResolution

    try {
      if (pathSegments.length === 0) {
        // Root path - check for home page
        resolution = await this.resolveHomePage()
      } else if (pathSegments.length === 1) {
        // Single segment - could be page, post, or archive
        resolution = await this.resolveSingleSegment(pathSegments[0])
      } else if (pathSegments.length === 2) {
        // Two segments - likely post type + post slug
        resolution = await this.resolvePostTypeRoute(pathSegments[0], pathSegments[1])
      } else {
        // Multiple segments - hierarchical pages or complex routes
        resolution = await this.resolveHierarchicalRoute(pathSegments)
      }

      // Cache the result
      this.cache.set(pathname, {
        data: resolution,
        timestamp: Date.now()
      })

      return resolution

    } catch (error) {
      console.error('Route resolution error:', error)
      return { type: 'notfound' }
    }
  }

  /**
   * Get full page data for rendering with caching
   */
  static async getPageData(pageId: string): Promise<ResolvedPage | null> {
    // Check cache first with separate cache for page data
    const cacheKey = `pageData:${pageId}`
    const cached = this.pageDataCache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data
    }

    try {
      const page = await prisma.page.findUnique({
        where: {
          id: pageId,
          status: 'published' // Only fetch published pages
        },
        include: {
          blocks: {
            orderBy: { position: 'asc' },
            where: { isVisible: true }
          }
        }
      })

      if (!page) {
        return null
      }

      const resolvedPage = page as ResolvedPage

      // Cache the result
      this.pageDataCache.set(cacheKey, {
        data: resolvedPage,
        timestamp: Date.now()
      })

      return resolvedPage
    } catch (error) {
      console.error('Error fetching page data:', error)
      return null
    }
  }

  /**
   * Get full post data for rendering
   */
  static async getPostData(postId: string): Promise<ResolvedPost | null> {
    try {
      const post = await prisma.post.findUnique({
        where: { id: postId },
        include: {
          taxonomyTerms: {
            include: {
              term: {
                include: {
                  taxonomy: true
                }
              }
            }
          },
          postTypeRef: true
        }
      })

      return post as ResolvedPost
    } catch (error) {
      console.error('Error fetching post data:', error)
      return null
    }
  }

  /**
   * Get archive data for post type listing
   */
  static async getArchiveData(postTypeName: string, page: number = 1, limit: number = 10) {
    try {
      const postType = await prisma.postType.findUnique({
        where: { name: postTypeName }
      })

      if (!postType) return null

      const offset = (page - 1) * limit

      const [posts, totalCount] = await Promise.all([
        prisma.post.findMany({
          where: {
            postType: postTypeName,
            status: 'published'
          },
          include: {
            taxonomyTerms: {
              include: {
                term: {
                  include: {
                    taxonomy: true
                  }
                }
              }
            }
          },
          orderBy: { publishedAt: 'desc' },
          skip: offset,
          take: limit
        }),
        prisma.post.count({
          where: {
            postType: postTypeName,
            status: 'published'
          }
        })
      ])

      return {
        postType,
        posts,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit)
        }
      }
    } catch (error) {
      console.error('Error fetching archive data:', error)
      return null
    }
  }

  private static async resolveHomePage(): Promise<RouteResolution> {
    try {
      // Check for custom homepage in site settings first
      const siteSettingsService = new SiteSettingsService()
      const homepage = await siteSettingsService.getHomepage()

      if (homepage?.pageId) {
        const customHomePage = await prisma.page.findUnique({
          where: {
            id: homepage.pageId,
            status: 'published'
          },
          select: {
            id: true,
            slug: true,
            title: true,
            seoTitle: true,
            seoDescription: true
          }
        })

        if (customHomePage) {
          return {
            type: 'page',
            data: customHomePage,
            metadata: {
              title: customHomePage.seoTitle || customHomePage.title,
              description: customHomePage.seoDescription || undefined,
              canonical: '/'
            }
          }
        }
      }
    } catch (error) {
      console.error('Error checking custom homepage:', error)
      // Continue to fallback
    }

    // Fallback: Check for designated home page (legacy)
    const homePage = await prisma.page.findFirst({
      where: {
        isHomePage: true,
        status: 'published'
      },
      select: {
        id: true,
        slug: true,
        title: true,
        seoTitle: true,
        seoDescription: true
      }
    })

    if (homePage) {
      return {
        type: 'page',
        data: homePage,
        metadata: {
          title: homePage.seoTitle || homePage.title,
          description: homePage.seoDescription || undefined,
          canonical: '/'
        }
      }
    }

    return { type: 'notfound' }
  }

  private static async resolveSingleSegment(slug: string): Promise<RouteResolution> {
    // Priority 1: Check for page
    const page = await prisma.page.findUnique({
      where: { slug, status: 'published' },
      select: {
        id: true,
        slug: true,
        title: true,
        seoTitle: true,
        seoDescription: true
      }
    })

    if (page) {
      return {
        type: 'page',
        data: page,
        metadata: {
          title: page.seoTitle || page.title,
          description: page.seoDescription || undefined,
          canonical: `/${slug}`
        }
      }
    }

    // Priority 2: Check for post type archive
    const postType = await prisma.postType.findUnique({
      where: {
        name: slug,
        isActive: true,
        isPublic: true,
        hasArchive: true
      },
      select: {
        id: true,
        name: true,
        label: true,
        description: true
      }
    })

    if (postType) {
      return {
        type: 'archive',
        data: postType,
        metadata: {
          title: `${postType.label} Archive`,
          description: postType.description || `Browse all ${postType.label.toLowerCase()}`,
          canonical: `/${slug}`
        }
      }
    }

    // Priority 3: Check for post (default post type)
    const post = await prisma.post.findUnique({
      where: { slug, status: 'published' },
      select: {
        id: true,
        slug: true,
        title: true,
        excerpt: true,
        seoTitle: true,
        seoDescription: true,
        postType: true
      }
    })

    if (post) {
      return {
        type: 'post',
        data: post,
        metadata: {
          title: post.seoTitle || post.title,
          description: post.seoDescription || post.excerpt || undefined,
          canonical: `/${slug}`
        }
      }
    }

    return { type: 'notfound' }
  }

  private static async resolvePostTypeRoute(postTypeName: string, postSlug: string): Promise<RouteResolution> {
    // Verify post type exists and is public
    const postType = await prisma.postType.findUnique({
      where: {
        name: postTypeName,
        isActive: true,
        isPublic: true
      }
    })

    if (!postType) {
      return { type: 'notfound' }
    }

    // Find the post
    const post = await prisma.post.findUnique({
      where: {
        slug: postSlug,
        postType: postTypeName,
        status: 'published'
      },
      select: {
        id: true,
        slug: true,
        title: true,
        excerpt: true,
        seoTitle: true,
        seoDescription: true,
        postType: true
      }
    })

    if (post) {
      return {
        type: 'post',
        data: { ...post, postTypeData: postType },
        metadata: {
          title: post.seoTitle || post.title,
          description: post.seoDescription || post.excerpt || undefined,
          canonical: `/${postTypeName}/${postSlug}`
        }
      }
    }

    return { type: 'notfound' }
  }

  private static async resolveHierarchicalRoute(pathSegments: string[]): Promise<RouteResolution> {
    // For now, we'll implement basic hierarchical page support
    // This can be extended for more complex hierarchical structures
    
    const fullSlug = pathSegments.join('/')
    
    // Check if there's a page with this full slug
    const page = await prisma.page.findUnique({
      where: { slug: fullSlug, status: 'published' },
      select: {
        id: true,
        slug: true,
        title: true,
        seoTitle: true,
        seoDescription: true
      }
    })

    if (page) {
      return {
        type: 'page',
        data: page,
        metadata: {
          title: page.seoTitle || page.title,
          description: page.seoDescription || undefined,
          canonical: `/${fullSlug}`
        }
      }
    }

    return { type: 'notfound' }
  }

  /**
   * Clear the route cache
   */
  static clearCache(): void {
    this.cache.clear()
  }

  /**
   * Generate sitemap data
   */
  static async generateSitemapData() {
    try {
      const [pages, posts, postTypes] = await Promise.all([
        prisma.page.findMany({
          where: { status: 'published' },
          select: {
            slug: true,
            updatedAt: true,
            type: true
          }
        }),
        prisma.post.findMany({
          where: { status: 'published' },
          select: {
            slug: true,
            postType: true,
            updatedAt: true
          }
        }),
        prisma.postType.findMany({
          where: {
            isActive: true,
            isPublic: true,
            hasArchive: true
          },
          select: {
            name: true,
            updatedAt: true
          }
        })
      ])

      const sitemapEntries = [
        // Pages
        ...pages.map(page => ({
          url: `/${page.slug}`,
          lastModified: page.updatedAt,
          changeFrequency: 'weekly' as const,
          priority: page.type === 'home' ? 1.0 : 0.8
        })),
        
        // Posts
        ...posts.map(post => ({
          url: post.postType === 'post' ? `/${post.slug}` : `/${post.postType}/${post.slug}`,
          lastModified: post.updatedAt,
          changeFrequency: 'monthly' as const,
          priority: 0.6
        })),
        
        // Post type archives
        ...postTypes.map(postType => ({
          url: `/${postType.name}`,
          lastModified: postType.updatedAt,
          changeFrequency: 'daily' as const,
          priority: 0.7
        }))
      ]

      return sitemapEntries
    } catch (error) {
      console.error('Error generating sitemap data:', error)
      return []
    }
  }
}
