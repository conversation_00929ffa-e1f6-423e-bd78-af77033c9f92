import { LayoutStructure, LayoutStyling, ResponsiveSettings } from '../types'

export class LayoutTemplateGenerator {
  /**
   * Generate Modern E-commerce Layout
   */
  static generateModernEcommerce(): {
    structure: LayoutStructure
    styling: LayoutStyling
    responsive: ResponsiveSettings
  } {
    return {
      structure: {
        header: {
          id: 'header-modern-ecommerce',
          type: 'header',
          name: 'Modern E-commerce Header',
          position: 1,
          blocks: [
            {
              id: 'logo-modern',
              type: 'logo',
              name: 'Brand Logo',
              position: 1,
              configuration: { size: 'large', alignment: 'left' },
              content: { 
                text: 'Your Store',
                image: '/logo-placeholder.png'
              },
              styling: {
                typography: { fontSize: '24px', fontWeight: 'bold' },
                colors: { text: '#1a1a1a' }
              },
              responsive: {
                mobile: { typography: { fontSize: '20px' } },
                tablet: { typography: { fontSize: '22px' } },
                desktop: { typography: { fontSize: '24px' } },
                large: { typography: { fontSize: '26px' } }
              },
              conditions: {},
              isVisible: true
            },
            {
              id: 'search-modern',
              type: 'search',
              name: 'Product Search',
              position: 2,
              configuration: { size: 'large', alignment: 'center' },
              content: { 
                placeholder: 'Search products...',
                showSuggestions: true
              },
              styling: {
                border: { radius: '8px' },
                spacing: { top: '8px', right: '16px', bottom: '8px', left: '16px' }
              },
              responsive: {
                mobile: { display: 'none' },
                tablet: { display: 'block', width: '300px' },
                desktop: { display: 'block', width: '400px' },
                large: { display: 'block', width: '500px' }
              },
              conditions: {},
              isVisible: true
            },
            {
              id: 'nav-modern',
              type: 'navigation',
              name: 'Main Navigation',
              position: 3,
              configuration: { style: 'horizontal', showIcons: false },
              content: { menu: 'main-menu' },
              styling: {
                typography: { fontSize: '16px', fontWeight: '500' },
                spacing: { top: '0', right: '24px', bottom: '0', left: '24px' }
              },
              responsive: {
                mobile: { display: 'none' },
                tablet: { display: 'flex' },
                desktop: { display: 'flex' },
                large: { display: 'flex' }
              },
              conditions: {},
              isVisible: true
            },
            {
              id: 'cart-modern',
              type: 'cart',
              name: 'Shopping Cart',
              position: 4,
              configuration: { size: 'medium', alignment: 'right', showCounter: true },
              content: {},
              styling: {
                colors: { primary: '#ff6b6b' }
              },
              responsive: {
                mobile: { display: 'block' },
                tablet: { display: 'block' },
                desktop: { display: 'block' },
                large: { display: 'block' }
              },
              conditions: {},
              isVisible: true
            }
          ],
          configuration: { 
            layout: 'flex', 
            alignment: 'justify',
            spacing: { top: '16px', right: '32px', bottom: '16px', left: '32px' }
          },
          styling: { 
            background: { type: 'color', color: '#ffffff' },
            border: { width: '0 0 1px 0', style: 'solid', color: '#e5e7eb', radius: '0' },
            shadow: { type: 'box', x: 0, y: 2, blur: 4, spread: 0, color: 'rgba(0,0,0,0.1)' }
          },
          responsive: {
            mobile: { spacing: { top: '12px', right: '16px', bottom: '12px', left: '16px' } },
            tablet: { spacing: { top: '16px', right: '24px', bottom: '16px', left: '24px' } },
            desktop: { spacing: { top: '16px', right: '32px', bottom: '16px', left: '32px' } },
            large: { spacing: { top: '20px', right: '40px', bottom: '20px', left: '40px' } }
          },
          isVisible: true
        },
        main: {
          id: 'main-modern-ecommerce',
          type: 'main',
          name: 'Main Content Area',
          position: 2,
          blocks: [
            {
              id: 'content-modern',
              type: 'content',
              name: 'Page Content',
              position: 1,
              configuration: { layout: 'container' },
              content: {},
              styling: {
                spacing: { top: '32px', right: '0', bottom: '32px', left: '0' }
              },
              responsive: {
                mobile: { spacing: { top: '24px', right: '0', bottom: '24px', left: '0' } },
                tablet: { spacing: { top: '32px', right: '0', bottom: '32px', left: '0' } },
                desktop: { spacing: { top: '32px', right: '0', bottom: '32px', left: '0' } },
                large: { spacing: { top: '40px', right: '0', bottom: '40px', left: '0' } }
              },
              conditions: {},
              isVisible: true
            }
          ],
          configuration: { layout: 'block' },
          styling: {
            background: { type: 'color', color: '#ffffff' }
          },
          responsive: {
            mobile: { display: 'block' },
            tablet: { display: 'block' },
            desktop: { display: 'block' },
            large: { display: 'block' }
          },
          isVisible: true
        },
        footer: {
          id: 'footer-modern-ecommerce',
          type: 'footer',
          name: 'Modern Footer',
          position: 3,
          blocks: [
            {
              id: 'links-footer-modern',
              type: 'links',
              name: 'Footer Links',
              position: 1,
              configuration: { layout: 'grid', columns: 4 },
              content: { 
                menu: 'footer-menu',
                sections: [
                  { title: 'Shop', links: ['New Arrivals', 'Best Sellers', 'Sale'] },
                  { title: 'Support', links: ['Help Center', 'Contact Us', 'Returns'] },
                  { title: 'Company', links: ['About Us', 'Careers', 'Press'] },
                  { title: 'Legal', links: ['Privacy Policy', 'Terms of Service'] }
                ]
              },
              styling: {
                typography: { fontSize: '14px' },
                spacing: { top: '0', right: '0', bottom: '32px', left: '0' }
              },
              responsive: {
                mobile: { configuration: { columns: 2 } },
                tablet: { configuration: { columns: 3 } },
                desktop: { configuration: { columns: 4 } },
                large: { configuration: { columns: 4 } }
              },
              conditions: {},
              isVisible: true
            },
            {
              id: 'social-footer-modern',
              type: 'social',
              name: 'Social Media Links',
              position: 2,
              configuration: { alignment: 'center', style: 'icons' },
              content: {
                links: [
                  { id: 'facebook', text: 'Facebook', url: '#', icon: '📘' },
                  { id: 'instagram', text: 'Instagram', url: '#', icon: '📷' },
                  { id: 'twitter', text: 'Twitter', url: '#', icon: '🐦' },
                  { id: 'youtube', text: 'YouTube', url: '#', icon: '📺' }
                ]
              },
              styling: {
                spacing: { top: '0', right: '0', bottom: '24px', left: '0' }
              },
              responsive: {
                mobile: { display: 'flex' },
                tablet: { display: 'flex' },
                desktop: { display: 'flex' },
                large: { display: 'flex' }
              },
              conditions: {},
              isVisible: true
            },
            {
              id: 'copyright-footer-modern',
              type: 'copyright',
              name: 'Copyright Notice',
              position: 3,
              configuration: { alignment: 'center' },
              content: { 
                text: '© 2024 Your Store. All rights reserved.',
                showYear: true
              },
              styling: {
                typography: { fontSize: '12px' },
                colors: { text: '#6b7280' }
              },
              responsive: {
                mobile: { display: 'block' },
                tablet: { display: 'block' },
                desktop: { display: 'block' },
                large: { display: 'block' }
              },
              conditions: {},
              isVisible: true
            }
          ],
          configuration: { 
            layout: 'block',
            spacing: { top: '48px', right: '32px', bottom: '32px', left: '32px' }
          },
          styling: { 
            background: { type: 'color', color: '#f9fafb' },
            border: { width: '1px 0 0 0', style: 'solid', color: '#e5e7eb', radius: '0' }
          },
          responsive: {
            mobile: { spacing: { top: '32px', right: '16px', bottom: '24px', left: '16px' } },
            tablet: { spacing: { top: '40px', right: '24px', bottom: '28px', left: '24px' } },
            desktop: { spacing: { top: '48px', right: '32px', bottom: '32px', left: '32px' } },
            large: { spacing: { top: '56px', right: '40px', bottom: '36px', left: '40px' } }
          },
          isVisible: true
        }
      },
      styling: {
        theme: 'modern-ecommerce',
        colorScheme: 'light',
        typography: {
          fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
          fontSize: '16px',
          fontWeight: '400',
          lineHeight: '1.6',
          letterSpacing: '0',
          textAlign: 'left',
          textTransform: 'none'
        },
        spacing: { top: 0, right: 0, bottom: 0, left: 0 },
        colors: {
          primary: '#3b82f6',
          secondary: '#1f2937',
          accent: '#ff6b6b',
          text: '#1f2937',
          background: '#ffffff',
          border: '#e5e7eb'
        }
      },
      responsive: {
        mobile: { 
          display: 'block', 
          width: '100%', 
          height: 'auto', 
          spacing: { top: 0, right: 16, bottom: 0, left: 16 }, 
          typography: { fontSize: '14px' } 
        },
        tablet: { 
          display: 'block', 
          width: '100%', 
          height: 'auto', 
          spacing: { top: 0, right: 24, bottom: 0, left: 24 }, 
          typography: { fontSize: '15px' } 
        },
        desktop: { 
          display: 'block', 
          width: '100%', 
          height: 'auto', 
          spacing: { top: 0, right: 32, bottom: 0, left: 32 }, 
          typography: { fontSize: '16px' } 
        },
        large: { 
          display: 'block', 
          width: '100%', 
          height: 'auto', 
          spacing: { top: 0, right: 40, bottom: 0, left: 40 }, 
          typography: { fontSize: '16px' } 
        }
      }
    }
  }

  /**
   * Generate Minimal Blog Layout
   */
  static generateMinimalBlog(): {
    structure: LayoutStructure
    styling: LayoutStyling
    responsive: ResponsiveSettings
  } {
    return {
      structure: {
        header: {
          id: 'header-minimal-blog',
          type: 'header',
          name: 'Minimal Blog Header',
          position: 1,
          blocks: [
            {
              id: 'logo-blog',
              type: 'logo',
              name: 'Blog Title',
              position: 1,
              configuration: { size: 'large', alignment: 'center' },
              content: { 
                text: 'My Blog',
                subtitle: 'Thoughts, stories and ideas'
              },
              styling: {
                typography: { 
                  fontSize: '32px', 
                  fontWeight: '700',
                  textAlign: 'center'
                },
                colors: { text: '#1f2937' }
              },
              responsive: {
                mobile: { typography: { fontSize: '24px' } },
                tablet: { typography: { fontSize: '28px' } },
                desktop: { typography: { fontSize: '32px' } },
                large: { typography: { fontSize: '36px' } }
              },
              conditions: {},
              isVisible: true
            },
            {
              id: 'nav-blog',
              type: 'navigation',
              name: 'Blog Navigation',
              position: 2,
              configuration: { style: 'horizontal', alignment: 'center' },
              content: { menu: 'blog-menu' },
              styling: {
                typography: { fontSize: '16px', fontWeight: '500' },
                spacing: { top: '16px', right: '0', bottom: '0', left: '0' }
              },
              responsive: {
                mobile: { display: 'block' },
                tablet: { display: 'block' },
                desktop: { display: 'block' },
                large: { display: 'block' }
              },
              conditions: {},
              isVisible: true
            }
          ],
          configuration: { 
            layout: 'block',
            alignment: 'center',
            spacing: { top: '48px', right: '32px', bottom: '48px', left: '32px' }
          },
          styling: { 
            background: { type: 'color', color: '#ffffff' },
            border: { width: '0 0 1px 0', style: 'solid', color: '#f3f4f6', radius: '0' }
          },
          responsive: {
            mobile: { spacing: { top: '32px', right: '16px', bottom: '32px', left: '16px' } },
            tablet: { spacing: { top: '40px', right: '24px', bottom: '40px', left: '24px' } },
            desktop: { spacing: { top: '48px', right: '32px', bottom: '48px', left: '32px' } },
            large: { spacing: { top: '56px', right: '40px', bottom: '56px', left: '40px' } }
          },
          isVisible: true
        },
        main: {
          id: 'main-blog',
          type: 'main',
          name: 'Blog Content',
          position: 2,
          blocks: [
            {
              id: 'content-blog',
              type: 'content',
              name: 'Article Content',
              position: 1,
              configuration: { layout: 'article', maxWidth: '800px' },
              content: {},
              styling: {
                spacing: { top: '48px', right: '32px', bottom: '48px', left: '32px' },
                typography: { 
                  fontSize: '18px',
                  lineHeight: '1.8',
                  fontFamily: 'Georgia, serif'
                }
              },
              responsive: {
                mobile: { 
                  spacing: { top: '32px', right: '16px', bottom: '32px', left: '16px' },
                  typography: { fontSize: '16px' }
                },
                tablet: { 
                  spacing: { top: '40px', right: '24px', bottom: '40px', left: '24px' },
                  typography: { fontSize: '17px' }
                },
                desktop: { 
                  spacing: { top: '48px', right: '32px', bottom: '48px', left: '32px' },
                  typography: { fontSize: '18px' }
                },
                large: { 
                  spacing: { top: '56px', right: '40px', bottom: '56px', left: '40px' },
                  typography: { fontSize: '18px' }
                }
              },
              conditions: {},
              isVisible: true
            }
          ],
          configuration: { layout: 'block' },
          styling: {
            background: { type: 'color', color: '#ffffff' }
          },
          responsive: {
            mobile: { display: 'block' },
            tablet: { display: 'block' },
            desktop: { display: 'block' },
            large: { display: 'block' }
          },
          isVisible: true
        },
        sidebar: {
          id: 'sidebar-blog',
          type: 'sidebar',
          name: 'Blog Sidebar',
          position: 3,
          blocks: [
            {
              id: 'widget-recent-posts',
              type: 'widget',
              name: 'Recent Posts',
              position: 1,
              configuration: { widget: 'recent-posts' },
              content: { widget: 'sidebar-primary' },
              styling: {
                spacing: { top: '0', right: '0', bottom: '32px', left: '0' }
              },
              responsive: {
                mobile: { display: 'none' },
                tablet: { display: 'none' },
                desktop: { display: 'block' },
                large: { display: 'block' }
              },
              conditions: {},
              isVisible: true
            }
          ],
          configuration: { 
            layout: 'block',
            spacing: { top: '48px', right: '32px', bottom: '48px', left: '32px' }
          },
          styling: { 
            background: { type: 'color', color: '#f9fafb' },
            border: { width: '1px 0 0 1px', style: 'solid', color: '#f3f4f6', radius: '0' }
          },
          responsive: {
            mobile: { display: 'none' },
            tablet: { display: 'none' },
            desktop: { display: 'block', width: '300px' },
            large: { display: 'block', width: '320px' }
          },
          isVisible: true
        },
        footer: {
          id: 'footer-blog',
          type: 'footer',
          name: 'Simple Footer',
          position: 4,
          blocks: [
            {
              id: 'copyright-blog',
              type: 'copyright',
              name: 'Copyright',
              position: 1,
              configuration: { alignment: 'center' },
              content: { 
                text: '© 2024 My Blog. Made with ❤️'
              },
              styling: {
                typography: { fontSize: '14px' },
                colors: { text: '#6b7280' }
              },
              responsive: {
                mobile: { display: 'block' },
                tablet: { display: 'block' },
                desktop: { display: 'block' },
                large: { display: 'block' }
              },
              conditions: {},
              isVisible: true
            }
          ],
          configuration: { 
            layout: 'block',
            alignment: 'center',
            spacing: { top: '32px', right: '32px', bottom: '32px', left: '32px' }
          },
          styling: { 
            background: { type: 'color', color: '#f9fafb' },
            border: { width: '1px 0 0 0', style: 'solid', color: '#f3f4f6', radius: '0' }
          },
          responsive: {
            mobile: { spacing: { top: '24px', right: '16px', bottom: '24px', left: '16px' } },
            tablet: { spacing: { top: '28px', right: '24px', bottom: '28px', left: '24px' } },
            desktop: { spacing: { top: '32px', right: '32px', bottom: '32px', left: '32px' } },
            large: { spacing: { top: '36px', right: '40px', bottom: '36px', left: '40px' } }
          },
          isVisible: true
        }
      },
      styling: {
        theme: 'minimal-blog',
        colorScheme: 'light',
        typography: {
          fontFamily: 'Georgia, "Times New Roman", serif',
          fontSize: '18px',
          fontWeight: '400',
          lineHeight: '1.8',
          letterSpacing: '0',
          textAlign: 'left',
          textTransform: 'none'
        },
        spacing: { top: 0, right: 0, bottom: 0, left: 0 },
        colors: {
          primary: '#1f2937',
          secondary: '#6b7280',
          accent: '#3b82f6',
          text: '#1f2937',
          background: '#ffffff',
          border: '#f3f4f6'
        }
      },
      responsive: {
        mobile: { 
          display: 'block', 
          width: '100%', 
          height: 'auto', 
          spacing: { top: 0, right: 16, bottom: 0, left: 16 }, 
          typography: { fontSize: '16px' } 
        },
        tablet: { 
          display: 'block', 
          width: '100%', 
          height: 'auto', 
          spacing: { top: 0, right: 24, bottom: 0, left: 24 }, 
          typography: { fontSize: '17px' } 
        },
        desktop: { 
          display: 'block', 
          width: '100%', 
          height: 'auto', 
          spacing: { top: 0, right: 32, bottom: 0, left: 32 }, 
          typography: { fontSize: '18px' } 
        },
        large: { 
          display: 'block', 
          width: '100%', 
          height: 'auto', 
          spacing: { top: 0, right: 40, bottom: 0, left: 40 }, 
          typography: { fontSize: '18px' } 
        }
      }
    }
  }

  /**
   * Get all available templates
   */
  static getAllTemplates() {
    return {
      'modern-ecommerce': this.generateModernEcommerce(),
      'minimal-blog': this.generateMinimalBlog()
      // Add more templates here
    }
  }
}
