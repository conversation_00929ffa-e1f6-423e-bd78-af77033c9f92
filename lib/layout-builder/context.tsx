'use client'

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react'
import { 
  LayoutBuilderState, 
  LayoutBuilderAction, 
  Layout, 
  LayoutSection, 
  LayoutBlock,
  SectionType,
  BlockType,
  LayoutStructure,
  LayoutStyling,
  ResponsiveSettings
} from './types'
import { generateId } from '../page-builder/utils'

// Initial state
const initialState: LayoutBuilderState = {
  layout: {
    id: '',
    name: 'New Layout',
    description: '',
    type: 'page',
    category: 'custom',
    structure: {
      main: {
        id: 'main-section',
        type: 'main',
        name: 'Main Content',
        position: 1,
        blocks: [],
        configuration: {
          layout: 'block',
          alignment: 'left',
          spacing: { top: '0', right: '0', bottom: '0', left: '0' },
          background: { type: 'none' },
          container: { maxWidth: '1200px', padding: '1rem' }
        },
        styling: {
          background: { type: 'none' },
          border: { width: '0', style: 'none', color: 'transparent' },
          spacing: { top: '0', right: '0', bottom: '0', left: '0' },
          shadow: { type: 'none' }
        },
        responsive: {
          mobile: { isVisible: true },
          tablet: { isVisible: true },
          desktop: { isVisible: true },
          large: { isVisible: true }
        },
        isVisible: true
      }
    },
    styling: {
      theme: 'default',
      colorScheme: 'light',
      typography: {
        fontFamily: 'Inter, sans-serif',
        fontSize: '16px',
        lineHeight: '1.5',
        fontWeight: 'normal'
      },
      spacing: { top: '0', right: '0', bottom: '0', left: '0' },
      colors: {
        primary: '#000000',
        secondary: '#666666',
        background: '#ffffff',
        text: '#000000'
      }
    },
    responsive: {
      mobile: { breakpoint: '768px', isVisible: true },
      tablet: { breakpoint: '1024px', isVisible: true },
      desktop: { breakpoint: '1280px', isVisible: true },
      large: { breakpoint: '1536px', isVisible: true }
    },
    conditions: {},
    isTemplate: false,
    isSystem: false,
    isActive: true,
    usageCount: 0,
    tags: [],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  selectedSectionId: null,
  selectedBlockId: null,
  isDragging: false,
  isPreviewMode: false,
  devicePreview: 'desktop',
  history: [],
  historyIndex: -1,
  isSaving: false,
  hasUnsavedChanges: false,
}

// Reducer
function layoutBuilderReducer(state: LayoutBuilderState, action: LayoutBuilderAction): LayoutBuilderState {
  switch (action.type) {
    case 'SET_LAYOUT':
      return {
        ...state,
        layout: action.payload,
        hasUnsavedChanges: false,
        history: [{
          structure: action.payload.structure,
          styling: action.payload.styling,
          responsive: action.payload.responsive,
          timestamp: Date.now()
        }],
        historyIndex: 0,
      }

    case 'UPDATE_LAYOUT':
      const updatedLayout = { ...state.layout, ...action.payload }
      return {
        ...state,
        layout: updatedLayout,
        hasUnsavedChanges: true,
      }

    case 'UPDATE_STRUCTURE':
      return {
        ...state,
        layout: {
          ...state.layout,
          structure: action.payload
        },
        hasUnsavedChanges: true,
      }

    case 'UPDATE_STYLING':
      return {
        ...state,
        layout: {
          ...state.layout,
          styling: action.payload
        },
        hasUnsavedChanges: true,
      }

    case 'UPDATE_RESPONSIVE':
      return {
        ...state,
        layout: {
          ...state.layout,
          responsive: action.payload
        },
        hasUnsavedChanges: true,
      }

    case 'ADD_SECTION': {
      const { sectionType, position } = action.payload
      const newSection: LayoutSection = {
        id: generateId(),
        type: sectionType,
        name: `${sectionType.charAt(0).toUpperCase() + sectionType.slice(1)} Section`,
        position: position ?? Object.keys(state.layout.structure).length + 1,
        blocks: [],
        configuration: {
          layout: 'block',
          alignment: 'left',
          spacing: { top: '0', right: '0', bottom: '0', left: '0' },
          background: { type: 'none' },
          container: { maxWidth: '1200px', padding: '1rem' }
        },
        styling: {
          background: { type: 'none' },
          border: { width: '0', style: 'none', color: 'transparent' },
          spacing: { top: '0', right: '0', bottom: '0', left: '0' },
          shadow: { type: 'none' }
        },
        responsive: {
          mobile: { isVisible: true },
          tablet: { isVisible: true },
          desktop: { isVisible: true },
          large: { isVisible: true }
        },
        isVisible: true
      }

      return {
        ...state,
        layout: {
          ...state.layout,
          structure: {
            ...state.layout.structure,
            [sectionType]: newSection
          }
        },
        hasUnsavedChanges: true,
      }
    }

    case 'UPDATE_SECTION': {
      const { id, updates } = action.payload
      const updatedStructure = { ...state.layout.structure }
      
      // Find and update the section
      Object.keys(updatedStructure).forEach(key => {
        if (updatedStructure[key]?.id === id) {
          updatedStructure[key] = { ...updatedStructure[key]!, ...updates }
        }
      })

      return {
        ...state,
        layout: {
          ...state.layout,
          structure: updatedStructure
        },
        hasUnsavedChanges: true,
      }
    }

    case 'DELETE_SECTION': {
      const sectionId = action.payload
      const updatedStructure = { ...state.layout.structure }
      
      // Find and remove the section
      Object.keys(updatedStructure).forEach(key => {
        if (updatedStructure[key]?.id === sectionId) {
          delete updatedStructure[key]
        }
      })

      return {
        ...state,
        layout: {
          ...state.layout,
          structure: updatedStructure
        },
        selectedSectionId: state.selectedSectionId === sectionId ? null : state.selectedSectionId,
        hasUnsavedChanges: true,
      }
    }

    case 'ADD_BLOCK': {
      const { sectionId, blockType, position } = action.payload
      const newBlock: LayoutBlock = {
        id: generateId(),
        type: blockType,
        name: `${blockType.charAt(0).toUpperCase() + blockType.slice(1)} Block`,
        position: position ?? 0,
        configuration: {
          size: 'medium',
          alignment: 'left',
          spacing: { top: '0', right: '0', bottom: '0', left: '0' },
          animation: { type: 'none' }
        },
        content: {},
        styling: {
          background: { type: 'none' },
          border: { width: '0', style: 'none', color: 'transparent' },
          spacing: { top: '0', right: '0', bottom: '0', left: '0' },
          typography: {
            fontFamily: 'inherit',
            fontSize: '16px',
            lineHeight: '1.5',
            fontWeight: 'normal'
          },
          colors: {
            primary: '#000000',
            secondary: '#666666',
            background: 'transparent',
            text: '#000000'
          },
          shadow: { type: 'none' }
        },
        responsive: {
          mobile: { isVisible: true },
          tablet: { isVisible: true },
          desktop: { isVisible: true },
          large: { isVisible: true }
        },
        conditions: {},
        isVisible: true
      }

      const updatedStructure = { ...state.layout.structure }
      
      // Find the section and add the block
      Object.keys(updatedStructure).forEach(key => {
        if (updatedStructure[key]?.id === sectionId) {
          const section = updatedStructure[key]!
          const blocks = [...section.blocks]
          if (position !== undefined) {
            blocks.splice(position, 0, newBlock)
          } else {
            blocks.push(newBlock)
          }
          updatedStructure[key] = { ...section, blocks }
        }
      })

      return {
        ...state,
        layout: {
          ...state.layout,
          structure: updatedStructure
        },
        hasUnsavedChanges: true,
      }
    }

    case 'UPDATE_BLOCK': {
      const { id, updates } = action.payload
      const updatedStructure = { ...state.layout.structure }
      
      // Find and update the block
      Object.keys(updatedStructure).forEach(sectionKey => {
        const section = updatedStructure[sectionKey]
        if (section) {
          const blockIndex = section.blocks.findIndex(block => block.id === id)
          if (blockIndex !== -1) {
            const updatedBlocks = [...section.blocks]
            updatedBlocks[blockIndex] = { ...updatedBlocks[blockIndex], ...updates }
            updatedStructure[sectionKey] = { ...section, blocks: updatedBlocks }
          }
        }
      })

      return {
        ...state,
        layout: {
          ...state.layout,
          structure: updatedStructure
        },
        hasUnsavedChanges: true,
      }
    }

    case 'DELETE_BLOCK': {
      const blockId = action.payload
      const updatedStructure = { ...state.layout.structure }
      
      // Find and remove the block
      Object.keys(updatedStructure).forEach(sectionKey => {
        const section = updatedStructure[sectionKey]
        if (section) {
          const updatedBlocks = section.blocks.filter(block => block.id !== blockId)
          updatedStructure[sectionKey] = { ...section, blocks: updatedBlocks }
        }
      })

      return {
        ...state,
        layout: {
          ...state.layout,
          structure: updatedStructure
        },
        selectedBlockId: state.selectedBlockId === blockId ? null : state.selectedBlockId,
        hasUnsavedChanges: true,
      }
    }

    case 'SELECT_SECTION':
      return {
        ...state,
        selectedSectionId: action.payload,
        selectedBlockId: null, // Clear block selection when selecting section
      }

    case 'SELECT_BLOCK':
      return {
        ...state,
        selectedBlockId: action.payload,
      }

    case 'SET_DRAGGING':
      return {
        ...state,
        isDragging: action.payload,
      }

    case 'SET_PREVIEW_MODE':
      return {
        ...state,
        isPreviewMode: action.payload,
      }

    case 'SET_DEVICE_PREVIEW':
      return {
        ...state,
        devicePreview: action.payload,
      }

    case 'SET_SAVING':
      return {
        ...state,
        isSaving: action.payload,
      }

    case 'SET_UNSAVED_CHANGES':
      return {
        ...state,
        hasUnsavedChanges: action.payload,
      }

    case 'SAVE_TO_HISTORY': {
      const newHistoryEntry = {
        structure: state.layout.structure,
        styling: state.layout.styling,
        responsive: state.layout.responsive,
        timestamp: Date.now()
      }

      const newHistory = state.history.slice(0, state.historyIndex + 1)
      newHistory.push(newHistoryEntry)

      // Limit history to 50 entries
      if (newHistory.length > 50) {
        newHistory.shift()
      }

      return {
        ...state,
        history: newHistory,
        historyIndex: newHistory.length - 1,
      }
    }

    case 'UNDO': {
      if (state.historyIndex > 0) {
        const previousEntry = state.history[state.historyIndex - 1]
        return {
          ...state,
          layout: {
            ...state.layout,
            structure: previousEntry.structure,
            styling: previousEntry.styling,
            responsive: previousEntry.responsive
          },
          historyIndex: state.historyIndex - 1,
          hasUnsavedChanges: true,
        }
      }
      return state
    }

    case 'REDO': {
      if (state.historyIndex < state.history.length - 1) {
        const nextEntry = state.history[state.historyIndex + 1]
        return {
          ...state,
          layout: {
            ...state.layout,
            structure: nextEntry.structure,
            styling: nextEntry.styling,
            responsive: nextEntry.responsive
          },
          historyIndex: state.historyIndex + 1,
          hasUnsavedChanges: true,
        }
      }
      return state
    }

    default:
      return state
  }
}

// Context
interface LayoutBuilderContextType {
  state: LayoutBuilderState
  dispatch: React.Dispatch<LayoutBuilderAction>

  // Helper functions
  addSection: (sectionType: SectionType, position?: number) => void
  updateSection: (id: string, updates: Partial<LayoutSection>) => void
  deleteSection: (id: string) => void
  addBlock: (sectionId: string, blockType: BlockType, position?: number) => void
  updateBlock: (id: string, updates: Partial<LayoutBlock>) => void
  deleteBlock: (id: string) => void
  moveBlock: (id: string, newSectionId: string, newPosition: number) => void
  selectSection: (id: string | null) => void
  selectBlock: (id: string | null) => void
  setPreviewMode: (enabled: boolean) => void
  setDevicePreview: (device: 'desktop' | 'tablet' | 'mobile' | 'large') => void
  updateLayout: (updates: Partial<Layout>) => void
  updateStructure: (structure: LayoutStructure) => void
  updateStyling: (styling: LayoutStyling) => void
  updateResponsive: (responsive: ResponsiveSettings) => void
  undo: () => void
  redo: () => void
  canUndo: boolean
  canRedo: boolean
  saveToHistory: () => void

  // AI and Grid Layout functions
  generateAILayout: (prompt: string, options?: any) => Promise<void>
  applyLayoutTemplate: (template: any) => void
  updateGridLayout: (blockId: string, gridLayout: any) => void
  clearLayout: () => void
}

const LayoutBuilderContext = createContext<LayoutBuilderContextType | undefined>(undefined)

// Provider
interface LayoutBuilderProviderProps {
  children: React.ReactNode
  initialLayout?: Layout
}

export function LayoutBuilderProvider({ children, initialLayout }: LayoutBuilderProviderProps) {
  const [state, dispatch] = useReducer(layoutBuilderReducer, {
    ...initialState,
    layout: initialLayout || initialState.layout,
  })

  // Initialize with initial layout if provided
  useEffect(() => {
    if (initialLayout) {
      dispatch({ type: 'SET_LAYOUT', payload: initialLayout })
    }
  }, [initialLayout])

  // Helper functions
  const addSection = useCallback((sectionType: SectionType, position?: number) => {
    dispatch({ type: 'ADD_SECTION', payload: { sectionType, position } })
    dispatch({ type: 'SAVE_TO_HISTORY' })
  }, [])

  const updateSection = useCallback((id: string, updates: Partial<LayoutSection>) => {
    dispatch({ type: 'UPDATE_SECTION', payload: { id, updates } })
  }, [])

  const deleteSection = useCallback((id: string) => {
    dispatch({ type: 'DELETE_SECTION', payload: id })
    dispatch({ type: 'SAVE_TO_HISTORY' })
  }, [])

  const addBlock = useCallback((sectionId: string, blockType: BlockType, position?: number) => {
    dispatch({ type: 'ADD_BLOCK', payload: { sectionId, blockType, position } })
    dispatch({ type: 'SAVE_TO_HISTORY' })
  }, [])

  const updateBlock = useCallback((id: string, updates: Partial<LayoutBlock>) => {
    dispatch({ type: 'UPDATE_BLOCK', payload: { id, updates } })
  }, [])

  const deleteBlock = useCallback((id: string) => {
    dispatch({ type: 'DELETE_BLOCK', payload: id })
    dispatch({ type: 'SAVE_TO_HISTORY' })
  }, [])

  const moveBlock = useCallback((id: string, newSectionId: string, newPosition: number) => {
    dispatch({ type: 'MOVE_BLOCK', payload: { id, newSectionId, newPosition } })
    dispatch({ type: 'SAVE_TO_HISTORY' })
  }, [])

  const selectSection = useCallback((id: string | null) => {
    dispatch({ type: 'SELECT_SECTION', payload: id })
  }, [])

  const selectBlock = useCallback((id: string | null) => {
    dispatch({ type: 'SELECT_BLOCK', payload: id })
  }, [])

  const setPreviewMode = useCallback((enabled: boolean) => {
    dispatch({ type: 'SET_PREVIEW_MODE', payload: enabled })
  }, [])

  const setDevicePreview = useCallback((device: 'desktop' | 'tablet' | 'mobile') => {
    dispatch({ type: 'SET_DEVICE_PREVIEW', payload: device })
  }, [])

  const updateLayout = useCallback((updates: Partial<Layout>) => {
    dispatch({ type: 'UPDATE_LAYOUT', payload: updates })
  }, [])

  const updateStructure = useCallback((structure: LayoutStructure) => {
    dispatch({ type: 'UPDATE_STRUCTURE', payload: structure })
    dispatch({ type: 'SAVE_TO_HISTORY' })
  }, [])

  const updateStyling = useCallback((styling: LayoutStyling) => {
    dispatch({ type: 'UPDATE_STYLING', payload: styling })
  }, [])

  const updateResponsive = useCallback((responsive: ResponsiveSettings) => {
    dispatch({ type: 'UPDATE_RESPONSIVE', payload: responsive })
  }, [])

  const undo = useCallback(() => {
    dispatch({ type: 'UNDO' })
  }, [])

  const redo = useCallback(() => {
    dispatch({ type: 'REDO' })
  }, [])

  const saveToHistory = useCallback(() => {
    dispatch({ type: 'SAVE_TO_HISTORY' })
  }, [])

  // AI and Grid Layout functions
  const generateAILayout = useCallback(async (prompt: string, options: any = {}) => {
    try {
      const response = await fetch('/api/ai-layout-builder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [{
            role: 'user',
            content: prompt
          }],
          ...options
        })
      })

      if (!response.ok) {
        throw new Error('Failed to generate AI layout')
      }

      const data = await response.json()
      if (data.layout) {
        dispatch({ type: 'SAVE_TO_HISTORY' })
        dispatch({ type: 'UPDATE_LAYOUT', payload: data.layout })
      }
    } catch (error) {
      console.error('AI layout generation failed:', error)
      throw error
    }
  }, [])

  const applyLayoutTemplate = useCallback((template: any) => {
    dispatch({ type: 'SAVE_TO_HISTORY' })

    if (template.structure) {
      dispatch({ type: 'UPDATE_STRUCTURE', payload: template.structure })
    }

    if (template.styling) {
      dispatch({ type: 'UPDATE_STYLING', payload: template.styling })
    }

    if (template.responsive) {
      dispatch({ type: 'UPDATE_RESPONSIVE', payload: template.responsive })
    }
  }, [])

  const updateGridLayout = useCallback((blockId: string, gridLayout: any) => {
    dispatch({ type: 'UPDATE_BLOCK', payload: {
      id: blockId,
      updates: { gridLayout }
    }})
  }, [])

  const clearLayout = useCallback(() => {
    dispatch({ type: 'SAVE_TO_HISTORY' })
    dispatch({ type: 'UPDATE_STRUCTURE', payload: {} })
  }, [])

  const canUndo = state.historyIndex > 0
  const canRedo = state.historyIndex < state.history.length - 1

  const value: LayoutBuilderContextType = {
    state,
    dispatch,
    addSection,
    updateSection,
    deleteSection,
    addBlock,
    updateBlock,
    deleteBlock,
    moveBlock,
    selectSection,
    selectBlock,
    setPreviewMode,
    setDevicePreview,
    updateLayout,
    updateStructure,
    updateStyling,
    updateResponsive,
    undo,
    redo,
    canUndo,
    canRedo,
    saveToHistory,
    generateAILayout,
    applyLayoutTemplate,
    updateGridLayout,
    clearLayout,
  }

  return (
    <LayoutBuilderContext.Provider value={value}>
      {children}
    </LayoutBuilderContext.Provider>
  )
}

// Hook
export function useLayoutBuilder() {
  const context = useContext(LayoutBuilderContext)
  if (context === undefined) {
    throw new Error('useLayoutBuilder must be used within a LayoutBuilderProvider')
  }
  return context
}
