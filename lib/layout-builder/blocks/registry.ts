import { BlockType } from '../types'

export interface LayoutBlockDefinition {
  type: BlockType
  name: string
  description: string
  category: 'navigation' | 'content' | 'media' | 'ecommerce' | 'social' | 'utility'
  icon: string
  defaultConfig: any
  defaultContent: any
  defaultStyling: any
  isLayoutSpecific: boolean
  allowedSections: ('header' | 'main' | 'sidebar' | 'footer' | 'custom')[]
}

class LayoutBlockRegistry {
  private blocks: Map<BlockType, LayoutBlockDefinition> = new Map()

  constructor() {
    this.registerDefaultBlocks()
  }

  private registerDefaultBlocks() {
    // Logo Block
    this.register({
      type: 'logo',
      name: '<PERSON>go',
      description: 'Brand logo with customizable size and alignment',
      category: 'media',
      icon: 'Image',
      defaultConfig: {
        size: 'medium',
        alignment: 'left',
        spacing: { top: '0.5rem', right: '0', bottom: '0.5rem', left: '0' },
        animation: { type: 'none' }
      },
      defaultContent: {
        text: 'Your Brand',
        image: '/logo-placeholder.png',
        alt: 'Brand Logo',
        link: '/'
      },
      defaultStyling: {
        background: { type: 'none' },
        border: { width: '0', style: 'none', color: 'transparent' },
        spacing: { top: '0.5rem', right: '0', bottom: '0.5rem', left: '0' },
        typography: {
          fontFamily: 'inherit',
          fontSize: '24px',
          lineHeight: '1.2',
          fontWeight: 'bold'
        },
        colors: {
          primary: '#000000',
          secondary: '#666666',
          background: 'transparent',
          text: '#000000'
        },
        shadow: { type: 'none' }
      },
      isLayoutSpecific: true,
      allowedSections: ['header', 'footer']
    })

    // Navigation Block
    this.register({
      type: 'navigation',
      name: 'Navigation Menu',
      description: 'Main navigation menu with dropdown support',
      category: 'navigation',
      icon: 'Menu',
      defaultConfig: {
        size: 'medium',
        alignment: 'left',
        spacing: { top: '0', right: '0', bottom: '0', left: '0' },
        animation: { type: 'none' },
        layout: 'horizontal',
        showDropdowns: true,
        mobileBreakpoint: '768px'
      },
      defaultContent: {
        menu: 'main-menu',
        items: [
          { label: 'Home', url: '/', children: [] },
          { label: 'Shop', url: '/shop', children: [] },
          { label: 'About', url: '/about', children: [] },
          { label: 'Contact', url: '/contact', children: [] }
        ]
      },
      defaultStyling: {
        background: { type: 'none' },
        border: { width: '0', style: 'none', color: 'transparent' },
        spacing: { top: '0', right: '0', bottom: '0', left: '0' },
        typography: {
          fontFamily: 'inherit',
          fontSize: '16px',
          lineHeight: '1.5',
          fontWeight: 'normal'
        },
        colors: {
          primary: '#000000',
          secondary: '#666666',
          background: 'transparent',
          text: '#000000'
        },
        shadow: { type: 'none' }
      },
      isLayoutSpecific: true,
      allowedSections: ['header', 'footer']
    })

    // Search Block
    this.register({
      type: 'search',
      name: 'Search',
      description: 'Search input with customizable styling',
      category: 'utility',
      icon: 'Search',
      defaultConfig: {
        size: 'medium',
        alignment: 'left',
        spacing: { top: '0', right: '0', bottom: '0', left: '0' },
        animation: { type: 'none' },
        placeholder: 'Search...',
        showButton: true,
        buttonText: 'Search'
      },
      defaultContent: {
        placeholder: 'Search products...',
        buttonText: 'Search',
        searchType: 'products'
      },
      defaultStyling: {
        background: { type: 'solid', color: '#ffffff' },
        border: { width: '1px', style: 'solid', color: '#e5e5e5' },
        spacing: { top: '0.5rem', right: '0.5rem', bottom: '0.5rem', left: '0.5rem' },
        typography: {
          fontFamily: 'inherit',
          fontSize: '14px',
          lineHeight: '1.5',
          fontWeight: 'normal'
        },
        colors: {
          primary: '#000000',
          secondary: '#666666',
          background: '#ffffff',
          text: '#000000'
        },
        shadow: { type: 'none' }
      },
      isLayoutSpecific: true,
      allowedSections: ['header', 'sidebar']
    })

    // Cart Block
    this.register({
      type: 'cart',
      name: 'Shopping Cart',
      description: 'Shopping cart icon with item count',
      category: 'ecommerce',
      icon: 'ShoppingCart',
      defaultConfig: {
        size: 'medium',
        alignment: 'right',
        spacing: { top: '0', right: '0', bottom: '0', left: '0' },
        animation: { type: 'none' },
        showCount: true,
        showTotal: false
      },
      defaultContent: {
        icon: 'shopping-cart',
        text: 'Cart',
        link: '/cart'
      },
      defaultStyling: {
        background: { type: 'none' },
        border: { width: '0', style: 'none', color: 'transparent' },
        spacing: { top: '0.5rem', right: '0.5rem', bottom: '0.5rem', left: '0.5rem' },
        typography: {
          fontFamily: 'inherit',
          fontSize: '14px',
          lineHeight: '1.5',
          fontWeight: 'normal'
        },
        colors: {
          primary: '#000000',
          secondary: '#666666',
          background: 'transparent',
          text: '#000000'
        },
        shadow: { type: 'none' }
      },
      isLayoutSpecific: true,
      allowedSections: ['header']
    })

    // Content Block
    this.register({
      type: 'content',
      name: 'Content Area',
      description: 'Main content area for page content',
      category: 'content',
      icon: 'FileText',
      defaultConfig: {
        size: 'auto',
        alignment: 'left',
        spacing: { top: '1rem', right: '1rem', bottom: '1rem', left: '1rem' },
        animation: { type: 'none' },
        container: true,
        maxWidth: '1200px'
      },
      defaultContent: {
        html: '<p>Main content will be rendered here</p>',
        allowPageBuilder: true
      },
      defaultStyling: {
        background: { type: 'none' },
        border: { width: '0', style: 'none', color: 'transparent' },
        spacing: { top: '1rem', right: '1rem', bottom: '1rem', left: '1rem' },
        typography: {
          fontFamily: 'inherit',
          fontSize: '16px',
          lineHeight: '1.6',
          fontWeight: 'normal'
        },
        colors: {
          primary: '#000000',
          secondary: '#666666',
          background: 'transparent',
          text: '#000000'
        },
        shadow: { type: 'none' }
      },
      isLayoutSpecific: true,
      allowedSections: ['main']
    })

    // Widget Block
    this.register({
      type: 'widget',
      name: 'Widget Area',
      description: 'Widget area for sidebar content',
      category: 'content',
      icon: 'Package',
      defaultConfig: {
        size: 'auto',
        alignment: 'left',
        spacing: { top: '0', right: '0', bottom: '0', left: '0' },
        animation: { type: 'none' },
        widgetArea: 'sidebar-primary'
      },
      defaultContent: {
        widgetArea: 'sidebar-primary',
        widgets: []
      },
      defaultStyling: {
        background: { type: 'none' },
        border: { width: '0', style: 'none', color: 'transparent' },
        spacing: { top: '0', right: '0', bottom: '0', left: '0' },
        typography: {
          fontFamily: 'inherit',
          fontSize: '14px',
          lineHeight: '1.5',
          fontWeight: 'normal'
        },
        colors: {
          primary: '#000000',
          secondary: '#666666',
          background: 'transparent',
          text: '#000000'
        },
        shadow: { type: 'none' }
      },
      isLayoutSpecific: true,
      allowedSections: ['sidebar']
    })

    // Social Links Block
    this.register({
      type: 'social',
      name: 'Social Links',
      description: 'Social media links with icons',
      category: 'social',
      icon: 'Share2',
      defaultConfig: {
        size: 'medium',
        alignment: 'left',
        spacing: { top: '0.5rem', right: '0', bottom: '0.5rem', left: '0' },
        animation: { type: 'none' },
        layout: 'horizontal',
        showLabels: false
      },
      defaultContent: {
        links: [
          { platform: 'facebook', url: '#', label: 'Facebook' },
          { platform: 'twitter', url: '#', label: 'Twitter' },
          { platform: 'instagram', url: '#', label: 'Instagram' }
        ]
      },
      defaultStyling: {
        background: { type: 'none' },
        border: { width: '0', style: 'none', color: 'transparent' },
        spacing: { top: '0.5rem', right: '0.5rem', bottom: '0.5rem', left: '0.5rem' },
        typography: {
          fontFamily: 'inherit',
          fontSize: '14px',
          lineHeight: '1.5',
          fontWeight: 'normal'
        },
        colors: {
          primary: '#000000',
          secondary: '#666666',
          background: 'transparent',
          text: '#000000'
        },
        shadow: { type: 'none' }
      },
      isLayoutSpecific: true,
      allowedSections: ['header', 'footer']
    })

    // Copyright Block
    this.register({
      type: 'copyright',
      name: 'Copyright',
      description: 'Copyright notice and legal text',
      category: 'content',
      icon: 'Copyright',
      defaultConfig: {
        size: 'small',
        alignment: 'center',
        spacing: { top: '1rem', right: '0', bottom: '1rem', left: '0' },
        animation: { type: 'none' },
        showYear: true
      },
      defaultContent: {
        text: '© {year} Your Company Name. All rights reserved.',
        year: new Date().getFullYear()
      },
      defaultStyling: {
        background: { type: 'none' },
        border: { width: '0', style: 'none', color: 'transparent' },
        spacing: { top: '1rem', right: '0', bottom: '1rem', left: '0' },
        typography: {
          fontFamily: 'inherit',
          fontSize: '12px',
          lineHeight: '1.4',
          fontWeight: 'normal'
        },
        colors: {
          primary: '#666666',
          secondary: '#999999',
          background: 'transparent',
          text: '#666666'
        },
        shadow: { type: 'none' }
      },
      isLayoutSpecific: true,
      allowedSections: ['footer']
    })

    // Links Block
    this.register({
      type: 'links',
      name: 'Links',
      description: 'Collection of useful links',
      category: 'navigation',
      icon: 'Link',
      defaultConfig: {
        size: 'medium',
        alignment: 'left',
        spacing: { top: '0.5rem', right: '0', bottom: '0.5rem', left: '0' },
        animation: { type: 'none' },
        layout: 'vertical',
        showIcons: false
      },
      defaultContent: {
        title: 'Quick Links',
        links: [
          { label: 'About Us', url: '/about' },
          { label: 'Contact', url: '/contact' },
          { label: 'Privacy Policy', url: '/privacy' },
          { label: 'Terms of Service', url: '/terms' }
        ]
      },
      defaultStyling: {
        background: { type: 'none' },
        border: { width: '0', style: 'none', color: 'transparent' },
        spacing: { top: '0.5rem', right: '0', bottom: '0.5rem', left: '0' },
        typography: {
          fontFamily: 'inherit',
          fontSize: '14px',
          lineHeight: '1.5',
          fontWeight: 'normal'
        },
        colors: {
          primary: '#000000',
          secondary: '#666666',
          background: 'transparent',
          text: '#000000'
        },
        shadow: { type: 'none' }
      },
      isLayoutSpecific: true,
      allowedSections: ['footer', 'sidebar']
    })

    // Filters Block
    this.register({
      type: 'filters',
      name: 'Filters',
      description: 'Product or content filters',
      category: 'utility',
      icon: 'Filter',
      defaultConfig: {
        size: 'medium',
        alignment: 'left',
        spacing: { top: '0', right: '0', bottom: '0', left: '0' },
        animation: { type: 'none' },
        filterType: 'category',
        showClearAll: true
      },
      defaultContent: {
        title: 'Filter By',
        filters: [
          { label: 'Category', type: 'category', options: [] },
          { label: 'Price', type: 'price', options: [] },
          { label: 'Brand', type: 'brand', options: [] }
        ]
      },
      defaultStyling: {
        background: { type: 'none' },
        border: { width: '0', style: 'none', color: 'transparent' },
        spacing: { top: '0', right: '0', bottom: '0', left: '0' },
        typography: {
          fontFamily: 'inherit',
          fontSize: '14px',
          lineHeight: '1.5',
          fontWeight: 'normal'
        },
        colors: {
          primary: '#000000',
          secondary: '#666666',
          background: 'transparent',
          text: '#000000'
        },
        shadow: { type: 'none' }
      },
      isLayoutSpecific: true,
      allowedSections: ['sidebar']
    })

    // Categories Block
    this.register({
      type: 'categories',
      name: 'Categories',
      description: 'Product or content categories',
      category: 'navigation',
      icon: 'Tag',
      defaultConfig: {
        size: 'medium',
        alignment: 'left',
        spacing: { top: '0', right: '0', bottom: '0', left: '0' },
        animation: { type: 'none' },
        showCounts: true,
        hierarchical: true
      },
      defaultContent: {
        title: 'Categories',
        categories: [
          { name: 'Electronics', count: 25, children: [] },
          { name: 'Clothing', count: 18, children: [] },
          { name: 'Home & Garden', count: 12, children: [] }
        ]
      },
      defaultStyling: {
        background: { type: 'none' },
        border: { width: '0', style: 'none', color: 'transparent' },
        spacing: { top: '0', right: '0', bottom: '0', left: '0' },
        typography: {
          fontFamily: 'inherit',
          fontSize: '14px',
          lineHeight: '1.5',
          fontWeight: 'normal'
        },
        colors: {
          primary: '#000000',
          secondary: '#666666',
          background: 'transparent',
          text: '#000000'
        },
        shadow: { type: 'none' }
      },
      isLayoutSpecific: true,
      allowedSections: ['sidebar']
    })

    // Recent Posts Block
    this.register({
      type: 'recent-posts',
      name: 'Recent Posts',
      description: 'List of recent blog posts',
      category: 'content',
      icon: 'FileText',
      defaultConfig: {
        size: 'medium',
        alignment: 'left',
        spacing: { top: '0', right: '0', bottom: '0', left: '0' },
        animation: { type: 'none' },
        postCount: 5,
        showDate: true,
        showExcerpt: false
      },
      defaultContent: {
        title: 'Recent Posts',
        posts: []
      },
      defaultStyling: {
        background: { type: 'none' },
        border: { width: '0', style: 'none', color: 'transparent' },
        spacing: { top: '0', right: '0', bottom: '0', left: '0' },
        typography: {
          fontFamily: 'inherit',
          fontSize: '14px',
          lineHeight: '1.5',
          fontWeight: 'normal'
        },
        colors: {
          primary: '#000000',
          secondary: '#666666',
          background: 'transparent',
          text: '#000000'
        },
        shadow: { type: 'none' }
      },
      isLayoutSpecific: true,
      allowedSections: ['sidebar']
    })

    // Tags Block
    this.register({
      type: 'tags',
      name: 'Tags',
      description: 'Tag cloud or list',
      category: 'navigation',
      icon: 'Tag',
      defaultConfig: {
        size: 'medium',
        alignment: 'left',
        spacing: { top: '0', right: '0', bottom: '0', left: '0' },
        animation: { type: 'none' },
        layout: 'cloud',
        maxTags: 20
      },
      defaultContent: {
        title: 'Tags',
        tags: [
          { name: 'Technology', count: 15 },
          { name: 'Design', count: 12 },
          { name: 'Business', count: 8 },
          { name: 'Marketing', count: 6 }
        ]
      },
      defaultStyling: {
        background: { type: 'none' },
        border: { width: '0', style: 'none', color: 'transparent' },
        spacing: { top: '0', right: '0', bottom: '0', left: '0' },
        typography: {
          fontFamily: 'inherit',
          fontSize: '12px',
          lineHeight: '1.4',
          fontWeight: 'normal'
        },
        colors: {
          primary: '#000000',
          secondary: '#666666',
          background: 'transparent',
          text: '#000000'
        },
        shadow: { type: 'none' }
      },
      isLayoutSpecific: true,
      allowedSections: ['sidebar']
    })
  }

  register(definition: LayoutBlockDefinition) {
    this.blocks.set(definition.type, definition)
  }

  getBlockType(type: BlockType): LayoutBlockDefinition | undefined {
    return this.blocks.get(type)
  }

  getAllBlocks(): LayoutBlockDefinition[] {
    return Array.from(this.blocks.values())
  }

  getBlocksByCategory(category: string): LayoutBlockDefinition[] {
    return Array.from(this.blocks.values()).filter(block => block.category === category)
  }

  getBlocksForSection(sectionType: 'header' | 'main' | 'sidebar' | 'footer' | 'custom'): LayoutBlockDefinition[] {
    return Array.from(this.blocks.values()).filter(block => 
      block.allowedSections.includes(sectionType)
    )
  }
}

export const layoutBlockRegistry = new LayoutBlockRegistry()
