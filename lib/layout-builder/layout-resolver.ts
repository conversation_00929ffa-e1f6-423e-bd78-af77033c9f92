import { PrismaClient } from '@prisma/client'
import { cache } from 'react'
import {
  Layout,
  LayoutAssignment,
  ResolvedLayout,
  LayoutType,
  LayoutCategory,
  LayoutStructure,
  LayoutStyling,
  ResponsiveSettings,
  NavigationItem,
  NavigationMenu,
  WidgetArea
} from './types'

const prisma = new PrismaClient()

export class LayoutResolver {
  /**
   * Resolve the appropriate layout for a given route resolution
   */
  static async resolve(
    routeResolution: any,
    layoutId?: string
  ): Promise<ResolvedLayout | null> {
    try {
      // If specific layout ID is provided, use it
      if (layoutId) {
        return await this.getLayoutById(layoutId)
      }

      // Determine layout based on route resolution
      const layout = await this.resolveLayoutForRoute(routeResolution)
      
      if (!layout) {
        // Fallback to default site layout
        return await this.getDefaultLayout()
      }

      return layout
    } catch (error) {
      console.error('Error resolving layout:', error)
      return await this.getDefaultLayout()
    }
  }

  /**
   * Get layout by ID with all related data
   */
  static getLayoutById = cache(async (layoutId: string): Promise<ResolvedLayout | null> => {
    try {
      const layout = await prisma.layout.findUnique({
        where: { id: layoutId, isActive: true },
        include: {
          assignments: {
            where: { isActive: true },
            orderBy: { priority: 'desc' }
          },
          sections: {
            where: { isVisible: true },
            include: {
              layoutBlocks: {
                where: { isVisible: true },
                orderBy: { position: 'asc' }
              }
            },
            orderBy: { position: 'asc' }
          },
          versions: {
            where: { isCurrent: true },
            take: 1
          }
        }
      })

      if (!layout) {
        return null
      }

      // Get navigation menus and widget areas
      const [menus, widgets] = await Promise.all([
        this.getNavigationMenus(),
        this.getWidgetAreas()
      ])

      return {
        layout: {
          ...layout,
          structure: layout.structure as unknown as LayoutStructure,
          styling: layout.styling as unknown as LayoutStyling,
          responsive: layout.responsive as unknown as ResponsiveSettings
        } as Layout,
        sections: layout.sections as any[],
        assignments: layout.assignments as LayoutAssignment[],
        menus: menus.map(menu => ({
          ...menu,
          description: menu.description || undefined,
          items: menu.items as unknown as NavigationItem[]
        })) as NavigationMenu[],
        widgets: widgets.map(widget => ({
          ...widget,
          description: widget.description || undefined,
          widgets: widget.widgets as any[]
        })) as WidgetArea[]
      }
    } catch (error) {
      console.error('Error getting layout by ID:', error)
      return null
    }
  })

  /**
   * Resolve layout based on route resolution
   */
  private static async resolveLayoutForRoute(routeResolution: any): Promise<ResolvedLayout | null> {
    try {
      const assignments = await prisma.layoutAssignment.findMany({
        where: {
          isActive: true,
          OR: [
            // Global layout
            { targetType: 'global' },
            // Page-specific layout
            {
              targetType: 'page',
              targetId: routeResolution.type === 'page' ? routeResolution.data?.id : undefined
            },
            // Post type layout
            {
              targetType: 'post-type',
              targetId: routeResolution.type === 'post' ? routeResolution.data?.postType : undefined
            },
            // URL-based layout
            {
              targetType: 'specific',
              targetSlug: routeResolution.pathname
            }
          ]
        },
        include: {
          layout: true
        },
        orderBy: { priority: 'desc' }
      })

      // Find the highest priority assignment with an active layout
      const assignment = assignments.find(a =>
        a.layout &&
        a.layout.isActive &&
        this.evaluateConditions(a.conditions, routeResolution)
      )
      
      if (assignment?.layout) {
        return await this.getLayoutById(assignment.layout.id)
      }

      return null
    } catch (error) {
      console.error('Error resolving layout for route:', error)
      return null
    }
  }

  /**
   * Get default site layout
   */
  private static async getDefaultLayout(): Promise<ResolvedLayout | null> {
    try {
      const defaultLayout = await prisma.layout.findFirst({
        where: {
          type: 'site',
          isSystem: true,
          isActive: true
        },
        orderBy: { createdAt: 'asc' }
      })

      if (defaultLayout) {
        return await this.getLayoutById(defaultLayout.id)
      }

      // If no default layout exists, create one
      return await this.createDefaultLayout()
    } catch (error) {
      console.error('Error getting default layout:', error)
      return null
    }
  }

  /**
   * Create a basic default layout
   */
  private static async createDefaultLayout(): Promise<ResolvedLayout | null> {
    try {
      const defaultLayout = await prisma.layout.create({
        data: {
          name: 'Default Site Layout',
          description: 'Basic site layout with header, main content, and footer',
          type: 'site',
          category: 'custom',
          structure: {
            header: {
              id: 'header-1',
              type: 'header',
              name: 'Site Header',
              position: 1,
              blocks: [
                {
                  id: 'logo-1',
                  type: 'logo',
                  name: 'Site Logo',
                  position: 1,
                  configuration: { size: 'medium', alignment: 'left' },
                  content: { text: 'Coco Milk Kids' },
                  styling: {},
                  responsive: {},
                  conditions: {},
                  isVisible: true
                },
                {
                  id: 'nav-1',
                  type: 'navigation',
                  name: 'Main Navigation',
                  position: 2,
                  configuration: { style: 'horizontal' },
                  content: { menu: 'main-menu' },
                  styling: {},
                  responsive: {},
                  conditions: {},
                  isVisible: true
                }
              ],
              configuration: { layout: 'flex', alignment: 'justify' },
              styling: { background: { type: 'color', color: '#ffffff' } },
              responsive: {},
              isVisible: true
            },
            main: {
              id: 'main-1',
              type: 'main',
              name: 'Main Content',
              position: 2,
              blocks: [
                {
                  id: 'content-1',
                  type: 'content',
                  name: 'Page Content',
                  position: 1,
                  configuration: {},
                  content: {},
                  styling: {},
                  responsive: {},
                  conditions: {},
                  isVisible: true
                }
              ],
              configuration: { layout: 'block' },
              styling: {},
              responsive: {},
              isVisible: true
            },
            footer: {
              id: 'footer-1',
              type: 'footer',
              name: 'Site Footer',
              position: 3,
              blocks: [
                {
                  id: 'copyright-1',
                  type: 'copyright',
                  name: 'Copyright',
                  position: 1,
                  configuration: { alignment: 'center' },
                  content: { text: '© 2024 Coco Milk Kids. All rights reserved.' },
                  styling: {},
                  responsive: {},
                  conditions: {},
                  isVisible: true
                }
              ],
              configuration: { layout: 'flex', alignment: 'center' },
              styling: { background: { type: 'color', color: '#f8f9fa' } },
              responsive: {},
              isVisible: true
            }
          },
          styling: {
            theme: 'default',
            colorScheme: 'light',
            typography: {
              fontFamily: 'Inter, sans-serif',
              fontSize: '16px',
              fontWeight: '400',
              lineHeight: '1.5',
              letterSpacing: '0',
              textAlign: 'left',
              textTransform: 'none'
            },
            spacing: { top: 0, right: 0, bottom: 0, left: 0 },
            colors: {
              primary: '#ffcc00',
              secondary: '#333333',
              accent: '#ff6b6b',
              text: '#333333',
              background: '#ffffff',
              border: '#e5e5e5'
            }
          },
          responsive: {
            mobile: { display: 'block', width: '100%', height: 'auto', spacing: { top: 0, right: 16, bottom: 0, left: 16 }, typography: { fontSize: '14px' } },
            tablet: { display: 'block', width: '100%', height: 'auto', spacing: { top: 0, right: 24, bottom: 0, left: 24 }, typography: { fontSize: '15px' } },
            desktop: { display: 'block', width: '100%', height: 'auto', spacing: { top: 0, right: 32, bottom: 0, left: 32 }, typography: { fontSize: '16px' } },
            large: { display: 'block', width: '100%', height: 'auto', spacing: { top: 0, right: 40, bottom: 0, left: 40 }, typography: { fontSize: '16px' } }
          },
          conditions: {},
          isTemplate: true,
          isSystem: true,
          isActive: true
        }
      })

      // Create global assignment
      await prisma.layoutAssignment.create({
        data: {
          layoutId: defaultLayout.id,
          targetType: 'global',
          priority: 0,
          conditions: {},
          isActive: true
        }
      })

      return await this.getLayoutById(defaultLayout.id)
    } catch (error) {
      console.error('Error creating default layout:', error)
      return null
    }
  }

  /**
   * Get all navigation menus
   */
  private static async getNavigationMenus() {
    try {
      return await prisma.navigationMenu.findMany({
        where: { isActive: true },
        orderBy: { name: 'asc' }
      })
    } catch (error) {
      console.error('Error getting navigation menus:', error)
      return []
    }
  }

  /**
   * Get all widget areas
   */
  private static async getWidgetAreas() {
    try {
      return await prisma.widgetArea.findMany({
        where: { isActive: true },
        orderBy: { name: 'asc' }
      })
    } catch (error) {
      console.error('Error getting widget areas:', error)
      return []
    }
  }

  /**
   * Evaluate layout conditions
   */
  private static evaluateConditions(conditions: any, _context: any): boolean {
    // Simple condition evaluation - can be extended
    if (!conditions || Object.keys(conditions).length === 0) {
      return true
    }

    // Add condition evaluation logic here
    // For now, return true (all conditions pass)
    return true
  }

  /**
   * Get layouts by type and category
   */
  static async getLayouts(
    type?: LayoutType,
    category?: LayoutCategory,
    includeTemplates = false
  ): Promise<Layout[]> {
    try {
      const where: any = { isActive: true }
      
      if (type) where.type = type
      if (category) where.category = category
      if (!includeTemplates) where.isTemplate = false

      const layouts = await prisma.layout.findMany({
        where,
        orderBy: [
          { isSystem: 'desc' },
          { usageCount: 'desc' },
          { name: 'asc' }
        ]
      })

      return layouts.map(layout => ({
        ...layout,
        structure: layout.structure as unknown as LayoutStructure,
        styling: layout.styling as unknown as LayoutStyling,
        responsive: layout.responsive as unknown as ResponsiveSettings
      })) as Layout[]
    } catch (error) {
      console.error('Error getting layouts:', error)
      return []
    }
  }

  /**
   * Increment layout usage count
   */
  static async incrementUsage(layoutId: string): Promise<void> {
    try {
      await prisma.layout.update({
        where: { id: layoutId },
        data: {
          usageCount: { increment: 1 }
        }
      })
    } catch (error) {
      console.error('Error incrementing layout usage:', error)
    }
  }
}
