// Layout Builder Type Definitions

export interface Layout {
  id: string
  name: string
  description?: string
  type: LayoutType
  category: LayoutCategory
  structure: LayoutStructure
  styling: LayoutStyling
  responsive: ResponsiveSettings
  conditions: LayoutConditions
  isTemplate: boolean
  isSystem: boolean
  isActive: boolean
  usageCount: number
  thumbnail?: string
  tags: string[]
  createdBy?: string
  updatedBy?: string
  createdAt: Date
  updatedAt: Date
}

export type LayoutType = 'site' | 'page' | 'post-type' | 'template'
export type LayoutCategory = 'ecommerce' | 'blog' | 'portfolio' | 'landing' | 'corporate' | 'custom'

export interface LayoutStructure {
  header?: LayoutSection
  main: LayoutSection
  sidebar?: LayoutSection
  footer?: LayoutSection
  [key: string]: LayoutSection | undefined
}

export interface LayoutSection {
  id: string
  type: SectionType
  name: string
  position: number
  blocks: LayoutBlock[]
  configuration: SectionConfiguration
  styling: SectionStyling
  responsive: ResponsiveSettings
  isVisible: boolean
}

export type SectionType = 'header' | 'main' | 'sidebar' | 'footer' | 'custom'

export interface LayoutBlock {
  id: string
  type: BlockType
  name: string
  position: number
  configuration: BlockConfiguration
  content: BlockContent
  styling: BlockStyling
  responsive: ResponsiveSettings
  conditions: BlockConditions
  isVisible: boolean
}

export type BlockType = 
  | 'logo' 
  | 'navigation' 
  | 'search' 
  | 'cart' 
  | 'content' 
  | 'widget' 
  | 'social' 
  | 'copyright' 
  | 'breadcrumbs'
  | 'filters'
  | 'categories'
  | 'recent-posts'
  | 'tags'
  | 'links'
  | 'custom'

// Configuration Interfaces
export interface SectionConfiguration {
  layout: 'flex' | 'grid' | 'block'
  alignment: 'left' | 'center' | 'right' | 'justify'
  spacing: SpacingSettings
  background: BackgroundSettings
  container: ContainerSettings
  useContainer?: boolean // Whether to wrap content in container
  containerPadding?: boolean // Whether to apply default container padding
  [key: string]: any
}

export interface BlockConfiguration {
  size: 'small' | 'medium' | 'large' | 'auto'
  alignment: 'left' | 'center' | 'right'
  spacing: SpacingSettings
  animation: AnimationSettings
  [key: string]: any
}

export interface BlockContent {
  text?: string
  html?: string
  image?: string
  links?: LinkItem[]
  menu?: string // Menu ID
  widget?: string // Widget ID
  [key: string]: any
}

// Styling Interfaces
export interface LayoutStyling {
  theme: string
  colorScheme: 'light' | 'dark' | 'auto'
  typography: TypographySettings
  spacing: SpacingSettings
  colors: ColorSettings
  [key: string]: any
}

export interface SectionStyling {
  background: BackgroundSettings
  border: BorderSettings
  spacing: SpacingSettings
  shadow: ShadowSettings
  [key: string]: any
}

export interface BlockStyling {
  background: BackgroundSettings
  border: BorderSettings
  spacing: SpacingSettings
  typography: TypographySettings
  colors: ColorSettings
  shadow: ShadowSettings
  [key: string]: any
}

// Responsive Settings
export interface ResponsiveSettings {
  mobile: ResponsiveBreakpoint
  tablet: ResponsiveBreakpoint
  desktop: ResponsiveBreakpoint
  large: ResponsiveBreakpoint
}

export interface ResponsiveBreakpoint {
  display: 'block' | 'flex' | 'grid' | 'none'
  width: string | number
  height: string | number
  spacing: SpacingSettings
  typography: TypographySettings
  [key: string]: any
}

// Condition Interfaces
export interface LayoutConditions {
  userRole?: string[]
  deviceType?: ('mobile' | 'tablet' | 'desktop')[]
  pageType?: string[]
  customConditions?: CustomCondition[]
}

export interface BlockConditions {
  userLoggedIn?: boolean
  userRole?: string[]
  deviceType?: ('mobile' | 'tablet' | 'desktop')[]
  timeRange?: TimeRange
  customConditions?: CustomCondition[]
}

export interface CustomCondition {
  type: string
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than'
  value: any
}

export interface TimeRange {
  start: string // ISO date string
  end: string // ISO date string
}

// Utility Interfaces
export interface SpacingSettings {
  top: string | number
  right: string | number
  bottom: string | number
  left: string | number
}

export interface BackgroundSettings {
  type: 'color' | 'gradient' | 'image' | 'video'
  color?: string
  gradient?: GradientSettings
  image?: ImageSettings
  video?: VideoSettings
}

export interface GradientSettings {
  type: 'linear' | 'radial'
  direction: string
  colors: ColorStop[]
}

export interface ColorStop {
  color: string
  position: number
}

export interface ImageSettings {
  url: string
  size: 'cover' | 'contain' | 'auto'
  position: string
  repeat: 'no-repeat' | 'repeat' | 'repeat-x' | 'repeat-y'
}

export interface VideoSettings {
  url: string
  autoplay: boolean
  loop: boolean
  muted: boolean
}

export interface BorderSettings {
  width: string | number
  style: 'solid' | 'dashed' | 'dotted' | 'none'
  color: string
  radius: string | number
}

export interface ShadowSettings {
  type: 'box' | 'text' | 'none'
  x: number
  y: number
  blur: number
  spread: number
  color: string
}

export interface TypographySettings {
  fontFamily: string
  fontSize: string | number
  fontWeight: string | number
  lineHeight: string | number
  letterSpacing: string | number
  textAlign: 'left' | 'center' | 'right' | 'justify'
  textTransform: 'none' | 'uppercase' | 'lowercase' | 'capitalize'
}

export interface ColorSettings {
  primary: string
  secondary: string
  accent: string
  text: string
  background: string
  border: string
  [key: string]: string
}

export interface ContainerSettings {
  maxWidth: string | number
  padding: SpacingSettings
  margin: SpacingSettings
  centered: boolean
}

export interface AnimationSettings {
  type: 'none' | 'fade' | 'slide' | 'scale' | 'rotate' | 'bounce'
  duration: number
  delay: number
  easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear'
}

export interface LinkItem {
  id: string
  text: string
  url: string
  target: '_self' | '_blank' | '_parent' | '_top'
  icon?: string
  children?: LinkItem[]
}

// Layout Assignment Interfaces
export interface LayoutAssignment {
  id: string
  layoutId: string
  targetType: AssignmentTargetType
  targetId?: string
  targetSlug?: string
  priority: number
  conditions: LayoutConditions
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export type AssignmentTargetType = 'global' | 'page' | 'post-type' | 'specific' | 'conditional'

// Navigation and Widget Interfaces
export interface NavigationMenu {
  id: string
  name: string
  slug: string
  description?: string
  items: NavigationItem[]
  settings: NavigationSettings
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface NavigationItem {
  id: string
  text: string
  url: string
  target: string
  icon?: string
  cssClass?: string
  children?: NavigationItem[]
  position: number
  isVisible: boolean
}

export interface NavigationSettings {
  style: 'horizontal' | 'vertical' | 'dropdown' | 'mega'
  mobileStyle: 'hamburger' | 'drawer' | 'accordion'
  showIcons: boolean
  showArrows: boolean
  maxDepth: number
  [key: string]: any
}

export interface WidgetArea {
  id: string
  name: string
  slug: string
  description?: string
  widgets: Widget[]
  settings: WidgetAreaSettings
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Widget {
  id: string
  type: string
  title?: string
  content: any
  settings: any
  position: number
  isVisible: boolean
}

export interface WidgetAreaSettings {
  layout: 'vertical' | 'horizontal' | 'grid'
  spacing: SpacingSettings
  [key: string]: any
}

// Layout Builder State Management
export interface LayoutBuilderState {
  layout: Layout
  selectedSectionId: string | null
  selectedBlockId: string | null
  isDragging: boolean
  isPreviewMode: boolean
  devicePreview: 'desktop' | 'tablet' | 'mobile'
  history: LayoutHistoryEntry[]
  historyIndex: number
  isSaving: boolean
  hasUnsavedChanges: boolean
}

export interface LayoutHistoryEntry {
  structure: LayoutStructure
  styling: LayoutStyling
  responsive: ResponsiveSettings
  timestamp: number
}

export type LayoutBuilderAction =
  | { type: 'SET_LAYOUT'; payload: Layout }
  | { type: 'UPDATE_LAYOUT'; payload: Partial<Layout> }
  | { type: 'UPDATE_STRUCTURE'; payload: LayoutStructure }
  | { type: 'UPDATE_STYLING'; payload: LayoutStyling }
  | { type: 'UPDATE_RESPONSIVE'; payload: ResponsiveSettings }
  | { type: 'ADD_SECTION'; payload: { sectionType: SectionType; position?: number } }
  | { type: 'UPDATE_SECTION'; payload: { id: string; updates: Partial<LayoutSection> } }
  | { type: 'DELETE_SECTION'; payload: string }
  | { type: 'ADD_BLOCK'; payload: { sectionId: string; blockType: BlockType; position?: number } }
  | { type: 'UPDATE_BLOCK'; payload: { id: string; updates: Partial<LayoutBlock> } }
  | { type: 'DELETE_BLOCK'; payload: string }
  | { type: 'MOVE_BLOCK'; payload: { id: string; newSectionId: string; newPosition: number } }
  | { type: 'SELECT_SECTION'; payload: string | null }
  | { type: 'SELECT_BLOCK'; payload: string | null }
  | { type: 'SET_DRAGGING'; payload: boolean }
  | { type: 'SET_PREVIEW_MODE'; payload: boolean }
  | { type: 'SET_DEVICE_PREVIEW'; payload: 'desktop' | 'tablet' | 'mobile' }
  | { type: 'SET_SAVING'; payload: boolean }
  | { type: 'SET_UNSAVED_CHANGES'; payload: boolean }
  | { type: 'UNDO' }
  | { type: 'REDO' }
  | { type: 'SAVE_TO_HISTORY' }

// Layout Builder Context
export interface LayoutBuilderContext {
  currentLayout?: Layout
  isEditing: boolean
  selectedSection?: string
  selectedBlock?: string
  previewMode: 'desktop' | 'tablet' | 'mobile'
  isDirty: boolean
}

// Layout Resolution
export interface ResolvedLayout {
  layout: Layout
  sections: LayoutSection[]
  assignments: LayoutAssignment[]
  menus: NavigationMenu[]
  widgets: WidgetArea[]
}
