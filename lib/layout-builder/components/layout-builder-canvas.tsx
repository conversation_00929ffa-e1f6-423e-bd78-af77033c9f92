'use client'

import React, { useState, useRef } from 'react'
import { useLayoutBuilder } from '../context'
import { LayoutSection, LayoutBlock, SectionType } from '../types'
import { LayoutBlockRenderer } from './layout-block-renderer'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Plus, 
  GripVertical, 
  Eye, 
  EyeOff, 
  Settings, 
  Trash2,
  Navigation,
  FileText,
  Package,
  Copyright,
  Layers
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface LayoutBuilderCanvasProps {
  devicePreview: 'desktop' | 'tablet' | 'mobile'
  isPreviewMode: boolean
}

export function LayoutBuilderCanvas({ devicePreview, isPreviewMode }: LayoutBuilderCanvasProps) {
  const { 
    state, 
    selectSection, 
    selectBlock, 
    addBlock,
    deleteSection,
    deleteBlock,
    updateSection,
    updateBlock
  } = useLayoutBuilder()

  const { layout, selectedSectionId, selectedBlockId } = state
  const sections = Object.entries(layout.structure).filter(([_, section]) => section !== undefined)

  // Handle drop events
  const handleDrop = (e: React.DragEvent, sectionId: string, position?: number) => {
    e.preventDefault()
    
    try {
      const data = JSON.parse(e.dataTransfer.getData('application/json'))
      if (data.type === 'layout-block') {
        addBlock(sectionId, data.blockType, position)
      }
    } catch (error) {
      console.error('Error handling drop:', error)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  // Get section icon
  const getSectionIcon = (sectionType: SectionType) => {
    const iconMap: Record<SectionType, React.ComponentType<any>> = {
      header: Navigation,
      main: FileText,
      sidebar: Package,
      footer: Copyright,
      custom: Layers
    }
    return iconMap[sectionType] || Layers
  }

  if (isPreviewMode) {
    return (
      <div className="min-h-full">
        {sections.map(([sectionKey, section]) => (
          section && section.isVisible && (
            <SectionRenderer
              key={section.id}
              section={section}
              isPreviewMode={true}
              devicePreview={devicePreview}
            />
          )
        ))}
      </div>
    )
  }

  return (
    <div className="min-h-full bg-white">
      {sections.length === 0 ? (
        <div className="flex items-center justify-center min-h-[400px] p-8">
          <div className="text-center">
            <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Layers className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Empty Layout</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Start building your layout by adding sections from the sidebar
            </p>
            <Badge variant="outline" className="text-xs">
              Drag blocks from the sidebar to get started
            </Badge>
          </div>
        </div>
      ) : (
        <div className="space-y-1">
          {sections.map(([sectionKey, section]) => (
            section && (
              <SectionCanvas
                key={section.id}
                section={section}
                isSelected={selectedSectionId === section.id}
                selectedBlockId={selectedBlockId}
                onSelectSection={selectSection}
                onSelectBlock={selectBlock}
                onDeleteSection={deleteSection}
                onDeleteBlock={deleteBlock}
                onUpdateSection={updateSection}
                onUpdateBlock={updateBlock}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                getSectionIcon={getSectionIcon}
                devicePreview={devicePreview}
              />
            )
          ))}
        </div>
      )}
    </div>
  )
}

// Section Canvas Component
interface SectionCanvasProps {
  section: LayoutSection
  isSelected: boolean
  selectedBlockId: string | null
  onSelectSection: (id: string | null) => void
  onSelectBlock: (id: string | null) => void
  onDeleteSection: (id: string) => void
  onDeleteBlock: (id: string) => void
  onUpdateSection: (id: string, updates: Partial<LayoutSection>) => void
  onUpdateBlock: (id: string, updates: Partial<LayoutBlock>) => void
  onDrop: (e: React.DragEvent, sectionId: string, position?: number) => void
  onDragOver: (e: React.DragEvent) => void
  getSectionIcon: (type: SectionType) => React.ComponentType<any>
  devicePreview: 'desktop' | 'tablet' | 'mobile'
}

function SectionCanvas({
  section,
  isSelected,
  selectedBlockId,
  onSelectSection,
  onSelectBlock,
  onDeleteSection,
  onDeleteBlock,
  onUpdateSection,
  onUpdateBlock,
  onDrop,
  onDragOver,
  getSectionIcon,
  devicePreview
}: SectionCanvasProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isDragOver, setIsDragOver] = useState(false)

  const SectionIcon = getSectionIcon(section.type)

  const handleDrop = (e: React.DragEvent) => {
    setIsDragOver(false)
    onDrop(e, section.id)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
    onDragOver(e)
  }

  const handleDragLeave = () => {
    setIsDragOver(false)
  }

  const handleToggleVisibility = (e: React.MouseEvent) => {
    e.stopPropagation()
    onUpdateSection(section.id, { isVisible: !section.isVisible })
  }

  const handleDeleteSection = (e: React.MouseEvent) => {
    e.stopPropagation()
    onDeleteSection(section.id)
  }

  return (
    <div
      className={cn(
        'relative border-2 border-dashed transition-all duration-200',
        isSelected 
          ? 'border-purple-300 bg-purple-50/50' 
          : isDragOver 
            ? 'border-blue-300 bg-blue-50/50'
            : 'border-gray-200 hover:border-gray-300',
        !section.isVisible && 'opacity-50'
      )}
      onClick={() => onSelectSection(isSelected ? null : section.id)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
    >
      {/* Section Header */}
      <div className={cn(
        'flex items-center justify-between p-3 bg-white border-b transition-opacity',
        (isSelected || isHovered) ? 'opacity-100' : 'opacity-0'
      )}>
        <div className="flex items-center gap-2">
          <SectionIcon className="h-4 w-4 text-gray-600" />
          <span className="text-sm font-medium text-gray-900">{section.name}</span>
          <Badge variant="outline" className="text-xs">
            {section.type}
          </Badge>
          <Badge variant="secondary" className="text-xs">
            {section.blocks.length} blocks
          </Badge>
        </div>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggleVisibility}
            className="h-6 w-6 p-0 hover:bg-gray-100"
          >
            {section.isVisible ? (
              <Eye className="h-3 w-3" />
            ) : (
              <EyeOff className="h-3 w-3" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 hover:bg-gray-100"
          >
            <Settings className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDeleteSection}
            className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Section Content */}
      <div className="min-h-[100px] p-4">
        {section.blocks.length === 0 ? (
          <div className="flex items-center justify-center min-h-[60px] border-2 border-dashed border-gray-200 rounded-lg">
            <div className="text-center">
              <Plus className="h-6 w-6 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">
                Drop blocks here or click to add
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            {section.blocks.map((block) => (
              <BlockCanvas
                key={block.id}
                block={block}
                isSelected={selectedBlockId === block.id}
                onSelectBlock={onSelectBlock}
                onDeleteBlock={onDeleteBlock}
                onUpdateBlock={onUpdateBlock}
                devicePreview={devicePreview}
              />
            ))}
          </div>
        )}
      </div>

      {/* Drop Indicator */}
      {isDragOver && (
        <div className="absolute inset-0 bg-blue-500/10 border-2 border-blue-500 border-dashed rounded-lg flex items-center justify-center">
          <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
            Drop block here
          </div>
        </div>
      )}
    </div>
  )
}

// Block Canvas Component
interface BlockCanvasProps {
  block: LayoutBlock
  isSelected: boolean
  onSelectBlock: (id: string | null) => void
  onDeleteBlock: (id: string) => void
  onUpdateBlock: (id: string, updates: Partial<LayoutBlock>) => void
  devicePreview: 'desktop' | 'tablet' | 'mobile'
}

function BlockCanvas({
  block,
  isSelected,
  onSelectBlock,
  onDeleteBlock,
  onUpdateBlock,
  devicePreview
}: BlockCanvasProps) {
  const [isHovered, setIsHovered] = useState(false)

  const handleToggleVisibility = (e: React.MouseEvent) => {
    e.stopPropagation()
    onUpdateBlock(block.id, { isVisible: !block.isVisible })
  }

  const handleDeleteBlock = (e: React.MouseEvent) => {
    e.stopPropagation()
    onDeleteBlock(block.id)
  }

  return (
    <div
      className={cn(
        'relative border rounded-lg transition-all duration-200 cursor-pointer',
        isSelected 
          ? 'border-blue-300 bg-blue-50/50 shadow-sm' 
          : 'border-gray-200 hover:border-gray-300 bg-white',
        !block.isVisible && 'opacity-50'
      )}
      onClick={() => onSelectBlock(isSelected ? null : block.id)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Block Header */}
      <div className={cn(
        'flex items-center justify-between p-2 border-b bg-gray-50/50 transition-opacity',
        (isSelected || isHovered) ? 'opacity-100' : 'opacity-0'
      )}>
        <div className="flex items-center gap-2">
          <GripVertical className="h-3 w-3 text-gray-400 cursor-move" />
          <span className="text-xs font-medium text-gray-900">{block.name}</span>
          <Badge variant="outline" className="text-xs">
            {block.type}
          </Badge>
        </div>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggleVisibility}
            className="h-5 w-5 p-0 hover:bg-gray-100"
          >
            {block.isVisible ? (
              <Eye className="h-2.5 w-2.5" />
            ) : (
              <EyeOff className="h-2.5 w-2.5" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDeleteBlock}
            className="h-5 w-5 p-0 hover:bg-red-100 hover:text-red-600"
          >
            <Trash2 className="h-2.5 w-2.5" />
          </Button>
        </div>
      </div>

      {/* Block Content */}
      <div className="p-3">
        <SectionRenderer
          section={{
            id: 'temp',
            type: 'custom',
            name: 'temp',
            position: 0,
            blocks: [block],
            configuration: {
              layout: 'block',
              alignment: 'left',
              spacing: { top: '0', right: '0', bottom: '0', left: '0' },
              background: { type: 'none' },
              container: { maxWidth: '100%', padding: '0' }
            },
            styling: {
              background: { type: 'none' },
              border: { width: '0', style: 'none', color: 'transparent' },
              spacing: { top: '0', right: '0', bottom: '0', left: '0' },
              shadow: { type: 'none' }
            },
            responsive: {
              mobile: { isVisible: true },
              tablet: { isVisible: true },
              desktop: { isVisible: true },
              large: { isVisible: true }
            },
            isVisible: true
          }}
          isPreviewMode={false}
          devicePreview={devicePreview}
        />
      </div>
    </div>
  )
}

// Section Renderer Component (simplified for canvas)
interface SectionRendererProps {
  section: LayoutSection
  isPreviewMode: boolean
  devicePreview: 'desktop' | 'tablet' | 'mobile'
}

function SectionRenderer({ section, isPreviewMode, devicePreview }: SectionRendererProps) {
  if (!section.isVisible && isPreviewMode) {
    return null
  }

  return (
    <div className="w-full">
      {section.blocks.map((block) => (
        block.isVisible && (
          <div key={block.id} className="w-full">
            <LayoutBlockRenderer 
              block={block} 
              layout={{
                layout: {
                  id: 'temp',
                  name: 'temp',
                  type: 'page',
                  category: 'custom',
                  structure: { main: section },
                  styling: {},
                  responsive: {},
                  conditions: {},
                  isTemplate: false,
                  isSystem: false,
                  isActive: true,
                  usageCount: 0,
                  tags: [],
                  createdAt: new Date(),
                  updatedAt: new Date()
                },
                sections: [section],
                assignments: [],
                menus: [],
                widgets: []
              }}
            />
          </div>
        )
      ))}
    </div>
  )
}
