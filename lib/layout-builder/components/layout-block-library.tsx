'use client'

import React, { useState, useMemo } from 'react'
import { useLayoutBuilder } from '../context'
import { layoutBlockRegistry, LayoutBlockDefinition } from '../blocks/registry'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Search,
  Image,
  Menu,
  ShoppingCart,
  FileText,
  Package,
  Share2,
  Copyright,
  Navigation,
  Layers,
  Filter,
  Tag,
  Link,
  Settings
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { SectionType } from '../types'

interface LayoutBlockLibraryProps {
  selectedSection?: SectionType | null
}

export function LayoutBlockLibrary({ selectedSection }: LayoutBlockLibraryProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const { addBlock, state } = useLayoutBuilder()

  // Get all blocks and organize by category
  const allBlocks = layoutBlockRegistry.getAllBlocks()
  
  // Filter blocks based on selected section
  const availableBlocks = useMemo(() => {
    if (!selectedSection) return allBlocks
    return allBlocks.filter(block => 
      block.allowedSections.includes(selectedSection)
    )
  }, [allBlocks, selectedSection])

  // Get categories from available blocks
  const categories = useMemo(() => {
    const cats = new Set(['all'])
    availableBlocks.forEach(block => cats.add(block.category))
    return Array.from(cats)
  }, [availableBlocks])

  // Filter and search blocks
  const filteredBlocks = useMemo(() => {
    let blocks = availableBlocks

    // Filter by category
    if (selectedCategory !== 'all') {
      blocks = blocks.filter(block => block.category === selectedCategory)
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      blocks = blocks.filter(block =>
        block.name.toLowerCase().includes(query) ||
        block.description.toLowerCase().includes(query) ||
        block.category.toLowerCase().includes(query)
      )
    }

    return blocks
  }, [availableBlocks, selectedCategory, searchQuery])

  // Group blocks by category for display
  const blocksByCategory = useMemo(() => {
    const grouped: Record<string, LayoutBlockDefinition[]> = {}
    
    categories.forEach(category => {
      if (category === 'all') {
        grouped[category] = filteredBlocks
      } else {
        grouped[category] = filteredBlocks.filter(block => block.category === category)
      }
    })
    
    return grouped
  }, [categories, filteredBlocks])

  // Handle adding a block
  const handleAddBlock = (blockType: string) => {
    if (!selectedSection) {
      // Find the first available section for this block
      const blockDef = layoutBlockRegistry.getBlockType(blockType as any)
      if (blockDef && blockDef.allowedSections.length > 0) {
        const firstSection = Object.keys(state.layout.structure).find(sectionKey => {
          const section = state.layout.structure[sectionKey as keyof typeof state.layout.structure]
          return section && blockDef.allowedSections.includes(section.type)
        })
        
        if (firstSection) {
          const section = state.layout.structure[firstSection as keyof typeof state.layout.structure]
          if (section) {
            addBlock(section.id, blockType as any)
          }
        }
      }
    } else {
      // Find the section with the selected type
      const sectionEntry = Object.entries(state.layout.structure).find(([_, section]) => 
        section?.type === selectedSection
      )
      
      if (sectionEntry && sectionEntry[1]) {
        addBlock(sectionEntry[1].id, blockType as any)
      }
    }
  }

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, blockType: string) => {
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'layout-block',
      blockType
    }))
  }

  // Get icon component for block type
  const getBlockIcon = (iconName: string, category: string) => {
    const iconMap: Record<string, React.ComponentType<any>> = {
      'Image': Image,
      'Menu': Menu,
      'Search': Search,
      'ShoppingCart': ShoppingCart,
      'FileText': FileText,
      'Package': Package,
      'Share2': Share2,
      'Copyright': Copyright,
      'Navigation': Navigation,
      'Layers': Layers,
      'Filter': Filter,
      'Tag': Tag,
      'Link': Link,
      'Settings': Settings
    }

    const IconComponent = iconMap[iconName]
    if (IconComponent) {
      return <IconComponent className="h-5 w-5" />
    }

    // Fallback icons based on category
    const categoryIcons: Record<string, React.ComponentType<any>> = {
      navigation: Navigation,
      content: FileText,
      media: Image,
      ecommerce: ShoppingCart,
      social: Share2,
      utility: Settings
    }

    const CategoryIcon = categoryIcons[category] || Package
    return <CategoryIcon className="h-5 w-5" />
  }

  // Category display names and colors
  const categoryConfig: Record<string, { name: string; color: string; icon: React.ComponentType<any> }> = {
    all: { name: 'All Blocks', color: 'bg-gray-100 text-gray-700', icon: Layers },
    navigation: { name: 'Navigation', color: 'bg-blue-100 text-blue-700', icon: Navigation },
    content: { name: 'Content', color: 'bg-green-100 text-green-700', icon: FileText },
    media: { name: 'Media', color: 'bg-purple-100 text-purple-700', icon: Image },
    ecommerce: { name: 'E-commerce', color: 'bg-orange-100 text-orange-700', icon: ShoppingCart },
    social: { name: 'Social', color: 'bg-pink-100 text-pink-700', icon: Share2 },
    utility: { name: 'Utility', color: 'bg-indigo-100 text-indigo-700', icon: Settings }
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Gutenberg-style Header */}
      <div className="px-4 py-3 border-b border-gray-200">
        {selectedSection && (
          <div className="mb-3">
            <Badge variant="secondary" className="text-xs">
              {selectedSection} section
            </Badge>
          </div>
        )}

        {/* Search Bar - Gutenberg style */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Gutenberg-style Categories */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="h-full flex flex-col">
          {/* Category Tabs - Gutenberg style */}
          <div className="border-b border-gray-200">
            <ScrollArea className="w-full">
              <TabsList className="inline-flex h-auto p-0 bg-transparent">
                {categories.map((category) => {
                  const config = categoryConfig[category]
                  const Icon = config?.icon || Package

                  return (
                    <TabsTrigger
                      key={category}
                      value={category}
                      className={cn(
                        'flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 border-transparent',
                        'data-[state=active]:border-blue-500 data-[state=active]:text-blue-600',
                        'hover:text-gray-700 hover:bg-gray-50',
                        'transition-colors duration-200'
                      )}
                    >
                      <Icon className="h-4 w-4" />
                      <span>{config?.name || category}</span>
                    </TabsTrigger>
                  )
                })}
              </TabsList>
            </ScrollArea>
          </div>

          {/* Gutenberg-style Block Grid */}
          <div className="flex-1 overflow-hidden">
            {categories.map((category) => (
              <TabsContent
                key={category}
                value={category}
                className="h-full mt-0 data-[state=active]:flex data-[state=active]:flex-col"
              >
                <ScrollArea className="flex-1 p-4">
                  {blocksByCategory[category]?.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-gray-400 mb-4">
                        <Package className="h-12 w-12 mx-auto" />
                      </div>
                      <p className="text-sm text-gray-500">
                        {searchQuery ? 'No blocks found' : 'No blocks in this category'}
                      </p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 gap-2">
                      {blocksByCategory[category]?.map((block) => (
                        <GutenbergLayoutBlockItem
                          key={block.type}
                          block={block}
                          onDragStart={(e: React.DragEvent) => handleDragStart(e, block.type)}
                          onClick={() => handleAddBlock(block.type)}
                          getIcon={getBlockIcon}
                        />
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </TabsContent>
            ))}
          </div>
        </Tabs>
      </div>
    </div>
  )
}

// Gutenberg-style layout block item
interface GutenbergLayoutBlockItemProps {
  block: LayoutBlockDefinition
  onDragStart: (e: React.DragEvent) => void
  onClick: () => void
  getIcon: (iconName: string, category: string) => React.ReactNode
}

function GutenbergLayoutBlockItem({ block, onDragStart, onClick, getIcon }: GutenbergLayoutBlockItemProps) {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <div
      className={cn(
        'group relative bg-white border border-gray-200 rounded-lg p-3 cursor-pointer transition-all duration-200',
        'hover:border-blue-400 hover:shadow-sm',
        isHovered && 'border-blue-400 shadow-sm'
      )}
      draggable
      onDragStart={onDragStart}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Block Icon - Gutenberg style */}
      <div className="flex flex-col items-center text-center space-y-2">
        <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="text-gray-600">
            {getIcon(block.icon, block.category)}
          </div>
        </div>

        {/* Block Name */}
        <div className="w-full">
          <h3 className="text-xs font-medium text-gray-900 truncate">
            {block.name}
          </h3>
        </div>
      </div>

      {/* Hover overlay - subtle like Gutenberg */}
      <div className={cn(
        'absolute inset-0 bg-blue-50 rounded-lg opacity-0 transition-opacity pointer-events-none',
        isHovered && 'opacity-30'
      )} />
    </div>
  )
}
