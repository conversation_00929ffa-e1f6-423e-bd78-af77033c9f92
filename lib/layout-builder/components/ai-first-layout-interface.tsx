'use client'

import React, { useState, useRef, useEffect } from 'react'
import { useLayoutBuilder } from '../context'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Sparkles,
  MessageSquare,
  Wand2,
  Brain,
  Send,
  Loader2,
  Lightbulb,
  Zap,
  Target,
  Palette,
  Layout,
  Navigation,
  Package,
  Copyright,
  FileText,
  Star,
  Plus,
  ChevronDown,
  ChevronUp,
  Layers
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'
import { SectionType } from '../types'

interface AIFirstLayoutInterfaceProps {
  className?: string
}

export function AIFirstLayoutInterface({ className }: AIFirstLayoutInterfaceProps) {
  const { state, addSection, addBlock } = useLayoutBuilder()
  const [activeTab, setActiveTab] = useState<'chat' | 'suggestions' | 'templates'>('chat')
  const [isExpanded, setIsExpanded] = useState(true)
  const [input, setInput] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [messages, setMessages] = useState<any[]>([])
  const inputRef = useRef<HTMLInputElement>(null)

  // Quick action prompts for common layout tasks
  const quickActions = [
    {
      icon: Navigation,
      label: 'Create Header',
      prompt: 'Create a modern header with logo, navigation menu, and user account area',
      category: 'layout',
      color: 'bg-blue-500',
      sectionType: 'header' as SectionType
    },
    {
      icon: Package,
      label: 'Add Sidebar',
      prompt: 'Add a sidebar with widget areas for additional content and navigation',
      category: 'layout',
      color: 'bg-green-500',
      sectionType: 'sidebar' as SectionType
    },
    {
      icon: Copyright,
      label: 'Design Footer',
      prompt: 'Create a comprehensive footer with links, contact info, and social media',
      category: 'layout',
      color: 'bg-gray-500',
      sectionType: 'footer' as SectionType
    },
    {
      icon: Layout,
      label: 'Complete Layout',
      prompt: 'Generate a complete site layout with header, main content, sidebar, and footer',
      category: 'template',
      color: 'bg-purple-500',
      sectionType: null
    },
    {
      icon: Palette,
      label: 'E-commerce Layout',
      prompt: 'Create an e-commerce layout optimized for product browsing and shopping',
      category: 'template',
      color: 'bg-orange-500',
      sectionType: null
    },
    {
      icon: FileText,
      label: 'Blog Layout',
      prompt: 'Design a blog-focused layout with content areas and reading optimization',
      category: 'template',
      color: 'bg-indigo-500',
      sectionType: null
    }
  ]

  // AI suggestions based on current layout structure
  const aiSuggestions = [
    {
      type: 'improvement',
      title: 'Add Navigation Menu',
      description: 'Include a main navigation menu in your header',
      action: () => handleQuickAction('Add a responsive navigation menu to the header section with dropdown support')
    },
    {
      type: 'content',
      title: 'Footer Content',
      description: 'Add essential footer elements like links and contact info',
      action: () => handleQuickAction('Create footer content with company links, contact information, and social media icons')
    },
    {
      type: 'layout',
      title: 'Responsive Design',
      description: 'Optimize layout for mobile and tablet devices',
      action: () => handleQuickAction('Optimize the current layout for mobile and tablet responsiveness')
    },
    {
      type: 'seo',
      title: 'SEO Structure',
      description: 'Improve layout structure for better search engine optimization',
      action: () => handleQuickAction('Enhance layout structure with proper semantic HTML and SEO optimization')
    }
  ]

  // Layout templates for quick starts
  const layoutTemplates = [
    {
      name: 'E-commerce Store',
      description: 'Complete layout for online store with product focus',
      sections: ['header', 'main', 'sidebar', 'footer'],
      features: ['Product navigation', 'Shopping cart', 'User account', 'Search'],
      style: 'modern'
    },
    {
      name: 'Corporate Website',
      description: 'Professional business layout with service focus',
      sections: ['header', 'main', 'footer'],
      features: ['Company navigation', 'Service areas', 'Contact forms', 'Testimonials'],
      style: 'professional'
    },
    {
      name: 'Blog Platform',
      description: 'Content-focused layout optimized for reading',
      sections: ['header', 'main', 'sidebar', 'footer'],
      features: ['Article navigation', 'Category filters', 'Author info', 'Related posts'],
      style: 'minimal'
    },
    {
      name: 'Portfolio Site',
      description: 'Creative layout showcasing work and projects',
      sections: ['header', 'main', 'footer'],
      features: ['Project gallery', 'About section', 'Contact form', 'Social links'],
      style: 'creative'
    }
  ]

  const handleQuickAction = async (prompt: string, sectionType?: SectionType | null) => {
    setIsGenerating(true)
    setInput(prompt)
    
    try {
      // Mock AI processing - in production, this would call an AI service
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      if (sectionType) {
        // Add specific section
        addSection(sectionType)
        toast.success(`${sectionType.charAt(0).toUpperCase() + sectionType.slice(1)} section created!`)
      } else {
        // Generate complete layout or complex structure
        toast.success('Layout structure generated!')
      }
      
      // Add to messages
      setMessages(prev => [
        ...prev,
        { role: 'user', content: prompt },
        { role: 'assistant', content: `I've ${sectionType ? `created a ${sectionType} section` : 'generated the layout structure'} based on your request. The layout has been optimized for your needs.` }
      ])
    } catch (error) {
      console.error('Quick action error:', error)
      toast.error('Failed to execute AI action')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleCustomSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim()) return
    
    setIsGenerating(true)
    
    try {
      // Mock AI processing
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Add to messages
      setMessages(prev => [
        ...prev,
        { role: 'user', content: input },
        { role: 'assistant', content: 'I understand your layout requirements. Let me help you create the optimal structure for your needs.' }
      ])
      
      setInput('')
      toast.success('AI is processing your layout request!')
    } catch (error) {
      console.error('Custom submit error:', error)
      toast.error('Failed to process AI request')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleTemplateGenerate = async (template: any) => {
    setIsGenerating(true)
    
    try {
      // Mock template generation
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // Add sections based on template
      for (const sectionType of template.sections) {
        if (sectionType !== 'main') { // main section usually exists by default
          addSection(sectionType as SectionType)
        }
      }
      
      toast.success(`${template.name} layout template generated!`)
    } catch (error) {
      console.error('Template generation error:', error)
      toast.error('Failed to generate template')
    } finally {
      setIsGenerating(false)
    }
  }

  // Focus input when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  return (
    <Card className={cn('h-full flex flex-col', className)}>
      {/* Header */}
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Brain className="h-5 w-5 text-purple-600" />
            AI Layout Designer
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>
        <p className="text-sm text-muted-foreground">
          Describe your layout needs and let AI create the perfect structure
        </p>
      </CardHeader>

      {isExpanded && (
        <CardContent className="flex-1 flex flex-col p-0">
          {/* AI Input */}
          <div className="p-4 border-b">
            <form onSubmit={handleCustomSubmit} className="space-y-3">
              <div className="flex gap-2">
                <Input
                  ref={inputRef}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Describe your layout requirements... e.g., 'Create an e-commerce layout with header navigation and product sidebar'"
                  disabled={isGenerating}
                  className="flex-1"
                />
                <Button
                  type="submit"
                  size="sm"
                  disabled={isGenerating || !input.trim()}
                  className="px-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                >
                  {isGenerating ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
              
              {isGenerating && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Loader2 className="h-3 w-3 animate-spin" />
                  AI is designing your layout...
                </div>
              )}
            </form>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-3 mx-4 mt-4">
              <TabsTrigger value="chat" className="text-xs">
                <MessageSquare className="h-3 w-3 mr-1" />
                Chat
              </TabsTrigger>
              <TabsTrigger value="suggestions" className="text-xs">
                <Lightbulb className="h-3 w-3 mr-1" />
                Suggestions
              </TabsTrigger>
              <TabsTrigger value="templates" className="text-xs">
                <Wand2 className="h-3 w-3 mr-1" />
                Templates
              </TabsTrigger>
            </TabsList>

            {/* Chat Tab */}
            <TabsContent value="chat" className="flex-1 flex flex-col mt-4">
              <ScrollArea className="flex-1 px-4">
                {messages.length === 0 ? (
                  <div className="space-y-3">
                    <div className="text-center py-6">
                      <Sparkles className="h-8 w-8 mx-auto mb-3 text-purple-500" />
                      <h3 className="font-medium mb-2">Start with AI Layout Design</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Choose a quick action or describe your layout requirements
                      </p>
                    </div>
                    
                    {/* Quick Actions Grid */}
                    <div className="grid grid-cols-1 gap-2">
                      {quickActions.map((action, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => handleQuickAction(action.prompt, action.sectionType)}
                          disabled={isGenerating}
                          className="justify-start h-auto p-3 text-left"
                        >
                          <div className={cn('w-8 h-8 rounded-lg flex items-center justify-center mr-3', action.color)}>
                            <action.icon className="h-4 w-4 text-white" />
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-sm">{action.label}</div>
                            <div className="text-xs text-muted-foreground">{action.prompt}</div>
                          </div>
                        </Button>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {messages.map((message, index) => (
                      <div
                        key={index}
                        className={cn(
                          'p-3 rounded-lg max-w-[85%]',
                          message.role === 'user'
                            ? 'bg-purple-100 ml-auto text-purple-900'
                            : 'bg-gray-100 text-gray-900'
                        )}
                      >
                        <div className="text-sm">{message.content}</div>
                      </div>
                    ))}
                    
                    {messages.length > 0 && (
                      <div className="flex justify-center pt-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setMessages([])}
                          className="text-xs"
                        >
                          Clear Conversation
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>

            {/* Suggestions Tab */}
            <TabsContent value="suggestions" className="flex-1 mt-4">
              <ScrollArea className="h-full px-4">
                <div className="space-y-3">
                  {aiSuggestions.map((suggestion, index) => (
                    <Card key={index} className="p-3">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Lightbulb className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-sm mb-1">{suggestion.title}</h4>
                          <p className="text-xs text-muted-foreground mb-2">{suggestion.description}</p>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={suggestion.action}
                            disabled={isGenerating}
                            className="text-xs h-7"
                          >
                            <Zap className="h-3 w-3 mr-1" />
                            Apply
                          </Button>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {suggestion.type}
                        </Badge>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Templates Tab */}
            <TabsContent value="templates" className="flex-1 mt-4">
              <ScrollArea className="h-full px-4">
                <div className="space-y-3">
                  {layoutTemplates.map((template, index) => (
                    <Card key={index} className="p-3">
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-medium text-sm mb-1">{template.name}</h4>
                          <p className="text-xs text-muted-foreground">{template.description}</p>
                        </div>
                        
                        <div className="flex flex-wrap gap-1">
                          {template.sections.map((section) => (
                            <Badge key={section} variant="outline" className="text-xs">
                              {section}
                            </Badge>
                          ))}
                        </div>
                        
                        <div className="flex flex-wrap gap-1">
                          {template.features.map((feature) => (
                            <Badge key={feature} variant="secondary" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                        </div>
                        
                        <Button
                          size="sm"
                          onClick={() => handleTemplateGenerate(template)}
                          disabled={isGenerating}
                          className="w-full text-xs h-7"
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Generate Layout
                        </Button>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      )}
    </Card>
  )
}
