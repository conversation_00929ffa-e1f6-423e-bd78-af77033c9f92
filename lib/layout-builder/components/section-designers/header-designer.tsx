'use client'

import React, { useState } from 'react'
import { useLayoutBuilder } from '../../context'
import { LayoutSection, SectionType } from '../../types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Navigation, 
  Image, 
  Search, 
  ShoppingCart, 
  Plus, 
  Settings, 
  Palette, 
  Layout,
  Eye,
  EyeOff,
  Trash2,
  GripVertical
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { layoutBlockRegistry } from '../../blocks/registry'

export function HeaderDesigner() {
  const { state, updateSection, addBlock, updateBlock, deleteBlock } = useLayoutBuilder()
  const [activeTab, setActiveTab] = useState<'layout' | 'blocks' | 'styling'>('layout')

  // Get or create header section
  const headerSection = state.layout.structure.header || createDefaultHeaderSection()

  const createDefaultHeaderSection = (): LayoutSection => ({
    id: 'header-section',
    type: 'header',
    name: 'Header',
    position: 0,
    blocks: [],
    configuration: {
      layout: 'flex',
      alignment: 'center',
      spacing: { top: '1rem', right: '2rem', bottom: '1rem', left: '2rem' },
      background: { type: 'color', color: '#ffffff' },
      container: { 
        maxWidth: '1200px', 
        padding: { top: '0', right: '1rem', bottom: '0', left: '1rem' },
        margin: { top: '0', right: 'auto', bottom: '0', left: 'auto' },
        centered: true
      }
    },
    styling: {
      background: { type: 'color', color: '#ffffff' },
      border: { width: '0', style: 'none', color: 'transparent', radius: '0' },
      spacing: { top: '0', right: '0', bottom: '1px', left: '0' },
      shadow: { type: 'box', x: 0, y: 1, blur: 3, spread: 0, color: 'rgba(0,0,0,0.1)' }
    },
    responsive: {
      mobile: { 
        display: 'flex', 
        width: '100%', 
        height: 'auto',
        spacing: { top: '0.5rem', right: '1rem', bottom: '0.5rem', left: '1rem' },
        typography: { 
          fontFamily: 'inherit', 
          fontSize: '14px', 
          fontWeight: 'normal', 
          lineHeight: '1.5',
          letterSpacing: '0',
          textAlign: 'left',
          textTransform: 'none'
        }
      },
      tablet: { 
        display: 'flex', 
        width: '100%', 
        height: 'auto',
        spacing: { top: '0.75rem', right: '1.5rem', bottom: '0.75rem', left: '1.5rem' },
        typography: { 
          fontFamily: 'inherit', 
          fontSize: '15px', 
          fontWeight: 'normal', 
          lineHeight: '1.5',
          letterSpacing: '0',
          textAlign: 'left',
          textTransform: 'none'
        }
      },
      desktop: { 
        display: 'flex', 
        width: '100%', 
        height: 'auto',
        spacing: { top: '1rem', right: '2rem', bottom: '1rem', left: '2rem' },
        typography: { 
          fontFamily: 'inherit', 
          fontSize: '16px', 
          fontWeight: 'normal', 
          lineHeight: '1.5',
          letterSpacing: '0',
          textAlign: 'left',
          textTransform: 'none'
        }
      },
      large: { 
        display: 'flex', 
        width: '100%', 
        height: 'auto',
        spacing: { top: '1rem', right: '2rem', bottom: '1rem', left: '2rem' },
        typography: { 
          fontFamily: 'inherit', 
          fontSize: '16px', 
          fontWeight: 'normal', 
          lineHeight: '1.5',
          letterSpacing: '0',
          textAlign: 'left',
          textTransform: 'none'
        }
      }
    },
    isVisible: true
  })

  // Get header-specific blocks
  const headerBlocks = layoutBlockRegistry.getBlocksForSection('header')

  const handleAddBlock = (blockType: string) => {
    addBlock(headerSection.id, blockType as any)
  }

  const handleUpdateSection = (updates: Partial<LayoutSection>) => {
    updateSection(headerSection.id, updates)
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="flex items-center gap-2 mb-3">
          <div className="p-1.5 bg-blue-100 rounded-lg">
            <Navigation className="h-4 w-4 text-blue-600" />
          </div>
          <h2 className="text-lg font-semibold text-gray-900">Header Designer</h2>
          <Badge variant="outline" className="text-xs">
            {headerSection.blocks.length} blocks
          </Badge>
        </div>
        
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <span>Design your site header with logo, navigation, and utility blocks</span>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="layout" className="text-xs">
              <Layout className="h-3 w-3 mr-1" />
              Layout
            </TabsTrigger>
            <TabsTrigger value="blocks" className="text-xs">
              <Plus className="h-3 w-3 mr-1" />
              Blocks
            </TabsTrigger>
            <TabsTrigger value="styling" className="text-xs">
              <Palette className="h-3 w-3 mr-1" />
              Styling
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          {/* Layout Tab */}
          <TabsContent value="layout" className="h-full mt-0 p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Header Configuration</CardTitle>
                <CardDescription className="text-xs">
                  Configure the basic layout and container settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="header-layout" className="text-xs">Layout Type</Label>
                    <Select
                      value={headerSection.configuration.layout}
                      onValueChange={(value) => handleUpdateSection({
                        configuration: {
                          ...headerSection.configuration,
                          layout: value as 'flex' | 'grid' | 'block'
                        }
                      })}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="flex">Flex</SelectItem>
                        <SelectItem value="grid">Grid</SelectItem>
                        <SelectItem value="block">Block</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="header-alignment" className="text-xs">Alignment</Label>
                    <Select
                      value={headerSection.configuration.alignment}
                      onValueChange={(value) => handleUpdateSection({
                        configuration: {
                          ...headerSection.configuration,
                          alignment: value as 'left' | 'center' | 'right' | 'justify'
                        }
                      })}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                        <SelectItem value="justify">Justify</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="header-max-width" className="text-xs">Max Width</Label>
                  <Input
                    id="header-max-width"
                    value={headerSection.configuration.container.maxWidth}
                    onChange={(e) => handleUpdateSection({
                      configuration: {
                        ...headerSection.configuration,
                        container: {
                          ...headerSection.configuration.container,
                          maxWidth: e.target.value
                        }
                      }
                    })}
                    className="h-8"
                    placeholder="1200px"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="header-visible" className="text-xs">Visible</Label>
                  <Switch
                    id="header-visible"
                    checked={headerSection.isVisible}
                    onCheckedChange={(checked) => handleUpdateSection({ isVisible: checked })}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Blocks Tab */}
          <TabsContent value="blocks" className="h-full mt-0 p-4 space-y-4">
            {/* Current Blocks */}
            {headerSection.blocks.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Current Blocks</CardTitle>
                  <CardDescription className="text-xs">
                    Manage blocks in your header
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {headerSection.blocks.map((block) => (
                      <div
                        key={block.id}
                        className="flex items-center justify-between p-2 border rounded-lg hover:bg-gray-50"
                      >
                        <div className="flex items-center gap-2">
                          <GripVertical className="h-3 w-3 text-gray-400 cursor-move" />
                          <span className="text-sm font-medium">{block.name}</span>
                          <Badge variant="outline" className="text-xs">{block.type}</Badge>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => updateBlock(block.id, { isVisible: !block.isVisible })}
                            className="h-6 w-6 p-0"
                          >
                            {block.isVisible ? (
                              <Eye className="h-3 w-3" />
                            ) : (
                              <EyeOff className="h-3 w-3" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteBlock(block.id)}
                            className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Available Blocks */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Available Header Blocks</CardTitle>
                <CardDescription className="text-xs">
                  Add blocks to build your header
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-2">
                  {headerBlocks.map((blockDef) => (
                    <Button
                      key={blockDef.type}
                      variant="outline"
                      onClick={() => handleAddBlock(blockDef.type)}
                      className="justify-start h-auto p-3"
                    >
                      <div className="flex items-center gap-3 w-full">
                        <div className="p-1 bg-gray-100 rounded">
                          {blockDef.type === 'logo' && <Image className="h-3 w-3" />}
                          {blockDef.type === 'navigation' && <Navigation className="h-3 w-3" />}
                          {blockDef.type === 'search' && <Search className="h-3 w-3" />}
                          {blockDef.type === 'cart' && <ShoppingCart className="h-3 w-3" />}
                          {blockDef.type === 'social' && <Settings className="h-3 w-3" />}
                        </div>
                        <div className="text-left">
                          <div className="font-medium text-xs">{blockDef.name}</div>
                          <div className="text-xs text-muted-foreground">{blockDef.description}</div>
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Styling Tab */}
          <TabsContent value="styling" className="h-full mt-0 p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Header Styling</CardTitle>
                <CardDescription className="text-xs">
                  Customize the appearance of your header
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-xs text-muted-foreground">
                  Advanced styling controls will be implemented here
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
