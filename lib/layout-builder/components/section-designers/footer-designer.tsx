'use client'

import React, { useState } from 'react'
import { useLayoutBuilder } from '../../context'
import { LayoutSection } from '../../types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Copyright, 
  Share2, 
  Navigation, 
  FileText, 
  Plus, 
  Settings, 
  Palette, 
  Layout,
  Eye,
  EyeOff,
  Trash2,
  GripVertical,
  Link
} from 'lucide-react'
import { layoutBlockRegistry } from '../../blocks/registry'

export function FooterDesigner() {
  const { state, updateSection, addBlock, updateBlock, deleteBlock } = useLayoutBuilder()
  const [activeTab, setActiveTab] = useState<'layout' | 'blocks' | 'styling'>('layout')

  // Get or create footer section
  const footerSection = state.layout.structure.footer || createDefaultFooterSection()

  const createDefaultFooterSection = (): LayoutSection => ({
    id: 'footer-section',
    type: 'footer',
    name: 'Footer',
    position: 3,
    blocks: [],
    configuration: {
      layout: 'grid',
      alignment: 'center',
      spacing: { top: '2rem', right: '2rem', bottom: '2rem', left: '2rem' },
      background: { type: 'color', color: '#1f2937' },
      container: { 
        maxWidth: '1200px', 
        padding: { top: '2rem', right: '1rem', bottom: '2rem', left: '1rem' },
        margin: { top: '0', right: 'auto', bottom: '0', left: 'auto' },
        centered: true
      }
    },
    styling: {
      background: { type: 'color', color: '#1f2937' },
      border: { width: '1px', style: 'solid', color: '#374151', radius: '0' },
      spacing: { top: '1px', right: '0', bottom: '0', left: '0' },
      shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' }
    },
    responsive: {
      mobile: { 
        display: 'block', 
        width: '100%', 
        height: 'auto',
        spacing: { top: '1rem', right: '1rem', bottom: '1rem', left: '1rem' },
        typography: { 
          fontFamily: 'inherit', 
          fontSize: '14px', 
          fontWeight: 'normal', 
          lineHeight: '1.5',
          letterSpacing: '0',
          textAlign: 'center',
          textTransform: 'none'
        }
      },
      tablet: { 
        display: 'grid', 
        width: '100%', 
        height: 'auto',
        spacing: { top: '1.5rem', right: '1.5rem', bottom: '1.5rem', left: '1.5rem' },
        typography: { 
          fontFamily: 'inherit', 
          fontSize: '14px', 
          fontWeight: 'normal', 
          lineHeight: '1.5',
          letterSpacing: '0',
          textAlign: 'left',
          textTransform: 'none'
        }
      },
      desktop: { 
        display: 'grid', 
        width: '100%', 
        height: 'auto',
        spacing: { top: '2rem', right: '2rem', bottom: '2rem', left: '2rem' },
        typography: { 
          fontFamily: 'inherit', 
          fontSize: '14px', 
          fontWeight: 'normal', 
          lineHeight: '1.5',
          letterSpacing: '0',
          textAlign: 'left',
          textTransform: 'none'
        }
      },
      large: { 
        display: 'grid', 
        width: '100%', 
        height: 'auto',
        spacing: { top: '2rem', right: '2rem', bottom: '2rem', left: '2rem' },
        typography: { 
          fontFamily: 'inherit', 
          fontSize: '14px', 
          fontWeight: 'normal', 
          lineHeight: '1.5',
          letterSpacing: '0',
          textAlign: 'left',
          textTransform: 'none'
        }
      }
    },
    isVisible: true
  })

  // Get footer-specific blocks
  const footerBlocks = layoutBlockRegistry.getBlocksForSection('footer')

  const handleAddBlock = (blockType: string) => {
    addBlock(footerSection.id, blockType as any)
  }

  const handleUpdateSection = (updates: Partial<LayoutSection>) => {
    updateSection(footerSection.id, updates)
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b bg-gradient-to-r from-gray-50 to-slate-50">
        <div className="flex items-center gap-2 mb-3">
          <div className="p-1.5 bg-gray-100 rounded-lg">
            <Copyright className="h-4 w-4 text-gray-600" />
          </div>
          <h2 className="text-lg font-semibold text-gray-900">Footer Designer</h2>
          <Badge variant="outline" className="text-xs">
            {footerSection.blocks.length} blocks
          </Badge>
        </div>
        
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <span>Design your site footer with links, social media, and copyright information</span>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="layout" className="text-xs">
              <Layout className="h-3 w-3 mr-1" />
              Layout
            </TabsTrigger>
            <TabsTrigger value="blocks" className="text-xs">
              <Plus className="h-3 w-3 mr-1" />
              Blocks
            </TabsTrigger>
            <TabsTrigger value="styling" className="text-xs">
              <Palette className="h-3 w-3 mr-1" />
              Styling
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          {/* Layout Tab */}
          <TabsContent value="layout" className="h-full mt-0 p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Footer Configuration</CardTitle>
                <CardDescription className="text-xs">
                  Configure the footer layout and container settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="footer-layout" className="text-xs">Layout Type</Label>
                    <Select
                      value={footerSection.configuration.layout}
                      onValueChange={(value) => handleUpdateSection({
                        configuration: {
                          ...footerSection.configuration,
                          layout: value as 'flex' | 'grid' | 'block'
                        }
                      })}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="grid">Grid</SelectItem>
                        <SelectItem value="flex">Flex</SelectItem>
                        <SelectItem value="block">Block</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="footer-alignment" className="text-xs">Alignment</Label>
                    <Select
                      value={footerSection.configuration.alignment}
                      onValueChange={(value) => handleUpdateSection({
                        configuration: {
                          ...footerSection.configuration,
                          alignment: value as 'left' | 'center' | 'right' | 'justify'
                        }
                      })}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                        <SelectItem value="justify">Justify</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="footer-max-width" className="text-xs">Max Width</Label>
                  <Input
                    id="footer-max-width"
                    value={footerSection.configuration.container.maxWidth}
                    onChange={(e) => handleUpdateSection({
                      configuration: {
                        ...footerSection.configuration,
                        container: {
                          ...footerSection.configuration.container,
                          maxWidth: e.target.value
                        }
                      }
                    })}
                    className="h-8"
                    placeholder="1200px"
                  />
                </div>

                <div className="space-y-3">
                  <Label className="text-xs font-medium">Footer Columns</Label>
                  <div className="grid grid-cols-4 gap-2">
                    {[1, 2, 3, 4].map((cols) => (
                      <Button
                        key={cols}
                        variant="outline"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={() => {
                          // This would set grid columns in the future
                          console.log(`Set ${cols} columns`)
                        }}
                      >
                        {cols} Col
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="footer-visible" className="text-xs">Visible</Label>
                  <Switch
                    id="footer-visible"
                    checked={footerSection.isVisible}
                    onCheckedChange={(checked) => handleUpdateSection({ isVisible: checked })}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Blocks Tab */}
          <TabsContent value="blocks" className="h-full mt-0 p-4 space-y-4">
            {/* Current Blocks */}
            {footerSection.blocks.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Current Blocks</CardTitle>
                  <CardDescription className="text-xs">
                    Manage blocks in your footer
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {footerSection.blocks.map((block) => (
                      <div
                        key={block.id}
                        className="flex items-center justify-between p-2 border rounded-lg hover:bg-gray-50"
                      >
                        <div className="flex items-center gap-2">
                          <GripVertical className="h-3 w-3 text-gray-400 cursor-move" />
                          <span className="text-sm font-medium">{block.name}</span>
                          <Badge variant="outline" className="text-xs">{block.type}</Badge>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => updateBlock(block.id, { isVisible: !block.isVisible })}
                            className="h-6 w-6 p-0"
                          >
                            {block.isVisible ? (
                              <Eye className="h-3 w-3" />
                            ) : (
                              <EyeOff className="h-3 w-3" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteBlock(block.id)}
                            className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Available Blocks */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Available Footer Blocks</CardTitle>
                <CardDescription className="text-xs">
                  Add blocks to build your footer
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-2">
                  {footerBlocks.map((blockDef) => (
                    <Button
                      key={blockDef.type}
                      variant="outline"
                      onClick={() => handleAddBlock(blockDef.type)}
                      className="justify-start h-auto p-3"
                    >
                      <div className="flex items-center gap-3 w-full">
                        <div className="p-1 bg-gray-100 rounded">
                          {blockDef.type === 'copyright' && <Copyright className="h-3 w-3" />}
                          {blockDef.type === 'social' && <Share2 className="h-3 w-3" />}
                          {blockDef.type === 'navigation' && <Navigation className="h-3 w-3" />}
                          {blockDef.type === 'links' && <Link className="h-3 w-3" />}
                          {blockDef.type === 'content' && <FileText className="h-3 w-3" />}
                        </div>
                        <div className="text-left">
                          <div className="font-medium text-xs">{blockDef.name}</div>
                          <div className="text-xs text-muted-foreground">{blockDef.description}</div>
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Styling Tab */}
          <TabsContent value="styling" className="h-full mt-0 p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Footer Styling</CardTitle>
                <CardDescription className="text-xs">
                  Customize the appearance of your footer
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-xs text-muted-foreground">
                  Advanced styling controls will be implemented here
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
