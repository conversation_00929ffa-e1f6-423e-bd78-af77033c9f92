'use client'

import React, { useState } from 'react'
import { useLayoutBuilder } from '../../context'
import { LayoutSection } from '../../types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Package, 
  Search, 
  FileText, 
  Tag, 
  Plus, 
  Settings, 
  Palette, 
  Layout,
  Eye,
  EyeOff,
  Trash2,
  GripVertical,
  Filter
} from 'lucide-react'
import { layoutBlockRegistry } from '../../blocks/registry'

export function SidebarDesigner() {
  const { state, updateSection, addBlock, updateBlock, deleteBlock } = useLayoutBuilder()
  const [activeTab, setActiveTab] = useState<'layout' | 'blocks' | 'styling'>('layout')

  // Get or create sidebar section
  const sidebarSection = state.layout.structure.sidebar || createDefaultSidebarSection()

  const createDefaultSidebarSection = (): LayoutSection => ({
    id: 'sidebar-section',
    type: 'sidebar',
    name: 'Sidebar',
    position: 2,
    blocks: [],
    configuration: {
      layout: 'block',
      alignment: 'left',
      spacing: { top: '1rem', right: '1rem', bottom: '1rem', left: '1rem' },
      background: { type: 'color', color: '#f9fafb' },
      container: { 
        maxWidth: '300px', 
        padding: { top: '1rem', right: '1rem', bottom: '1rem', left: '1rem' },
        margin: { top: '0', right: '0', bottom: '0', left: '0' },
        centered: false
      }
    },
    styling: {
      background: { type: 'color', color: '#f9fafb' },
      border: { width: '1px', style: 'solid', color: '#e5e7eb', radius: '0' },
      spacing: { top: '0', right: '0', bottom: '0', left: '0' },
      shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' }
    },
    responsive: {
      mobile: { 
        display: 'none', 
        width: '100%', 
        height: 'auto',
        spacing: { top: '1rem', right: '1rem', bottom: '1rem', left: '1rem' },
        typography: { 
          fontFamily: 'inherit', 
          fontSize: '14px', 
          fontWeight: 'normal', 
          lineHeight: '1.5',
          letterSpacing: '0',
          textAlign: 'left',
          textTransform: 'none'
        }
      },
      tablet: { 
        display: 'block', 
        width: '250px', 
        height: 'auto',
        spacing: { top: '1rem', right: '1rem', bottom: '1rem', left: '1rem' },
        typography: { 
          fontFamily: 'inherit', 
          fontSize: '14px', 
          fontWeight: 'normal', 
          lineHeight: '1.5',
          letterSpacing: '0',
          textAlign: 'left',
          textTransform: 'none'
        }
      },
      desktop: { 
        display: 'block', 
        width: '300px', 
        height: 'auto',
        spacing: { top: '1rem', right: '1rem', bottom: '1rem', left: '1rem' },
        typography: { 
          fontFamily: 'inherit', 
          fontSize: '14px', 
          fontWeight: 'normal', 
          lineHeight: '1.5',
          letterSpacing: '0',
          textAlign: 'left',
          textTransform: 'none'
        }
      },
      large: { 
        display: 'block', 
        width: '320px', 
        height: 'auto',
        spacing: { top: '1rem', right: '1rem', bottom: '1rem', left: '1rem' },
        typography: { 
          fontFamily: 'inherit', 
          fontSize: '14px', 
          fontWeight: 'normal', 
          lineHeight: '1.5',
          letterSpacing: '0',
          textAlign: 'left',
          textTransform: 'none'
        }
      }
    },
    isVisible: true
  })

  // Get sidebar-specific blocks
  const sidebarBlocks = layoutBlockRegistry.getBlocksForSection('sidebar')

  const handleAddBlock = (blockType: string) => {
    addBlock(sidebarSection.id, blockType as any)
  }

  const handleUpdateSection = (updates: Partial<LayoutSection>) => {
    updateSection(sidebarSection.id, updates)
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b bg-gradient-to-r from-green-50 to-blue-50">
        <div className="flex items-center gap-2 mb-3">
          <div className="p-1.5 bg-green-100 rounded-lg">
            <Package className="h-4 w-4 text-green-600" />
          </div>
          <h2 className="text-lg font-semibold text-gray-900">Sidebar Designer</h2>
          <Badge variant="outline" className="text-xs">
            {sidebarSection.blocks.length} blocks
          </Badge>
        </div>
        
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <span>Design your sidebar with widgets, filters, and content blocks</span>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="layout" className="text-xs">
              <Layout className="h-3 w-3 mr-1" />
              Layout
            </TabsTrigger>
            <TabsTrigger value="blocks" className="text-xs">
              <Plus className="h-3 w-3 mr-1" />
              Blocks
            </TabsTrigger>
            <TabsTrigger value="styling" className="text-xs">
              <Palette className="h-3 w-3 mr-1" />
              Styling
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          {/* Layout Tab */}
          <TabsContent value="layout" className="h-full mt-0 p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Sidebar Configuration</CardTitle>
                <CardDescription className="text-xs">
                  Configure the sidebar layout and responsive behavior
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="sidebar-layout" className="text-xs">Layout Type</Label>
                    <Select
                      value={sidebarSection.configuration.layout}
                      onValueChange={(value) => handleUpdateSection({
                        configuration: {
                          ...sidebarSection.configuration,
                          layout: value as 'flex' | 'grid' | 'block'
                        }
                      })}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="block">Block</SelectItem>
                        <SelectItem value="flex">Flex</SelectItem>
                        <SelectItem value="grid">Grid</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="sidebar-width" className="text-xs">Width</Label>
                    <Input
                      id="sidebar-width"
                      value={sidebarSection.configuration.container.maxWidth}
                      onChange={(e) => handleUpdateSection({
                        configuration: {
                          ...sidebarSection.configuration,
                          container: {
                            ...sidebarSection.configuration.container,
                            maxWidth: e.target.value
                          }
                        }
                      })}
                      className="h-8"
                      placeholder="300px"
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <Label className="text-xs font-medium">Responsive Visibility</Label>
                  
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="sidebar-mobile" className="text-xs">Mobile</Label>
                      <Switch
                        id="sidebar-mobile"
                        checked={sidebarSection.responsive.mobile.display !== 'none'}
                        onCheckedChange={(checked) => handleUpdateSection({
                          responsive: {
                            ...sidebarSection.responsive,
                            mobile: {
                              ...sidebarSection.responsive.mobile,
                              display: checked ? 'block' : 'none'
                            }
                          }
                        })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="sidebar-tablet" className="text-xs">Tablet</Label>
                      <Switch
                        id="sidebar-tablet"
                        checked={sidebarSection.responsive.tablet.display !== 'none'}
                        onCheckedChange={(checked) => handleUpdateSection({
                          responsive: {
                            ...sidebarSection.responsive,
                            tablet: {
                              ...sidebarSection.responsive.tablet,
                              display: checked ? 'block' : 'none'
                            }
                          }
                        })}
                      />
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="sidebar-visible" className="text-xs">Visible</Label>
                  <Switch
                    id="sidebar-visible"
                    checked={sidebarSection.isVisible}
                    onCheckedChange={(checked) => handleUpdateSection({ isVisible: checked })}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Blocks Tab */}
          <TabsContent value="blocks" className="h-full mt-0 p-4 space-y-4">
            {/* Current Blocks */}
            {sidebarSection.blocks.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Current Blocks</CardTitle>
                  <CardDescription className="text-xs">
                    Manage blocks in your sidebar
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {sidebarSection.blocks.map((block) => (
                      <div
                        key={block.id}
                        className="flex items-center justify-between p-2 border rounded-lg hover:bg-gray-50"
                      >
                        <div className="flex items-center gap-2">
                          <GripVertical className="h-3 w-3 text-gray-400 cursor-move" />
                          <span className="text-sm font-medium">{block.name}</span>
                          <Badge variant="outline" className="text-xs">{block.type}</Badge>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => updateBlock(block.id, { isVisible: !block.isVisible })}
                            className="h-6 w-6 p-0"
                          >
                            {block.isVisible ? (
                              <Eye className="h-3 w-3" />
                            ) : (
                              <EyeOff className="h-3 w-3" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteBlock(block.id)}
                            className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Available Blocks */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Available Sidebar Blocks</CardTitle>
                <CardDescription className="text-xs">
                  Add blocks to build your sidebar
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-2">
                  {sidebarBlocks.map((blockDef) => (
                    <Button
                      key={blockDef.type}
                      variant="outline"
                      onClick={() => handleAddBlock(blockDef.type)}
                      className="justify-start h-auto p-3"
                    >
                      <div className="flex items-center gap-3 w-full">
                        <div className="p-1 bg-gray-100 rounded">
                          {blockDef.type === 'widget' && <Package className="h-3 w-3" />}
                          {blockDef.type === 'search' && <Search className="h-3 w-3" />}
                          {blockDef.type === 'filters' && <Filter className="h-3 w-3" />}
                          {blockDef.type === 'categories' && <Tag className="h-3 w-3" />}
                          {blockDef.type === 'recent-posts' && <FileText className="h-3 w-3" />}
                          {blockDef.type === 'tags' && <Tag className="h-3 w-3" />}
                        </div>
                        <div className="text-left">
                          <div className="font-medium text-xs">{blockDef.name}</div>
                          <div className="text-xs text-muted-foreground">{blockDef.description}</div>
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Styling Tab */}
          <TabsContent value="styling" className="h-full mt-0 p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Sidebar Styling</CardTitle>
                <CardDescription className="text-xs">
                  Customize the appearance of your sidebar
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-xs text-muted-foreground">
                  Advanced styling controls will be implemented here
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
