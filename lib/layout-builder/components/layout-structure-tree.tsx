'use client'

import React, { useState } from 'react'
import { useLayoutBuilder } from '../context'
import { LayoutSection, LayoutBlock, SectionType } from '../types'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import {
  ChevronDown,
  ChevronRight,
  Plus,
  Trash2,
  GripVertical,
  Eye,
  EyeOff,
  Settings,
  Copy,
  Move,
  Layers,
  Package,
  Navigation,
  FileText,
  Image,
  ShoppingCart,
  Share2,
  Copyright
} from 'lucide-react'
import { cn } from '@/lib/utils'

export function LayoutStructureTree() {
  const { 
    state, 
    selectSection, 
    selectBlock, 
    deleteSection, 
    deleteBlock, 
    addSection,
    updateSection,
    updateBlock
  } = useLayoutBuilder()

  const { layout, selectedSectionId, selectedBlockId } = state
  const sections = Object.entries(layout.structure).filter(([_, section]) => section !== undefined)

  // Get icon for section type
  const getSectionIcon = (sectionType: SectionType) => {
    const iconMap: Record<SectionType, React.ComponentType<any>> = {
      header: Navigation,
      main: FileText,
      sidebar: Package,
      footer: Copyright,
      custom: Layers
    }
    return iconMap[sectionType] || Layers
  }

  // Get icon for block type
  const getBlockIcon = (blockType: string) => {
    const iconMap: Record<string, React.ComponentType<any>> = {
      logo: Image,
      navigation: Navigation,
      search: Package,
      cart: ShoppingCart,
      content: FileText,
      widget: Package,
      social: Share2,
      copyright: Copyright,
      breadcrumbs: Navigation,
      filters: Package,
      categories: Package,
      'recent-posts': FileText,
      tags: Package,
      links: Package,
      custom: Settings
    }
    return iconMap[blockType] || Package
  }

  // Handle adding a new section
  const handleAddSection = () => {
    // For now, add a custom section
    addSection('custom')
  }

  return (
    <TooltipProvider>
      <div className="h-full flex flex-col bg-white">
        {/* Header */}
        <div className="p-4 border-b bg-gradient-to-r from-purple-50 to-blue-50">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className="p-1.5 bg-purple-100 rounded-lg">
                <Layers className="h-4 w-4 text-purple-600" />
              </div>
              <h2 className="text-lg font-semibold text-gray-900">Layout Structure</h2>
            </div>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleAddSection}
                  className="h-8 w-8 p-0 hover:bg-purple-100 hover:border-purple-300"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Add Section</TooltipContent>
            </Tooltip>
          </div>

          {/* Structure Stats */}
          <div className="text-xs text-muted-foreground">
            {sections.length} section{sections.length !== 1 ? 's' : ''} • {' '}
            {sections.reduce((total, [_, section]) => total + (section?.blocks.length || 0), 0)} block{sections.reduce((total, [_, section]) => total + (section?.blocks.length || 0), 0) !== 1 ? 's' : ''}
          </div>
        </div>

        {/* Structure Tree */}
        <ScrollArea className="flex-1 p-4">
          {sections.length === 0 ? (
            <div className="text-center py-12">
              <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Layers className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-sm font-medium text-gray-900 mb-1">No sections yet</h3>
              <p className="text-xs text-muted-foreground mb-4">
                Add sections to start building your layout structure
              </p>
              <Button size="sm" onClick={handleAddSection} className="bg-purple-600 hover:bg-purple-700">
                <Plus className="h-4 w-4 mr-2" />
                Add Section
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              {sections.map(([sectionKey, section]) => (
                <SectionTreeItem
                  key={section?.id || sectionKey}
                  sectionKey={sectionKey}
                  section={section!}
                  isSelected={selectedSectionId === section?.id}
                  selectedBlockId={selectedBlockId}
                  onSelectSection={selectSection}
                  onSelectBlock={selectBlock}
                  onDeleteSection={deleteSection}
                  onDeleteBlock={deleteBlock}
                  onUpdateSection={updateSection}
                  onUpdateBlock={updateBlock}
                  getSectionIcon={getSectionIcon}
                  getBlockIcon={getBlockIcon}
                />
              ))}
            </div>
          )}
        </ScrollArea>

        {/* Footer */}
        <div className="p-4 border-t bg-gradient-to-r from-gray-50 to-blue-50">
          <div className="text-xs text-muted-foreground text-center">
            Click to select • Right-click for options
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}

// Section Tree Item Component
interface SectionTreeItemProps {
  sectionKey: string
  section: LayoutSection
  isSelected: boolean
  selectedBlockId: string | null
  onSelectSection: (id: string | null) => void
  onSelectBlock: (id: string | null) => void
  onDeleteSection: (id: string) => void
  onDeleteBlock: (id: string) => void
  onUpdateSection: (id: string, updates: Partial<LayoutSection>) => void
  onUpdateBlock: (id: string, updates: Partial<LayoutBlock>) => void
  getSectionIcon: (type: SectionType) => React.ComponentType<any>
  getBlockIcon: (type: string) => React.ComponentType<any>
}

function SectionTreeItem({
  sectionKey,
  section,
  isSelected,
  selectedBlockId,
  onSelectSection,
  onSelectBlock,
  onDeleteSection,
  onDeleteBlock,
  onUpdateSection,
  onUpdateBlock,
  getSectionIcon,
  getBlockIcon
}: SectionTreeItemProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  const [isHovered, setIsHovered] = useState(false)

  const SectionIcon = getSectionIcon(section.type)
  const hasBlocks = section.blocks.length > 0

  const handleToggleVisibility = (e: React.MouseEvent) => {
    e.stopPropagation()
    onUpdateSection(section.id, { isVisible: !section.isVisible })
  }

  const handleDeleteSection = (e: React.MouseEvent) => {
    e.stopPropagation()
    onDeleteSection(section.id)
  }

  return (
    <div className="space-y-1">
      {/* Section Header */}
      <div
        className={cn(
          'group flex items-center gap-2 p-3 rounded-lg cursor-pointer transition-all duration-200',
          'border-2 hover:shadow-sm',
          isSelected 
            ? 'bg-purple-50 border-purple-200 shadow-sm' 
            : 'bg-white border-gray-200 hover:border-gray-300',
          !section.isVisible && 'opacity-60'
        )}
        onClick={() => onSelectSection(isSelected ? null : section.id)}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Expand/Collapse Button */}
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 hover:bg-gray-100"
          onClick={(e) => {
            e.stopPropagation()
            setIsExpanded(!isExpanded)
          }}
        >
          {hasBlocks ? (
            isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )
          ) : (
            <div className="h-3 w-3" />
          )}
        </Button>

        {/* Section Icon */}
        <div className={cn(
          'flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center transition-colors',
          isSelected ? 'bg-purple-100 text-purple-600' : 'bg-gray-100 text-gray-600'
        )}>
          <SectionIcon className="h-4 w-4" />
        </div>

        {/* Section Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className={cn(
              'font-medium text-sm truncate',
              isSelected ? 'text-purple-900' : 'text-gray-900'
            )}>
              {section.name}
            </span>
            <Badge variant="outline" className="text-xs">
              {section.type}
            </Badge>
          </div>
          <div className="text-xs text-muted-foreground">
            {section.blocks.length} block{section.blocks.length !== 1 ? 's' : ''}
          </div>
        </div>

        {/* Section Actions */}
        <div className={cn(
          'flex items-center gap-1 transition-opacity',
          isHovered ? 'opacity-100' : 'opacity-0'
        )}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-gray-100"
                onClick={handleToggleVisibility}
              >
                {section.isVisible ? (
                  <Eye className="h-3 w-3" />
                ) : (
                  <EyeOff className="h-3 w-3" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {section.isVisible ? 'Hide Section' : 'Show Section'}
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                onClick={handleDeleteSection}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Delete Section</TooltipContent>
          </Tooltip>
        </div>
      </div>

      {/* Section Blocks */}
      {isExpanded && hasBlocks && (
        <div className="ml-6 space-y-1">
          {section.blocks.map((block) => (
            <BlockTreeItem
              key={block.id}
              block={block}
              isSelected={selectedBlockId === block.id}
              onSelectBlock={onSelectBlock}
              onDeleteBlock={onDeleteBlock}
              onUpdateBlock={onUpdateBlock}
              getBlockIcon={getBlockIcon}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Block Tree Item Component
interface BlockTreeItemProps {
  block: LayoutBlock
  isSelected: boolean
  onSelectBlock: (id: string | null) => void
  onDeleteBlock: (id: string) => void
  onUpdateBlock: (id: string, updates: Partial<LayoutBlock>) => void
  getBlockIcon: (type: string) => React.ComponentType<any>
}

function BlockTreeItem({
  block,
  isSelected,
  onSelectBlock,
  onDeleteBlock,
  onUpdateBlock,
  getBlockIcon
}: BlockTreeItemProps) {
  const [isHovered, setIsHovered] = useState(false)

  const BlockIcon = getBlockIcon(block.type)

  const handleToggleVisibility = (e: React.MouseEvent) => {
    e.stopPropagation()
    onUpdateBlock(block.id, { isVisible: !block.isVisible })
  }

  const handleDeleteBlock = (e: React.MouseEvent) => {
    e.stopPropagation()
    onDeleteBlock(block.id)
  }

  return (
    <div
      className={cn(
        'group flex items-center gap-2 p-2 rounded-lg cursor-pointer transition-all duration-200',
        'border hover:shadow-sm',
        isSelected
          ? 'bg-blue-50 border-blue-200 shadow-sm'
          : 'bg-white border-gray-100 hover:border-gray-200',
        !block.isVisible && 'opacity-60'
      )}
      onClick={() => onSelectBlock(isSelected ? null : block.id)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Drag Handle */}
      <div className="flex-shrink-0 cursor-move opacity-0 group-hover:opacity-100 transition-opacity">
        <GripVertical className="h-3 w-3 text-gray-400" />
      </div>

      {/* Block Icon */}
      <div className={cn(
        'flex-shrink-0 w-6 h-6 rounded flex items-center justify-center transition-colors',
        isSelected ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
      )}>
        <BlockIcon className="h-3 w-3" />
      </div>

      {/* Block Info */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className={cn(
            'font-medium text-xs truncate',
            isSelected ? 'text-blue-900' : 'text-gray-900'
          )}>
            {block.name}
          </span>
          <Badge variant="outline" className="text-xs">
            {block.type}
          </Badge>
        </div>
      </div>

      {/* Block Actions */}
      <div className={cn(
        'flex items-center gap-1 transition-opacity',
        isHovered ? 'opacity-100' : 'opacity-0'
      )}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0 hover:bg-gray-100"
              onClick={handleToggleVisibility}
            >
              {block.isVisible ? (
                <Eye className="h-2.5 w-2.5" />
              ) : (
                <EyeOff className="h-2.5 w-2.5" />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {block.isVisible ? 'Hide Block' : 'Show Block'}
          </TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0 hover:bg-red-100 hover:text-red-600"
              onClick={handleDeleteBlock}
            >
              <Trash2 className="h-2.5 w-2.5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Delete Block</TooltipContent>
        </Tooltip>
      </div>
    </div>
  )
}
