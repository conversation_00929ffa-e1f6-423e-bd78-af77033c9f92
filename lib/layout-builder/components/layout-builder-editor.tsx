'use client'

import React, { useState } from 'react'
import { useLayoutBuilder } from '../context'
import { LayoutBuilderCanvas } from './layout-builder-canvas'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  PanelLeft,
  PanelRight,
  Eye,
  EyeOff,
  Smartphone,
  Tablet,
  Monitor,
  Undo,
  Redo,
  Settings,
  Save,
  Download,
  Upload,
  Layers,
  Palette,
  Code,
  Play,
  Pause,
  Brain,
  Sparkles,
  Wand2,
  Plus
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'

interface LayoutBuilderEditorProps {
  initialLayout?: any
  onSave?: () => Promise<void>
  onPreview?: () => void
  onBack?: () => void
  className?: string
}

export function LayoutBuilderEditor({
  initialLayout: _initialLayout,
  onSave: _onSave,
  onPreview,
  onBack: _onBack,
  className
}: LayoutBuilderEditorProps) {
  const { 
    state, 
    setPreviewMode, 
    setDevicePreview,
    undo,
    redo,
    canUndo,
    canRedo
  } = useLayoutBuilder()

  const [leftSidebarOpen, setLeftSidebarOpen] = useState(true)
  const [rightSidebarOpen, setRightSidebarOpen] = useState(true)

  const { 
    isPreviewMode, 
    devicePreview, 
    hasUnsavedChanges, 
    isSaving,
    layout 
  } = state

  // Handle save
  const handleSave = async () => {
    if (_onSave) {
      await _onSave()
    }
  }

  // Handle preview
  const handlePreview = () => {
    setPreviewMode(!isPreviewMode)
    if (onPreview) {
      onPreview()
    }
  }

  // Get device preview width
  const getCanvasWidth = () => {
    switch (devicePreview) {
      case 'mobile':
        return '375px'
      case 'tablet':
        return '768px'
      default:
        return '100%'
    }
  }

  return (
    <div className={cn('h-full flex flex-col bg-gray-50', className)}>
      {/* Top Toolbar */}
      <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200 shadow-sm">
        <div className="flex items-center space-x-4">
          {/* Layout Info */}
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Layers className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <h1 className="font-semibold text-gray-900">{layout.name}</h1>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Badge variant="outline" className="text-xs">
                  {layout.type}
                </Badge>
                <span>•</span>
                <span>{layout.category}</span>
              </div>
            </div>
          </div>

          <Separator orientation="vertical" className="h-8" />

          {/* Status Indicator */}
          <div className="flex items-center gap-2">
            <div className={cn(
              'w-2 h-2 rounded-full',
              hasUnsavedChanges ? 'bg-orange-500' : 'bg-green-500'
            )} />
            <span className="text-sm text-muted-foreground">
              {isSaving ? 'Saving...' : hasUnsavedChanges ? 'Unsaved changes' : 'All changes saved'}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Undo/Redo */}
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={undo}
              disabled={!canUndo}
              className="h-8 w-8 p-0"
            >
              <Undo className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={redo}
              disabled={!canRedo}
              className="h-8 w-8 p-0"
            >
              <Redo className="h-4 w-4" />
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Device Preview */}
          <div className="flex items-center gap-1 p-1 bg-gray-100 rounded-lg">
            {(['desktop', 'tablet', 'mobile'] as const).map((device) => {
              const Icon = device === 'desktop' ? Monitor : device === 'tablet' ? Tablet : Smartphone
              return (
                <Button
                  key={device}
                  variant={devicePreview === device ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setDevicePreview(device)}
                  className="h-8 w-8 p-0"
                >
                  <Icon className="h-4 w-4" />
                </Button>
              )
            })}
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Preview Mode */}
          <Button
            variant={isPreviewMode ? 'default' : 'outline'}
            size="sm"
            onClick={handlePreview}
            className="gap-2"
          >
            {isPreviewMode ? (
              <>
                <Pause className="h-4 w-4" />
                Exit Preview
              </>
            ) : (
              <>
                <Play className="h-4 w-4" />
                Preview
              </>
            )}
          </Button>

          {/* Actions Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Actions
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleSave} disabled={isSaving || !hasUnsavedChanges}>
                <Save className="h-4 w-4 mr-2" />
                Save Layout
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Export Layout
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Upload className="h-4 w-4 mr-2" />
                Import Layout
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Code className="h-4 w-4 mr-2" />
                View Code
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Save Button */}
          <Button 
            onClick={handleSave} 
            disabled={isSaving || !hasUnsavedChanges}
            size="sm"
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </div>

      {/* Editor Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar Toggles */}
        {!leftSidebarOpen && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setLeftSidebarOpen(true)}
            className="absolute left-2 top-1/2 -translate-y-1/2 z-10 h-8 w-8 p-0 bg-white border shadow-sm"
          >
            <PanelLeft className="h-4 w-4" />
          </Button>
        )}

        {!rightSidebarOpen && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setRightSidebarOpen(true)}
            className="absolute right-2 top-1/2 -translate-y-1/2 z-10 h-8 w-8 p-0 bg-white border shadow-sm"
          >
            <PanelRight className="h-4 w-4" />
          </Button>
        )}

        {/* Main Canvas Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Canvas Header */}
          <div className="flex items-center justify-between p-4 bg-white border-b">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLeftSidebarOpen(!leftSidebarOpen)}
                className={cn('h-8 w-8 p-0', leftSidebarOpen && 'bg-gray-100')}
              >
                <PanelLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm font-medium">Canvas</span>
              <Badge variant="outline" className="text-xs">
                {devicePreview}
              </Badge>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-xs text-muted-foreground">
                {getCanvasWidth()}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setRightSidebarOpen(!rightSidebarOpen)}
                className={cn('h-8 w-8 p-0', rightSidebarOpen && 'bg-gray-100')}
              >
                <PanelRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Canvas */}
          <div className="flex-1 overflow-auto bg-gray-100 p-6">
            <div className="flex justify-center">
              <div 
                className="bg-white shadow-lg rounded-lg overflow-hidden transition-all duration-300"
                style={{ 
                  width: getCanvasWidth(),
                  minHeight: '600px',
                  maxWidth: '100%'
                }}
              >
                <LayoutBuilderCanvas
                  devicePreview={devicePreview}
                  isPreviewMode={isPreviewMode}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
