'use client'

import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { 
  Search, 
  ShoppingCart, 
  Menu, 
  X, 
  Star, 
  Heart,
  Share2,
  Filter,
  Grid,
  List,
  ChevronDown,
  User,
  Mail,
  Phone,
  MapPin
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { LayoutBlock, ResolvedLayout } from '../types'

// Enhanced Logo Block with multiple display options
export function EnhancedLogoBlock({ block }: { block: LayoutBlock }) {
  const { content, configuration, styling } = block
  
  return (
    <div className={`logo-block ${getAlignmentClass(configuration.alignment)}`}>
      <Link href="/" className="flex items-center space-x-3">
        {content.image && (
          <div className="relative">
            <Image 
              src={content.image} 
              alt={content.text || 'Logo'} 
              width={configuration.size === 'large' ? 48 : configuration.size === 'medium' ? 40 : 32}
              height={configuration.size === 'large' ? 48 : configuration.size === 'medium' ? 40 : 32}
              className="object-contain"
            />
          </div>
        )}
        <div className="flex flex-col">
          <span 
            className={`font-bold text-${getSizeClass(configuration.size)} text-primary`}
            style={{ 
              fontSize: styling?.typography?.fontSize,
              fontWeight: styling?.typography?.fontWeight,
              color: styling?.colors?.text
            }}
          >
            {content.text || 'Your Brand'}
          </span>
          {content.subtitle && (
            <span className="text-sm text-muted-foreground">
              {content.subtitle}
            </span>
          )}
        </div>
      </Link>
    </div>
  )
}

// Advanced Search Block with filters and suggestions
export function AdvancedSearchBlock({ block }: { block: LayoutBlock }) {
  const { content, configuration } = block
  const [searchTerm, setSearchTerm] = React.useState('')
  const [showSuggestions, setShowSuggestions] = React.useState(false)
  
  return (
    <div className={`search-block ${getAlignmentClass(configuration.alignment)}`}>
      <div className="relative">
        <div className="flex">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="search"
              placeholder={content.placeholder || "Search..."}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onFocus={() => setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              className="pl-10"
              style={{ width: configuration.width || '300px' }}
            />
          </div>
          {content.showFilters && (
            <Button variant="outline" className="ml-2">
              <Filter className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        {/* Search Suggestions */}
        {showSuggestions && content.showSuggestions && searchTerm && (
          <div className="absolute top-full left-0 right-0 bg-white border rounded-md shadow-lg z-50 mt-1">
            <div className="p-2">
              <div className="text-sm text-gray-600 mb-2">Suggestions</div>
              {['Popular searches', 'Recent searches', 'Categories'].map((suggestion, index) => (
                <div 
                  key={index}
                  className="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                >
                  {suggestion}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Enhanced Cart Block with counter and dropdown
export function EnhancedCartBlock({ block }: { block: LayoutBlock }) {
  const { configuration } = block
  const [isOpen, setIsOpen] = React.useState(false)
  const cartCount = 3 // This would come from cart context
  
  return (
    <div className="cart-block relative">
      <Button 
        variant="ghost" 
        size="sm" 
        className="relative"
        onClick={() => setIsOpen(!isOpen)}
      >
        <ShoppingCart className="h-5 w-5" />
        {configuration.showCounter && cartCount > 0 && (
          <span className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {cartCount}
          </span>
        )}
      </Button>
      
      {/* Cart Dropdown */}
      {isOpen && (
        <div className="absolute top-full right-0 bg-white border rounded-md shadow-lg z-50 mt-2 w-80">
          <div className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold">Shopping Cart</h3>
              <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)}>
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            {cartCount > 0 ? (
              <>
                <div className="space-y-3 mb-4">
                  {/* Cart items would be mapped here */}
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gray-200 rounded"></div>
                    <div className="flex-1">
                      <div className="text-sm font-medium">Product Name</div>
                      <div className="text-xs text-gray-600">Qty: 1</div>
                    </div>
                    <div className="text-sm font-semibold">R299</div>
                  </div>
                </div>
                
                <div className="border-t pt-3">
                  <div className="flex justify-between mb-3">
                    <span className="font-semibold">Total:</span>
                    <span className="font-semibold">R897</span>
                  </div>
                  <div className="space-y-2">
                    <Button className="w-full" size="sm">
                      View Cart
                    </Button>
                    <Button variant="outline" className="w-full" size="sm">
                      Checkout
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-8 text-gray-500">
                Your cart is empty
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

// Product Grid Block for e-commerce layouts
export function ProductGridBlock({ block }: { block: LayoutBlock }) {
  const { content, configuration } = block
  const [viewMode, setViewMode] = React.useState<'grid' | 'list'>('grid')
  const [products, setProducts] = React.useState<any[]>([])
  const [loading, setLoading] = React.useState(true)

  // Fetch real products from API
  React.useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await fetch('/api/e-commerce/products?featured=true&limit=6&status=active')
        const data = await response.json()

        if (data.success) {
          setProducts(data.data || [])
        } else {
          // Fallback to demo products
          setProducts([
            {
              id: 'demo-1',
              title: 'Premium Kids T-Shirt',
              price: { amount: 299, currency: 'ZAR' },
              images: [{ url: '/assets/images/cocomilk_kids-20220927_125643-1359487094.jpg', alt: 'Kids T-Shirt' }],
              rating: 4.5
            },
            {
              id: 'demo-2',
              title: 'Comfortable Jeans',
              price: { amount: 399, currency: 'ZAR' },
              images: [{ url: '/assets/images/cocomilk_kids-20210912_114630-3065525289.jpg', alt: 'Kids Jeans' }],
              rating: 4.2
            },
          ])
        }
      } catch (error) {
        console.error('Error fetching products:', error)
        // Use fallback products
        setProducts([
          {
            id: 'demo-1',
            title: 'Premium Kids T-Shirt',
            price: { amount: 299, currency: 'ZAR' },
            images: [{ url: '/assets/images/cocomilk_kids-20220927_125643-1359487094.jpg', alt: 'Kids T-Shirt' }],
            rating: 4.5
          }
        ])
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [])
  
  return (
    <div className="product-grid-block">
      {/* Toolbar */}
      <div className="flex items-center justify-between mb-6">
        <div className="text-sm text-gray-600">
          Showing {products.length} products
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {/* Products */}
      <div className={viewMode === 'grid' 
        ? `grid grid-cols-${configuration.columns || 3} gap-6`
        : 'space-y-4'
      }>
        {products.map((product) => (
          <ProductCard 
            key={product.id} 
            product={product} 
            viewMode={viewMode}
          />
        ))}
      </div>
    </div>
  )
}

// Product Card Component
function ProductCard({ 
  product, 
  viewMode 
}: { 
  product: any
  viewMode: 'grid' | 'list'
}) {
  return (
    <Card className={`group hover:shadow-lg transition-shadow ${
      viewMode === 'list' ? 'flex' : ''
    }`}>
      <div className={viewMode === 'list' ? 'w-48 flex-shrink-0' : ''}>
        <div className="aspect-square bg-gray-200 rounded-t-lg relative overflow-hidden">
          {product.images?.[0]?.url ? (
            <img
              src={product.images[0].url}
              alt={product.images[0].alt || product.title}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center text-gray-400">
              Product Image
            </div>
          )}
          
          {/* Quick Actions */}
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="flex flex-col space-y-1">
              <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
                <Heart className="h-4 w-4" />
              </Button>
              <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      <CardContent className={`p-4 ${viewMode === 'list' ? 'flex-1' : ''}`}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="font-semibold text-sm mb-1">{product.title || product.name}</h3>

            {/* Rating */}
            {product.rating && (
              <div className="flex items-center space-x-1 mb-2">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-3 w-3 ${
                        i < Math.floor(product.rating)
                          ? 'fill-yellow-400 text-yellow-400'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-xs text-gray-600">({product.rating})</span>
              </div>
            )}

            <div className="flex items-center justify-between">
              <span className="font-bold text-primary">
                R{product.price?.amount || product.price}
              </span>
              <Button size="sm">Add to Cart</Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Enhanced Footer Links Block
export function EnhancedFooterLinksBlock({ block }: { block: LayoutBlock }) {
  const { content, configuration } = block
  
  return (
    <div className="footer-links-block">
      <div className={`grid grid-cols-${configuration.columns || 4} gap-8`}>
        {content.sections?.map((section: any, index: number) => (
          <div key={index}>
            <h3 className="font-semibold mb-4">{section.title}</h3>
            <ul className="space-y-2">
              {section.links.map((link: string, linkIndex: number) => (
                <li key={linkIndex}>
                  <Link 
                    href="#" 
                    className="text-sm text-gray-600 hover:text-primary transition-colors"
                  >
                    {link}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  )
}

// Contact Information Block
export function ContactInfoBlock({ block }: { block: LayoutBlock }) {
  const { content } = block
  
  return (
    <div className="contact-info-block">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {content.phone && (
          <div className="flex items-center space-x-3">
            <Phone className="h-5 w-5 text-primary" />
            <div>
              <div className="font-medium">Phone</div>
              <div className="text-sm text-gray-600">{content.phone}</div>
            </div>
          </div>
        )}
        
        {content.email && (
          <div className="flex items-center space-x-3">
            <Mail className="h-5 w-5 text-primary" />
            <div>
              <div className="font-medium">Email</div>
              <div className="text-sm text-gray-600">{content.email}</div>
            </div>
          </div>
        )}
        
        {content.address && (
          <div className="flex items-center space-x-3">
            <MapPin className="h-5 w-5 text-primary" />
            <div>
              <div className="font-medium">Address</div>
              <div className="text-sm text-gray-600">{content.address}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Newsletter Signup Block
export function NewsletterBlock({ block }: { block: LayoutBlock }) {
  const { content } = block
  const [email, setEmail] = React.useState('')
  const [isLoading, setIsLoading] = React.useState(false)
  const [isSubscribed, setIsSubscribed] = React.useState(false)
  const [message, setMessage] = React.useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email) return

    setIsLoading(true)
    setMessage('')

    try {
      const response = await fetch('/api/e-commerce/newsletter/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (data.success) {
        setIsSubscribed(true)
        setMessage('Successfully subscribed to our newsletter!')
        setEmail('')
      } else {
        setMessage(data.error || 'Subscription failed. Please try again.')
      }
    } catch (error) {
      setMessage('Network error. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  if (isSubscribed) {
    return (
      <div className="newsletter-block bg-green-50 rounded-lg p-6">
        <div className="text-center">
          <div className="text-green-600 mb-2">✓</div>
          <h3 className="font-semibold mb-2 text-green-800">
            Thank you for subscribing!
          </h3>
          <p className="text-sm text-green-600">
            You'll receive our latest updates and exclusive offers.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="newsletter-block bg-gray-50 rounded-lg p-6">
      <div className="text-center">
        <h3 className="font-semibold mb-2">
          {content.title || 'Subscribe to our newsletter'}
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          {content.description || 'Get the latest updates and offers'}
        </p>

        <form onSubmit={handleSubmit} className="max-w-md mx-auto">
          <div className="flex">
            <Input
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="rounded-r-none"
              required
              disabled={isLoading}
            />
            <Button
              type="submit"
              className="rounded-l-none"
              disabled={isLoading || !email}
            >
              {isLoading ? 'Subscribing...' : 'Subscribe'}
            </Button>
          </div>

          {message && (
            <p className={`text-xs mt-2 ${message.includes('Successfully') ? 'text-green-600' : 'text-red-600'}`}>
              {message}
            </p>
          )}
        </form>

        <p className="text-xs text-gray-500 mt-2">
          We respect your privacy. Unsubscribe at any time.
        </p>
      </div>
    </div>
  )
}

// Utility functions
function getAlignmentClass(alignment: string): string {
  switch (alignment) {
    case 'left': return 'text-left justify-start'
    case 'center': return 'text-center justify-center'
    case 'right': return 'text-right justify-end'
    default: return 'text-left justify-start'
  }
}

function getSizeClass(size: string): string {
  switch (size) {
    case 'small': return 'lg'
    case 'medium': return 'xl'
    case 'large': return '2xl'
    default: return 'xl'
  }
}
