import React from 'react'
import { ResolvedLayout, LayoutSection, LayoutBlock } from '../types'
import { LayoutBlockRenderer } from './layout-block-renderer'
import Header from '@/components/header'

interface LayoutRendererProps {
  layout: ResolvedLayout
  children: React.ReactNode
  showDefaultHeader?: boolean
}

export function LayoutRenderer({ layout, children, showDefaultHeader = true }: LayoutRendererProps) {
  if (!layout?.layout) {
    // Fallback to basic layout with default header
    return (
      <div className="min-h-screen flex flex-col">
        {showDefaultHeader && <Header />}
        <main className="flex-1">
          {children}
        </main>
      </div>
    )
  }

  const { structure, styling } = layout.layout
  const hasCustomHeader = structure.header && structure.header.isVisible && structure.header.blocks.length > 0

  return (
    <div
      className="min-h-screen flex flex-col"
      style={getLayoutStyles(styling)}
    >
      {/* Header Section - Custom or Default */}
      {hasCustomHeader ? (
        <LayoutSectionRenderer
          section={structure.header}
          layout={layout}
        />
      ) : (
        showDefaultHeader && <Header />
      )}

      {/* Main Content Area */}
      <div className="flex flex-1">
        {/* Sidebar (if exists and positioned left) */}
        {structure.sidebar && (
          <LayoutSectionRenderer
            section={structure.sidebar}
            layout={layout}
            className="w-64 flex-shrink-0"
          />
        )}

        {/* Main Content */}
        <main className="flex-1">
          {structure.main ? (
            <LayoutSectionRenderer
              section={structure.main}
              layout={layout}
              children={children}
            />
          ) : (
            children
          )}
        </main>
      </div>

      {/* Footer Section */}
      {structure.footer && (
        <LayoutSectionRenderer
          section={structure.footer}
          layout={layout}
        />
      )}
    </div>
  )
}

interface LayoutSectionRendererProps {
  section: LayoutSection
  layout: ResolvedLayout
  className?: string
  children?: React.ReactNode
}

function LayoutSectionRenderer({
  section,
  layout,
  className = '',
  children
}: LayoutSectionRendererProps) {
  if (!section.isVisible) {
    return null
  }

  const sectionStyles = getSectionStyles(section)
  const containerClasses = getSectionClasses(section, className)

  // Check if section should use container constraints
  const useContainer = section.configuration?.useContainer !== false
  const containerPadding = section.configuration?.containerPadding !== false

  return (
    <section
      className={containerClasses}
      style={sectionStyles}
      data-section-type={section.type}
      data-section-id={section.id}
    >
      {useContainer ? (
        <div className={`container mx-auto ${containerPadding ? 'px-4' : ''}`}>
          {/* Render section blocks */}
          {section.blocks.map((block) => (
            <LayoutBlockRenderer
              key={block.id}
              block={block}
              layout={layout}
            />
          ))}

          {/* Render children for main content section */}
          {section.type === 'main' && children}
        </div>
      ) : (
        <>
          {/* Render section blocks without container */}
          {section.blocks.map((block) => (
            <LayoutBlockRenderer
              key={block.id}
              block={block}
              layout={layout}
            />
          ))}

          {/* Render children for main content section without container */}
          {section.type === 'main' && children}
        </>
      )}
    </section>
  )
}

// Styling utility functions
function getLayoutStyles(styling: any): React.CSSProperties {
  if (!styling) return {}

  const styles: React.CSSProperties = {}

  // Color scheme
  if (styling.colors) {
    styles.color = styling.colors.text
    styles.backgroundColor = styling.colors.background
  }

  // Typography
  if (styling.typography) {
    styles.fontFamily = styling.typography.fontFamily
    styles.fontSize = styling.typography.fontSize
    styles.fontWeight = styling.typography.fontWeight
    styles.lineHeight = styling.typography.lineHeight
  }

  return styles
}

function getSectionStyles(section: LayoutSection): React.CSSProperties {
  const styles: React.CSSProperties = {}
  const { styling } = section

  if (!styling) return styles

  // Background
  if (styling.background) {
    switch (styling.background.type) {
      case 'color':
        styles.backgroundColor = styling.background.color
        break
      case 'gradient':
        if (styling.background.gradient) {
          const { type, direction, colors } = styling.background.gradient
          const colorStops = colors.map((c: any) => `${c.color} ${c.position}%`).join(', ')
          styles.background = `${type}-gradient(${direction}, ${colorStops})`
        }
        break
      case 'image':
        if (styling.background.image) {
          styles.backgroundImage = `url(${styling.background.image.url})`
          styles.backgroundSize = styling.background.image.size
          styles.backgroundPosition = styling.background.image.position
          styles.backgroundRepeat = styling.background.image.repeat
        }
        break
    }
  }

  // Spacing
  if (styling.spacing) {
    styles.paddingTop = styling.spacing.top
    styles.paddingRight = styling.spacing.right
    styles.paddingBottom = styling.spacing.bottom
    styles.paddingLeft = styling.spacing.left
  }

  // Border
  if (styling.border) {
    styles.borderWidth = styling.border.width
    styles.borderStyle = styling.border.style
    styles.borderColor = styling.border.color
    styles.borderRadius = styling.border.radius
  }

  // Shadow
  if (styling.shadow && styling.shadow.type === 'box') {
    styles.boxShadow = `${styling.shadow.x}px ${styling.shadow.y}px ${styling.shadow.blur}px ${styling.shadow.spread}px ${styling.shadow.color}`
  }

  return styles
}

function getSectionClasses(section: LayoutSection, additionalClasses = ''): string {
  const classes = ['layout-section']
  
  // Section type class
  classes.push(`section-${section.type}`)
  
  // Layout configuration classes
  if (section.configuration) {
    const { layout, alignment } = section.configuration
    
    if (layout === 'flex') {
      classes.push('flex')
      
      switch (alignment) {
        case 'left':
          classes.push('justify-start')
          break
        case 'center':
          classes.push('justify-center')
          break
        case 'right':
          classes.push('justify-end')
          break
        case 'justify':
          classes.push('justify-between')
          break
      }
    } else if (layout === 'grid') {
      classes.push('grid')
    }
  }

  // Add additional classes
  if (additionalClasses) {
    classes.push(additionalClasses)
  }

  return classes.join(' ')
}

// Responsive utilities
export function useResponsiveLayout(layout: ResolvedLayout) {
  const [currentBreakpoint, setCurrentBreakpoint] = React.useState<'mobile' | 'tablet' | 'desktop' | 'large'>('desktop')

  React.useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth
      if (width < 768) {
        setCurrentBreakpoint('mobile')
      } else if (width < 1024) {
        setCurrentBreakpoint('tablet')
      } else if (width < 1440) {
        setCurrentBreakpoint('desktop')
      } else {
        setCurrentBreakpoint('large')
      }
    }

    updateBreakpoint()
    window.addEventListener('resize', updateBreakpoint)
    return () => window.removeEventListener('resize', updateBreakpoint)
  }, [])

  return {
    currentBreakpoint,
    responsive: layout.layout.responsive?.[currentBreakpoint] || {}
  }
}

export default LayoutRenderer
