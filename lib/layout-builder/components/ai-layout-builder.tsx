'use client'

import React, { useState, useCallback } from 'react'
import { useChat } from 'ai/react'
import { useLayoutBuilder } from '../context'
import { GridLayoutBuilder } from '@/lib/page-builder/components/grid-layout-builder'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import {
  Wand2,
  <PERSON>rkles,
  Layout,
  Grid,
  Layers,
  <PERSON>tings,
  Save,
  Eye,
  <PERSON>Off,
  Undo,
  Redo,
  Download,
  Upload,
  Copy,
  Trash2,
  Plus,
  Smartphone,
  Tablet,
  Monitor,
  Tv,
  Send,
  Loader2,
  RefreshCw,
  Target,
  Zap,
  Palette
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

interface AILayoutBuilderProps {
  className?: string
  onLayoutSaved?: (layout: any) => void
}

interface LayoutSection {
  id: string
  name: string
  type: 'header' | 'main' | 'sidebar' | 'footer' | 'custom'
  blocks: any[]
  gridLayout: any[]
}

export function AILayoutBuilder({ className, onLayoutSaved }: AILayoutBuilderProps) {
  const { 
    state, 
    addSection, 
    updateSection, 
    deleteSection, 
    addBlock, 
    updateBlock, 
    deleteBlock,
    updateLayout,
    undo,
    redo,
    canUndo,
    canRedo
  } = useLayoutBuilder()

  const [activeTab, setActiveTab] = useState<'ai' | 'sections' | 'settings'>('ai')
  const [isGenerating, setIsGenerating] = useState(false)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [devicePreview, setDevicePreview] = useState<'mobile' | 'tablet' | 'desktop' | 'large'>('desktop')
  
  // AI Generation Form State
  const [prompt, setPrompt] = useState('')
  const [layoutType, setLayoutType] = useState('page')
  const [layoutCategory, setLayoutCategory] = useState('ecommerce')
  const [includeSections, setIncludeSections] = useState<string[]>(['header', 'main', 'footer'])
  const [designStyle, setDesignStyle] = useState('modern')
  const [targetDevice, setTargetDevice] = useState('desktop')

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
    setMessages
  } = useChat({
    api: '/api/ai-layout-builder',
    onFinish: (message) => {
      setIsGenerating(false)
      try {
        const response = JSON.parse(message.content)
        if (response.layout) {
          handleGeneratedLayout(response.layout)
          toast.success('AI layout generated successfully!')
        }
      } catch (error) {
        console.error('Error parsing AI response:', error)
        toast.error('Failed to parse AI response')
      }
    },
    onError: (error) => {
      setIsGenerating(false)
      toast.error('AI layout generation failed: ' + error.message)
    }
  })

  // Available sections
  const sectionTypes = [
    { id: 'header', label: 'Header', icon: Layout, description: 'Top navigation and branding' },
    { id: 'main', label: 'Main Content', icon: Grid, description: 'Primary content area' },
    { id: 'sidebar', label: 'Sidebar', icon: Layers, description: 'Secondary content and widgets' },
    { id: 'footer', label: 'Footer', icon: Layout, description: 'Bottom links and information' },
    { id: 'custom', label: 'Custom Section', icon: Plus, description: 'Custom content section' }
  ]

  // Layout templates
  const layoutTemplates = [
    {
      id: 'ecommerce-standard',
      name: 'E-commerce Standard',
      description: 'Header, main content, sidebar, footer',
      sections: ['header', 'main', 'sidebar', 'footer'],
      category: 'ecommerce'
    },
    {
      id: 'blog-layout',
      name: 'Blog Layout',
      description: 'Header, article content, sidebar, footer',
      sections: ['header', 'main', 'sidebar', 'footer'],
      category: 'blog'
    },
    {
      id: 'landing-page',
      name: 'Landing Page',
      description: 'Header, hero, features, footer',
      sections: ['header', 'main', 'footer'],
      category: 'landing'
    },
    {
      id: 'dashboard',
      name: 'Dashboard',
      description: 'Header, sidebar navigation, main content',
      sections: ['header', 'sidebar', 'main'],
      category: 'dashboard'
    }
  ]

  // Handle AI layout generation
  const handleAIGeneration = useCallback(async (customPrompt?: string) => {
    setIsGenerating(true)
    
    const generationPrompt = customPrompt || prompt || `Create a ${layoutType} layout for ${layoutCategory} with ${designStyle} design style. Include ${includeSections.join(', ')} sections. Target device: ${targetDevice}.`
    
    try {
      await handleSubmit({
        preventDefault: () => {},
        target: {
          prompt: { value: generationPrompt },
          layoutType: { value: layoutType },
          layoutCategory: { value: layoutCategory },
          includeSections: { value: includeSections },
          designStyle: { value: designStyle },
          targetDevice: { value: targetDevice }
        }
      } as any)
    } catch (error) {
      setIsGenerating(false)
      console.error('AI generation error:', error)
      toast.error('Failed to generate layout')
    }
  }, [prompt, layoutType, layoutCategory, includeSections, designStyle, targetDevice, handleSubmit])

  // Handle generated layout
  const handleGeneratedLayout = useCallback((layout: any) => {
    if (layout.structure) {
      updateLayout({
        ...state.layout,
        structure: layout.structure,
        styling: layout.styling || state.layout.styling,
        responsive: layout.responsive || state.layout.responsive
      })
      
      onLayoutSaved?.(layout)
    }
  }, [state.layout, updateLayout, onLayoutSaved])

  // Apply template
  const handleApplyTemplate = useCallback((template: any) => {
    const newStructure: any = {}
    
    template.sections.forEach((sectionType: string, index: number) => {
      const sectionId = `${sectionType}-${Date.now()}-${index}`
      newStructure[sectionType] = {
        id: sectionId,
        type: sectionType,
        name: sectionType.charAt(0).toUpperCase() + sectionType.slice(1),
        position: index,
        blocks: [],
        configuration: getDefaultSectionConfig(sectionType),
        styling: getDefaultSectionStyling(sectionType),
        responsive: getDefaultResponsiveSettings(),
        isVisible: true
      }
    })
    
    updateLayout({
      ...state.layout,
      structure: newStructure
    })
    
    toast.success(`Applied ${template.name} template`)
  }, [state.layout, updateLayout])

  // Toggle section inclusion
  const toggleSectionInclusion = useCallback((sectionId: string) => {
    setIncludeSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    )
  }, [])

  // Save layout
  const handleSaveLayout = useCallback(async () => {
    try {
      // Implementation for saving layout
      toast.success('Layout saved successfully!')
      onLayoutSaved?.(state.layout)
    } catch (error) {
      toast.error('Failed to save layout')
    }
  }, [state.layout, onLayoutSaved])

  return (
    <div className={cn('ai-layout-builder h-screen flex flex-col', className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-background">
        <div className="flex items-center space-x-4">
          <div>
            <h1 className="text-lg font-semibold">{state.layout.name}</h1>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <span>{Object.keys(state.layout.structure).length} sections</span>
              <Badge variant="outline">{state.layout.type}</Badge>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Device Preview */}
          <div className="flex items-center space-x-1 border rounded-md p-1">
            {(['mobile', 'tablet', 'desktop', 'large'] as const).map((device) => {
              const icons = { mobile: Smartphone, tablet: Tablet, desktop: Monitor, large: Tv }
              const Icon = icons[device]
              return (
                <Button
                  key={device}
                  variant={devicePreview === device ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setDevicePreview(device)}
                  className="h-8 w-8 p-0"
                >
                  <Icon className="h-4 w-4" />
                </Button>
              )
            })}
          </div>

          {/* Undo/Redo */}
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={undo}
              disabled={!canUndo}
              className="h-8 w-8 p-0"
            >
              <Undo className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={redo}
              disabled={!canRedo}
              className="h-8 w-8 p-0"
            >
              <Redo className="h-4 w-4" />
            </Button>
          </div>

          {/* Preview Toggle */}
          <Button
            variant={isPreviewMode ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setIsPreviewMode(!isPreviewMode)}
          >
            {isPreviewMode ? (
              <>
                <EyeOff className="h-4 w-4 mr-2" />
                Exit Preview
              </>
            ) : (
              <>
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </>
            )}
          </Button>

          {/* Save */}
          <Button onClick={handleSaveLayout}>
            <Save className="h-4 w-4 mr-2" />
            Save Layout
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        {!isPreviewMode && (
          <div className="w-80 border-r bg-background flex flex-col">
            <Tabs value={activeTab} onValueChange={setActiveTab as any} className="flex-1 flex flex-col">
              <TabsList className="grid w-full grid-cols-3 m-4 mb-0">
                <TabsTrigger value="ai">
                  <Wand2 className="h-4 w-4 mr-1" />
                  AI
                </TabsTrigger>
                <TabsTrigger value="sections">
                  <Layers className="h-4 w-4 mr-1" />
                  Sections
                </TabsTrigger>
                <TabsTrigger value="settings">
                  <Settings className="h-4 w-4 mr-1" />
                  Settings
                </TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-hidden">
                {/* AI Tab */}
                <TabsContent value="ai" className="h-full m-0 p-4">
                  <ScrollArea className="h-full">
                    <div className="space-y-4">
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center space-x-2">
                            <Sparkles className="h-4 w-4" />
                            <span>AI Layout Generator</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div>
                            <Label htmlFor="prompt">Describe your layout</Label>
                            <Textarea
                              id="prompt"
                              placeholder="Create a modern e-commerce layout with header, product grid, and footer..."
                              value={prompt}
                              onChange={(e) => setPrompt(e.target.value)}
                              rows={3}
                            />
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <Label>Layout Type</Label>
                              <Select value={layoutType} onValueChange={setLayoutType}>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="page">Page Layout</SelectItem>
                                  <SelectItem value="site">Site Layout</SelectItem>
                                  <SelectItem value="template">Template</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div>
                              <Label>Category</Label>
                              <Select value={layoutCategory} onValueChange={setLayoutCategory}>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="ecommerce">E-commerce</SelectItem>
                                  <SelectItem value="blog">Blog</SelectItem>
                                  <SelectItem value="portfolio">Portfolio</SelectItem>
                                  <SelectItem value="landing">Landing</SelectItem>
                                  <SelectItem value="corporate">Corporate</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          <div>
                            <Label>Include Sections</Label>
                            <div className="grid grid-cols-2 gap-2 mt-2">
                              {sectionTypes.map((section) => (
                                <div key={section.id} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={section.id}
                                    checked={includeSections.includes(section.id)}
                                    onCheckedChange={() => toggleSectionInclusion(section.id)}
                                  />
                                  <Label htmlFor={section.id} className="text-sm">
                                    {section.label}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>

                          <Button 
                            onClick={() => handleAIGeneration()}
                            disabled={isGenerating || isLoading}
                            className="w-full"
                          >
                            {isGenerating || isLoading ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                Generating...
                              </>
                            ) : (
                              <>
                                <Sparkles className="h-4 w-4 mr-2" />
                                Generate Layout
                              </>
                            )}
                          </Button>
                        </CardContent>
                      </Card>

                      {/* Templates */}
                      <Card>
                        <CardHeader>
                          <CardTitle>Quick Templates</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            {layoutTemplates.map((template) => (
                              <div key={template.id} className="flex items-center justify-between p-2 border rounded">
                                <div>
                                  <div className="font-medium text-sm">{template.name}</div>
                                  <div className="text-xs text-muted-foreground">{template.description}</div>
                                </div>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleApplyTemplate(template)}
                                >
                                  <Download className="h-3 w-3" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </ScrollArea>
                </TabsContent>

                {/* Sections Tab */}
                <TabsContent value="sections" className="h-full m-0 p-4">
                  <ScrollArea className="h-full">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium">Layout Sections</h3>
                        <Button size="sm" variant="outline">
                          <Plus className="h-3 w-3 mr-1" />
                          Add Section
                        </Button>
                      </div>

                      {Object.entries(state.layout.structure).map(([key, section]) => (
                        section && (
                          <Card key={section.id}>
                            <CardContent className="p-3">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <div className="w-2 h-2 bg-primary rounded-full" />
                                  <span className="font-medium">{section.name}</span>
                                  <Badge variant="outline" className="text-xs">
                                    {section.type}
                                  </Badge>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                                    <Settings className="h-3 w-3" />
                                  </Button>
                                  <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                                    <Copy className="h-3 w-3" />
                                  </Button>
                                  <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-destructive">
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                              <div className="mt-2 text-xs text-muted-foreground">
                                {section.blocks.length} blocks
                              </div>
                            </CardContent>
                          </Card>
                        )
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>

                {/* Settings Tab */}
                <TabsContent value="settings" className="h-full m-0 p-4">
                  <ScrollArea className="h-full">
                    <div className="space-y-4">
                      <div>
                        <Label>Layout Name</Label>
                        <Input
                          value={state.layout.name}
                          onChange={(e) => updateLayout({ ...state.layout, name: e.target.value })}
                          placeholder="Enter layout name"
                        />
                      </div>
                      
                      <div>
                        <Label>Description</Label>
                        <Textarea
                          value={state.layout.description || ''}
                          onChange={(e) => updateLayout({ ...state.layout, description: e.target.value })}
                          placeholder="Enter layout description"
                          rows={3}
                        />
                      </div>
                    </div>
                  </ScrollArea>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        )}

        {/* Canvas */}
        <div className="flex-1 bg-gray-50">
          {/* Layout preview/editor would go here */}
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <Layout className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Layout Canvas</h3>
              <p className="text-muted-foreground">
                Your layout structure will be displayed here
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Helper functions
function getDefaultSectionConfig(sectionType: string) {
  return {
    layout: 'block',
    alignment: 'left',
    spacing: { top: '0', right: '0', bottom: '0', left: '0' },
    background: { type: 'none' },
    container: { maxWidth: '1200px', padding: '1rem' }
  }
}

function getDefaultSectionStyling(sectionType: string) {
  return {
    background: { type: 'none' },
    border: { width: '0', style: 'none', color: 'transparent' },
    spacing: { top: '0', right: '0', bottom: '0', left: '0' },
    shadow: { type: 'none' }
  }
}

function getDefaultResponsiveSettings() {
  return {
    mobile: { isVisible: true },
    tablet: { isVisible: true },
    desktop: { isVisible: true },
    large: { isVisible: true }
  }
}
