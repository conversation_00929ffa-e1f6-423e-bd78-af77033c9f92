'use client'

import React, { useState, useEffect } from 'react'
import { useLayoutBuilder } from '../context'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Sparkles,
  Wand2,
  Brain,
  Plus,
  Loader2,
  Check,
  X,
  RefreshCw,
  Target,
  Palette,
  Layout,
  Navigation,
  Package,
  Copyright,
  FileText,
  Star,
  ChevronDown,
  ChevronUp,
  Lightbulb,
  Layers
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'
import { SectionType, BlockType } from '../types'

interface EnhancedAILayoutGeneratorProps {
  className?: string
  onLayoutGenerated?: (layout: any) => void
}

export function EnhancedAILayoutGenerator({ className, onLayoutGenerated }: EnhancedAILayoutGeneratorProps) {
  const { state, addSection, addBlock } = useLayoutBuilder()
  const [isExpanded, setIsExpanded] = useState(false)
  const [activeMode, setActiveMode] = useState<'simple' | 'advanced'>('simple')
  
  // Simple mode state
  const [simplePrompt, setSimplePrompt] = useState('')
  
  // Advanced mode state
  const [layoutType, setLayoutType] = useState('')
  const [requirements, setRequirements] = useState('')
  const [context, setContext] = useState('')
  const [style, setStyle] = useState('')
  const [targetSections, setTargetSections] = useState<SectionType[]>([])
  
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedLayouts, setGeneratedLayouts] = useState<any[]>([])
  const [smartSuggestions, setSmartSuggestions] = useState<any[]>([])

  // Layout type options
  const layoutTypes = [
    { value: 'ecommerce', label: 'E-commerce Store', icon: Package, description: 'Online store with product focus' },
    { value: 'corporate', label: 'Corporate Website', icon: FileText, description: 'Professional business site' },
    { value: 'blog', label: 'Blog Platform', icon: FileText, description: 'Content-focused layout' },
    { value: 'portfolio', label: 'Portfolio Site', icon: Star, description: 'Creative showcase layout' },
    { value: 'landing', label: 'Landing Page', icon: Target, description: 'Single-page conversion focus' },
    { value: 'dashboard', label: 'Admin Dashboard', icon: Layout, description: 'Data and control interface' },
    { value: 'magazine', label: 'Magazine Layout', icon: FileText, description: 'Editorial content layout' },
    { value: 'custom', label: 'Custom Layout', icon: Layers, description: 'Tailored to specific needs' }
  ]

  // Style options
  const styleOptions = [
    { value: 'modern', label: 'Modern', description: 'Clean, contemporary design' },
    { value: 'minimal', label: 'Minimal', description: 'Simple, uncluttered layout' },
    { value: 'bold', label: 'Bold', description: 'Strong, impactful design' },
    { value: 'elegant', label: 'Elegant', description: 'Sophisticated, refined look' },
    { value: 'creative', label: 'Creative', description: 'Artistic, unique design' },
    { value: 'professional', label: 'Professional', description: 'Business-focused appearance' }
  ]

  // Section options
  const sectionOptions = [
    { value: 'header', label: 'Header', icon: Navigation, description: 'Top navigation and branding' },
    { value: 'sidebar', label: 'Sidebar', icon: Package, description: 'Side content and widgets' },
    { value: 'footer', label: 'Footer', icon: Copyright, description: 'Bottom links and info' }
  ]

  // Generate smart suggestions based on current layout
  useEffect(() => {
    const generateSmartSuggestions = () => {
      const currentSections = Object.keys(state.layout.structure).filter(
        key => state.layout.structure[key as keyof typeof state.layout.structure] !== undefined
      )
      const suggestions = []

      // Analyze current layout structure
      const hasHeader = currentSections.includes('header')
      const hasSidebar = currentSections.includes('sidebar')
      const hasFooter = currentSections.includes('footer')

      // Suggest missing essential sections
      if (!hasHeader) {
        suggestions.push({
          type: 'section',
          title: 'Add Header Section',
          description: 'Create a header with navigation and branding',
          prompt: 'Create a modern header section with logo, main navigation, and user utilities for an e-commerce site',
          priority: 'high',
          sectionType: 'header' as SectionType
        })
      }

      if (!hasFooter && currentSections.length > 0) {
        suggestions.push({
          type: 'section',
          title: 'Add Footer Section',
          description: 'Include footer with links and information',
          prompt: 'Create a comprehensive footer with company links, contact information, and social media',
          priority: 'high',
          sectionType: 'footer' as SectionType
        })
      }

      if (!hasSidebar && hasHeader) {
        suggestions.push({
          type: 'section',
          title: 'Add Sidebar',
          description: 'Include sidebar for additional content',
          prompt: 'Add a sidebar with widget areas for categories, recent posts, and promotional content',
          priority: 'medium',
          sectionType: 'sidebar' as SectionType
        })
      }

      // Layout enhancement suggestions
      if (currentSections.length > 1) {
        suggestions.push({
          type: 'enhancement',
          title: 'Optimize Layout',
          description: 'Improve layout structure and flow',
          prompt: 'Optimize the current layout for better user experience and conversion',
          priority: 'medium',
          sectionType: null
        })
      }

      // Responsive design suggestions
      if (currentSections.length > 0) {
        suggestions.push({
          type: 'responsive',
          title: 'Mobile Optimization',
          description: 'Enhance mobile and tablet experience',
          prompt: 'Optimize layout for mobile devices with responsive navigation and touch-friendly elements',
          priority: 'low',
          sectionType: null
        })
      }

      setSmartSuggestions(suggestions.slice(0, 4)) // Limit to 4 suggestions
    }

    generateSmartSuggestions()
  }, [state.layout.structure])

  const handleSimpleGenerate = async () => {
    if (!simplePrompt.trim()) return

    setIsGenerating(true)
    try {
      // Mock AI generation - in production, this would call an AI service
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const result = {
        id: Date.now().toString(),
        type: 'auto-generated',
        description: simplePrompt,
        sections: ['header', 'main', 'footer'],
        timestamp: new Date().toISOString()
      }

      setGeneratedLayouts(prev => [result, ...prev])
      setSimplePrompt('')
      toast.success('Layout generated successfully!')
      
      if (onLayoutGenerated) {
        onLayoutGenerated(result)
      }
    } catch (error) {
      console.error('Simple generation error:', error)
      toast.error('Failed to generate layout')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleAdvancedGenerate = async () => {
    if (!layoutType || !requirements) return

    setIsGenerating(true)
    try {
      // Mock AI generation
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      const result = {
        id: Date.now().toString(),
        type: layoutType,
        description: requirements,
        sections: targetSections.length > 0 ? targetSections : ['header', 'main', 'footer'],
        style,
        context,
        timestamp: new Date().toISOString()
      }

      setGeneratedLayouts(prev => [result, ...prev])
      
      // Reset form
      setLayoutType('')
      setRequirements('')
      setContext('')
      setStyle('')
      setTargetSections([])
      
      toast.success('Advanced layout generated successfully!')
      
      if (onLayoutGenerated) {
        onLayoutGenerated(result)
      }
    } catch (error) {
      console.error('Advanced generation error:', error)
      toast.error('Failed to generate layout')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleSuggestionGenerate = async (suggestion: any) => {
    setIsGenerating(true)
    try {
      // Mock AI generation
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      if (suggestion.sectionType) {
        addSection(suggestion.sectionType)
        toast.success(`${suggestion.title} completed successfully!`)
      } else {
        toast.success(`${suggestion.title} applied successfully!`)
      }
    } catch (error) {
      console.error('Suggestion generation error:', error)
      toast.error('Failed to apply suggestion')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleApplyLayout = (layout: any) => {
    try {
      // Apply the generated layout
      layout.sections.forEach((sectionType: SectionType) => {
        if (sectionType !== 'main') { // main usually exists by default
          addSection(sectionType)
        }
      })
      
      toast.success('Layout applied successfully!')
    } catch (error) {
      console.error('Apply layout error:', error)
      toast.error('Failed to apply layout')
    }
  }

  return (
    <Card className={cn('h-full flex flex-col', className)}>
      {/* Header */}
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Wand2 className="h-5 w-5 text-blue-600" />
            AI Layout Generator
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>
        <p className="text-sm text-muted-foreground">
          Generate complete layouts with AI assistance
        </p>
      </CardHeader>

      {isExpanded && (
        <CardContent className="flex-1 flex flex-col p-4 space-y-4">
          {/* Mode Toggle */}
          <div className="flex rounded-lg bg-muted p-1">
            <Button
              variant={activeMode === 'simple' ? 'default' : 'ghost'}
              size="sm"
              className="flex-1 h-8"
              onClick={() => setActiveMode('simple')}
            >
              <Sparkles className="mr-2 h-3 w-3" />
              Simple
            </Button>
            <Button
              variant={activeMode === 'advanced' ? 'default' : 'ghost'}
              size="sm"
              className="flex-1 h-8"
              onClick={() => setActiveMode('advanced')}
            >
              <Brain className="mr-2 h-3 w-3" />
              Advanced
            </Button>
          </div>

          <ScrollArea className="flex-1">
            {activeMode === 'simple' ? (
              <div className="space-y-4">
                {/* Simple Mode */}
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      Describe your layout requirements
                    </label>
                    <Textarea
                      value={simplePrompt}
                      onChange={(e) => setSimplePrompt(e.target.value)}
                      placeholder="e.g., Create an e-commerce layout with header navigation, product sidebar, and comprehensive footer"
                      className="min-h-[80px]"
                      disabled={isGenerating}
                    />
                  </div>
                  
                  <Button
                    onClick={handleSimpleGenerate}
                    disabled={isGenerating || !simplePrompt.trim()}
                    className="w-full"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Sparkles className="mr-2 h-4 w-4" />
                        Generate Layout
                      </>
                    )}
                  </Button>
                </div>

                {/* Smart Suggestions */}
                {smartSuggestions.length > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Lightbulb className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm font-medium">Smart Suggestions</span>
                    </div>
                    
                    <div className="space-y-2">
                      {smartSuggestions.map((suggestion, index) => (
                        <Card key={index} className="p-3">
                          <div className="flex items-start justify-between gap-3">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium text-sm">{suggestion.title}</h4>
                                <Badge 
                                  variant={suggestion.priority === 'high' ? 'default' : 'secondary'}
                                  className="text-xs"
                                >
                                  {suggestion.priority}
                                </Badge>
                              </div>
                              <p className="text-xs text-muted-foreground">{suggestion.description}</p>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleSuggestionGenerate(suggestion)}
                              disabled={isGenerating}
                              className="text-xs h-7 px-2"
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {/* Advanced Mode */}
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Layout Type</label>
                    <Select value={layoutType} onValueChange={setLayoutType} disabled={isGenerating}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose layout type" />
                      </SelectTrigger>
                      <SelectContent>
                        {layoutTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            <div className="flex items-center gap-2">
                              <type.icon className="h-4 w-4" />
                              <div>
                                <div className="font-medium">{type.label}</div>
                                <div className="text-xs text-muted-foreground">{type.description}</div>
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Requirements</label>
                    <Textarea
                      value={requirements}
                      onChange={(e) => setRequirements(e.target.value)}
                      placeholder="Describe specific requirements for this layout..."
                      className="min-h-[60px]"
                      disabled={isGenerating}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Context (Optional)</label>
                    <Input
                      value={context}
                      onChange={(e) => setContext(e.target.value)}
                      placeholder="Additional context or business information"
                      disabled={isGenerating}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Style</label>
                    <Select value={style} onValueChange={setStyle} disabled={isGenerating}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose style" />
                      </SelectTrigger>
                      <SelectContent>
                        {styleOptions.map((styleOption) => (
                          <SelectItem key={styleOption.value} value={styleOption.value}>
                            <div>
                              <div className="font-medium">{styleOption.label}</div>
                              <div className="text-xs text-muted-foreground">{styleOption.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Button
                    onClick={handleAdvancedGenerate}
                    disabled={isGenerating || !layoutType || !requirements}
                    className="w-full"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Brain className="mr-2 h-4 w-4" />
                        Generate Layout
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )}
          </ScrollArea>

          {/* Generated Layouts Preview */}
          {generatedLayouts.length > 0 && (
            <div className="space-y-2 border-t pt-4">
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">Recently Generated</span>
              </div>
              <div className="space-y-2">
                {generatedLayouts.slice(0, 2).map((layout, index) => (
                  <Card key={index} className="p-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {layout.type}
                        </Badge>
                        <span className="text-sm truncate">{layout.description}</span>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleApplyLayout(layout)}
                        className="text-xs h-6 px-2"
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  )
}
