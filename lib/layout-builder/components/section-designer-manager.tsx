'use client'

import React, { useState } from 'react'
import { useLayoutBuilder } from '../context'
import { SectionType } from '../types'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Navigation, 
  Package, 
  Copyright, 
  FileText,
  Plus,
  Settings,
  Layers,
  Eye,
  EyeOff
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { HeaderDesigner } from './section-designers/header-designer'
import { SidebarDesigner } from './section-designers/sidebar-designer'
import { FooterDesigner } from './section-designers/footer-designer'

export function SectionDesignerManager() {
  const { state, addSection, updateSection } = useLayoutBuilder()
  const [activeSection, setActiveSection] = useState<SectionType | 'overview'>('overview')

  const sections = [
    {
      type: 'header' as SectionType,
      name: 'Header',
      description: 'Site header with logo, navigation, and utilities',
      icon: Navigation,
      color: 'bg-blue-100 text-blue-700',
      exists: !!state.layout.structure.header
    },
    {
      type: 'sidebar' as SectionType,
      name: 'Sidebar',
      description: 'Sidebar with widgets and additional content',
      icon: Package,
      color: 'bg-green-100 text-green-700',
      exists: !!state.layout.structure.sidebar
    },
    {
      type: 'footer' as SectionType,
      name: 'Footer',
      description: 'Site footer with links and copyright',
      icon: Copyright,
      color: 'bg-gray-100 text-gray-700',
      exists: !!state.layout.structure.footer
    }
  ]

  const handleAddSection = (sectionType: SectionType) => {
    addSection(sectionType)
    setActiveSection(sectionType)
  }

  const handleToggleSectionVisibility = (sectionType: SectionType) => {
    const section = state.layout.structure[sectionType]
    if (section) {
      updateSection(section.id, { isVisible: !section.isVisible })
    }
  }

  const getSectionBlockCount = (sectionType: SectionType) => {
    const section = state.layout.structure[sectionType]
    return section?.blocks.length || 0
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b bg-gradient-to-r from-purple-50 to-blue-50">
        <div className="flex items-center gap-2 mb-3">
          <div className="p-1.5 bg-purple-100 rounded-lg">
            <Layers className="h-4 w-4 text-purple-600" />
          </div>
          <h2 className="text-lg font-semibold text-gray-900">Section Designers</h2>
        </div>
        
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <span>Design individual sections of your layout</span>
        </div>
      </div>

      {/* Section Tabs */}
      <div className="border-b">
        <Tabs value={activeSection} onValueChange={(value) => setActiveSection(value as any)}>
          <ScrollArea className="w-full">
            <TabsList className="inline-flex h-auto p-1 bg-transparent">
              <TabsTrigger 
                value="overview" 
                className="flex items-center gap-2 px-4 py-2 text-xs font-medium data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700"
              >
                <Settings className="h-3 w-3" />
                Overview
              </TabsTrigger>
              {sections.map((section) => (
                <TabsTrigger 
                  key={section.type}
                  value={section.type}
                  className="flex items-center gap-2 px-4 py-2 text-xs font-medium data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700"
                  disabled={!section.exists}
                >
                  <section.icon className="h-3 w-3" />
                  {section.name}
                  {section.exists && (
                    <Badge variant="outline" className="text-xs">
                      {getSectionBlockCount(section.type)}
                    </Badge>
                  )}
                </TabsTrigger>
              ))}
            </TabsList>
          </ScrollArea>
        </Tabs>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeSection} onValueChange={(value) => setActiveSection(value as any)}>
          {/* Overview Tab */}
          <TabsContent value="overview" className="h-full mt-0 p-4">
            <div className="space-y-4">
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Layers className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Layout Sections</h3>
                <p className="text-sm text-muted-foreground mb-6">
                  Manage and design the main sections of your layout
                </p>
              </div>

              <div className="grid grid-cols-1 gap-4">
                {sections.map((section) => (
                  <div
                    key={section.type}
                    className={cn(
                      'border rounded-lg p-4 transition-all duration-200',
                      section.exists 
                        ? 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm' 
                        : 'bg-gray-50 border-dashed border-gray-300'
                    )}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={cn('p-2 rounded-lg', section.color)}>
                          <section.icon className="h-5 w-5" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-sm text-gray-900">{section.name}</h4>
                          <p className="text-xs text-muted-foreground">{section.description}</p>
                          {section.exists && (
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className="text-xs">
                                {getSectionBlockCount(section.type)} blocks
                              </Badge>
                              <Badge 
                                variant={state.layout.structure[section.type]?.isVisible ? 'default' : 'secondary'} 
                                className="text-xs"
                              >
                                {state.layout.structure[section.type]?.isVisible ? 'Visible' : 'Hidden'}
                              </Badge>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        {section.exists ? (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleToggleSectionVisibility(section.type)}
                              className="h-8 w-8 p-0"
                            >
                              {state.layout.structure[section.type]?.isVisible ? (
                                <Eye className="h-4 w-4" />
                              ) : (
                                <EyeOff className="h-4 w-4" />
                              )}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setActiveSection(section.type)}
                              className="text-xs"
                            >
                              <Settings className="h-3 w-3 mr-1" />
                              Design
                            </Button>
                          </>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleAddSection(section.type)}
                            className="text-xs"
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            Add {section.name}
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Main Content Section Info */}
              <div className="border rounded-lg p-4 bg-blue-50 border-blue-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-sm text-gray-900">Main Content</h4>
                    <p className="text-xs text-muted-foreground">
                      The main content area is always present and contains your page content
                    </p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="outline" className="text-xs">
                        {getSectionBlockCount('main')} blocks
                      </Badge>
                      <Badge variant="default" className="text-xs">
                        Always Visible
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Header Designer */}
          <TabsContent value="header" className="h-full mt-0">
            <HeaderDesigner />
          </TabsContent>

          {/* Sidebar Designer */}
          <TabsContent value="sidebar" className="h-full mt-0">
            <SidebarDesigner />
          </TabsContent>

          {/* Footer Designer */}
          <TabsContent value="footer" className="h-full mt-0">
            <FooterDesigner />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
