'use client'

import React from 'react'
import Link from 'next/link'
import { Search, ShoppingCart, Menu, X } from 'lucide-react'
import { LayoutBlock, ResolvedLayout, type NavigationItem } from '../types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

interface LayoutBlockRendererProps {
  block: LayoutBlock
  layout: ResolvedLayout
}

export function LayoutBlockRenderer({ block, layout }: LayoutBlockRendererProps) {
  if (!block.isVisible) {
    return null
  }

  const blockStyles = getBlockStyles(block)
  const blockClasses = getBlockClasses(block)

  return (
    <div 
      className={blockClasses}
      style={blockStyles}
      data-block-type={block.type}
      data-block-id={block.id}
    >
      {renderBlockContent(block, layout)}
    </div>
  )
}

function renderBlockContent(block: LayoutBlock, layout: ResolvedLayout): React.ReactNode {
  switch (block.type) {
    case 'logo':
      return <LogoBlock block={block} />
    
    case 'navigation':
      return <NavigationBlock block={block} layout={layout} />
    
    case 'search':
      return <SearchBlock block={block} />
    
    case 'cart':
      return <CartBlock block={block} />
    
    case 'content':
      return <ContentBlock block={block} />
    
    case 'social':
      return <SocialBlock block={block} />
    
    case 'copyright':
      return <CopyrightBlock block={block} />
    
    case 'breadcrumbs':
      return <BreadcrumbsBlock block={block} />
    
    case 'widget':
      return <WidgetBlock block={block} layout={layout} />
    
    case 'custom':
      return <CustomBlock block={block} />
    
    default:
      return <div>Unknown block type: {block.type}</div>
  }
}

// Individual Block Components

function LogoBlock({ block }: { block: LayoutBlock }) {
  const { content, configuration } = block
  
  return (
    <div className={`logo-block ${getAlignmentClass(configuration.alignment)}`}>
      <Link href="/" className="flex items-center space-x-2">
        {content.image ? (
          <img 
            src={content.image} 
            alt={content.text || 'Logo'} 
            className={`h-${getSizeClass(configuration.size)}`}
          />
        ) : (
          <span className={`font-bold text-${getSizeClass(configuration.size)} text-primary`}>
            {content.text || 'Logo'}
          </span>
        )}
      </Link>
    </div>
  )
}

function NavigationBlock({ block, layout }: { block: LayoutBlock; layout: ResolvedLayout }) {
  const { content } = block
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false)
  
  // Find the navigation menu
  const menu = layout.menus.find(m => m.slug === content.menu || m.id === content.menu)
  
  if (!menu) {
    return <div>Navigation menu not found</div>
  }

  return (
    <nav className="navigation-block">
      {/* Desktop Navigation */}
      <div className="hidden md:flex items-center space-x-6">
        {menu.items.map((item) => (
          <NavigationItem key={item.id} item={item} />
        ))}
      </div>

      {/* Mobile Navigation */}
      <div className="md:hidden">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
        </Button>
        
        {isMobileMenuOpen && (
          <div className="absolute top-full left-0 right-0 bg-white shadow-lg border-t z-50">
            <div className="py-2">
              {menu.items.map((item) => (
                <MobileNavigationItem 
                  key={item.id} 
                  item={item} 
                  onClose={() => setIsMobileMenuOpen(false)}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}

function NavigationItem({ item }: { item: NavigationItem }) {
  if (!item.isVisible) return null

  return (
    <Link 
      href={item.url}
      target={item.target}
      className={`nav-item hover:text-primary transition-colors ${item.cssClass || ''}`}
    >
      {item.icon && <span className="mr-2">{item.icon}</span>}
      {item.text}
    </Link>
  )
}

function MobileNavigationItem({ 
  item, 
  onClose 
}: { 
  item: NavigationItem
  onClose: () => void 
}) {
  if (!item.isVisible) return null

  return (
    <Link 
      href={item.url}
      target={item.target}
      className={`block px-4 py-2 hover:bg-gray-50 ${item.cssClass || ''}`}
      onClick={onClose}
    >
      {item.icon && <span className="mr-2">{item.icon}</span>}
      {item.text}
    </Link>
  )
}

function SearchBlock({ block }: { block: LayoutBlock }) {
  const { configuration } = block
  
  return (
    <div className={`search-block ${getAlignmentClass(configuration.alignment)}`}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          type="search"
          placeholder="Search..."
          className="pl-10 w-64"
        />
      </div>
    </div>
  )
}

function CartBlock({ block: _block }: { block: LayoutBlock }) {
  return (
    <div className="cart-block">
      <Button variant="ghost" size="sm" className="relative">
        <ShoppingCart className="h-5 w-5" />
        <span className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs rounded-full h-5 w-5 flex items-center justify-center">
          0
        </span>
      </Button>
    </div>
  )
}

function ContentBlock({ block: _block }: { block: LayoutBlock }) {
  // This is where the main page content will be rendered
  // The actual content is passed as children to the LayoutRenderer
  return <div className="content-block" />
}

function SocialBlock({ block }: { block: LayoutBlock }) {
  const { content } = block
  
  return (
    <div className="social-block flex space-x-4">
      {content.links?.map((link: any) => (
        <Link 
          key={link.id}
          href={link.url}
          target={link.target}
          className="text-gray-600 hover:text-primary transition-colors"
        >
          {link.icon && <span>{link.icon}</span>}
          {!link.icon && link.text}
        </Link>
      ))}
    </div>
  )
}

function CopyrightBlock({ block }: { block: LayoutBlock }) {
  const { content, configuration } = block
  
  return (
    <div className={`copyright-block ${getAlignmentClass(configuration.alignment)}`}>
      <p className="text-sm text-gray-600">
        {content.text || `© ${new Date().getFullYear()} All rights reserved.`}
      </p>
    </div>
  )
}

function BreadcrumbsBlock({ block: _block }: { block: LayoutBlock }) {
  // TODO: Implement breadcrumbs based on current route
  return (
    <div className="breadcrumbs-block">
      <nav className="text-sm text-gray-600">
        <Link href="/" className="hover:text-primary">Home</Link>
        <span className="mx-2">/</span>
        <span>Current Page</span>
      </nav>
    </div>
  )
}

function WidgetBlock({ block, layout }: { block: LayoutBlock; layout: ResolvedLayout }) {
  const { content } = block
  
  // Find the widget area
  const widgetArea = layout.widgets.find(w => w.slug === content.widget || w.id === content.widget)
  
  if (!widgetArea) {
    return <div>Widget area not found</div>
  }

  return (
    <div className="widget-block">
      {widgetArea.widgets.map((widget) => (
        <div key={widget.id} className="widget mb-4">
          {widget.title && (
            <h3 className="widget-title font-semibold mb-2">{widget.title}</h3>
          )}
          <div className="widget-content">
            {/* Render widget content based on type */}
            {renderWidgetContent(widget)}
          </div>
        </div>
      ))}
    </div>
  )
}

function CustomBlock({ block }: { block: LayoutBlock }) {
  const { content } = block
  
  return (
    <div className="custom-block">
      {content.html ? (
        <div dangerouslySetInnerHTML={{ __html: content.html }} />
      ) : (
        <div>{content.text}</div>
      )}
    </div>
  )
}

// Utility functions

function renderWidgetContent(widget: any): React.ReactNode {
  switch (widget.type) {
    case 'text':
      return <div>{widget.content.text}</div>
    case 'html':
      return <div dangerouslySetInnerHTML={{ __html: widget.content.html }} />
    case 'recent-posts':
      return <div>Recent Posts Widget</div>
    case 'categories':
      return <div>Categories Widget</div>
    default:
      return <div>Unknown widget type: {widget.type}</div>
  }
}

function getBlockStyles(block: LayoutBlock): React.CSSProperties {
  const styles: React.CSSProperties = {}
  const { styling } = block

  if (!styling) return styles

  // Apply styling similar to section styling
  // Background, spacing, typography, etc.
  
  return styles
}

function getBlockClasses(block: LayoutBlock): string {
  const classes = ['layout-block', `block-${block.type}`]
  
  // Add configuration-based classes
  if (block.configuration) {
    if (block.configuration.size) {
      classes.push(`size-${block.configuration.size}`)
    }
    if (block.configuration.alignment) {
      classes.push(getAlignmentClass(block.configuration.alignment))
    }
  }

  return classes.join(' ')
}

function getAlignmentClass(alignment: string): string {
  switch (alignment) {
    case 'left': return 'text-left'
    case 'center': return 'text-center'
    case 'right': return 'text-right'
    default: return 'text-left'
  }
}

function getSizeClass(size: string): string {
  switch (size) {
    case 'small': return '8'
    case 'medium': return '10'
    case 'large': return '12'
    default: return '10'
  }
}

export default LayoutBlockRenderer
