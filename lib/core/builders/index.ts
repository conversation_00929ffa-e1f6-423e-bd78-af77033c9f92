// Core Builders Module
// Enterprise-grade page and layout builders with full CMS integration

// Types
export type {
  CMSBuilderState,
  CMSBlock,
  CustomFieldBinding,
  TaxonomyBinding,
  AIBlockMetadata,
  ConditionalRule,
  DynamicContentRule,
  ResponsiveBlockSettings,
  BlockSettings,
  BlockFactory,
  BlockCreationContext,
  BlockTypeDefinition,
  BlockEnhancementOptions,
  EnhancedBlockProps,
  CMSBuilderConfig,
  DeviceType,
  PermissionLevel,
  BuilderMode,
  ResponsiveLayouts,
  BuilderHistoryEntry,
  CMSPermissions,
  ActiveHooks,
  ActivePlugins,
  ValidationResult,
  ValidationError,
  ValidationWarning,
  BlockPosition,
  BuilderEvent,
  AIGenerationRequest,
  AIGenerationResponse
} from './types'

// Core Factory and HOCs
export { CMSBlockFactory } from './block-factory'
export {
  withCMSEnhancements,
  withPermissions,
  withStyling,
  withValidation,
  withAI,
  withAnalytics,
  createEnhancedBlock
} from './block-hocs'

// Context and State Management
export { CMSBuilderProvider, useCMSBuilder } from './cms-builder-context'

// Main Builder Components
export { UnifiedCMSBuilder } from './unified-cms-builder'
export { CMSPageBuilder } from './cms-page-builder'
export { CMSLayoutBuilder } from './cms-layout-builder'
export { AdvancedGridBuilder } from './advanced-grid-builder'

// Utility Functions
export const builderUtils = {
  /**
   * Create a new block instance with CMS integration
   */
  createBlock: (type: string, postType: any, context?: any) => {
    const factory = CMSBlockFactory.getInstance()
    return factory.createBlock(type, postType, context)
  },

  /**
   * Validate a block against post type requirements
   */
  validateBlock: (block: any, postType: any) => {
    const factory = CMSBlockFactory.getInstance()
    return factory.validateBlock(block, postType)
  },

  /**
   * Get available blocks for a post type
   */
  getAvailableBlocks: (postType: any) => {
    const factory = CMSBlockFactory.getInstance()
    return factory.getAvailableBlocks(postType)
  },

  /**
   * Register a new block type
   */
  registerBlockType: (definition: any) => {
    const factory = CMSBlockFactory.getInstance()
    return factory.registerBlockType(definition)
  },

  /**
   * Generate responsive layouts from blocks
   */
  generateResponsiveLayouts: (blocks: any[], breakpoints: Record<string, number>) => {
    const baseLayout = blocks.map((block, index) => ({
      i: block.id,
      x: (index % 4) * 3,
      y: Math.floor(index / 4) * 2,
      w: block.gridLayout?.w ?? 3,
      h: block.gridLayout?.h ?? 2,
      minW: block.gridLayout?.minW ?? 1,
      minH: block.gridLayout?.minH ?? 1
    }))

    return {
      lg: baseLayout,
      md: baseLayout.map(item => ({ ...item, w: Math.min(item.w, 10) })),
      sm: baseLayout.map(item => ({ ...item, w: Math.min(item.w, 6) })),
      xs: baseLayout.map(item => ({ ...item, w: Math.min(item.w, 4) })),
      xxs: baseLayout.map(item => ({ ...item, w: Math.min(item.w, 2) }))
    }
  },

  /**
   * Convert blocks to content for saving
   */
  blocksToContent: (blocks: any[]) => {
    return blocks.map(block => ({
      type: block.type,
      content: block.content,
      config: block.config,
      customFieldBindings: block.customFieldBindings,
      taxonomyBindings: block.taxonomyBindings,
      responsiveSettings: block.responsiveSettings
    }))
  },

  /**
   * Convert content back to blocks
   */
  contentToBlocks: (content: any[], postType: any) => {
    const factory = CMSBlockFactory.getInstance()
    
    return content.map((item, index) => ({
      id: `block-${Date.now()}-${index}`,
      type: item.type,
      content: item.content || {},
      config: item.config || {},
      postTypeContext: postType.name,
      customFieldBindings: item.customFieldBindings || [],
      taxonomyBindings: item.taxonomyBindings || [],
      responsiveSettings: item.responsiveSettings || {
        mobile: { visible: true },
        tablet: { visible: true },
        desktop: { visible: true },
        large: { visible: true }
      },
      gridLayout: {
        x: (index % 4) * 3,
        y: Math.floor(index / 4) * 2,
        w: 3,
        h: 2,
        minW: 1,
        minH: 1
      }
    }))
  }
}

// Constants
export const BUILDER_CONSTANTS = {
  // Default grid settings
  DEFAULT_GRID_SETTINGS: {
    breakpoints: { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 },
    columns: { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 },
    rowHeight: 100,
    margin: [16, 16] as [number, number],
    containerPadding: [0, 0] as [number, number]
  },

  // Default block types
  DEFAULT_BLOCK_TYPES: [
    'text',
    'hero',
    'image',
    'button',
    'product-grid',
    'testimonials',
    'gallery',
    'video',
    'custom-field-display',
    'taxonomy-terms'
  ],

  // South African e-commerce specific
  SA_ECOMMERCE_BLOCKS: [
    'product-grid',
    'product-showcase',
    'price-display',
    'payment-methods',
    'shipping-calculator',
    'currency-converter'
  ],

  // Device breakpoints
  DEVICE_BREAKPOINTS: {
    mobile: 480,
    tablet: 768,
    desktop: 1024,
    large: 1200
  },

  // Permission levels
  PERMISSION_LEVELS: {
    READ: 'read',
    WRITE: 'write',
    ADMIN: 'admin',
    SUPER_ADMIN: 'super_admin'
  },

  // Builder modes
  BUILDER_MODES: {
    PAGE: 'page',
    LAYOUT: 'layout',
    TEMPLATE: 'template',
    COMPONENT: 'component'
  }
}

// Hooks for extending functionality
export const builderHooks = {
  /**
   * Add a hook for block creation
   */
  onBlockCreate: (callback: (block: any) => void) => {
    // This would integrate with the CMS hook system
    console.log('Block create hook registered')
  },

  /**
   * Add a hook for block update
   */
  onBlockUpdate: (callback: (block: any) => void) => {
    // This would integrate with the CMS hook system
    console.log('Block update hook registered')
  },

  /**
   * Add a hook for layout change
   */
  onLayoutChange: (callback: (layout: any) => void) => {
    // This would integrate with the CMS hook system
    console.log('Layout change hook registered')
  },

  /**
   * Add a hook for AI generation
   */
  onAIGeneration: (callback: (result: any) => void) => {
    // This would integrate with the CMS hook system
    console.log('AI generation hook registered')
  }
}

// Plugin system integration
export const builderPlugins = {
  /**
   * Register a builder plugin
   */
  register: (plugin: {
    name: string
    version: string
    blocks?: any[]
    hooks?: any[]
    components?: any[]
  }) => {
    console.log(`Builder plugin '${plugin.name}' registered`)
    
    // Register custom blocks
    if (plugin.blocks) {
      const factory = CMSBlockFactory.getInstance()
      plugin.blocks.forEach(block => factory.registerBlockType(block))
    }

    // Register hooks
    if (plugin.hooks) {
      plugin.hooks.forEach(hook => {
        // This would integrate with the CMS hook system
        console.log(`Hook '${hook.name}' registered`)
      })
    }

    return true
  },

  /**
   * Get registered plugins
   */
  getRegistered: () => {
    // This would return registered plugins from the CMS system
    return []
  }
}

// AI Integration helpers
export const aiHelpers = {
  /**
   * Generate block suggestions based on content
   */
  generateSuggestions: async (content: string, postType: any) => {
    // This would integrate with your AI service
    return []
  },

  /**
   * Optimize block layout using AI
   */
  optimizeLayout: async (blocks: any[], target: string) => {
    // This would integrate with your AI service
    return blocks
  },

  /**
   * Generate content for blocks using AI
   */
  generateContent: async (blockType: string, context: any) => {
    // This would integrate with your AI service
    return {}
  }
}

// Advanced Features
export { aiBuilderService, aiUtils } from './ai-integration'
export { validationSystem, validationUtils } from './validation-system'
export {
  registerSAEcommerceBlocks,
  SAEcommerceBlockComponents,
  SAPaymentMethodsBlock,
  SAShippingCalculatorBlock,
  SAPriceDisplayBlock,
  SABusinessHoursBlock,
  SAContactInfoBlock,
  POPIComplianceBlock
} from './sa-ecommerce-blocks'

// Examples
export { default as Examples } from './examples/complete-implementation'

// Export everything for easy access
export default {
  // Components
  UnifiedCMSBuilder,
  CMSPageBuilder,
  CMSLayoutBuilder,
  AdvancedGridBuilder,

  // Context
  CMSBuilderProvider,
  useCMSBuilder,

  // Factory and HOCs
  CMSBlockFactory,
  createEnhancedBlock,

  // Advanced Features
  aiBuilderService,
  validationSystem,
  registerSAEcommerceBlocks,
  SAEcommerceBlockComponents,

  // Utilities
  builderUtils,
  builderHooks,
  builderPlugins,
  aiHelpers,
  aiUtils,
  validationUtils,

  // Constants
  BUILDER_CONSTANTS,

  // Examples
  Examples
}
