// Simple Usage Example
// Basic example showing how to use the CMS Builder system

'use client'

import React, { useState, useEffect } from 'react'
import { 
  UnifiedCMSBuilder,
  CMSPageBuilder,
  CMSBuilderProvider,
  registerSAEcommerceBlocks,
  builderUtils,
  validationSystem
} from '@/lib/core/builders'
import { PostType, CMSContent } from '@/lib/cms/types'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, AlertTriangle, Zap } from 'lucide-react'
import { toast } from 'sonner'

// Example 1: Basic Page Builder
export function BasicPageBuilder() {
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    // Register South African e-commerce blocks
    registerSAEcommerceBlocks()
    setIsInitialized(true)
  }, [])

  // Mock post type for demonstration
  const mockPostType: PostType = {
    id: '1',
    name: 'page',
    label: 'Page',
    labelPlural: 'Pages',
    description: 'Static pages',
    isPublic: true,
    isHierarchical: true,
    hasArchive: false,
    supportsTitle: true,
    supportsContent: true,
    supportsExcerpt: true,
    supportsThumbnail: true,
    supportsComments: false,
    supportsRevisions: true,
    supportsPageBuilder: true,
    supportsCustomFields: true,
    taxonomies: [],
    customFields: [],
    templates: [],
    isSystem: false,
    isActive: true
  }

  const handleSave = async (content: CMSContent) => {
    try {
      // Validate content before saving
      const validationReport = await validationSystem.validatePage(content.blocks || [], {
        postType: mockPostType,
        customFields: [],
        taxonomyTerms: [],
        allBlocks: content.blocks || [],
        region: 'south-africa'
      })

      if (!validationReport.isValid) {
        toast.error(`Validation failed: ${validationReport.errors.length} errors`)
        return
      }

      // Simulate saving to backend
      console.log('Saving content:', content)
      toast.success('Content saved successfully!')
    } catch (error) {
      console.error('Save failed:', error)
      toast.error('Failed to save content')
    }
  }

  const handlePublish = async (content: CMSContent) => {
    try {
      // Enhanced validation for publishing
      const validationReport = await validationSystem.validatePage(content.blocks || [], {
        postType: mockPostType,
        customFields: [],
        taxonomyTerms: [],
        allBlocks: content.blocks || [],
        region: 'south-africa'
      })

      if (validationReport.score < 80) {
        toast.error('Content quality score too low for publishing')
        return
      }

      // Simulate publishing
      console.log('Publishing content:', content)
      toast.success('Content published successfully!')
    } catch (error) {
      console.error('Publish failed:', error)
      toast.error('Failed to publish content')
    }
  }

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4" />
          <p>Initializing builder...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen">
      <UnifiedCMSBuilder
        initialPostType={mockPostType}
        onSave={handleSave}
        onPublish={handlePublish}
      />
    </div>
  )
}

// Example 2: Product Page Builder
export function ProductPageBuilder() {
  const [product, setProduct] = useState<CMSContent | null>(null)

  useEffect(() => {
    // Initialize with sample product
    setProduct({
      id: 'product-1',
      title: 'Premium South African Wine',
      slug: 'premium-sa-wine',
      content: 'A premium wine from the Western Cape region.',
      excerpt: 'Premium wine from South Africa',
      status: 'draft',
      postType: 'product',
      authorId: 'user-1',
      blocks: [],
      layouts: {
        lg: [],
        md: [],
        sm: [],
        xs: [],
        xxs: []
      },
      customFields: {
        price: 299.99,
        currency: 'ZAR',
        sku: 'WINE-001',
        stock: 50
      },
      createdAt: new Date(),
      updatedAt: new Date()
    })
  }, [])

  const productPostType: PostType = {
    id: '2',
    name: 'product',
    label: 'Product',
    labelPlural: 'Products',
    description: 'E-commerce products',
    isPublic: true,
    isHierarchical: false,
    hasArchive: true,
    supportsTitle: true,
    supportsContent: true,
    supportsExcerpt: true,
    supportsThumbnail: true,
    supportsComments: false,
    supportsRevisions: true,
    supportsPageBuilder: true,
    supportsCustomFields: true,
    taxonomies: ['product_category', 'product_tag'],
    customFields: [],
    templates: [],
    isSystem: false,
    isActive: true
  }

  const handleSaveProduct = async (content: CMSContent) => {
    try {
      // Add South African e-commerce specific validation
      const validationReport = await validationSystem.validatePage(content.blocks || [], {
        postType: productPostType,
        customFields: [],
        taxonomyTerms: [],
        allBlocks: content.blocks || [],
        region: 'south-africa',
        compliance: ['popi-act', 'consumer-protection-act']
      })

      console.log('Validation report:', validationReport)
      
      // Simulate API call
      setProduct(content)
      toast.success('Product saved successfully!')
    } catch (error) {
      console.error('Save failed:', error)
      toast.error('Failed to save product')
    }
  }

  if (!product) {
    return <div>Loading...</div>
  }

  return (
    <CMSPageBuilder
      postType={productPostType}
      content={product}
      mode="edit"
      onSave={handleSaveProduct}
      onPublish={handleSaveProduct}
    />
  )
}

// Example 3: Block Factory Demo
export function BlockFactoryDemo() {
  const [blocks, setBlocks] = useState<any[]>([])
  const [validationResults, setValidationResults] = useState<any[]>([])

  const mockPostType: PostType = {
    id: '1',
    name: 'page',
    label: 'Page',
    labelPlural: 'Pages',
    description: 'Static pages',
    isPublic: true,
    isHierarchical: true,
    hasArchive: false,
    supportsTitle: true,
    supportsContent: true,
    supportsExcerpt: true,
    supportsThumbnail: true,
    supportsComments: false,
    supportsRevisions: true,
    supportsPageBuilder: true,
    supportsCustomFields: true,
    taxonomies: [],
    customFields: [],
    templates: [],
    isSystem: false,
    isActive: true
  }

  const createBlock = (blockType: string) => {
    try {
      const block = builderUtils.createBlock(blockType, mockPostType, {
        customFields: [],
        taxonomies: []
      })

      setBlocks(prev => [...prev, block])
      toast.success(`${blockType} block created!`)
    } catch (error) {
      console.error('Block creation failed:', error)
      toast.error(`Failed to create ${blockType} block`)
    }
  }

  const validateBlocks = async () => {
    try {
      const results = []
      
      for (const block of blocks) {
        const validation = await validationSystem.validateBlock(block, {
          postType: mockPostType,
          customFields: [],
          taxonomyTerms: [],
          allBlocks: blocks,
          region: 'south-africa'
        })
        
        results.push({
          blockId: block.id,
          blockType: block.type,
          validation
        })
      }
      
      setValidationResults(results)
      toast.success('Validation completed!')
    } catch (error) {
      console.error('Validation failed:', error)
      toast.error('Validation failed')
    }
  }

  const clearBlocks = () => {
    setBlocks([])
    setValidationResults([])
    toast.success('Blocks cleared!')
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Block Factory Demo</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button onClick={() => createBlock('hero')}>
              Create Hero Block
            </Button>
            <Button onClick={() => createBlock('text')}>
              Create Text Block
            </Button>
            <Button onClick={() => createBlock('sa-payment-methods')}>
              Create SA Payment Methods
            </Button>
            <Button onClick={() => createBlock('sa-price-display')}>
              Create SA Price Display
            </Button>
            <Button onClick={() => createBlock('sa-shipping-calculator')}>
              Create SA Shipping Calculator
            </Button>
          </div>

          <div className="flex space-x-2">
            <Button onClick={validateBlocks} variant="outline">
              Validate All Blocks
            </Button>
            <Button onClick={clearBlocks} variant="outline">
              Clear All Blocks
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Created Blocks */}
            <div>
              <h3 className="font-medium mb-3">Created Blocks ({blocks.length})</h3>
              <div className="space-y-2">
                {blocks.map((block, index) => (
                  <div key={block.id} className="p-3 border rounded">
                    <div className="flex items-center justify-between">
                      <Badge variant="outline">{block.type}</Badge>
                      {block.aiGenerated && (
                        <Badge variant="secondary">
                          <Zap className="h-3 w-3 mr-1" />
                          AI
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      ID: {block.id.substring(0, 8)}...
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Validation Results */}
            <div>
              <h3 className="font-medium mb-3">Validation Results</h3>
              <div className="space-y-2">
                {validationResults.map((result, index) => (
                  <div key={index} className="p-3 border rounded">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{result.blockType}</span>
                      {result.validation.isValid ? (
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Valid
                        </Badge>
                      ) : (
                        <Badge variant="destructive">
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          Invalid
                        </Badge>
                      )}
                    </div>
                    {result.validation.errors.length > 0 && (
                      <div className="text-sm text-red-600 mt-1">
                        {result.validation.errors.length} error(s)
                      </div>
                    )}
                    {result.validation.warnings.length > 0 && (
                      <div className="text-sm text-yellow-600 mt-1">
                        {result.validation.warnings.length} warning(s)
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {blocks.length === 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            No blocks created yet. Click the buttons above to create some blocks and see them in action!
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}

// Export all examples
export default {
  BasicPageBuilder,
  ProductPageBuilder,
  BlockFactoryDemo
}
