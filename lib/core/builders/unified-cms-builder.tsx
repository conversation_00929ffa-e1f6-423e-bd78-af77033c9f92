// Unified CMS Builder Component
// Enterprise-grade page builder with full CMS integration

'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { Responsive, WidthProvider, Layout } from 'react-grid-layout'
import { PostType, CustomField, TaxonomyTerm, CMSContent } from '@/lib/cms/types'
import { PostTypeService } from '@/lib/cms/services/post-type-service'
import { CustomFieldService } from '@/lib/cms/services/custom-field-service'
import { TaxonomyService } from '@/lib/cms/services/taxonomy-service'

// Service methods are now implemented in the actual services
import { CMSBuilderProvider, useCMSBuilder } from './cms-builder-context'
import { CMSBlockFactory } from './block-factory'
import { createEnhancedBlock } from './block-hocs'
import { BlockRenderer } from '@/lib/page-builder/components/block-renderer'
import { AIBlockGenerator } from '@/lib/ai-block-generator/components/AIBlockGenerator'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Grid,
  Layout as LayoutIcon,
  Plus,
  Settings,
  Eye,
  EyeOff,
  Undo,
  Redo,
  Save,
  Smartphone,
  Tablet,
  Monitor,
  Tv,
  Wand2,
  Database,
  Tags,
  FileText,
  Layers,
  History,
  Zap
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

// Make ResponsiveGridLayout responsive
const ResponsiveGridLayout = WidthProvider(Responsive)

interface UnifiedCMSBuilderProps {
  className?: string
  initialPostType?: PostType
  initialContent?: CMSContent
  onSave?: (content: CMSContent) => Promise<void>
  onPublish?: (content: CMSContent) => Promise<void>
}

export function UnifiedCMSBuilder({
  className,
  initialPostType,
  initialContent,
  onSave,
  onPublish
}: UnifiedCMSBuilderProps) {
  return (
    <CMSBuilderProvider 
      initialPostType={initialPostType}
      initialContent={initialContent}
    >
      <UnifiedCMSBuilderContent 
        className={className}
        onSave={onSave}
        onPublish={onPublish}
      />
    </CMSBuilderProvider>
  )
}

function UnifiedCMSBuilderContent({
  className,
  onSave,
  onPublish
}: {
  className?: string
  onSave?: (content: CMSContent) => Promise<void>
  onPublish?: (content: CMSContent) => Promise<void>
}) {
  const {
    state,
    setPostType,
    setContent,
    setCustomFields,
    setTaxonomyTerms,
    addBlock,
    updateBlock,
    deleteBlock,
    selectBlock,
    duplicateBlock,
    updateLayouts,
    setPreviewMode,
    setDevicePreview,
    undo,
    redo,
    canUndo,
    canRedo,
    saveState,
    generateAIBlock
  } = useCMSBuilder()

  // Local state
  const [availablePostTypes, setAvailablePostTypes] = useState<PostType[]>([])
  const [availableBlocks, setAvailableBlocks] = useState<any[]>([])
  const [activeTab, setActiveTab] = useState<'blocks' | 'ai' | 'content' | 'settings'>('blocks')
  const [isSaving, setIsSaving] = useState(false)

  // Load available post types
  useEffect(() => {
    const loadPostTypes = async () => {
      try {
        const postTypes = await PostTypeService.getPostTypes()
        setAvailablePostTypes(postTypes)
        
        // Set default post type if none selected
        if (!state.currentPostType && postTypes.length > 0) {
          setPostType(postTypes[0])
        }
      } catch (error) {
        console.error('Failed to load post types:', error)
        toast.error('Failed to load post types')
      }
    }

    loadPostTypes()
  }, [state.currentPostType, setPostType])

  // Load custom fields and taxonomies when post type changes
  useEffect(() => {
    const loadPostTypeData = async () => {
      if (!state.currentPostType) return

      try {
        // Load custom fields
        const customFields = await CustomFieldService.getFieldsForPostType(state.currentPostType.name)
        setCustomFields(customFields)

        // Load taxonomies
        const taxonomies = await TaxonomyService.getTaxonomiesForPostType(state.currentPostType.name)
        const allTerms: TaxonomyTerm[] = []
        for (const taxonomy of taxonomies) {
          if (taxonomy.terms) {
            allTerms.push(...taxonomy.terms)
          }
        }
        setTaxonomyTerms(allTerms)

        // Update available blocks
        const factory = CMSBlockFactory.getInstance()
        const blocks = factory.getAvailableBlocks(state.currentPostType)
        setAvailableBlocks(blocks)
      } catch (error) {
        console.error('Failed to load post type data:', error)
        toast.error('Failed to load post type data')
      }
    }

    loadPostTypeData()
  }, [state.currentPostType, setCustomFields, setTaxonomyTerms])

  // Grid configuration
  const gridConfig = useMemo(() => ({
    breakpoints: { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 },
    cols: { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 },
    rowHeight: 100,
    margin: [16, 16] as [number, number],
    containerPadding: [0, 0] as [number, number]
  }), [])

  // Convert blocks to grid layout
  const gridBlocks = useMemo(() => {
    return state.blocks.map((block, index) => ({
      ...block,
      layout: {
        i: block.id,
        x: (index % 4) * 3,
        y: Math.floor(index / 4) * 2,
        w: 3,
        h: 2,
        minW: 1,
        minH: 1,
        maxW: 12,
        maxH: 10,
        static: state.isPreviewMode,
        isDraggable: !state.isPreviewMode,
        isResizable: !state.isPreviewMode
      }
    }))
  }, [state.blocks, state.isPreviewMode])

  // Handle layout changes
  const handleLayoutChange = (layout: Layout[], layouts: any) => {
    if (state.isDragging) return

    const newLayouts = {
      lg: layout,
      md: layout.map(item => ({ ...item, w: Math.min(item.w, 10) })),
      sm: layout.map(item => ({ ...item, w: Math.min(item.w, 6) })),
      xs: layout.map(item => ({ ...item, w: Math.min(item.w, 4) })),
      xxs: layout.map(item => ({ ...item, w: Math.min(item.w, 2) }))
    }

    updateLayouts(newLayouts)
  }

  // Handle block addition
  const handleAddBlock = async (blockType: string) => {
    try {
      await addBlock(blockType)
      toast.success(`${blockType} block added`)
    } catch (error) {
      console.error('Failed to add block:', error)
      toast.error('Failed to add block')
    }
  }

  // Handle AI block generation
  const handleAIGeneration = async (prompt: string, blockType?: string) => {
    if (!state.currentPostType) {
      toast.error('Please select a post type first')
      return
    }

    try {
      await generateAIBlock({
        prompt,
        blockType,
        postType: state.currentPostType.name,
        context: {
          customFields: state.customFields,
          taxonomies: state.taxonomyTerms
        }
      })
      toast.success('AI blocks generated successfully')
    } catch (error) {
      console.error('AI generation failed:', error)
      toast.error('AI generation failed')
    }
  }

  // Handle save
  const handleSave = async () => {
    if (!state.currentContent || !onSave) return

    setIsSaving(true)
    try {
      const updatedContent: CMSContent = {
        ...state.currentContent,
        blocks: state.blocks,
        layouts: state.layouts,
        updatedAt: new Date()
      }

      await onSave(updatedContent)
      await saveState()
      toast.success('Content saved successfully')
    } catch (error) {
      console.error('Save failed:', error)
      toast.error('Failed to save content')
    } finally {
      setIsSaving(false)
    }
  }

  // Enhanced Block Renderer
  const EnhancedBlockRenderer = createEnhancedBlock(BlockRenderer)

  return (
    <div className={cn('unified-cms-builder h-screen flex flex-col', className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-background">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Grid className="h-5 w-5 text-primary" />
            <h1 className="text-lg font-semibold">CMS Builder</h1>
          </div>

          {/* Post Type Selector */}
          <Select
            value={state.currentPostType?.name || ''}
            onValueChange={(value) => {
              const postType = availablePostTypes.find(pt => pt.name === value)
              if (postType) setPostType(postType)
            }}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Select post type" />
            </SelectTrigger>
            <SelectContent>
              {availablePostTypes.map((postType) => (
                <SelectItem key={postType.id} value={postType.name}>
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4" />
                    <span>{postType.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Badge variant="outline">
            {state.blocks.length} block{state.blocks.length !== 1 ? 's' : ''}
          </Badge>
        </div>

        <div className="flex items-center space-x-2">
          {/* Device Preview */}
          <div className="flex items-center space-x-1 border rounded-md p-1">
            <Button
              variant={state.devicePreview === 'mobile' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setDevicePreview('mobile')}
              className="h-8 w-8 p-0"
            >
              <Smartphone className="h-4 w-4" />
            </Button>
            <Button
              variant={state.devicePreview === 'tablet' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setDevicePreview('tablet')}
              className="h-8 w-8 p-0"
            >
              <Tablet className="h-4 w-4" />
            </Button>
            <Button
              variant={state.devicePreview === 'desktop' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setDevicePreview('desktop')}
              className="h-8 w-8 p-0"
            >
              <Monitor className="h-4 w-4" />
            </Button>
            <Button
              variant={state.devicePreview === 'large' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setDevicePreview('large')}
              className="h-8 w-8 p-0"
            >
              <Tv className="h-4 w-4" />
            </Button>
          </div>

          {/* History Controls */}
          <div className="flex items-center space-x-1">
            <Button
              variant="outline"
              size="sm"
              onClick={undo}
              disabled={!canUndo}
              className="h-8 w-8 p-0"
            >
              <Undo className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={redo}
              disabled={!canRedo}
              className="h-8 w-8 p-0"
            >
              <Redo className="h-4 w-4" />
            </Button>
          </div>

          {/* Preview Toggle */}
          <Button
            variant={state.isPreviewMode ? 'default' : 'outline'}
            size="sm"
            onClick={() => setPreviewMode(!state.isPreviewMode)}
          >
            {state.isPreviewMode ? (
              <>
                <EyeOff className="h-4 w-4 mr-2" />
                Edit
              </>
            ) : (
              <>
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </>
            )}
          </Button>

          {/* Save Button */}
          <Button
            onClick={handleSave}
            disabled={isSaving || !state.hasUnsavedChanges}
            className="min-w-20"
          >
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Saving
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        {!state.isPreviewMode && (
          <div className="w-80 border-r bg-background flex flex-col">
            <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="flex-1">
              <TabsList className="grid w-full grid-cols-4 m-4">
                <TabsTrigger value="blocks" className="text-xs">
                  <Layers className="h-4 w-4 mr-1" />
                  Blocks
                </TabsTrigger>
                <TabsTrigger value="ai" className="text-xs">
                  <Zap className="h-4 w-4 mr-1" />
                  AI
                </TabsTrigger>
                <TabsTrigger value="content" className="text-xs">
                  <Database className="h-4 w-4 mr-1" />
                  Content
                </TabsTrigger>
                <TabsTrigger value="settings" className="text-xs">
                  <Settings className="h-4 w-4 mr-1" />
                  Settings
                </TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-hidden">
                <TabsContent value="blocks" className="h-full m-0">
                  <ScrollArea className="h-full px-4">
                    <div className="space-y-4 pb-4">
                      <h3 className="font-medium">Available Blocks</h3>
                      <div className="grid grid-cols-2 gap-2">
                        {availableBlocks.map((blockDef) => (
                          <Button
                            key={blockDef.type}
                            variant="outline"
                            className="h-auto p-3 flex flex-col items-center space-y-2"
                            onClick={() => handleAddBlock(blockDef.type)}
                          >
                            <div className="text-2xl">{blockDef.icon}</div>
                            <span className="text-xs text-center">{blockDef.name}</span>
                          </Button>
                        ))}
                      </div>
                    </div>
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="ai" className="h-full m-0">
                  <div className="p-4">
                    <AIBlockGenerator
                      onBlockGenerated={(block) => {
                        console.log('AI block generated:', block)
                      }}
                      onLayoutGenerated={(layout) => {
                        console.log('AI layout generated:', layout)
                      }}
                    />
                  </div>
                </TabsContent>

                <TabsContent value="content" className="h-full m-0">
                  <ScrollArea className="h-full px-4">
                    <div className="space-y-4 pb-4">
                      <div>
                        <h3 className="font-medium mb-2">Custom Fields</h3>
                        {state.customFields.length > 0 ? (
                          <div className="space-y-2">
                            {state.customFields.map((field) => (
                              <div key={field.id} className="p-2 border rounded text-sm">
                                <div className="font-medium">{field.label}</div>
                                <div className="text-muted-foreground">{field.type}</div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-sm text-muted-foreground">No custom fields available</p>
                        )}
                      </div>

                      <Separator />

                      <div>
                        <h3 className="font-medium mb-2">Taxonomies</h3>
                        {state.taxonomyTerms.length > 0 ? (
                          <div className="space-y-2">
                            {state.taxonomyTerms.map((term) => (
                              <Badge key={term.id} variant="outline">
                                <Tags className="h-3 w-3 mr-1" />
                                {term.name}
                              </Badge>
                            ))}
                          </div>
                        ) : (
                          <p className="text-sm text-muted-foreground">No taxonomy terms available</p>
                        )}
                      </div>
                    </div>
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="settings" className="h-full m-0">
                  <ScrollArea className="h-full px-4">
                    <div className="space-y-4 pb-4">
                      <h3 className="font-medium">Builder Settings</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Post Type:</span>
                          <span className="font-medium">{state.currentPostType?.label || 'None'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Blocks:</span>
                          <span className="font-medium">{state.blocks.length}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Device:</span>
                          <span className="font-medium capitalize">{state.devicePreview}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Mode:</span>
                          <span className="font-medium">{state.isPreviewMode ? 'Preview' : 'Edit'}</span>
                        </div>
                      </div>
                    </div>
                  </ScrollArea>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        )}

        {/* Canvas */}
        <div className="flex-1 overflow-auto bg-muted/30">
          <div className="p-4">
            {gridBlocks.length === 0 ? (
              <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center">
                  <Grid className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Start Building</h3>
                  <p className="text-muted-foreground mb-4">
                    Add blocks from the sidebar or use AI to generate content
                  </p>
                  {!state.isPreviewMode && (
                    <div className="flex items-center justify-center space-x-2">
                      <Button onClick={() => handleAddBlock('hero')}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Hero Block
                      </Button>
                      <Button variant="outline" onClick={() => setActiveTab('ai')}>
                        <Wand2 className="h-4 w-4 mr-2" />
                        Use AI
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <ResponsiveGridLayout
                className="layout"
                layouts={state.layouts}
                breakpoints={gridConfig.breakpoints}
                cols={gridConfig.cols}
                rowHeight={gridConfig.rowHeight}
                onLayoutChange={handleLayoutChange}
                isDraggable={!state.isPreviewMode}
                isResizable={!state.isPreviewMode}
                margin={gridConfig.margin}
                containerPadding={gridConfig.containerPadding}
                useCSSTransforms={true}
                preventCollision={false}
                compactType="vertical"
              >
                {gridBlocks.map((block) => (
                  <div key={block.id} className="grid-item">
                    <Card className={cn(
                      'h-full relative group cursor-pointer transition-all',
                      state.selectedBlockId === block.id && 'ring-2 ring-primary',
                      !state.isPreviewMode && 'hover:shadow-md'
                    )}>
                      <CardContent className="p-4 h-full">
                        <EnhancedBlockRenderer
                          block={block}
                          postType={state.currentPostType!}
                          permissions={state.permissions}
                          onUpdate={(updatedBlock) => updateBlock(block.id, updatedBlock)}
                          onDelete={() => deleteBlock(block.id)}
                          isPreviewMode={state.isPreviewMode}
                        />
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </ResponsiveGridLayout>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
