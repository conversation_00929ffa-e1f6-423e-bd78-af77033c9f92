// Comprehensive Test Suite for CMS Builders
// Enterprise-grade testing with React Testing Library and Jest

import React from 'react'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { jest } from '@jest/globals'

// Import components to test
import { UnifiedCMSBuilder } from '../unified-cms-builder'
import { CMSPageBuilder } from '../cms-page-builder'
import { CMSLayoutBuilder } from '../cms-layout-builder'
import { AdvancedGridBuilder } from '../advanced-grid-builder'
import { CMSBuilderProvider, useCMSBuilder } from '../cms-builder-context'
import { CMSBlockFactory } from '../block-factory'
import { validationSystem } from '../validation-system'
import { aiBuilderService } from '../ai-integration'

// Mock dependencies
jest.mock('@/lib/cms/services/post-type-service')
jest.mock('@/lib/cms/services/custom-field-service')
jest.mock('@/lib/cms/services/taxonomy-service')
jest.mock('@/lib/cms/plugins/hook-system')
jest.mock('@/lib/page-builder/components/block-renderer')
jest.mock('@/lib/ai-block-generator/components/AIBlockGenerator')

// Test data
const mockPostType = {
  id: '1',
  name: 'product',
  label: 'Product',
  labelPlural: 'Products',
  description: 'E-commerce product',
  isPublic: true,
  isHierarchical: false,
  hasArchive: true,
  supportsTitle: true,
  supportsContent: true,
  supportsExcerpt: true,
  supportsThumbnail: true,
  supportsComments: false,
  supportsRevisions: true,
  supportsPageBuilder: true,
  supportsCustomFields: true,
  taxonomies: ['product_category', 'product_tag'],
  customFields: [],
  templates: ['single-product.php'],
  isSystem: false,
  isActive: true
}

const mockCustomFields = [
  {
    id: '1',
    groupId: '1',
    label: 'Price',
    name: 'price',
    type: 'number',
    required: true,
    defaultValue: '0'
  },
  {
    id: '2',
    groupId: '1',
    label: 'Product Image',
    name: 'product_image',
    type: 'image',
    required: false
  }
]

const mockTaxonomyTerms = [
  {
    id: '1',
    name: 'Electronics',
    slug: 'electronics',
    taxonomy: { id: '1', name: 'product_category', label: 'Product Category' }
  },
  {
    id: '2',
    name: 'Featured',
    slug: 'featured',
    taxonomy: { id: '2', name: 'product_tag', label: 'Product Tag' }
  }
]

const mockContent = {
  id: '1',
  title: 'Test Product',
  slug: 'test-product',
  content: 'Test product description',
  excerpt: 'Test excerpt',
  status: 'draft' as const,
  postType: 'product',
  authorId: '1',
  blocks: [],
  layouts: {
    lg: [],
    md: [],
    sm: [],
    xs: [],
    xxs: []
  },
  createdAt: new Date(),
  updatedAt: new Date()
}

// Test utilities
const renderWithProvider = (component: React.ReactElement, props = {}) => {
  return render(
    <CMSBuilderProvider 
      initialPostType={mockPostType}
      initialContent={mockContent}
      {...props}
    >
      {component}
    </CMSBuilderProvider>
  )
}

describe('CMSBuilderProvider', () => {
  test('provides initial state correctly', () => {
    const TestComponent = () => {
      const { state } = useCMSBuilder()
      return (
        <div>
          <span data-testid="post-type">{state.currentPostType?.name}</span>
          <span data-testid="content-title">{state.currentContent?.title}</span>
        </div>
      )
    }

    renderWithProvider(<TestComponent />)

    expect(screen.getByTestId('post-type')).toHaveTextContent('product')
    expect(screen.getByTestId('content-title')).toHaveTextContent('Test Product')
  })

  test('handles block operations correctly', async () => {
    const TestComponent = () => {
      const { state, addBlock, updateBlock, deleteBlock } = useCMSBuilder()
      
      return (
        <div>
          <span data-testid="block-count">{state.blocks.length}</span>
          <button 
            data-testid="add-block"
            onClick={() => addBlock('text')}
          >
            Add Block
          </button>
          {state.blocks.map((block, index) => (
            <div key={block.id} data-testid={`block-${index}`}>
              <span>{block.type}</span>
              <button 
                onClick={() => updateBlock(block.id, { content: { ...block.content, updated: true } })}
                data-testid={`update-${index}`}
              >
                Update
              </button>
              <button 
                onClick={() => deleteBlock(block.id)}
                data-testid={`delete-${index}`}
              >
                Delete
              </button>
            </div>
          ))}
        </div>
      )
    }

    renderWithProvider(<TestComponent />)

    // Initially no blocks
    expect(screen.getByTestId('block-count')).toHaveTextContent('0')

    // Add a block
    await act(async () => {
      fireEvent.click(screen.getByTestId('add-block'))
    })

    await waitFor(() => {
      expect(screen.getByTestId('block-count')).toHaveTextContent('1')
    })

    // Check block was added
    expect(screen.getByTestId('block-0')).toBeInTheDocument()
    expect(screen.getByTestId('block-0')).toHaveTextContent('text')

    // Update block
    await act(async () => {
      fireEvent.click(screen.getByTestId('update-0'))
    })

    // Delete block
    await act(async () => {
      fireEvent.click(screen.getByTestId('delete-0'))
    })

    await waitFor(() => {
      expect(screen.getByTestId('block-count')).toHaveTextContent('0')
    })
  })
})

describe('UnifiedCMSBuilder', () => {
  test('renders with post type selector', () => {
    render(<UnifiedCMSBuilder initialPostType={mockPostType} />)

    expect(screen.getByText('CMS Builder')).toBeInTheDocument()
    expect(screen.getByText('Product')).toBeInTheDocument()
  })

  test('handles device preview switching', async () => {
    render(<UnifiedCMSBuilder initialPostType={mockPostType} />)

    const mobileButton = screen.getByRole('button', { name: /mobile/i })
    const tabletButton = screen.getByRole('button', { name: /tablet/i })

    await userEvent.click(mobileButton)
    // Check if mobile preview is active (would need to check state or visual indicators)

    await userEvent.click(tabletButton)
    // Check if tablet preview is active
  })

  test('handles preview mode toggle', async () => {
    render(<UnifiedCMSBuilder initialPostType={mockPostType} />)

    const previewButton = screen.getByRole('button', { name: /preview/i })
    
    await userEvent.click(previewButton)
    
    // Should show "Edit" button when in preview mode
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /edit/i })).toBeInTheDocument()
    })
  })
})

describe('CMSPageBuilder', () => {
  test('renders page creation interface', () => {
    render(
      <CMSPageBuilder 
        postType={mockPostType}
        mode="create"
      />
    )

    expect(screen.getByText('Create Product')).toBeInTheDocument()
    expect(screen.getByLabelText(/title/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/slug/i)).toBeInTheDocument()
  })

  test('handles page title and slug input', async () => {
    render(
      <CMSPageBuilder 
        postType={mockPostType}
        mode="create"
      />
    )

    const titleInput = screen.getByLabelText(/title/i)
    const slugInput = screen.getByLabelText(/slug/i)

    await userEvent.type(titleInput, 'New Product')
    
    // Slug should auto-generate in create mode
    await waitFor(() => {
      expect(slugInput).toHaveValue('new-product')
    })
  })

  test('renders custom fields when available', () => {
    const TestPageBuilder = () => (
      <CMSBuilderProvider 
        initialPostType={mockPostType}
        initialContent={mockContent}
      >
        <CMSPageBuilder 
          postType={mockPostType}
          mode="edit"
        />
      </CMSBuilderProvider>
    )

    // Mock custom fields
    jest.spyOn(require('@/lib/cms/services/custom-field-service'), 'getFieldsForPostType')
      .mockResolvedValue(mockCustomFields)

    render(<TestPageBuilder />)

    // Custom fields should be rendered in sidebar
    expect(screen.getByText('Custom Fields')).toBeInTheDocument()
  })
})

describe('CMSLayoutBuilder', () => {
  test('renders layout builder interface', () => {
    render(<CMSLayoutBuilder templateType="page" />)

    expect(screen.getByText('Layout Builder')).toBeInTheDocument()
    expect(screen.getByText('Add Sections')).toBeInTheDocument()
  })

  test('handles section addition', async () => {
    render(<CMSLayoutBuilder templateType="page" />)

    const addHeaderButton = screen.getByRole('button', { name: /header/i })
    
    await userEvent.click(addHeaderButton)
    
    // Should add a header section
    await waitFor(() => {
      expect(screen.getByText('New header')).toBeInTheDocument()
    })
  })
})

describe('AdvancedGridBuilder', () => {
  test('renders grid builder with empty state', () => {
    render(<AdvancedGridBuilder />)

    expect(screen.getByText('Advanced Grid Builder')).toBeInTheDocument()
    expect(screen.getByText('Empty Grid')).toBeInTheDocument()
  })

  test('handles grid settings changes', async () => {
    render(<AdvancedGridBuilder />)

    // Test column adjustment (would need to find the slider)
    const columnSlider = screen.getByRole('slider', { name: /columns/i })
    
    if (columnSlider) {
      fireEvent.change(columnSlider, { target: { value: '8' } })
      
      await waitFor(() => {
        expect(screen.getByText('8 columns')).toBeInTheDocument()
      })
    }
  })
})

describe('CMSBlockFactory', () => {
  let factory: CMSBlockFactory

  beforeEach(() => {
    factory = CMSBlockFactory.getInstance()
  })

  test('creates blocks with correct structure', () => {
    const block = factory.createBlock('text', mockPostType)

    expect(block).toHaveProperty('id')
    expect(block).toHaveProperty('type', 'text')
    expect(block).toHaveProperty('postTypeContext', 'product')
    expect(block).toHaveProperty('customFieldBindings')
    expect(block).toHaveProperty('taxonomyBindings')
  })

  test('validates blocks correctly', () => {
    const block = factory.createBlock('text', mockPostType)
    const validation = factory.validateBlock(block, mockPostType)

    expect(validation).toHaveProperty('isValid')
    expect(validation).toHaveProperty('errors')
    expect(validation).toHaveProperty('warnings')
  })

  test('gets available blocks for post type', () => {
    const availableBlocks = factory.getAvailableBlocks(mockPostType)

    expect(Array.isArray(availableBlocks)).toBe(true)
    expect(availableBlocks.length).toBeGreaterThan(0)
  })
})

describe('Validation System', () => {
  test('validates blocks with required content', async () => {
    const factory = CMSBlockFactory.getInstance()
    const block = factory.createBlock('text', mockPostType)
    
    // Remove required content
    block.content = {}

    const result = await validationSystem.validateBlock(block, {
      postType: mockPostType,
      customFields: mockCustomFields,
      taxonomyTerms: mockTaxonomyTerms,
      allBlocks: [block]
    })

    expect(result.isValid).toBe(false)
    expect(result.errors.length).toBeGreaterThan(0)
  })

  test('validates South African compliance', async () => {
    const factory = CMSBlockFactory.getInstance()
    const block = factory.createBlock('sa-price-display', mockPostType)
    
    block.content = {
      price: 100,
      priceText: '100' // Missing currency indicator
    }

    const result = await validationSystem.validateBlock(block, {
      postType: mockPostType,
      customFields: mockCustomFields,
      taxonomyTerms: mockTaxonomyTerms,
      allBlocks: [block],
      region: 'south-africa'
    })

    expect(result.warnings.length).toBeGreaterThan(0)
    expect(result.warnings.some(w => w.message.includes('ZAR'))).toBe(true)
  })
})

describe('AI Integration', () => {
  beforeEach(() => {
    // Mock fetch for AI API calls
    global.fetch = jest.fn()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  test('generates blocks with AI', async () => {
    const mockResponse = {
      content: { title: 'AI Generated Title' },
      config: {},
      blockType: 'hero'
    }

    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      body: {
        getReader: () => ({
          read: jest.fn()
            .mockResolvedValueOnce({
              done: false,
              value: new TextEncoder().encode(JSON.stringify(mockResponse))
            })
            .mockResolvedValueOnce({ done: true }),
          releaseLock: jest.fn()
        })
      }
    })

    const block = await aiBuilderService.generateBlock({
      prompt: 'Create a hero section',
      blockType: 'hero',
      postType: 'product'
    })

    expect(block.type).toBe('hero')
    expect(block.content.title).toBe('AI Generated Title')
    expect(block.aiGenerated).toBe(true)
  })

  test('handles AI generation errors gracefully', async () => {
    ;(global.fetch as jest.Mock).mockRejectedValueOnce(new Error('API Error'))

    await expect(
      aiBuilderService.generateBlock({
        prompt: 'Create a hero section',
        blockType: 'hero',
        postType: 'product'
      })
    ).rejects.toThrow('Failed to generate block')
  })
})

describe('South African E-commerce Features', () => {
  test('creates SA payment methods block correctly', () => {
    const factory = CMSBlockFactory.getInstance()
    const block = factory.createBlock('sa-payment-methods', mockPostType)

    expect(block.type).toBe('sa-payment-methods')
    expect(block.config.currency).toBe('ZAR')
    expect(block.config.methods).toContain('payfast')
    expect(block.config.methods).toContain('ozow')
  })

  test('validates ZAR currency formatting', async () => {
    const factory = CMSBlockFactory.getInstance()
    const block = factory.createBlock('sa-price-display', mockPostType)
    
    block.content = {
      price: 1500,
      currency: 'ZAR',
      showVAT: true
    }

    const result = await validationSystem.validateBlock(block, {
      postType: mockPostType,
      customFields: mockCustomFields,
      taxonomyTerms: mockTaxonomyTerms,
      allBlocks: [block],
      region: 'south-africa'
    })

    expect(result.isValid).toBe(true)
  })
})

describe('Responsive Design', () => {
  test('generates responsive layouts correctly', () => {
    const blocks = [
      { id: '1', type: 'hero', gridLayout: { w: 12, h: 4 } },
      { id: '2', type: 'text', gridLayout: { w: 6, h: 2 } }
    ]

    const layouts = require('../index').builderUtils.generateResponsiveLayouts(
      blocks,
      { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }
    )

    expect(layouts).toHaveProperty('lg')
    expect(layouts).toHaveProperty('md')
    expect(layouts).toHaveProperty('sm')
    expect(layouts).toHaveProperty('xs')
    expect(layouts).toHaveProperty('xxs')

    // Check that smaller breakpoints have constrained widths
    expect(layouts.sm[0].w).toBeLessThanOrEqual(6)
    expect(layouts.xs[0].w).toBeLessThanOrEqual(4)
  })
})

describe('Performance', () => {
  test('renders large number of blocks efficiently', () => {
    const manyBlocks = Array.from({ length: 100 }, (_, i) => ({
      id: `block-${i}`,
      type: 'text',
      content: { content: `Block ${i}` },
      config: {}
    }))

    const start = performance.now()
    
    render(
      <AdvancedGridBuilder 
        initialBlocks={manyBlocks}
      />
    )
    
    const end = performance.now()
    const renderTime = end - start

    // Should render within reasonable time (adjust threshold as needed)
    expect(renderTime).toBeLessThan(1000) // 1 second
  })
})

describe('Accessibility', () => {
  test('has proper ARIA labels and roles', () => {
    render(<UnifiedCMSBuilder initialPostType={mockPostType} />)

    // Check for proper button labels
    const buttons = screen.getAllByRole('button')
    buttons.forEach(button => {
      expect(button).toHaveAttribute('aria-label')
    })
  })

  test('supports keyboard navigation', async () => {
    render(<UnifiedCMSBuilder initialPostType={mockPostType} />)

    const firstButton = screen.getAllByRole('button')[0]
    firstButton.focus()

    expect(document.activeElement).toBe(firstButton)

    // Test tab navigation
    await userEvent.tab()
    expect(document.activeElement).not.toBe(firstButton)
  })
})
