// Integration Test for CMS Builder System
// Verify all components work together correctly

import { 
  CMSBlockFactory,
  validationSystem,
  aiBuilderService,
  registerSAEcommerceBlocks,
  builderUtils,
  BUILDER_CONSTANTS
} from '../index'

// Mock dependencies
jest.mock('@/lib/cms/plugins/hook-system', () => ({
  HookSystem: {
    doAction: jest.fn(),
    applyFilters: jest.fn((filter, value) => Promise.resolve(value))
  }
}))

jest.mock('@/lib/cms/services/post-type-service')
jest.mock('@/lib/cms/services/custom-field-service')
jest.mock('@/lib/cms/services/taxonomy-service')

// Mock fetch for AI integration
global.fetch = jest.fn()

describe('CMS Builder System Integration', () => {
  const mockPostType = {
    id: '1',
    name: 'product',
    label: 'Product',
    labelPlural: 'Products',
    description: 'E-commerce product',
    isPublic: true,
    isHierarchical: false,
    hasArchive: true,
    supportsTitle: true,
    supportsContent: true,
    supportsExcerpt: true,
    supportsThumbnail: true,
    supportsComments: false,
    supportsRevisions: true,
    supportsPageBuilder: true,
    supportsCustomFields: true,
    taxonomies: ['product_category', 'product_tag'],
    customFields: [],
    templates: ['single-product.php'],
    isSystem: false,
    isActive: true
  }

  beforeEach(() => {
    jest.clearAllMocks()
    // Register SA e-commerce blocks
    registerSAEcommerceBlocks()
  })

  describe('Block Factory Integration', () => {
    test('creates blocks successfully', () => {
      const factory = CMSBlockFactory.getInstance()
      
      const block = factory.createBlock('text', mockPostType)
      
      expect(block).toHaveProperty('id')
      expect(block).toHaveProperty('type', 'text')
      expect(block).toHaveProperty('postTypeContext', 'product')
      expect(block).toHaveProperty('customFieldBindings')
      expect(block).toHaveProperty('taxonomyBindings')
    })

    test('validates blocks correctly', () => {
      const factory = CMSBlockFactory.getInstance()
      const block = factory.createBlock('text', mockPostType)
      
      const validation = factory.validateBlock(block, mockPostType)
      
      expect(validation).toHaveProperty('isValid')
      expect(validation).toHaveProperty('errors')
      expect(validation).toHaveProperty('warnings')
    })

    test('gets available blocks for post type', () => {
      const factory = CMSBlockFactory.getInstance()
      const availableBlocks = factory.getAvailableBlocks(mockPostType)
      
      expect(Array.isArray(availableBlocks)).toBe(true)
      expect(availableBlocks.length).toBeGreaterThan(0)
    })
  })

  describe('South African E-commerce Integration', () => {
    test('creates SA payment methods block', () => {
      const factory = CMSBlockFactory.getInstance()
      const block = factory.createBlock('sa-payment-methods', mockPostType)
      
      expect(block.type).toBe('sa-payment-methods')
      expect(block.config.currency).toBe('ZAR')
      expect(block.config.methods).toContain('payfast')
      expect(block.config.methods).toContain('ozow')
    })

    test('creates SA price display block', () => {
      const factory = CMSBlockFactory.getInstance()
      const block = factory.createBlock('sa-price-display', mockPostType)
      
      expect(block.type).toBe('sa-price-display')
      expect(block.config.currency).toBe('ZAR')
      expect(block.config.showVAT).toBe(true)
      expect(block.config.vatRate).toBe(0.15)
    })

    test('creates SA shipping calculator block', () => {
      const factory = CMSBlockFactory.getInstance()
      const block = factory.createBlock('sa-shipping-calculator', mockPostType)
      
      expect(block.type).toBe('sa-shipping-calculator')
      expect(block.config.showFreeShippingThreshold).toBe(true)
      expect(block.config.freeShippingAmount).toBe(500)
    })
  })

  describe('Validation System Integration', () => {
    test('validates blocks with South African compliance', async () => {
      const factory = CMSBlockFactory.getInstance()
      const block = factory.createBlock('sa-price-display', mockPostType)
      
      block.content = {
        price: 100,
        priceText: '100' // Missing currency indicator
      }

      const result = await validationSystem.validateBlock(block, {
        postType: mockPostType,
        customFields: [],
        taxonomyTerms: [],
        allBlocks: [block],
        region: 'south-africa'
      })

      expect(result).toHaveProperty('isValid')
      expect(result).toHaveProperty('errors')
      expect(result).toHaveProperty('warnings')
    })

    test('validates page with multiple blocks', async () => {
      const factory = CMSBlockFactory.getInstance()
      const blocks = [
        factory.createBlock('hero', mockPostType),
        factory.createBlock('sa-payment-methods', mockPostType),
        factory.createBlock('sa-price-display', mockPostType)
      ]

      const report = await validationSystem.validatePage(blocks, {
        postType: mockPostType,
        customFields: [],
        taxonomyTerms: [],
        allBlocks: blocks,
        region: 'south-africa'
      })

      expect(report).toHaveProperty('isValid')
      expect(report).toHaveProperty('score')
      expect(report).toHaveProperty('categories')
      expect(report).toHaveProperty('suggestions')
    })
  })

  describe('AI Integration', () => {
    test('handles AI block generation', async () => {
      const mockResponse = {
        content: { title: 'AI Generated Title' },
        config: {},
        blockType: 'hero',
        confidence: 0.9
      }

      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        body: {
          getReader: () => ({
            read: jest.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(JSON.stringify(mockResponse))
              })
              .mockResolvedValueOnce({ done: true }),
            releaseLock: jest.fn()
          })
        }
      })

      const block = await aiBuilderService.generateBlock({
        prompt: 'Create a hero section for South African e-commerce',
        blockType: 'hero',
        postType: 'product'
      })

      expect(block.type).toBe('hero')
      expect(block.content.title).toBe('AI Generated Title')
      expect(block.aiGenerated).toBe(true)
      expect(block.aiMetadata?.confidence).toBe(0.9)
    })

    test('handles AI generation errors gracefully', async () => {
      ;(global.fetch as jest.Mock).mockRejectedValueOnce(new Error('API Error'))

      await expect(
        aiBuilderService.generateBlock({
          prompt: 'Create a hero section',
          blockType: 'hero',
          postType: 'product'
        })
      ).rejects.toThrow('Failed to generate block')
    })
  })

  describe('Builder Utils Integration', () => {
    test('generates responsive layouts correctly', () => {
      const blocks = [
        { id: '1', type: 'hero', gridLayout: { w: 12, h: 4 } },
        { id: '2', type: 'text', gridLayout: { w: 6, h: 2 } }
      ]

      const layouts = builderUtils.generateResponsiveLayouts(
        blocks,
        BUILDER_CONSTANTS.DEFAULT_GRID_SETTINGS.breakpoints
      )

      expect(layouts).toHaveProperty('lg')
      expect(layouts).toHaveProperty('md')
      expect(layouts).toHaveProperty('sm')
      expect(layouts).toHaveProperty('xs')
      expect(layouts).toHaveProperty('xxs')

      // Check that smaller breakpoints have constrained widths
      expect(layouts.sm[0].w).toBeLessThanOrEqual(6)
      expect(layouts.xs[0].w).toBeLessThanOrEqual(4)
    })

    test('converts blocks to content and back', () => {
      const factory = CMSBlockFactory.getInstance()
      const originalBlocks = [
        factory.createBlock('hero', mockPostType),
        factory.createBlock('text', mockPostType)
      ]

      const content = builderUtils.blocksToContent(originalBlocks)
      expect(Array.isArray(content)).toBe(true)
      expect(content.length).toBe(2)

      const convertedBlocks = builderUtils.contentToBlocks(content, mockPostType)
      expect(Array.isArray(convertedBlocks)).toBe(true)
      expect(convertedBlocks.length).toBe(2)
      expect(convertedBlocks[0]).toHaveProperty('type', 'hero')
      expect(convertedBlocks[1]).toHaveProperty('type', 'text')
    })
  })

  describe('Constants and Configuration', () => {
    test('has all required constants', () => {
      expect(BUILDER_CONSTANTS).toHaveProperty('DEFAULT_GRID_SETTINGS')
      expect(BUILDER_CONSTANTS).toHaveProperty('DEFAULT_BLOCK_TYPES')
      expect(BUILDER_CONSTANTS).toHaveProperty('SA_ECOMMERCE_BLOCKS')
      expect(BUILDER_CONSTANTS).toHaveProperty('DEVICE_BREAKPOINTS')
      expect(BUILDER_CONSTANTS).toHaveProperty('PERMISSION_LEVELS')
      expect(BUILDER_CONSTANTS).toHaveProperty('BUILDER_MODES')
    })

    test('has correct South African e-commerce blocks', () => {
      const saBlocks = BUILDER_CONSTANTS.SA_ECOMMERCE_BLOCKS
      
      expect(saBlocks).toContain('product-grid')
      expect(saBlocks).toContain('product-showcase')
      expect(saBlocks).toContain('price-display')
      expect(saBlocks).toContain('payment-methods')
      expect(saBlocks).toContain('shipping-calculator')
      expect(saBlocks).toContain('currency-converter')
    })

    test('has correct device breakpoints', () => {
      const breakpoints = BUILDER_CONSTANTS.DEVICE_BREAKPOINTS
      
      expect(breakpoints.mobile).toBe(480)
      expect(breakpoints.tablet).toBe(768)
      expect(breakpoints.desktop).toBe(1024)
      expect(breakpoints.large).toBe(1200)
    })
  })

  describe('Error Handling', () => {
    test('handles invalid block types gracefully', () => {
      const factory = CMSBlockFactory.getInstance()
      
      expect(() => {
        factory.createBlock('invalid-block-type', mockPostType)
      }).toThrow('Block type \'invalid-block-type\' not found')
    })

    test('handles validation errors gracefully', async () => {
      const factory = CMSBlockFactory.getInstance()
      const block = factory.createBlock('text', mockPostType)
      
      // Remove required content
      block.content = {}

      const result = await validationSystem.validateBlock(block, {
        postType: mockPostType,
        customFields: [],
        taxonomyTerms: [],
        allBlocks: [block]
      })

      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })
  })

  describe('Performance', () => {
    test('handles large number of blocks efficiently', () => {
      const factory = CMSBlockFactory.getInstance()
      const start = performance.now()
      
      const blocks = Array.from({ length: 100 }, () => 
        factory.createBlock('text', mockPostType)
      )
      
      const end = performance.now()
      const creationTime = end - start

      expect(blocks.length).toBe(100)
      expect(creationTime).toBeLessThan(1000) // Should create 100 blocks in under 1 second
    })
  })
})
