import { wordpressAuth, wooCommerceClient } from './wordpress'

// Types for authentication
export interface LoginCredentials {
  usernameOrEmail: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  first_name?: string
  last_name?: string
  username?: string
  billing?: any
  shipping?: any
}

export interface AuthUser {
  id: number
  username: string
  email: string
  first_name: string
  last_name: string
  display_name: string
  roles: string[]
  avatar_url?: string
}

export interface AuthCustomer {
  id: number
  billing: any
  shipping: any
  is_paying_customer: boolean
}

export interface AuthResponse {
  success: boolean
  data?: {
    token: string
    user: AuthUser
    customer?: AuthCustomer
  }
  error?: string
}

/**
 * Authentication service class
 */
export class AuthService {
  /**
   * Authenticate user with WordPress and get customer data from WooCommerce
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const { usernameOrEmail, password } = credentials

      // Authenticate with Word<PERSON>ress
      const authResponse = await wordpressAuth.login(usernameOrEmail, password)
      
      // Get user information
      const user = await wordpressAuth.getCurrentUser(authResponse.token)
      
      // Get customer information from WooCommerce
      let customer: AuthCustomer | undefined
      try {
        const wcCustomer = await wooCommerceClient.getCustomerByEmail(user.email)
        if (wcCustomer) {
          customer = {
            id: wcCustomer.id,
            billing: wcCustomer.billing,
            shipping: wcCustomer.shipping,
            is_paying_customer: wcCustomer.is_paying_customer,
          }
        }
      } catch (error) {
        console.warn('Customer not found in WooCommerce:', error)
      }

      const authUser: AuthUser = {
        id: user.id,
        username: user.username,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        display_name: user.name,
        roles: user.roles,
        avatar_url: user.avatar_urls['96'] || '',
      }

      return {
        success: true,
        data: {
          token: authResponse.token,
          user: authUser,
          customer,
        },
      }

    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Login failed',
      }
    }
  }

  /**
   * Register new user and customer
   */
  async register(registerData: RegisterData): Promise<AuthResponse> {
    try {
      const { email, password, first_name, last_name, username, billing, shipping } = registerData

      // Generate username if not provided
      const finalUsername = username || this.generateUsernameFromEmail(email)

      // Create WordPress user
      const user = await wordpressAuth.createUser({
        username: finalUsername,
        email,
        password,
        first_name: first_name || '',
        last_name: last_name || '',
      })

      // Create WooCommerce customer
      let customer: AuthCustomer | undefined
      try {
        const wcCustomer = await wooCommerceClient.createCustomer({
          email,
          first_name: first_name || '',
          last_name: last_name || '',
          username: finalUsername,
          billing: billing || {},
          shipping: shipping || {},
        })

        customer = {
          id: wcCustomer.id,
          billing: wcCustomer.billing,
          shipping: wcCustomer.shipping,
          is_paying_customer: wcCustomer.is_paying_customer,
        }
      } catch (error) {
        console.warn('Failed to create WooCommerce customer:', error)
      }

      // Login the user to get token
      const authResponse = await wordpressAuth.login(finalUsername, password)

      const authUser: AuthUser = {
        id: user.id,
        username: user.username,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        display_name: user.name,
        roles: user.roles,
        avatar_url: user.avatar_urls?.['96'] || '',
      }

      return {
        success: true,
        data: {
          token: authResponse.token,
          user: authUser,
          customer,
        },
      }

    } catch (error) {
      console.error('Registration error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Registration failed',
      }
    }
  }

  /**
   * Validate token
   */
  async validateToken(token: string): Promise<{ valid: boolean; user?: AuthUser }> {
    try {
      const isValid = await wordpressAuth.validateToken(token)
      
      if (!isValid) {
        return { valid: false }
      }

      // Get current user
      const user = await wordpressAuth.getCurrentUser(token)

      const authUser: AuthUser = {
        id: user.id,
        username: user.username,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        display_name: user.name,
        roles: user.roles,
        avatar_url: user.avatar_urls['96'] || '',
      }

      return { valid: true, user: authUser }

    } catch (error) {
      console.error('Token validation error:', error)
      return { valid: false }
    }
  }

  /**
   * Get current user and customer data
   */
  async getCurrentUserData(token: string): Promise<{
    user?: AuthUser
    customer?: AuthCustomer
  }> {
    try {
      // Get user information
      const user = await wordpressAuth.getCurrentUser(token)
      
      // Get customer information from WooCommerce
      let customer: AuthCustomer | undefined
      try {
        const wcCustomer = await wooCommerceClient.getCustomerByEmail(user.email)
        if (wcCustomer) {
          customer = {
            id: wcCustomer.id,
            billing: wcCustomer.billing,
            shipping: wcCustomer.shipping,
            is_paying_customer: wcCustomer.is_paying_customer,
          }
        }
      } catch (error) {
        console.warn('Customer not found in WooCommerce:', error)
      }

      const authUser: AuthUser = {
        id: user.id,
        username: user.username,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        display_name: user.name,
        roles: user.roles,
        avatar_url: user.avatar_urls['96'] || '',
      }

      return { user: authUser, customer }

    } catch (error) {
      console.error('Error getting current user data:', error)
      return {}
    }
  }

  /**
   * Update customer information
   */
  async updateCustomer(token: string, customerData: any): Promise<{ success: boolean; error?: string }> {
    try {
      // Get current user
      const user = await wordpressAuth.getCurrentUser(token)
      
      // Get customer from WooCommerce
      const customer = await wooCommerceClient.getCustomerByEmail(user.email)
      
      if (!customer) {
        return { success: false, error: 'Customer not found' }
      }

      // Update customer in WooCommerce
      await wooCommerceClient.updateCustomer(customer.id, customerData)

      return { success: true }

    } catch (error) {
      console.error('Error updating customer:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update customer',
      }
    }
  }

  /**
   * Logout user
   */
  async logout(token: string): Promise<void> {
    try {
      await wordpressAuth.logout(token)
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  /**
   * Check if user has specific role
   */
  hasRole(user: AuthUser, role: string): boolean {
    return user.roles.includes(role)
  }

  /**
   * Check if user is admin
   */
  isAdmin(user: AuthUser): boolean {
    return this.hasRole(user, 'administrator')
  }

  /**
   * Check if user is customer
   */
  isCustomer(user: AuthUser): boolean {
    return this.hasRole(user, 'customer')
  }

  /**
   * Generate username from email
   */
  private generateUsernameFromEmail(email: string): string {
    return email.split('@')[0].toLowerCase().replace(/[^a-z0-9]/g, '')
  }

  /**
   * Validate email format
   */
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * Validate password strength
   */
  validatePassword(password: string): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter')
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter')
    }

    if (!/[0-9]/.test(password)) {
      errors.push('Password must contain at least one number')
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }
}

// Export singleton instance
export const authService = new AuthService()

// Export utility functions
export const authUtils = {
  /**
   * Extract token from Authorization header
   */
  extractTokenFromHeader(authHeader: string): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null
    }
    return authHeader.substring(7)
  },

  /**
   * Format user display name
   */
  formatDisplayName(user: AuthUser): string {
    if (user.display_name) {
      return user.display_name
    }
    
    if (user.first_name && user.last_name) {
      return `${user.first_name} ${user.last_name}`
    }
    
    if (user.first_name) {
      return user.first_name
    }
    
    return user.username
  },

  /**
   * Get user initials for avatar
   */
  getUserInitials(user: AuthUser): string {
    if (user.first_name && user.last_name) {
      return `${user.first_name[0]}${user.last_name[0]}`.toUpperCase()
    }
    
    if (user.first_name) {
      return user.first_name[0].toUpperCase()
    }
    
    return user.username[0].toUpperCase()
  },
}
