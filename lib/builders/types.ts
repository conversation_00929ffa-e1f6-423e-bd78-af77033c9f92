// Independent Builder System Types
// Complete type definitions for the new builder system

export interface BuilderBlock {
  id: string
  type: string
  name: string
  category: 'layout' | 'content' | 'media' | 'form' | 'ecommerce' | 'navigation' | 'social'
  icon: string
  description: string
  content: Record<string, any>
  settings: BuilderBlockSettings
  styles: BuilderBlockStyles
  responsive: ResponsiveSettings
  conditions: DisplayConditions
  animation: AnimationSettings
  position: {
    x: number
    y: number
    w: number
    h: number
  }
  parentId?: string
  children?: string[]
  isLocked: boolean
  isVisible: boolean
  createdAt: Date
  updatedAt: Date
}

export interface BuilderBlockSettings {
  className?: string
  customCss?: string
  customJs?: string
  attributes?: Record<string, string>
  seo?: {
    title?: string
    description?: string
    keywords?: string[]
  }
  accessibility?: {
    ariaLabel?: string
    ariaDescribedBy?: string
    role?: string
    tabIndex?: number
  }
}

export interface BuilderBlockStyles {
  background?: {
    color?: string
    image?: string
    gradient?: string
    size?: 'cover' | 'contain' | 'auto'
    position?: string
    repeat?: 'no-repeat' | 'repeat' | 'repeat-x' | 'repeat-y'
  }
  spacing?: {
    margin?: SpacingValue
    padding?: SpacingValue
  }
  border?: {
    width?: number
    style?: 'solid' | 'dashed' | 'dotted' | 'none'
    color?: string
    radius?: number
  }
  typography?: {
    fontFamily?: string
    fontSize?: number
    fontWeight?: number
    lineHeight?: number
    letterSpacing?: number
    textAlign?: 'left' | 'center' | 'right' | 'justify'
    textDecoration?: 'none' | 'underline' | 'line-through'
    color?: string
  }
  layout?: {
    display?: 'block' | 'flex' | 'grid' | 'inline' | 'inline-block'
    flexDirection?: 'row' | 'column'
    justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around'
    alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch'
    gap?: number
    gridColumns?: number
    gridRows?: number
  }
  effects?: {
    opacity?: number
    transform?: string
    filter?: string
    boxShadow?: string
    transition?: string
  }
}

export interface SpacingValue {
  top?: number
  right?: number
  bottom?: number
  left?: number
}

export interface ResponsiveSettings {
  desktop: DeviceSettings
  tablet: DeviceSettings
  mobile: DeviceSettings
}

export interface DeviceSettings {
  visible: boolean
  styles?: Partial<BuilderBlockStyles>
  position?: Partial<BuilderBlock['position']>
}

export interface DisplayConditions {
  userRole?: string[]
  dateRange?: {
    start?: Date
    end?: Date
  }
  location?: {
    countries?: string[]
    regions?: string[]
  }
  device?: ('desktop' | 'tablet' | 'mobile')[]
  customLogic?: string
}

export interface AnimationSettings {
  type: 'none' | 'fade' | 'slide' | 'scale' | 'rotate' | 'bounce' | 'custom'
  duration: number
  delay: number
  easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear'
  trigger: 'load' | 'scroll' | 'hover' | 'click'
  customKeyframes?: string
}

export interface BuilderPage {
  id: string
  title: string
  slug: string
  description?: string
  status: 'draft' | 'published' | 'archived'
  type: 'page' | 'template' | 'component'
  category?: string
  blocks: BuilderBlock[]
  settings: PageSettings
  seo: SEOSettings
  performance: PerformanceSettings
  version: number
  parentId?: string
  templateId?: string
  authorId: string
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
}

export interface PageSettings {
  layout: 'full-width' | 'boxed' | 'custom'
  maxWidth?: number
  background?: BuilderBlockStyles['background']
  customCss?: string
  customJs?: string
  customHead?: string
  requiresAuth?: boolean
  allowComments?: boolean
  enableSharing?: boolean
  canonicalUrl?: string
}

export interface SEOSettings {
  title?: string
  description?: string
  keywords?: string[]
  ogTitle?: string
  ogDescription?: string
  ogImage?: string
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player'
  twitterTitle?: string
  twitterDescription?: string
  twitterImage?: string
  structuredData?: Record<string, any>
  noIndex?: boolean
  noFollow?: boolean
  canonical?: string
}

export interface PerformanceSettings {
  lazyLoading: boolean
  imageOptimization: boolean
  cacheStrategy: 'none' | 'browser' | 'cdn' | 'full'
  cacheTTL?: number
  preloadCritical: boolean
  minifyOutput: boolean
}

export interface BuilderTemplate {
  id: string
  name: string
  description: string
  category: string
  thumbnail: string
  blocks: BuilderBlock[]
  settings: PageSettings
  isPublic: boolean
  isPremium: boolean
  tags: string[]
  authorId: string
  downloads: number
  rating: number
  createdAt: Date
  updatedAt: Date
}

export interface BuilderComponent {
  id: string
  name: string
  description: string
  category: string
  icon: string
  blocks: BuilderBlock[]
  props: ComponentProp[]
  isReusable: boolean
  isGlobal: boolean
  version: string
  authorId: string
  createdAt: Date
  updatedAt: Date
}

export interface ComponentProp {
  name: string
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'color' | 'image' | 'url'
  label: string
  description?: string
  defaultValue?: any
  required: boolean
  options?: { label: string; value: any }[]
  validation?: {
    min?: number
    max?: number
    pattern?: string
    custom?: string
  }
}

export interface BuilderHistory {
  id: string
  pageId: string
  action: 'create' | 'update' | 'delete' | 'move' | 'duplicate' | 'style' | 'content'
  blockId?: string
  previousState?: any
  newState?: any
  userId: string
  timestamp: Date
  description: string
}

export interface BuilderSettings {
  gridSize: number
  snapToGrid: boolean
  showGrid: boolean
  showRulers: boolean
  showOutlines: boolean
  autoSave: boolean
  autoSaveInterval: number
  undoLimit: number
  defaultDevice: 'desktop' | 'tablet' | 'mobile'
  theme: 'light' | 'dark' | 'auto'
  shortcuts: Record<string, string>
  plugins: string[]
}

export interface BuilderState {
  currentPage: BuilderPage | null
  selectedBlocks: string[]
  hoveredBlock: string | null
  draggedBlock: BuilderBlock | null
  clipboard: BuilderBlock[]
  history: BuilderHistory[]
  historyIndex: number
  isPreviewMode: boolean
  currentDevice: 'desktop' | 'tablet' | 'mobile'
  zoom: number
  settings: BuilderSettings
  isLoading: boolean
  error: string | null
}

export interface BuilderAction {
  type: string
  payload?: any
}

export interface BuilderContextType {
  state: BuilderState
  dispatch: (action: BuilderAction) => void
  
  // Page actions
  createPage: (page: Partial<BuilderPage>) => Promise<BuilderPage>
  updatePage: (id: string, updates: Partial<BuilderPage>) => Promise<void>
  deletePage: (id: string) => Promise<void>
  duplicatePage: (id: string) => Promise<BuilderPage>
  
  // Block actions
  addBlock: (block: Partial<BuilderBlock>, parentId?: string) => void
  updateBlock: (id: string, updates: Partial<BuilderBlock>) => void
  deleteBlock: (id: string) => void
  duplicateBlock: (id: string) => void
  moveBlock: (id: string, newPosition: BuilderBlock['position']) => void
  
  // Selection actions
  selectBlock: (id: string, multi?: boolean) => void
  selectBlocks: (ids: string[]) => void
  clearSelection: () => void
  
  // History actions
  undo: () => void
  redo: () => void
  saveState: (action: string, blockId?: string) => void
  
  // Clipboard actions
  copyBlocks: (ids: string[]) => void
  cutBlocks: (ids: string[]) => void
  pasteBlocks: (parentId?: string) => void
  
  // Utility actions
  setPreviewMode: (enabled: boolean) => void
  setDevice: (device: 'desktop' | 'tablet' | 'mobile') => void
  setZoom: (zoom: number) => void
  updateSettings: (settings: Partial<BuilderSettings>) => void
}

export interface BlockDefinition {
  type: string
  name: string
  category: string
  icon: string
  description: string
  defaultContent: Record<string, any>
  defaultSettings: BuilderBlockSettings
  defaultStyles: BuilderBlockStyles
  props: ComponentProp[]
  allowChildren: boolean
  allowedParents?: string[]
  maxChildren?: number
  isContainer: boolean
  isInline: boolean
  component: React.ComponentType<any>
  settingsComponent?: React.ComponentType<any>
  previewComponent?: React.ComponentType<any>
}

export interface BuilderPlugin {
  id: string
  name: string
  version: string
  description: string
  author: string
  blocks?: BlockDefinition[]
  components?: React.ComponentType<any>[]
  hooks?: Record<string, Function>
  settings?: ComponentProp[]
  install: () => void
  uninstall: () => void
}

export interface BuilderTheme {
  id: string
  name: string
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    surface: string
    text: string
    textSecondary: string
    border: string
    success: string
    warning: string
    error: string
  }
  typography: {
    fontFamily: string
    headingFont: string
    fontSize: {
      xs: number
      sm: number
      base: number
      lg: number
      xl: number
      '2xl': number
      '3xl': number
      '4xl': number
    }
    fontWeight: {
      light: number
      normal: number
      medium: number
      semibold: number
      bold: number
    }
  }
  spacing: {
    xs: number
    sm: number
    md: number
    lg: number
    xl: number
    '2xl': number
  }
  borderRadius: {
    none: number
    sm: number
    md: number
    lg: number
    full: number
  }
  shadows: {
    sm: string
    md: string
    lg: string
    xl: string
  }
}

export interface BuilderAPI {
  // Pages
  getPages: (filters?: any) => Promise<BuilderPage[]>
  getPage: (id: string) => Promise<BuilderPage | null>
  createPage: (page: Partial<BuilderPage>) => Promise<BuilderPage>
  updatePage: (id: string, updates: Partial<BuilderPage>) => Promise<BuilderPage>
  deletePage: (id: string) => Promise<void>
  
  // Templates
  getTemplates: (filters?: any) => Promise<BuilderTemplate[]>
  getTemplate: (id: string) => Promise<BuilderTemplate | null>
  createTemplate: (template: Partial<BuilderTemplate>) => Promise<BuilderTemplate>
  
  // Components
  getComponents: (filters?: any) => Promise<BuilderComponent[]>
  getComponent: (id: string) => Promise<BuilderComponent | null>
  createComponent: (component: Partial<BuilderComponent>) => Promise<BuilderComponent>
  
  // Assets
  uploadAsset: (file: File) => Promise<{ url: string; id: string }>
  getAssets: (filters?: any) => Promise<any[]>
  deleteAsset: (id: string) => Promise<void>
}

export default {
  BuilderBlock,
  BuilderPage,
  BuilderTemplate,
  BuilderComponent,
  BuilderState,
  BuilderContextType,
  BlockDefinition,
  BuilderPlugin,
  BuilderTheme,
  BuilderAPI
}
