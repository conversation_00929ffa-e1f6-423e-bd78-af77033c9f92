// Independent Builder Context
// Complete state management for the new builder system

'use client'

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react'
import { 
  BuilderState, 
  BuilderAction, 
  BuilderContextType, 
  BuilderPage, 
  BuilderBlock,
  BuilderSettings,
  BuilderHistory
} from '../types'
import { builderReducer, initialBuilderState } from './builder-reducer'
import { BuilderAPI } from '../api/builder-api'
import { generateId } from '../utils/helpers'

// Create context
const BuilderContext = createContext<BuilderContextType | null>(null)

// Builder provider component
export function BuilderProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(builderReducer, initialBuilderState)

  // Auto-save functionality
  useEffect(() => {
    if (!state.settings.autoSave || !state.currentPage) return

    const interval = setInterval(() => {
      if (state.currentPage) {
        updatePage(state.currentPage.id, state.currentPage)
      }
    }, state.settings.autoSaveInterval * 1000)

    return () => clearInterval(interval)
  }, [state.settings.autoSave, state.settings.autoSaveInterval, state.currentPage])

  // Page actions
  const createPage = useCallback(async (pageData: Partial<BuilderPage>): Promise<BuilderPage> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      
      const newPage: BuilderPage = {
        id: generateId(),
        title: pageData.title || 'Untitled Page',
        slug: pageData.slug || 'untitled-page',
        description: pageData.description || '',
        status: pageData.status || 'draft',
        type: pageData.type || 'page',
        category: pageData.category,
        blocks: pageData.blocks || [],
        settings: pageData.settings || {
          layout: 'boxed',
          maxWidth: 1200,
          lazyLoading: true,
          imageOptimization: true,
          cacheStrategy: 'browser',
          preloadCritical: true,
          minifyOutput: true
        },
        seo: pageData.seo || {},
        performance: pageData.performance || {
          lazyLoading: true,
          imageOptimization: true,
          cacheStrategy: 'browser',
          preloadCritical: true,
          minifyOutput: true
        },
        version: 1,
        parentId: pageData.parentId,
        templateId: pageData.templateId,
        authorId: pageData.authorId || 'current-user',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const createdPage = await BuilderAPI.createPage(newPage)
      
      dispatch({ type: 'SET_CURRENT_PAGE', payload: createdPage })
      dispatch({ type: 'SAVE_STATE', payload: { action: 'create_page', description: 'Created new page' } })
      
      return createdPage
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message })
      throw error
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [])

  const updatePage = useCallback(async (id: string, updates: Partial<BuilderPage>): Promise<void> => {
    try {
      const updatedPage = await BuilderAPI.updatePage(id, {
        ...updates,
        updatedAt: new Date()
      })
      
      if (state.currentPage?.id === id) {
        dispatch({ type: 'SET_CURRENT_PAGE', payload: updatedPage })
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message })
      throw error
    }
  }, [state.currentPage])

  const deletePage = useCallback(async (id: string): Promise<void> => {
    try {
      await BuilderAPI.deletePage(id)
      
      if (state.currentPage?.id === id) {
        dispatch({ type: 'SET_CURRENT_PAGE', payload: null })
      }
      
      dispatch({ type: 'SAVE_STATE', payload: { action: 'delete_page', description: 'Deleted page' } })
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message })
      throw error
    }
  }, [state.currentPage])

  const duplicatePage = useCallback(async (id: string): Promise<BuilderPage> => {
    try {
      const originalPage = await BuilderAPI.getPage(id)
      if (!originalPage) throw new Error('Page not found')

      const duplicatedPage = await createPage({
        ...originalPage,
        id: undefined,
        title: `${originalPage.title} (Copy)`,
        slug: `${originalPage.slug}-copy`,
        status: 'draft',
        createdAt: undefined,
        updatedAt: undefined,
        publishedAt: undefined
      })

      return duplicatedPage
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message })
      throw error
    }
  }, [createPage])

  // Block actions
  const addBlock = useCallback((blockData: Partial<BuilderBlock>, parentId?: string) => {
    if (!state.currentPage) return

    const newBlock: BuilderBlock = {
      id: generateId(),
      type: blockData.type || 'text',
      name: blockData.name || 'New Block',
      category: blockData.category || 'content',
      icon: blockData.icon || 'square',
      description: blockData.description || '',
      content: blockData.content || {},
      settings: blockData.settings || {},
      styles: blockData.styles || {},
      responsive: blockData.responsive || {
        desktop: { visible: true },
        tablet: { visible: true },
        mobile: { visible: true }
      },
      conditions: blockData.conditions || {},
      animation: blockData.animation || {
        type: 'none',
        duration: 300,
        delay: 0,
        easing: 'ease',
        trigger: 'load'
      },
      position: blockData.position || { x: 0, y: 0, w: 12, h: 4 },
      parentId,
      children: [],
      isLocked: false,
      isVisible: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    dispatch({ type: 'ADD_BLOCK', payload: { block: newBlock, parentId } })
    dispatch({ type: 'SELECT_BLOCK', payload: newBlock.id })
    dispatch({ type: 'SAVE_STATE', payload: { action: 'add_block', blockId: newBlock.id, description: 'Added new block' } })
  }, [state.currentPage])

  const updateBlock = useCallback((id: string, updates: Partial<BuilderBlock>) => {
    dispatch({ type: 'UPDATE_BLOCK', payload: { id, updates: { ...updates, updatedAt: new Date() } } })
    dispatch({ type: 'SAVE_STATE', payload: { action: 'update_block', blockId: id, description: 'Updated block' } })
  }, [])

  const deleteBlock = useCallback((id: string) => {
    dispatch({ type: 'DELETE_BLOCK', payload: id })
    dispatch({ type: 'SAVE_STATE', payload: { action: 'delete_block', blockId: id, description: 'Deleted block' } })
  }, [])

  const duplicateBlock = useCallback((id: string) => {
    if (!state.currentPage) return

    const originalBlock = state.currentPage.blocks.find(block => block.id === id)
    if (!originalBlock) return

    const duplicatedBlock: BuilderBlock = {
      ...originalBlock,
      id: generateId(),
      name: `${originalBlock.name} (Copy)`,
      position: {
        ...originalBlock.position,
        y: originalBlock.position.y + originalBlock.position.h + 1
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }

    dispatch({ type: 'ADD_BLOCK', payload: { block: duplicatedBlock, parentId: originalBlock.parentId } })
    dispatch({ type: 'SELECT_BLOCK', payload: duplicatedBlock.id })
    dispatch({ type: 'SAVE_STATE', payload: { action: 'duplicate_block', blockId: duplicatedBlock.id, description: 'Duplicated block' } })
  }, [state.currentPage])

  const moveBlock = useCallback((id: string, newPosition: BuilderBlock['position']) => {
    dispatch({ type: 'MOVE_BLOCK', payload: { id, position: newPosition } })
    dispatch({ type: 'SAVE_STATE', payload: { action: 'move_block', blockId: id, description: 'Moved block' } })
  }, [])

  // Selection actions
  const selectBlock = useCallback((id: string, multi = false) => {
    dispatch({ type: 'SELECT_BLOCK', payload: { id, multi } })
  }, [])

  const selectBlocks = useCallback((ids: string[]) => {
    dispatch({ type: 'SELECT_BLOCKS', payload: ids })
  }, [])

  const clearSelection = useCallback(() => {
    dispatch({ type: 'CLEAR_SELECTION' })
  }, [])

  // History actions
  const undo = useCallback(() => {
    dispatch({ type: 'UNDO' })
  }, [])

  const redo = useCallback(() => {
    dispatch({ type: 'REDO' })
  }, [])

  const saveState = useCallback((action: string, blockId?: string) => {
    const historyEntry: BuilderHistory = {
      id: generateId(),
      pageId: state.currentPage?.id || '',
      action: action as any,
      blockId,
      previousState: state.currentPage,
      newState: state.currentPage,
      userId: 'current-user',
      timestamp: new Date(),
      description: action
    }

    dispatch({ type: 'SAVE_STATE', payload: historyEntry })
  }, [state.currentPage])

  // Clipboard actions
  const copyBlocks = useCallback((ids: string[]) => {
    if (!state.currentPage) return

    const blocksToCopy = state.currentPage.blocks.filter(block => ids.includes(block.id))
    dispatch({ type: 'COPY_BLOCKS', payload: blocksToCopy })
  }, [state.currentPage])

  const cutBlocks = useCallback((ids: string[]) => {
    copyBlocks(ids)
    ids.forEach(id => deleteBlock(id))
  }, [copyBlocks, deleteBlock])

  const pasteBlocks = useCallback((parentId?: string) => {
    if (state.clipboard.length === 0) return

    const pastedBlocks = state.clipboard.map(block => ({
      ...block,
      id: generateId(),
      parentId,
      position: {
        ...block.position,
        y: block.position.y + 10 // Offset pasted blocks
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }))

    pastedBlocks.forEach(block => {
      dispatch({ type: 'ADD_BLOCK', payload: { block, parentId } })
    })

    dispatch({ type: 'SELECT_BLOCKS', payload: pastedBlocks.map(block => block.id) })
    dispatch({ type: 'SAVE_STATE', payload: { action: 'paste_blocks', description: 'Pasted blocks' } })
  }, [state.clipboard])

  // Utility actions
  const setPreviewMode = useCallback((enabled: boolean) => {
    dispatch({ type: 'SET_PREVIEW_MODE', payload: enabled })
  }, [])

  const setDevice = useCallback((device: 'desktop' | 'tablet' | 'mobile') => {
    dispatch({ type: 'SET_DEVICE', payload: device })
  }, [])

  const setZoom = useCallback((zoom: number) => {
    dispatch({ type: 'SET_ZOOM', payload: zoom })
  }, [])

  const updateSettings = useCallback((settings: Partial<BuilderSettings>) => {
    dispatch({ type: 'UPDATE_SETTINGS', payload: settings })
  }, [])

  // Context value
  const contextValue: BuilderContextType = {
    state,
    dispatch,
    
    // Page actions
    createPage,
    updatePage,
    deletePage,
    duplicatePage,
    
    // Block actions
    addBlock,
    updateBlock,
    deleteBlock,
    duplicateBlock,
    moveBlock,
    
    // Selection actions
    selectBlock,
    selectBlocks,
    clearSelection,
    
    // History actions
    undo,
    redo,
    saveState,
    
    // Clipboard actions
    copyBlocks,
    cutBlocks,
    pasteBlocks,
    
    // Utility actions
    setPreviewMode,
    setDevice,
    setZoom,
    updateSettings
  }

  return (
    <BuilderContext.Provider value={contextValue}>
      {children}
    </BuilderContext.Provider>
  )
}

// Hook to use builder context
export function useBuilder(): BuilderContextType {
  const context = useContext(BuilderContext)
  if (!context) {
    throw new Error('useBuilder must be used within a BuilderProvider')
  }
  return context
}

// Hook for specific builder state
export function useBuilderState() {
  const { state } = useBuilder()
  return state
}

// Hook for builder actions
export function useBuilderActions() {
  const { 
    createPage, updatePage, deletePage, duplicatePage,
    addBlock, updateBlock, deleteBlock, duplicateBlock, moveBlock,
    selectBlock, selectBlocks, clearSelection,
    undo, redo, saveState,
    copyBlocks, cutBlocks, pasteBlocks,
    setPreviewMode, setDevice, setZoom, updateSettings
  } = useBuilder()

  return {
    // Page actions
    createPage, updatePage, deletePage, duplicatePage,
    // Block actions
    addBlock, updateBlock, deleteBlock, duplicateBlock, moveBlock,
    // Selection actions
    selectBlock, selectBlocks, clearSelection,
    // History actions
    undo, redo, saveState,
    // Clipboard actions
    copyBlocks, cutBlocks, pasteBlocks,
    // Utility actions
    setPreviewMode, setDevice, setZoom, updateSettings
  }
}

export default BuilderProvider
