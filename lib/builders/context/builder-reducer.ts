// Builder State Reducer
// Handles all state mutations for the builder system

import { BuilderState, BuilderAction, BuilderBlock, BuilderPage, BuilderHistory } from '../types'

export const initialBuilderState: BuilderState = {
  currentPage: null,
  selectedBlocks: [],
  hoveredBlock: null,
  draggedBlock: null,
  clipboard: [],
  history: [],
  historyIndex: -1,
  isPreviewMode: false,
  currentDevice: 'desktop',
  zoom: 100,
  settings: {
    gridSize: 12,
    snapToGrid: true,
    showGrid: true,
    showRulers: false,
    showOutlines: true,
    autoSave: true,
    autoSaveInterval: 30,
    undoLimit: 50,
    defaultDevice: 'desktop',
    theme: 'light',
    shortcuts: {
      'ctrl+z': 'undo',
      'ctrl+y': 'redo',
      'ctrl+c': 'copy',
      'ctrl+v': 'paste',
      'ctrl+x': 'cut',
      'delete': 'delete',
      'ctrl+d': 'duplicate',
      'escape': 'clearSelection'
    },
    plugins: []
  },
  isLoading: false,
  error: null
}

export function builderReducer(state: BuilderState, action: BuilderAction): BuilderState {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload
      }

    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false
      }

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null
      }

    case 'SET_CURRENT_PAGE':
      return {
        ...state,
        currentPage: action.payload,
        selectedBlocks: [],
        hoveredBlock: null,
        error: null
      }

    case 'UPDATE_PAGE':
      if (!state.currentPage) return state
      
      return {
        ...state,
        currentPage: {
          ...state.currentPage,
          ...action.payload,
          updatedAt: new Date()
        }
      }

    case 'ADD_BLOCK': {
      if (!state.currentPage) return state

      const { block, parentId } = action.payload
      const updatedBlocks = [...state.currentPage.blocks, block]

      // Update parent's children if parentId is provided
      if (parentId) {
        const parentIndex = updatedBlocks.findIndex(b => b.id === parentId)
        if (parentIndex !== -1) {
          updatedBlocks[parentIndex] = {
            ...updatedBlocks[parentIndex],
            children: [...(updatedBlocks[parentIndex].children || []), block.id]
          }
        }
      }

      return {
        ...state,
        currentPage: {
          ...state.currentPage,
          blocks: updatedBlocks,
          updatedAt: new Date()
        }
      }
    }

    case 'UPDATE_BLOCK': {
      if (!state.currentPage) return state

      const { id, updates } = action.payload
      const blockIndex = state.currentPage.blocks.findIndex(block => block.id === id)
      
      if (blockIndex === -1) return state

      const updatedBlocks = [...state.currentPage.blocks]
      updatedBlocks[blockIndex] = {
        ...updatedBlocks[blockIndex],
        ...updates,
        updatedAt: new Date()
      }

      return {
        ...state,
        currentPage: {
          ...state.currentPage,
          blocks: updatedBlocks,
          updatedAt: new Date()
        }
      }
    }

    case 'DELETE_BLOCK': {
      if (!state.currentPage) return state

      const blockId = action.payload
      const blockToDelete = state.currentPage.blocks.find(block => block.id === blockId)
      
      if (!blockToDelete) return state

      // Remove block and its children
      const blocksToRemove = new Set([blockId])
      const addChildrenToRemove = (parentId: string) => {
        state.currentPage!.blocks.forEach(block => {
          if (block.parentId === parentId) {
            blocksToRemove.add(block.id)
            addChildrenToRemove(block.id)
          }
        })
      }
      addChildrenToRemove(blockId)

      const updatedBlocks = state.currentPage.blocks.filter(block => !blocksToRemove.has(block.id))

      // Update parent's children array if block had a parent
      if (blockToDelete.parentId) {
        const parentIndex = updatedBlocks.findIndex(b => b.id === blockToDelete.parentId)
        if (parentIndex !== -1) {
          updatedBlocks[parentIndex] = {
            ...updatedBlocks[parentIndex],
            children: updatedBlocks[parentIndex].children?.filter(childId => childId !== blockId) || []
          }
        }
      }

      return {
        ...state,
        currentPage: {
          ...state.currentPage,
          blocks: updatedBlocks,
          updatedAt: new Date()
        },
        selectedBlocks: state.selectedBlocks.filter(id => !blocksToRemove.has(id)),
        hoveredBlock: blocksToRemove.has(state.hoveredBlock || '') ? null : state.hoveredBlock
      }
    }

    case 'MOVE_BLOCK': {
      if (!state.currentPage) return state

      const { id, position } = action.payload
      const blockIndex = state.currentPage.blocks.findIndex(block => block.id === id)
      
      if (blockIndex === -1) return state

      const updatedBlocks = [...state.currentPage.blocks]
      updatedBlocks[blockIndex] = {
        ...updatedBlocks[blockIndex],
        position,
        updatedAt: new Date()
      }

      return {
        ...state,
        currentPage: {
          ...state.currentPage,
          blocks: updatedBlocks,
          updatedAt: new Date()
        }
      }
    }

    case 'SELECT_BLOCK': {
      const { id, multi } = action.payload

      if (multi) {
        const isSelected = state.selectedBlocks.includes(id)
        return {
          ...state,
          selectedBlocks: isSelected
            ? state.selectedBlocks.filter(blockId => blockId !== id)
            : [...state.selectedBlocks, id]
        }
      }

      return {
        ...state,
        selectedBlocks: [id]
      }
    }

    case 'SELECT_BLOCKS':
      return {
        ...state,
        selectedBlocks: action.payload
      }

    case 'CLEAR_SELECTION':
      return {
        ...state,
        selectedBlocks: [],
        hoveredBlock: null
      }

    case 'SET_HOVERED_BLOCK':
      return {
        ...state,
        hoveredBlock: action.payload
      }

    case 'SET_DRAGGED_BLOCK':
      return {
        ...state,
        draggedBlock: action.payload
      }

    case 'COPY_BLOCKS':
      return {
        ...state,
        clipboard: action.payload
      }

    case 'SAVE_STATE': {
      const historyEntry: BuilderHistory = action.payload
      const newHistory = state.history.slice(0, state.historyIndex + 1)
      newHistory.push(historyEntry)

      // Limit history size
      if (newHistory.length > state.settings.undoLimit) {
        newHistory.shift()
      }

      return {
        ...state,
        history: newHistory,
        historyIndex: newHistory.length - 1
      }
    }

    case 'UNDO': {
      if (state.historyIndex <= 0) return state

      const previousState = state.history[state.historyIndex - 1]
      
      return {
        ...state,
        currentPage: previousState.previousState,
        historyIndex: state.historyIndex - 1
      }
    }

    case 'REDO': {
      if (state.historyIndex >= state.history.length - 1) return state

      const nextState = state.history[state.historyIndex + 1]
      
      return {
        ...state,
        currentPage: nextState.newState,
        historyIndex: state.historyIndex + 1
      }
    }

    case 'SET_PREVIEW_MODE':
      return {
        ...state,
        isPreviewMode: action.payload,
        selectedBlocks: action.payload ? [] : state.selectedBlocks
      }

    case 'SET_DEVICE':
      return {
        ...state,
        currentDevice: action.payload
      }

    case 'SET_ZOOM':
      return {
        ...state,
        zoom: Math.max(25, Math.min(200, action.payload))
      }

    case 'UPDATE_SETTINGS':
      return {
        ...state,
        settings: {
          ...state.settings,
          ...action.payload
        }
      }

    case 'RESET_BUILDER':
      return {
        ...initialBuilderState,
        settings: state.settings // Preserve settings
      }

    default:
      return state
  }
}

// Action creators
export const builderActions = {
  setLoading: (loading: boolean): BuilderAction => ({
    type: 'SET_LOADING',
    payload: loading
  }),

  setError: (error: string | null): BuilderAction => ({
    type: 'SET_ERROR',
    payload: error
  }),

  clearError: (): BuilderAction => ({
    type: 'CLEAR_ERROR'
  }),

  setCurrentPage: (page: BuilderPage | null): BuilderAction => ({
    type: 'SET_CURRENT_PAGE',
    payload: page
  }),

  updatePage: (updates: Partial<BuilderPage>): BuilderAction => ({
    type: 'UPDATE_PAGE',
    payload: updates
  }),

  addBlock: (block: BuilderBlock, parentId?: string): BuilderAction => ({
    type: 'ADD_BLOCK',
    payload: { block, parentId }
  }),

  updateBlock: (id: string, updates: Partial<BuilderBlock>): BuilderAction => ({
    type: 'UPDATE_BLOCK',
    payload: { id, updates }
  }),

  deleteBlock: (id: string): BuilderAction => ({
    type: 'DELETE_BLOCK',
    payload: id
  }),

  moveBlock: (id: string, position: BuilderBlock['position']): BuilderAction => ({
    type: 'MOVE_BLOCK',
    payload: { id, position }
  }),

  selectBlock: (id: string, multi = false): BuilderAction => ({
    type: 'SELECT_BLOCK',
    payload: { id, multi }
  }),

  selectBlocks: (ids: string[]): BuilderAction => ({
    type: 'SELECT_BLOCKS',
    payload: ids
  }),

  clearSelection: (): BuilderAction => ({
    type: 'CLEAR_SELECTION'
  }),

  setHoveredBlock: (id: string | null): BuilderAction => ({
    type: 'SET_HOVERED_BLOCK',
    payload: id
  }),

  setDraggedBlock: (block: BuilderBlock | null): BuilderAction => ({
    type: 'SET_DRAGGED_BLOCK',
    payload: block
  }),

  copyBlocks: (blocks: BuilderBlock[]): BuilderAction => ({
    type: 'COPY_BLOCKS',
    payload: blocks
  }),

  saveState: (historyEntry: BuilderHistory): BuilderAction => ({
    type: 'SAVE_STATE',
    payload: historyEntry
  }),

  undo: (): BuilderAction => ({
    type: 'UNDO'
  }),

  redo: (): BuilderAction => ({
    type: 'REDO'
  }),

  setPreviewMode: (enabled: boolean): BuilderAction => ({
    type: 'SET_PREVIEW_MODE',
    payload: enabled
  }),

  setDevice: (device: 'desktop' | 'tablet' | 'mobile'): BuilderAction => ({
    type: 'SET_DEVICE',
    payload: device
  }),

  setZoom: (zoom: number): BuilderAction => ({
    type: 'SET_ZOOM',
    payload: zoom
  }),

  updateSettings: (settings: Partial<BuilderState['settings']>): BuilderAction => ({
    type: 'UPDATE_SETTINGS',
    payload: settings
  }),

  resetBuilder: (): BuilderAction => ({
    type: 'RESET_BUILDER'
  })
}

export default builderReducer
