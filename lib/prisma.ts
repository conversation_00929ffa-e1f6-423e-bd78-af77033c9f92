import { PrismaClient } from '@prisma/client'

// Global Prisma client instance for development hot reloading
declare global {
  var __prisma: PrismaClient | undefined
}

// Database configuration
interface DatabaseConfig {
  url?: string
  maxConnections?: number
  connectionTimeout?: number
  queryTimeout?: number
  logLevel?: 'info' | 'query' | 'warn' | 'error'
  enableLogging?: boolean
}

// Default configuration
const defaultConfig: DatabaseConfig = {
  url: process.env.DATABASE_URL,
  maxConnections: 10,
  connectionTimeout: 10000,
  queryTimeout: 30000,
  logLevel: 'warn',
  enableLogging: process.env.NODE_ENV === 'development',
}

// Create Prisma client with configuration
function createPrismaClient(config: DatabaseConfig = defaultConfig): PrismaClient {
  const logLevels = config.enableLogging ? [config.logLevel!] : []
  
  return new PrismaClient({
    log: logLevels,
    datasources: {
      db: {
        url: config.url,
      },
    },
  })
}

// Get or create Prisma client (singleton pattern)
function getPrismaClient(config?: DatabaseConfig): PrismaClient {
  if (globalThis.__prisma) {
    return globalThis.__prisma
  }

  const prisma = createPrismaClient(config)

  // Store in global for development hot reloading
  if (process.env.NODE_ENV === 'development') {
    globalThis.__prisma = prisma
  }

  return prisma
}

// Export the main Prisma client instance
export const prisma = getPrismaClient()

// Database utility functions
export async function connectDatabase(): Promise<void> {
  try {
    await prisma.$connect()
    console.log('✅ Database connected successfully')
  } catch (error) {
    console.error('❌ Failed to connect to database:', error)
    throw error
  }
}

export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect()
    console.log('✅ Database disconnected successfully')
  } catch (error) {
    console.error('❌ Failed to disconnect from database:', error)
    throw error
  }
}

export async function healthCheck(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`
    return true
  } catch (error) {
    console.error('❌ Database health check failed:', error)
    return false
  }
}

// Transaction wrapper
export async function withTransaction<T>(
  fn: (prisma: Omit<PrismaClient, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">) => Promise<T>
): Promise<T> {
  return prisma.$transaction(fn)
}

// Raw query helpers
export async function executeRawQuery<T = any>(query: string, params?: any[]): Promise<T> {
  return prisma.$queryRawUnsafe(query, ...(params || []))
}

export async function executeRawCommand(command: string, params?: any[]): Promise<number> {
  return prisma.$executeRawUnsafe(command, ...(params || []))
}

// Graceful shutdown handler
export function setupGracefulShutdown(): void {
  const shutdown = async (signal: string) => {
    console.log(`Received ${signal}, shutting down gracefully...`)
    try {
      await disconnectDatabase()
      process.exit(0)
    } catch (error) {
      console.error('Error during shutdown:', error)
      process.exit(1)
    }
  }

  process.on('SIGTERM', () => shutdown('SIGTERM'))
  process.on('SIGINT', () => shutdown('SIGINT'))
}

// Development utilities
export async function resetDatabase(): Promise<void> {
  if (process.env.NODE_ENV === 'production') {
    throw new Error('Database reset is not allowed in production')
  }

  console.log('⚠️  Database reset should be done using Prisma CLI: npx prisma migrate reset')
}

export async function runMigrations(): Promise<void> {
  console.log('📦 Migrations should be run using Prisma CLI: npx prisma migrate deploy')
}

// Performance monitoring
export async function getConnectionInfo(): Promise<{
  activeConnections: number
  maxConnections: number
  databaseSize?: string
}> {
  try {
    // PostgreSQL specific queries - would need to be adapted for other databases
    const [connectionResult, sizeResult] = await Promise.all([
      prisma.$queryRaw<Array<{ count: number }>>`
        SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active'
      `,
      prisma.$queryRaw<Array<{ size: string }>>`
        SELECT pg_size_pretty(pg_database_size(current_database())) as size
      `
    ])

    return {
      activeConnections: Number(connectionResult[0]?.count || 0),
      maxConnections: defaultConfig.maxConnections || 10,
      databaseSize: sizeResult[0]?.size
    }
  } catch (error) {
    console.error('Failed to get connection info:', error)
    return {
      activeConnections: 0,
      maxConnections: defaultConfig.maxConnections || 10
    }
  }
}

// Export types for convenience
export type { PrismaClient } from '@prisma/client'

// Default export for convenience
export default prisma
