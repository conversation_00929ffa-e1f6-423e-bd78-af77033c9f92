import type { 
  ShippingProviderConfig, 
  SAProvince, 
  ShippingProvider,
  ShippingServiceType,
  ShippingFeature 
} from './types'

// Shipping provider configurations
export const shippingConfig: ShippingProviderConfig = {
  courierGuy: {
    username: process.env.COURIER_GUY_USERNAME || '',
    password: process.env.COURIER_GUY_PASSWORD || '',
    accountNumber: process.env.COURIER_GUY_ACCOUNT || '',
    sandbox: process.env.NODE_ENV !== 'production',
  },
  postnet: {
    apiKey: process.env.POSTNET_API_KEY || '',
    accountNumber: process.env.POSTNET_ACCOUNT || '',
    sandbox: process.env.NODE_ENV !== 'production',
  },
  aramex: {
    username: process.env.ARAMEX_USERNAME || '',
    password: process.env.ARAMEX_PASSWORD || '',
    accountNumber: process.env.ARAMEX_ACCOUNT || '',
    accountPin: process.env.ARAMEX_PIN || '',
    accountEntity: process.env.ARAMEX_ENTITY || '',
    accountCountryCode: 'ZA',
    sandbox: process.env.NODE_ENV !== 'production',
  },
}

// South African provinces with postal code ranges
export const SA_PROVINCES: SAProvince[] = [
  {
    code: 'WC',
    name: 'Western Cape',
    postalCodeRanges: [
      { from: '6500', to: '6999' },
      { from: '7000', to: '8999' },
    ],
  },
  {
    code: 'GP',
    name: 'Gauteng',
    postalCodeRanges: [
      { from: '1000', to: '2999' },
    ],
  },
  {
    code: 'KZN',
    name: 'KwaZulu-Natal',
    postalCodeRanges: [
      { from: '3000', to: '4999' },
    ],
  },
  {
    code: 'EC',
    name: 'Eastern Cape',
    postalCodeRanges: [
      { from: '5000', to: '6499' },
    ],
  },
  {
    code: 'FS',
    name: 'Free State',
    postalCodeRanges: [
      { from: '9300', to: '9999' },
    ],
  },
  {
    code: 'LP',
    name: 'Limpopo',
    postalCodeRanges: [
      { from: '0700', to: '0999' },
    ],
  },
  {
    code: 'MP',
    name: 'Mpumalanga',
    postalCodeRanges: [
      { from: '1200', to: '1299' },
      { from: '2300', to: '2499' },
    ],
  },
  {
    code: 'NC',
    name: 'Northern Cape',
    postalCodeRanges: [
      { from: '8000', to: '8999' },
    ],
  },
  {
    code: 'NW',
    name: 'North West',
    postalCodeRanges: [
      { from: '2500', to: '2999' },
    ],
  },
]

// Shipping zones for South Africa
export const SHIPPING_ZONES = {
  METRO: {
    name: 'Metro Areas',
    description: 'Major metropolitan areas',
    deliveryDays: 1,
    surcharge: 0,
    postalCodes: [
      '7000-7999', // Cape Town
      '2000-2199', // Johannesburg
      '4000-4099', // Durban
      '0001-0199', // Pretoria
    ],
  },
  URBAN: {
    name: 'Urban Areas',
    description: 'Urban and suburban areas',
    deliveryDays: 2,
    surcharge: 0,
    postalCodes: [
      '1000-2999', // Gauteng
      '3000-4999', // KZN
      '6500-8999', // Western Cape
    ],
  },
  RURAL: {
    name: 'Rural Areas',
    description: 'Rural and remote areas',
    deliveryDays: 5,
    surcharge: 50,
    postalCodes: [
      '0700-0999', // Limpopo rural
      '5000-6499', // Eastern Cape rural
      '8000-8999', // Northern Cape
    ],
  },
} as const

// Provider service configurations
export const PROVIDER_SERVICES = {
  [ShippingProvider.COURIER_GUY]: {
    name: 'The Courier Guy',
    services: [
      {
        id: 'overnight',
        name: 'Overnight',
        type: ShippingServiceType.OVERNIGHT,
        maxWeight: 30,
        deliveryDays: { min: 1, max: 1 },
        features: [ShippingFeature.TRACKING, ShippingFeature.SMS_NOTIFICATIONS],
      },
      {
        id: 'express',
        name: 'Express',
        type: ShippingServiceType.EXPRESS,
        maxWeight: 30,
        deliveryDays: { min: 1, max: 2 },
        features: [ShippingFeature.TRACKING, ShippingFeature.SMS_NOTIFICATIONS],
      },
      {
        id: 'economy',
        name: 'Economy',
        type: ShippingServiceType.ECONOMY,
        maxWeight: 30,
        deliveryDays: { min: 2, max: 5 },
        features: [ShippingFeature.TRACKING],
      },
    ],
    apiUrl: {
      sandbox: 'https://api-staging.thecourierguy.co.za',
      production: 'https://api.thecourierguy.co.za',
    },
  },
  [ShippingProvider.POSTNET]: {
    name: 'PostNet',
    services: [
      {
        id: 'express',
        name: 'PostNet Express',
        type: ShippingServiceType.EXPRESS,
        maxWeight: 30,
        deliveryDays: { min: 1, max: 3 },
        features: [ShippingFeature.TRACKING, ShippingFeature.COLLECTION_POINT],
      },
      {
        id: 'standard',
        name: 'PostNet Standard',
        type: ShippingServiceType.STANDARD,
        maxWeight: 30,
        deliveryDays: { min: 2, max: 5 },
        features: [ShippingFeature.TRACKING, ShippingFeature.COLLECTION_POINT],
      },
    ],
    apiUrl: {
      sandbox: 'https://api-staging.postnet.co.za',
      production: 'https://api.postnet.co.za',
    },
  },
  [ShippingProvider.ARAMEX]: {
    name: 'Aramex',
    services: [
      {
        id: 'express',
        name: 'Aramex Express',
        type: ShippingServiceType.EXPRESS,
        maxWeight: 50,
        deliveryDays: { min: 1, max: 2 },
        features: [ShippingFeature.TRACKING, ShippingFeature.SIGNATURE_REQUIRED],
      },
      {
        id: 'standard',
        name: 'Aramex Standard',
        type: ShippingServiceType.STANDARD,
        maxWeight: 50,
        deliveryDays: { min: 2, max: 4 },
        features: [ShippingFeature.TRACKING],
      },
    ],
    apiUrl: {
      sandbox: 'https://ws.dev.aramex.net',
      production: 'https://ws.aramex.net',
    },
  },
} as const

// Default shipping dimensions and weights
export const DEFAULT_PACKAGE = {
  dimensions: {
    length: 30, // cm
    width: 20, // cm
    height: 10, // cm
    weight: 1, // kg
  },
  maxDimensions: {
    length: 120, // cm
    width: 80, // cm
    height: 80, // cm
    weight: 30, // kg
  },
} as const

// Shipping rate calculation
export const RATE_CALCULATION = {
  baseCost: 50, // ZAR
  perKgRate: 15, // ZAR per kg
  volumetricDivisor: 5000, // cm³/kg for volumetric weight
  fuelSurcharge: 0.15, // 15%
  vatRate: 0.15, // 15% VAT
  insuranceRate: 0.01, // 1% of declared value
  
  // Distance-based multipliers
  zoneMultipliers: {
    METRO: 1.0,
    URBAN: 1.2,
    RURAL: 1.5,
  },
  
  // Service type multipliers
  serviceMultipliers: {
    [ShippingServiceType.SAME_DAY]: 3.0,
    [ShippingServiceType.OVERNIGHT]: 2.0,
    [ShippingServiceType.EXPRESS]: 1.5,
    [ShippingServiceType.STANDARD]: 1.0,
    [ShippingServiceType.ECONOMY]: 0.8,
  },
} as const

// Packaging options
export const PACKAGING_OPTIONS = {
  envelope: {
    name: 'Envelope',
    maxWeight: 0.5,
    dimensions: { length: 35, width: 25, height: 2 },
    cost: 0,
  },
  small_box: {
    name: 'Small Box',
    maxWeight: 5,
    dimensions: { length: 30, width: 20, height: 15 },
    cost: 5,
  },
  medium_box: {
    name: 'Medium Box',
    maxWeight: 15,
    dimensions: { length: 40, width: 30, height: 25 },
    cost: 10,
  },
  large_box: {
    name: 'Large Box',
    maxWeight: 30,
    dimensions: { length: 60, width: 40, height: 40 },
    cost: 20,
  },
  custom: {
    name: 'Custom Packaging',
    maxWeight: 50,
    dimensions: { length: 120, width: 80, height: 80 },
    cost: 0,
  },
} as const

// Delivery time slots
export const DELIVERY_TIME_SLOTS = [
  { id: '09-12', name: '9:00 AM - 12:00 PM', cost: 20 },
  { id: '12-15', name: '12:00 PM - 3:00 PM', cost: 15 },
  { id: '15-18', name: '3:00 PM - 6:00 PM', cost: 15 },
  { id: '18-21', name: '6:00 PM - 9:00 PM', cost: 25 },
] as const

// Special services
export const SPECIAL_SERVICES = {
  signature_required: {
    name: 'Signature Required',
    description: 'Requires recipient signature on delivery',
    cost: 10,
  },
  insurance: {
    name: 'Insurance',
    description: 'Package insurance coverage',
    cost: 0, // Calculated as percentage of value
  },
  sms_notifications: {
    name: 'SMS Notifications',
    description: 'SMS updates on delivery status',
    cost: 5,
  },
  email_notifications: {
    name: 'Email Notifications',
    description: 'Email updates on delivery status',
    cost: 0,
  },
  weekend_delivery: {
    name: 'Weekend Delivery',
    description: 'Saturday delivery available',
    cost: 30,
  },
  collection_point: {
    name: 'Collection Point',
    description: 'Deliver to nearest collection point',
    cost: -10, // Discount for collection point delivery
  },
} as const

// Validation rules
export const VALIDATION_RULES = {
  postalCode: {
    pattern: /^[0-9]{4}$/,
    message: 'Postal code must be 4 digits',
  },
  phone: {
    pattern: /^(\+27|0)[0-9]{9}$/,
    message: 'Invalid South African phone number',
  },
  weight: {
    min: 0.1,
    max: 50,
    message: 'Weight must be between 0.1kg and 50kg',
  },
  dimensions: {
    min: 1,
    max: 120,
    message: 'Dimensions must be between 1cm and 120cm',
  },
  value: {
    min: 1,
    max: 100000,
    message: 'Value must be between R1 and R100,000',
  },
} as const

// Error messages
export const ERROR_MESSAGES = {
  INVALID_ADDRESS: 'Invalid shipping address',
  INVALID_POSTAL_CODE: 'Invalid postal code format',
  UNSUPPORTED_AREA: 'Delivery not available to this area',
  EXCEEDS_WEIGHT_LIMIT: 'Package exceeds weight limit',
  EXCEEDS_SIZE_LIMIT: 'Package exceeds size limit',
  INVALID_SERVICE: 'Invalid shipping service selected',
  PROVIDER_ERROR: 'Shipping provider error',
  NETWORK_ERROR: 'Network connection error',
  QUOTE_EXPIRED: 'Shipping quote has expired',
  BOOKING_FAILED: 'Failed to book shipment',
  TRACKING_UNAVAILABLE: 'Tracking information unavailable',
} as const

// Success messages
export const SUCCESS_MESSAGES = {
  QUOTE_GENERATED: 'Shipping quotes generated successfully',
  BOOKING_CREATED: 'Shipment booked successfully',
  TRACKING_UPDATED: 'Tracking information updated',
  DELIVERY_COMPLETED: 'Package delivered successfully',
} as const

// Cache configuration
export const CACHE_CONFIG = {
  quotes: {
    ttl: 15 * 60 * 1000, // 15 minutes
    key: 'shipping_quotes',
  },
  rates: {
    ttl: 60 * 60 * 1000, // 1 hour
    key: 'shipping_rates',
  },
  zones: {
    ttl: 24 * 60 * 60 * 1000, // 24 hours
    key: 'shipping_zones',
  },
} as const

// Webhook endpoints
export const WEBHOOK_ENDPOINTS = {
  courierGuy: '/api/webhooks/courier-guy',
  postnet: '/api/webhooks/postnet',
  aramex: '/api/webhooks/aramex',
} as const

// All exports are already available above
