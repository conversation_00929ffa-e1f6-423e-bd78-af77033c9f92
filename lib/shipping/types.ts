// Shipping Integration Types and Interfaces

export interface ShippingAddress {
  firstName: string
  lastName: string
  company?: string
  line1: string
  line2?: string
  suburb: string
  city: string
  province: string
  postalCode: string
  country: string
  phone?: string
  email?: string
  specialInstructions?: string
}

export interface ShippingDimensions {
  length: number // cm
  width: number // cm
  height: number // cm
  weight: number // kg
}

export interface ShippingItem {
  id: string
  name: string
  description?: string
  quantity: number
  value: number
  weight: number
  dimensions?: ShippingDimensions
  category?: string
  sku?: string
  isFragile?: boolean
  isDangerous?: boolean
}

export interface ShippingQuoteRequest {
  fromAddress: ShippingAddress
  toAddress: ShippingAddress
  items: ShippingItem[]
  totalValue: number
  totalWeight: number
  dimensions: ShippingDimensions
  serviceType?: ShippingServiceType
  deliveryDate?: Date
  insurance?: boolean
  signature?: boolean
  tracking?: boolean
}

export interface ShippingQuote {
  id: string
  provider: ShippingProvider
  service: ShippingService
  cost: number
  currency: string
  estimatedDays: number
  estimatedDeliveryDate: Date
  features: ShippingFeature[]
  restrictions?: string[]
  terms?: string
  validUntil: Date
}

export interface ShippingService {
  id: string
  name: string
  description: string
  type: ShippingServiceType
  maxWeight: number
  maxDimensions: ShippingDimensions
  deliveryDays: {
    min: number
    max: number
  }
  features: ShippingFeature[]
  coverage: ShippingCoverage[]
}

export enum ShippingServiceType {
  STANDARD = 'standard',
  EXPRESS = 'express',
  OVERNIGHT = 'overnight',
  SAME_DAY = 'same_day',
  ECONOMY = 'economy',
  PREMIUM = 'premium'
}

export enum ShippingFeature {
  TRACKING = 'tracking',
  INSURANCE = 'insurance',
  SIGNATURE_REQUIRED = 'signature_required',
  PROOF_OF_DELIVERY = 'proof_of_delivery',
  SMS_NOTIFICATIONS = 'sms_notifications',
  EMAIL_NOTIFICATIONS = 'email_notifications',
  DOOR_TO_DOOR = 'door_to_door',
  COLLECTION_POINT = 'collection_point',
  WEEKEND_DELIVERY = 'weekend_delivery',
  EVENING_DELIVERY = 'evening_delivery'
}

export enum ShippingCoverage {
  NATIONAL = 'national',
  PROVINCIAL = 'provincial',
  METRO = 'metro',
  RURAL = 'rural',
  INTERNATIONAL = 'international'
}

export enum ShippingProvider {
  COURIER_GUY = 'courier_guy',
  POSTNET = 'postnet',
  ARAMEX = 'aramex',
  DHL = 'dhl',
  FEDEX = 'fedex',
  SA_POST = 'sa_post',
  DAWN_WING = 'dawn_wing',
  FASTWAY = 'fastway'
}

export interface ShippingBookingRequest {
  quoteId: string
  reference: string
  fromAddress: ShippingAddress
  toAddress: ShippingAddress
  items: ShippingItem[]
  service: ShippingService
  specialInstructions?: string
  deliveryInstructions?: string
  collectionDate?: Date
  deliveryDate?: Date
  insurance?: {
    required: boolean
    value?: number
  }
  notifications?: {
    sms?: boolean
    email?: boolean
    whatsapp?: boolean
  }
}

export interface ShippingBooking {
  id: string
  trackingNumber: string
  reference: string
  provider: ShippingProvider
  service: ShippingService
  status: ShippingStatus
  fromAddress: ShippingAddress
  toAddress: ShippingAddress
  items: ShippingItem[]
  cost: number
  currency: string
  
  // Dates
  bookedAt: Date
  collectionDate?: Date
  estimatedDeliveryDate: Date
  actualDeliveryDate?: Date
  
  // Tracking
  trackingEvents: TrackingEvent[]
  currentLocation?: string
  
  // Documents
  waybill?: string
  label?: string
  invoice?: string
  
  // Insurance
  insured: boolean
  insuranceValue?: number
  
  // Metadata
  createdBy: string
  updatedAt: Date
}

export enum ShippingStatus {
  PENDING = 'pending',
  BOOKED = 'booked',
  COLLECTED = 'collected',
  IN_TRANSIT = 'in_transit',
  OUT_FOR_DELIVERY = 'out_for_delivery',
  DELIVERED = 'delivered',
  FAILED_DELIVERY = 'failed_delivery',
  RETURNED = 'returned',
  CANCELLED = 'cancelled',
  LOST = 'lost',
  DAMAGED = 'damaged'
}

export interface TrackingEvent {
  id: string
  timestamp: Date
  status: ShippingStatus
  location: string
  description: string
  signature?: string
  photo?: string
  notes?: string
}

export interface ShippingRate {
  provider: ShippingProvider
  service: ShippingServiceType
  weightFrom: number
  weightTo: number
  zone: string
  rate: number
  currency: string
  fuelSurcharge?: number
  vatRate?: number
}

export interface ShippingZone {
  id: string
  name: string
  description: string
  postalCodes: string[]
  provinces: string[]
  deliveryDays: number
  isRural: boolean
  surcharge?: number
}

// Provider-specific configurations
export interface CourierGuyConfig {
  username: string
  password: string
  accountNumber: string
  sandbox: boolean
}

export interface PostNetConfig {
  apiKey: string
  accountNumber: string
  sandbox: boolean
}

export interface AramexConfig {
  username: string
  password: string
  accountNumber: string
  accountPin: string
  accountEntity: string
  accountCountryCode: string
  sandbox: boolean
}

export interface ShippingProviderConfig {
  courierGuy?: CourierGuyConfig
  postnet?: PostNetConfig
  aramex?: AramexConfig
}

// API Response types
export interface ShippingResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  provider?: ShippingProvider
}

export interface QuoteResponse extends ShippingResponse<ShippingQuote[]> {
  requestId: string
  validUntil: Date
}

export interface BookingResponse extends ShippingResponse<ShippingBooking> {
  trackingNumber: string
  waybill?: string
  label?: string
}

export interface TrackingResponse extends ShippingResponse<TrackingEvent[]> {
  trackingNumber: string
  currentStatus: ShippingStatus
  estimatedDelivery?: Date
}

// Webhook types
export interface ShippingWebhook {
  provider: ShippingProvider
  event: ShippingWebhookEvent
  trackingNumber: string
  status: ShippingStatus
  timestamp: Date
  data: any
  signature?: string
}

export enum ShippingWebhookEvent {
  STATUS_UPDATE = 'status_update',
  DELIVERED = 'delivered',
  FAILED_DELIVERY = 'failed_delivery',
  RETURNED = 'returned',
  EXCEPTION = 'exception'
}

// South African specific types
export interface SAProvince {
  code: string
  name: string
  postalCodeRanges: Array<{
    from: string
    to: string
  }>
}

export interface SAPostalCode {
  code: string
  suburb: string
  city: string
  province: string
  isRural: boolean
  deliveryDays: number
}

// Shipping calculator types
export interface ShippingCalculatorRequest {
  fromPostalCode: string
  toPostalCode: string
  weight: number
  dimensions: ShippingDimensions
  value: number
  serviceType?: ShippingServiceType
}

export interface ShippingCalculatorResponse {
  quotes: Array<{
    provider: ShippingProvider
    service: string
    cost: number
    days: number
    features: ShippingFeature[]
  }>
  cheapest: ShippingQuote
  fastest: ShippingQuote
}

// Insurance types
export interface ShippingInsurance {
  required: boolean
  value: number
  premium: number
  coverage: string
  terms: string
}

// Collection point types
export interface CollectionPoint {
  id: string
  name: string
  address: ShippingAddress
  hours: {
    monday: string
    tuesday: string
    wednesday: string
    thursday: string
    friday: string
    saturday: string
    sunday: string
  }
  services: string[]
  maxPackageSize: ShippingDimensions
  maxPackageWeight: number
  distance?: number
}

// Shipping label types
export interface ShippingLabel {
  format: 'PDF' | 'PNG' | 'ZPL'
  size: 'A4' | 'A5' | '4x6' | '6x4'
  content: string // Base64 encoded
  url?: string
}

// Delivery options
export interface DeliveryOptions {
  timeSlots: Array<{
    id: string
    name: string
    startTime: string
    endTime: string
    cost: number
  }>
  instructions: {
    maxLength: number
    examples: string[]
  }
  specialServices: Array<{
    id: string
    name: string
    description: string
    cost: number
  }>
}

// All types and enums are already exported above
