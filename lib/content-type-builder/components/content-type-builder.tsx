'use client'

import React, { useState, useCallback } from 'react'
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent } from '@dnd-kit/core'
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { 
  Plus, 
  Save, 
  Eye, 
  Settings, 
  Trash2, 
  GripVertical,
  Type,
  Hash,
  Calendar,
  Image,
  Link,
  Code,
  Folder
} from 'lucide-react'
import { CustomField, FieldType, CreatePostTypeInput, PostType } from '../../posts/types'
import { FieldLibrary } from './field-library'
import { FieldEditor } from './field-editor'
import { FieldPreview } from './field-preview'
import { ContentTypeSettings } from './content-type-settings'
import { generateId } from '../../page-builder/utils'

interface ContentTypeBuilderProps {
  initialData?: PostType
  onSave?: (data: CreatePostTypeInput) => void
  onPreview?: (data: CreatePostTypeInput) => void
}

export function ContentTypeBuilder({ 
  initialData, 
  onSave, 
  onPreview 
}: ContentTypeBuilderProps) {
  const [contentType, setContentType] = useState<CreatePostTypeInput>({
    name: initialData?.name || '',
    label: initialData?.label || '',
    labelPlural: initialData?.labelPlural || '',
    description: initialData?.description || '',
    icon: initialData?.icon || '📄',
    isPublic: initialData?.isPublic ?? true,
    isHierarchical: initialData?.isHierarchical ?? false,
    hasArchive: initialData?.hasArchive ?? true,
    supportsTitle: initialData?.supportsTitle ?? true,
    supportsContent: initialData?.supportsContent ?? true,
    supportsExcerpt: initialData?.supportsExcerpt ?? false,
    supportsThumbnail: initialData?.supportsThumbnail ?? false,
    supportsComments: initialData?.supportsComments ?? false,
    supportsRevisions: initialData?.supportsRevisions ?? true,
    supportsPageBuilder: initialData?.supportsPageBuilder ?? false,
    menuPosition: initialData?.menuPosition,
    taxonomies: initialData?.taxonomies || [],
    customFields: initialData?.customFields || [],
    templates: initialData?.templates || [],
  })

  const [activeTab, setActiveTab] = useState<'fields' | 'settings' | 'preview'>('fields')
  const [selectedField, setSelectedField] = useState<CustomField | null>(null)
  const [draggedField, setDraggedField] = useState<CustomField | null>(null)
  const [isFieldEditorOpen, setIsFieldEditorOpen] = useState(false)

  const handleAddField = useCallback((fieldType: FieldType) => {
    const newField: CustomField = {
      id: generateId(),
      name: `field_${contentType.customFields?.length || 0}`,
      label: `New ${fieldType} Field`,
      type: fieldType,
      required: false,
      validation: [],
      settings: getDefaultFieldSettings(fieldType),
      position: contentType.customFields?.length || 0,
    }

    setContentType(prev => ({
      ...prev,
      customFields: [...(prev.customFields || []), newField]
    }))

    setSelectedField(newField)
    setIsFieldEditorOpen(true)
  }, [contentType.customFields])

  const handleUpdateField = useCallback((fieldId: string, updates: Partial<CustomField>) => {
    setContentType(prev => ({
      ...prev,
      customFields: prev.customFields?.map(field => 
        field.id === fieldId ? { ...field, ...updates } : field
      ) || []
    }))

    if (selectedField?.id === fieldId) {
      setSelectedField(prev => prev ? { ...prev, ...updates } : null)
    }
  }, [selectedField])

  const handleDeleteField = useCallback((fieldId: string) => {
    setContentType(prev => ({
      ...prev,
      customFields: prev.customFields?.filter(field => field.id !== fieldId) || []
    }))

    if (selectedField?.id === fieldId) {
      setSelectedField(null)
      setIsFieldEditorOpen(false)
    }
  }, [selectedField])

  const handleDragStart = (event: DragStartEvent) => {
    const field = contentType.customFields?.find(f => f.id === event.active.id)
    setDraggedField(field || null)
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    
    if (!over || active.id === over.id) {
      setDraggedField(null)
      return
    }

    const fields = contentType.customFields || []
    const oldIndex = fields.findIndex(field => field.id === active.id)
    const newIndex = fields.findIndex(field => field.id === over.id)

    if (oldIndex !== -1 && newIndex !== -1) {
      const newFields = [...fields]
      const [movedField] = newFields.splice(oldIndex, 1)
      newFields.splice(newIndex, 0, movedField)

      // Update positions
      const updatedFields = newFields.map((field, index) => ({
        ...field,
        position: index
      }))

      setContentType(prev => ({
        ...prev,
        customFields: updatedFields
      }))
    }

    setDraggedField(null)
  }

  const handleSave = () => {
    if (onSave) {
      onSave(contentType)
    }
  }

  const handlePreview = () => {
    if (onPreview) {
      onPreview(contentType)
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b bg-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Content Type Builder</h1>
            <p className="text-gray-600">
              {initialData ? `Editing: ${initialData.label}` : 'Create a new content type'}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={handlePreview}>
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>
            <Button onClick={handleSave}>
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Field Library */}
        <div className="w-80 border-r bg-gray-50">
          <FieldLibrary onAddField={handleAddField} />
        </div>

        {/* Center - Main Editor */}
        <div className="flex-1 flex flex-col">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="flex-1 flex flex-col">
            <div className="border-b bg-white px-4">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="fields">Custom Fields</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
                <TabsTrigger value="preview">Preview</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="fields" className="flex-1 p-4 overflow-hidden">
              <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
                <div className="h-full flex flex-col">
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold mb-2">Custom Fields</h3>
                    <p className="text-gray-600 text-sm">
                      Drag and drop fields to reorder them. Click on a field to edit its properties.
                    </p>
                  </div>

                  <ScrollArea className="flex-1">
                    <SortableContext 
                      items={contentType.customFields?.map(f => f.id) || []}
                      strategy={verticalListSortingStrategy}
                    >
                      <div className="space-y-2">
                        {contentType.customFields?.map((field) => (
                          <FieldPreview
                            key={field.id}
                            field={field}
                            isSelected={selectedField?.id === field.id}
                            onSelect={() => {
                              setSelectedField(field)
                              setIsFieldEditorOpen(true)
                            }}
                            onDelete={() => handleDeleteField(field.id)}
                          />
                        ))}
                      </div>
                    </SortableContext>

                    {(!contentType.customFields || contentType.customFields.length === 0) && (
                      <div className="text-center py-12 text-gray-500">
                        <Folder className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p className="text-lg font-medium mb-2">No custom fields yet</p>
                        <p className="text-sm">
                          Add fields from the library on the left to get started
                        </p>
                      </div>
                    )}
                  </ScrollArea>
                </div>

                <DragOverlay>
                  {draggedField && (
                    <div className="bg-white border rounded-lg p-3 shadow-lg">
                      <div className="flex items-center space-x-2">
                        <GripVertical className="h-4 w-4 text-gray-400" />
                        <getFieldIcon type={draggedField.type} />
                        <span className="font-medium">{draggedField.label}</span>
                        <Badge variant="secondary">{draggedField.type}</Badge>
                      </div>
                    </div>
                  )}
                </DragOverlay>
              </DndContext>
            </TabsContent>

            <TabsContent value="settings" className="flex-1 p-4">
              <ContentTypeSettings
                contentType={contentType}
                onChange={setContentType}
              />
            </TabsContent>

            <TabsContent value="preview" className="flex-1 p-4">
              <div className="h-full">
                <h3 className="text-lg font-semibold mb-4">Content Type Preview</h3>
                <div className="bg-white border rounded-lg p-6">
                  <div className="mb-6">
                    <h4 className="text-xl font-bold mb-2">{contentType.label || 'Untitled Content Type'}</h4>
                    <p className="text-gray-600">{contentType.description || 'No description provided'}</p>
                  </div>

                  <div className="space-y-4">
                    {contentType.supportsTitle && (
                      <div>
                        <Label>Title</Label>
                        <Input placeholder="Enter title..." disabled />
                      </div>
                    )}

                    {contentType.supportsContent && (
                      <div>
                        <Label>Content</Label>
                        <Textarea placeholder="Enter content..." rows={4} disabled />
                      </div>
                    )}

                    {contentType.supportsExcerpt && (
                      <div>
                        <Label>Excerpt</Label>
                        <Textarea placeholder="Enter excerpt..." rows={2} disabled />
                      </div>
                    )}

                    {contentType.customFields?.map((field) => (
                      <div key={field.id}>
                        <Label>
                          {field.label}
                          {field.required && <span className="text-red-500 ml-1">*</span>}
                        </Label>
                        <div className="mt-1">
                          <PreviewFieldInput field={field} />
                        </div>
                        {field.description && (
                          <p className="text-sm text-gray-500 mt-1">{field.description}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Sidebar - Field Editor */}
        {isFieldEditorOpen && selectedField && (
          <div className="w-96 border-l bg-white">
            <FieldEditor
              field={selectedField}
              onUpdate={(updates) => handleUpdateField(selectedField.id, updates)}
              onClose={() => setIsFieldEditorOpen(false)}
            />
          </div>
        )}
      </div>
    </div>
  )
}

// Helper function to get default field settings
function getDefaultFieldSettings(fieldType: FieldType): any {
  const defaults: Record<FieldType, any> = {
    text: { placeholder: 'Enter text...' },
    textarea: { rows: 4, placeholder: 'Enter text...' },
    richtext: {},
    number: { min: 0, step: 1 },
    email: { placeholder: '<EMAIL>' },
    url: { placeholder: 'https://example.com' },
    password: {},
    date: {},
    datetime: {},
    time: {},
    boolean: {},
    select: { options: [{ label: 'Option 1', value: 'option1' }] },
    multiselect: { options: [{ label: 'Option 1', value: 'option1' }], multiple: true },
    radio: { options: [{ label: 'Option 1', value: 'option1' }] },
    checkbox: { options: [{ label: 'Option 1', value: 'option1' }] },
    image: { allowedFileTypes: ['image/jpeg', 'image/png', 'image/webp'] },
    gallery: { allowedFileTypes: ['image/jpeg', 'image/png', 'image/webp'], multiple: true },
    file: {},
    color: {},
    range: { min: 0, max: 100, step: 1 },
    relationship: { relationshipType: 'one-to-many' },
    json: {},
    code: { codeLanguage: 'javascript' },
    repeater: { subFields: [] },
    group: { subFields: [] },
  }

  return defaults[fieldType] || {}
}

// Helper function to get field icon
function getFieldIcon({ type }: { type: FieldType }) {
  const icons: Record<FieldType, React.ComponentType<any>> = {
    text: Type,
    textarea: Type,
    richtext: Type,
    number: Hash,
    email: Type,
    url: Type,
    password: Type,
    date: Calendar,
    datetime: Calendar,
    time: Calendar,
    boolean: Type,
    select: Type,
    multiselect: Type,
    radio: Type,
    checkbox: Type,
    image: Image,
    gallery: Image,
    file: Type,
    color: Type,
    range: Type,
    relationship: Link,
    json: Code,
    code: Code,
    repeater: Folder,
    group: Folder,
  }

  const Icon = icons[type] || Type
  return <Icon className="h-4 w-4" />
}

// Helper component for preview field inputs
function PreviewFieldInput({ field }: { field: CustomField }) {
  switch (field.type) {
    case 'text':
    case 'email':
    case 'url':
    case 'password':
      return <Input placeholder={field.settings.placeholder} disabled />
    
    case 'textarea':
      return <Textarea placeholder={field.settings.placeholder} rows={field.settings.rows || 4} disabled />
    
    case 'number':
    case 'range':
      return <Input type="number" min={field.settings.min} max={field.settings.max} step={field.settings.step} disabled />
    
    case 'date':
      return <Input type="date" disabled />
    
    case 'datetime':
      return <Input type="datetime-local" disabled />
    
    case 'time':
      return <Input type="time" disabled />
    
    case 'boolean':
      return <Switch disabled />
    
    case 'color':
      return <Input type="color" disabled />
    
    case 'select':
      return (
        <select className="w-full p-2 border rounded" disabled>
          <option>Select an option...</option>
          {field.settings.options?.map((option: any, index: number) => (
            <option key={index} value={option.value}>{option.label}</option>
          ))}
        </select>
      )
    
    default:
      return <Input placeholder={`${field.type} field`} disabled />
  }
}
