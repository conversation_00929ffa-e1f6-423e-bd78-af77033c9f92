'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { 
  Search,
  Type,
  AlignLeft,
  Edit3,
  Hash,
  Mail,
  Link,
  Calendar,
  Clock,
  ToggleLeft,
  ChevronDown,
  List,
  Circle,
  Square,
  Image,
  Images,
  Paperclip,
  Palette,
  Sliders,
  Link2,
  Code2,
  Code,
  Copy,
  Folder
} from 'lucide-react'
import { FieldType } from '../../posts/types'
import { getAllFieldTypes } from '../integration/page-builder-fields'

// Helper function to map icon names to components
function getIconComponent(iconName: string): React.ComponentType<any> {
  const iconMap: Record<string, React.ComponentType<any>> = {
    'Type': Type,
    'AlignLeft': AlignLeft,
    'Edit3': Edit3,
    'Hash': Hash,
    'Mail': Mail,
    'Link': Link,
    'Calendar': Calendar,
    'Clock': Clock,
    'ToggleLeft': ToggleLeft,
    'ChevronDown': ChevronDown,
    'List': List,
    'Circle': Circle,
    'Square': Square,
    'Image': Image,
    'Images': Images,
    'Paperclip': Paperclip,
    'Palette': Palette,
    'Sliders': Sliders,
    'Link2': Link2,
    'Code2': Code2,
    'Code': Code,
    'Copy': Copy,
    'Folder': Folder,
    'Move': Sliders, // Fallback for Move icon
    'Layers': Copy, // Fallback for Layers icon
    'Zap': Copy, // Fallback for Zap icon
    'Star': Circle, // Fallback for Star icon
  }

  return iconMap[iconName] || Type
}

interface FieldLibraryProps {
  onAddField: (fieldType: FieldType) => void
}

interface FieldTypeInfo {
  type: FieldType
  label: string
  description: string
  icon: React.ComponentType<any>
  category: 'basic' | 'content' | 'choice' | 'media' | 'advanced' | 'design' | 'ui'
  tags: string[]
}

// Get enhanced field types from integration layer
const enhancedFieldTypes = getAllFieldTypes()

const fieldTypes: FieldTypeInfo[] = enhancedFieldTypes.map(fieldType => ({
  type: fieldType.type as FieldType,
  label: fieldType.label,
  description: fieldType.description,
  icon: getIconComponent(fieldType.icon),
  category: fieldType.category as 'basic' | 'content' | 'choice' | 'media' | 'advanced' | 'design' | 'ui',
  tags: [fieldType.type, fieldType.category, ...fieldType.label.toLowerCase().split(' ')]
}))

export function FieldLibrary({ onAddField }: FieldLibraryProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  const filteredFields = fieldTypes.filter(field => {
    const matchesSearch = searchQuery === '' || 
      field.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      field.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      field.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesCategory = selectedCategory === 'all' || field.category === selectedCategory

    return matchesSearch && matchesCategory
  })

  const categories = [
    { value: 'all', label: 'All Fields', count: fieldTypes.length },
    { value: 'basic', label: 'Basic', count: fieldTypes.filter(f => f.category === 'basic').length },
    { value: 'content', label: 'Content', count: fieldTypes.filter(f => f.category === 'content').length },
    { value: 'choice', label: 'Choice', count: fieldTypes.filter(f => f.category === 'choice').length },
    { value: 'media', label: 'Media', count: fieldTypes.filter(f => f.category === 'media').length },
    { value: 'design', label: 'Design', count: fieldTypes.filter(f => f.category === 'design').length },
    { value: 'ui', label: 'UI', count: fieldTypes.filter(f => f.category === 'ui').length },
    { value: 'advanced', label: 'Advanced', count: fieldTypes.filter(f => f.category === 'advanced').length },
  ]

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b bg-white">
        <h3 className="text-lg font-semibold mb-3">Field Library</h3>
        
        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search fields..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
          <TabsList className="grid w-full grid-cols-2 h-auto">
            <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
            <TabsTrigger value="basic" className="text-xs">Basic</TabsTrigger>
          </TabsList>
          <div className="mt-2 grid grid-cols-3 gap-1">
            <TabsTrigger value="content" className="text-xs h-8">Content</TabsTrigger>
            <TabsTrigger value="choice" className="text-xs h-8">Choice</TabsTrigger>
            <TabsTrigger value="media" className="text-xs h-8">Media</TabsTrigger>
            <TabsTrigger value="design" className="text-xs h-8">Design</TabsTrigger>
            <TabsTrigger value="ui" className="text-xs h-8">UI</TabsTrigger>
            <TabsTrigger value="advanced" className="text-xs h-8">Advanced</TabsTrigger>
          </div>
        </Tabs>
      </div>

      {/* Field List */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-2">
          {filteredFields.map((field) => (
            <FieldLibraryItem
              key={field.type}
              field={field}
              onAdd={() => onAddField(field.type)}
            />
          ))}

          {filteredFields.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No fields found</p>
              <p className="text-xs">Try adjusting your search or category filter</p>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="p-4 border-t bg-gray-50">
        <p className="text-xs text-gray-500 text-center">
          {filteredFields.length} of {fieldTypes.length} fields
        </p>
      </div>
    </div>
  )
}

interface FieldLibraryItemProps {
  field: FieldTypeInfo
  onAdd: () => void
}

function FieldLibraryItem({ field, onAdd }: FieldLibraryItemProps) {
  const Icon = field.icon

  return (
    <div className="group border rounded-lg p-3 hover:border-blue-300 hover:bg-blue-50 transition-colors cursor-pointer">
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 p-2 bg-gray-100 rounded-md group-hover:bg-blue-100 transition-colors">
          <Icon className="h-4 w-4 text-gray-600 group-hover:text-blue-600" />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h4 className="text-sm font-medium text-gray-900 truncate">
              {field.label}
            </h4>
            <Badge variant="secondary" className="text-xs">
              {field.category}
            </Badge>
          </div>
          
          <p className="text-xs text-gray-500 mb-2 line-clamp-2">
            {field.description}
          </p>
          
          <div className="flex items-center justify-between">
            <div className="flex flex-wrap gap-1">
              {field.tags.slice(0, 2).map((tag) => (
                <span
                  key={tag}
                  className="inline-block px-1.5 py-0.5 bg-gray-100 text-gray-600 text-xs rounded"
                >
                  {tag}
                </span>
              ))}
              {field.tags.length > 2 && (
                <span className="text-xs text-gray-400">
                  +{field.tags.length - 2}
                </span>
              )}
            </div>
            
            <Button
              size="sm"
              variant="ghost"
              onClick={onAdd}
              className="opacity-0 group-hover:opacity-100 transition-opacity h-6 px-2 text-xs"
            >
              Add
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
