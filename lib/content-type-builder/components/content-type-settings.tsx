'use client'

import React from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { 
  Settings, 
  FileText, 
  Eye, 
  MessageSquare, 
  Archive, 
  Users, 
  Tag,
  Plus,
  X
} from 'lucide-react'
import { CreatePostTypeInput } from '../../posts/types'

interface ContentTypeSettingsProps {
  contentType: CreatePostTypeInput
  onChange: (updates: Partial<CreatePostTypeInput>) => void
}

export function ContentTypeSettings({ contentType, onChange }: ContentTypeSettingsProps) {
  const handleInputChange = (field: keyof CreatePostTypeInput, value: any) => {
    onChange({ [field]: value })
  }

  const handleTaxonomyAdd = (taxonomy: string) => {
    if (taxonomy && !contentType.taxonomies?.includes(taxonomy)) {
      const newTaxonomies = [...(contentType.taxonomies || []), taxonomy]
      onChange({ taxonomies: newTaxonomies })
    }
  }

  const handleTaxonomyRemove = (taxonomy: string) => {
    const newTaxonomies = contentType.taxonomies?.filter(t => t !== taxonomy) || []
    onChange({ taxonomies: newTaxonomies })
  }

  const handleTemplateAdd = (template: string) => {
    if (template && !contentType.templates?.includes(template)) {
      const newTemplates = [...(contentType.templates || []), template]
      onChange({ templates: newTemplates })
    }
  }

  const handleTemplateRemove = (template: string) => {
    const newTemplates = contentType.templates?.filter(t => t !== template) || []
    onChange({ templates: newTemplates })
  }

  return (
    <ScrollArea className="h-full">
      <div className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Basic Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="content-type-name">Name</Label>
                <Input
                  id="content-type-name"
                  value={contentType.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="post_type_name"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Used in URLs and code. Use lowercase with underscores.
                </p>
              </div>

              <div>
                <Label htmlFor="content-type-icon">Icon</Label>
                <Input
                  id="content-type-icon"
                  value={contentType.icon || ''}
                  onChange={(e) => handleInputChange('icon', e.target.value)}
                  placeholder="📄"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Emoji or icon identifier
                </p>
              </div>
            </div>

            <div>
              <Label htmlFor="content-type-label">Label (Singular)</Label>
              <Input
                id="content-type-label"
                value={contentType.label}
                onChange={(e) => handleInputChange('label', e.target.value)}
                placeholder="Post"
              />
            </div>

            <div>
              <Label htmlFor="content-type-label-plural">Label (Plural)</Label>
              <Input
                id="content-type-label-plural"
                value={contentType.labelPlural}
                onChange={(e) => handleInputChange('labelPlural', e.target.value)}
                placeholder="Posts"
              />
            </div>

            <div>
              <Label htmlFor="content-type-description">Description</Label>
              <Textarea
                id="content-type-description"
                value={contentType.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe what this content type is used for..."
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="content-type-menu-position">Menu Position</Label>
              <Input
                id="content-type-menu-position"
                type="number"
                value={contentType.menuPosition || ''}
                onChange={(e) => handleInputChange('menuPosition', parseInt(e.target.value) || undefined)}
                placeholder="5"
                min="1"
                max="100"
              />
              <p className="text-xs text-gray-500 mt-1">
                Position in admin menu (lower numbers appear first)
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Visibility & Access */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Eye className="h-5 w-5" />
              <span>Visibility & Access</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="is-public">Public</Label>
                <p className="text-sm text-gray-500">
                  Make this content type visible to the public
                </p>
              </div>
              <Switch
                id="is-public"
                checked={contentType.isPublic ?? true}
                onCheckedChange={(checked) => handleInputChange('isPublic', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="is-hierarchical">Hierarchical</Label>
                <p className="text-sm text-gray-500">
                  Allow parent-child relationships (like pages)
                </p>
              </div>
              <Switch
                id="is-hierarchical"
                checked={contentType.isHierarchical ?? false}
                onCheckedChange={(checked) => handleInputChange('isHierarchical', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="has-archive">Has Archive</Label>
                <p className="text-sm text-gray-500">
                  Create an archive page for this content type
                </p>
              </div>
              <Switch
                id="has-archive"
                checked={contentType.hasArchive ?? true}
                onCheckedChange={(checked) => handleInputChange('hasArchive', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Content Features */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Content Features</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="supports-title">Title</Label>
                <Switch
                  id="supports-title"
                  checked={contentType.supportsTitle ?? true}
                  onCheckedChange={(checked) => handleInputChange('supportsTitle', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="supports-content">Content</Label>
                <Switch
                  id="supports-content"
                  checked={contentType.supportsContent ?? true}
                  onCheckedChange={(checked) => handleInputChange('supportsContent', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="supports-excerpt">Excerpt</Label>
                <Switch
                  id="supports-excerpt"
                  checked={contentType.supportsExcerpt ?? false}
                  onCheckedChange={(checked) => handleInputChange('supportsExcerpt', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="supports-thumbnail">Featured Image</Label>
                <Switch
                  id="supports-thumbnail"
                  checked={contentType.supportsThumbnail ?? false}
                  onCheckedChange={(checked) => handleInputChange('supportsThumbnail', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="supports-comments">Comments</Label>
                <Switch
                  id="supports-comments"
                  checked={contentType.supportsComments ?? false}
                  onCheckedChange={(checked) => handleInputChange('supportsComments', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="supports-revisions">Revisions</Label>
                <Switch
                  id="supports-revisions"
                  checked={contentType.supportsRevisions ?? true}
                  onCheckedChange={(checked) => handleInputChange('supportsRevisions', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="supports-page-builder">Page Builder</Label>
                <Switch
                  id="supports-page-builder"
                  checked={contentType.supportsPageBuilder ?? false}
                  onCheckedChange={(checked) => handleInputChange('supportsPageBuilder', checked)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Taxonomies */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Tag className="h-5 w-5" />
              <span>Taxonomies</span>
            </CardTitle>
            <p className="text-sm text-gray-500">
              Categories, tags, and other classification systems
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              {contentType.taxonomies?.map((taxonomy) => (
                <Badge key={taxonomy} variant="secondary" className="flex items-center space-x-1">
                  <span>{taxonomy}</span>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleTaxonomyRemove(taxonomy)}
                    className="h-4 w-4 p-0 hover:bg-transparent"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>

            <div className="flex space-x-2">
              <Input
                placeholder="Add taxonomy (e.g., category, tag)"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleTaxonomyAdd(e.currentTarget.value)
                    e.currentTarget.value = ''
                  }
                }}
              />
              <Button
                variant="outline"
                onClick={() => {
                  const input = document.querySelector('input[placeholder*="taxonomy"]') as HTMLInputElement
                  if (input?.value) {
                    handleTaxonomyAdd(input.value)
                    input.value = ''
                  }
                }}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            <div className="text-sm text-gray-500">
              <p className="font-medium mb-1">Common taxonomies:</p>
              <div className="flex flex-wrap gap-1">
                {['category', 'tag', 'author', 'status', 'type'].map((taxonomy) => (
                  <Button
                    key={taxonomy}
                    size="sm"
                    variant="ghost"
                    onClick={() => handleTaxonomyAdd(taxonomy)}
                    className="h-6 px-2 text-xs"
                    disabled={contentType.taxonomies?.includes(taxonomy)}
                  >
                    {taxonomy}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Templates */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Archive className="h-5 w-5" />
              <span>Templates</span>
            </CardTitle>
            <p className="text-sm text-gray-500">
              Custom templates for displaying this content type
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              {contentType.templates?.map((template) => (
                <Badge key={template} variant="secondary" className="flex items-center space-x-1">
                  <span>{template}</span>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleTemplateRemove(template)}
                    className="h-4 w-4 p-0 hover:bg-transparent"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>

            <div className="flex space-x-2">
              <Input
                placeholder="Add template (e.g., single-post, archive-post)"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleTemplateAdd(e.currentTarget.value)
                    e.currentTarget.value = ''
                  }
                }}
              />
              <Button
                variant="outline"
                onClick={() => {
                  const input = document.querySelector('input[placeholder*="template"]') as HTMLInputElement
                  if (input?.value) {
                    handleTemplateAdd(input.value)
                    input.value = ''
                  }
                }}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            <div className="text-sm text-gray-500">
              <p className="font-medium mb-1">Suggested templates:</p>
              <div className="flex flex-wrap gap-1">
                {[
                  `single-${contentType.name}`,
                  `archive-${contentType.name}`,
                  `taxonomy-${contentType.name}`,
                  `page-${contentType.name}`
                ].map((template) => (
                  <Button
                    key={template}
                    size="sm"
                    variant="ghost"
                    onClick={() => handleTemplateAdd(template)}
                    className="h-6 px-2 text-xs"
                    disabled={contentType.templates?.includes(template)}
                  >
                    {template}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ScrollArea>
  )
}
