'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  X, 
  Plus, 
  Trash2, 
  Settings, 
  Eye, 
  Code,
  AlertCircle,
  Info
} from 'lucide-react'
import { CustomField, FieldOption, ValidationRule, ConditionalRule } from '../../posts/types'

interface FieldEditorProps {
  field: CustomField
  onUpdate: (updates: Partial<CustomField>) => void
  onClose: () => void
}

export function FieldEditor({ field, onUpdate, onClose }: FieldEditorProps) {
  const [localField, setLocalField] = useState<CustomField>(field)
  const [activeTab, setActiveTab] = useState<'general' | 'validation' | 'conditional' | 'advanced'>('general')

  useEffect(() => {
    setLocalField(field)
  }, [field])

  const handleUpdate = (updates: Partial<CustomField>) => {
    const updatedField = { ...localField, ...updates }
    setLocalField(updatedField)
    onUpdate(updates)
  }

  const handleSettingsUpdate = (key: string, value: any) => {
    const newSettings = { ...localField.settings, [key]: value }
    handleUpdate({ settings: newSettings })
  }

  const handleAddOption = () => {
    const currentOptions = localField.settings.options || []
    const newOption: FieldOption = {
      label: `Option ${currentOptions.length + 1}`,
      value: `option${currentOptions.length + 1}`
    }
    handleSettingsUpdate('options', [...currentOptions, newOption])
  }

  const handleUpdateOption = (index: number, updates: Partial<FieldOption>) => {
    const currentOptions = localField.settings.options || []
    const newOptions = currentOptions.map((option, i) => 
      i === index ? { ...option, ...updates } : option
    )
    handleSettingsUpdate('options', newOptions)
  }

  const handleRemoveOption = (index: number) => {
    const currentOptions = localField.settings.options || []
    const newOptions = currentOptions.filter((_, i) => i !== index)
    handleSettingsUpdate('options', newOptions)
  }

  const handleAddValidationRule = () => {
    const newRule: ValidationRule = {
      type: 'required',
      message: 'This field is required'
    }
    handleUpdate({ validation: [...localField.validation, newRule] })
  }

  const handleUpdateValidationRule = (index: number, updates: Partial<ValidationRule>) => {
    const newRules = localField.validation.map((rule, i) => 
      i === index ? { ...rule, ...updates } : rule
    )
    handleUpdate({ validation: newRules })
  }

  const handleRemoveValidationRule = (index: number) => {
    const newRules = localField.validation.filter((_, i) => i !== index)
    handleUpdate({ validation: newRules })
  }

  const needsOptions = ['select', 'multiselect', 'radio', 'checkbox'].includes(localField.type)
  const isAdvancedField = ['relationship', 'repeater', 'group', 'code', 'json'].includes(localField.type)

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b bg-white">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">Edit Field</h3>
            <div className="flex items-center space-x-2 mt-1">
              <Badge variant="secondary">{localField.type}</Badge>
              <span className="text-sm text-gray-500">{localField.name}</span>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="h-full flex flex-col">
          <div className="border-b bg-white px-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="validation">Validation</TabsTrigger>
              <TabsTrigger value="conditional">Conditional</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>
          </div>

          <ScrollArea className="flex-1">
            <div className="p-4">
              <TabsContent value="general" className="space-y-4 mt-0">
                {/* Basic Properties */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Basic Properties</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="field-name">Field Name</Label>
                      <Input
                        id="field-name"
                        value={localField.name}
                        onChange={(e) => handleUpdate({ name: e.target.value })}
                        placeholder="field_name"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Used in code and database. Use lowercase with underscores.
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="field-label">Label</Label>
                      <Input
                        id="field-label"
                        value={localField.label}
                        onChange={(e) => handleUpdate({ label: e.target.value })}
                        placeholder="Field Label"
                      />
                    </div>

                    <div>
                      <Label htmlFor="field-description">Description</Label>
                      <Textarea
                        id="field-description"
                        value={localField.description || ''}
                        onChange={(e) => handleUpdate({ description: e.target.value })}
                        placeholder="Optional description or help text"
                        rows={2}
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="field-required"
                        checked={localField.required}
                        onCheckedChange={(checked) => handleUpdate({ required: checked })}
                      />
                      <Label htmlFor="field-required">Required field</Label>
                    </div>
                  </CardContent>
                </Card>

                {/* Field Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Field Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Common Settings */}
                    <div>
                      <Label htmlFor="field-placeholder">Placeholder</Label>
                      <Input
                        id="field-placeholder"
                        value={localField.settings.placeholder || ''}
                        onChange={(e) => handleSettingsUpdate('placeholder', e.target.value)}
                        placeholder="Enter placeholder text..."
                      />
                    </div>

                    <div>
                      <Label htmlFor="field-help">Help Text</Label>
                      <Input
                        id="field-help"
                        value={localField.settings.helpText || ''}
                        onChange={(e) => handleSettingsUpdate('helpText', e.target.value)}
                        placeholder="Additional help text"
                      />
                    </div>

                    {/* Type-specific Settings */}
                    {(localField.type === 'text' || localField.type === 'textarea') && (
                      <>
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <Label htmlFor="field-min-length">Min Length</Label>
                            <Input
                              id="field-min-length"
                              type="number"
                              value={localField.settings.minLength || ''}
                              onChange={(e) => handleSettingsUpdate('minLength', parseInt(e.target.value) || undefined)}
                            />
                          </div>
                          <div>
                            <Label htmlFor="field-max-length">Max Length</Label>
                            <Input
                              id="field-max-length"
                              type="number"
                              value={localField.settings.maxLength || ''}
                              onChange={(e) => handleSettingsUpdate('maxLength', parseInt(e.target.value) || undefined)}
                            />
                          </div>
                        </div>
                      </>
                    )}

                    {localField.type === 'textarea' && (
                      <div>
                        <Label htmlFor="field-rows">Rows</Label>
                        <Input
                          id="field-rows"
                          type="number"
                          value={localField.settings.rows || 4}
                          onChange={(e) => handleSettingsUpdate('rows', parseInt(e.target.value) || 4)}
                          min="1"
                          max="20"
                        />
                      </div>
                    )}

                    {(localField.type === 'number' || localField.type === 'range') && (
                      <>
                        <div className="grid grid-cols-3 gap-2">
                          <div>
                            <Label htmlFor="field-min">Min</Label>
                            <Input
                              id="field-min"
                              type="number"
                              value={localField.settings.min || ''}
                              onChange={(e) => handleSettingsUpdate('min', parseFloat(e.target.value) || undefined)}
                            />
                          </div>
                          <div>
                            <Label htmlFor="field-max">Max</Label>
                            <Input
                              id="field-max"
                              type="number"
                              value={localField.settings.max || ''}
                              onChange={(e) => handleSettingsUpdate('max', parseFloat(e.target.value) || undefined)}
                            />
                          </div>
                          <div>
                            <Label htmlFor="field-step">Step</Label>
                            <Input
                              id="field-step"
                              type="number"
                              value={localField.settings.step || 1}
                              onChange={(e) => handleSettingsUpdate('step', parseFloat(e.target.value) || 1)}
                              step="0.01"
                            />
                          </div>
                        </div>
                      </>
                    )}

                    {needsOptions && (
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <Label>Options</Label>
                          <Button size="sm" variant="outline" onClick={handleAddOption}>
                            <Plus className="h-3 w-3 mr-1" />
                            Add Option
                          </Button>
                        </div>
                        <div className="space-y-2">
                          {(localField.settings.options || []).map((option, index) => (
                            <div key={index} className="flex items-center space-x-2 p-2 border rounded">
                              <Input
                                placeholder="Label"
                                value={option.label}
                                onChange={(e) => handleUpdateOption(index, { label: e.target.value })}
                                className="flex-1"
                              />
                              <Input
                                placeholder="Value"
                                value={option.value}
                                onChange={(e) => handleUpdateOption(index, { value: e.target.value })}
                                className="flex-1"
                              />
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleRemoveOption(index)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {localField.type === 'relationship' && (
                      <>
                        <div>
                          <Label htmlFor="field-relationship-type">Relationship Type</Label>
                          <Select
                            value={localField.settings.relationshipType || 'one-to-many'}
                            onValueChange={(value) => handleSettingsUpdate('relationshipType', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="one-to-one">One to One</SelectItem>
                              <SelectItem value="one-to-many">One to Many</SelectItem>
                              <SelectItem value="many-to-many">Many to Many</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label htmlFor="field-related-post-type">Related Content Type</Label>
                          <Input
                            id="field-related-post-type"
                            value={localField.settings.relatedPostType || ''}
                            onChange={(e) => handleSettingsUpdate('relatedPostType', e.target.value)}
                            placeholder="post, page, product, etc."
                          />
                        </div>
                      </>
                    )}

                    {localField.type === 'code' && (
                      <div>
                        <Label htmlFor="field-code-language">Language</Label>
                        <Select
                          value={localField.settings.codeLanguage || 'javascript'}
                          onValueChange={(value) => handleSettingsUpdate('codeLanguage', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="javascript">JavaScript</SelectItem>
                            <SelectItem value="typescript">TypeScript</SelectItem>
                            <SelectItem value="html">HTML</SelectItem>
                            <SelectItem value="css">CSS</SelectItem>
                            <SelectItem value="json">JSON</SelectItem>
                            <SelectItem value="markdown">Markdown</SelectItem>
                            <SelectItem value="sql">SQL</SelectItem>
                            <SelectItem value="python">Python</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    {(localField.type === 'image' || localField.type === 'gallery' || localField.type === 'file') && (
                      <>
                        <div>
                          <Label htmlFor="field-allowed-types">Allowed File Types</Label>
                          <Input
                            id="field-allowed-types"
                            value={(localField.settings.allowedFileTypes || []).join(', ')}
                            onChange={(e) => handleSettingsUpdate('allowedFileTypes', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                            placeholder="image/jpeg, image/png, image/webp"
                          />
                        </div>

                        <div>
                          <Label htmlFor="field-max-file-size">Max File Size (MB)</Label>
                          <Input
                            id="field-max-file-size"
                            type="number"
                            value={localField.settings.maxFileSize || ''}
                            onChange={(e) => handleSettingsUpdate('maxFileSize', parseFloat(e.target.value) || undefined)}
                            min="0.1"
                            step="0.1"
                          />
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="validation" className="space-y-4 mt-0">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm">Validation Rules</CardTitle>
                      <Button size="sm" variant="outline" onClick={handleAddValidationRule}>
                        <Plus className="h-3 w-3 mr-1" />
                        Add Rule
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {localField.validation.length === 0 ? (
                      <div className="text-center py-4 text-gray-500">
                        <AlertCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No validation rules</p>
                        <p className="text-xs">Add rules to validate user input</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {localField.validation.map((rule, index) => (
                          <div key={index} className="p-3 border rounded space-y-2">
                            <div className="flex items-center justify-between">
                              <Select
                                value={rule.type}
                                onValueChange={(value) => handleUpdateValidationRule(index, { type: value as any })}
                              >
                                <SelectTrigger className="w-40">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="required">Required</SelectItem>
                                  <SelectItem value="min">Minimum</SelectItem>
                                  <SelectItem value="max">Maximum</SelectItem>
                                  <SelectItem value="pattern">Pattern</SelectItem>
                                  <SelectItem value="email">Email</SelectItem>
                                  <SelectItem value="url">URL</SelectItem>
                                  <SelectItem value="unique">Unique</SelectItem>
                                  <SelectItem value="custom">Custom</SelectItem>
                                </SelectContent>
                              </Select>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleRemoveValidationRule(index)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>

                            {(rule.type === 'min' || rule.type === 'max' || rule.type === 'pattern') && (
                              <Input
                                placeholder={rule.type === 'pattern' ? 'Regular expression' : 'Value'}
                                value={rule.value || ''}
                                onChange={(e) => handleUpdateValidationRule(index, { value: e.target.value })}
                              />
                            )}

                            <Input
                              placeholder="Error message"
                              value={rule.message}
                              onChange={(e) => handleUpdateValidationRule(index, { message: e.target.value })}
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="conditional" className="space-y-4 mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Conditional Logic</CardTitle>
                    <p className="text-xs text-gray-500">
                      Show or hide this field based on other field values
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-4 text-gray-500">
                      <Info className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Conditional logic coming soon</p>
                      <p className="text-xs">This feature will be available in a future update</p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="advanced" className="space-y-4 mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Advanced Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="field-width">Field Width</Label>
                      <Select
                        value={localField.settings.width || 'full'}
                        onValueChange={(value) => handleSettingsUpdate('width', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="full">Full Width</SelectItem>
                          <SelectItem value="half">Half Width</SelectItem>
                          <SelectItem value="third">One Third</SelectItem>
                          <SelectItem value="quarter">One Quarter</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="field-class">CSS Class</Label>
                      <Input
                        id="field-class"
                        value={localField.settings.className || ''}
                        onChange={(e) => handleSettingsUpdate('className', e.target.value)}
                        placeholder="custom-field-class"
                      />
                    </div>

                    <div>
                      <Label htmlFor="field-group">Field Group</Label>
                      <Input
                        id="field-group"
                        value={localField.group || ''}
                        onChange={(e) => handleUpdate({ group: e.target.value })}
                        placeholder="Group name (optional)"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </div>
          </ScrollArea>
        </Tabs>
      </div>
    </div>
  )
}
