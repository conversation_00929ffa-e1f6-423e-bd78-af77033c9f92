'use client'

import React from 'react'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  GripVertical, 
  Trash2, 
  Edit,
  Type,
  AlignLeft,
  Edit3,
  Hash,
  Mail,
  Link,
  Calendar,
  Clock,
  ToggleLeft,
  ChevronDown,
  List,
  Circle,
  Square,
  Image,
  Images,
  Paperclip,
  Palette,
  Sliders,
  Link2,
  Code2,
  Code,
  Copy,
  Folder,
  AlertTriangle
} from 'lucide-react'
import { CustomField, FieldType } from '../../posts/types'

interface FieldPreviewProps {
  field: CustomField
  isSelected: boolean
  onSelect: () => void
  onDelete: () => void
}

export function FieldPreview({ field, isSelected, onSelect, onDelete }: FieldPreviewProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: field.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const Icon = getFieldIcon(field.type)

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        group border rounded-lg p-3 bg-white transition-all duration-200
        ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}
        ${isDragging ? 'opacity-50 shadow-lg' : ''}
      `}
    >
      <div className="flex items-center space-x-3">
        {/* Drag Handle */}
        <div
          {...attributes}
          {...listeners}
          className="flex-shrink-0 p-1 rounded cursor-grab hover:bg-gray-100 transition-colors"
        >
          <GripVertical className="h-4 w-4 text-gray-400" />
        </div>

        {/* Field Icon */}
        <div className={`
          flex-shrink-0 p-2 rounded-md transition-colors
          ${isSelected ? 'bg-blue-100' : 'bg-gray-100 group-hover:bg-gray-200'}
        `}>
          <Icon className={`h-4 w-4 ${isSelected ? 'text-blue-600' : 'text-gray-600'}`} />
        </div>

        {/* Field Info */}
        <div className="flex-1 min-w-0 cursor-pointer" onClick={onSelect}>
          <div className="flex items-center justify-between mb-1">
            <h4 className="text-sm font-medium text-gray-900 truncate">
              {field.label}
            </h4>
            <div className="flex items-center space-x-1">
              {field.required && (
                <Badge variant="destructive" className="text-xs">
                  Required
                </Badge>
              )}
              <Badge variant="secondary" className="text-xs">
                {field.type}
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500 font-mono">
                {field.name}
              </span>
              {field.description && (
                <span className="text-xs text-gray-400 truncate max-w-32">
                  {field.description}
                </span>
              )}
            </div>
            
            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation()
                  onSelect()
                }}
                className="h-6 w-6 p-0"
              >
                <Edit className="h-3 w-3" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete()
                }}
                className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Field Validation Indicators */}
      {(field.validation.length > 0 || hasFieldErrors(field)) && (
        <div className="mt-2 pt-2 border-t border-gray-100">
          <div className="flex items-center space-x-2">
            {field.validation.length > 0 && (
              <div className="flex items-center space-x-1">
                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                <span className="text-xs text-gray-500">
                  {field.validation.length} validation rule{field.validation.length !== 1 ? 's' : ''}
                </span>
              </div>
            )}
            
            {hasFieldErrors(field) && (
              <div className="flex items-center space-x-1">
                <AlertTriangle className="h-3 w-3 text-amber-500" />
                <span className="text-xs text-amber-600">
                  Configuration issues
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Field Settings Preview */}
      {getFieldSettingsPreview(field) && (
        <div className="mt-2 pt-2 border-t border-gray-100">
          <div className="text-xs text-gray-500">
            {getFieldSettingsPreview(field)}
          </div>
        </div>
      )}
    </div>
  )
}

// Helper function to get field icon
function getFieldIcon(type: FieldType) {
  const icons: Record<FieldType, React.ComponentType<any>> = {
    text: Type,
    textarea: AlignLeft,
    richtext: Edit3,
    number: Hash,
    email: Mail,
    url: Link,
    password: Type,
    date: Calendar,
    datetime: Calendar,
    time: Clock,
    boolean: ToggleLeft,
    select: ChevronDown,
    multiselect: List,
    radio: Circle,
    checkbox: Square,
    image: Image,
    gallery: Images,
    file: Paperclip,
    color: Palette,
    range: Sliders,
    relationship: Link2,
    json: Code2,
    code: Code,
    repeater: Copy,
    group: Folder,
  }

  return icons[type] || Type
}

// Helper function to check for field configuration errors
function hasFieldErrors(field: CustomField): boolean {
  // Check for missing required settings based on field type
  if (['select', 'multiselect', 'radio', 'checkbox'].includes(field.type)) {
    return !field.settings.options || field.settings.options.length === 0
  }

  if (field.type === 'relationship') {
    return !field.settings.relatedPostType
  }

  if (['repeater', 'group'].includes(field.type)) {
    return !field.settings.subFields || field.settings.subFields.length === 0
  }

  // Check for invalid field name
  if (!field.name || !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(field.name)) {
    return true
  }

  return false
}

// Helper function to get field settings preview
function getFieldSettingsPreview(field: CustomField): string | null {
  switch (field.type) {
    case 'text':
    case 'textarea':
      const lengthInfo = []
      if (field.settings.minLength) lengthInfo.push(`min: ${field.settings.minLength}`)
      if (field.settings.maxLength) lengthInfo.push(`max: ${field.settings.maxLength}`)
      return lengthInfo.length > 0 ? lengthInfo.join(', ') : null

    case 'number':
    case 'range':
      const rangeInfo = []
      if (field.settings.min !== undefined) rangeInfo.push(`min: ${field.settings.min}`)
      if (field.settings.max !== undefined) rangeInfo.push(`max: ${field.settings.max}`)
      if (field.settings.step !== undefined) rangeInfo.push(`step: ${field.settings.step}`)
      return rangeInfo.length > 0 ? rangeInfo.join(', ') : null

    case 'select':
    case 'multiselect':
    case 'radio':
    case 'checkbox':
      const optionCount = field.settings.options?.length || 0
      return `${optionCount} option${optionCount !== 1 ? 's' : ''}`

    case 'image':
    case 'gallery':
    case 'file':
      const fileInfo = []
      if (field.settings.allowedFileTypes?.length) {
        fileInfo.push(`${field.settings.allowedFileTypes.length} file type${field.settings.allowedFileTypes.length !== 1 ? 's' : ''}`)
      }
      if (field.settings.maxFileSize) {
        fileInfo.push(`max: ${field.settings.maxFileSize}MB`)
      }
      return fileInfo.length > 0 ? fileInfo.join(', ') : null

    case 'relationship':
      return field.settings.relatedPostType 
        ? `→ ${field.settings.relatedPostType} (${field.settings.relationshipType || 'one-to-many'})`
        : null

    case 'code':
      return field.settings.codeLanguage ? `Language: ${field.settings.codeLanguage}` : null

    case 'repeater':
    case 'group':
      const subFieldCount = field.settings.subFields?.length || 0
      return `${subFieldCount} sub-field${subFieldCount !== 1 ? 's' : ''}`

    default:
      return null
  }
}
