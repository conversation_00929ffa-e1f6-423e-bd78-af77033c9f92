// Visual Content Type Builder Service
// Service for creating and managing custom content types with visual field builder

import { PrismaClient } from '@prisma/client'
import { 
  CustomField, 
  FieldType, 
  ContentTypeTemplate, 
  CreatePostTypeInput,
  PostType,
  FieldGroup,
  ValidationRule,
  ConditionalRule
} from '../posts/types'
import { generateId } from '../page-builder/utils'

export class ContentTypeBuilderService {
  private db: PrismaClient

  constructor() {
    this.db = new PrismaClient()
  }

  /**
   * Create a new content type with custom fields
   */
  async createContentType(input: CreatePostTypeInput): Promise<PostType> {
    try {
      // Validate custom fields
      if (input.customFields) {
        this.validateCustomFields(input.customFields)
      }

      // Create the post type
      const postType = await this.db.postType.create({
        data: {
          name: input.name,
          label: input.label,
          labelPlural: input.labelPlural,
          description: input.description,
          icon: input.icon,
          isPublic: input.isPublic ?? true,
          isHierarchical: input.isHierarchical ?? false,
          hasArchive: input.hasArchive ?? true,
          supportsTitle: input.supportsTitle ?? true,
          supportsContent: input.supportsContent ?? true,
          supportsExcerpt: input.supportsExcerpt ?? false,
          supportsThumbnail: input.supportsThumbnail ?? false,
          supportsComments: input.supportsComments ?? false,
          supportsRevisions: input.supportsRevisions ?? true,
          supportsPageBuilder: input.supportsPageBuilder ?? false,
          menuPosition: input.menuPosition,
          capabilities: input.capabilities || {},
          taxonomies: input.taxonomies || [],
          customFields: input.customFields || [],
          templates: input.templates || [],
          isSystem: false,
          isActive: true,
        }
      })

      return postType as PostType
    } catch (error) {
      console.error('Error creating content type:', error)
      throw new Error('Failed to create content type')
    }
  }

  /**
   * Update content type with new custom fields
   */
  async updateContentType(id: string, updates: Partial<CreatePostTypeInput>): Promise<PostType> {
    try {
      if (updates.customFields) {
        this.validateCustomFields(updates.customFields)
      }

      const postType = await this.db.postType.update({
        where: { id },
        data: {
          ...updates,
          updatedAt: new Date(),
        }
      })

      return postType as PostType
    } catch (error) {
      console.error('Error updating content type:', error)
      throw new Error('Failed to update content type')
    }
  }

  /**
   * Add custom field to existing content type
   */
  async addCustomField(postTypeId: string, field: CustomField): Promise<PostType> {
    try {
      this.validateCustomField(field)

      const postType = await this.db.postType.findUnique({
        where: { id: postTypeId }
      })

      if (!postType) {
        throw new Error('Post type not found')
      }

      const customFields = Array.isArray(postType.customFields) 
        ? postType.customFields as CustomField[]
        : []

      // Add the new field
      customFields.push({
        ...field,
        id: field.id || generateId(),
        position: field.position || customFields.length,
        createdAt: new Date(),
        updatedAt: new Date(),
      })

      const updatedPostType = await this.db.postType.update({
        where: { id: postTypeId },
        data: {
          customFields,
          updatedAt: new Date(),
        }
      })

      return updatedPostType as PostType
    } catch (error) {
      console.error('Error adding custom field:', error)
      throw new Error('Failed to add custom field')
    }
  }

  /**
   * Update custom field in content type
   */
  async updateCustomField(postTypeId: string, fieldId: string, updates: Partial<CustomField>): Promise<PostType> {
    try {
      const postType = await this.db.postType.findUnique({
        where: { id: postTypeId }
      })

      if (!postType) {
        throw new Error('Post type not found')
      }

      const customFields = Array.isArray(postType.customFields) 
        ? postType.customFields as CustomField[]
        : []

      const fieldIndex = customFields.findIndex(field => field.id === fieldId)
      if (fieldIndex === -1) {
        throw new Error('Field not found')
      }

      // Update the field
      customFields[fieldIndex] = {
        ...customFields[fieldIndex],
        ...updates,
        updatedAt: new Date(),
      }

      const updatedPostType = await this.db.postType.update({
        where: { id: postTypeId },
        data: {
          customFields,
          updatedAt: new Date(),
        }
      })

      return updatedPostType as PostType
    } catch (error) {
      console.error('Error updating custom field:', error)
      throw new Error('Failed to update custom field')
    }
  }

  /**
   * Remove custom field from content type
   */
  async removeCustomField(postTypeId: string, fieldId: string): Promise<PostType> {
    try {
      const postType = await this.db.postType.findUnique({
        where: { id: postTypeId }
      })

      if (!postType) {
        throw new Error('Post type not found')
      }

      const customFields = Array.isArray(postType.customFields) 
        ? postType.customFields as CustomField[]
        : []

      const filteredFields = customFields.filter(field => field.id !== fieldId)

      const updatedPostType = await this.db.postType.update({
        where: { id: postTypeId },
        data: {
          customFields: filteredFields,
          updatedAt: new Date(),
        }
      })

      return updatedPostType as PostType
    } catch (error) {
      console.error('Error removing custom field:', error)
      throw new Error('Failed to remove custom field')
    }
  }

  /**
   * Reorder custom fields
   */
  async reorderCustomFields(postTypeId: string, fieldOrder: string[]): Promise<PostType> {
    try {
      const postType = await this.db.postType.findUnique({
        where: { id: postTypeId }
      })

      if (!postType) {
        throw new Error('Post type not found')
      }

      const customFields = Array.isArray(postType.customFields) 
        ? postType.customFields as CustomField[]
        : []

      // Reorder fields based on the provided order
      const reorderedFields = fieldOrder.map((fieldId, index) => {
        const field = customFields.find(f => f.id === fieldId)
        if (!field) {
          throw new Error(`Field with ID ${fieldId} not found`)
        }
        return {
          ...field,
          position: index,
          updatedAt: new Date(),
        }
      })

      const updatedPostType = await this.db.postType.update({
        where: { id: postTypeId },
        data: {
          customFields: reorderedFields,
          updatedAt: new Date(),
        }
      })

      return updatedPostType as PostType
    } catch (error) {
      console.error('Error reordering custom fields:', error)
      throw new Error('Failed to reorder custom fields')
    }
  }

  /**
   * Get available field types with their configurations
   */
  getAvailableFieldTypes(): Array<{
    type: FieldType
    label: string
    description: string
    icon: string
    category: string
    defaultSettings: any
  }> {
    return [
      {
        type: 'text',
        label: 'Text',
        description: 'Single line text input',
        icon: 'Type',
        category: 'basic',
        defaultSettings: { placeholder: 'Enter text...' }
      },
      {
        type: 'textarea',
        label: 'Textarea',
        description: 'Multi-line text input',
        icon: 'AlignLeft',
        category: 'basic',
        defaultSettings: { rows: 4, placeholder: 'Enter text...' }
      },
      {
        type: 'richtext',
        label: 'Rich Text',
        description: 'WYSIWYG editor',
        icon: 'Edit3',
        category: 'content',
        defaultSettings: {}
      },
      {
        type: 'number',
        label: 'Number',
        description: 'Numeric input',
        icon: 'Hash',
        category: 'basic',
        defaultSettings: { min: 0, step: 1 }
      },
      {
        type: 'email',
        label: 'Email',
        description: 'Email address input',
        icon: 'Mail',
        category: 'basic',
        defaultSettings: { placeholder: '<EMAIL>' }
      },
      {
        type: 'url',
        label: 'URL',
        description: 'Website URL input',
        icon: 'Link',
        category: 'basic',
        defaultSettings: { placeholder: 'https://example.com' }
      },
      {
        type: 'date',
        label: 'Date',
        description: 'Date picker',
        icon: 'Calendar',
        category: 'basic',
        defaultSettings: {}
      },
      {
        type: 'datetime',
        label: 'Date & Time',
        description: 'Date and time picker',
        icon: 'Clock',
        category: 'basic',
        defaultSettings: {}
      },
      {
        type: 'boolean',
        label: 'Boolean',
        description: 'True/false toggle',
        icon: 'ToggleLeft',
        category: 'basic',
        defaultSettings: {}
      },
      {
        type: 'select',
        label: 'Select',
        description: 'Dropdown selection',
        icon: 'ChevronDown',
        category: 'choice',
        defaultSettings: { options: [] }
      },
      {
        type: 'multiselect',
        label: 'Multi Select',
        description: 'Multiple selection dropdown',
        icon: 'List',
        category: 'choice',
        defaultSettings: { options: [], multiple: true }
      },
      {
        type: 'radio',
        label: 'Radio',
        description: 'Radio button group',
        icon: 'Circle',
        category: 'choice',
        defaultSettings: { options: [] }
      },
      {
        type: 'checkbox',
        label: 'Checkbox',
        description: 'Checkbox group',
        icon: 'Square',
        category: 'choice',
        defaultSettings: { options: [] }
      },
      {
        type: 'image',
        label: 'Image',
        description: 'Single image upload',
        icon: 'Image',
        category: 'media',
        defaultSettings: { allowedFileTypes: ['image/jpeg', 'image/png', 'image/webp'] }
      },
      {
        type: 'gallery',
        label: 'Gallery',
        description: 'Multiple image upload',
        icon: 'Images',
        category: 'media',
        defaultSettings: { allowedFileTypes: ['image/jpeg', 'image/png', 'image/webp'], multiple: true }
      },
      {
        type: 'file',
        label: 'File',
        description: 'File upload',
        icon: 'Paperclip',
        category: 'media',
        defaultSettings: {}
      },
      {
        type: 'color',
        label: 'Color',
        description: 'Color picker',
        icon: 'Palette',
        category: 'basic',
        defaultSettings: {}
      },
      {
        type: 'range',
        label: 'Range',
        description: 'Range slider',
        icon: 'Sliders',
        category: 'basic',
        defaultSettings: { min: 0, max: 100, step: 1 }
      },
      {
        type: 'relationship',
        label: 'Relationship',
        description: 'Link to other content',
        icon: 'Link2',
        category: 'advanced',
        defaultSettings: { relationshipType: 'one-to-many' }
      },
      {
        type: 'json',
        label: 'JSON',
        description: 'JSON data input',
        icon: 'Code2',
        category: 'advanced',
        defaultSettings: {}
      },
      {
        type: 'code',
        label: 'Code',
        description: 'Code editor',
        icon: 'Code',
        category: 'advanced',
        defaultSettings: { codeLanguage: 'javascript' }
      },
      {
        type: 'repeater',
        label: 'Repeater',
        description: 'Repeatable field group',
        icon: 'Copy',
        category: 'advanced',
        defaultSettings: { subFields: [] }
      },
      {
        type: 'group',
        label: 'Group',
        description: 'Field group container',
        icon: 'Folder',
        category: 'advanced',
        defaultSettings: { subFields: [] }
      }
    ]
  }

  /**
   * Validate custom fields
   */
  private validateCustomFields(fields: CustomField[]): void {
    fields.forEach(field => this.validateCustomField(field))
  }

  /**
   * Validate a single custom field
   */
  private validateCustomField(field: CustomField): void {
    if (!field.name || !field.label || !field.type) {
      throw new Error('Field must have name, label, and type')
    }

    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(field.name)) {
      throw new Error('Field name must be a valid identifier')
    }

    // Validate field-specific settings
    if (field.type === 'select' || field.type === 'multiselect' || field.type === 'radio' || field.type === 'checkbox') {
      if (!field.settings.options || !Array.isArray(field.settings.options) || field.settings.options.length === 0) {
        throw new Error(`${field.type} field must have at least one option`)
      }
    }

    if (field.type === 'relationship') {
      if (!field.settings.relatedPostType) {
        throw new Error('Relationship field must specify related post type')
      }
    }

    if (field.type === 'repeater' || field.type === 'group') {
      if (!field.settings.subFields || !Array.isArray(field.settings.subFields)) {
        throw new Error(`${field.type} field must have sub-fields`)
      }
    }
  }
}

export const contentTypeBuilderService = new ContentTypeBuilderService()
