// Integration layer between Content Type Builder and Page Builder Custom Fields
// Converts Content Type Builder field definitions to Page Builder field configs

import { CustomField, FieldType, FieldSettings } from '../../posts/types'
import { FieldConfig } from '../../page-builder/components/properties-panel/custom-fields/types'

/**
 * Convert Content Type Builder CustomField to Page Builder FieldConfig
 */
export function convertToPageBuilderField(customField: CustomField): FieldConfig {
  const baseConfig: FieldConfig = {
    id: customField.id,
    type: mapFieldType(customField.type),
    label: customField.label,
    description: customField.description,
    placeholder: customField.settings.placeholder,
    defaultValue: customField.defaultValue,
    disabled: false,
    hidden: false,
    className: customField.settings.className,
    validation: convertValidation(customField),
  }

  // Add type-specific configurations
  return {
    ...baseConfig,
    ...mapFieldSettings(customField.type, customField.settings)
  }
}

/**
 * Convert multiple CustomFields to FieldConfigs
 */
export function convertToPageBuilderFields(customFields: CustomField[]): FieldConfig[] {
  return customFields
    .sort((a, b) => a.position - b.position)
    .map(convertToPageBuilderField)
}

/**
 * Map Content Type Builder field types to Page Builder field types
 */
function mapFieldType(fieldType: FieldType): string {
  const typeMapping: Record<FieldType, string> = {
    // Basic mappings
    'text': 'text',
    'textarea': 'textarea',
    'richtext': 'richtext',
    'number': 'number',
    'email': 'text', // Page builder doesn't have email type, use text with validation
    'url': 'text', // Page builder doesn't have url type, use text with validation
    'password': 'text', // Page builder doesn't have password type, use text
    'date': 'date',
    'datetime': 'datetime',
    'time': 'time',
    'boolean': 'boolean',
    'select': 'select',
    'multiselect': 'multi-select',
    'radio': 'select', // Map radio to select with single selection
    'checkbox': 'multi-select', // Map checkbox to multi-select
    'image': 'image',
    'gallery': 'media', // Use media field for galleries
    'file': 'file',
    'color': 'color',
    'range': 'range',
    'relationship': 'select', // Map relationship to select for now
    'json': 'code', // Map JSON to code field
    'code': 'code',
    'repeater': 'repeater',
    'group': 'object', // Map group to object field
  }

  return typeMapping[fieldType] || 'text'
}

/**
 * Convert field settings based on field type
 */
function mapFieldSettings(fieldType: FieldType, settings: FieldSettings): Partial<FieldConfig> {
  switch (fieldType) {
    case 'text':
    case 'email':
    case 'url':
    case 'password':
      return {
        validation: {
          minLength: settings.minLength,
          maxLength: settings.maxLength,
          pattern: fieldType === 'email' ? /^[^\s@]+@[^\s@]+\.[^\s@]+$/ : 
                   fieldType === 'url' ? /^https?:\/\/.+/ : undefined
        }
      }

    case 'textarea':
      return {
        rows: settings.rows,
        validation: {
          minLength: settings.minLength,
          maxLength: settings.maxLength,
        }
      }

    case 'number':
    case 'range':
      return {
        min: settings.min,
        max: settings.max,
        step: settings.step,
        unit: fieldType === 'range' ? 'px' : undefined,
        showValue: fieldType === 'range' ? true : undefined
      }

    case 'select':
    case 'radio':
      return {
        options: settings.options?.map(option => ({
          label: option.label,
          value: option.value,
          icon: option.icon,
          disabled: option.disabled
        })) || [],
        multiple: false,
        searchable: true,
        clearable: true
      }

    case 'multiselect':
    case 'checkbox':
      return {
        options: settings.options?.map(option => ({
          label: option.label,
          value: option.value,
          icon: option.icon,
          disabled: option.disabled
        })) || [],
        multiple: true,
        searchable: true,
        clearable: true
      }

    case 'image':
      return {
        accept: settings.allowedFileTypes || ['image/jpeg', 'image/png', 'image/webp'],
        maxSize: settings.maxFileSize ? settings.maxFileSize * 1024 * 1024 : undefined, // Convert MB to bytes
        preview: true,
        crop: false
      }

    case 'gallery':
      return {
        accept: settings.allowedFileTypes || ['image/jpeg', 'image/png', 'image/webp'],
        maxSize: settings.maxFileSize ? settings.maxFileSize * 1024 * 1024 : undefined,
        multiple: true,
        preview: true
      }

    case 'file':
      return {
        accept: settings.allowedFileTypes,
        maxSize: settings.maxFileSize ? settings.maxFileSize * 1024 * 1024 : undefined,
        multiple: settings.multiple || false,
        preview: true
      }

    case 'color':
      return {
        format: 'hex',
        alpha: true,
        presets: [],
        gradient: false
      }

    case 'code':
      return {
        language: settings.codeLanguage || 'javascript',
        theme: 'vs-dark',
        lineNumbers: true,
        wordWrap: true,
        height: 200,
        readOnly: false
      }

    case 'richtext':
      return {
        toolbar: ['bold', 'italic', 'underline', 'link', 'bulletList', 'orderedList'],
        plugins: ['link', 'lists'],
        formats: ['bold', 'italic', 'underline', 'link'],
        height: 200,
        maxLength: settings.maxLength
      }

    case 'repeater':
      return {
        itemSchema: settings.subFields?.map(convertToPageBuilderField) || [],
        minItems: 0,
        maxItems: 50,
        addLabel: 'Add Item',
        removeLabel: 'Remove',
        sortable: true
      }

    case 'group':
      return {
        properties: settings.subFields?.reduce((acc, field) => {
          acc[field.name] = convertToPageBuilderField(field)
          return acc
        }, {} as Record<string, FieldConfig>) || {},
        layout: 'vertical',
        columns: 1
      }

    case 'relationship':
      // For now, convert to a simple select
      // In the future, this could be enhanced with dynamic loading
      return {
        options: [],
        searchable: true,
        clearable: true,
        multiple: settings.relationshipType?.includes('many') || false
      }

    default:
      return {}
  }
}

/**
 * Convert Content Type Builder validation to Page Builder validation
 */
function convertValidation(customField: CustomField): any {
  const validation: any = {
    required: customField.required
  }

  customField.validation.forEach(rule => {
    switch (rule.type) {
      case 'required':
        validation.required = true
        break
      case 'min':
        validation.min = rule.value
        break
      case 'max':
        validation.max = rule.value
        break
      case 'pattern':
        validation.pattern = new RegExp(rule.value)
        break
      case 'email':
        validation.pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        break
      case 'url':
        validation.pattern = /^https?:\/\/.+/
        break
      case 'custom':
        validation.custom = (value: any) => {
          // Custom validation would need to be implemented
          return null
        }
        break
    }
  })

  return validation
}

/**
 * Enhanced field types that leverage Page Builder's advanced fields
 */
export const enhancedFieldTypes = [
  // Design & Layout Fields
  {
    type: 'spacing' as FieldType,
    label: 'Spacing',
    description: 'Margin and padding controls',
    icon: 'Move',
    category: 'design',
    defaultSettings: {
      sides: ['top', 'right', 'bottom', 'left'],
      linked: true,
      unit: 'px',
      min: 0,
      max: 100
    }
  },
  {
    type: 'border' as FieldType,
    label: 'Border',
    description: 'Border width, style, and color',
    icon: 'Square',
    category: 'design',
    defaultSettings: {
      sides: ['top', 'right', 'bottom', 'left'],
      properties: ['width', 'style', 'color']
    }
  },
  {
    type: 'shadow' as FieldType,
    label: 'Shadow',
    description: 'Box shadow effects',
    icon: 'Layers',
    category: 'design',
    defaultSettings: {
      multiple: false,
      presets: []
    }
  },
  {
    type: 'gradient' as FieldType,
    label: 'Gradient',
    description: 'Gradient color picker',
    icon: 'Palette',
    category: 'design',
    defaultSettings: {
      direction: true,
      stops: true,
      presets: []
    }
  },
  {
    type: 'animation' as FieldType,
    label: 'Animation',
    description: 'CSS animation properties',
    icon: 'Zap',
    category: 'design',
    defaultSettings: {
      properties: ['duration', 'delay', 'easing'],
      presets: []
    }
  },

  // Advanced UI Fields
  {
    type: 'icon' as FieldType,
    label: 'Icon',
    description: 'Icon picker with search',
    icon: 'Star',
    category: 'ui',
    defaultSettings: {
      iconSet: 'lucide',
      size: 24,
      searchable: true,
      categories: []
    }
  },
  {
    type: 'font' as FieldType,
    label: 'Font',
    description: 'Font family, weight, and style',
    icon: 'Type',
    category: 'ui',
    defaultSettings: {
      families: [],
      weights: ['400', '500', '600', '700'],
      styles: ['normal', 'italic'],
      preview: true
    }
  },
  {
    type: 'link' as FieldType,
    label: 'Link',
    description: 'URL with internal/external options',
    icon: 'Link',
    category: 'content',
    defaultSettings: {
      allowExternal: true,
      allowInternal: true,
      allowEmail: true,
      allowPhone: true,
      allowAnchor: true
    }
  }
]

/**
 * Get all available field types including enhanced ones
 */
export function getAllFieldTypes() {
  return [
    // Basic field types from Content Type Builder
    ...getBasicFieldTypes(),
    // Enhanced field types from Page Builder
    ...enhancedFieldTypes
  ]
}

function getBasicFieldTypes() {
  return [
    { type: 'text', label: 'Text', description: 'Single line text', icon: 'Type', category: 'basic' },
    { type: 'textarea', label: 'Textarea', description: 'Multi-line text', icon: 'AlignLeft', category: 'basic' },
    { type: 'richtext', label: 'Rich Text', description: 'WYSIWYG editor', icon: 'Edit3', category: 'content' },
    { type: 'number', label: 'Number', description: 'Numeric input', icon: 'Hash', category: 'basic' },
    { type: 'email', label: 'Email', description: 'Email address', icon: 'Mail', category: 'basic' },
    { type: 'url', label: 'URL', description: 'Website URL', icon: 'Link', category: 'basic' },
    { type: 'date', label: 'Date', description: 'Date picker', icon: 'Calendar', category: 'basic' },
    { type: 'datetime', label: 'Date & Time', description: 'Date and time picker', icon: 'Clock', category: 'basic' },
    { type: 'boolean', label: 'Boolean', description: 'True/false toggle', icon: 'ToggleLeft', category: 'basic' },
    { type: 'select', label: 'Select', description: 'Dropdown selection', icon: 'ChevronDown', category: 'choice' },
    { type: 'multiselect', label: 'Multi Select', description: 'Multiple selection', icon: 'List', category: 'choice' },
    { type: 'image', label: 'Image', description: 'Single image upload', icon: 'Image', category: 'media' },
    { type: 'gallery', label: 'Gallery', description: 'Multiple images', icon: 'Images', category: 'media' },
    { type: 'file', label: 'File', description: 'File upload', icon: 'Paperclip', category: 'media' },
    { type: 'color', label: 'Color', description: 'Color picker', icon: 'Palette', category: 'basic' },
    { type: 'range', label: 'Range', description: 'Range slider', icon: 'Sliders', category: 'basic' },
    { type: 'code', label: 'Code', description: 'Code editor', icon: 'Code', category: 'advanced' },
    { type: 'repeater', label: 'Repeater', description: 'Repeatable fields', icon: 'Copy', category: 'advanced' },
    { type: 'group', label: 'Group', description: 'Field group', icon: 'Folder', category: 'advanced' },
  ]
}
