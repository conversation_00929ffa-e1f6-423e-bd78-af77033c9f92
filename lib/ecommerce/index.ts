// E-commerce Library Main Export
// This is the main entry point for the e-commerce library

// Export all types
export * from './types'

// Export utilities
export * from './utils/api-response'

// Export configuration
export * from './config/database'
export * from './config/constants'

// Import and export core services
import { ProductService } from './services/product-service'
import { CartService } from './services/cart-service'
import { AuthService } from './services/auth-service'
import { OrderService } from './services/order-service'
import { PaymentService } from './services/payment-service'
import { CustomerService } from './services/customer-service'
import { AnalyticsService } from './services/analytics-service'
import { ReportsService } from './services/reports-service'
import { FulfillmentService } from './services/fulfillment-service'

export { ProductService } from './services/product-service'
export { CartService } from './services/cart-service'
export { AuthService } from './services/auth-service'
export { OrderService } from './services/order-service'
export { PaymentService } from './services/payment-service'
export { CustomerService } from './services/customer-service'
export { AnalyticsService } from './services/analytics-service'
export { ReportsService } from './services/reports-service'
export { FulfillmentService } from './services/fulfillment-service'

// Import Appwrite integration
import { appwriteIntegration, isAppwriteConfigured } from '@/lib/appwrite'


// Import and export database utilities
import { prisma, db, withTransaction, isHealthy, connectDatabase, disconnectDatabase } from './config/database'
export { prisma, db, withTransaction, isHealthy, connectDatabase, disconnectDatabase } from './config/database'

// Main E-commerce class that provides access to all services
export class EcommerceLibrary {
  public readonly products: ProductService
  public readonly cart: CartService
  public readonly auth: AuthService
  public readonly orders: OrderService
  public readonly payments: PaymentService
  public readonly customers: CustomerService
  public readonly analytics: AnalyticsService
  public readonly reports: ReportsService
  public readonly fulfillment: FulfillmentService
  public readonly appwrite: typeof appwriteIntegration

  constructor() {
    this.products = new ProductService()
    this.cart = new CartService()
    this.auth = new AuthService()
    this.orders = new OrderService()
    this.payments = new PaymentService()
    this.customers = new CustomerService()
    this.analytics = new AnalyticsService()
    this.reports = new ReportsService()
    this.fulfillment = new FulfillmentService()
    this.appwrite = appwriteIntegration
  }

  /**
   * Initialize the e-commerce library
   */
  async initialize(): Promise<void> {
    try {
      await connectDatabase()

      // Initialize Appwrite if configured
      if (isAppwriteConfigured()) {
        try {
          await this.appwrite.initialize()
          console.log('Appwrite integration initialized successfully')
        } catch (appwriteError) {
          console.warn('Appwrite initialization failed, continuing without it:', appwriteError)
        }
      } else {
        console.log('Appwrite not configured, skipping initialization')
      }

      console.log('E-commerce library initialized successfully')
    } catch (error) {
      console.error('Failed to initialize e-commerce library:', error)
      throw error
    }
  }

  /**
   * Cleanup and disconnect
   */
  async cleanup(): Promise<void> {
    try {
      await disconnectDatabase()
      console.log('E-commerce library cleaned up successfully')
    } catch (error) {
      console.error('Failed to cleanup e-commerce library:', error)
      throw error
    }
  }

  /**
   * Health check for the library
   */
  async healthCheck(): Promise<boolean> {
    return isHealthy()
  }
}

// Create and export a default instance
export const ecommerce = new EcommerceLibrary()

// Export individual service instances for direct use
// Note: These are created lazily to avoid circular dependency issues
let _productService: ProductService | null = null
let _cartService: CartService | null = null
let _authService: AuthService | null = null
let _orderService: OrderService | null = null
let _paymentService: PaymentService | null = null
let _customerService: CustomerService | null = null
let _analyticsService: AnalyticsService | null = null
let _reportsService: ReportsService | null = null
let _fulfillmentService: FulfillmentService | null = null

export const productService = () => _productService || (_productService = new ProductService())
export const cartService = () => _cartService || (_cartService = new CartService())
export const authService = () => _authService || (_authService = new AuthService())
export const orderService = () => _orderService || (_orderService = new OrderService())
export const paymentService = () => _paymentService || (_paymentService = new PaymentService())
export const customerService = () => _customerService || (_customerService = new CustomerService())
export const analyticsService = () => _analyticsService || (_analyticsService = new AnalyticsService())
export const reportsService = () => _reportsService || (_reportsService = new ReportsService())
export const fulfillmentService = () => _fulfillmentService || (_fulfillmentService = new FulfillmentService())

// Utility function to create a new instance with custom configuration
export function createEcommerceLibrary(): EcommerceLibrary {
  return new EcommerceLibrary()
}

// Version information
export const VERSION = '1.0.0'
export const LIBRARY_NAME = 'Custom E-commerce Library'

// Feature flags
export const FEATURES = {
  PRODUCTS: true,
  CART: true,
  AUTHENTICATION: true,
  ORDERS: true,
  PAYMENTS: true,
  INVENTORY: false, // Not yet implemented
  SHIPPING: false,  // Not yet implemented
  ADMIN: false      // Not yet implemented
} as const

// Library configuration interface
export interface EcommerceConfig {
  database?: {
    url?: string
    maxConnections?: number
    connectionTimeout?: number
    queryTimeout?: number
    logLevel?: 'info' | 'query' | 'warn' | 'error'
    enableLogging?: boolean
  }
  auth?: {
    jwtSecret?: string
    jwtExpiresIn?: string
    refreshTokenExpiresIn?: string
  }
  payments?: {
    defaultGateway?: string
    testMode?: boolean
  }
  features?: {
    enableInventoryTracking?: boolean
    enableReviews?: boolean
    enableWishlist?: boolean
    enableLoyalty?: boolean
  }
}

// Configure the library
export function configureEcommerce(config: EcommerceConfig): void {
  // Apply configuration
  if (config.database) {
    // Database configuration would be applied here
    console.log('Database configuration applied')
  }

  if (config.auth) {
    // Auth configuration would be applied here
    console.log('Auth configuration applied')
  }

  if (config.payments) {
    // Payment configuration would be applied here
    console.log('Payment configuration applied')
  }

  if (config.features) {
    // Feature flags would be applied here
    console.log('Feature configuration applied')
  }
}

// Error handling utilities
export function handleEcommerceError(error: any): {
  code: string
  message: string
  statusCode: number
} {
  if (error.name === 'NotFoundError') {
    return {
      code: 'NOT_FOUND',
      message: error.message,
      statusCode: 404
    }
  }

  if (error.name === 'ValidationError') {
    return {
      code: 'VALIDATION_ERROR',
      message: error.message,
      statusCode: 400
    }
  }

  if (error.name === 'InsufficientStockError') {
    return {
      code: 'INSUFFICIENT_STOCK',
      message: error.message,
      statusCode: 400
    }
  }

  // Default error
  return {
    code: 'INTERNAL_ERROR',
    message: error.message || 'An unexpected error occurred',
    statusCode: 500
  }
}

// Logging utilities
export const logger = {
  info: (message: string, meta?: any) => {
    console.log(`[ECOMMERCE] ${message}`, meta || '')
  },
  warn: (message: string, meta?: any) => {
    console.warn(`[ECOMMERCE] ${message}`, meta || '')
  },
  error: (message: string, meta?: any) => {
    console.error(`[ECOMMERCE] ${message}`, meta || '')
  },
  debug: (message: string, meta?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[ECOMMERCE] ${message}`, meta || '')
    }
  }
}

// Event system for the library
export class EcommerceEventEmitter {
  private listeners: Map<string, Function[]> = new Map()

  on(event: string, listener: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(listener)
  }

  off(event: string, listener: Function): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      const index = eventListeners.indexOf(listener)
      if (index > -1) {
        eventListeners.splice(index, 1)
      }
    }
  }

  emit(event: string, data?: any): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          logger.error(`Error in event listener for ${event}:`, error)
        }
      })
    }
  }

  removeAllListeners(event?: string): void {
    if (event) {
      this.listeners.delete(event)
    } else {
      this.listeners.clear()
    }
  }
}

// Global event emitter instance
export const events = new EcommerceEventEmitter()

// Middleware utilities for Next.js integration
export function withEcommerceAuth(handler: Function) {
  return async (req: any, res: any) => {
    try {
      // Extract token from request
      const token = req.headers.authorization?.replace('Bearer ', '')
      
      if (!token) {
        return res.status(401).json({ error: 'Authentication required' })
      }

      // Verify token and get user
      const userResult = await authService().getCurrentUser(token)
      
      if (!userResult.success) {
        return res.status(401).json({ error: 'Invalid token' })
      }

      // Add user to request
      req.user = userResult.data

      return handler(req, res)
    } catch (error) {
      logger.error('Auth middleware error:', error)
      return res.status(500).json({ error: 'Internal server error' })
    }
  }
}

export function withEcommerceErrorHandling(handler: Function) {
  return async (req: any, res: any) => {
    try {
      return await handler(req, res)
    } catch (error) {
      logger.error('API error:', error)
      const errorResponse = handleEcommerceError(error)
      return res.status(errorResponse.statusCode).json({
        error: {
          code: errorResponse.code,
          message: errorResponse.message
        }
      })
    }
  }
}

// React integration utilities (to be expanded)
export const reactIntegration = {
  // Placeholder for React hooks and providers
  // These would be implemented in separate files
  hooks: {
    // useProducts, useCart, useAuth, etc.
  },
  providers: {
    // EcommerceProvider, CartProvider, AuthProvider, etc.
  },
  components: {
    // ProductCard, CartItem, CheckoutForm, etc.
  }
}

// Export default instance for convenience
export default ecommerce
