// Simplified Fulfillment Service for Testing
// This version works without external API dependencies

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export interface SimpleFulfillmentRequest {
  orderId: string
  items: Array<{
    orderItemId: string
    quantity: number
  }>
  carrier: string
  service: string
  notes?: string
}

export class SimpleFulfillmentService {
  /**
   * Create a basic fulfillment record
   */
  async createFulfillment(request: SimpleFulfillmentRequest) {
    try {
      // Validate order exists
      const order = await prisma.order.findUnique({
        where: { id: request.orderId },
        include: { items: true }
      })

      if (!order) {
        throw new Error('Order not found')
      }

      // Generate mock tracking number
      const trackingNumber = this.generateTrackingNumber(request.carrier)

      // Create fulfillment record
      const fulfillment = await prisma.orderFulfillment.create({
        data: {
          orderId: request.orderId,
          status: 'pending',
          trackingCompany: request.carrier,
          trackingNumber,
          trackingUrl: `https://tracking.example.com/${trackingNumber}`,
          locationName: 'Main Warehouse',
          notes: request.notes,
          notifyCustomer: true,
          items: {
            create: request.items.map(item => ({
              orderItemId: item.orderItemId,
              quantity: item.quantity
            }))
          }
        },
        include: {
          items: true,
          order: true
        }
      })

      // Update order status
      await prisma.order.update({
        where: { id: request.orderId },
        data: {
          status: 'processing',
          fulfillmentStatus: 'partially_fulfilled'
        }
      })

      return {
        success: true,
        data: fulfillment
      }

    } catch (error) {
      console.error('Create fulfillment error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create fulfillment'
      }
    }
  }

  /**
   * Get fulfillment by ID
   */
  async getFulfillment(fulfillmentId: string) {
    try {
      const fulfillment = await prisma.orderFulfillment.findUnique({
        where: { id: fulfillmentId },
        include: {
          order: {
            select: {
              id: true,
              orderNumber: true,
              customerEmail: true,
              customerFirstName: true,
              customerLastName: true,
              total: true,
              currency: true
            }
          },
          items: {
            include: {
              orderItem: {
                include: {
                  product: {
                    select: {
                      id: true,
                      name: true,
                      sku: true,
                      images: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      return fulfillment
    } catch (error) {
      console.error('Get fulfillment error:', error)
      return null
    }
  }

  /**
   * Update fulfillment status
   */
  async updateFulfillmentStatus(fulfillmentId: string, status: string, notes?: string) {
    try {
      const updateData: any = {
        status,
        updatedAt: new Date()
      }

      if (notes) {
        updateData.notes = notes
      }

      // Set timestamps based on status
      if (status === 'open') {
        updateData.shippedAt = new Date()
      } else if (status === 'delivered') {
        updateData.deliveredAt = new Date()
      }

      const fulfillment = await prisma.orderFulfillment.update({
        where: { id: fulfillmentId },
        data: updateData,
        include: {
          order: true,
          items: true
        }
      })

      // Update order status if delivered
      if (status === 'delivered') {
        await this.checkAndUpdateOrderDeliveryStatus(fulfillment.orderId)
      }

      return fulfillment
    } catch (error) {
      console.error('Update fulfillment status error:', error)
      throw error
    }
  }

  /**
   * Cancel fulfillment
   */
  async cancelFulfillment(fulfillmentId: string, reason: string) {
    try {
      await prisma.orderFulfillment.update({
        where: { id: fulfillmentId },
        data: {
          status: 'cancelled',
          notes: `Cancelled: ${reason}`,
          updatedAt: new Date()
        }
      })

      return { success: true, message: 'Fulfillment cancelled successfully' }
    } catch (error) {
      console.error('Cancel fulfillment error:', error)
      throw error
    }
  }

  /**
   * Get fulfillment metrics
   */
  async getFulfillmentMetrics(startDate: Date, endDate: Date) {
    try {
      const fulfillments = await prisma.orderFulfillment.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        },
        include: {
          items: true
        }
      })

      const metrics = {
        totalFulfillments: fulfillments.length,
        statusBreakdown: {} as Record<string, number>,
        averageFulfillmentTime: 0,
        onTimeDeliveryRate: 0,
        totalItemsFulfilled: 0,
        fulfillmentsByCarrier: {} as Record<string, number>
      }

      let totalFulfillmentTime = 0
      let onTimeDeliveries = 0

      for (const fulfillment of fulfillments) {
        // Status breakdown
        metrics.statusBreakdown[fulfillment.status] = (metrics.statusBreakdown[fulfillment.status] || 0) + 1

        // Carrier breakdown
        if (fulfillment.trackingCompany) {
          metrics.fulfillmentsByCarrier[fulfillment.trackingCompany] = (metrics.fulfillmentsByCarrier[fulfillment.trackingCompany] || 0) + 1
        }

        // Items count
        metrics.totalItemsFulfilled += fulfillment.items.reduce((sum, item) => sum + item.quantity, 0)

        // Fulfillment time calculation
        if (fulfillment.shippedAt) {
          const fulfillmentTime = fulfillment.shippedAt.getTime() - fulfillment.createdAt.getTime()
          totalFulfillmentTime += fulfillmentTime
        }

        // On-time delivery calculation (mock)
        if (fulfillment.deliveredAt) {
          onTimeDeliveries++
        }
      }

      metrics.averageFulfillmentTime = fulfillments.length > 0 ? totalFulfillmentTime / fulfillments.length : 0
      metrics.onTimeDeliveryRate = fulfillments.length > 0 ? (onTimeDeliveries / fulfillments.length) * 100 : 0

      return metrics
    } catch (error) {
      console.error('Get fulfillment metrics error:', error)
      throw error
    }
  }

  /**
   * Generate mock tracking number
   */
  private generateTrackingNumber(carrier: string): string {
    const prefix = carrier.toUpperCase().substring(0, 3)
    const timestamp = Date.now().toString()
    const random = Math.random().toString(36).substring(2, 8).toUpperCase()
    return `${prefix}${timestamp.slice(-8)}${random}`
  }

  /**
   * Check and update order delivery status
   */
  private async checkAndUpdateOrderDeliveryStatus(orderId: string) {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        fulfillments: true
      }
    })

    if (!order) return

    // Check if all fulfillments are delivered
    const allDelivered = order.fulfillments.every(f => f.status === 'delivered')

    if (allDelivered && order.status !== 'delivered') {
      await prisma.order.update({
        where: { id: orderId },
        data: {
          status: 'delivered',
          fulfillmentStatus: 'fulfilled',
          deliveredAt: new Date(),
          updatedAt: new Date()
        }
      })
    }
  }
}
