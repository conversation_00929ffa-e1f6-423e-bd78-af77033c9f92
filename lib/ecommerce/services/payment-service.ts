import { prisma } from '@/lib/prisma'
import crypto from 'crypto'

export interface PaymentMethod {
  id: string
  name: string
  type: 'payfast' | 'ozow' | 'card' | 'eft'
  enabled: boolean
  config: Record<string, any>
}

export interface PaymentRequest {
  orderId: string
  amount: number
  currency: 'ZAR'
  customerEmail: string
  customerName: string
  description: string
  returnUrl: string
  cancelUrl: string
  notifyUrl: string
}

export interface PaymentResponse {
  success: boolean
  paymentId: string
  redirectUrl?: string
  error?: string
}

// PayFast Integration
export class PayFastService {
  private merchantId: string
  private merchantKey: string
  private passphrase: string
  private sandbox: boolean

  constructor() {
    this.merchantId = process.env.PAYFAST_MERCHANT_ID || ''
    this.merchantKey = process.env.PAYFAST_MERCHANT_KEY || ''
    this.passphrase = process.env.PAYFAST_PASSPHRASE || ''
    this.sandbox = process.env.NODE_ENV !== 'production'
  }

  private generateSignature(data: Record<string, any>): string {
    // Remove signature if it exists
    delete data.signature

    // Create parameter string
    const paramString = Object.keys(data)
      .sort()
      .map(key => `${key}=${encodeURIComponent(data[key])}`)
      .join('&')

    // Add passphrase if provided
    const stringToHash = this.passphrase 
      ? `${paramString}&passphrase=${encodeURIComponent(this.passphrase)}`
      : paramString

    return crypto.createHash('md5').update(stringToHash).digest('hex')
  }

  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      const paymentData = {
        merchant_id: this.merchantId,
        merchant_key: this.merchantKey,
        return_url: request.returnUrl,
        cancel_url: request.cancelUrl,
        notify_url: request.notifyUrl,
        name_first: request.customerName.split(' ')[0],
        name_last: request.customerName.split(' ').slice(1).join(' '),
        email_address: request.customerEmail,
        m_payment_id: request.orderId,
        amount: request.amount.toFixed(2),
        item_name: request.description,
        item_description: request.description,
        custom_str1: request.orderId,
        custom_str2: 'ecommerce',
        custom_str3: 'order'
      }

      // Generate signature
      const signature = this.generateSignature({ ...paymentData })
      paymentData.signature = signature

      // Store payment record
      await prisma.payment.create({
        data: {
          id: crypto.randomUUID(),
          orderId: request.orderId,
          amount: request.amount,
          currency: request.currency,
          provider: 'payfast',
          status: 'pending',
          providerPaymentId: request.orderId,
          metadata: paymentData
        }
      })

      const baseUrl = this.sandbox 
        ? 'https://sandbox.payfast.co.za/eng/process'
        : 'https://www.payfast.co.za/eng/process'

      const queryString = Object.keys(paymentData)
        .map(key => `${key}=${encodeURIComponent(paymentData[key])}`)
        .join('&')

      return {
        success: true,
        paymentId: request.orderId,
        redirectUrl: `${baseUrl}?${queryString}`
      }
    } catch (error) {
      console.error('PayFast payment creation error:', error)
      return {
        success: false,
        paymentId: '',
        error: 'Failed to create PayFast payment'
      }
    }
  }

  async verifyPayment(data: Record<string, any>): Promise<boolean> {
    try {
      const receivedSignature = data.signature
      delete data.signature

      const calculatedSignature = this.generateSignature(data)
      
      return receivedSignature === calculatedSignature
    } catch (error) {
      console.error('PayFast verification error:', error)
      return false
    }
  }
}

// Ozow Integration
export class OzowService {
  private siteCode: string
  private privateKey: string
  private apiKey: string
  private sandbox: boolean

  constructor() {
    this.siteCode = process.env.OZOW_SITE_CODE || ''
    this.privateKey = process.env.OZOW_PRIVATE_KEY || ''
    this.apiKey = process.env.OZOW_API_KEY || ''
    this.sandbox = process.env.NODE_ENV !== 'production'
  }

  private generateHashCheck(data: Record<string, any>): string {
    const sortedKeys = Object.keys(data).sort()
    const concatenated = sortedKeys.map(key => data[key]).join('')
    const withKey = concatenated + this.privateKey
    
    return crypto.createHash('sha512').update(withKey.toLowerCase()).digest('hex')
  }

  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      const transactionReference = `TXN_${request.orderId}_${Date.now()}`
      
      const paymentData = {
        SiteCode: this.siteCode,
        CountryCode: 'ZA',
        CurrencyCode: request.currency,
        Amount: request.amount.toFixed(2),
        TransactionReference: transactionReference,
        BankReference: request.description,
        Customer: request.customerEmail,
        SuccessUrl: request.returnUrl,
        ErrorUrl: request.cancelUrl,
        CancelUrl: request.cancelUrl,
        NotifyUrl: request.notifyUrl,
        IsTest: this.sandbox
      }

      // Generate hash check
      const hashCheck = this.generateHashCheck(paymentData)
      paymentData.HashCheck = hashCheck

      // Store payment record
      await prisma.payment.create({
        data: {
          id: crypto.randomUUID(),
          orderId: request.orderId,
          amount: request.amount,
          currency: request.currency,
          provider: 'ozow',
          status: 'pending',
          providerPaymentId: transactionReference,
          metadata: paymentData
        }
      })

      const baseUrl = this.sandbox
        ? 'https://staging.ozow.com'
        : 'https://pay.ozow.com'

      const queryString = Object.keys(paymentData)
        .map(key => `${key}=${encodeURIComponent(paymentData[key])}`)
        .join('&')

      return {
        success: true,
        paymentId: transactionReference,
        redirectUrl: `${baseUrl}?${queryString}`
      }
    } catch (error) {
      console.error('Ozow payment creation error:', error)
      return {
        success: false,
        paymentId: '',
        error: 'Failed to create Ozow payment'
      }
    }
  }

  async verifyPayment(data: Record<string, any>): Promise<boolean> {
    try {
      const receivedHash = data.HashCheck
      delete data.HashCheck

      const calculatedHash = this.generateHashCheck(data)
      
      return receivedHash === calculatedHash
    } catch (error) {
      console.error('Ozow verification error:', error)
      return false
    }
  }
}

// Main Payment Service
export class PaymentService {
  private payfast: PayFastService
  private ozow: OzowService

  constructor() {
    this.payfast = new PayFastService()
    this.ozow = new OzowService()
  }

  async getAvailablePaymentMethods(): Promise<PaymentMethod[]> {
    return [
      {
        id: 'payfast',
        name: 'PayFast',
        type: 'payfast',
        enabled: true,
        config: {}
      },
      {
        id: 'ozow',
        name: 'Ozow',
        type: 'ozow',
        enabled: true,
        config: {}
      }
    ]
  }

  async createPayment(method: string, request: PaymentRequest): Promise<PaymentResponse> {
    switch (method) {
      case 'payfast':
        return this.payfast.createPayment(request)
      case 'ozow':
        return this.ozow.createPayment(request)
      default:
        return {
          success: false,
          paymentId: '',
          error: 'Unsupported payment method'
        }
    }
  }

  async verifyPayment(method: string, data: Record<string, any>): Promise<boolean> {
    switch (method) {
      case 'payfast':
        return this.payfast.verifyPayment(data)
      case 'ozow':
        return this.ozow.verifyPayment(data)
      default:
        return false
    }
  }

  async updatePaymentStatus(paymentId: string, status: string, metadata?: Record<string, any>) {
    try {
      await prisma.payment.update({
        where: { providerPaymentId: paymentId },
        data: {
          status,
          metadata: metadata ? { ...metadata } : undefined,
          updatedAt: new Date()
        }
      })
    } catch (error) {
      console.error('Payment status update error:', error)
    }
  }
}
