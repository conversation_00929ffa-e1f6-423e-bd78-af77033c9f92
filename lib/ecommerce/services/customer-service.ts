// Customer Service - Customer management functionality
import { prisma } from '../config/database'
import { 
  User,
  CreateUserInput,
  UpdateUserInput,
  CustomerSearchParams,
  CustomerFilters,
  PaginatedResponse,
  UserAddress,
  UserAnalytics,
  ApiResponse,
  NotFoundError,
  ValidationError
} from '../types'
import { createSuccessResponse, createErrorResponse } from '../utils/api-response'
import bcrypt from 'bcryptjs'

export class CustomerService {
  /**
   * Get all customers with optional filtering, sorting, and pagination
   */
  async getCustomers(params: CustomerSearchParams = {}): Promise<ApiResponse<PaginatedResponse<User>>> {
    try {
      const {
        query,
        filters = {},
        sort = { field: 'createdAt', direction: 'desc' },
        page = 1,
        limit = 20
      } = params

      const skip = (page - 1) * limit
      const where: any = {}

      // Apply filters
      if (filters.isActive !== undefined) {
        where.isActive = filters.isActive
      }

      if (filters.isBlocked !== undefined) {
        where.isBlocked = filters.isBlocked
      }

      if (filters.emailVerified !== undefined) {
        where.emailVerified = filters.emailVerified
      }

      if (filters.loyaltyTier?.length) {
        where.loyaltyTier = { in: filters.loyaltyTier }
      }

      if (filters.totalSpentMin !== undefined) {
        where.totalSpent = { ...where.totalSpent, gte: filters.totalSpentMin }
      }

      if (filters.totalSpentMax !== undefined) {
        where.totalSpent = { ...where.totalSpent, lte: filters.totalSpentMax }
      }

      if (filters.orderCountMin !== undefined) {
        where.orderCount = { ...where.orderCount, gte: filters.orderCountMin }
      }

      if (filters.orderCountMax !== undefined) {
        where.orderCount = { ...where.orderCount, lte: filters.orderCountMax }
      }

      if (filters.createdAfter) {
        where.createdAt = { ...where.createdAt, gte: filters.createdAfter }
      }

      if (filters.createdBefore) {
        where.createdAt = { ...where.createdAt, lte: filters.createdBefore }
      }

      if (filters.lastOrderAfter) {
        where.lastOrderAt = { ...where.lastOrderAt, gte: filters.lastOrderAfter }
      }

      if (filters.lastOrderBefore) {
        where.lastOrderAt = { ...where.lastOrderAt, lte: filters.lastOrderBefore }
      }

      if (filters.tags?.length) {
        where.tags = { hasSome: filters.tags }
      }

      // Apply search query
      if (query) {
        where.OR = [
          { email: { contains: query, mode: 'insensitive' } },
          { firstName: { contains: query, mode: 'insensitive' } },
          { lastName: { contains: query, mode: 'insensitive' } },
          { displayName: { contains: query, mode: 'insensitive' } },
          { phone: { contains: query, mode: 'insensitive' } }
        ]
      }

      // Build order by
      const orderBy: any = {}
      orderBy[sort.field] = sort.direction

      // Execute queries
      const [customers, total] = await Promise.all([
        prisma.user.findMany({
          where,
          orderBy,
          skip,
          take: limit,
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            displayName: true,
            phone: true,
            dateOfBirth: true,
            gender: true,
            emailVerified: true,
            phoneVerified: true,
            lastLoginAt: true,
            acceptsMarketing: true,
            preferredLanguage: true,
            preferredCurrency: true,
            timezone: true,
            avatar: true,
            bio: true,
            isActive: true,
            isBlocked: true,
            customerSince: true,
            totalSpent: true,
            orderCount: true,
            averageOrderValue: true,
            lastOrderAt: true,
            loyaltyPoints: true,
            loyaltyTier: true,
            metafields: true,
            tags: true,
            notes: true,
            createdAt: true,
            updatedAt: true
          }
        }),
        prisma.user.count({ where })
      ])

      // Calculate pagination info
      const totalPages = Math.ceil(total / limit)
      const hasNext = page < totalPages
      const hasPrev = page > 1

      const response: PaginatedResponse<User> = {
        data: customers as User[],
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext,
          hasPrev
        }
      }

      return createSuccessResponse(response)
    } catch (error) {
      console.error('Get customers error:', error)
      return createErrorResponse('Failed to fetch customers')
    }
  }

  /**
   * Get a single customer by ID
   */
  async getCustomerById(id: string): Promise<ApiResponse<User>> {
    try {
      const customer = await prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          displayName: true,
          phone: true,
          dateOfBirth: true,
          gender: true,
          emailVerified: true,
          phoneVerified: true,
          lastLoginAt: true,
          acceptsMarketing: true,
          preferredLanguage: true,
          preferredCurrency: true,
          timezone: true,
          avatar: true,
          bio: true,
          isActive: true,
          isBlocked: true,
          customerSince: true,
          totalSpent: true,
          orderCount: true,
          averageOrderValue: true,
          lastOrderAt: true,
          loyaltyPoints: true,
          loyaltyTier: true,
          metafields: true,
          tags: true,
          notes: true,
          createdAt: true,
          updatedAt: true
        }
      })

      if (!customer) {
        throw new NotFoundError('Customer', id)
      }

      return createSuccessResponse(customer as User)
    } catch (error) {
      console.error('Get customer by ID error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to fetch customer')
    }
  }

  /**
   * Get customer by email
   */
  async getCustomerByEmail(email: string): Promise<ApiResponse<User>> {
    try {
      const customer = await prisma.user.findUnique({
        where: { email: email.toLowerCase() },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          displayName: true,
          phone: true,
          dateOfBirth: true,
          gender: true,
          emailVerified: true,
          phoneVerified: true,
          lastLoginAt: true,
          acceptsMarketing: true,
          preferredLanguage: true,
          preferredCurrency: true,
          timezone: true,
          avatar: true,
          bio: true,
          isActive: true,
          isBlocked: true,
          customerSince: true,
          totalSpent: true,
          orderCount: true,
          averageOrderValue: true,
          lastOrderAt: true,
          loyaltyPoints: true,
          loyaltyTier: true,
          metafields: true,
          tags: true,
          notes: true,
          createdAt: true,
          updatedAt: true
        }
      })

      if (!customer) {
        throw new NotFoundError('Customer', email)
      }

      return createSuccessResponse(customer as User)
    } catch (error) {
      console.error('Get customer by email error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to fetch customer')
    }
  }

  /**
   * Create a new customer
   */
  async createCustomer(input: CreateUserInput): Promise<ApiResponse<User>> {
    try {
      // Validate required fields
      if (!input.email) {
        throw new ValidationError('Email is required')
      }

      if (!this.isValidEmail(input.email)) {
        throw new ValidationError('Invalid email format')
      }

      // Check if customer already exists
      const existingCustomer = await prisma.user.findUnique({
        where: { email: input.email.toLowerCase() }
      })

      if (existingCustomer) {
        throw new ValidationError('Customer with this email already exists')
      }

      // Hash password if provided
      let hashedPassword = undefined
      if (input.password) {
        if (input.password.length < 8) {
          throw new ValidationError('Password must be at least 8 characters long')
        }
        hashedPassword = await bcrypt.hash(input.password, 12)
      }

      // Create customer
      const customer = await prisma.user.create({
        data: {
          email: input.email.toLowerCase(),
          password: hashedPassword,
          firstName: input.firstName,
          lastName: input.lastName,
          displayName: input.displayName,
          phone: input.phone,
          dateOfBirth: input.dateOfBirth,
          gender: input.gender,
          acceptsMarketing: input.acceptsMarketing || false,
          preferredLanguage: input.preferredLanguage || 'en',
          preferredCurrency: input.preferredCurrency || 'ZAR',
          timezone: input.timezone,
          avatar: input.avatar,
          bio: input.bio,
          emailVerified: false,
          phoneVerified: false,
          isActive: true,
          isBlocked: false,
          totalSpent: 0,
          orderCount: 0,
          averageOrderValue: 0,
          customerSince: new Date(),
          metafields: input.metafields,
          tags: input.tags,
          notes: input.notes
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          displayName: true,
          phone: true,
          dateOfBirth: true,
          gender: true,
          emailVerified: true,
          phoneVerified: true,
          lastLoginAt: true,
          acceptsMarketing: true,
          preferredLanguage: true,
          preferredCurrency: true,
          timezone: true,
          avatar: true,
          bio: true,
          isActive: true,
          isBlocked: true,
          customerSince: true,
          totalSpent: true,
          orderCount: true,
          averageOrderValue: true,
          lastOrderAt: true,
          loyaltyPoints: true,
          loyaltyTier: true,
          metafields: true,
          tags: true,
          notes: true,
          createdAt: true,
          updatedAt: true
        }
      })

      return createSuccessResponse(customer as User)
    } catch (error) {
      console.error('Create customer error:', error)
      if (error instanceof ValidationError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to create customer')
    }
  }

  /**
   * Update an existing customer
   */
  async updateCustomer(input: UpdateUserInput): Promise<ApiResponse<User>> {
    try {
      // Check if customer exists
      const existingCustomer = await prisma.user.findUnique({
        where: { id: input.id }
      })

      if (!existingCustomer) {
        throw new NotFoundError('Customer', input.id)
      }

      // If email is being updated, check for conflicts
      if (input.email && input.email !== existingCustomer.email) {
        const conflictingCustomer = await prisma.user.findUnique({
          where: { email: input.email.toLowerCase() }
        })

        if (conflictingCustomer) {
          throw new ValidationError('Customer with this email already exists')
        }
      }

      // Update customer
      const customer = await prisma.user.update({
        where: { id: input.id },
        data: {
          email: input.email?.toLowerCase(),
          firstName: input.firstName,
          lastName: input.lastName,
          displayName: input.displayName,
          phone: input.phone,
          dateOfBirth: input.dateOfBirth,
          gender: input.gender,
          acceptsMarketing: input.acceptsMarketing,
          preferredLanguage: input.preferredLanguage,
          preferredCurrency: input.preferredCurrency,
          timezone: input.timezone,
          avatar: input.avatar,
          bio: input.bio,
          isActive: input.isActive,
          isBlocked: input.isBlocked,
          loyaltyPoints: input.loyaltyPoints,
          loyaltyTier: input.loyaltyTier,
          metafields: input.metafields,
          tags: input.tags,
          notes: input.notes
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          displayName: true,
          phone: true,
          dateOfBirth: true,
          gender: true,
          emailVerified: true,
          phoneVerified: true,
          lastLoginAt: true,
          acceptsMarketing: true,
          preferredLanguage: true,
          preferredCurrency: true,
          timezone: true,
          avatar: true,
          bio: true,
          isActive: true,
          isBlocked: true,
          customerSince: true,
          totalSpent: true,
          orderCount: true,
          averageOrderValue: true,
          lastOrderAt: true,
          loyaltyPoints: true,
          loyaltyTier: true,
          metafields: true,
          tags: true,
          notes: true,
          createdAt: true,
          updatedAt: true
        }
      })

      return createSuccessResponse(customer as User)
    } catch (error) {
      console.error('Update customer error:', error)
      if (error instanceof NotFoundError || error instanceof ValidationError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to update customer')
    }
  }

  /**
   * Delete a customer
   */
  async deleteCustomer(id: string): Promise<ApiResponse<void>> {
    try {
      // Check if customer exists
      const existingCustomer = await prisma.user.findUnique({
        where: { id }
      })

      if (!existingCustomer) {
        throw new NotFoundError('Customer', id)
      }

      // Check if customer has orders
      const orderCount = await prisma.order.count({
        where: { customerId: id }
      })

      if (orderCount > 0) {
        // Instead of deleting, mark as inactive
        await prisma.user.update({
          where: { id },
          data: { 
            isActive: false,
            isBlocked: true,
            notes: `Account deactivated on ${new Date().toISOString()}`
          }
        })
      } else {
        // Safe to delete if no orders
        await prisma.user.delete({
          where: { id }
        })
      }

      return createSuccessResponse(undefined)
    } catch (error) {
      console.error('Delete customer error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to delete customer')
    }
  }

  /**
   * Get customer addresses
   */
  async getCustomerAddresses(customerId: string): Promise<ApiResponse<UserAddress[]>> {
    try {
      const addresses = await prisma.userAddress.findMany({
        where: { userId: customerId },
        orderBy: { isDefault: 'desc' }
      })

      return createSuccessResponse(addresses as UserAddress[])
    } catch (error) {
      console.error('Get customer addresses error:', error)
      return createErrorResponse('Failed to fetch customer addresses')
    }
  }

  /**
   * Add customer address
   */
  async addCustomerAddress(customerId: string, address: Omit<UserAddress, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<UserAddress>> {
    try {
      // Check if customer exists
      const customer = await prisma.user.findUnique({
        where: { id: customerId }
      })

      if (!customer) {
        throw new NotFoundError('Customer', customerId)
      }

      // If this is set as default, unset other defaults
      if (address.isDefault) {
        await prisma.userAddress.updateMany({
          where: { userId: customerId },
          data: { isDefault: false }
        })
      }

      const newAddress = await prisma.userAddress.create({
        data: {
          ...address,
          userId: customerId
        }
      })

      return createSuccessResponse(newAddress as UserAddress)
    } catch (error) {
      console.error('Add customer address error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to add customer address')
    }
  }

  /**
   * Update customer address
   */
  async updateCustomerAddress(customerId: string, addressId: string, updates: Partial<UserAddress>): Promise<ApiResponse<UserAddress>> {
    try {
      const address = await prisma.userAddress.findFirst({
        where: {
          id: addressId,
          userId: customerId
        }
      })

      if (!address) {
        throw new NotFoundError('Address', addressId)
      }

      // If this is being set as default, unset other defaults
      if (updates.isDefault) {
        await prisma.userAddress.updateMany({
          where: { 
            userId: customerId,
            id: { not: addressId }
          },
          data: { isDefault: false }
        })
      }

      const updatedAddress = await prisma.userAddress.update({
        where: { id: addressId },
        data: updates
      })

      return createSuccessResponse(updatedAddress as UserAddress)
    } catch (error) {
      console.error('Update customer address error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to update customer address')
    }
  }

  /**
   * Delete customer address
   */
  async deleteCustomerAddress(customerId: string, addressId: string): Promise<ApiResponse<void>> {
    try {
      const address = await prisma.userAddress.findFirst({
        where: {
          id: addressId,
          userId: customerId
        }
      })

      if (!address) {
        throw new NotFoundError('Address', addressId)
      }

      await prisma.userAddress.delete({
        where: { id: addressId }
      })

      return createSuccessResponse(undefined)
    } catch (error) {
      console.error('Delete customer address error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to delete customer address')
    }
  }

  /**
   * Get customer analytics
   */
  async getCustomerAnalytics(customerId: string, startDate: Date, endDate: Date): Promise<ApiResponse<UserAnalytics>> {
    try {
      const customer = await prisma.user.findUnique({
        where: { id: customerId }
      })

      if (!customer) {
        throw new NotFoundError('Customer', customerId)
      }

      // Get orders in date range
      const orders = await prisma.order.findMany({
        where: {
          customerId,
          createdAt: {
            gte: startDate,
            lte: endDate
          },
          paymentStatus: 'paid'
        },
        include: {
          items: {
            include: {
              product: {
                include: {
                  categories: true
                }
              }
            }
          }
        }
      })

      const totalSpent = orders.reduce((sum, order) => sum + order.total, 0)
      const orderCount = orders.length
      const averageOrderValue = orderCount > 0 ? totalSpent / orderCount : 0

      // Calculate lifetime value (all time)
      const lifetimeOrders = await prisma.order.aggregate({
        where: {
          customerId,
          paymentStatus: 'paid'
        },
        _sum: { total: true },
        _count: true
      })

      const lifetimeValue = lifetimeOrders._sum.total || 0

      // Get last activity
      const lastActivity = await prisma.userActivity.findFirst({
        where: { userId: customerId },
        orderBy: { createdAt: 'desc' }
      })

      // Calculate preferred categories
      const categoryMap = new Map<string, number>()
      orders.forEach(order => {
        order.items.forEach(item => {
          item.product.categories.forEach(category => {
            categoryMap.set(category.name, (categoryMap.get(category.name) || 0) + item.quantity)
          })
        })
      })

      const preferredCategories = Array.from(categoryMap.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([category]) => category)

      // Simple engagement score calculation
      const daysSinceLastOrder = customer.lastOrderAt 
        ? Math.floor((Date.now() - customer.lastOrderAt.getTime()) / (1000 * 60 * 60 * 24))
        : 999

      let engagementScore = 0
      if (daysSinceLastOrder < 30) engagementScore += 40
      else if (daysSinceLastOrder < 90) engagementScore += 20
      
      if (orderCount > 5) engagementScore += 30
      else if (orderCount > 1) engagementScore += 15

      if (averageOrderValue > 1000) engagementScore += 30
      else if (averageOrderValue > 500) engagementScore += 15

      // Churn risk assessment
      let churnRisk: 'low' | 'medium' | 'high' = 'low'
      if (daysSinceLastOrder > 180) churnRisk = 'high'
      else if (daysSinceLastOrder > 90) churnRisk = 'medium'

      const analytics: UserAnalytics = {
        userId: customerId,
        totalSpent,
        orderCount,
        averageOrderValue,
        lifetimeValue,
        acquisitionDate: customer.customerSince || customer.createdAt,
        acquisitionChannel: 'direct', // Would need tracking implementation
        lastActivityDate: lastActivity?.createdAt || customer.lastLoginAt || customer.createdAt,
        engagementScore,
        churnRisk,
        preferredCategories,
        preferredBrands: [], // Would need brand tracking
        period: {
          start: startDate,
          end: endDate
        }
      }

      return createSuccessResponse(analytics)
    } catch (error) {
      console.error('Get customer analytics error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to fetch customer analytics')
    }
  }

  /**
   * Update customer stats (called when orders are placed)
   */
  async updateCustomerStats(customerId: string): Promise<ApiResponse<void>> {
    try {
      const stats = await prisma.order.aggregate({
        where: {
          customerId,
          paymentStatus: 'paid'
        },
        _sum: { total: true },
        _count: true,
        _avg: { total: true }
      })

      const lastOrder = await prisma.order.findFirst({
        where: {
          customerId,
          paymentStatus: 'paid'
        },
        orderBy: { createdAt: 'desc' }
      })

      await prisma.user.update({
        where: { id: customerId },
        data: {
          totalSpent: stats._sum.total || 0,
          orderCount: stats._count,
          averageOrderValue: stats._avg.total || 0,
          lastOrderAt: lastOrder?.createdAt
        }
      })

      return createSuccessResponse(undefined)
    } catch (error) {
      console.error('Update customer stats error:', error)
      return createErrorResponse('Failed to update customer stats')
    }
  }

  /**
   * Search customers
   */
  async searchCustomers(query: string, limit: number = 20): Promise<ApiResponse<User[]>> {
    try {
      const customers = await prisma.user.findMany({
        where: {
          OR: [
            { email: { contains: query, mode: 'insensitive' } },
            { firstName: { contains: query, mode: 'insensitive' } },
            { lastName: { contains: query, mode: 'insensitive' } },
            { displayName: { contains: query, mode: 'insensitive' } },
            { phone: { contains: query, mode: 'insensitive' } }
          ]
        },
        take: limit,
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          displayName: true,
          phone: true,
          avatar: true,
          isActive: true,
          isBlocked: true,
          totalSpent: true,
          orderCount: true,
          lastOrderAt: true,
          createdAt: true
        }
      })

      return createSuccessResponse(customers as User[])
    } catch (error) {
      console.error('Search customers error:', error)
      return createErrorResponse('Failed to search customers')
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }
}