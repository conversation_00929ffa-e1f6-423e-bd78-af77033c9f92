// Production-Ready Order Fulfillment Service
// Handles automated order processing, inventory allocation, shipping, and tracking

import { prisma } from '../config/database'
import { ApiResponse } from '../types'
import { createSuccessResponse, createErrorResponse } from '../utils/api-response'
import { EventEmitter } from 'events'

export interface FulfillmentRequest {
  orderId: string
  items: Array<{
    orderItemId: string
    quantity: number
    warehouseLocation?: string
  }>
  shippingMethod: {
    carrier: string
    service: string
    trackingNumber?: string
  }
  fulfillmentCenter?: string
  priority: 'low' | 'normal' | 'high' | 'urgent'
  notes?: string
  notifyCustomer?: boolean
}

export interface FulfillmentResult {
  success: boolean
  fulfillmentId?: string
  trackingNumber?: string
  estimatedDelivery?: Date
  error?: string
  warnings?: string[]
}

export interface InventoryAllocation {
  orderItemId: string
  productId: string
  variantId?: string
  requestedQuantity: number
  allocatedQuantity: number
  warehouseLocation: string
  reservationId: string
}

export interface ShippingLabel {
  labelUrl: string
  trackingNumber: string
  carrier: string
  service: string
  cost: number
  estimatedDelivery: Date
}

export class FulfillmentService extends EventEmitter {
  private readonly maxRetries = 3
  private readonly retryDelay = 1000 // 1 second

  constructor() {
    super()
    this.setupEventHandlers()
  }

  /**
   * Process order fulfillment with automated workflow
   */
  async processFulfillment(request: FulfillmentRequest): Promise<FulfillmentResult> {
    try {
      // 1. Validate order and items
      const validation = await this.validateFulfillmentRequest(request)
      if (!validation.valid) {
        return { success: false, error: validation.error }
      }

      // 2. Check inventory availability
      const inventoryCheck = await this.checkInventoryAvailability(request.items)
      if (!inventoryCheck.available) {
        return { 
          success: false, 
          error: 'Insufficient inventory',
          warnings: inventoryCheck.warnings
        }
      }

      // 3. Allocate inventory
      const allocations = await this.allocateInventory(request.items)
      
      // 4. Create fulfillment record
      const fulfillment = await this.createFulfillmentRecord(request, allocations)

      // 5. Generate shipping label
      const shippingLabel = await this.generateShippingLabel(request, fulfillment.id)

      // 6. Update fulfillment with shipping details
      await this.updateFulfillmentShipping(fulfillment.id, shippingLabel)

      // 7. Update order status
      await this.updateOrderFulfillmentStatus(request.orderId)

      // 8. Send notifications
      if (request.notifyCustomer !== false) {
        await this.sendFulfillmentNotifications(fulfillment.id)
      }

      // 9. Emit fulfillment events
      this.emit('fulfillment:created', { fulfillmentId: fulfillment.id, orderId: request.orderId })
      this.emit('fulfillment:shipped', { fulfillmentId: fulfillment.id, trackingNumber: shippingLabel.trackingNumber })

      return {
        success: true,
        fulfillmentId: fulfillment.id,
        trackingNumber: shippingLabel.trackingNumber,
        estimatedDelivery: shippingLabel.estimatedDelivery
      }

    } catch (error) {
      console.error('Fulfillment processing error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown fulfillment error'
      }
    }
  }

  /**
   * Validate fulfillment request
   */
  private async validateFulfillmentRequest(request: FulfillmentRequest): Promise<{ valid: boolean; error?: string }> {
    // Check if order exists and is in fulfillable state
    const order = await prisma.order.findUnique({
      where: { id: request.orderId },
      include: { items: true }
    })

    if (!order) {
      return { valid: false, error: 'Order not found' }
    }

    if (!['confirmed', 'processing'].includes(order.status)) {
      return { valid: false, error: `Order status '${order.status}' is not fulfillable` }
    }

    if (order.paymentStatus !== 'paid' && order.paymentStatus !== 'authorized') {
      return { valid: false, error: 'Order payment not confirmed' }
    }

    // Validate items exist in order
    for (const item of request.items) {
      const orderItem = order.items.find(oi => oi.id === item.orderItemId)
      if (!orderItem) {
        return { valid: false, error: `Order item ${item.orderItemId} not found` }
      }
      if (item.quantity > orderItem.quantity) {
        return { valid: false, error: `Requested quantity exceeds order quantity for item ${item.orderItemId}` }
      }
    }

    return { valid: true }
  }

  /**
   * Check inventory availability for all items
   */
  private async checkInventoryAvailability(items: FulfillmentRequest['items']): Promise<{ available: boolean; warnings?: string[] }> {
    const warnings: string[] = []
    
    for (const item of items) {
      // Get order item details
      const orderItem = await prisma.orderItem.findUnique({
        where: { id: item.orderItemId }
      })

      if (!orderItem) {
        return { available: false, warnings: [`Order item ${item.orderItemId} not found`] }
      }

      // Check inventory via inventory service
      try {
        const response = await fetch('/api/inventory/check', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            productId: orderItem.productId,
            variantId: orderItem.variantId,
            quantity: item.quantity
          })
        })

        const result = await response.json()
        
        if (!result.available) {
          return { 
            available: false, 
            warnings: [`Insufficient inventory for product ${orderItem.productId}. Available: ${result.availableQuantity}, Requested: ${item.quantity}`]
          }
        }

        if (result.availableQuantity < result.recommendedStock) {
          warnings.push(`Low stock warning for product ${orderItem.productId}`)
        }

      } catch (error) {
        console.error('Inventory check error:', error)
        return { available: false, warnings: ['Inventory service unavailable'] }
      }
    }

    return { available: true, warnings: warnings.length > 0 ? warnings : undefined }
  }

  /**
   * Allocate inventory for fulfillment
   */
  private async allocateInventory(items: FulfillmentRequest['items']): Promise<InventoryAllocation[]> {
    const allocations: InventoryAllocation[] = []

    for (const item of items) {
      const orderItem = await prisma.orderItem.findUnique({
        where: { id: item.orderItemId }
      })

      if (!orderItem) {
        throw new Error(`Order item ${item.orderItemId} not found`)
      }

      // Reserve inventory
      try {
        const response = await fetch('/api/inventory/reserve', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            productId: orderItem.productId,
            variantId: orderItem.variantId,
            quantity: item.quantity,
            orderId: orderItem.orderId,
            reason: 'fulfillment_allocation'
          })
        })

        const result = await response.json()
        
        if (!result.success) {
          throw new Error(`Failed to allocate inventory: ${result.error}`)
        }

        allocations.push({
          orderItemId: item.orderItemId,
          productId: orderItem.productId,
          variantId: orderItem.variantId,
          requestedQuantity: item.quantity,
          allocatedQuantity: result.allocatedQuantity,
          warehouseLocation: item.warehouseLocation || 'main',
          reservationId: result.reservationId
        })

      } catch (error) {
        console.error('Inventory allocation error:', error)
        throw new Error(`Failed to allocate inventory for item ${item.orderItemId}`)
      }
    }

    return allocations
  }

  /**
   * Create fulfillment record in database
   */
  private async createFulfillmentRecord(request: FulfillmentRequest, allocations: InventoryAllocation[]) {
    return await prisma.orderFulfillment.create({
      data: {
        orderId: request.orderId,
        status: 'pending',
        carrier: request.shippingMethod.carrier,
        service: request.shippingMethod.service,
        trackingNumber: request.shippingMethod.trackingNumber,
        locationName: request.fulfillmentCenter || 'Main Warehouse',
        notes: request.notes,
        notifyCustomer: request.notifyCustomer !== false,
        items: {
          create: request.items.map(item => ({
            orderItemId: item.orderItemId,
            quantity: item.quantity
          }))
        }
      },
      include: {
        items: true
      }
    })
  }

  /**
   * Setup event handlers for fulfillment workflow
   */
  private setupEventHandlers() {
    this.on('fulfillment:created', this.handleFulfillmentCreated.bind(this))
    this.on('fulfillment:shipped', this.handleFulfillmentShipped.bind(this))
    this.on('fulfillment:delivered', this.handleFulfillmentDelivered.bind(this))
    this.on('fulfillment:failed', this.handleFulfillmentFailed.bind(this))
  }

  private async handleFulfillmentCreated(data: { fulfillmentId: string; orderId: string }) {
    console.log(`Fulfillment created: ${data.fulfillmentId} for order: ${data.orderId}`)
    // Additional processing can be added here
  }

  private async handleFulfillmentShipped(data: { fulfillmentId: string; trackingNumber: string }) {
    console.log(`Fulfillment shipped: ${data.fulfillmentId}, tracking: ${data.trackingNumber}`)
    // Update order status, send notifications, etc.
  }

  private async handleFulfillmentDelivered(data: { fulfillmentId: string }) {
    console.log(`Fulfillment delivered: ${data.fulfillmentId}`)
    // Mark order as delivered, trigger post-delivery workflows
  }

  private async handleFulfillmentFailed(data: { fulfillmentId: string; error: string }) {
    console.error(`Fulfillment failed: ${data.fulfillmentId}, error: ${data.error}`)
    // Handle failure scenarios, rollback inventory, notify staff
  }

  /**
   * Generate shipping label through carrier API
   */
  private async generateShippingLabel(request: FulfillmentRequest, fulfillmentId: string): Promise<ShippingLabel> {
    try {
      // Get order details for shipping
      const order = await prisma.order.findUnique({
        where: { id: request.orderId },
        include: { items: true }
      })

      if (!order) {
        throw new Error('Order not found for shipping label generation')
      }

      // Calculate package dimensions and weight
      const packageDetails = await this.calculatePackageDetails(request.items)

      // Generate shipping label via shipping service
      const response = await fetch('/api/shipping/labels', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          orderId: request.orderId,
          fulfillmentId,
          carrier: request.shippingMethod.carrier,
          service: request.shippingMethod.service,
          fromAddress: await this.getWarehouseAddress(request.fulfillmentCenter),
          toAddress: order.shippingAddress,
          packageDetails,
          declaredValue: order.total,
          currency: order.currency
        })
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(`Shipping label generation failed: ${result.error}`)
      }

      return {
        labelUrl: result.labelUrl,
        trackingNumber: result.trackingNumber,
        carrier: request.shippingMethod.carrier,
        service: request.shippingMethod.service,
        cost: result.cost,
        estimatedDelivery: new Date(result.estimatedDelivery)
      }

    } catch (error) {
      console.error('Shipping label generation error:', error)
      throw new Error(`Failed to generate shipping label: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Update fulfillment with shipping information
   */
  private async updateFulfillmentShipping(fulfillmentId: string, shippingLabel: ShippingLabel) {
    return await prisma.orderFulfillment.update({
      where: { id: fulfillmentId },
      data: {
        status: 'open',
        trackingNumber: shippingLabel.trackingNumber,
        trackingUrl: `https://tracking.example.com/${shippingLabel.trackingNumber}`,
        shippedAt: new Date(),
        estimatedDeliveryAt: shippingLabel.estimatedDelivery,
        updatedAt: new Date()
      }
    })
  }

  /**
   * Update order fulfillment status based on fulfillments
   */
  private async updateOrderFulfillmentStatus(orderId: string) {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        items: true,
        fulfillments: {
          include: { items: true }
        }
      }
    })

    if (!order) return

    // Check if order is fully fulfilled
    const isFullyFulfilled = this.checkIfOrderFullyFulfilled(order)
    const isPartiallyFulfilled = order.fulfillments.length > 0

    let newStatus = order.status
    let newFulfillmentStatus = order.fulfillmentStatus

    if (isFullyFulfilled) {
      newStatus = 'shipped'
      newFulfillmentStatus = 'fulfilled'
    } else if (isPartiallyFulfilled) {
      newFulfillmentStatus = 'partially_fulfilled'
    }

    if (newStatus !== order.status || newFulfillmentStatus !== order.fulfillmentStatus) {
      await prisma.order.update({
        where: { id: orderId },
        data: {
          status: newStatus,
          fulfillmentStatus: newFulfillmentStatus,
          shippedAt: isFullyFulfilled ? new Date() : order.shippedAt,
          updatedAt: new Date()
        }
      })
    }
  }

  /**
   * Send fulfillment notifications to customer
   */
  private async sendFulfillmentNotifications(fulfillmentId: string) {
    try {
      const fulfillment = await prisma.orderFulfillment.findUnique({
        where: { id: fulfillmentId },
        include: {
          order: true,
          items: {
            include: {
              orderItem: true
            }
          }
        }
      })

      if (!fulfillment) return

      // Send email notification
      await fetch('/api/notifications/fulfillment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'fulfillment_shipped',
          orderId: fulfillment.orderId,
          fulfillmentId: fulfillment.id,
          customerEmail: fulfillment.order.customerEmail,
          trackingNumber: fulfillment.trackingNumber,
          estimatedDelivery: fulfillment.estimatedDeliveryAt
        })
      })

      // Send SMS notification if enabled
      if (fulfillment.order.customerPhone) {
        await fetch('/api/notifications/sms', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            to: fulfillment.order.customerPhone,
            message: `Your order #${fulfillment.order.orderNumber} has been shipped! Track it: ${fulfillment.trackingUrl}`
          })
        })
      }

      // Update notification status
      await prisma.orderFulfillment.update({
        where: { id: fulfillmentId },
        data: {
          emailSent: true,
          smsSent: !!fulfillment.order.customerPhone
        }
      })

    } catch (error) {
      console.error('Fulfillment notification error:', error)
    }
  }

  /**
   * Calculate package details for shipping
   */
  private async calculatePackageDetails(items: FulfillmentRequest['items']) {
    let totalWeight = 0
    let totalVolume = 0

    for (const item of items) {
      const orderItem = await prisma.orderItem.findUnique({
        where: { id: item.orderItemId },
        include: {
          product: true
        }
      })

      if (orderItem?.product) {
        // Get product dimensions and weight from product data
        const weight = parseFloat(orderItem.product.weight || '0') * item.quantity
        const volume = (
          parseFloat(orderItem.product.length || '0') *
          parseFloat(orderItem.product.width || '0') *
          parseFloat(orderItem.product.height || '0')
        ) * item.quantity

        totalWeight += weight
        totalVolume += volume
      }
    }

    // Calculate package dimensions (simplified - in production, use bin packing algorithm)
    const packageDimensions = this.calculateOptimalPackageDimensions(totalVolume)

    return {
      weight: Math.max(totalWeight, 0.1), // Minimum 100g
      dimensions: packageDimensions,
      declaredValue: 0 // Will be set from order total
    }
  }

  /**
   * Get warehouse address for shipping
   */
  private async getWarehouseAddress(fulfillmentCenter?: string) {
    // In production, this would fetch from warehouse configuration
    return {
      name: 'Coco Milk Kids Warehouse',
      address1: '123 Warehouse Street',
      city: 'Cape Town',
      province: 'Western Cape',
      postalCode: '8001',
      country: 'South Africa',
      phone: '+27 21 123 4567'
    }
  }

  /**
   * Calculate optimal package dimensions
   */
  private calculateOptimalPackageDimensions(volume: number) {
    // Simplified calculation - in production, use proper bin packing
    const cubeRoot = Math.cbrt(volume)
    return {
      length: Math.max(cubeRoot * 1.2, 10), // cm
      width: Math.max(cubeRoot, 10), // cm
      height: Math.max(cubeRoot * 0.8, 5), // cm
    }
  }

  /**
   * Check if order is fully fulfilled
   */
  private checkIfOrderFullyFulfilled(order: any): boolean {
    for (const orderItem of order.items) {
      const fulfilledQuantity = order.fulfillments.reduce((total: number, fulfillment: any) => {
        const fulfillmentItem = fulfillment.items.find((item: any) => item.orderItemId === orderItem.id)
        return total + (fulfillmentItem?.quantity || 0)
      }, 0)

      if (fulfilledQuantity < orderItem.quantity) {
        return false
      }
    }
    return true
  }

  /**
   * Get fulfillment by ID
   */
  async getFulfillment(fulfillmentId: string) {
    return await prisma.orderFulfillment.findUnique({
      where: { id: fulfillmentId },
      include: {
        order: true,
        items: {
          include: {
            orderItem: {
              include: {
                product: true
              }
            }
          }
        }
      }
    })
  }

  /**
   * Get all fulfillments for an order
   */
  async getOrderFulfillments(orderId: string) {
    return await prisma.orderFulfillment.findMany({
      where: { orderId },
      include: {
        items: {
          include: {
            orderItem: {
              include: {
                product: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  /**
   * Update fulfillment status
   */
  async updateFulfillmentStatus(fulfillmentId: string, status: string, notes?: string) {
    const fulfillment = await prisma.orderFulfillment.update({
      where: { id: fulfillmentId },
      data: {
        status,
        notes: notes || undefined,
        deliveredAt: status === 'delivered' ? new Date() : undefined,
        updatedAt: new Date()
      }
    })

    // Emit status change event
    this.emit(`fulfillment:${status}`, { fulfillmentId })

    // Update order status if delivered
    if (status === 'delivered') {
      await this.checkAndUpdateOrderDeliveryStatus(fulfillment.orderId)
    }

    return fulfillment
  }

  /**
   * Cancel fulfillment and restore inventory
   */
  async cancelFulfillment(fulfillmentId: string, reason: string) {
    const fulfillment = await this.getFulfillment(fulfillmentId)

    if (!fulfillment) {
      throw new Error('Fulfillment not found')
    }

    if (['delivered', 'cancelled'].includes(fulfillment.status)) {
      throw new Error(`Cannot cancel fulfillment with status: ${fulfillment.status}`)
    }

    // Restore inventory for each item
    for (const item of fulfillment.items) {
      await fetch('/api/inventory/restore', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productId: item.orderItem.productId,
          variantId: item.orderItem.variantId,
          quantity: item.quantity,
          orderId: fulfillment.orderId,
          reason: `fulfillment_cancelled: ${reason}`
        })
      })
    }

    // Update fulfillment status
    await prisma.orderFulfillment.update({
      where: { id: fulfillmentId },
      data: {
        status: 'cancelled',
        notes: `Cancelled: ${reason}`,
        updatedAt: new Date()
      }
    })

    // Update order fulfillment status
    await this.updateOrderFulfillmentStatus(fulfillment.orderId)

    this.emit('fulfillment:cancelled', { fulfillmentId, reason })

    return { success: true, message: 'Fulfillment cancelled successfully' }
  }

  /**
   * Track fulfillment status via carrier API
   */
  async trackFulfillment(fulfillmentId: string) {
    const fulfillment = await this.getFulfillment(fulfillmentId)

    if (!fulfillment || !fulfillment.trackingNumber) {
      throw new Error('Fulfillment or tracking number not found')
    }

    try {
      // Call carrier tracking API
      const response = await fetch('/api/shipping/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          carrier: fulfillment.carrier,
          trackingNumber: fulfillment.trackingNumber
        })
      })

      const trackingData = await response.json()

      if (trackingData.success) {
        // Update fulfillment status if changed
        if (trackingData.status !== fulfillment.status) {
          await this.updateFulfillmentStatus(fulfillmentId, trackingData.status)
        }

        return {
          success: true,
          trackingNumber: fulfillment.trackingNumber,
          status: trackingData.status,
          events: trackingData.events,
          estimatedDelivery: trackingData.estimatedDelivery,
          actualDelivery: trackingData.actualDelivery
        }
      }

      return { success: false, error: trackingData.error }

    } catch (error) {
      console.error('Tracking error:', error)
      return { success: false, error: 'Failed to track fulfillment' }
    }
  }
}
