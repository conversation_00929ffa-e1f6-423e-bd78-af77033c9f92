import { prisma } from '@/lib/prisma'

export interface DashboardMetrics {
  // Overview Stats
  totalRevenue: number
  totalOrders: number
  totalProducts: number
  totalCustomers: number
  
  // Growth Metrics
  revenueGrowth: number
  orderGrowth: number
  customerGrowth: number
  productGrowth: number
  
  // Performance Metrics
  averageOrderValue: number
  conversionRate: number
  customerLifetimeValue: number
  inventoryTurnover: number
  
  // Time-based Data
  revenueByDay: Array<{ date: string; revenue: number; orders: number }>
  ordersByStatus: Array<{ status: string; count: number; percentage: number }>
  topProducts: Array<{ id: string; name: string; sales: number; revenue: number }>
  topCategories: Array<{ name: string; sales: number; revenue: number; percentage: number }>
  customerSegments: Array<{ segment: string; count: number; revenue: number }>
  
  // Inventory Alerts
  lowStockProducts: Array<{ id: string; name: string; stock: number; threshold: number }>
  outOfStockProducts: Array<{ id: string; name: string; lastSold: Date | null }>
  
  // Recent Activity
  recentOrders: Array<{ id: string; customerName: string; total: number; status: string; createdAt: Date }>
  recentCustomers: Array<{ id: string; name: string; email: string; totalSpent: number; createdAt: Date }>
}

export interface TimeRange {
  start: Date
  end: Date
  previousStart: Date
  previousEnd: Date
}

export class DashboardAnalyticsService {
  /**
   * Get comprehensive dashboard metrics for a time period
   */
  async getDashboardMetrics(days: number = 30): Promise<DashboardMetrics> {
    const timeRange = this.getTimeRange(days)
    
    try {
      const [
        overviewStats,
        growthMetrics,
        performanceMetrics,
        timeBasedData,
        inventoryData,
        recentActivity
      ] = await Promise.all([
        this.getOverviewStats(timeRange),
        this.getGrowthMetrics(timeRange),
        this.getPerformanceMetrics(timeRange),
        this.getTimeBasedData(timeRange),
        this.getInventoryData(),
        this.getRecentActivity()
      ])

      return {
        ...overviewStats,
        ...growthMetrics,
        ...performanceMetrics,
        ...timeBasedData,
        ...inventoryData,
        ...recentActivity
      }
    } catch (error) {
      console.error('Dashboard metrics error:', error)
      throw new Error('Failed to fetch dashboard metrics')
    }
  }

  /**
   * Get overview statistics
   */
  private async getOverviewStats(timeRange: TimeRange) {
    const [orderStats, productCount, customerCount] = await Promise.all([
      prisma.order.aggregate({
        where: {
          createdAt: { gte: timeRange.start, lte: timeRange.end },
          status: { in: ['processing', 'shipped', 'delivered'] }
        },
        _sum: { total: true },
        _count: { id: true }
      }),
      prisma.product.count({
        where: { status: 'active' }
      }),
      prisma.user.count({
        where: { 
          createdAt: { gte: timeRange.start, lte: timeRange.end }
        }
      })
    ])

    return {
      totalRevenue: Number(orderStats._sum.total) || 0,
      totalOrders: orderStats._count.id || 0,
      totalProducts: productCount,
      totalCustomers: customerCount
    }
  }

  /**
   * Get growth metrics compared to previous period
   */
  private async getGrowthMetrics(timeRange: TimeRange) {
    const [currentPeriod, previousPeriod] = await Promise.all([
      prisma.order.aggregate({
        where: {
          createdAt: { gte: timeRange.start, lte: timeRange.end },
          status: { in: ['processing', 'shipped', 'delivered'] }
        },
        _sum: { total: true },
        _count: { id: true }
      }),
      prisma.order.aggregate({
        where: {
          createdAt: { gte: timeRange.previousStart, lte: timeRange.previousEnd },
          status: { in: ['processing', 'shipped', 'delivered'] }
        },
        _sum: { total: true },
        _count: { id: true }
      })
    ])

    const currentRevenue = Number(currentPeriod._sum.total) || 0
    const previousRevenue = Number(previousPeriod._sum.total) || 0
    const currentOrders = currentPeriod._count.id || 0
    const previousOrders = previousPeriod._count.id || 0

    const revenueGrowth = previousRevenue > 0 ? ((currentRevenue - previousRevenue) / previousRevenue) * 100 : 0
    const orderGrowth = previousOrders > 0 ? ((currentOrders - previousOrders) / previousOrders) * 100 : 0

    // Get customer growth
    const [currentCustomers, previousCustomers] = await Promise.all([
      prisma.user.count({
        where: { 
          createdAt: { gte: timeRange.start, lte: timeRange.end }
        }
      }),
      prisma.user.count({
        where: { 
          createdAt: { gte: timeRange.previousStart, lte: timeRange.previousEnd }
        }
      })
    ])

    const customerGrowth = previousCustomers > 0 ? ((currentCustomers - previousCustomers) / previousCustomers) * 100 : 0

    return {
      revenueGrowth,
      orderGrowth,
      customerGrowth,
      productGrowth: 0 // Products don't have time-based growth in this context
    }
  }

  /**
   * Get performance metrics
   */
  private async getPerformanceMetrics(timeRange: TimeRange) {
    const orderStats = await prisma.order.aggregate({
      where: {
        createdAt: { gte: timeRange.start, lte: timeRange.end },
        status: { in: ['processing', 'shipped', 'delivered'] }
      },
      _avg: { total: true },
      _sum: { total: true },
      _count: { id: true }
    })

    const averageOrderValue = Number(orderStats._avg.total) || 0

    // Calculate customer lifetime value (simplified)
    const customerLTV = await prisma.user.findMany({
      include: {
        orders: {
          where: { status: { in: ['processing', 'shipped', 'delivered'] } },
          select: { total: true }
        }
      },
      take: 100 // Sample for performance
    })

    const avgLTV = customerLTV.reduce((sum, customer) => {
      const customerTotal = customer.orders.reduce((total, order) => total + Number(order.total), 0)
      return sum + customerTotal
    }, 0) / (customerLTV.length || 1)

    return {
      averageOrderValue,
      conversionRate: 0, // Would need page view data
      customerLifetimeValue: avgLTV,
      inventoryTurnover: 0 // Would need inventory movement data
    }
  }

  /**
   * Get time-based data for charts
   */
  private async getTimeBasedData(timeRange: TimeRange) {
    // Revenue by day
    const dailyRevenue = await prisma.$queryRaw<Array<{
      date: Date
      revenue: number
      orders: bigint
    }>>`
      SELECT 
        DATE("createdAt") as date,
        SUM(total) as revenue,
        COUNT(*) as orders
      FROM "orders"
      WHERE "createdAt" >= ${timeRange.start}
        AND "createdAt" <= ${timeRange.end}
        AND status IN ('processing', 'shipped', 'delivered')
      GROUP BY DATE("createdAt")
      ORDER BY date ASC
    `

    const revenueByDay = dailyRevenue.map(day => ({
      date: day.date.toISOString().split('T')[0],
      revenue: Number(day.revenue),
      orders: Number(day.orders)
    }))

    // Orders by status
    const ordersByStatus = await prisma.order.groupBy({
      by: ['status'],
      _count: { id: true },
      where: {
        createdAt: { gte: timeRange.start, lte: timeRange.end }
      }
    })

    const totalOrders = ordersByStatus.reduce((sum, status) => sum + status._count.id, 0)
    const orderStatusData = ordersByStatus.map(status => ({
      status: status.status,
      count: status._count.id,
      percentage: totalOrders > 0 ? (status._count.id / totalOrders) * 100 : 0
    }))

    // Top products
    const topProducts = await prisma.$queryRaw<Array<{
      id: string
      title: string
      sales: bigint
      revenue: number
    }>>`
      SELECT 
        p.id,
        p.title,
        COUNT(oi.id) as sales,
        SUM(oi."unitPrice" * oi.quantity) as revenue
      FROM "products" p
      JOIN "order_items" oi ON p.id = oi."productId"
      JOIN "orders" o ON oi."orderId" = o.id
      WHERE o."createdAt" >= ${timeRange.start}
        AND o."createdAt" <= ${timeRange.end}
        AND o.status IN ('processing', 'shipped', 'delivered')
      GROUP BY p.id, p.title
      ORDER BY sales DESC
      LIMIT 10
    `

    const topProductsData = topProducts.map(product => ({
      id: product.id,
      name: product.title,
      sales: Number(product.sales),
      revenue: Number(product.revenue)
    }))

    // Top categories
    const topCategories = await prisma.$queryRaw<Array<{
      category: string
      sales: bigint
      revenue: number
    }>>`
      SELECT 
        p."productType" as category,
        COUNT(oi.id) as sales,
        SUM(oi."unitPrice" * oi.quantity) as revenue
      FROM "products" p
      JOIN "order_items" oi ON p.id = oi."productId"
      JOIN "orders" o ON oi."orderId" = o.id
      WHERE o."createdAt" >= ${timeRange.start}
        AND o."createdAt" <= ${timeRange.end}
        AND o.status IN ('processing', 'shipped', 'delivered')
        AND p."productType" IS NOT NULL
      GROUP BY p."productType"
      ORDER BY sales DESC
      LIMIT 10
    `

    const totalCategorySales = topCategories.reduce((sum, cat) => sum + Number(cat.sales), 0)
    const topCategoriesData = topCategories.map(category => ({
      name: category.category || 'Uncategorized',
      sales: Number(category.sales),
      revenue: Number(category.revenue),
      percentage: totalCategorySales > 0 ? (Number(category.sales) / totalCategorySales) * 100 : 0
    }))

    // Customer segments (simplified)
    const customerSegments = [
      { segment: 'New', count: 0, revenue: 0 },
      { segment: 'Regular', count: 0, revenue: 0 },
      { segment: 'VIP', count: 0, revenue: 0 }
    ]

    return {
      revenueByDay,
      ordersByStatus: orderStatusData,
      topProducts: topProductsData,
      topCategories: topCategoriesData,
      customerSegments
    }
  }

  /**
   * Get inventory alerts
   */
  private async getInventoryData() {
    const lowStockProducts = await prisma.product.findMany({
      where: {
        trackQuantity: true,
        inventoryQuantity: { lte: 10, gt: 0 },
        status: 'active'
      },
      select: {
        id: true,
        title: true,
        inventoryQuantity: true
      },
      take: 20
    })

    const outOfStockProducts = await prisma.product.findMany({
      where: {
        trackQuantity: true,
        inventoryQuantity: { lte: 0 },
        status: 'active'
      },
      select: {
        id: true,
        title: true,
        updatedAt: true
      },
      take: 20
    })

    return {
      lowStockProducts: lowStockProducts.map(product => ({
        id: product.id,
        name: product.title,
        stock: product.inventoryQuantity || 0,
        threshold: 10
      })),
      outOfStockProducts: outOfStockProducts.map(product => ({
        id: product.id,
        name: product.title,
        lastSold: product.updatedAt
      }))
    }
  }

  /**
   * Get recent activity
   */
  private async getRecentActivity() {
    const recentOrders = await prisma.order.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: { firstName: true, lastName: true, displayName: true, email: true }
        }
      }
    })

    const recentCustomers = await prisma.user.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        orders: {
          where: { status: { in: ['processing', 'shipped', 'delivered'] } },
          select: { total: true }
        }
      }
    })

    return {
      recentOrders: recentOrders.map(order => ({
        id: order.id,
        customerName: order.user?.displayName || 
                     (order.user?.firstName && order.user?.lastName 
                       ? `${order.user.firstName} ${order.user.lastName}` 
                       : order.user?.firstName || 'Guest'),
        total: Number(order.total),
        status: order.status,
        createdAt: order.createdAt
      })),
      recentCustomers: recentCustomers.map(customer => ({
        id: customer.id,
        name: customer.displayName || 
              (customer.firstName && customer.lastName 
                ? `${customer.firstName} ${customer.lastName}` 
                : customer.firstName || 'Unknown'),
        email: customer.email || '',
        totalSpent: customer.orders.reduce((sum, order) => sum + Number(order.total), 0),
        createdAt: customer.createdAt
      }))
    }
  }

  /**
   * Calculate time ranges for current and previous periods
   */
  private getTimeRange(days: number): TimeRange {
    const end = new Date()
    const start = new Date()
    start.setDate(end.getDate() - days)

    const previousEnd = new Date(start)
    const previousStart = new Date(start)
    previousStart.setDate(previousEnd.getDate() - days)

    return {
      start,
      end,
      previousStart,
      previousEnd
    }
  }
}
