// Cart Service - Shopping cart management functionality
import { prisma } from '../config/database'
import { 
  Cart, 
  CartItem,
  AddToCartInput,
  UpdateCartItemInput,
  RemoveFromCartInput,
  ApplyDiscountInput,
  RemoveDiscountInput,
  UpdateShippingAddressInput,
  SelectShippingMethodInput,
  CartValidationResult,
  ApiResponse,
  NotFoundError,
  ValidationError,
  InsufficientStockError,
  Money
} from '../types'
import { createSuccessResponse, createErrorResponse } from '../utils/api-response'

export class CartService {
  /**
   * Get cart by ID
   */
  async getCart(cartId: string): Promise<ApiResponse<Cart>> {
    try {
      const cart = await prisma.cart.findUnique({
        where: { id: cartId },
        include: {
          items: {
            include: {
              product: {
                include: {
                  images: true
                }
              },
              variant: true
            }
          },
          appliedDiscounts: true,
          shippingAddress: true,
          shippingMethod: true,
          taxLines: true
        }
      })

      if (!cart) {
        throw new NotFoundError('Cart', cartId)
      }

      // Transform cart data
      const transformedCart = this.transformCart(cart)

      return createSuccessResponse(transformedCart)
    } catch (error) {
      console.error('Get cart error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to fetch cart')
    }
  }

  /**
   * Get cart by session ID
   */
  async getCartBySession(sessionId: string): Promise<ApiResponse<Cart>> {
    try {
      const cart = await prisma.cart.findFirst({
        where: { sessionId },
        include: {
          items: {
            include: {
              product: {
                include: {
                  images: true
                }
              },
              variant: true
            }
          },
          appliedDiscounts: true,
          shippingAddress: true,
          shippingMethod: true,
          taxLines: true
        }
      })

      if (!cart) {
        // Create a new cart for this session
        return this.createCart(sessionId)
      }

      const transformedCart = this.transformCart(cart)
      return createSuccessResponse(transformedCart)
    } catch (error) {
      console.error('Get cart by session error:', error)
      return createErrorResponse('Failed to fetch cart')
    }
  }

  /**
   * Get cart by user ID
   */
  async getCartByUser(userId: string): Promise<ApiResponse<Cart>> {
    try {
      const cart = await prisma.cart.findFirst({
        where: { userId },
        include: {
          items: {
            include: {
              product: {
                include: {
                  images: true
                }
              },
              variant: true
            }
          },
          appliedDiscounts: true,
          shippingAddress: true,
          shippingMethod: true,
          taxLines: true
        }
      })

      if (!cart) {
        // Create a new cart for this user
        return this.createCart(undefined, userId)
      }

      const transformedCart = this.transformCart(cart)
      return createSuccessResponse(transformedCart)
    } catch (error) {
      console.error('Get cart by user error:', error)
      return createErrorResponse('Failed to fetch cart')
    }
  }

  /**
   * Create a new cart
   */
  async createCart(sessionId?: string, userId?: string): Promise<ApiResponse<Cart>> {
    try {
      const cart = await prisma.cart.create({
        data: {
          sessionId,
          userId,
          status: 'active',
          currency: 'ZAR',
          subtotal: { amount: 0, currency: 'ZAR' },
          totalDiscount: { amount: 0, currency: 'ZAR' },
          totalTax: { amount: 0, currency: 'ZAR' },
          totalShipping: { amount: 0, currency: 'ZAR' },
          total: { amount: 0, currency: 'ZAR' },
          itemCount: 0,
          taxIncluded: false,
          lastActivityAt: new Date()
        },
        include: {
          items: true,
          appliedDiscounts: true,
          shippingAddress: true,
          shippingMethod: true,
          taxLines: true
        }
      })

      const transformedCart = this.transformCart(cart)
      return createSuccessResponse(transformedCart)
    } catch (error) {
      console.error('Create cart error:', error)
      return createErrorResponse('Failed to create cart')
    }
  }

  /**
   * Add item to cart
   */
  async addToCart(cartId: string, input: AddToCartInput): Promise<ApiResponse<Cart>> {
    try {
      // Validate product and variant
      const product = await prisma.product.findUnique({
        where: { id: input.productId },
        include: {
          variants: true,
          images: true
        }
      })

      if (!product) {
        throw new NotFoundError('Product', input.productId)
      }

      if (!product.isAvailable || product.status !== 'active') {
        throw new ValidationError('Product is not available')
      }

      let variant = null
      if (input.variantId) {
        variant = product.variants.find(v => v.id === input.variantId)
        if (!variant) {
          throw new NotFoundError('Product variant', input.variantId)
        }
        if (!variant.available) {
          throw new ValidationError('Product variant is not available')
        }
      }

      // Check stock availability
      const availableQuantity = variant ? variant.inventoryQuantity : product.inventoryQuantity
      if (product.trackQuantity && availableQuantity < input.quantity) {
        throw new InsufficientStockError(input.productId, input.quantity, availableQuantity)
      }

      // Check if item already exists in cart
      const existingItem = await prisma.cartItem.findFirst({
        where: {
          cartId,
          productId: input.productId,
          variantId: input.variantId || null
        }
      })

      if (existingItem) {
        // Update existing item quantity
        const newQuantity = existingItem.quantity + input.quantity
        
        if (product.trackQuantity && availableQuantity < newQuantity) {
          throw new InsufficientStockError(input.productId, newQuantity, availableQuantity)
        }

        await prisma.cartItem.update({
          where: { id: existingItem.id },
          data: {
            quantity: newQuantity,
            totalPrice: {
              amount: (variant?.price.amount || product.price.amount) * newQuantity,
              currency: variant?.price.currency || product.price.currency
            },
            updatedAt: new Date()
          }
        })
      } else {
        // Create new cart item
        const unitPrice = variant?.price || product.price
        const totalPrice = {
          amount: unitPrice.amount * input.quantity,
          currency: unitPrice.currency
        }

        await prisma.cartItem.create({
          data: {
            cartId,
            productId: input.productId,
            variantId: input.variantId,
            quantity: input.quantity,
            unitPrice,
            totalPrice,
            productTitle: product.title,
            productSlug: product.slug,
            productImage: product.images[0]?.url,
            variantTitle: variant?.title,
            variantOptions: variant?.options || [],
            isAvailable: true,
            maxQuantity: availableQuantity,
            customAttributes: input.customAttributes,
            personalizedMessage: input.personalizedMessage,
            giftWrap: input.giftWrap || false,
            compareAtPrice: variant?.compareAtPrice || product.compareAtPrice,
            addedAt: new Date()
          }
        })
      }

      // Update cart totals
      await this.recalculateCart(cartId)

      // Return updated cart
      return this.getCart(cartId)
    } catch (error) {
      console.error('Add to cart error:', error)
      if (error instanceof NotFoundError || error instanceof ValidationError || error instanceof InsufficientStockError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to add item to cart')
    }
  }

  /**
   * Update cart item
   */
  async updateCartItem(cartId: string, input: UpdateCartItemInput): Promise<ApiResponse<Cart>> {
    try {
      const cartItem = await prisma.cartItem.findFirst({
        where: {
          id: input.itemId,
          cartId
        },
        include: {
          product: true,
          variant: true
        }
      })

      if (!cartItem) {
        throw new NotFoundError('Cart item', input.itemId)
      }

      // If quantity is being updated, check stock
      if (input.quantity !== undefined) {
        const availableQuantity = cartItem.variant 
          ? cartItem.variant.inventoryQuantity 
          : cartItem.product.inventoryQuantity

        if (cartItem.product.trackQuantity && availableQuantity < input.quantity) {
          throw new InsufficientStockError(cartItem.productId, input.quantity, availableQuantity)
        }

        // Update quantity and total price
        const unitPrice = cartItem.unitPrice
        const totalPrice = {
          amount: unitPrice.amount * input.quantity,
          currency: unitPrice.currency
        }

        await prisma.cartItem.update({
          where: { id: input.itemId },
          data: {
            quantity: input.quantity,
            totalPrice,
            customAttributes: input.customAttributes,
            personalizedMessage: input.personalizedMessage,
            giftWrap: input.giftWrap,
            updatedAt: new Date()
          }
        })
      } else {
        // Update other attributes only
        await prisma.cartItem.update({
          where: { id: input.itemId },
          data: {
            customAttributes: input.customAttributes,
            personalizedMessage: input.personalizedMessage,
            giftWrap: input.giftWrap,
            updatedAt: new Date()
          }
        })
      }

      // Update cart totals
      await this.recalculateCart(cartId)

      // Return updated cart
      return this.getCart(cartId)
    } catch (error) {
      console.error('Update cart item error:', error)
      if (error instanceof NotFoundError || error instanceof InsufficientStockError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to update cart item')
    }
  }

  /**
   * Remove item from cart
   */
  async removeFromCart(cartId: string, input: RemoveFromCartInput): Promise<ApiResponse<Cart>> {
    try {
      const cartItem = await prisma.cartItem.findFirst({
        where: {
          id: input.itemId,
          cartId
        }
      })

      if (!cartItem) {
        throw new NotFoundError('Cart item', input.itemId)
      }

      await prisma.cartItem.delete({
        where: { id: input.itemId }
      })

      // Update cart totals
      await this.recalculateCart(cartId)

      // Return updated cart
      return this.getCart(cartId)
    } catch (error) {
      console.error('Remove from cart error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to remove item from cart')
    }
  }

  /**
   * Clear cart
   */
  async clearCart(cartId: string): Promise<ApiResponse<Cart>> {
    try {
      // Delete all cart items
      await prisma.cartItem.deleteMany({
        where: { cartId }
      })

      // Reset cart totals
      await prisma.cart.update({
        where: { id: cartId },
        data: {
          subtotal: { amount: 0, currency: 'ZAR' },
          totalDiscount: { amount: 0, currency: 'ZAR' },
          totalTax: { amount: 0, currency: 'ZAR' },
          totalShipping: { amount: 0, currency: 'ZAR' },
          total: { amount: 0, currency: 'ZAR' },
          itemCount: 0,
          lastActivityAt: new Date()
        }
      })

      return this.getCart(cartId)
    } catch (error) {
      console.error('Clear cart error:', error)
      return createErrorResponse('Failed to clear cart')
    }
  }

  /**
   * Apply discount to cart
   */
  async applyDiscount(input: ApplyDiscountInput): Promise<ApiResponse<Cart>> {
    try {
      // Find the discount/coupon
      const discount = await prisma.coupon.findUnique({
        where: { code: input.code }
      })

      if (!discount) {
        throw new NotFoundError('Discount code', input.code)
      }

      if (!discount.isActive || (discount.expiresAt && discount.expiresAt < new Date())) {
        throw new ValidationError('Discount code is not valid or has expired')
      }

      if (discount.usageLimit && discount.usageCount >= discount.usageLimit) {
        throw new ValidationError('Discount code has reached its usage limit')
      }

      // Check if discount is already applied
      const existingDiscount = await prisma.appliedDiscount.findFirst({
        where: {
          cartId: input.cartId,
          code: input.code
        }
      })

      if (existingDiscount) {
        throw new ValidationError('Discount code is already applied')
      }

      // Apply the discount
      await prisma.appliedDiscount.create({
        data: {
          cartId: input.cartId,
          code: input.code,
          title: discount.title,
          description: discount.description,
          type: discount.type,
          value: discount.value,
          amount: { amount: 0, currency: 'ZAR' } // Will be calculated in recalculateCart
        }
      })

      // Update cart totals
      await this.recalculateCart(input.cartId)

      return this.getCart(input.cartId)
    } catch (error) {
      console.error('Apply discount error:', error)
      if (error instanceof NotFoundError || error instanceof ValidationError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to apply discount')
    }
  }

  /**
   * Remove discount from cart
   */
  async removeDiscount(input: RemoveDiscountInput): Promise<ApiResponse<Cart>> {
    try {
      const appliedDiscount = await prisma.appliedDiscount.findFirst({
        where: {
          id: input.discountId,
          cartId: input.cartId
        }
      })

      if (!appliedDiscount) {
        throw new NotFoundError('Applied discount', input.discountId)
      }

      await prisma.appliedDiscount.delete({
        where: { id: input.discountId }
      })

      // Update cart totals
      await this.recalculateCart(input.cartId)

      return this.getCart(input.cartId)
    } catch (error) {
      console.error('Remove discount error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to remove discount')
    }
  }

  /**
   * Update shipping address
   */
  async updateShippingAddress(input: UpdateShippingAddressInput): Promise<ApiResponse<Cart>> {
    try {
      await prisma.cart.update({
        where: { id: input.cartId },
        data: {
          shippingAddress: input.address,
          lastActivityAt: new Date()
        }
      })

      // Recalculate shipping costs
      await this.recalculateCart(input.cartId)

      return this.getCart(input.cartId)
    } catch (error) {
      console.error('Update shipping address error:', error)
      return createErrorResponse('Failed to update shipping address')
    }
  }

  /**
   * Select shipping method
   */
  async selectShippingMethod(input: SelectShippingMethodInput): Promise<ApiResponse<Cart>> {
    try {
      // In a real implementation, you would fetch the shipping method details
      // For now, we'll use mock data
      const shippingMethod = {
        id: input.shippingMethodId,
        title: 'Standard Shipping',
        description: '5-7 business days',
        price: { amount: 99, currency: 'ZAR' },
        estimatedDelivery: { min: 5, max: 7, unit: 'days' as const }
      }

      await prisma.cart.update({
        where: { id: input.cartId },
        data: {
          shippingMethod,
          totalShipping: shippingMethod.price,
          lastActivityAt: new Date()
        }
      })

      // Recalculate totals
      await this.recalculateCart(input.cartId)

      return this.getCart(input.cartId)
    } catch (error) {
      console.error('Select shipping method error:', error)
      return createErrorResponse('Failed to select shipping method')
    }
  }

  /**
   * Validate cart
   */
  async validateCart(cartId: string): Promise<ApiResponse<CartValidationResult>> {
    try {
      const cart = await prisma.cart.findUnique({
        where: { id: cartId },
        include: {
          items: {
            include: {
              product: true,
              variant: true
            }
          }
        }
      })

      if (!cart) {
        throw new NotFoundError('Cart', cartId)
      }

      const errors: any[] = []
      const warnings: any[] = []

      // Validate each cart item
      for (const item of cart.items) {
        // Check product availability
        if (!item.product.isAvailable || item.product.status !== 'active') {
          errors.push({
            type: 'item_unavailable',
            itemId: item.id,
            message: `${item.productTitle} is no longer available`,
            details: { productId: item.productId }
          })
          continue
        }

        // Check variant availability
        if (item.variant && !item.variant.available) {
          errors.push({
            type: 'item_unavailable',
            itemId: item.id,
            message: `${item.productTitle} (${item.variantTitle}) is no longer available`,
            details: { productId: item.productId, variantId: item.variantId }
          })
          continue
        }

        // Check stock levels
        const availableQuantity = item.variant 
          ? item.variant.inventoryQuantity 
          : item.product.inventoryQuantity

        if (item.product.trackQuantity) {
          if (availableQuantity < item.quantity) {
            errors.push({
              type: 'insufficient_stock',
              itemId: item.id,
              message: `Only ${availableQuantity} units of ${item.productTitle} are available`,
              details: { 
                productId: item.productId, 
                requested: item.quantity, 
                available: availableQuantity 
              }
            })
          } else if (availableQuantity <= 5) {
            warnings.push({
              type: 'low_stock',
              itemId: item.id,
              message: `Only ${availableQuantity} units of ${item.productTitle} remaining`,
              details: { productId: item.productId, available: availableQuantity }
            })
          }
        }

        // Check price changes
        const currentPrice = item.variant?.price || item.product.price
        if (currentPrice.amount !== item.unitPrice.amount) {
          if (currentPrice.amount > item.unitPrice.amount) {
            warnings.push({
              type: 'price_increase',
              itemId: item.id,
              message: `Price of ${item.productTitle} has increased`,
              details: { 
                oldPrice: item.unitPrice, 
                newPrice: currentPrice 
              }
            })
          }
        }
      }

      const result: CartValidationResult = {
        isValid: errors.length === 0,
        errors,
        warnings
      }

      return createSuccessResponse(result)
    } catch (error) {
      console.error('Validate cart error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to validate cart')
    }
  }

  /**
   * Recalculate cart totals
   */
  private async recalculateCart(cartId: string): Promise<void> {
    const cart = await prisma.cart.findUnique({
      where: { id: cartId },
      include: {
        items: true,
        appliedDiscounts: true
      }
    })

    if (!cart) return

    // Calculate subtotal
    const subtotal = cart.items.reduce((sum, item) => sum + item.totalPrice.amount, 0)

    // Calculate discounts
    let totalDiscount = 0
    for (const discount of cart.appliedDiscounts) {
      let discountAmount = 0
      if (discount.type === 'percentage') {
        discountAmount = (subtotal * discount.value) / 100
      } else if (discount.type === 'fixed_amount') {
        discountAmount = discount.value
      }
      totalDiscount += discountAmount

      // Update the discount amount
      await prisma.appliedDiscount.update({
        where: { id: discount.id },
        data: {
          amount: { amount: discountAmount, currency: 'ZAR' }
        }
      })
    }

    // Calculate tax (15% VAT for South Africa)
    const taxRate = 0.15
    const taxableAmount = subtotal - totalDiscount
    const totalTax = taxableAmount * taxRate

    // Get shipping cost
    const totalShipping = cart.shippingMethod?.price?.amount || 0

    // Calculate total
    const total = subtotal - totalDiscount + totalTax + totalShipping

    // Update cart
    await prisma.cart.update({
      where: { id: cartId },
      data: {
        itemCount: cart.items.length,
        subtotal: { amount: subtotal, currency: 'ZAR' },
        totalDiscount: { amount: totalDiscount, currency: 'ZAR' },
        totalTax: { amount: totalTax, currency: 'ZAR' },
        totalShipping: { amount: totalShipping, currency: 'ZAR' },
        total: { amount: total, currency: 'ZAR' },
        lastActivityAt: new Date()
      }
    })
  }

  /**
   * Transform cart data for API response
   */
  private transformCart(cart: any): Cart {
    return {
      ...cart,
      items: cart.items.map((item: any) => ({
        ...item,
        productImage: item.product?.images?.[0]?.url || item.productImage
      }))
    }
  }
}