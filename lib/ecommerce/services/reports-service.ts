// Reports Service - Business reporting and analytics functionality
import { prisma } from '../config/database'
import { 
  ApiResponse,
  Money,
  PaginatedResponse
} from '../types'
import { createSuccessResponse, createErrorResponse } from '../utils/api-response'

// Report interfaces
export interface SalesReport {
  period: {
    start: Date
    end: Date
  }
  summary: {
    totalRevenue: Money
    totalOrders: number
    averageOrderValue: Money
    totalCustomers: number
    newCustomers: number
    returningCustomers: number
    conversionRate: number
    refundRate: number
  }
  trends: {
    revenueGrowth: number
    orderGrowth: number
    customerGrowth: number
  }
  breakdown: {
    daily: Array<{
      date: string
      revenue: Money
      orders: number
      customers: number
    }>
    byCategory: Array<{
      category: string
      revenue: Money
      orders: number
      percentage: number
    }>
    byProduct: Array<{
      productId: string
      productName: string
      revenue: Money
      quantity: number
      orders: number
    }>
    byRegion: Array<{
      region: string
      revenue: Money
      orders: number
      customers: number
    }>
  }
}

export interface CustomerReport {
  period: {
    start: Date
    end: Date
  }
  summary: {
    totalCustomers: number
    newCustomers: number
    activeCustomers: number
    churnedCustomers: number
    averageLifetimeValue: Money
    averageOrderFrequency: number
    customerRetentionRate: number
    customerAcquisitionCost: Money
  }
  segments: Array<{
    segment: string
    customerCount: number
    revenue: Money
    averageOrderValue: Money
    percentage: number
  }>
  cohorts: Array<{
    cohort: string
    size: number
    retentionRates: number[]
    lifetimeValue: Money
  }>
  demographics: {
    byAge: Array<{
      ageGroup: string
      count: number
      percentage: number
    }>
    byGender: Array<{
      gender: string
      count: number
      percentage: number
    }>
    byLocation: Array<{
      location: string
      count: number
      percentage: number
    }>
  }
}

export interface ProductReport {
  period: {
    start: Date
    end: Date
  }
  summary: {
    totalProducts: number
    activeProducts: number
    totalSales: number
    totalRevenue: Money
    averagePrice: Money
    topPerformingCategory: string
    lowStockProducts: number
    outOfStockProducts: number
  }
  performance: {
    topSelling: Array<{
      productId: string
      productName: string
      category: string
      quantitySold: number
      revenue: Money
      profit: Money
      conversionRate: number
    }>
    lowPerforming: Array<{
      productId: string
      productName: string
      category: string
      quantitySold: number
      revenue: Money
      views: number
      conversionRate: number
    }>
    trending: Array<{
      productId: string
      productName: string
      growthRate: number
      currentPeriodSales: number
      previousPeriodSales: number
    }>
  }
  inventory: {
    turnoverRate: number
    averageDaysToSell: number
    slowMovingItems: Array<{
      productId: string
      productName: string
      currentStock: number
      daysSinceLastSale: number
      turnoverRate: number
    }>
    stockAlerts: Array<{
      productId: string
      productName: string
      currentStock: number
      reorderLevel: number
      status: 'low' | 'out'
    }>
  }
}

export interface FinancialReport {
  period: {
    start: Date
    end: Date
  }
  revenue: {
    gross: Money
    net: Money
    recurring: Money
    oneTime: Money
    refunds: Money
    growth: number
  }
  costs: {
    productCosts: Money
    shippingCosts: Money
    paymentFees: Money
    marketingCosts: Money
    operationalCosts: Money
    total: Money
  }
  profit: {
    gross: Money
    net: Money
    margin: number
    growth: number
  }
  cashFlow: {
    inflow: Money
    outflow: Money
    net: Money
    projectedNextMonth: Money
  }
  taxes: {
    collected: Money
    owed: Money
    rate: number
  }
}

export interface MarketingReport {
  period: {
    start: Date
    end: Date
  }
  campaigns: Array<{
    campaignId: string
    campaignName: string
    channel: string
    spend: Money
    impressions: number
    clicks: number
    conversions: number
    revenue: Money
    roi: number
    ctr: number
    conversionRate: number
  }>
  channels: Array<{
    channel: string
    visitors: number
    conversions: number
    revenue: Money
    cost: Money
    roi: number
    conversionRate: number
  }>
  attribution: {
    firstTouch: Array<{
      source: string
      conversions: number
      revenue: Money
      percentage: number
    }>
    lastTouch: Array<{
      source: string
      conversions: number
      revenue: Money
      percentage: number
    }>
  }
}

export interface InventoryReport {
  period: {
    start: Date
    end: Date
  }
  summary: {
    totalValue: Money
    totalItems: number
    averageTurnover: number
    stockoutRate: number
    carryingCost: Money
    deadStock: Money
  }
  movements: Array<{
    productId: string
    productName: string
    openingStock: number
    received: number
    sold: number
    adjusted: number
    closingStock: number
    value: Money
  }>
  analysis: {
    fastMoving: Array<{
      productId: string
      productName: string
      turnoverRate: number
      daysOfStock: number
      reorderFrequency: number
    }>
    slowMoving: Array<{
      productId: string
      productName: string
      turnoverRate: number
      daysOfStock: number
      lastSaleDate: Date
    }>
    deadStock: Array<{
      productId: string
      productName: string
      daysWithoutSale: number
      currentValue: Money
      recommendedAction: string
    }>
  }
}

export class ReportsService {
  /**
   * Generate sales report
   */
  async generateSalesReport(startDate: Date, endDate: Date): Promise<ApiResponse<SalesReport>> {
    try {
      // Get orders in the period
      const orders = await prisma.order.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          },
          paymentStatus: 'paid'
        },
        include: {
          items: {
            include: {
              product: {
                include: {
                  categories: true
                }
              }
            }
          },
          customer: true,
          shippingAddress: true
        }
      })

      // Calculate summary metrics
      const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0)
      const totalOrders = orders.length
      const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0

      // Get customer metrics
      const customerIds = new Set(orders.map(order => order.customerId).filter(Boolean))
      const totalCustomers = customerIds.size

      // Get new customers in period
      const newCustomers = await prisma.user.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        }
      })

      const returningCustomers = totalCustomers - newCustomers

      // Calculate daily breakdown
      const dailyMap = new Map<string, { revenue: number; orders: number; customers: Set<string> }>()
      
      orders.forEach(order => {
        const date = order.createdAt.toISOString().split('T')[0]
        if (!dailyMap.has(date)) {
          dailyMap.set(date, { revenue: 0, orders: 0, customers: new Set() })
        }
        const day = dailyMap.get(date)!
        day.revenue += order.total
        day.orders += 1
        if (order.customerId) {
          day.customers.add(order.customerId)
        }
      })

      const daily = Array.from(dailyMap.entries()).map(([date, data]) => ({
        date,
        revenue: { amount: data.revenue, currency: 'ZAR' },
        orders: data.orders,
        customers: data.customers.size
      }))

      // Category breakdown
      const categoryMap = new Map<string, { revenue: number; orders: Set<string> }>()
      
      orders.forEach(order => {
        order.items.forEach(item => {
          item.product.categories.forEach(category => {
            if (!categoryMap.has(category.name)) {
              categoryMap.set(category.name, { revenue: 0, orders: new Set() })
            }
            const cat = categoryMap.get(category.name)!
            cat.revenue += item.price * item.quantity
            cat.orders.add(order.id)
          })
        })
      })

      const byCategory = Array.from(categoryMap.entries()).map(([category, data]) => ({
        category,
        revenue: { amount: data.revenue, currency: 'ZAR' },
        orders: data.orders.size,
        percentage: totalRevenue > 0 ? (data.revenue / totalRevenue) * 100 : 0
      }))

      // Product breakdown
      const productMap = new Map<string, { name: string; revenue: number; quantity: number; orders: Set<string> }>()
      
      orders.forEach(order => {
        order.items.forEach(item => {
          if (!productMap.has(item.productId)) {
            productMap.set(item.productId, { 
              name: item.name, 
              revenue: 0, 
              quantity: 0, 
              orders: new Set() 
            })
          }
          const prod = productMap.get(item.productId)!
          prod.revenue += item.price * item.quantity
          prod.quantity += item.quantity
          prod.orders.add(order.id)
        })
      })

      const byProduct = Array.from(productMap.entries())
        .map(([productId, data]) => ({
          productId,
          productName: data.name,
          revenue: { amount: data.revenue, currency: 'ZAR' },
          quantity: data.quantity,
          orders: data.orders.size
        }))
        .sort((a, b) => b.revenue.amount - a.revenue.amount)
        .slice(0, 20)

      // Region breakdown (simplified)
      const regionMap = new Map<string, { revenue: number; orders: number; customers: Set<string> }>()
      
      orders.forEach(order => {
        const region = order.shippingAddress?.province || 'Unknown'
        if (!regionMap.has(region)) {
          regionMap.set(region, { revenue: 0, orders: 0, customers: new Set() })
        }
        const reg = regionMap.get(region)!
        reg.revenue += order.total
        reg.orders += 1
        if (order.customerId) {
          reg.customers.add(order.customerId)
        }
      })

      const byRegion = Array.from(regionMap.entries()).map(([region, data]) => ({
        region,
        revenue: { amount: data.revenue, currency: 'ZAR' },
        orders: data.orders,
        customers: data.customers.size
      }))

      // Calculate trends (simplified - would need previous period data)
      const report: SalesReport = {
        period: { start: startDate, end: endDate },
        summary: {
          totalRevenue: { amount: totalRevenue, currency: 'ZAR' },
          totalOrders,
          averageOrderValue: { amount: averageOrderValue, currency: 'ZAR' },
          totalCustomers,
          newCustomers,
          returningCustomers,
          conversionRate: 0.025, // Mock data - would need session tracking
          refundRate: 0.02 // Mock data - would need refund tracking
        },
        trends: {
          revenueGrowth: 0.15, // Mock data
          orderGrowth: 0.12, // Mock data
          customerGrowth: 0.08 // Mock data
        },
        breakdown: {
          daily,
          byCategory,
          byProduct,
          byRegion
        }
      }

      return createSuccessResponse(report)
    } catch (error) {
      console.error('Generate sales report error:', error)
      return createErrorResponse('Failed to generate sales report')
    }
  }

  /**
   * Generate customer report
   */
  async generateCustomerReport(startDate: Date, endDate: Date): Promise<ApiResponse<CustomerReport>> {
    try {
      // Get all customers
      const totalCustomers = await prisma.user.count()
      
      // Get new customers in period
      const newCustomers = await prisma.user.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        }
      })

      // Get active customers (those who placed orders in period)
      const activeCustomerIds = await prisma.order.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          },
          paymentStatus: 'paid'
        },
        select: { customerId: true },
        distinct: ['customerId']
      })

      const activeCustomers = activeCustomerIds.filter(order => order.customerId).length

      // Calculate average lifetime value
      const lifetimeStats = await prisma.user.aggregate({
        _avg: { totalSpent: true }
      })

      // Customer segments (simplified)
      const segments = [
        {
          segment: 'High Value',
          customerCount: await prisma.user.count({ where: { totalSpent: { gte: 5000 } } }),
          revenue: { amount: 0, currency: 'ZAR' },
          averageOrderValue: { amount: 0, currency: 'ZAR' },
          percentage: 0
        },
        {
          segment: 'Medium Value',
          customerCount: await prisma.user.count({ 
            where: { 
              totalSpent: { gte: 1000, lt: 5000 } 
            } 
          }),
          revenue: { amount: 0, currency: 'ZAR' },
          averageOrderValue: { amount: 0, currency: 'ZAR' },
          percentage: 0
        },
        {
          segment: 'Low Value',
          customerCount: await prisma.user.count({ where: { totalSpent: { lt: 1000 } } }),
          revenue: { amount: 0, currency: 'ZAR' },
          averageOrderValue: { amount: 0, currency: 'ZAR' },
          percentage: 0
        }
      ]

      // Calculate percentages
      segments.forEach(segment => {
        segment.percentage = totalCustomers > 0 ? (segment.customerCount / totalCustomers) * 100 : 0
      })

      const report: CustomerReport = {
        period: { start: startDate, end: endDate },
        summary: {
          totalCustomers,
          newCustomers,
          activeCustomers,
          churnedCustomers: 0, // Would need churn calculation
          averageLifetimeValue: { 
            amount: lifetimeStats._avg.totalSpent || 0, 
            currency: 'ZAR' 
          },
          averageOrderFrequency: 0, // Would need calculation
          customerRetentionRate: 0.85, // Mock data
          customerAcquisitionCost: { amount: 150, currency: 'ZAR' } // Mock data
        },
        segments,
        cohorts: [], // Would need cohort analysis implementation
        demographics: {
          byAge: [], // Would need age group analysis
          byGender: [], // Would need gender analysis
          byLocation: [] // Would need location analysis
        }
      }

      return createSuccessResponse(report)
    } catch (error) {
      console.error('Generate customer report error:', error)
      return createErrorResponse('Failed to generate customer report')
    }
  }

  /**
   * Generate product report
   */
  async generateProductReport(startDate: Date, endDate: Date): Promise<ApiResponse<ProductReport>> {
    try {
      // Get product counts
      const totalProducts = await prisma.product.count()
      const activeProducts = await prisma.product.count({ 
        where: { status: 'active', isVisible: true } 
      })

      // Get sales data
      const orderItems = await prisma.orderItem.findMany({
        where: {
          order: {
            createdAt: {
              gte: startDate,
              lte: endDate
            },
            paymentStatus: 'paid'
          }
        },
        include: {
          product: {
            include: {
              categories: true
            }
          }
        }
      })

      const totalSales = orderItems.reduce((sum, item) => sum + item.quantity, 0)
      const totalRevenue = orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
      const averagePrice = totalSales > 0 ? totalRevenue / totalSales : 0

      // Top selling products
      const productSalesMap = new Map<string, {
        name: string
        category: string
        quantity: number
        revenue: number
        profit: number
      }>()

      orderItems.forEach(item => {
        if (!productSalesMap.has(item.productId)) {
          productSalesMap.set(item.productId, {
            name: item.name,
            category: item.product.categories[0]?.name || 'Uncategorized',
            quantity: 0,
            revenue: 0,
            profit: 0
          })
        }
        const prod = productSalesMap.get(item.productId)!
        prod.quantity += item.quantity
        prod.revenue += item.price * item.quantity
        // Profit calculation would need cost data
        prod.profit += (item.price * item.quantity) * 0.3 // Mock 30% margin
      })

      const topSelling = Array.from(productSalesMap.entries())
        .map(([productId, data]) => ({
          productId,
          productName: data.name,
          category: data.category,
          quantitySold: data.quantity,
          revenue: { amount: data.revenue, currency: 'ZAR' },
          profit: { amount: data.profit, currency: 'ZAR' },
          conversionRate: 0.05 // Mock data - would need view tracking
        }))
        .sort((a, b) => b.quantitySold - a.quantitySold)
        .slice(0, 20)

      // Get inventory alerts - using raw SQL for comparison
      const lowStockProducts = await prisma.$queryRaw<Array<{ count: bigint }>>`
        SELECT COUNT(*) as count FROM inventory 
        WHERE quantity <= reorder_level
      `

      const outOfStockProducts = await prisma.inventory.count({
        where: { quantity: 0 }
      })

      const report: ProductReport = {
        period: { start: startDate, end: endDate },
        summary: {
          totalProducts,
          activeProducts,
          totalSales,
          totalRevenue: { amount: totalRevenue, currency: 'ZAR' },
          averagePrice: { amount: averagePrice, currency: 'ZAR' },
          topPerformingCategory: topSelling[0]?.category || 'N/A',
          lowStockProducts: Number(lowStockProducts[0]?.count || 0),
          outOfStockProducts
        },
        performance: {
          topSelling,
          lowPerforming: [], // Would need view/conversion data
          trending: [] // Would need trend analysis
        },
        inventory: {
          turnoverRate: 0, // Would need calculation
          averageDaysToSell: 0, // Would need calculation
          slowMovingItems: [], // Would need analysis
          stockAlerts: [] // Would need inventory integration
        }
      }

      return createSuccessResponse(report)
    } catch (error) {
      console.error('Generate product report error:', error)
      return createErrorResponse('Failed to generate product report')
    }
  }

  /**
   * Generate financial report
   */
  async generateFinancialReport(startDate: Date, endDate: Date): Promise<ApiResponse<FinancialReport>> {
    try {
      // Get revenue data
      const orders = await prisma.order.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          },
          paymentStatus: 'paid'
        }
      })

      const grossRevenue = orders.reduce((sum, order) => sum + order.total, 0)
      
      // Get refunds (simplified)
      const refunds = orders.reduce((sum, order) => sum + (order.refundAmount || 0), 0)
      const netRevenue = grossRevenue - refunds

      // Mock cost calculations (would need actual cost tracking)
      const productCosts = grossRevenue * 0.4 // 40% COGS
      const shippingCosts = orders.length * 50 // R50 average shipping
      const paymentFees = grossRevenue * 0.025 // 2.5% payment fees
      const marketingCosts = grossRevenue * 0.1 // 10% marketing
      const operationalCosts = grossRevenue * 0.15 // 15% operational

      const totalCosts = productCosts + shippingCosts + paymentFees + marketingCosts + operationalCosts

      const grossProfit = grossRevenue - productCosts
      const netProfit = netRevenue - totalCosts

      // Tax calculation (15% VAT)
      const taxCollected = grossRevenue * 0.15

      const report: FinancialReport = {
        period: { start: startDate, end: endDate },
        revenue: {
          gross: { amount: grossRevenue, currency: 'ZAR' },
          net: { amount: netRevenue, currency: 'ZAR' },
          recurring: { amount: 0, currency: 'ZAR' }, // Would need subscription tracking
          oneTime: { amount: grossRevenue, currency: 'ZAR' },
          refunds: { amount: refunds, currency: 'ZAR' },
          growth: 0.15 // Mock data
        },
        costs: {
          productCosts: { amount: productCosts, currency: 'ZAR' },
          shippingCosts: { amount: shippingCosts, currency: 'ZAR' },
          paymentFees: { amount: paymentFees, currency: 'ZAR' },
          marketingCosts: { amount: marketingCosts, currency: 'ZAR' },
          operationalCosts: { amount: operationalCosts, currency: 'ZAR' },
          total: { amount: totalCosts, currency: 'ZAR' }
        },
        profit: {
          gross: { amount: grossProfit, currency: 'ZAR' },
          net: { amount: netProfit, currency: 'ZAR' },
          margin: grossRevenue > 0 ? (netProfit / grossRevenue) * 100 : 0,
          growth: 0.12 // Mock data
        },
        cashFlow: {
          inflow: { amount: netRevenue, currency: 'ZAR' },
          outflow: { amount: totalCosts, currency: 'ZAR' },
          net: { amount: netRevenue - totalCosts, currency: 'ZAR' },
          projectedNextMonth: { amount: netRevenue * 1.1, currency: 'ZAR' } // Mock projection
        },
        taxes: {
          collected: { amount: taxCollected, currency: 'ZAR' },
          owed: { amount: taxCollected, currency: 'ZAR' },
          rate: 15
        }
      }

      return createSuccessResponse(report)
    } catch (error) {
      console.error('Generate financial report error:', error)
      return createErrorResponse('Failed to generate financial report')
    }
  }

  /**
   * Generate inventory report
   */
  async generateInventoryReport(startDate: Date, endDate: Date): Promise<ApiResponse<InventoryReport>> {
    try {
      // Get inventory data
      const inventory = await prisma.inventory.findMany({
        include: {
          product: true
        }
      })

      const totalValue = inventory.reduce((sum, item) => {
        const price = item.product.price?.amount || 0
        return sum + (item.quantity * price)
      }, 0)

      const totalItems = inventory.reduce((sum, item) => sum + item.quantity, 0)

      // Get movements (simplified - would need actual movement tracking)
      const movements = inventory.slice(0, 20).map(item => ({
        productId: item.productId,
        productName: item.product.title,
        openingStock: item.quantity + 10, // Mock data
        received: 5, // Mock data
        sold: 10, // Mock data
        adjusted: 0,
        closingStock: item.quantity,
        value: { 
          amount: item.quantity * (item.product.price?.amount || 0), 
          currency: 'ZAR' 
        }
      }))

      const report: InventoryReport = {
        period: { start: startDate, end: endDate },
        summary: {
          totalValue: { amount: totalValue, currency: 'ZAR' },
          totalItems,
          averageTurnover: 0, // Would need calculation
          stockoutRate: 0, // Would need calculation
          carryingCost: { amount: totalValue * 0.2, currency: 'ZAR' }, // Mock 20% carrying cost
          deadStock: { amount: 0, currency: 'ZAR' } // Would need analysis
        },
        movements,
        analysis: {
          fastMoving: [], // Would need turnover analysis
          slowMoving: [], // Would need turnover analysis
          deadStock: [] // Would need sales analysis
        }
      }

      return createSuccessResponse(report)
    } catch (error) {
      console.error('Generate inventory report error:', error)
      return createErrorResponse('Failed to generate inventory report')
    }
  }

  /**
   * Get available report types
   */
  async getReportTypes(): Promise<ApiResponse<Array<{ type: string; name: string; description: string }>>> {
    try {
      const reportTypes = [
        {
          type: 'sales',
          name: 'Sales Report',
          description: 'Comprehensive sales performance analysis including revenue, orders, and trends'
        },
        {
          type: 'customer',
          name: 'Customer Report',
          description: 'Customer analytics including acquisition, retention, and segmentation'
        },
        {
          type: 'product',
          name: 'Product Report',
          description: 'Product performance analysis including top sellers and inventory status'
        },
        {
          type: 'financial',
          name: 'Financial Report',
          description: 'Financial overview including revenue, costs, profit, and cash flow'
        },
        {
          type: 'inventory',
          name: 'Inventory Report',
          description: 'Inventory analysis including stock levels, movements, and turnover'
        },
        {
          type: 'marketing',
          name: 'Marketing Report',
          description: 'Marketing campaign performance and channel attribution analysis'
        }
      ]

      return createSuccessResponse(reportTypes)
    } catch (error) {
      console.error('Get report types error:', error)
      return createErrorResponse('Failed to fetch report types')
    }
  }

  /**
   * Export report data
   */
  async exportReport(
    reportType: string, 
    startDate: Date, 
    endDate: Date, 
    format: 'csv' | 'excel' | 'pdf' = 'csv'
  ): Promise<ApiResponse<{ downloadUrl: string; expiresAt: Date }>> {
    try {
      // In a real implementation, you would generate the actual file
      // For now, return a mock response
      const downloadUrl = `/api/reports/download/${reportType}-${Date.now()}.${format}`
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours

      return createSuccessResponse({ downloadUrl, expiresAt })
    } catch (error) {
      console.error('Export report error:', error)
      return createErrorResponse('Failed to export report')
    }
  }
}