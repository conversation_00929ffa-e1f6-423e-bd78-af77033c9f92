import { prisma } from '@/lib/prisma'

export interface InventoryItem {
  id: string
  productId: string
  variantId?: string
  sku: string
  quantity: number
  reserved: number
  available: number
  reorderLevel: number
  reorderQuantity: number
  cost: number
  location?: string
  updatedAt: Date
}

export interface InventoryReservation {
  id: string
  productId: string
  variantId?: string
  quantity: number
  orderId: string
  expiresAt: Date
  createdAt: Date
}

export interface StockMovement {
  id: string
  productId: string
  variantId?: string
  type: 'in' | 'out' | 'adjustment' | 'reserved' | 'released'
  quantity: number
  reason: string
  reference?: string
  createdAt: Date
}

export class InventoryService {
  async getInventory(productId: string, variantId?: string): Promise<InventoryItem | null> {
    try {
      const inventory = await prisma.inventory.findFirst({
        where: {
          productId,
          variantId: variantId || null
        }
      })

      if (!inventory) return null

      return {
        id: inventory.id,
        productId: inventory.productId,
        variantId: inventory.variantId,
        sku: inventory.sku,
        quantity: inventory.quantity,
        reserved: inventory.reserved,
        available: inventory.quantity - inventory.reserved,
        reorderLevel: inventory.reorderLevel,
        reorderQuantity: inventory.reorderQuantity,
        cost: inventory.cost,
        location: inventory.location,
        updatedAt: inventory.updatedAt
      }
    } catch (error) {
      console.error('Get inventory error:', error)
      return null
    }
  }

  async updateInventory(
    productId: string, 
    variantId: string | undefined, 
    quantity: number, 
    cost?: number
  ): Promise<InventoryItem | null> {
    try {
      const inventory = await prisma.inventory.upsert({
        where: {
          productId_variantId: {
            productId,
            variantId: variantId || null
          }
        },
        update: {
          quantity,
          cost: cost || undefined,
          updatedAt: new Date()
        },
        create: {
          productId,
          variantId,
          sku: await this.generateSKU(productId, variantId),
          quantity,
          reserved: 0,
          reorderLevel: 10,
          reorderQuantity: 50,
          cost: cost || 0
        }
      })

      // Record stock movement
      await this.recordStockMovement({
        productId,
        variantId,
        type: 'adjustment',
        quantity,
        reason: 'Inventory update'
      })

      return this.getInventory(productId, variantId)
    } catch (error) {
      console.error('Update inventory error:', error)
      return null
    }
  }

  async adjustInventory(
    productId: string,
    variantId: string | undefined,
    adjustment: number,
    reason: string,
    reference?: string
  ): Promise<InventoryItem | null> {
    try {
      const current = await this.getInventory(productId, variantId)
      if (!current) {
        throw new Error('Inventory item not found')
      }

      const newQuantity = Math.max(0, current.quantity + adjustment)

      const inventory = await prisma.inventory.update({
        where: {
          productId_variantId: {
            productId,
            variantId: variantId || null
          }
        },
        data: {
          quantity: newQuantity,
          updatedAt: new Date()
        }
      })

      // Record stock movement
      await this.recordStockMovement({
        productId,
        variantId,
        type: adjustment > 0 ? 'in' : 'out',
        quantity: Math.abs(adjustment),
        reason,
        reference
      })

      return this.getInventory(productId, variantId)
    } catch (error) {
      console.error('Adjust inventory error:', error)
      return null
    }
  }

  async checkAvailability(productId: string, variantId: string | undefined, quantity: number): Promise<boolean> {
    try {
      const inventory = await this.getInventory(productId, variantId)
      if (!inventory) return false

      return inventory.available >= quantity
    } catch (error) {
      console.error('Check availability error:', error)
      return false
    }
  }

  async reserveInventory(
    productId: string,
    variantId: string | undefined,
    quantity: number,
    orderId: string,
    expirationMinutes = 30
  ): Promise<boolean> {
    try {
      const available = await this.checkAvailability(productId, variantId, quantity)
      if (!available) return false

      // Create reservation
      const expiresAt = new Date(Date.now() + expirationMinutes * 60 * 1000)
      
      await prisma.inventoryReservation.create({
        data: {
          productId,
          variantId,
          quantity,
          orderId,
          expiresAt
        }
      })

      // Update reserved quantity
      await prisma.inventory.update({
        where: {
          productId_variantId: {
            productId,
            variantId: variantId || null
          }
        },
        data: {
          reserved: {
            increment: quantity
          }
        }
      })

      // Record stock movement
      await this.recordStockMovement({
        productId,
        variantId,
        type: 'reserved',
        quantity,
        reason: 'Order reservation',
        reference: orderId
      })

      return true
    } catch (error) {
      console.error('Reserve inventory error:', error)
      return false
    }
  }

  async releaseReservation(
    productId: string,
    variantId: string | undefined,
    quantity: number,
    orderId: string
  ): Promise<boolean> {
    try {
      // Remove reservation
      await prisma.inventoryReservation.deleteMany({
        where: {
          productId,
          variantId: variantId || null,
          orderId
        }
      })

      // Update reserved quantity
      await prisma.inventory.update({
        where: {
          productId_variantId: {
            productId,
            variantId: variantId || null
          }
        },
        data: {
          reserved: {
            decrement: quantity
          }
        }
      })

      // Record stock movement
      await this.recordStockMovement({
        productId,
        variantId,
        type: 'released',
        quantity,
        reason: 'Reservation released',
        reference: orderId
      })

      return true
    } catch (error) {
      console.error('Release reservation error:', error)
      return false
    }
  }

  async commitReservation(
    productId: string,
    variantId: string | undefined,
    quantity: number,
    orderId: string
  ): Promise<boolean> {
    try {
      // Remove reservation
      await prisma.inventoryReservation.deleteMany({
        where: {
          productId,
          variantId: variantId || null,
          orderId
        }
      })

      // Update quantities
      await prisma.inventory.update({
        where: {
          productId_variantId: {
            productId,
            variantId: variantId || null
          }
        },
        data: {
          quantity: {
            decrement: quantity
          },
          reserved: {
            decrement: quantity
          }
        }
      })

      // Record stock movement
      await this.recordStockMovement({
        productId,
        variantId,
        type: 'out',
        quantity,
        reason: 'Order fulfilled',
        reference: orderId
      })

      return true
    } catch (error) {
      console.error('Commit reservation error:', error)
      return false
    }
  }

  async getStockMovements(
    productId?: string,
    variantId?: string,
    limit = 50,
    offset = 0
  ): Promise<StockMovement[]> {
    try {
      const movements = await prisma.stockMovement.findMany({
        where: {
          ...(productId && { productId }),
          ...(variantId && { variantId })
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      })

      return movements.map(movement => ({
        id: movement.id,
        productId: movement.productId,
        variantId: movement.variantId,
        type: movement.type as StockMovement['type'],
        quantity: movement.quantity,
        reason: movement.reason,
        reference: movement.reference,
        createdAt: movement.createdAt
      }))
    } catch (error) {
      console.error('Get stock movements error:', error)
      return []
    }
  }

  async getLowStockItems(): Promise<InventoryItem[]> {
    try {
      const lowStockItems = await prisma.inventory.findMany({
        where: {
          quantity: {
            lte: prisma.inventory.fields.reorderLevel
          }
        },
        orderBy: { quantity: 'asc' }
      })

      return Promise.all(
        lowStockItems.map(async item => {
          const inventory = await this.getInventory(item.productId, item.variantId)
          return inventory!
        })
      )
    } catch (error) {
      console.error('Get low stock items error:', error)
      return []
    }
  }

  async cleanupExpiredReservations(): Promise<number> {
    try {
      const expiredReservations = await prisma.inventoryReservation.findMany({
        where: {
          expiresAt: {
            lt: new Date()
          }
        }
      })

      for (const reservation of expiredReservations) {
        await this.releaseReservation(
          reservation.productId,
          reservation.variantId,
          reservation.quantity,
          reservation.orderId
        )
      }

      return expiredReservations.length
    } catch (error) {
      console.error('Cleanup expired reservations error:', error)
      return 0
    }
  }

  private async recordStockMovement(movement: Omit<StockMovement, 'id' | 'createdAt'>): Promise<void> {
    try {
      await prisma.stockMovement.create({
        data: {
          productId: movement.productId,
          variantId: movement.variantId,
          type: movement.type,
          quantity: movement.quantity,
          reason: movement.reason,
          reference: movement.reference
        }
      })
    } catch (error) {
      console.error('Record stock movement error:', error)
    }
  }

  private async generateSKU(productId: string, variantId?: string): Promise<string> {
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { name: true }
    })

    if (!product) throw new Error('Product not found')

    const productCode = product.name
      .toUpperCase()
      .replace(/[^A-Z0-9]/g, '')
      .substring(0, 6)

    const variantCode = variantId ? variantId.substring(0, 4).toUpperCase() : '0000'
    const timestamp = Date.now().toString().slice(-6)

    return `${productCode}-${variantCode}-${timestamp}`
  }
}
