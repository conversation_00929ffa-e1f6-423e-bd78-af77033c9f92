import { prisma } from '../config/database'
import { ApiResponse } from '../types'
import { createSuccessResponse, createErrorResponse } from '../utils/api-response'

export interface SalesAnalytics {
  totalRevenue: number
  totalOrders: number
  averageOrderValue: number
  conversionRate: number
  topProducts: Array<{
    productId: string
    productName: string
    totalSold: number
    revenue: number
  }>
  salesByDay: Array<{
    date: string
    revenue: number
    orders: number
  }>
  salesByCategory: Array<{
    category: string
    revenue: number
    orders: number
  }>
}

export interface CustomerAnalytics {
  totalCustomers: number
  newCustomers: number
  returningCustomers: number
  customerLifetimeValue: number
  topCustomers: Array<{
    customerId: string
    customerEmail: string
    totalSpent: number
    orderCount: number
  }>
  customersByRegion: Array<{
    province: string
    customerCount: number
    revenue: number
  }>
}

export interface ProductAnalytics {
  totalProducts: number
  activeProducts: number
  lowStockProducts: number
  outOfStockProducts: number
  topPerformingProducts: Array<{
    productId: string
    productName: string
    views: number
    sales: number
    conversionRate: number
    revenue: number
  }>
  categoryPerformance: Array<{
    category: string
    productCount: number
    totalSales: number
    averagePrice: number
  }>
}

export interface InventoryAnalytics {
  totalInventoryValue: number
  lowStockItems: number
  outOfStockItems: number
  fastMovingItems: Array<{
    productId: string
    productName: string
    turnoverRate: number
    daysOfStock: number
  }>
  slowMovingItems: Array<{
    productId: string
    productName: string
    turnoverRate: number
    daysOfStock: number
  }>
  inventoryByCategory: Array<{
    category: string
    totalValue: number
    itemCount: number
  }>
}

export class AnalyticsService {
  async getSalesAnalytics(startDate: Date, endDate: Date): Promise<ApiResponse<SalesAnalytics>> {
    try {
      const whereClause = {
        createdAt: {
          gte: startDate,
          lte: endDate
        },
        paymentStatus: 'paid'
      }

      // Basic sales metrics
      const [totalStats, topProducts, dailySales] = await Promise.all([
        prisma.order.aggregate({
          where: whereClause,
          _sum: { total: true },
          _count: true,
          _avg: { total: true }
        }),
        this.getTopProducts(startDate, endDate),
        this.getDailySales(startDate, endDate)
      ])

      // Calculate conversion rate (orders / total sessions)
      // This would require session tracking implementation
      const conversionRate = 0.025 // Mock 2.5% conversion rate

      const analytics = {
        totalRevenue: totalStats._sum.total || 0,
        totalOrders: totalStats._count,
        averageOrderValue: totalStats._avg.total || 0,
        conversionRate,
        topProducts,
        salesByDay: dailySales,
        salesByCategory: await this.getSalesByCategory(startDate, endDate)
      }

      return createSuccessResponse(analytics)
    } catch (error) {
      console.error('Get sales analytics error:', error)
      return createErrorResponse('Failed to fetch sales analytics')
    }
  }

  async getCustomerAnalytics(startDate: Date, endDate: Date): Promise<ApiResponse<CustomerAnalytics>> {
    try {
      const [totalCustomers, newCustomers, topCustomers, customersByRegion] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate
            }
          }
        }),
        this.getTopCustomers(startDate, endDate),
        this.getCustomersByRegion(startDate, endDate)
      ])

      // Calculate returning customers
      const returningCustomers = await prisma.user.count({
        where: {
          orders: {
            some: {
              createdAt: {
                gte: startDate,
                lte: endDate
              }
            }
          },
          createdAt: {
            lt: startDate
          }
        }
      })

      // Calculate customer lifetime value
      const lifetimeValueResult = await prisma.order.aggregate({
        where: {
          paymentStatus: 'paid',
          customerId: { not: null }
        },
        _avg: { total: true }
      })

      return {
        totalCustomers,
        newCustomers,
        returningCustomers,
        customerLifetimeValue: lifetimeValueResult._avg.total || 0,
        topCustomers,
        customersByRegion
      }
    } catch (error) {
      console.error('Get customer analytics error:', error)
      throw new Error('Failed to fetch customer analytics')
    }
  }

  async getProductAnalytics(): Promise<ProductAnalytics> {
    try {
      const [totalProducts, activeProducts, lowStockCount, outOfStockCount] = await Promise.all([
        prisma.product.count(),
        prisma.product.count({ where: { isActive: true } }),
        prisma.inventory.count({
          where: {
            quantity: {
              lte: prisma.inventory.fields.reorderLevel
            }
          }
        }),
        prisma.inventory.count({
          where: { quantity: 0 }
        })
      ])

      const [topPerformingProducts, categoryPerformance] = await Promise.all([
        this.getTopPerformingProducts(),
        this.getCategoryPerformance()
      ])

      return {
        totalProducts,
        activeProducts,
        lowStockProducts: lowStockCount,
        outOfStockProducts: outOfStockCount,
        topPerformingProducts,
        categoryPerformance
      }
    } catch (error) {
      console.error('Get product analytics error:', error)
      throw new Error('Failed to fetch product analytics')
    }
  }

  async getInventoryAnalytics(): Promise<InventoryAnalytics> {
    try {
      const [inventoryValue, lowStockItems, outOfStockItems] = await Promise.all([
        prisma.inventory.aggregate({
          _sum: {
            quantity: true
          }
        }),
        prisma.inventory.count({
          where: {
            quantity: {
              lte: prisma.inventory.fields.reorderLevel
            }
          }
        }),
        prisma.inventory.count({
          where: { quantity: 0 }
        })
      ])

      const [fastMovingItems, slowMovingItems, inventoryByCategory] = await Promise.all([
        this.getFastMovingItems(),
        this.getSlowMovingItems(),
        this.getInventoryByCategory()
      ])

      return {
        totalInventoryValue: (inventoryValue._sum.quantity || 0) * 100, // Mock average cost
        lowStockItems,
        outOfStockItems,
        fastMovingItems,
        slowMovingItems,
        inventoryByCategory
      }
    } catch (error) {
      console.error('Get inventory analytics error:', error)
      throw new Error('Failed to fetch inventory analytics')
    }
  }

  private async getTopProducts(startDate: Date, endDate: Date) {
    const result = await prisma.orderItem.groupBy({
      by: ['productId', 'name'],
      where: {
        order: {
          createdAt: {
            gte: startDate,
            lte: endDate
          },
          paymentStatus: 'paid'
        }
      },
      _sum: {
        quantity: true,
        price: true
      },
      orderBy: {
        _sum: {
          quantity: 'desc'
        }
      },
      take: 10
    })

    return result.map(item => ({
      productId: item.productId,
      productName: item.name,
      totalSold: item._sum.quantity || 0,
      revenue: (item._sum.price || 0) * (item._sum.quantity || 0)
    }))
  }

  private async getDailySales(startDate: Date, endDate: Date) {
    // This would require a more complex query to group by date
    // For now, return mock data structure
    const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
    const dailySales = []

    for (let i = 0; i < days; i++) {
      const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000)
      dailySales.push({
        date: date.toISOString().split('T')[0],
        revenue: Math.random() * 5000 + 1000, // Mock data
        orders: Math.floor(Math.random() * 20 + 5) // Mock data
      })
    }

    return dailySales
  }

  private async getSalesByCategory(startDate: Date, endDate: Date) {
    // This would require joining with product categories
    // For now, return mock data
    return [
      { category: 'Girls', revenue: 15000, orders: 45 },
      { category: 'Boys', revenue: 12000, orders: 38 },
      { category: 'Baby', revenue: 8000, orders: 25 },
      { category: 'Accessories', revenue: 5000, orders: 20 }
    ]
  }

  private async getTopCustomers(startDate: Date, endDate: Date) {
    const result = await prisma.order.groupBy({
      by: ['customerId', 'customerEmail'],
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        },
        paymentStatus: 'paid',
        customerId: { not: null }
      },
      _sum: {
        total: true
      },
      _count: true,
      orderBy: {
        _sum: {
          total: 'desc'
        }
      },
      take: 10
    })

    return result.map(customer => ({
      customerId: customer.customerId || '',
      customerEmail: customer.customerEmail,
      totalSpent: customer._sum.total || 0,
      orderCount: customer._count
    }))
  }

  private async getCustomersByRegion(startDate: Date, endDate: Date) {
    // This would require analyzing shipping addresses
    // For now, return mock data
    return [
      { province: 'Gauteng', customerCount: 150, revenue: 45000 },
      { province: 'Western Cape', customerCount: 120, revenue: 38000 },
      { province: 'KwaZulu-Natal', customerCount: 80, revenue: 25000 },
      { province: 'Eastern Cape', customerCount: 45, revenue: 15000 }
    ]
  }

  private async getTopPerformingProducts() {
    // This would require view tracking and conversion calculation
    // For now, return mock data
    return [
      {
        productId: '1',
        productName: 'Girls Summer Dress',
        views: 1500,
        sales: 45,
        conversionRate: 0.03,
        revenue: 4500
      },
      {
        productId: '2',
        productName: 'Boys Casual Shirt',
        views: 1200,
        sales: 38,
        conversionRate: 0.032,
        revenue: 3800
      }
    ]
  }

  private async getCategoryPerformance() {
    // This would require category analysis
    // For now, return mock data
    return [
      {
        category: 'Girls',
        productCount: 45,
        totalSales: 150,
        averagePrice: 299.99
      },
      {
        category: 'Boys',
        productCount: 38,
        totalSales: 120,
        averagePrice: 279.99
      }
    ]
  }

  private async getFastMovingItems() {
    // This would require turnover rate calculation
    // For now, return mock data
    return [
      {
        productId: '1',
        productName: 'Popular T-Shirt',
        turnoverRate: 12.5,
        daysOfStock: 15
      }
    ]
  }

  private async getSlowMovingItems() {
    // This would require turnover rate calculation
    // For now, return mock data
    return [
      {
        productId: '2',
        productName: 'Seasonal Jacket',
        turnoverRate: 2.1,
        daysOfStock: 90
      }
    ]
  }

  private async getInventoryByCategory() {
    // This would require category-based inventory analysis
    // For now, return mock data
    return [
      {
        category: 'Girls',
        totalValue: 125000,
        itemCount: 450
      },
      {
        category: 'Boys',
        totalValue: 98000,
        itemCount: 380
      }
    ]
  }
}
