import { ShippingAddress } from './order-service'

export interface ShippingRate {
  id: string
  name: string
  description: string
  price: number
  currency: string
  estimatedDays: number
  carrier: string
}

export interface ShippingLabel {
  id: string
  trackingNumber: string
  labelUrl: string
  carrier: string
  service: string
}

export interface TrackingInfo {
  trackingNumber: string
  status: 'pending' | 'in_transit' | 'delivered' | 'exception' | 'cancelled'
  events: TrackingEvent[]
  estimatedDelivery?: Date
}

export interface TrackingEvent {
  timestamp: Date
  status: string
  description: string
  location?: string
}

// South African courier services
export class ShippingService {
  private courierGuyApiKey: string
  private postNetApiKey: string
  private dawnWingApiKey: string

  constructor() {
    this.courierGuyApiKey = process.env.COURIER_GUY_API_KEY || ''
    this.postNetApiKey = process.env.POSTNET_API_KEY || ''
    this.dawnWingApiKey = process.env.DAWN_WING_API_KEY || ''
  }

  async getShippingRates(
    fromAddress: ShippingAddress,
    toAddress: ShippingAddress,
    weight: number, // in kg
    dimensions?: { length: number; width: number; height: number } // in cm
  ): Promise<ShippingRate[]> {
    const rates: ShippingRate[] = []

    try {
      // Standard rates for South African shipping
      rates.push(
        {
          id: 'standard',
          name: 'Standard Shipping',
          description: '3-5 business days',
          price: 99.00,
          currency: 'ZAR',
          estimatedDays: 4,
          carrier: 'PostNet'
        },
        {
          id: 'express',
          name: 'Express Shipping',
          description: '1-2 business days',
          price: 149.00,
          currency: 'ZAR',
          estimatedDays: 1,
          carrier: 'CourierGuy'
        },
        {
          id: 'overnight',
          name: 'Overnight Shipping',
          description: 'Next business day',
          price: 199.00,
          currency: 'ZAR',
          estimatedDays: 1,
          carrier: 'DawnWing'
        }
      )

      // Add distance-based pricing
      const distance = this.calculateDistance(fromAddress, toAddress)
      if (distance > 500) { // Long distance
        rates.forEach(rate => {
          rate.price += 50 // Add R50 for long distance
        })
      }

      // Add weight-based pricing
      if (weight > 5) { // Over 5kg
        const extraWeight = weight - 5
        const extraCost = Math.ceil(extraWeight) * 20 // R20 per kg
        rates.forEach(rate => {
          rate.price += extraCost
        })
      }

      return rates

    } catch (error) {
      console.error('Get shipping rates error:', error)
      return rates // Return default rates on error
    }
  }

  async createShippingLabel(
    orderId: string,
    fromAddress: ShippingAddress,
    toAddress: ShippingAddress,
    weight: number,
    service: string,
    items: Array<{ name: string; quantity: number; value: number }>
  ): Promise<ShippingLabel | null> {
    try {
      // Generate tracking number
      const trackingNumber = this.generateTrackingNumber(service)

      // In a real implementation, this would call the courier API
      // For now, we'll simulate the response
      const label: ShippingLabel = {
        id: `label_${Date.now()}`,
        trackingNumber,
        labelUrl: `/api/shipping/labels/${trackingNumber}.pdf`,
        carrier: this.getCarrierForService(service),
        service
      }

      // Store shipping info in database
      await this.storeShippingInfo(orderId, label, fromAddress, toAddress, weight, items)

      return label

    } catch (error) {
      console.error('Create shipping label error:', error)
      return null
    }
  }

  async trackShipment(trackingNumber: string): Promise<TrackingInfo | null> {
    try {
      // In a real implementation, this would call the courier tracking API
      // For now, we'll simulate tracking data
      const mockEvents: TrackingEvent[] = [
        {
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          status: 'picked_up',
          description: 'Package picked up from sender',
          location: 'Johannesburg, GP'
        },
        {
          timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
          status: 'in_transit',
          description: 'Package in transit to destination',
          location: 'Cape Town, WC'
        }
      ]

      // Simulate current status based on time
      const daysSinceShipped = Math.floor((Date.now() - mockEvents[0].timestamp.getTime()) / (24 * 60 * 60 * 1000))
      let status: TrackingInfo['status'] = 'in_transit'
      
      if (daysSinceShipped >= 3) {
        status = 'delivered'
        mockEvents.push({
          timestamp: new Date(),
          status: 'delivered',
          description: 'Package delivered successfully',
          location: 'Cape Town, WC'
        })
      }

      return {
        trackingNumber,
        status,
        events: mockEvents,
        estimatedDelivery: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000)
      }

    } catch (error) {
      console.error('Track shipment error:', error)
      return null
    }
  }

  async cancelShipment(trackingNumber: string): Promise<boolean> {
    try {
      // In a real implementation, this would call the courier cancellation API
      console.log(`Cancelling shipment: ${trackingNumber}`)
      return true

    } catch (error) {
      console.error('Cancel shipment error:', error)
      return false
    }
  }

  private calculateDistance(from: ShippingAddress, to: ShippingAddress): number {
    // Simplified distance calculation based on provinces
    const provinceDistances: Record<string, Record<string, number>> = {
      'GP': { 'WC': 1400, 'KZN': 600, 'EC': 1000, 'FS': 400, 'LP': 300, 'MP': 350, 'NW': 200, 'NC': 800 },
      'WC': { 'GP': 1400, 'KZN': 1600, 'EC': 700, 'FS': 1000, 'LP': 1700, 'MP': 1500, 'NW': 1200, 'NC': 500 },
      'KZN': { 'GP': 600, 'WC': 1600, 'EC': 800, 'FS': 500, 'LP': 900, 'MP': 400, 'NW': 800, 'NC': 1200 }
      // Add more province combinations as needed
    }

    const fromProvince = from.province.toUpperCase()
    const toProvince = to.province.toUpperCase()

    if (fromProvince === toProvince) return 100 // Same province
    
    return provinceDistances[fromProvince]?.[toProvince] || 500 // Default distance
  }

  private generateTrackingNumber(service: string): string {
    const prefix = service.toUpperCase().substring(0, 3)
    const timestamp = Date.now().toString().slice(-8)
    const random = Math.random().toString(36).substring(2, 6).toUpperCase()
    
    return `${prefix}${timestamp}${random}`
  }

  private getCarrierForService(service: string): string {
    switch (service) {
      case 'standard': return 'PostNet'
      case 'express': return 'CourierGuy'
      case 'overnight': return 'DawnWing'
      default: return 'PostNet'
    }
  }

  private async storeShippingInfo(
    orderId: string,
    label: ShippingLabel,
    fromAddress: ShippingAddress,
    toAddress: ShippingAddress,
    weight: number,
    items: Array<{ name: string; quantity: number; value: number }>
  ): Promise<void> {
    try {
      // Store shipping information in database
      // This would typically use Prisma to store in a shipments table
      console.log('Storing shipping info for order:', orderId, label)
      
    } catch (error) {
      console.error('Store shipping info error:', error)
    }
  }

  // Webhook handler for courier status updates
  async handleCourierWebhook(provider: string, data: any): Promise<void> {
    try {
      let trackingNumber: string
      let status: string

      switch (provider) {
        case 'courierguy':
          trackingNumber = data.tracking_number
          status = data.status
          break
        case 'postnet':
          trackingNumber = data.waybill_number
          status = data.delivery_status
          break
        case 'dawnwing':
          trackingNumber = data.consignment_number
          status = data.status_code
          break
        default:
          console.error('Unknown courier provider:', provider)
          return
      }

      // Update order with tracking information
      // This would typically update the order status and send notifications
      console.log(`Courier update: ${trackingNumber} - ${status}`)

    } catch (error) {
      console.error('Handle courier webhook error:', error)
    }
  }
}
