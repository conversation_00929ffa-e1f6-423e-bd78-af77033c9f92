import { Order } from './order-service'

export interface EmailTemplate {
  subject: string
  html: string
  text: string
}

export class EmailService {
  private apiKey: string
  private fromEmail: string
  private fromName: string

  constructor() {
    this.apiKey = process.env.SENDGRID_API_KEY || process.env.RESEND_API_KEY || ''
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>'
    this.fromName = process.env.FROM_NAME || 'Coco Milk Kids'
  }

  async sendOrderConfirmation(order: Order): Promise<boolean> {
    try {
      const template = this.generateOrderConfirmationTemplate(order)
      
      return await this.sendEmail({
        to: order.customerEmail,
        subject: template.subject,
        html: template.html,
        text: template.text
      })
    } catch (error) {
      console.error('Send order confirmation error:', error)
      return false
    }
  }

  async sendOrderStatusUpdate(order: Order): Promise<boolean> {
    try {
      const template = this.generateOrderStatusUpdateTemplate(order)
      
      return await this.sendEmail({
        to: order.customerEmail,
        subject: template.subject,
        html: template.html,
        text: template.text
      })
    } catch (error) {
      console.error('Send order status update error:', error)
      return false
    }
  }

  async sendShippingNotification(order: Order): Promise<boolean> {
    try {
      const template = this.generateShippingNotificationTemplate(order)
      
      return await this.sendEmail({
        to: order.customerEmail,
        subject: template.subject,
        html: template.html,
        text: template.text
      })
    } catch (error) {
      console.error('Send shipping notification error:', error)
      return false
    }
  }

  private async sendEmail(params: {
    to: string
    subject: string
    html: string
    text: string
  }): Promise<boolean> {
    try {
      // Using Resend as primary email service
      if (process.env.RESEND_API_KEY) {
        const response = await fetch('https://api.resend.com/emails', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.RESEND_API_KEY}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            from: `${this.fromName} <${this.fromEmail}>`,
            to: [params.to],
            subject: params.subject,
            html: params.html,
            text: params.text
          })
        })

        return response.ok
      }

      // Fallback to SendGrid
      if (process.env.SENDGRID_API_KEY) {
        const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.SENDGRID_API_KEY}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            personalizations: [{
              to: [{ email: params.to }]
            }],
            from: {
              email: this.fromEmail,
              name: this.fromName
            },
            subject: params.subject,
            content: [
              {
                type: 'text/html',
                value: params.html
              },
              {
                type: 'text/plain',
                value: params.text
              }
            ]
          })
        })

        return response.ok
      }

      // Development mode - log email
      console.log('Email would be sent:', params)
      return true
    } catch (error) {
      console.error('Send email error:', error)
      return false
    }
  }

  private generateOrderConfirmationTemplate(order: Order): EmailTemplate {
    const subject = `Order Confirmation - ${order.orderNumber}`
    
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${subject}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 20px; margin-bottom: 30px; }
            .order-details { background: #f9f9f9; padding: 20px; margin: 20px 0; }
            .items-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            .items-table th, .items-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
            .items-table th { background: #f5f5f5; font-weight: bold; }
            .total-row { font-weight: bold; font-size: 1.1em; }
            .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>COCO MILK KIDS</h1>
              <h2>Order Confirmation</h2>
            </div>
            
            <p>Dear Customer,</p>
            <p>Thank you for your order! We've received your order and will process it shortly.</p>
            
            <div class="order-details">
              <h3>Order Details</h3>
              <p><strong>Order Number:</strong> ${order.orderNumber}</p>
              <p><strong>Order Date:</strong> ${order.createdAt.toLocaleDateString()}</p>
              <p><strong>Email:</strong> ${order.customerEmail}</p>
            </div>
            
            <h3>Items Ordered</h3>
            <table class="items-table">
              <thead>
                <tr>
                  <th>Item</th>
                  <th>Quantity</th>
                  <th>Price</th>
                  <th>Total</th>
                </tr>
              </thead>
              <tbody>
                ${order.items.map(item => `
                  <tr>
                    <td>
                      ${item.name}
                      ${item.color ? `<br><small>Color: ${item.color}</small>` : ''}
                      ${item.size ? `<br><small>Size: ${item.size}</small>` : ''}
                    </td>
                    <td>${item.quantity}</td>
                    <td>R${item.price.toFixed(2)}</td>
                    <td>R${(item.price * item.quantity).toFixed(2)}</td>
                  </tr>
                `).join('')}
              </tbody>
              <tfoot>
                <tr>
                  <td colspan="3"><strong>Subtotal:</strong></td>
                  <td><strong>R${order.subtotal.toFixed(2)}</strong></td>
                </tr>
                <tr>
                  <td colspan="3"><strong>Shipping:</strong></td>
                  <td><strong>R${order.shippingCost.toFixed(2)}</strong></td>
                </tr>
                <tr>
                  <td colspan="3"><strong>VAT (15%):</strong></td>
                  <td><strong>R${order.taxAmount.toFixed(2)}</strong></td>
                </tr>
                ${order.discountAmount > 0 ? `
                  <tr>
                    <td colspan="3"><strong>Discount:</strong></td>
                    <td><strong>-R${order.discountAmount.toFixed(2)}</strong></td>
                  </tr>
                ` : ''}
                <tr class="total-row">
                  <td colspan="3"><strong>Total:</strong></td>
                  <td><strong>R${order.total.toFixed(2)}</strong></td>
                </tr>
              </tfoot>
            </table>
            
            <div class="order-details">
              <h3>Shipping Address</h3>
              <p>
                ${order.shippingAddress.firstName} ${order.shippingAddress.lastName}<br>
                ${order.shippingAddress.address1}<br>
                ${order.shippingAddress.address2 ? `${order.shippingAddress.address2}<br>` : ''}
                ${order.shippingAddress.city}, ${order.shippingAddress.province} ${order.shippingAddress.postalCode}<br>
                ${order.shippingAddress.country}
              </p>
            </div>
            
            <p>We'll send you another email when your order ships with tracking information.</p>
            
            <div class="footer">
              <p>Thank you for shopping with Coco Milk Kids!</p>
              <p>If you have any questions, please contact <NAME_EMAIL></p>
            </div>
          </div>
        </body>
      </html>
    `
    
    const text = `
      Order Confirmation - ${order.orderNumber}
      
      Dear Customer,
      
      Thank you for your order! We've received your order and will process it shortly.
      
      Order Details:
      Order Number: ${order.orderNumber}
      Order Date: ${order.createdAt.toLocaleDateString()}
      Email: ${order.customerEmail}
      
      Items Ordered:
      ${order.items.map(item => 
        `${item.name} ${item.color ? `(${item.color})` : ''} ${item.size ? `(${item.size})` : ''} - Qty: ${item.quantity} - R${(item.price * item.quantity).toFixed(2)}`
      ).join('\n')}
      
      Subtotal: R${order.subtotal.toFixed(2)}
      Shipping: R${order.shippingCost.toFixed(2)}
      VAT (15%): R${order.taxAmount.toFixed(2)}
      ${order.discountAmount > 0 ? `Discount: -R${order.discountAmount.toFixed(2)}\n` : ''}
      Total: R${order.total.toFixed(2)}
      
      Shipping Address:
      ${order.shippingAddress.firstName} ${order.shippingAddress.lastName}
      ${order.shippingAddress.address1}
      ${order.shippingAddress.address2 ? `${order.shippingAddress.address2}\n` : ''}
      ${order.shippingAddress.city}, ${order.shippingAddress.province} ${order.shippingAddress.postalCode}
      ${order.shippingAddress.country}
      
      We'll send you another email when your order ships with tracking information.
      
      Thank you for shopping with Coco Milk Kids!
      If you have any questions, please contact <NAME_EMAIL>
    `
    
    return { subject, html, text }
  }

  private generateOrderStatusUpdateTemplate(order: Order): EmailTemplate {
    const statusMessages = {
      pending: 'Your order is being processed',
      confirmed: 'Your order has been confirmed',
      processing: 'Your order is being prepared',
      shipped: 'Your order has been shipped',
      delivered: 'Your order has been delivered',
      cancelled: 'Your order has been cancelled',
      refunded: 'Your order has been refunded'
    }

    const subject = `Order Update - ${order.orderNumber} - ${statusMessages[order.status]}`
    
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${subject}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 20px; margin-bottom: 30px; }
            .status-update { background: #f0f8ff; padding: 20px; margin: 20px 0; border-left: 4px solid #007cba; }
            .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>COCO MILK KIDS</h1>
              <h2>Order Update</h2>
            </div>
            
            <div class="status-update">
              <h3>${statusMessages[order.status]}</h3>
              <p><strong>Order Number:</strong> ${order.orderNumber}</p>
              <p><strong>Status:</strong> ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}</p>
              ${order.trackingNumber ? `<p><strong>Tracking Number:</strong> ${order.trackingNumber}</p>` : ''}
            </div>
            
            <p>Dear Customer,</p>
            <p>We wanted to update you on the status of your order.</p>
            
            ${order.status === 'shipped' && order.trackingNumber ? `
              <p>Your order has been shipped and is on its way to you. You can track your package using the tracking number above.</p>
            ` : ''}
            
            <div class="footer">
              <p>Thank you for shopping with Coco Milk Kids!</p>
              <p>If you have any questions, please contact <NAME_EMAIL></p>
            </div>
          </div>
        </body>
      </html>
    `
    
    const text = `
      Order Update - ${order.orderNumber}
      
      ${statusMessages[order.status]}
      
      Order Number: ${order.orderNumber}
      Status: ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}
      ${order.trackingNumber ? `Tracking Number: ${order.trackingNumber}` : ''}
      
      Dear Customer,
      
      We wanted to update you on the status of your order.
      
      ${order.status === 'shipped' && order.trackingNumber ? 
        'Your order has been shipped and is on its way to you. You can track your package using the tracking number above.' : ''}
      
      Thank you for shopping with Coco Milk Kids!
      If you have any questions, please contact <NAME_EMAIL>
    `
    
    return { subject, html, text }
  }

  private generateShippingNotificationTemplate(order: Order): EmailTemplate {
    return this.generateOrderStatusUpdateTemplate(order)
  }
}
