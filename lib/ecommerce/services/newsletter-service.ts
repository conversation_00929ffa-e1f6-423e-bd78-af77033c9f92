import { prisma } from '@/lib/prisma'

export interface NewsletterSubscription {
  email: string
  firstName?: string
  lastName?: string
  preferences?: {
    newArrivals?: boolean
    sales?: boolean
    editorial?: boolean
  }
}

export interface NewsletterSubscriber {
  id: string
  email: string
  firstName?: string
  lastName?: string
  isActive: boolean
  preferences: {
    newArrivals: boolean
    sales: boolean
    editorial: boolean
  }
  subscribedAt: Date
  unsubscribedAt?: Date
}

/**
 * Subscribe a user to the newsletter
 */
export async function subscribeToNewsletter(data: NewsletterSubscription): Promise<NewsletterSubscriber> {
  try {
    // Check if email already exists
    const existingSubscriber = await prisma.newsletterSubscriber.findUnique({
      where: { email: data.email }
    })

    if (existingSubscriber) {
      if (existingSubscriber.isActive) {
        throw new Error('Email is already subscribed to newsletter')
      } else {
        // Reactivate existing subscriber
        const reactivated = await prisma.newsletterSubscriber.update({
          where: { email: data.email },
          data: {
            isActive: true,
            firstName: data.firstName || existingSubscriber.firstName,
            lastName: data.lastName || existingSubscriber.lastName,
            preferences: {
              newArrivals: data.preferences?.newArrivals ?? true,
              sales: data.preferences?.sales ?? true,
              editorial: data.preferences?.editorial ?? true
            },
            unsubscribedAt: null
          }
        })

        return {
          id: reactivated.id,
          email: reactivated.email,
          firstName: reactivated.firstName || undefined,
          lastName: reactivated.lastName || undefined,
          isActive: reactivated.isActive,
          preferences: reactivated.preferences as any,
          subscribedAt: reactivated.subscribedAt,
          unsubscribedAt: reactivated.unsubscribedAt || undefined
        }
      }
    }

    // Create new subscriber
    const subscriber = await prisma.newsletterSubscriber.create({
      data: {
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        isActive: true,
        preferences: {
          newArrivals: data.preferences?.newArrivals ?? true,
          sales: data.preferences?.sales ?? true,
          editorial: data.preferences?.editorial ?? true
        }
      }
    })

    // TODO: Send welcome email
    // await sendWelcomeEmail(subscriber.email, subscriber.firstName)

    return {
      id: subscriber.id,
      email: subscriber.email,
      firstName: subscriber.firstName || undefined,
      lastName: subscriber.lastName || undefined,
      isActive: subscriber.isActive,
      preferences: subscriber.preferences as any,
      subscribedAt: subscriber.subscribedAt,
      unsubscribedAt: subscriber.unsubscribedAt || undefined
    }
  } catch (error) {
    console.error('Newsletter subscription error:', error)
    throw new Error('Failed to subscribe to newsletter')
  }
}

/**
 * Unsubscribe a user from the newsletter
 */
export async function unsubscribeFromNewsletter(email: string): Promise<void> {
  try {
    await prisma.newsletterSubscriber.update({
      where: { email },
      data: {
        isActive: false,
        unsubscribedAt: new Date()
      }
    })

    // TODO: Send unsubscribe confirmation email
    // await sendUnsubscribeConfirmationEmail(email)
  } catch (error) {
    console.error('Newsletter unsubscribe error:', error)
    throw new Error('Failed to unsubscribe from newsletter')
  }
}

/**
 * Update newsletter preferences
 */
export async function updateNewsletterPreferences(
  email: string, 
  preferences: Partial<NewsletterSubscription['preferences']>
): Promise<NewsletterSubscriber> {
  try {
    const subscriber = await prisma.newsletterSubscriber.update({
      where: { email },
      data: {
        preferences: {
          newArrivals: preferences.newArrivals ?? true,
          sales: preferences.sales ?? true,
          editorial: preferences.editorial ?? true
        }
      }
    })

    return {
      id: subscriber.id,
      email: subscriber.email,
      firstName: subscriber.firstName || undefined,
      lastName: subscriber.lastName || undefined,
      isActive: subscriber.isActive,
      preferences: subscriber.preferences as any,
      subscribedAt: subscriber.subscribedAt,
      unsubscribedAt: subscriber.unsubscribedAt || undefined
    }
  } catch (error) {
    console.error('Newsletter preferences update error:', error)
    throw new Error('Failed to update newsletter preferences')
  }
}

/**
 * Get newsletter subscriber by email
 */
export async function getNewsletterSubscriber(email: string): Promise<NewsletterSubscriber | null> {
  try {
    const subscriber = await prisma.newsletterSubscriber.findUnique({
      where: { email }
    })

    if (!subscriber) {
      return null
    }

    return {
      id: subscriber.id,
      email: subscriber.email,
      firstName: subscriber.firstName || undefined,
      lastName: subscriber.lastName || undefined,
      isActive: subscriber.isActive,
      preferences: subscriber.preferences as any,
      subscribedAt: subscriber.subscribedAt,
      unsubscribedAt: subscriber.unsubscribedAt || undefined
    }
  } catch (error) {
    console.error('Get newsletter subscriber error:', error)
    return null
  }
}

/**
 * Get all active newsletter subscribers
 */
export async function getActiveNewsletterSubscribers(): Promise<NewsletterSubscriber[]> {
  try {
    const subscribers = await prisma.newsletterSubscriber.findMany({
      where: { isActive: true },
      orderBy: { subscribedAt: 'desc' }
    })

    return subscribers.map(subscriber => ({
      id: subscriber.id,
      email: subscriber.email,
      firstName: subscriber.firstName || undefined,
      lastName: subscriber.lastName || undefined,
      isActive: subscriber.isActive,
      preferences: subscriber.preferences as any,
      subscribedAt: subscriber.subscribedAt,
      unsubscribedAt: subscriber.unsubscribedAt || undefined
    }))
  } catch (error) {
    console.error('Get active newsletter subscribers error:', error)
    return []
  }
}

/**
 * Get newsletter subscription statistics
 */
export async function getNewsletterStats() {
  try {
    const [totalSubscribers, activeSubscribers, recentSubscribers] = await Promise.all([
      prisma.newsletterSubscriber.count(),
      prisma.newsletterSubscriber.count({ where: { isActive: true } }),
      prisma.newsletterSubscriber.count({
        where: {
          isActive: true,
          subscribedAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        }
      })
    ])

    return {
      total: totalSubscribers,
      active: activeSubscribers,
      inactive: totalSubscribers - activeSubscribers,
      recentSubscriptions: recentSubscribers
    }
  } catch (error) {
    console.error('Get newsletter stats error:', error)
    return {
      total: 0,
      active: 0,
      inactive: 0,
      recentSubscriptions: 0
    }
  }
}
