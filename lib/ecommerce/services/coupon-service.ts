import { prisma } from '@/lib/prisma'

export interface Coupon {
  id: string
  code: string
  name: string
  description?: string
  type: 'percentage' | 'fixed_amount' | 'free_shipping'
  value: number
  minimumOrderAmount?: number
  maximumDiscountAmount?: number
  usageLimit?: number
  usageCount: number
  customerUsageLimit?: number
  isActive: boolean
  startsAt?: Date
  expiresAt?: Date
  applicableProducts?: string[]
  applicableCategories?: string[]
  excludedProducts?: string[]
  excludedCategories?: string[]
  createdAt: Date
  updatedAt: Date
}

export interface CouponValidationResult {
  isValid: boolean
  coupon?: Coupon
  discountAmount: number
  error?: string
}

export interface CreateCouponRequest {
  code: string
  name: string
  description?: string
  type: 'percentage' | 'fixed_amount' | 'free_shipping'
  value: number
  minimumOrderAmount?: number
  maximumDiscountAmount?: number
  usageLimit?: number
  customerUsageLimit?: number
  startsAt?: Date
  expiresAt?: Date
  applicableProducts?: string[]
  applicableCategories?: string[]
  excludedProducts?: string[]
  excludedCategories?: string[]
}

export class CouponService {
  async createCoupon(data: CreateCouponRequest): Promise<Coupon> {
    try {
      // Check if code already exists
      const existingCoupon = await prisma.coupon.findUnique({
        where: { code: data.code.toUpperCase() }
      })

      if (existingCoupon) {
        throw new Error('Coupon code already exists')
      }

      const coupon = await prisma.coupon.create({
        data: {
          code: data.code.toUpperCase(),
          name: data.name,
          description: data.description,
          type: data.type,
          value: data.value,
          minimumOrderAmount: data.minimumOrderAmount,
          maximumDiscountAmount: data.maximumDiscountAmount,
          usageLimit: data.usageLimit,
          usageCount: 0,
          customerUsageLimit: data.customerUsageLimit,
          isActive: true,
          startsAt: data.startsAt,
          expiresAt: data.expiresAt,
          applicableProducts: data.applicableProducts || [],
          applicableCategories: data.applicableCategories || [],
          excludedProducts: data.excludedProducts || [],
          excludedCategories: data.excludedCategories || []
        }
      })

      return this.formatCoupon(coupon)
    } catch (error) {
      console.error('Create coupon error:', error)
      throw new Error('Failed to create coupon')
    }
  }

  async getCoupon(couponId: string): Promise<Coupon | null> {
    try {
      const coupon = await prisma.coupon.findUnique({
        where: { id: couponId }
      })

      return coupon ? this.formatCoupon(coupon) : null
    } catch (error) {
      console.error('Get coupon error:', error)
      return null
    }
  }

  async getCouponByCode(code: string): Promise<Coupon | null> {
    try {
      const coupon = await prisma.coupon.findUnique({
        where: { code: code.toUpperCase() }
      })

      return coupon ? this.formatCoupon(coupon) : null
    } catch (error) {
      console.error('Get coupon by code error:', error)
      return null
    }
  }

  async validateCoupon(
    code: string,
    customerId: string | null,
    orderItems: Array<{ productId: string; quantity: number; price: number }>,
    subtotal: number
  ): Promise<CouponValidationResult> {
    try {
      const coupon = await this.getCouponByCode(code)

      if (!coupon) {
        return {
          isValid: false,
          discountAmount: 0,
          error: 'Invalid coupon code'
        }
      }

      // Check if coupon is active
      if (!coupon.isActive) {
        return {
          isValid: false,
          discountAmount: 0,
          error: 'Coupon is not active'
        }
      }

      // Check start date
      if (coupon.startsAt && new Date() < coupon.startsAt) {
        return {
          isValid: false,
          discountAmount: 0,
          error: 'Coupon is not yet valid'
        }
      }

      // Check expiry date
      if (coupon.expiresAt && new Date() > coupon.expiresAt) {
        return {
          isValid: false,
          discountAmount: 0,
          error: 'Coupon has expired'
        }
      }

      // Check usage limit
      if (coupon.usageLimit && coupon.usageCount >= coupon.usageLimit) {
        return {
          isValid: false,
          discountAmount: 0,
          error: 'Coupon usage limit reached'
        }
      }

      // Check customer usage limit
      if (customerId && coupon.customerUsageLimit) {
        const customerUsage = await prisma.couponUsage.count({
          where: {
            couponId: coupon.id,
            customerId
          }
        })

        if (customerUsage >= coupon.customerUsageLimit) {
          return {
            isValid: false,
            discountAmount: 0,
            error: 'You have reached the usage limit for this coupon'
          }
        }
      }

      // Check minimum order amount
      if (coupon.minimumOrderAmount && subtotal < coupon.minimumOrderAmount) {
        return {
          isValid: false,
          discountAmount: 0,
          error: `Minimum order amount of R${coupon.minimumOrderAmount.toFixed(2)} required`
        }
      }

      // Check product/category restrictions
      const applicableAmount = this.calculateApplicableAmount(coupon, orderItems)
      if (applicableAmount === 0) {
        return {
          isValid: false,
          discountAmount: 0,
          error: 'Coupon is not applicable to items in your cart'
        }
      }

      // Calculate discount amount
      const discountAmount = this.calculateDiscountAmount(coupon, applicableAmount)

      return {
        isValid: true,
        coupon,
        discountAmount
      }
    } catch (error) {
      console.error('Validate coupon error:', error)
      return {
        isValid: false,
        discountAmount: 0,
        error: 'Failed to validate coupon'
      }
    }
  }

  async applyCoupon(
    couponId: string,
    orderId: string,
    customerId: string | null,
    discountAmount: number
  ): Promise<boolean> {
    try {
      // Record coupon usage
      await prisma.couponUsage.create({
        data: {
          couponId,
          orderId,
          customerId,
          discountAmount
        }
      })

      // Increment usage count
      await prisma.coupon.update({
        where: { id: couponId },
        data: {
          usageCount: {
            increment: 1
          }
        }
      })

      return true
    } catch (error) {
      console.error('Apply coupon error:', error)
      return false
    }
  }

  async getAllCoupons(filters?: {
    isActive?: boolean
    type?: string
    page?: number
    limit?: number
  }): Promise<{ coupons: Coupon[]; total: number }> {
    try {
      const {
        isActive,
        type,
        page = 1,
        limit = 20
      } = filters || {}

      const whereClause: any = {}
      if (isActive !== undefined) whereClause.isActive = isActive
      if (type) whereClause.type = type

      const [coupons, total] = await Promise.all([
        prisma.coupon.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: (page - 1) * limit
        }),
        prisma.coupon.count({ where: whereClause })
      ])

      return {
        coupons: coupons.map(coupon => this.formatCoupon(coupon)),
        total
      }
    } catch (error) {
      console.error('Get all coupons error:', error)
      return { coupons: [], total: 0 }
    }
  }

  async updateCoupon(couponId: string, data: Partial<CreateCouponRequest>): Promise<Coupon | null> {
    try {
      const coupon = await prisma.coupon.update({
        where: { id: couponId },
        data: {
          ...(data.name && { name: data.name }),
          ...(data.description !== undefined && { description: data.description }),
          ...(data.type && { type: data.type }),
          ...(data.value !== undefined && { value: data.value }),
          ...(data.minimumOrderAmount !== undefined && { minimumOrderAmount: data.minimumOrderAmount }),
          ...(data.maximumDiscountAmount !== undefined && { maximumDiscountAmount: data.maximumDiscountAmount }),
          ...(data.usageLimit !== undefined && { usageLimit: data.usageLimit }),
          ...(data.customerUsageLimit !== undefined && { customerUsageLimit: data.customerUsageLimit }),
          ...(data.startsAt !== undefined && { startsAt: data.startsAt }),
          ...(data.expiresAt !== undefined && { expiresAt: data.expiresAt }),
          ...(data.applicableProducts && { applicableProducts: data.applicableProducts }),
          ...(data.applicableCategories && { applicableCategories: data.applicableCategories }),
          ...(data.excludedProducts && { excludedProducts: data.excludedProducts }),
          ...(data.excludedCategories && { excludedCategories: data.excludedCategories }),
          updatedAt: new Date()
        }
      })

      return this.formatCoupon(coupon)
    } catch (error) {
      console.error('Update coupon error:', error)
      return null
    }
  }

  async deactivateCoupon(couponId: string): Promise<boolean> {
    try {
      await prisma.coupon.update({
        where: { id: couponId },
        data: { isActive: false }
      })

      return true
    } catch (error) {
      console.error('Deactivate coupon error:', error)
      return false
    }
  }

  private calculateApplicableAmount(
    coupon: Coupon,
    orderItems: Array<{ productId: string; quantity: number; price: number }>
  ): number {
    // If no restrictions, apply to all items
    if (
      (!coupon.applicableProducts || coupon.applicableProducts.length === 0) &&
      (!coupon.applicableCategories || coupon.applicableCategories.length === 0) &&
      (!coupon.excludedProducts || coupon.excludedProducts.length === 0) &&
      (!coupon.excludedCategories || coupon.excludedCategories.length === 0)
    ) {
      return orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
    }

    let applicableAmount = 0

    for (const item of orderItems) {
      let isApplicable = true

      // Check if product is excluded
      if (coupon.excludedProducts && coupon.excludedProducts.includes(item.productId)) {
        isApplicable = false
      }

      // Check if product is in applicable products (if specified)
      if (coupon.applicableProducts && coupon.applicableProducts.length > 0) {
        isApplicable = coupon.applicableProducts.includes(item.productId)
      }

      // TODO: Add category checks when product categories are implemented

      if (isApplicable) {
        applicableAmount += item.price * item.quantity
      }
    }

    return applicableAmount
  }

  private calculateDiscountAmount(coupon: Coupon, applicableAmount: number): number {
    let discountAmount = 0

    switch (coupon.type) {
      case 'percentage':
        discountAmount = applicableAmount * (coupon.value / 100)
        break
      case 'fixed_amount':
        discountAmount = coupon.value
        break
      case 'free_shipping':
        discountAmount = 0 // Shipping discount handled separately
        break
    }

    // Apply maximum discount limit
    if (coupon.maximumDiscountAmount && discountAmount > coupon.maximumDiscountAmount) {
      discountAmount = coupon.maximumDiscountAmount
    }

    // Ensure discount doesn't exceed applicable amount
    if (discountAmount > applicableAmount) {
      discountAmount = applicableAmount
    }

    return Math.round(discountAmount * 100) / 100 // Round to 2 decimal places
  }

  private formatCoupon(coupon: any): Coupon {
    return {
      id: coupon.id,
      code: coupon.code,
      name: coupon.name,
      description: coupon.description,
      type: coupon.type,
      value: coupon.value,
      minimumOrderAmount: coupon.minimumOrderAmount,
      maximumDiscountAmount: coupon.maximumDiscountAmount,
      usageLimit: coupon.usageLimit,
      usageCount: coupon.usageCount,
      customerUsageLimit: coupon.customerUsageLimit,
      isActive: coupon.isActive,
      startsAt: coupon.startsAt,
      expiresAt: coupon.expiresAt,
      applicableProducts: coupon.applicableProducts || [],
      applicableCategories: coupon.applicableCategories || [],
      excludedProducts: coupon.excludedProducts || [],
      excludedCategories: coupon.excludedCategories || [],
      createdAt: coupon.createdAt,
      updatedAt: coupon.updatedAt
    }
  }
}
