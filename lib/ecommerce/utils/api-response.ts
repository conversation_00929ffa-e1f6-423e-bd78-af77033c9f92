// Standardized API Response Utilities
// Ensures consistent response format across all e-commerce APIs

import { ApiResponse, ApiError } from '../types/base'

/**
 * Create a successful API response
 */
export function createSuccessResponse<T>(data: T): ApiResponse<T> {
  return {
    success: true,
    data
  }
}

/**
 * Create an error API response
 */
export function createErrorResponse(error: string | ApiError): ApiResponse<never> {
  if (typeof error === 'string') {
    return {
      success: false,
      error: {
        code: 'GENERIC_ERROR',
        message: error
      }
    }
  }

  return {
    success: false,
    error
  }
}

/**
 * Create a validation error response
 */
export function createValidationErrorResponse(message: string): ApiResponse<never> {
  return {
    success: false,
    error: {
      code: 'VALIDATION_ERROR',
      message
    }
  }
}

/**
 * Create an authentication error response
 */
export function createAuthErrorResponse(message: string = 'Authentication required'): ApiResponse<never> {
  return {
    success: false,
    error: {
      code: 'AUTH_ERROR',
      message
    }
  }
}

/**
 * Create a not found error response
 */
export function createNotFoundErrorResponse(resource: string, id?: string): ApiResponse<never> {
  const message = id 
    ? `${resource} with ID '${id}' not found`
    : `${resource} not found`
  
  return {
    success: false,
    error: {
      code: 'NOT_FOUND',
      message
    }
  }
}

/**
 * Create a forbidden error response
 */
export function createForbiddenErrorResponse(message: string = 'Access forbidden'): ApiResponse<never> {
  return {
    success: false,
    error: {
      code: 'FORBIDDEN',
      message
    }
  }
}

/**
 * Create an internal server error response
 */
export function createServerErrorResponse(message: string = 'Internal server error'): ApiResponse<never> {
  return {
    success: false,
    error: {
      code: 'SERVER_ERROR',
      message
    }
  }
}

/**
 * Extract error message from various error types
 */
export function extractErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  
  if (typeof error === 'string') {
    return error
  }
  
  if (error && typeof error === 'object' && 'message' in error) {
    return String((error as any).message)
  }
  
  return 'An unexpected error occurred'
}

/**
 * Convert service response to API response format
 */
export function convertServiceResponse<T>(serviceResponse: any): ApiResponse<T> {
  if (serviceResponse?.success) {
    return createSuccessResponse(serviceResponse.data)
  } else {
    const errorMessage = serviceResponse?.error || serviceResponse?.message || 'Operation failed'
    return createErrorResponse(errorMessage)
  }
}

/**
 * Handle and format errors for API responses
 */
export function handleApiError(error: unknown): { response: ApiResponse<never>, statusCode: number } {
  console.error('API Error:', error)
  
  if (error instanceof Error) {
    switch (error.name) {
      case 'ValidationError':
        return {
          response: createValidationErrorResponse(error.message),
          statusCode: 400
        }
      case 'NotFoundError':
        return {
          response: createNotFoundErrorResponse('Resource'),
          statusCode: 404
        }
      case 'UnauthorizedError':
        return {
          response: createAuthErrorResponse(error.message),
          statusCode: 401
        }
      case 'ForbiddenError':
        return {
          response: createForbiddenErrorResponse(error.message),
          statusCode: 403
        }
      default:
        return {
          response: createServerErrorResponse(error.message),
          statusCode: 500
        }
    }
  }
  
  return {
    response: createServerErrorResponse(extractErrorMessage(error)),
    statusCode: 500
  }
}