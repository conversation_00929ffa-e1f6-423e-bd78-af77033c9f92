// React hooks for order management
'use client'

import { useState, useEffect, useCallback } from 'react'
import { 
  Order, 
  CreateOrderInput,
  UpdateOrderInput,
  OrderSearchParams,
  PaginatedResponse
} from '../types'

export interface UseOrdersOptions {
  initialParams?: OrderSearchParams
  autoFetch?: boolean
}

export interface UseOrdersReturn {
  orders: Order[]
  loading: boolean
  error: string | null
  pagination: PaginatedResponse<Order>['pagination'] | null
  searchOrders: (params?: OrderSearchParams) => Promise<void>
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing order search and listing
 */
export function useOrders(options: UseOrdersOptions = {}): UseOrdersReturn {
  const { initialParams = {}, autoFetch = true } = options

  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<PaginatedResponse<Order>['pagination'] | null>(null)
  const [currentParams, setCurrentParams] = useState<OrderSearchParams>(initialParams)

  const searchOrders = useCallback(async (params?: OrderSearchParams) => {
    setLoading(true)
    setError(null)

    const searchParams = params || currentParams
    setCurrentParams(searchParams)

    try {
      const queryParams = new URLSearchParams()
      
      if (searchParams.page) queryParams.append('page', searchParams.page.toString())
      if (searchParams.limit) queryParams.append('limit', searchParams.limit.toString())
      if (searchParams.status) queryParams.append('status', searchParams.status)
      if (searchParams.paymentStatus) queryParams.append('paymentStatus', searchParams.paymentStatus)
      if (searchParams.customerId) queryParams.append('customerId', searchParams.customerId)
      if (searchParams.sortBy) queryParams.append('sortBy', searchParams.sortBy)
      if (searchParams.sortOrder) queryParams.append('sortOrder', searchParams.sortOrder)

      const response = await fetch(`/api/e-commerce/orders?${queryParams.toString()}`, {
        headers: { 'x-admin-request': 'true' }
      })
      const result = await response.json()

      if (result.success) {
        setOrders(result.data || [])
        setPagination(result.pagination || null)
      } else {
        setError(result.error || 'Failed to fetch orders')
        setOrders([])
        setPagination(null)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      setOrders([])
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, [currentParams])

  const refetch = useCallback(() => {
    return searchOrders(currentParams)
  }, [searchOrders, currentParams])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch) {
      searchOrders(initialParams)
    }
  }, []) // Only run on mount

  return {
    orders,
    loading,
    error,
    pagination,
    searchOrders,
    refetch,
    clearError
  }
}

export interface UseOrderOptions {
  orderId?: string
  autoFetch?: boolean
}

export interface UseOrderReturn {
  order: Order | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing a single order
 */
export function useOrder(options: UseOrderOptions = {}): UseOrderReturn {
  const { orderId, autoFetch = true } = options

  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchOrder = useCallback(async () => {
    if (!orderId) {
      setOrder(null)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/orders/${orderId}`, {
        headers: { 'x-admin-request': 'true' }
      })
      const result = await response.json()

      if (result.success && result.data) {
        setOrder(result.data)
      } else {
        setError(result.error || 'Failed to fetch order')
        setOrder(null)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      setOrder(null)
    } finally {
      setLoading(false)
    }
  }, [orderId])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch && orderId) {
      fetchOrder()
    }
  }, [fetchOrder, autoFetch, orderId])

  return {
    order,
    loading,
    error,
    refetch: fetchOrder,
    clearError
  }
}

export interface UseOrderMutationsReturn {
  createOrder: (input: CreateOrderInput) => Promise<Order | null>
  updateOrder: (input: UpdateOrderInput) => Promise<Order | null>
  cancelOrder: (orderId: string) => Promise<boolean>
  updateOrderStatus: (orderId: string, status: string) => Promise<boolean>
  loading: boolean
  error: string | null
  clearError: () => void
}

/**
 * Hook for order mutations (create, update, cancel)
 */
export function useOrderMutations(): UseOrderMutationsReturn {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createOrder = useCallback(async (input: CreateOrderInput): Promise<Order | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      const result = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || 'Failed to create order')
        return null
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const updateOrder = useCallback(async (input: UpdateOrderInput): Promise<Order | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/orders/${input.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      const result = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || 'Failed to update order')
        return null
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const cancelOrder = useCallback(async (orderId: string): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/orders/bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'cancel',
          orderIds: [orderId]
        })
      })

      const result = await response.json()

      if (result.success) {
        return true
      } else {
        setError(result.error || 'Failed to cancel order')
        return false
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  const updateOrderStatus = useCallback(async (orderId: string, status: string): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/orders/${orderId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      })

      const result = await response.json()

      if (result.success) {
        return true
      } else {
        setError(result.error || 'Failed to update order status')
        return false
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    createOrder,
    updateOrder,
    cancelOrder,
    updateOrderStatus,
    loading,
    error,
    clearError
  }
}
