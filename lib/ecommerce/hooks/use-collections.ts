// React hooks for collection management
'use client'

import { useState, useEffect, useCallback } from 'react'

export interface ProductCollection {
  id: string
  title: string
  slug: string
  description?: string
  image?: string
  sortOrder: 'manual' | 'best-selling' | 'created' | 'price-asc' | 'price-desc'
  isVisible: boolean
  seoTitle?: string
  seoDescription?: string
  productCount?: number
  createdAt: Date
  updatedAt: Date
}

export interface CreateCollectionInput {
  title: string
  slug?: string
  description?: string
  image?: string
  sortOrder?: 'manual' | 'best-selling' | 'created' | 'price-asc' | 'price-desc'
  isVisible?: boolean
  seoTitle?: string
  seoDescription?: string
  productIds?: string[]
}

export interface UpdateCollectionInput extends CreateCollectionInput {
  id: string
}

export interface UseCollectionsReturn {
  collections: ProductCollection[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing product collections
 */
export function useCollections(): UseCollectionsReturn {
  const [collections, setCollections] = useState<ProductCollection[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchCollections = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/collections')
      const result = await response.json()

      if (result.success && result.data) {
        setCollections(result.data)
      } else {
        setError(result.error || 'Failed to fetch collections')
        setCollections([])
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      setCollections([])
    } finally {
      setLoading(false)
    }
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    fetchCollections()
  }, [fetchCollections])

  return {
    collections,
    loading,
    error,
    refetch: fetchCollections,
    clearError
  }
}

export interface UseCollectionMutationsReturn {
  createCollection: (input: CreateCollectionInput) => Promise<ProductCollection | null>
  updateCollection: (input: UpdateCollectionInput) => Promise<ProductCollection | null>
  deleteCollection: (id: string) => Promise<boolean>
  addProductsToCollection: (collectionId: string, productIds: string[]) => Promise<boolean>
  removeProductsFromCollection: (collectionId: string, productIds: string[]) => Promise<boolean>
  loading: boolean
  error: string | null
  clearError: () => void
}

/**
 * Hook for collection mutations (create, update, delete)
 */
export function useCollectionMutations(): UseCollectionMutationsReturn {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createCollection = useCallback(async (input: CreateCollectionInput): Promise<ProductCollection | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/collections', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      const result = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || 'Failed to create collection')
        return null
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const updateCollection = useCallback(async (input: UpdateCollectionInput): Promise<ProductCollection | null> => {
    if (!input.id) {
      setError('Collection ID is required for update')
      return null
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/collections/${input.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      const result = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || 'Failed to update collection')
        return null
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const deleteCollection = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/collections/${id}`, {
        method: 'DELETE',
      })

      const result = await response.json()

      if (result.success) {
        return true
      } else {
        setError(result.error || 'Failed to delete collection')
        return false
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  const addProductsToCollection = useCallback(async (collectionId: string, productIds: string[]): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/collections/${collectionId}/products`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ productIds }),
      })

      const result = await response.json()

      if (result.success) {
        return true
      } else {
        setError(result.error || 'Failed to add products to collection')
        return false
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  const removeProductsFromCollection = useCallback(async (collectionId: string, productIds: string[]): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/collections/${collectionId}/products`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ productIds }),
      })

      const result = await response.json()

      if (result.success) {
        return true
      } else {
        setError(result.error || 'Failed to remove products from collection')
        return false
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    createCollection,
    updateCollection,
    deleteCollection,
    addProductsToCollection,
    removeProductsFromCollection,
    loading,
    error,
    clearError
  }
}
