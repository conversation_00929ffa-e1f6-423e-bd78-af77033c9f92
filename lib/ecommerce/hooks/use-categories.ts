// React hooks for category management
'use client'

import { useState, useEffect, useCallback } from 'react'

export interface ProductCategory {
  id: string
  name: string
  slug: string
  description?: string
  image?: string
  parentId?: string
  children?: ProductCategory[]
  position: number
  isVisible: boolean
  seoTitle?: string
  seoDescription?: string
  productCount?: number
  createdAt: Date
  updatedAt: Date
}

export interface CreateCategoryInput {
  name: string
  slug?: string
  description?: string
  image?: string
  parentId?: string
  position?: number
  isVisible?: boolean
  seoTitle?: string
  seoDescription?: string
}

export interface UpdateCategoryInput extends CreateCategoryInput {
  id: string
}

export interface UseCategoriesReturn {
  categories: ProductCategory[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing product categories
 */
export function useCategories(): UseCategoriesReturn {
  const [categories, setCategories] = useState<ProductCategory[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchCategories = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/categories')
      const result = await response.json()

      if (result.success && result.data) {
        setCategories(result.data)
      } else {
        setError(result.error || 'Failed to fetch categories')
        setCategories([])
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      setCategories([])
    } finally {
      setLoading(false)
    }
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  return {
    categories,
    loading,
    error,
    refetch: fetchCategories,
    clearError
  }
}

export interface UseCategoryMutationsReturn {
  createCategory: (input: CreateCategoryInput) => Promise<ProductCategory | null>
  updateCategory: (input: UpdateCategoryInput) => Promise<ProductCategory | null>
  deleteCategory: (id: string) => Promise<boolean>
  reorderCategories: (categoryIds: string[]) => Promise<boolean>
  loading: boolean
  error: string | null
  clearError: () => void
}

/**
 * Hook for category mutations (create, update, delete)
 */
export function useCategoryMutations(): UseCategoryMutationsReturn {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createCategory = useCallback(async (input: CreateCategoryInput): Promise<ProductCategory | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      const result = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || 'Failed to create category')
        return null
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const updateCategory = useCallback(async (input: UpdateCategoryInput): Promise<ProductCategory | null> => {
    if (!input.id) {
      setError('Category ID is required for update')
      return null
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/categories/${input.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      const result = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || 'Failed to update category')
        return null
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const deleteCategory = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/categories/${id}`, {
        method: 'DELETE',
      })

      const result = await response.json()

      if (result.success) {
        return true
      } else {
        setError(result.error || 'Failed to delete category')
        return false
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  const reorderCategories = useCallback(async (categoryIds: string[]): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/categories/reorder', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ categoryIds }),
      })

      const result = await response.json()

      if (result.success) {
        return true
      } else {
        setError(result.error || 'Failed to reorder categories')
        return false
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    createCategory,
    updateCategory,
    deleteCategory,
    reorderCategories,
    loading,
    error,
    clearError
  }
}
