// React hooks for analytics data management
'use client'

import { useState, useEffect, useCallback } from 'react'
import { AnalyticsData, AnalyticsTimeRange } from '../types'

export interface UseAnalyticsOptions {
  timeRange?: AnalyticsTimeRange
  autoFetch?: boolean
}

export interface UseAnalyticsReturn {
  analytics: AnalyticsData | null
  loading: boolean
  error: string | null
  refetch: (timeRange?: AnalyticsTimeRange) => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing analytics data
 */
export function useAnalytics(options: UseAnalyticsOptions = {}): UseAnalyticsReturn {
  const { timeRange = '30d', autoFetch = true } = options

  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentTimeRange, setCurrentTimeRange] = useState<AnalyticsTimeRange>(timeRange)

  const fetchAnalytics = useCallback(async (newTimeRange?: AnalyticsTimeRange) => {
    setLoading(true)
    setError(null)

    try {
      const range = newTimeRange || currentTimeRange
      setCurrentTimeRange(range)

      const response = await fetch(`/api/e-commerce/analytics?timeRange=${range}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch analytics')
      }

      if (data.success) {
        setAnalytics(data.data)
      } else {
        throw new Error(data.error || 'Failed to fetch analytics')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      setAnalytics(null)
    } finally {
      setLoading(false)
    }
  }, [currentTimeRange])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch) {
      fetchAnalytics(timeRange)
    }
  }, []) // Only run on mount

  return {
    analytics,
    loading,
    error,
    refetch: fetchAnalytics,
    clearError
  }
}

export interface UseSalesAnalyticsReturn {
  salesData: AnalyticsData['sales'] | null
  loading: boolean
  error: string | null
  refetch: (timeRange?: AnalyticsTimeRange) => Promise<void>
  clearError: () => void
}

/**
 * Hook for sales analytics specifically
 */
export function useSalesAnalytics(options: UseAnalyticsOptions = {}): UseSalesAnalyticsReturn {
  const { analytics, loading, error, refetch, clearError } = useAnalytics(options)

  return {
    salesData: analytics?.sales || null,
    loading,
    error,
    refetch,
    clearError
  }
}

export interface UseProductAnalyticsReturn {
  productData: AnalyticsData['products'] | null
  loading: boolean
  error: string | null
  refetch: (timeRange?: AnalyticsTimeRange) => Promise<void>
  clearError: () => void
}

/**
 * Hook for product analytics specifically
 */
export function useProductAnalytics(options: UseAnalyticsOptions = {}): UseProductAnalyticsReturn {
  const { analytics, loading, error, refetch, clearError } = useAnalytics(options)

  return {
    productData: analytics?.products || null,
    loading,
    error,
    refetch,
    clearError
  }
}

export interface UseCustomerAnalyticsReturn {
  customerData: AnalyticsData['customers'] | null
  loading: boolean
  error: string | null
  refetch: (timeRange?: AnalyticsTimeRange) => Promise<void>
  clearError: () => void
}

/**
 * Hook for customer analytics specifically
 */
export function useCustomerAnalytics(options: UseAnalyticsOptions = {}): UseCustomerAnalyticsReturn {
  const { analytics, loading, error, refetch, clearError } = useAnalytics(options)

  return {
    customerData: analytics?.customers || null,
    loading,
    error,
    refetch,
    clearError
  }
}

// Utility hook for analytics time range management
export interface UseAnalyticsTimeRangeReturn {
  timeRange: AnalyticsTimeRange
  setTimeRange: (range: AnalyticsTimeRange) => void
  timeRangeOptions: Array<{
    value: AnalyticsTimeRange
    label: string
  }>
}

export function useAnalyticsTimeRange(initialRange: AnalyticsTimeRange = '30d'): UseAnalyticsTimeRangeReturn {
  const [timeRange, setTimeRange] = useState<AnalyticsTimeRange>(initialRange)

  const timeRangeOptions = [
    { value: '7d' as AnalyticsTimeRange, label: 'Last 7 days' },
    { value: '30d' as AnalyticsTimeRange, label: 'Last 30 days' },
    { value: '90d' as AnalyticsTimeRange, label: 'Last 90 days' },
    { value: '1y' as AnalyticsTimeRange, label: 'Last year' }
  ]

  return {
    timeRange,
    setTimeRange,
    timeRangeOptions
  }
}
