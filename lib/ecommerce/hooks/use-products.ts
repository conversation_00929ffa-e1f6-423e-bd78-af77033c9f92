// React hooks for product management
'use client'

import { useState, useEffect, useCallback } from 'react'
import { 
  Product, 
  ProductSearchParams, 
  PaginatedResponse,
  CreateProductInput,
  UpdateProductInput,
  ApiResponse
} from '../types'
// Note: Using API routes instead of direct service calls to avoid Prisma in browser

export interface UseProductsOptions {
  initialParams?: ProductSearchParams
  autoFetch?: boolean
}

export interface UseProductsReturn {
  products: Product[]
  loading: boolean
  error: { code: string; message: string } | null
  pagination: PaginatedResponse<Product>['pagination'] | null
  searchProducts: (params?: ProductSearchParams) => Promise<void>
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing product search and listing
 */
export function useProducts(options: UseProductsOptions = {}): UseProductsReturn {
  const { initialParams = {}, autoFetch = true } = options

  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)
  const [pagination, setPagination] = useState<PaginatedResponse<Product>['pagination'] | null>(null)
  const [currentParams, setCurrentParams] = useState<ProductSearchParams>(initialParams)

  const searchProducts = useCallback(async (params?: ProductSearchParams) => {
    setLoading(true)
    setError(null)

    try {
      const searchParams = params || currentParams
      // Only update currentParams if params were provided to avoid infinite loops
      if (params) {
        setCurrentParams(searchParams)
      }

      // Build query string from search parameters
      const queryParams = new URLSearchParams()

      if (searchParams.query) queryParams.set('query', searchParams.query)
      if (searchParams.page) queryParams.set('page', searchParams.page.toString())
      if (searchParams.limit) queryParams.set('limit', searchParams.limit.toString())
      if (searchParams.sort?.field) queryParams.set('sortBy', searchParams.sort.field)
      if (searchParams.sort?.direction) queryParams.set('sortOrder', searchParams.sort.direction)

      // Add filters
      if (searchParams.filters?.categoryIds?.length) queryParams.set('categoryIds', searchParams.filters.categoryIds.join(','))
      if (searchParams.filters?.tagIds?.length) queryParams.set('tagIds', searchParams.filters.tagIds.join(','))
      if (searchParams.filters?.collectionIds?.length) queryParams.set('collectionIds', searchParams.filters.collectionIds.join(','))
      if (searchParams.filters?.vendor) queryParams.set('vendor', searchParams.filters.vendor)
      if (searchParams.filters?.productType) queryParams.set('productType', searchParams.filters.productType)
      if (searchParams.filters?.status?.length) queryParams.set('status', searchParams.filters.status.join(','))
      if (searchParams.filters?.inStock !== undefined) queryParams.set('inStock', searchParams.filters.inStock.toString())
      if (searchParams.filters?.onSale !== undefined) queryParams.set('onSale', searchParams.filters.onSale.toString())
      if (searchParams.filters?.isVisible !== undefined) queryParams.set('isVisible', searchParams.filters.isVisible.toString())
      if (searchParams.filters?.priceRange?.min) queryParams.set('minPrice', searchParams.filters.priceRange.min.toString())
      if (searchParams.filters?.priceRange?.max) queryParams.set('maxPrice', searchParams.filters.priceRange.max.toString())

      const response = await fetch(`/api/e-commerce/products?${queryParams.toString()}`)
      const result: ApiResponse<PaginatedResponse<Product>> = await response.json()

      if (result.success && result.data) {
        setProducts(result.data.data || [])
        setPagination(result.data.pagination || null)
      } else {
        setError(result.error || { code: 'FETCH_ERROR', message: 'Failed to fetch products' })
        setProducts([])
        setPagination(null)
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      setProducts([])
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, []) // Remove currentParams dependency to prevent infinite loops

  const refetch = useCallback(() => {
    return searchProducts(currentParams)
  }, [searchProducts, currentParams])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch) {
      searchProducts(initialParams)
    }
  }, [autoFetch]) // Add autoFetch dependency but keep initialParams stable

  return {
    products,
    loading,
    error,
    pagination,
    searchProducts,
    refetch,
    clearError
  }
}

export interface UseProductOptions {
  productId?: string
  slug?: string
  autoFetch?: boolean
}

export interface UseProductReturn {
  product: Product | null
  loading: boolean
  error: { code: string; message: string } | null
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing a single product
 */
export function useProduct(options: UseProductOptions = {}): UseProductReturn {
  const { productId, slug, autoFetch = true } = options

  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)

  const fetchProduct = useCallback(async () => {
    if (!productId && !slug) {
      setProduct(null)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const endpoint = productId
        ? `/api/e-commerce/products/${productId}`
        : `/api/e-commerce/products/slug/${slug}`

      const response = await fetch(endpoint)
      const result: ApiResponse<Product> = await response.json()

      if (result.success && result.data) {
        setProduct(result.data)
      } else {
        setError(result.error || { code: 'FETCH_ERROR', message: 'Failed to fetch product' })
        setProduct(null)
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      setProduct(null)
    } finally {
      setLoading(false)
    }
  }, [productId, slug])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch && (productId || slug)) {
      fetchProduct()
    }
  }, [autoFetch, productId, slug]) // Remove fetchProduct dependency to prevent infinite loops

  return {
    product,
    loading,
    error,
    refetch: fetchProduct,
    clearError
  }
}

export interface UseProductMutationsReturn {
  createProduct: (input: CreateProductInput) => Promise<Product | null>
  updateProduct: (input: UpdateProductInput) => Promise<Product | null>
  deleteProduct: (id: string) => Promise<boolean>
  updateInventory: (productId: string, quantity: number, variantId?: string) => Promise<boolean>
  bulkUpdateStatus: (productIds: string[], status: string) => Promise<boolean>
  bulkDelete: (productIds: string[]) => Promise<boolean>
  duplicateProduct: (productId: string) => Promise<Product | null>
  loading: boolean
  error: { code: string; message: string } | null
  clearError: () => void
}

/**
 * Hook for product mutations (create, update, delete)
 */
export function useProductMutations(): UseProductMutationsReturn {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)

  const createProduct = useCallback(async (input: CreateProductInput): Promise<Product | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      const result: ApiResponse<Product> = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || { code: 'CREATE_PRODUCT_ERROR', message: 'Failed to create product' })
        return null
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const updateProduct = useCallback(async (input: UpdateProductInput): Promise<Product | null> => {
    if (!input.id) {
      setError({ code: 'VALIDATION_ERROR', message: 'Product ID is required for update' })
      return null
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${input.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      const result: ApiResponse<Product> = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || { code: 'UPDATE_PRODUCT_ERROR', message: 'Failed to update product' })
        return null
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const deleteProduct = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${id}`, {
        method: 'DELETE',
      })

      const result: ApiResponse<void> = await response.json()

      if (result.success) {
        return true
      } else {
        setError(result.error || { code: 'DELETE_PRODUCT_ERROR', message: 'Failed to delete product' })
        return false
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  const updateInventory = useCallback(async (
    productId: string,
    quantity: number,
    variantId?: string
  ): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${productId}/inventory`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          quantity,
          variantId,
        }),
      })

      const result = await response.json()

      if (result.success) {
        return true
      } else {
        setError(result.error || { code: 'UPDATE_INVENTORY_ERROR', message: 'Failed to update inventory' })
        return false
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  const bulkUpdateStatus = useCallback(async (productIds: string[], status: string): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/products/bulk/status', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productIds,
          status,
        }),
      })

      const result = await response.json()

      if (result.success) {
        return true
      } else {
        setError(result.error || { code: 'BULK_UPDATE_STATUS_ERROR', message: 'Failed to update product status' })
        return false
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  const bulkDelete = useCallback(async (productIds: string[]): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/products/bulk/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productIds,
        }),
      })

      const result = await response.json()

      if (result.success) {
        return true
      } else {
        setError(result.error || { code: 'BULK_DELETE_ERROR', message: 'Failed to delete products' })
        return false
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  const duplicateProduct = useCallback(async (productId: string): Promise<Product | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${productId}/duplicate`, {
        method: 'POST',
      })

      const result = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || { code: 'DUPLICATE_PRODUCT_ERROR', message: 'Failed to duplicate product' })
        return null
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    createProduct,
    updateProduct,
    deleteProduct,
    updateInventory,
    bulkUpdateStatus,
    bulkDelete,
    duplicateProduct,
    loading,
    error,
    clearError
  }
}

export interface UseRelatedProductsOptions {
  productId?: string
  limit?: number
  autoFetch?: boolean
}

export interface UseRelatedProductsReturn {
  relatedProducts: Product[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for fetching related products
 */
export function useRelatedProducts(options: UseRelatedProductsOptions = {}): UseRelatedProductsReturn {
  const { productId, limit = 4, autoFetch = true } = options

  const [relatedProducts, setRelatedProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchRelatedProducts = useCallback(async () => {
    if (!productId) {
      setRelatedProducts([])
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${productId}/related?limit=${limit}`)
      const result = await response.json()

      if (result.success && result.data) {
        setRelatedProducts(result.data)
      } else {
        setError(result.error || 'Failed to fetch related products')
        setRelatedProducts([])
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      setRelatedProducts([])
    } finally {
      setLoading(false)
    }
  }, [productId, limit])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch && productId) {
      fetchRelatedProducts()
    }
  }, [autoFetch, productId, limit]) // Remove fetchRelatedProducts dependency and add limit to prevent infinite loops

  return {
    relatedProducts,
    loading,
    error,
    refetch: fetchRelatedProducts,
    clearError
  }
}

// Utility hook for product filtering
export interface UseProductFiltersReturn {
  filters: ProductSearchParams['filters']
  updateFilter: (key: string, value: any) => void
  clearFilter: (key: string) => void
  clearAllFilters: () => void
  hasActiveFilters: boolean
}

export function useProductFilters(initialFilters = {}): UseProductFiltersReturn {
  const [filters, setFilters] = useState(initialFilters)

  const updateFilter = useCallback((key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }, [])

  const clearFilter = useCallback((key: string) => {
    setFilters(prev => {
      const newFilters = { ...prev } as any
      delete newFilters[key]
      return newFilters
    })
  }, [])

  const clearAllFilters = useCallback(() => {
    setFilters({})
  }, [])

  const hasActiveFilters = Object.keys(filters).length > 0

  return {
    filters,
    updateFilter,
    clearFilter,
    clearAllFilters,
    hasActiveFilters
  }
}
