import { useState, useEffect, createContext, useContext } from 'react'

interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  emailVerified: boolean
  createdAt: string
}

interface AuthContextType {
  user: User | null
  loading: boolean
  error: string | null
  login: (email: string, password: string) => Promise<boolean>
  register: (userData: RegisterData) => Promise<boolean>
  logout: () => Promise<void>
  updateProfile: (data: Partial<User>) => Promise<boolean>
  sendPasswordReset: (email: string) => Promise<boolean>
  verifyEmail: (token: string) => Promise<boolean>
  refetch: () => void
}

interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  phone?: string
}

const AuthContext = createContext<AuthContextType | null>(null)

export function useAuth(): AuthContextType {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchUser = async () => {
    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        setLoading(false)
        return
      }

      const response = await fetch('/api/e-commerce/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const data = await response.json()

      if (data.success) {
        setUser(data.data)
      } else {
        localStorage.removeItem('auth_token')
        setUser(null)
      }
    } catch (err) {
      console.error('Auth check error:', err)
      localStorage.removeItem('auth_token')
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setError(null)
      const response = await fetch('/api/e-commerce/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      const data = await response.json()

      if (data.success) {
        localStorage.setItem('auth_token', data.token)
        setUser(data.user)
        return true
      } else {
        setError(data.error || 'Login failed')
        return false
      }
    } catch (err) {
      setError('Network error occurred')
      console.error('Login error:', err)
      return false
    }
  }

  const register = async (userData: RegisterData): Promise<boolean> => {
    try {
      setError(null)
      const response = await fetch('/api/e-commerce/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      })

      const data = await response.json()

      if (data.success) {
        if (data.token) {
          localStorage.setItem('auth_token', data.token)
          setUser(data.user)
        }
        return true
      } else {
        setError(data.error || 'Registration failed')
        return false
      }
    } catch (err) {
      setError('Network error occurred')
      console.error('Registration error:', err)
      return false
    }
  }

  const logout = async (): Promise<void> => {
    try {
      const token = localStorage.getItem('auth_token')
      if (token) {
        await fetch('/api/e-commerce/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        })
      }
    } catch (err) {
      console.error('Logout error:', err)
    } finally {
      localStorage.removeItem('auth_token')
      setUser(null)
    }
  }

  const updateProfile = async (updateData: Partial<User>): Promise<boolean> => {
    try {
      setError(null)
      const token = localStorage.getItem('auth_token')
      if (!token) return false

      const response = await fetch('/api/e-commerce/auth/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(updateData),
      })

      const data = await response.json()

      if (data.success) {
        setUser(data.data)
        return true
      } else {
        setError(data.error || 'Profile update failed')
        return false
      }
    } catch (err) {
      setError('Network error occurred')
      console.error('Profile update error:', err)
      return false
    }
  }

  const sendPasswordReset = async (email: string): Promise<boolean> => {
    try {
      setError(null)
      const response = await fetch('/api/e-commerce/auth/password-reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (data.success) {
        return true
      } else {
        setError(data.error || 'Password reset failed')
        return false
      }
    } catch (err) {
      setError('Network error occurred')
      console.error('Password reset error:', err)
      return false
    }
  }

  const verifyEmail = async (token: string): Promise<boolean> => {
    try {
      setError(null)
      const response = await fetch('/api/e-commerce/auth/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      })

      const data = await response.json()

      if (data.success) {
        if (user) {
          setUser({ ...user, emailVerified: true })
        }
        return true
      } else {
        setError(data.error || 'Email verification failed')
        return false
      }
    } catch (err) {
      setError('Network error occurred')
      console.error('Email verification error:', err)
      return false
    }
  }

  const refetch = () => {
    fetchUser()
  }

  useEffect(() => {
    fetchUser()
  }, [])

  return {
    user,
    loading,
    error,
    login,
    register,
    logout,
    updateProfile,
    sendPasswordReset,
    verifyEmail,
    refetch,
  }
}
