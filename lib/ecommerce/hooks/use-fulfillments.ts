'use client'

import { useState, useEffect, useCallback } from 'react'

export interface Fulfillment {
  id: string
  orderId: string
  status: string
  carrier: string
  service: string
  trackingNumber?: string
  trackingUrl?: string
  locationName: string
  notes?: string
  createdAt: string
  shippedAt?: string
  deliveredAt?: string
  estimatedDeliveryAt?: string
  order: {
    orderNumber: string
    customerEmail: string
    customerFirstName?: string
    customerLastName?: string
    total: number
    currency: string
  }
  items: Array<{
    id: string
    quantity: number
    orderItem: {
      productTitle: string
      variantTitle?: string
      unitPrice: number
      product: {
        name: string
        sku: string
        images: string[]
      }
    }
  }>
}

export interface FulfillmentRequest {
  orderId: string
  items: Array<{
    orderItemId: string
    quantity: number
    warehouseLocation?: string
  }>
  shippingMethod: {
    carrier: string
    service: string
    trackingNumber?: string
  }
  fulfillmentCenter?: string
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  notes?: string
  notifyCustomer?: boolean
}

export interface FulfillmentMetrics {
  totalFulfillments: number
  totalItemsFulfilled: number
  averageFulfillmentTimeHours: number
  onTimeDeliveryRate: string
  fulfillmentRate: string
  statusBreakdown: Record<string, number>
  carrierBreakdown: Record<string, number>
}

export function useFulfillments() {
  const [fulfillments, setFulfillments] = useState<Fulfillment[]>([])
  const [metrics, setMetrics] = useState<FulfillmentMetrics | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchFulfillments = useCallback(async (filters?: {
    orderId?: string
    status?: string
    page?: number
    limit?: number
  }) => {
    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()
      if (filters?.orderId) params.append('orderId', filters.orderId)
      if (filters?.status) params.append('status', filters.status)
      if (filters?.page) params.append('page', filters.page.toString())
      if (filters?.limit) params.append('limit', filters.limit.toString())

      const response = await fetch(`/api/e-commerce/fulfillments?${params}`, {
        headers: { 'x-admin-request': 'true' }
      })

      const data = await response.json()

      if (data.success) {
        setFulfillments(data.data)
        return { success: true, data: data.data, pagination: data.pagination }
      } else {
        setError(data.error || 'Failed to fetch fulfillments')
        return { success: false, error: data.error }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch fulfillments'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [])

  const fetchMetrics = useCallback(async (period: string = '30d') => {
    try {
      const response = await fetch(`/api/e-commerce/fulfillments/metrics?period=${period}`, {
        headers: { 'x-admin-request': 'true' }
      })

      const data = await response.json()

      if (data.success) {
        setMetrics(data.data.overview)
        return { success: true, data: data.data }
      } else {
        return { success: false, error: data.error }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch metrics'
      return { success: false, error: errorMessage }
    }
  }, [])

  const createFulfillment = useCallback(async (request: FulfillmentRequest) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/fulfillments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-request': 'true'
        },
        body: JSON.stringify(request)
      })

      const data = await response.json()

      if (data.success) {
        // Refresh fulfillments list
        await fetchFulfillments()
        return { success: true, data: data.data }
      } else {
        setError(data.error || 'Failed to create fulfillment')
        return { success: false, error: data.error, warnings: data.warnings }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create fulfillment'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [fetchFulfillments])

  const updateFulfillmentStatus = useCallback(async (fulfillmentId: string, status: string, notes?: string) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/fulfillments/${fulfillmentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-request': 'true'
        },
        body: JSON.stringify({ status, notes })
      })

      const data = await response.json()

      if (data.success) {
        // Update local state
        setFulfillments(prev => prev.map(f => 
          f.id === fulfillmentId ? { ...f, status, notes: notes || f.notes } : f
        ))
        return { success: true, data: data.data }
      } else {
        setError(data.error || 'Failed to update fulfillment')
        return { success: false, error: data.error }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update fulfillment'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [])

  const cancelFulfillment = useCallback(async (fulfillmentId: string, reason: string) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/fulfillments/${fulfillmentId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-request': 'true'
        },
        body: JSON.stringify({ reason })
      })

      const data = await response.json()

      if (data.success) {
        // Update local state
        setFulfillments(prev => prev.map(f => 
          f.id === fulfillmentId ? { ...f, status: 'cancelled' } : f
        ))
        return { success: true, message: data.message }
      } else {
        setError(data.error || 'Failed to cancel fulfillment')
        return { success: false, error: data.error }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to cancel fulfillment'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [])

  const trackFulfillment = useCallback(async (fulfillmentId: string) => {
    try {
      const response = await fetch(`/api/e-commerce/fulfillments/${fulfillmentId}/track`)
      const data = await response.json()

      if (data.success) {
        // Update local state with tracking info
        setFulfillments(prev => prev.map(f => 
          f.id === fulfillmentId ? { 
            ...f, 
            status: data.data.status,
            // Add any other tracking updates
          } : f
        ))
        return { success: true, data: data.data }
      } else {
        return { success: false, error: data.error }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to track fulfillment'
      return { success: false, error: errorMessage }
    }
  }, [])

  const bulkUpdateStatus = useCallback(async (fulfillmentIds: string[], status: string, notes?: string) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/fulfillments/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-request': 'true'
        },
        body: JSON.stringify({
          action: 'updateStatus',
          fulfillmentIds,
          data: { status, notes }
        })
      })

      const data = await response.json()

      if (data.success) {
        // Refresh fulfillments list
        await fetchFulfillments()
        return { success: true, data: data.data }
      } else {
        setError(data.error || 'Failed to bulk update fulfillments')
        return { success: false, error: data.error }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to bulk update fulfillments'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [fetchFulfillments])

  const getFulfillment = useCallback(async (fulfillmentId: string) => {
    try {
      const response = await fetch(`/api/e-commerce/fulfillments/${fulfillmentId}`, {
        headers: { 'x-admin-request': 'true' }
      })

      const data = await response.json()

      if (data.success) {
        return { success: true, data: data.data }
      } else {
        return { success: false, error: data.error }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch fulfillment'
      return { success: false, error: errorMessage }
    }
  }, [])

  return {
    fulfillments,
    metrics,
    loading,
    error,
    fetchFulfillments,
    fetchMetrics,
    createFulfillment,
    updateFulfillmentStatus,
    cancelFulfillment,
    trackFulfillment,
    bulkUpdateStatus,
    getFulfillment
  }
}
