// React hooks for dashboard data management
'use client'

import { useState, useEffect, useCallback } from 'react'
import type { Product, Order } from '../types'

export interface DashboardStats {
  totalProducts: number
  totalOrders: number
  totalCustomers: number
  totalRevenue: number
  lowStockProducts: number
  pendingOrders: number
}

export interface UseDashboardReturn {
  stats: DashboardStats
  recentOrders: Order[]
  lowStockProducts: Product[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing dashboard data
 */
export function useDashboard(): UseDashboardReturn {
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalOrders: 0,
    totalCustomers: 0,
    totalRevenue: 0,
    lowStockProducts: 0,
    pendingOrders: 0
  })
  const [recentOrders, setRecentOrders] = useState<Order[]>([])
  const [lowStockProducts, setLowStockProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchDashboardData = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      // Fetch products using API routes
      const productsResponse = await fetch('/api/e-commerce/products?page=1&limit=100&status=active')
      const productsData = await productsResponse.json()

      // Fetch recent orders using API routes
      const ordersResponse = await fetch('/api/e-commerce/orders?page=1&limit=10&sortBy=createdAt&sortOrder=desc')
      const ordersData = await ordersResponse.json()

      // Fetch customers using API routes
      const customersResponse = await fetch('/api/e-commerce/customers?page=1&limit=100&isActive=true')
      const customersData = await customersResponse.json()

      if (!productsResponse.ok || !ordersResponse.ok || !customersResponse.ok) {
        throw new Error('Failed to fetch dashboard data')
      }

      const products = productsData.success ? productsData.data : []
      const orders = ordersData.success ? ordersData.data : []
      const customers = customersData.success ? customersData.data : []

      // Calculate stats
      const totalRevenue = orders.reduce((sum: number, order: Order) => {
        return sum + (order.total?.amount || 0)
      }, 0)
      const pendingOrders = orders.filter((order: Order) => order.status === 'pending').length
      const lowStock = products.filter((product: Product) =>
        product.trackQuantity &&
        product.inventoryQuantity !== null &&
        product.inventoryQuantity <= 5
      )

      setStats({
        totalProducts: products.length,
        totalOrders: orders.length,
        totalCustomers: customers.length,
        totalRevenue,
        lowStockProducts: lowStock.length,
        pendingOrders
      })

      setRecentOrders(orders.slice(0, 5))
      setLowStockProducts(lowStock.slice(0, 5))

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    fetchDashboardData()
  }, [fetchDashboardData])

  return {
    stats,
    recentOrders,
    lowStockProducts,
    loading,
    error,
    refetch: fetchDashboardData,
    clearError
  }
}

export interface UseOrdersOptions {
  page?: number
  limit?: number
  status?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  autoFetch?: boolean
}

export interface UseOrdersReturn {
  orders: Order[]
  loading: boolean
  error: string | null
  pagination: any
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing orders data
 */
export function useOrders(options: UseOrdersOptions = {}): UseOrdersReturn {
  const { 
    page = 1, 
    limit = 20, 
    status, 
    sortBy = 'createdAt', 
    sortOrder = 'desc',
    autoFetch = true 
  } = options

  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState(null)

  const fetchOrders = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        sortBy,
        sortOrder
      })

      if (status) {
        params.append('status', status)
      }

      const response = await fetch(`/api/e-commerce/orders?${params}`, {
        headers: { 'x-admin-request': 'true' }
      })
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch orders')
      }

      if (data.success) {
        setOrders(data.data || [])
        setPagination(data.pagination || null)
      } else {
        throw new Error(data.error || 'Failed to fetch orders')
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      setOrders([])
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, [page, limit, status, sortBy, sortOrder])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch) {
      fetchOrders()
    }
  }, [fetchOrders, autoFetch])

  return {
    orders,
    loading,
    error,
    pagination,
    refetch: fetchOrders,
    clearError
  }
}

export interface UseOrderOptions {
  orderId?: string
  autoFetch?: boolean
}

export interface UseOrderReturn {
  order: Order | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing a single order
 */
export function useOrder(orderId?: string, options: { autoFetch?: boolean } = {}): UseOrderReturn {
  const { autoFetch = true } = options

  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchOrder = useCallback(async () => {
    if (!orderId) {
      setOrder(null)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/orders/${orderId}`, {
        headers: { 'x-admin-request': 'true' }
      })
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch order')
      }

      if (data.success) {
        setOrder(data.data)
      } else {
        throw new Error(data.error || 'Failed to fetch order')
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      setOrder(null)
    } finally {
      setLoading(false)
    }
  }, [orderId])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch && orderId) {
      fetchOrder()
    }
  }, [fetchOrder, autoFetch, orderId])

  return {
    order,
    loading,
    error,
    refetch: fetchOrder,
    clearError
  }
}
