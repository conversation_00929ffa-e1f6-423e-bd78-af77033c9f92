// React hooks for customer management
'use client'

import { useState, useEffect, useCallback } from 'react'
import { 
  User, 
  CustomerSearchParams, 
  PaginatedResponse,
  CreateUserInput,
  UpdateUserInput 
} from '../types'

export interface UseCustomersOptions {
  initialParams?: CustomerSearchParams
  autoFetch?: boolean
}

export interface UseCustomersReturn {
  customers: User[]
  loading: boolean
  error: string | null
  pagination: PaginatedResponse<User>['pagination'] | null
  searchCustomers: (params?: CustomerSearchParams) => Promise<void>
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing customer search and listing
 */
export function useCustomers(options: UseCustomersOptions = {}): UseCustomersReturn {
  const { initialParams = {}, autoFetch = true } = options

  const [customers, setCustomers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<PaginatedResponse<User>['pagination'] | null>(null)
  const [currentParams, setCurrentParams] = useState<CustomerSearchParams>(initialParams)

  const searchCustomers = useCallback(async (params?: CustomerSearchParams) => {
    setLoading(true)
    setError(null)

    try {
      const searchParams = params || currentParams
      setCurrentParams(searchParams)

      // Build query string
      const queryParams = new URLSearchParams()
      
      if (searchParams.query) {
        queryParams.append('query', searchParams.query)
      }
      
      if (searchParams.page) {
        queryParams.append('page', searchParams.page.toString())
      }
      
      if (searchParams.limit) {
        queryParams.append('limit', searchParams.limit.toString())
      }
      
      if (searchParams.sort?.field) {
        queryParams.append('sortBy', searchParams.sort.field)
      }
      
      if (searchParams.sort?.direction) {
        queryParams.append('sortOrder', searchParams.sort.direction)
      }

      // Add filters
      if (searchParams.filters) {
        const filters = searchParams.filters
        
        if (filters.isActive !== undefined) {
          queryParams.append('isActive', filters.isActive.toString())
        }
        
        if (filters.isBlocked !== undefined) {
          queryParams.append('isBlocked', filters.isBlocked.toString())
        }
        
        if (filters.emailVerified !== undefined) {
          queryParams.append('emailVerified', filters.emailVerified.toString())
        }
        
        if (filters.loyaltyTier && filters.loyaltyTier.length > 0) {
          queryParams.append('loyaltyTier', filters.loyaltyTier.join(','))
        }
        
        if (filters.totalSpentMin !== undefined) {
          queryParams.append('totalSpentMin', filters.totalSpentMin.toString())
        }
        
        if (filters.totalSpentMax !== undefined) {
          queryParams.append('totalSpentMax', filters.totalSpentMax.toString())
        }
        
        if (filters.orderCountMin !== undefined) {
          queryParams.append('orderCountMin', filters.orderCountMin.toString())
        }
        
        if (filters.orderCountMax !== undefined) {
          queryParams.append('orderCountMax', filters.orderCountMax.toString())
        }
        
        if (filters.createdAfter) {
          queryParams.append('createdAfter', filters.createdAfter.toISOString())
        }
        
        if (filters.createdBefore) {
          queryParams.append('createdBefore', filters.createdBefore.toISOString())
        }
        
        if (filters.lastOrderAfter) {
          queryParams.append('lastOrderAfter', filters.lastOrderAfter.toISOString())
        }
        
        if (filters.lastOrderBefore) {
          queryParams.append('lastOrderBefore', filters.lastOrderBefore.toISOString())
        }
        
        if (filters.tags && filters.tags.length > 0) {
          queryParams.append('tags', filters.tags.join(','))
        }
      }

      const response = await fetch(`/api/e-commerce/customers?${queryParams}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch customers')
      }

      if (data.success) {
        setCustomers(data.data || [])
        setPagination(data.pagination || null)
      } else {
        throw new Error(data.error || 'Failed to fetch customers')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      setCustomers([])
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, [currentParams])

  const refetch = useCallback(() => {
    return searchCustomers(currentParams)
  }, [searchCustomers, currentParams])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch) {
      searchCustomers(initialParams)
    }
  }, []) // Only run on mount

  return {
    customers,
    loading,
    error,
    pagination,
    searchCustomers,
    refetch,
    clearError
  }
}

export interface UseCustomerOptions {
  customerId?: string
  autoFetch?: boolean
}

export interface UseCustomerReturn {
  customer: User | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing a single customer
 */
export function useCustomer(options: UseCustomerOptions = {}): UseCustomerReturn {
  const { customerId, autoFetch = true } = options

  const [customer, setCustomer] = useState<User | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchCustomer = useCallback(async () => {
    if (!customerId) {
      setCustomer(null)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/customers/${customerId}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch customer')
      }

      if (data.success) {
        setCustomer(data.data)
      } else {
        throw new Error(data.error || 'Failed to fetch customer')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      setCustomer(null)
    } finally {
      setLoading(false)
    }
  }, [customerId])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch && customerId) {
      fetchCustomer()
    }
  }, [fetchCustomer, autoFetch, customerId])

  return {
    customer,
    loading,
    error,
    refetch: fetchCustomer,
    clearError
  }
}

export interface UseCustomerMutationsReturn {
  createCustomer: (input: CreateUserInput) => Promise<User | null>
  updateCustomer: (input: UpdateUserInput) => Promise<User | null>
  deleteCustomer: (id: string) => Promise<boolean>
  loading: boolean
  error: string | null
  clearError: () => void
}

/**
 * Hook for customer mutations (create, update, delete)
 */
export function useCustomerMutations(): UseCustomerMutationsReturn {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createCustomer = useCallback(async (input: CreateUserInput): Promise<User | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(input)
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create customer')
      }

      if (data.success) {
        return data.data
      } else {
        throw new Error(data.error || 'Failed to create customer')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const updateCustomer = useCallback(async (input: UpdateUserInput): Promise<User | null> => {
    setLoading(true)
    setError(null)

    try {
      const { id, ...updateData } = input
      
      const response = await fetch(`/api/e-commerce/customers/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update customer')
      }

      if (data.success) {
        return data.data
      } else {
        throw new Error(data.error || 'Failed to update customer')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const deleteCustomer = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/customers/${id}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete customer')
      }

      if (data.success) {
        return true
      } else {
        throw new Error(data.error || 'Failed to delete customer')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    createCustomer,
    updateCustomer,
    deleteCustomer,
    loading,
    error,
    clearError
  }
}
