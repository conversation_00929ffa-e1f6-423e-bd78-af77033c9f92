'use client'

import { useState, useEffect, useCallback } from 'react'
import type { DashboardMetrics } from '../services/dashboard-analytics-service'

// Transform the stats API response to match the expected DashboardMetrics format
function transformStatsToMetrics(stats: any): DashboardMetrics {
  return {
    // Overview Stats
    totalRevenue: stats?.totalRevenue || 0,
    totalOrders: stats?.totalOrders || 0,
    totalProducts: stats?.totalProducts || 0,
    totalCustomers: stats?.totalCustomers || 0,
    
    // Growth Metrics
    revenueGrowth: stats?.revenueGrowth || 0,
    orderGrowth: stats?.orderGrowth || 0,
    customerGrowth: stats?.customerGrowth || 0,
    productGrowth: stats?.productGrowth || 0,
    
    // Performance Metrics
    averageOrderValue: stats?.averageOrderValue || 0,
    conversionRate: stats?.conversionRate || 0,
    customerLifetimeValue: stats?.customerLifetimeValue || 0,
    inventoryTurnover: stats?.inventoryTurnover || 0,
    
    // Time-based Data
    revenueByDay: stats?.revenueByDay || [],
    ordersByStatus: stats?.ordersByStatus || [],
    topProducts: stats?.topProducts || [],
    topCategories: stats?.topCategories || [],
    customerSegments: stats?.customerSegments || [],
    
    // Inventory Alerts
    lowStockProducts: stats?.lowStockProducts || [],
    outOfStockProducts: stats?.outOfStockProducts || [],
    
    // Recent Activity
    recentOrders: stats?.recentOrders || [],
    recentCustomers: stats?.recentCustomers || []
  }
}

interface UseDashboardAnalyticsOptions {
  days?: number
  autoRefresh?: boolean
  refreshInterval?: number
}

interface UseDashboardAnalyticsReturn {
  metrics: DashboardMetrics | null
  loading: boolean
  error: string | null
  lastUpdated: Date | null
  refetch: () => Promise<void>
  refresh: () => Promise<void>
}

export function useDashboardAnalytics(options: UseDashboardAnalyticsOptions = {}): UseDashboardAnalyticsReturn {
  const {
    days = 30,
    autoRefresh = false,
    refreshInterval = 300000 // 5 minutes
  } = options

  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchMetrics = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/admin/dashboard/analytics?days=${days}`, {
        headers: { 'x-admin-request': 'true' }
      })
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch dashboard analytics')
      }

      // Transform the stats API response to match the expected format
      const transformedMetrics = transformStatsToMetrics(data.data)
      setMetrics(transformedMetrics)
      setLastUpdated(new Date())
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch dashboard analytics'
      setError(errorMessage)
      console.error('Dashboard analytics error:', err)
    } finally {
      setLoading(false)
    }
  }, [days])

  const refresh = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/admin/dashboard/analytics?days=${days}`, {
        headers: { 'x-admin-request': 'true' }
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to refresh dashboard analytics')
      }

      // Transform the stats API response to match the expected format
      const transformedMetrics = transformStatsToMetrics(data.data)
      setMetrics(transformedMetrics)
      setLastUpdated(new Date())
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh dashboard analytics'
      setError(errorMessage)
      console.error('Dashboard analytics refresh error:', err)
    } finally {
      setLoading(false)
    }
  }, [days])

  // Initial fetch
  useEffect(() => {
    fetchMetrics()
  }, [fetchMetrics])

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(fetchMetrics, refreshInterval)
    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, fetchMetrics])

  return {
    metrics,
    loading,
    error,
    lastUpdated,
    refetch: fetchMetrics,
    refresh
  }
}

/**
 * Hook for specific metric types
 */
export function useRevenueMetrics(days: number = 30) {
  const { metrics, loading, error } = useDashboardAnalytics({ days })

  return {
    totalRevenue: metrics?.totalRevenue || 0,
    revenueGrowth: metrics?.revenueGrowth || 0,
    averageOrderValue: metrics?.averageOrderValue || 0,
    revenueByDay: metrics?.revenueByDay || [],
    loading,
    error
  }
}

export function useOrderMetrics(days: number = 30) {
  const { metrics, loading, error } = useDashboardAnalytics({ days })

  return {
    totalOrders: metrics?.totalOrders || 0,
    orderGrowth: metrics?.orderGrowth || 0,
    ordersByStatus: metrics?.ordersByStatus || [],
    recentOrders: metrics?.recentOrders || [],
    loading,
    error
  }
}

export function useProductMetrics(days: number = 30) {
  const { metrics, loading, error } = useDashboardAnalytics({ days })

  return {
    totalProducts: metrics?.totalProducts || 0,
    topProducts: metrics?.topProducts || [],
    topCategories: metrics?.topCategories || [],
    lowStockProducts: metrics?.lowStockProducts || [],
    outOfStockProducts: metrics?.outOfStockProducts || [],
    loading,
    error
  }
}

export function useCustomerMetrics(days: number = 30) {
  const { metrics, loading, error } = useDashboardAnalytics({ days })

  return {
    totalCustomers: metrics?.totalCustomers || 0,
    customerGrowth: metrics?.customerGrowth || 0,
    customerLifetimeValue: metrics?.customerLifetimeValue || 0,
    customerSegments: metrics?.customerSegments || [],
    recentCustomers: metrics?.recentCustomers || [],
    loading,
    error
  }
}

/**
 * Hook for dashboard overview stats
 */
export function useDashboardOverview(days: number = 30) {
  const { metrics, loading, error, lastUpdated, refetch } = useDashboardAnalytics({ 
    days,
    autoRefresh: true,
    refreshInterval: 300000 // 5 minutes
  })

  const overviewStats = [
    {
      title: 'Total Revenue',
      value: metrics?.totalRevenue || 0,
      growth: metrics?.revenueGrowth || 0,
      format: 'currency'
    },
    {
      title: 'Total Orders',
      value: metrics?.totalOrders || 0,
      growth: metrics?.orderGrowth || 0,
      format: 'number'
    },
    {
      title: 'Total Products',
      value: metrics?.totalProducts || 0,
      growth: metrics?.productGrowth || 0,
      format: 'number'
    },
    {
      title: 'Total Customers',
      value: metrics?.totalCustomers || 0,
      growth: metrics?.customerGrowth || 0,
      format: 'number'
    }
  ]

  return {
    overviewStats,
    metrics,
    loading,
    error,
    lastUpdated,
    refetch
  }
}
