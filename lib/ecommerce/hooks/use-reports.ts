// React hooks for reports data management
'use client'

import { useState, useEffect, useCallback } from 'react'
import { ReportData, AnalyticsTimeRange } from '../types'

export interface UseReportsOptions {
  timeRange?: AnalyticsTimeRange
  autoFetch?: boolean
}

export interface UseReportsReturn {
  reports: ReportData | null
  loading: boolean
  error: string | null
  refetch: (timeRange?: AnalyticsTimeRange) => Promise<void>
  clearError: () => void
  exportReport: (format: 'pdf' | 'excel' | 'csv', reportType?: string) => void
}

/**
 * Hook for managing reports data
 */
export function useReports(options: UseReportsOptions = {}): UseReportsReturn {
  const { timeRange = '30d', autoFetch = true } = options

  const [reports, setReports] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentTimeRange, setCurrentTimeRange] = useState<AnalyticsTimeRange>(timeRange)

  const fetchReports = useCallback(async (newTimeRange?: AnalyticsTimeRange) => {
    setLoading(true)
    setError(null)

    try {
      const range = newTimeRange || currentTimeRange
      setCurrentTimeRange(range)

      const response = await fetch(`/api/e-commerce/reports?timeRange=${range}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch reports')
      }

      if (data.success) {
        setReports(data.data)
      } else {
        throw new Error(data.error || 'Failed to fetch reports')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      setReports(null)
    } finally {
      setLoading(false)
    }
  }, [currentTimeRange])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const exportReport = useCallback((format: 'pdf' | 'excel' | 'csv', reportType: string = 'comprehensive') => {
    // Simulate export functionality
    console.log(`Exporting ${reportType} report as ${format}`)
    
    // In a real implementation, this would:
    // 1. Call an export API endpoint
    // 2. Generate the report in the requested format
    // 3. Download the file
    
    // For now, show a notification
    if (typeof window !== 'undefined') {
      alert(`${reportType.charAt(0).toUpperCase() + reportType.slice(1)} report exported as ${format.toUpperCase()}`)
    }
  }, [])

  useEffect(() => {
    if (autoFetch) {
      fetchReports(timeRange)
    }
  }, []) // Only run on mount

  return {
    reports,
    loading,
    error,
    refetch: fetchReports,
    clearError,
    exportReport
  }
}

export interface UseSalesReportsReturn {
  salesReport: ReportData['salesReport'] | null
  loading: boolean
  error: string | null
  refetch: (timeRange?: AnalyticsTimeRange) => Promise<void>
  clearError: () => void
}

/**
 * Hook for sales reports specifically
 */
export function useSalesReports(options: UseReportsOptions = {}): UseSalesReportsReturn {
  const { reports, loading, error, refetch, clearError } = useReports(options)

  return {
    salesReport: reports?.salesReport || null,
    loading,
    error,
    refetch,
    clearError
  }
}

export interface UseCustomerReportsReturn {
  customerReport: ReportData['customerReport'] | null
  loading: boolean
  error: string | null
  refetch: (timeRange?: AnalyticsTimeRange) => Promise<void>
  clearError: () => void
}

/**
 * Hook for customer reports specifically
 */
export function useCustomerReports(options: UseReportsOptions = {}): UseCustomerReportsReturn {
  const { reports, loading, error, refetch, clearError } = useReports(options)

  return {
    customerReport: reports?.customerReport || null,
    loading,
    error,
    refetch,
    clearError
  }
}

export interface UseInventoryReportsReturn {
  inventoryReport: ReportData['inventoryReport'] | null
  loading: boolean
  error: string | null
  refetch: (timeRange?: AnalyticsTimeRange) => Promise<void>
  clearError: () => void
}

/**
 * Hook for inventory reports specifically
 */
export function useInventoryReports(options: UseReportsOptions = {}): UseInventoryReportsReturn {
  const { reports, loading, error, refetch, clearError } = useReports(options)

  return {
    inventoryReport: reports?.inventoryReport || null,
    loading,
    error,
    refetch,
    clearError
  }
}

// Utility hook for reports time range management
export interface UseReportsTimeRangeReturn {
  timeRange: AnalyticsTimeRange
  setTimeRange: (range: AnalyticsTimeRange) => void
  timeRangeOptions: Array<{
    value: AnalyticsTimeRange
    label: string
  }>
}

export function useReportsTimeRange(initialRange: AnalyticsTimeRange = '30d'): UseReportsTimeRangeReturn {
  const [timeRange, setTimeRange] = useState<AnalyticsTimeRange>(initialRange)

  const timeRangeOptions = [
    { value: '7d' as AnalyticsTimeRange, label: 'Last 7 days' },
    { value: '30d' as AnalyticsTimeRange, label: 'Last 30 days' },
    { value: '90d' as AnalyticsTimeRange, label: 'Last 90 days' },
    { value: '1y' as AnalyticsTimeRange, label: 'Last year' }
  ]

  return {
    timeRange,
    setTimeRange,
    timeRangeOptions
  }
}

// Utility hook for report export functionality
export interface UseReportExportReturn {
  exportReport: (format: 'pdf' | 'excel' | 'csv', reportType?: string) => void
  isExporting: boolean
}

export function useReportExport(): UseReportExportReturn {
  const [isExporting, setIsExporting] = useState(false)

  const exportReport = useCallback(async (format: 'pdf' | 'excel' | 'csv', reportType: string = 'comprehensive') => {
    setIsExporting(true)
    
    try {
      // Simulate export delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // In a real implementation, this would call an API endpoint
      console.log(`Exporting ${reportType} report as ${format}`)
      
      if (typeof window !== 'undefined') {
        alert(`${reportType.charAt(0).toUpperCase() + reportType.slice(1)} report exported as ${format.toUpperCase()}`)
      }
    } catch (error) {
      console.error('Export failed:', error)
      if (typeof window !== 'undefined') {
        alert('Export failed. Please try again.')
      }
    } finally {
      setIsExporting(false)
    }
  }, [])

  return {
    exportReport,
    isExporting
  }
}
