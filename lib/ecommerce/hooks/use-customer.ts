import { useState, useEffect } from 'react'

interface Customer {
  id: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  dateOfBirth?: string
  createdAt: string
  updatedAt: string
}

interface Address {
  id: string
  customerId: string
  firstName: string
  lastName: string
  company?: string
  address1: string
  address2?: string
  city: string
  province: string
  country: string
  zip: string
  phone?: string
  isDefault: boolean
  type: 'shipping' | 'billing'
}

interface UseCustomerReturn {
  customer: Customer | null
  addresses: Address[]
  loading: boolean
  error: string | null
  updateCustomer: (data: Partial<Customer>) => Promise<boolean>
  addAddress: (address: Omit<Address, 'id' | 'customerId'>) => Promise<boolean>
  updateAddress: (id: string, address: Partial<Address>) => Promise<boolean>
  deleteAddress: (id: string) => Promise<boolean>
  setDefaultAddress: (id: string) => Promise<boolean>
  refetch: () => void
}

export function useCustomer(customerId?: string): UseCustomerReturn {
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [addresses, setAddresses] = useState<Address[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchCustomer = async () => {
    if (!customerId) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/e-commerce/customers/${customerId}`)
      const data = await response.json()

      if (data.success) {
        setCustomer(data.data.customer)
        setAddresses(data.data.addresses || [])
      } else {
        setError(data.error || 'Failed to fetch customer data')
      }
    } catch (err) {
      setError('Network error occurred')
      console.error('Customer fetch error:', err)
    } finally {
      setLoading(false)
    }
  }

  const updateCustomer = async (updateData: Partial<Customer>): Promise<boolean> => {
    if (!customerId) return false

    try {
      const response = await fetch(`/api/e-commerce/customers/${customerId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      })

      const data = await response.json()

      if (data.success) {
        setCustomer(data.data)
        return true
      } else {
        setError(data.error || 'Failed to update customer')
        return false
      }
    } catch (err) {
      setError('Network error occurred')
      console.error('Customer update error:', err)
      return false
    }
  }

  const addAddress = async (addressData: Omit<Address, 'id' | 'customerId'>): Promise<boolean> => {
    if (!customerId) return false

    try {
      const response = await fetch(`/api/e-commerce/customers/${customerId}/addresses`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(addressData),
      })

      const data = await response.json()

      if (data.success) {
        setAddresses(prev => [...prev, data.data])
        return true
      } else {
        setError(data.error || 'Failed to add address')
        return false
      }
    } catch (err) {
      setError('Network error occurred')
      console.error('Address add error:', err)
      return false
    }
  }

  const updateAddress = async (addressId: string, updateData: Partial<Address>): Promise<boolean> => {
    if (!customerId) return false

    try {
      const response = await fetch(`/api/e-commerce/customers/${customerId}/addresses/${addressId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      })

      const data = await response.json()

      if (data.success) {
        setAddresses(prev => prev.map(addr => 
          addr.id === addressId ? { ...addr, ...data.data } : addr
        ))
        return true
      } else {
        setError(data.error || 'Failed to update address')
        return false
      }
    } catch (err) {
      setError('Network error occurred')
      console.error('Address update error:', err)
      return false
    }
  }

  const deleteAddress = async (addressId: string): Promise<boolean> => {
    if (!customerId) return false

    try {
      const response = await fetch(`/api/e-commerce/customers/${customerId}/addresses/${addressId}`, {
        method: 'DELETE',
      })

      const data = await response.json()

      if (data.success) {
        setAddresses(prev => prev.filter(addr => addr.id !== addressId))
        return true
      } else {
        setError(data.error || 'Failed to delete address')
        return false
      }
    } catch (err) {
      setError('Network error occurred')
      console.error('Address delete error:', err)
      return false
    }
  }

  const setDefaultAddress = async (addressId: string): Promise<boolean> => {
    if (!customerId) return false

    try {
      const response = await fetch(`/api/e-commerce/customers/${customerId}/addresses/${addressId}/default`, {
        method: 'POST',
      })

      const data = await response.json()

      if (data.success) {
        setAddresses(prev => prev.map(addr => ({
          ...addr,
          isDefault: addr.id === addressId
        })))
        return true
      } else {
        setError(data.error || 'Failed to set default address')
        return false
      }
    } catch (err) {
      setError('Network error occurred')
      console.error('Set default address error:', err)
      return false
    }
  }

  const refetch = () => {
    fetchCustomer()
  }

  useEffect(() => {
    fetchCustomer()
  }, [customerId])

  return {
    customer,
    addresses,
    loading,
    error,
    updateCustomer,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
    refetch,
  }
}
