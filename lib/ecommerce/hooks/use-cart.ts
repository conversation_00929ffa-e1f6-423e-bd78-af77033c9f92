// React hooks for cart management
'use client'

import { useState, useEffect, useCallback } from 'react'
import { 
  Cart, 
  CartItem,
  AddToCartInput,
  UpdateCartItemInput,
  RemoveFromCartInput,
  ApplyDiscountInput,
  RemoveDiscountInput,
  ApiResponse
} from '../types'

export interface UseCartOptions {
  userId?: string
  sessionId?: string
  autoFetch?: boolean
}

export interface UseCartReturn {
  cart: Cart | null
  loading: boolean
  error: { code: string; message: string } | null
  addToCart: (input: AddToCartInput) => Promise<void>
  updateCartItem: (input: UpdateCartItemInput) => Promise<void>
  removeFromCart: (input: RemoveFromCartInput) => Promise<void>
  applyDiscount: (input: ApplyDiscountInput) => Promise<void>
  removeDiscount: (input: RemoveDiscountInput) => Promise<void>
  clearCart: () => Promise<void>
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing shopping cart
 */
export function useCart(options: UseCartOptions = {}): UseCartReturn {
  const { userId, sessionId, autoFetch = true } = options

  const [cart, setCart] = useState<Cart | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)

  const fetchCart = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()
      if (userId) params.append('userId', userId)
      if (sessionId) params.append('sessionId', sessionId)

      const response = await fetch(`/api/e-commerce/cart?${params.toString()}`)
      const result: ApiResponse<Cart> = await response.json()

      if (result.success && result.data) {
        setCart(result.data)
      } else {
        setError(result.error || { code: 'FETCH_ERROR', message: 'Failed to fetch cart' })
        setCart(null)
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      setCart(null)
    } finally {
      setLoading(false)
    }
  }, [userId, sessionId])

  const addToCart = useCallback(async (input: AddToCartInput) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/cart/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...input,
          userId,
          sessionId
        }),
      })

      const result: ApiResponse<Cart> = await response.json()

      if (result.success && result.data) {
        setCart(result.data)
      } else {
        setError(result.error || { code: 'ADD_TO_CART_ERROR', message: 'Failed to add item to cart' })
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
    } finally {
      setLoading(false)
    }
  }, [userId, sessionId])

  const updateCartItem = useCallback(async (input: UpdateCartItemInput) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/cart/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      const result: ApiResponse<Cart> = await response.json()

      if (result.success && result.data) {
        setCart(result.data)
      } else {
        setError(result.error || { code: 'UPDATE_CART_ERROR', message: 'Failed to update cart item' })
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
    } finally {
      setLoading(false)
    }
  }, [])

  const removeFromCart = useCallback(async (input: RemoveFromCartInput) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/cart/remove', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      const result: ApiResponse<Cart> = await response.json()

      if (result.success && result.data) {
        setCart(result.data)
      } else {
        setError(result.error || { code: 'REMOVE_FROM_CART_ERROR', message: 'Failed to remove item from cart' })
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
    } finally {
      setLoading(false)
    }
  }, [])

  const applyDiscount = useCallback(async (input: ApplyDiscountInput) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/cart/discount/apply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      const result: ApiResponse<Cart> = await response.json()

      if (result.success && result.data) {
        setCart(result.data)
      } else {
        setError(result.error || { code: 'APPLY_DISCOUNT_ERROR', message: 'Failed to apply discount' })
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
    } finally {
      setLoading(false)
    }
  }, [])

  const removeDiscount = useCallback(async (input: RemoveDiscountInput) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/cart/discount/remove', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      const result: ApiResponse<Cart> = await response.json()

      if (result.success && result.data) {
        setCart(result.data)
      } else {
        setError(result.error || { code: 'REMOVE_DISCOUNT_ERROR', message: 'Failed to remove discount' })
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
    } finally {
      setLoading(false)
    }
  }, [])

  const clearCart = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/cart/clear', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, sessionId }),
      })

      const result: ApiResponse<void> = await response.json()

      if (result.success) {
        setCart(null)
      } else {
        setError(result.error || { code: 'CLEAR_CART_ERROR', message: 'Failed to clear cart' })
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
    } finally {
      setLoading(false)
    }
  }, [userId, sessionId])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch) {
      fetchCart()
    }
  }, [autoFetch, userId, sessionId]) // Use primitive dependencies instead of fetchCart function

  return {
    cart,
    loading,
    error,
    addToCart,
    updateCartItem,
    removeFromCart,
    applyDiscount,
    removeDiscount,
    clearCart,
    refetch: fetchCart,
    clearError
  }
}
