// Database configuration and connection management for the e-commerce library

import { PrismaClient } from '@prisma/client'

// Global Prisma client instance
declare global {
  var __prisma: PrismaClient | undefined
}

// Database configuration
export interface DatabaseConfig {
  url: string
  maxConnections?: number
  connectionTimeout?: number
  queryTimeout?: number
  logLevel?: 'info' | 'query' | 'warn' | 'error'
  enableLogging?: boolean
}

// Default database configuration
const defaultConfig: DatabaseConfig = {
  url: process.env.DATABASE_URL || 'postgresql://localhost:5432/ecommerce',
  maxConnections: 10,
  connectionTimeout: 10000,
  queryTimeout: 30000,
  logLevel: 'warn',
  enableLogging: process.env.NODE_ENV === 'development',
}

// Create Prisma client with configuration
function createPrismaClient(config: DatabaseConfig = defaultConfig): PrismaClient {
  const logLevels = config.enableLogging ? [config.logLevel!] : []
  
  return new PrismaClient({
    log: logLevels,
    datasources: {
      db: {
        url: config.url,
      },
    },
  })
}

// Get or create Prisma client (singleton pattern)
export function getPrismaClient(config?: DatabaseConfig): PrismaClient {
  if (globalThis.__prisma) {
    return globalThis.__prisma
  }

  const prisma = createPrismaClient(config)

  // Store in global for development hot reloading
  if (process.env.NODE_ENV === 'development') {
    globalThis.__prisma = prisma
  }

  return prisma
}

// Database connection manager
export class DatabaseManager {
  private static instance: DatabaseManager
  private prisma: PrismaClient
  private config: DatabaseConfig

  private constructor(config: DatabaseConfig = defaultConfig) {
    this.config = config
    this.prisma = createPrismaClient(config)
  }

  static getInstance(config?: DatabaseConfig): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager(config)
    }
    return DatabaseManager.instance
  }

  get client(): PrismaClient {
    return this.prisma
  }

  async connect(): Promise<void> {
    try {
      await this.prisma.$connect()
      console.log('Database connected successfully')
    } catch (error) {
      console.error('Failed to connect to database:', error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect()
      console.log('Database disconnected successfully')
    } catch (error) {
      console.error('Failed to disconnect from database:', error)
      throw error
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`
      return true
    } catch (error) {
      console.error('Database health check failed:', error)
      return false
    }
  }

  async transaction<T>(fn: (prisma: Omit<PrismaClient, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">) => Promise<T>): Promise<T> {
    return this.prisma.$transaction(fn)
  }

  async executeRawQuery<T = any>(query: string, params?: any[]): Promise<T> {
    return this.prisma.$queryRawUnsafe(query, ...(params || []))
  }

  async executeRawCommand(command: string, params?: any[]): Promise<number> {
    return this.prisma.$executeRawUnsafe(command, ...(params || []))
  }

  // Migration helpers
  async runMigrations(): Promise<void> {
    try {
      // This would typically be handled by Prisma CLI in production
      console.log('Migrations should be run using Prisma CLI: npx prisma migrate deploy')
    } catch (error) {
      console.error('Migration failed:', error)
      throw error
    }
  }

  async resetDatabase(): Promise<void> {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Database reset is not allowed in production')
    }

    try {
      // This would typically be handled by Prisma CLI
      console.log('Database reset should be done using Prisma CLI: npx prisma migrate reset')
    } catch (error) {
      console.error('Database reset failed:', error)
      throw error
    }
  }

  // Backup and restore (basic implementation)
  async createBackup(backupName?: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const name = backupName || `backup-${timestamp}`
    
    // This is a placeholder - actual implementation would depend on database type
    console.log(`Creating backup: ${name}`)
    return name
  }

  async restoreBackup(backupName: string): Promise<void> {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Database restore is not allowed in production')
    }

    // This is a placeholder - actual implementation would depend on database type
    console.log(`Restoring backup: ${backupName}`)
  }

  // Performance monitoring
  async getConnectionInfo(): Promise<{
    activeConnections: number
    maxConnections: number
    databaseSize?: string
  }> {
    try {
      // PostgreSQL specific queries - would need to be adapted for other databases
      const [connectionResult, sizeResult] = await Promise.all([
        this.prisma.$queryRaw<Array<{ count: number }>>`
          SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active'
        `,
        this.prisma.$queryRaw<Array<{ size: string }>>`
          SELECT pg_size_pretty(pg_database_size(current_database())) as size
        `
      ])

      return {
        activeConnections: Number(connectionResult[0]?.count || 0),
        maxConnections: this.config.maxConnections || 10,
        databaseSize: sizeResult[0]?.size
      }
    } catch (error) {
      console.error('Failed to get connection info:', error)
      return {
        activeConnections: 0,
        maxConnections: this.config.maxConnections || 10
      }
    }
  }

  async getSlowQueries(limit: number = 10): Promise<Array<{
    query: string
    duration: number
    calls: number
  }>> {
    try {
      // PostgreSQL specific - would need adaptation for other databases
      const result = await this.prisma.$queryRaw<Array<{
        query: string
        mean_exec_time: number
        calls: number
      }>>`
        SELECT 
          query,
          mean_exec_time,
          calls
        FROM pg_stat_statements 
        ORDER BY mean_exec_time DESC 
        LIMIT ${limit}
      `

      return result.map((row: { query: string; mean_exec_time: number; calls: number }) => ({
        query: row.query,
        duration: row.mean_exec_time,
        calls: row.calls
      }))
    } catch (error) {
      console.error('Failed to get slow queries:', error)
      return []
    }
  }
}

// Export default instance
export const db = DatabaseManager.getInstance()

// Export Prisma client for direct use
export const prisma = getPrismaClient()

// Utility functions
export async function withTransaction<T>(
  fn: (prisma: Omit<PrismaClient, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">) => Promise<T>
): Promise<T> {
  return db.transaction(fn)
}

export async function isHealthy(): Promise<boolean> {
  return db.healthCheck()
}

// Connection lifecycle hooks
export async function connectDatabase(config?: DatabaseConfig): Promise<void> {
  const dbManager = DatabaseManager.getInstance(config)
  await dbManager.connect()
}

export async function disconnectDatabase(): Promise<void> {
  await db.disconnect()
}

// Graceful shutdown handler
export function setupGracefulShutdown(): void {
  const shutdown = async (signal: string) => {
    console.log(`Received ${signal}, shutting down gracefully...`)
    try {
      await disconnectDatabase()
      process.exit(0)
    } catch (error) {
      console.error('Error during shutdown:', error)
      process.exit(1)
    }
  }

  process.on('SIGTERM', () => shutdown('SIGTERM'))
  process.on('SIGINT', () => shutdown('SIGINT'))
}

// Database seeding utilities
export async function seedDatabase(): Promise<void> {
  console.log('Database seeding should be implemented based on your specific needs')
  // Implementation would go here
}

export async function clearDatabase(): Promise<void> {
  if (process.env.NODE_ENV === 'production') {
    throw new Error('Database clearing is not allowed in production')
  }

  console.log('Database clearing should be implemented based on your specific needs')
  // Implementation would go here
}
