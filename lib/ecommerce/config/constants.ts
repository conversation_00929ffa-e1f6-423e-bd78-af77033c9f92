// Constants and configuration for the e-commerce library

// Currency configuration
export const CURRENCIES = {
  ZAR: {
    code: '<PERSON><PERSON>',
    name: 'South African Rand',
    symbol: 'R',
    decimals: 2,
    locale: 'en-ZA'
  },
  USD: {
    code: 'USD',
    name: 'US Dollar',
    symbol: '$',
    decimals: 2,
    locale: 'en-US'
  },
  EUR: {
    code: 'EUR',
    name: 'Euro',
    symbol: '€',
    decimals: 2,
    locale: 'en-EU'
  },
  GBP: {
    code: 'GBP',
    name: 'British Pound',
    symbol: '£',
    decimals: 2,
    locale: 'en-GB'
  }
} as const

export const DEFAULT_CURRENCY = 'ZAR'

// Order statuses
export const ORDER_STATUSES = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PROCESSING: 'processing',
  SHIPPED: 'shipped',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',
  RETURNED: 'returned'
} as const

export const PAYMENT_STATUSES = {
  PENDING: 'pending',
  AUTHORIZED: 'authorized',
  PARTIALLY_PAID: 'partially_paid',
  PAID: 'paid',
  PARTIALLY_REFUNDED: 'partially_refunded',
  REFUNDED: 'refunded',
  VOIDED: 'voided'
} as const

export const FULFILLMENT_STATUSES = {
  UNFULFILLED: 'unfulfilled',
  PARTIALLY_FULFILLED: 'partially_fulfilled',
  FULFILLED: 'fulfilled',
  SHIPPED: 'shipped',
  DELIVERED: 'delivered',
  RETURNED: 'returned',
  CANCELLED: 'cancelled'
} as const

// Product statuses
export const PRODUCT_STATUSES = {
  ACTIVE: 'active',
  DRAFT: 'draft',
  ARCHIVED: 'archived'
} as const

// Cart statuses
export const CART_STATUSES = {
  ACTIVE: 'active',
  ABANDONED: 'abandoned',
  CONVERTED: 'converted',
  EXPIRED: 'expired'
} as const

// Inventory statuses
export const INVENTORY_STATUSES = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  DISCONTINUED: 'discontinued'
} as const

// Payment methods
export const PAYMENT_METHODS = {
  CREDIT_CARD: 'credit_card',
  DEBIT_CARD: 'debit_card',
  BANK_ACCOUNT: 'bank_account',
  DIGITAL_WALLET: 'digital_wallet',
  BUY_NOW_PAY_LATER: 'buy_now_pay_later',
  CRYPTOCURRENCY: 'cryptocurrency',
  GIFT_CARD: 'gift_card',
  STORE_CREDIT: 'store_credit'
} as const

// Shipping methods
export const SHIPPING_METHODS = {
  FLAT_RATE: 'flat_rate',
  FREE_SHIPPING: 'free_shipping',
  LOCAL_PICKUP: 'local_pickup',
  CALCULATED: 'calculated',
  TABLE_RATE: 'table_rate'
} as const

// Tax types
export const TAX_TYPES = {
  SALES_TAX: 'sales_tax',
  VAT: 'vat',
  GST: 'gst',
  HST: 'hst',
  PST: 'pst',
  CUSTOM: 'custom'
} as const

// User roles and permissions
export const USER_ROLES = {
  CUSTOMER: 'customer',
  ADMIN: 'admin',
  MANAGER: 'manager',
  STAFF: 'staff'
} as const

export const PERMISSIONS = {
  // Product permissions
  PRODUCTS_VIEW: 'products:view',
  PRODUCTS_CREATE: 'products:create',
  PRODUCTS_UPDATE: 'products:update',
  PRODUCTS_DELETE: 'products:delete',
  
  // Order permissions
  ORDERS_VIEW: 'orders:view',
  ORDERS_CREATE: 'orders:create',
  ORDERS_UPDATE: 'orders:update',
  ORDERS_DELETE: 'orders:delete',
  ORDERS_FULFILL: 'orders:fulfill',
  ORDERS_REFUND: 'orders:refund',
  
  // Customer permissions
  CUSTOMERS_VIEW: 'customers:view',
  CUSTOMERS_CREATE: 'customers:create',
  CUSTOMERS_UPDATE: 'customers:update',
  CUSTOMERS_DELETE: 'customers:delete',
  
  // Inventory permissions
  INVENTORY_VIEW: 'inventory:view',
  INVENTORY_UPDATE: 'inventory:update',
  INVENTORY_ADJUST: 'inventory:adjust',
  INVENTORY_TRANSFER: 'inventory:transfer',
  
  // Analytics permissions
  ANALYTICS_VIEW: 'analytics:view',
  ANALYTICS_EXPORT: 'analytics:export',
  
  // Settings permissions
  SETTINGS_VIEW: 'settings:view',
  SETTINGS_UPDATE: 'settings:update',
  
  // Admin permissions
  ADMIN_USERS: 'admin:users',
  ADMIN_ROLES: 'admin:roles',
  ADMIN_SYSTEM: 'admin:system'
} as const

// Default pagination
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100
} as const

// File upload limits
export const FILE_LIMITS = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_FILES_PER_UPLOAD: 10,
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'text/csv', 'application/vnd.ms-excel']
} as const

// Cache configuration
export const CACHE = {
  DEFAULT_TTL: 5 * 60 * 1000, // 5 minutes
  PRODUCT_TTL: 10 * 60 * 1000, // 10 minutes
  CATEGORY_TTL: 30 * 60 * 1000, // 30 minutes
  USER_TTL: 2 * 60 * 1000, // 2 minutes
  CART_TTL: 24 * 60 * 60 * 1000, // 24 hours
  SESSION_TTL: 30 * 60 * 1000 // 30 minutes
} as const

// Rate limiting
export const RATE_LIMITS = {
  API_REQUESTS_PER_MINUTE: 100,
  LOGIN_ATTEMPTS_PER_HOUR: 5,
  PASSWORD_RESET_PER_HOUR: 3,
  EMAIL_VERIFICATION_PER_HOUR: 3
} as const

// Email templates
export const EMAIL_TEMPLATES = {
  WELCOME: 'welcome',
  ORDER_CONFIRMATION: 'order_confirmation',
  ORDER_SHIPPED: 'order_shipped',
  ORDER_DELIVERED: 'order_delivered',
  ORDER_CANCELLED: 'order_cancelled',
  PASSWORD_RESET: 'password_reset',
  EMAIL_VERIFICATION: 'email_verification',
  ABANDONED_CART: 'abandoned_cart',
  LOW_STOCK_ALERT: 'low_stock_alert'
} as const

// Notification types
export const NOTIFICATION_TYPES = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error'
} as const

// Event types for analytics and webhooks
export const EVENT_TYPES = {
  // User events
  USER_REGISTERED: 'user.registered',
  USER_LOGIN: 'user.login',
  USER_LOGOUT: 'user.logout',
  
  // Product events
  PRODUCT_VIEWED: 'product.viewed',
  PRODUCT_ADDED_TO_CART: 'product.added_to_cart',
  PRODUCT_REMOVED_FROM_CART: 'product.removed_from_cart',
  PRODUCT_ADDED_TO_WISHLIST: 'product.added_to_wishlist',
  
  // Order events
  ORDER_CREATED: 'order.created',
  ORDER_PAID: 'order.paid',
  ORDER_SHIPPED: 'order.shipped',
  ORDER_DELIVERED: 'order.delivered',
  ORDER_CANCELLED: 'order.cancelled',
  ORDER_REFUNDED: 'order.refunded',
  
  // Cart events
  CART_CREATED: 'cart.created',
  CART_UPDATED: 'cart.updated',
  CART_ABANDONED: 'cart.abandoned',
  CART_CONVERTED: 'cart.converted',
  
  // Payment events
  PAYMENT_INITIATED: 'payment.initiated',
  PAYMENT_SUCCEEDED: 'payment.succeeded',
  PAYMENT_FAILED: 'payment.failed',
  PAYMENT_REFUNDED: 'payment.refunded',
  
  // Inventory events
  INVENTORY_LOW_STOCK: 'inventory.low_stock',
  INVENTORY_OUT_OF_STOCK: 'inventory.out_of_stock',
  INVENTORY_RESTOCKED: 'inventory.restocked'
} as const

// Error codes
export const ERROR_CODES = {
  // General errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  
  // Authentication errors
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  
  // Product errors
  PRODUCT_NOT_FOUND: 'PRODUCT_NOT_FOUND',
  PRODUCT_UNAVAILABLE: 'PRODUCT_UNAVAILABLE',
  INSUFFICIENT_STOCK: 'INSUFFICIENT_STOCK',
  
  // Order errors
  ORDER_NOT_FOUND: 'ORDER_NOT_FOUND',
  ORDER_CANNOT_BE_MODIFIED: 'ORDER_CANNOT_BE_MODIFIED',
  ORDER_ALREADY_FULFILLED: 'ORDER_ALREADY_FULFILLED',
  
  // Payment errors
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  PAYMENT_DECLINED: 'PAYMENT_DECLINED',
  PAYMENT_METHOD_INVALID: 'PAYMENT_METHOD_INVALID',
  
  // Cart errors
  CART_NOT_FOUND: 'CART_NOT_FOUND',
  CART_EMPTY: 'CART_EMPTY',
  CART_ITEM_NOT_FOUND: 'CART_ITEM_NOT_FOUND',
  
  // Shipping errors
  SHIPPING_ADDRESS_INVALID: 'SHIPPING_ADDRESS_INVALID',
  SHIPPING_METHOD_UNAVAILABLE: 'SHIPPING_METHOD_UNAVAILABLE',
  
  // Inventory errors
  INVENTORY_NOT_FOUND: 'INVENTORY_NOT_FOUND',
  INVENTORY_INSUFFICIENT: 'INVENTORY_INSUFFICIENT'
} as const

// South African specific constants
export const SOUTH_AFRICA = {
  COUNTRY_CODE: 'ZA',
  CURRENCY: 'ZAR',
  PROVINCES: [
    'Eastern Cape',
    'Free State',
    'Gauteng',
    'KwaZulu-Natal',
    'Limpopo',
    'Mpumalanga',
    'Northern Cape',
    'North West',
    'Western Cape'
  ],
  VAT_RATE: 0.15, // 15% VAT
  POSTAL_CODE_PATTERN: /^\d{4}$/,
  PHONE_PATTERN: /^(\+27|0)[1-9]\d{8}$/
} as const

// Feature flags
export const FEATURES = {
  MULTI_CURRENCY: true,
  INVENTORY_TRACKING: true,
  REVIEWS_ENABLED: true,
  WISHLIST_ENABLED: true,
  GIFT_CARDS: true,
  LOYALTY_PROGRAM: false,
  SUBSCRIPTIONS: false,
  MARKETPLACE: false
} as const

// API versioning
export const API = {
  VERSION: 'v1',
  BASE_PATH: '/api/v1',
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000 // 1 second
} as const
