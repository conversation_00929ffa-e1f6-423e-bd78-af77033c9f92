// Shipping and tax related types and interfaces

import { BaseEntity, Money, Address } from './base'

export interface ShippingZone extends BaseEntity {
  name: string
  description?: string
  
  // Coverage
  countries: string[]
  states?: Array<{
    country: string
    states: string[]
  }>
  postalCodes?: Array<{
    country: string
    postalCodes: string[]
  }>
  
  // Status
  isActive: boolean
  
  // Methods
  shippingMethods: ShippingMethod[]
  
  // Metadata
  metadata?: Record<string, any>
}

export interface ShippingMethod extends BaseEntity {
  name: string
  description?: string
  code: string
  
  // Zone
  shippingZoneId: string
  
  // Type
  type: 'flat_rate' | 'free_shipping' | 'local_pickup' | 'calculated' | 'table_rate'
  
  // Pricing
  price: Money
  freeShippingThreshold?: Money
  
  // Calculation method
  calculationMethod: 'per_order' | 'per_item' | 'per_weight' | 'per_class'
  
  // Weight/dimension limits
  minWeight?: number
  maxWeight?: number
  minDimensions?: {
    length: number
    width: number
    height: number
    unit: string
  }
  maxDimensions?: {
    length: number
    width: number
    height: number
    unit: string
  }
  
  // Delivery estimates
  estimatedDelivery?: {
    min: number
    max: number
    unit: 'hours' | 'days' | 'weeks'
  }
  
  // Carrier integration
  carrier?: {
    name: string
    service: string
    accountNumber?: string
  }
  
  // Conditions
  conditions?: Array<{
    type: 'min_amount' | 'max_amount' | 'min_weight' | 'max_weight' | 'product_category' | 'product_tag'
    operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains'
    value: string | number
  }>
  
  // Status
  isActive: boolean
  
  // Tax
  isTaxable: boolean
  taxClass?: string
  
  // Metadata
  metadata?: Record<string, any>
}

export interface ShippingRate {
  id: string
  methodId: string
  methodName: string
  price: Money
  estimatedDelivery?: {
    min: number
    max: number
    unit: 'hours' | 'days' | 'weeks'
    date?: Date
  }
  carrier?: string
  service?: string
  description?: string
  metadata?: Record<string, any>
}

export interface ShippingCalculation {
  // Input
  items: Array<{
    productId: string
    variantId?: string
    quantity: number
    weight?: number
    dimensions?: {
      length: number
      width: number
      height: number
      unit: string
    }
    value: Money
    requiresShipping: boolean
    shippingClass?: string
  }>
  destination: Address
  origin?: Address
  
  // Results
  rates: ShippingRate[]
  errors?: string[]
  warnings?: string[]
}

export interface TaxRate extends BaseEntity {
  name: string
  code: string
  rate: number // percentage
  
  // Jurisdiction
  country: string
  state?: string
  city?: string
  postalCode?: string
  
  // Type
  type: 'sales_tax' | 'vat' | 'gst' | 'hst' | 'pst' | 'custom'
  
  // Application
  isCompound: boolean // applies to subtotal + other taxes
  priority: number
  
  // Product applicability
  productTypes?: string[]
  productCategories?: string[]
  taxClass?: string
  
  // Status
  isActive: boolean
  
  // Dates
  effectiveFrom: Date
  effectiveTo?: Date
  
  // Metadata
  metadata?: Record<string, any>
}

export interface TaxCalculation {
  // Input
  items: Array<{
    productId: string
    variantId?: string
    quantity: number
    unitPrice: Money
    totalPrice: Money
    isTaxable: boolean
    taxClass?: string
    productType?: string
    productCategory?: string
  }>
  shippingAmount?: Money
  destination: Address
  origin?: Address
  customerType?: 'individual' | 'business'
  taxExemptionId?: string
  
  // Results
  taxLines: Array<{
    name: string
    rate: number
    amount: Money
    jurisdiction?: string
    type: string
  }>
  totalTax: Money
  taxIncluded: boolean
  errors?: string[]
  warnings?: string[]
}

export interface Shipment extends BaseEntity {
  // Reference
  shipmentNumber: string
  orderId: string
  
  // Items
  items: ShipmentItem[]
  
  // Addresses
  origin: Address
  destination: Address
  
  // Carrier
  carrier: string
  service: string
  trackingNumber?: string
  trackingUrl?: string
  
  // Status
  status: 'pending' | 'picked_up' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'exception' | 'returned'
  
  // Dates
  shippedAt?: Date
  estimatedDeliveryAt?: Date
  deliveredAt?: Date
  
  // Package details
  packages: ShipmentPackage[]
  
  // Cost
  shippingCost: Money
  insuranceAmount?: Money
  
  // Documents
  labelUrl?: string
  invoiceUrl?: string
  customsFormUrl?: string
  
  // Tracking events
  trackingEvents: TrackingEvent[]
  
  // Metadata
  notes?: string
  metadata?: Record<string, any>
}

export interface ShipmentItem {
  id: string
  orderItemId: string
  productId: string
  variantId?: string
  quantity: number
  
  // Product details
  name: string
  sku?: string
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
    unit: string
  }
  value: Money
  
  // Customs (for international shipping)
  hsCode?: string
  countryOfOrigin?: string
  description?: string
}

export interface ShipmentPackage {
  id: string
  type: 'box' | 'envelope' | 'tube' | 'pak' | 'custom'
  
  // Dimensions
  length: number
  width: number
  height: number
  weight: number
  dimensionUnit: string
  weightUnit: string
  
  // Contents
  items: Array<{
    shipmentItemId: string
    quantity: number
  }>
  
  // Tracking
  trackingNumber?: string
  
  // Value
  declaredValue?: Money
  insuredValue?: Money
  
  // Customs
  customsValue?: Money
  customsDescription?: string
}

export interface TrackingEvent {
  id: string
  timestamp: Date
  status: string
  description: string
  location?: {
    city?: string
    state?: string
    country?: string
    postalCode?: string
  }
  carrier?: string
  metadata?: Record<string, any>
}

export interface ShippingLabel {
  id: string
  shipmentId: string
  packageId?: string
  
  // Label details
  labelUrl: string
  labelFormat: 'pdf' | 'png' | 'zpl' | 'epl'
  
  // Tracking
  trackingNumber: string
  
  // Cost
  cost: Money
  
  // Dates
  createdAt: Date
  expiresAt?: Date
  
  // Status
  isVoided: boolean
  voidedAt?: Date
  
  // Metadata
  metadata?: Record<string, any>
}

// Carrier integrations
export interface CarrierAccount extends BaseEntity {
  name: string
  carrier: 'ups' | 'fedex' | 'dhl' | 'usps' | 'canada_post' | 'royal_mail' | 'australia_post' | 'custom'
  
  // Credentials
  credentials: Record<string, string>
  
  // Configuration
  isActive: boolean
  isTestMode: boolean
  
  // Services
  supportedServices: string[]
  
  // Features
  features: {
    tracking: boolean
    insurance: boolean
    signatureConfirmation: boolean
    adultSignature: boolean
    saturdayDelivery: boolean
    holdAtLocation: boolean
    customsDocuments: boolean
  }
  
  // Metadata
  metadata?: Record<string, any>
}

export interface CarrierRate {
  carrier: string
  service: string
  serviceName: string
  price: Money
  estimatedDelivery?: Date
  transitTime?: number
  guaranteedDelivery?: boolean
  
  // Additional services
  insurance?: {
    available: boolean
    cost?: Money
  }
  signatureConfirmation?: {
    available: boolean
    cost?: Money
  }
  
  // Metadata
  metadata?: Record<string, any>
}

// Shipping rules and automation
export interface ShippingRule extends BaseEntity {
  name: string
  description?: string
  
  // Conditions
  conditions: Array<{
    type: 'order_total' | 'order_weight' | 'destination_country' | 'destination_state' | 'product_category' | 'product_tag' | 'customer_group'
    operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'not_contains'
    value: string | number
  }>
  
  // Actions
  actions: Array<{
    type: 'set_shipping_method' | 'add_shipping_discount' | 'require_signature' | 'add_insurance' | 'set_delivery_instructions'
    value: any
  }>
  
  // Priority
  priority: number
  
  // Status
  isActive: boolean
  
  // Metadata
  metadata?: Record<string, any>
}

// Delivery preferences
export interface DeliveryPreference extends BaseEntity {
  customerId: string
  
  // Preferences
  preferredCarrier?: string
  preferredService?: string
  deliveryInstructions?: string
  
  // Options
  requireSignature: boolean
  leaveAtDoor: boolean
  holdAtLocation: boolean
  
  // Notifications
  emailNotifications: boolean
  smsNotifications: boolean
  
  // Delivery windows
  preferredDeliveryWindows?: Array<{
    day: string
    startTime: string
    endTime: string
  }>
  
  // Address preferences
  defaultShippingAddressId?: string
  alternateAddresses?: string[]
  
  // Metadata
  metadata?: Record<string, any>
}

// Returns and exchanges
export interface ReturnShipment extends BaseEntity {
  // Reference
  returnNumber: string
  orderId: string
  originalShipmentId?: string
  
  // Items
  items: Array<{
    orderItemId: string
    quantity: number
    reason: string
    condition: 'new' | 'used' | 'damaged'
  }>
  
  // Addresses
  origin: Address
  destination: Address
  
  // Shipping
  carrier?: string
  service?: string
  trackingNumber?: string
  shippingCost?: Money
  
  // Status
  status: 'pending' | 'shipped' | 'in_transit' | 'delivered' | 'processed'
  
  // Dates
  shippedAt?: Date
  deliveredAt?: Date
  processedAt?: Date
  
  // Label
  labelUrl?: string
  
  // Metadata
  notes?: string
  metadata?: Record<string, any>
}

// Shipping analytics
export interface ShippingAnalytics {
  period: {
    start: Date
    end: Date
  }
  
  // Volume
  totalShipments: number
  totalPackages: number
  totalWeight: number
  
  // Cost
  totalShippingCost: Money
  averageShippingCost: Money
  
  // Performance
  onTimeDeliveryRate: number
  averageDeliveryTime: number
  damageRate: number
  lostPackageRate: number
  
  // By carrier
  byCarrier: Array<{
    carrier: string
    shipmentCount: number
    cost: Money
    onTimeRate: number
    averageDeliveryTime: number
  }>
  
  // By service
  byService: Array<{
    service: string
    shipmentCount: number
    cost: Money
    onTimeRate: number
  }>
  
  // By destination
  byDestination: Array<{
    country: string
    state?: string
    shipmentCount: number
    cost: Money
    averageDeliveryTime: number
  }>
  
  // Trends
  dailyTrends: Array<{
    date: Date
    shipmentCount: number
    cost: Money
    onTimeRate: number
  }>
}

// Input types
export interface CreateShipmentInput {
  orderId: string
  items: Array<{
    orderItemId: string
    quantity: number
  }>
  carrier: string
  service: string
  packages: Array<{
    type: string
    length: number
    width: number
    height: number
    weight: number
    items: Array<{
      orderItemId: string
      quantity: number
    }>
  }>
  insuranceAmount?: Money
  signatureRequired?: boolean
  deliveryInstructions?: string
  metadata?: Record<string, any>
}

export interface GetShippingRatesInput {
  items: Array<{
    productId: string
    variantId?: string
    quantity: number
    weight?: number
    dimensions?: {
      length: number
      width: number
      height: number
      unit: string
    }
    value: Money
  }>
  destination: Address
  origin?: Address
  preferredCarriers?: string[]
  preferredServices?: string[]
}

export interface CalculateTaxInput {
  items: Array<{
    productId: string
    variantId?: string
    quantity: number
    unitPrice: Money
    isTaxable: boolean
    taxClass?: string
  }>
  shippingAmount?: Money
  destination: Address
  origin?: Address
  customerType?: 'individual' | 'business'
  taxExemptionId?: string
}
