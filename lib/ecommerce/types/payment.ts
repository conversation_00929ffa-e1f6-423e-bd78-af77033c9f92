// Payment processing related types and interfaces

import { BaseEntity, Money, Address } from './base'

export interface PaymentMethod extends BaseEntity {
  // Customer
  customerId?: string
  
  // Type
  type: 'credit_card' | 'debit_card' | 'bank_account' | 'digital_wallet' | 'buy_now_pay_later' | 'cryptocurrency' | 'gift_card' | 'store_credit'
  
  // Card details (for card payments)
  card?: {
    brand: 'visa' | 'mastercard' | 'amex' | 'discover' | 'diners' | 'jcb' | 'unionpay' | 'unknown'
    last4: string
    expiryMonth: number
    expiryYear: number
    fingerprint: string
    funding: 'credit' | 'debit' | 'prepaid' | 'unknown'
    country: string
    issuer?: string
  }
  
  // Bank account details
  bankAccount?: {
    accountType: 'checking' | 'savings'
    routingNumber: string
    last4: string
    bankName?: string
    country: string
  }
  
  // Digital wallet details
  digitalWallet?: {
    provider: 'paypal' | 'apple_pay' | 'google_pay' | 'samsung_pay' | 'amazon_pay'
    email?: string
    phone?: string
  }
  
  // Billing address
  billingAddress?: Address
  
  // Status
  isDefault: boolean
  isVerified: boolean
  isActive: boolean
  
  // Gateway
  gatewayId: string
  gatewayCustomerId?: string
  gatewayPaymentMethodId: string
  
  // Metadata
  nickname?: string
  metadata?: Record<string, any>
}

export interface PaymentIntent extends BaseEntity {
  // Amount
  amount: Money
  
  // Status
  status: 'requires_payment_method' | 'requires_confirmation' | 'requires_action' | 'processing' | 'requires_capture' | 'cancelled' | 'succeeded'
  
  // Customer
  customerId?: string
  
  // Payment method
  paymentMethodId?: string
  paymentMethodTypes: string[]
  
  // Capture
  captureMethod: 'automatic' | 'manual'
  
  // Confirmation
  confirmationMethod: 'automatic' | 'manual'
  
  // Gateway
  gatewayId: string
  gatewayIntentId: string
  
  // Client secret (for frontend)
  clientSecret?: string
  
  // Metadata
  orderId?: string
  description?: string
  statementDescriptor?: string
  metadata?: Record<string, any>
  
  // Dates
  expiresAt?: Date
  confirmedAt?: Date
  cancelledAt?: Date
  succeededAt?: Date
  
  // Error
  lastPaymentError?: PaymentError
  
  // Receipt
  receiptEmail?: string
  receiptUrl?: string
}

export interface Payment extends BaseEntity {
  // Reference
  paymentNumber: string
  orderId?: string
  invoiceId?: string
  
  // Amount
  amount: Money
  amountCaptured: Money
  amountRefunded: Money
  
  // Status
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'cancelled' | 'requires_action'
  
  // Customer
  customerId?: string
  
  // Payment method
  paymentMethodId?: string
  paymentMethod?: PaymentMethod
  
  // Gateway
  gatewayId: string
  gatewayPaymentId: string
  gatewayChargeId?: string
  
  // Processing
  processingFee?: Money
  netAmount?: Money
  
  // Dates
  authorizedAt?: Date
  capturedAt?: Date
  failedAt?: Date
  cancelledAt?: Date
  
  // Receipt
  receiptEmail?: string
  receiptUrl?: string
  receiptNumber?: string
  
  // Dispute
  disputed: boolean
  disputeReason?: string
  disputeStatus?: 'warning_needs_response' | 'warning_under_review' | 'warning_closed' | 'needs_response' | 'under_review' | 'charge_refunded' | 'won' | 'lost'
  
  // Refunds
  refunds: PaymentRefund[]
  
  // Risk
  riskLevel: 'low' | 'medium' | 'high'
  riskScore?: number
  riskReasons?: string[]
  
  // Metadata
  description?: string
  statementDescriptor?: string
  metadata?: Record<string, any>
  
  // Error
  failureCode?: string
  failureMessage?: string
}

export interface PaymentRefund extends BaseEntity {
  // Reference
  refundNumber: string
  paymentId: string
  
  // Amount
  amount: Money
  
  // Status
  status: 'pending' | 'succeeded' | 'failed' | 'cancelled'
  
  // Gateway
  gatewayRefundId: string
  
  // Reason
  reason: 'duplicate' | 'fraudulent' | 'requested_by_customer' | 'expired_uncaptured_charge' | 'other'
  reasonDetails?: string
  
  // Dates
  processedAt?: Date
  failedAt?: Date
  
  // Metadata
  metadata?: Record<string, any>
  
  // Error
  failureCode?: string
  failureMessage?: string
}

export interface PaymentGateway {
  id: string
  name: string
  provider: 'stripe' | 'paypal' | 'square' | 'yoco' | 'payu' | 'payfast' | 'ozow' | 'custom'
  
  // Configuration
  isActive: boolean
  isTestMode: boolean
  supportedCurrencies: string[]
  supportedCountries: string[]
  supportedPaymentMethods: string[]
  
  // Credentials
  credentials: Record<string, string>
  
  // Features
  features: {
    supportsAuthorization: boolean
    supportsCapture: boolean
    supportsRefunds: boolean
    supportsPartialRefunds: boolean
    supportsVoids: boolean
    supportsRecurring: boolean
    supportsTokenization: boolean
    supportsWebhooks: boolean
    supports3DS: boolean
    supportsSCA: boolean
  }
  
  // Fees
  fees: {
    fixedFee?: Money
    percentageFee?: number
    perTransactionFee?: Money
    monthlyFee?: Money
    setupFee?: Money
  }
  
  // Limits
  limits: {
    minAmount?: Money
    maxAmount?: Money
    dailyLimit?: Money
    monthlyLimit?: Money
  }
  
  // Processing times
  processingTimes: {
    authorization: string
    capture: string
    refund: string
    settlement: string
  }
  
  // Metadata
  metadata?: Record<string, any>
}

export interface PaymentError {
  code: string
  message: string
  type: 'card_error' | 'invalid_request_error' | 'api_error' | 'authentication_error' | 'rate_limit_error'
  param?: string
  declineCode?: string
  chargeId?: string
}

// Payment processing inputs
export interface CreatePaymentIntentInput {
  amount: Money
  customerId?: string
  paymentMethodId?: string
  paymentMethodTypes?: string[]
  captureMethod?: 'automatic' | 'manual'
  confirmationMethod?: 'automatic' | 'manual'
  orderId?: string
  description?: string
  statementDescriptor?: string
  receiptEmail?: string
  metadata?: Record<string, any>
}

export interface ConfirmPaymentIntentInput {
  paymentIntentId: string
  paymentMethodId?: string
  returnUrl?: string
  metadata?: Record<string, any>
}

export interface CapturePaymentInput {
  paymentId: string
  amount?: Money
  metadata?: Record<string, any>
}

export interface RefundPaymentInput {
  paymentId: string
  amount?: Money
  reason?: PaymentRefund['reason']
  reasonDetails?: string
  metadata?: Record<string, any>
}

export interface CreatePaymentMethodInput {
  type: PaymentMethod['type']
  customerId?: string
  
  // Card details
  card?: {
    number: string
    expiryMonth: number
    expiryYear: number
    cvc: string
  }
  
  // Bank account details
  bankAccount?: {
    accountNumber: string
    routingNumber: string
    accountType: 'checking' | 'savings'
  }
  
  // Digital wallet
  digitalWallet?: {
    provider: string
    token: string
  }
  
  // Billing address
  billingAddress?: Address
  
  // Metadata
  nickname?: string
  metadata?: Record<string, any>
}

// Payment analytics
export interface PaymentAnalytics {
  period: {
    start: Date
    end: Date
  }
  totalPayments: number
  totalAmount: Money
  totalRefunded: Money
  netAmount: Money
  averagePaymentAmount: Money
  successRate: number
  refundRate: number
  disputeRate: number
  processingFees: Money
  
  // By gateway
  byGateway: Array<{
    gatewayId: string
    gatewayName: string
    paymentCount: number
    amount: Money
    successRate: number
    averageProcessingTime: number
  }>
  
  // By payment method
  byPaymentMethod: Array<{
    type: string
    paymentCount: number
    amount: Money
    successRate: number
  }>
  
  // By currency
  byCurrency: Array<{
    currency: string
    paymentCount: number
    amount: Money
  }>
  
  // Trends
  dailyTrends: Array<{
    date: Date
    paymentCount: number
    amount: Money
    successRate: number
  }>
  
  // Top failure reasons
  topFailureReasons: Array<{
    reason: string
    count: number
    percentage: number
  }>
}

// Subscription payments
export interface PaymentSubscription extends BaseEntity {
  customerId: string
  paymentMethodId: string
  
  // Plan
  planId: string
  planName: string
  
  // Billing
  amount: Money
  interval: 'day' | 'week' | 'month' | 'year'
  intervalCount: number
  
  // Status
  status: 'active' | 'past_due' | 'cancelled' | 'unpaid' | 'incomplete' | 'incomplete_expired' | 'trialing'
  
  // Trial
  trialStart?: Date
  trialEnd?: Date
  
  // Billing cycle
  currentPeriodStart: Date
  currentPeriodEnd: Date
  
  // Dates
  startDate: Date
  endDate?: Date
  cancelledAt?: Date
  
  // Gateway
  gatewaySubscriptionId: string
  
  // Metadata
  metadata?: Record<string, any>
}

// Fraud detection
export interface FraudCheck extends BaseEntity {
  paymentId: string
  orderId?: string
  
  // Risk assessment
  riskScore: number
  riskLevel: 'low' | 'medium' | 'high'
  
  // Checks performed
  checks: Array<{
    type: 'address_verification' | 'cvc_check' | 'velocity_check' | 'blacklist_check' | 'geolocation_check' | 'device_fingerprint'
    result: 'pass' | 'fail' | 'unavailable'
    score?: number
    details?: Record<string, any>
  }>
  
  // Decision
  decision: 'approve' | 'decline' | 'review'
  decisionReason?: string
  
  // Manual review
  requiresReview: boolean
  reviewedBy?: string
  reviewedAt?: Date
  reviewDecision?: 'approve' | 'decline'
  reviewNotes?: string
  
  // Metadata
  metadata?: Record<string, any>
}

// Payment disputes
export interface PaymentDispute extends BaseEntity {
  paymentId: string
  
  // Dispute details
  disputeId: string
  reason: 'credit_not_processed' | 'duplicate' | 'fraudulent' | 'general' | 'incorrect_account_details' | 'insufficient_funds' | 'product_not_received' | 'product_unacceptable' | 'subscription_canceled' | 'unrecognized'
  status: 'warning_needs_response' | 'warning_under_review' | 'warning_closed' | 'needs_response' | 'under_review' | 'charge_refunded' | 'won' | 'lost'
  
  // Amount
  amount: Money
  
  // Evidence
  evidence: {
    accessActivityLog?: string
    billingAddress?: string
    cancellationPolicy?: string
    cancellationPolicyDisclosure?: string
    cancellationRebuttal?: string
    customerCommunication?: string
    customerEmailAddress?: string
    customerName?: string
    customerPurchaseIp?: string
    customerSignature?: string
    duplicateChargeDocumentation?: string
    duplicateChargeExplanation?: string
    duplicateChargeId?: string
    productDescription?: string
    receipt?: string
    refundPolicy?: string
    refundPolicyDisclosure?: string
    refundRefusalExplanation?: string
    serviceDate?: string
    serviceDocumentation?: string
    shippingAddress?: string
    shippingCarrier?: string
    shippingDate?: string
    shippingDocumentation?: string
    shippingTrackingNumber?: string
    uncategorizedFile?: string
    uncategorizedText?: string
  }
  
  // Dates
  evidenceDueBy?: Date
  submittedAt?: Date
  
  // Metadata
  metadata?: Record<string, any>
}

// Saved payment methods for customers
export interface SavedPaymentMethod extends BaseEntity {
  customerId: string
  paymentMethodId: string
  isDefault: boolean
  nickname?: string
  lastUsedAt?: Date
  usageCount: number
}

// Payment webhooks
export interface PaymentWebhook extends BaseEntity {
  gatewayId: string
  eventType: string
  eventId: string
  data: Record<string, any>
  signature?: string
  processed: boolean
  processedAt?: Date
  error?: string
  retryCount: number
  nextRetryAt?: Date
}
