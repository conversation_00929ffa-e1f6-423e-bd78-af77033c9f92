// Admin dashboard and management related types

import { BaseEntity, Money, PaginatedResponse } from './base'

export interface AdminUser extends BaseEntity {
  email: string
  firstName: string
  lastName: string
  displayName: string
  
  // Authentication
  passwordHash: string
  lastLoginAt?: Date
  loginAttempts: number
  lockedUntil?: Date
  
  // Profile
  avatar?: string
  phone?: string
  timezone: string
  locale: string
  
  // Permissions
  roles: AdminRole[]
  permissions: string[]
  
  // Status
  isActive: boolean
  isEmailVerified: boolean
  
  // Two-factor authentication
  twoFactorEnabled: boolean
  twoFactorSecret?: string
  backupCodes?: string[]
  
  // Session management
  activeSessions: AdminSession[]
  
  // Metadata
  metadata?: Record<string, any>
  notes?: string
}

export interface AdminRole extends BaseEntity {
  name: string
  description?: string
  permissions: string[]
  isSystemRole: boolean
  userCount: number
}

export interface AdminSession extends BaseEntity {
  userId: string
  token: string
  ipAddress: string
  userAgent: string
  isActive: boolean
  lastActivityAt: Date
  expiresAt: Date
}

export interface AdminPermission {
  id: string
  name: string
  description?: string
  resource: string
  action: string
  category: string
}

// Dashboard analytics
export interface DashboardStats {
  // Sales
  totalRevenue: Money
  totalOrders: number
  averageOrderValue: Money
  conversionRate: number
  
  // Products
  totalProducts: number
  lowStockProducts: number
  outOfStockProducts: number
  
  // Customers
  totalCustomers: number
  newCustomers: number
  returningCustomers: number
  
  // Inventory
  totalInventoryValue: Money
  inventoryTurnover: number
  
  // Performance
  pageViews: number
  uniqueVisitors: number
  bounceRate: number
  
  // Period comparison
  period: {
    start: Date
    end: Date
  }
  previousPeriod?: {
    start: Date
    end: Date
  }
  growth?: {
    revenue: number
    orders: number
    customers: number
    products: number
  }
}

export interface SalesAnalytics {
  period: {
    start: Date
    end: Date
  }
  
  // Revenue
  totalRevenue: Money
  netRevenue: Money
  grossProfit: Money
  grossMargin: number
  
  // Orders
  totalOrders: number
  averageOrderValue: Money
  medianOrderValue: Money
  
  // Trends
  dailySales: Array<{
    date: Date
    revenue: Money
    orders: number
    averageOrderValue: Money
  }>
  
  // Top performers
  topProducts: Array<{
    productId: string
    productName: string
    revenue: Money
    quantity: number
    orders: number
  }>
  
  topCategories: Array<{
    categoryId: string
    categoryName: string
    revenue: Money
    orders: number
  }>
  
  topCustomers: Array<{
    customerId: string
    customerName: string
    customerEmail: string
    revenue: Money
    orders: number
  }>
  
  // Geographic
  salesByCountry: Array<{
    country: string
    revenue: Money
    orders: number
    customers: number
  }>
  
  salesByRegion: Array<{
    region: string
    revenue: Money
    orders: number
  }>
}

export interface CustomerAnalytics {
  period: {
    start: Date
    end: Date
  }
  
  // Overview
  totalCustomers: number
  newCustomers: number
  returningCustomers: number
  activeCustomers: number
  
  // Lifetime value
  averageLifetimeValue: Money
  medianLifetimeValue: Money
  
  // Retention
  retentionRate: number
  churnRate: number
  
  // Segmentation
  customerSegments: Array<{
    segment: string
    customerCount: number
    revenue: Money
    averageOrderValue: Money
  }>
  
  // Acquisition
  acquisitionChannels: Array<{
    channel: string
    customerCount: number
    cost: Money
    revenue: Money
    roi: number
  }>
  
  // Cohort analysis
  cohorts: Array<{
    cohort: string
    size: number
    retentionRates: number[]
    revenue: Money[]
  }>
}

export interface AdminProductAnalytics {
  period: {
    start: Date
    end: Date
  }
  
  // Overview
  totalProducts: number
  activeProducts: number
  lowStockProducts: number
  outOfStockProducts: number
  
  // Performance
  topSellingProducts: Array<{
    productId: string
    productName: string
    quantity: number
    revenue: Money
    views: number
    conversionRate: number
  }>
  
  slowMovingProducts: Array<{
    productId: string
    productName: string
    daysSinceLastSale: number
    currentStock: number
    value: Money
  }>
  
  // Categories
  categoryPerformance: Array<{
    categoryId: string
    categoryName: string
    productCount: number
    revenue: Money
    averagePrice: Money
    conversionRate: number
  }>
  
  // Inventory
  inventoryValue: Money
  inventoryTurnover: number
  averageDaysOnHand: number
  
  // Pricing
  averageProductPrice: Money
  priceDistribution: Array<{
    range: string
    productCount: number
    revenue: Money
  }>
}

// System monitoring
export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical'
  uptime: number
  
  // Performance
  responseTime: number
  throughput: number
  errorRate: number
  
  // Resources
  cpuUsage: number
  memoryUsage: number
  diskUsage: number
  
  // Database
  databaseConnections: number
  databaseResponseTime: number
  
  // External services
  externalServices: Array<{
    name: string
    status: 'up' | 'down' | 'degraded'
    responseTime?: number
    lastChecked: Date
  }>
  
  // Alerts
  activeAlerts: SystemAlert[]
  
  lastUpdated: Date
}

export interface SystemAlert extends BaseEntity {
  type: 'error' | 'warning' | 'info'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  message: string
  source: string
  
  // Status
  status: 'active' | 'acknowledged' | 'resolved'
  
  // Dates
  triggeredAt: Date
  acknowledgedAt?: Date
  resolvedAt?: Date
  
  // Users
  acknowledgedBy?: string
  resolvedBy?: string
  
  // Metadata
  metadata?: Record<string, any>
}

// Activity logs
export interface AdminActivity extends BaseEntity {
  userId: string
  userName: string
  
  // Action
  action: string
  resource: string
  resourceId?: string
  
  // Details
  description: string
  changes?: Record<string, any>
  
  // Context
  ipAddress: string
  userAgent: string
  sessionId: string
  
  // Metadata
  metadata?: Record<string, any>
}

// Bulk operations
export interface BulkOperation extends BaseEntity {
  type: 'product_update' | 'product_delete' | 'order_update' | 'customer_update' | 'inventory_adjustment'
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  
  // Progress
  totalItems: number
  processedItems: number
  successfulItems: number
  failedItems: number
  
  // Data
  inputData: any
  results?: any
  errors?: Array<{
    item: any
    error: string
  }>
  
  // Execution
  startedAt?: Date
  completedAt?: Date
  estimatedCompletion?: Date
  
  // User
  createdBy: string
  
  // Metadata
  metadata?: Record<string, any>
}

// Data exports
export interface DataExport extends BaseEntity {
  type: 'products' | 'orders' | 'customers' | 'inventory' | 'analytics'
  format: 'csv' | 'xlsx' | 'json' | 'pdf'
  
  // Filters
  filters: Record<string, any>
  dateRange?: {
    start: Date
    end: Date
  }
  
  // Status
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'expired'
  
  // Results
  recordCount?: number
  fileSize?: number
  downloadUrl?: string
  expiresAt?: Date
  
  // Execution
  startedAt?: Date
  completedAt?: Date
  
  // User
  requestedBy: string
  
  // Metadata
  metadata?: Record<string, any>
}

// Settings management
export interface SystemSetting {
  key: string
  value: any
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  category: string
  description?: string
  isPublic: boolean
  isEditable: boolean
  validationRules?: Record<string, any>
  updatedAt: Date
  updatedBy: string
}

export interface SettingsGroup {
  category: string
  name: string
  description?: string
  settings: SystemSetting[]
}

// Notifications
export interface AdminNotification extends BaseEntity {
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  
  // Targeting
  userId?: string
  roleIds?: string[]
  isGlobal: boolean
  
  // Status
  isRead: boolean
  readAt?: Date
  
  // Action
  actionUrl?: string
  actionText?: string
  
  // Scheduling
  scheduledAt?: Date
  expiresAt?: Date
  
  // Metadata
  metadata?: Record<string, any>
}

// API management
export interface ApiKey extends BaseEntity {
  name: string
  key: string
  secret?: string
  
  // Permissions
  permissions: string[]
  allowedIps?: string[]
  
  // Usage
  usageCount: number
  lastUsedAt?: Date
  
  // Limits
  rateLimit?: number
  dailyLimit?: number
  monthlyLimit?: number
  
  // Status
  isActive: boolean
  
  // Dates
  expiresAt?: Date
  
  // User
  createdBy: string
  
  // Metadata
  metadata?: Record<string, any>
}

export interface ApiUsage extends BaseEntity {
  apiKeyId: string
  endpoint: string
  method: string
  statusCode: number
  responseTime: number
  ipAddress: string
  userAgent?: string
  requestSize?: number
  responseSize?: number
  timestamp: Date
}

// Webhooks management
export interface WebhookEndpoint extends BaseEntity {
  name: string
  url: string
  
  // Events
  events: string[]
  
  // Security
  secret?: string
  
  // Status
  isActive: boolean
  
  // Delivery
  deliveryAttempts: number
  lastDeliveryAt?: Date
  lastDeliveryStatus?: 'success' | 'failed'
  
  // Metadata
  metadata?: Record<string, any>
}

export interface WebhookDelivery extends BaseEntity {
  webhookEndpointId: string
  eventType: string
  payload: any
  
  // Delivery
  status: 'pending' | 'success' | 'failed'
  attempts: number
  nextAttemptAt?: Date
  
  // Response
  responseStatus?: number
  responseBody?: string
  responseTime?: number
  
  // Error
  errorMessage?: string
  
  // Metadata
  metadata?: Record<string, any>
}

// Input types
export interface CreateAdminUserInput {
  email: string
  firstName: string
  lastName: string
  password: string
  roleIds: string[]
  isActive?: boolean
  metadata?: Record<string, any>
}

export interface UpdateAdminUserInput extends Partial<Omit<CreateAdminUserInput, 'password'>> {
  id: string
  currentPassword?: string
  newPassword?: string
}

export interface AdminFilters {
  isActive?: boolean
  roleIds?: string[]
  lastLoginAfter?: Date
  lastLoginBefore?: Date
  createdAfter?: Date
  createdBefore?: Date
}

export interface AdminSearchParams {
  query?: string
  filters?: AdminFilters
  sort?: {
    field: 'email' | 'firstName' | 'lastName' | 'lastLoginAt' | 'createdAt'
    direction: 'asc' | 'desc'
  }
  page?: number
  limit?: number
}

// Analytics types for the analytics service
export type AnalyticsTimeRange = '7d' | '30d' | '90d' | '1y'

export interface AnalyticsData {
  timeRange: AnalyticsTimeRange
  period: {
    start: Date
    end: Date
  }
  sales: SalesAnalytics
  products: ProductAnalytics
  customers: SimpleCustomerAnalytics
  orders: any
  generatedAt: Date
}

export interface SalesAnalytics {
  totalRevenue: number
  totalOrders: number
  totalCustomers: number
  avgOrderValue: number
  revenueGrowth: number
  orderGrowth: number
  customerGrowth: number
  avgOrderValueGrowth: number
  dailyData: Array<{
    date: string
    revenue: number
    orders: number
    customers: number
    avgOrderValue: number
  }>
}

export interface ProductAnalytics {
  topProducts: Array<{
    id: string
    title: string
    handle: string
    totalQuantity: number
    totalRevenue: number
    orderCount: number
  }>
  categoryPerformance: Array<{
    category: string
    productCount: number
    totalQuantity: number
    totalRevenue: number
  }>
  inventoryInsights: {
    totalProducts: number
    totalInventory: number
    avgInventoryPerProduct: number
    lowStockProducts: number
  }
}

// Simplified customer analytics for the analytics service (using numbers instead of Money)
export interface SimpleCustomerAnalytics {
  totalCustomers: number
  newCustomers: number
  returningCustomers: number
  activeCustomers: number
  averageLifetimeValue: number
  medianLifetimeValue: number
  retentionRate: number
  churnRate: number
  customerSegments: Array<{
    segment: string
    customerCount: number
    revenue: number
    averageOrderValue: number
  }>
  topCustomers: Array<{
    customerId: string
    customerEmail: string
    customerName: string
    totalSpent: number
    orderCount: number
  }>
}

// Reports types
export interface ReportData {
  timeRange: AnalyticsTimeRange
  period: {
    start: Date
    end: Date
  }
  salesReport: SalesReport
  customerReport: CustomerReport
  inventoryReport: InventoryReport
  generatedAt: Date
}

export interface SalesReport {
  totalRevenue: number
  totalOrders: number
  averageOrderValue: number
  conversionRate: number
  revenueGrowth: number
  orderGrowth: number
  topSellingProducts: Array<{
    id: string
    title: string
    handle: string
    quantity: number
    revenue: number
    orderCount: number
  }>
  salesByPeriod: Array<{
    period: string
    revenue: number
    orders: number
    customers: number
  }>
  categoryPerformance: Array<{
    category: string
    revenue: number
    orders: number
    products: number
  }>
}

export interface CustomerReport {
  totalCustomers: number
  newCustomers: number
  returningCustomers: number
  activeCustomers: number
  customerLifetimeValue: number
  customerGrowth: number
  retentionRate: number
  churnRate: number
  customerSegments: Array<{
    segment: string
    customerCount: number
    revenue: number
    percentage: number
  }>
  customersByRegion: Array<{
    region: string
    customers: number
    percentage: number
  }>
  customerAcquisition: Array<{
    month: string
    new: number
    returning: number
  }>
  topCustomers: Array<{
    customerId: string
    customerEmail: string
    customerName: string
    totalSpent: number
    orderCount: number
  }>
}

export interface InventoryReport {
  totalProducts: number
  lowStockItems: number
  outOfStockItems: number
  inventoryValue: number
  inventoryTurnover: number
  stockHealth: number
  topCategories: Array<{
    category: string
    products: number
    value: number
    percentage: number
  }>
  stockMovement: Array<{
    month: string
    inbound: number
    outbound: number
  }>
  slowMovingItems: Array<{
    id: string
    title: string
    handle: string
    quantitySold: number
  }>
  fastMovingItems: Array<{
    id: string
    title: string
    handle: string
    quantitySold: number
  }>
}
