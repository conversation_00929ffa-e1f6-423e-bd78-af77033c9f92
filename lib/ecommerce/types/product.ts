// Product-related types and interfaces

import { BaseEntity, Money } from './base'

export interface ProductImage {
  id: string
  url: string
  altText?: string
  position: number
  width?: number
  height?: number
}

export interface ProductVariant {
  id: string
  productId: string
  sku: string
  title: string
  price: Money
  compareAtPrice?: Money
  costPerItem?: Money
  weight?: number
  weightUnit?: string
  inventoryQuantity: number
  inventoryPolicy: 'deny' | 'continue'
  fulfillmentService: string
  inventoryManagement: boolean
  options: ProductVariantOption[]
  image?: ProductImage
  available: boolean
  barcode?: string
  taxable: boolean
  requiresShipping: boolean
  trackQuantity: boolean
  continueSellingWhenOutOfStock: boolean
  metafields?: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

export interface ProductVariantOption {
  name: string
  value: string
}

export interface ProductOption {
  id: string
  name: string
  position: number
  values: string[]
}

export interface ProductCategory {
  id: string
  name: string
  slug: string
  description?: string
  image?: string
  parentId?: string
  children?: ProductCategory[]
  position: number
  isVisible: boolean
  seoTitle?: string
  seoDescription?: string
  createdAt: Date
  updatedAt: Date
}

export interface ProductTag {
  id: string
  name: string
  slug: string
  description?: string
}

export interface ProductCollection {
  id: string
  title: string
  slug: string
  description?: string
  image?: string
  sortOrder: 'manual' | 'best-selling' | 'created' | 'price-asc' | 'price-desc'
  isVisible: boolean
  conditions?: ProductCollectionCondition[]
  productIds?: string[]
  seoTitle?: string
  seoDescription?: string
  createdAt: Date
  updatedAt: Date
}

export interface ProductCollectionCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'starts_with' | 'ends_with' | 'greater_than' | 'less_than'
  value: string
}

export interface ProductReview {
  id: string
  productId: string
  userId: string
  userName: string
  userEmail: string
  rating: number
  title?: string
  content: string
  isVerified: boolean
  isApproved: boolean
  helpfulCount: number
  images?: string[]
  createdAt: Date
  updatedAt: Date
}

export interface ProductSeo {
  title?: string
  description?: string
  keywords?: string[]
  canonicalUrl?: string
  ogTitle?: string
  ogDescription?: string
  ogImage?: string
  twitterTitle?: string
  twitterDescription?: string
  twitterImage?: string
}

export interface Product extends BaseEntity {
  title: string
  slug: string
  description: string
  descriptionHtml?: string
  vendor?: string
  productType?: string
  handle: string
  status: 'active' | 'draft' | 'archived'
  publishedAt?: Date
  
  // Pricing
  price: Money
  compareAtPrice?: Money
  costPerItem?: Money
  
  // Inventory
  trackQuantity: boolean
  continueSellingWhenOutOfStock: boolean
  inventoryQuantity: number
  
  // Physical properties
  weight?: number
  weightUnit?: string
  dimensions?: {
    length?: number
    width?: number
    height?: number
    unit?: string
  }
  
  // Media
  images: ProductImage[]
  featuredImage?: ProductImage
  
  // Variants and options
  hasVariants: boolean
  variants: ProductVariant[]
  options: ProductOption[]
  
  // Organization
  categories: ProductCategory[]
  tags: ProductTag[]
  collections: ProductCollection[]
  
  // SEO
  seo: ProductSeo
  
  // Reviews
  reviews?: ProductReview[]
  averageRating?: number
  reviewCount?: number
  
  // Metadata
  metafields?: Record<string, any>
  isGiftCard: boolean
  requiresShipping: boolean
  isTaxable: boolean
  
  // Visibility
  isVisible: boolean
  isAvailable: boolean
  availableForSale: boolean
  
  // Related products
  relatedProductIds?: string[]
  crossSellProductIds?: string[]
  upSellProductIds?: string[]
}

// Product creation and update interfaces
export interface CreateProductInput {
  title: string
  slug?: string
  description: string
  descriptionHtml?: string
  vendor?: string
  productType?: string
  price: Money
  compareAtPrice?: Money
  costPerItem?: Money
  trackQuantity?: boolean
  continueSellingWhenOutOfStock?: boolean
  inventoryQuantity?: number
  weight?: number
  weightUnit?: string
  dimensions?: Product['dimensions']
  images?: Omit<ProductImage, 'id'>[]
  variants?: Omit<ProductVariant, 'id' | 'productId' | 'createdAt' | 'updatedAt'>[]
  options?: Omit<ProductOption, 'id'>[]
  categoryIds?: string[]
  tagIds?: string[]
  collectionIds?: string[]
  seo?: ProductSeo
  metafields?: Record<string, any>
  isGiftCard?: boolean
  requiresShipping?: boolean
  isTaxable?: boolean
  status?: Product['status']
  isVisible?: boolean
}

export interface UpdateProductInput extends Partial<CreateProductInput> {
  id: string
}

// Product query interfaces
export interface ProductFilters {
  categoryIds?: string[]
  tagIds?: string[]
  collectionIds?: string[]
  vendor?: string
  productType?: string
  priceRange?: {
    min?: number
    max?: number
  }
  inStock?: boolean
  onSale?: boolean
  isVisible?: boolean
  status?: Product['status'][]
}

export interface ProductSortOptions {
  field: 'title' | 'price' | 'createdAt' | 'updatedAt' | 'inventoryQuantity' | 'averageRating'
  direction: 'asc' | 'desc'
}

export interface ProductSearchParams {
  query?: string
  filters?: ProductFilters
  sort?: ProductSortOptions
  page?: number
  limit?: number
}

// Product analytics
export interface ProductPerformanceAnalytics {
  productId: string
  views: number
  addToCartCount: number
  purchaseCount: number
  revenue: Money
  conversionRate: number
  averageOrderValue: Money
  returnRate: number
  period: {
    start: Date
    end: Date
  }
}

// Product recommendations
export interface ProductRecommendation {
  productId: string
  score: number
  reason: 'similar' | 'frequently_bought_together' | 'trending' | 'personalized'
  metadata?: Record<string, any>
}

// Product availability
export interface ProductAvailability {
  productId: string
  variantId?: string
  available: boolean
  quantity: number
  nextAvailableDate?: Date
  estimatedRestockDate?: Date
}

// Product pricing rules
export interface ProductPricingRule {
  id: string
  name: string
  productIds?: string[]
  categoryIds?: string[]
  customerGroupIds?: string[]
  discountType: 'percentage' | 'fixed_amount'
  discountValue: number
  minQuantity?: number
  maxQuantity?: number
  startDate?: Date
  endDate?: Date
  isActive: boolean
}

// Bulk operations
export interface BulkProductOperation {
  operation: 'update' | 'delete' | 'publish' | 'unpublish'
  productIds: string[]
  data?: Partial<UpdateProductInput>
}

export interface BulkProductResult {
  success: boolean
  processedCount: number
  failedCount: number
  errors?: Array<{
    productId: string
    error: string
  }>
}

// Product Attributes
export interface ProductAttribute {
  id: string
  name: string
  slug: string
  type: 'text' | 'number' | 'boolean' | 'select' | 'multiselect' | 'color' | 'image'
  description?: string
  isRequired: boolean
  isVariant: boolean
  isFilter: boolean
  position: number
  options: string[]
  validation?: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

export interface ProductAttributeValue {
  id: string
  productId: string
  attributeId: string
  value: string
  attribute?: ProductAttribute
  createdAt: Date
  updatedAt: Date
}

// Product Bundles
export interface ProductBundle {
  id: string
  title: string
  slug: string
  description?: string
  price: Money
  compareAtPrice?: Money
  isActive: boolean
  items: ProductBundleItem[]
  createdAt: Date
  updatedAt: Date
}

export interface ProductBundleItem {
  id: string
  bundleId: string
  productId: string
  variantId?: string
  quantity: number
  discount?: number
  position: number
  product?: Product
  variant?: ProductVariant
  createdAt: Date
  updatedAt: Date
}

// Product Relations
export interface ProductRelation {
  id: string
  productId: string
  relatedProductId: string
  type: 'related' | 'cross_sell' | 'up_sell'
  position: number
  relatedProduct?: Product
  createdAt: Date
  updatedAt: Date
}

// Enhanced Product interface with new relations
export interface EnhancedProduct extends Product {
  attributeValues?: ProductAttributeValue[]
  bundleItems?: ProductBundleItem[]
  relations?: ProductRelation[]
  relatedTo?: ProductRelation[]
}

// Variant creation and management
export interface CreateVariantInput {
  title: string
  sku?: string
  price: Money
  compareAtPrice?: Money
  costPerItem?: Money
  weight?: number
  weightUnit?: string
  inventoryQuantity?: number
  inventoryPolicy?: 'deny' | 'continue'
  fulfillmentService?: string
  inventoryManagement?: boolean
  options?: ProductVariantOption[]
  barcode?: string
  taxable?: boolean
  requiresShipping?: boolean
  trackQuantity?: boolean
  continueSellingWhenOutOfStock?: boolean
  metafields?: Record<string, any>
}

export interface UpdateVariantInput extends Partial<CreateVariantInput> {
  id: string
}

// Bulk variant operations
export interface BulkVariantOperation {
  operation: 'create' | 'update' | 'delete'
  productId: string
  variants: CreateVariantInput[] | UpdateVariantInput[]
}

export interface BulkVariantResult {
  success: boolean
  processedCount: number
  failedCount: number
  errors?: Array<{
    variantId?: string
    sku?: string
    error: string
  }>
}

// Product attribute management
export interface CreateAttributeInput {
  name: string
  slug?: string
  type: ProductAttribute['type']
  description?: string
  isRequired?: boolean
  isVariant?: boolean
  isFilter?: boolean
  position?: number
  options?: string[]
  validation?: Record<string, any>
}

export interface UpdateAttributeInput extends Partial<CreateAttributeInput> {
  id: string
}

// Product bundle management
export interface CreateBundleInput {
  title: string
  slug?: string
  description?: string
  price: Money
  compareAtPrice?: Money
  isActive?: boolean
  items: Array<{
    productId: string
    variantId?: string
    quantity: number
    discount?: number
    position?: number
  }>
}

export interface UpdateBundleInput extends Partial<CreateBundleInput> {
  id: string
}
