// Base types for the e-commerce library

export interface BaseEntity {
  id: string
  createdAt: Date
  updatedAt: Date
}

export interface Money {
  amount: number
  currency: string
}

export interface Address {
  firstName: string
  lastName: string
  company?: string
  address1: string
  address2?: string
  city: string
  province: string
  country: string
  postalCode: string
  phone?: string
}

export interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: PaginationInfo
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
  }
}

export interface ApiError {
  code: string
  message: string
  statusCode?: number
}

// Custom error classes
export class NotFoundError extends Error {
  constructor(resource: string, id: string) {
    super(`${resource} with ID '${id}' not found`)
    this.name = 'NotFoundError'
  }
}

export class ValidationError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'ValidationError'
  }
}

export class InsufficientStockError extends Error {
  constructor(productId: string, requested: number, available: number) {
    super(`Insufficient stock for product ${productId}. Requested: ${requested}, Available: ${available}`)
    this.name = 'InsufficientStockError'
  }
}

export class UnauthorizedError extends Error {
  constructor(message: string = 'Unauthorized access') {
    super(message)
    this.name = 'UnauthorizedError'
  }
}

export class ForbiddenError extends Error {
  constructor(message: string = 'Access forbidden') {
    super(message)
    this.name = 'ForbiddenError'
  }
}

// Event types
export interface BaseEvent {
  id: string
  type: string
  timestamp: Date
  userId?: string
  sessionId?: string
  metadata?: Record<string, any>
}

// Audit log
export interface AuditLog extends BaseEntity {
  userId?: string
  action: string
  resource: string
  resourceId?: string
  oldValues?: Record<string, any>
  newValues?: Record<string, any>
  ipAddress?: string
  userAgent?: string
}

// File upload
export interface FileUpload {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  uploadedAt: Date
  uploadedBy?: string
}

// Search and filter base types
export interface BaseFilters {
  createdAfter?: Date
  createdBefore?: Date
  updatedAfter?: Date
  updatedBefore?: Date
}

export interface BaseSortOptions {
  field: string
  direction: 'asc' | 'desc'
}

export interface BaseSearchParams {
  query?: string
  page?: number
  limit?: number
}

// Metadata
export interface Metafield {
  key: string
  value: any
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  namespace?: string
  description?: string
}

// Location/Geography
export interface Coordinates {
  latitude: number
  longitude: number
}

export interface Location extends Address {
  coordinates?: Coordinates
  timezone?: string
}

// Contact information
export interface ContactInfo {
  email?: string
  phone?: string
  website?: string
  socialMedia?: {
    facebook?: string
    twitter?: string
    instagram?: string
    linkedin?: string
  }
}

// Business hours
export interface BusinessHours {
  monday?: { open: string; close: string; closed?: boolean }
  tuesday?: { open: string; close: string; closed?: boolean }
  wednesday?: { open: string; close: string; closed?: boolean }
  thursday?: { open: string; close: string; closed?: boolean }
  friday?: { open: string; close: string; closed?: boolean }
  saturday?: { open: string; close: string; closed?: boolean }
  sunday?: { open: string; close: string; closed?: boolean }
}

// Notification preferences
export interface NotificationPreferences {
  email: boolean
  sms: boolean
  push: boolean
  marketing: boolean
  orderUpdates: boolean
  promotions: boolean
  newsletter: boolean
}

// Feature flags
export interface FeatureFlag {
  key: string
  enabled: boolean
  description?: string
  conditions?: Array<{
    type: 'user_id' | 'user_group' | 'percentage' | 'date_range'
    value: any
  }>
}

// Rate limiting
export interface RateLimit {
  requests: number
  window: number // in seconds
  identifier: string // IP, user ID, etc.
}

// Cache configuration
export interface CacheConfig {
  ttl: number // time to live in seconds
  key: string
  tags?: string[]
}

// Webhook
export interface WebhookEvent {
  id: string
  type: string
  data: any
  timestamp: Date
  retryCount?: number
  status: 'pending' | 'delivered' | 'failed'
}

// Health check
export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: Date
  services: Array<{
    name: string
    status: 'up' | 'down' | 'degraded'
    responseTime?: number
    error?: string
  }>
}

// Configuration
export interface AppConfig {
  name: string
  version: string
  environment: 'development' | 'staging' | 'production'
  features: Record<string, boolean>
  limits: {
    maxFileSize: number
    maxRequestSize: number
    rateLimit: RateLimit
  }
}
