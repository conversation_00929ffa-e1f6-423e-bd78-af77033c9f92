// Inventory management related types and interfaces

import { BaseEntity, Money } from './base'

export interface InventoryItem extends BaseEntity {
  // Product identification
  productId: string
  variantId?: string
  sku: string
  
  // Basic information
  name: string
  description?: string
  
  // Stock levels
  quantity: number
  reservedQuantity: number
  availableQuantity: number
  committedQuantity: number
  
  // Thresholds
  lowStockThreshold: number
  outOfStockThreshold: number
  reorderPoint: number
  reorderQuantity: number
  maxStockLevel?: number
  
  // Costing
  costPrice: Money
  averageCost: Money
  lastCostPrice?: Money
  
  // Location
  locationId: string
  binLocation?: string
  zone?: string
  aisle?: string
  shelf?: string
  
  // Tracking
  trackQuantity: boolean
  allowBackorders: boolean
  continueSellingWhenOutOfStock: boolean
  
  // Batch/Serial tracking
  batchTracked: boolean
  serialTracked: boolean
  expiryTracked: boolean
  
  // Status
  status: 'active' | 'inactive' | 'discontinued'
  
  // Dates
  lastStockUpdate: Date
  lastCountDate?: Date
  nextCountDate?: Date
  
  // Supplier information
  supplierId?: string
  supplierSku?: string
  leadTime?: number // in days
  
  // Physical properties
  weight?: number
  weightUnit?: string
  dimensions?: {
    length?: number
    width?: number
    height?: number
    unit?: string
  }
  
  // Metadata
  attributes?: Record<string, any>
  tags?: string[]
  notes?: string
}

export interface InventoryLocation extends BaseEntity {
  name: string
  code: string
  type: 'warehouse' | 'store' | 'dropship' | 'supplier' | 'virtual'
  
  // Address
  address?: {
    street: string
    city: string
    state: string
    country: string
    postalCode: string
  }
  
  // Contact
  contactName?: string
  contactEmail?: string
  contactPhone?: string
  
  // Configuration
  isActive: boolean
  isPrimary: boolean
  allowsInventory: boolean
  allowsFulfillment: boolean
  
  // Capacity
  maxCapacity?: number
  currentUtilization?: number
  
  // Operating hours
  operatingHours?: Array<{
    day: string
    open: string
    close: string
  }>
  
  // Metadata
  attributes?: Record<string, any>
  notes?: string
}

export interface InventoryMovement extends BaseEntity {
  // Reference
  inventoryItemId: string
  locationId: string
  
  // Movement details
  type: 'adjustment' | 'sale' | 'purchase' | 'transfer' | 'return' | 'damage' | 'theft' | 'count' | 'reservation' | 'release'
  direction: 'in' | 'out'
  quantity: number
  
  // Pricing
  unitCost?: Money
  totalCost?: Money
  
  // References
  referenceType?: 'order' | 'purchase_order' | 'transfer' | 'adjustment' | 'count'
  referenceId?: string
  referenceNumber?: string
  
  // Batch/Serial
  batchNumber?: string
  serialNumbers?: string[]
  expiryDate?: Date
  
  // Reason
  reason: string
  reasonCode?: string
  
  // User
  userId?: string
  userName?: string
  
  // Metadata
  notes?: string
  attributes?: Record<string, any>
}

export interface InventoryAdjustment extends BaseEntity {
  // Reference
  adjustmentNumber: string
  locationId: string
  
  // Details
  reason: 'count_discrepancy' | 'damage' | 'theft' | 'expiry' | 'quality_control' | 'other'
  reasonDetails?: string
  
  // Items
  items: InventoryAdjustmentItem[]
  
  // Status
  status: 'draft' | 'pending' | 'approved' | 'rejected' | 'applied'
  
  // Approval
  requiresApproval: boolean
  approvedBy?: string
  approvedAt?: Date
  rejectedBy?: string
  rejectedAt?: Date
  rejectionReason?: string
  
  // User
  createdBy: string
  
  // Metadata
  notes?: string
  attachments?: string[]
}

export interface InventoryAdjustmentItem {
  id: string
  inventoryItemId: string
  expectedQuantity: number
  actualQuantity: number
  adjustmentQuantity: number
  reason?: string
  unitCost?: Money
  totalCost?: Money
  batchNumber?: string
  serialNumbers?: string[]
}

export interface InventoryCount extends BaseEntity {
  // Reference
  countNumber: string
  locationId: string
  
  // Type
  type: 'full' | 'partial' | 'cycle' | 'spot'
  
  // Scope
  includeAllItems: boolean
  categoryIds?: string[]
  productIds?: string[]
  tags?: string[]
  
  // Status
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled'
  
  // Dates
  scheduledDate: Date
  startedAt?: Date
  completedAt?: Date
  
  // Results
  itemsExpected: number
  itemsCounted: number
  discrepanciesFound: number
  totalVarianceValue?: Money
  
  // Team
  assignedTo?: string[]
  countedBy?: string[]
  
  // Items
  items: InventoryCountItem[]
  
  // Metadata
  notes?: string
  instructions?: string
}

export interface InventoryCountItem {
  id: string
  inventoryItemId: string
  expectedQuantity: number
  countedQuantity?: number
  variance?: number
  varianceValue?: Money
  status: 'pending' | 'counted' | 'discrepancy' | 'verified'
  countedBy?: string
  countedAt?: Date
  notes?: string
  batchNumber?: string
  serialNumbers?: string[]
}

export interface InventoryTransfer extends BaseEntity {
  // Reference
  transferNumber: string
  
  // Locations
  fromLocationId: string
  toLocationId: string
  
  // Status
  status: 'draft' | 'pending' | 'in_transit' | 'received' | 'cancelled'
  
  // Items
  items: InventoryTransferItem[]
  
  // Dates
  requestedDate: Date
  shippedDate?: Date
  expectedDate?: Date
  receivedDate?: Date
  
  // Shipping
  trackingNumber?: string
  carrier?: string
  shippingCost?: Money
  
  // Users
  requestedBy: string
  shippedBy?: string
  receivedBy?: string
  
  // Metadata
  reason?: string
  notes?: string
  attachments?: string[]
}

export interface InventoryTransferItem {
  id: string
  inventoryItemId: string
  requestedQuantity: number
  shippedQuantity?: number
  receivedQuantity?: number
  damagedQuantity?: number
  unitCost?: Money
  batchNumber?: string
  serialNumbers?: string[]
  notes?: string
}

export interface InventoryReservation extends BaseEntity {
  // Reference
  inventoryItemId: string
  locationId: string
  
  // Reservation details
  quantity: number
  reservationType: 'order' | 'quote' | 'hold' | 'allocation'
  
  // References
  referenceType: 'order' | 'quote' | 'manual'
  referenceId: string
  referenceNumber?: string
  
  // Status
  status: 'active' | 'fulfilled' | 'cancelled' | 'expired'
  
  // Dates
  expiresAt?: Date
  fulfilledAt?: Date
  cancelledAt?: Date
  
  // User
  reservedBy: string
  
  // Metadata
  reason?: string
  notes?: string
}

export interface InventoryAlert extends BaseEntity {
  // Type
  type: 'low_stock' | 'out_of_stock' | 'overstock' | 'expiry_warning' | 'reorder_point' | 'negative_stock'
  severity: 'low' | 'medium' | 'high' | 'critical'
  
  // Item
  inventoryItemId: string
  locationId: string
  
  // Details
  message: string
  currentValue?: number
  thresholdValue?: number
  
  // Status
  status: 'active' | 'acknowledged' | 'resolved' | 'dismissed'
  
  // Dates
  triggeredAt: Date
  acknowledgedAt?: Date
  resolvedAt?: Date
  dismissedAt?: Date
  
  // Users
  acknowledgedBy?: string
  resolvedBy?: string
  dismissedBy?: string
  
  // Actions
  suggestedActions?: string[]
  actionsTaken?: string[]
  
  // Metadata
  metadata?: Record<string, any>
}

// Input types for operations
export interface CreateInventoryItemInput {
  productId: string
  variantId?: string
  sku: string
  name: string
  description?: string
  quantity: number
  lowStockThreshold: number
  reorderPoint: number
  reorderQuantity: number
  costPrice: Money
  locationId: string
  trackQuantity?: boolean
  allowBackorders?: boolean
  batchTracked?: boolean
  serialTracked?: boolean
  supplierId?: string
  supplierSku?: string
  leadTime?: number
  attributes?: Record<string, any>
  tags?: string[]
  notes?: string
}

export interface UpdateInventoryItemInput extends Partial<CreateInventoryItemInput> {
  id: string
}

export interface AdjustInventoryInput {
  inventoryItemId: string
  quantity: number
  reason: string
  reasonCode?: string
  unitCost?: Money
  batchNumber?: string
  serialNumbers?: string[]
  notes?: string
}

export interface ReserveInventoryInput {
  inventoryItemId: string
  quantity: number
  reservationType: InventoryReservation['reservationType']
  referenceType: InventoryReservation['referenceType']
  referenceId: string
  referenceNumber?: string
  expiresAt?: Date
  reason?: string
  notes?: string
}

export interface TransferInventoryInput {
  fromLocationId: string
  toLocationId: string
  items: Array<{
    inventoryItemId: string
    quantity: number
    batchNumber?: string
    serialNumbers?: string[]
  }>
  reason?: string
  notes?: string
  expectedDate?: Date
}

// Query interfaces
export interface InventoryFilters {
  locationIds?: string[]
  productIds?: string[]
  categoryIds?: string[]
  supplierIds?: string[]
  status?: InventoryItem['status'][]
  lowStock?: boolean
  outOfStock?: boolean
  overStock?: boolean
  batchTracked?: boolean
  serialTracked?: boolean
  tags?: string[]
}

export interface InventorySearchParams {
  query?: string
  filters?: InventoryFilters
  sort?: {
    field: 'name' | 'sku' | 'quantity' | 'lowStockThreshold' | 'lastStockUpdate'
    direction: 'asc' | 'desc'
  }
  page?: number
  limit?: number
}

// Analytics
export interface InventoryAnalytics {
  period: {
    start: Date
    end: Date
  }
  totalItems: number
  totalValue: Money
  lowStockItems: number
  outOfStockItems: number
  overstockItems: number
  turnoverRate: number
  averageDaysOnHand: number
  stockAccuracy: number
  topMovingItems: Array<{
    inventoryItemId: string
    name: string
    movementCount: number
    totalMovement: number
  }>
  slowMovingItems: Array<{
    inventoryItemId: string
    name: string
    daysSinceLastMovement: number
  }>
  locationUtilization: Array<{
    locationId: string
    locationName: string
    utilization: number
    capacity: number
  }>
}

// Forecasting
export interface InventoryForecast {
  inventoryItemId: string
  locationId: string
  period: {
    start: Date
    end: Date
  }
  predictedDemand: number
  recommendedOrderQuantity: number
  recommendedOrderDate: Date
  confidence: number
  factors: Array<{
    name: string
    impact: number
    description: string
  }>
}

// Supplier integration
export interface SupplierCatalogItem {
  supplierId: string
  supplierSku: string
  supplierName: string
  productName: string
  description?: string
  unitPrice: Money
  minimumOrderQuantity: number
  leadTime: number
  availability: 'in_stock' | 'limited' | 'out_of_stock' | 'discontinued'
  lastUpdated: Date
}

export interface PurchaseOrder extends BaseEntity {
  orderNumber: string
  supplierId: string
  locationId: string
  status: 'draft' | 'sent' | 'confirmed' | 'partially_received' | 'received' | 'cancelled'
  items: PurchaseOrderItem[]
  subtotal: Money
  tax: Money
  shipping: Money
  total: Money
  expectedDate?: Date
  receivedDate?: Date
  notes?: string
}

export interface PurchaseOrderItem {
  id: string
  inventoryItemId: string
  supplierSku: string
  description: string
  orderedQuantity: number
  receivedQuantity: number
  unitPrice: Money
  totalPrice: Money
}

// Batch and serial tracking
export interface InventoryBatch extends BaseEntity {
  batchNumber: string
  inventoryItemId: string
  locationId: string
  quantity: number
  availableQuantity: number
  costPrice: Money
  expiryDate?: Date
  manufactureDate?: Date
  supplierId?: string
  supplierBatchNumber?: string
  status: 'active' | 'expired' | 'recalled' | 'quarantined'
  attributes?: Record<string, any>
}

export interface InventorySerial extends BaseEntity {
  serialNumber: string
  inventoryItemId: string
  locationId: string
  batchId?: string
  status: 'available' | 'reserved' | 'sold' | 'returned' | 'damaged'
  costPrice: Money
  manufactureDate?: Date
  warrantyExpiry?: Date
  attributes?: Record<string, any>
}
