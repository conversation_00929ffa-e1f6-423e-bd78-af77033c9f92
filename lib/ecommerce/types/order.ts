// Order management related types and interfaces

import { BaseEntity, Money, Address } from './base'
import { CartItem, AppliedDiscount, TaxLine } from './cart'
import { User } from './user'

export interface Order extends BaseEntity {
  // Identification
  orderNumber: string
  cartId?: string
  userId?: string
  
  // Customer information
  customer?: {
    id?: string
    email: string
    firstName?: string
    lastName?: string
    phone?: string
  }
  
  // Addresses
  billingAddress: Address
  shippingAddress: Address
  
  // Items
  items: OrderItem[]
  itemCount: number
  
  // Pricing
  subtotal: Money
  totalDiscount: Money
  totalTax: Money
  totalShipping: Money
  totalTip: Money
  total: Money
  
  // Discounts
  appliedDiscounts: AppliedDiscount[]
  
  // Tax
  taxLines: TaxLine[]
  taxIncluded: boolean
  
  // Shipping
  shippingMethod: {
    id: string
    title: string
    price: Money
    carrier?: string
    service?: string
    trackingNumber?: string
    trackingUrl?: string
    estimatedDelivery?: Date
    actualDelivery?: Date
  }
  
  // Payment
  paymentStatus: 'pending' | 'authorized' | 'partially_paid' | 'paid' | 'partially_refunded' | 'refunded' | 'voided'
  paymentMethod?: {
    type: 'credit_card' | 'debit_card' | 'paypal' | 'bank_transfer' | 'cash_on_delivery' | 'store_credit' | 'gift_card'
    last4?: string
    brand?: string
    gateway?: string
  }
  
  // Fulfillment
  fulfillmentStatus: 'unfulfilled' | 'partially_fulfilled' | 'fulfilled' | 'shipped' | 'delivered' | 'returned' | 'cancelled'
  fulfillments: OrderFulfillment[]
  
  // Status
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded' | 'returned'
  financialStatus: 'pending' | 'authorized' | 'partially_paid' | 'paid' | 'partially_refunded' | 'refunded' | 'voided'
  
  // Dates
  confirmedAt?: Date
  processedAt?: Date
  shippedAt?: Date
  deliveredAt?: Date
  cancelledAt?: Date
  
  // Communication
  customerNote?: string
  internalNotes?: string[]
  
  // Metadata
  currency: string
  locale?: string
  source: 'web' | 'mobile' | 'pos' | 'api' | 'admin'
  sourceIdentifier?: string
  attributes?: Record<string, any>
  tags?: string[]
  
  // Risk assessment
  riskLevel: 'low' | 'medium' | 'high'
  riskReasons?: string[]
  
  // Returns and refunds
  returns?: OrderReturn[]
  refunds?: OrderRefund[]
  
  // Analytics
  acquisitionChannel?: string
  referrer?: string
  utmSource?: string
  utmMedium?: string
  utmCampaign?: string
}

export interface OrderItem {
  id: string
  orderId: string
  productId: string
  variantId?: string
  quantity: number
  unitPrice: Money
  totalPrice: Money
  
  // Product information (snapshot at time of order)
  productTitle: string
  productSlug: string
  productImage?: string
  variantTitle?: string
  variantOptions?: Array<{
    name: string
    value: string
  }>
  sku?: string
  
  // Fulfillment
  fulfillmentStatus: 'unfulfilled' | 'fulfilled' | 'shipped' | 'delivered' | 'returned' | 'cancelled'
  fulfillableQuantity: number
  fulfilledQuantity: number
  
  // Pricing
  compareAtPrice?: Money
  discountAmount?: Money
  discountReason?: string
  
  // Physical properties
  weight?: number
  weightUnit?: string
  requiresShipping: boolean
  isTaxable: boolean
  
  // Customization
  customAttributes?: Record<string, any>
  personalizedMessage?: string
  giftWrap?: boolean
  
  // Returns and refunds
  returnableQuantity: number
  refundableQuantity: number
  
  // Metadata
  vendor?: string
  productType?: string
}

export interface OrderFulfillment extends BaseEntity {
  orderId: string
  status: 'pending' | 'open' | 'success' | 'cancelled' | 'error' | 'failure'
  
  // Items
  items: OrderFulfillmentItem[]
  
  // Shipping
  trackingNumber?: string
  trackingUrl?: string
  trackingCompany?: string
  
  // Location
  locationId?: string
  locationName?: string
  
  // Dates
  shippedAt?: Date
  estimatedDeliveryAt?: Date
  deliveredAt?: Date
  
  // Notifications
  notifyCustomer: boolean
  emailSent?: boolean
  smsSent?: boolean
  
  // Metadata
  notes?: string
  receipt?: {
    testCase?: boolean
    authorization?: string
  }
}

export interface OrderFulfillmentItem {
  id: string
  orderItemId: string
  quantity: number
}

export interface OrderReturn extends BaseEntity {
  orderId: string
  returnNumber: string
  status: 'pending' | 'approved' | 'rejected' | 'received' | 'processed' | 'refunded'
  
  // Items
  items: OrderReturnItem[]
  
  // Reason
  reason: 'defective' | 'wrong_item' | 'not_as_described' | 'changed_mind' | 'damaged_in_shipping' | 'other'
  reasonDetails?: string
  
  // Refund
  refundAmount: Money
  refundMethod: 'original_payment' | 'store_credit' | 'exchange'
  
  // Shipping
  returnShippingCost?: Money
  returnTrackingNumber?: string
  
  // Dates
  requestedAt: Date
  approvedAt?: Date
  receivedAt?: Date
  processedAt?: Date
  refundedAt?: Date
  
  // Communication
  customerNote?: string
  internalNotes?: string[]
  
  // Photos/evidence
  images?: string[]
}

export interface OrderReturnItem {
  id: string
  orderItemId: string
  quantity: number
  reason?: string
  condition: 'new' | 'used' | 'damaged' | 'defective'
  restockable: boolean
}

export interface OrderRefund extends BaseEntity {
  orderId: string
  refundNumber: string
  amount: Money
  reason: string
  
  // Items
  items?: OrderRefundItem[]
  
  // Shipping
  shippingRefund?: Money
  
  // Payment
  paymentGatewayRefundId?: string
  
  // Status
  status: 'pending' | 'success' | 'failed' | 'cancelled'
  
  // Dates
  processedAt?: Date
  
  // Metadata
  notes?: string
  notifyCustomer: boolean
}

export interface OrderRefundItem {
  orderItemId: string
  quantity: number
  amount: Money
}

// Order creation and updates
export interface CreateOrderInput {
  // Customer
  customer?: {
    id?: string
    email: string
    firstName?: string
    lastName?: string
    phone?: string
  }
  
  // Addresses
  billingAddress: Address
  shippingAddress: Address
  
  // Items
  items: Array<{
    productId: string
    variantId?: string
    quantity: number
    unitPrice?: Money
    customAttributes?: Record<string, any>
    personalizedMessage?: string
    giftWrap?: boolean
  }>
  
  // Shipping
  shippingMethodId: string
  
  // Discounts
  discountCodes?: string[]
  
  // Payment
  paymentMethodId?: string
  
  // Metadata
  customerNote?: string
  source?: Order['source']
  sourceIdentifier?: string
  attributes?: Record<string, any>
  tags?: string[]
}

export interface UpdateOrderInput {
  id: string
  status?: Order['status']
  paymentStatus?: Order['paymentStatus']
  fulfillmentStatus?: Order['fulfillmentStatus']
  customerNote?: string
  internalNotes?: string[]
  tags?: string[]
  attributes?: Record<string, any>
}

// Order queries
export interface OrderFilters {
  status?: Order['status'][]
  paymentStatus?: Order['paymentStatus'][]
  fulfillmentStatus?: Order['fulfillmentStatus'][]
  riskLevel?: Order['riskLevel'][]
  source?: Order['source'][]
  customerId?: string
  customerEmail?: string
  orderNumber?: string
  createdAfter?: Date
  createdBefore?: Date
  totalMin?: number
  totalMax?: number
  tags?: string[]
}

export interface OrderSortOptions {
  field: 'orderNumber' | 'createdAt' | 'total' | 'status' | 'customerEmail'
  direction: 'asc' | 'desc'
}

export interface OrderSearchParams {
  query?: string
  filters?: OrderFilters
  sort?: OrderSortOptions
  page?: number
  limit?: number
}

// Order analytics
export interface OrderAnalytics {
  period: {
    start: Date
    end: Date
  }
  totalOrders: number
  totalRevenue: Money
  averageOrderValue: Money
  conversionRate: number
  returnRate: number
  refundRate: number
  topProducts: Array<{
    productId: string
    productTitle: string
    quantity: number
    revenue: Money
  }>
  topCustomers: Array<{
    customerId: string
    customerEmail: string
    orderCount: number
    totalSpent: Money
  }>
  salesByDay: Array<{
    date: Date
    orders: number
    revenue: Money
  }>
  statusDistribution: Record<Order['status'], number>
}

// Order notifications
export interface OrderNotification {
  type: 'order_confirmed' | 'order_shipped' | 'order_delivered' | 'order_cancelled' | 'order_refunded'
  orderId: string
  recipientEmail: string
  templateId?: string
  data: Record<string, any>
  scheduledAt?: Date
  sentAt?: Date
  status: 'pending' | 'sent' | 'failed'
}

// Order exports
export interface OrderExport extends BaseEntity {
  filters: OrderFilters
  format: 'csv' | 'xlsx' | 'pdf'
  status: 'pending' | 'processing' | 'completed' | 'failed'
  downloadUrl?: string
  expiresAt?: Date
  recordCount?: number
}

// Order automation
export interface OrderAutomation extends BaseEntity {
  name: string
  description?: string
  trigger: {
    event: 'order_created' | 'order_paid' | 'order_shipped' | 'order_delivered' | 'order_cancelled'
    conditions?: Record<string, any>
  }
  actions: Array<{
    type: 'send_email' | 'send_sms' | 'update_inventory' | 'create_fulfillment' | 'add_tag' | 'webhook'
    config: Record<string, any>
  }>
  isActive: boolean
  executionCount: number
  lastExecutedAt?: Date
}

// Bulk operations
export interface BulkOrderOperation {
  operation: 'update_status' | 'add_tags' | 'remove_tags' | 'export' | 'fulfill' | 'cancel'
  orderIds: string[]
  data?: any
}

export interface BulkOrderResult {
  success: boolean
  processedCount: number
  failedCount: number
  errors?: Array<{
    orderId: string
    error: string
  }>
}
