// User and authentication related types

import { BaseEntity, Address } from './base'

export interface User extends BaseEntity {
  email: string
  firstName?: string
  lastName?: string
  displayName?: string
  phone?: string
  dateOfBirth?: Date
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say'
  
  // Authentication
  emailVerified: boolean
  phoneVerified: boolean
  lastLoginAt?: Date
  
  // Preferences
  acceptsMarketing: boolean
  preferredLanguage: string
  preferredCurrency: string
  timezone?: string
  
  // Profile
  avatar?: string
  bio?: string
  
  // Status
  isActive: boolean
  isBlocked: boolean
  
  // Addresses
  defaultBillingAddressId?: string
  defaultShippingAddressId?: string
  
  // Customer data
  customerSince?: Date
  totalSpent: number
  orderCount: number
  averageOrderValue: number
  lastOrderAt?: Date
  
  // Loyalty
  loyaltyPoints?: number
  loyaltyTier?: string
  
  // Metadata
  metafields?: Record<string, any>
  tags?: string[]
  notes?: string
}

// Customer creation and update interfaces (moved to bottom to avoid duplication)

// Customer queries
export interface CustomerFilters {
  isActive?: boolean
  isBlocked?: boolean
  emailVerified?: boolean
  loyaltyTier?: string[]
  totalSpentMin?: number
  totalSpentMax?: number
  orderCountMin?: number
  orderCountMax?: number
  createdAfter?: Date
  createdBefore?: Date
  lastOrderAfter?: Date
  lastOrderBefore?: Date
  tags?: string[]
}

export interface CustomerSortOptions {
  field: 'email' | 'firstName' | 'lastName' | 'createdAt' | 'totalSpent' | 'orderCount' | 'lastOrderAt'
  direction: 'asc' | 'desc'
}

export interface CustomerSearchParams {
  query?: string
  filters?: CustomerFilters
  sort?: CustomerSortOptions
  page?: number
  limit?: number
}



export interface UserAddress extends Omit<Address, 'id'>, BaseEntity {
  userId: string
  isDefault: boolean
  type: 'billing' | 'shipping' | 'both'
  label?: string
}

export interface UserSession extends BaseEntity {
  userId: string
  token: string
  refreshToken?: string
  expiresAt: Date
  ipAddress?: string
  userAgent?: string
  isActive: boolean
  lastActivityAt: Date
}

export interface UserRole {
  id: string
  name: string
  description?: string
  permissions: string[]
  isSystemRole: boolean
  createdAt: Date
  updatedAt: Date
}

export interface UserPermission {
  id: string
  name: string
  description?: string
  resource: string
  action: string
  createdAt: Date
  updatedAt: Date
}

// Authentication interfaces
export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterCredentials {
  email: string
  password: string
  firstName?: string
  lastName?: string
  phone?: string
  acceptsMarketing?: boolean
}

export interface AuthResponse {
  success: boolean
  user?: User
  token?: string
  refreshToken?: string
  expiresAt?: Date
  error?: string
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordReset {
  token: string
  newPassword: string
}

export interface EmailVerification {
  token: string
}

export interface PhoneVerification {
  phone: string
  code: string
}

// User preferences
export interface UserPreferences {
  userId: string
  notifications: {
    email: {
      orderUpdates: boolean
      promotions: boolean
      newsletter: boolean
      productRecommendations: boolean
      priceDrops: boolean
      backInStock: boolean
    }
    sms: {
      orderUpdates: boolean
      promotions: boolean
      deliveryUpdates: boolean
    }
    push: {
      orderUpdates: boolean
      promotions: boolean
      recommendations: boolean
    }
  }
  privacy: {
    profileVisibility: 'public' | 'private'
    showPurchaseHistory: boolean
    allowDataCollection: boolean
    allowPersonalization: boolean
  }
  shopping: {
    savePaymentMethods: boolean
    saveShippingAddresses: boolean
    autoApplyCoupons: boolean
    wishlistVisibility: 'public' | 'private'
  }
}

// User activity tracking
export interface UserActivity extends BaseEntity {
  userId: string
  type: 'login' | 'logout' | 'view_product' | 'add_to_cart' | 'remove_from_cart' | 'place_order' | 'cancel_order' | 'write_review' | 'update_profile'
  entityType?: string
  entityId?: string
  metadata?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  sessionId?: string
}

// Customer groups
export interface CustomerGroup extends BaseEntity {
  name: string
  description?: string
  conditions: CustomerGroupCondition[]
  benefits: CustomerGroupBenefit[]
  isActive: boolean
  memberCount: number
}

export interface CustomerGroupCondition {
  field: 'totalSpent' | 'orderCount' | 'averageOrderValue' | 'customerSince' | 'lastOrderAt' | 'tags'
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'greater_than_or_equal' | 'less_than_or_equal' | 'contains' | 'not_contains'
  value: string | number | Date
}

export interface CustomerGroupBenefit {
  type: 'discount_percentage' | 'discount_fixed' | 'free_shipping' | 'early_access' | 'loyalty_multiplier'
  value: number
  description?: string
}

// User creation and update interfaces
export interface CreateUserInput {
  email: string
  password?: string
  firstName?: string
  lastName?: string
  displayName?: string
  phone?: string
  dateOfBirth?: Date
  gender?: User['gender']
  acceptsMarketing?: boolean
  preferredLanguage?: string
  preferredCurrency?: string
  timezone?: string
  avatar?: string
  bio?: string
  metafields?: Record<string, any>
  tags?: string[]
  notes?: string
}

export interface UpdateUserInput extends Partial<Omit<CreateUserInput, 'email' | 'password'>> {
  id: string
  email?: string
  isActive?: boolean
  isBlocked?: boolean
  loyaltyPoints?: number
  loyaltyTier?: string
}

export interface ChangePasswordInput {
  userId: string
  currentPassword: string
  newPassword: string
}

// User query interfaces
export interface UserFilters {
  isActive?: boolean
  isBlocked?: boolean
  emailVerified?: boolean
  acceptsMarketing?: boolean
  customerGroupIds?: string[]
  tags?: string[]
  registeredAfter?: Date
  registeredBefore?: Date
  lastLoginAfter?: Date
  lastLoginBefore?: Date
  totalSpentMin?: number
  totalSpentMax?: number
  orderCountMin?: number
  orderCountMax?: number
}

export interface UserSortOptions {
  field: 'email' | 'firstName' | 'lastName' | 'createdAt' | 'lastLoginAt' | 'totalSpent' | 'orderCount'
  direction: 'asc' | 'desc'
}

export interface UserSearchParams {
  query?: string
  filters?: UserFilters
  sort?: UserSortOptions
  page?: number
  limit?: number
}

// User analytics
export interface UserAnalytics {
  userId: string
  totalSpent: number
  orderCount: number
  averageOrderValue: number
  lifetimeValue: number
  acquisitionDate: Date
  acquisitionChannel?: string
  lastActivityDate: Date
  engagementScore: number
  churnRisk: 'low' | 'medium' | 'high'
  preferredCategories: string[]
  preferredBrands: string[]
  period: {
    start: Date
    end: Date
  }
}

// User segmentation
export interface UserSegment extends BaseEntity {
  name: string
  description?: string
  conditions: UserSegmentCondition[]
  userCount: number
  isActive: boolean
  autoUpdate: boolean
  lastUpdatedAt: Date
}

export interface UserSegmentCondition {
  field: string
  operator: string
  value: any
  logicalOperator?: 'AND' | 'OR'
}

// Social authentication
export interface SocialAuthProvider {
  provider: 'google' | 'facebook' | 'twitter' | 'apple' | 'github'
  providerId: string
  email?: string
  name?: string
  avatar?: string
}

export interface SocialAuthResponse extends AuthResponse {
  isNewUser: boolean
  provider: string
}

// Two-factor authentication
export interface TwoFactorAuth {
  userId: string
  secret: string
  backupCodes: string[]
  isEnabled: boolean
  lastUsedAt?: Date
  createdAt: Date
}

export interface TwoFactorSetup {
  secret: string
  qrCode: string
  backupCodes: string[]
}

export interface TwoFactorVerification {
  userId: string
  code: string
}

// Account deletion
export interface AccountDeletionRequest extends BaseEntity {
  userId: string
  reason?: string
  scheduledAt: Date
  isProcessed: boolean
  processedAt?: Date
}

// User export
export interface UserExportRequest extends BaseEntity {
  userId: string
  format: 'json' | 'csv' | 'pdf'
  includeOrders: boolean
  includeAddresses: boolean
  includeActivity: boolean
  status: 'pending' | 'processing' | 'completed' | 'failed'
  downloadUrl?: string
  expiresAt?: Date
}
