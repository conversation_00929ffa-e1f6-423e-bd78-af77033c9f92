// This is a mock implementation that would be replaced with actual API calls to WooCommerce

export interface Product {
  id: string
  name: string
  slug: string
  description: string
  price: number
  compareAtPrice?: number
  images: string[]
  colors: { name: string; value: string }[]
  sizes: string[]
  categoryId: string
  isNew?: boolean
  isSale?: boolean
}

// Mock products data
const products: Product[] = [
  {
    id: "1",
    name: "Striped Cotton T-Shirt",
    slug: "striped-cotton-t-shirt",
    description: "A comfortable striped t-shirt made from 100% organic cotton. Perfect for everyday wear.",
    price: 459.00,
    compareAtPrice: 549.00,
    images: [
      "https://images.unsplash.com/photo-1519238359922-989348752efb?q=80&w=600&h=600&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1519238360710-25a86d4a04a4?q=80&w=600&h=600&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1519238152759-66dbd0ed468a?q=80&w=600&h=600&auto=format&fit=crop",
    ],
    colors: [
      { name: "Blue", value: "#012169" },
      { name: "Red", value: "#6C1411" },
      { name: "Black", value: "#0D0D0D" },
    ],
    sizes: ["XS", "S", "M", "L", "XL"],
    categoryId: "tops",
    isSale: true,
  },
  {
    id: "2",
    name: "Comfort Fit Jeans",
    slug: "comfort-fit-jeans",
    description: "Stylish and comfortable jeans with an elastic waistband for all-day comfort.",
    price: 739.00,
    images: [
      "https://images.unsplash.com/photo-1541580621-cb65cc53084b?q=80&w=600&h=600&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1475178626620-a4d074967452?q=80&w=600&h=600&auto=format&fit=crop",
    ],
    colors: [
      { name: "Blue", value: "#012169" },
      { name: "Black", value: "#0D0D0D" },
    ],
    sizes: ["XS", "S", "M", "L", "XL"],
    categoryId: "bottoms",
    isNew: true,
  },
  {
    id: "3",
    name: "Hooded Sweatshirt",
    slug: "hooded-sweatshirt",
    description: "A cozy hooded sweatshirt perfect for cooler days. Made from soft, durable fabric.",
    price: 649.00,
    compareAtPrice: 829.00,
    images: [
      "https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?q=80&w=600&h=600&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1555009393-f20bdb245c4d?q=80&w=600&h=600&auto=format&fit=crop",
    ],
    colors: [
      { name: "Red", value: "#6C1411" },
      { name: "Brown", value: "#2B1D18" },
      { name: "Black", value: "#0D0D0D" },
    ],
    sizes: ["XS", "S", "M", "L", "XL"],
    categoryId: "outerwear",
    isSale: true,
  },
  {
    id: "4",
    name: "Summer Dress",
    slug: "summer-dress",
    description: "A lightweight summer dress with a playful pattern. Perfect for warm days.",
    price: 559.00,
    images: [
      "https://images.unsplash.com/photo-1603344204980-4edb0ea63148?q=80&w=600&h=600&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1603344202552-acf328f5234e?q=80&w=600&h=600&auto=format&fit=crop",
    ],
    colors: [
      { name: "Blue", value: "#012169" },
      { name: "Red", value: "#6C1411" },
    ],
    sizes: ["XS", "S", "M", "L", "XL"],
    categoryId: "dresses",
    isNew: true,
  },
  {
    id: "5",
    name: "Canvas Backpack",
    slug: "canvas-backpack",
    description: "A durable canvas backpack with multiple compartments. Perfect for school or day trips.",
    price: 929.00,
    compareAtPrice: 1119.00,
    images: [
      "https://images.unsplash.com/photo-1622560480654-d96214fdc887?q=80&w=600&h=600&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1617006898062-b7c9572f192e?q=80&w=600&h=600&auto=format&fit=crop",
    ],
    colors: [
      { name: "Brown", value: "#2B1D18" },
      { name: "Black", value: "#0D0D0D" },
    ],
    sizes: ["One Size"],
    categoryId: "accessories",
    isSale: true,
  },
  {
    id: "6",
    name: "Denim Shorts",
    slug: "denim-shorts",
    description: "Classic denim shorts with an adjustable waistband. Perfect for active kids.",
    price: 519.00,
    images: [
      "https://images.unsplash.com/photo-1626566340238-0c6b72a23c88?q=80&w=600&h=600&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1626566340145-30ef41640fa1?q=80&w=600&h=600&auto=format&fit=crop",
    ],
    colors: [
      { name: "Blue", value: "#012169" },
      { name: "Black", value: "#0D0D0D" },
    ],
    sizes: ["XS", "S", "M", "L", "XL"],
    categoryId: "bottoms",
  },
  {
    id: "7",
    name: "Graphic Print T-Shirt",
    slug: "graphic-print-t-shirt",
    description: "A fun t-shirt with a colorful graphic print. Made from soft, breathable cotton.",
    price: 429.00,
    images: [
      "https://images.unsplash.com/photo-1596870230751-ebdfce98ec42?q=80&w=600&h=600&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1596870230211-30b415eabef9?q=80&w=600&h=600&auto=format&fit=crop",
    ],
    colors: [
      { name: "White", value: "#FFFFFF" },
      { name: "Gray", value: "#E5E5E5" },
    ],
    sizes: ["XS", "S", "M", "L", "XL"],
    categoryId: "tops",
    isNew: true,
  },
  {
    id: "8",
    name: "Knit Beanie",
    slug: "knit-beanie",
    description: "A warm knit beanie for cold days. Made from soft, itch-free yarn.",
    price: 279.00,
    compareAtPrice: 359.00,
    images: [
      "https://images.unsplash.com/photo-1576871337622-98d48d1cf531?q=80&w=600&h=600&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1576871337632-b9aef4c17ab9?q=80&w=600&h=600&auto=format&fit=crop",
    ],
    colors: [
      { name: "Red", value: "#6C1411" },
      { name: "Blue", value: "#012169" },
      { name: "Black", value: "#0D0D0D" },
    ],
    sizes: ["S", "M", "L"],
    categoryId: "accessories",
    isSale: true,
  },
]

interface GetProductsOptions {
  sort?: string
  category?: string
  color?: string
  size?: string
}

export async function getProducts(options: GetProductsOptions = {}): Promise<Product[]> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500))

  let filteredProducts = [...products]

  // Apply filters
  if (options.category) {
    filteredProducts = filteredProducts.filter((product) => product.categoryId === options.category)
  }

  if (options.color) {
    filteredProducts = filteredProducts.filter((product) =>
      product.colors.some((color) => color.name.toLowerCase() === options.color),
    )
  }

  if (options.size) {
    filteredProducts = filteredProducts.filter((product) => product.sizes.includes(options.size && options.size.toUpperCase() || ""))
  }

  // Apply sorting
  if (options.sort) {
    switch (options.sort) {
      case "price-asc":
        filteredProducts.sort((a, b) => a.price - b.price)
        break
      case "price-desc":
        filteredProducts.sort((a, b) => b.price - a.price)
        break
      case "name-asc":
        filteredProducts.sort((a, b) => a.name.localeCompare(b.name))
        break
      case "name-desc":
        filteredProducts.sort((a, b) => b.name.localeCompare(a.name))
        break
      case "newest":
        filteredProducts.sort((a, b) => (a.isNew === b.isNew ? 0 : a.isNew ? -1 : 1))
        break
      default: // 'featured'
        // For featured, prioritize sale items and new items
        filteredProducts.sort((a, b) => {
          if (a.isSale && !b.isSale) return -1
          if (!a.isSale && b.isSale) return 1
          if (a.isNew && !b.isNew) return -1
          if (!a.isNew && b.isNew) return 1
          return 0
        })
    }
  }

  return filteredProducts
}

export async function getProduct(slug: string): Promise<Product | null> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500))

  const product = products.find((p) => p.slug === slug)
  return product || null
}

export async function getRelatedProducts(categoryId: string, currentProductId: string): Promise<Product[]> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500))

  return products.filter((p) => p.categoryId === categoryId && p.id !== currentProductId).slice(0, 4)
}

export async function searchProducts(query: string): Promise<Product[]> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500))

  const searchTerms = query.toLowerCase().split(" ")

  return products
    .filter((product) => {
      const productText = `${product.name} ${product.description} ${product.categoryId}`.toLowerCase()
      return searchTerms.every((term) => productText.includes(term))
    })
    .slice(0, 10) // Limit to 10 results
}

export async function getProductsByIds(ids: string[]): Promise<Product[]> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500))

  return products.filter((product) => ids.includes(product.id))
}
