// WordPress-Style Posts Utilities
// Helper functions for post management, content processing, and data transformation

import DOMPurify from 'isomorphic-dompurify'

/**
 * Generate a URL-friendly slug from a title
 */
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
}

/**
 * Generate a unique slug by appending a number if needed
 */
export async function generateUniqueSlug(
  title: string, 
  checkSlugExists: (slug: string) => Promise<boolean>
): Promise<string> {
  let baseSlug = generateSlug(title)
  let slug = baseSlug
  let counter = 1

  while (await checkSlugExists(slug)) {
    slug = `${baseSlug}-${counter}`
    counter++
  }

  return slug
}

/**
 * Sanitize HTML content to prevent XSS attacks
 */
export function sanitizeHtml(html: string): string {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: [
      'p', 'br', 'strong', 'em', 'u', 's', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li', 'blockquote', 'a', 'img', 'figure', 'figcaption',
      'table', 'thead', 'tbody', 'tr', 'th', 'td', 'code', 'pre', 'span', 'div'
    ],
    ALLOWED_ATTR: [
      'href', 'src', 'alt', 'title', 'class', 'id', 'style', 'target', 'rel'
    ],
    ALLOW_DATA_ATTR: false
  })
}

/**
 * Extract excerpt from content
 */
export function extractExcerpt(content: string, maxLength: number = 160): string {
  // Strip HTML tags
  const textContent = content.replace(/<[^>]*>/g, '')
  
  // Trim and truncate
  const trimmed = textContent.trim()
  
  if (trimmed.length <= maxLength) {
    return trimmed
  }
  
  // Find the last complete word within the limit
  const truncated = trimmed.substring(0, maxLength)
  const lastSpaceIndex = truncated.lastIndexOf(' ')
  
  if (lastSpaceIndex > 0) {
    return truncated.substring(0, lastSpaceIndex) + '...'
  }
  
  return truncated + '...'
}

/**
 * Calculate estimated reading time in minutes
 */
export function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200
  const textContent = content.replace(/<[^>]*>/g, '')
  const wordCount = textContent.trim().split(/\s+/).length
  return Math.ceil(wordCount / wordsPerMinute)
}

/**
 * Format post status for display
 */
export function formatPostStatus(status: string): string {
  const statusMap: Record<string, string> = {
    draft: 'Draft',
    published: 'Published',
    private: 'Private',
    trash: 'Trash',
    scheduled: 'Scheduled'
  }
  return statusMap[status] || status
}

/**
 * Check if post is published and visible
 */
export function isPostPublic(post: any): boolean {
  return post.status === 'published' && 
         (!post.password || post.password === '') &&
         (!post.publishedAt || post.publishedAt <= new Date())
}

/**
 * Get post permalink based on post type and slug
 */
export function getPostPermalink(post: any, baseUrl: string = ''): string {
  const { postType, slug } = post
  
  switch (postType) {
    case 'page':
      return `${baseUrl}/${slug}`
    case 'post':
      return `${baseUrl}/blog/${slug}`
    case 'product':
      return `${baseUrl}/products/${slug}`
    default:
      return `${baseUrl}/${postType}/${slug}`
  }
}

/**
 * Generate SEO meta tags for a post
 */
export function generateSeoMeta(post: any, siteTitle: string = '') {
  const title = post.seoTitle || post.title
  const description = post.seoDescription || post.excerpt || ''
  const image = post.ogImage || post.featuredImage || ''
  const url = getPostPermalink(post)
  
  return {
    title: siteTitle ? `${title} | ${siteTitle}` : title,
    description,
    keywords: post.seoKeywords?.join(', ') || '',
    canonical: post.canonicalUrl || url,
    robots: post.metaRobots || 'index,follow',
    openGraph: {
      title: post.ogTitle || title,
      description: post.ogDescription || description,
      image,
      url,
      type: 'article',
      publishedTime: post.publishedAt?.toISOString(),
      modifiedTime: post.updatedAt?.toISOString(),
      author: post.authorName,
    },
    twitter: {
      card: post.twitterCard || 'summary_large_image',
      title: post.ogTitle || title,
      description: post.ogDescription || description,
      image,
    }
  }
}

/**
 * Validate post data
 */
export function validatePostData(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (!data.title || data.title.trim() === '') {
    errors.push('Title is required')
  }
  
  if (!data.postType || data.postType.trim() === '') {
    errors.push('Post type is required')
  }
  
  if (data.slug && !/^[a-z0-9-]+$/.test(data.slug)) {
    errors.push('Slug can only contain lowercase letters, numbers, and hyphens')
  }
  
  if (data.status && !['draft', 'published', 'private', 'trash', 'scheduled'].includes(data.status)) {
    errors.push('Invalid post status')
  }
  
  if (data.scheduledAt && data.status !== 'scheduled') {
    errors.push('Scheduled date can only be set for scheduled posts')
  }
  
  if (data.password && data.status === 'private') {
    errors.push('Private posts cannot have passwords')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Transform post data for API response
 */
export function transformPostForApi(post: any, includePrivateFields: boolean = false) {
  const publicFields = {
    id: post.id,
    title: post.title,
    slug: post.slug,
    content: post.contentHtml || post.content,
    excerpt: post.excerpt,
    status: post.status,
    postType: post.postType,
    featuredImage: post.featuredImage,
    featuredImageAlt: post.featuredImageAlt,
    publishedAt: post.publishedAt,
    authorName: post.authorName,
    viewCount: post.viewCount,
    shareCount: post.shareCount,
    likeCount: post.likeCount,
    commentCount: post.commentCount,
    isFeatured: post.isFeatured,
    createdAt: post.createdAt,
    updatedAt: post.updatedAt,
    taxonomyTerms: post.taxonomyTerms,
    comments: post.comments,
    blocks: post.blocks,
  }
  
  if (includePrivateFields) {
    return {
      ...publicFields,
      authorId: post.authorId,
      authorEmail: post.authorEmail,
      seoTitle: post.seoTitle,
      seoDescription: post.seoDescription,
      seoKeywords: post.seoKeywords,
      ogImage: post.ogImage,
      ogTitle: post.ogTitle,
      ogDescription: post.ogDescription,
      twitterCard: post.twitterCard,
      canonicalUrl: post.canonicalUrl,
      metaRobots: post.metaRobots,
      allowComments: post.allowComments,
      allowPingbacks: post.allowPingbacks,
      isSticky: post.isSticky,
      usePageBuilder: post.usePageBuilder,
      pageBuilderData: post.pageBuilderData,
      customFields: post.customFields,
      metadata: post.metadata,
      meta: post.meta,
      revisions: post.revisions,
    }
  }
  
  return publicFields
}

/**
 * Parse and validate custom fields
 */
export function parseCustomFields(fields: any): Record<string, any> {
  if (!fields || typeof fields !== 'object') {
    return {}
  }
  
  const parsed: Record<string, any> = {}
  
  for (const [key, value] of Object.entries(fields)) {
    // Validate field key
    if (typeof key === 'string' && key.trim() !== '') {
      parsed[key] = value
    }
  }
  
  return parsed
}

/**
 * Generate post archive URL
 */
export function getPostArchiveUrl(postType: string, baseUrl: string = ''): string {
  switch (postType) {
    case 'post':
      return `${baseUrl}/blog`
    case 'product':
      return `${baseUrl}/products`
    case 'page':
      return `${baseUrl}/pages`
    default:
      return `${baseUrl}/${postType}`
  }
}

/**
 * Check if user can edit post
 */
export function canUserEditPost(post: any, userId?: string, userRole?: string): boolean {
  // System administrators can edit everything
  if (userRole === 'admin' || userRole === 'super_admin') {
    return true
  }
  
  // Post authors can edit their own posts
  if (userId && post.authorId === userId) {
    return true
  }
  
  // Editors can edit posts in their assigned post types
  if (userRole === 'editor') {
    return true
  }
  
  return false
}

/**
 * Get post type capabilities
 */
export function getPostTypeCapabilities(postType: string): Record<string, string> {
  return {
    edit_post: `edit_${postType}`,
    read_post: `read_${postType}`,
    delete_post: `delete_${postType}`,
    edit_posts: `edit_${postType}s`,
    edit_others_posts: `edit_others_${postType}s`,
    publish_posts: `publish_${postType}s`,
    read_private_posts: `read_private_${postType}s`,
    delete_posts: `delete_${postType}s`,
    delete_private_posts: `delete_private_${postType}s`,
    delete_published_posts: `delete_published_${postType}s`,
    delete_others_posts: `delete_others_${postType}s`,
    edit_private_posts: `edit_private_${postType}s`,
    edit_published_posts: `edit_published_${postType}s`,
  }
}
