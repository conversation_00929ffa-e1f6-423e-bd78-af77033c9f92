// WordPress-Style Post Type Service
// Service for managing post types and their configurations

import { PrismaClient } from '@prisma/client'
import { 
  PostType, 
  CreatePostTypeInput, 
  ApiResponse,
  PostTypeApiResponse
} from '../types'
import { prisma } from '../../ecommerce/config/database'
import { getPostTypeCapabilities } from '../utils'

export class PostTypeService {
  private db: PrismaClient

  constructor(database?: PrismaClient) {
    this.db = database || prisma
  }

  /**
   * Register a new post type
   */
  async registerPostType(input: CreatePostTypeInput): Promise<PostTypeApiResponse> {
    try {
      // Check if post type already exists
      const existingPostType = await this.db.postType.findUnique({
        where: { name: input.name }
      })

      if (existingPostType) {
        return {
          success: false,
          error: `Post type '${input.name}' already exists`
        }
      }

      // Validate post type name
      if (!/^[a-z_][a-z0-9_]*$/.test(input.name)) {
        return {
          success: false,
          error: 'Post type name must contain only lowercase letters, numbers, and underscores'
        }
      }

      // Generate capabilities if not provided
      const capabilities = input.capabilities || getPostTypeCapabilities(input.name)

      const postType = await this.db.postType.create({
        data: {
          name: input.name,
          label: input.label,
          labelPlural: input.labelPlural,
          description: input.description,
          icon: input.icon,
          isPublic: input.isPublic ?? true,
          isHierarchical: input.isHierarchical ?? false,
          hasArchive: input.hasArchive ?? true,
          supportsTitle: input.supportsTitle ?? true,
          supportsContent: input.supportsContent ?? true,
          supportsExcerpt: input.supportsExcerpt ?? false,
          supportsThumbnail: input.supportsThumbnail ?? false,
          supportsComments: input.supportsComments ?? false,
          supportsRevisions: input.supportsRevisions ?? true,
          supportsPageBuilder: input.supportsPageBuilder ?? false,
          menuPosition: input.menuPosition,
          capabilities,
          taxonomies: input.taxonomies || [],
          customFields: input.customFields,
          templates: input.templates || [],
          isSystem: false,
          isActive: true,
        }
      })

      return {
        success: true,
        data: this.transformPostTypeData(postType),
        message: 'Post type registered successfully'
      }

    } catch (error) {
      console.error('Error registering post type:', error)
      return {
        success: false,
        error: 'Failed to register post type'
      }
    }
  }

  /**
   * Get all post types
   */
  async getPostTypes(includeInactive: boolean = false): Promise<ApiResponse<PostType[]>> {
    try {
      const where = includeInactive ? {} : { isActive: true }
      
      const postTypes = await this.db.postType.findMany({
        where,
        orderBy: [
          { menuPosition: 'asc' },
          { name: 'asc' }
        ]
      })

      return {
        success: true,
        data: postTypes.map(pt => this.transformPostTypeData(pt))
      }

    } catch (error) {
      console.error('Error fetching post types:', error)
      return {
        success: false,
        error: 'Failed to fetch post types'
      }
    }
  }

  /**
   * Get post type by name
   */
  async getPostType(name: string): Promise<PostTypeApiResponse> {
    try {
      const postType = await this.db.postType.findUnique({
        where: { name }
      })

      if (!postType) {
        return {
          success: false,
          error: 'Post type not found'
        }
      }

      return {
        success: true,
        data: this.transformPostTypeData(postType)
      }

    } catch (error) {
      console.error('Error fetching post type:', error)
      return {
        success: false,
        error: 'Failed to fetch post type'
      }
    }
  }

  /**
   * Update post type
   */
  async updatePostType(name: string, updates: Partial<CreatePostTypeInput>): Promise<PostTypeApiResponse> {
    try {
      const existingPostType = await this.db.postType.findUnique({
        where: { name }
      })

      if (!existingPostType) {
        return {
          success: false,
          error: 'Post type not found'
        }
      }

      if (existingPostType.isSystem) {
        return {
          success: false,
          error: 'Cannot modify system post types'
        }
      }

      const postType = await this.db.postType.update({
        where: { name },
        data: {
          label: updates.label,
          labelPlural: updates.labelPlural,
          description: updates.description,
          icon: updates.icon,
          isPublic: updates.isPublic,
          isHierarchical: updates.isHierarchical,
          hasArchive: updates.hasArchive,
          supportsTitle: updates.supportsTitle,
          supportsContent: updates.supportsContent,
          supportsExcerpt: updates.supportsExcerpt,
          supportsThumbnail: updates.supportsThumbnail,
          supportsComments: updates.supportsComments,
          supportsRevisions: updates.supportsRevisions,
          supportsPageBuilder: updates.supportsPageBuilder,
          menuPosition: updates.menuPosition,
          capabilities: updates.capabilities,
          taxonomies: updates.taxonomies,
          customFields: updates.customFields,
          templates: updates.templates,
        }
      })

      return {
        success: true,
        data: this.transformPostTypeData(postType),
        message: 'Post type updated successfully'
      }

    } catch (error) {
      console.error('Error updating post type:', error)
      return {
        success: false,
        error: 'Failed to update post type'
      }
    }
  }

  /**
   * Deactivate post type
   */
  async deactivatePostType(name: string): Promise<ApiResponse<boolean>> {
    try {
      const existingPostType = await this.db.postType.findUnique({
        where: { name }
      })

      if (!existingPostType) {
        return {
          success: false,
          error: 'Post type not found'
        }
      }

      if (existingPostType.isSystem) {
        return {
          success: false,
          error: 'Cannot deactivate system post types'
        }
      }

      await this.db.postType.update({
        where: { name },
        data: { isActive: false }
      })

      return {
        success: true,
        data: true,
        message: 'Post type deactivated successfully'
      }

    } catch (error) {
      console.error('Error deactivating post type:', error)
      return {
        success: false,
        error: 'Failed to deactivate post type'
      }
    }
  }

  /**
   * Initialize default post types
   */
  async initializeDefaultPostTypes(): Promise<ApiResponse<boolean>> {
    try {
      const defaultPostTypes = [
        {
          name: 'post',
          label: 'Post',
          labelPlural: 'Posts',
          description: 'Blog posts and articles',
          icon: '📝',
          isPublic: true,
          isHierarchical: false,
          hasArchive: true,
          supportsTitle: true,
          supportsContent: true,
          supportsExcerpt: true,
          supportsThumbnail: true,
          supportsComments: true,
          supportsRevisions: true,
          supportsPageBuilder: false,
          menuPosition: 5,
          taxonomies: ['category', 'tag'],
          templates: ['single-post', 'archive-post'],
          isSystem: true,
        },
        {
          name: 'page',
          label: 'Page',
          labelPlural: 'Pages',
          description: 'Static pages',
          icon: '📄',
          isPublic: true,
          isHierarchical: true,
          hasArchive: false,
          supportsTitle: true,
          supportsContent: true,
          supportsExcerpt: false,
          supportsThumbnail: true,
          supportsComments: false,
          supportsRevisions: true,
          supportsPageBuilder: true,
          menuPosition: 20,
          taxonomies: [],
          templates: ['page', 'page-custom'],
          isSystem: true,
        },
        {
          name: 'product',
          label: 'Product',
          labelPlural: 'Products',
          description: 'E-commerce products',
          icon: '🛍️',
          isPublic: true,
          isHierarchical: false,
          hasArchive: true,
          supportsTitle: true,
          supportsContent: true,
          supportsExcerpt: true,
          supportsThumbnail: true,
          supportsComments: false,
          supportsRevisions: true,
          supportsPageBuilder: false,
          menuPosition: 10,
          taxonomies: ['product_category', 'product_tag'],
          templates: ['single-product', 'archive-product'],
          isSystem: true,
        }
      ]

      for (const postTypeData of defaultPostTypes) {
        const existing = await this.db.postType.findUnique({
          where: { name: postTypeData.name }
        })

        if (!existing) {
          await this.db.postType.create({
            data: {
              ...postTypeData,
              capabilities: getPostTypeCapabilities(postTypeData.name),
            }
          })
        }
      }

      return {
        success: true,
        data: true,
        message: 'Default post types initialized successfully'
      }

    } catch (error) {
      console.error('Error initializing default post types:', error)
      return {
        success: false,
        error: 'Failed to initialize default post types'
      }
    }
  }

  /**
   * Get public post types for frontend
   */
  async getPublicPostTypes(): Promise<ApiResponse<PostType[]>> {
    try {
      const postTypes = await this.db.postType.findMany({
        where: {
          isActive: true,
          isPublic: true
        },
        orderBy: [
          { menuPosition: 'asc' },
          { name: 'asc' }
        ]
      })

      return {
        success: true,
        data: postTypes.map(pt => this.transformPostTypeData(pt))
      }

    } catch (error) {
      console.error('Error fetching public post types:', error)
      return {
        success: false,
        error: 'Failed to fetch public post types'
      }
    }
  }

  /**
   * Check if post type supports a feature
   */
  async supportsFeature(postTypeName: string, feature: string): Promise<boolean> {
    try {
      const postType = await this.db.postType.findUnique({
        where: { name: postTypeName }
      })

      if (!postType) {
        return false
      }

      const featureMap: Record<string, keyof PostType> = {
        title: 'supportsTitle',
        content: 'supportsContent',
        excerpt: 'supportsExcerpt',
        thumbnail: 'supportsThumbnail',
        comments: 'supportsComments',
        revisions: 'supportsRevisions',
        'page-builder': 'supportsPageBuilder',
      }

      const featureKey = featureMap[feature]
      return featureKey ? Boolean(postType[featureKey]) : false

    } catch (error) {
      console.error('Error checking post type feature support:', error)
      return false
    }
  }

  /**
   * Transform database post type data to API format
   */
  private transformPostTypeData(postType: any): PostType {
    return {
      id: postType.id,
      name: postType.name,
      label: postType.label,
      labelPlural: postType.labelPlural,
      description: postType.description,
      icon: postType.icon,
      isPublic: postType.isPublic,
      isHierarchical: postType.isHierarchical,
      hasArchive: postType.hasArchive,
      supportsTitle: postType.supportsTitle,
      supportsContent: postType.supportsContent,
      supportsExcerpt: postType.supportsExcerpt,
      supportsThumbnail: postType.supportsThumbnail,
      supportsComments: postType.supportsComments,
      supportsRevisions: postType.supportsRevisions,
      supportsPageBuilder: postType.supportsPageBuilder,
      menuPosition: postType.menuPosition,
      capabilities: postType.capabilities,
      taxonomies: postType.taxonomies,
      customFields: postType.customFields,
      templates: postType.templates,
      isSystem: postType.isSystem,
      isActive: postType.isActive,
      createdAt: postType.createdAt,
      updatedAt: postType.updatedAt,
    }
  }
}
