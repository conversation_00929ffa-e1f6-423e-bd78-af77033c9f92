// WordPress-Style Posts Service
// Core service for managing posts with full WordPress-like functionality

import { PrismaClient } from '@prisma/client'
import { 
  Post, 
  CreatePostInput, 
  UpdatePostInput, 
  PostQueryParams, 
  PaginatedPostsResponse,
  ApiResponse,
  PostApiResponse,
  PostsApiResponse,
  PostStatus
} from '../types'
import { prisma } from '../../ecommerce/config/database'
import { generateSlug, sanitizeHtml, extractExcerpt } from '../utils'

export class PostService {
  private db: PrismaClient

  constructor(database?: PrismaClient) {
    this.db = database || prisma
  }

  /**
   * Create a new post
   */
  async createPost(input: CreatePostInput): Promise<PostApiResponse> {
    try {
      // Generate slug if not provided
      const slug = input.slug || generateSlug(input.title)
      
      // Check if slug is unique
      const existingPost = await this.db.post.findUnique({
        where: { slug }
      })

      if (existingPost) {
        return {
          success: false,
          error: 'A post with this slug already exists'
        }
      }

      // Validate post type exists
      const postType = await this.db.postType.findUnique({
        where: { name: input.postType }
      })

      if (!postType) {
        return {
          success: false,
          error: `Post type '${input.postType}' does not exist`
        }
      }

      // Process content
      const contentHtml = input.content ? sanitizeHtml(input.content) : undefined
      const excerpt = input.excerpt || (input.content ? extractExcerpt(input.content) : undefined)

      // Create post with relations in a transaction
      const result = await this.db.$transaction(async (tx) => {
        // Create the post
        const post = await tx.post.create({
          data: {
            title: input.title,
            slug,
            content: input.content,
            contentHtml,
            excerpt,
            status: input.status || 'draft',
            postType: input.postType,
            parentId: input.parentId,
            menuOrder: input.menuOrder || 0,
            featuredImage: input.featuredImage,
            featuredImageAlt: input.featuredImageAlt,
            template: input.template,
            password: input.password,
            publishedAt: input.status === 'published' ? new Date() : input.publishedAt,
            scheduledAt: input.scheduledAt,
            authorId: input.authorId,
            authorName: input.authorName,
            authorEmail: input.authorEmail,
            seoTitle: input.seoTitle,
            seoDescription: input.seoDescription,
            seoKeywords: input.seoKeywords || [],
            ogImage: input.ogImage,
            ogTitle: input.ogTitle,
            ogDescription: input.ogDescription,
            twitterCard: input.twitterCard,
            canonicalUrl: input.canonicalUrl,
            metaRobots: input.metaRobots,
            allowComments: input.allowComments ?? true,
            allowPingbacks: input.allowPingbacks ?? true,
            isSticky: input.isSticky || false,
            isFeatured: input.isFeatured || false,
            usePageBuilder: input.usePageBuilder || false,
            pageBuilderData: input.pageBuilderData,
            customFields: input.customFields,
            metadata: input.metadata,
          }
        })

        // Add taxonomy terms if provided
        if (input.taxonomyTerms && input.taxonomyTerms.length > 0) {
          await tx.postTaxonomyTerm.createMany({
            data: input.taxonomyTerms.map(termId => ({
              postId: post.id,
              termId
            }))
          })
        }

        // Add blocks if provided
        if (input.blocks && input.blocks.length > 0) {
          await tx.postBlock.createMany({
            data: input.blocks.map((block, index) => ({
              postId: post.id,
              blockType: block.blockType,
              position: block.position ?? index,
              isVisible: block.isVisible ?? true,
              configuration: block.configuration,
              content: block.content,
              styling: block.styling,
              responsive: block.responsive,
              animation: block.animation,
              conditions: block.conditions,
            }))
          })
        }

        // Create initial revision
        await tx.postRevision.create({
          data: {
            postId: post.id,
            title: post.title,
            content: post.content,
            contentHtml: post.contentHtml,
            excerpt: post.excerpt,
            revisionNumber: 1,
            changeType: 'revision',
            changeSummary: 'Initial creation',
            authorId: input.authorId,
            authorName: input.authorName,
            metadata: {
              initialPost: true,
              postData: post
            }
          }
        })

        return post
      })

      // Fetch the complete post with relations
      const completePost = await this.getPostById(result.id)
      
      return {
        success: true,
        data: completePost.data,
        message: 'Post created successfully'
      }

    } catch (error) {
      console.error('Error creating post:', error)
      return {
        success: false,
        error: 'Failed to create post'
      }
    }
  }

  /**
   * Get post by ID with full relations
   */
  async getPostById(id: string, include?: string[]): Promise<PostApiResponse> {
    try {
      const post = await this.db.post.findUnique({
        where: { id },
        include: {
          postTypeRef: true,
          parent: true,
          children: true,
          taxonomyTerms: {
            include: {
              term: {
                include: {
                  taxonomy: true
                }
              }
            }
          },
          meta: include?.includes('meta') ?? false,
          comments: include?.includes('comments') ? {
            where: { status: 'approved' },
            orderBy: { createdAt: 'desc' },
            take: 10
          } : false,
          revisions: include?.includes('revisions') ? {
            orderBy: { revisionNumber: 'desc' },
            take: 5
          } : false,
          blocks: include?.includes('blocks') ? {
            orderBy: { position: 'asc' }
          } : false,
        }
      })

      if (!post) {
        return {
          success: false,
          error: 'Post not found'
        }
      }

      return {
        success: true,
        data: this.transformPostData(post)
      }

    } catch (error) {
      console.error('Error fetching post:', error)
      return {
        success: false,
        error: 'Failed to fetch post'
      }
    }
  }

  /**
   * Get post by slug
   */
  async getPostBySlug(slug: string, include?: string[]): Promise<PostApiResponse> {
    try {
      const post = await this.db.post.findUnique({
        where: { slug },
        include: {
          postTypeRef: true,
          parent: true,
          children: true,
          taxonomyTerms: {
            include: {
              term: {
                include: {
                  taxonomy: true
                }
              }
            }
          },
          meta: include?.includes('meta') ?? false,
          comments: include?.includes('comments') ? {
            where: { status: 'approved' },
            orderBy: { createdAt: 'desc' },
            take: 10
          } : false,
          revisions: include?.includes('revisions') ? {
            orderBy: { revisionNumber: 'desc' },
            take: 5
          } : false,
          blocks: include?.includes('blocks') ? {
            orderBy: { position: 'asc' }
          } : false,
        }
      })

      if (!post) {
        return {
          success: false,
          error: 'Post not found'
        }
      }

      // Increment view count
      await this.db.post.update({
        where: { id: post.id },
        data: { viewCount: { increment: 1 } }
      })

      return {
        success: true,
        data: this.transformPostData(post)
      }

    } catch (error) {
      console.error('Error fetching post by slug:', error)
      return {
        success: false,
        error: 'Failed to fetch post'
      }
    }
  }

  /**
   * Query posts with filters and pagination
   */
  async queryPosts(params: PostQueryParams = {}): Promise<PostsApiResponse> {
    try {
      const {
        postType,
        status = 'published',
        authorId,
        parentId,
        search,
        taxonomies,
        meta,
        orderBy = 'createdAt',
        order = 'desc',
        limit = 10,
        offset = 0,
        include = []
      } = params

      // Build where clause
      const where: any = {}

      // Post type filter
      if (postType) {
        if (Array.isArray(postType)) {
          where.postType = { in: postType }
        } else {
          where.postType = postType
        }
      }

      // Status filter
      if (Array.isArray(status)) {
        where.status = { in: status }
      } else {
        where.status = status
      }

      // Author filter
      if (authorId) {
        where.authorId = authorId
      }

      // Parent filter
      if (parentId) {
        where.parentId = parentId
      }

      // Search filter
      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { content: { contains: search, mode: 'insensitive' } },
          { excerpt: { contains: search, mode: 'insensitive' } }
        ]
      }

      // Taxonomy filters
      if (taxonomies) {
        where.taxonomyTerms = {
          some: {
            term: {
              OR: Object.entries(taxonomies).map(([taxonomyName, termSlugs]) => ({
                taxonomy: { name: taxonomyName },
                slug: { in: termSlugs }
              }))
            }
          }
        }
      }

      // Meta filters
      if (meta) {
        where.meta = {
          some: {
            OR: Object.entries(meta).map(([key, value]) => ({
              metaKey: key,
              metaValue: String(value)
            }))
          }
        }
      }

      // Get total count
      const total = await this.db.post.count({ where })

      // Get posts
      const posts = await this.db.post.findMany({
        where,
        include: {
          postTypeRef: true,
          parent: include.includes('parent'),
          children: include.includes('children'),
          taxonomyTerms: {
            include: {
              term: {
                include: {
                  taxonomy: true
                }
              }
            }
          },
          meta: include.includes('meta'),
          comments: include.includes('comments') ? {
            where: { status: 'approved' },
            orderBy: { createdAt: 'desc' },
            take: 5
          } : false,
          revisions: include.includes('revisions') ? {
            orderBy: { revisionNumber: 'desc' },
            take: 3
          } : false,
          blocks: include.includes('blocks') ? {
            orderBy: { position: 'asc' }
          } : false,
        },
        orderBy: { [orderBy]: order },
        take: limit,
        skip: offset
      })

      const totalPages = Math.ceil(total / limit)
      const currentPage = Math.floor(offset / limit) + 1

      return {
        success: true,
        data: {
          posts: posts.map(post => this.transformPostData(post)),
          total,
          page: currentPage,
          limit,
          totalPages,
          hasNext: currentPage < totalPages,
          hasPrev: currentPage > 1
        }
      }

    } catch (error) {
      console.error('Error querying posts:', error)
      return {
        success: false,
        error: 'Failed to query posts'
      }
    }
  }

  /**
   * Update a post
   */
  async updatePost(input: UpdatePostInput): Promise<PostApiResponse> {
    try {
      // Check if post exists
      const existingPost = await this.db.post.findUnique({
        where: { id: input.id }
      })

      if (!existingPost) {
        return {
          success: false,
          error: 'Post not found'
        }
      }

      // Check slug uniqueness if slug is being updated
      if (input.slug && input.slug !== existingPost.slug) {
        const slugExists = await this.db.post.findUnique({
          where: { slug: input.slug }
        })

        if (slugExists) {
          return {
            success: false,
            error: 'A post with this slug already exists'
          }
        }
      }

      // Process content if being updated
      const contentHtml = input.content ? sanitizeHtml(input.content) : undefined
      const excerpt = input.excerpt || (input.content ? extractExcerpt(input.content) : undefined)

      // Update post in a transaction
      const result = await this.db.$transaction(async (tx) => {
        // Update the post
        const updatedPost = await tx.post.update({
          where: { id: input.id },
          data: {
            title: input.title,
            slug: input.slug,
            content: input.content,
            contentHtml,
            excerpt,
            status: input.status,
            postType: input.postType,
            parentId: input.parentId,
            menuOrder: input.menuOrder,
            featuredImage: input.featuredImage,
            featuredImageAlt: input.featuredImageAlt,
            template: input.template,
            password: input.password,
            publishedAt: input.status === 'published' && !existingPost.publishedAt ? new Date() : input.publishedAt,
            scheduledAt: input.scheduledAt,
            authorId: input.authorId,
            authorName: input.authorName,
            authorEmail: input.authorEmail,
            seoTitle: input.seoTitle,
            seoDescription: input.seoDescription,
            seoKeywords: input.seoKeywords,
            ogImage: input.ogImage,
            ogTitle: input.ogTitle,
            ogDescription: input.ogDescription,
            twitterCard: input.twitterCard,
            canonicalUrl: input.canonicalUrl,
            metaRobots: input.metaRobots,
            allowComments: input.allowComments,
            allowPingbacks: input.allowPingbacks,
            isSticky: input.isSticky,
            isFeatured: input.isFeatured,
            usePageBuilder: input.usePageBuilder,
            pageBuilderData: input.pageBuilderData,
            customFields: input.customFields,
            metadata: input.metadata,
          }
        })

        // Update taxonomy terms if provided
        if (input.taxonomyTerms) {
          // Remove existing terms
          await tx.postTaxonomyTerm.deleteMany({
            where: { postId: input.id }
          })

          // Add new terms
          if (input.taxonomyTerms.length > 0) {
            await tx.postTaxonomyTerm.createMany({
              data: input.taxonomyTerms.map(termId => ({
                postId: input.id,
                termId
              }))
            })
          }
        }

        // Update blocks if provided
        if (input.blocks) {
          // Remove existing blocks
          await tx.postBlock.deleteMany({
            where: { postId: input.id }
          })

          // Add new blocks
          if (input.blocks.length > 0) {
            await tx.postBlock.createMany({
              data: input.blocks.map((block, index) => ({
                postId: input.id,
                blockType: block.blockType,
                position: block.position ?? index,
                isVisible: block.isVisible ?? true,
                configuration: block.configuration,
                content: block.content,
                styling: block.styling,
                responsive: block.responsive,
                animation: block.animation,
                conditions: block.conditions,
              }))
            })
          }
        }

        // Create revision
        const latestRevision = await tx.postRevision.findFirst({
          where: { postId: input.id },
          orderBy: { revisionNumber: 'desc' }
        })

        await tx.postRevision.create({
          data: {
            postId: input.id,
            title: updatedPost.title,
            content: updatedPost.content,
            contentHtml: updatedPost.contentHtml,
            excerpt: updatedPost.excerpt,
            revisionNumber: (latestRevision?.revisionNumber || 0) + 1,
            changeType: 'revision',
            changeSummary: 'Post updated',
            authorId: input.authorId,
            authorName: input.authorName,
            metadata: {
              updatedFields: Object.keys(input).filter(key => key !== 'id'),
              previousData: existingPost
            }
          }
        })

        return updatedPost
      })

      // Fetch the complete updated post
      const completePost = await this.getPostById(result.id)

      return {
        success: true,
        data: completePost.data,
        message: 'Post updated successfully'
      }

    } catch (error) {
      console.error('Error updating post:', error)
      return {
        success: false,
        error: 'Failed to update post'
      }
    }
  }

  /**
   * Delete a post (move to trash or permanently delete)
   */
  async deletePost(id: string, force: boolean = false): Promise<ApiResponse<boolean>> {
    try {
      const existingPost = await this.db.post.findUnique({
        where: { id }
      })

      if (!existingPost) {
        return {
          success: false,
          error: 'Post not found'
        }
      }

      if (force || existingPost.status === 'trash') {
        // Permanently delete
        await this.db.post.delete({
          where: { id }
        })

        return {
          success: true,
          data: true,
          message: 'Post permanently deleted'
        }
      } else {
        // Move to trash
        await this.db.post.update({
          where: { id },
          data: { status: 'trash' }
        })

        return {
          success: true,
          data: true,
          message: 'Post moved to trash'
        }
      }

    } catch (error) {
      console.error('Error deleting post:', error)
      return {
        success: false,
        error: 'Failed to delete post'
      }
    }
  }

  /**
   * Transform database post data to API format
   */
  private transformPostData(post: any): Post {
    return {
      id: post.id,
      title: post.title,
      slug: post.slug,
      content: post.content,
      contentHtml: post.contentHtml,
      excerpt: post.excerpt,
      status: post.status,
      postType: post.postType,
      parentId: post.parentId,
      menuOrder: post.menuOrder,
      featuredImage: post.featuredImage,
      featuredImageAlt: post.featuredImageAlt,
      template: post.template,
      password: post.password,
      publishedAt: post.publishedAt,
      scheduledAt: post.scheduledAt,
      authorId: post.authorId,
      authorName: post.authorName,
      authorEmail: post.authorEmail,
      seoTitle: post.seoTitle,
      seoDescription: post.seoDescription,
      seoKeywords: post.seoKeywords,
      ogImage: post.ogImage,
      ogTitle: post.ogTitle,
      ogDescription: post.ogDescription,
      twitterCard: post.twitterCard,
      canonicalUrl: post.canonicalUrl,
      metaRobots: post.metaRobots,
      viewCount: post.viewCount,
      shareCount: post.shareCount,
      likeCount: post.likeCount,
      commentCount: post.commentCount,
      usePageBuilder: post.usePageBuilder,
      pageBuilderData: post.pageBuilderData,
      allowComments: post.allowComments,
      allowPingbacks: post.allowPingbacks,
      isSticky: post.isSticky,
      isFeatured: post.isFeatured,
      customFields: post.customFields,
      metadata: post.metadata,
      createdAt: post.createdAt,
      updatedAt: post.updatedAt,
      postTypeRef: post.postTypeRef,
      parent: post.parent,
      children: post.children,
      taxonomyTerms: post.taxonomyTerms,
      meta: post.meta,
      comments: post.comments,
      revisions: post.revisions,
      blocks: post.blocks,
    }
  }
}
