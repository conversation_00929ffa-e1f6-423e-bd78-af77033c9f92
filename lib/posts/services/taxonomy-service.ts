// WordPress-Style Taxonomy Service
// Service for managing taxonomies (categories, tags, custom taxonomies)

import { PrismaClient } from '@prisma/client'
import { 
  Taxonomy, 
  TaxonomyTerm, 
  CreateTaxonomyInput, 
  CreateTaxonomyTermInput,
  ApiResponse,
  TaxonomyApiResponse
} from '../types'
import { prisma } from '../../ecommerce/config/database'
import { generateSlug } from '../utils'

export class TaxonomyService {
  private db: PrismaClient

  constructor(database?: PrismaClient) {
    this.db = database || prisma
  }

  /**
   * Register a new taxonomy
   */
  async registerTaxonomy(input: CreateTaxonomyInput): Promise<TaxonomyApiResponse> {
    try {
      // Check if taxonomy already exists
      const existingTaxonomy = await this.db.taxonomy.findUnique({
        where: { name: input.name }
      })

      if (existingTaxonomy) {
        return {
          success: false,
          error: `Taxonomy '${input.name}' already exists`
        }
      }

      // Validate taxonomy name
      if (!/^[a-z_][a-z0-9_]*$/.test(input.name)) {
        return {
          success: false,
          error: 'Taxonomy name must contain only lowercase letters, numbers, and underscores'
        }
      }

      const taxonomy = await this.db.taxonomy.create({
        data: {
          name: input.name,
          label: input.label,
          labelPlural: input.labelPlural,
          description: input.description,
          isHierarchical: input.isHierarchical ?? false,
          isPublic: input.isPublic ?? true,
          showInMenu: input.showInMenu ?? true,
          showInRest: input.showInRest ?? true,
          postTypes: input.postTypes || [],
          capabilities: input.capabilities,
          metaBoxCallback: input.metaBoxCallback,
          isSystem: false,
          isActive: true,
        }
      })

      return {
        success: true,
        data: this.transformTaxonomyData(taxonomy),
        message: 'Taxonomy registered successfully'
      }

    } catch (error) {
      console.error('Error registering taxonomy:', error)
      return {
        success: false,
        error: 'Failed to register taxonomy'
      }
    }
  }

  /**
   * Get all taxonomies
   */
  async getTaxonomies(includeInactive: boolean = false): Promise<ApiResponse<Taxonomy[]>> {
    try {
      const where = includeInactive ? {} : { isActive: true }
      
      const taxonomies = await this.db.taxonomy.findMany({
        where,
        include: {
          terms: {
            orderBy: { name: 'asc' },
            take: 10 // Limit terms for performance
          }
        },
        orderBy: { name: 'asc' }
      })

      return {
        success: true,
        data: taxonomies.map(t => this.transformTaxonomyData(t))
      }

    } catch (error) {
      console.error('Error fetching taxonomies:', error)
      return {
        success: false,
        error: 'Failed to fetch taxonomies'
      }
    }
  }

  /**
   * Get taxonomy by name
   */
  async getTaxonomy(name: string): Promise<TaxonomyApiResponse> {
    try {
      const taxonomy = await this.db.taxonomy.findUnique({
        where: { name },
        include: {
          terms: {
            orderBy: { name: 'asc' }
          }
        }
      })

      if (!taxonomy) {
        return {
          success: false,
          error: 'Taxonomy not found'
        }
      }

      return {
        success: true,
        data: this.transformTaxonomyData(taxonomy)
      }

    } catch (error) {
      console.error('Error fetching taxonomy:', error)
      return {
        success: false,
        error: 'Failed to fetch taxonomy'
      }
    }
  }

  /**
   * Create a new taxonomy term
   */
  async createTerm(input: CreateTaxonomyTermInput): Promise<ApiResponse<TaxonomyTerm>> {
    try {
      // Validate taxonomy exists
      const taxonomy = await this.db.taxonomy.findUnique({
        where: { id: input.taxonomyId }
      })

      if (!taxonomy) {
        return {
          success: false,
          error: 'Taxonomy not found'
        }
      }

      // Generate slug if not provided
      const slug = input.slug || generateSlug(input.name)

      // Check if slug is unique within taxonomy
      const existingTerm = await this.db.taxonomyTerm.findFirst({
        where: {
          taxonomyId: input.taxonomyId,
          slug
        }
      })

      if (existingTerm) {
        return {
          success: false,
          error: 'A term with this slug already exists in this taxonomy'
        }
      }

      // Validate parent if hierarchical
      if (input.parentId && taxonomy.isHierarchical) {
        const parent = await this.db.taxonomyTerm.findFirst({
          where: {
            id: input.parentId,
            taxonomyId: input.taxonomyId
          }
        })

        if (!parent) {
          return {
            success: false,
            error: 'Parent term not found'
          }
        }
      }

      const term = await this.db.taxonomyTerm.create({
        data: {
          name: input.name,
          slug,
          description: input.description,
          taxonomyId: input.taxonomyId,
          parentId: input.parentId,
          color: input.color,
          image: input.image,
          metadata: input.metadata,
        },
        include: {
          taxonomy: true,
          parent: true,
          children: true
        }
      })

      return {
        success: true,
        data: this.transformTermData(term),
        message: 'Term created successfully'
      }

    } catch (error) {
      console.error('Error creating term:', error)
      return {
        success: false,
        error: 'Failed to create term'
      }
    }
  }

  /**
   * Get terms for a taxonomy
   */
  async getTerms(
    taxonomyName: string, 
    parentId?: string,
    includeChildren: boolean = true
  ): Promise<ApiResponse<TaxonomyTerm[]>> {
    try {
      const taxonomy = await this.db.taxonomy.findUnique({
        where: { name: taxonomyName }
      })

      if (!taxonomy) {
        return {
          success: false,
          error: 'Taxonomy not found'
        }
      }

      const where: any = {
        taxonomyId: taxonomy.id
      }

      if (parentId !== undefined) {
        where.parentId = parentId
      }

      const terms = await this.db.taxonomyTerm.findMany({
        where,
        include: {
          taxonomy: true,
          parent: true,
          children: includeChildren,
          posts: {
            include: {
              post: {
                select: {
                  id: true,
                  title: true,
                  slug: true,
                  status: true
                }
              }
            }
          }
        },
        orderBy: { name: 'asc' }
      })

      return {
        success: true,
        data: terms.map(t => this.transformTermData(t))
      }

    } catch (error) {
      console.error('Error fetching terms:', error)
      return {
        success: false,
        error: 'Failed to fetch terms'
      }
    }
  }

  /**
   * Update term count for a taxonomy term
   */
  async updateTermCount(termId: string): Promise<void> {
    try {
      const count = await this.db.postTaxonomyTerm.count({
        where: { termId }
      })

      await this.db.taxonomyTerm.update({
        where: { id: termId },
        data: { count }
      })
    } catch (error) {
      console.error('Error updating term count:', error)
    }
  }

  /**
   * Initialize default taxonomies
   */
  async initializeDefaultTaxonomies(): Promise<ApiResponse<boolean>> {
    try {
      const defaultTaxonomies = [
        {
          name: 'category',
          label: 'Category',
          labelPlural: 'Categories',
          description: 'Post categories',
          isHierarchical: true,
          postTypes: ['post'],
          isSystem: true,
        },
        {
          name: 'tag',
          label: 'Tag',
          labelPlural: 'Tags',
          description: 'Post tags',
          isHierarchical: false,
          postTypes: ['post'],
          isSystem: true,
        },
        {
          name: 'product_category',
          label: 'Product Category',
          labelPlural: 'Product Categories',
          description: 'Product categories',
          isHierarchical: true,
          postTypes: ['product'],
          isSystem: true,
        },
        {
          name: 'product_tag',
          label: 'Product Tag',
          labelPlural: 'Product Tags',
          description: 'Product tags',
          isHierarchical: false,
          postTypes: ['product'],
          isSystem: true,
        }
      ]

      for (const taxonomyData of defaultTaxonomies) {
        const existing = await this.db.taxonomy.findUnique({
          where: { name: taxonomyData.name }
        })

        if (!existing) {
          await this.db.taxonomy.create({
            data: taxonomyData
          })
        }
      }

      return {
        success: true,
        data: true,
        message: 'Default taxonomies initialized successfully'
      }

    } catch (error) {
      console.error('Error initializing default taxonomies:', error)
      return {
        success: false,
        error: 'Failed to initialize default taxonomies'
      }
    }
  }

  /**
   * Transform database taxonomy data to API format
   */
  private transformTaxonomyData(taxonomy: any): Taxonomy {
    return {
      id: taxonomy.id,
      name: taxonomy.name,
      label: taxonomy.label,
      labelPlural: taxonomy.labelPlural,
      description: taxonomy.description,
      isHierarchical: taxonomy.isHierarchical,
      isPublic: taxonomy.isPublic,
      showInMenu: taxonomy.showInMenu,
      showInRest: taxonomy.showInRest,
      postTypes: taxonomy.postTypes,
      capabilities: taxonomy.capabilities,
      metaBoxCallback: taxonomy.metaBoxCallback,
      isSystem: taxonomy.isSystem,
      isActive: taxonomy.isActive,
      createdAt: taxonomy.createdAt,
      updatedAt: taxonomy.updatedAt,
      terms: taxonomy.terms?.map((t: any) => this.transformTermData(t))
    }
  }

  /**
   * Transform database term data to API format
   */
  private transformTermData(term: any): TaxonomyTerm {
    return {
      id: term.id,
      name: term.name,
      slug: term.slug,
      description: term.description,
      taxonomyId: term.taxonomyId,
      parentId: term.parentId,
      count: term.count,
      color: term.color,
      image: term.image,
      metadata: term.metadata,
      createdAt: term.createdAt,
      updatedAt: term.updatedAt,
      taxonomy: term.taxonomy ? this.transformTaxonomyData(term.taxonomy) : undefined,
      parent: term.parent ? this.transformTermData(term.parent) : undefined,
      children: term.children?.map((c: any) => this.transformTermData(c)),
      posts: term.posts
    }
  }
}
