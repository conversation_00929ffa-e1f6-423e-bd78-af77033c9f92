// Post Type Templates
// Pre-configured templates for common post types

import { CreatePostTypeInput } from '../types'

export interface PostTypeTemplate {
  id: string
  name: string
  description: string
  icon: string
  category: 'content' | 'ecommerce' | 'portfolio' | 'business' | 'other'
  config: CreatePostTypeInput
  suggestedTaxonomies?: {
    name: string
    label: string
    labelPlural: string
    description: string
    isHierarchical: boolean
  }[]
  suggestedFields?: {
    name: string
    type: string
    label: string
    required: boolean
    description?: string
  }[]
}

export const postTypeTemplates: PostTypeTemplate[] = [
  // Content Templates
  {
    id: 'portfolio',
    name: 'Portfolio',
    description: 'Showcase your work, projects, and creative pieces',
    icon: '🎨',
    category: 'portfolio',
    config: {
      name: 'portfolio',
      label: 'Portfolio Item',
      labelPlural: 'Portfolio Items',
      description: 'Showcase creative work and projects',
      icon: '🎨',
      isPublic: true,
      isHierarchical: false,
      hasArchive: true,
      supportsTitle: true,
      supportsContent: true,
      supportsExcerpt: true,
      supportsThumbnail: true,
      supportsComments: false,
      supportsRevisions: true,
      supportsPageBuilder: true,
      menuPosition: 20,
      taxonomies: ['portfolio_category', 'portfolio_tag'],
      templates: ['single-portfolio', 'archive-portfolio'],
    },
    suggestedTaxonomies: [
      {
        name: 'portfolio_category',
        label: 'Portfolio Category',
        labelPlural: 'Portfolio Categories',
        description: 'Categories for portfolio items',
        isHierarchical: true
      },
      {
        name: 'portfolio_tag',
        label: 'Portfolio Tag',
        labelPlural: 'Portfolio Tags',
        description: 'Tags for portfolio items',
        isHierarchical: false
      }
    ],
    suggestedFields: [
      { name: 'client_name', type: 'string', label: 'Client Name', required: false },
      { name: 'project_url', type: 'string', label: 'Project URL', required: false },
      { name: 'completion_date', type: 'string', label: 'Completion Date', required: false },
      { name: 'technologies', type: 'array', label: 'Technologies Used', required: false },
      { name: 'gallery', type: 'array', label: 'Image Gallery', required: false }
    ]
  },
  {
    id: 'testimonial',
    name: 'Testimonials',
    description: 'Customer reviews and testimonials',
    icon: '💬',
    category: 'business',
    config: {
      name: 'testimonial',
      label: 'Testimonial',
      labelPlural: 'Testimonials',
      description: 'Customer reviews and testimonials',
      icon: '💬',
      isPublic: true,
      isHierarchical: false,
      hasArchive: true,
      supportsTitle: true,
      supportsContent: true,
      supportsExcerpt: false,
      supportsThumbnail: true,
      supportsComments: false,
      supportsRevisions: true,
      supportsPageBuilder: false,
      menuPosition: 30,
      taxonomies: ['testimonial_category'],
      templates: ['single-testimonial', 'archive-testimonial'],
    },
    suggestedTaxonomies: [
      {
        name: 'testimonial_category',
        label: 'Testimonial Category',
        labelPlural: 'Testimonial Categories',
        description: 'Categories for testimonials',
        isHierarchical: true
      }
    ],
    suggestedFields: [
      { name: 'customer_name', type: 'string', label: 'Customer Name', required: true },
      { name: 'customer_title', type: 'string', label: 'Customer Title', required: false },
      { name: 'company', type: 'string', label: 'Company', required: false },
      { name: 'rating', type: 'number', label: 'Rating (1-5)', required: false },
      { name: 'customer_photo', type: 'string', label: 'Customer Photo', required: false }
    ]
  },
  {
    id: 'team',
    name: 'Team Members',
    description: 'Staff profiles and team member information',
    icon: '👥',
    category: 'business',
    config: {
      name: 'team_member',
      label: 'Team Member',
      labelPlural: 'Team Members',
      description: 'Staff profiles and team information',
      icon: '👥',
      isPublic: true,
      isHierarchical: false,
      hasArchive: true,
      supportsTitle: true,
      supportsContent: true,
      supportsExcerpt: true,
      supportsThumbnail: true,
      supportsComments: false,
      supportsRevisions: true,
      supportsPageBuilder: false,
      menuPosition: 35,
      taxonomies: ['team_department'],
      templates: ['single-team-member', 'archive-team'],
    },
    suggestedTaxonomies: [
      {
        name: 'team_department',
        label: 'Department',
        labelPlural: 'Departments',
        description: 'Team departments',
        isHierarchical: true
      }
    ],
    suggestedFields: [
      { name: 'job_title', type: 'string', label: 'Job Title', required: true },
      { name: 'email', type: 'string', label: 'Email', required: false },
      { name: 'phone', type: 'string', label: 'Phone', required: false },
      { name: 'linkedin', type: 'string', label: 'LinkedIn URL', required: false },
      { name: 'twitter', type: 'string', label: 'Twitter URL', required: false },
      { name: 'bio', type: 'string', label: 'Short Bio', required: false }
    ]
  },
  // E-commerce Templates
  {
    id: 'service',
    name: 'Services',
    description: 'Business services and offerings',
    icon: '🛠️',
    category: 'business',
    config: {
      name: 'service',
      label: 'Service',
      labelPlural: 'Services',
      description: 'Business services and offerings',
      icon: '🛠️',
      isPublic: true,
      isHierarchical: true,
      hasArchive: true,
      supportsTitle: true,
      supportsContent: true,
      supportsExcerpt: true,
      supportsThumbnail: true,
      supportsComments: false,
      supportsRevisions: true,
      supportsPageBuilder: true,
      menuPosition: 25,
      taxonomies: ['service_category'],
      templates: ['single-service', 'archive-service'],
    },
    suggestedTaxonomies: [
      {
        name: 'service_category',
        label: 'Service Category',
        labelPlural: 'Service Categories',
        description: 'Categories for services',
        isHierarchical: true
      }
    ],
    suggestedFields: [
      { name: 'price', type: 'number', label: 'Price', required: false },
      { name: 'duration', type: 'string', label: 'Duration', required: false },
      { name: 'features', type: 'array', label: 'Features', required: false },
      { name: 'booking_url', type: 'string', label: 'Booking URL', required: false }
    ]
  },
  // Content Templates
  {
    id: 'faq',
    name: 'FAQ',
    description: 'Frequently asked questions',
    icon: '❓',
    category: 'content',
    config: {
      name: 'faq',
      label: 'FAQ',
      labelPlural: 'FAQs',
      description: 'Frequently asked questions',
      icon: '❓',
      isPublic: true,
      isHierarchical: false,
      hasArchive: true,
      supportsTitle: true,
      supportsContent: true,
      supportsExcerpt: false,
      supportsThumbnail: false,
      supportsComments: false,
      supportsRevisions: true,
      supportsPageBuilder: false,
      menuPosition: 40,
      taxonomies: ['faq_category'],
      templates: ['single-faq', 'archive-faq'],
    },
    suggestedTaxonomies: [
      {
        name: 'faq_category',
        label: 'FAQ Category',
        labelPlural: 'FAQ Categories',
        description: 'Categories for FAQs',
        isHierarchical: true
      }
    ],
    suggestedFields: [
      { name: 'order', type: 'number', label: 'Display Order', required: false },
      { name: 'is_featured', type: 'boolean', label: 'Featured FAQ', required: false }
    ]
  },
  {
    id: 'event',
    name: 'Events',
    description: 'Events, workshops, and activities',
    icon: '📅',
    category: 'content',
    config: {
      name: 'event',
      label: 'Event',
      labelPlural: 'Events',
      description: 'Events, workshops, and activities',
      icon: '📅',
      isPublic: true,
      isHierarchical: false,
      hasArchive: true,
      supportsTitle: true,
      supportsContent: true,
      supportsExcerpt: true,
      supportsThumbnail: true,
      supportsComments: true,
      supportsRevisions: true,
      supportsPageBuilder: true,
      menuPosition: 28,
      taxonomies: ['event_category', 'event_tag'],
      templates: ['single-event', 'archive-event'],
    },
    suggestedTaxonomies: [
      {
        name: 'event_category',
        label: 'Event Category',
        labelPlural: 'Event Categories',
        description: 'Categories for events',
        isHierarchical: true
      },
      {
        name: 'event_tag',
        label: 'Event Tag',
        labelPlural: 'Event Tags',
        description: 'Tags for events',
        isHierarchical: false
      }
    ],
    suggestedFields: [
      { name: 'start_date', type: 'string', label: 'Start Date', required: true },
      { name: 'end_date', type: 'string', label: 'End Date', required: false },
      { name: 'location', type: 'string', label: 'Location', required: false },
      { name: 'price', type: 'number', label: 'Ticket Price', required: false },
      { name: 'capacity', type: 'number', label: 'Max Capacity', required: false },
      { name: 'registration_url', type: 'string', label: 'Registration URL', required: false }
    ]
  },
  {
    id: 'recipe',
    name: 'Recipes',
    description: 'Food recipes and cooking instructions',
    icon: '🍳',
    category: 'content',
    config: {
      name: 'recipe',
      label: 'Recipe',
      labelPlural: 'Recipes',
      description: 'Food recipes and cooking instructions',
      icon: '🍳',
      isPublic: true,
      isHierarchical: false,
      hasArchive: true,
      supportsTitle: true,
      supportsContent: true,
      supportsExcerpt: true,
      supportsThumbnail: true,
      supportsComments: true,
      supportsRevisions: true,
      supportsPageBuilder: false,
      menuPosition: 32,
      taxonomies: ['recipe_category', 'recipe_cuisine', 'recipe_diet'],
      templates: ['single-recipe', 'archive-recipe'],
    },
    suggestedTaxonomies: [
      {
        name: 'recipe_category',
        label: 'Recipe Category',
        labelPlural: 'Recipe Categories',
        description: 'Categories for recipes (appetizer, main course, etc.)',
        isHierarchical: true
      },
      {
        name: 'recipe_cuisine',
        label: 'Cuisine',
        labelPlural: 'Cuisines',
        description: 'Cuisine types (Italian, Asian, etc.)',
        isHierarchical: false
      },
      {
        name: 'recipe_diet',
        label: 'Dietary Restriction',
        labelPlural: 'Dietary Restrictions',
        description: 'Dietary restrictions (vegan, gluten-free, etc.)',
        isHierarchical: false
      }
    ],
    suggestedFields: [
      { name: 'prep_time', type: 'number', label: 'Prep Time (minutes)', required: false },
      { name: 'cook_time', type: 'number', label: 'Cook Time (minutes)', required: false },
      { name: 'servings', type: 'number', label: 'Servings', required: false },
      { name: 'difficulty', type: 'string', label: 'Difficulty Level', required: false },
      { name: 'ingredients', type: 'array', label: 'Ingredients', required: false },
      { name: 'instructions', type: 'array', label: 'Instructions', required: false },
      { name: 'nutrition', type: 'object', label: 'Nutrition Info', required: false }
    ]
  }
]

export const getTemplatesByCategory = (category: string) => {
  return postTypeTemplates.filter(template => template.category === category)
}

export const getTemplateById = (id: string) => {
  return postTypeTemplates.find(template => template.id === id)
}

export const getAllCategories = () => {
  return [...new Set(postTypeTemplates.map(template => template.category))]
}
