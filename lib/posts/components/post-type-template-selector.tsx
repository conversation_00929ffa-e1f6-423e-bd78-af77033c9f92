'use client'

// Post Type Template Selector
// Component for selecting from pre-built post type templates

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Search, 
  Sparkles, 
  ArrowRight, 

  FileText,
  ShoppingBag,
  Briefcase,
  Palette,
  MoreHorizontal
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { 
  postTypeTemplates, 
  getTemplatesByCategory, 
  getAllCategories,
  PostTypeTemplate 
} from '../templates/post-type-templates'

interface PostTypeTemplateSelectorProps {
  onSelectTemplate: (template: PostTypeTemplate) => void
  onCreateCustom: () => void
  className?: string
}

export function PostTypeTemplateSelector({
  onSelectTemplate,
  onCreateCustom,
  className
}: PostTypeTemplateSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')

  const categories = getAllCategories()
  const categoryIcons = {
    content: FileText,
    ecommerce: ShoppingBag,
    business: Briefcase,
    portfolio: Palette,
    other: MoreHorizontal
  }

  const categoryLabels = {
    content: 'Content',
    ecommerce: 'E-commerce',
    business: 'Business',
    portfolio: 'Portfolio',
    other: 'Other'
  }

  // Filter templates based on search and category
  const filteredTemplates = postTypeTemplates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const TemplateCard = ({ template }: { template: PostTypeTemplate }) => (
    <Card 
      className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:border-primary/50"
      onClick={() => onSelectTemplate(template)}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="text-2xl">{template.icon}</div>
            <div>
              <CardTitle className="text-lg">{template.name}</CardTitle>
              <Badge variant="secondary" className="text-xs mt-1">
                {categoryLabels[template.category as keyof typeof categoryLabels]}
              </Badge>
            </div>
          </div>
          <ArrowRight className="h-4 w-4 text-muted-foreground" />
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm text-muted-foreground mb-4">
          {template.description}
        </p>
        
        <div className="space-y-3">
          {/* Features */}
          <div>
            <div className="text-xs font-medium text-muted-foreground mb-1">Features</div>
            <div className="flex flex-wrap gap-1">
              {template.config.supportsTitle && <Badge variant="outline" className="text-xs">Title</Badge>}
              {template.config.supportsContent && <Badge variant="outline" className="text-xs">Content</Badge>}
              {template.config.supportsThumbnail && <Badge variant="outline" className="text-xs">Images</Badge>}
              {template.config.supportsComments && <Badge variant="outline" className="text-xs">Comments</Badge>}
              {template.config.supportsPageBuilder && <Badge variant="outline" className="text-xs">Page Builder</Badge>}
            </div>
          </div>

          {/* Custom Fields */}
          {template.suggestedFields && template.suggestedFields.length > 0 && (
            <div>
              <div className="text-xs font-medium text-muted-foreground mb-1">
                Custom Fields ({template.suggestedFields.length})
              </div>
              <div className="text-xs text-muted-foreground">
                {template.suggestedFields.slice(0, 3).map(field => field.label).join(', ')}
                {template.suggestedFields.length > 3 && ` +${template.suggestedFields.length - 3} more`}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className={cn('max-w-6xl mx-auto p-6', className)}>
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2">Choose a Post Type Template</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Start with a pre-built template or create a custom post type from scratch. 
          Templates include suggested fields and configurations to get you started quickly.
        </p>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative max-w-md mx-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Categories */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="mb-8">
        <TabsList className="grid w-full grid-cols-6 max-w-2xl mx-auto">
          <TabsTrigger value="all" className="flex items-center gap-2">
            All
          </TabsTrigger>
          {categories.map((category) => {
            const Icon = categoryIcons[category as keyof typeof categoryIcons]
            return (
              <TabsTrigger key={category} value={category} className="flex items-center gap-2">
                <Icon className="h-4 w-4" />
                {categoryLabels[category as keyof typeof categoryLabels]}
              </TabsTrigger>
            )
          })}
        </TabsList>

        <TabsContent value="all" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map((template) => (
              <TemplateCard key={template.id} template={template} />
            ))}
          </div>
        </TabsContent>

        {categories.map((category) => (
          <TabsContent key={category} value={category} className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {getTemplatesByCategory(category).filter(template => 
                template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                template.description.toLowerCase().includes(searchTerm.toLowerCase())
              ).map((template) => (
                <TemplateCard key={template.id} template={template} />
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>

      {/* No Results */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <div className="text-4xl mb-4">🔍</div>
          <h3 className="text-lg font-medium mb-2">No templates found</h3>
          <p className="text-muted-foreground mb-6">
            Try adjusting your search terms or browse different categories
          </p>
          <Button onClick={onCreateCustom}>
            <Sparkles className="h-4 w-4 mr-2" />
            Create Custom Post Type
          </Button>
        </div>
      )}

      {/* Custom Option */}
      {filteredTemplates.length > 0 && (
        <div className="mt-12 pt-8 border-t">
          <div className="text-center">
            <h3 className="text-lg font-medium mb-2">Need something different?</h3>
            <p className="text-muted-foreground mb-4">
              Create a custom post type with your own configuration
            </p>
            <Button variant="outline" onClick={onCreateCustom}>
              <Sparkles className="h-4 w-4 mr-2" />
              Create Custom Post Type
            </Button>
          </div>
        </div>
      )}

      {/* Template Stats */}
      <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{postTypeTemplates.length}</div>
            <div className="text-sm text-muted-foreground">Templates Available</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{categories.length}</div>
            <div className="text-sm text-muted-foreground">Categories</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">
              {postTypeTemplates.reduce((acc, t) => acc + (t.suggestedFields?.length || 0), 0)}
            </div>
            <div className="text-sm text-muted-foreground">Custom Fields</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">
              {postTypeTemplates.reduce((acc, t) => acc + (t.suggestedTaxonomies?.length || 0), 0)}
            </div>
            <div className="text-sm text-muted-foreground">Taxonomies</div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
