'use client'

// Custom Post Type Builder Component
// Advanced interface for creating and editing custom post types

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Save, 
  Eye, 
  Settings, 
  FileText, 
  Tag, 
  Plus, 
  X, 
  Info,
  Code,
  Palette,
  Grid,
  List,
  MessageSquare,
  Image,
  Calendar,
  Archive
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { PostType, CreatePostTypeInput, Taxonomy } from '../types'

interface PostTypeBuilderProps {
  postType?: PostType
  onSave: (data: CreatePostTypeInput) => Promise<void>
  onPreview?: () => void
  className?: string
  isLoading?: boolean
}

export function PostTypeBuilder({
  postType,
  onSave,
  onPreview,
  className,
  isLoading = false
}: PostTypeBuilderProps) {
  // Form state
  const [formData, setFormData] = useState<Partial<CreatePostTypeInput>>({
    name: postType?.name || '',
    label: postType?.label || '',
    labelPlural: postType?.labelPlural || '',
    description: postType?.description || '',
    icon: postType?.icon || '📄',
    isPublic: postType?.isPublic ?? true,
    isHierarchical: postType?.isHierarchical ?? false,
    hasArchive: postType?.hasArchive ?? true,
    supportsTitle: postType?.supportsTitle ?? true,
    supportsContent: postType?.supportsContent ?? true,
    supportsExcerpt: postType?.supportsExcerpt ?? false,
    supportsThumbnail: postType?.supportsThumbnail ?? false,
    supportsComments: postType?.supportsComments ?? false,
    supportsRevisions: postType?.supportsRevisions ?? true,
    supportsPageBuilder: postType?.supportsPageBuilder ?? false,
    menuPosition: postType?.menuPosition || 25,
    taxonomies: postType?.taxonomies || [],
    templates: postType?.templates || [],
    customFields: postType?.customFields || {},
  })

  const [availableTaxonomies, setAvailableTaxonomies] = useState<Taxonomy[]>([])
  const [newTaxonomy, setNewTaxonomy] = useState('')
  const [newTemplate, setNewTemplate] = useState('')
  const [customFieldName, setCustomFieldName] = useState('')
  const [customFieldType, setCustomFieldType] = useState('string')
  const [isDirty, setIsDirty] = useState(false)

  // Fetch available taxonomies
  useEffect(() => {
    const fetchTaxonomies = async () => {
      try {
        const response = await fetch('/api/taxonomies')
        const data = await response.json()
        if (data.success) {
          setAvailableTaxonomies(data.data)
        }
      } catch (error) {
        console.error('Error fetching taxonomies:', error)
      }
    }
    fetchTaxonomies()
  }, [])

  // Auto-generate name from label
  useEffect(() => {
    if (formData.label && !postType) {
      const generatedName = formData.label
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '_')
      updateFormData({ name: generatedName })
    }
  }, [formData.label, postType])

  // Auto-generate plural label
  useEffect(() => {
    if (formData.label && !formData.labelPlural) {
      const plural = formData.label.endsWith('s') 
        ? formData.label 
        : formData.label + 's'
      updateFormData({ labelPlural: plural })
    }
  }, [formData.label, formData.labelPlural])

  // Update form data
  const updateFormData = (updates: Partial<CreatePostTypeInput>) => {
    setFormData(prev => ({ ...prev, ...updates }))
    setIsDirty(true)
  }

  // Handle save
  const handleSave = async () => {
    await onSave(formData as CreatePostTypeInput)
    setIsDirty(false)
  }

  // Add taxonomy
  const addTaxonomy = () => {
    if (newTaxonomy && !formData.taxonomies?.includes(newTaxonomy)) {
      updateFormData({
        taxonomies: [...(formData.taxonomies || []), newTaxonomy]
      })
      setNewTaxonomy('')
    }
  }

  // Remove taxonomy
  const removeTaxonomy = (taxonomy: string) => {
    updateFormData({
      taxonomies: formData.taxonomies?.filter(t => t !== taxonomy) || []
    })
  }

  // Add template
  const addTemplate = () => {
    if (newTemplate && !formData.templates?.includes(newTemplate)) {
      updateFormData({
        templates: [...(formData.templates || []), newTemplate]
      })
      setNewTemplate('')
    }
  }

  // Remove template
  const removeTemplate = (template: string) => {
    updateFormData({
      templates: formData.templates?.filter(t => t !== template) || []
    })
  }

  // Add custom field
  const addCustomField = () => {
    if (customFieldName) {
      const newFields = {
        ...formData.customFields,
        [customFieldName]: {
          type: customFieldType,
          label: customFieldName.charAt(0).toUpperCase() + customFieldName.slice(1),
          required: false
        }
      }
      updateFormData({ customFields: newFields })
      setCustomFieldName('')
      setCustomFieldType('string')
    }
  }

  // Remove custom field
  const removeCustomField = (fieldName: string) => {
    const newFields = { ...formData.customFields }
    delete newFields[fieldName]
    updateFormData({ customFields: newFields })
  }

  const iconOptions = [
    '📄', '📝', '📰', '📚', '📖', '🗞️', '📋', '📌', '📎', '🔖',
    '🛍️', '🛒', '💼', '📦', '🎁', '🏷️', '💳', '💰', '📊', '📈',
    '🎨', '🖼️', '📷', '🎭', '🎪', '🎬', '🎵', '🎤', '🎸', '🎹',
    '👤', '👥', '🏢', '🏠', '🌟', '⭐', '🔥', '💡', '🚀', '⚡'
  ]

  const supportFeatures = [
    { key: 'supportsTitle', label: 'Title', icon: FileText, description: 'Post title field' },
    { key: 'supportsContent', label: 'Content', icon: FileText, description: 'Main content editor' },
    { key: 'supportsExcerpt', label: 'Excerpt', icon: FileText, description: 'Short summary field' },
    { key: 'supportsThumbnail', label: 'Featured Image', icon: Image, description: 'Featured image support' },
    { key: 'supportsComments', label: 'Comments', icon: MessageSquare, description: 'Comment system' },
    { key: 'supportsRevisions', label: 'Revisions', icon: Archive, description: 'Version control' },
    { key: 'supportsPageBuilder', label: 'Page Builder', icon: Grid, description: 'Visual page builder' },
  ]

  return (
    <div className={cn('max-w-6xl mx-auto p-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">
            {postType ? 'Edit' : 'Create'} Post Type
          </h1>
          {isDirty && (
            <Badge variant="secondary" className="mt-1">
              Unsaved changes
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {onPreview && (
            <Button variant="outline" onClick={onPreview}>
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>
          )}
          
          <Button 
            onClick={handleSave}
            disabled={isLoading || !formData.name || !formData.label}
          >
            <Save className="h-4 w-4 mr-2" />
            {postType ? 'Update' : 'Create'} Post Type
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="basic" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic</TabsTrigger>
              <TabsTrigger value="features">Features</TabsTrigger>
              <TabsTrigger value="taxonomies">Taxonomies</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="label">Label *</Label>
                      <Input
                        id="label"
                        value={formData.label}
                        onChange={(e) => updateFormData({ label: e.target.value })}
                        placeholder="e.g., Product"
                      />
                    </div>
                    <div>
                      <Label htmlFor="labelPlural">Plural Label *</Label>
                      <Input
                        id="labelPlural"
                        value={formData.labelPlural}
                        onChange={(e) => updateFormData({ labelPlural: e.target.value })}
                        placeholder="e.g., Products"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="name">Name (Slug) *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => updateFormData({ name: e.target.value })}
                      placeholder="e.g., product"
                      disabled={!!postType}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Lowercase letters, numbers, and underscores only. Cannot be changed after creation.
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => updateFormData({ description: e.target.value })}
                      placeholder="Brief description of this post type..."
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="icon">Icon</Label>
                    <div className="flex items-center gap-2 mt-1">
                      <div className="text-2xl">{formData.icon}</div>
                      <Select value={formData.icon} onValueChange={(value) => updateFormData({ icon: value })}>
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <div className="grid grid-cols-8 gap-1 p-2">
                            {iconOptions.map((icon) => (
                              <SelectItem key={icon} value={icon} className="text-center">
                                <span className="text-lg">{icon}</span>
                              </SelectItem>
                            ))}
                          </div>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="menuPosition">Menu Position</Label>
                    <Input
                      id="menuPosition"
                      type="number"
                      value={formData.menuPosition}
                      onChange={(e) => updateFormData({ menuPosition: parseInt(e.target.value) })}
                      placeholder="25"
                      min="1"
                      max="100"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Position in admin menu (lower numbers appear first)
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="features" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Grid className="h-5 w-5" />
                    Supported Features
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {supportFeatures.map((feature) => (
                    <div key={feature.key} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <feature.icon className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{feature.label}</div>
                          <div className="text-sm text-muted-foreground">{feature.description}</div>
                        </div>
                      </div>
                      <Switch
                        checked={formData[feature.key as keyof CreatePostTypeInput] as boolean}
                        onCheckedChange={(checked) => updateFormData({ [feature.key]: checked })}
                      />
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Behavior Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">Public</div>
                      <div className="text-sm text-muted-foreground">Show in frontend and REST API</div>
                    </div>
                    <Switch
                      checked={formData.isPublic}
                      onCheckedChange={(checked) => updateFormData({ isPublic: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">Hierarchical</div>
                      <div className="text-sm text-muted-foreground">Can have parent/child relationships</div>
                    </div>
                    <Switch
                      checked={formData.isHierarchical}
                      onCheckedChange={(checked) => updateFormData({ isHierarchical: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">Has Archive</div>
                      <div className="text-sm text-muted-foreground">Generate archive/listing pages</div>
                    </div>
                    <Switch
                      checked={formData.hasArchive}
                      onCheckedChange={(checked) => updateFormData({ hasArchive: checked })}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="taxonomies" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Tag className="h-5 w-5" />
                    Taxonomies
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <Select value={newTaxonomy} onValueChange={setNewTaxonomy}>
                      <SelectTrigger className="flex-1">
                        <SelectValue placeholder="Select taxonomy..." />
                      </SelectTrigger>
                      <SelectContent>
                        {availableTaxonomies.map((taxonomy) => (
                          <SelectItem key={taxonomy.id} value={taxonomy.name}>
                            {taxonomy.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button onClick={addTaxonomy} disabled={!newTaxonomy}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="space-y-2">
                    {formData.taxonomies?.map((taxonomy) => (
                      <div key={taxonomy} className="flex items-center justify-between p-2 bg-muted rounded">
                        <span>{availableTaxonomies.find(t => t.name === taxonomy)?.label || taxonomy}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeTaxonomy(taxonomy)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Code className="h-5 w-5" />
                    Templates
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <Input
                      value={newTemplate}
                      onChange={(e) => setNewTemplate(e.target.value)}
                      placeholder="e.g., single-product, archive-product"
                      className="flex-1"
                    />
                    <Button onClick={addTemplate} disabled={!newTemplate}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="space-y-2">
                    {formData.templates?.map((template) => (
                      <div key={template} className="flex items-center justify-between p-2 bg-muted rounded">
                        <span>{template}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeTemplate(template)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5" />
                    Custom Fields
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <Input
                      value={customFieldName}
                      onChange={(e) => setCustomFieldName(e.target.value)}
                      placeholder="Field name"
                      className="flex-1"
                    />
                    <Select value={customFieldType} onValueChange={setCustomFieldType}>
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="string">Text</SelectItem>
                        <SelectItem value="number">Number</SelectItem>
                        <SelectItem value="boolean">Boolean</SelectItem>
                        <SelectItem value="array">Array</SelectItem>
                        <SelectItem value="object">Object</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button onClick={addCustomField} disabled={!customFieldName}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="space-y-2">
                    {Object.entries(formData.customFields || {}).map(([fieldName, fieldConfig]) => (
                      <div key={fieldName} className="flex items-center justify-between p-2 bg-muted rounded">
                        <div>
                          <span className="font-medium">{fieldName}</span>
                          <span className="text-sm text-muted-foreground ml-2">
                            ({(fieldConfig as any)?.type || 'string'})
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCustomField(fieldName)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Preview</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 border rounded-lg bg-muted/50">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg">{formData.icon}</span>
                  <span className="font-medium">{formData.label || 'Post Type'}</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  {formData.description || 'No description provided'}
                </p>
                <div className="mt-2 text-xs text-muted-foreground">
                  Slug: {formData.name || 'post_type'}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Help */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Info className="h-4 w-4" />
                Help
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <p><strong>Name:</strong> Unique identifier (cannot be changed)</p>
              <p><strong>Label:</strong> Display name in admin</p>
              <p><strong>Hierarchical:</strong> Like pages (parent/child)</p>
              <p><strong>Archive:</strong> Creates listing pages</p>
              <p><strong>Taxonomies:</strong> Categories and tags</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
