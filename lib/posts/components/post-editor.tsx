'use client'

// WordPress-Style Post Editor Component
// Advanced editor for creating and editing posts with all WordPress-like features

import React, { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { RichTextEditor } from './rich-text-editor'
import { 
  Save, 
  Eye, 
  Calendar, 
  Image, 
  Tag, 
  Settings,
  FileText,
  Globe,
  Lock,
  Trash2
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Post, PostStatus, CreatePostInput, UpdatePostInput } from '../types'

interface PostEditorProps {
  post?: Post
  postType: string
  onSave: (data: CreatePostInput | UpdatePostInput) => Promise<void>
  onPreview?: () => void
  onDelete?: () => Promise<void>
  className?: string
  isLoading?: boolean
}

export function PostEditor({
  post,
  postType,
  onSave,
  onPreview,
  onDelete,
  className,
  isLoading = false
}: PostEditorProps) {
  // Form state
  const [formData, setFormData] = useState<Partial<CreatePostInput>>({
    title: post?.title || '',
    slug: post?.slug || '',
    content: post?.content || '',
    excerpt: post?.excerpt || '',
    status: post?.status || 'draft',
    postType,
    featuredImage: post?.featuredImage || '',
    featuredImageAlt: post?.featuredImageAlt || '',
    seoTitle: post?.seoTitle || '',
    seoDescription: post?.seoDescription || '',
    seoKeywords: post?.seoKeywords || [],
    allowComments: post?.allowComments ?? true,
    isFeatured: post?.isFeatured || false,
    isSticky: post?.isSticky || false,
    scheduledAt: post?.scheduledAt,
  })

  const [isDirty, setIsDirty] = useState(false)
  const [activeTab, setActiveTab] = useState('content')

  // Auto-generate slug from title
  const generateSlug = useCallback((title: string) => {
    return title
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '')
  }, [])

  // Update form data
  const updateFormData = useCallback((updates: Partial<CreatePostInput>) => {
    setFormData(prev => ({ ...prev, ...updates }))
    setIsDirty(true)
  }, [])

  // Auto-generate slug when title changes
  useEffect(() => {
    if (formData.title && (!formData.slug || !post)) {
      const newSlug = generateSlug(formData.title)
      updateFormData({ slug: newSlug })
    }
  }, [formData.title, formData.slug, post, generateSlug, updateFormData])

  // Handle save
  const handleSave = useCallback(async (status?: PostStatus) => {
    const dataToSave = {
      ...formData,
      status: status || formData.status || 'draft',
      publishedAt: status === 'published' && !post?.publishedAt ? new Date() : undefined,
    }

    if (post) {
      await onSave({ id: post.id, ...dataToSave } as UpdatePostInput)
    } else {
      await onSave(dataToSave as CreatePostInput)
    }
    
    setIsDirty(false)
  }, [formData, post, onSave])

  // Handle keyword input
  const handleKeywordChange = useCallback((value: string) => {
    const keywords = value.split(',').map(k => k.trim()).filter(k => k.length > 0)
    updateFormData({ seoKeywords: keywords })
  }, [updateFormData])

  const statusOptions = [
    { value: 'draft', label: 'Draft', icon: FileText },
    { value: 'published', label: 'Published', icon: Globe },
    { value: 'private', label: 'Private', icon: Lock },
    { value: 'scheduled', label: 'Scheduled', icon: Calendar },
  ]

  return (
    <div className={cn('max-w-7xl mx-auto p-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">
            {post ? 'Edit' : 'Create'} {postType === 'post' ? 'Post' : postType}
          </h1>
          {isDirty && (
            <Badge variant="secondary" className="mt-1">
              Unsaved changes
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {onPreview && (
            <Button variant="outline" onClick={onPreview}>
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>
          )}
          
          <Button 
            variant="outline" 
            onClick={() => handleSave('draft')}
            disabled={isLoading}
          >
            <Save className="h-4 w-4 mr-2" />
            Save Draft
          </Button>
          
          <Button 
            onClick={() => handleSave('published')}
            disabled={isLoading}
          >
            Publish
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-3">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="seo">SEO</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="content" className="space-y-6">
              {/* Title */}
              <div>
                <Label htmlFor="title" className="text-base font-medium">Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => updateFormData({ title: e.target.value })}
                  placeholder="Enter post title..."
                  className="text-lg mt-2"
                />
              </div>

              {/* Slug */}
              <div>
                <Label htmlFor="slug" className="text-sm font-medium">Slug</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => updateFormData({ slug: e.target.value })}
                  placeholder="post-slug"
                  className="mt-1"
                />
              </div>

              {/* Content Editor */}
              <div>
                <Label className="text-base font-medium">Content</Label>
                <div className="mt-2">
                  <RichTextEditor
                    value={formData.content || ''}
                    onChange={(content) => updateFormData({ content })}
                    placeholder="Start writing your post..."
                    minHeight="400px"
                  />
                </div>
              </div>

              {/* Excerpt */}
              <div>
                <Label htmlFor="excerpt" className="text-sm font-medium">Excerpt</Label>
                <Textarea
                  id="excerpt"
                  value={formData.excerpt}
                  onChange={(e) => updateFormData({ excerpt: e.target.value })}
                  placeholder="Optional excerpt..."
                  rows={3}
                  className="mt-1"
                />
              </div>
            </TabsContent>

            <TabsContent value="seo" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5" />
                    SEO Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="seoTitle">SEO Title</Label>
                    <Input
                      id="seoTitle"
                      value={formData.seoTitle}
                      onChange={(e) => updateFormData({ seoTitle: e.target.value })}
                      placeholder="SEO optimized title..."
                    />
                  </div>

                  <div>
                    <Label htmlFor="seoDescription">SEO Description</Label>
                    <Textarea
                      id="seoDescription"
                      value={formData.seoDescription}
                      onChange={(e) => updateFormData({ seoDescription: e.target.value })}
                      placeholder="SEO meta description..."
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="seoKeywords">Keywords</Label>
                    <Input
                      id="seoKeywords"
                      value={formData.seoKeywords?.join(', ')}
                      onChange={(e) => handleKeywordChange(e.target.value)}
                      placeholder="keyword1, keyword2, keyword3"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Separate keywords with commas
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Post Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="allowComments">Allow Comments</Label>
                    <Switch
                      id="allowComments"
                      checked={formData.allowComments}
                      onCheckedChange={(checked) => updateFormData({ allowComments: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="isFeatured">Featured Post</Label>
                    <Switch
                      id="isFeatured"
                      checked={formData.isFeatured}
                      onCheckedChange={(checked) => updateFormData({ isFeatured: checked })}
                    />
                  </div>

                  {postType === 'post' && (
                    <div className="flex items-center justify-between">
                      <Label htmlFor="isSticky">Sticky Post</Label>
                      <Switch
                        id="isSticky"
                        checked={formData.isSticky}
                        onCheckedChange={(checked) => updateFormData({ isSticky: checked })}
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Publish Status */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Publish</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="status" className="text-xs">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: PostStatus) => updateFormData({ status: value })}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <option.icon className="h-4 w-4" />
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {formData.status === 'scheduled' && (
                <div>
                  <Label htmlFor="scheduledAt" className="text-xs">Scheduled Date</Label>
                  <Input
                    id="scheduledAt"
                    type="datetime-local"
                    value={formData.scheduledAt?.toISOString().slice(0, 16)}
                    onChange={(e) => updateFormData({ 
                      scheduledAt: e.target.value ? new Date(e.target.value) : undefined 
                    })}
                    className="mt-1"
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Featured Image */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Image className="h-4 w-4" />
                Featured Image
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="featuredImage" className="text-xs">Image URL</Label>
                <Input
                  id="featuredImage"
                  value={formData.featuredImage}
                  onChange={(e) => updateFormData({ featuredImage: e.target.value })}
                  placeholder="https://..."
                  className="mt-1"
                />
              </div>

              {formData.featuredImage && (
                <>
                  <div>
                    <Label htmlFor="featuredImageAlt" className="text-xs">Alt Text</Label>
                    <Input
                      id="featuredImageAlt"
                      value={formData.featuredImageAlt}
                      onChange={(e) => updateFormData({ featuredImageAlt: e.target.value })}
                      placeholder="Image description..."
                      className="mt-1"
                    />
                  </div>
                  
                  <div className="aspect-video rounded-lg overflow-hidden bg-muted">
                    <img
                      src={formData.featuredImage}
                      alt={formData.featuredImageAlt || 'Featured image'}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Actions */}
          {post && onDelete && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm text-destructive">Danger Zone</CardTitle>
              </CardHeader>
              <CardContent>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={onDelete}
                  className="w-full"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Post
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
