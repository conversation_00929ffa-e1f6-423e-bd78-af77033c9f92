'use client'

// Blog Post Display Component
// Frontend component for rendering individual blog posts

import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { 
  Calendar, 
  User, 
  Eye, 
  Heart, 
  Share2, 
  MessageCircle,
  Clock,
  Tag
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { Post } from '../types'
import { generateSeoMeta } from '../utils'
import { cn } from '@/lib/utils'

interface BlogPostProps {
  post: Post
  showFullContent?: boolean
  showMeta?: boolean
  showActions?: boolean
  showComments?: boolean
  className?: string
  onLike?: () => void
  onShare?: () => void
  onComment?: () => void
}

export function BlogPost({
  post,
  showFullContent = true,
  showMeta = true,
  showActions = true,
  showComments = false,
  className,
  onLike,
  onShare,
  onComment
}: BlogPostProps) {
  const seoMeta = generateSeoMeta(post)
  
  // Calculate reading time
  const readingTime = post.content 
    ? Math.ceil(post.content.replace(/<[^>]*>/g, '').split(' ').length / 200)
    : 0

  // Get categories and tags
  const categories = post.taxonomyTerms?.filter(
    term => term.term?.taxonomy?.name === 'category'
  ) || []
  
  const tags = post.taxonomyTerms?.filter(
    term => term.term?.taxonomy?.name === 'tag'
  ) || []

  return (
    <article className={cn('space-y-6', className)}>
      {/* Featured Image */}
      {post.featuredImage && (
        <div className="aspect-video rounded-lg overflow-hidden bg-muted">
          <img
            src={post.featuredImage}
            alt={post.featuredImageAlt || post.title}
            className="w-full h-full object-cover"
          />
        </div>
      )}

      {/* Header */}
      <header className="space-y-4">
        {/* Categories */}
        {categories.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Badge 
                key={category.id} 
                variant="secondary"
                className="text-xs"
              >
                {category.term?.name}
              </Badge>
            ))}
          </div>
        )}

        {/* Title */}
        <h1 className="text-3xl md:text-4xl font-bold leading-tight">
          {post.title}
        </h1>

        {/* Excerpt */}
        {post.excerpt && (
          <p className="text-lg text-muted-foreground leading-relaxed">
            {post.excerpt}
          </p>
        )}

        {/* Meta Information */}
        {showMeta && (
          <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
            {/* Author */}
            {post.authorName && (
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                <span>By {post.authorName}</span>
              </div>
            )}

            {/* Published Date */}
            {post.publishedAt && (
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>
                  {formatDistanceToNow(new Date(post.publishedAt), { addSuffix: true })}
                </span>
              </div>
            )}

            {/* Reading Time */}
            {readingTime > 0 && (
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{readingTime} min read</span>
              </div>
            )}

            {/* View Count */}
            <div className="flex items-center gap-1">
              <Eye className="h-4 w-4" />
              <span>{post.viewCount} views</span>
            </div>
          </div>
        )}
      </header>

      <Separator />

      {/* Content */}
      <div className="prose prose-gray max-w-none">
        {showFullContent ? (
          <div 
            dangerouslySetInnerHTML={{ 
              __html: post.contentHtml || post.content || '' 
            }} 
          />
        ) : (
          <div>
            {post.excerpt && (
              <p className="text-muted-foreground">{post.excerpt}</p>
            )}
            <Button variant="link" className="p-0 h-auto">
              Read more →
            </Button>
          </div>
        )}
      </div>

      {/* Tags */}
      {tags.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm font-medium">
            <Tag className="h-4 w-4" />
            <span>Tags</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <Badge 
                key={tag.id} 
                variant="outline"
                className="text-xs"
              >
                #{tag.term?.name}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Actions */}
      {showActions && (
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-4">
            {/* Like Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onLike}
              className="flex items-center gap-2"
            >
              <Heart className="h-4 w-4" />
              <span>{post.likeCount}</span>
            </Button>

            {/* Comment Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onComment}
              className="flex items-center gap-2"
            >
              <MessageCircle className="h-4 w-4" />
              <span>{post.commentCount}</span>
            </Button>
          </div>

          {/* Share Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onShare}
            className="flex items-center gap-2"
          >
            <Share2 className="h-4 w-4" />
            <span>Share</span>
          </Button>
        </div>
      )}

      {/* Comments Section */}
      {showComments && post.comments && post.comments.length > 0 && (
        <div className="space-y-4 pt-6 border-t">
          <h3 className="text-lg font-semibold">
            Comments ({post.commentCount})
          </h3>
          
          <div className="space-y-4">
            {post.comments.map((comment) => (
              <div key={comment.id} className="space-y-2 p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{comment.authorName}</span>
                    <span className="text-xs text-muted-foreground">
                      {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
                    </span>
                  </div>
                </div>
                <div 
                  className="text-sm"
                  dangerouslySetInnerHTML={{ __html: comment.content }}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* SEO Meta Tags (for head injection) */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BlogPosting",
            "headline": post.title,
            "description": post.excerpt || seoMeta.description,
            "image": post.featuredImage || seoMeta.openGraph.image,
            "author": {
              "@type": "Person",
              "name": post.authorName
            },
            "publisher": {
              "@type": "Organization",
              "name": "Your Site Name" // This should be configurable
            },
            "datePublished": post.publishedAt,
            "dateModified": post.updatedAt,
            "mainEntityOfPage": {
              "@type": "WebPage",
              "@id": seoMeta.canonical
            }
          })
        }}
      />
    </article>
  )
}
