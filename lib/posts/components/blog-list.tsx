'use client'

// Blog List Component
// Frontend component for displaying a list/grid of blog posts

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Search, 
  Calendar, 
  User, 
  Eye, 
  ArrowRight,
  Grid3X3,
  List,
  Filter
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { Post, PostQueryParams } from '../types'
import { cn } from '@/lib/utils'
import Link from 'next/link'

interface BlogListProps {
  initialPosts?: Post[]
  showSearch?: boolean
  showFilters?: boolean
  showPagination?: boolean
  layout?: 'grid' | 'list'
  postsPerPage?: number
  className?: string
  baseUrl?: string
}

export function BlogList({
  initialPosts = [],
  showSearch = true,
  showFilters = true,
  showPagination = true,
  layout: initialLayout = 'grid',
  postsPerPage = 9,
  className,
  baseUrl = '/blog'
}: BlogListProps) {
  const [posts, setPosts] = useState<Post[]>(initialPosts)
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [layout, setLayout] = useState<'grid' | 'list'>(initialLayout)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [categories, setCategories] = useState<any[]>([])

  // Fetch posts based on filters
  const fetchPosts = async (params: Partial<PostQueryParams> = {}) => {
    try {
      setLoading(true)
      
      const queryParams = new URLSearchParams({
        postType: 'post',
        status: 'published',
        limit: postsPerPage.toString(),
        offset: ((currentPage - 1) * postsPerPage).toString(),
        orderBy: 'publishedAt',
        order: 'desc',
        include: 'taxonomyTerms',
        ...params
      } as any)

      if (searchTerm) {
        queryParams.set('search', searchTerm)
      }

      if (selectedCategory !== 'all') {
        queryParams.set('taxonomy_category', selectedCategory)
      }

      const response = await fetch(`/api/posts?${queryParams}`)
      const data = await response.json()

      if (data.success) {
        setPosts(data.data.posts)
        setTotalPages(data.data.totalPages)
      }
    } catch (error) {
      console.error('Error fetching posts:', error)
    } finally {
      setLoading(false)
    }
  }

  // Fetch categories for filter
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/taxonomies/category/terms')
      const data = await response.json()
      
      if (data.success) {
        setCategories(data.data)
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }

  useEffect(() => {
    if (initialPosts.length === 0) {
      fetchPosts()
    }
    if (showFilters) {
      fetchCategories()
    }
  }, [])

  useEffect(() => {
    if (initialPosts.length === 0) {
      fetchPosts()
    }
  }, [searchTerm, selectedCategory, currentPage])

  const PostCard = ({ post }: { post: Post }) => {
    const categories = post.taxonomyTerms?.filter(
      term => term.term?.taxonomy?.name === 'category'
    ) || []

    const readingTime = post.content 
      ? Math.ceil(post.content.replace(/<[^>]*>/g, '').split(' ').length / 200)
      : 0

    if (layout === 'list') {
      return (
        <Card className="overflow-hidden hover:shadow-lg transition-shadow">
          <div className="md:flex">
            {post.featuredImage && (
              <div className="md:w-1/3">
                <div className="aspect-video md:aspect-square md:h-full">
                  <img
                    src={post.featuredImage}
                    alt={post.featuredImageAlt || post.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            )}
            <div className={cn("p-6", post.featuredImage ? "md:w-2/3" : "w-full")}>
              <div className="space-y-4">
                {/* Categories */}
                {categories.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {categories.slice(0, 2).map((category) => (
                      <Badge key={category.id} variant="secondary" className="text-xs">
                        {category.term?.name}
                      </Badge>
                    ))}
                  </div>
                )}

                {/* Title */}
                <h3 className="text-xl font-semibold line-clamp-2">
                  <Link 
                    href={`${baseUrl}/${post.slug}`}
                    className="hover:text-primary transition-colors"
                  >
                    {post.title}
                  </Link>
                </h3>

                {/* Excerpt */}
                {post.excerpt && (
                  <p className="text-muted-foreground line-clamp-3">
                    {post.excerpt}
                  </p>
                )}

                {/* Meta */}
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <div className="flex items-center gap-4">
                    {post.authorName && (
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span>{post.authorName}</span>
                      </div>
                    )}
                    {post.publishedAt && (
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>
                          {formatDistanceToNow(new Date(post.publishedAt), { addSuffix: true })}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  <Link 
                    href={`${baseUrl}/${post.slug}`}
                    className="flex items-center gap-1 text-primary hover:underline"
                  >
                    Read more <ArrowRight className="h-3 w-3" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </Card>
      )
    }

    // Grid layout
    return (
      <Card className="overflow-hidden hover:shadow-lg transition-shadow">
        {post.featuredImage && (
          <div className="aspect-video">
            <img
              src={post.featuredImage}
              alt={post.featuredImageAlt || post.title}
              className="w-full h-full object-cover"
            />
          </div>
        )}
        <CardContent className="p-6 space-y-4">
          {/* Categories */}
          {categories.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {categories.slice(0, 2).map((category) => (
                <Badge key={category.id} variant="secondary" className="text-xs">
                  {category.term?.name}
                </Badge>
              ))}
            </div>
          )}

          {/* Title */}
          <h3 className="text-lg font-semibold line-clamp-2">
            <Link 
              href={`${baseUrl}/${post.slug}`}
              className="hover:text-primary transition-colors"
            >
              {post.title}
            </Link>
          </h3>

          {/* Excerpt */}
          {post.excerpt && (
            <p className="text-sm text-muted-foreground line-clamp-3">
              {post.excerpt}
            </p>
          )}

          {/* Meta */}
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-2">
              {post.authorName && (
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  <span>{post.authorName}</span>
                </div>
              )}
              <div className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                <span>{post.viewCount}</span>
              </div>
            </div>
            
            {post.publishedAt && (
              <span>
                {formatDistanceToNow(new Date(post.publishedAt), { addSuffix: true })}
              </span>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  const LoadingSkeleton = () => (
    <div className={cn(
      "grid gap-6",
      layout === 'grid' 
        ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3" 
        : "grid-cols-1"
    )}>
      {Array.from({ length: postsPerPage }).map((_, i) => (
        <Card key={i} className="overflow-hidden">
          <Skeleton className="aspect-video" />
          <CardContent className="p-6 space-y-4">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <div className="flex justify-between">
              <Skeleton className="h-3 w-24" />
              <Skeleton className="h-3 w-16" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )

  return (
    <div className={cn('space-y-6', className)}>
      {/* Filters */}
      {(showSearch || showFilters) && (
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              {showSearch && (
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search posts..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              )}

              {/* Category Filter */}
              {showFilters && (
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.slug}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}

              {/* Layout Toggle */}
              <div className="flex gap-1">
                <Button
                  variant={layout === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setLayout('grid')}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={layout === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setLayout('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Posts Grid/List */}
      {loading ? (
        <LoadingSkeleton />
      ) : posts.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <h3 className="text-lg font-medium mb-2">No posts found</h3>
            <p className="text-muted-foreground">
              {searchTerm || selectedCategory !== 'all' 
                ? 'Try adjusting your search or filters' 
                : 'No blog posts have been published yet'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className={cn(
          "grid gap-6",
          layout === 'grid' 
            ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3" 
            : "grid-cols-1"
        )}>
          {posts.map((post) => (
            <PostCard key={post.id} post={post} />
          ))}
        </div>
      )}

      {/* Pagination */}
      {showPagination && totalPages > 1 && (
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1 || loading}
          >
            Previous
          </Button>
          
          <span className="text-sm text-muted-foreground">
            Page {currentPage} of {totalPages}
          </span>
          
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages || loading}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}
