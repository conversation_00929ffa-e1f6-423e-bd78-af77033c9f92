'use client'

// Rich Text Editor Component
// Advanced WYSIWYG editor for blog posts and content creation

import React, { useCallback, useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Toggle } from '@/components/ui/toggle'
import { 
  Bold, 
  Italic, 
  Underline, 
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  List,
  ListOrdered,
  Quote,
  Link,
  Image,
  Code,
  Heading1,
  Heading2,
  Heading3,
  Undo,
  Redo,
  Eye,
  EyeOff
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  minHeight?: string
  maxHeight?: string
  showPreview?: boolean
  readOnly?: boolean
}

export function RichTextEditor({
  value,
  onChange,
  placeholder = 'Start writing...',
  className,
  minHeight = '300px',
  maxHeight = '600px',
  showPreview = true,
  readOnly = false
}: RichTextEditorProps) {
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [editorRef, setEditorRef] = useState<HTMLDivElement | null>(null)
  const [selection, setSelection] = useState<Selection | null>(null)

  // Save selection when editor loses focus
  const saveSelection = useCallback(() => {
    const sel = window.getSelection()
    if (sel && sel.rangeCount > 0) {
      setSelection(sel)
    }
  }, [])

  // Restore selection when needed
  const restoreSelection = useCallback(() => {
    if (selection && editorRef?.contains(selection.anchorNode)) {
      const sel = window.getSelection()
      sel?.removeAllRanges()
      if (selection.rangeCount > 0) {
        sel?.addRange(selection.getRangeAt(0))
      }
    }
  }, [selection, editorRef])

  // Execute formatting command
  const execCommand = useCallback((command: string, value?: string) => {
    if (readOnly) return
    
    restoreSelection()
    document.execCommand(command, false, value)
    
    // Update content
    if (editorRef) {
      onChange(editorRef.innerHTML)
    }
    
    // Save new selection
    saveSelection()
  }, [readOnly, restoreSelection, onChange, editorRef, saveSelection])

  // Check if command is active
  const isCommandActive = useCallback((command: string) => {
    try {
      return document.queryCommandState(command)
    } catch {
      return false
    }
  }, [])

  // Handle content change
  const handleContentChange = useCallback(() => {
    if (editorRef && !readOnly) {
      onChange(editorRef.innerHTML)
    }
  }, [editorRef, onChange, readOnly])

  // Handle key shortcuts
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (readOnly) return

    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault()
          execCommand('bold')
          break
        case 'i':
          e.preventDefault()
          execCommand('italic')
          break
        case 'u':
          e.preventDefault()
          execCommand('underline')
          break
        case 'z':
          e.preventDefault()
          if (e.shiftKey) {
            execCommand('redo')
          } else {
            execCommand('undo')
          }
          break
      }
    }
  }, [readOnly, execCommand])

  // Insert link
  const insertLink = useCallback(() => {
    const url = prompt('Enter URL:')
    if (url) {
      execCommand('createLink', url)
    }
  }, [execCommand])

  // Insert image
  const insertImage = useCallback(() => {
    const url = prompt('Enter image URL:')
    if (url) {
      execCommand('insertImage', url)
    }
  }, [execCommand])

  // Format as heading
  const formatHeading = useCallback((level: number) => {
    execCommand('formatBlock', `h${level}`)
  }, [execCommand])

  // Initialize editor content
  useEffect(() => {
    if (editorRef && value !== editorRef.innerHTML) {
      editorRef.innerHTML = value
    }
  }, [value, editorRef])

  const toolbarButtons = [
    {
      group: 'format',
      buttons: [
        { icon: Bold, command: 'bold', tooltip: 'Bold (Ctrl+B)' },
        { icon: Italic, command: 'italic', tooltip: 'Italic (Ctrl+I)' },
        { icon: Underline, command: 'underline', tooltip: 'Underline (Ctrl+U)' },
        { icon: Strikethrough, command: 'strikeThrough', tooltip: 'Strikethrough' },
      ]
    },
    {
      group: 'headings',
      buttons: [
        { icon: Heading1, action: () => formatHeading(1), tooltip: 'Heading 1' },
        { icon: Heading2, action: () => formatHeading(2), tooltip: 'Heading 2' },
        { icon: Heading3, action: () => formatHeading(3), tooltip: 'Heading 3' },
      ]
    },
    {
      group: 'align',
      buttons: [
        { icon: AlignLeft, command: 'justifyLeft', tooltip: 'Align Left' },
        { icon: AlignCenter, command: 'justifyCenter', tooltip: 'Align Center' },
        { icon: AlignRight, command: 'justifyRight', tooltip: 'Align Right' },
        { icon: AlignJustify, command: 'justifyFull', tooltip: 'Justify' },
      ]
    },
    {
      group: 'lists',
      buttons: [
        { icon: List, command: 'insertUnorderedList', tooltip: 'Bullet List' },
        { icon: ListOrdered, command: 'insertOrderedList', tooltip: 'Numbered List' },
        { icon: Quote, command: 'formatBlock', value: 'blockquote', tooltip: 'Quote' },
      ]
    },
    {
      group: 'insert',
      buttons: [
        { icon: Link, action: insertLink, tooltip: 'Insert Link' },
        { icon: Image, action: insertImage, tooltip: 'Insert Image' },
        { icon: Code, command: 'formatBlock', value: 'pre', tooltip: 'Code Block' },
      ]
    },
    {
      group: 'history',
      buttons: [
        { icon: Undo, command: 'undo', tooltip: 'Undo (Ctrl+Z)' },
        { icon: Redo, command: 'redo', tooltip: 'Redo (Ctrl+Shift+Z)' },
      ]
    }
  ]

  return (
    <div className={cn('border rounded-lg overflow-hidden', className)}>
      {/* Toolbar */}
      {!readOnly && (
        <div className="border-b bg-muted/50 p-2">
          <div className="flex items-center gap-1 flex-wrap">
            {toolbarButtons.map((group, groupIndex) => (
              <React.Fragment key={group.group}>
                {groupIndex > 0 && <Separator orientation="vertical" className="h-6 mx-1" />}
                {group.buttons.map((button, buttonIndex) => (
                  <Toggle
                    key={buttonIndex}
                    size="sm"
                    pressed={button.command ? isCommandActive(button.command) : false}
                    onPressedChange={() => {
                      if (button.action) {
                        button.action()
                      } else if (button.command) {
                        execCommand(button.command, button.value)
                      }
                    }}
                    title={button.tooltip}
                  >
                    <button.icon className="h-4 w-4" />
                  </Toggle>
                ))}
              </React.Fragment>
            ))}
            
            {showPreview && (
              <>
                <Separator orientation="vertical" className="h-6 mx-1" />
                <Toggle
                  size="sm"
                  pressed={isPreviewMode}
                  onPressedChange={setIsPreviewMode}
                  title="Toggle Preview"
                >
                  {isPreviewMode ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Toggle>
              </>
            )}
          </div>
        </div>
      )}

      {/* Editor Content */}
      <div className="relative">
        {isPreviewMode ? (
          <div 
            className="p-4 prose prose-gray max-w-none"
            style={{ minHeight, maxHeight }}
            dangerouslySetInnerHTML={{ __html: value }}
          />
        ) : (
          <div
            ref={setEditorRef}
            contentEditable={!readOnly}
            className={cn(
              'p-4 outline-none prose prose-gray max-w-none',
              'focus:ring-2 focus:ring-ring focus:ring-offset-2',
              readOnly && 'cursor-default'
            )}
            style={{ minHeight, maxHeight, overflowY: 'auto' }}
            onInput={handleContentChange}
            onBlur={saveSelection}
            onKeyDown={handleKeyDown}
            data-placeholder={placeholder}
            suppressContentEditableWarning
          />
        )}
        
        {/* Placeholder */}
        {!value && !isPreviewMode && (
          <div 
            className="absolute top-4 left-4 text-muted-foreground pointer-events-none"
            style={{ fontSize: '16px' }}
          >
            {placeholder}
          </div>
        )}
      </div>
    </div>
  )
}
