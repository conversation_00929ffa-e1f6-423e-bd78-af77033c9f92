'use client'

// Post Type Creation Wizard
// Step-by-step guided interface for creating custom post types

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  ChevronLeft, 
  ChevronRight, 
  Check, 
  FileText, 
  Settings, 
  Tag, 
  Palette,
  Sparkles
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { CreatePostTypeInput } from '../types'

interface PostTypeWizardProps {
  onComplete: (data: CreatePostTypeInput) => Promise<void>
  onCancel: () => void
  className?: string
  isLoading?: boolean
}

interface WizardStep {
  id: string
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
}

const wizardSteps: WizardStep[] = [
  {
    id: 'basic',
    title: 'Basic Information',
    description: 'Set up the basic details for your post type',
    icon: FileText
  },
  {
    id: 'features',
    title: 'Features & Capabilities',
    description: 'Choose what features your post type will support',
    icon: Settings
  },
  {
    id: 'taxonomies',
    title: 'Organization',
    description: 'Set up categories and tags for organization',
    icon: Tag
  },
  {
    id: 'customization',
    title: 'Customization',
    description: 'Add custom fields and templates',
    icon: Palette
  },
  {
    id: 'review',
    title: 'Review & Create',
    description: 'Review your settings and create the post type',
    icon: Sparkles
  }
]

export function PostTypeWizard({
  onComplete,
  onCancel,
  className,
  isLoading = false
}: PostTypeWizardProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState<Partial<CreatePostTypeInput>>({
    name: '',
    label: '',
    labelPlural: '',
    description: '',
    icon: '📄',
    isPublic: true,
    isHierarchical: false,
    hasArchive: true,
    supportsTitle: true,
    supportsContent: true,
    supportsExcerpt: false,
    supportsThumbnail: false,
    supportsComments: false,
    supportsRevisions: true,
    supportsPageBuilder: false,
    menuPosition: 25,
    taxonomies: [],
    templates: [],
    customFields: {},
  })

  const updateFormData = (updates: Partial<CreatePostTypeInput>) => {
    setFormData(prev => ({ ...prev, ...updates }))
  }

  const nextStep = () => {
    if (currentStep < wizardSteps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const canProceed = () => {
    switch (currentStep) {
      case 0: // Basic
        return formData.name && formData.label && formData.labelPlural
      case 1: // Features
      case 2: // Taxonomies
      case 3: // Customization
        return true
      case 4: // Review
        return true
      default:
        return false
    }
  }

  const handleComplete = async () => {
    await onComplete(formData as CreatePostTypeInput)
  }

  // Auto-generate name from label
  const handleLabelChange = (label: string) => {
    updateFormData({ label })
    if (!formData.name) {
      const generatedName = label
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '_')
      updateFormData({ name: generatedName })
    }
    if (!formData.labelPlural) {
      const plural = label.endsWith('s') ? label : label + 's'
      updateFormData({ labelPlural: plural })
    }
  }

  const progress = ((currentStep + 1) / wizardSteps.length) * 100

  const iconOptions = [
    '📄', '📝', '📰', '📚', '📖', '🗞️', '📋', '📌',
    '🛍️', '🛒', '💼', '📦', '🎁', '🏷️', '💳', '💰',
    '🎨', '🖼️', '📷', '🎭', '🎪', '🎬', '🎵', '🎤',
    '👤', '👥', '🏢', '🏠', '🌟', '⭐', '🔥', '💡'
  ]

  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Basic Information
        return (
          <div className="space-y-6">
            <div>
              <Label htmlFor="label">What would you like to call this content type? *</Label>
              <Input
                id="label"
                value={formData.label}
                onChange={(e) => handleLabelChange(e.target.value)}
                placeholder="e.g., Product, Portfolio Item, Testimonial"
                className="mt-2"
              />
              <p className="text-xs text-muted-foreground mt-1">
                This is the display name that will appear in the admin interface
              </p>
            </div>

            <div>
              <Label htmlFor="labelPlural">Plural form *</Label>
              <Input
                id="labelPlural"
                value={formData.labelPlural}
                onChange={(e) => updateFormData({ labelPlural: e.target.value })}
                placeholder="e.g., Products, Portfolio Items, Testimonials"
                className="mt-2"
              />
            </div>

            <div>
              <Label htmlFor="name">Technical name (slug) *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => updateFormData({ name: e.target.value })}
                placeholder="e.g., product, portfolio_item, testimonial"
                className="mt-2"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Lowercase letters, numbers, and underscores only. Cannot be changed later.
              </p>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => updateFormData({ description: e.target.value })}
                placeholder="Brief description of what this content type is for..."
                rows={3}
                className="mt-2"
              />
            </div>

            <div>
              <Label>Choose an icon</Label>
              <div className="grid grid-cols-8 gap-2 mt-2">
                {iconOptions.map((icon) => (
                  <button
                    key={icon}
                    type="button"
                    onClick={() => updateFormData({ icon })}
                    className={cn(
                      "p-2 text-lg border rounded hover:bg-muted transition-colors",
                      formData.icon === icon && "bg-primary text-primary-foreground"
                    )}
                  >
                    {icon}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )

      case 1: // Features & Capabilities
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">What features should this content type support?</h3>
              
              <div className="space-y-4">
                {[
                  { key: 'supportsTitle', label: 'Title', description: 'Every post needs a title' },
                  { key: 'supportsContent', label: 'Content Editor', description: 'Rich text content editor' },
                  { key: 'supportsExcerpt', label: 'Excerpt', description: 'Short summary or description' },
                  { key: 'supportsThumbnail', label: 'Featured Image', description: 'Main image for the content' },
                  { key: 'supportsComments', label: 'Comments', description: 'Allow users to comment' },
                  { key: 'supportsRevisions', label: 'Revisions', description: 'Track changes and versions' },
                  { key: 'supportsPageBuilder', label: 'Page Builder', description: 'Visual drag-and-drop editor' },
                ].map((feature) => (
                  <div key={feature.key} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <div className="font-medium">{feature.label}</div>
                      <div className="text-sm text-muted-foreground">{feature.description}</div>
                    </div>
                    <Switch
                      checked={formData[feature.key as keyof CreatePostTypeInput] as boolean}
                      onCheckedChange={(checked) => updateFormData({ [feature.key]: checked })}
                    />
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Behavior Settings</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <div className="font-medium">Hierarchical</div>
                    <div className="text-sm text-muted-foreground">Can have parent/child relationships (like pages)</div>
                  </div>
                  <Switch
                    checked={formData.isHierarchical}
                    onCheckedChange={(checked) => updateFormData({ isHierarchical: checked })}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <div className="font-medium">Has Archive</div>
                    <div className="text-sm text-muted-foreground">Create listing pages for this content type</div>
                  </div>
                  <Switch
                    checked={formData.hasArchive}
                    onCheckedChange={(checked) => updateFormData({ hasArchive: checked })}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <div className="font-medium">Public</div>
                    <div className="text-sm text-muted-foreground">Show in frontend and make available via API</div>
                  </div>
                  <Switch
                    checked={formData.isPublic}
                    onCheckedChange={(checked) => updateFormData({ isPublic: checked })}
                  />
                </div>
              </div>
            </div>
          </div>
        )

      case 2: // Taxonomies
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Organization & Categorization</h3>
              <p className="text-muted-foreground mb-4">
                How would you like to organize and categorize this content?
              </p>
              
              <div className="space-y-4">
                <div className="p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">Suggested Taxonomies</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    We'll create these taxonomies for you based on your content type
                  </p>
                  
                  <div className="space-y-2">
                    <Badge variant="outline">{formData.name}_category</Badge>
                    <Badge variant="outline">{formData.name}_tag</Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )

      case 3: // Customization
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Customization Options</h3>
              <p className="text-muted-foreground mb-4">
                Additional customization options for your content type
              </p>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="menuPosition">Menu Position</Label>
                  <Input
                    id="menuPosition"
                    type="number"
                    value={formData.menuPosition}
                    onChange={(e) => updateFormData({ menuPosition: parseInt(e.target.value) })}
                    min="1"
                    max="100"
                    className="mt-2"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Position in admin menu (lower numbers appear first)
                  </p>
                </div>

                <div className="p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">Custom Fields</h4>
                  <p className="text-sm text-muted-foreground">
                    Custom fields can be added after creating the post type
                  </p>
                </div>

                <div className="p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">Templates</h4>
                  <p className="text-sm text-muted-foreground">
                    Custom templates can be configured after creation
                  </p>
                </div>
              </div>
            </div>
          </div>
        )

      case 4: // Review
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Review Your Post Type</h3>
              
              <div className="space-y-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <span className="text-2xl">{formData.icon}</span>
                      <div>
                        <div className="font-medium">{formData.label}</div>
                        <div className="text-sm text-muted-foreground">{formData.name}</div>
                      </div>
                    </div>
                    {formData.description && (
                      <p className="text-sm text-muted-foreground">{formData.description}</p>
                    )}
                  </CardContent>
                </Card>

                <div className="grid grid-cols-2 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <h4 className="font-medium mb-2">Features</h4>
                      <div className="space-y-1 text-sm">
                        {formData.supportsTitle && <div>✓ Title</div>}
                        {formData.supportsContent && <div>✓ Content Editor</div>}
                        {formData.supportsExcerpt && <div>✓ Excerpt</div>}
                        {formData.supportsThumbnail && <div>✓ Featured Image</div>}
                        {formData.supportsComments && <div>✓ Comments</div>}
                        {formData.supportsRevisions && <div>✓ Revisions</div>}
                        {formData.supportsPageBuilder && <div>✓ Page Builder</div>}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <h4 className="font-medium mb-2">Settings</h4>
                      <div className="space-y-1 text-sm">
                        <div>{formData.isPublic ? '✓' : '✗'} Public</div>
                        <div>{formData.isHierarchical ? '✓' : '✗'} Hierarchical</div>
                        <div>{formData.hasArchive ? '✓' : '✗'} Has Archive</div>
                        <div>Menu Position: {formData.menuPosition}</div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className={cn('max-w-4xl mx-auto p-6', className)}>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-2">Create Custom Post Type</h1>
        <p className="text-muted-foreground">
          Follow the steps below to create a new content type for your application
        </p>
      </div>

      {/* Progress */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium">
            Step {currentStep + 1} of {wizardSteps.length}
          </span>
          <span className="text-sm text-muted-foreground">
            {Math.round(progress)}% complete
          </span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {/* Steps */}
      <div className="flex items-center justify-between mb-8">
        {wizardSteps.map((step, index) => (
          <div
            key={step.id}
            className={cn(
              "flex flex-col items-center text-center",
              index <= currentStep ? "text-primary" : "text-muted-foreground"
            )}
          >
            <div
              className={cn(
                "w-10 h-10 rounded-full flex items-center justify-center mb-2",
                index < currentStep && "bg-primary text-primary-foreground",
                index === currentStep && "bg-primary/10 border-2 border-primary",
                index > currentStep && "bg-muted"
              )}
            >
              {index < currentStep ? (
                <Check className="h-5 w-5" />
              ) : (
                <step.icon className="h-5 w-5" />
              )}
            </div>
            <div className="text-xs font-medium hidden md:block">{step.title}</div>
          </div>
        ))}
      </div>

      {/* Content */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {React.createElement(wizardSteps[currentStep].icon, { className: "h-5 w-5" })}
            {wizardSteps[currentStep].title}
          </CardTitle>
          <p className="text-muted-foreground">{wizardSteps[currentStep].description}</p>
        </CardHeader>
        <CardContent>
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={currentStep === 0 ? onCancel : prevStep}
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          {currentStep === 0 ? 'Cancel' : 'Previous'}
        </Button>

        <Button
          onClick={currentStep === wizardSteps.length - 1 ? handleComplete : nextStep}
          disabled={!canProceed() || isLoading}
        >
          {currentStep === wizardSteps.length - 1 ? (
            <>
              <Sparkles className="h-4 w-4 mr-2" />
              Create Post Type
            </>
          ) : (
            <>
              Next
              <ChevronRight className="h-4 w-4 ml-2" />
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
