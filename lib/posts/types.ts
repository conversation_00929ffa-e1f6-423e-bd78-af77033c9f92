// WordPress-Style Posts System Types
// Comprehensive type definitions for the unified content management system

export interface PostType {
  id: string
  name: string
  label: string
  labelPlural: string
  description?: string
  icon?: string
  isPublic: boolean
  isHierarchical: boolean
  hasArchive: boolean
  supportsTitle: boolean
  supportsContent: boolean
  supportsExcerpt: boolean
  supportsThumbnail: boolean
  supportsComments: boolean
  supportsRevisions: boolean
  supportsPageBuilder: boolean
  menuPosition?: number
  capabilities?: Record<string, any>
  taxonomies: string[]
  customFields: CustomField[]
  templates: string[]
  isSystem: boolean
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Visual Content Type Builder Types
export interface CustomField {
  id: string
  name: string
  label: string
  type: FieldType
  description?: string
  required: boolean
  defaultValue?: any
  validation: ValidationRule[]
  conditionalLogic?: ConditionalRule[]
  settings: FieldSettings
  position: number
  group?: string
  isSystem?: boolean
  createdAt?: Date
  updatedAt?: Date
}

export type FieldType =
  | 'text'
  | 'textarea'
  | 'richtext'
  | 'number'
  | 'email'
  | 'url'
  | 'password'
  | 'date'
  | 'datetime'
  | 'time'
  | 'boolean'
  | 'select'
  | 'multiselect'
  | 'radio'
  | 'checkbox'
  | 'image'
  | 'gallery'
  | 'file'
  | 'color'
  | 'range'
  | 'relationship'
  | 'json'
  | 'code'
  | 'repeater'
  | 'group'

export interface FieldSettings {
  placeholder?: string
  helpText?: string
  maxLength?: number
  minLength?: number
  min?: number
  max?: number
  step?: number
  options?: FieldOption[]
  multiple?: boolean
  allowedFileTypes?: string[]
  maxFileSize?: number
  relationshipType?: 'one-to-one' | 'one-to-many' | 'many-to-many'
  relatedPostType?: string
  codeLanguage?: string
  rows?: number
  cols?: number
  subFields?: CustomField[]
  width?: 'full' | 'half' | 'third' | 'quarter'
  className?: string
}

export interface FieldOption {
  label: string
  value: string | number
  disabled?: boolean
  color?: string
  icon?: string
}

export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom' | 'email' | 'url' | 'unique'
  value?: any
  message: string
  customValidator?: string
}

export interface ConditionalRule {
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'is_empty' | 'is_not_empty'
  value: any
  action: 'show' | 'hide' | 'require' | 'disable' | 'enable'
}

// Field Group for organizing fields
export interface FieldGroup {
  id: string
  name: string
  label: string
  description?: string
  isCollapsible: boolean
  isCollapsed: boolean
  position: number
  fields: string[] // Field IDs
  styling?: {
    backgroundColor?: string
    borderColor?: string
    textColor?: string
  }
}

// Content Type Template for quick setup
export interface ContentTypeTemplate {
  id: string
  name: string
  label: string
  description: string
  category: 'blog' | 'ecommerce' | 'portfolio' | 'business' | 'custom'
  icon: string
  preview?: string
  postTypeConfig: Partial<CreatePostTypeInput>
  customFields: CustomField[]
  taxonomies?: CreateTaxonomyInput[]
  isBuiltIn: boolean
  tags: string[]
}

export interface Post {
  id: string
  title: string
  slug: string
  content?: string
  contentHtml?: string
  excerpt?: string
  status: PostStatus
  postType: string
  parentId?: string
  menuOrder: number
  featuredImage?: string
  featuredImageAlt?: string
  template?: string
  password?: string
  publishedAt?: Date
  scheduledAt?: Date
  authorId?: string
  authorName?: string
  authorEmail?: string
  
  // SEO Fields
  seoTitle?: string
  seoDescription?: string
  seoKeywords: string[]
  ogImage?: string
  ogTitle?: string
  ogDescription?: string
  twitterCard?: string
  canonicalUrl?: string
  metaRobots?: string
  
  // Analytics & Engagement
  viewCount: number
  shareCount: number
  likeCount: number
  commentCount: number
  
  // Page Builder Integration
  usePageBuilder: boolean
  pageBuilderData?: Record<string, any>
  
  // Content Settings
  allowComments: boolean
  allowPingbacks: boolean
  isSticky: boolean
  isFeatured: boolean
  
  // Custom Fields & Metadata
  customFields?: Record<string, any>
  metadata?: Record<string, any>
  
  createdAt: Date
  updatedAt: Date
  
  // Relations
  postTypeRef?: PostType
  parent?: Post
  children?: Post[]
  taxonomyTerms?: PostTaxonomyTerm[]
  meta?: PostMeta[]
  comments?: PostComment[]
  revisions?: PostRevision[]
  blocks?: PostBlock[]
}

export interface PostMeta {
  id: string
  postId: string
  metaKey: string
  metaValue?: string
  metaType: MetaType
  isPrivate: boolean
  autoload: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Taxonomy {
  id: string
  name: string
  label: string
  labelPlural: string
  description?: string
  isHierarchical: boolean
  isPublic: boolean
  showInMenu: boolean
  showInRest: boolean
  postTypes: string[]
  capabilities?: Record<string, any>
  metaBoxCallback?: string
  isSystem: boolean
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  terms?: TaxonomyTerm[]
}

export interface TaxonomyTerm {
  id: string
  name: string
  slug: string
  description?: string
  taxonomyId: string
  parentId?: string
  count: number
  color?: string
  image?: string
  metadata?: Record<string, any>
  createdAt: Date
  updatedAt: Date
  taxonomy?: Taxonomy
  parent?: TaxonomyTerm
  children?: TaxonomyTerm[]
  posts?: PostTaxonomyTerm[]
}

export interface PostTaxonomyTerm {
  id: string
  postId: string
  termId: string
  createdAt: Date
  post?: Post
  term?: TaxonomyTerm
}

export interface PostComment {
  id: string
  postId: string
  parentId?: string
  authorName: string
  authorEmail: string
  authorUrl?: string
  authorIp?: string
  content: string
  status: CommentStatus
  type: CommentType
  userAgent?: string
  metadata?: Record<string, any>
  createdAt: Date
  updatedAt: Date
  post?: Post
  parent?: PostComment
  replies?: PostComment[]
}

export interface PostRevision {
  id: string
  postId: string
  title: string
  content?: string
  contentHtml?: string
  excerpt?: string
  revisionNumber: number
  changeType: RevisionType
  changeSummary?: string
  authorId?: string
  authorName?: string
  metadata?: Record<string, any>
  createdAt: Date
  post?: Post
}

export interface PostBlock {
  id: string
  postId: string
  blockType: string
  position: number
  isVisible: boolean
  configuration: Record<string, any>
  content?: Record<string, any>
  styling?: Record<string, any>
  responsive?: Record<string, any>
  animation?: Record<string, any>
  conditions?: Record<string, any>
  createdAt: Date
  updatedAt: Date
  post?: Post
}

// Enums and Union Types
export type PostStatus = 'draft' | 'published' | 'private' | 'trash' | 'scheduled'
export type MetaType = 'string' | 'number' | 'boolean' | 'json' | 'array'
export type CommentStatus = 'approved' | 'pending' | 'spam' | 'trash'
export type CommentType = 'comment' | 'review' | 'pingback'
export type RevisionType = 'revision' | 'autosave' | 'backup'

// Input Types for Creating/Updating
export interface CreatePostTypeInput {
  name: string
  label: string
  labelPlural: string
  description?: string
  icon?: string
  isPublic?: boolean
  isHierarchical?: boolean
  hasArchive?: boolean
  supportsTitle?: boolean
  supportsContent?: boolean
  supportsExcerpt?: boolean
  supportsThumbnail?: boolean
  supportsComments?: boolean
  supportsRevisions?: boolean
  supportsPageBuilder?: boolean
  menuPosition?: number
  capabilities?: Record<string, any>
  taxonomies?: string[]
  customFields?: CustomField[]
  templates?: string[]
}

export interface CreatePostInput {
  title: string
  slug?: string
  content?: string
  excerpt?: string
  status?: PostStatus
  postType: string
  parentId?: string
  menuOrder?: number
  featuredImage?: string
  featuredImageAlt?: string
  template?: string
  password?: string
  publishedAt?: Date
  scheduledAt?: Date
  authorId?: string
  authorName?: string
  authorEmail?: string
  seoTitle?: string
  seoDescription?: string
  seoKeywords?: string[]
  ogImage?: string
  ogTitle?: string
  ogDescription?: string
  twitterCard?: string
  canonicalUrl?: string
  metaRobots?: string
  allowComments?: boolean
  allowPingbacks?: boolean
  isSticky?: boolean
  isFeatured?: boolean
  usePageBuilder?: boolean
  pageBuilderData?: Record<string, any>
  customFields?: Record<string, any>
  metadata?: Record<string, any>
  taxonomyTerms?: string[]
  blocks?: CreatePostBlockInput[]
}

export interface UpdatePostInput extends Partial<CreatePostInput> {
  id: string
}

export interface CreatePostBlockInput {
  blockType: string
  position?: number
  isVisible?: boolean
  configuration: Record<string, any>
  content?: Record<string, any>
  styling?: Record<string, any>
  responsive?: Record<string, any>
  animation?: Record<string, any>
  conditions?: Record<string, any>
}

export interface CreateTaxonomyInput {
  name: string
  label: string
  labelPlural: string
  description?: string
  isHierarchical?: boolean
  isPublic?: boolean
  showInMenu?: boolean
  showInRest?: boolean
  postTypes?: string[]
  capabilities?: Record<string, any>
  metaBoxCallback?: string
}

export interface CreateTaxonomyTermInput {
  name: string
  slug?: string
  description?: string
  taxonomyId: string
  parentId?: string
  color?: string
  image?: string
  metadata?: Record<string, any>
}

// Query and Filter Types
export interface PostQueryParams {
  postType?: string | string[]
  status?: PostStatus | PostStatus[]
  authorId?: string
  parentId?: string
  search?: string
  taxonomies?: Record<string, string[]>
  meta?: Record<string, any>
  orderBy?: PostOrderBy
  order?: 'asc' | 'desc'
  limit?: number
  offset?: number
  include?: PostInclude[]
}

export type PostOrderBy = 'createdAt' | 'updatedAt' | 'publishedAt' | 'title' | 'menuOrder' | 'viewCount'
export type PostInclude = 'meta' | 'taxonomyTerms' | 'comments' | 'revisions' | 'blocks' | 'parent' | 'children'

export interface PaginatedPostsResponse {
  posts: Post[]
  total: number
  page: number
  limit: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

// Enhanced User Role & Permission System Types
export interface Permission {
  id: string
  name: string
  resource: string
  action: PermissionAction
  description?: string
  isSystem?: boolean
  createdAt?: Date
  updatedAt?: Date
}

export type PermissionAction =
  | 'create'
  | 'read'
  | 'update'
  | 'delete'
  | 'publish'
  | 'unpublish'
  | 'manage'
  | 'view'
  | 'edit'
  | 'moderate'
  | 'export'
  | 'import'

export interface Role {
  id: string
  name: string
  slug: string
  description?: string
  color?: string
  isSystem: boolean
  isActive: boolean
  level: number // Hierarchy level (1 = highest)
  permissions: Permission[]
  capabilities: RoleCapabilities
  contentTypePermissions: ContentTypePermissions
  restrictions: RoleRestrictions
  createdAt: Date
  updatedAt: Date
}

export interface RoleCapabilities {
  // System capabilities
  accessAdmin: boolean
  manageUsers: boolean
  manageRoles: boolean
  manageSettings: boolean
  viewAnalytics: boolean
  managePlugins: boolean
  manageThemes: boolean
  manageBackups: boolean

  // Content capabilities
  createContent: boolean
  editOwnContent: boolean
  editOthersContent: boolean
  deleteOwnContent: boolean
  deleteOthersContent: boolean
  publishContent: boolean
  moderateComments: boolean

  // Media capabilities
  uploadMedia: boolean
  editMedia: boolean
  deleteMedia: boolean

  // Advanced capabilities
  editCode: boolean
  manageDatabase: boolean
  viewLogs: boolean
  exportData: boolean
  importData: boolean
}

export interface ContentTypePermissions {
  [contentType: string]: {
    create: boolean
    read: boolean
    update: boolean
    delete: boolean
    publish: boolean
    moderate: boolean
    viewOwn: boolean
    viewOthers: boolean
    editOwn: boolean
    editOthers: boolean
    deleteOwn: boolean
    deleteOthers: boolean
  }
}

export interface RoleRestrictions {
  // Time-based restrictions
  accessHours?: {
    start: string // HH:mm format
    end: string
    timezone?: string
  }
  accessDays?: number[] // 0-6 (Sunday-Saturday)

  // IP-based restrictions
  allowedIPs?: string[]
  blockedIPs?: string[]

  // Content restrictions
  maxPosts?: number
  maxPages?: number
  maxUploads?: number
  maxStorageSize?: number // in MB

  // Feature restrictions
  disabledFeatures?: string[]
  allowedPostTypes?: string[]
  allowedTaxonomies?: string[]
}

export interface UserRole {
  id: string
  userId: string
  roleId: string
  assignedBy: string
  assignedAt: Date
  expiresAt?: Date
  isActive: boolean
  metadata?: Record<string, any>
}

export interface PermissionCheck {
  resource: string
  action: PermissionAction
  resourceId?: string
  userId?: string
  context?: Record<string, any>
}

export interface PermissionResult {
  allowed: boolean
  reason?: string
  restrictions?: string[]
  context?: Record<string, any>
}

// Role Templates for quick setup
export interface RoleTemplate {
  id: string
  name: string
  description: string
  category: 'content' | 'admin' | 'custom' | 'ecommerce' | 'developer'
  icon: string
  roleConfig: Partial<Role>
  isBuiltIn: boolean
  tags: string[]
}

// Database Schema Generator Types
export interface DatabaseTable {
  id: string
  name: string
  displayName: string
  description?: string
  schema: string
  isSystem: boolean
  isActive: boolean
  position: { x: number; y: number }
  fields: DatabaseField[]
  relationships: DatabaseRelationship[]
  indexes: DatabaseIndex[]
  constraints: DatabaseConstraint[]
  metadata: TableMetadata
  createdAt: Date
  updatedAt: Date
}

export interface DatabaseField {
  id: string
  name: string
  displayName: string
  type: DatabaseFieldType
  isRequired: boolean
  isPrimaryKey: boolean
  isUnique: boolean
  isIndexed: boolean
  defaultValue?: any
  length?: number
  precision?: number
  scale?: number
  enumValues?: string[]
  description?: string
  validation: FieldValidation[]
  position: number
  metadata: FieldMetadata
}

export type DatabaseFieldType =
  | 'string'
  | 'text'
  | 'integer'
  | 'bigint'
  | 'decimal'
  | 'float'
  | 'boolean'
  | 'date'
  | 'datetime'
  | 'timestamp'
  | 'time'
  | 'json'
  | 'uuid'
  | 'enum'
  | 'binary'
  | 'array'

export interface DatabaseRelationship {
  id: string
  name: string
  type: RelationshipType
  fromTable: string
  fromField: string
  toTable: string
  toField: string
  onDelete: CascadeAction
  onUpdate: CascadeAction
  isRequired: boolean
  description?: string
  metadata: RelationshipMetadata
}

export type RelationshipType =
  | 'one-to-one'
  | 'one-to-many'
  | 'many-to-one'
  | 'many-to-many'

export type CascadeAction =
  | 'cascade'
  | 'restrict'
  | 'set-null'
  | 'set-default'
  | 'no-action'

export interface DatabaseIndex {
  id: string
  name: string
  type: IndexType
  fields: string[]
  isUnique: boolean
  isPartial: boolean
  condition?: string
  description?: string
}

export type IndexType =
  | 'btree'
  | 'hash'
  | 'gin'
  | 'gist'
  | 'spgist'
  | 'brin'

export interface DatabaseConstraint {
  id: string
  name: string
  type: ConstraintType
  fields: string[]
  condition?: string
  referencedTable?: string
  referencedFields?: string[]
  description?: string
}

export type ConstraintType =
  | 'primary-key'
  | 'foreign-key'
  | 'unique'
  | 'check'
  | 'not-null'
  | 'default'

export interface TableMetadata {
  category: string
  tags: string[]
  documentation?: string
  apiEndpoints?: string[]
  permissions?: string[]
  auditEnabled?: boolean
  softDelete?: boolean
  timestamps?: boolean
  versioning?: boolean
}

export interface FieldMetadata {
  category: string
  tags: string[]
  documentation?: string
  displayOptions?: {
    widget: string
    options: Record<string, any>
  }
  validationRules?: string[]
  searchable?: boolean
  sortable?: boolean
  filterable?: boolean
}

export interface RelationshipMetadata {
  displayName?: string
  inverseDisplayName?: string
  cascadeDescription?: string
  businessRules?: string[]
}

export interface FieldValidation {
  type: 'min' | 'max' | 'pattern' | 'custom' | 'range' | 'length'
  value?: any
  message: string
  condition?: string
}

export interface DatabaseSchema {
  id: string
  name: string
  version: string
  description?: string
  tables: DatabaseTable[]
  views: DatabaseView[]
  functions: DatabaseFunction[]
  triggers: DatabaseTrigger[]
  migrations: SchemaMigration[]
  metadata: SchemaMetadata
  createdAt: Date
  updatedAt: Date
}

export interface DatabaseView {
  id: string
  name: string
  query: string
  description?: string
  fields: ViewField[]
  dependencies: string[]
  isActive: boolean
}

export interface ViewField {
  name: string
  type: string
  source: string
  description?: string
}

export interface DatabaseFunction {
  id: string
  name: string
  returnType: string
  parameters: FunctionParameter[]
  body: string
  language: string
  description?: string
  isActive: boolean
}

export interface FunctionParameter {
  name: string
  type: string
  defaultValue?: any
  isRequired: boolean
}

export interface DatabaseTrigger {
  id: string
  name: string
  table: string
  event: TriggerEvent
  timing: TriggerTiming
  function: string
  condition?: string
  description?: string
  isActive: boolean
}

export type TriggerEvent = 'insert' | 'update' | 'delete'
export type TriggerTiming = 'before' | 'after' | 'instead-of'

export interface SchemaMigration {
  id: string
  version: string
  name: string
  description?: string
  upScript: string
  downScript: string
  appliedAt?: Date
  rolledBackAt?: Date
  status: MigrationStatus
  checksum: string
}

export type MigrationStatus = 'pending' | 'applied' | 'failed' | 'rolled-back'

export interface SchemaMetadata {
  version: string
  compatibility: string[]
  features: string[]
  documentation?: string
  changelog?: ChangelogEntry[]
}

export interface ChangelogEntry {
  version: string
  date: Date
  changes: string[]
  breaking: boolean
}

// Schema Generation Options
export interface SchemaGenerationOptions {
  target: 'prisma' | 'sql' | 'typescript' | 'graphql'
  includeComments: boolean
  includeIndexes: boolean
  includeConstraints: boolean
  includeSeeds: boolean
  formatOutput: boolean
  customTemplates?: Record<string, string>
}

// Schema Validation
export interface SchemaValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
  suggestions: ValidationSuggestion[]
}

export interface ValidationError {
  type: string
  message: string
  table?: string
  field?: string
  relationship?: string
  severity: 'error' | 'warning' | 'info'
}

export interface ValidationWarning {
  type: string
  message: string
  table?: string
  field?: string
  suggestion?: string
}

export interface ValidationSuggestion {
  type: string
  message: string
  action: string
  impact: 'low' | 'medium' | 'high'
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PostsApiResponse extends ApiResponse<PaginatedPostsResponse> {}
export interface PostApiResponse extends ApiResponse<Post> {}
export interface PostTypeApiResponse extends ApiResponse<PostType> {}
export interface TaxonomyApiResponse extends ApiResponse<Taxonomy> {}
