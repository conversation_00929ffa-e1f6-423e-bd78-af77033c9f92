# WordPress-Style Posts System

A comprehensive, WordPress-inspired content management system built with TypeScript, Next.js, and Prisma. This system provides a unified approach to managing all types of content including blog posts, pages, products, and custom post types.

## 🚀 Features

### Core WordPress-Like Functionality
- **Post Types**: Extensible content type system (posts, pages, products, custom types)
- **Taxonomies**: Categories, tags, and custom taxonomies with hierarchical support
- **Custom Fields**: Flexible metadata storage system
- **Revisions**: Complete version control and change tracking
- **Comments**: Nested comment system with moderation
- **SEO**: Built-in SEO optimization fields
- **Scheduling**: Post scheduling functionality
- **Hierarchical Content**: Parent/child relationships for pages

### Modern Enhancements
- **Rich Text Editor**: Advanced WYSIWYG editing with live preview
- **Page Builder Integration**: Visual content creation with blocks
- **API-First**: RESTful API for all operations
- **TypeScript**: Full type safety throughout
- **Responsive Design**: Mobile-optimized admin interface
- **Real-time Updates**: Live editing experience

## 📁 File Structure

```
lib/posts/
├── README.md                   # This file
├── types.ts                    # TypeScript definitions
├── utils.ts                    # Utility functions
├── init.ts                     # System initialization
├── services/
│   ├── post-service.ts         # Post CRUD operations
│   ├── post-type-service.ts    # Post type management
│   └── taxonomy-service.ts     # Taxonomy management
└── components/
    ├── rich-text-editor.tsx    # WYSIWYG editor
    ├── post-editor.tsx         # Complete post editor
    ├── blog-post.tsx           # Single post display
    └── blog-list.tsx           # Post listing component

app/api/
├── posts/
│   ├── route.ts               # Posts API endpoints
│   ├── [id]/route.ts          # Individual post endpoints
│   └── init/route.ts          # System initialization
├── post-types/
│   └── route.ts               # Post types API
└── taxonomies/
    ├── route.ts               # Taxonomies API
    └── [name]/terms/route.ts  # Taxonomy terms API

app/admin/blog/
├── page.tsx                   # Blog management interface
├── new/page.tsx              # New post creation
└── edit/[id]/page.tsx        # Post editing interface

app/blog/
├── page.tsx                   # Public blog listing
└── [slug]/page.tsx           # Individual blog post
```

## 🛠️ Installation & Setup

### 1. Database Setup

The system requires the Prisma schema to be updated with the Posts models. The models are already defined in your `prisma/schema.prisma` file.

Run the database migration:

```bash
npx prisma db push
# or
npx prisma migrate dev --name add-posts-system
```

### 2. Initialize the System

After setting up the database, initialize the default post types and taxonomies:

```bash
# Via API call
curl -X POST http://localhost:3000/api/posts/init?action=full

# Or programmatically
import { initializePostsSystem } from '@/lib/posts/init'
await initializePostsSystem()
```

### 3. Dependencies

The system uses these additional dependencies:

```bash
pnpm add isomorphic-dompurify date-fns
```

## 📖 Usage Examples

### Creating a Blog Post

```typescript
import { PostService } from '@/lib/posts/services/post-service'

const postService = new PostService()

const newPost = await postService.createPost({
  title: 'My First Blog Post',
  content: '<p>This is the content of my blog post...</p>',
  excerpt: 'A brief summary of the post',
  postType: 'post',
  status: 'published',
  authorName: 'John Doe',
  authorEmail: '<EMAIL>',
  seoTitle: 'SEO Optimized Title',
  seoDescription: 'Meta description for search engines',
  seoKeywords: ['blog', 'tutorial', 'guide'],
  featuredImage: 'https://example.com/image.jpg',
  allowComments: true,
  isFeatured: true,
})
```

### Querying Posts

```typescript
// Get published blog posts
const posts = await postService.queryPosts({
  postType: 'post',
  status: 'published',
  orderBy: 'publishedAt',
  order: 'desc',
  limit: 10,
  include: ['taxonomyTerms', 'meta']
})

// Search posts
const searchResults = await postService.queryPosts({
  search: 'tutorial',
  postType: 'post',
  status: 'published'
})

// Filter by category
const categoryPosts = await postService.queryPosts({
  postType: 'post',
  taxonomies: {
    category: ['technology', 'tutorials']
  }
})
```

### Creating Custom Post Types

```typescript
import { PostTypeService } from '@/lib/posts/services/post-type-service'

const postTypeService = new PostTypeService()

await postTypeService.registerPostType({
  name: 'portfolio',
  label: 'Portfolio Item',
  labelPlural: 'Portfolio Items',
  description: 'Portfolio showcase items',
  icon: '🎨',
  isPublic: true,
  supportsTitle: true,
  supportsContent: true,
  supportsThumbnail: true,
  supportsPageBuilder: true,
  taxonomies: ['portfolio_category', 'portfolio_tag']
})
```

### Managing Taxonomies

```typescript
import { TaxonomyService } from '@/lib/posts/services/taxonomy-service'

const taxonomyService = new TaxonomyService()

// Create a custom taxonomy
await taxonomyService.registerTaxonomy({
  name: 'portfolio_category',
  label: 'Portfolio Category',
  labelPlural: 'Portfolio Categories',
  isHierarchical: true,
  postTypes: ['portfolio']
})

// Create taxonomy terms
await taxonomyService.createTerm({
  name: 'Web Design',
  taxonomyId: 'taxonomy-id',
  description: 'Web design projects',
  color: '#3B82F6'
})
```

## 🎨 Frontend Components

### Blog Post Display

```tsx
import { BlogPost } from '@/lib/posts/components/blog-post'

<BlogPost
  post={post}
  showFullContent={true}
  showMeta={true}
  showActions={true}
  showComments={true}
  onLike={() => handleLike(post.id)}
  onShare={() => handleShare(post)}
  onComment={() => scrollToComments()}
/>
```

### Blog List

```tsx
import { BlogList } from '@/lib/posts/components/blog-list'

<BlogList
  showSearch={true}
  showFilters={true}
  showPagination={true}
  layout="grid"
  postsPerPage={9}
  baseUrl="/blog"
/>
```

### Post Editor

```tsx
import { PostEditor } from '@/lib/posts/components/post-editor'

<PostEditor
  post={existingPost} // Optional for editing
  postType="post"
  onSave={handleSave}
  onPreview={handlePreview}
  onDelete={handleDelete}
/>
```

## 🔧 API Endpoints

### Posts API

- `GET /api/posts` - Query posts with filters
- `POST /api/posts` - Create new post
- `GET /api/posts/[id]` - Get post by ID
- `PUT /api/posts/[id]` - Update post
- `DELETE /api/posts/[id]` - Delete post

### Post Types API

- `GET /api/post-types` - Get all post types
- `POST /api/post-types` - Register new post type

### Taxonomies API

- `GET /api/taxonomies` - Get all taxonomies
- `POST /api/taxonomies` - Register new taxonomy
- `GET /api/taxonomies/[name]/terms` - Get taxonomy terms
- `POST /api/taxonomies/[name]/terms` - Create new term

### System Initialization

- `POST /api/posts/init?action=full` - Initialize complete system
- `POST /api/posts/init?action=post-types` - Initialize post types only
- `POST /api/posts/init?action=taxonomies` - Initialize taxonomies only

## 🔒 Security Features

- **Input Validation**: Comprehensive data validation
- **Content Sanitization**: XSS prevention with DOMPurify
- **SQL Injection Protection**: Prisma ORM security
- **Permission System**: Role-based access control ready

## 🎯 SEO Features

- **Meta Tags**: Title, description, keywords
- **Open Graph**: Social media optimization
- **Twitter Cards**: Twitter-specific metadata
- **Canonical URLs**: Duplicate content prevention
- **Structured Data**: JSON-LD schema ready
- **Sitemap Ready**: Easy integration with sitemap generation

## 🚀 Performance Optimizations

- **Database Indexing**: Optimized queries with proper indexes
- **Lazy Loading**: Components load data as needed
- **Caching Ready**: Built for integration with caching layers
- **Pagination**: Efficient data loading with pagination
- **Image Optimization**: Ready for Next.js image optimization

## 🔄 Migration from WordPress

The system is designed to be familiar to WordPress users:

- **Post Types** → Same concept, enhanced with TypeScript
- **Custom Fields** → JSON-based flexible metadata
- **Taxonomies** → Categories, tags, and custom taxonomies
- **Revisions** → Complete version control
- **Comments** → Nested comment system
- **SEO** → Built-in optimization fields

## 📚 Advanced Usage

### Custom Field Types

```typescript
// Define custom fields in post type
const customFields = {
  price: { type: 'number', required: true },
  gallery: { type: 'array', items: 'string' },
  specifications: { type: 'object' }
}

await postTypeService.registerPostType({
  name: 'product',
  customFields
})
```

### Hooks and Events

```typescript
// Post lifecycle hooks (implement as needed)
const postService = new PostService()

postService.on('post:created', (post) => {
  // Send notifications, update cache, etc.
})

postService.on('post:published', (post) => {
  // Update sitemap, send to social media, etc.
})
```

## 🤝 Contributing

1. Follow TypeScript best practices
2. Add tests for new features
3. Update documentation
4. Follow the existing code style
5. Add proper error handling

## 📄 License

This Posts system is part of your e-commerce application and follows the same licensing terms.
