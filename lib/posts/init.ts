// WordPress-Style Posts System Initialization
// Script to initialize default post types, taxonomies, and sample data

import { PostTypeService } from './services/post-type-service'
import { TaxonomyService } from './services/taxonomy-service'
import { PostService } from './services/post-service'

export class PostsSystemInitializer {
  private postTypeService: PostTypeService
  private taxonomyService: TaxonomyService
  private postService: PostService

  constructor() {
    this.postTypeService = new PostTypeService()
    this.taxonomyService = new TaxonomyService()
    this.postService = new PostService()
  }

  /**
   * Initialize the complete posts system
   */
  async initializeSystem(): Promise<void> {
    console.log('🚀 Initializing WordPress-style Posts System...')

    try {
      // Step 1: Initialize Post Types
      await this.initializePostTypes()
      
      // Step 2: Initialize Taxonomies
      await this.initializeTaxonomies()
      
      // Step 3: Create Sample Categories and Tags
      await this.createSampleTerms()
      
      // Step 4: Create Sample Posts (optional)
      await this.createSamplePosts()

      console.log('✅ Posts System initialized successfully!')
    } catch (error) {
      console.error('❌ Error initializing Posts System:', error)
      throw error
    }
  }

  /**
   * Initialize default post types
   */
  private async initializePostTypes(): Promise<void> {
    console.log('📝 Initializing post types...')
    
    const result = await this.postTypeService.initializeDefaultPostTypes()
    
    if (result.success) {
      console.log('✅ Post types initialized')
    } else {
      throw new Error(`Failed to initialize post types: ${result.error}`)
    }
  }

  /**
   * Initialize default taxonomies
   */
  private async initializeTaxonomies(): Promise<void> {
    console.log('🏷️ Initializing taxonomies...')
    
    const result = await this.taxonomyService.initializeDefaultTaxonomies()
    
    if (result.success) {
      console.log('✅ Taxonomies initialized')
    } else {
      throw new Error(`Failed to initialize taxonomies: ${result.error}`)
    }
  }

  /**
   * Create sample taxonomy terms
   */
  private async createSampleTerms(): Promise<void> {
    console.log('🎯 Creating sample categories and tags...')

    try {
      // Get taxonomies
      const categoryTaxonomy = await this.taxonomyService.getTaxonomy('category')
      const tagTaxonomy = await this.taxonomyService.getTaxonomy('tag')
      const productCategoryTaxonomy = await this.taxonomyService.getTaxonomy('product_category')
      const productTagTaxonomy = await this.taxonomyService.getTaxonomy('product_tag')

      // Create blog categories
      if (categoryTaxonomy.success && categoryTaxonomy.data) {
        const categories = [
          {
            name: 'Technology',
            description: 'Posts about technology and innovation',
            color: '#3B82F6'
          },
          {
            name: 'Lifestyle',
            description: 'Lifestyle and wellness content',
            color: '#10B981'
          },
          {
            name: 'Business',
            description: 'Business insights and tips',
            color: '#F59E0B'
          },
          {
            name: 'News',
            description: 'Latest news and updates',
            color: '#EF4444'
          }
        ]

        for (const category of categories) {
          await this.taxonomyService.createTerm({
            ...category,
            taxonomyId: categoryTaxonomy.data.id
          })
        }
      }

      // Create blog tags
      if (tagTaxonomy.success && tagTaxonomy.data) {
        const tags = [
          'tutorial', 'guide', 'tips', 'review', 'news', 'update',
          'beginner', 'advanced', 'featured', 'trending'
        ]

        for (const tag of tags) {
          await this.taxonomyService.createTerm({
            name: tag.charAt(0).toUpperCase() + tag.slice(1),
            taxonomyId: tagTaxonomy.data.id
          })
        }
      }

      // Create product categories
      if (productCategoryTaxonomy.success && productCategoryTaxonomy.data) {
        const productCategories = [
          {
            name: 'Kids Clothing',
            description: 'Clothing for children',
            color: '#8B5CF6'
          },
          {
            name: 'Accessories',
            description: 'Kids accessories and extras',
            color: '#EC4899'
          },
          {
            name: 'Toys',
            description: 'Educational and fun toys',
            color: '#06B6D4'
          }
        ]

        for (const category of productCategories) {
          await this.taxonomyService.createTerm({
            ...category,
            taxonomyId: productCategoryTaxonomy.data.id
          })
        }
      }

      console.log('✅ Sample terms created')
    } catch (error) {
      console.error('Error creating sample terms:', error)
    }
  }

  /**
   * Create sample blog posts
   */
  private async createSamplePosts(): Promise<void> {
    console.log('📄 Creating sample blog posts...')

    try {
      // Get category terms for assignment
      const categoryTerms = await this.taxonomyService.getTerms('category')
      const tagTerms = await this.taxonomyService.getTerms('tag')

      const samplePosts = [
        {
          title: 'Welcome to Our Blog',
          content: `
            <h2>Welcome to our new blog!</h2>
            <p>We're excited to share our journey with you through this blog. Here you'll find insights, tips, and stories about our products and the community we're building.</p>
            <p>Stay tuned for regular updates, tutorials, and behind-the-scenes content that we hope you'll find valuable and engaging.</p>
            <h3>What to Expect</h3>
            <ul>
              <li>Product updates and announcements</li>
              <li>How-to guides and tutorials</li>
              <li>Community spotlights</li>
              <li>Industry insights and trends</li>
            </ul>
            <p>Thank you for being part of our community!</p>
          `,
          excerpt: 'Welcome to our new blog! We\'re excited to share our journey with you.',
          status: 'published' as const,
          postType: 'post',
          authorName: 'Admin User',
          authorEmail: '<EMAIL>',
          featuredImage: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=400&fit=crop',
          featuredImageAlt: 'Welcome blog post image',
          seoTitle: 'Welcome to Our Blog - Latest Updates and Insights',
          seoDescription: 'Welcome to our blog where we share insights, tips, and stories about our products and community.',
          seoKeywords: ['blog', 'welcome', 'community', 'updates'],
          isFeatured: true,
          allowComments: true,
        },
        {
          title: 'The Importance of Quality Kids Clothing',
          content: `
            <h2>Why Quality Matters in Children's Clothing</h2>
            <p>When it comes to children's clothing, quality should never be compromised. Kids are active, growing, and need clothes that can keep up with their adventures while providing comfort and durability.</p>
            
            <h3>Key Factors to Consider</h3>
            <h4>1. Fabric Quality</h4>
            <p>Choose natural, breathable fabrics like cotton that are gentle on sensitive skin and allow for proper air circulation.</p>
            
            <h4>2. Durability</h4>
            <p>Kids' clothes need to withstand frequent washing, playing, and general wear and tear. Look for reinforced seams and quality construction.</p>
            
            <h4>3. Comfort and Fit</h4>
            <p>Proper fit is crucial for children's comfort and movement. Clothes should allow for growth while not being too loose or restrictive.</p>
            
            <h4>4. Safety</h4>
            <p>Ensure all clothing meets safety standards, with secure buttons, safe dyes, and appropriate sizing for the child's age.</p>
            
            <p>Investing in quality children's clothing is an investment in your child's comfort, health, and happiness.</p>
          `,
          excerpt: 'Discover why quality matters when choosing children\'s clothing and what factors to consider.',
          status: 'published' as const,
          postType: 'post',
          authorName: 'Sarah Johnson',
          authorEmail: '<EMAIL>',
          featuredImage: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=800&h=400&fit=crop',
          featuredImageAlt: 'Quality kids clothing',
          seoTitle: 'The Importance of Quality Kids Clothing - Complete Guide',
          seoDescription: 'Learn why quality matters in children\'s clothing and discover key factors to consider when shopping for kids.',
          seoKeywords: ['kids clothing', 'quality', 'children', 'fabric', 'durability'],
          allowComments: true,
        },
        {
          title: 'Seasonal Style Guide for Kids',
          content: `
            <h2>Dressing Your Kids for Every Season</h2>
            <p>Each season brings its own challenges and opportunities when it comes to dressing children. Here's our comprehensive guide to keeping your kids stylish and comfortable year-round.</p>
            
            <h3>Spring: Fresh and Layered</h3>
            <p>Spring weather can be unpredictable, so layering is key. Light sweaters, comfortable jeans, and breathable fabrics work well.</p>
            
            <h3>Summer: Cool and Protected</h3>
            <p>Focus on lightweight, breathable fabrics, sun protection, and comfortable fits that allow for active play.</p>
            
            <h3>Fall: Cozy and Practical</h3>
            <p>Transition into warmer layers with stylish jackets, comfortable pants, and versatile pieces that can be mixed and matched.</p>
            
            <h3>Winter: Warm and Functional</h3>
            <p>Prioritize warmth without sacrificing style. Look for insulated jackets, warm accessories, and layering pieces.</p>
            
            <h3>Year-Round Tips</h3>
            <ul>
              <li>Choose versatile pieces that can be styled multiple ways</li>
              <li>Invest in quality basics that last</li>
              <li>Consider your child's activities and lifestyle</li>
              <li>Don't forget about comfort and ease of movement</li>
            </ul>
          `,
          excerpt: 'A comprehensive guide to dressing your kids stylishly and comfortably throughout the year.',
          status: 'published' as const,
          postType: 'post',
          authorName: 'Mike Chen',
          authorEmail: '<EMAIL>',
          featuredImage: 'https://images.unsplash.com/photo-1519340241574-2cec6aef0c01?w=800&h=400&fit=crop',
          featuredImageAlt: 'Kids seasonal clothing',
          seoTitle: 'Seasonal Style Guide for Kids - Year-Round Fashion Tips',
          seoDescription: 'Complete seasonal style guide for children\'s clothing. Learn how to dress your kids for every season.',
          seoKeywords: ['kids fashion', 'seasonal clothing', 'children style', 'wardrobe'],
          allowComments: true,
        }
      ]

      for (const postData of samplePosts) {
        // Assign random categories and tags
        const randomCategories = categoryTerms.success && categoryTerms.data 
          ? [categoryTerms.data[Math.floor(Math.random() * categoryTerms.data.length)]?.id].filter(Boolean)
          : []
        
        const randomTags = tagTerms.success && tagTerms.data
          ? tagTerms.data
              .sort(() => 0.5 - Math.random())
              .slice(0, 2)
              .map(tag => tag.id)
          : []

        await this.postService.createPost({
          ...postData,
          taxonomyTerms: [...randomCategories, ...randomTags]
        })
      }

      console.log('✅ Sample posts created')
    } catch (error) {
      console.error('Error creating sample posts:', error)
    }
  }

  /**
   * Reset the entire system (use with caution!)
   */
  async resetSystem(): Promise<void> {
    console.log('🔄 Resetting Posts System...')
    console.warn('⚠️ This will delete all posts, post types, and taxonomies!')
    
    // This would require additional methods to delete all data
    // Implementation depends on your specific needs and safety requirements
    
    console.log('✅ System reset complete')
  }
}

// Export convenience function
export async function initializePostsSystem(): Promise<void> {
  const initializer = new PostsSystemInitializer()
  await initializer.initializeSystem()
}

// Export individual initialization functions
export async function initializePostTypes(): Promise<void> {
  const postTypeService = new PostTypeService()
  await postTypeService.initializeDefaultPostTypes()
}

export async function initializeTaxonomies(): Promise<void> {
  const taxonomyService = new TaxonomyService()
  await taxonomyService.initializeDefaultTaxonomies()
}
