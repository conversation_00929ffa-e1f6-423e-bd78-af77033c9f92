'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { Role, Permission, PermissionResult, PermissionAction } from '../../posts/types'
import { usePermissions } from '../hooks/use-permissions'

interface PermissionContextType {
  userRoles: Role[]
  userPermissions: Permission[]
  checkPermission: (resource: string, action: PermissionAction, resourceId?: string, context?: Record<string, any>) => Promise<PermissionResult>
  can: (resource: string, action: PermissionAction, resourceId?: string) => boolean
  cannot: (resource: string, action: PermissionAction, resourceId?: string) => boolean
  isLoading: boolean
  refreshPermissions: () => Promise<void>
}

const PermissionContext = createContext<PermissionContextType | undefined>(undefined)

interface PermissionProviderProps {
  userId?: string
  children: React.ReactNode
}

export function PermissionProvider({ userId, children }: PermissionProviderProps) {
  const [userRoles, setUserRoles] = useState<Role[]>([])
  const [userPermissions, setUserPermissions] = useState<Permission[]>([])
  const [isLoadingRoles, setIsLoadingRoles] = useState(false)

  const {
    checkPermission,
    can,
    cannot,
    isLoading: isCheckingPermissions,
    clearCache
  } = usePermissions({ userId })

  const isLoading = isLoadingRoles || isCheckingPermissions

  /**
   * Fetch user roles and permissions
   */
  const fetchUserRolesAndPermissions = async () => {
    if (!userId) {
      setUserRoles([])
      setUserPermissions([])
      return
    }

    setIsLoadingRoles(true)

    try {
      // Fetch user roles
      const rolesResponse = await fetch(`/api/auth/user-roles?userId=${userId}`)
      const rolesResult = await rolesResponse.json()

      if (rolesResult.success) {
        // Fetch detailed role information
        const roleDetails = await Promise.all(
          rolesResult.data.map(async (userRole: any) => {
            const roleResponse = await fetch(`/api/auth/roles/${userRole.roleId}`)
            const roleResult = await roleResponse.json()
            return roleResult.success ? roleResult.data : null
          })
        )

        const validRoles = roleDetails.filter(Boolean) as Role[]
        setUserRoles(validRoles)

        // Extract all permissions from roles
        const allPermissions: Permission[] = []
        validRoles.forEach(role => {
          if (role.permissions) {
            allPermissions.push(...role.permissions)
          }
        })

        // Remove duplicate permissions
        const uniquePermissions = allPermissions.filter((permission, index, self) =>
          index === self.findIndex(p => p.resource === permission.resource && p.action === permission.action)
        )

        setUserPermissions(uniquePermissions)
      }
    } catch (error) {
      console.error('Error fetching user roles and permissions:', error)
    } finally {
      setIsLoadingRoles(false)
    }
  }

  /**
   * Refresh permissions and clear cache
   */
  const refreshPermissions = async () => {
    clearCache()
    await fetchUserRolesAndPermissions()
  }

  useEffect(() => {
    fetchUserRolesAndPermissions()
  }, [userId])

  const contextValue: PermissionContextType = {
    userRoles,
    userPermissions,
    checkPermission,
    can,
    cannot,
    isLoading,
    refreshPermissions
  }

  return (
    <PermissionContext.Provider value={contextValue}>
      {children}
    </PermissionContext.Provider>
  )
}

/**
 * Hook to use permission context
 */
export function usePermissionContext() {
  const context = useContext(PermissionContext)
  if (context === undefined) {
    throw new Error('usePermissionContext must be used within a PermissionProvider')
  }
  return context
}

/**
 * Hook to check if user has specific role
 */
export function useHasRole(roleSlug: string) {
  const { userRoles, isLoading } = usePermissionContext()
  
  const hasRole = userRoles.some(role => role.slug === roleSlug && role.isActive)
  
  return { hasRole, isLoading }
}

/**
 * Hook to check if user has any of the specified roles
 */
export function useHasAnyRole(roleSlugs: string[]) {
  const { userRoles, isLoading } = usePermissionContext()
  
  const hasAnyRole = userRoles.some(role => 
    roleSlugs.includes(role.slug) && role.isActive
  )
  
  return { hasAnyRole, isLoading }
}

/**
 * Hook to check if user has all of the specified roles
 */
export function useHasAllRoles(roleSlugs: string[]) {
  const { userRoles, isLoading } = usePermissionContext()
  
  const hasAllRoles = roleSlugs.every(slug =>
    userRoles.some(role => role.slug === slug && role.isActive)
  )
  
  return { hasAllRoles, isLoading }
}

/**
 * Hook to get user's highest role level
 */
export function useUserLevel() {
  const { userRoles, isLoading } = usePermissionContext()
  
  const level = userRoles.length > 0 
    ? Math.min(...userRoles.filter(role => role.isActive).map(role => role.level))
    : null
  
  return { level, isLoading }
}

/**
 * Component for role-based rendering
 */
interface RoleGateProps {
  roles: string | string[]
  requireAll?: boolean
  fallback?: React.ReactNode
  children: React.ReactNode
}

export function RoleGate({ 
  roles, 
  requireAll = false, 
  fallback, 
  children 
}: RoleGateProps) {
  const roleSlugs = Array.isArray(roles) ? roles : [roles]
  
  const { hasAnyRole, isLoading: hasAnyRoleLoading } = useHasAnyRole(roleSlugs)
  const { hasAllRoles, isLoading: hasAllRolesLoading } = useHasAllRoles(roleSlugs)
  
  const isLoading = hasAnyRoleLoading || hasAllRolesLoading
  const hasAccess = requireAll ? hasAllRoles : hasAnyRole

  if (isLoading) {
    return <div className="animate-pulse bg-gray-200 rounded h-8 w-32"></div>
  }

  if (!hasAccess) {
    return fallback || null
  }

  return <>{children}</>
}

/**
 * Higher-order component for role-based access
 */
export function withRole<T extends object>(
  Component: React.ComponentType<T>,
  roles: string | string[],
  requireAll = false,
  fallback?: React.ComponentType<T> | React.ReactNode
) {
  return function RoleWrappedComponent(props: T) {
    const roleSlugs = Array.isArray(roles) ? roles : [roles]
    const { hasAnyRole, hasAllRoles, isLoading } = requireAll 
      ? useHasAllRoles(roleSlugs)
      : useHasAnyRole(roleSlugs)

    const hasAccess = requireAll ? hasAllRoles : hasAnyRole

    if (isLoading) {
      return <div className="animate-pulse bg-gray-200 rounded h-8 w-32"></div>
    }

    if (!hasAccess) {
      if (fallback) {
        if (React.isValidElement(fallback)) {
          return fallback
        }
        const FallbackComponent = fallback as React.ComponentType<T>
        return <FallbackComponent {...props} />
      }
      return null
    }

    return <Component {...props} />
  }
}
