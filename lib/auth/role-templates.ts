// Role Templates for Quick Setup
// Pre-configured role templates for common use cases

import { RoleTemplate, Role, RoleCapabilities, ContentTypePermissions, Permission } from '../posts/types'

/**
 * Built-in role templates for quick setup
 */
export const roleTemplates: RoleTemplate[] = [
  // Admin Roles
  {
    id: 'super-admin',
    name: 'Super Administrator',
    description: 'Full system access with all permissions',
    category: 'admin',
    icon: '👑',
    isBuiltIn: true,
    tags: ['admin', 'full-access', 'system'],
    roleConfig: {
      name: 'Super Administrator',
      slug: 'super-admin',
      description: 'Complete system administrator with unrestricted access',
      color: '#dc2626',
      isSystem: true,
      isActive: true,
      level: 1,
      capabilities: {
        // System capabilities
        accessAdmin: true,
        manageUsers: true,
        manageRoles: true,
        manageSettings: true,
        viewAnalytics: true,
        managePlugins: true,
        manageThemes: true,
        manageBackups: true,
        
        // Content capabilities
        createContent: true,
        editOwnContent: true,
        editOthersContent: true,
        deleteOwnContent: true,
        deleteOthersContent: true,
        publishContent: true,
        moderateComments: true,
        
        // Media capabilities
        uploadMedia: true,
        editMedia: true,
        deleteMedia: true,
        
        // Advanced capabilities
        editCode: true,
        manageDatabase: true,
        viewLogs: true,
        exportData: true,
        importData: true,
      },
      contentTypePermissions: {},
      restrictions: {}
    }
  },

  {
    id: 'admin',
    name: 'Administrator',
    description: 'Site administrator with most permissions',
    category: 'admin',
    icon: '🛡️',
    isBuiltIn: true,
    tags: ['admin', 'management', 'content'],
    roleConfig: {
      name: 'Administrator',
      slug: 'administrator',
      description: 'Site administrator with content and user management access',
      color: '#dc2626',
      isSystem: true,
      isActive: true,
      level: 2,
      capabilities: {
        // System capabilities
        accessAdmin: true,
        manageUsers: true,
        manageRoles: false,
        manageSettings: true,
        viewAnalytics: true,
        managePlugins: false,
        manageThemes: true,
        manageBackups: false,
        
        // Content capabilities
        createContent: true,
        editOwnContent: true,
        editOthersContent: true,
        deleteOwnContent: true,
        deleteOthersContent: true,
        publishContent: true,
        moderateComments: true,
        
        // Media capabilities
        uploadMedia: true,
        editMedia: true,
        deleteMedia: true,
        
        // Advanced capabilities
        editCode: false,
        manageDatabase: false,
        viewLogs: true,
        exportData: true,
        importData: true,
      },
      contentTypePermissions: {},
      restrictions: {}
    }
  },

  // Content Roles
  {
    id: 'editor',
    name: 'Editor',
    description: 'Content editor with publishing permissions',
    category: 'content',
    icon: '✏️',
    isBuiltIn: true,
    tags: ['content', 'editor', 'publish'],
    roleConfig: {
      name: 'Editor',
      slug: 'editor',
      description: 'Content editor who can create, edit, and publish content',
      color: '#059669',
      isSystem: true,
      isActive: true,
      level: 3,
      capabilities: {
        // System capabilities
        accessAdmin: true,
        manageUsers: false,
        manageRoles: false,
        manageSettings: false,
        viewAnalytics: true,
        managePlugins: false,
        manageThemes: false,
        manageBackups: false,
        
        // Content capabilities
        createContent: true,
        editOwnContent: true,
        editOthersContent: true,
        deleteOwnContent: true,
        deleteOthersContent: false,
        publishContent: true,
        moderateComments: true,
        
        // Media capabilities
        uploadMedia: true,
        editMedia: true,
        deleteMedia: false,
        
        // Advanced capabilities
        editCode: false,
        manageDatabase: false,
        viewLogs: false,
        exportData: true,
        importData: false,
      },
      contentTypePermissions: {},
      restrictions: {}
    }
  },

  {
    id: 'author',
    name: 'Author',
    description: 'Content author with limited publishing permissions',
    category: 'content',
    icon: '📝',
    isBuiltIn: true,
    tags: ['content', 'author', 'create'],
    roleConfig: {
      name: 'Author',
      slug: 'author',
      description: 'Content author who can create and edit their own content',
      color: '#0891b2',
      isSystem: true,
      isActive: true,
      level: 4,
      capabilities: {
        // System capabilities
        accessAdmin: true,
        manageUsers: false,
        manageRoles: false,
        manageSettings: false,
        viewAnalytics: false,
        managePlugins: false,
        manageThemes: false,
        manageBackups: false,
        
        // Content capabilities
        createContent: true,
        editOwnContent: true,
        editOthersContent: false,
        deleteOwnContent: true,
        deleteOthersContent: false,
        publishContent: true,
        moderateComments: false,
        
        // Media capabilities
        uploadMedia: true,
        editMedia: false,
        deleteMedia: false,
        
        // Advanced capabilities
        editCode: false,
        manageDatabase: false,
        viewLogs: false,
        exportData: false,
        importData: false,
      },
      contentTypePermissions: {},
      restrictions: {
        maxPosts: 100,
        maxUploads: 50,
        maxStorageSize: 500, // 500MB
      }
    }
  },

  {
    id: 'contributor',
    name: 'Contributor',
    description: 'Content contributor without publishing permissions',
    category: 'content',
    icon: '👤',
    isBuiltIn: true,
    tags: ['content', 'contributor', 'draft'],
    roleConfig: {
      name: 'Contributor',
      slug: 'contributor',
      description: 'Content contributor who can create drafts but cannot publish',
      color: '#7c3aed',
      isSystem: true,
      isActive: true,
      level: 5,
      capabilities: {
        // System capabilities
        accessAdmin: true,
        manageUsers: false,
        manageRoles: false,
        manageSettings: false,
        viewAnalytics: false,
        managePlugins: false,
        manageThemes: false,
        manageBackups: false,
        
        // Content capabilities
        createContent: true,
        editOwnContent: true,
        editOthersContent: false,
        deleteOwnContent: false,
        deleteOthersContent: false,
        publishContent: false,
        moderateComments: false,
        
        // Media capabilities
        uploadMedia: true,
        editMedia: false,
        deleteMedia: false,
        
        // Advanced capabilities
        editCode: false,
        manageDatabase: false,
        viewLogs: false,
        exportData: false,
        importData: false,
      },
      contentTypePermissions: {},
      restrictions: {
        maxPosts: 20,
        maxUploads: 10,
        maxStorageSize: 100, // 100MB
      }
    }
  },

  // E-commerce Roles
  {
    id: 'shop-manager',
    name: 'Shop Manager',
    description: 'E-commerce shop manager with product and order management',
    category: 'ecommerce',
    icon: '🛒',
    isBuiltIn: true,
    tags: ['ecommerce', 'shop', 'products', 'orders'],
    roleConfig: {
      name: 'Shop Manager',
      slug: 'shop-manager',
      description: 'E-commerce manager with full product and order management access',
      color: '#ea580c',
      isSystem: false,
      isActive: true,
      level: 3,
      capabilities: {
        // System capabilities
        accessAdmin: true,
        manageUsers: false,
        manageRoles: false,
        manageSettings: false,
        viewAnalytics: true,
        managePlugins: false,
        manageThemes: false,
        manageBackups: false,
        
        // Content capabilities
        createContent: true,
        editOwnContent: true,
        editOthersContent: true,
        deleteOwnContent: true,
        deleteOthersContent: true,
        publishContent: true,
        moderateComments: true,
        
        // Media capabilities
        uploadMedia: true,
        editMedia: true,
        deleteMedia: true,
        
        // Advanced capabilities
        editCode: false,
        manageDatabase: false,
        viewLogs: false,
        exportData: true,
        importData: true,
      },
      contentTypePermissions: {
        'product': {
          create: true,
          read: true,
          update: true,
          delete: true,
          publish: true,
          moderate: true,
          viewOwn: true,
          viewOthers: true,
          editOwn: true,
          editOthers: true,
          deleteOwn: true,
          deleteOthers: true,
        },
        'order': {
          create: false,
          read: true,
          update: true,
          delete: false,
          publish: false,
          moderate: true,
          viewOwn: true,
          viewOthers: true,
          editOwn: true,
          editOthers: true,
          deleteOwn: false,
          deleteOthers: false,
        }
      },
      restrictions: {}
    }
  },

  // Developer Roles
  {
    id: 'developer',
    name: 'Developer',
    description: 'Developer with code and system access',
    category: 'developer',
    icon: '💻',
    isBuiltIn: true,
    tags: ['developer', 'code', 'system', 'technical'],
    roleConfig: {
      name: 'Developer',
      slug: 'developer',
      description: 'Developer with code editing and system management access',
      color: '#1f2937',
      isSystem: false,
      isActive: true,
      level: 2,
      capabilities: {
        // System capabilities
        accessAdmin: true,
        manageUsers: false,
        manageRoles: false,
        manageSettings: true,
        viewAnalytics: true,
        managePlugins: true,
        manageThemes: true,
        manageBackups: true,
        
        // Content capabilities
        createContent: true,
        editOwnContent: true,
        editOthersContent: false,
        deleteOwnContent: true,
        deleteOthersContent: false,
        publishContent: true,
        moderateComments: false,
        
        // Media capabilities
        uploadMedia: true,
        editMedia: true,
        deleteMedia: true,
        
        // Advanced capabilities
        editCode: true,
        manageDatabase: true,
        viewLogs: true,
        exportData: true,
        importData: true,
      },
      contentTypePermissions: {},
      restrictions: {}
    }
  },

  // Custom Role Template
  {
    id: 'custom',
    name: 'Custom Role',
    description: 'Create a custom role from scratch',
    category: 'custom',
    icon: '⚙️',
    isBuiltIn: true,
    tags: ['custom', 'blank', 'configure'],
    roleConfig: {
      name: 'Custom Role',
      slug: 'custom-role',
      description: 'Custom role with configurable permissions',
      color: '#6b7280',
      isSystem: false,
      isActive: true,
      level: 10,
      capabilities: {
        // All capabilities set to false by default
        accessAdmin: false,
        manageUsers: false,
        manageRoles: false,
        manageSettings: false,
        viewAnalytics: false,
        managePlugins: false,
        manageThemes: false,
        manageBackups: false,
        createContent: false,
        editOwnContent: false,
        editOthersContent: false,
        deleteOwnContent: false,
        deleteOthersContent: false,
        publishContent: false,
        moderateComments: false,
        uploadMedia: false,
        editMedia: false,
        deleteMedia: false,
        editCode: false,
        manageDatabase: false,
        viewLogs: false,
        exportData: false,
        importData: false,
      },
      contentTypePermissions: {},
      restrictions: {}
    }
  }
]

/**
 * Get role template by ID
 */
export function getRoleTemplate(templateId: string): RoleTemplate | undefined {
  return roleTemplates.find(template => template.id === templateId)
}

/**
 * Get role templates by category
 */
export function getRoleTemplatesByCategory(category: string): RoleTemplate[] {
  return roleTemplates.filter(template => template.category === category)
}

/**
 * Get all role template categories
 */
export function getRoleTemplateCategories(): Array<{
  id: string
  name: string
  description: string
  count: number
}> {
  const categories = [
    {
      id: 'admin',
      name: 'Administration',
      description: 'Administrative roles with system management access',
      count: roleTemplates.filter(t => t.category === 'admin').length
    },
    {
      id: 'content',
      name: 'Content Management',
      description: 'Content creation and editing roles',
      count: roleTemplates.filter(t => t.category === 'content').length
    },
    {
      id: 'ecommerce',
      name: 'E-commerce',
      description: 'Online store and product management roles',
      count: roleTemplates.filter(t => t.category === 'ecommerce').length
    },
    {
      id: 'developer',
      name: 'Development',
      description: 'Technical roles with code and system access',
      count: roleTemplates.filter(t => t.category === 'developer').length
    },
    {
      id: 'custom',
      name: 'Custom',
      description: 'Create custom roles with specific permissions',
      count: roleTemplates.filter(t => t.category === 'custom').length
    }
  ]

  return categories
}
