// Enhanced Role & Permission Service
// Comprehensive role-based access control system

import { PrismaClient } from '@prisma/client'
import { 
  Role, 
  Permission, 
  UserRole, 
  PermissionCheck, 
  PermissionResult, 
  RoleCapabilities,
  ContentTypePermissions,
  RoleRestrictions,
  PermissionAction
} from '../posts/types'

export class RolePermissionService {
  private db: PrismaClient

  constructor() {
    this.db = new PrismaClient()
  }

  /**
   * Check if user has permission for a specific action
   */
  async checkPermission(check: PermissionCheck): Promise<PermissionResult> {
    try {
      const { resource, action, resourceId, userId, context } = check

      if (!userId) {
        return { allowed: false, reason: 'User not authenticated' }
      }

      // Get user roles
      const userRoles = await this.getUserRoles(userId)
      if (!userRoles.length) {
        return { allowed: false, reason: 'User has no roles assigned' }
      }

      // Check each role for permission
      for (const userRole of userRoles) {
        if (!userRole.isActive) continue

        // Check if role has expired
        if (userRole.expiresAt && new Date() > userRole.expiresAt) continue

        const role = await this.getRole(userRole.roleId)
        if (!role || !role.isActive) continue

        // Check role restrictions
        const restrictionCheck = await this.checkRoleRestrictions(role, context)
        if (!restrictionCheck.allowed) continue

        // Check specific permission
        const hasPermission = await this.roleHasPermission(role, resource, action, resourceId, context)
        if (hasPermission.allowed) {
          return hasPermission
        }
      }

      return { allowed: false, reason: 'Insufficient permissions' }
    } catch (error) {
      console.error('Error checking permission:', error)
      return { allowed: false, reason: 'Permission check failed' }
    }
  }

  /**
   * Check if role has specific permission
   */
  private async roleHasPermission(
    role: Role, 
    resource: string, 
    action: PermissionAction, 
    resourceId?: string,
    context?: Record<string, any>
  ): Promise<PermissionResult> {
    // Check direct permissions
    const hasDirectPermission = role.permissions.some(permission => 
      permission.resource === resource && permission.action === action
    )

    if (hasDirectPermission) {
      return { allowed: true, reason: 'Direct permission granted' }
    }

    // Check wildcard permissions
    const hasWildcardPermission = role.permissions.some(permission => 
      (permission.resource === '*' || permission.resource === resource) &&
      (permission.action === 'manage' || permission.action === action)
    )

    if (hasWildcardPermission) {
      return { allowed: true, reason: 'Wildcard permission granted' }
    }

    // Check capability-based permissions
    const capabilityCheck = this.checkCapabilityPermission(role.capabilities, resource, action)
    if (capabilityCheck.allowed) {
      return capabilityCheck
    }

    // Check content type permissions
    if (resource.startsWith('content:')) {
      const contentType = resource.split(':')[1]
      const contentPermissionCheck = this.checkContentTypePermission(
        role.contentTypePermissions, 
        contentType, 
        action,
        context
      )
      if (contentPermissionCheck.allowed) {
        return contentPermissionCheck
      }
    }

    return { allowed: false, reason: 'No matching permission found' }
  }

  /**
   * Check capability-based permissions
   */
  private checkCapabilityPermission(
    capabilities: RoleCapabilities, 
    resource: string, 
    action: PermissionAction
  ): PermissionResult {
    const capabilityMap: Record<string, keyof RoleCapabilities> = {
      'admin': 'accessAdmin',
      'users:manage': 'manageUsers',
      'roles:manage': 'manageRoles',
      'settings:manage': 'manageSettings',
      'analytics:view': 'viewAnalytics',
      'plugins:manage': 'managePlugins',
      'themes:manage': 'manageThemes',
      'backups:manage': 'manageBackups',
      'content:create': 'createContent',
      'content:edit:own': 'editOwnContent',
      'content:edit:others': 'editOthersContent',
      'content:delete:own': 'deleteOwnContent',
      'content:delete:others': 'deleteOthersContent',
      'content:publish': 'publishContent',
      'comments:moderate': 'moderateComments',
      'media:upload': 'uploadMedia',
      'media:edit': 'editMedia',
      'media:delete': 'deleteMedia',
      'code:edit': 'editCode',
      'database:manage': 'manageDatabase',
      'logs:view': 'viewLogs',
      'data:export': 'exportData',
      'data:import': 'importData',
    }

    const capabilityKey = capabilityMap[`${resource}:${action}`] || capabilityMap[resource]
    
    if (capabilityKey && capabilities[capabilityKey]) {
      return { allowed: true, reason: `Capability '${capabilityKey}' granted` }
    }

    return { allowed: false, reason: 'No matching capability' }
  }

  /**
   * Check content type specific permissions
   */
  private checkContentTypePermission(
    contentTypePermissions: ContentTypePermissions,
    contentType: string,
    action: PermissionAction,
    context?: Record<string, any>
  ): PermissionResult {
    const permissions = contentTypePermissions[contentType]
    if (!permissions) {
      return { allowed: false, reason: `No permissions for content type '${contentType}'` }
    }

    // Map actions to permission properties
    const actionMap: Record<PermissionAction, keyof typeof permissions> = {
      'create': 'create',
      'read': 'read',
      'update': 'update',
      'delete': 'delete',
      'publish': 'publish',
      'moderate': 'moderate',
      'view': 'read',
      'edit': 'update',
      'manage': 'update', // Fallback to update for manage
      'unpublish': 'publish',
      'export': 'read',
      'import': 'create',
    }

    const permissionKey = actionMap[action]
    if (!permissionKey) {
      return { allowed: false, reason: `Unknown action '${action}'` }
    }

    // Check ownership-based permissions
    if (context?.isOwner) {
      const ownPermissionKey = `${permissionKey}Own` as keyof typeof permissions
      if (ownPermissionKey in permissions && permissions[ownPermissionKey]) {
        return { allowed: true, reason: `Own content permission '${ownPermissionKey}' granted` }
      }
    }

    // Check others permissions
    const othersPermissionKey = `${permissionKey}Others` as keyof typeof permissions
    if (othersPermissionKey in permissions && permissions[othersPermissionKey]) {
      return { allowed: true, reason: `Others content permission '${othersPermissionKey}' granted` }
    }

    // Check general permission
    if (permissions[permissionKey]) {
      return { allowed: true, reason: `Content type permission '${permissionKey}' granted` }
    }

    return { allowed: false, reason: `No '${action}' permission for content type '${contentType}'` }
  }

  /**
   * Check role restrictions (time, IP, etc.)
   */
  private async checkRoleRestrictions(
    role: Role, 
    context?: Record<string, any>
  ): Promise<PermissionResult> {
    const restrictions = role.restrictions

    // Check time-based restrictions
    if (restrictions.accessHours) {
      const now = new Date()
      const currentTime = now.toTimeString().slice(0, 5) // HH:mm format
      
      if (currentTime < restrictions.accessHours.start || currentTime > restrictions.accessHours.end) {
        return { 
          allowed: false, 
          reason: `Access denied: outside allowed hours (${restrictions.accessHours.start}-${restrictions.accessHours.end})` 
        }
      }
    }

    // Check day-based restrictions
    if (restrictions.accessDays) {
      const currentDay = new Date().getDay()
      if (!restrictions.accessDays.includes(currentDay)) {
        return { allowed: false, reason: 'Access denied: outside allowed days' }
      }
    }

    // Check IP restrictions
    if (context?.userIP) {
      if (restrictions.blockedIPs?.includes(context.userIP)) {
        return { allowed: false, reason: 'Access denied: IP address blocked' }
      }
      
      if (restrictions.allowedIPs?.length && !restrictions.allowedIPs.includes(context.userIP)) {
        return { allowed: false, reason: 'Access denied: IP address not in allowed list' }
      }
    }

    return { allowed: true }
  }

  /**
   * Get user roles
   */
  async getUserRoles(userId: string): Promise<UserRole[]> {
    try {
      const userRoles = await this.db.userRole.findMany({
        where: { 
          userId,
          isActive: true,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } }
          ]
        },
        orderBy: { assignedAt: 'desc' }
      })

      return userRoles as UserRole[]
    } catch (error) {
      console.error('Error fetching user roles:', error)
      return []
    }
  }

  /**
   * Get role by ID
   */
  async getRole(roleId: string): Promise<Role | null> {
    try {
      const role = await this.db.role.findUnique({
        where: { id: roleId },
        include: {
          permissions: true
        }
      })

      return role as Role | null
    } catch (error) {
      console.error('Error fetching role:', error)
      return null
    }
  }

  /**
   * Assign role to user
   */
  async assignRole(
    userId: string, 
    roleId: string, 
    assignedBy: string,
    expiresAt?: Date
  ): Promise<UserRole> {
    try {
      // Check if user already has this role
      const existingUserRole = await this.db.userRole.findFirst({
        where: { userId, roleId, isActive: true }
      })

      if (existingUserRole) {
        throw new Error('User already has this role')
      }

      const userRole = await this.db.userRole.create({
        data: {
          userId,
          roleId,
          assignedBy,
          assignedAt: new Date(),
          expiresAt,
          isActive: true,
        }
      })

      return userRole as UserRole
    } catch (error) {
      console.error('Error assigning role:', error)
      throw error
    }
  }

  /**
   * Remove role from user
   */
  async removeRole(userId: string, roleId: string): Promise<void> {
    try {
      await this.db.userRole.updateMany({
        where: { userId, roleId },
        data: { isActive: false }
      })
    } catch (error) {
      console.error('Error removing role:', error)
      throw error
    }
  }

  /**
   * Create new role
   */
  async createRole(roleData: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>): Promise<Role> {
    try {
      const role = await this.db.role.create({
        data: {
          ...roleData,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        include: {
          permissions: true
        }
      })

      return role as Role
    } catch (error) {
      console.error('Error creating role:', error)
      throw error
    }
  }

  /**
   * Update role
   */
  async updateRole(roleId: string, updates: Partial<Role>): Promise<Role> {
    try {
      const role = await this.db.role.update({
        where: { id: roleId },
        data: {
          ...updates,
          updatedAt: new Date(),
        },
        include: {
          permissions: true
        }
      })

      return role as Role
    } catch (error) {
      console.error('Error updating role:', error)
      throw error
    }
  }

  /**
   * Delete role
   */
  async deleteRole(roleId: string): Promise<void> {
    try {
      // Check if role is system role
      const role = await this.getRole(roleId)
      if (role?.isSystem) {
        throw new Error('Cannot delete system role')
      }

      // Remove all user role assignments
      await this.db.userRole.updateMany({
        where: { roleId },
        data: { isActive: false }
      })

      // Delete the role
      await this.db.role.delete({
        where: { id: roleId }
      })
    } catch (error) {
      console.error('Error deleting role:', error)
      throw error
    }
  }

  /**
   * Get all roles
   */
  async getAllRoles(includeInactive = false): Promise<Role[]> {
    try {
      const roles = await this.db.role.findMany({
        where: includeInactive ? {} : { isActive: true },
        include: {
          permissions: true
        },
        orderBy: [
          { level: 'asc' },
          { name: 'asc' }
        ]
      })

      return roles as Role[]
    } catch (error) {
      console.error('Error fetching roles:', error)
      return []
    }
  }

  /**
   * Get user's effective permissions (combined from all roles)
   */
  async getUserPermissions(userId: string): Promise<Permission[]> {
    try {
      const userRoles = await this.getUserRoles(userId)
      const permissions: Permission[] = []

      for (const userRole of userRoles) {
        const role = await this.getRole(userRole.roleId)
        if (role) {
          permissions.push(...role.permissions)
        }
      }

      // Remove duplicates
      const uniquePermissions = permissions.filter((permission, index, self) =>
        index === self.findIndex(p => p.resource === permission.resource && p.action === permission.action)
      )

      return uniquePermissions
    } catch (error) {
      console.error('Error fetching user permissions:', error)
      return []
    }
  }
}

export const rolePermissionService = new RolePermissionService()
