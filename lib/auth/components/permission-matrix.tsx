'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Shield, 
  Lock, 
  Unlock,
  Eye,
  Edit,
  Trash2,
  Plus,
  Settings,
  Database,
  Users,
  ShoppingCart,
  BarChart3,
  Mail,
  FileText,
  Workflow
} from 'lucide-react'

interface Permission {
  id: string
  name: string
  description: string
  category: string
  resource: string
  action: string
}

interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  isSystem: boolean
  userCount: number
}

interface PermissionMatrixProps {
  roles: Role[]
  permissions: Permission[]
  onUpdateRolePermissions: (roleId: string, permissions: string[]) => void
  onCreateRole: (role: Omit<Role, 'id' | 'userCount'>) => void
  onDeleteRole: (roleId: string) => void
}

export function PermissionMatrix({
  roles,
  permissions,
  onUpdateRolePermissions,
  onCreateRole,
  onDeleteRole
}: PermissionMatrixProps) {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [showCreateRole, setShowCreateRole] = useState(false)

  // Group permissions by category
  const permissionsByCategory = permissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = []
    }
    acc[permission.category].push(permission)
    return acc
  }, {} as Record<string, Permission[]>)

  const categories = [
    { id: 'all', name: 'All Permissions', icon: Shield },
    { id: 'users', name: 'User Management', icon: Users },
    { id: 'content', name: 'Content Management', icon: FileText },
    { id: 'ecommerce', name: 'E-commerce', icon: ShoppingCart },
    { id: 'analytics', name: 'Analytics', icon: BarChart3 },
    { id: 'system', name: 'System Settings', icon: Settings },
    { id: 'database', name: 'Database', icon: Database },
    { id: 'workflows', name: 'Workflows', icon: Workflow },
    { id: 'communication', name: 'Communication', icon: Mail }
  ]

  const filteredPermissions = selectedCategory === 'all' 
    ? permissions 
    : permissions.filter(p => p.category === selectedCategory)

  const hasPermission = (roleId: string, permissionId: string) => {
    const role = roles.find(r => r.id === roleId)
    return role?.permissions.includes(permissionId) || false
  }

  const togglePermission = (roleId: string, permissionId: string) => {
    const role = roles.find(r => r.id === roleId)
    if (!role) return

    const currentPermissions = role.permissions
    const hasCurrentPermission = currentPermissions.includes(permissionId)
    
    const newPermissions = hasCurrentPermission
      ? currentPermissions.filter(p => p !== permissionId)
      : [...currentPermissions, permissionId]
    
    onUpdateRolePermissions(roleId, newPermissions)
  }

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'read':
      case 'view':
        return <Eye className="h-3 w-3" />
      case 'create':
      case 'add':
        return <Plus className="h-3 w-3" />
      case 'update':
      case 'edit':
        return <Edit className="h-3 w-3" />
      case 'delete':
      case 'remove':
        return <Trash2 className="h-3 w-3" />
      default:
        return <Shield className="h-3 w-3" />
    }
  }

  const getActionColor = (action: string) => {
    switch (action) {
      case 'read':
      case 'view':
        return 'text-blue-600'
      case 'create':
      case 'add':
        return 'text-green-600'
      case 'update':
      case 'edit':
        return 'text-yellow-600'
      case 'delete':
      case 'remove':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Permission Matrix</h2>
          <p className="text-muted-foreground">
            Configure role-based permissions for your application
          </p>
        </div>
        <Button onClick={() => setShowCreateRole(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Role
        </Button>
      </div>

      {/* Category Filter */}
      <Card>
        <CardContent className="p-4">
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
            <TabsList className="grid w-full grid-cols-3 lg:grid-cols-5">
              {categories.slice(0, 5).map(category => {
                const Icon = category.icon
                return (
                  <TabsTrigger key={category.id} value={category.id} className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    <span className="hidden sm:inline">{category.name}</span>
                  </TabsTrigger>
                )
              })}
            </TabsList>
            <div className="mt-2 grid grid-cols-2 lg:grid-cols-4 gap-2">
              {categories.slice(5).map(category => {
                const Icon = category.icon
                return (
                  <TabsTrigger key={category.id} value={category.id} className="flex items-center gap-2 h-10">
                    <Icon className="h-4 w-4" />
                    <span className="text-sm">{category.name}</span>
                  </TabsTrigger>
                )
              })}
            </div>
          </Tabs>
        </CardContent>
      </Card>

      {/* Permission Matrix */}
      <Card>
        <CardHeader>
          <CardTitle>Role Permissions</CardTitle>
          <CardDescription>
            Toggle permissions for each role. System roles have restricted editing.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-4 font-medium">Permission</th>
                  {roles.map(role => (
                    <th key={role.id} className="text-center p-4 font-medium min-w-32">
                      <div className="space-y-1">
                        <div className="flex items-center justify-center gap-2">
                          {role.name}
                          {role.isSystem && (
                            <Lock className="h-3 w-3 text-muted-foreground" />
                          )}
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {role.userCount} users
                        </Badge>
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {filteredPermissions.map(permission => (
                  <tr key={permission.id} className="border-b hover:bg-muted/50">
                    <td className="p-4">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className={getActionColor(permission.action)}>
                            {getActionIcon(permission.action)}
                          </span>
                          <span className="font-medium">{permission.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {permission.resource}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {permission.description}
                        </p>
                      </div>
                    </td>
                    {roles.map(role => (
                      <td key={role.id} className="p-4 text-center">
                        <Switch
                          checked={hasPermission(role.id, permission.id)}
                          onCheckedChange={() => togglePermission(role.id, permission.id)}
                          disabled={role.isSystem}
                        />
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredPermissions.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No permissions found for the selected category
            </div>
          )}
        </CardContent>
      </Card>

      {/* Role Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {roles.map(role => {
          const rolePermissions = permissions.filter(p => role.permissions.includes(p.id))
          const permissionsByCategory = rolePermissions.reduce((acc, p) => {
            acc[p.category] = (acc[p.category] || 0) + 1
            return acc
          }, {} as Record<string, number>)

          return (
            <Card key={role.id}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{role.name}</CardTitle>
                  {role.isSystem ? (
                    <Lock className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onDeleteRole(role.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  )}
                </div>
                <CardDescription>{role.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Total Permissions:</span>
                    <Badge>{rolePermissions.length}</Badge>
                  </div>
                  
                  <div className="space-y-2">
                    {Object.entries(permissionsByCategory).map(([category, count]) => (
                      <div key={category} className="flex items-center justify-between text-xs">
                        <span className="capitalize">{category}:</span>
                        <span>{count}</span>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex items-center justify-between text-sm pt-2 border-t">
                    <span>Users:</span>
                    <Badge variant="secondary">{role.userCount}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Create Role Modal */}
      {showCreateRole && (
        <CreateRoleModal
          onClose={() => setShowCreateRole(false)}
          onCreate={onCreateRole}
        />
      )}
    </div>
  )
}

interface CreateRoleModalProps {
  onClose: () => void
  onCreate: (role: Omit<Role, 'id' | 'userCount'>) => void
}

function CreateRoleModal({ onClose, onCreate }: CreateRoleModalProps) {
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')

  const handleCreate = () => {
    if (name && description) {
      onCreate({
        name,
        description,
        permissions: [],
        isSystem: false
      })
      onClose()
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Create New Role</CardTitle>
          <CardDescription>
            Create a new role with custom permissions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="role-name" className="text-sm font-medium">
              Role Name
            </label>
            <input
              id="role-name"
              type="text"
              placeholder="e.g., Content Manager"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
            />
          </div>
          
          <div className="space-y-2">
            <label htmlFor="role-description" className="text-sm font-medium">
              Description
            </label>
            <textarea
              id="role-description"
              placeholder="Describe the role's responsibilities..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full px-3 py-2 border rounded-md h-20 resize-none"
            />
          </div>
          
          <div className="flex gap-2 pt-4">
            <Button onClick={handleCreate} className="flex-1">
              Create Role
            </Button>
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
