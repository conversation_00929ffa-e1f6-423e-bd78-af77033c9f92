'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Save, 
  X, 
  Shield, 
  Users, 
  Settings, 
  Lock,
  Clock,
  Globe,
  Database,
  FileText,
  Image,
  Code,
  BarChart3
} from 'lucide-react'
import { Role, RoleCapabilities, ContentTypePermissions, RoleRestrictions } from '../../posts/types'

interface RoleEditorProps {
  role: Role
  isCreating: boolean
  onSave: (roleData: Partial<Role>) => void
  onCancel: () => void
}

export function RoleEditor({ role, isCreating, onSave, onCancel }: RoleEditorProps) {
  const [formData, setFormData] = useState<Partial<Role>>(role)
  const [activeTab, setActiveTab] = useState<'general' | 'capabilities' | 'content' | 'restrictions'>('general')

  useEffect(() => {
    setFormData(role)
  }, [role])

  const handleInputChange = (field: keyof Role, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleCapabilityChange = (capability: keyof RoleCapabilities, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      capabilities: {
        ...prev.capabilities,
        [capability]: value
      }
    }))
  }

  const handleContentTypePermissionChange = (
    contentType: string, 
    permission: string, 
    value: boolean
  ) => {
    setFormData(prev => ({
      ...prev,
      contentTypePermissions: {
        ...prev.contentTypePermissions,
        [contentType]: {
          ...prev.contentTypePermissions?.[contentType],
          [permission]: value
        }
      }
    }))
  }

  const handleRestrictionChange = (restriction: keyof RoleRestrictions, value: any) => {
    setFormData(prev => ({
      ...prev,
      restrictions: {
        ...prev.restrictions,
        [restriction]: value
      }
    }))
  }

  const handleSave = () => {
    // Generate slug from name if creating new role
    if (isCreating && formData.name) {
      formData.slug = formData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '')
    }

    onSave(formData)
  }

  const capabilityGroups = [
    {
      title: 'System Administration',
      icon: <Settings className="h-4 w-4" />,
      capabilities: [
        { key: 'accessAdmin', label: 'Access Admin Panel', description: 'Can access the admin dashboard' },
        { key: 'manageUsers', label: 'Manage Users', description: 'Create, edit, and delete users' },
        { key: 'manageRoles', label: 'Manage Roles', description: 'Create and modify user roles' },
        { key: 'manageSettings', label: 'Manage Settings', description: 'Modify system settings' },
        { key: 'viewAnalytics', label: 'View Analytics', description: 'Access analytics and reports' },
        { key: 'managePlugins', label: 'Manage Plugins', description: 'Install and configure plugins' },
        { key: 'manageThemes', label: 'Manage Themes', description: 'Install and modify themes' },
        { key: 'manageBackups', label: 'Manage Backups', description: 'Create and restore backups' },
      ]
    },
    {
      title: 'Content Management',
      icon: <FileText className="h-4 w-4" />,
      capabilities: [
        { key: 'createContent', label: 'Create Content', description: 'Create new posts and pages' },
        { key: 'editOwnContent', label: 'Edit Own Content', description: 'Edit content they created' },
        { key: 'editOthersContent', label: 'Edit Others Content', description: 'Edit content created by others' },
        { key: 'deleteOwnContent', label: 'Delete Own Content', description: 'Delete content they created' },
        { key: 'deleteOthersContent', label: 'Delete Others Content', description: 'Delete content created by others' },
        { key: 'publishContent', label: 'Publish Content', description: 'Publish and unpublish content' },
        { key: 'moderateComments', label: 'Moderate Comments', description: 'Approve and manage comments' },
      ]
    },
    {
      title: 'Media Management',
      icon: <Image className="h-4 w-4" />,
      capabilities: [
        { key: 'uploadMedia', label: 'Upload Media', description: 'Upload images and files' },
        { key: 'editMedia', label: 'Edit Media', description: 'Edit media files and metadata' },
        { key: 'deleteMedia', label: 'Delete Media', description: 'Delete media files' },
      ]
    },
    {
      title: 'Advanced Features',
      icon: <Code className="h-4 w-4" />,
      capabilities: [
        { key: 'editCode', label: 'Edit Code', description: 'Edit theme files and custom code' },
        { key: 'manageDatabase', label: 'Manage Database', description: 'Access database management tools' },
        { key: 'viewLogs', label: 'View Logs', description: 'Access system logs and debugging info' },
        { key: 'exportData', label: 'Export Data', description: 'Export content and user data' },
        { key: 'importData', label: 'Import Data', description: 'Import content and user data' },
      ]
    }
  ]

  const contentTypes = ['post', 'page', 'product', 'order', 'user', 'media']
  const contentPermissions = [
    { key: 'create', label: 'Create' },
    { key: 'read', label: 'Read' },
    { key: 'update', label: 'Update' },
    { key: 'delete', label: 'Delete' },
    { key: 'publish', label: 'Publish' },
    { key: 'moderate', label: 'Moderate' },
  ]

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b bg-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">
              {isCreating ? 'Create Role' : `Edit Role: ${role.name}`}
            </h1>
            <p className="text-gray-600">
              {isCreating ? 'Create a new user role with specific permissions' : 'Modify role permissions and settings'}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={onCancel}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleSave}>
              <Save className="h-4 w-4 mr-2" />
              Save Role
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="h-full flex flex-col">
          <div className="border-b bg-white px-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="capabilities">Capabilities</TabsTrigger>
              <TabsTrigger value="content">Content Types</TabsTrigger>
              <TabsTrigger value="restrictions">Restrictions</TabsTrigger>
            </TabsList>
          </div>

          <ScrollArea className="flex-1">
            <div className="p-6">
              <TabsContent value="general" className="space-y-6 mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Shield className="h-5 w-5" />
                      <span>Basic Information</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="role-name">Role Name</Label>
                        <Input
                          id="role-name"
                          value={formData.name || ''}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          placeholder="Administrator"
                        />
                      </div>

                      <div>
                        <Label htmlFor="role-level">Hierarchy Level</Label>
                        <Select
                          value={formData.level?.toString() || '10'}
                          onValueChange={(value) => handleInputChange('level', parseInt(value))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">1 - Super Admin</SelectItem>
                            <SelectItem value="2">2 - Admin</SelectItem>
                            <SelectItem value="3">3 - Manager</SelectItem>
                            <SelectItem value="4">4 - Editor</SelectItem>
                            <SelectItem value="5">5 - Author</SelectItem>
                            <SelectItem value="6">6 - Contributor</SelectItem>
                            <SelectItem value="10">10 - Custom</SelectItem>
                          </SelectContent>
                        </Select>
                        <p className="text-xs text-gray-500 mt-1">
                          Lower numbers have higher priority
                        </p>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="role-description">Description</Label>
                      <Textarea
                        id="role-description"
                        value={formData.description || ''}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        placeholder="Describe what this role can do..."
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="role-color">Color</Label>
                        <Input
                          id="role-color"
                          type="color"
                          value={formData.color || '#6b7280'}
                          onChange={(e) => handleInputChange('color', e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="role-active">Active</Label>
                          <Switch
                            id="role-active"
                            checked={formData.isActive ?? true}
                            onCheckedChange={(checked) => handleInputChange('isActive', checked)}
                          />
                        </div>
                        
                        {formData.isSystem && (
                          <div className="flex items-center space-x-2">
                            <Lock className="h-4 w-4 text-amber-500" />
                            <span className="text-sm text-amber-600">System Role</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="capabilities" className="space-y-6 mt-0">
                {capabilityGroups.map((group) => (
                  <Card key={group.title}>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        {group.icon}
                        <span>{group.title}</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {group.capabilities.map((capability) => (
                          <div key={capability.key} className="flex items-start space-x-3 p-3 border rounded-lg">
                            <Switch
                              checked={formData.capabilities?.[capability.key as keyof RoleCapabilities] ?? false}
                              onCheckedChange={(checked) => handleCapabilityChange(capability.key as keyof RoleCapabilities, checked)}
                            />
                            <div className="flex-1">
                              <Label className="text-sm font-medium">{capability.label}</Label>
                              <p className="text-xs text-gray-500 mt-1">{capability.description}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>

              <TabsContent value="content" className="space-y-6 mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Database className="h-5 w-5" />
                      <span>Content Type Permissions</span>
                    </CardTitle>
                    <p className="text-sm text-gray-500">
                      Configure permissions for each content type
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {contentTypes.map((contentType) => (
                        <div key={contentType} className="border rounded-lg p-4">
                          <h4 className="font-medium mb-3 capitalize">{contentType}</h4>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                            {contentPermissions.map((permission) => (
                              <div key={permission.key} className="flex items-center space-x-2">
                                <Switch
                                  checked={formData.contentTypePermissions?.[contentType]?.[permission.key] ?? false}
                                  onCheckedChange={(checked) => 
                                    handleContentTypePermissionChange(contentType, permission.key, checked)
                                  }
                                />
                                <Label className="text-sm">{permission.label}</Label>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="restrictions" className="space-y-6 mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Lock className="h-5 w-5" />
                      <span>Access Restrictions</span>
                    </CardTitle>
                    <p className="text-sm text-gray-500">
                      Set limits and restrictions for this role
                    </p>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Time Restrictions */}
                    <div>
                      <h4 className="font-medium mb-3 flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <span>Time Restrictions</span>
                      </h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>Access Start Time</Label>
                          <Input
                            type="time"
                            value={formData.restrictions?.accessHours?.start || ''}
                            onChange={(e) => handleRestrictionChange('accessHours', {
                              ...formData.restrictions?.accessHours,
                              start: e.target.value
                            })}
                          />
                        </div>
                        <div>
                          <Label>Access End Time</Label>
                          <Input
                            type="time"
                            value={formData.restrictions?.accessHours?.end || ''}
                            onChange={(e) => handleRestrictionChange('accessHours', {
                              ...formData.restrictions?.accessHours,
                              end: e.target.value
                            })}
                          />
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Content Limits */}
                    <div>
                      <h4 className="font-medium mb-3 flex items-center space-x-2">
                        <BarChart3 className="h-4 w-4" />
                        <span>Content Limits</span>
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div>
                          <Label>Max Posts</Label>
                          <Input
                            type="number"
                            value={formData.restrictions?.maxPosts || ''}
                            onChange={(e) => handleRestrictionChange('maxPosts', parseInt(e.target.value) || undefined)}
                            placeholder="Unlimited"
                          />
                        </div>
                        <div>
                          <Label>Max Pages</Label>
                          <Input
                            type="number"
                            value={formData.restrictions?.maxPages || ''}
                            onChange={(e) => handleRestrictionChange('maxPages', parseInt(e.target.value) || undefined)}
                            placeholder="Unlimited"
                          />
                        </div>
                        <div>
                          <Label>Max Uploads</Label>
                          <Input
                            type="number"
                            value={formData.restrictions?.maxUploads || ''}
                            onChange={(e) => handleRestrictionChange('maxUploads', parseInt(e.target.value) || undefined)}
                            placeholder="Unlimited"
                          />
                        </div>
                        <div>
                          <Label>Max Storage (MB)</Label>
                          <Input
                            type="number"
                            value={formData.restrictions?.maxStorageSize || ''}
                            onChange={(e) => handleRestrictionChange('maxStorageSize', parseInt(e.target.value) || undefined)}
                            placeholder="Unlimited"
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </div>
          </ScrollArea>
        </Tabs>
      </div>
    </div>
  )
}
