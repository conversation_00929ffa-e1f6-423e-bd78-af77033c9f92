'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Search, 
  Crown, 
  Shield, 
  FileText, 
  ShoppingCart, 
  Code, 
  Settings,
  ArrowRight,
  X
} from 'lucide-react'
import { RoleTemplate } from '../../posts/types'
import { roleTemplates, getRoleTemplateCategories } from '../role-templates'

interface RoleTemplateSelectorProps {
  onSelect: (template: RoleTemplate) => void
  onCancel?: () => void
  showAsGrid?: boolean
}

export function RoleTemplateSelector({ onSelect, onCancel, showAsGrid = false }: RoleTemplateSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  const categories = getRoleTemplateCategories()

  const filteredTemplates = roleTemplates.filter(template => {
    const matchesSearch = searchQuery === '' || 
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory

    return matchesSearch && matchesCategory
  })

  const getCategoryIcon = (category: string) => {
    const icons = {
      admin: <Crown className="h-5 w-5" />,
      content: <FileText className="h-5 w-5" />,
      ecommerce: <ShoppingCart className="h-5 w-5" />,
      developer: <Code className="h-5 w-5" />,
      custom: <Settings className="h-5 w-5" />
    }
    return icons[category as keyof typeof icons] || <Shield className="h-5 w-5" />
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      admin: 'bg-red-100 text-red-800 border-red-200',
      content: 'bg-blue-100 text-blue-800 border-blue-200',
      ecommerce: 'bg-green-100 text-green-800 border-green-200',
      developer: 'bg-purple-100 text-purple-800 border-purple-200',
      custom: 'bg-gray-100 text-gray-800 border-gray-200'
    }
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  const getRoleIcon = (template: RoleTemplate) => {
    return <span className="text-2xl">{template.icon}</span>
  }

  if (showAsGrid) {
    return (
      <div className="space-y-6">
        {/* Search and Filters */}
        <div className="flex items-center space-x-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="admin">Admin</TabsTrigger>
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="ecommerce">E-commerce</TabsTrigger>
            <TabsTrigger value="developer">Developer</TabsTrigger>
            <TabsTrigger value="custom">Custom</TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => (
            <Card key={template.id} className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => onSelect(template)}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    {getRoleIcon(template)}
                    <div>
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <Badge className={getCategoryColor(template.category)} variant="outline">
                        {template.category}
                      </Badge>
                    </div>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                </div>
              </CardHeader>
              
              <CardContent>
                <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                  {template.description}
                </p>

                <div className="flex flex-wrap gap-1">
                  {template.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                    >
                      {tag}
                    </span>
                  ))}
                  {template.tags.length > 3 && (
                    <span className="text-xs text-gray-400">
                      +{template.tags.length - 3} more
                    </span>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b bg-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Choose Role Template</h1>
            <p className="text-gray-600">
              Select a pre-configured role template to get started quickly
            </p>
          </div>
          {onCancel && (
            <Button variant="outline" onClick={onCancel}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <div className="p-6">
          {/* Search */}
          <div className="mb-6">
            <div className="relative max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Categories */}
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="space-y-6">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="admin">Admin</TabsTrigger>
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="ecommerce">E-commerce</TabsTrigger>
              <TabsTrigger value="developer">Developer</TabsTrigger>
              <TabsTrigger value="custom">Custom</TabsTrigger>
            </TabsList>

            {/* Category Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
              {categories.map((category) => (
                <Card 
                  key={category.id} 
                  className={`cursor-pointer transition-colors ${
                    selectedCategory === category.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => setSelectedCategory(category.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      {getCategoryIcon(category.id)}
                      <h3 className="font-medium">{category.name}</h3>
                    </div>
                    <p className="text-sm text-gray-500 mb-2">{category.description}</p>
                    <Badge variant="secondary">{category.count} templates</Badge>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Templates List */}
            <ScrollArea className="h-96">
              <div className="space-y-4">
                {filteredTemplates.length === 0 ? (
                  <div className="text-center py-12 text-gray-500">
                    <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-lg font-medium mb-2">No templates found</p>
                    <p className="text-sm">Try adjusting your search or category filter</p>
                  </div>
                ) : (
                  filteredTemplates.map((template) => (
                    <Card 
                      key={template.id} 
                      className="hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => onSelect(template)}
                    >
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-4">
                            <div className="flex-shrink-0">
                              {getRoleIcon(template)}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <h3 className="text-lg font-semibold">{template.name}</h3>
                                <Badge className={getCategoryColor(template.category)} variant="outline">
                                  {template.category}
                                </Badge>
                                {template.isBuiltIn && (
                                  <Badge variant="secondary">Built-in</Badge>
                                )}
                              </div>
                              <p className="text-gray-600 mb-3">{template.description}</p>
                              
                              {/* Capabilities Preview */}
                              <div className="space-y-2">
                                <h4 className="text-sm font-medium text-gray-700">Key Capabilities:</h4>
                                <div className="flex flex-wrap gap-1">
                                  {Object.entries(template.roleConfig.capabilities || {})
                                    .filter(([_, value]) => value)
                                    .slice(0, 5)
                                    .map(([key, _]) => (
                                      <span
                                        key={key}
                                        className="inline-block px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded"
                                      >
                                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                                      </span>
                                    ))}
                                </div>
                              </div>

                              {/* Tags */}
                              <div className="mt-3">
                                <div className="flex flex-wrap gap-1">
                                  {template.tags.map((tag) => (
                                    <span
                                      key={tag}
                                      className="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                                    >
                                      {tag}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                          <ArrowRight className="h-5 w-5 text-gray-400 flex-shrink-0 mt-1" />
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </ScrollArea>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
