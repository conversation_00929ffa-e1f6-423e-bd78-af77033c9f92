'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Copy, 
  Users,
  Shield,
  Settings,
  Crown,
  UserCheck,
  AlertTriangle
} from 'lucide-react'
import { Role, RoleTemplate } from '../../posts/types'
import { RoleEditor } from './role-editor'
import { RoleTemplateSelector } from './role-template-selector'
import { UserRoleManager } from './user-role-manager'
import { PermissionMatrix } from './permission-matrix'
import { toast } from 'sonner'

interface RoleManagerProps {
  onRoleSelect?: (role: Role) => void
}

export function RoleManager({ onRoleSelect }: RoleManagerProps) {
  const [roles, setRoles] = useState<Role[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [activeTab, setActiveTab] = useState<'roles' | 'users' | 'permissions' | 'templates'>('roles')
  const [isEditorOpen, setIsEditorOpen] = useState(false)
  const [isTemplateOpen, setIsTemplateOpen] = useState(false)
  const [isCreating, setIsCreating] = useState(false)

  useEffect(() => {
    fetchRoles()
  }, [])

  const fetchRoles = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/auth/roles')
      const result = await response.json()
      
      if (result.success) {
        setRoles(result.data || [])
      } else {
        toast.error('Failed to fetch roles')
      }
    } catch (error) {
      console.error('Error fetching roles:', error)
      toast.error('Failed to fetch roles')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateRole = () => {
    setSelectedRole(null)
    setIsCreating(true)
    setIsTemplateOpen(true)
  }

  const handleEditRole = (role: Role) => {
    setSelectedRole(role)
    setIsCreating(false)
    setIsEditorOpen(true)
    if (onRoleSelect) {
      onRoleSelect(role)
    }
  }

  const handleTemplateSelect = (template: RoleTemplate) => {
    setSelectedRole({
      id: '',
      ...template.roleConfig,
      permissions: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    } as Role)
    setIsTemplateOpen(false)
    setIsEditorOpen(true)
  }

  const handleSaveRole = async (roleData: Partial<Role>) => {
    try {
      const url = isCreating ? '/api/auth/roles' : `/api/auth/roles/${selectedRole?.id}`
      const method = isCreating ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(roleData)
      })

      const result = await response.json()

      if (result.success) {
        toast.success(isCreating ? 'Role created successfully' : 'Role updated successfully')
        setIsEditorOpen(false)
        fetchRoles()
      } else {
        toast.error(result.error || 'Failed to save role')
      }
    } catch (error) {
      console.error('Error saving role:', error)
      toast.error('Failed to save role')
    }
  }

  const handleDeleteRole = async (role: Role) => {
    if (role.isSystem) {
      toast.error('Cannot delete system role')
      return
    }

    if (!confirm(`Are you sure you want to delete "${role.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/auth/roles/${role.id}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Role deleted successfully')
        fetchRoles()
      } else {
        toast.error(result.error || 'Failed to delete role')
      }
    } catch (error) {
      console.error('Error deleting role:', error)
      toast.error('Failed to delete role')
    }
  }

  const handleDuplicateRole = async (role: Role) => {
    const duplicateData = {
      ...role,
      id: undefined,
      name: `${role.name} Copy`,
      slug: `${role.slug}-copy`,
      isSystem: false,
    }

    await handleSaveRole(duplicateData)
  }

  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    role.slug.toLowerCase().includes(searchQuery.toLowerCase()) ||
    role.description?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getRoleIcon = (role: Role) => {
    if (role.level === 1) return <Crown className="h-4 w-4" />
    if (role.level <= 2) return <Shield className="h-4 w-4" />
    if (role.level <= 4) return <UserCheck className="h-4 w-4" />
    return <Users className="h-4 w-4" />
  }

  const getRoleColor = (role: Role) => {
    if (role.level === 1) return 'bg-red-100 text-red-800 border-red-200'
    if (role.level <= 2) return 'bg-orange-100 text-orange-800 border-orange-200'
    if (role.level <= 4) return 'bg-blue-100 text-blue-800 border-blue-200'
    return 'bg-gray-100 text-gray-800 border-gray-200'
  }

  if (isTemplateOpen) {
    return (
      <RoleTemplateSelector
        onSelect={handleTemplateSelect}
        onCancel={() => setIsTemplateOpen(false)}
      />
    )
  }

  if (isEditorOpen && selectedRole) {
    return (
      <RoleEditor
        role={selectedRole}
        isCreating={isCreating}
        onSave={handleSaveRole}
        onCancel={() => setIsEditorOpen(false)}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Role & Permission Management</h1>
          <p className="text-gray-600">
            Manage user roles and permissions for your CMS
          </p>
        </div>
        <Button onClick={handleCreateRole}>
          <Plus className="h-4 w-4 mr-2" />
          Create Role
        </Button>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="roles">Roles</TabsTrigger>
          <TabsTrigger value="users">User Assignments</TabsTrigger>
          <TabsTrigger value="permissions">Permission Matrix</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="roles" className="space-y-4">
          {/* Search and Filters */}
          <div className="flex items-center space-x-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search roles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Shield className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Roles</p>
                    <p className="text-2xl font-bold">{roles.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Crown className="h-5 w-5 text-red-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">System Roles</p>
                    <p className="text-2xl font-bold">
                      {roles.filter(r => r.isSystem).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <UserCheck className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Roles</p>
                    <p className="text-2xl font-bold">
                      {roles.filter(r => r.isActive).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Settings className="h-5 w-5 text-purple-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Custom Roles</p>
                    <p className="text-2xl font-bold">
                      {roles.filter(r => !r.isSystem).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Roles Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {isLoading ? (
              // Loading skeletons
              Array.from({ length: 6 }).map((_, index) => (
                <Card key={index} className="animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="h-3 bg-gray-200 rounded"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : filteredRoles.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <Shield className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchQuery ? 'No roles found' : 'No roles yet'}
                </h3>
                <p className="text-gray-500 mb-4">
                  {searchQuery 
                    ? 'Try adjusting your search terms'
                    : 'Create your first role to get started'
                  }
                </p>
                {!searchQuery && (
                  <Button onClick={handleCreateRole}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Role
                  </Button>
                )}
              </div>
            ) : (
              filteredRoles.map((role) => (
                <Card key={role.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-2">
                        {getRoleIcon(role)}
                        <div>
                          <CardTitle className="text-lg">{role.name}</CardTitle>
                          <p className="text-sm text-gray-500 font-mono">{role.slug}</p>
                        </div>
                      </div>
                      <div className="flex flex-col items-end space-y-1">
                        {role.isSystem && (
                          <Badge variant="secondary">System</Badge>
                        )}
                        <Badge 
                          className={getRoleColor(role)}
                          variant="outline"
                        >
                          Level {role.level}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                      {role.description || 'No description provided'}
                    </p>

                    <div className="space-y-2 mb-4">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">Permissions:</span>
                        <span className="font-medium">{role.permissions?.length || 0}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">Status:</span>
                        <Badge variant={role.isActive ? "default" : "secondary"}>
                          {role.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditRole(role)}
                        className="flex-1"
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDuplicateRole(role)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      {!role.isSystem && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDeleteRole(role)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="users">
          <UserRoleManager roles={roles} />
        </TabsContent>

        <TabsContent value="permissions">
          <PermissionMatrix roles={roles} />
        </TabsContent>

        <TabsContent value="templates">
          <RoleTemplateSelector
            onSelect={handleTemplateSelect}
            showAsGrid={true}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
