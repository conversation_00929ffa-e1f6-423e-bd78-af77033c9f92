import React from 'react'
import Link from 'next/link'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Calendar, User, Eye, ArrowRight, Search } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface PostType {
  id: string
  name: string
  label: string
  labelPlural: string
  description?: string | null
  isPublic: boolean
  hasArchive: boolean
  taxonomies: string[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

interface Post {
  id: string
  title: string
  slug: string
  excerpt?: string | null
  status: string
  postType: string
  featuredImage?: string | null
  featuredImageAlt?: string | null
  publishedAt?: Date | null
  authorName?: string | null
  viewCount: number
  taxonomyTerms?: Array<{
    id: string
    postId: string
    termId: string
    createdAt: Date
    term: {
      id: string
      name: string
      slug: string
      description?: string | null
      parentId?: string | null
      taxonomyId: string
      metadata?: any
      createdAt: Date
      updatedAt: Date
      taxonomy: {
        id: string
        name: string
        label: string
        labelPlural: string
        description?: string | null
        isHierarchical: boolean
        isPublic: boolean
        capabilities?: any
        isSystem: boolean
        isActive: boolean
        createdAt: Date
        updatedAt: Date
      }
    }
  }>
}

interface ArchiveRendererProps {
  postType: PostType
  posts: (Post & { taxonomyTerms?: any[] })[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  searchParams?: { [key: string]: string | string[] | undefined }
  pathname: string
}

export function ArchiveRenderer({
  postType,
  posts,
  pagination,
  searchParams,
  pathname
}: ArchiveRendererProps) {
  const currentSearch = (searchParams?.search as string) || ''
  const currentSort = (searchParams?.sort as string) || 'newest'
  const currentCategory = (searchParams?.category as string) || 'all'

  // Determine layout based on post type
  const layout = getLayoutForPostType(postType.name)

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold mb-4">
          {postType.labelPlural}
        </h1>
        {postType.description && (
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            {postType.description}
          </p>
        )}
        
        {/* Stats */}
        <div className="mt-6 text-sm text-muted-foreground">
          Showing {posts.length} of {pagination.total} {postType.labelPlural.toLowerCase()}
        </div>
      </div>

      {/* Filters and Search */}
      <div className="mb-8">
        <ArchiveFilters
          postType={postType}
          currentSearch={currentSearch}
          currentSort={currentSort}
          currentCategory={currentCategory}
        />
      </div>

      {/* Content */}
      {posts.length === 0 ? (
        <EmptyState postType={postType} />
      ) : (
        <>
          {/* Posts Grid/List */}
          <div className={getGridClasses(layout)}>
            {posts.map((post) => (
              <PostCard 
                key={post.id}
                post={post}
                postType={postType}
                layout={layout}
              />
            ))}
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="mt-12">
              <Pagination 
                pagination={pagination}
                pathname={pathname}
                searchParams={searchParams}
              />
            </div>
          )}
        </>
      )}
    </div>
  )
}

// Archive Filters Component (Client Component for interactivity)
function ArchiveFilters({
  postType,
  currentSearch,
  currentSort,
  currentCategory
}: {
  postType: PostType
  currentSearch: string
  currentSort: string
  currentCategory: string
}) {
  return (
    <div className="bg-gray-50 rounded-lg p-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder={`Search ${postType.labelPlural.toLowerCase()}...`}
            defaultValue={currentSearch}
            className="pl-10"
          />
        </div>

        {/* Sort */}
        <Select defaultValue={currentSort}>
          <SelectTrigger>
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="newest">Newest First</SelectItem>
            <SelectItem value="oldest">Oldest First</SelectItem>
            <SelectItem value="title">Title A-Z</SelectItem>
            <SelectItem value="title-desc">Title Z-A</SelectItem>
            <SelectItem value="popular">Most Popular</SelectItem>
          </SelectContent>
        </Select>

        {/* Category Filter (if applicable) */}
        {postType.taxonomies.includes('category') && (
          <Select defaultValue={currentCategory}>
            <SelectTrigger>
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {/* TODO: Load actual categories */}
              <SelectItem value="general">General</SelectItem>
              <SelectItem value="featured">Featured</SelectItem>
            </SelectContent>
          </Select>
        )}
      </div>
    </div>
  )
}

// Post Card Component
function PostCard({ 
  post, 
  postType, 
  layout 
}: { 
  post: Post & { taxonomyTerms?: any[] }
  postType: PostType
  layout: 'grid' | 'list' | 'masonry' | 'portfolio'
}) {
  const postUrl = postType.name === 'post' ? `/${post.slug}` : `/${postType.name}/${post.slug}`
  
  // Get categories
  const categories = post.taxonomyTerms?.filter(
    term => term.term?.taxonomy?.name === 'category'
  ) || []

  if (layout === 'list') {
    return (
      <Card className="mb-6">
        <div className="flex">
          {post.featuredImage && (
            <div className="w-48 h-32 flex-shrink-0">
              <img
                src={post.featuredImage}
                alt={post.featuredImageAlt || post.title}
                className="w-full h-full object-cover rounded-l-lg"
              />
            </div>
          )}
          <div className="flex-1 p-6">
            <div className="flex justify-between items-start mb-2">
              <h3 className="text-xl font-semibold">
                <Link href={postUrl} className="hover:text-blue-600 transition-colors">
                  {post.title}
                </Link>
              </h3>
              <div className="text-sm text-muted-foreground">
                {formatDistanceToNow(new Date(post.publishedAt!), { addSuffix: true })}
              </div>
            </div>
            
            {post.excerpt && (
              <p className="text-muted-foreground mb-4 line-clamp-2">
                {post.excerpt}
              </p>
            )}
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                {post.authorName && (
                  <div className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    <span>{post.authorName}</span>
                  </div>
                )}
                <div className="flex items-center gap-1">
                  <Eye className="h-4 w-4" />
                  <span>{post.viewCount} views</span>
                </div>
              </div>
              
              <Button variant="ghost" size="sm" asChild>
                <Link href={postUrl}>
                  Read More <ArrowRight className="h-4 w-4 ml-1" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </Card>
    )
  }

  // Grid/Portfolio layout
  return (
    <Card className="group hover:shadow-lg transition-shadow duration-200">
      {post.featuredImage && (
        <div className="aspect-video overflow-hidden rounded-t-lg">
          <img
            src={post.featuredImage}
            alt={post.featuredImageAlt || post.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
          />
        </div>
      )}
      
      <CardHeader>
        <div className="flex items-start justify-between mb-2">
          <CardTitle className="text-lg line-clamp-2">
            <Link href={postUrl} className="hover:text-blue-600 transition-colors">
              {post.title}
            </Link>
          </CardTitle>
        </div>
        
        {/* Categories */}
        {categories.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-2">
            {categories.slice(0, 2).map((category: any) => (
              <Badge key={category.id} variant="secondary" className="text-xs">
                {category.term.name}
              </Badge>
            ))}
          </div>
        )}
      </CardHeader>
      
      <CardContent>
        {post.excerpt && (
          <p className="text-muted-foreground text-sm mb-4 line-clamp-3">
            {post.excerpt}
          </p>
        )}
        
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-2">
            <Calendar className="h-3 w-3" />
            <span>{formatDistanceToNow(new Date(post.publishedAt!), { addSuffix: true })}</span>
          </div>
          <div className="flex items-center gap-1">
            <Eye className="h-3 w-3" />
            <span>{post.viewCount}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Pagination Component
function Pagination({ 
  pagination, 
  pathname, 
  searchParams 
}: {
  pagination: { page: number; totalPages: number }
  pathname: string
  searchParams?: { [key: string]: string | string[] | undefined }
}) {
  const createPageUrl = (page: number) => {
    const url = new URL(pathname, 'http://localhost')
    Object.entries(searchParams || {}).forEach(([key, value]) => {
      if (value && key !== 'page') {
        url.searchParams.set(key, value.toString())
      }
    })
    if (page > 1) {
      url.searchParams.set('page', page.toString())
    }
    return url.pathname + url.search
  }

  return (
    <div className="flex justify-center items-center gap-2">
      {pagination.page > 1 && (
        <Button variant="outline" asChild>
          <Link href={createPageUrl(pagination.page - 1)}>
            Previous
          </Link>
        </Button>
      )}
      
      <span className="px-4 py-2 text-sm text-muted-foreground">
        Page {pagination.page} of {pagination.totalPages}
      </span>
      
      {pagination.page < pagination.totalPages && (
        <Button variant="outline" asChild>
          <Link href={createPageUrl(pagination.page + 1)}>
            Next
          </Link>
        </Button>
      )}
    </div>
  )
}

// Empty State Component
function EmptyState({ postType }: { postType: PostType }) {
  return (
    <div className="text-center py-12">
      <div className="text-6xl mb-4">📝</div>
      <h3 className="text-xl font-semibold mb-2">
        No {postType.labelPlural.toLowerCase()} found
      </h3>
      <p className="text-muted-foreground mb-6">
        There are no {postType.labelPlural.toLowerCase()} to display at the moment.
      </p>
      <Button variant="outline" asChild>
        <Link href="/">
          Back to Home
        </Link>
      </Button>
    </div>
  )
}

// Helper Functions
function getLayoutForPostType(postTypeName: string): 'grid' | 'list' | 'masonry' | 'portfolio' {
  switch (postTypeName) {
    case 'portfolio':
      return 'portfolio'
    case 'post':
      return 'list'
    case 'product':
      return 'grid'
    default:
      return 'grid'
  }
}

function getGridClasses(layout: 'grid' | 'list' | 'masonry' | 'portfolio'): string {
  switch (layout) {
    case 'list':
      return 'space-y-6'
    case 'portfolio':
      return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
    case 'grid':
      return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
    default:
      return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
  }
}
