// Dynamic Page Renderer for CMS Builder Integration
// Next.js 15 App Router compatible dynamic page rendering system

'use client'

import React, { Suspense, useMemo } from 'react'
import { notFound } from 'next/navigation'
import { CMSContent, PostType } from '@/lib/cms/types'
import { CMSBlock } from '@/lib/core/builders/types'
import { BlockRenderer } from '@/lib/page-builder/components/block-renderer'
import { LayoutRenderer } from '@/lib/layout-builder/components/layout-renderer'
import { PageErrorBoundary } from '@/lib/page-builder/components/page-error-boundary'
import { SEOHead } from './seo-head'
import { PerformanceMonitor } from './performance-monitor'
import { generateId } from '@/lib/page-builder/utils'

interface DynamicPageRendererProps {
  content: CMSContent
  postType: PostType
  layout?: any
  isPreview?: boolean
  className?: string
}

export function DynamicPageRenderer({
  content,
  postType,
  layout,
  isPreview = false,
  className
}: DynamicPageRendererProps) {
  // Convert CMS blocks to page builder blocks
  const pageBlocks = useMemo(() => {
    if (!content.blocks || content.blocks.length === 0) {
      return []
    }

    return content.blocks.map((block: CMSBlock, index: number) => ({
      id: block.id || generateId(),
      type: block.type,
      content: block.content || {},
      config: block.config || {},
      position: index,
      isVisible: block.responsiveSettings?.desktop?.visible !== false,
      animation: block.config?.animation || 'none',
      customCss: block.config?.customCss || '',
      customJs: block.config?.customJs || ''
    }))
  }, [content.blocks])

  // Create page data structure
  const pageData = useMemo(() => ({
    id: content.id,
    title: content.title,
    slug: content.slug,
    content: content.content || '',
    excerpt: content.excerpt || '',
    status: content.status,
    type: content.postType,
    blocks: pageBlocks,
    settings: {
      customCss: content.metadata?.customCss || '',
      customJs: content.metadata?.customJs || '',
      seoTitle: content.metadata?.seoTitle || content.title,
      seoDescription: content.metadata?.seoDescription || content.excerpt,
      socialImage: content.featuredImage || '',
      noIndex: content.metadata?.noIndex || false,
      noFollow: content.metadata?.noFollow || false
    },
    createdAt: content.createdAt,
    updatedAt: content.updatedAt,
    publishedAt: content.publishedAt
  }), [content, pageBlocks])

  // Handle empty content
  if (!content || !content.id) {
    if (isPreview) {
      return <EmptyPagePreview postType={postType} />
    }
    notFound()
  }

  // Handle draft content in non-preview mode
  if (content.status === 'draft' && !isPreview) {
    notFound()
  }

  return (
    <PageErrorBoundary>
      <PerformanceMonitor pageId={content.id} isPreview={isPreview}>
        <div className={className}>
          {/* SEO Head */}
          <SEOHead
            title={pageData.settings.seoTitle}
            description={pageData.settings.seoDescription}
            image={pageData.settings.socialImage}
            url={`/${content.slug}`}
            type={postType.name}
            noIndex={pageData.settings.noIndex}
            noFollow={pageData.settings.noFollow}
            publishedAt={content.publishedAt}
            modifiedAt={content.updatedAt}
          />

          {/* Preview Banner */}
          {isPreview && <PreviewBanner content={content} />}

          {/* Custom CSS */}
          {pageData.settings.customCss && (
            <style dangerouslySetInnerHTML={{ __html: pageData.settings.customCss }} />
          )}

          {/* Render with layout if available */}
          {layout ? (
            <LayoutRenderer layout={layout} showDefaultHeader={false}>
              <DynamicContentRenderer pageData={pageData} postType={postType} />
            </LayoutRenderer>
          ) : (
            <DynamicContentRenderer pageData={pageData} postType={postType} />
          )}

          {/* Custom JavaScript */}
          {pageData.settings.customJs && (
            <script dangerouslySetInnerHTML={{ __html: pageData.settings.customJs }} />
          )}
        </div>
      </PerformanceMonitor>
    </PageErrorBoundary>
  )
}

// Content renderer component
function DynamicContentRenderer({ 
  pageData, 
  postType 
}: { 
  pageData: any
  postType: PostType 
}) {
  const sortedBlocks = useMemo(() => {
    return [...pageData.blocks].sort((a, b) => a.position - b.position)
  }, [pageData.blocks])

  const visibleBlocks = useMemo(() => {
    return sortedBlocks.filter(block => block.isVisible)
  }, [sortedBlocks])

  if (visibleBlocks.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {pageData.title}
          </h1>
          {pageData.content && (
            <div 
              className="text-gray-600 prose prose-lg mx-auto"
              dangerouslySetInnerHTML={{ __html: pageData.content }}
            />
          )}
          {!pageData.content && (
            <p className="text-gray-500">
              This {postType.label.toLowerCase()} has no content to display.
            </p>
          )}
        </div>
      </div>
    )
  }

  return (
    <Suspense fallback={<ContentLoadingSkeleton />}>
      <div className="dynamic-content">
        {visibleBlocks.map((block, index) => (
          <Suspense 
            key={block.id} 
            fallback={<BlockLoadingSkeleton blockType={block.type} />}
          >
            <BlockRenderer
              block={block}
              index={index}
              isEditing={false}
            />
          </Suspense>
        ))}
      </div>
    </Suspense>
  )
}

// Preview banner component
function PreviewBanner({ content }: { content: CMSContent }) {
  return (
    <div className="bg-blue-600 text-white px-4 py-2 text-sm text-center sticky top-0 z-50">
      <div className="flex items-center justify-center space-x-4">
        <span>🔍 Preview Mode</span>
        <span>•</span>
        <span>{content.title}</span>
        <span>•</span>
        <span className="capitalize">{content.status}</span>
        {content.updatedAt && (
          <>
            <span>•</span>
            <span>Updated {new Date(content.updatedAt).toLocaleDateString()}</span>
          </>
        )}
      </div>
    </div>
  )
}

// Empty page preview component
function EmptyPagePreview({ postType }: { postType: PostType }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center max-w-md mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border p-8">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">📄</span>
          </div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            New {postType.label}
          </h1>
          <p className="text-gray-600 mb-4">
            This {postType.label.toLowerCase()} is empty. Start building by adding blocks in the editor.
          </p>
          <div className="text-sm text-gray-500">
            Preview Mode - Content not published
          </div>
        </div>
      </div>
    </div>
  )
}

// Loading skeletons
function ContentLoadingSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="h-64 bg-gray-200 rounded mb-8"></div>
      <div className="space-y-4">
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
      </div>
    </div>
  )
}

function BlockLoadingSkeleton({ blockType }: { blockType: string }) {
  const getSkeletonHeight = (type: string) => {
    switch (type) {
      case 'hero': return 'h-96'
      case 'image': return 'h-64'
      case 'text': return 'h-32'
      case 'button': return 'h-12'
      default: return 'h-48'
    }
  }

  return (
    <div className={`animate-pulse bg-gray-200 rounded mb-4 ${getSkeletonHeight(blockType)}`}>
      <div className="p-4">
        <div className="text-xs text-gray-400 mb-2">{blockType} block loading...</div>
      </div>
    </div>
  )
}

// Static page renderer for SSG
export function StaticDynamicPageRenderer(props: DynamicPageRendererProps) {
  return <DynamicPageRenderer {...props} />
}

// Server component wrapper for App Router
export async function ServerDynamicPageRenderer({
  slug,
  preview = false,
  previewToken
}: {
  slug: string
  preview?: boolean
  previewToken?: string
}) {
  // This would be implemented to fetch data server-side
  // For now, we'll return a client component
  return (
    <div>
      <p>Server-side rendering for slug: {slug}</p>
      {preview && <p>Preview mode with token: {previewToken}</p>}
    </div>
  )
}

export default DynamicPageRenderer
