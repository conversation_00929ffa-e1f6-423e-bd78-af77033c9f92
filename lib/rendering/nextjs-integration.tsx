// Next.js 15 App Router Integration for CMS Rendering
// Provides utilities for dynamic routing, metadata generation, and static generation

import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { RouteResolver } from '@/lib/routing/route-resolver'
import { PostTypeService } from '@/lib/cms/services/post-type-service'
import { ContentService } from '@/lib/cms/services/content-service'
import { generateId } from '@/lib/page-builder/utils'

// Types for Next.js integration
export interface DynamicRouteParams {
  slug: string[]
}

export interface DynamicSearchParams {
  [key: string]: string | string[] | undefined
}

export interface StaticParamsResult {
  slug: string[]
}

// Generate metadata for dynamic pages
export async function generateDynamicMetadata(
  params: DynamicRouteParams,
  searchParams?: DynamicSearchParams
): Promise<Metadata> {
  try {
    const pathname = '/' + params.slug.join('/')
    const resolution = await RouteResolver.resolve(pathname)

    if (resolution.type === 'notfound') {
      return {
        title: 'Page Not Found',
        description: 'The page you are looking for could not be found.',
        robots: { index: false, follow: false }
      }
    }

    switch (resolution.type) {
      case 'page':
        return await generatePageMetadata(resolution.data.id, pathname)
      
      case 'post':
        return await generatePostMetadata(resolution.data.id, pathname)
      
      case 'archive':
        return await generateArchiveMetadata(resolution.data, pathname)
      
      default:
        return getDefaultMetadata()
    }
  } catch (error) {
    console.error('Error generating metadata:', error)
    return getDefaultMetadata()
  }
}

// Generate metadata for pages
async function generatePageMetadata(pageId: string, pathname: string): Promise<Metadata> {
  const pageData = await RouteResolver.getPageData(pageId)
  
  if (!pageData) {
    return { title: 'Page Not Found' }
  }

  return {
    title: pageData.seoTitle || pageData.title,
    description: pageData.seoDescription || pageData.description || undefined,
    keywords: pageData.seoKeywords?.join(', ') || undefined,
    openGraph: {
      title: pageData.seoTitle || pageData.title,
      description: pageData.seoDescription || pageData.description || undefined,
      type: 'website',
      url: pathname,
      siteName: process.env.NEXT_PUBLIC_SITE_NAME || 'CMS Site',
      locale: 'en_ZA'
    },
    twitter: {
      card: 'summary_large_image',
      title: pageData.seoTitle || pageData.title,
      description: pageData.seoDescription || pageData.description || undefined
    },
    robots: {
      index: pageData.status === 'published',
      follow: pageData.status === 'published'
    },
    alternates: {
      canonical: pathname
    }
  }
}

// Generate metadata for posts
async function generatePostMetadata(postId: string, pathname: string): Promise<Metadata> {
  const postData = await RouteResolver.getPostData(postId)
  
  if (!postData) {
    return { title: 'Post Not Found' }
  }

  return {
    title: postData.seoTitle || postData.title,
    description: postData.seoDescription || postData.excerpt || undefined,
    keywords: postData.seoKeywords?.join(', ') || undefined,
    authors: postData.authorName ? [{ name: postData.authorName }] : undefined,
    openGraph: {
      title: postData.seoTitle || postData.title,
      description: postData.seoDescription || postData.excerpt || undefined,
      type: 'article',
      url: pathname,
      siteName: process.env.NEXT_PUBLIC_SITE_NAME || 'CMS Site',
      locale: 'en_ZA',
      publishedTime: postData.publishedAt?.toISOString(),
      modifiedTime: postData.updatedAt?.toISOString(),
      authors: postData.authorName ? [postData.authorName] : undefined,
      images: postData.featuredImage ? [{ url: postData.featuredImage }] : undefined
    },
    twitter: {
      card: 'summary_large_image',
      title: postData.seoTitle || postData.title,
      description: postData.seoDescription || postData.excerpt || undefined,
      images: postData.featuredImage ? [postData.featuredImage] : undefined
    },
    robots: {
      index: postData.status === 'published',
      follow: postData.status === 'published'
    },
    alternates: {
      canonical: postData.canonicalUrl || pathname
    }
  }
}

// Generate metadata for archives
async function generateArchiveMetadata(postTypeData: any, pathname: string): Promise<Metadata> {
  return {
    title: `${postTypeData.labelPlural} - ${process.env.NEXT_PUBLIC_SITE_NAME}`,
    description: postTypeData.description || `Browse all ${postTypeData.labelPlural.toLowerCase()}`,
    openGraph: {
      title: `${postTypeData.labelPlural} Archive`,
      description: postTypeData.description || `Browse all ${postTypeData.labelPlural.toLowerCase()}`,
      type: 'website',
      url: pathname,
      siteName: process.env.NEXT_PUBLIC_SITE_NAME || 'CMS Site',
      locale: 'en_ZA'
    },
    robots: {
      index: true,
      follow: true
    },
    alternates: {
      canonical: pathname
    }
  }
}

// Default metadata fallback
function getDefaultMetadata(): Metadata {
  return {
    title: process.env.NEXT_PUBLIC_SITE_NAME || 'CMS Site',
    description: 'A modern CMS-powered website',
    robots: { index: true, follow: true }
  }
}

// Generate static params for known routes
export async function generateStaticParams(): Promise<StaticParamsResult[]> {
  try {
    const params: StaticParamsResult[] = []

    // Get all published pages
    const pages = await ContentService.getContent({
      postType: 'page',
      status: 'published',
      limit: 1000
    })

    pages.content.forEach((page: any) => {
      if (page.slug && page.slug !== 'home') {
        params.push({
          slug: page.slug.split('/').filter(Boolean)
        })
      }
    })

    // Get all published posts from all post types
    const postTypes = await PostTypeService.getPostTypes({ isActive: true, isPublic: true })
    
    for (const postType of postTypes) {
      if (postType.name === 'page') continue // Skip pages, already handled

      const posts = await ContentService.getContent({
        postType: postType.name,
        status: 'published',
        limit: 500 // Limit for build performance
      })

      posts.content.forEach((post: any) => {
        if (post.slug) {
          // For posts, include post type in URL if not default 'post'
          const slugParts = postType.name === 'post' 
            ? [post.slug]
            : [postType.name, post.slug]
          
          params.push({
            slug: slugParts
          })
        }
      })

      // Add archive pages for post types with archives
      if (postType.hasArchive) {
        params.push({
          slug: [postType.name]
        })
      }
    }

    return params.slice(0, 1000) // Limit total for build performance
  } catch (error) {
    console.error('Error generating static params:', error)
    return []
  }
}

// Revalidate function for ISR
export async function revalidatePage(slug: string): Promise<boolean> {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/revalidate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.REVALIDATE_TOKEN}`
      },
      body: JSON.stringify({ path: `/${slug}` })
    })

    return response.ok
  } catch (error) {
    console.error('Error revalidating page:', error)
    return false
  }
}

// Generate sitemap data
export async function generateSitemapData() {
  try {
    const urls: Array<{
      url: string
      lastModified: Date
      changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
      priority: number
    }> = []

    // Add homepage
    urls.push({
      url: '/',
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1.0
    })

    // Add pages
    const pages = await ContentService.getContent({
      postType: 'page',
      status: 'published',
      limit: 1000
    })

    pages.content.forEach((page: any) => {
      if (page.slug && page.slug !== 'home') {
        urls.push({
          url: `/${page.slug}`,
          lastModified: new Date(page.updatedAt),
          changeFrequency: 'weekly',
          priority: 0.8
        })
      }
    })

    // Add posts from all post types
    const postTypes = await PostTypeService.getPostTypes({ isActive: true, isPublic: true })
    
    for (const postType of postTypes) {
      if (postType.name === 'page') continue

      const posts = await ContentService.getContent({
        postType: postType.name,
        status: 'published',
        limit: 1000
      })

      posts.content.forEach((post: any) => {
        if (post.slug) {
          const url = postType.name === 'post' 
            ? `/${post.slug}`
            : `/${postType.name}/${post.slug}`
          
          urls.push({
            url,
            lastModified: new Date(post.updatedAt),
            changeFrequency: 'monthly',
            priority: 0.6
          })
        }
      })

      // Add archive pages
      if (postType.hasArchive) {
        urls.push({
          url: `/${postType.name}`,
          lastModified: new Date(),
          changeFrequency: 'daily',
          priority: 0.7
        })
      }
    }

    return urls
  } catch (error) {
    console.error('Error generating sitemap data:', error)
    return []
  }
}

// Middleware helper for route matching
export function shouldHandleRoute(pathname: string): boolean {
  // Skip known static routes
  const skipPatterns = [
    /^\/api\//,
    /^\/admin\//,
    /^\/preview\//,
    /^\/blog\//,
    /^\/products\//,
    /^\/collections\//,
    /^\/cart/,
    /^\/checkout/,
    /^\/account/,
    /^\/wishlist/,
    /^\/compare/,
    /^\/search/,
    /^\/contact/,
    /^\/about/,
    /^\/faq/,
    /^\/help/,
    /^\/terms/,
    /^\/privacy/,
    /^\/shipping/,
    /^\/stores/,
    /^\/newsletter/,
    /^\/brand/,
    /^\/orders/,
    /^\/frontend\//,
    /^\/dynamic-page\//,
    /^\/maintenance$/,
    /^\/_next\//,
    /\.(png|jpg|jpeg|gif|svg|css|js|woff|woff2|ttf|eot|ico|webp|mp4|webm|ogg|mp3|wav|flac|aac|opus|pdf|zip|tar|gz|rar|7z|doc|docx|xls|xlsx|ppt|pptx)$/
  ]

  return !skipPatterns.some(pattern => pattern.test(pathname))
}

// Cache utilities for performance
export const CacheUtils = {
  // Generate cache key for content
  generateCacheKey: (type: string, id: string, version?: string) => {
    return `cms:${type}:${id}:${version || 'latest'}`
  },

  // Get cache headers for different content types
  getCacheHeaders: (contentType: 'page' | 'post' | 'archive', isPreview = false) => {
    if (isPreview) {
      return {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    }

    switch (contentType) {
      case 'page':
        return {
          'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400'
        }
      case 'post':
        return {
          'Cache-Control': 'public, s-maxage=1800, stale-while-revalidate=3600'
        }
      case 'archive':
        return {
          'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600'
        }
      default:
        return {
          'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600'
        }
    }
  }
}

export default {
  generateDynamicMetadata,
  generateStaticParams,
  generateSitemapData,
  revalidatePage,
  shouldHandleRoute,
  CacheUtils
}
