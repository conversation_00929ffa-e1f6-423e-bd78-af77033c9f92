import React, { Suspense } from 'react'
import { notFound } from 'next/navigation'
import { RouteResolver } from '@/lib/routing/route-resolver'
import { LayoutResolver } from '@/lib/layout-builder/layout-resolver'
import { LayoutRenderer } from '@/lib/layout-builder/components/layout-renderer'
import { <PERSON><PERSON><PERSON><PERSON>, StaticPageRenderer } from '@/lib/page-builder/components/page-renderer'
import { BlogPost } from '@/lib/posts/components/blog-post'
import { PostTypeRenderer } from './post-type-renderer'
import { ArchiveRenderer } from './archive-renderer'
import { PageData } from '@/lib/page-builder/types'
import { Post } from '@/lib/posts/types'
import { C<PERSON><PERSON><PERSON><PERSON>, ArchiveRenderer as CMSArchiveRenderer } from '@/lib/cms/rendering/cms-renderer'
import { ContentService } from '@/lib/cms/services/content-service'
import { PostTypeService } from '@/lib/cms/services/post-type-service'
import { HookSystem, CMS_HOOKS } from '@/lib/cms/plugins/hook-system'


interface UniversalRendererProps {
  pathname: string
  searchParams?: { [key: string]: string | string[] | undefined }
}

export async function UniversalRenderer({ pathname, searchParams }: UniversalRendererProps) {
  try {
    // Apply before render hook
    await HookSystem.doAction(CMS_HOOKS.BEFORE_RENDER, { pathname, searchParams })

    // Resolve the route to determine what to render
    const resolution = await RouteResolver.resolve(pathname)

    if (resolution.type === 'notfound') {
      notFound()
    }

    // Apply template hierarchy filter if available
    if (resolution.template) {
      const filteredHierarchy = await HookSystem.applyFilters(
        CMS_HOOKS.TEMPLATE_HIERARCHY,
        [resolution.template]
      )
      resolution.template = filteredHierarchy[0] || resolution.template
    }

    // Execute template resolved hook
    await HookSystem.doAction(CMS_HOOKS.TEMPLATE_RESOLVED, resolution)

    // Resolve the layout for this route
    const layout = await LayoutResolver.resolve(resolution)

    // Render content based on resolution type
    const content = await renderContent(resolution, searchParams, pathname)

    // Apply after render hook
    await HookSystem.doAction(CMS_HOOKS.AFTER_RENDER, {
      pathname,
      searchParams,
      resolution,
      content
    })

    // If layout is available, wrap content with layout
    if (layout) {
      // Check if layout has a custom header configured
      const hasCustomHeader = layout?.layout?.structure?.header &&
                             layout.layout.structure.header.isVisible &&
                             layout.layout.structure.header.blocks.length > 0

      return (
        <LayoutRenderer layout={layout} showDefaultHeader={!hasCustomHeader}>
          {content}
        </LayoutRenderer>
      )
    }

    // Fallback to content without layout - show default header
    return (
      <>
        {content}
      </>
    )
  } catch (error) {
    console.error('Universal renderer error:', error)
    notFound()
  }
}

// Separate content rendering function
async function renderContent(
  resolution: any,
  searchParams?: { [key: string]: string | string[] | undefined },
  pathname?: string
): Promise<React.ReactNode> {
  // Handle different content types
  switch (resolution.type) {
    case 'page':
      return (
        <Suspense fallback={<PageLoadingSkeleton />}>
          <PageContentRenderer pageData={resolution.data} />
        </Suspense>
      )

    case 'post':
      return (
        <Suspense fallback={<PostLoadingSkeleton />}>
          <PostContentRenderer postData={resolution.data} />
        </Suspense>
      )

    case 'archive':
      return (
        <Suspense fallback={<ArchiveLoadingSkeleton />}>
          <ArchiveContentRenderer
            postTypeData={resolution.data}
            pathname={pathname || ''}
            searchParams={searchParams}
          />
        </Suspense>
      )

    default:
      notFound()
  }
}

// Page Content Renderer
async function PageContentRenderer({
  pageData
}: {
  pageData: any
}) {
  const fullPageData = await RouteResolver.getPageData(pageData.id)

  if (!fullPageData) {
    notFound()
  }

  // Check authentication if required
  if (fullPageData.requiresAuth) {
    // TODO: Implement authentication check
    // For now, we'll allow access
  }

  // Transform database data to PageData format for PageRenderer
  const pageDataForRenderer: PageData = {
    id: fullPageData.id,
    title: fullPageData.title,
    slug: fullPageData.slug,
    description: fullPageData.description || undefined,
    status: fullPageData.status as 'draft' | 'published' | 'archived',
    type: fullPageData.type as 'custom' | 'product' | 'category' | 'home' | 'landing',
    template: fullPageData.template || undefined,
    publishedAt: fullPageData.publishedAt || undefined,
    createdAt: fullPageData.createdAt,
    updatedAt: fullPageData.updatedAt,
    blocks: fullPageData.blocks.map((block: any) => ({
      id: block.id,
      type: block.blockType,
      position: block.position,
      isVisible: block.isVisible,
      configuration: block.configuration || {},
      content: block.content || {},
      styling: block.styling || {},
      responsive: block.responsive || {},
      animation: block.animation || {},
      conditions: block.conditions || {},
    })),
    settings: {
      title: fullPageData.title,
      description: fullPageData.description || undefined,
      seoTitle: fullPageData.seoTitle || undefined,
      seoDescription: fullPageData.seoDescription || undefined,
      seoKeywords: fullPageData.seoKeywords || [],
      ogImage: fullPageData.ogImage || undefined,
      customCss: fullPageData.customCss || undefined,
      customJs: fullPageData.customJs || undefined,
      requiresAuth: fullPageData.requiresAuth || false,
      allowComments: fullPageData.allowComments || false,
    }
  }

  // Use the static PageRenderer component for frontend rendering
  return (
    <div className="min-h-screen">
      <StaticPageRenderer page={pageDataForRenderer} />
    </div>
  )
}

// Post Content Renderer
async function PostContentRenderer({
  postData
}: {
  postData: any
}) {
  const fullPostData = await RouteResolver.getPostData(postData.id)

  if (!fullPostData) {
    notFound()
  }

  // Check if post uses page builder
  if (fullPostData.usePageBuilder && fullPostData.pageBuilderData) {
    // Render using page builder
    const pageBuilderConfig = fullPostData.pageBuilderData as PageData

    return (
      <div className="min-h-screen">
        <PageRenderer page={pageBuilderConfig} />
      </div>
    )
  }

  // Convert ResolvedPost to Post type for BlogPost component
  const postForBlogComponent = {
    ...fullPostData,
    content: fullPostData.content || undefined,
    contentHtml: fullPostData.contentHtml || undefined,
    excerpt: fullPostData.excerpt || undefined,
    featuredImage: fullPostData.featuredImage || undefined,
    featuredImageAlt: fullPostData.featuredImageAlt || undefined,
    template: fullPostData.template || undefined,
    password: fullPostData.password || undefined,
    publishedAt: fullPostData.publishedAt || undefined,
    scheduledAt: fullPostData.scheduledAt || undefined,
    authorId: fullPostData.authorId || undefined,
    authorName: fullPostData.authorName || undefined,
    authorEmail: fullPostData.authorEmail || undefined,
    seoTitle: fullPostData.seoTitle || undefined,
    seoDescription: fullPostData.seoDescription || undefined,
    ogImage: fullPostData.ogImage || undefined,
    ogTitle: fullPostData.ogTitle || undefined,
    ogDescription: fullPostData.ogDescription || undefined,
    twitterCard: fullPostData.twitterCard || undefined,
    canonicalUrl: fullPostData.canonicalUrl || undefined,
    metaRobots: fullPostData.metaRobots || undefined,
    customFields: fullPostData.customFields || undefined,
    metadata: fullPostData.metadata || undefined,
    parentId: fullPostData.parentId || undefined,
    status: fullPostData.status as 'draft' | 'published' | 'private' | 'trash' | 'scheduled',
    // Transform taxonomy terms to match expected structure
    taxonomyTerms: fullPostData.taxonomyTerms?.map(termRelation => ({
      ...termRelation,
      term: {
        ...termRelation.term,
        count: 0, // Default count since it's not available in the resolved data
        color: termRelation.term.metadata?.color || undefined,
        image: termRelation.term.metadata?.image || undefined
      }
    }))
  }

  // Determine which post type renderer to use
  const postType = fullPostData.postType || 'post'

  switch (postType) {
    case 'post':
      return (
        <div className="container mx-auto px-4 py-8">
          <BlogPost
            post={postForBlogComponent as Post}
            showFullContent={true}
            showMeta={true}
            showActions={true}
            showComments={fullPostData.allowComments}
          />
        </div>
      )

    default:
      return (
        <PostTypeRenderer
          post={fullPostData}
          postType={postType}
        />
      )
  }
}

// Archive Content Renderer
async function ArchiveContentRenderer({
  postTypeData,
  searchParams,
  pathname
}: {
  postTypeData: any
  searchParams?: { [key: string]: string | string[] | undefined }
  pathname: string
}) {
  const page = parseInt((searchParams?.page as string) || '1')
  const limit = parseInt((searchParams?.limit as string) || '10')

  const archiveData = await RouteResolver.getArchiveData(postTypeData.name, page, limit)
  
  if (!archiveData) {
    notFound()
  }

  return (
    <ArchiveRenderer 
      postType={archiveData.postType}
      posts={archiveData.posts}
      pagination={archiveData.pagination}
      searchParams={searchParams}
      pathname={pathname}
    />
  )
}

// Loading Skeletons
function PageLoadingSkeleton() {
  return (
    <div className="min-h-screen animate-pulse">
      <div className="container mx-auto px-4 py-8">
        <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-8"></div>
        <div className="space-y-4">
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="h-24 bg-gray-200 rounded"></div>
          <div className="h-40 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>
  )
}

function PostLoadingSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8 animate-pulse">
      <div className="max-w-4xl mx-auto">
        <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
        <div className="space-y-4">
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          <div className="h-4 bg-gray-200 rounded w-4/5"></div>
          <div className="h-32 bg-gray-200 rounded my-8"></div>
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    </div>
  )
}

function ArchiveLoadingSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8 animate-pulse">
      <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="space-y-4">
            <div className="h-48 bg-gray-200 rounded"></div>
            <div className="h-6 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        ))}
      </div>
    </div>
  )
}

// Error Boundary Component
export function UniversalRendererErrorBoundary({
  reset
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Something went wrong!
        </h2>
        <p className="text-gray-600 mb-6">
          We encountered an error while loading this page.
        </p>
        <button
          onClick={reset}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          Try again
        </button>
      </div>
    </div>
  )
}
