# CMS Rendering System

A comprehensive rendering system for Next.js 15 App Router that integrates with our CMS builders to provide dynamic page generation, layout management, and performance optimization.

## 🚀 **Features**

### **Core Rendering**
- ✅ **Universal Renderer** - Handles all content types (pages, posts, archives)
- ✅ **Dynamic Page Renderer** - CMS builder integration with block rendering
- ✅ **Layout System** - Responsive layouts with template hierarchy
- ✅ **SEO Optimization** - Comprehensive meta tags and structured data
- ✅ **Performance Monitoring** - Core Web Vitals tracking and optimization

### **Next.js 15 Integration**
- ✅ **App Router Compatible** - Full Next.js 15 App Router support
- ✅ **Static Generation** - ISR and SSG with dynamic params
- ✅ **Metadata API** - Dynamic metadata generation
- ✅ **Middleware Support** - Route handling and caching
- ✅ **Error Boundaries** - Graceful error handling

### **CMS Builder Integration**
- ✅ **Block Rendering** - Seamless integration with CMS builders
- ✅ **Layout Resolution** - Dynamic layout selection
- ✅ **Content Validation** - Real-time content validation
- ✅ **Preview Mode** - Live preview with performance monitoring

## 📁 **File Structure**

```
lib/rendering/
├── index.ts                    # Main exports
├── universal-renderer.tsx      # Core rendering logic
├── dynamic-page-renderer.tsx   # CMS builder integration
├── post-type-renderer.tsx      # Custom post type rendering
├── archive-renderer.tsx        # Archive/listing pages
├── seo-head.tsx               # SEO and metadata
├── performance-monitor.tsx     # Performance tracking
├── nextjs-integration.tsx     # Next.js 15 utilities
├── routing-modifiers.tsx      # Dynamic routing
├── layout-system.tsx          # Layout management
└── README.md                  # This file
```

## 🔧 **Usage**

### **Basic Setup**

```tsx
// app/[...slug]/page.tsx
import { UniversalRenderer } from '@/lib/rendering'
import { generateDynamicMetadata } from '@/lib/rendering'

export async function generateMetadata({ params }) {
  return await generateDynamicMetadata(params)
}

export default async function DynamicPage({ params, searchParams }) {
  const pathname = '/' + params.slug.join('/')
  
  return (
    <UniversalRenderer
      pathname={pathname}
      searchParams={searchParams}
    />
  )
}
```

### **Layout Provider Setup**

```tsx
// app/layout.tsx
import { LayoutProvider } from '@/lib/rendering'

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <LayoutProvider>
          {children}
        </LayoutProvider>
      </body>
    </html>
  )
}
```

### **Dynamic Page Rendering**

```tsx
// Custom page component
import { DynamicPageRenderer } from '@/lib/rendering'

export function CustomPage({ content, postType }) {
  return (
    <DynamicPageRenderer
      content={content}
      postType={postType}
      className="custom-page"
    />
  )
}
```

### **SEO Integration**

```tsx
// Custom SEO component
import { SEOHead, useSEOData } from '@/lib/rendering'

export function CustomSEO({ content, postType }) {
  const seoData = useSEOData(content, postType)
  
  return <SEOHead {...seoData} />
}
```

### **Performance Monitoring**

```tsx
// With performance tracking
import { PerformanceMonitor } from '@/lib/rendering'

export function MonitoredPage({ children, pageId }) {
  return (
    <PerformanceMonitor pageId={pageId}>
      {children}
    </PerformanceMonitor>
  )
}
```

## 🎯 **Key Components**

### **UniversalRenderer**
The main rendering component that handles all content types:

```tsx
<UniversalRenderer
  pathname="/about"
  searchParams={{ preview: 'true' }}
/>
```

**Features:**
- Automatic route resolution
- CMS builder detection
- Layout integration
- Hook system support
- Error handling

### **DynamicPageRenderer**
Specialized renderer for CMS builder content:

```tsx
<DynamicPageRenderer
  content={cmsContent}
  postType={postType}
  layout={customLayout}
  isPreview={true}
/>
```

**Features:**
- Block-based rendering
- Responsive design
- Preview mode
- Performance optimization
- SEO integration

### **Layout System**
Comprehensive layout management:

```tsx
// Using layout provider
const { setLayout, layoutMode } = useLayout()

// Responsive layout
<ResponsiveLayout content={content} postType={postType}>
  {children}
</ResponsiveLayout>
```

**Features:**
- Template hierarchy
- Responsive breakpoints
- Dynamic resolution
- Custom layouts
- Device detection

## 🔄 **Routing Integration**

### **Dynamic Routing**

```tsx
import { useDynamicRouting } from '@/lib/rendering'

export function Navigation() {
  const { navigateTo, isLoading } = useDynamicRouting()
  
  const handleClick = () => {
    navigateTo('/products/new-item')
  }
  
  return (
    <button onClick={handleClick} disabled={isLoading}>
      Navigate
    </button>
  )
}
```

### **Route Modifiers**

```tsx
import { RouteModifierService } from '@/lib/rendering'

// Add redirect
RouteModifierService.addModifier({
  id: 'old-to-new',
  name: 'Redirect old URLs',
  pattern: '^/old-path/(.*)',
  replacement: '/new-path/$1',
  type: 'redirect',
  status: 301,
  isActive: true,
  priority: 10
})
```

### **Breadcrumbs**

```tsx
import { useBreadcrumbs } from '@/lib/rendering'

export function Breadcrumbs() {
  const breadcrumbs = useBreadcrumbs()
  
  return (
    <nav>
      {breadcrumbs.map((crumb, index) => (
        <a key={index} href={crumb.href}>
          {crumb.label}
        </a>
      ))}
    </nav>
  )
}
```

## 📊 **Performance Features**

### **Core Web Vitals**
- **FCP** - First Contentful Paint tracking
- **LCP** - Largest Contentful Paint monitoring
- **CLS** - Cumulative Layout Shift detection
- **FID** - First Input Delay measurement
- **TTI** - Time to Interactive calculation

### **Caching Strategy**
- **Pages**: 1 hour cache with stale-while-revalidate
- **Posts**: 30 minutes cache
- **Archives**: 5 minutes cache
- **Preview**: No cache

### **Performance Utilities**

```tsx
import { PerformanceUtils } from '@/lib/rendering'

// Preload resources
PerformanceUtils.preloadResource('/critical.css', 'style')

// Lazy load images
PerformanceUtils.lazyLoadImage(imageElement)

// Measure render time
PerformanceUtils.measureRender('MyComponent', () => {
  // Component render logic
})
```

## 🔍 **SEO Features**

### **Automatic Meta Tags**
- Title and description optimization
- Open Graph tags
- Twitter Card support
- Structured data (JSON-LD)
- Canonical URLs
- Robots meta tags

### **South African Optimization**
- ZAR currency support
- South African geo tags
- Local business schema
- POPI Act compliance
- Local timezone support

### **Product SEO**

```tsx
import { ProductSEOHead } from '@/lib/rendering'

<ProductSEOHead product={productData} />
```

Generates:
- Product schema markup
- Price and availability
- Brand information
- Review aggregation
- Local business data

## 🛠 **Configuration**

### **Environment Variables**

```env
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
NEXT_PUBLIC_SITE_NAME=Your Site Name
REVALIDATE_TOKEN=your-revalidate-token
GOOGLE_SITE_VERIFICATION=your-verification-code
```

### **Constants**

```tsx
import { RENDERING_CONSTANTS } from '@/lib/rendering'

// Cache TTL values
RENDERING_CONSTANTS.CACHE_TTL.PAGE // 3600 seconds

// Performance thresholds
RENDERING_CONSTANTS.PERFORMANCE_THRESHOLDS.LCP // 2500ms

// Device breakpoints
RENDERING_CONSTANTS.BREAKPOINTS.MOBILE // 768px
```

## 🚨 **Error Handling**

### **Error Types**

```tsx
import { 
  RenderingError, 
  ContentNotFoundError, 
  LayoutNotFoundError 
} from '@/lib/rendering'

try {
  // Rendering logic
} catch (error) {
  if (error instanceof ContentNotFoundError) {
    // Handle content not found
  }
}
```

### **Error Boundary**

```tsx
import { UniversalRendererErrorBoundary } from '@/lib/rendering'

<UniversalRendererErrorBoundary reset={() => window.location.reload()}>
  <YourComponent />
</UniversalRendererErrorBoundary>
```

## 🔌 **Extensibility**

### **Rendering Hooks**

```tsx
import { RenderingHooks } from '@/lib/rendering'
import { HookSystem } from '@/lib/cms/plugins/hook-system'

// Add custom rendering logic
HookSystem.addFilter(RenderingHooks.MODIFY_CONTENT, (content) => {
  // Modify content before rendering
  return enhancedContent
})

// Add SEO modifications
HookSystem.addFilter(RenderingHooks.MODIFY_SEO_TITLE, (title, content) => {
  return `${title} - Custom Suffix`
})
```

### **Custom Layout Templates**

```tsx
import { LayoutTemplateSystem } from '@/lib/rendering'

LayoutTemplateSystem.registerTemplate({
  name: 'custom-layout',
  label: 'Custom Layout',
  description: 'A custom layout template',
  postTypes: ['page', 'post'],
  generate: (content, postType) => {
    // Return layout configuration
    return customLayoutConfig
  }
})
```

## 📈 **Best Practices**

1. **Performance**
   - Use appropriate cache headers
   - Implement lazy loading
   - Monitor Core Web Vitals
   - Optimize images and assets

2. **SEO**
   - Always provide meta descriptions
   - Use structured data
   - Implement canonical URLs
   - Optimize for local search

3. **Accessibility**
   - Use semantic HTML
   - Provide alt text for images
   - Ensure keyboard navigation
   - Test with screen readers

4. **Error Handling**
   - Implement error boundaries
   - Provide fallback content
   - Log errors for monitoring
   - Test error scenarios

## 🔄 **Migration Guide**

### **From Static Pages**

1. Move pages to CMS content
2. Convert layouts to templates
3. Update routing configuration
4. Test dynamic rendering

### **From Other CMS**

1. Export content to CMS format
2. Map post types and fields
3. Configure layouts and templates
4. Set up redirects for old URLs

---

**The rendering system is now fully integrated with our CMS builders and ready for production use with Next.js 15 App Router!**
