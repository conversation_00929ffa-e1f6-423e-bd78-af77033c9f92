// Performance Monitor for Dynamic Page Rendering
// Tracks Core Web Vitals and rendering performance

'use client'

import React, { useEffect, useRef, useState } from 'react'
import { usePathname } from 'next/navigation'

interface PerformanceMetrics {
  pageId: string
  pathname: string
  loadTime: number
  renderTime: number
  firstContentfulPaint?: number
  largestContentfulPaint?: number
  cumulativeLayoutShift?: number
  firstInputDelay?: number
  timeToInteractive?: number
  blocksCount: number
  cacheHit: boolean
  isPreview: boolean
  timestamp: number
}

interface PerformanceMonitorProps {
  pageId: string
  isPreview?: boolean
  children: React.ReactNode
  onMetrics?: (metrics: PerformanceMetrics) => void
}

export function PerformanceMonitor({
  pageId,
  isPreview = false,
  children,
  onMetrics
}: PerformanceMonitorProps) {
  const pathname = usePathname()
  const startTimeRef = useRef<number>(Date.now())
  const renderStartRef = useRef<number>(Date.now())
  const [metrics, setMetrics] = useState<Partial<PerformanceMetrics>>({})
  const observerRef = useRef<PerformanceObserver | null>(null)

  // Initialize performance monitoring
  useEffect(() => {
    renderStartRef.current = Date.now()

    // Set up Performance Observer for Core Web Vitals
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      try {
        // Observer for paint metrics
        const paintObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry) => {
            if (entry.name === 'first-contentful-paint') {
              setMetrics(prev => ({
                ...prev,
                firstContentfulPaint: entry.startTime
              }))
            }
          })
        })
        paintObserver.observe({ entryTypes: ['paint'] })

        // Observer for LCP
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          if (lastEntry) {
            setMetrics(prev => ({
              ...prev,
              largestContentfulPaint: lastEntry.startTime
            }))
          }
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

        // Observer for CLS
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          })
          setMetrics(prev => ({
            ...prev,
            cumulativeLayoutShift: clsValue
          }))
        })
        clsObserver.observe({ entryTypes: ['layout-shift'] })

        // Observer for FID
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry) => {
            setMetrics(prev => ({
              ...prev,
              firstInputDelay: entry.processingStart - entry.startTime
            }))
          })
        })
        fidObserver.observe({ entryTypes: ['first-input'] })

        observerRef.current = paintObserver

        // Clean up observers
        return () => {
          paintObserver.disconnect()
          lcpObserver.disconnect()
          clsObserver.disconnect()
          fidObserver.disconnect()
        }
      } catch (error) {
        console.warn('Performance Observer not supported:', error)
      }
    }
  }, [])

  // Calculate render time when component mounts
  useEffect(() => {
    const renderTime = Date.now() - renderStartRef.current
    setMetrics(prev => ({
      ...prev,
      renderTime
    }))
  }, [])

  // Calculate load time and send metrics when page is fully loaded
  useEffect(() => {
    const handleLoad = () => {
      const loadTime = Date.now() - startTimeRef.current
      const blocksCount = document.querySelectorAll('[data-block-type]').length
      
      // Check if page was served from cache
      const cacheHit = document.querySelector('meta[name="x-cache"]')?.getAttribute('content') === 'HIT'

      const finalMetrics: PerformanceMetrics = {
        pageId,
        pathname,
        loadTime,
        renderTime: metrics.renderTime || 0,
        firstContentfulPaint: metrics.firstContentfulPaint,
        largestContentfulPaint: metrics.largestContentfulPaint,
        cumulativeLayoutShift: metrics.cumulativeLayoutShift,
        firstInputDelay: metrics.firstInputDelay,
        timeToInteractive: calculateTTI(),
        blocksCount,
        cacheHit,
        isPreview,
        timestamp: Date.now()
      }

      setMetrics(finalMetrics)
      onMetrics?.(finalMetrics)

      // Send metrics to analytics (only in production)
      if (process.env.NODE_ENV === 'production' && !isPreview) {
        sendMetricsToAnalytics(finalMetrics)
      }
    }

    if (document.readyState === 'complete') {
      handleLoad()
    } else {
      window.addEventListener('load', handleLoad)
      return () => window.removeEventListener('load', handleLoad)
    }
  }, [pageId, pathname, metrics, isPreview, onMetrics])

  return (
    <>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <PerformanceDebugPanel metrics={metrics as PerformanceMetrics} />
      )}
    </>
  )
}

// Calculate Time to Interactive (TTI)
function calculateTTI(): number {
  if (typeof window === 'undefined' || !('performance' in window)) {
    return 0
  }

  try {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    return navigation.domInteractive - navigation.navigationStart
  } catch (error) {
    return 0
  }
}

// Send metrics to analytics service
async function sendMetricsToAnalytics(metrics: PerformanceMetrics) {
  try {
    await fetch('/api/analytics/performance', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(metrics)
    })
  } catch (error) {
    console.warn('Failed to send performance metrics:', error)
  }
}

// Debug panel for development
function PerformanceDebugPanel({ metrics }: { metrics: PerformanceMetrics }) {
  const [isVisible, setIsVisible] = useState(false)

  if (!metrics || Object.keys(metrics).length === 0) {
    return null
  }

  const getScoreColor = (value: number, thresholds: [number, number]) => {
    if (value <= thresholds[0]) return 'text-green-600'
    if (value <= thresholds[1]) return 'text-yellow-600'
    return 'text-red-600'
  }

  const formatTime = (time: number) => {
    if (time < 1000) return `${Math.round(time)}ms`
    return `${(time / 1000).toFixed(2)}s`
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium shadow-lg hover:bg-blue-700 transition-colors"
      >
        ⚡ Performance
      </button>
      
      {isVisible && (
        <div className="absolute bottom-12 right-0 bg-white border border-gray-200 rounded-lg shadow-xl p-4 w-80 max-h-96 overflow-y-auto">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-gray-900">Performance Metrics</h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Page Load:</span>
              <span className={getScoreColor(metrics.loadTime, [1000, 3000])}>
                {formatTime(metrics.loadTime)}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span>Render Time:</span>
              <span className={getScoreColor(metrics.renderTime, [100, 300])}>
                {formatTime(metrics.renderTime)}
              </span>
            </div>
            
            {metrics.firstContentfulPaint && (
              <div className="flex justify-between">
                <span>FCP:</span>
                <span className={getScoreColor(metrics.firstContentfulPaint, [1800, 3000])}>
                  {formatTime(metrics.firstContentfulPaint)}
                </span>
              </div>
            )}
            
            {metrics.largestContentfulPaint && (
              <div className="flex justify-between">
                <span>LCP:</span>
                <span className={getScoreColor(metrics.largestContentfulPaint, [2500, 4000])}>
                  {formatTime(metrics.largestContentfulPaint)}
                </span>
              </div>
            )}
            
            {metrics.cumulativeLayoutShift !== undefined && (
              <div className="flex justify-between">
                <span>CLS:</span>
                <span className={getScoreColor(metrics.cumulativeLayoutShift * 1000, [100, 250])}>
                  {metrics.cumulativeLayoutShift.toFixed(3)}
                </span>
              </div>
            )}
            
            {metrics.firstInputDelay && (
              <div className="flex justify-between">
                <span>FID:</span>
                <span className={getScoreColor(metrics.firstInputDelay, [100, 300])}>
                  {formatTime(metrics.firstInputDelay)}
                </span>
              </div>
            )}
            
            <div className="border-t pt-2 mt-2">
              <div className="flex justify-between">
                <span>Blocks:</span>
                <span>{metrics.blocksCount}</span>
              </div>
              
              <div className="flex justify-between">
                <span>Cache:</span>
                <span className={metrics.cacheHit ? 'text-green-600' : 'text-red-600'}>
                  {metrics.cacheHit ? 'HIT' : 'MISS'}
                </span>
              </div>
              
              {metrics.isPreview && (
                <div className="flex justify-between">
                  <span>Mode:</span>
                  <span className="text-blue-600">Preview</span>
                </div>
              )}
            </div>
          </div>
          
          <div className="mt-3 pt-2 border-t text-xs text-gray-500">
            Page ID: {metrics.pageId.substring(0, 8)}...
          </div>
        </div>
      )}
    </div>
  )
}

// Hook for accessing performance metrics
export function usePerformanceMetrics() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  
  const recordMetrics = (newMetrics: PerformanceMetrics) => {
    setMetrics(newMetrics)
  }
  
  return {
    metrics,
    recordMetrics
  }
}

// Performance optimization utilities
export const PerformanceUtils = {
  // Preload critical resources
  preloadResource: (href: string, as: string) => {
    if (typeof document !== 'undefined') {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = href
      link.as = as
      document.head.appendChild(link)
    }
  },

  // Lazy load images
  lazyLoadImage: (img: HTMLImageElement) => {
    if ('IntersectionObserver' in window) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const target = entry.target as HTMLImageElement
            target.src = target.dataset.src || ''
            target.classList.remove('lazy')
            observer.unobserve(target)
          }
        })
      })
      observer.observe(img)
    }
  },

  // Measure component render time
  measureRender: (componentName: string, renderFn: () => void) => {
    const start = performance.now()
    renderFn()
    const end = performance.now()
    console.log(`${componentName} render time: ${end - start}ms`)
  }
}

export default PerformanceMonitor
