// Routing Modifiers for Dynamic Frontend Generation
// Provides URL rewriting, redirects, and dynamic route handling

'use client'

import { useRouter, usePathname, useSearchParams } from 'next/navigation'
import { useEffect, useState, useCallback } from 'react'
import { RouteResolver } from '@/lib/routing/route-resolver'
import { PostTypeService } from '@/lib/cms/services/post-type-service'

// Types for routing modifiers
export interface RouteModifier {
  id: string
  name: string
  pattern: string
  replacement: string
  type: 'redirect' | 'rewrite' | 'proxy'
  status?: number
  conditions?: RouteCondition[]
  isActive: boolean
  priority: number
}

export interface RouteCondition {
  type: 'header' | 'query' | 'cookie' | 'device' | 'geo' | 'time'
  key: string
  operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'regex'
  value: string
  negate?: boolean
}

export interface DynamicRoute {
  pattern: string
  component: string
  layout?: string
  middleware?: string[]
  cache?: {
    ttl: number
    tags: string[]
  }
  seo?: {
    title?: string
    description?: string
    noindex?: boolean
  }
}

// Hook for dynamic routing
export function useDynamicRouting() {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Navigate to dynamic route
  const navigateTo = useCallback(async (path: string, options?: {
    replace?: boolean
    scroll?: boolean
    shallow?: boolean
  }) => {
    try {
      setIsLoading(true)
      setError(null)

      // Check if route exists
      const resolution = await RouteResolver.resolve(path)
      
      if (resolution.type === 'notfound') {
        throw new Error('Route not found')
      }

      // Navigate using Next.js router
      if (options?.replace) {
        router.replace(path, { scroll: options.scroll })
      } else {
        router.push(path, { scroll: options.scroll })
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Navigation failed'
      setError(errorMessage)
      console.error('Dynamic navigation error:', err)
    } finally {
      setIsLoading(false)
    }
  }, [router])

  // Get current route info
  const getCurrentRoute = useCallback(async () => {
    try {
      const resolution = await RouteResolver.resolve(pathname)
      return {
        pathname,
        searchParams: Object.fromEntries(searchParams.entries()),
        resolution
      }
    } catch (err) {
      console.error('Error getting current route:', err)
      return null
    }
  }, [pathname, searchParams])

  return {
    navigateTo,
    getCurrentRoute,
    isLoading,
    error,
    pathname,
    searchParams
  }
}

// Route modifier service
export class RouteModifierService {
  private static modifiers: RouteModifier[] = []
  private static dynamicRoutes: DynamicRoute[] = []

  // Add route modifier
  static addModifier(modifier: RouteModifier) {
    this.modifiers.push(modifier)
    this.modifiers.sort((a, b) => b.priority - a.priority)
  }

  // Remove route modifier
  static removeModifier(id: string) {
    this.modifiers = this.modifiers.filter(m => m.id !== id)
  }

  // Apply route modifiers to a path
  static async applyModifiers(path: string, context: {
    headers?: Record<string, string>
    query?: Record<string, string>
    cookies?: Record<string, string>
    userAgent?: string
  } = {}): Promise<{
    path: string
    type: 'redirect' | 'rewrite' | 'proxy' | 'none'
    status?: number
  }> {
    for (const modifier of this.modifiers) {
      if (!modifier.isActive) continue

      // Check if pattern matches
      const regex = new RegExp(modifier.pattern)
      if (!regex.test(path)) continue

      // Check conditions
      if (modifier.conditions && !this.checkConditions(modifier.conditions, context)) {
        continue
      }

      // Apply modifier
      const newPath = path.replace(regex, modifier.replacement)
      
      return {
        path: newPath,
        type: modifier.type,
        status: modifier.status
      }
    }

    return { path, type: 'none' }
  }

  // Check route conditions
  private static checkConditions(
    conditions: RouteCondition[],
    context: any
  ): boolean {
    return conditions.every(condition => {
      let value: string | undefined

      switch (condition.type) {
        case 'header':
          value = context.headers?.[condition.key.toLowerCase()]
          break
        case 'query':
          value = context.query?.[condition.key]
          break
        case 'cookie':
          value = context.cookies?.[condition.key]
          break
        case 'device':
          value = this.getDeviceType(context.userAgent)
          break
        case 'geo':
          value = context.headers?.['cf-ipcountry'] || context.headers?.['x-country']
          break
        case 'time':
          value = new Date().getHours().toString()
          break
        default:
          return false
      }

      if (!value) return condition.negate || false

      let matches = false
      switch (condition.operator) {
        case 'equals':
          matches = value === condition.value
          break
        case 'contains':
          matches = value.includes(condition.value)
          break
        case 'starts_with':
          matches = value.startsWith(condition.value)
          break
        case 'ends_with':
          matches = value.endsWith(condition.value)
          break
        case 'regex':
          matches = new RegExp(condition.value).test(value)
          break
      }

      return condition.negate ? !matches : matches
    })
  }

  // Get device type from user agent
  private static getDeviceType(userAgent?: string): string {
    if (!userAgent) return 'unknown'
    
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      return 'mobile'
    }
    if (/Tablet|iPad/.test(userAgent)) {
      return 'tablet'
    }
    return 'desktop'
  }

  // Add dynamic route
  static addDynamicRoute(route: DynamicRoute) {
    this.dynamicRoutes.push(route)
  }

  // Get matching dynamic route
  static getDynamicRoute(path: string): DynamicRoute | null {
    return this.dynamicRoutes.find(route => {
      const regex = new RegExp(route.pattern)
      return regex.test(path)
    }) || null
  }
}

// URL builder utility
export class URLBuilder {
  private baseUrl: string
  private path: string
  private params: URLSearchParams

  constructor(baseUrl?: string) {
    this.baseUrl = baseUrl || ''
    this.path = ''
    this.params = new URLSearchParams()
  }

  // Set path
  setPath(path: string): URLBuilder {
    this.path = path.startsWith('/') ? path : `/${path}`
    return this
  }

  // Add query parameter
  addParam(key: string, value: string | number | boolean): URLBuilder {
    this.params.set(key, value.toString())
    return this
  }

  // Add multiple parameters
  addParams(params: Record<string, string | number | boolean>): URLBuilder {
    Object.entries(params).forEach(([key, value]) => {
      this.params.set(key, value.toString())
    })
    return this
  }

  // Remove parameter
  removeParam(key: string): URLBuilder {
    this.params.delete(key)
    return this
  }

  // Build URL
  build(): string {
    const queryString = this.params.toString()
    const url = `${this.baseUrl}${this.path}`
    return queryString ? `${url}?${queryString}` : url
  }

  // Build relative URL
  buildRelative(): string {
    const queryString = this.params.toString()
    return queryString ? `${this.path}?${queryString}` : this.path
  }

  // Parse existing URL
  static parse(url: string): URLBuilder {
    const urlObj = new URL(url, 'http://localhost')
    const builder = new URLBuilder(urlObj.origin)
    builder.setPath(urlObj.pathname)
    urlObj.searchParams.forEach((value, key) => {
      builder.addParam(key, value)
    })
    return builder
  }
}

// Breadcrumb generator
export function useBreadcrumbs() {
  const pathname = usePathname()
  const [breadcrumbs, setBreadcrumbs] = useState<Array<{
    label: string
    href: string
    isActive: boolean
  }>>([])

  useEffect(() => {
    const generateBreadcrumbs = async () => {
      try {
        const segments = pathname.split('/').filter(Boolean)
        const crumbs = [{ label: 'Home', href: '/', isActive: false }]

        let currentPath = ''
        for (let i = 0; i < segments.length; i++) {
          currentPath += `/${segments[i]}`
          const isLast = i === segments.length - 1

          // Try to resolve the route to get proper title
          try {
            const resolution = await RouteResolver.resolve(currentPath)
            let label = segments[i]

            if (resolution.type === 'page' && resolution.data) {
              const pageData = await RouteResolver.getPageData(resolution.data.id)
              label = pageData?.title || label
            } else if (resolution.type === 'post' && resolution.data) {
              const postData = await RouteResolver.getPostData(resolution.data.id)
              label = postData?.title || label
            } else if (resolution.type === 'archive' && resolution.data) {
              label = resolution.data.labelPlural || label
            }

            crumbs.push({
              label: label.charAt(0).toUpperCase() + label.slice(1),
              href: currentPath,
              isActive: isLast
            })
          } catch {
            // Fallback to segment name
            crumbs.push({
              label: segments[i].charAt(0).toUpperCase() + segments[i].slice(1),
              href: currentPath,
              isActive: isLast
            })
          }
        }

        setBreadcrumbs(crumbs)
      } catch (error) {
        console.error('Error generating breadcrumbs:', error)
        setBreadcrumbs([{ label: 'Home', href: '/', isActive: false }])
      }
    }

    generateBreadcrumbs()
  }, [pathname])

  return breadcrumbs
}

// Route preloader for performance
export function useRoutePreloader() {
  const preloadRoute = useCallback(async (path: string) => {
    try {
      // Preload the route data
      await RouteResolver.resolve(path)
      
      // Preload the page component if using dynamic imports
      if (typeof window !== 'undefined') {
        const link = document.createElement('link')
        link.rel = 'prefetch'
        link.href = path
        document.head.appendChild(link)
      }
    } catch (error) {
      console.warn('Failed to preload route:', path, error)
    }
  }, [])

  return { preloadRoute }
}

export default {
  useDynamicRouting,
  RouteModifierService,
  URLBuilder,
  useBreadcrumbs,
  useRoutePreloader
}
