// SEO Head Component for Dynamic Pages
// Comprehensive SEO meta tags and structured data for CMS content

'use client'

import Head from 'next/head'
import { usePathname } from 'next/navigation'

interface SEOHeadProps {
  title: string
  description?: string
  image?: string
  url?: string
  type?: string
  noIndex?: boolean
  noFollow?: boolean
  publishedAt?: Date
  modifiedAt?: Date
  author?: string
  tags?: string[]
  canonicalUrl?: string
  alternateUrls?: { hreflang: string; href: string }[]
  structuredData?: any
}

export function SEOHead({
  title,
  description,
  image,
  url,
  type = 'website',
  noIndex = false,
  noFollow = false,
  publishedAt,
  modifiedAt,
  author,
  tags = [],
  canonicalUrl,
  alternateUrls = [],
  structuredData
}: SEOHeadProps) {
  const pathname = usePathname()
  const currentUrl = url || pathname
  const fullUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://localhost:3000'}${currentUrl}`
  const canonical = canonicalUrl || fullUrl

  // Generate robots meta tag
  const robotsContent = [
    noIndex ? 'noindex' : 'index',
    noFollow ? 'nofollow' : 'follow'
  ].join(', ')

  // Generate structured data for articles/products
  const generateStructuredData = () => {
    if (structuredData) {
      return structuredData
    }

    const baseData = {
      '@context': 'https://schema.org',
      '@type': type === 'product' ? 'Product' : 'Article',
      headline: title,
      description: description,
      url: fullUrl,
      ...(image && { image: image }),
      ...(publishedAt && { datePublished: publishedAt.toISOString() }),
      ...(modifiedAt && { dateModified: modifiedAt.toISOString() }),
      ...(author && {
        author: {
          '@type': 'Person',
          name: author
        }
      })
    }

    // Add product-specific data
    if (type === 'product') {
      return {
        ...baseData,
        '@type': 'Product',
        brand: {
          '@type': 'Brand',
          name: process.env.NEXT_PUBLIC_SITE_NAME || 'Coco Milk Store'
        },
        offers: {
          '@type': 'Offer',
          availability: 'https://schema.org/InStock',
          priceCurrency: 'ZAR'
        }
      }
    }

    // Add article-specific data
    if (type === 'article' || type === 'page') {
      return {
        ...baseData,
        '@type': 'Article',
        publisher: {
          '@type': 'Organization',
          name: process.env.NEXT_PUBLIC_SITE_NAME || 'Coco Milk Store',
          logo: {
            '@type': 'ImageObject',
            url: `${process.env.NEXT_PUBLIC_SITE_URL}/logo.png`
          }
        }
      }
    }

    return baseData
  }

  const jsonLd = generateStructuredData()

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      {description && <meta name="description" content={description} />}
      <meta name="robots" content={robotsContent} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={canonical} />
      
      {/* Alternate URLs for internationalization */}
      {alternateUrls.map((alt, index) => (
        <link
          key={index}
          rel="alternate"
          hrefLang={alt.hreflang}
          href={alt.href}
        />
      ))}

      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={title} />
      {description && <meta property="og:description" content={description} />}
      <meta property="og:url" content={fullUrl} />
      <meta property="og:type" content={type === 'product' ? 'product' : 'website'} />
      <meta property="og:site_name" content={process.env.NEXT_PUBLIC_SITE_NAME || 'Coco Milk Store'} />
      <meta property="og:locale" content="en_ZA" />
      {image && <meta property="og:image" content={image} />}
      {image && <meta property="og:image:alt" content={title} />}
      {publishedAt && <meta property="article:published_time" content={publishedAt.toISOString()} />}
      {modifiedAt && <meta property="article:modified_time" content={modifiedAt.toISOString()} />}
      {author && <meta property="article:author" content={author} />}
      {tags.map((tag, index) => (
        <meta key={index} property="article:tag" content={tag} />
      ))}

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={image ? "summary_large_image" : "summary"} />
      <meta name="twitter:title" content={title} />
      {description && <meta name="twitter:description" content={description} />}
      {image && <meta name="twitter:image" content={image} />}
      {image && <meta name="twitter:image:alt" content={title} />}

      {/* Additional Meta Tags for South African Context */}
      <meta name="geo.region" content="ZA" />
      <meta name="geo.country" content="South Africa" />
      <meta name="language" content="en-ZA" />
      <meta name="currency" content="ZAR" />

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      {/* Preconnect to external domains for performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* DNS Prefetch for common external resources */}
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
      
      {/* Favicon and App Icons */}
      <link rel="icon" href="/favicon.ico" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Theme Color for Mobile Browsers */}
      <meta name="theme-color" content="#ffffff" />
      <meta name="msapplication-TileColor" content="#ffffff" />
    </Head>
  )
}

// Hook for generating SEO data from CMS content
export function useSEOData(content: any, postType: any) {
  const pathname = usePathname()
  
  return {
    title: content.metadata?.seoTitle || content.title || 'Untitled',
    description: content.metadata?.seoDescription || content.excerpt || '',
    image: content.featuredImage || content.metadata?.socialImage || '',
    url: pathname,
    type: postType.name,
    noIndex: content.metadata?.noIndex || false,
    noFollow: content.metadata?.noFollow || false,
    publishedAt: content.publishedAt,
    modifiedAt: content.updatedAt,
    author: content.author?.name || '',
    tags: content.taxonomyTerms?.map((term: any) => term.name) || [],
    canonicalUrl: content.metadata?.canonicalUrl || '',
    structuredData: content.metadata?.structuredData || null
  }
}

// Component for product-specific SEO
export function ProductSEOHead({ product }: { product: any }) {
  const seoData = {
    title: `${product.title} - ${process.env.NEXT_PUBLIC_SITE_NAME}`,
    description: product.excerpt || `Buy ${product.title} online in South Africa`,
    image: product.featuredImage,
    type: 'product',
    structuredData: {
      '@context': 'https://schema.org',
      '@type': 'Product',
      name: product.title,
      description: product.excerpt,
      image: product.featuredImage,
      brand: {
        '@type': 'Brand',
        name: process.env.NEXT_PUBLIC_SITE_NAME || 'Coco Milk Store'
      },
      offers: {
        '@type': 'Offer',
        price: product.customFields?.price || '0',
        priceCurrency: 'ZAR',
        availability: product.customFields?.stock > 0 
          ? 'https://schema.org/InStock' 
          : 'https://schema.org/OutOfStock',
        seller: {
          '@type': 'Organization',
          name: process.env.NEXT_PUBLIC_SITE_NAME || 'Coco Milk Store'
        }
      },
      aggregateRating: product.customFields?.averageRating && {
        '@type': 'AggregateRating',
        ratingValue: product.customFields.averageRating,
        reviewCount: product.customFields.reviewCount || 1
      }
    }
  }

  return <SEOHead {...seoData} />
}

// Component for category/archive SEO
export function ArchiveSEOHead({ 
  title, 
  description, 
  type = 'category',
  items = []
}: { 
  title: string
  description?: string
  type?: string
  items?: any[]
}) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: title,
    description: description,
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: items.length,
      itemListElement: items.slice(0, 10).map((item, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        item: {
          '@type': item.type === 'product' ? 'Product' : 'Article',
          name: item.title,
          url: `${process.env.NEXT_PUBLIC_SITE_URL}/${item.slug}`
        }
      }))
    }
  }

  return (
    <SEOHead
      title={`${title} - ${process.env.NEXT_PUBLIC_SITE_NAME}`}
      description={description || `Browse ${title.toLowerCase()} at ${process.env.NEXT_PUBLIC_SITE_NAME}`}
      type="website"
      structuredData={structuredData}
    />
  )
}

export default SEOHead
