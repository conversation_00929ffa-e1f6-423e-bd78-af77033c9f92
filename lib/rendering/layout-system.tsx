// Layout System for Dynamic Frontend Generation
// Provides layout resolution, template hierarchy, and responsive layouts

'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { LayoutRenderer } from '@/lib/layout-builder/components/layout-renderer'
import { PostType, CMSContent } from '@/lib/cms/types'
import { ResolvedLayout } from '@/lib/layout-builder/types'
import { LayoutResolver } from '@/lib/layout-builder/layout-resolver'
import Header from '@/components/header'
import Footer from '@/components/footer'

// Layout context
interface LayoutContextType {
  currentLayout: ResolvedLayout | null
  layoutMode: 'default' | 'custom' | 'builder'
  deviceType: 'mobile' | 'tablet' | 'desktop'
  setLayout: (layout: ResolvedLayout | null) => void
  setLayoutMode: (mode: 'default' | 'custom' | 'builder') => void
}

const LayoutContext = createContext<LayoutContextType | null>(null)

// Layout provider component
export function LayoutProvider({ children }: { children: React.ReactNode }) {
  const [currentLayout, setCurrentLayout] = useState<ResolvedLayout | null>(null)
  const [layoutMode, setLayoutMode] = useState<'default' | 'custom' | 'builder'>('default')
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop'>('desktop')

  // Detect device type
  useEffect(() => {
    const updateDeviceType = () => {
      const width = window.innerWidth
      if (width < 768) {
        setDeviceType('mobile')
      } else if (width < 1024) {
        setDeviceType('tablet')
      } else {
        setDeviceType('desktop')
      }
    }

    updateDeviceType()
    window.addEventListener('resize', updateDeviceType)
    return () => window.removeEventListener('resize', updateDeviceType)
  }, [])

  const setLayout = (layout: ResolvedLayout | null) => {
    setCurrentLayout(layout)
    setLayoutMode(layout ? 'custom' : 'default')
  }

  return (
    <LayoutContext.Provider value={{
      currentLayout,
      layoutMode,
      deviceType,
      setLayout,
      setLayoutMode
    }}>
      {children}
    </LayoutContext.Provider>
  )
}

// Hook to use layout context
export function useLayout() {
  const context = useContext(LayoutContext)
  if (!context) {
    throw new Error('useLayout must be used within a LayoutProvider')
  }
  return context
}

// Dynamic layout resolver
export class DynamicLayoutResolver {
  // Resolve layout for content
  static async resolveForContent(
    content: CMSContent,
    postType: PostType,
    context?: any
  ): Promise<ResolvedLayout | null> {
    try {
      // Check if content has custom layout
      if (content.metadata?.layoutId) {
        return await LayoutResolver.getLayout(content.metadata.layoutId)
      }

      // Check post type default layout
      if (postType.metadata?.defaultLayoutId) {
        return await LayoutResolver.getLayout(postType.metadata.defaultLayoutId)
      }

      // Check for template-based layout
      const templateLayout = await this.resolveTemplateLayout(content, postType)
      if (templateLayout) {
        return templateLayout
      }

      // Fallback to default layout
      return await this.getDefaultLayout(postType.name)
    } catch (error) {
      console.error('Error resolving layout:', error)
      return null
    }
  }

  // Resolve template-based layout
  private static async resolveTemplateLayout(
    content: CMSContent,
    postType: PostType
  ): Promise<ResolvedLayout | null> {
    const template = content.metadata?.template || postType.templates?.[0]
    if (!template) return null

    // Map templates to layouts
    const templateLayoutMap: Record<string, string> = {
      'single-product': 'product-layout',
      'archive-product': 'product-archive-layout',
      'page-landing': 'landing-layout',
      'page-full-width': 'full-width-layout',
      'post-single': 'blog-layout'
    }

    const layoutName = templateLayoutMap[template]
    if (layoutName) {
      return await LayoutResolver.getLayoutByName(layoutName)
    }

    return null
  }

  // Get default layout for post type
  private static async getDefaultLayout(postTypeName: string): Promise<ResolvedLayout | null> {
    const defaultLayouts: Record<string, string> = {
      'page': 'default-page-layout',
      'post': 'default-blog-layout',
      'product': 'default-product-layout',
      'portfolio': 'default-portfolio-layout'
    }

    const layoutName = defaultLayouts[postTypeName] || 'default-layout'
    return await LayoutResolver.getLayoutByName(layoutName)
  }
}

// Responsive layout component
export function ResponsiveLayout({
  children,
  content,
  postType,
  className
}: {
  children: React.ReactNode
  content?: CMSContent
  postType?: PostType
  className?: string
}) {
  const { currentLayout, layoutMode, deviceType } = useLayout()
  const [resolvedLayout, setResolvedLayout] = useState<ResolvedLayout | null>(null)

  // Resolve layout when content changes
  useEffect(() => {
    if (content && postType && layoutMode === 'default') {
      DynamicLayoutResolver.resolveForContent(content, postType)
        .then(setResolvedLayout)
        .catch(console.error)
    } else {
      setResolvedLayout(currentLayout)
    }
  }, [content, postType, currentLayout, layoutMode])

  // Use resolved layout or fallback
  const layout = resolvedLayout || currentLayout

  if (layout) {
    return (
      <LayoutRenderer 
        layout={layout} 
        showDefaultHeader={false}
        className={className}
      >
        {children}
      </LayoutRenderer>
    )
  }

  // Fallback to default layout structure
  return (
    <DefaultLayoutStructure className={className}>
      {children}
    </DefaultLayoutStructure>
  )
}

// Default layout structure
function DefaultLayoutStructure({ 
  children, 
  className 
}: { 
  children: React.ReactNode
  className?: string 
}) {
  return (
    <div className={`min-h-screen flex flex-col ${className || ''}`}>
      <Header />
      <main className="flex-1">
        {children}
      </main>
      <Footer />
    </div>
  )
}

// Layout template system
export class LayoutTemplateSystem {
  private static templates: Map<string, LayoutTemplate> = new Map()

  // Register layout template
  static registerTemplate(template: LayoutTemplate) {
    this.templates.set(template.name, template)
  }

  // Get template
  static getTemplate(name: string): LayoutTemplate | null {
    return this.templates.get(name) || null
  }

  // Get all templates
  static getAllTemplates(): LayoutTemplate[] {
    return Array.from(this.templates.values())
  }

  // Apply template to content
  static applyTemplate(
    templateName: string,
    content: CMSContent,
    postType: PostType
  ): ResolvedLayout | null {
    const template = this.getTemplate(templateName)
    if (!template) return null

    return template.generate(content, postType)
  }
}

// Layout template interface
export interface LayoutTemplate {
  name: string
  label: string
  description: string
  postTypes: string[]
  preview?: string
  generate: (content: CMSContent, postType: PostType) => ResolvedLayout
}

// Predefined layout templates
export const DefaultLayoutTemplates: LayoutTemplate[] = [
  {
    name: 'default-page',
    label: 'Default Page',
    description: 'Standard page layout with header and footer',
    postTypes: ['page'],
    generate: (content, postType) => ({
      id: 'default-page-layout',
      name: 'Default Page Layout',
      layout: {
        structure: {
          header: {
            id: 'header',
            type: 'header',
            isVisible: true,
            blocks: [],
            configuration: { useContainer: true }
          },
          main: {
            id: 'main',
            type: 'main',
            isVisible: true,
            blocks: [],
            configuration: { useContainer: true }
          },
          footer: {
            id: 'footer',
            type: 'footer',
            isVisible: true,
            blocks: [],
            configuration: { useContainer: true }
          }
        },
        styling: {
          colors: {
            background: '#ffffff',
            text: '#000000'
          }
        }
      },
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    })
  },
  {
    name: 'full-width-page',
    label: 'Full Width Page',
    description: 'Full width page layout without container constraints',
    postTypes: ['page', 'landing'],
    generate: (content, postType) => ({
      id: 'full-width-layout',
      name: 'Full Width Layout',
      layout: {
        structure: {
          header: {
            id: 'header',
            type: 'header',
            isVisible: true,
            blocks: [],
            configuration: { useContainer: false }
          },
          main: {
            id: 'main',
            type: 'main',
            isVisible: true,
            blocks: [],
            configuration: { useContainer: false }
          },
          footer: {
            id: 'footer',
            type: 'footer',
            isVisible: true,
            blocks: [],
            configuration: { useContainer: false }
          }
        },
        styling: {
          colors: {
            background: '#ffffff',
            text: '#000000'
          }
        }
      },
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    })
  },
  {
    name: 'product-layout',
    label: 'Product Layout',
    description: 'E-commerce product page layout',
    postTypes: ['product'],
    generate: (content, postType) => ({
      id: 'product-layout',
      name: 'Product Layout',
      layout: {
        structure: {
          header: {
            id: 'header',
            type: 'header',
            isVisible: true,
            blocks: [],
            configuration: { useContainer: true }
          },
          main: {
            id: 'main',
            type: 'main',
            isVisible: true,
            blocks: [],
            configuration: { useContainer: true }
          },
          sidebar: {
            id: 'sidebar',
            type: 'sidebar',
            isVisible: true,
            blocks: [],
            configuration: { useContainer: false }
          },
          footer: {
            id: 'footer',
            type: 'footer',
            isVisible: true,
            blocks: [],
            configuration: { useContainer: true }
          }
        },
        styling: {
          colors: {
            background: '#ffffff',
            text: '#000000'
          }
        }
      },
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    })
  }
]

// Initialize default templates
DefaultLayoutTemplates.forEach(template => {
  LayoutTemplateSystem.registerTemplate(template)
})

// Layout utilities
export const LayoutUtils = {
  // Check if layout supports post type
  supportsPostType: (layout: ResolvedLayout, postTypeName: string): boolean => {
    // Implementation would check layout configuration
    return true // Simplified for now
  },

  // Get responsive layout for device
  getResponsiveLayout: (layout: ResolvedLayout, device: 'mobile' | 'tablet' | 'desktop'): any => {
    return layout.layout.responsive?.[device] || layout.layout
  },

  // Merge layouts
  mergeLayouts: (base: ResolvedLayout, override: Partial<ResolvedLayout>): ResolvedLayout => {
    return {
      ...base,
      ...override,
      layout: {
        ...base.layout,
        ...override.layout,
        structure: {
          ...base.layout.structure,
          ...override.layout?.structure
        }
      }
    }
  }
}

export default {
  LayoutProvider,
  useLayout,
  ResponsiveLayout,
  DynamicLayoutResolver,
  LayoutTemplateSystem,
  LayoutUtils
}
