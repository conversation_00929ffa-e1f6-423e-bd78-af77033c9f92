// Rendering System Index
// Exports all rendering components and utilities for CMS Builder integration

// Core rendering components
export { UniversalRenderer, UniversalRendererErrorBoundary } from './universal-renderer'
export { DynamicPageRenderer, StaticDynamicPageRenderer, ServerDynamicPageRenderer } from './dynamic-page-renderer'
export { PostTypeRenderer } from './post-type-renderer'
export { ArchiveRenderer } from './archive-renderer'

// SEO and metadata
export { SEOHead, useSEOData, ProductSEOHead, ArchiveSEOHead } from './seo-head'

// Performance monitoring
export { PerformanceMonitor, usePerformanceMetrics, PerformanceUtils } from './performance-monitor'

// Next.js integration
export {
  generateDynamicMetadata,
  generateStaticParams,
  generateSitemapData,
  revalidatePage,
  shouldHandleRoute,
  CacheUtils
} from './nextjs-integration'

// Routing modifiers
export {
  useDynamicRouting,
  RouteModifierService,
  URLBuilder,
  useBreadcrumbs,
  useRoutePreloader
} from './routing-modifiers'

// Layout system
export {
  LayoutProvider,
  useLayout,
  ResponsiveLayout,
  DynamicLayoutResolver,
  LayoutTemplateSystem,
  LayoutUtils,
  DefaultLayoutTemplates
} from './layout-system'

// Types
export type {
  DynamicRouteParams,
  DynamicSearchParams,
  StaticParamsResult
} from './nextjs-integration'

export type {
  RouteModifier,
  RouteCondition,
  DynamicRoute
} from './routing-modifiers'

export type {
  LayoutTemplate
} from './layout-system'

// Utility functions for common rendering tasks
export const RenderingUtils = {
  // Check if content should use dynamic rendering
  shouldUseDynamicRendering: (content: any): boolean => {
    return !!(content?.blocks && content.blocks.length > 0)
  },

  // Get cache key for content
  getCacheKey: (type: string, id: string, version?: string): string => {
    return `render:${type}:${id}:${version || 'latest'}`
  },

  // Generate page title
  generatePageTitle: (title: string, siteName?: string): string => {
    const site = siteName || process.env.NEXT_PUBLIC_SITE_NAME || 'CMS Site'
    return title === site ? title : `${title} | ${site}`
  },

  // Generate meta description
  generateMetaDescription: (content: string, maxLength = 160): string => {
    if (!content) return ''
    
    // Strip HTML tags
    const text = content.replace(/<[^>]*>/g, '')
    
    // Truncate to max length
    if (text.length <= maxLength) return text
    
    return text.substring(0, maxLength - 3).trim() + '...'
  },

  // Extract featured image from content
  extractFeaturedImage: (content: any): string | null => {
    // Check explicit featured image
    if (content.featuredImage) return content.featuredImage
    
    // Check first image block
    if (content.blocks) {
      const imageBlock = content.blocks.find((block: any) => 
        block.type === 'image' && block.content?.src
      )
      if (imageBlock) return imageBlock.content.src
    }
    
    // Check content HTML for images
    if (content.content) {
      const imgMatch = content.content.match(/<img[^>]+src="([^"]+)"/i)
      if (imgMatch) return imgMatch[1]
    }
    
    return null
  },

  // Generate structured data
  generateStructuredData: (content: any, type: string): any => {
    const baseData = {
      '@context': 'https://schema.org',
      '@type': type === 'product' ? 'Product' : 'Article',
      headline: content.title,
      description: content.excerpt || content.description,
      url: `${process.env.NEXT_PUBLIC_SITE_URL}/${content.slug}`,
      datePublished: content.publishedAt?.toISOString(),
      dateModified: content.updatedAt?.toISOString()
    }

    const featuredImage = RenderingUtils.extractFeaturedImage(content)
    if (featuredImage) {
      baseData.image = featuredImage
    }

    if (content.author) {
      baseData.author = {
        '@type': 'Person',
        name: content.author.name || content.author
      }
    }

    return baseData
  }
}

// Constants for rendering
export const RENDERING_CONSTANTS = {
  // Cache TTL values (in seconds)
  CACHE_TTL: {
    PAGE: 3600,      // 1 hour
    POST: 1800,      // 30 minutes
    ARCHIVE: 300,    // 5 minutes
    PREVIEW: 0       // No cache
  },

  // SEO defaults
  SEO_DEFAULTS: {
    TITLE_SUFFIX: process.env.NEXT_PUBLIC_SITE_NAME || 'CMS Site',
    DESCRIPTION_LENGTH: 160,
    TITLE_LENGTH: 60,
    OG_IMAGE_WIDTH: 1200,
    OG_IMAGE_HEIGHT: 630
  },

  // Performance thresholds
  PERFORMANCE_THRESHOLDS: {
    FCP: 1800,       // First Contentful Paint (ms)
    LCP: 2500,       // Largest Contentful Paint (ms)
    FID: 100,        // First Input Delay (ms)
    CLS: 0.1,        // Cumulative Layout Shift
    TTI: 3800        // Time to Interactive (ms)
  },

  // Device breakpoints
  BREAKPOINTS: {
    MOBILE: 768,
    TABLET: 1024,
    DESKTOP: 1200,
    LARGE: 1440
  },

  // Supported content types
  CONTENT_TYPES: [
    'page',
    'post',
    'product',
    'portfolio',
    'testimonial',
    'team',
    'event',
    'faq'
  ],

  // Layout types
  LAYOUT_TYPES: [
    'default',
    'full-width',
    'sidebar-left',
    'sidebar-right',
    'landing',
    'product',
    'archive'
  ]
}

// Error types for rendering
export class RenderingError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message)
    this.name = 'RenderingError'
  }
}

export class ContentNotFoundError extends RenderingError {
  constructor(contentId: string) {
    super(`Content not found: ${contentId}`, 'CONTENT_NOT_FOUND', 404)
  }
}

export class LayoutNotFoundError extends RenderingError {
  constructor(layoutId: string) {
    super(`Layout not found: ${layoutId}`, 'LAYOUT_NOT_FOUND', 404)
  }
}

export class TemplateNotFoundError extends RenderingError {
  constructor(templateName: string) {
    super(`Template not found: ${templateName}`, 'TEMPLATE_NOT_FOUND', 404)
  }
}

// Rendering hooks for extensibility
export const RenderingHooks = {
  // Before render hooks
  BEFORE_PAGE_RENDER: 'before_page_render',
  BEFORE_POST_RENDER: 'before_post_render',
  BEFORE_ARCHIVE_RENDER: 'before_archive_render',

  // After render hooks
  AFTER_PAGE_RENDER: 'after_page_render',
  AFTER_POST_RENDER: 'after_post_render',
  AFTER_ARCHIVE_RENDER: 'after_archive_render',

  // Content modification hooks
  MODIFY_CONTENT: 'modify_content',
  MODIFY_METADATA: 'modify_metadata',
  MODIFY_LAYOUT: 'modify_layout',

  // SEO hooks
  MODIFY_SEO_TITLE: 'modify_seo_title',
  MODIFY_SEO_DESCRIPTION: 'modify_seo_description',
  MODIFY_STRUCTURED_DATA: 'modify_structured_data'
}

// Default export
export default {
  UniversalRenderer,
  DynamicPageRenderer,
  SEOHead,
  PerformanceMonitor,
  LayoutProvider,
  RenderingUtils,
  RENDERING_CONSTANTS,
  RenderingHooks
}
