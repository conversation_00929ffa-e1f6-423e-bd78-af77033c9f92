import React from 'react'
import { ResolvedPost } from '@/lib/routing/route-resolver'
import { BlogPost } from '@/lib/posts/components/blog-post'
import { Post } from '@/lib/posts/types'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Calendar, User, Eye, Share2, Heart, MessageCircle } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface PostTypeRendererProps {
  post: ResolvedPost
  postType: string
}

export function PostTypeRenderer({ post, postType }: PostTypeRendererProps) {
  switch (postType) {
    case 'portfolio':
      return <PortfolioRenderer post={post} />
    case 'product':
      return <ProductPostRenderer post={post} />
    case 'testimonial':
      return <TestimonialRenderer post={post} />
    case 'team':
      return <TeamMemberRenderer post={post} />
    case 'event':
      return <EventRenderer post={post} />
    case 'faq':
      return <FAQRenderer post={post} />
    default:
      return <DefaultPostRenderer post={post} />
  }
}

// Portfolio Item Renderer
function PortfolioRenderer({ post }: { post: ResolvedPost }) {
  const customFields = post.customFields as any || {}
  const gallery = customFields.gallery || []
  const projectUrl = customFields.projectUrl
  const technologies = customFields.technologies || []
  const client = customFields.client

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">{post.title}</h1>
          {post.excerpt && (
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              {post.excerpt}
            </p>
          )}
          
          {/* Meta Information */}
          <div className="flex flex-wrap justify-center gap-4 mt-6">
            {client && (
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>Client: {client}</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>{formatDistanceToNow(new Date(post.publishedAt!), { addSuffix: true })}</span>
            </div>
          </div>

          {/* Technologies */}
          {technologies.length > 0 && (
            <div className="flex flex-wrap justify-center gap-2 mt-4">
              {technologies.map((tech: string, index: number) => (
                <Badge key={index} variant="secondary">{tech}</Badge>
              ))}
            </div>
          )}
        </div>

        {/* Featured Image */}
        {post.featuredImage && (
          <div className="mb-12">
            <img
              src={post.featuredImage}
              alt={post.featuredImageAlt || post.title}
              className="w-full h-auto rounded-lg shadow-lg"
            />
          </div>
        )}

        {/* Content */}
        <div className="prose prose-lg max-w-none mb-12">
          <div dangerouslySetInnerHTML={{ __html: post.contentHtml || post.content || '' }} />
        </div>

        {/* Gallery */}
        {gallery.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Project Gallery</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {gallery.map((image: string, index: number) => (
                <img
                  key={index}
                  src={image}
                  alt={`${post.title} - Image ${index + 1}`}
                  className="w-full h-64 object-cover rounded-lg"
                />
              ))}
            </div>
          </div>
        )}

        {/* Project Link */}
        {projectUrl && (
          <div className="text-center">
            <Button asChild size="lg">
              <a href={projectUrl} target="_blank" rel="noopener noreferrer">
                View Live Project
              </a>
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

// Product Post Renderer (different from e-commerce products)
function ProductPostRenderer({ post }: { post: ResolvedPost }) {
  const customFields = post.customFields as any || {}
  const price = customFields.price
  const features = customFields.features || []
  const specifications = customFields.specifications || {}

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Image */}
          <div>
            {post.featuredImage && (
              <img
                src={post.featuredImage}
                alt={post.featuredImageAlt || post.title}
                className="w-full h-auto rounded-lg"
              />
            )}
          </div>

          {/* Content */}
          <div>
            <h1 className="text-3xl font-bold mb-4">{post.title}</h1>
            
            {price && (
              <div className="text-2xl font-bold text-green-600 mb-4">
                R{price}
              </div>
            )}

            {post.excerpt && (
              <p className="text-lg text-muted-foreground mb-6">{post.excerpt}</p>
            )}

            {/* Features */}
            {features.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-3">Key Features</h3>
                <ul className="list-disc list-inside space-y-1">
                  {features.map((feature: string, index: number) => (
                    <li key={index}>{feature}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Specifications */}
            {Object.keys(specifications).length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-3">Specifications</h3>
                <div className="space-y-2">
                  {Object.entries(specifications).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="font-medium">{key}:</span>
                      <span>{value as string}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Full Description */}
        <div className="mt-12">
          <Separator className="mb-8" />
          <div className="prose max-w-none">
            <div dangerouslySetInnerHTML={{ __html: post.contentHtml || post.content || '' }} />
          </div>
        </div>
      </div>
    </div>
  )
}

// Testimonial Renderer
function TestimonialRenderer({ post }: { post: ResolvedPost }) {
  const customFields = post.customFields as any || {}
  const rating = customFields.rating || 5
  const company = customFields.company
  const position = customFields.position

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto text-center">
        <Card className="p-8">
          <CardHeader>
            {/* Rating */}
            <div className="flex justify-center mb-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <span
                  key={i}
                  className={`text-2xl ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}
                >
                  ★
                </span>
              ))}
            </div>
            
            <CardTitle className="text-2xl mb-4">{post.title}</CardTitle>
          </CardHeader>
          
          <CardContent>
            {/* Testimonial Content */}
            <blockquote className="text-lg italic mb-6">
              <div dangerouslySetInnerHTML={{ __html: post.contentHtml || post.content || '' }} />
            </blockquote>

            {/* Author Info */}
            <div className="flex items-center justify-center gap-4">
              {post.featuredImage && (
                <img
                  src={post.featuredImage}
                  alt={post.featuredImageAlt || post.title}
                  className="w-16 h-16 rounded-full object-cover"
                />
              )}
              <div className="text-left">
                <div className="font-semibold">{post.authorName || 'Anonymous'}</div>
                {position && <div className="text-sm text-muted-foreground">{position}</div>}
                {company && <div className="text-sm text-muted-foreground">{company}</div>}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Team Member Renderer
function TeamMemberRenderer({ post }: { post: ResolvedPost }) {
  const customFields = post.customFields as any || {}
  const position = customFields.position
  const department = customFields.department
  const socialLinks = customFields.socialLinks || {}
  const skills = customFields.skills || []

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Image */}
          <div className="text-center">
            {post.featuredImage && (
              <img
                src={post.featuredImage}
                alt={post.featuredImageAlt || post.title}
                className="w-64 h-64 rounded-full object-cover mx-auto mb-4"
              />
            )}
            <h1 className="text-2xl font-bold">{post.title}</h1>
            {position && <p className="text-lg text-muted-foreground">{position}</p>}
            {department && <p className="text-sm text-muted-foreground">{department}</p>}
          </div>

          {/* Bio and Details */}
          <div className="lg:col-span-2">
            {post.excerpt && (
              <p className="text-lg mb-6">{post.excerpt}</p>
            )}

            <div className="prose max-w-none mb-6">
              <div dangerouslySetInnerHTML={{ __html: post.contentHtml || post.content || '' }} />
            </div>

            {/* Skills */}
            {skills.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-3">Skills & Expertise</h3>
                <div className="flex flex-wrap gap-2">
                  {skills.map((skill: string, index: number) => (
                    <Badge key={index} variant="outline">{skill}</Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Social Links */}
            {Object.keys(socialLinks).length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-3">Connect</h3>
                <div className="flex gap-4">
                  {Object.entries(socialLinks).map(([platform, url]) => (
                    <Button key={platform} variant="outline" size="sm" asChild>
                      <a href={url as string} target="_blank" rel="noopener noreferrer">
                        {platform}
                      </a>
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// Event Renderer
function EventRenderer({ post }: { post: ResolvedPost }) {
  const customFields = post.customFields as any || {}
  const eventDate = customFields.eventDate
  const location = customFields.location
  const price = customFields.price
  const registrationUrl = customFields.registrationUrl

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-6">{post.title}</h1>
        
        {/* Event Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {eventDate && (
            <Card>
              <CardContent className="p-4 text-center">
                <Calendar className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                <div className="font-semibold">Date</div>
                <div className="text-sm text-muted-foreground">
                  {new Date(eventDate).toLocaleDateString()}
                </div>
              </CardContent>
            </Card>
          )}
          
          {location && (
            <Card>
              <CardContent className="p-4 text-center">
                <div className="font-semibold">Location</div>
                <div className="text-sm text-muted-foreground">{location}</div>
              </CardContent>
            </Card>
          )}
          
          {price && (
            <Card>
              <CardContent className="p-4 text-center">
                <div className="font-semibold">Price</div>
                <div className="text-sm text-muted-foreground">R{price}</div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Featured Image */}
        {post.featuredImage && (
          <img
            src={post.featuredImage}
            alt={post.featuredImageAlt || post.title}
            className="w-full h-64 object-cover rounded-lg mb-8"
          />
        )}

        {/* Content */}
        <div className="prose max-w-none mb-8">
          <div dangerouslySetInnerHTML={{ __html: post.contentHtml || post.content || '' }} />
        </div>

        {/* Registration */}
        {registrationUrl && (
          <div className="text-center">
            <Button size="lg" asChild>
              <a href={registrationUrl} target="_blank" rel="noopener noreferrer">
                Register Now
              </a>
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

// FAQ Renderer
function FAQRenderer({ post }: { post: ResolvedPost }) {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">{post.title}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose max-w-none">
              <div dangerouslySetInnerHTML={{ __html: post.contentHtml || post.content || '' }} />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Default Post Renderer (fallback)
function DefaultPostRenderer({ post }: { post: ResolvedPost }) {
  return (
    <div className="container mx-auto px-4 py-8">
      <BlogPost
        post={post as Post}
        showFullContent={true}
        showMeta={true}
        showActions={true}
        showComments={post.allowComments}
      />
    </div>
  )
}
