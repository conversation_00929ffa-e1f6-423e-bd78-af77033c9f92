'use client'

import React, { useState, useCallback, useMemo } from 'react'
import { Responsive, WidthProvider, Layout } from 'react-grid-layout'
import { usePageBuilder } from '../context'
import { BlockRenderer } from './block-renderer'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Plus, 
  Settings, 
  Trash2, 
  Copy, 
  Move, 
  Maximize2,
  Minimize2,
  Grid,
  Smartphone,
  Tablet,
  Monitor,
  Tv
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

// Make ResponsiveGridLayout responsive
const ResponsiveGridLayout = WidthProvider(Responsive)

interface GridLayoutBuilderProps {
  className?: string
  isPreviewMode?: boolean
  devicePreview?: 'mobile' | 'tablet' | 'desktop' | 'large'
}

interface GridBlock {
  id: string
  type: string
  content: any
  config: any
  layout: {
    i: string
    x: number
    y: number
    w: number
    h: number
    minW?: number
    minH?: number
    maxW?: number
    maxH?: number
    static?: boolean
    isDraggable?: boolean
    isResizable?: boolean
  }
}

export function GridLayoutBuilder({ 
  className, 
  isPreviewMode = false,
  devicePreview = 'desktop' 
}: GridLayoutBuilderProps) {
  const { state, addBlock, updateBlock, deleteBlock, selectBlock } = usePageBuilder()
  const [isDragging, setIsDragging] = useState(false)
  const [selectedBlockId, setSelectedBlockId] = useState<string | null>(null)

  // Convert page blocks to grid blocks
  const gridBlocks = useMemo(() => {
    return state.page.blocks.map((block, index) => ({
      id: block.id,
      type: block.type,
      content: block.content,
      config: block.config,
      layout: {
        i: block.id,
        x: block.gridLayout?.x ?? (index % 4) * 3,
        y: block.gridLayout?.y ?? Math.floor(index / 4) * 2,
        w: block.gridLayout?.w ?? 3,
        h: block.gridLayout?.h ?? 2,
        minW: block.gridLayout?.minW ?? 1,
        minH: block.gridLayout?.minH ?? 1,
        maxW: block.gridLayout?.maxW ?? 12,
        maxH: block.gridLayout?.maxH ?? 10,
        static: isPreviewMode,
        isDraggable: !isPreviewMode,
        isResizable: !isPreviewMode
      }
    }))
  }, [state.page.blocks, isPreviewMode])

  // Responsive breakpoints
  const breakpoints = {
    lg: 1200,
    md: 996,
    sm: 768,
    xs: 480,
    xxs: 0
  }

  // Columns for each breakpoint
  const cols = {
    lg: 12,
    md: 10,
    sm: 6,
    xs: 4,
    xxs: 2
  }

  // Generate layouts for all breakpoints
  const layouts = useMemo(() => {
    const baseLayout = gridBlocks.map(block => block.layout)
    
    return {
      lg: baseLayout,
      md: baseLayout.map(item => ({ ...item, w: Math.min(item.w, 10) })),
      sm: baseLayout.map(item => ({ ...item, w: Math.min(item.w, 6) })),
      xs: baseLayout.map(item => ({ ...item, w: Math.min(item.w, 4) })),
      xxs: baseLayout.map(item => ({ ...item, w: Math.min(item.w, 2) }))
    }
  }, [gridBlocks])

  // Handle layout change
  const handleLayoutChange = useCallback((layout: Layout[], layouts: any) => {
    if (isDragging) return

    // Update blocks with new layout positions
    layout.forEach(layoutItem => {
      const block = state.page.blocks.find(b => b.id === layoutItem.i)
      if (block) {
        updateBlock(block.id, {
          ...block,
          gridLayout: {
            x: layoutItem.x,
            y: layoutItem.y,
            w: layoutItem.w,
            h: layoutItem.h,
            minW: layoutItem.minW,
            minH: layoutItem.minH,
            maxW: layoutItem.maxW,
            maxH: layoutItem.maxH
          }
        })
      }
    })
  }, [isDragging, state.page.blocks, updateBlock])

  // Handle drag start
  const handleDragStart = useCallback(() => {
    setIsDragging(true)
  }, [])

  // Handle drag stop
  const handleDragStop = useCallback((layout: Layout[]) => {
    setIsDragging(false)
    handleLayoutChange(layout, {})
    toast.success('Layout updated')
  }, [handleLayoutChange])

  // Handle resize stop
  const handleResizeStop = useCallback((layout: Layout[]) => {
    handleLayoutChange(layout, {})
    toast.success('Block resized')
  }, [handleLayoutChange])

  // Add new block
  const handleAddBlock = useCallback((blockType: string) => {
    const newBlock = {
      id: `block-${Date.now()}`,
      type: blockType,
      content: {},
      config: {},
      gridLayout: {
        x: 0,
        y: 0,
        w: 3,
        h: 2,
        minW: 1,
        minH: 1
      }
    }

    addBlock(newBlock)
    toast.success(`${blockType} block added`)
  }, [addBlock])

  // Delete block
  const handleDeleteBlock = useCallback((blockId: string) => {
    deleteBlock(blockId)
    setSelectedBlockId(null)
    toast.success('Block deleted')
  }, [deleteBlock])

  // Duplicate block
  const handleDuplicateBlock = useCallback((blockId: string) => {
    const block = state.page.blocks.find(b => b.id === blockId)
    if (block) {
      const duplicatedBlock = {
        ...block,
        id: `block-${Date.now()}`,
        gridLayout: {
          ...block.gridLayout,
          x: (block.gridLayout?.x ?? 0) + (block.gridLayout?.w ?? 3),
          y: block.gridLayout?.y ?? 0
        }
      }
      addBlock(duplicatedBlock)
      toast.success('Block duplicated')
    }
  }, [state.page.blocks, addBlock])

  // Get device-specific row height
  const getRowHeight = () => {
    switch (devicePreview) {
      case 'mobile': return 60
      case 'tablet': return 80
      case 'desktop': return 100
      case 'large': return 120
      default: return 100
    }
  }

  // Get current breakpoint
  const getCurrentBreakpoint = () => {
    switch (devicePreview) {
      case 'mobile': return 'xs'
      case 'tablet': return 'sm'
      case 'desktop': return 'md'
      case 'large': return 'lg'
      default: return 'lg'
    }
  }

  return (
    <div className={cn('grid-layout-builder', className)}>
      {/* Toolbar */}
      {!isPreviewMode && (
        <div className="flex items-center justify-between p-4 border-b bg-background">
          <div className="flex items-center space-x-2">
            <Grid className="h-5 w-5 text-muted-foreground" />
            <span className="font-medium">Grid Layout</span>
            <Badge variant="outline">{gridBlocks.length} blocks</Badge>
          </div>

          <div className="flex items-center space-x-2">
            {/* Device Preview Icons */}
            <div className="flex items-center space-x-1 border rounded-md p-1">
              <Button
                variant={devicePreview === 'mobile' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => {}}
                className="h-8 w-8 p-0"
              >
                <Smartphone className="h-4 w-4" />
              </Button>
              <Button
                variant={devicePreview === 'tablet' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => {}}
                className="h-8 w-8 p-0"
              >
                <Tablet className="h-4 w-4" />
              </Button>
              <Button
                variant={devicePreview === 'desktop' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => {}}
                className="h-8 w-8 p-0"
              >
                <Monitor className="h-4 w-4" />
              </Button>
              <Button
                variant={devicePreview === 'large' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => {}}
                className="h-8 w-8 p-0"
              >
                <Tv className="h-4 w-4" />
              </Button>
            </div>

            {/* Add Block Button */}
            <Button
              onClick={() => handleAddBlock('text')}
              size="sm"
              className="flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Add Block</span>
            </Button>
          </div>
        </div>
      )}

      {/* Grid Layout */}
      <div className="flex-1 p-4">
        {gridBlocks.length === 0 ? (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Grid className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Empty Grid</h3>
              <p className="text-muted-foreground mb-4">
                Start building your layout by adding blocks
              </p>
              <Button onClick={() => handleAddBlock('text')}>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Block
              </Button>
            </div>
          </div>
        ) : (
          <ResponsiveGridLayout
            className="layout"
            layouts={layouts}
            breakpoints={breakpoints}
            cols={cols}
            rowHeight={getRowHeight()}
            onLayoutChange={handleLayoutChange}
            onDragStart={handleDragStart}
            onDragStop={handleDragStop}
            onResizeStop={handleResizeStop}
            isDraggable={!isPreviewMode}
            isResizable={!isPreviewMode}
            margin={[16, 16]}
            containerPadding={[0, 0]}
            useCSSTransforms={true}
            preventCollision={false}
            compactType="vertical"
          >
            {gridBlocks.map((block) => (
              <div key={block.id} className="grid-item">
                <GridBlockWrapper
                  block={block}
                  isSelected={selectedBlockId === block.id}
                  isPreviewMode={isPreviewMode}
                  onSelect={() => setSelectedBlockId(block.id)}
                  onDelete={() => handleDeleteBlock(block.id)}
                  onDuplicate={() => handleDuplicateBlock(block.id)}
                  onConfigure={() => selectBlock(block.id)}
                />
              </div>
            ))}
          </ResponsiveGridLayout>
        )}
      </div>
    </div>
  )
}

interface GridBlockWrapperProps {
  block: GridBlock
  isSelected: boolean
  isPreviewMode: boolean
  onSelect: () => void
  onDelete: () => void
  onDuplicate: () => void
  onConfigure: () => void
}

function GridBlockWrapper({
  block,
  isSelected,
  isPreviewMode,
  onSelect,
  onDelete,
  onDuplicate,
  onConfigure
}: GridBlockWrapperProps) {
  return (
    <Card 
      className={cn(
        'h-full relative group cursor-pointer transition-all',
        isSelected && 'ring-2 ring-primary',
        !isPreviewMode && 'hover:shadow-md'
      )}
      onClick={onSelect}
    >
      {/* Block Controls */}
      {!isPreviewMode && (
        <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="flex items-center space-x-1 bg-background border rounded-md p-1 shadow-sm">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onConfigure()
              }}
              className="h-6 w-6 p-0"
            >
              <Settings className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onDuplicate()
              }}
              className="h-6 w-6 p-0"
            >
              <Copy className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onDelete()
              }}
              className="h-6 w-6 p-0 text-destructive hover:text-destructive"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}

      {/* Block Type Badge */}
      {!isPreviewMode && (
        <div className="absolute top-2 left-2 z-10">
          <Badge variant="secondary" className="text-xs">
            {block.type}
          </Badge>
        </div>
      )}

      {/* Block Content */}
      <CardContent className="p-4 h-full">
        <BlockRenderer
          block={{
            id: block.id,
            type: block.type,
            content: block.content,
            config: block.config
          }}
          isPreviewMode={isPreviewMode}
        />
      </CardContent>
    </Card>
  )
}
