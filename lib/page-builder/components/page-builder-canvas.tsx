'use client'

import React, { useCallback } from 'react'
import { usePageBuilder } from '../context'
import { BlockRenderer } from './block-renderer'
import { DropZone } from './drop-zone'
import { EmptyCanvas } from './empty-canvas'
import { cn } from '@/lib/utils'

interface PageBuilderCanvasProps {
  devicePreview: 'desktop' | 'tablet' | 'mobile'
  isPreviewMode: boolean
  className?: string
}

export function PageBuilderCanvas({ 
  devicePreview, 
  isPreviewMode, 
  className 
}: PageBuilderCanvasProps) {
  const { state, moveBlock, selectBlock } = usePageBuilder()
  const { page } = state

  // Get canvas width based on device preview
  const getCanvasWidth = () => {
    switch (devicePreview) {
      case 'mobile':
        return 'max-w-sm'
      case 'tablet':
        return 'max-w-2xl'
      default:
        return 'max-w-full'
    }
  }

  // Handle block drag and drop
  const handleBlockMove = useCallback((dragIndex: number, hoverIndex: number) => {
    const draggedBlock = page.blocks[dragIndex]
    if (draggedBlock) {
      moveBlock(draggedBlock.id, hoverIndex)
    }
  }, [page.blocks, moveBlock])

  // Handle canvas click (deselect blocks)
  const handleCanvasClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && !isPreviewMode) {
      selectBlock(null)
    }
  }

  // Sort blocks by position
  const sortedBlocks = [...page.blocks].sort((a, b) => a.position - b.position)

  return (
    <div 
      className={cn(
        'min-h-full bg-white transition-all duration-300',
        className
      )}
      onClick={handleCanvasClick}
    >
      {/* Canvas Container */}
      <div className={cn(
        'mx-auto transition-all duration-300',
        getCanvasWidth(),
        {
          'px-4': devicePreview === 'mobile',
          'px-6': devicePreview === 'tablet',
          'px-8': devicePreview === 'desktop',
        }
      )}>
        {/* Device Frame (for mobile/tablet preview) */}
        {(devicePreview === 'mobile' || devicePreview === 'tablet') && !isPreviewMode && (
          <div className="py-4">
            <div className={cn(
              'mx-auto border-2 border-gray-300 rounded-lg overflow-hidden shadow-lg bg-white',
              {
                'w-[375px] min-h-[667px]': devicePreview === 'mobile',
                'w-[768px] min-h-[1024px]': devicePreview === 'tablet',
              }
            )}>
              <CanvasContent 
                blocks={sortedBlocks}
                isPreviewMode={isPreviewMode}
                onBlockMove={handleBlockMove}
              />
            </div>
          </div>
        )}

        {/* Full Width Canvas (desktop or preview mode) */}
        {(devicePreview === 'desktop' || isPreviewMode) && (
          <CanvasContent 
            blocks={sortedBlocks}
            isPreviewMode={isPreviewMode}
            onBlockMove={handleBlockMove}
          />
        )}
      </div>
    </div>
  )
}

// Canvas Content Component
interface CanvasContentProps {
  blocks: any[]
  isPreviewMode: boolean
  onBlockMove: (dragIndex: number, hoverIndex: number) => void
}

function CanvasContent({ blocks, isPreviewMode, onBlockMove }: CanvasContentProps) {
  if (blocks.length === 0) {
    return <EmptyCanvas isPreviewMode={isPreviewMode} />
  }

  return (
    <div className="relative">
      {/* Drop zone at the top */}
      {!isPreviewMode && (
        <DropZone 
          position={0} 
          isFirst={true}
          className="mb-2"
        />
      )}

      {/* Render blocks */}
      {blocks.map((block, index) => (
        <div key={block.id} className="relative">
          <BlockRenderer
            block={block}
            index={index}
            isEditing={!isPreviewMode}
            onMove={onBlockMove}
          />
          
          {/* Drop zone between blocks */}
          {!isPreviewMode && index < blocks.length - 1 && (
            <DropZone 
              position={index + 1}
              className="my-2"
            />
          )}
        </div>
      ))}

      {/* Drop zone at the bottom */}
      {!isPreviewMode && (
        <DropZone 
          position={blocks.length} 
          isLast={true}
          className="mt-2"
        />
      )}
    </div>
  )
}

// Canvas Overlay for editing mode
interface CanvasOverlayProps {
  isVisible: boolean
  children: React.ReactNode
}

function CanvasOverlay({ isVisible, children }: CanvasOverlayProps) {
  if (!isVisible) return null

  return (
    <div className="absolute inset-0 bg-blue-500 bg-opacity-10 border-2 border-blue-500 border-dashed rounded-lg pointer-events-none">
      <div className="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
        {children}
      </div>
    </div>
  )
}

// Canvas Grid (for alignment guides)
interface CanvasGridProps {
  isVisible: boolean
}

function CanvasGrid({ isVisible }: CanvasGridProps) {
  if (!isVisible) return null

  return (
    <div 
      className="absolute inset-0 pointer-events-none opacity-20"
      style={{
        backgroundImage: `
          linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)
        `,
        backgroundSize: '20px 20px'
      }}
    />
  )
}

// Canvas Ruler (for precise positioning)
interface CanvasRulerProps {
  isVisible: boolean
  orientation: 'horizontal' | 'vertical'
}

function CanvasRuler({ isVisible, orientation }: CanvasRulerProps) {
  if (!isVisible) return null

  const isHorizontal = orientation === 'horizontal'

  return (
    <div className={cn(
      'absolute bg-gray-100 border-gray-300',
      {
        'top-0 left-0 right-0 h-6 border-b': isHorizontal,
        'top-0 left-0 bottom-0 w-6 border-r': !isHorizontal,
      }
    )}>
      {/* Ruler markings would go here */}
    </div>
  )
}

// Canvas Zoom Controls
interface CanvasZoomControlsProps {
  zoom: number
  onZoomChange: (zoom: number) => void
  className?: string
}

function CanvasZoomControls({ zoom, onZoomChange, className }: CanvasZoomControlsProps) {
  const zoomLevels = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 2]

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <button
        onClick={() => {
          const currentIndex = zoomLevels.indexOf(zoom)
          if (currentIndex > 0) {
            onZoomChange(zoomLevels[currentIndex - 1])
          }
        }}
        disabled={zoom <= zoomLevels[0]}
        className="px-2 py-1 text-sm border rounded disabled:opacity-50"
      >
        -
      </button>
      
      <span className="text-sm font-medium min-w-[60px] text-center">
        {Math.round(zoom * 100)}%
      </span>
      
      <button
        onClick={() => {
          const currentIndex = zoomLevels.indexOf(zoom)
          if (currentIndex < zoomLevels.length - 1) {
            onZoomChange(zoomLevels[currentIndex + 1])
          }
        }}
        disabled={zoom >= zoomLevels[zoomLevels.length - 1]}
        className="px-2 py-1 text-sm border rounded disabled:opacity-50"
      >
        +
      </button>
      
      <button
        onClick={() => onZoomChange(1)}
        className="px-2 py-1 text-sm border rounded"
      >
        Reset
      </button>
    </div>
  )
}

// Canvas Status Bar
interface CanvasStatusBarProps {
  blockCount: number
  selectedBlockType?: string
  hasUnsavedChanges: boolean
}

function CanvasStatusBar({ 
  blockCount, 
  selectedBlockType, 
  hasUnsavedChanges 
}: CanvasStatusBarProps) {
  return (
    <div className="flex items-center justify-between px-4 py-2 bg-gray-50 border-t text-sm text-muted-foreground">
      <div className="flex items-center space-x-4">
        <span>{blockCount} blocks</span>
        {selectedBlockType && (
          <span>Selected: {selectedBlockType}</span>
        )}
      </div>
      
      <div className="flex items-center space-x-2">
        {hasUnsavedChanges && (
          <span className="text-orange-600">Unsaved changes</span>
        )}
      </div>
    </div>
  )
}

export { CanvasOverlay, CanvasGrid, CanvasRuler, CanvasZoomControls, CanvasStatusBar }
