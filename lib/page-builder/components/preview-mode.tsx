'use client'

import React, { useState, useEffect } from 'react'
import { usePageBuilder } from '../context'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  Monitor, 
  Tablet, 
  Smartphone, 
  Eye, 
  EyeOff, 
  RotateCcw,
  ExternalLink,
  Share2
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { PageRenderer } from './page-renderer'

interface PreviewModeProps {
  className?: string
}

export function PreviewMode({ className }: PreviewModeProps) {
  const { state, exitPreviewMode } = usePageBuilder()
  const [activeDevice, setActiveDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [showGrid, setShowGrid] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)

  // Handle escape key to exit preview
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        exitPreviewMode()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [exitPreviewMode])

  const getDeviceClasses = () => {
    switch (activeDevice) {
      case 'mobile':
        return 'w-[375px] h-[667px]' // iPhone SE dimensions
      case 'tablet':
        return 'w-[768px] h-[1024px]' // iPad dimensions
      default:
        return 'w-full h-full min-h-[800px]' // Desktop
    }
  }

  const getDeviceScale = () => {
    if (isFullscreen) return 'scale-100'
    
    switch (activeDevice) {
      case 'mobile':
        return 'scale-75'
      case 'tablet':
        return 'scale-80'
      default:
        return 'scale-100'
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: state.page.title || 'Page Preview',
          text: 'Check out this page preview',
          url: window.location.href
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      // You could show a toast notification here
    }
  }

  const handleExternalPreview = () => {
    // Open preview in new tab/window
    const previewUrl = `/preview/${state.page.id}`
    window.open(previewUrl, '_blank')
  }

  return (
    <div className={cn('fixed inset-0 z-50 bg-background', className)}>
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={exitPreviewMode}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Back to Editor
            </Button>
            
            <div className="text-sm text-muted-foreground">
              Previewing: <span className="font-medium">{state.page.title || 'Untitled Page'}</span>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Device Selector */}
            <Tabs value={activeDevice} onValueChange={(value) => setActiveDevice(value as any)}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="desktop" className="flex items-center space-x-1">
                  <Monitor className="h-4 w-4" />
                  <span className="hidden sm:inline">Desktop</span>
                </TabsTrigger>
                <TabsTrigger value="tablet" className="flex items-center space-x-1">
                  <Tablet className="h-4 w-4" />
                  <span className="hidden sm:inline">Tablet</span>
                </TabsTrigger>
                <TabsTrigger value="mobile" className="flex items-center space-x-1">
                  <Smartphone className="h-4 w-4" />
                  <span className="hidden sm:inline">Mobile</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Preview Options */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowGrid(!showGrid)}
              className={cn(showGrid && 'bg-primary text-primary-foreground')}
            >
              {showGrid ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              <span className="hidden sm:inline ml-2">Grid</span>
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsFullscreen(!isFullscreen)}
            >
              <ExternalLink className="h-4 w-4" />
              <span className="hidden sm:inline ml-2">
                {isFullscreen ? 'Fit' : 'Fullscreen'}
              </span>
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleExternalPreview}
            >
              <ExternalLink className="h-4 w-4" />
              <span className="hidden sm:inline ml-2">Open in New Tab</span>
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleShare}
            >
              <Share2 className="h-4 w-4" />
              <span className="hidden sm:inline ml-2">Share</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 overflow-auto bg-gray-100">
        <div className="flex items-center justify-center min-h-full p-8">
          <div className={cn(
            'relative transition-all duration-300 ease-in-out',
            getDeviceClasses(),
            getDeviceScale(),
            activeDevice !== 'desktop' && 'border border-gray-300 rounded-lg shadow-xl bg-white'
          )}>
            {/* Device Frame (for mobile/tablet) */}
            {activeDevice !== 'desktop' && (
              <>
                {/* Device Header */}
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground">
                  {activeDevice === 'mobile' ? 'iPhone SE (375×667)' : 'iPad (768×1024)'}
                </div>
                
                {/* Device Notch/Camera (for mobile) */}
                {activeDevice === 'mobile' && (
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-black rounded-b-xl z-10" />
                )}
              </>
            )}

            {/* Grid Overlay */}
            {showGrid && (
              <div className="absolute inset-0 z-10 pointer-events-none">
                <div 
                  className="w-full h-full opacity-20"
                  style={{
                    backgroundImage: `
                      linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)
                    `,
                    backgroundSize: '20px 20px'
                  }}
                />
              </div>
            )}

            {/* Page Content */}
            <div className={cn(
              'w-full h-full overflow-auto',
              activeDevice !== 'desktop' && 'rounded-lg'
            )}>
              <PageRenderer 
                page={state.page} 
                isPreview={true}
                deviceType={activeDevice}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Footer Info */}
      <div className="border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-2">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-4">
            <span>Blocks: {state.page.blocks.length}</span>
            <span>Device: {activeDevice}</span>
            <span>
              Viewport: {activeDevice === 'mobile' ? '375×667' : activeDevice === 'tablet' ? '768×1024' : 'Full Width'}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span>Press ESC to exit preview</span>
          </div>
        </div>
      </div>
    </div>
  )
}

// Device-specific CSS classes for responsive preview
export const deviceStyles = {
  mobile: {
    fontSize: '14px',
    lineHeight: '1.4',
  },
  tablet: {
    fontSize: '16px',
    lineHeight: '1.5',
  },
  desktop: {
    fontSize: '16px',
    lineHeight: '1.6',
  },
}

// Utility function to get device-specific styles
export function getDeviceStyles(device: 'mobile' | 'tablet' | 'desktop') {
  return deviceStyles[device]
}

// Hook for responsive preview
export function useResponsivePreview() {
  const [device, setDevice] = useState<'mobile' | 'tablet' | 'desktop'>('desktop')
  
  const getBreakpointClasses = () => {
    switch (device) {
      case 'mobile':
        return 'max-w-sm'
      case 'tablet':
        return 'max-w-3xl'
      default:
        return 'max-w-full'
    }
  }

  const isDevice = (targetDevice: 'mobile' | 'tablet' | 'desktop') => {
    return device === targetDevice
  }

  return {
    device,
    setDevice,
    getBreakpointClasses,
    isDevice,
    isMobile: device === 'mobile',
    isTablet: device === 'tablet',
    isDesktop: device === 'desktop',
  }
}
