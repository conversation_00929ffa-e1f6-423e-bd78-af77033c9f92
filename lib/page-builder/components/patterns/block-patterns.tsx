'use client'

import React, { useState, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Search,
  Grid3X3,
  Star,
  Clock,
  Layers,
  Zap,
  Eye,
  Plus
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface BlockPattern {
  id: string
  name: string
  description: string
  category: string
  tags: string[]
  preview: string
  blocks: any[]
  featured?: boolean
  premium?: boolean
}

interface BlockPatternsProps {
  onInsertPattern: (pattern: BlockPattern) => void
  trigger?: React.ReactNode
  className?: string
}

export function BlockPatterns({
  onInsertPattern,
  trigger,
  className
}: BlockPatternsProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [activeCategory, setActiveCategory] = useState('all')
  const [selectedPattern, setSelectedPattern] = useState<BlockPattern | null>(null)

  // Sample patterns - in real app, these would come from API/database
  const patterns = useMemo(() => SAMPLE_PATTERNS, [])

  // Filter patterns
  const filteredPatterns = useMemo(() => {
    let filtered = patterns

    if (searchQuery) {
      filtered = filtered.filter(pattern =>
        pattern.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        pattern.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        pattern.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    if (activeCategory !== 'all') {
      if (activeCategory === 'featured') {
        filtered = filtered.filter(pattern => pattern.featured)
      } else {
        filtered = filtered.filter(pattern => pattern.category === activeCategory)
      }
    }

    return filtered
  }, [patterns, searchQuery, activeCategory])

  // Get categories with counts
  const categories = useMemo(() => {
    const categoryMap = new Map<string, number>()
    
    patterns.forEach(pattern => {
      const count = categoryMap.get(pattern.category) || 0
      categoryMap.set(pattern.category, count + 1)
    })

    return [
      { id: 'all', label: 'All Patterns', count: patterns.length },
      { id: 'featured', label: 'Featured', count: patterns.filter(p => p.featured).length },
      { id: 'hero', label: 'Hero Sections', count: categoryMap.get('hero') || 0 },
      { id: 'content', label: 'Content', count: categoryMap.get('content') || 0 },
      { id: 'testimonials', label: 'Testimonials', count: categoryMap.get('testimonials') || 0 },
      { id: 'cta', label: 'Call to Action', count: categoryMap.get('cta') || 0 },
      { id: 'footer', label: 'Footers', count: categoryMap.get('footer') || 0 }
    ].filter(cat => cat.count > 0)
  }, [patterns])

  const handleInsertPattern = (pattern: BlockPattern) => {
    onInsertPattern(pattern)
    setIsOpen(false)
    setSelectedPattern(null)
  }

  const defaultTrigger = (
    <Button variant="outline">
      <Grid3X3 className="h-4 w-4 mr-2" />
      Browse Patterns
    </Button>
  )

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-6xl h-[80vh] p-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="flex items-center gap-2">
            <Grid3X3 className="h-5 w-5" />
            Block Patterns
          </DialogTitle>
        </DialogHeader>

        <div className="flex h-full">
          {/* Sidebar */}
          <div className="w-80 border-r bg-gray-50 p-4">
            {/* Search */}
            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search patterns..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Categories */}
            <div className="space-y-1">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Categories</h3>
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={cn(
                    'w-full flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-colors',
                    activeCategory === category.id
                      ? 'bg-blue-100 text-blue-700'
                      : 'hover:bg-gray-100'
                  )}
                >
                  <span>{category.label}</span>
                  <Badge variant="secondary" className="text-xs">
                    {category.count}
                  </Badge>
                </button>
              ))}
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex">
            {/* Pattern Grid */}
            <div className="flex-1 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  {activeCategory === 'all' ? 'All Patterns' : 
                   categories.find(c => c.id === activeCategory)?.label}
                </h3>
                <span className="text-sm text-gray-500">
                  {filteredPatterns.length} pattern{filteredPatterns.length !== 1 ? 's' : ''}
                </span>
              </div>

              <ScrollArea className="h-[calc(100vh-200px)]">
                {filteredPatterns.length > 0 ? (
                  <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
                    {filteredPatterns.map((pattern) => (
                      <PatternCard
                        key={pattern.id}
                        pattern={pattern}
                        onSelect={() => setSelectedPattern(pattern)}
                        onInsert={() => handleInsertPattern(pattern)}
                        isSelected={selectedPattern?.id === pattern.id}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Search className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No patterns found
                    </h3>
                    <p className="text-gray-500">
                      Try adjusting your search or browse different categories
                    </p>
                  </div>
                )}
              </ScrollArea>
            </div>

            {/* Preview Panel */}
            {selectedPattern && (
              <div className="w-80 border-l bg-gray-50 p-4">
                <PatternPreview
                  pattern={selectedPattern}
                  onInsert={() => handleInsertPattern(selectedPattern)}
                  onClose={() => setSelectedPattern(null)}
                />
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Pattern card component
interface PatternCardProps {
  pattern: BlockPattern
  onSelect: () => void
  onInsert: () => void
  isSelected: boolean
}

function PatternCard({ pattern, onSelect, onInsert, isSelected }: PatternCardProps) {
  return (
    <div
      className={cn(
        'group relative border rounded-lg overflow-hidden cursor-pointer transition-all',
        isSelected ? 'ring-2 ring-blue-500' : 'hover:shadow-md'
      )}
      onClick={onSelect}
    >
      {/* Preview Image */}
      <div className="aspect-[4/3] bg-gray-100 relative overflow-hidden">
        <img
          src={pattern.preview}
          alt={pattern.name}
          className="w-full h-full object-cover"
        />
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity flex gap-2">
            <Button size="sm" variant="secondary" onClick={(e) => {
              e.stopPropagation()
              onSelect()
            }}>
              <Eye className="h-4 w-4 mr-1" />
              Preview
            </Button>
            <Button size="sm" onClick={(e) => {
              e.stopPropagation()
              onInsert()
            }}>
              <Plus className="h-4 w-4 mr-1" />
              Insert
            </Button>
          </div>
        </div>

        {/* Badges */}
        <div className="absolute top-2 left-2 flex gap-1">
          {pattern.featured && (
            <Badge className="bg-yellow-500 text-white">
              <Star className="h-3 w-3 mr-1" />
              Featured
            </Badge>
          )}
          {pattern.premium && (
            <Badge className="bg-purple-500 text-white">
              <Zap className="h-3 w-3 mr-1" />
              Premium
            </Badge>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-3">
        <h4 className="font-medium text-sm mb-1">{pattern.name}</h4>
        <p className="text-xs text-gray-600 mb-2 line-clamp-2">
          {pattern.description}
        </p>
        
        {/* Tags */}
        <div className="flex flex-wrap gap-1">
          {pattern.tags.slice(0, 2).map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs px-1 py-0">
              {tag}
            </Badge>
          ))}
          {pattern.tags.length > 2 && (
            <Badge variant="outline" className="text-xs px-1 py-0">
              +{pattern.tags.length - 2}
            </Badge>
          )}
        </div>
      </div>
    </div>
  )
}

// Pattern preview component
interface PatternPreviewProps {
  pattern: BlockPattern
  onInsert: () => void
  onClose: () => void
}

function PatternPreview({ pattern, onInsert, onClose }: PatternPreviewProps) {
  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold">{pattern.name}</h3>
        <Button variant="ghost" size="sm" onClick={onClose}>
          ×
        </Button>
      </div>

      <div className="flex-1 space-y-4">
        {/* Preview */}
        <div className="aspect-[4/3] border rounded-lg overflow-hidden">
          <img
            src={pattern.preview}
            alt={pattern.name}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Details */}
        <div className="space-y-3">
          <p className="text-sm text-gray-600">{pattern.description}</p>
          
          <div>
            <h4 className="text-sm font-medium mb-2">Includes:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              {pattern.blocks.map((block, index) => (
                <li key={index} className="flex items-center gap-2">
                  <span className="w-1 h-1 bg-gray-400 rounded-full" />
                  {block.type} block
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">Tags:</h4>
            <div className="flex flex-wrap gap-1">
              {pattern.tags.map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="pt-4 border-t">
        <Button onClick={onInsert} className="w-full">
          <Plus className="h-4 w-4 mr-2" />
          Insert Pattern
        </Button>
      </div>
    </div>
  )
}

// Sample patterns data
const SAMPLE_PATTERNS: BlockPattern[] = [
  {
    id: '1',
    name: 'Hero with CTA',
    description: 'A compelling hero section with headline, description, and call-to-action button',
    category: 'hero',
    tags: ['hero', 'cta', 'landing'],
    preview: '/api/placeholder/400/300',
    featured: true,
    blocks: [
      { type: 'hero', configuration: {} }
    ]
  },
  {
    id: '2',
    name: 'Feature Grid',
    description: 'Three-column feature grid with icons and descriptions',
    category: 'content',
    tags: ['features', 'grid', 'icons'],
    preview: '/api/placeholder/400/300',
    blocks: [
      { type: 'grid', configuration: {} }
    ]
  },
  {
    id: '3',
    name: 'Testimonial Carousel',
    description: 'Customer testimonials in an elegant carousel layout',
    category: 'testimonials',
    tags: ['testimonials', 'carousel', 'social-proof'],
    preview: '/api/placeholder/400/300',
    featured: true,
    blocks: [
      { type: 'testimonials', configuration: {} }
    ]
  },
  {
    id: '4',
    name: 'Newsletter Signup',
    description: 'Email capture form with compelling copy and design',
    category: 'cta',
    tags: ['newsletter', 'email', 'conversion'],
    preview: '/api/placeholder/400/300',
    blocks: [
      { type: 'newsletter', configuration: {} }
    ]
  }
]
