'use client'

import React, { useState, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Plus,
  Search,
  Type,
  Image,
  Layout,
  ShoppingCart,
  Megaphone,
  Layers,
  Star,
  Clock
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { blockRegistry } from '../../blocks/registry'
import { BlockCategory } from '../../types'

interface GutenbergBlockInserterProps {
  onInsertBlock: (blockType: string) => void
  trigger?: React.ReactNode
  align?: 'start' | 'center' | 'end'
  side?: 'top' | 'right' | 'bottom' | 'left'
  className?: string
}

export function G<PERSON>nbergBlockInserter({
  onInsertBlock,
  trigger,
  align = 'start',
  side = 'bottom',
  className
}: GutenbergBlockInserterProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [activeCategory, setActiveCategory] = useState<string>('all')
  const [recentBlocks, setRecentBlocks] = useState<string[]>(['text', 'image', 'button'])

  // Get all available blocks
  const allBlocks = blockRegistry.getAllBlockTypes()
  
  // Filter blocks based on search and category
  const filteredBlocks = useMemo(() => {
    let blocks = allBlocks

    // Filter by search query
    if (searchQuery) {
      blocks = blocks.filter(block =>
        block.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        block.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        block.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    // Filter by category
    if (activeCategory !== 'all') {
      if (activeCategory === 'recent') {
        blocks = blocks.filter(block => recentBlocks.includes(block.name))
      } else {
        blocks = blocks.filter(block => block.category === activeCategory)
      }
    }

    return blocks
  }, [allBlocks, searchQuery, activeCategory, recentBlocks])

  // Get categories with counts
  const categories = useMemo(() => {
    const categoryMap = new Map<string, number>()
    
    // Count blocks per category
    allBlocks.forEach(block => {
      const count = categoryMap.get(block.category) || 0
      categoryMap.set(block.category, count + 1)
    })

    const categoryInfo = [
      { id: 'all', label: 'All', icon: Layers, count: allBlocks.length },
      { id: 'recent', label: 'Recent', icon: Clock, count: recentBlocks.length },
      { id: 'content', label: 'Content', icon: Type, count: categoryMap.get('content') || 0 },
      { id: 'media', label: 'Media', icon: Image, count: categoryMap.get('media') || 0 },
      { id: 'layout', label: 'Layout', icon: Layout, count: categoryMap.get('layout') || 0 },
      { id: 'ecommerce', label: 'E-commerce', icon: ShoppingCart, count: categoryMap.get('ecommerce') || 0 },
      { id: 'marketing', label: 'Marketing', icon: Megaphone, count: categoryMap.get('marketing') || 0 }
    ]

    return categoryInfo.filter(cat => cat.count > 0)
  }, [allBlocks, recentBlocks])

  const handleInsertBlock = (blockType: string) => {
    onInsertBlock(blockType)
    
    // Add to recent blocks
    setRecentBlocks(prev => {
      const updated = [blockType, ...prev.filter(b => b !== blockType)]
      return updated.slice(0, 6) // Keep only 6 recent blocks
    })
    
    setIsOpen(false)
    setSearchQuery('')
  }

  const defaultTrigger = (
    <Button variant="outline" className="border-dashed">
      <Plus className="h-4 w-4 mr-2" />
      Add Block
    </Button>
  )

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        {trigger || defaultTrigger}
      </PopoverTrigger>
      <PopoverContent 
        className={cn("w-96 p-0", className)}
        align={align}
        side={side}
        sideOffset={8}
      >
        {/* Header */}
        <div className="p-4 border-b bg-gray-50">
          <h3 className="font-semibold text-sm mb-3">Add a block</h3>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search blocks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 text-sm"
            />
          </div>
        </div>

        {/* Categories and Blocks */}
        <Tabs value={activeCategory} onValueChange={setActiveCategory} className="w-full">
          {/* Category Tabs */}
          <div className="border-b">
            <ScrollArea className="w-full">
              <TabsList className="w-full justify-start h-auto p-2 bg-transparent">
                {categories.map((category) => (
                  <TabsTrigger
                    key={category.id}
                    value={category.id}
                    className="text-xs px-3 py-2 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700"
                  >
                    <category.icon className="h-3 w-3 mr-1" />
                    {category.label}
                    <Badge variant="secondary" className="ml-1 text-xs px-1">
                      {category.count}
                    </Badge>
                  </TabsTrigger>
                ))}
              </TabsList>
            </ScrollArea>
          </div>

          {/* Block Grid */}
          <ScrollArea className="h-80">
            <div className="p-4">
              {filteredBlocks.length > 0 ? (
                <div className="grid grid-cols-2 gap-2">
                  {filteredBlocks.map((block) => (
                    <BlockCard
                      key={block.name}
                      block={block}
                      onClick={() => handleInsertBlock(block.name)}
                      isRecent={recentBlocks.includes(block.name)}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No blocks found</p>
                  {searchQuery && (
                    <p className="text-xs mt-1">
                      Try searching for "{searchQuery}" with different terms
                    </p>
                  )}
                </div>
              )}
            </div>
          </ScrollArea>
        </Tabs>

        {/* Footer */}
        <div className="p-3 border-t bg-gray-50 text-xs text-gray-600">
          <p>💡 Tip: Use keyboard shortcuts to quickly add blocks</p>
        </div>
      </PopoverContent>
    </Popover>
  )
}

// Individual block card component
interface BlockCardProps {
  block: any
  onClick: () => void
  isRecent?: boolean
}

function BlockCard({ block, onClick, isRecent }: BlockCardProps) {
  return (
    <button
      onClick={onClick}
      className={cn(
        "p-3 text-left border rounded-lg hover:bg-blue-50 hover:border-blue-200 transition-colors",
        "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
      )}
    >
      <div className="flex items-start gap-2">
        <div className="text-lg flex-shrink-0 mt-0.5">
          {block.icon}
        </div>
        <div className="min-w-0 flex-1">
          <div className="flex items-center gap-1 mb-1">
            <h4 className="font-medium text-sm truncate">
              {block.displayName}
            </h4>
            {isRecent && (
              <Star className="h-3 w-3 text-yellow-500 flex-shrink-0" />
            )}
          </div>
          {block.description && (
            <p className="text-xs text-gray-600 line-clamp-2">
              {block.description}
            </p>
          )}
          {block.tags && block.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {block.tags.slice(0, 2).map((tag: string) => (
                <Badge 
                  key={tag} 
                  variant="outline" 
                  className="text-xs px-1 py-0"
                >
                  {tag}
                </Badge>
              ))}
              {block.tags.length > 2 && (
                <Badge variant="outline" className="text-xs px-1 py-0">
                  +{block.tags.length - 2}
                </Badge>
              )}
            </div>
          )}
        </div>
      </div>
    </button>
  )
}

// Quick insert button for specific block types
interface QuickInsertButtonProps {
  blockType: string
  onInsert: (blockType: string) => void
  className?: string
}

export function QuickInsertButton({ 
  blockType, 
  onInsert, 
  className 
}: QuickInsertButtonProps) {
  const block = blockRegistry.getBlockType(blockType)
  
  if (!block) return null

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => onInsert(blockType)}
      className={cn("h-8 px-2 text-xs", className)}
    >
      <span className="mr-1">{block.icon}</span>
      {block.displayName}
    </Button>
  )
}

// Floating add button (like Gutenberg's + button)
interface FloatingAddButtonProps {
  onInsertBlock: (blockType: string) => void
  position?: 'top' | 'bottom'
  className?: string
}

export function FloatingAddButton({ 
  onInsertBlock, 
  position = 'bottom',
  className 
}: FloatingAddButtonProps) {
  return (
    <div className={cn(
      "absolute left-1/2 transform -translate-x-1/2 z-10",
      position === 'top' ? '-top-3' : '-bottom-3',
      className
    )}>
      <GutenbergBlockInserter
        onInsertBlock={onInsertBlock}
        trigger={
          <Button
            size="sm"
            className="h-6 w-6 p-0 rounded-full bg-blue-500 hover:bg-blue-600 text-white shadow-lg"
          >
            <Plus className="h-3 w-3" />
          </Button>
        }
        align="center"
        side={position === 'top' ? 'bottom' : 'top'}
      />
    </div>
  )
}
