'use client'

import React, { useState, useMemo } from 'react'
import { usePageBuilder } from '../context'
import { blockRegistry } from '../blocks/registry'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Search,
  Package,
  Image,
  Type,
  ShoppingCart,
  Layout,
  Share2,
  Settings,
  Zap,
  Plus
} from 'lucide-react'
import { cn } from '@/lib/utils'

export function BlockLibrary() {
  const { addBlock } = usePageBuilder()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  // Get all block types
  const allBlocks = blockRegistry.getAllBlockTypes()
  const categories = ['all', ...blockRegistry.getCategories()]

  // Enhanced category configuration with icons and colors
  const categoryConfig: Record<string, { name: string; icon: any; color: string; description: string }> = {
    all: { name: 'All Blocks', icon: Package, color: 'bg-gray-100 text-gray-700', description: 'All available blocks' },
    content: { name: 'Content', icon: Type, color: 'bg-blue-100 text-blue-700', description: 'Text and content blocks' },
    layout: { name: 'Layout', icon: Layout, color: 'bg-green-100 text-green-700', description: 'Structure and layout blocks' },
    media: { name: 'Media', icon: Image, color: 'bg-purple-100 text-purple-700', description: 'Images and media blocks' },
    ecommerce: { name: 'E-commerce', icon: ShoppingCart, color: 'bg-orange-100 text-orange-700', description: 'Shopping and product blocks' },
    marketing: { name: 'Marketing', icon: Zap, color: 'bg-pink-100 text-pink-700', description: 'Promotional and marketing blocks' },
    social: { name: 'Social', icon: Share2, color: 'bg-indigo-100 text-indigo-700', description: 'Social media blocks' },
    utility: { name: 'Utility', icon: Settings, color: 'bg-teal-100 text-teal-700', description: 'Utility and functional blocks' }
  }

  // Enhanced filtering logic
  const filteredBlocks = useMemo(() => {
    return allBlocks.filter(block => {
      const matchesSearch = searchQuery === '' ||
        block.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        block.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        block.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))

      const matchesCategory = selectedCategory === 'all' || block.category === selectedCategory

      return matchesSearch && matchesCategory
    })
  }, [allBlocks, searchQuery, selectedCategory])

  // Group blocks by category for display
  const blocksByCategory = useMemo(() => {
    const grouped: Record<string, any[]> = {}
    categories.forEach(category => {
      if (category === 'all') {
        grouped[category] = filteredBlocks
      } else {
        grouped[category] = filteredBlocks.filter(block => block.category === category)
      }
    })
    return grouped
  }, [categories, filteredBlocks])

  // Handle block drag start
  const handleDragStart = (e: React.DragEvent, blockType: string) => {
    e.dataTransfer.setData('text/plain', JSON.stringify({
      type: 'new-block',
      blockType
    }))
    e.dataTransfer.effectAllowed = 'copy'
  }

  // Handle block click (add to canvas)
  const handleBlockClick = (blockType: string) => {
    addBlock(blockType)
  }



  return (
    <div className="h-full flex flex-col bg-white">
      {/* Gutenberg-style Header */}
      <div className="px-4 py-3 border-b border-gray-200">
        {/* Search Bar - Gutenberg style */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Gutenberg-style Categories */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="h-full flex flex-col">
          {/* Category Tabs - Gutenberg style */}
          <div className="border-b border-gray-200">
            <ScrollArea className="w-full">
              <TabsList className="inline-flex h-auto p-0 bg-transparent">
                {categories.map((category) => {
                  const config = categoryConfig[category]
                  const Icon = config?.icon || Package

                  return (
                    <TabsTrigger
                      key={category}
                      value={category}
                      className={cn(
                        'flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 border-transparent',
                        'data-[state=active]:border-blue-500 data-[state=active]:text-blue-600',
                        'hover:text-gray-700 hover:bg-gray-50',
                        'transition-colors duration-200'
                      )}
                    >
                      <Icon className="h-4 w-4" />
                      <span>{config?.name || category}</span>
                    </TabsTrigger>
                  )
                })}
              </TabsList>
            </ScrollArea>
          </div>

          {/* Gutenberg-style Block Grid */}
          <div className="flex-1 overflow-hidden">
            {categories.map((category) => (
              <TabsContent
                key={category}
                value={category}
                className="h-full mt-0 data-[state=active]:flex data-[state=active]:flex-col"
              >
                <ScrollArea className="flex-1 p-4">
                  {blocksByCategory[category]?.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-gray-400 mb-4">
                        <Package className="h-12 w-12 mx-auto" />
                      </div>
                      <p className="text-sm text-gray-500">
                        {searchQuery ? 'No blocks found' : 'No blocks in this category'}
                      </p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 gap-2">
                      {blocksByCategory[category]?.map((block) => (
                        <GutenbergBlockItem
                          key={block.id}
                          block={block}
                          onDragStart={(e: React.DragEvent) => handleDragStart(e, block.name)}
                          onClick={() => handleBlockClick(block.name)}
                        />
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </TabsContent>
            ))}
          </div>
        </Tabs>
      </div>

    </div>
  )
}

// Gutenberg-style block item
interface GutenbergBlockItemProps {
  block: any
  onDragStart: (e: React.DragEvent) => void
  onClick: () => void
}

function GutenbergBlockItem({ block, onDragStart, onClick }: GutenbergBlockItemProps) {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <div
      className={cn(
        'group relative bg-white border border-gray-200 rounded-lg p-3 cursor-pointer transition-all duration-200',
        'hover:border-blue-400 hover:shadow-sm',
        isHovered && 'border-blue-400 shadow-sm'
      )}
      draggable
      onDragStart={onDragStart}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Block Icon - Gutenberg style */}
      <div className="flex flex-col items-center text-center space-y-2">
        <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
          <span className="text-xl">{block.icon || '📦'}</span>
        </div>

        {/* Block Name */}
        <div className="w-full">
          <h3 className="text-xs font-medium text-gray-900 truncate">
            {block.displayName}
          </h3>
        </div>
      </div>

      {/* Hover overlay - subtle like Gutenberg */}
      <div className={cn(
        'absolute inset-0 bg-blue-50 rounded-lg opacity-0 transition-opacity pointer-events-none',
        isHovered && 'opacity-30'
      )} />
    </div>
  )
}


