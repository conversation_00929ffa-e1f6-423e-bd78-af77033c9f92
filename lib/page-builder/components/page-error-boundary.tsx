'use client'

import React from 'react'
import { Alert<PERSON>riangle, RefreshCw, Home } from 'lucide-react'
import Link from 'next/link'

interface PageErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

interface PageErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; reset: () => void }>
}

export class PageErrorBoundary extends React.Component<
  PageErrorBoundaryProps,
  PageErrorBoundaryState
> {
  constructor(props: PageErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): PageErrorBoundaryState {
    return {
      hasError: true,
      error,
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Page Builder Error:', error, errorInfo)
    
    // Log error to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      // TODO: Send to error monitoring service
      // Example: Sentry.captureException(error, { extra: errorInfo })
    }

    this.setState({
      hasError: true,
      error,
      errorInfo,
    })
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return (
          <FallbackComponent 
            error={this.state.error!} 
            reset={this.handleReset} 
          />
        )
      }

      // Default error UI
      return <DefaultPageErrorFallback error={this.state.error!} reset={this.handleReset} />
    }

    return this.props.children
  }
}

// Default error fallback component
function DefaultPageErrorFallback({ 
  error, 
  reset 
}: { 
  error: Error
  reset: () => void 
}) {
  const isDevelopment = process.env.NODE_ENV === 'development'

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full mx-4">
        <div className="bg-white rounded-lg shadow-lg p-6 text-center">
          {/* Error Icon */}
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>

          {/* Error Title */}
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Something went wrong
          </h1>

          {/* Error Description */}
          <p className="text-gray-600 mb-6">
            We encountered an error while loading this page. This might be a temporary issue.
          </p>

          {/* Development Error Details */}
          {isDevelopment && (
            <div className="mb-6 p-4 bg-red-50 rounded-lg text-left">
              <h3 className="text-sm font-medium text-red-800 mb-2">
                Error Details (Development Only):
              </h3>
              <pre className="text-xs text-red-700 overflow-auto max-h-32">
                {error.message}
                {error.stack && `\n\n${error.stack}`}
              </pre>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button
              onClick={reset}
              className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </button>
            
            <Link
              href="/"
              className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <Home className="h-4 w-4 mr-2" />
              Go Home
            </Link>
          </div>

          {/* Support Information */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              If this problem persists, please{' '}
              <Link 
                href="/contact" 
                className="text-blue-600 hover:text-blue-500 underline"
              >
                contact support
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Block-level error boundary for individual blocks
export function BlockErrorBoundary({ 
  children, 
  blockType, 
  blockId 
}: { 
  children: React.ReactNode
  blockType: string
  blockId: string
}) {
  return (
    <PageErrorBoundary
      fallback={({ error, reset }) => (
        <BlockErrorFallback 
          error={error}
          reset={reset}
          blockType={blockType}
          blockId={blockId}
        />
      )}
    >
      {children}
    </PageErrorBoundary>
  )
}

// Block error fallback component
function BlockErrorFallback({
  error,
  reset,
  blockType,
  blockId
}: {
  error: Error
  reset: () => void
  blockType: string
  blockId: string
}) {
  const isDevelopment = process.env.NODE_ENV === 'development'

  return (
    <div className="border-2 border-red-200 bg-red-50 rounded-lg p-4 my-4">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <AlertTriangle className="h-5 w-5 text-red-400" />
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-red-800">
            Block Error: {blockType}
          </h3>
          <div className="mt-2 text-sm text-red-700">
            <p>This block failed to render properly.</p>
            {isDevelopment && (
              <details className="mt-2">
                <summary className="cursor-pointer font-medium">
                  Error Details (Development)
                </summary>
                <pre className="mt-2 text-xs bg-red-100 p-2 rounded overflow-auto">
                  Block ID: {blockId}
                  Block Type: {blockType}
                  Error: {error.message}
                  {error.stack && `\n\nStack:\n${error.stack}`}
                </pre>
              </details>
            )}
          </div>
          <div className="mt-3">
            <button
              onClick={reset}
              className="text-sm bg-red-100 text-red-800 px-3 py-1 rounded hover:bg-red-200 transition-colors"
            >
              Retry Block
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Hook for handling errors in functional components
export function useErrorHandler() {
  return (error: Error, errorInfo?: React.ErrorInfo) => {
    console.error('Page Builder Error:', error, errorInfo)
    
    // Log to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      // TODO: Send to error monitoring service
    }
  }
}

// Async error boundary for handling promise rejections
export function AsyncErrorBoundary({ 
  children 
}: { 
  children: React.ReactNode 
}) {
  React.useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason)
      
      // Log to monitoring service
      if (process.env.NODE_ENV === 'production') {
        // TODO: Send to error monitoring service
      }
    }

    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])

  return <>{children}</>
}
