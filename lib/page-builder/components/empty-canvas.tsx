'use client'

import React from 'react'
import { usePageBuilder } from '../context'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Plus, Sparkles, Layout, Palette } from 'lucide-react'

interface EmptyCanvasProps {
  isPreviewMode: boolean
}

export function EmptyCanvas({ isPreviewMode }: EmptyCanvasProps) {
  const { addBlock } = usePageBuilder()

  if (isPreviewMode) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-6 flex items-center justify-center">
            <Layout className="h-12 w-12 text-gray-400" />
          </div>
          <h2 className="text-2xl font-light text-gray-600 mb-2">Empty Page</h2>
          <p className="text-gray-500">This page has no content to display.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-8">
      <div className="max-w-2xl mx-auto text-center">
        {/* Hero Section */}
        <div className="mb-8">
          <div className="w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full mx-auto mb-6 flex items-center justify-center">
            <Sparkles className="h-16 w-16 text-blue-500" />
          </div>
          <h1 className="text-3xl font-light text-gray-900 mb-4">
            Start Building Your Page
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            Create beautiful, responsive pages with our drag-and-drop page builder. 
            Add blocks from the library or start with a template.
          </p>
        </div>

        {/* Quick Start Options */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <QuickStartCard
            icon={<Plus className="h-8 w-8" />}
            title="Add Your First Block"
            description="Choose from our library of pre-built blocks"
            onClick={() => addBlock('hero')}
            buttonText="Add Hero Block"
          />
          
          <QuickStartCard
            icon={<Layout className="h-8 w-8" />}
            title="Start with Template"
            description="Use a pre-designed page template"
            onClick={() => {
              // Add multiple blocks for a template
              addBlock('hero')
              setTimeout(() => addBlock('product-grid'), 100)
              setTimeout(() => addBlock('testimonials'), 200)
            }}
            buttonText="Use Template"
          />
          
          <QuickStartCard
            icon={<Palette className="h-8 w-8" />}
            title="Customize Design"
            description="Personalize colors, fonts, and spacing"
            onClick={() => addBlock('text')}
            buttonText="Add Text Block"
          />
        </div>

        {/* Popular Blocks */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Popular Blocks</h3>
          <div className="flex flex-wrap justify-center gap-2">
            {popularBlocks.map((block) => (
              <Button
                key={block.type}
                variant="outline"
                size="sm"
                onClick={() => addBlock(block.type)}
                className="flex items-center space-x-2"
              >
                <span>{block.icon}</span>
                <span>{block.name}</span>
              </Button>
            ))}
          </div>
        </div>

        {/* Tips */}
        <div className="bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-3">💡 Pro Tips</h3>
          <ul className="text-sm text-blue-800 space-y-2 text-left">
            <li>• Drag blocks from the left sidebar to add them to your page</li>
            <li>• Click on any block to edit its content and styling</li>
            <li>• Use the device preview buttons to see how your page looks on different screens</li>
            <li>• Save your work regularly using the save button in the top toolbar</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

// Quick start card component
interface QuickStartCardProps {
  icon: React.ReactNode
  title: string
  description: string
  onClick: () => void
  buttonText: string
}

function QuickStartCard({ 
  icon, 
  title, 
  description, 
  onClick, 
  buttonText 
}: QuickStartCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer group">
      <CardContent className="p-6 text-center">
        <div className="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center group-hover:bg-blue-100 transition-colors">
          <div className="text-gray-600 group-hover:text-blue-600 transition-colors">
            {icon}
          </div>
        </div>
        <h3 className="font-medium text-gray-900 mb-2">{title}</h3>
        <p className="text-sm text-gray-600 mb-4">{description}</p>
        <Button 
          size="sm" 
          onClick={onClick}
          className="w-full"
        >
          {buttonText}
        </Button>
      </CardContent>
    </Card>
  )
}

// Popular blocks data
const popularBlocks = [
  { type: 'hero', name: 'Hero Section', icon: '🎯' },
  { type: 'product-grid', name: 'Product Grid', icon: '🛍️' },
  { type: 'testimonials', name: 'Testimonials', icon: '💬' },
  { type: 'newsletter', name: 'Newsletter', icon: '📧' },
  { type: 'text', name: 'Text Content', icon: '📝' },
  { type: 'image', name: 'Image', icon: '🖼️' },
]

// Empty canvas with animation
export function AnimatedEmptyCanvas({ isPreviewMode }: EmptyCanvasProps) {
  return (
    <div className="animate-fade-in">
      <EmptyCanvas isPreviewMode={isPreviewMode} />
    </div>
  )
}

// Empty canvas with different layouts
interface EmptyCanvasVariantProps extends EmptyCanvasProps {
  variant?: 'default' | 'minimal' | 'detailed'
}

export function EmptyCanvasVariant({ 
  isPreviewMode, 
  variant = 'default' 
}: EmptyCanvasVariantProps) {
  const { addBlock } = usePageBuilder()

  if (variant === 'minimal') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Plus className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500 mb-4">Click to add your first block</p>
          <Button onClick={() => addBlock('hero')}>
            Add Block
          </Button>
        </div>
      </div>
    )
  }

  if (variant === 'detailed') {
    return (
      <div className="min-h-screen p-8">
        <div className="max-w-4xl mx-auto">
          {/* Detailed tutorial content */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-light text-gray-900 mb-4">
              Welcome to the Page Builder
            </h1>
            <p className="text-xl text-gray-600">
              Create stunning pages with our intuitive drag-and-drop interface
            </p>
          </div>

          {/* Feature highlights */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <FeatureCard
              icon="🎨"
              title="Visual Design"
              description="Design beautiful pages without coding"
            />
            <FeatureCard
              icon="📱"
              title="Responsive"
              description="Looks great on all devices"
            />
            <FeatureCard
              icon="⚡"
              title="Fast Loading"
              description="Optimized for performance"
            />
            <FeatureCard
              icon="🛍️"
              title="E-commerce Ready"
              description="Built-in product blocks"
            />
            <FeatureCard
              icon="🔧"
              title="Customizable"
              description="Full control over styling"
            />
            <FeatureCard
              icon="💾"
              title="Auto Save"
              description="Never lose your work"
            />
          </div>

          {/* Get started section */}
          <div className="text-center">
            <Button 
              size="lg" 
              onClick={() => addBlock('hero')}
              className="px-8 py-3"
            >
              Get Started
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return <EmptyCanvas isPreviewMode={isPreviewMode} />
}

// Feature card component
interface FeatureCardProps {
  icon: string
  title: string
  description: string
}

function FeatureCard({ icon, title, description }: FeatureCardProps) {
  return (
    <div className="text-center p-6">
      <div className="text-4xl mb-4">{icon}</div>
      <h3 className="font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-sm text-gray-600">{description}</p>
    </div>
  )
}
