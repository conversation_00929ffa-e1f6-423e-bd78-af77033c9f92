'use client'

import React from 'react'
import { usePageBuilder } from '../context'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { 
  Settings, 
  Search, 
  Share2, 
  Code, 
  Shield,
  Calendar,
  Eye,
  Globe
} from 'lucide-react'
import { generateSlug } from '../utils'

export function PageSettingsPanel() {
  const { state, dispatch } = usePageBuilder()
  const { page } = state

  // Update page settings
  const updatePageSettings = (updates: any) => {
    dispatch({
      type: 'SET_PAGE',
      payload: {
        ...page,
        ...updates,
        settings: {
          ...page.settings,
          ...updates.settings,
        },
      },
    })
  }

  // Generate slug from title
  const handleTitleChange = (title: string) => {
    const slug = generateSlug(title)
    updatePageSettings({
      title,
      slug,
      settings: { ...page.settings, title },
    })
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6 border-b">
        <div className="flex items-center space-x-2 mb-2">
          <Settings className="h-5 w-5" />
          <h2 className="text-lg font-semibold">Page Settings</h2>
        </div>
        <p className="text-sm text-muted-foreground">
          Configure page metadata, SEO, and publishing options
        </p>
      </div>

      {/* Settings Tabs */}
      <div className="flex-1 overflow-hidden">
        <Tabs defaultValue="general" className="h-full flex flex-col">
          <div className="px-6 pt-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="general" className="text-xs">
                <Globe className="h-3 w-3 mr-1" />
                General
              </TabsTrigger>
              <TabsTrigger value="seo" className="text-xs">
                <Search className="h-3 w-3 mr-1" />
                SEO
              </TabsTrigger>
              <TabsTrigger value="social" className="text-xs">
                <Share2 className="h-3 w-3 mr-1" />
                Social
              </TabsTrigger>
              <TabsTrigger value="advanced" className="text-xs">
                <Code className="h-3 w-3 mr-1" />
                Advanced
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="flex-1 overflow-hidden">
            {/* General Tab */}
            <TabsContent value="general" className="h-full mt-0">
              <ScrollArea className="h-full px-6 pb-6">
                <div className="pt-4 space-y-6">
                  {/* Basic Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Basic Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label htmlFor="page-title">Page Title</Label>
                        <Input
                          id="page-title"
                          value={page.title}
                          onChange={(e) => handleTitleChange(e.target.value)}
                          placeholder="Enter page title"
                        />
                      </div>

                      <div>
                        <Label htmlFor="page-slug">Page Slug</Label>
                        <Input
                          id="page-slug"
                          value={page.slug}
                          onChange={(e) => updatePageSettings({ slug: e.target.value })}
                          placeholder="page-slug"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          URL: /{page.slug}
                        </p>
                      </div>

                      <div>
                        <Label htmlFor="page-description">Description</Label>
                        <Textarea
                          id="page-description"
                          value={page.description || ''}
                          onChange={(e) => updatePageSettings({ description: e.target.value })}
                          placeholder="Brief description of the page"
                          rows={3}
                        />
                      </div>

                      <div>
                        <Label htmlFor="page-type">Page Type</Label>
                        <Select
                          value={page.type}
                          onValueChange={(value) => updatePageSettings({ type: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="custom">Custom Page</SelectItem>
                            <SelectItem value="landing">Landing Page</SelectItem>
                            <SelectItem value="product">Product Page</SelectItem>
                            <SelectItem value="category">Category Page</SelectItem>
                            <SelectItem value="home">Home Page</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Publishing */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Publishing</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label htmlFor="page-status">Status</Label>
                        <Select
                          value={page.status}
                          onValueChange={(value) => updatePageSettings({ status: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="draft">Draft</SelectItem>
                            <SelectItem value="published">Published</SelectItem>
                            <SelectItem value="archived">Archived</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium">Home Page</Label>
                          <p className="text-xs text-muted-foreground">
                            Set as the site's home page
                          </p>
                        </div>
                        <Switch
                          checked={page.isHomePage || false}
                          onCheckedChange={(checked) => updatePageSettings({ isHomePage: checked })}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium">Landing Page</Label>
                          <p className="text-xs text-muted-foreground">
                            Optimize for marketing campaigns
                          </p>
                        </div>
                        <Switch
                          checked={page.isLandingPage || false}
                          onCheckedChange={(checked) => updatePageSettings({ isLandingPage: checked })}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium">Require Authentication</Label>
                          <p className="text-xs text-muted-foreground">
                            Only logged-in users can view
                          </p>
                        </div>
                        <Switch
                          checked={page.requiresAuth || false}
                          onCheckedChange={(checked) => updatePageSettings({ requiresAuth: checked })}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </ScrollArea>
            </TabsContent>

            {/* SEO Tab */}
            <TabsContent value="seo" className="h-full mt-0">
              <ScrollArea className="h-full px-6 pb-6">
                <div className="pt-4 space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Search Engine Optimization</CardTitle>
                      <CardDescription>
                        Optimize your page for search engines
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label htmlFor="seo-title">SEO Title</Label>
                        <Input
                          id="seo-title"
                          value={page.seoTitle || ''}
                          onChange={(e) => updatePageSettings({ seoTitle: e.target.value })}
                          placeholder="SEO optimized title"
                          maxLength={60}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          {(page.seoTitle || '').length}/60 characters
                        </p>
                      </div>

                      <div>
                        <Label htmlFor="seo-description">SEO Description</Label>
                        <Textarea
                          id="seo-description"
                          value={page.seoDescription || ''}
                          onChange={(e) => updatePageSettings({ seoDescription: e.target.value })}
                          placeholder="Brief description for search results"
                          rows={3}
                          maxLength={160}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          {(page.seoDescription || '').length}/160 characters
                        </p>
                      </div>

                      <div>
                        <Label htmlFor="seo-keywords">Keywords</Label>
                        <Input
                          id="seo-keywords"
                          value={(page.seoKeywords || []).join(', ')}
                          onChange={(e) => {
                            const keywords = e.target.value.split(',').map(k => k.trim()).filter(Boolean)
                            updatePageSettings({ seoKeywords: keywords })
                          }}
                          placeholder="keyword1, keyword2, keyword3"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Separate keywords with commas
                        </p>
                      </div>

                      {/* Keywords Display */}
                      {page.seoKeywords && page.seoKeywords.length > 0 && (
                        <div>
                          <Label className="text-sm">Current Keywords</Label>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {page.seoKeywords.map((keyword, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {keyword}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Social Tab */}
            <TabsContent value="social" className="h-full mt-0">
              <ScrollArea className="h-full px-6 pb-6">
                <div className="pt-4 space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Social Media</CardTitle>
                      <CardDescription>
                        Configure how your page appears when shared
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label htmlFor="og-image">Open Graph Image</Label>
                        <Input
                          id="og-image"
                          value={page.ogImage || ''}
                          onChange={(e) => updatePageSettings({ ogImage: e.target.value })}
                          placeholder="https://example.com/image.jpg"
                          type="url"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Recommended size: 1200x630px
                        </p>
                      </div>

                      {page.ogImage && (
                        <div>
                          <Label className="text-sm">Preview</Label>
                          <div className="mt-2 border rounded-lg p-3 bg-gray-50">
                            <img
                              src={page.ogImage}
                              alt="Open Graph preview"
                              className="w-full h-32 object-cover rounded mb-2"
                              onError={(e) => {
                                e.currentTarget.style.display = 'none'
                              }}
                            />
                            <h4 className="font-medium text-sm">{page.seoTitle || page.title}</h4>
                            <p className="text-xs text-muted-foreground mt-1">
                              {page.seoDescription || page.description}
                            </p>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Advanced Tab */}
            <TabsContent value="advanced" className="h-full mt-0">
              <ScrollArea className="h-full px-6 pb-6">
                <div className="pt-4 space-y-6">
                  {/* Custom Code */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Custom Code</CardTitle>
                      <CardDescription>
                        Add custom CSS and JavaScript
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label htmlFor="custom-css">Custom CSS</Label>
                        <Textarea
                          id="custom-css"
                          value={page.customCss || ''}
                          onChange={(e) => updatePageSettings({ customCss: e.target.value })}
                          placeholder="/* Custom CSS for this page */&#10;.my-custom-class {&#10;  /* Your styles here */&#10;}"
                          rows={6}
                          className="font-mono text-xs"
                        />
                      </div>

                      <div>
                        <Label htmlFor="custom-js">Custom JavaScript</Label>
                        <Textarea
                          id="custom-js"
                          value={page.customJs || ''}
                          onChange={(e) => updatePageSettings({ customJs: e.target.value })}
                          placeholder="// Custom JavaScript for this page&#10;console.log('Page loaded');"
                          rows={6}
                          className="font-mono text-xs"
                        />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Page Metadata */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Page Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2 text-xs">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Page ID:</span>
                        <span className="font-mono">{page.id || 'Not saved'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Created:</span>
                        <span>{page.createdAt ? new Date(page.createdAt).toLocaleDateString() : 'Not saved'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Last Modified:</span>
                        <span>{page.updatedAt ? new Date(page.updatedAt).toLocaleDateString() : 'Not saved'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Blocks:</span>
                        <span>{page.blocks.length}</span>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </ScrollArea>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  )
}
