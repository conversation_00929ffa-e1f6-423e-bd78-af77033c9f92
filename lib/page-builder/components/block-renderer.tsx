'use client'

import React, { Suspense } from 'react'
import { PageBlock } from '../types'
import {
  BlockErrorBoundary,
  BlockLoadingPlaceholder,
  BlockNotFound
} from '../blocks/base-block'
import { HeroBlock } from '../blocks/hero-block'
import { ProductGridBlock } from '../blocks/product-grid-block'
import { ColumnsBlock } from '../blocks/columns-block'
import { GridBlock } from '../blocks/grid-block'
import { CardBlock } from '../blocks/card-block'
import { AccordionBlock } from '../blocks/accordion-block'
import { TabsBlock } from '../blocks/tabs-block'
import { DividerBlock } from '../blocks/divider-block'
import { ButtonGroupBlock } from '../blocks/button-group-block'
import { FeatureListBlock } from '../blocks/feature-list-block'
import { StorySectionBlock } from '../blocks/story-section-block'
import { ValuesGridBlock } from '../blocks/values-grid-block'
import { MissionStatementBlock } from '../blocks/mission-statement-block'
import { ContactInfoBlock } from '../blocks/contact-info-block'
import { ContactFormBlock } from '../blocks/contact-form-block'
import { FAQAccordionBlock } from '../blocks/faq-accordion-block'
import { BrandAssetsBlock } from '../blocks/brand-assets-block'
import { LegalContentBlock } from '../blocks/legal-content-block'
import { HelpTopicsGridBlock } from '../blocks/help-topics-grid-block'
import { ShippingInfoCardsBlock } from '../blocks/shipping-info-cards-block'
import { StoreLocatorBlock } from '../blocks/store-locator-block'
import { NewsletterBenefitsBlock } from '../blocks/newsletter-benefits-block'
import { FeaturedCategoriesBlock } from '../blocks/featured-categories-block'
import { ProductListingBlock } from '../blocks/ecommerce/product-listing-block'
import { CartBlock } from '../blocks/ecommerce/cart-block'
import { CheckoutBlock } from '../blocks/ecommerce/checkout-block'
import { ProductDetailsBlock } from '../blocks/ecommerce/product-details-block'
import { WishlistBlock } from '../blocks/ecommerce/wishlist-block'
import { CollectionHeaderBlock } from '../blocks/ecommerce/collection-header-block'
import { AccountDashboardBlock } from '../blocks/ecommerce/account-dashboard-block'
import { ProductComparisonBlock } from '../blocks/ecommerce/product-comparison-block'
import { HeroSectionBlock } from '../blocks/landing/hero-section-block'
import { NewArrivalsBlock } from '../blocks/landing/new-arrivals-block'
import { EditorialSectionBlock } from '../blocks/landing/editorial-section-block'
import { NewsletterSignupBlock } from '../blocks/landing/newsletter-signup-block'
import { SpecialOffersBannerBlock as LandingSpecialOffersBannerBlock } from '../blocks/landing/special-offers-banner-block'
import { SearchWidgetBlock } from '../blocks/search-widget-block'
import { SocialMediaWidgetBlock } from '../blocks/social-media-widget-block'
import { ContactInfoWidgetBlock } from '../blocks/contact-info-widget-block'
import { StatsCounterWidgetBlock } from '../blocks/stats-counter-widget-block'
import { ProgressBarWidgetBlock } from '../blocks/progress-bar-widget-block'
import { AlertNotificationWidgetBlock } from '../blocks/alert-notification-widget-block'
import { ClockTimerWidgetBlock } from '../blocks/clock-timer-widget-block'
import { VideoPlayerBlock } from '../blocks/video-player-block'
import { FormBuilderBlock } from '../blocks/form-builder-block'
import { ImageGalleryBlock } from '../blocks/image-gallery-block'
import { MapBlock } from '../blocks/map-block'
import { blockRegistry } from '../blocks/registry'

// Dynamic imports for better code splitting
const TestimonialsBlock = React.lazy(() => 
  import('../blocks/testimonials-block').then(module => ({ default: module.TestimonialsBlock }))
)
// NewsletterBlock removed - use newsletter-signup-block from landing directory instead
const TextBlock = React.lazy(() => 
  import('../blocks/text-block').then(module => ({ default: module.TextBlock }))
)
const ImageBlock = React.lazy(() => 
  import('../blocks/image-block').then(module => ({ default: module.ImageBlock }))
)
const SpacerBlock = React.lazy(() =>
  import('../blocks/spacer-block').then(module => ({ default: module.SpacerBlock }))
)

const CodeBlock = React.lazy(() =>
  import('../blocks/code-block').then(module => ({ default: module.CodeBlock }))
)

interface BlockRendererProps {
  block: PageBlock
  index: number
  isEditing?: boolean
  onMove?: (dragIndex: number, hoverIndex: number) => void
}

export function BlockRenderer({ 
  block, 
  index, 
  isEditing = false, 
  onMove 
}: BlockRendererProps) {
  // Get block type definition from registry
  const blockType = blockRegistry.getBlockType(block.type)
  
  if (!blockType) {
    return <BlockNotFound blockType={block.type} />
  }

  // Render the appropriate block component
  const renderBlockContent = () => {
    switch (block.type) {
      case 'hero':
        return <HeroBlock block={block} isEditing={isEditing} />
      
      case 'product-grid':
        return <ProductGridBlock block={block} isEditing={isEditing} />
      
      case 'testimonials':
        return (
          <Suspense fallback={<BlockLoadingPlaceholder blockType="testimonials" />}>
            <TestimonialsBlock block={block} isEditing={isEditing} />
          </Suspense>
        )
      
      case 'newsletter':
        // Redirect to newsletter-signup block from landing directory
        return <NewsletterSignupBlock block={block} isEditing={isEditing} />
      
      case 'text':
        return (
          <Suspense fallback={<BlockLoadingPlaceholder blockType="text" />}>
            <TextBlock block={block} isEditing={isEditing} />
          </Suspense>
        )
      
      case 'image':
        return (
          <Suspense fallback={<BlockLoadingPlaceholder blockType="image" />}>
            <ImageBlock block={block} isEditing={isEditing} />
          </Suspense>
        )
      
      case 'spacer':
        return (
          <Suspense fallback={<BlockLoadingPlaceholder blockType="spacer" />}>
            <SpacerBlock block={block} isEditing={isEditing} />
          </Suspense>
        )

      case 'columns':
        return <ColumnsBlock block={block} isEditing={isEditing} />

      case 'grid':
        return <GridBlock block={block} isEditing={isEditing} />

      case 'card':
        return <CardBlock block={block} isEditing={isEditing} />

      case 'accordion':
        return <AccordionBlock block={block} isEditing={isEditing} />

      case 'tabs':
        return <TabsBlock block={block} isEditing={isEditing} />

      case 'divider':
        return <DividerBlock block={block} isEditing={isEditing} />

      case 'button-group':
        return <ButtonGroupBlock block={block} isEditing={isEditing} />

      case 'feature-list':
        return <FeatureListBlock block={block} isEditing={isEditing} />

      case 'search-widget':
        return <SearchWidgetBlock block={block} isEditing={isEditing} />

      case 'social-media-widget':
        return <SocialMediaWidgetBlock block={block} isEditing={isEditing} />

      case 'contact-info-widget':
        return <ContactInfoWidgetBlock block={block} isEditing={isEditing} />

      case 'stats-counter-widget':
        return <StatsCounterWidgetBlock block={block} isEditing={isEditing} />

      case 'progress-bar-widget':
        return <ProgressBarWidgetBlock block={block} isEditing={isEditing} />

      case 'alert-notification-widget':
        return <AlertNotificationWidgetBlock block={block} isEditing={isEditing} />

      case 'clock-timer-widget':
        return <ClockTimerWidgetBlock block={block} isEditing={isEditing} />

      case 'video-player':
        return <VideoPlayerBlock block={block} isEditing={isEditing} />

      case 'form-builder':
        return <FormBuilderBlock block={block} isEditing={isEditing} />

      case 'image-gallery':
        return <ImageGalleryBlock block={block} isEditing={isEditing} />

      case 'map':
        return <MapBlock block={block} isEditing={isEditing} />

      case 'code':
        return (
          <Suspense fallback={<BlockLoadingPlaceholder blockType="code" />}>
            <CodeBlock
              configuration={block.configuration as any}
              isSelected={false}
              isEditing={isEditing}
              onUpdate={(updates) => {
                // Handle block updates here if needed
                console.log('Code block updated:', updates)
              }}
            />
          </Suspense>
        )

      case 'story-section':
        return <StorySectionBlock block={block} isEditing={isEditing} />

      case 'values-grid':
        return <ValuesGridBlock block={block} isEditing={isEditing} />

      case 'mission-statement':
        return <MissionStatementBlock block={block} isEditing={isEditing} />

      case 'contact-info':
        return <ContactInfoBlock block={block} isEditing={isEditing} />

      case 'contact-form':
        return <ContactFormBlock block={block} isEditing={isEditing} />

      case 'faq-accordion':
        return <FAQAccordionBlock block={block} isEditing={isEditing} />

      case 'brand-assets':
        return <BrandAssetsBlock block={block} isEditing={isEditing} />

      case 'legal-content':
        return <LegalContentBlock block={block} isEditing={isEditing} />

      case 'help-topics-grid':
        return <HelpTopicsGridBlock block={block} isEditing={isEditing} />

      case 'shipping-info-cards':
        return <ShippingInfoCardsBlock block={block} isEditing={isEditing} />

      case 'store-locator':
        return <StoreLocatorBlock block={block} isEditing={isEditing} />

      case 'newsletter-benefits':
        return <NewsletterBenefitsBlock block={block} isEditing={isEditing} />

      case 'featured-categories':
        return <FeaturedCategoriesBlock block={block} isEditing={isEditing} />

      case 'editorial-grid':
        // Redirect to editorial-section block from landing directory
        return <EditorialSectionBlock block={block} isEditing={isEditing} />

      case 'product-listing':
        return <ProductListingBlock block={block} isEditing={isEditing} />

      case 'cart':
        return <CartBlock block={block} isEditing={isEditing} />

      case 'checkout':
        return <CheckoutBlock block={block} isEditing={isEditing} />

      case 'product-details':
        return <ProductDetailsBlock block={block} isEditing={isEditing} />

      case 'wishlist':
        return <WishlistBlock block={block} isEditing={isEditing} />

      case 'collection-header':
        return <CollectionHeaderBlock block={block} isEditing={isEditing} />

      case 'account-dashboard':
        return <AccountDashboardBlock block={block} isEditing={isEditing} />

      case 'product-comparison':
        return <ProductComparisonBlock block={block} isEditing={isEditing} />

      case 'hero-section':
        return <HeroSectionBlock block={block} isEditing={isEditing} />

      case 'new-arrivals':
        return <NewArrivalsBlock block={block} isEditing={isEditing} />

      case 'editorial-section':
        return <EditorialSectionBlock block={block} isEditing={isEditing} />

      case 'newsletter-signup':
        return <NewsletterSignupBlock block={block} isEditing={isEditing} />

      case 'special-offers-banner':
        return <LandingSpecialOffersBannerBlock block={block} isEditing={isEditing} />

      default:
        return <BlockNotFound blockType={block.type} />
    }
  }

  return (
    <BlockErrorBoundary blockType={block.type} blockId={block.id}>
      {isEditing ? (
        <DraggableBlockWrapper
          block={block}
          index={index}
          onMove={onMove}
        >
          {renderBlockContent()}
        </DraggableBlockWrapper>
      ) : (
        renderBlockContent()
      )}
    </BlockErrorBoundary>
  )
}

// Draggable wrapper for editing mode
interface DraggableBlockWrapperProps {
  block: PageBlock
  index: number
  children: React.ReactNode
  onMove?: (dragIndex: number, hoverIndex: number) => void
}

function DraggableBlockWrapper({ 
  block, 
  index, 
  children, 
  onMove 
}: DraggableBlockWrapperProps) {
  const [isDragging, setIsDragging] = React.useState(false)
  const dragRef = React.useRef<HTMLDivElement>(null)

  // Handle drag start
  const handleDragStart = (e: React.DragEvent) => {
    setIsDragging(true)
    e.dataTransfer.setData('text/plain', JSON.stringify({
      type: 'existing-block',
      blockId: block.id,
      index
    }))
    e.dataTransfer.effectAllowed = 'move'
  }

  // Handle drag end
  const handleDragEnd = () => {
    setIsDragging(false)
  }

  // Handle drag over
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  // Handle drop
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    
    try {
      const dragData = JSON.parse(e.dataTransfer.getData('text/plain'))
      
      if (dragData.type === 'existing-block' && onMove) {
        const dragIndex = dragData.index
        const hoverIndex = index
        
        if (dragIndex !== hoverIndex) {
          onMove(dragIndex, hoverIndex)
        }
      }
    } catch (error) {
      console.error('Error handling drop:', error)
    }
  }

  return (
    <div
      ref={dragRef}
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      className={`relative transition-all duration-200 ${
        isDragging ? 'opacity-50 scale-95' : ''
      }`}
      data-block-index={index}
    >
      {children}
      
      {/* Drag Handle */}
      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <div className="bg-blue-500 text-white p-1 rounded cursor-move">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M7 2a2 2 0 1 1 .001 4.001A2 2 0 0 1 7 2zM7 8a2 2 0 1 1 .001 4.001A2 2 0 0 1 7 8zM7 14a2 2 0 1 1 .001 4.001A2 2 0 0 1 7 14zM13 2a2 2 0 1 1 .001 4.001A2 2 0 0 1 13 2zM13 8a2 2 0 1 1 .001 4.001A2 2 0 0 1 13 8zM13 14a2 2 0 1 1 .001 4.001A2 2 0 0 1 13 14z" />
          </svg>
        </div>
      </div>
    </div>
  )
}

// Block registry for dynamic imports
const blockComponents = {
  hero: HeroBlock,
  'product-grid': ProductGridBlock,
  testimonials: TestimonialsBlock,
  newsletter: NewsletterSignupBlock, // Use landing newsletter block
  text: TextBlock,
  image: ImageBlock,
  spacer: SpacerBlock,
  columns: ColumnsBlock,
  grid: GridBlock,
  card: CardBlock,
  accordion: AccordionBlock,
  tabs: TabsBlock,
  divider: DividerBlock,
  'button-group': ButtonGroupBlock,
  'feature-list': FeatureListBlock,
  'search-widget': SearchWidgetBlock,
  'social-media-widget': SocialMediaWidgetBlock,
  'contact-info-widget': ContactInfoWidgetBlock,
  'stats-counter-widget': StatsCounterWidgetBlock,
  'progress-bar-widget': ProgressBarWidgetBlock,
  'alert-notification-widget': AlertNotificationWidgetBlock,
  'clock-timer-widget': ClockTimerWidgetBlock,
  'video-player': VideoPlayerBlock,
  'form-builder': FormBuilderBlock,
  'image-gallery': ImageGalleryBlock,
  'map': MapBlock,
  'code': CodeBlock,
  'story-section': StorySectionBlock,
  'values-grid': ValuesGridBlock,
  'mission-statement': MissionStatementBlock,
  'contact-info': ContactInfoBlock,
  'contact-form': ContactFormBlock,
  'faq-accordion': FAQAccordionBlock,
  'brand-assets': BrandAssetsBlock,
  'legal-content': LegalContentBlock,
  'help-topics-grid': HelpTopicsGridBlock,
  'shipping-info-cards': ShippingInfoCardsBlock,
  'store-locator': StoreLocatorBlock,
  'newsletter-benefits': NewsletterBenefitsBlock,
  'featured-categories': FeaturedCategoriesBlock,
  'editorial-grid': EditorialSectionBlock, // Use landing editorial block
  'special-offers-banner': LandingSpecialOffersBannerBlock, // Use landing special offers block
  'product-listing': ProductListingBlock,
  'cart': CartBlock,
  'checkout': CheckoutBlock,
  'product-details': ProductDetailsBlock,
  'wishlist': WishlistBlock,
  'collection-header': CollectionHeaderBlock,
  'account-dashboard': AccountDashboardBlock,
  'product-comparison': ProductComparisonBlock,
  'hero-section': HeroSectionBlock,
  'new-arrivals': NewArrivalsBlock,
  'editorial-section': EditorialSectionBlock,
  'newsletter-signup': NewsletterSignupBlock,
}

// Helper function to get block component
export function getBlockComponent(blockType: string) {
  return blockComponents[blockType as keyof typeof blockComponents]
}

// Helper function to check if block type is supported
export function isBlockTypeSupported(blockType: string): boolean {
  return blockType in blockComponents
}

// Block preview component for the block library
interface BlockPreviewProps {
  blockType: string
  config?: any
  className?: string
}

export function BlockPreview({ blockType, config, className }: BlockPreviewProps) {
  const blockDefinition = blockRegistry.getBlockType(blockType)
  
  if (!blockDefinition) {
    return <BlockNotFound blockType={blockType} />
  }

  const previewBlock: PageBlock = {
    id: `preview-${blockType}`,
    type: blockType,
    position: 0,
    isVisible: true,
    configuration: config || blockDefinition.defaultConfig,
    content: {},
    styling: {},
    responsive: {},
    animation: {},
    conditions: {},
  }

  return (
    <div className={`pointer-events-none ${className}`}>
      <BlockRenderer 
        block={previewBlock} 
        index={0} 
        isEditing={false} 
      />
    </div>
  )
}

// Block thumbnail component for the block library
interface BlockThumbnailProps {
  blockType: string
  className?: string
}

export function BlockThumbnail({ blockType, className }: BlockThumbnailProps) {
  const blockDefinition = blockRegistry.getBlockType(blockType)
  
  if (!blockDefinition) {
    return (
      <div className={`bg-gray-100 rounded-lg p-4 ${className}`}>
        <div className="text-center text-gray-500">
          <span className="text-2xl">❓</span>
          <p className="text-xs mt-1">Unknown</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white border rounded-lg p-4 ${className}`}>
      <div className="text-center">
        {blockDefinition.thumbnail ? (
          <img 
            src={blockDefinition.thumbnail} 
            alt={blockDefinition.displayName}
            className="w-full h-20 object-cover rounded mb-2"
          />
        ) : (
          <div className="w-full h-20 bg-gray-100 rounded mb-2 flex items-center justify-center">
            <span className="text-2xl">{blockDefinition.icon || '📦'}</span>
          </div>
        )}
        <p className="text-xs font-medium text-gray-900">
          {blockDefinition.displayName}
        </p>
        {blockDefinition.description && (
          <p className="text-xs text-gray-500 mt-1 line-clamp-2">
            {blockDefinition.description}
          </p>
        )}
      </div>
    </div>
  )
}

// Export all block components for external use
export {
  HeroBlock,
  ProductGridBlock,
  TestimonialsBlock,
  // NewsletterBlock, // Removed - use NewsletterSignupBlock from landing directory
  TextBlock,
  ImageBlock,
  SpacerBlock,
  ColumnsBlock,
  GridBlock,
  CardBlock,
  AccordionBlock,
  TabsBlock,
  DividerBlock,
  ButtonGroupBlock,
  FeatureListBlock,
  SearchWidgetBlock,
  SocialMediaWidgetBlock,
  ContactInfoWidgetBlock,
  StatsCounterWidgetBlock,
  ProgressBarWidgetBlock,
  AlertNotificationWidgetBlock,
  ClockTimerWidgetBlock,
  VideoPlayerBlock,
  FormBuilderBlock,
  ImageGalleryBlock,
  MapBlock,
  CodeBlock,
  StorySectionBlock,
  ValuesGridBlock,
  MissionStatementBlock,
  ContactInfoBlock,
  ContactFormBlock,
  FAQAccordionBlock,
  BrandAssetsBlock,
  LegalContentBlock,
  HelpTopicsGridBlock,
  ShippingInfoCardsBlock,
  StoreLocatorBlock,
  NewsletterBenefitsBlock,
  FeaturedCategoriesBlock,
  // EditorialGridBlock, // Removed - use EditorialSectionBlock from landing directory
  // SpecialOffersBannerBlock, // Removed - use LandingSpecialOffersBannerBlock from landing directory
  ProductListingBlock,
  CartBlock,
  CheckoutBlock,
  ProductDetailsBlock,
  WishlistBlock,
  CollectionHeaderBlock,
  AccountDashboardBlock,
  ProductComparisonBlock,
  HeroSectionBlock,
  NewArrivalsBlock,
  EditorialSectionBlock,
  NewsletterSignupBlock,
}
