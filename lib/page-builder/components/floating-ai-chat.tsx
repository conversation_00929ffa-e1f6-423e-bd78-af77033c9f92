"use client"

import React, { useState, useRef, useEffect } from 'react'
import { useChat } from '@ai-sdk/react'
import { usePageBuilder } from '../context'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Send, 
  Loader2, 
  Sparkles, 
  X,
  Wand2,
  Layout,
  Palette,
  ShoppingBag,
  Users,
  Type,
  Zap
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'
import { motion, AnimatePresence } from 'framer-motion'

interface FloatingAIChatProps {
  isOpen: boolean
  onClose: () => void
  className?: string
}

export function FloatingAIChat({ isOpen, onClose, className }: FloatingAIChatProps) {
  const { state } = usePageBuilder()
  const [isGenerating, setIsGenerating] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    error,
    stop,
    setMessages,
  } = useChat({
    api: '/api/ai-page-designer',
    maxSteps: 5,
    onFinish: () => {
      setIsGenerating(false)
      toast.success('AI has finished processing your request!')
    },
    onError: (error) => {
      setIsGenerating(false)
      toast.error('AI Designer error: ' + error.message)
    },
  })

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => inputRef.current?.focus(), 100)
    }
  }, [isOpen])

  // Quick action prompts
  const quickActions = [
    {
      icon: Layout,
      label: 'Layout',
      prompt: 'Create a modern landing page layout for a kids clothing store',
      color: 'bg-blue-500'
    },
    {
      icon: Palette,
      label: 'Style',
      prompt: 'Apply a playful, colorful design theme suitable for children',
      color: 'bg-purple-500'
    },
    {
      icon: ShoppingBag,
      label: 'Products',
      prompt: 'Add product showcase sections and shopping elements',
      color: 'bg-green-500'
    },
    {
      icon: Users,
      label: 'Kids Theme',
      prompt: 'Make this page more appealing to children and parents',
      color: 'bg-pink-500'
    },
    {
      icon: Type,
      label: 'Content',
      prompt: 'Generate engaging content for a kids clothing brand',
      color: 'bg-orange-500'
    },
    {
      icon: Zap,
      label: 'Optimize',
      prompt: 'Optimize this page for better performance and mobile',
      color: 'bg-yellow-500'
    }
  ]

  const handleQuickAction = (prompt: string) => {
    setIsGenerating(true)
    handleSubmit(new Event('submit') as any, {
      body: {
        messages: [{ role: 'user', content: prompt }],
        pageData: state.page,
        action: 'designPage'
      }
    })
  }

  const handleCustomSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim()) return
    
    setIsGenerating(true)
    handleSubmit(e, {
      body: {
        pageData: state.page,
        action: 'designPage'
      }
    })
  }

  // Get the latest message
  const latestMessage = messages[messages.length - 1]
  const hasMessages = messages.length > 0

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
            onClick={onClose}
          />
          
          {/* Floating Chat */}
          <motion.div
            initial={{ y: 100, opacity: 0, scale: 0.95 }}
            animate={{ y: 0, opacity: 1, scale: 1 }}
            exit={{ y: 100, opacity: 0, scale: 0.95 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className={cn(
              "fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50",
              "w-full max-w-2xl mx-4",
              className
            )}
          >
            <Card className="bg-white/95 backdrop-blur-lg border shadow-2xl">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b">
                <div className="flex items-center gap-2">
                  <div className="p-1.5 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500">
                    <Sparkles className="h-4 w-4 text-white" />
                  </div>
                  <h3 className="font-semibold">AI Page Designer</h3>
                  <Badge variant="secondary" className="text-xs">
                    Realtime
                  </Badge>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* Content */}
              <div className="p-4 space-y-4">
                {/* Latest Message or Welcome */}
                {hasMessages ? (
                  <div className="space-y-3">
                    {/* Latest AI Response */}
                    {latestMessage && latestMessage.role === 'assistant' && (
                      <div className="flex gap-3">
                        <div className="p-1.5 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1">
                          <Sparkles className="h-3 w-3 text-white" />
                        </div>
                        <div className="bg-muted rounded-lg px-3 py-2 max-w-[80%]">
                          <div className="text-sm whitespace-pre-wrap">{latestMessage.content}</div>
                        </div>
                      </div>
                    )}
                    
                    {/* Latest User Message */}
                    {latestMessage && latestMessage.role === 'user' && (
                      <div className="flex justify-end">
                        <div className="bg-primary text-primary-foreground rounded-lg px-3 py-2 max-w-[80%]">
                          <div className="text-sm whitespace-pre-wrap">{latestMessage.content}</div>
                        </div>
                      </div>
                    )}
                    
                    {/* Generating Indicator */}
                    {isGenerating && (
                      <div className="flex gap-3">
                        <div className="p-1.5 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1">
                          <Loader2 className="h-3 w-3 text-white animate-spin" />
                        </div>
                        <div className="bg-muted rounded-lg px-3 py-2">
                          <div className="flex items-center gap-2 text-sm">
                            <span>AI is designing...</span>
                            <div className="flex gap-1">
                              <div className="w-1 h-1 bg-current rounded-full animate-pulse" />
                              <div className="w-1 h-1 bg-current rounded-full animate-pulse delay-100" />
                              <div className="w-1 h-1 bg-current rounded-full animate-pulse delay-200" />
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  /* Welcome State */
                  <div className="text-center py-4">
                    <div className="p-3 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 w-fit mx-auto mb-3">
                      <Wand2 className="h-6 w-6 text-white" />
                    </div>
                    <h4 className="font-medium mb-1">AI Page Designer</h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      Ask me to design or modify your page!
                    </p>
                    
                    {/* Quick Actions */}
                    <div className="grid grid-cols-3 gap-2 mb-4">
                      {quickActions.map((action, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => handleQuickAction(action.prompt)}
                          disabled={isGenerating}
                          className="h-auto p-2 flex flex-col items-center gap-1 text-xs"
                        >
                          <div className={cn('p-1 rounded text-white', action.color)}>
                            <action.icon className="h-3 w-3" />
                          </div>
                          {action.label}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Input Area */}
                <form onSubmit={handleCustomSubmit} className="flex gap-2">
                  <Input
                    ref={inputRef}
                    value={input}
                    onChange={handleInputChange}
                    placeholder="Describe what you want to create or modify..."
                    disabled={isGenerating}
                    className="flex-1 bg-white/50"
                  />
                  <Button
                    type="submit"
                    size="sm"
                    disabled={isGenerating || !input.trim()}
                    className="px-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                  >
                    {isGenerating ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                </form>

                {/* Stop Button */}
                {isGenerating && (
                  <div className="flex justify-center">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={stop}
                      className="text-xs"
                    >
                      Stop Generation
                    </Button>
                  </div>
                )}

                {/* Error Display */}
                {error && (
                  <div className="text-sm text-destructive bg-destructive/10 rounded-lg p-2">
                    Error: {error.message}
                  </div>
                )}
              </div>
            </Card>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}
