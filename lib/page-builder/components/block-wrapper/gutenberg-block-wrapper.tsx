'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import {
  Settings,
  MoreVertical,
  Copy,
  Trash2,
  MoveUp,
  MoveDown,
  Eye,
  EyeOff,
  Grip,
  Plus,
  ChevronDown
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { DynamicBlockEditor } from '../properties-panel/dynamic-block-editor'
import { QuickSettings } from '../block-toolbar'
import { blockRegistry } from '../../blocks/registry'

interface GutenbergBlockWrapperProps {
  block: any
  isSelected?: boolean
  isHovered?: boolean
  onSelect?: () => void
  onUpdate?: (updates: any) => void
  onDuplicate?: () => void
  onDelete?: () => void
  onMoveUp?: () => void
  onMoveDown?: () => void
  onToggleVisibility?: () => void
  onInsertBefore?: () => void
  onInsertAfter?: () => void
  children: React.ReactNode
  className?: string
}

export function GutenbergBlockWrapper({
  block,
  isSelected = false,
  isHovered = false,
  onSelect,
  onUpdate,
  onDuplicate,
  onDelete,
  onMoveUp,
  onMoveDown,
  onToggleVisibility,
  onInsertBefore,
  onInsertAfter,
  children,
  className
}: GutenbergBlockWrapperProps) {
  const [showToolbar, setShowToolbar] = useState(false)
  const [activePopover, setActivePopover] = useState<string | null>(null)
  const wrapperRef = useRef<HTMLDivElement>(null)

  // Safety checks
  if (!block || !children) {
    return <div>Invalid block</div>
  }

  const blockDefinition = blockRegistry.getBlockType(block.type)

  // If block type is not found, show error
  if (!blockDefinition) {
    return (
      <div className="p-4 border border-red-200 bg-red-50 rounded">
        <p className="text-red-600 text-sm">Unknown block type: {block.type}</p>
      </div>
    )
  }

  useEffect(() => {
    setShowToolbar(isSelected || isHovered)
  }, [isSelected, isHovered])

  const hasSettings = blockDefinition.configSchema &&
    Object.keys(blockDefinition.configSchema.properties || {}).length > 0

  return (
    <div
      ref={wrapperRef}
      className={cn(
        'relative group transition-all duration-200',
        isSelected && 'ring-2 ring-blue-500 ring-offset-2',
        isHovered && !isSelected && 'ring-1 ring-gray-300',
        block.hidden && 'opacity-50',
        className
      )}
      onClick={(e) => {
        e.stopPropagation()
        onSelect?.()
      }}
      onMouseEnter={() => setShowToolbar(true)}
      onMouseLeave={() => !isSelected && setShowToolbar(false)}
    >
      {/* Block Content */}
      <div className={cn(
        'relative',
        (isSelected || isHovered) && 'outline-none'
      )}>
        {children}
      </div>

      {/* Gutenberg-style Block Toolbar */}
      {showToolbar && blockDefinition && (
        <div className="absolute top-0 left-0 transform -translate-y-full z-50">
          <div className="flex items-center gap-1 mb-1">
            {/* Main Toolbar */}
            <div className="flex items-center bg-white border border-gray-200 rounded-lg shadow-lg p-1">
              {/* Drag Handle */}
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 cursor-grab hover:bg-gray-50"
                draggable
              >
                <Grip className="h-4 w-4 text-gray-400" />
              </Button>

              {/* Block Type Badge */}
              <div className="px-2 py-1 bg-gray-50 rounded text-xs font-medium text-gray-700 flex items-center gap-1">
                <span>{blockDefinition.icon}</span>
                {blockDefinition.displayName}
              </div>

              {/* Quick Settings */}
              {hasSettings && (
                <QuickSettings
                  block={block}
                  blockType={blockDefinition}
                  onUpdate={onUpdate || (() => {})}
                />
              )}

              {/* More Options */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-gray-50"
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  {/* Settings */}
                  {hasSettings && (
                    <>
                      <Popover 
                        open={activePopover === 'settings'} 
                        onOpenChange={(open) => setActivePopover(open ? 'settings' : null)}
                      >
                        <PopoverTrigger asChild>
                          <DropdownMenuItem 
                            onSelect={(e) => e.preventDefault()}
                            className="text-blue-600 cursor-pointer"
                          >
                            <Settings className="h-4 w-4 mr-2" />
                            Show more settings
                            <span className="ml-auto text-xs text-muted-foreground">
                              Ctrl+Shift+,
                            </span>
                          </DropdownMenuItem>
                        </PopoverTrigger>
                        <PopoverContent 
                          className="w-80 p-0" 
                          align="start"
                          side="bottom"
                          sideOffset={8}
                        >
                          <div className="p-4 border-b bg-gray-50">
                            <h3 className="font-semibold text-sm flex items-center gap-2">
                              <span>{blockDefinition.icon}</span>
                              {blockDefinition.displayName} Settings
                            </h3>
                            {blockDefinition.description && (
                              <p className="text-xs text-muted-foreground mt-1">
                                {blockDefinition.description}
                              </p>
                            )}
                          </div>
                          <div className="max-h-96 overflow-y-auto">
                            <DynamicBlockEditor
                              block={block}
                              blockType={blockDefinition}
                              schema={blockDefinition.configSchema}
                              onUpdate={onUpdate || (() => {})}
                            />
                          </div>
                        </PopoverContent>
                      </Popover>
                      <DropdownMenuSeparator />
                    </>
                  )}

                  {/* Copy */}
                  {onDuplicate && (
                    <DropdownMenuItem onClick={onDuplicate}>
                      <Copy className="h-4 w-4 mr-2" />
                      Copy
                      <span className="ml-auto text-xs text-muted-foreground">
                        Ctrl+C
                      </span>
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuItem onClick={onDuplicate}>
                    <Copy className="h-4 w-4 mr-2" />
                    Duplicate
                    <span className="ml-auto text-xs text-muted-foreground">
                      Ctrl+Shift+D
                    </span>
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />

                  {/* Insert Actions */}
                  {onInsertBefore && (
                    <DropdownMenuItem onClick={onInsertBefore}>
                      <Plus className="h-4 w-4 mr-2" />
                      Insert before
                      <span className="ml-auto text-xs text-muted-foreground">
                        Ctrl+Alt+T
                      </span>
                    </DropdownMenuItem>
                  )}

                  {onInsertAfter && (
                    <DropdownMenuItem onClick={onInsertAfter}>
                      <Plus className="h-4 w-4 mr-2" />
                      Insert after
                      <span className="ml-auto text-xs text-muted-foreground">
                        Ctrl+Alt+Y
                      </span>
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuSeparator />

                  {/* Move Actions */}
                  {onMoveUp && (
                    <DropdownMenuItem onClick={onMoveUp}>
                      <MoveUp className="h-4 w-4 mr-2" />
                      Move up
                    </DropdownMenuItem>
                  )}

                  {onMoveDown && (
                    <DropdownMenuItem onClick={onMoveDown}>
                      <MoveDown className="h-4 w-4 mr-2" />
                      Move down
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuSeparator />

                  {/* Visibility */}
                  {onToggleVisibility && (
                    <DropdownMenuItem onClick={onToggleVisibility}>
                      {block.hidden ? (
                        <>
                          <Eye className="h-4 w-4 mr-2" />
                          Show block
                        </>
                      ) : (
                        <>
                          <EyeOff className="h-4 w-4 mr-2" />
                          Hide block
                        </>
                      )}
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuSeparator />

                  {/* Remove */}
                  {onDelete && (
                    <DropdownMenuItem 
                      onClick={onDelete}
                      className="text-red-600 focus:text-red-600"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Remove block
                      <span className="ml-auto text-xs text-muted-foreground">
                        Shift+Alt+Z
                      </span>
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      )}

      {/* Insert Indicators */}
      {showToolbar && (
        <>
          {/* Insert Before */}
          {onInsertBefore && (
            <div className="absolute top-0 left-0 right-0 transform -translate-y-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <div className="h-1 bg-blue-500 rounded-full mx-4 relative">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onInsertBefore}
                  className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 h-6 w-6 p-0 bg-blue-500 hover:bg-blue-600 text-white rounded-full"
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </div>
            </div>
          )}

          {/* Insert After */}
          {onInsertAfter && (
            <div className="absolute bottom-0 left-0 right-0 transform translate-y-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <div className="h-1 bg-blue-500 rounded-full mx-4 relative">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onInsertAfter}
                  className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 h-6 w-6 p-0 bg-blue-500 hover:bg-blue-600 text-white rounded-full"
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  )
}
