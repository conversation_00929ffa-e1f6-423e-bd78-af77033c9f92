'use client'

import React from 'react'
import { PageBlock } from '../types'
import { ThemedBlock, ThemedComponents } from '@/lib/theme-generator/components/themed-ui'
import { useTheme } from '@/lib/theme-generator/theme-context'
import { cn } from '@/lib/utils'

interface ThemedBlockRendererProps {
  block: PageBlock
  pageId?: string
  isEditing?: boolean
  onBlockUpdate?: (blockId: string, updates: Partial<PageBlock>) => void
}

export function ThemedBlockRenderer({
  block,
  pageId,
  isEditing = false,
  onBlockUpdate
}: ThemedBlockRendererProps) {
  const { currentTheme } = useTheme()

  // Get theme customizations for this block
  const blockThemeId = block.styling?.themeId || currentTheme?.id
  const blockCustomizations = block.styling?.themeCustomizations || {}

  // Render block content with theme
  const renderBlockContent = () => {
    switch (block.type) {
      case 'heading':
        return (
          <ThemedComponents.Card
            blockId={block.id}
            themeId={blockThemeId}
            customizations={blockCustomizations}
            className={cn('p-6', block.styling?.className)}
          >
            <ThemedComponents.CardContent>
              <h1 
                className={cn(
                  'text-4xl font-bold',
                  block.configuration?.level === 1 && 'text-4xl',
                  block.configuration?.level === 2 && 'text-3xl',
                  block.configuration?.level === 3 && 'text-2xl',
                  block.configuration?.level === 4 && 'text-xl',
                  block.configuration?.level === 5 && 'text-lg',
                  block.configuration?.level === 6 && 'text-base',
                  block.configuration?.alignment === 'center' && 'text-center',
                  block.configuration?.alignment === 'right' && 'text-right'
                )}
              >
                {block.configuration?.text || 'Heading'}
              </h1>
            </ThemedComponents.CardContent>
          </ThemedComponents.Card>
        )

      case 'text':
        return (
          <ThemedComponents.Card
            blockId={block.id}
            themeId={blockThemeId}
            customizations={blockCustomizations}
            className={cn('p-6', block.styling?.className)}
          >
            <ThemedComponents.CardContent>
              <div 
                className="prose max-w-none"
                dangerouslySetInnerHTML={{ 
                  __html: block.configuration?.content || '<p>Text content</p>' 
                }}
              />
            </ThemedComponents.CardContent>
          </ThemedComponents.Card>
        )

      case 'button':
        return (
          <ThemedComponents.Card
            blockId={block.id}
            themeId={blockThemeId}
            customizations={blockCustomizations}
            className={cn('p-6', block.styling?.className)}
          >
            <ThemedComponents.CardContent className="flex justify-center">
              <ThemedComponents.Button
                variant={block.configuration?.variant || 'default'}
                size={block.configuration?.size || 'default'}
                blockId={block.id}
                themeId={blockThemeId}
                customizations={blockCustomizations}
                onClick={() => {
                  if (block.configuration?.link) {
                    window.open(block.configuration.link, '_blank')
                  }
                }}
              >
                {block.configuration?.text || 'Button'}
              </ThemedComponents.Button>
            </ThemedComponents.CardContent>
          </ThemedComponents.Card>
        )

      case 'hero':
        return (
          <ThemedComponents.Card
            blockId={block.id}
            themeId={blockThemeId}
            customizations={blockCustomizations}
            className={cn(
              'relative overflow-hidden',
              block.styling?.className
            )}
            style={{
              backgroundImage: block.configuration?.backgroundImage 
                ? `url(${block.configuration.backgroundImage})`
                : undefined,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              minHeight: '400px'
            }}
          >
            <div className="absolute inset-0 bg-black/20" />
            <ThemedComponents.CardContent className="relative z-10 flex items-center justify-center min-h-[400px] text-center text-white">
              <div className="max-w-4xl mx-auto space-y-6">
                <h1 className="text-5xl font-bold">
                  {block.configuration?.title || 'Hero Title'}
                </h1>
                {block.configuration?.subtitle && (
                  <p className="text-xl opacity-90">
                    {block.configuration.subtitle}
                  </p>
                )}
                {block.configuration?.ctaText && (
                  <ThemedComponents.Button
                    size="lg"
                    blockId={block.id}
                    themeId={blockThemeId}
                    customizations={blockCustomizations}
                    onClick={() => {
                      if (block.configuration?.ctaLink) {
                        window.open(block.configuration.ctaLink, '_blank')
                      }
                    }}
                  >
                    {block.configuration.ctaText}
                  </ThemedComponents.Button>
                )}
              </div>
            </ThemedComponents.CardContent>
          </ThemedComponents.Card>
        )

      case 'featured-products':
        return (
          <ThemedComponents.Card
            blockId={block.id}
            themeId={blockThemeId}
            customizations={blockCustomizations}
            className={cn('p-6', block.styling?.className)}
          >
            <ThemedComponents.CardHeader>
              <ThemedComponents.CardTitle className="text-center text-3xl">
                {block.configuration?.title || 'Featured Products'}
              </ThemedComponents.CardTitle>
            </ThemedComponents.CardHeader>
            <ThemedComponents.CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {Array.from({ length: block.configuration?.limit || 4 }).map((_, index) => (
                  <ThemedComponents.Card key={index} className="overflow-hidden">
                    <div className="aspect-square bg-gray-200" />
                    <ThemedComponents.CardContent className="p-4">
                      <h3 className="font-medium">Product {index + 1}</h3>
                      <p className="text-sm text-muted-foreground">Product description</p>
                      {block.configuration?.showPrice && (
                        <p className="font-bold mt-2">R199.99</p>
                      )}
                      {block.configuration?.showAddToCart && (
                        <ThemedComponents.Button 
                          size="sm" 
                          className="w-full mt-2"
                          blockId={block.id}
                          themeId={blockThemeId}
                          customizations={blockCustomizations}
                        >
                          Add to Cart
                        </ThemedComponents.Button>
                      )}
                    </ThemedComponents.CardContent>
                  </ThemedComponents.Card>
                ))}
              </div>
            </ThemedComponents.CardContent>
          </ThemedComponents.Card>
        )

      case 'newsletter':
        return (
          <ThemedComponents.Card
            blockId={block.id}
            themeId={blockThemeId}
            customizations={blockCustomizations}
            className={cn('p-6 text-center', block.styling?.className)}
          >
            <ThemedComponents.CardContent className="max-w-md mx-auto space-y-4">
              <h3 className="text-2xl font-bold">
                {block.configuration?.title || 'Stay Updated'}
              </h3>
              {block.configuration?.description && (
                <p className="text-muted-foreground">
                  {block.configuration.description}
                </p>
              )}
              <div className="flex space-x-2">
                <ThemedComponents.Input
                  placeholder={block.configuration?.placeholder || 'Enter your email'}
                  blockId={block.id}
                  themeId={blockThemeId}
                  customizations={blockCustomizations}
                  className="flex-1"
                />
                <ThemedComponents.Button
                  blockId={block.id}
                  themeId={blockThemeId}
                  customizations={blockCustomizations}
                >
                  Subscribe
                </ThemedComponents.Button>
              </div>
            </ThemedComponents.CardContent>
          </ThemedComponents.Card>
        )

      case 'testimonials':
        return (
          <ThemedComponents.Card
            blockId={block.id}
            themeId={blockThemeId}
            customizations={blockCustomizations}
            className={cn('p-6', block.styling?.className)}
          >
            <ThemedComponents.CardHeader>
              <ThemedComponents.CardTitle className="text-center text-3xl">
                {block.configuration?.title || 'What Our Customers Say'}
              </ThemedComponents.CardTitle>
            </ThemedComponents.CardHeader>
            <ThemedComponents.CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {Array.from({ length: 3 }).map((_, index) => (
                  <ThemedComponents.Card key={index}>
                    <ThemedComponents.CardContent className="p-6">
                      <div className="space-y-4">
                        {block.configuration?.showRatings && (
                          <div className="flex text-yellow-400">
                            {'★'.repeat(5)}
                          </div>
                        )}
                        <p className="text-sm">
                          "Great quality clothes for kids. Highly recommended!"
                        </p>
                        <div className="flex items-center space-x-2">
                          <div className="w-8 h-8 bg-gray-300 rounded-full" />
                          <div>
                            <p className="text-sm font-medium">Customer {index + 1}</p>
                            <p className="text-xs text-muted-foreground">Verified Buyer</p>
                          </div>
                        </div>
                      </div>
                    </ThemedComponents.CardContent>
                  </ThemedComponents.Card>
                ))}
              </div>
            </ThemedComponents.CardContent>
          </ThemedComponents.Card>
        )

      case 'alert':
        return (
          <ThemedComponents.Alert
            variant={block.configuration?.variant || 'default'}
            blockId={block.id}
            themeId={blockThemeId}
            customizations={blockCustomizations}
            className={cn(block.styling?.className)}
          >
            <ThemedComponents.AlertTitle>
              {block.configuration?.title || 'Alert'}
            </ThemedComponents.AlertTitle>
            <ThemedComponents.AlertDescription>
              {block.configuration?.description || 'This is an alert message.'}
            </ThemedComponents.AlertDescription>
          </ThemedComponents.Alert>
        )

      default:
        return (
          <ThemedComponents.Card
            blockId={block.id}
            themeId={blockThemeId}
            customizations={blockCustomizations}
            className={cn('p-6', block.styling?.className)}
          >
            <ThemedComponents.CardContent>
              <p className="text-muted-foreground">
                Unknown block type: {block.type}
              </p>
            </ThemedComponents.CardContent>
          </ThemedComponents.Card>
        )
    }
  }

  return (
    <ThemedBlock
      blockId={block.id}
      themeId={blockThemeId}
      customizations={blockCustomizations}
      className={cn(
        'themed-block-wrapper',
        !block.isVisible && 'opacity-50',
        isEditing && 'ring-2 ring-blue-500 ring-opacity-50',
        block.styling?.className
      )}
      style={{
        ...block.styling,
        ...block.responsive
      }}
    >
      {renderBlockContent()}
      
      {/* Theme customization overlay for editing mode */}
      {isEditing && (
        <div className="absolute top-2 right-2 z-10">
          <ThemedComponents.Badge variant="secondary">
            Theme: {currentTheme?.name || 'Default'}
          </ThemedComponents.Badge>
        </div>
      )}
    </ThemedBlock>
  )
}

// Hook for block theme management
export function useBlockTheme(blockId: string) {
  const { currentTheme, applyTheme, removeTheme } = useTheme()

  const applyThemeToBlock = async (themeId: string) => {
    await applyTheme('block', blockId)
  }

  const removeThemeFromBlock = async () => {
    await removeTheme('block', blockId)
  }

  return {
    currentTheme,
    applyThemeToBlock,
    removeThemeFromBlock
  }
}
