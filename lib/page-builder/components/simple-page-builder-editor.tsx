'use client'

import React, { useState } from 'react'
import { usePageBuilder } from '../context'
import { PageBuilderCanvas } from './page-builder-canvas'
import { FloatingAIChat } from './floating-ai-chat'
import { Button } from '@/components/ui/button'
import {
  Eye,
  EyeOff,
  Smartphone,
  Tablet,
  Monitor,
  Undo,
  Redo,
  Sparkles
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface SimplePageBuilderEditorProps {
  className?: string
}

export function SimplePageBuilderEditor({
  className
}: SimplePageBuilderEditorProps) {
  const {
    state,
    setPreviewMode,
    setDevicePreview,
    undo,
    redo
  } = usePageBuilder()

  const { isPreviewMode, devicePreview, hasUnsavedChanges, canUndo, canRedo } = state
  const [isAIChatOpen, setIsAIChatOpen] = useState(false)

  // Device preview options
  const deviceOptions = [
    { value: 'desktop' as const, icon: Monitor, label: 'Desktop' },
    { value: 'tablet' as const, icon: Tablet, label: 'Tablet' },
    { value: 'mobile' as const, icon: Smartphone, label: 'Mobile' },
  ]

  return (
    <div className={cn('h-full flex flex-col bg-gray-50', className)}>
      {/* Top Toolbar */}
      <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
        <div className="flex items-center space-x-4">
          {/* Status Indicator */}
          <div className="text-sm text-muted-foreground">
            {hasUnsavedChanges ? 'Unsaved changes' : 'All changes saved'}
          </div>
        </div>

        {/* Center Controls */}
        <div className="flex items-center space-x-2">
          {/* Undo/Redo */}
          <Button
            variant="ghost"
            size="sm"
            onClick={undo}
            disabled={!canUndo}
            title="Undo"
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={redo}
            disabled={!canRedo}
            title="Redo"
          >
            <Redo className="h-4 w-4" />
          </Button>

          {/* Device Preview */}
          <div className="flex items-center border rounded-md">
            {deviceOptions.map(({ value, icon: Icon, label }) => (
              <Button
                key={value}
                variant="ghost"
                size="sm"
                onClick={() => setDevicePreview(value)}
                className={cn(
                  'rounded-none border-0',
                  devicePreview === value && 'bg-gray-100'
                )}
                title={label}
              >
                <Icon className="h-4 w-4" />
              </Button>
            ))}
          </div>

          {/* AI Designer Toggle */}
          <Button
            variant={isAIChatOpen ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setIsAIChatOpen(!isAIChatOpen)}
            className={cn(
              'relative',
              isAIChatOpen && 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white'
            )}
            title="AI Page Designer"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            AI Designer
            {isAIChatOpen && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            )}
          </Button>

          {/* Preview Mode Toggle */}
          <Button
            variant={isPreviewMode ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setPreviewMode(!isPreviewMode)}
          >
            {isPreviewMode ? (
              <>
                <EyeOff className="h-4 w-4 mr-2" />
                Exit Preview
              </>
            ) : (
              <>
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </>
            )}
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          {/* Placeholder for future controls */}
        </div>
      </div>

      {/* Main Content Area - Just the Canvas */}
      <div className="flex-1 overflow-hidden">
        <PageBuilderCanvas
          devicePreview={devicePreview}
          isPreviewMode={isPreviewMode}
        />
      </div>

      {/* Floating AI Chat */}
      <FloatingAIChat
        isOpen={isAIChatOpen}
        onClose={() => setIsAIChatOpen(false)}
      />
    </div>
  )
}
