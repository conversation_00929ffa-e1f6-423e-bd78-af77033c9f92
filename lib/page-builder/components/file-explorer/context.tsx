'use client'

import React, { createContext, useContext, useReducer, useCallback } from 'react'
import { FileNode, FileExplorerState, FileExplorerAction, FileExplorerConfig } from './types'

const initialState: FileExplorerState = {
  files: [],
  selectedFileId: null,
  expandedFolders: new Set(),
  openFiles: [],
  activeFileId: null,
  searchQuery: '',
  isSearching: false,
  contextMenu: {
    isOpen: false,
    x: 0,
    y: 0,
    targetId: null,
    targetType: 'root'
  },
  dragState: {
    isDragging: false,
    draggedId: null,
    dropTargetId: null
  },
  clipboard: {
    operation: null,
    fileId: null
  },
  showHiddenFiles: false,
  sortBy: 'name',
  sortOrder: 'asc'
}

function fileExplorerReducer(state: FileExplorerState, action: FileExplorerAction): FileExplorerState {
  switch (action.type) {
    case 'SET_FILES':
      return { ...state, files: action.payload }

    case 'ADD_FILE': {
      const newFile = action.payload
      return { ...state, files: [...state.files, newFile] }
    }

    case 'UPDATE_FILE': {
      const { id, updates } = action.payload
      return {
        ...state,
        files: updateFileInTree(state.files, id, updates)
      }
    }

    case 'DELETE_FILE': {
      const fileId = action.payload
      return {
        ...state,
        files: removeFileFromTree(state.files, fileId),
        selectedFileId: state.selectedFileId === fileId ? null : state.selectedFileId,
        openFiles: state.openFiles.filter(id => id !== fileId),
        activeFileId: state.activeFileId === fileId ? null : state.activeFileId
      }
    }

    case 'SELECT_FILE':
      return { ...state, selectedFileId: action.payload }

    case 'TOGGLE_FOLDER': {
      const folderId = action.payload
      const newExpanded = new Set(state.expandedFolders)
      if (newExpanded.has(folderId)) {
        newExpanded.delete(folderId)
      } else {
        newExpanded.add(folderId)
      }
      return { ...state, expandedFolders: newExpanded }
    }

    case 'EXPAND_FOLDER': {
      const newExpanded = new Set(state.expandedFolders)
      newExpanded.add(action.payload)
      return { ...state, expandedFolders: newExpanded }
    }

    case 'COLLAPSE_FOLDER': {
      const newExpanded = new Set(state.expandedFolders)
      newExpanded.delete(action.payload)
      return { ...state, expandedFolders: newExpanded }
    }

    case 'OPEN_FILE': {
      const fileId = action.payload
      if (!state.openFiles.includes(fileId)) {
        return {
          ...state,
          openFiles: [...state.openFiles, fileId],
          activeFileId: fileId
        }
      }
      return { ...state, activeFileId: fileId }
    }

    case 'CLOSE_FILE': {
      const fileId = action.payload
      const newOpenFiles = state.openFiles.filter(id => id !== fileId)
      const newActiveFile = state.activeFileId === fileId 
        ? (newOpenFiles.length > 0 ? newOpenFiles[newOpenFiles.length - 1] : null)
        : state.activeFileId
      
      return {
        ...state,
        openFiles: newOpenFiles,
        activeFileId: newActiveFile
      }
    }

    case 'SET_ACTIVE_FILE':
      return { ...state, activeFileId: action.payload }

    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload }

    case 'TOGGLE_SEARCH':
      return { 
        ...state, 
        isSearching: !state.isSearching,
        searchQuery: !state.isSearching ? state.searchQuery : ''
      }

    case 'SHOW_CONTEXT_MENU':
      return {
        ...state,
        contextMenu: {
          isOpen: true,
          ...action.payload
        }
      }

    case 'HIDE_CONTEXT_MENU':
      return {
        ...state,
        contextMenu: {
          ...state.contextMenu,
          isOpen: false
        }
      }

    case 'START_DRAG':
      return {
        ...state,
        dragState: {
          isDragging: true,
          draggedId: action.payload,
          dropTargetId: null
        }
      }

    case 'SET_DROP_TARGET':
      return {
        ...state,
        dragState: {
          ...state.dragState,
          dropTargetId: action.payload
        }
      }

    case 'END_DRAG':
      return {
        ...state,
        dragState: {
          isDragging: false,
          draggedId: null,
          dropTargetId: null
        }
      }

    case 'SET_CLIPBOARD':
      return {
        ...state,
        clipboard: action.payload
      }

    case 'CLEAR_CLIPBOARD':
      return {
        ...state,
        clipboard: {
          operation: null,
          fileId: null
        }
      }

    case 'TOGGLE_HIDDEN_FILES':
      return { ...state, showHiddenFiles: !state.showHiddenFiles }

    case 'SET_SORT':
      return { ...state, ...action.payload }

    case 'START_RENAME':
      return {
        ...state,
        files: updateFileInTree(state.files, action.payload, { isEditing: true })
      }

    case 'END_RENAME': {
      const { id, newName } = action.payload
      return {
        ...state,
        files: updateFileInTree(state.files, id, { 
          name: newName, 
          isEditing: false 
        })
      }
    }

    case 'CANCEL_RENAME':
      return {
        ...state,
        files: updateFileInTree(state.files, action.payload, { isEditing: false })
      }

    default:
      return state
  }
}

// Helper functions
function updateFileInTree(files: FileNode[], id: string, updates: Partial<FileNode>): FileNode[] {
  return files.map(file => {
    if (file.id === id) {
      return { ...file, ...updates }
    }
    if (file.children) {
      return {
        ...file,
        children: updateFileInTree(file.children, id, updates)
      }
    }
    return file
  })
}

function removeFileFromTree(files: FileNode[], id: string): FileNode[] {
  return files.filter(file => {
    if (file.id === id) {
      return false
    }
    if (file.children) {
      file.children = removeFileFromTree(file.children, id)
    }
    return true
  })
}

interface FileExplorerContextType {
  state: FileExplorerState
  dispatch: React.Dispatch<FileExplorerAction>
  config: FileExplorerConfig
  // File operations
  selectFile: (id: string | null) => void
  toggleFolder: (id: string) => void
  openFile: (id: string) => void
  closeFile: (id: string) => void
  setActiveFile: (id: string | null) => void
  // Search
  setSearchQuery: (query: string) => void
  toggleSearch: () => void
  // Context menu
  showContextMenu: (x: number, y: number, targetId: string | null, targetType: 'file' | 'folder' | 'root') => void
  hideContextMenu: () => void
  // Drag and drop
  startDrag: (id: string) => void
  setDropTarget: (id: string | null) => void
  endDrag: () => void
  // Clipboard
  copyFile: (id: string) => void
  cutFile: (id: string) => void
  pasteFile: (targetId?: string) => void
  // File management
  createFile: (name: string, parentId?: string) => void
  createFolder: (name: string, parentId?: string) => void
  renameFile: (id: string, newName: string) => void
  deleteFile: (id: string) => void
  // Settings
  toggleHiddenFiles: () => void
  setSorting: (sortBy: FileExplorerState['sortBy'], sortOrder: FileExplorerState['sortOrder']) => void
  // Utilities
  findFileById: (id: string) => FileNode | null
  getFileContent: (id: string) => Promise<string>
  saveFileContent: (id: string, content: string) => Promise<void>
}

const FileExplorerContext = createContext<FileExplorerContextType | undefined>(undefined)

export function useFileExplorer() {
  const context = useContext(FileExplorerContext)
  if (context === undefined) {
    throw new Error('useFileExplorer must be used within a FileExplorerProvider')
  }
  return context
}

interface FileExplorerProviderProps {
  children: React.ReactNode
  config?: Partial<FileExplorerConfig>
}

const defaultConfig: FileExplorerConfig = {
  rootPath: '/',
  showFileSize: true,
  showLastModified: true,
  enableDragDrop: true,
  enableContextMenu: true,
  enableSearch: true,
  enableNewFile: true,
  enableNewFolder: true,
  enableRename: true,
  enableDelete: true,
  enableCopyPaste: true
}

export function FileExplorerProvider({ children, config: userConfig }: FileExplorerProviderProps) {
  const [state, dispatch] = useReducer(fileExplorerReducer, initialState)
  const config = { ...defaultConfig, ...userConfig }

  // Action creators
  const selectFile = useCallback((id: string | null) => {
    dispatch({ type: 'SELECT_FILE', payload: id })
  }, [])

  const toggleFolder = useCallback((id: string) => {
    dispatch({ type: 'TOGGLE_FOLDER', payload: id })
  }, [])

  const openFile = useCallback((id: string) => {
    dispatch({ type: 'OPEN_FILE', payload: id })
  }, [])

  const closeFile = useCallback((id: string) => {
    dispatch({ type: 'CLOSE_FILE', payload: id })
  }, [])

  const setActiveFile = useCallback((id: string | null) => {
    dispatch({ type: 'SET_ACTIVE_FILE', payload: id })
  }, [])

  const setSearchQuery = useCallback((query: string) => {
    dispatch({ type: 'SET_SEARCH_QUERY', payload: query })
  }, [])

  const toggleSearch = useCallback(() => {
    dispatch({ type: 'TOGGLE_SEARCH' })
  }, [])

  const showContextMenu = useCallback((x: number, y: number, targetId: string | null, targetType: 'file' | 'folder' | 'root') => {
    dispatch({ type: 'SHOW_CONTEXT_MENU', payload: { x, y, targetId, targetType } })
  }, [])

  const hideContextMenu = useCallback(() => {
    dispatch({ type: 'HIDE_CONTEXT_MENU' })
  }, [])

  const startDrag = useCallback((id: string) => {
    dispatch({ type: 'START_DRAG', payload: id })
  }, [])

  const setDropTarget = useCallback((id: string | null) => {
    dispatch({ type: 'SET_DROP_TARGET', payload: id })
  }, [])

  const endDrag = useCallback(() => {
    dispatch({ type: 'END_DRAG' })
  }, [])

  const copyFile = useCallback((id: string) => {
    dispatch({ type: 'SET_CLIPBOARD', payload: { operation: 'copy', fileId: id } })
  }, [])

  const cutFile = useCallback((id: string) => {
    dispatch({ type: 'SET_CLIPBOARD', payload: { operation: 'cut', fileId: id } })
  }, [])

  const pasteFile = useCallback((targetId?: string) => {
    // Implementation would handle the actual paste operation
    dispatch({ type: 'CLEAR_CLIPBOARD' })
  }, [])

  const createFile = useCallback((name: string, parentId?: string) => {
    const newFile: FileNode = {
      id: `file-${Date.now()}-${Math.random()}`,
      name,
      type: 'file',
      path: parentId ? `${parentId}/${name}` : name,
      parentId,
      content: '',
      lastModified: new Date(),
      size: 0,
      isNew: true
    }
    dispatch({ type: 'ADD_FILE', payload: newFile })
  }, [])

  const createFolder = useCallback((name: string, parentId?: string) => {
    const newFolder: FileNode = {
      id: `folder-${Date.now()}-${Math.random()}`,
      name,
      type: 'folder',
      path: parentId ? `${parentId}/${name}` : name,
      parentId,
      children: [],
      lastModified: new Date(),
      isNew: true
    }
    dispatch({ type: 'ADD_FILE', payload: newFolder })
  }, [])

  const renameFile = useCallback((id: string, newName: string) => {
    dispatch({ type: 'END_RENAME', payload: { id, newName } })
  }, [])

  const deleteFile = useCallback((id: string) => {
    dispatch({ type: 'DELETE_FILE', payload: id })
  }, [])

  const toggleHiddenFiles = useCallback(() => {
    dispatch({ type: 'TOGGLE_HIDDEN_FILES' })
  }, [])

  const setSorting = useCallback((sortBy: FileExplorerState['sortBy'], sortOrder: FileExplorerState['sortOrder']) => {
    dispatch({ type: 'SET_SORT', payload: { sortBy, sortOrder } })
  }, [])

  const findFileById = useCallback((id: string): FileNode | null => {
    const findInTree = (files: FileNode[]): FileNode | null => {
      for (const file of files) {
        if (file.id === id) return file
        if (file.children) {
          const found = findInTree(file.children)
          if (found) return found
        }
      }
      return null
    }
    return findInTree(state.files)
  }, [state.files])

  const getFileContent = useCallback(async (id: string): Promise<string> => {
    const file = findFileById(id)
    return file?.content || ''
  }, [findFileById])

  const saveFileContent = useCallback(async (id: string, content: string): Promise<void> => {
    dispatch({ 
      type: 'UPDATE_FILE', 
      payload: { 
        id, 
        updates: { 
          content, 
          lastModified: new Date(),
          size: content.length 
        } 
      } 
    })
  }, [])

  const contextValue: FileExplorerContextType = {
    state,
    dispatch,
    config,
    selectFile,
    toggleFolder,
    openFile,
    closeFile,
    setActiveFile,
    setSearchQuery,
    toggleSearch,
    showContextMenu,
    hideContextMenu,
    startDrag,
    setDropTarget,
    endDrag,
    copyFile,
    cutFile,
    pasteFile,
    createFile,
    createFolder,
    renameFile,
    deleteFile,
    toggleHiddenFiles,
    setSorting,
    findFileById,
    getFileContent,
    saveFileContent
  }

  return (
    <FileExplorerContext.Provider value={contextValue}>
      {children}
    </FileExplorerContext.Provider>
  )
}
