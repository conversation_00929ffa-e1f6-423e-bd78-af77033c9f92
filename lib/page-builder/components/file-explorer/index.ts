// File Explorer Components and Utilities
// Export all file explorer related components and utilities

// Core Components
export { FileExplorer } from './file-explorer'
export { FileExplorerWithEditor } from './file-explorer-with-editor'
export { FileTreeNode } from './file-tree-node'
export { FileExplorerHeader } from './file-explorer-header'
export { ContextMenu } from './context-menu'

// Context and State Management
export { 
  FileExplorerProvider, 
  useFileExplorer 
} from './context'

// Types
export type { 
  FileNode, 
  FileExplorerState, 
  FileExplorerAction, 
  FileExplorerConfig,
  FileOperation,
  FileIconMapping,
  FolderIconMapping
} from './types'

// Icons and Utilities
export { FileIcon, getFileType } from './file-icons'
export { 
  getFileLanguage,
  formatFileSize,
  getParentPath,
  getFileName,
  joinPaths,
  isValidFileName,
  isHiddenFile
} from './types'
