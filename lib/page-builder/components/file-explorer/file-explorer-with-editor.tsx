'use client'

import React, { useState, useCallback } from 'react'
import { PanelGroup, Panel, PanelResizeHandle } from 'react-resizable-panels'
import { X, Save, RotateCcw } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { FileNode } from './types'
import { FileExplorer } from './file-explorer'
import { FileExplorerProvider } from './context'
import { CodeMirrorEditor } from '../code-editor/codemirror-editor'
import { getFileType, FileIcon } from './file-icons'

interface FileTab {
  id: string
  file: FileNode
  content: string
  isDirty: boolean
  originalContent: string
}

interface FileExplorerWithEditorProps {
  className?: string
  initialFiles?: FileNode[]
  onSave?: (file: FileNode, content: string) => Promise<void>
  theme?: 'light' | 'dark'
}

export function FileExplorerWithEditor({ 
  className, 
  initialFiles = [],
  onSave,
  theme = 'light'
}: FileExplorerWithEditorProps) {
  const [openTabs, setOpenTabs] = useState<FileTab[]>([])
  const [activeTabId, setActiveTabId] = useState<string | null>(null)
  const [selectedFile, setSelectedFile] = useState<FileNode | null>(null)

  // Handle file open from explorer
  const handleFileOpen = useCallback(async (file: FileNode) => {
    // Check if file is already open
    const existingTab = openTabs.find(tab => tab.id === file.id)
    
    if (existingTab) {
      setActiveTabId(file.id)
      return
    }

    // Create new tab
    const content = file.content || ''
    const newTab: FileTab = {
      id: file.id,
      file,
      content,
      isDirty: false,
      originalContent: content
    }

    setOpenTabs(prev => [...prev, newTab])
    setActiveTabId(file.id)
  }, [openTabs])

  // Handle tab close
  const handleTabClose = useCallback((tabId: string) => {
    const tab = openTabs.find(t => t.id === tabId)
    
    if (tab?.isDirty) {
      const confirmed = confirm(`${tab.file.name} has unsaved changes. Close anyway?`)
      if (!confirmed) return
    }

    setOpenTabs(prev => prev.filter(t => t.id !== tabId))
    
    if (activeTabId === tabId) {
      const remainingTabs = openTabs.filter(t => t.id !== tabId)
      setActiveTabId(remainingTabs.length > 0 ? remainingTabs[remainingTabs.length - 1].id : null)
    }
  }, [openTabs, activeTabId])

  // Handle content change
  const handleContentChange = useCallback((tabId: string, newContent: string) => {
    setOpenTabs(prev => prev.map(tab => {
      if (tab.id === tabId) {
        return {
          ...tab,
          content: newContent,
          isDirty: newContent !== tab.originalContent
        }
      }
      return tab
    }))
  }, [])

  // Handle save
  const handleSave = useCallback(async (tabId: string) => {
    const tab = openTabs.find(t => t.id === tabId)
    if (!tab) return

    try {
      await onSave?.(tab.file, tab.content)
      
      setOpenTabs(prev => prev.map(t => {
        if (t.id === tabId) {
          return {
            ...t,
            isDirty: false,
            originalContent: t.content
          }
        }
        return t
      }))
    } catch (error) {
      console.error('Failed to save file:', error)
      alert('Failed to save file. Please try again.')
    }
  }, [openTabs, onSave])

  // Handle revert
  const handleRevert = useCallback((tabId: string) => {
    const tab = openTabs.find(t => t.id === tabId)
    if (!tab) return

    const confirmed = confirm('Revert all changes to the last saved version?')
    if (!confirmed) return

    setOpenTabs(prev => prev.map(t => {
      if (t.id === tabId) {
        return {
          ...t,
          content: t.originalContent,
          isDirty: false
        }
      }
      return t
    }))
  }, [openTabs])

  // Get active tab
  const activeTab = openTabs.find(tab => tab.id === activeTabId)

  // Handle keyboard shortcuts
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's' && activeTabId) {
        e.preventDefault()
        handleSave(activeTabId)
      }
      
      if ((e.ctrlKey || e.metaKey) && e.key === 'w' && activeTabId) {
        e.preventDefault()
        handleTabClose(activeTabId)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [activeTabId, handleSave, handleTabClose])

  return (
    <FileExplorerProvider
      config={{
        rootPath: '/',
        showFileSize: true,
        showLastModified: true,
        enableDragDrop: true,
        enableContextMenu: true,
        enableSearch: true,
        enableNewFile: true,
        enableNewFolder: true,
        enableRename: true,
        enableDelete: true,
        enableCopyPaste: true
      }}
    >
      <div className={cn('h-full flex flex-col bg-gray-50 dark:bg-gray-900', className)}>
        <PanelGroup direction="horizontal" className="flex-1">
          {/* File Explorer Panel */}
          <Panel defaultSize={25} minSize={15} maxSize={40}>
            <FileExplorer
              onFileOpen={handleFileOpen}
              onFileSelect={setSelectedFile}
              className="h-full border-r border-gray-200 dark:border-gray-700"
            />
          </Panel>

          <PanelResizeHandle className="w-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors" />

          {/* Editor Panel */}
          <Panel defaultSize={75}>
            <div className="h-full flex flex-col">
              {openTabs.length === 0 ? (
                // Welcome Screen
                <div className="flex-1 flex items-center justify-center bg-white dark:bg-gray-800">
                  <div className="text-center text-gray-500 dark:text-gray-400">
                    <div className="text-6xl mb-4">📁</div>
                    <h3 className="text-lg font-medium mb-2">Welcome to File Explorer</h3>
                    <p className="text-sm">
                      Open a file from the explorer to start editing
                    </p>
                    {selectedFile && (
                      <div className="mt-4">
                        <p className="text-xs mb-2">Selected: {selectedFile.name}</p>
                        <Button
                          size="sm"
                          onClick={() => handleFileOpen(selectedFile)}
                          disabled={selectedFile.type !== 'file'}
                        >
                          Open File
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <>
                  {/* Tab Bar */}
                  <div className="flex items-center bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 overflow-x-auto">
                    <Tabs value={activeTabId || ''} onValueChange={setActiveTabId} className="flex-1">
                      <TabsList className="h-auto p-0 bg-transparent">
                        {openTabs.map((tab) => (
                          <TabsTrigger
                            key={tab.id}
                            value={tab.id}
                            className={cn(
                              'flex items-center space-x-2 px-3 py-2 text-sm border-r border-gray-200 dark:border-gray-700 rounded-none',
                              'data-[state=active]:bg-white dark:data-[state=active]:bg-gray-900',
                              tab.isDirty && 'italic'
                            )}
                          >
                            <FileIcon
                              fileName={tab.file.name}
                              size={14}
                            />
                            <span className="truncate max-w-32">
                              {tab.file.name}
                            </span>
                            {tab.isDirty && (
                              <Badge variant="secondary" className="h-4 w-4 p-0 rounded-full">
                                •
                              </Badge>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 ml-1 hover:bg-gray-200 dark:hover:bg-gray-600"
                              onClick={(e) => {
                                e.stopPropagation()
                                handleTabClose(tab.id)
                              }}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </TabsTrigger>
                        ))}
                      </TabsList>
                    </Tabs>
                  </div>

                  {/* Editor Content */}
                  <div className="flex-1 flex flex-col">
                    {activeTab && (
                      <>
                        {/* Editor Toolbar */}
                        <div className="flex items-center justify-between px-4 py-2 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                          <div className="flex items-center space-x-2">
                            <FileIcon fileName={activeTab.file.name} size={16} />
                            <span className="text-sm font-medium">
                              {activeTab.file.name}
                            </span>
                            {activeTab.isDirty && (
                              <Badge variant="outline" className="text-xs">
                                Modified
                              </Badge>
                            )}
                          </div>

                          <div className="flex items-center space-x-2">
                            {activeTab.isDirty && (
                              <>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRevert(activeTab.id)}
                                  title="Revert changes"
                                >
                                  <RotateCcw className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="default"
                                  size="sm"
                                  onClick={() => handleSave(activeTab.id)}
                                  title="Save file (Ctrl+S)"
                                >
                                  <Save className="h-4 w-4 mr-1" />
                                  Save
                                </Button>
                              </>
                            )}
                          </div>
                        </div>

                        {/* Code Editor */}
                        <div className="flex-1">
                          <CodeMirrorEditor
                            value={activeTab.content}
                            onChange={(content) => handleContentChange(activeTab.id, content)}
                            language={getFileType(activeTab.file.name)}
                            theme={theme}
                            height="100%"
                            lineNumbers={true}
                            searchEnabled={true}
                            autocompletion={true}
                            placeholder={`Start editing ${activeTab.file.name}...`}
                          />
                        </div>
                      </>
                    )}
                  </div>
                </>
              )}
            </div>
          </Panel>
        </PanelGroup>
      </div>
    </FileExplorerProvider>
  )
}
