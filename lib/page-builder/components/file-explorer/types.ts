// File Explorer Types
// Types for the VSCode-like file explorer system

export interface FileNode {
  id: string
  name: string
  type: 'file' | 'folder'
  path: string
  parentId?: string
  children?: FileNode[]
  size?: number
  lastModified?: Date
  content?: string
  language?: string
  isExpanded?: boolean
  isSelected?: boolean
  isEditing?: boolean
  isNew?: boolean
}

export interface FileExplorerState {
  files: FileNode[]
  selectedFileId: string | null
  expandedFolders: Set<string>
  openFiles: string[]
  activeFileId: string | null
  searchQuery: string
  isSearching: boolean
  contextMenu: {
    isOpen: boolean
    x: number
    y: number
    targetId: string | null
    targetType: 'file' | 'folder' | 'root'
  }
  dragState: {
    isDragging: boolean
    draggedId: string | null
    dropTargetId: string | null
  }
  clipboard: {
    operation: 'copy' | 'cut' | null
    fileId: string | null
  }
  showHiddenFiles: boolean
  sortBy: 'name' | 'type' | 'size' | 'modified'
  sortOrder: 'asc' | 'desc'
}

export type FileExplorerAction =
  | { type: 'SET_FILES'; payload: FileNode[] }
  | { type: 'ADD_FILE'; payload: FileNode }
  | { type: 'UPDATE_FILE'; payload: { id: string; updates: Partial<FileNode> } }
  | { type: 'DELETE_FILE'; payload: string }
  | { type: 'SELECT_FILE'; payload: string | null }
  | { type: 'TOGGLE_FOLDER'; payload: string }
  | { type: 'EXPAND_FOLDER'; payload: string }
  | { type: 'COLLAPSE_FOLDER'; payload: string }
  | { type: 'OPEN_FILE'; payload: string }
  | { type: 'CLOSE_FILE'; payload: string }
  | { type: 'SET_ACTIVE_FILE'; payload: string | null }
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'TOGGLE_SEARCH' }
  | { type: 'SHOW_CONTEXT_MENU'; payload: { x: number; y: number; targetId: string | null; targetType: 'file' | 'folder' | 'root' } }
  | { type: 'HIDE_CONTEXT_MENU' }
  | { type: 'START_DRAG'; payload: string }
  | { type: 'SET_DROP_TARGET'; payload: string | null }
  | { type: 'END_DRAG' }
  | { type: 'SET_CLIPBOARD'; payload: { operation: 'copy' | 'cut'; fileId: string } }
  | { type: 'CLEAR_CLIPBOARD' }
  | { type: 'TOGGLE_HIDDEN_FILES' }
  | { type: 'SET_SORT'; payload: { sortBy: FileExplorerState['sortBy']; sortOrder: FileExplorerState['sortOrder'] } }
  | { type: 'START_RENAME'; payload: string }
  | { type: 'END_RENAME'; payload: { id: string; newName: string } }
  | { type: 'CANCEL_RENAME'; payload: string }

export interface FileOperation {
  type: 'create' | 'read' | 'update' | 'delete' | 'rename' | 'move' | 'copy'
  path: string
  content?: string
  newPath?: string
}

export interface FileExplorerConfig {
  rootPath: string
  allowedExtensions?: string[]
  maxFileSize?: number
  showFileSize: boolean
  showLastModified: boolean
  enableDragDrop: boolean
  enableContextMenu: boolean
  enableSearch: boolean
  enableNewFile: boolean
  enableNewFolder: boolean
  enableRename: boolean
  enableDelete: boolean
  enableCopyPaste: boolean
}

export interface FileIconMapping {
  [extension: string]: {
    icon: string
    color: string
  }
}

export interface FolderIconMapping {
  [folderName: string]: {
    icon: string
    color: string
    openIcon?: string
  }
}

// File type detection
export const getFileLanguage = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase()
  
  switch (extension) {
    case 'js':
    case 'jsx':
      return 'javascript'
    case 'ts':
    case 'tsx':
      return 'typescript'
    case 'html':
    case 'htm':
      return 'html'
    case 'css':
      return 'css'
    case 'scss':
    case 'sass':
      return 'css'
    case 'json':
      return 'json'
    case 'md':
    case 'markdown':
      return 'markdown'
    case 'xml':
      return 'xml'
    case 'yaml':
    case 'yml':
      return 'yaml'
    case 'py':
      return 'python'
    case 'php':
      return 'php'
    case 'java':
      return 'java'
    case 'c':
    case 'cpp':
    case 'cc':
      return 'cpp'
    case 'cs':
      return 'csharp'
    case 'go':
      return 'go'
    case 'rs':
      return 'rust'
    case 'rb':
      return 'ruby'
    case 'sql':
      return 'sql'
    case 'sh':
    case 'bash':
      return 'shell'
    default:
      return 'text'
  }
}

// File size formatting
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// Path utilities
export const getParentPath = (path: string): string => {
  const parts = path.split('/').filter(Boolean)
  return parts.slice(0, -1).join('/') || '/'
}

export const getFileName = (path: string): string => {
  return path.split('/').pop() || ''
}

export const joinPaths = (...paths: string[]): string => {
  return paths
    .map(path => path.replace(/^\/+|\/+$/g, ''))
    .filter(Boolean)
    .join('/')
}

// File validation
export const isValidFileName = (name: string): boolean => {
  const invalidChars = /[<>:"/\\|?*\x00-\x1f]/
  return !invalidChars.test(name) && name.trim().length > 0
}

export const isHiddenFile = (name: string): boolean => {
  return name.startsWith('.') && name !== '.' && name !== '..'
}
