'use client'

import React, { useEffect, useRef } from 'react'
import { 
  Plus, 
  FolderPlus, 
  Edit3, 
  Trash2, 
  Copy, 
  Scissors, 
  Clipboard, 
  Download, 
  RefreshCw,
  Eye,
  EyeOff,
  SortAsc,
  SortDesc,
  FileText,
  Folder
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useFileExplorer } from './context'

interface ContextMenuProps {
  x: number
  y: number
  targetId: string | null
  targetType: 'file' | 'folder' | 'root'
  onClose: () => void
}

export function ContextMenu({ x, y, targetId, targetType, onClose }: ContextMenuProps) {
  const {
    state,
    config,
    createFile,
    createFolder,
    renameFile,
    deleteFile,
    copyFile,
    cutFile,
    pasteFile,
    toggleHiddenFiles,
    setSorting,
    dispatch,
    findFileById
  } = useFileExplorer()

  const menuRef = useRef<HTMLDivElement>(null)
  const targetFile = targetId ? findFileById(targetId) : null

  // Close menu on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    document.addEventListener('keydown', handleEscape)

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [onClose])

  // Position menu within viewport
  useEffect(() => {
    if (menuRef.current) {
      const menu = menuRef.current
      const rect = menu.getBoundingClientRect()
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      let adjustedX = x
      let adjustedY = y

      // Adjust horizontal position
      if (x + rect.width > viewportWidth) {
        adjustedX = viewportWidth - rect.width - 10
      }

      // Adjust vertical position
      if (y + rect.height > viewportHeight) {
        adjustedY = viewportHeight - rect.height - 10
      }

      menu.style.left = `${Math.max(10, adjustedX)}px`
      menu.style.top = `${Math.max(10, adjustedY)}px`
    }
  }, [x, y])

  const handleAction = (action: () => void) => {
    action()
    onClose()
  }

  const menuItems = []

  // File/Folder specific actions
  if (targetType === 'file' && targetFile) {
    menuItems.push(
      {
        icon: Eye,
        label: 'Open',
        action: () => handleAction(() => {
          // Open file in editor
          console.log('Open file:', targetFile.name)
        }),
        shortcut: 'Enter'
      },
      {
        icon: Edit3,
        label: 'Rename',
        action: () => handleAction(() => {
          dispatch({ type: 'START_RENAME', payload: targetId! })
        }),
        shortcut: 'F2',
        enabled: config.enableRename
      },
      { type: 'separator' },
      {
        icon: Copy,
        label: 'Copy',
        action: () => handleAction(() => copyFile(targetId!)),
        shortcut: 'Ctrl+C',
        enabled: config.enableCopyPaste
      },
      {
        icon: Scissors,
        label: 'Cut',
        action: () => handleAction(() => cutFile(targetId!)),
        shortcut: 'Ctrl+X',
        enabled: config.enableCopyPaste
      },
      { type: 'separator' },
      {
        icon: Download,
        label: 'Download',
        action: () => handleAction(() => {
          // Download file
          console.log('Download file:', targetFile.name)
        })
      },
      { type: 'separator' },
      {
        icon: Trash2,
        label: 'Delete',
        action: () => handleAction(() => deleteFile(targetId!)),
        shortcut: 'Delete',
        enabled: config.enableDelete,
        destructive: true
      }
    )
  }

  // Folder specific actions
  if (targetType === 'folder' && targetFile) {
    menuItems.push(
      {
        icon: Eye,
        label: 'Expand',
        action: () => handleAction(() => {
          dispatch({ type: 'EXPAND_FOLDER', payload: targetId! })
        })
      },
      { type: 'separator' },
      {
        icon: FileText,
        label: 'New File',
        action: () => handleAction(() => {
          const fileName = prompt('Enter file name:')
          if (fileName) {
            createFile(fileName, targetId!)
          }
        }),
        shortcut: 'Ctrl+N',
        enabled: config.enableNewFile
      },
      {
        icon: FolderPlus,
        label: 'New Folder',
        action: () => handleAction(() => {
          const folderName = prompt('Enter folder name:')
          if (folderName) {
            createFolder(folderName, targetId!)
          }
        }),
        shortcut: 'Ctrl+Shift+N',
        enabled: config.enableNewFolder
      },
      { type: 'separator' },
      {
        icon: Edit3,
        label: 'Rename',
        action: () => handleAction(() => {
          dispatch({ type: 'START_RENAME', payload: targetId! })
        }),
        shortcut: 'F2',
        enabled: config.enableRename
      },
      { type: 'separator' },
      {
        icon: Copy,
        label: 'Copy',
        action: () => handleAction(() => copyFile(targetId!)),
        shortcut: 'Ctrl+C',
        enabled: config.enableCopyPaste
      },
      {
        icon: Scissors,
        label: 'Cut',
        action: () => handleAction(() => cutFile(targetId!)),
        shortcut: 'Ctrl+X',
        enabled: config.enableCopyPaste
      }
    )

    // Add paste if clipboard has content
    if (state.clipboard.fileId && config.enableCopyPaste) {
      menuItems.push({
        icon: Clipboard,
        label: `Paste (${state.clipboard.operation})`,
        action: () => handleAction(() => pasteFile(targetId!)),
        shortcut: 'Ctrl+V'
      })
    }

    menuItems.push(
      { type: 'separator' },
      {
        icon: Trash2,
        label: 'Delete',
        action: () => handleAction(() => deleteFile(targetId!)),
        shortcut: 'Delete',
        enabled: config.enableDelete,
        destructive: true
      }
    )
  }

  // Root/Explorer actions
  if (targetType === 'root') {
    menuItems.push(
      {
        icon: FileText,
        label: 'New File',
        action: () => handleAction(() => {
          const fileName = prompt('Enter file name:')
          if (fileName) {
            createFile(fileName)
          }
        }),
        shortcut: 'Ctrl+N',
        enabled: config.enableNewFile
      },
      {
        icon: FolderPlus,
        label: 'New Folder',
        action: () => handleAction(() => {
          const folderName = prompt('Enter folder name:')
          if (folderName) {
            createFolder(folderName)
          }
        }),
        shortcut: 'Ctrl+Shift+N',
        enabled: config.enableNewFolder
      }
    )

    // Add paste if clipboard has content
    if (state.clipboard.fileId && config.enableCopyPaste) {
      menuItems.push(
        { type: 'separator' },
        {
          icon: Clipboard,
          label: `Paste (${state.clipboard.operation})`,
          action: () => handleAction(() => pasteFile()),
          shortcut: 'Ctrl+V'
        }
      )
    }

    menuItems.push(
      { type: 'separator' },
      {
        icon: RefreshCw,
        label: 'Refresh',
        action: () => handleAction(() => {
          // Refresh file tree
          console.log('Refresh file tree')
        }),
        shortcut: 'F5'
      },
      { type: 'separator' },
      {
        icon: state.showHiddenFiles ? EyeOff : Eye,
        label: state.showHiddenFiles ? 'Hide Hidden Files' : 'Show Hidden Files',
        action: () => handleAction(() => toggleHiddenFiles())
      },
      { type: 'separator' },
      {
        icon: SortAsc,
        label: 'Sort by Name',
        action: () => handleAction(() => setSorting('name', 'asc'))
      },
      {
        icon: SortDesc,
        label: 'Sort by Type',
        action: () => handleAction(() => setSorting('type', 'asc'))
      },
      {
        icon: SortAsc,
        label: 'Sort by Size',
        action: () => handleAction(() => setSorting('size', 'desc'))
      },
      {
        icon: SortDesc,
        label: 'Sort by Modified',
        action: () => handleAction(() => setSorting('modified', 'desc'))
      }
    )
  }

  return (
    <div
      ref={menuRef}
      className="fixed z-50 min-w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg py-1"
      style={{ left: x, top: y }}
    >
      {menuItems.map((item, index) => {
        if (item.type === 'separator') {
          return (
            <div
              key={index}
              className="h-px bg-gray-200 dark:bg-gray-700 my-1"
            />
          )
        }

        const Icon = item.icon
        const isEnabled = item.enabled !== false

        return (
          <button
            key={index}
            className={cn(
              'w-full flex items-center px-3 py-1.5 text-sm text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors',
              !isEnabled && 'opacity-50 cursor-not-allowed',
              item.destructive && 'text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20'
            )}
            onClick={isEnabled ? item.action : undefined}
            disabled={!isEnabled}
          >
            <Icon className="w-4 h-4 mr-3" />
            <span className="flex-1">{item.label}</span>
            {item.shortcut && (
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                {item.shortcut}
              </span>
            )}
          </button>
        )
      })}
    </div>
  )
}
