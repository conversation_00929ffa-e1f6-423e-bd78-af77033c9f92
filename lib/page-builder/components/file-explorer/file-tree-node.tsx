'use client'

import React, { useState, useRef, useEffect } from 'react'
import { ChevronRight, ChevronDown, MoreHorizontal } from 'lucide-react'
import { cn } from '@/lib/utils'
import { FileNode } from './types'
import { FileIcon } from './file-icons'
import { useFileExplorer } from './context'
import { formatFileSize } from './types'

interface FileTreeNodeProps {
  node: FileNode
  level: number
  onDoubleClick?: (node: FileNode) => void
  onContextMenu?: (e: React.MouseEvent, node: FileNode) => void
}

export function FileTreeNode({ node, level, onDoubleClick, onContextMenu }: FileTreeNodeProps) {
  const {
    state,
    selectFile,
    toggleFolder,
    openFile,
    startDrag,
    setDropTarget,
    endDrag,
    renameFile,
    config
  } = useFileExplorer()

  const [isRenaming, setIsRenaming] = useState(false)
  const [renameValue, setRenameValue] = useState(node.name)
  const inputRef = useRef<HTMLInputElement>(null)

  const isSelected = state.selectedFileId === node.id
  const isExpanded = state.expandedFolders.has(node.id)
  const isOpen = state.openFiles.includes(node.id)
  const isActive = state.activeFileId === node.id
  const isDragTarget = state.dragState.dropTargetId === node.id
  const isDragging = state.dragState.draggedId === node.id

  // Handle rename mode
  useEffect(() => {
    if (node.isEditing && !isRenaming) {
      setIsRenaming(true)
      setRenameValue(node.name)
    } else if (!node.isEditing && isRenaming) {
      setIsRenaming(false)
    }
  }, [node.isEditing, isRenaming])

  useEffect(() => {
    if (isRenaming && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [isRenaming])

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    selectFile(node.id)
    
    if (node.type === 'folder') {
      toggleFolder(node.id)
    }
  }

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (node.type === 'file') {
      openFile(node.id)
      onDoubleClick?.(node)
    } else {
      toggleFolder(node.id)
    }
  }

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    selectFile(node.id)
    onContextMenu?.(e, node)
  }

  const handleDragStart = (e: React.DragEvent) => {
    if (!config.enableDragDrop) return
    
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/plain', node.id)
    startDrag(node.id)
  }

  const handleDragOver = (e: React.DragEvent) => {
    if (!config.enableDragDrop) return
    
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
    
    if (node.type === 'folder') {
      setDropTarget(node.id)
    }
  }

  const handleDragLeave = (e: React.DragEvent) => {
    if (!config.enableDragDrop) return
    
    // Only clear drop target if we're actually leaving this element
    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX
    const y = e.clientY
    
    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setDropTarget(null)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    if (!config.enableDragDrop) return
    
    e.preventDefault()
    e.stopPropagation()
    
    const draggedId = e.dataTransfer.getData('text/plain')
    
    if (draggedId !== node.id && node.type === 'folder') {
      // Handle file move operation here
      console.log(`Move ${draggedId} to ${node.id}`)
    }
    
    endDrag()
    setDropTarget(null)
  }

  const handleRenameSubmit = () => {
    if (renameValue.trim() && renameValue !== node.name) {
      renameFile(node.id, renameValue.trim())
    }
    setIsRenaming(false)
  }

  const handleRenameCancel = () => {
    setRenameValue(node.name)
    setIsRenaming(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRenameSubmit()
    } else if (e.key === 'Escape') {
      handleRenameCancel()
    }
  }

  const paddingLeft = level * 12 + 8

  return (
    <div className="select-none">
      <div
        className={cn(
          'group flex items-center h-6 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors',
          isSelected && 'bg-blue-100 dark:bg-blue-900/30',
          isActive && 'bg-blue-200 dark:bg-blue-800/50',
          isDragTarget && 'bg-green-100 dark:bg-green-900/30',
          isDragging && 'opacity-50',
          node.isNew && 'bg-yellow-50 dark:bg-yellow-900/20'
        )}
        style={{ paddingLeft }}
        onClick={handleClick}
        onDoubleClick={handleDoubleClick}
        onContextMenu={handleContextMenu}
        draggable={config.enableDragDrop}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {/* Expand/Collapse Icon */}
        <div className="w-4 h-4 flex items-center justify-center mr-1">
          {node.type === 'folder' && (
            <button
              className="p-0 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              onClick={(e) => {
                e.stopPropagation()
                toggleFolder(node.id)
              }}
            >
              {isExpanded ? (
                <ChevronDown className="w-3 h-3" />
              ) : (
                <ChevronRight className="w-3 h-3" />
              )}
            </button>
          )}
        </div>

        {/* File Icon */}
        <div className="mr-2">
          <FileIcon
            fileName={node.name}
            isFolder={node.type === 'folder'}
            isOpen={isExpanded}
            size={16}
          />
        </div>

        {/* File Name */}
        <div className="flex-1 min-w-0">
          {isRenaming ? (
            <input
              ref={inputRef}
              type="text"
              value={renameValue}
              onChange={(e) => setRenameValue(e.target.value)}
              onBlur={handleRenameSubmit}
              onKeyDown={handleKeyDown}
              className="w-full px-1 py-0 text-sm bg-white dark:bg-gray-800 border border-blue-500 rounded focus:outline-none"
            />
          ) : (
            <span
              className={cn(
                'truncate block',
                isOpen && 'font-medium',
                node.isNew && 'italic text-blue-600 dark:text-blue-400'
              )}
              title={node.name}
            >
              {node.name}
            </span>
          )}
        </div>

        {/* File Info */}
        {!isRenaming && node.type === 'file' && (
          <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400 ml-2">
            {config.showFileSize && node.size !== undefined && (
              <span>{formatFileSize(node.size)}</span>
            )}
            {config.showLastModified && node.lastModified && (
              <span>{node.lastModified.toLocaleDateString()}</span>
            )}
          </div>
        )}

        {/* More Options */}
        {!isRenaming && (
          <div className="opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              onClick={(e) => {
                e.stopPropagation()
                handleContextMenu(e)
              }}
            >
              <MoreHorizontal className="w-3 h-3" />
            </button>
          </div>
        )}
      </div>

      {/* Children */}
      {node.type === 'folder' && isExpanded && node.children && (
        <div>
          {node.children.map((child) => (
            <FileTreeNode
              key={child.id}
              node={child}
              level={level + 1}
              onDoubleClick={onDoubleClick}
              onContextMenu={onContextMenu}
            />
          ))}
        </div>
      )}
    </div>
  )
}
