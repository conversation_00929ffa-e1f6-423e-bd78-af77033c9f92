'use client'

import React, { useEffect, useMemo } from 'react'
import { cn } from '@/lib/utils'
import { FileNode } from './types'
import { FileExplorerHeader } from './file-explorer-header'
import { FileTreeNode } from './file-tree-node'
import { ContextMenu } from './context-menu'
import { useFileExplorer } from './context'
import { isHiddenFile } from './types'

interface FileExplorerProps {
  className?: string
  onFileOpen?: (file: FileNode) => void
  onFileSelect?: (file: FileNode | null) => void
}

export function FileExplorer({ className, onFileOpen, onFileSelect }: FileExplorerProps) {
  const {
    state,
    config,
    hideContextMenu,
    showContextMenu,
    dispatch
  } = useFileExplorer()

  const {
    files,
    searchQuery,
    isSearching,
    showHiddenFiles,
    sortBy,
    sortOrder,
    contextMenu,
    selectedFileId
  } = state

  // Filter and sort files
  const processedFiles = useMemo(() => {
    let filteredFiles = [...files]

    // Filter hidden files
    if (!showHiddenFiles) {
      filteredFiles = filteredFiles.filter(file => !isHiddenFile(file.name))
    }

    // Search filter
    if (isSearching && searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filteredFiles = filteredFiles.filter(file =>
        file.name.toLowerCase().includes(query) ||
        file.path.toLowerCase().includes(query)
      )
    }

    // Sort files
    filteredFiles.sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'type':
          // Folders first, then by extension
          if (a.type !== b.type) {
            comparison = a.type === 'folder' ? -1 : 1
          } else {
            const aExt = a.name.split('.').pop() || ''
            const bExt = b.name.split('.').pop() || ''
            comparison = aExt.localeCompare(bExt)
          }
          break
        case 'size':
          comparison = (a.size || 0) - (b.size || 0)
          break
        case 'modified':
          const aTime = a.lastModified?.getTime() || 0
          const bTime = b.lastModified?.getTime() || 0
          comparison = aTime - bTime
          break
      }

      return sortOrder === 'asc' ? comparison : -comparison
    })

    return filteredFiles
  }, [files, showHiddenFiles, isSearching, searchQuery, sortBy, sortOrder])

  // Handle file double click
  const handleFileDoubleClick = (file: FileNode) => {
    if (file.type === 'file') {
      onFileOpen?.(file)
    }
  }

  // Handle file selection
  useEffect(() => {
    const selectedFile = selectedFileId 
      ? files.find(f => f.id === selectedFileId) || null 
      : null
    onFileSelect?.(selectedFile)
  }, [selectedFileId, files, onFileSelect])

  // Handle context menu
  const handleContextMenu = (e: React.MouseEvent, node?: FileNode) => {
    e.preventDefault()
    
    if (node) {
      showContextMenu(
        e.clientX,
        e.clientY,
        node.id,
        node.type
      )
    } else {
      showContextMenu(
        e.clientX,
        e.clientY,
        null,
        'root'
      )
    }
  }

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement) return

      switch (e.key) {
        case 'F2':
          if (selectedFileId && config.enableRename) {
            e.preventDefault()
            dispatch({ type: 'START_RENAME', payload: selectedFileId })
          }
          break
        case 'Delete':
          if (selectedFileId && config.enableDelete) {
            e.preventDefault()
            const confirmed = confirm('Are you sure you want to delete this item?')
            if (confirmed) {
              dispatch({ type: 'DELETE_FILE', payload: selectedFileId })
            }
          }
          break
        case 'Enter':
          if (selectedFileId) {
            e.preventDefault()
            const file = files.find(f => f.id === selectedFileId)
            if (file) {
              if (file.type === 'file') {
                dispatch({ type: 'OPEN_FILE', payload: selectedFileId })
                onFileOpen?.(file)
              } else {
                dispatch({ type: 'TOGGLE_FOLDER', payload: selectedFileId })
              }
            }
          }
          break
      }

      // Handle Ctrl combinations
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'n':
            if (config.enableNewFile) {
              e.preventDefault()
              const fileName = prompt('Enter file name:')
              if (fileName?.trim()) {
                dispatch({ 
                  type: 'ADD_FILE', 
                  payload: {
                    id: `file-${Date.now()}`,
                    name: fileName.trim(),
                    type: 'file',
                    path: fileName.trim(),
                    content: '',
                    size: 0,
                    lastModified: new Date(),
                    isNew: true
                  }
                })
              }
            }
            break
          case 'f':
            if (config.enableSearch) {
              e.preventDefault()
              dispatch({ type: 'TOGGLE_SEARCH' })
            }
            break
          case 'c':
            if (selectedFileId && config.enableCopyPaste) {
              e.preventDefault()
              dispatch({ 
                type: 'SET_CLIPBOARD', 
                payload: { operation: 'copy', fileId: selectedFileId } 
              })
            }
            break
          case 'x':
            if (selectedFileId && config.enableCopyPaste) {
              e.preventDefault()
              dispatch({ 
                type: 'SET_CLIPBOARD', 
                payload: { operation: 'cut', fileId: selectedFileId } 
              })
            }
            break
          case 'v':
            if (state.clipboard.fileId && config.enableCopyPaste) {
              e.preventDefault()
              // Handle paste operation
              console.log('Paste operation')
            }
            break
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [selectedFileId, files, config, state.clipboard, dispatch, onFileOpen])

  return (
    <div className={cn('flex flex-col h-full bg-white dark:bg-gray-900', className)}>
      {/* Header */}
      <FileExplorerHeader
        title="Explorer"
        onRefresh={() => {
          // Refresh file tree
          console.log('Refresh file tree')
        }}
      />

      {/* File Tree */}
      <div 
        className="flex-1 overflow-auto"
        onContextMenu={(e) => handleContextMenu(e)}
      >
        {processedFiles.length === 0 ? (
          <div className="flex items-center justify-center h-32 text-sm text-gray-500 dark:text-gray-400">
            {isSearching && searchQuery ? (
              <div className="text-center">
                <p>No files found matching "{searchQuery}"</p>
                <p className="text-xs mt-1">Try a different search term</p>
              </div>
            ) : (
              <div className="text-center">
                <p>No files in this workspace</p>
                <p className="text-xs mt-1">Create a new file or folder to get started</p>
              </div>
            )}
          </div>
        ) : (
          <div className="py-1">
            {processedFiles.map((file) => (
              <FileTreeNode
                key={file.id}
                node={file}
                level={0}
                onDoubleClick={handleFileDoubleClick}
                onContextMenu={handleContextMenu}
              />
            ))}
          </div>
        )}
      </div>

      {/* Context Menu */}
      {contextMenu.isOpen && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          targetId={contextMenu.targetId}
          targetType={contextMenu.targetType}
          onClose={hideContextMenu}
        />
      )}
    </div>
  )
}
