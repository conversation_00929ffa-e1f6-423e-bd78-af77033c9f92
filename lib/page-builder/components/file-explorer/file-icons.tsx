'use client'

import React from 'react'
import { 
  FileText, 
  Folder, 
  FolderOpen, 
  Image, 
  Video, 
  Music, 
  Archive, 
  Code, 
  Database,
  Settings,
  FileJson,
  Globe,
  Palette,
  Terminal,
  FileCode,
  FileImage,
  FileVideo,
  FileAudio,
  FileArchive,
  File,
  Package,
  GitBranch,
  Lock,
  Eye,
  Zap,
  Coffee,
  Hash,
  Layers,
  Box,
  Cpu,
  HardDrive,
  Monitor,
  Smartphone,
  Tablet
} from 'lucide-react'
import { FileIconMapping, FolderIconMapping } from './types'

// File extension to icon mapping
export const fileIconMap: FileIconMapping = {
  // Web Technologies
  'html': { icon: 'Globe', color: '#e34c26' },
  'htm': { icon: 'Globe', color: '#e34c26' },
  'css': { icon: 'Palette', color: '#1572b6' },
  'scss': { icon: 'Palette', color: '#cf649a' },
  'sass': { icon: 'Palette', color: '#cf649a' },
  'less': { icon: 'Palette', color: '#1d365d' },
  'js': { icon: 'FileCode', color: '#f1e05a' },
  'jsx': { icon: 'FileCode', color: '#61dafb' },
  'ts': { icon: 'FileCode', color: '#3178c6' },
  'tsx': { icon: 'FileCode', color: '#61dafb' },
  'vue': { icon: 'FileCode', color: '#4fc08d' },
  'svelte': { icon: 'FileCode', color: '#ff3e00' },
  'php': { icon: 'Code', color: '#777bb4' },
  'asp': { icon: 'Code', color: '#512bd4' },
  'aspx': { icon: 'Code', color: '#512bd4' },

  // Data & Config
  'json': { icon: 'FileJson', color: '#cbcb41' },
  'xml': { icon: 'Code', color: '#e37933' },
  'yaml': { icon: 'Settings', color: '#cb171e' },
  'yml': { icon: 'Settings', color: '#cb171e' },
  'toml': { icon: 'Settings', color: '#9c4221' },
  'ini': { icon: 'Settings', color: '#6d6d6d' },
  'env': { icon: 'Settings', color: '#faf047' },
  'config': { icon: 'Settings', color: '#6d6d6d' },

  // Programming Languages
  'py': { icon: 'FileCode', color: '#3776ab' },
  'java': { icon: 'Coffee', color: '#ed8b00' },
  'c': { icon: 'FileCode', color: '#555555' },
  'cpp': { icon: 'FileCode', color: '#f34b7d' },
  'cc': { icon: 'FileCode', color: '#f34b7d' },
  'h': { icon: 'FileCode', color: '#a9b7c6' },
  'hpp': { icon: 'FileCode', color: '#a9b7c6' },
  'cs': { icon: 'Hash', color: '#239120' },
  'go': { icon: 'FileCode', color: '#00add8' },
  'rs': { icon: 'FileCode', color: '#dea584' },
  'rb': { icon: 'FileCode', color: '#cc342d' },
  'swift': { icon: 'FileCode', color: '#fa7343' },
  'kt': { icon: 'FileCode', color: '#7f52ff' },
  'scala': { icon: 'FileCode', color: '#c22d40' },
  'r': { icon: 'FileCode', color: '#198ce7' },
  'matlab': { icon: 'FileCode', color: '#e16737' },

  // Shell & Scripts
  'sh': { icon: 'Terminal', color: '#89e051' },
  'bash': { icon: 'Terminal', color: '#89e051' },
  'zsh': { icon: 'Terminal', color: '#89e051' },
  'fish': { icon: 'Terminal', color: '#89e051' },
  'ps1': { icon: 'Terminal', color: '#012456' },
  'bat': { icon: 'Terminal', color: '#c1f12e' },
  'cmd': { icon: 'Terminal', color: '#c1f12e' },

  // Database
  'sql': { icon: 'Database', color: '#e38c00' },
  'db': { icon: 'Database', color: '#003b57' },
  'sqlite': { icon: 'Database', color: '#003b57' },
  'mdb': { icon: 'Database', color: '#a41e22' },

  // Documents
  'md': { icon: 'FileText', color: '#083fa1' },
  'markdown': { icon: 'FileText', color: '#083fa1' },
  'txt': { icon: 'FileText', color: '#6d6d6d' },
  'rtf': { icon: 'FileText', color: '#6d6d6d' },
  'pdf': { icon: 'FileText', color: '#ff0000' },
  'doc': { icon: 'FileText', color: '#2b579a' },
  'docx': { icon: 'FileText', color: '#2b579a' },
  'xls': { icon: 'FileText', color: '#217346' },
  'xlsx': { icon: 'FileText', color: '#217346' },
  'ppt': { icon: 'FileText', color: '#d24726' },
  'pptx': { icon: 'FileText', color: '#d24726' },

  // Images
  'jpg': { icon: 'FileImage', color: '#f1c40f' },
  'jpeg': { icon: 'FileImage', color: '#f1c40f' },
  'png': { icon: 'FileImage', color: '#f1c40f' },
  'gif': { icon: 'FileImage', color: '#f1c40f' },
  'svg': { icon: 'FileImage', color: '#ffb13b' },
  'webp': { icon: 'FileImage', color: '#f1c40f' },
  'ico': { icon: 'FileImage', color: '#f1c40f' },
  'bmp': { icon: 'FileImage', color: '#f1c40f' },
  'tiff': { icon: 'FileImage', color: '#f1c40f' },
  'psd': { icon: 'FileImage', color: '#31c5f0' },
  'ai': { icon: 'FileImage', color: '#ff9a00' },
  'sketch': { icon: 'FileImage', color: '#fdad00' },
  'figma': { icon: 'FileImage', color: '#f24e1e' },

  // Video
  'mp4': { icon: 'FileVideo', color: '#fd79a8' },
  'avi': { icon: 'FileVideo', color: '#fd79a8' },
  'mov': { icon: 'FileVideo', color: '#fd79a8' },
  'wmv': { icon: 'FileVideo', color: '#fd79a8' },
  'flv': { icon: 'FileVideo', color: '#fd79a8' },
  'webm': { icon: 'FileVideo', color: '#fd79a8' },
  'mkv': { icon: 'FileVideo', color: '#fd79a8' },

  // Audio
  'mp3': { icon: 'FileAudio', color: '#a29bfe' },
  'wav': { icon: 'FileAudio', color: '#a29bfe' },
  'flac': { icon: 'FileAudio', color: '#a29bfe' },
  'aac': { icon: 'FileAudio', color: '#a29bfe' },
  'ogg': { icon: 'FileAudio', color: '#a29bfe' },
  'wma': { icon: 'FileAudio', color: '#a29bfe' },

  // Archives
  'zip': { icon: 'FileArchive', color: '#fd79a8' },
  'rar': { icon: 'FileArchive', color: '#fd79a8' },
  '7z': { icon: 'FileArchive', color: '#fd79a8' },
  'tar': { icon: 'FileArchive', color: '#fd79a8' },
  'gz': { icon: 'FileArchive', color: '#fd79a8' },
  'bz2': { icon: 'FileArchive', color: '#fd79a8' },

  // Package Managers
  'package.json': { icon: 'Package', color: '#cb3837' },
  'yarn.lock': { icon: 'Package', color: '#2c8ebb' },
  'package-lock.json': { icon: 'Lock', color: '#cb3837' },
  'composer.json': { icon: 'Package', color: '#885630' },
  'requirements.txt': { icon: 'Package', color: '#3776ab' },
  'Gemfile': { icon: 'Package', color: '#cc342d' },
  'Cargo.toml': { icon: 'Package', color: '#dea584' },
  'go.mod': { icon: 'Package', color: '#00add8' },

  // Build & Config Files
  'webpack.config.js': { icon: 'Box', color: '#8dd6f9' },
  'vite.config.js': { icon: 'Zap', color: '#646cff' },
  'rollup.config.js': { icon: 'Box', color: '#ec3a37' },
  'gulpfile.js': { icon: 'Box', color: '#cf4647' },
  'gruntfile.js': { icon: 'Box', color: '#fba919' },
  'tsconfig.json': { icon: 'Settings', color: '#3178c6' },
  'jsconfig.json': { icon: 'Settings', color: '#f1e05a' },
  'babel.config.js': { icon: 'Settings', color: '#f9dc3e' },
  'eslint.config.js': { icon: 'Settings', color: '#4b32c3' },
  'prettier.config.js': { icon: 'Settings', color: '#f7b93e' },
  'tailwind.config.js': { icon: 'Settings', color: '#06b6d4' },
  'next.config.js': { icon: 'Settings', color: '#000000' },
  'nuxt.config.js': { icon: 'Settings', color: '#00dc82' },

  // Version Control
  '.gitignore': { icon: 'GitBranch', color: '#f05032' },
  '.gitattributes': { icon: 'GitBranch', color: '#f05032' },

  // Docker
  'Dockerfile': { icon: 'Box', color: '#2496ed' },
  'docker-compose.yml': { icon: 'Layers', color: '#2496ed' },

  // Default
  'default': { icon: 'File', color: '#6d6d6d' }
}

// Folder name to icon mapping
export const folderIconMap: FolderIconMapping = {
  'src': { icon: 'Code', color: '#90a4ae', openIcon: 'Code' },
  'components': { icon: 'Box', color: '#42a5f5', openIcon: 'Box' },
  'pages': { icon: 'Globe', color: '#66bb6a', openIcon: 'Globe' },
  'styles': { icon: 'Palette', color: '#ab47bc', openIcon: 'Palette' },
  'assets': { icon: 'Image', color: '#ffa726', openIcon: 'Image' },
  'images': { icon: 'FileImage', color: '#ffa726', openIcon: 'FileImage' },
  'public': { icon: 'Globe', color: '#66bb6a', openIcon: 'Globe' },
  'static': { icon: 'HardDrive', color: '#78909c', openIcon: 'HardDrive' },
  'lib': { icon: 'Package', color: '#5c6bc0', openIcon: 'Package' },
  'utils': { icon: 'Settings', color: '#78909c', openIcon: 'Settings' },
  'helpers': { icon: 'Settings', color: '#78909c', openIcon: 'Settings' },
  'hooks': { icon: 'Zap', color: '#ff7043', openIcon: 'Zap' },
  'context': { icon: 'Layers', color: '#26a69a', openIcon: 'Layers' },
  'store': { icon: 'Database', color: '#8d6e63', openIcon: 'Database' },
  'api': { icon: 'Cpu', color: '#42a5f5', openIcon: 'Cpu' },
  'services': { icon: 'Cpu', color: '#42a5f5', openIcon: 'Cpu' },
  'config': { icon: 'Settings', color: '#78909c', openIcon: 'Settings' },
  'tests': { icon: 'Eye', color: '#4caf50', openIcon: 'Eye' },
  'test': { icon: 'Eye', color: '#4caf50', openIcon: 'Eye' },
  '__tests__': { icon: 'Eye', color: '#4caf50', openIcon: 'Eye' },
  'docs': { icon: 'FileText', color: '#5c6bc0', openIcon: 'FileText' },
  'documentation': { icon: 'FileText', color: '#5c6bc0', openIcon: 'FileText' },
  'build': { icon: 'Box', color: '#ff7043', openIcon: 'Box' },
  'dist': { icon: 'Package', color: '#ff7043', openIcon: 'Package' },
  'node_modules': { icon: 'Package', color: '#8bc34a', openIcon: 'Package' },
  '.git': { icon: 'GitBranch', color: '#f44336', openIcon: 'GitBranch' },
  '.vscode': { icon: 'Monitor', color: '#007acc', openIcon: 'Monitor' },
  '.idea': { icon: 'Monitor', color: '#000000', openIcon: 'Monitor' },
  'mobile': { icon: 'Smartphone', color: '#ff9800', openIcon: 'Smartphone' },
  'tablet': { icon: 'Tablet', color: '#ff9800', openIcon: 'Tablet' },
  'desktop': { icon: 'Monitor', color: '#ff9800', openIcon: 'Monitor' },
  'default': { icon: 'Folder', color: '#90a4ae', openIcon: 'FolderOpen' }
}

// Icon component mapping
const iconComponents = {
  FileText,
  Folder,
  FolderOpen,
  Image,
  Video,
  Music,
  Archive,
  Code,
  Database,
  Settings,
  FileJson,
  Globe,
  Palette,
  Terminal,
  FileCode,
  FileImage,
  FileVideo,
  FileAudio,
  FileArchive,
  File,
  Package,
  GitBranch,
  Lock,
  Eye,
  Zap,
  Coffee,
  Hash,
  Layers,
  Box,
  Cpu,
  HardDrive,
  Monitor,
  Smartphone,
  Tablet
}

interface FileIconProps {
  fileName: string
  isFolder?: boolean
  isOpen?: boolean
  size?: number
  className?: string
}

export function FileIcon({ fileName, isFolder = false, isOpen = false, size = 16, className = '' }: FileIconProps) {
  let iconName: string
  let color: string

  if (isFolder) {
    const folderConfig = folderIconMap[fileName.toLowerCase()] || folderIconMap.default
    iconName = isOpen && folderConfig.openIcon ? folderConfig.openIcon : folderConfig.icon
    color = folderConfig.color
  } else {
    const extension = fileName.split('.').pop()?.toLowerCase() || ''
    const fileConfig = fileIconMap[extension] || fileIconMap[fileName.toLowerCase()] || fileIconMap.default
    iconName = fileConfig.icon
    color = fileConfig.color
  }

  const IconComponent = iconComponents[iconName as keyof typeof iconComponents] || File

  return (
    <IconComponent
      size={size}
      style={{ color }}
      className={className}
    />
  )
}

// Get file type for syntax highlighting
export function getFileType(fileName: string): string {
  const extension = fileName.split('.').pop()?.toLowerCase() || ''
  
  switch (extension) {
    case 'js':
    case 'jsx':
      return 'javascript'
    case 'ts':
    case 'tsx':
      return 'typescript'
    case 'html':
    case 'htm':
      return 'html'
    case 'css':
    case 'scss':
    case 'sass':
    case 'less':
      return 'css'
    case 'json':
      return 'json'
    case 'md':
    case 'markdown':
      return 'markdown'
    case 'xml':
      return 'xml'
    case 'yaml':
    case 'yml':
      return 'yaml'
    case 'py':
      return 'python'
    case 'php':
      return 'php'
    case 'java':
      return 'java'
    case 'c':
    case 'cpp':
    case 'cc':
      return 'cpp'
    case 'cs':
      return 'csharp'
    case 'go':
      return 'go'
    case 'rs':
      return 'rust'
    case 'rb':
      return 'ruby'
    case 'sql':
      return 'sql'
    case 'sh':
    case 'bash':
      return 'shell'
    default:
      return 'text'
  }
}
