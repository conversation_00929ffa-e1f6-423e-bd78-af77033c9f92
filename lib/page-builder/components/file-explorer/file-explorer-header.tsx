'use client'

import React, { useState, useRef, useEffect } from 'react'
import { 
  Search, 
  X, 
  Plus, 
  FolderPlus, 
  RefreshCw, 
  MoreVertical,
  SortAsc,
  SortDesc,
  Eye,
  EyeOff,
  FileText,
  Settings
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'
import { useFileExplorer } from './context'

interface FileExplorerHeaderProps {
  title?: string
  onRefresh?: () => void
  className?: string
}

export function FileExplorerHeader({ 
  title = 'Explorer', 
  onRefresh,
  className 
}: FileExplorerHeaderProps) {
  const {
    state,
    config,
    setSearchQuery,
    toggleSearch,
    createFile,
    createFolder,
    toggleHiddenFiles,
    setSorting
  } = useFileExplorer()

  const [isSearchFocused, setIsSearchFocused] = useState(false)
  const searchInputRef = useRef<HTMLInputElement>(null)

  const { searchQuery, isSearching, sortBy, sortOrder, showHiddenFiles } = state

  // Focus search input when search is toggled
  useEffect(() => {
    if (isSearching && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [isSearching])

  const handleSearchToggle = () => {
    toggleSearch()
    if (!isSearching) {
      setSearchQuery('')
    }
  }

  const handleSearchClear = () => {
    setSearchQuery('')
    if (searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }

  const handleNewFile = () => {
    const fileName = prompt('Enter file name:')
    if (fileName?.trim()) {
      createFile(fileName.trim())
    }
  }

  const handleNewFolder = () => {
    const folderName = prompt('Enter folder name:')
    if (folderName?.trim()) {
      createFolder(folderName.trim())
    }
  }

  const handleRefresh = () => {
    onRefresh?.()
    // Could also trigger a file tree refresh here
  }

  const getSortIcon = () => {
    return sortOrder === 'asc' ? SortAsc : SortDesc
  }

  const getSortLabel = () => {
    const direction = sortOrder === 'asc' ? 'Ascending' : 'Descending'
    const field = sortBy.charAt(0).toUpperCase() + sortBy.slice(1)
    return `${field} (${direction})`
  }

  return (
    <div className={cn('flex flex-col border-b border-gray-200 dark:border-gray-700', className)}>
      {/* Header Row */}
      <div className="flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-800/50">
        <div className="flex items-center space-x-2">
          <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
            {title}
          </h3>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {state.files.length} items
          </span>
        </div>

        <div className="flex items-center space-x-1">
          {/* Search Toggle */}
          {config.enableSearch && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSearchToggle}
              className={cn(
                'h-6 w-6 p-0',
                isSearching && 'bg-blue-100 dark:bg-blue-900/30'
              )}
              title="Search files"
            >
              <Search className="h-3 w-3" />
            </Button>
          )}

          {/* New File */}
          {config.enableNewFile && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNewFile}
              className="h-6 w-6 p-0"
              title="New file"
            >
              <FileText className="h-3 w-3" />
            </Button>
          )}

          {/* New Folder */}
          {config.enableNewFolder && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNewFolder}
              className="h-6 w-6 p-0"
              title="New folder"
            >
              <FolderPlus className="h-3 w-3" />
            </Button>
          )}

          {/* Refresh */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            className="h-6 w-6 p-0"
            title="Refresh"
          >
            <RefreshCw className="h-3 w-3" />
          </Button>

          {/* More Options */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                title="More options"
              >
                <MoreVertical className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {/* View Options */}
              <DropdownMenuItem onClick={toggleHiddenFiles}>
                {showHiddenFiles ? (
                  <>
                    <EyeOff className="h-4 w-4 mr-2" />
                    Hide Hidden Files
                  </>
                ) : (
                  <>
                    <Eye className="h-4 w-4 mr-2" />
                    Show Hidden Files
                  </>
                )}
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              {/* Sort Options */}
              <DropdownMenuItem onClick={() => setSorting('name', sortBy === 'name' && sortOrder === 'asc' ? 'desc' : 'asc')}>
                {getSortIcon()({ className: "h-4 w-4 mr-2" })}
                Sort by Name
              </DropdownMenuItem>

              <DropdownMenuItem onClick={() => setSorting('type', sortBy === 'type' && sortOrder === 'asc' ? 'desc' : 'asc')}>
                {getSortIcon()({ className: "h-4 w-4 mr-2" })}
                Sort by Type
              </DropdownMenuItem>

              <DropdownMenuItem onClick={() => setSorting('size', sortBy === 'size' && sortOrder === 'desc' ? 'asc' : 'desc')}>
                {getSortIcon()({ className: "h-4 w-4 mr-2" })}
                Sort by Size
              </DropdownMenuItem>

              <DropdownMenuItem onClick={() => setSorting('modified', sortBy === 'modified' && sortOrder === 'desc' ? 'asc' : 'desc')}>
                {getSortIcon()({ className: "h-4 w-4 mr-2" })}
                Sort by Modified
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              <DropdownMenuItem>
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Search Bar */}
      {isSearching && (
        <div className="px-3 py-2 border-t border-gray-200 dark:border-gray-700">
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
            <Input
              ref={searchInputRef}
              type="text"
              placeholder="Search files and folders..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setIsSearchFocused(false)}
              className={cn(
                'pl-7 pr-7 h-7 text-xs',
                isSearchFocused && 'ring-2 ring-blue-500'
              )}
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSearchClear}
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 p-0 hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
          
          {searchQuery && (
            <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              {/* Search results count would go here */}
              Searching for "{searchQuery}"...
            </div>
          )}
        </div>
      )}

      {/* Sort Indicator */}
      {!isSearching && (
        <div className="px-3 py-1 text-xs text-gray-500 dark:text-gray-400 bg-gray-25 dark:bg-gray-800/25">
          Sorted by {getSortLabel()}
        </div>
      )}
    </div>
  )
}
