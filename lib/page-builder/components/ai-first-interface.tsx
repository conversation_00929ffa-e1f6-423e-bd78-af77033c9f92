'use client'

import React, { useState, useRef, useEffect } from 'react'
import { usePageBuilder } from '../context'
import { useAIPageDesigner } from '../hooks/useAIPageDesigner'
import { useAIBlockGenerator } from '@/lib/ai-block-generator/hooks/useAIBlockGenerator'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Sparkles,
  MessageSquare,
  Wand2,
  Brain,
  Send,
  Loader2,
  Lightbulb,
  Zap,
  Target,
  Palette,
  Layout,
  Type,
  Image,
  ShoppingCart,
  Users,
  TrendingUp,
  Star,
  Plus,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

interface AIFirstInterfaceProps {
  className?: string
}

export function AIFirstInterface({ className }: AIFirstInterfaceProps) {
  const { state } = usePageBuilder()
  const [activeTab, setActiveTab] = useState<'chat' | 'suggestions' | 'templates'>('chat')
  const [isExpanded, setIsExpanded] = useState(true)
  const [input, setInput] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  const {
    messages,
    handleSubmit,
    isLoading,
    designPage,
    addContentBlock,
    modifyContent,
    generateContent,
    clearConversation,
    hasMessages
  } = useAIPageDesigner({
    onSuccess: () => {
      setIsGenerating(false)
      toast.success('AI has updated your page!')
    },
    onError: (error) => {
      setIsGenerating(false)
      toast.error('AI error: ' + error.message)
    }
  })

  const {
    generateBlock,
    generateLayout,
    isGenerating: isBlockGenerating,
    generatedBlocks,
    addGeneratedBlock
  } = useAIBlockGenerator()

  // Quick action prompts for common tasks
  const quickActions = [
    {
      icon: Layout,
      label: 'Create Hero Section',
      prompt: 'Create a modern hero section with headline, description, and call-to-action button',
      category: 'layout',
      color: 'bg-blue-500'
    },
    {
      icon: ShoppingCart,
      label: 'Add Product Showcase',
      prompt: 'Add a product showcase section with featured products and pricing',
      category: 'ecommerce',
      color: 'bg-green-500'
    },
    {
      icon: Users,
      label: 'Customer Testimonials',
      prompt: 'Create a testimonials section with customer reviews and ratings',
      category: 'social',
      color: 'bg-purple-500'
    },
    {
      icon: Type,
      label: 'About Section',
      prompt: 'Add an about section explaining our brand story and values',
      category: 'content',
      color: 'bg-orange-500'
    },
    {
      icon: Image,
      label: 'Image Gallery',
      prompt: 'Create a responsive image gallery showcasing our products',
      category: 'media',
      color: 'bg-pink-500'
    },
    {
      icon: TrendingUp,
      label: 'Features List',
      prompt: 'Add a features section highlighting key benefits and advantages',
      category: 'marketing',
      color: 'bg-indigo-500'
    }
  ]

  // AI suggestions based on current page content
  const aiSuggestions = [
    {
      type: 'improvement',
      title: 'Enhance Mobile Experience',
      description: 'Add responsive spacing and mobile-optimized layouts',
      action: () => handleQuickAction('Optimize this page for mobile devices with better spacing and responsive design')
    },
    {
      type: 'content',
      title: 'Add Call-to-Action',
      description: 'Include compelling CTAs to increase conversions',
      action: () => handleQuickAction('Add strategic call-to-action buttons throughout the page to improve conversions')
    },
    {
      type: 'seo',
      title: 'SEO Optimization',
      description: 'Improve page structure for better search rankings',
      action: () => handleQuickAction('Optimize page structure and content for better SEO performance')
    },
    {
      type: 'design',
      title: 'Visual Hierarchy',
      description: 'Improve content organization and visual flow',
      action: () => handleQuickAction('Improve visual hierarchy and content organization for better user experience')
    }
  ]

  // Page templates for quick starts
  const pageTemplates = [
    {
      name: 'E-commerce Landing',
      description: 'Complete landing page for online store',
      sections: ['hero', 'features', 'products', 'testimonials', 'footer'],
      style: 'modern',
      target: 'shoppers'
    },
    {
      name: 'Product Showcase',
      description: 'Detailed product presentation page',
      sections: ['hero', 'gallery', 'features', 'pricing', 'reviews'],
      style: 'minimal',
      target: 'buyers'
    },
    {
      name: 'Brand Story',
      description: 'About page with company story',
      sections: ['hero', 'about', 'team', 'values', 'contact'],
      style: 'elegant',
      target: 'customers'
    },
    {
      name: 'Collection Page',
      description: 'Product collection with filters',
      sections: ['hero', 'filters', 'products', 'pagination'],
      style: 'clean',
      target: 'browsers'
    }
  ]

  const handleQuickAction = async (prompt: string) => {
    setIsGenerating(true)
    setInput(prompt)
    
    try {
      await designPage(prompt)
    } catch (error) {
      console.error('Quick action error:', error)
      toast.error('Failed to execute AI action')
    }
  }

  const handleCustomSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim()) return
    
    setIsGenerating(true)
    
    try {
      await designPage(input)
      setInput('')
    } catch (error) {
      console.error('Custom submit error:', error)
      toast.error('Failed to process AI request')
    }
  }

  const handleTemplateGenerate = async (template: any) => {
    setIsGenerating(true)
    
    try {
      await generateLayout(
        'landing',
        template.description,
        template.sections,
        template.style,
        template.target
      )
      toast.success(`Generated ${template.name} template!`)
    } catch (error) {
      console.error('Template generation error:', error)
      toast.error('Failed to generate template')
    } finally {
      setIsGenerating(false)
    }
  }

  // Focus input when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  return (
    <Card className={cn('h-full flex flex-col', className)}>
      {/* Header */}
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Brain className="h-5 w-5 text-purple-600" />
            AI Page Builder
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>
        <p className="text-sm text-muted-foreground">
          Describe what you want to create and let AI build it for you
        </p>
      </CardHeader>

      {isExpanded && (
        <CardContent className="flex-1 flex flex-col p-0">
          {/* AI Input */}
          <div className="p-4 border-b">
            <form onSubmit={handleCustomSubmit} className="space-y-3">
              <div className="flex gap-2">
                <Input
                  ref={inputRef}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Describe what you want to create or modify..."
                  disabled={isGenerating || isLoading}
                  className="flex-1"
                />
                <Button
                  type="submit"
                  size="sm"
                  disabled={isGenerating || isLoading || !input.trim()}
                  className="px-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                >
                  {isGenerating || isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
              
              {(isGenerating || isLoading) && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Loader2 className="h-3 w-3 animate-spin" />
                  AI is working on your request...
                </div>
              )}
            </form>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-3 mx-4 mt-4">
              <TabsTrigger value="chat" className="text-xs">
                <MessageSquare className="h-3 w-3 mr-1" />
                Chat
              </TabsTrigger>
              <TabsTrigger value="suggestions" className="text-xs">
                <Lightbulb className="h-3 w-3 mr-1" />
                Suggestions
              </TabsTrigger>
              <TabsTrigger value="templates" className="text-xs">
                <Wand2 className="h-3 w-3 mr-1" />
                Templates
              </TabsTrigger>
            </TabsList>

            {/* Chat Tab */}
            <TabsContent value="chat" className="flex-1 flex flex-col mt-4">
              <ScrollArea className="flex-1 px-4">
                {!hasMessages ? (
                  <div className="space-y-3">
                    <div className="text-center py-6">
                      <Sparkles className="h-8 w-8 mx-auto mb-3 text-purple-500" />
                      <h3 className="font-medium mb-2">Start with AI</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Choose a quick action or describe what you want to create
                      </p>
                    </div>
                    
                    {/* Quick Actions Grid */}
                    <div className="grid grid-cols-1 gap-2">
                      {quickActions.map((action, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => handleQuickAction(action.prompt)}
                          disabled={isGenerating || isLoading}
                          className="justify-start h-auto p-3 text-left"
                        >
                          <div className={cn('w-8 h-8 rounded-lg flex items-center justify-center mr-3', action.color)}>
                            <action.icon className="h-4 w-4 text-white" />
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-sm">{action.label}</div>
                            <div className="text-xs text-muted-foreground">{action.prompt}</div>
                          </div>
                        </Button>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {messages.map((message, index) => (
                      <div
                        key={index}
                        className={cn(
                          'p-3 rounded-lg max-w-[85%]',
                          message.role === 'user'
                            ? 'bg-purple-100 ml-auto text-purple-900'
                            : 'bg-gray-100 text-gray-900'
                        )}
                      >
                        <div className="text-sm">{message.content}</div>
                      </div>
                    ))}
                    
                    {hasMessages && (
                      <div className="flex justify-center pt-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={clearConversation}
                          className="text-xs"
                        >
                          Clear Conversation
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>

            {/* Suggestions Tab */}
            <TabsContent value="suggestions" className="flex-1 mt-4">
              <ScrollArea className="h-full px-4">
                <div className="space-y-3">
                  {aiSuggestions.map((suggestion, index) => (
                    <Card key={index} className="p-3">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Lightbulb className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-sm mb-1">{suggestion.title}</h4>
                          <p className="text-xs text-muted-foreground mb-2">{suggestion.description}</p>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={suggestion.action}
                            disabled={isGenerating || isLoading}
                            className="text-xs h-7"
                          >
                            <Zap className="h-3 w-3 mr-1" />
                            Apply
                          </Button>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {suggestion.type}
                        </Badge>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Templates Tab */}
            <TabsContent value="templates" className="flex-1 mt-4">
              <ScrollArea className="h-full px-4">
                <div className="space-y-3">
                  {pageTemplates.map((template, index) => (
                    <Card key={index} className="p-3">
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-medium text-sm mb-1">{template.name}</h4>
                          <p className="text-xs text-muted-foreground">{template.description}</p>
                        </div>
                        
                        <div className="flex flex-wrap gap-1">
                          {template.sections.map((section) => (
                            <Badge key={section} variant="outline" className="text-xs">
                              {section}
                            </Badge>
                          ))}
                        </div>
                        
                        <Button
                          size="sm"
                          onClick={() => handleTemplateGenerate(template)}
                          disabled={isGenerating || isLoading}
                          className="w-full text-xs h-7"
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Generate Template
                        </Button>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      )}
    </Card>
  )
}
