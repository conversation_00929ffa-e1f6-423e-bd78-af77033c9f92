// Gutenberg-Style Page Builder Components
// Complete WordPress Gutenberg-inspired interface with dropdowns and popovers

// Core Components
export { GutenbergBlockWrapper } from '../block-wrapper/gutenberg-block-wrapper'
export { BlockToolbar, QuickSettings } from '../block-toolbar'
export { BlockFormatToolbar, InlineFormatToolbar } from '../block-toolbar/block-format-toolbar'

// Property Panels
export { CompactBlockSettings, InlineQuickSettings } from '../properties-panel/compact-block-settings'
export { DynamicBlockEditor } from '../properties-panel/dynamic-block-editor'

// Block Management
export { 
  GutenbergBlockInserter, 
  QuickInsertButton, 
  FloatingAddButton 
} from '../block-inserter/gutenberg-block-inserter'

export { 
  BlockNavigation, 
  CompactBlockNavigation 
} from '../navigation/block-navigation'

export { BlockPatterns } from '../patterns/block-patterns'

// Types and Interfaces
export interface GutenbergEditorProps {
  blocks: any[]
  selectedBlockId?: string
  onSelectBlock: (blockId: string) => void
  onUpdateBlock: (blockId: string, updates: any) => void
  onInsertBlock: (blockType: string, afterBlockId?: string) => void
  onDuplicateBlock: (blockId: string) => void
  onDeleteBlock: (blockId: string) => void
  onMoveBlock: (blockId: string, direction: 'up' | 'down') => void
  onToggleBlockVisibility: (blockId: string) => void
}

export interface BlockWrapperProps {
  block: any
  isSelected?: boolean
  isHovered?: boolean
  onSelect?: () => void
  onUpdate?: (updates: any) => void
  onDuplicate?: () => void
  onDelete?: () => void
  onMoveUp?: () => void
  onMoveDown?: () => void
  onToggleVisibility?: () => void
  onInsertBefore?: () => void
  onInsertAfter?: () => void
  children: React.ReactNode
  className?: string
}

export interface BlockToolbarProps {
  block: any
  onUpdate: (updates: any) => void
  onDuplicate?: () => void
  onDelete?: () => void
  onMoveUp?: () => void
  onMoveDown?: () => void
  onToggleVisibility?: () => void
  className?: string
}

// Utility Functions
export const gutenbergUtils = {
  /**
   * Get block title for display in navigation
   */
  getBlockTitle: (block: any, blockType: any): string => {
    const config = block.configuration || {}
    
    if (config.title) return config.title
    if (config.text) return config.text
    if (config.content) {
      const textContent = config.content.replace(/<[^>]*>/g, '').trim()
      return textContent.length > 30 ? textContent.substring(0, 30) + '...' : textContent
    }
    if (config.src && blockType.name === 'image') {
      const filename = config.src.split('/').pop()
      return filename || 'Image'
    }
    if (config.url && blockType.name === 'button') {
      return config.text || 'Button'
    }
    
    return blockType.displayName
  },

  /**
   * Check if block supports specific features
   */
  blockSupports: {
    textFormatting: (blockType: any): boolean => {
      return blockType.name === 'text' || 
        blockType.configSchema?.properties?.content?.['x-component'] === 'rich-text'
    },
    
    alignment: (blockType: any): boolean => {
      return !!(blockType.configSchema?.properties?.textAlign ||
        blockType.configSchema?.properties?.alignment)
    },
    
    colors: (blockType: any): boolean => {
      return !!(blockType.configSchema?.properties?.textColor ||
        blockType.configSchema?.properties?.backgroundColor)
    },
    
    spacing: (blockType: any): boolean => {
      return !!(blockType.configSchema?.properties?.padding ||
        blockType.configSchema?.properties?.margin)
    }
  },

  /**
   * Get quick access fields for a block type
   */
  getQuickAccessFields: (properties: any, maxFields: number = 3): string[] => {
    const priorityFields = [
      'content', 'text', 'title', 'subtitle', 'description',
      'backgroundColor', 'textColor', 'color', 
      'variant', 'size', 'alignment',
      'width', 'height'
    ]
    
    const availableFields = Object.keys(properties)
    
    return priorityFields
      .filter(field => availableFields.includes(field))
      .slice(0, maxFields)
  },

  /**
   * Organize blocks into categories for navigation
   */
  organizeBlocksByCategory: (blocks: any[]) => {
    const categories = new Map()
    
    blocks.forEach(block => {
      const category = block.category || 'other'
      if (!categories.has(category)) {
        categories.set(category, [])
      }
      categories.get(category).push(block)
    })
    
    return categories
  },

  /**
   * Generate block preview content
   */
  generateBlockPreview: (block: any, blockType: any): string => {
    const config = block.configuration || {}
    
    switch (blockType.name) {
      case 'text':
        return config.content || 'Text content...'
      case 'image':
        return config.alt || 'Image'
      case 'button':
        return config.text || 'Button'
      case 'hero':
        return config.title || 'Hero section'
      default:
        return blockType.displayName
    }
  }
}

// Constants
export const GUTENBERG_CONSTANTS = {
  // Keyboard shortcuts
  SHORTCUTS: {
    DUPLICATE: 'Ctrl+Shift+D',
    DELETE: 'Shift+Alt+Z',
    MOVE_UP: 'Ctrl+Alt+T',
    MOVE_DOWN: 'Ctrl+Alt+Y',
    SETTINGS: 'Ctrl+Shift+,',
    INSERT_BEFORE: 'Ctrl+Alt+T',
    INSERT_AFTER: 'Ctrl+Alt+Y'
  },

  // Color presets for quick access
  COLOR_PRESETS: [
    '#000000', '#ffffff', '#f3f4f6', '#374151', 
    '#ef4444', '#f97316', '#eab308', '#22c55e', 
    '#3b82f6', '#8b5cf6', '#ec4899', '#06b6d4'
  ],

  // Animation classes
  ANIMATIONS: {
    HOVER: 'transition-all duration-200 hover:shadow-md',
    SELECTED: 'ring-2 ring-blue-500 ring-offset-2',
    HOVERED: 'ring-1 ring-gray-300',
    TOOLBAR_APPEAR: 'opacity-0 group-hover:opacity-100 transition-opacity',
    SLIDE_IN: 'transform transition-transform duration-200'
  },

  // Z-index layers
  Z_INDEX: {
    TOOLBAR: 50,
    POPOVER: 60,
    MODAL: 70,
    TOOLTIP: 80
  }
}

// Hook for managing Gutenberg editor state
export const useGutenbergEditor = (initialBlocks: any[] = []) => {
  const [blocks, setBlocks] = React.useState(initialBlocks)
  const [selectedBlockId, setSelectedBlockId] = React.useState<string | null>(null)
  const [hoveredBlockId, setHoveredBlockId] = React.useState<string | null>(null)

  const updateBlock = React.useCallback((blockId: string, updates: any) => {
    setBlocks(prev => prev.map(block => 
      block.id === blockId 
        ? { ...block, configuration: { ...block.configuration, ...updates } }
        : block
    ))
  }, [])

  const insertBlock = React.useCallback((blockType: string, afterBlockId?: string) => {
    const newBlock = {
      id: Date.now().toString(),
      type: blockType,
      configuration: {}
    }

    if (afterBlockId) {
      const index = blocks.findIndex(b => b.id === afterBlockId)
      setBlocks(prev => [
        ...prev.slice(0, index + 1),
        newBlock,
        ...prev.slice(index + 1)
      ])
    } else {
      setBlocks(prev => [...prev, newBlock])
    }
  }, [blocks])

  const duplicateBlock = React.useCallback((blockId: string) => {
    const block = blocks.find(b => b.id === blockId)
    if (block) {
      const newBlock = { ...block, id: Date.now().toString() }
      const index = blocks.findIndex(b => b.id === blockId)
      setBlocks(prev => [
        ...prev.slice(0, index + 1),
        newBlock,
        ...prev.slice(index + 1)
      ])
    }
  }, [blocks])

  const deleteBlock = React.useCallback((blockId: string) => {
    setBlocks(prev => prev.filter(b => b.id !== blockId))
    if (selectedBlockId === blockId) {
      setSelectedBlockId(null)
    }
  }, [selectedBlockId])

  const moveBlock = React.useCallback((blockId: string, direction: 'up' | 'down') => {
    const index = blocks.findIndex(b => b.id === blockId)
    if (index === -1) return

    const newIndex = direction === 'up' ? index - 1 : index + 1
    if (newIndex < 0 || newIndex >= blocks.length) return

    const newBlocks = [...blocks]
    const [movedBlock] = newBlocks.splice(index, 1)
    newBlocks.splice(newIndex, 0, movedBlock)
    setBlocks(newBlocks)
  }, [blocks])

  const toggleBlockVisibility = React.useCallback((blockId: string) => {
    setBlocks(prev => prev.map(block => 
      block.id === blockId 
        ? { ...block, hidden: !block.hidden }
        : block
    ))
  }, [])

  return {
    blocks,
    selectedBlockId,
    hoveredBlockId,
    setSelectedBlockId,
    setHoveredBlockId,
    updateBlock,
    insertBlock,
    duplicateBlock,
    deleteBlock,
    moveBlock,
    toggleBlockVisibility
  }
}

// Re-export React for the hook
import React from 'react'
