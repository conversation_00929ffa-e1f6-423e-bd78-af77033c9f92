'use client'

import React, { useState, useCallback, useEffect } from 'react'
import { CodeMirrorEditor, CodeLanguage } from './codemirror-editor'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Download,
  Copy,
  Check,
  RefreshCw,
  Eye,
  EyeOff,
  Sun,
  Moon,
  FileCode,
  Palette,
  Globe
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { usePageBuilder } from '../../context'

export interface CodeEditorPanelProps {
  className?: string
  onClose?: () => void
}

interface CodeContent {
  html: string
  css: string
  javascript: string
}

export function CodeEditorPanel({ className }: CodeEditorPanelProps) {
  const { state, dispatch } = usePageBuilder()
  const [activeTab, setActiveTab] = useState<'html' | 'css' | 'javascript'>('html')
  const [theme, setTheme] = useState<'light' | 'dark'>('light')
  const [showPreview, setShowPreview] = useState(false)
  const [copied, setCopied] = useState(false)
  const [isFormatting, setIsFormatting] = useState(false)

  // Initialize code content from page data
  const [codeContent, setCodeContent] = useState<CodeContent>({
    html: generateHTMLFromPage(state.page),
    css: generateCSSFromPage(state.page),
    javascript: generateJavaScriptFromPage(state.page)
  })

  // Update code content when page changes
  useEffect(() => {
    setCodeContent({
      html: generateHTMLFromPage(state.page),
      css: generateCSSFromPage(state.page),
      javascript: generateJavaScriptFromPage(state.page)
    })
  }, [state.page])

  // Handle code changes
  const handleCodeChange = useCallback((type: keyof CodeContent, value: string) => {
    setCodeContent(prev => ({
      ...prev,
      [type]: value
    }))
  }, [])

  // Apply code changes to page
  const applyCodeChanges = useCallback(() => {
    try {
      // Parse HTML and update page structure
      const updatedPage = parseCodeToPage(codeContent)
      dispatch({ type: 'SET_PAGE', payload: updatedPage })
    } catch (error) {
      console.error('Error applying code changes:', error)
    }
  }, [codeContent, dispatch])

  // Copy code to clipboard
  const copyToClipboard = useCallback(async () => {
    try {
      const fullCode = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${state.page.title}</title>
  <style>
${codeContent.css}
  </style>
</head>
<body>
${codeContent.html}
  <script>
${codeContent.javascript}
  </script>
</body>
</html>`
      
      await navigator.clipboard.writeText(fullCode)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy code:', error)
    }
  }, [codeContent, state.page.title])

  // Format code
  const formatCode = useCallback(async () => {
    setIsFormatting(true)
    try {
      // Simple formatting - in production, you might want to use prettier
      const formatted = {
        html: formatHTML(codeContent.html),
        css: formatCSS(codeContent.css),
        javascript: formatJavaScript(codeContent.javascript)
      }
      setCodeContent(formatted)
    } catch (error) {
      console.error('Error formatting code:', error)
    } finally {
      setIsFormatting(false)
    }
  }, [codeContent])

  // Export code
  const exportCode = useCallback(() => {
    const fullCode = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${state.page.title}</title>
  <style>
${codeContent.css}
  </style>
</head>
<body>
${codeContent.html}
  <script>
${codeContent.javascript}
  </script>
</body>
</html>`

    const blob = new Blob([fullCode], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${state.page.slug || 'page'}.html`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }, [codeContent, state.page])

  const tabConfig = [
    { 
      id: 'html' as const, 
      label: 'HTML', 
      icon: Globe, 
      language: 'html' as CodeLanguage,
      description: 'Page structure and content'
    },
    { 
      id: 'css' as const, 
      label: 'CSS', 
      icon: Palette, 
      language: 'css' as CodeLanguage,
      description: 'Styling and layout'
    },
    { 
      id: 'javascript' as const, 
      label: 'JavaScript', 
      icon: FileCode, 
      language: 'javascript' as CodeLanguage,
      description: 'Interactive functionality'
    }
  ]

  return (
    <div className={cn('h-full flex flex-col bg-white', className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <FileCode className="h-5 w-5 text-blue-600" />
          <div>
            <h2 className="text-lg font-semibold">Code Editor</h2>
            <p className="text-sm text-muted-foreground">
              Edit page code directly
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Theme Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
            title={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}
          >
            {theme === 'light' ? (
              <Moon className="h-4 w-4" />
            ) : (
              <Sun className="h-4 w-4" />
            )}
          </Button>

          {/* Preview Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowPreview(!showPreview)}
            title={showPreview ? 'Hide preview' : 'Show preview'}
          >
            {showPreview ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </Button>

          <Separator orientation="vertical" className="h-6" />

          {/* Format Code */}
          <Button
            variant="ghost"
            size="sm"
            onClick={formatCode}
            disabled={isFormatting}
            title="Format code"
          >
            <RefreshCw className={cn('h-4 w-4', isFormatting && 'animate-spin')} />
          </Button>

          {/* Copy Code */}
          <Button
            variant="ghost"
            size="sm"
            onClick={copyToClipboard}
            title="Copy full HTML"
          >
            {copied ? (
              <Check className="h-4 w-4 text-green-600" />
            ) : (
              <Copy className="h-4 w-4" />
            )}
          </Button>

          {/* Export Code */}
          <Button
            variant="ghost"
            size="sm"
            onClick={exportCode}
            title="Export HTML file"
          >
            <Download className="h-4 w-4" />
          </Button>

          <Separator orientation="vertical" className="h-6" />

          {/* Apply Changes */}
          <Button
            onClick={applyCodeChanges}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700"
          >
            Apply Changes
          </Button>
        </div>
      </div>

      {/* Code Editor Tabs */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="h-full flex flex-col">
          {/* Tab Headers */}
          <div className="border-b border-gray-200 px-4">
            <TabsList className="grid w-full grid-cols-3">
              {tabConfig.map(({ id, label, icon: Icon }) => (
                <TabsTrigger key={id} value={id} className="flex items-center space-x-2">
                  <Icon className="h-4 w-4" />
                  <span>{label}</span>
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          {/* Tab Content */}
          <div className="flex-1 overflow-hidden">
            {tabConfig.map(({ id, language, description }) => (
              <TabsContent key={id} value={id} className="h-full m-0 p-4">
                <div className="h-full flex flex-col space-y-4">
                  {/* Tab Description */}
                  <div className="flex items-center justify-between">
                    <Badge variant="secondary" className="text-xs">
                      {description}
                    </Badge>
                    <div className="text-xs text-muted-foreground">
                      Lines: {codeContent[id].split('\n').length}
                    </div>
                  </div>

                  {/* Code Editor */}
                  <div className="flex-1">
                    <CodeMirrorEditor
                      value={codeContent[id]}
                      onChange={(value) => handleCodeChange(id, value)}
                      language={language}
                      theme={theme}
                      height="100%"
                      placeholder={`Enter ${id.toUpperCase()} code here...`}
                      searchEnabled={true}
                      autocompletion={true}
                      lineNumbers={true}
                      foldGutter={true}
                    />
                  </div>
                </div>
              </TabsContent>
            ))}
          </div>
        </Tabs>
      </div>

      {/* Preview Panel */}
      {showPreview && (
        <div className="border-t border-gray-200 h-64 overflow-auto">
          <div className="p-4">
            <h3 className="text-sm font-medium mb-2">Live Preview</h3>
            <div className="border border-gray-200 rounded-md p-4 bg-white">
              <iframe
                srcDoc={`
                  <!DOCTYPE html>
                  <html>
                    <head>
                      <style>${codeContent.css}</style>
                    </head>
                    <body>
                      ${codeContent.html}
                      <script>${codeContent.javascript}</script>
                    </body>
                  </html>
                `}
                className="w-full h-48 border-0"
                title="Code Preview"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Helper functions for code generation and parsing
function generateHTMLFromPage(page: any): string {
  // Convert page blocks to HTML
  return page.blocks?.map((block: any) => {
    return `<!-- ${block.type} block -->
<div class="block-${block.type}" data-block-id="${block.id}">
  <!-- Block content would be rendered here -->
  <p>Block: ${block.type}</p>
</div>`
  }).join('\n\n') || '<div>No content</div>'
}

function generateCSSFromPage(page: any): string {
  // Generate CSS from page styling
  return `/* Page Styles */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Block Styles */
${page.blocks?.map((block: any) => `
.block-${block.type} {
  /* Add block-specific styles */
  margin: 1rem 0;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
}
`).join('\n') || ''}`
}

function generateJavaScriptFromPage(page: any): string {
  // Generate JavaScript for page functionality
  return `// Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
  console.log('Page loaded: ${page.title}');
  
  // Add interactive functionality here
});`
}

function parseCodeToPage(_codeContent: CodeContent): any {
  // This would parse the code back to page structure
  // For now, return a basic structure
  return {
    title: 'Updated from Code',
    blocks: []
  }
}

// Simple code formatters
function formatHTML(html: string): string {
  return html.replace(/></g, '>\n<').replace(/^\s+|\s+$/g, '')
}

function formatCSS(css: string): string {
  return css.replace(/;/g, ';\n').replace(/{/g, ' {\n').replace(/}/g, '\n}\n')
}

function formatJavaScript(js: string): string {
  return js.replace(/;/g, ';\n').replace(/{/g, ' {\n').replace(/}/g, '\n}\n')
}
