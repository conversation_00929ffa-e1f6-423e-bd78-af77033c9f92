'use client'

import React, { createContext, useContext, useReducer, useCallback } from 'react'

export interface CodeEditorState {
  isOpen: boolean
  activeTab: 'html' | 'css' | 'javascript'
  theme: 'light' | 'dark'
  showPreview: boolean
  splitView: boolean
  codeContent: {
    html: string
    css: string
    javascript: string
  }
  settings: {
    lineNumbers: boolean
    foldGutter: boolean
    searchEnabled: boolean
    autocompletion: boolean
    wordWrap: boolean
    fontSize: number
  }
  history: {
    html: string[]
    css: string[]
    javascript: string[]
  }
  historyIndex: {
    html: number
    css: number
    javascript: number
  }
  hasUnsavedChanges: boolean
  isFormatting: boolean
  errors: {
    html: string[]
    css: string[]
    javascript: string[]
  }
}

export type CodeEditorAction =
  | { type: 'OPEN_EDITOR' }
  | { type: 'CLOSE_EDITOR' }
  | { type: 'SET_ACTIVE_TAB'; payload: 'html' | 'css' | 'javascript' }
  | { type: 'SET_THEME'; payload: 'light' | 'dark' }
  | { type: 'TOGGLE_PREVIEW' }
  | { type: 'TOGGLE_SPLIT_VIEW' }
  | { type: 'UPDATE_CODE'; payload: { type: 'html' | 'css' | 'javascript'; content: string } }
  | { type: 'UPDATE_SETTINGS'; payload: Partial<CodeEditorState['settings']> }
  | { type: 'UNDO'; payload: 'html' | 'css' | 'javascript' }
  | { type: 'REDO'; payload: 'html' | 'css' | 'javascript' }
  | { type: 'FORMAT_CODE'; payload: 'html' | 'css' | 'javascript' }
  | { type: 'SET_FORMATTING'; payload: boolean }
  | { type: 'SET_ERRORS'; payload: { type: 'html' | 'css' | 'javascript'; errors: string[] } }
  | { type: 'CLEAR_ERRORS'; payload: 'html' | 'css' | 'javascript' }
  | { type: 'SAVE_CHANGES' }
  | { type: 'RESET_TO_PAGE'; payload: { html: string; css: string; javascript: string } }

const initialState: CodeEditorState = {
  isOpen: false,
  activeTab: 'html',
  theme: 'light',
  showPreview: false,
  splitView: false,
  codeContent: {
    html: '',
    css: '',
    javascript: ''
  },
  settings: {
    lineNumbers: true,
    foldGutter: true,
    searchEnabled: true,
    autocompletion: true,
    wordWrap: false,
    fontSize: 14
  },
  history: {
    html: [''],
    css: [''],
    javascript: ['']
  },
  historyIndex: {
    html: 0,
    css: 0,
    javascript: 0
  },
  hasUnsavedChanges: false,
  isFormatting: false,
  errors: {
    html: [],
    css: [],
    javascript: []
  }
}

function codeEditorReducer(state: CodeEditorState, action: CodeEditorAction): CodeEditorState {
  switch (action.type) {
    case 'OPEN_EDITOR':
      return { ...state, isOpen: true }

    case 'CLOSE_EDITOR':
      return { ...state, isOpen: false }

    case 'SET_ACTIVE_TAB':
      return { ...state, activeTab: action.payload }

    case 'SET_THEME':
      return { ...state, theme: action.payload }

    case 'TOGGLE_PREVIEW':
      return { ...state, showPreview: !state.showPreview }

    case 'TOGGLE_SPLIT_VIEW':
      return { ...state, splitView: !state.splitView }

    case 'UPDATE_CODE': {
      const { type, content } = action.payload
      const newHistory = [...state.history[type]]
      const currentIndex = state.historyIndex[type]
      
      // Remove any history after current index
      newHistory.splice(currentIndex + 1)
      // Add new content
      newHistory.push(content)
      
      // Limit history size
      if (newHistory.length > 50) {
        newHistory.shift()
      }

      return {
        ...state,
        codeContent: {
          ...state.codeContent,
          [type]: content
        },
        history: {
          ...state.history,
          [type]: newHistory
        },
        historyIndex: {
          ...state.historyIndex,
          [type]: newHistory.length - 1
        },
        hasUnsavedChanges: true
      }
    }

    case 'UPDATE_SETTINGS':
      return {
        ...state,
        settings: {
          ...state.settings,
          ...action.payload
        }
      }

    case 'UNDO': {
      const type = action.payload
      const currentIndex = state.historyIndex[type]
      
      if (currentIndex > 0) {
        const newIndex = currentIndex - 1
        const content = state.history[type][newIndex]
        
        return {
          ...state,
          codeContent: {
            ...state.codeContent,
            [type]: content
          },
          historyIndex: {
            ...state.historyIndex,
            [type]: newIndex
          },
          hasUnsavedChanges: true
        }
      }
      return state
    }

    case 'REDO': {
      const type = action.payload
      const currentIndex = state.historyIndex[type]
      const maxIndex = state.history[type].length - 1
      
      if (currentIndex < maxIndex) {
        const newIndex = currentIndex + 1
        const content = state.history[type][newIndex]
        
        return {
          ...state,
          codeContent: {
            ...state.codeContent,
            [type]: content
          },
          historyIndex: {
            ...state.historyIndex,
            [type]: newIndex
          },
          hasUnsavedChanges: true
        }
      }
      return state
    }

    case 'SET_FORMATTING':
      return { ...state, isFormatting: action.payload }

    case 'SET_ERRORS': {
      const { type, errors } = action.payload
      return {
        ...state,
        errors: {
          ...state.errors,
          [type]: errors
        }
      }
    }

    case 'CLEAR_ERRORS': {
      const type = action.payload
      return {
        ...state,
        errors: {
          ...state.errors,
          [type]: []
        }
      }
    }

    case 'SAVE_CHANGES':
      return { ...state, hasUnsavedChanges: false }

    case 'RESET_TO_PAGE': {
      const { html, css, javascript } = action.payload
      return {
        ...state,
        codeContent: { html, css, javascript },
        history: {
          html: [html],
          css: [css],
          javascript: [javascript]
        },
        historyIndex: {
          html: 0,
          css: 0,
          javascript: 0
        },
        hasUnsavedChanges: false,
        errors: {
          html: [],
          css: [],
          javascript: []
        }
      }
    }

    default:
      return state
  }
}

interface CodeEditorContextType {
  state: CodeEditorState
  dispatch: React.Dispatch<CodeEditorAction>
  openEditor: () => void
  closeEditor: () => void
  setActiveTab: (tab: 'html' | 'css' | 'javascript') => void
  setTheme: (theme: 'light' | 'dark') => void
  togglePreview: () => void
  toggleSplitView: () => void
  updateCode: (type: 'html' | 'css' | 'javascript', content: string) => void
  updateSettings: (settings: Partial<CodeEditorState['settings']>) => void
  undo: (type: 'html' | 'css' | 'javascript') => void
  redo: (type: 'html' | 'css' | 'javascript') => void
  canUndo: (type: 'html' | 'css' | 'javascript') => boolean
  canRedo: (type: 'html' | 'css' | 'javascript') => boolean
  formatCode: (type: 'html' | 'css' | 'javascript') => Promise<void>
  setErrors: (type: 'html' | 'css' | 'javascript', errors: string[]) => void
  clearErrors: (type: 'html' | 'css' | 'javascript') => void
  saveChanges: () => void
  resetToPage: (html: string, css: string, javascript: string) => void
}

const CodeEditorContext = createContext<CodeEditorContextType | undefined>(undefined)

export function useCodeEditor() {
  const context = useContext(CodeEditorContext)
  if (context === undefined) {
    throw new Error('useCodeEditor must be used within a CodeEditorProvider')
  }
  return context
}

interface CodeEditorProviderProps {
  children: React.ReactNode
}

export function CodeEditorProvider({ children }: CodeEditorProviderProps) {
  const [state, dispatch] = useReducer(codeEditorReducer, initialState)

  // Action creators
  const openEditor = useCallback(() => {
    dispatch({ type: 'OPEN_EDITOR' })
  }, [])

  const closeEditor = useCallback(() => {
    dispatch({ type: 'CLOSE_EDITOR' })
  }, [])

  const setActiveTab = useCallback((tab: 'html' | 'css' | 'javascript') => {
    dispatch({ type: 'SET_ACTIVE_TAB', payload: tab })
  }, [])

  const setTheme = useCallback((theme: 'light' | 'dark') => {
    dispatch({ type: 'SET_THEME', payload: theme })
  }, [])

  const togglePreview = useCallback(() => {
    dispatch({ type: 'TOGGLE_PREVIEW' })
  }, [])

  const toggleSplitView = useCallback(() => {
    dispatch({ type: 'TOGGLE_SPLIT_VIEW' })
  }, [])

  const updateCode = useCallback((type: 'html' | 'css' | 'javascript', content: string) => {
    dispatch({ type: 'UPDATE_CODE', payload: { type, content } })
  }, [])

  const updateSettings = useCallback((settings: Partial<CodeEditorState['settings']>) => {
    dispatch({ type: 'UPDATE_SETTINGS', payload: settings })
  }, [])

  const undo = useCallback((type: 'html' | 'css' | 'javascript') => {
    dispatch({ type: 'UNDO', payload: type })
  }, [])

  const redo = useCallback((type: 'html' | 'css' | 'javascript') => {
    dispatch({ type: 'REDO', payload: type })
  }, [])

  const canUndo = useCallback((type: 'html' | 'css' | 'javascript') => {
    return state.historyIndex[type] > 0
  }, [state.historyIndex])

  const canRedo = useCallback((type: 'html' | 'css' | 'javascript') => {
    return state.historyIndex[type] < state.history[type].length - 1
  }, [state.historyIndex, state.history])

  const formatCode = useCallback(async (type: 'html' | 'css' | 'javascript') => {
    dispatch({ type: 'SET_FORMATTING', payload: true })
    
    try {
      // Simple formatting logic - in production, use prettier or similar
      const content = state.codeContent[type]
      let formatted = content
      
      switch (type) {
        case 'html':
          formatted = content.replace(/></g, '>\n<').replace(/^\s+|\s+$/g, '')
          break
        case 'css':
          formatted = content.replace(/;/g, ';\n').replace(/{/g, ' {\n').replace(/}/g, '\n}\n')
          break
        case 'javascript':
          formatted = content.replace(/;/g, ';\n').replace(/{/g, ' {\n').replace(/}/g, '\n}\n')
          break
      }
      
      updateCode(type, formatted)
    } catch (error) {
      console.error('Error formatting code:', error)
    } finally {
      dispatch({ type: 'SET_FORMATTING', payload: false })
    }
  }, [state.codeContent, updateCode])

  const setErrors = useCallback((type: 'html' | 'css' | 'javascript', errors: string[]) => {
    dispatch({ type: 'SET_ERRORS', payload: { type, errors } })
  }, [])

  const clearErrors = useCallback((type: 'html' | 'css' | 'javascript') => {
    dispatch({ type: 'CLEAR_ERRORS', payload: type })
  }, [])

  const saveChanges = useCallback(() => {
    dispatch({ type: 'SAVE_CHANGES' })
  }, [])

  const resetToPage = useCallback((html: string, css: string, javascript: string) => {
    dispatch({ type: 'RESET_TO_PAGE', payload: { html, css, javascript } })
  }, [])

  const contextValue: CodeEditorContextType = {
    state,
    dispatch,
    openEditor,
    closeEditor,
    setActiveTab,
    setTheme,
    togglePreview,
    toggleSplitView,
    updateCode,
    updateSettings,
    undo,
    redo,
    canUndo,
    canRedo,
    formatCode,
    setErrors,
    clearErrors,
    saveChanges,
    resetToPage
  }

  return (
    <CodeEditorContext.Provider value={contextValue}>
      {children}
    </CodeEditorContext.Provider>
  )
}
