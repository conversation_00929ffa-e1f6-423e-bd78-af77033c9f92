'use client'

import React, { useEffect, useRef, useState, useCallback } from 'react'
import { EditorView } from '@codemirror/view'
import { EditorState, Extension, StateEffect } from '@codemirror/state'
import { javascript } from '@codemirror/lang-javascript'
import { html } from '@codemirror/lang-html'
import { css } from '@codemirror/lang-css'
import { oneDark } from '@codemirror/theme-one-dark'
import { searchKeymap } from '@codemirror/search'
import { autocompletion } from '@codemirror/autocomplete'
import { lintKeymap } from '@codemirror/lint'
import { keymap } from '@codemirror/view'
import { defaultKeymap, historyKeymap } from '@codemirror/commands'
import { cn } from '@/lib/utils'
import { basicSetup } from '@uiw/react-codemirror'

export type CodeLanguage = 'javascript' | 'typescript' | 'html' | 'css' | 'json' | 'jsx' | 'tsx'

export interface CodeMirrorEditorProps {
  value: string
  onChange: (value: string) => void
  language?: CodeLanguage
  theme?: 'light' | 'dark'
  readOnly?: boolean
  placeholder?: string
  className?: string
  height?: string
  minHeight?: string
  maxHeight?: string
  lineNumbers?: boolean
  foldGutter?: boolean
  searchEnabled?: boolean
  autocompletion?: boolean
  onFocus?: () => void
  onBlur?: () => void
  extensions?: Extension[]
}

export function CodeMirrorEditor({
  value,
  onChange,
  language = 'javascript',
  theme = 'light',
  readOnly = false,
  placeholder = '',
  className,
  height = '400px',
  minHeight,
  maxHeight,
  lineNumbers = true,
  foldGutter = true,
  searchEnabled = true,
  autocompletion: enableAutocompletion = true,
  onFocus,
  onBlur,
  extensions: customExtensions = []
}: CodeMirrorEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null)
  const viewRef = useRef<EditorView | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)

  // Get language extension
  const getLanguageExtension = useCallback((lang: CodeLanguage): Extension => {
    switch (lang) {
      case 'javascript':
      case 'jsx':
        return javascript({ jsx: lang === 'jsx' })
      case 'typescript':
      case 'tsx':
        return javascript({ typescript: true, jsx: lang === 'tsx' })
      case 'html':
        return html()
      case 'css':
        return css()
      case 'json':
        return javascript()
      default:
        return javascript()
    }
  }, [])

  // Create editor extensions
  const createExtensions = useCallback((): Extension[] => {
    const extensions: Extension[] = [
      basicSetup({
        lineNumbers,
        foldGutter,
        dropCursor: false,
        allowMultipleSelections: false,
        indentOnInput: true,
        bracketMatching: true,
        closeBrackets: true,
        autocompletion: enableAutocompletion,
        highlightSelectionMatches: true,
        searchKeymap: searchEnabled
      }),
      getLanguageExtension(language),
      keymap.of([
        ...defaultKeymap,
        ...historyKeymap,
        ...(searchEnabled ? searchKeymap : []),
        ...(enableAutocompletion ? [] : []),
        ...lintKeymap
      ]),
      EditorView.updateListener.of((update) => {
        if (update.docChanged && !readOnly) {
          const newValue = update.state.doc.toString()
          onChange(newValue)
        }
      }),
      EditorState.readOnly.of(readOnly)
    ]

    // Add theme
    if (theme === 'dark') {
      extensions.push(oneDark)
    }

    // Add autocompletion if enabled
    if (enableAutocompletion) {
      extensions.push(autocompletion())
    }

    // Add focus/blur handlers
    if (onFocus || onBlur) {
      extensions.push(
        EditorView.focusChangeEffect.of((_state, focusing) => {
          if (focusing && onFocus) {
            onFocus()
          } else if (!focusing && onBlur) {
            onBlur()
          }
          return null
        })
      )
    }

    // Add placeholder
    if (placeholder) {
      extensions.push(
        EditorView.theme({
          '.cm-placeholder': {
            color: '#999',
            fontStyle: 'italic'
          }
        })
      )
    }

    // Add custom extensions
    extensions.push(...customExtensions)

    return extensions
  }, [
    language,
    theme,
    readOnly,
    lineNumbers,
    foldGutter,
    searchEnabled,
    enableAutocompletion,
    placeholder,
    onChange,
    onFocus,
    onBlur,
    customExtensions,
    getLanguageExtension
  ])

  // Initialize editor
  useEffect(() => {
    if (!editorRef.current || isInitialized) return

    const state = EditorState.create({
      doc: value,
      extensions: createExtensions()
    })

    const view = new EditorView({
      state,
      parent: editorRef.current
    })

    viewRef.current = view
    setIsInitialized(true)

    return () => {
      view.destroy()
      viewRef.current = null
      setIsInitialized(false)
    }
  }, []) // Only run once on mount

  // Update editor when props change
  useEffect(() => {
    if (!viewRef.current || !isInitialized) return

    const view = viewRef.current
    const currentValue = view.state.doc.toString()

    // Update document if value changed externally
    if (value !== currentValue) {
      view.dispatch({
        changes: {
          from: 0,
          to: view.state.doc.length,
          insert: value
        }
      })
    }
  }, [value, isInitialized])

  // Update extensions when relevant props change
  useEffect(() => {
    if (!viewRef.current || !isInitialized) return

    const view = viewRef.current
    view.dispatch({
      effects: StateEffect.reconfigure.of(createExtensions())
    })
  }, [
    language,
    theme,
    readOnly,
    lineNumbers,
    foldGutter,
    searchEnabled,
    enableAutocompletion,
    placeholder,
    customExtensions,
    createExtensions,
    isInitialized
  ])

  return (
    <div
      className={cn(
        'border border-gray-200 rounded-md overflow-hidden',
        'focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2',
        theme === 'dark' && 'border-gray-700',
        className
      )}
      style={{
        height,
        minHeight,
        maxHeight,
        overflow: 'auto'
      }}
    >
      <div
        ref={editorRef}
        className="h-full w-full"
        style={{ fontSize: '14px' }}
      />
    </div>
  )
}

// Export utility functions for external use
export const createCodeMirrorExtensions = {
  getLanguageExtension: (language: CodeLanguage): Extension => {
    switch (language) {
      case 'javascript':
      case 'jsx':
        return javascript({ jsx: language === 'jsx' })
      case 'typescript':
      case 'tsx':
        return javascript({ typescript: true, jsx: language === 'tsx' })
      case 'html':
        return html()
      case 'css':
        return css()
      case 'json':
        return javascript()
      default:
        return javascript()
    }
  }
}
