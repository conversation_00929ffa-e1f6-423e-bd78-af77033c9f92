"use client"

import React, { useState, useRef, useEffect } from 'react'
import { useChat } from '@ai-sdk/react'
import { usePageBuilder } from '../context'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Sparkles,
  Send,
  Loader2,
  Wand2,
  Layout,
  Palette,
  Type,
  ShoppingBag,
  Users,
  Zap,
  RefreshCw
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

interface AIPageDesignerProps {
  className?: string
}

export function AIPageDesigner({ className }: AIPageDesignerProps) {
  const { state } = usePageBuilder()
  const [isGenerating, setIsGenerating] = useState(false)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    error,
    stop,
    setMessages,
  } = useChat({
    api: '/api/ai-page-designer',
    maxSteps: 5,
    onFinish: () => {
      setIsGenerating(false)

      // AI has finished generating - show success message
      toast.success('AI has finished processing your request!')
    },
    onError: (error) => {
      setIsGenerating(false)
      toast.error('AI Designer error: ' + error.message)
    },
  })

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]')
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight
      }
    }
  }, [messages])

  // Quick action prompts
  const quickActions = [
    {
      icon: Layout,
      label: 'Create Layout',
      prompt: 'Create a modern landing page layout for a kids clothing store with hero section, featured products, and testimonials',
      color: 'bg-blue-500'
    },
    {
      icon: Palette,
      label: 'Style Page',
      prompt: 'Apply a playful, colorful design theme suitable for children with rounded corners and bright colors',
      color: 'bg-purple-500'
    },
    {
      icon: ShoppingBag,
      label: 'E-commerce Focus',
      prompt: 'Add product showcase sections, call-to-action buttons, and shopping-focused elements',
      color: 'bg-green-500'
    },
    {
      icon: Users,
      label: 'Kids Theme',
      prompt: 'Make this page more appealing to children and parents with fun elements, animations, and kid-friendly content',
      color: 'bg-pink-500'
    },
    {
      icon: Type,
      label: 'Content',
      prompt: 'Generate engaging content for a kids clothing brand including headings, descriptions, and call-to-actions',
      color: 'bg-orange-500'
    },
    {
      icon: Zap,
      label: 'Optimize',
      prompt: 'Optimize this page for better performance, accessibility, and mobile responsiveness',
      color: 'bg-yellow-500'
    }
  ]

  const handleQuickAction = (prompt: string) => {
    setIsGenerating(true)
    handleSubmit(new Event('submit') as any, {
      body: {
        messages: [{ role: 'user', content: prompt }],
        pageData: state.page,
        action: 'designPage'
      }
    })
  }

  const handleCustomSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim()) return
    
    setIsGenerating(true)
    handleSubmit(e, {
      body: {
        pageData: state.page,
        action: 'designPage'
      }
    })
  }

  const clearConversation = () => {
    setMessages([])
    toast.success('Conversation cleared')
  }

  return (
    <Card className={cn('h-full flex flex-col', className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <div className="p-1.5 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500">
            <Sparkles className="h-4 w-4 text-white" />
          </div>
          AI Page Designer
          <Badge variant="secondary" className="ml-auto text-xs">
            Realtime
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col gap-4 p-4 pt-0">
        {/* Quick Actions */}
        {messages.length === 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground">Quick Actions</h4>
            <div className="grid grid-cols-2 gap-2">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction(action.prompt)}
                  disabled={isGenerating}
                  className="h-auto p-3 flex flex-col items-center gap-2 text-xs"
                >
                  <div className={cn('p-1.5 rounded-md text-white', action.color)}>
                    <action.icon className="h-3 w-3" />
                  </div>
                  {action.label}
                </Button>
              ))}
            </div>
            <Separator />
          </div>
        )}

        {/* Chat Messages */}
        <ScrollArea ref={scrollAreaRef} className="flex-1 pr-4">
          <div className="space-y-4">
            {messages.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Wand2 className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Ask me to design or modify your page!</p>
                <p className="text-xs mt-1">I can create layouts, add content, style elements, and more.</p>
              </div>
            )}
            
            {messages.map((message, index) => (
              <div
                key={index}
                className={cn(
                  'flex gap-3 text-sm',
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                )}
              >
                {message.role === 'assistant' && (
                  <div className="p-1.5 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1">
                    <Sparkles className="h-3 w-3 text-white" />
                  </div>
                )}
                
                <div
                  className={cn(
                    'max-w-[80%] rounded-lg px-3 py-2',
                    message.role === 'user'
                      ? 'bg-primary text-primary-foreground ml-auto'
                      : 'bg-muted'
                  )}
                >
                  <div className="whitespace-pre-wrap">{message.content}</div>
                </div>
              </div>
            ))}
            
            {isGenerating && (
              <div className="flex gap-3 text-sm">
                <div className="p-1.5 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1">
                  <Loader2 className="h-3 w-3 text-white animate-spin" />
                </div>
                <div className="bg-muted rounded-lg px-3 py-2">
                  <div className="flex items-center gap-2">
                    <span>AI is designing...</span>
                    <div className="flex gap-1">
                      <div className="w-1 h-1 bg-current rounded-full animate-pulse" />
                      <div className="w-1 h-1 bg-current rounded-full animate-pulse delay-100" />
                      <div className="w-1 h-1 bg-current rounded-full animate-pulse delay-200" />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Input Area */}
        <div className="space-y-2">
          {messages.length > 0 && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={clearConversation}
                className="text-xs"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Clear
              </Button>
              {isGenerating && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={stop}
                  className="text-xs"
                >
                  Stop
                </Button>
              )}
            </div>
          )}
          
          <form onSubmit={handleCustomSubmit} className="flex gap-2">
            <Input
              ref={inputRef}
              value={input}
              onChange={handleInputChange}
              placeholder="Describe what you want to create or modify..."
              disabled={isGenerating}
              className="flex-1"
            />
            <Button
              type="submit"
              size="sm"
              disabled={isGenerating || !input.trim()}
              className="px-3"
            >
              {isGenerating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </form>
        </div>

        {error && (
          <div className="text-sm text-destructive bg-destructive/10 rounded-lg p-2">
            Error: {error.message}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
