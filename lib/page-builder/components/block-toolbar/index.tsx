'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import {
  Settings,
  MoreVertical,
  Copy,
  Trash2,
  MoveUp,
  MoveDown,
  Eye,
  EyeOff,
  Palette,
  Type,
  Layout,
  Zap,
  ChevronDown
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { DynamicBlockEditor } from '../properties-panel/dynamic-block-editor'
import { blockRegistry } from '../../blocks/registry'

interface BlockToolbarProps {
  block: any
  onUpdate: (updates: any) => void
  onDuplicate?: () => void
  onDelete?: () => void
  onMoveUp?: () => void
  onMoveDown?: () => void
  onToggleVisibility?: () => void
  className?: string
}

export function BlockToolbar({
  block,
  onUpdate,
  onDuplicate,
  onDelete,
  onMoveUp,
  onMoveDown,
  onToggleVisibility,
  className
}: BlockToolbarProps) {
  const [activePopover, setActivePopover] = useState<string | null>(null)
  
  const blockDefinition = blockRegistry.getBlockType(block.type)
  
  if (!blockDefinition) {
    return null
  }

  const hasSettings = blockDefinition.configSchema && 
    Object.keys(blockDefinition.configSchema.properties || {}).length > 0

  return (
    <div className={cn(
      'flex items-center gap-1 p-1 bg-white border border-gray-200 rounded-lg shadow-sm',
      'absolute top-0 left-0 transform -translate-y-full z-50',
      className
    )}>
      {/* Block Type Badge */}
      <Badge variant="secondary" className="text-xs px-2 py-1">
        <span className="mr-1">{blockDefinition.icon}</span>
        {blockDefinition.displayName}
      </Badge>

      {/* Quick Settings Popover */}
      {hasSettings && (
        <Popover 
          open={activePopover === 'settings'} 
          onOpenChange={(open) => setActivePopover(open ? 'settings' : null)}
        >
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-blue-50"
            >
              <Settings className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent 
            className="w-80 p-0" 
            align="start"
            side="bottom"
            sideOffset={8}
          >
            <div className="p-4 border-b">
              <h3 className="font-semibold text-sm flex items-center gap-2">
                <span>{blockDefinition.icon}</span>
                {blockDefinition.displayName} Settings
              </h3>
              {blockDefinition.description && (
                <p className="text-xs text-muted-foreground mt-1">
                  {blockDefinition.description}
                </p>
              )}
            </div>
            <div className="max-h-96 overflow-y-auto">
              <DynamicBlockEditor
                block={block}
                blockType={blockDefinition}
                schema={blockDefinition.configSchema}
                onUpdate={onUpdate}
              />
            </div>
          </PopoverContent>
        </Popover>
      )}

      {/* Block Actions Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-gray-50"
          >
            <MoreVertical className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-48">
          {/* Quick Actions */}
          {onDuplicate && (
            <DropdownMenuItem onClick={onDuplicate}>
              <Copy className="h-4 w-4 mr-2" />
              Duplicate
              <span className="ml-auto text-xs text-muted-foreground">
                Ctrl+Shift+D
              </span>
            </DropdownMenuItem>
          )}
          
          {onToggleVisibility && (
            <DropdownMenuItem onClick={onToggleVisibility}>
              {block.hidden ? (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Show Block
                </>
              ) : (
                <>
                  <EyeOff className="h-4 w-4 mr-2" />
                  Hide Block
                </>
              )}
            </DropdownMenuItem>
          )}

          <DropdownMenuSeparator />

          {/* Movement Actions */}
          {onMoveUp && (
            <DropdownMenuItem onClick={onMoveUp}>
              <MoveUp className="h-4 w-4 mr-2" />
              Move Up
              <span className="ml-auto text-xs text-muted-foreground">
                Ctrl+Alt+T
              </span>
            </DropdownMenuItem>
          )}
          
          {onMoveDown && (
            <DropdownMenuItem onClick={onMoveDown}>
              <MoveDown className="h-4 w-4 mr-2" />
              Move Down
              <span className="ml-auto text-xs text-muted-foreground">
                Ctrl+Alt+Y
              </span>
            </DropdownMenuItem>
          )}

          <DropdownMenuSeparator />

          {/* Advanced Settings */}
          {hasSettings && (
            <DropdownMenuItem 
              onClick={() => setActivePopover('settings')}
              className="text-blue-600"
            >
              <Settings className="h-4 w-4 mr-2" />
              Show more settings
              <span className="ml-auto text-xs text-muted-foreground">
                Ctrl+Shift+,
              </span>
            </DropdownMenuItem>
          )}

          <DropdownMenuSeparator />

          {/* Destructive Actions */}
          {onDelete && (
            <DropdownMenuItem 
              onClick={onDelete}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Remove block
              <span className="ml-auto text-xs text-muted-foreground">
                Shift+Alt+Z
              </span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

// Quick Settings Buttons for common actions
interface QuickSettingsProps {
  block: any
  blockType: any
  onUpdate: (updates: any) => void
}

export function QuickSettings({ block, blockType, onUpdate }: QuickSettingsProps) {
  // Safety checks
  if (!block || !blockType || !onUpdate || !blockType.configSchema) {
    return null
  }

  // Get quick access fields
  const schema = blockType.configSchema
  const properties = schema.properties || {}
  const quickFields = getQuickAccessFields(properties, 3)

  if (quickFields.length === 0) {
    return null
  }

  return (
    <div className="flex items-center gap-1">
      {quickFields.map((fieldId) => {
        const property = properties[fieldId]
        const value = block.configuration?.[fieldId]

        // Skip if property is not found
        if (!property) return null

        return (
          <Popover key={fieldId}>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-xs hover:bg-blue-50"
              >
                {property.title || fieldId}
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64 p-3" align="start">
              <div className="space-y-2">
                <label className="text-xs font-medium block">
                  {property.title || fieldId}
                </label>
                <QuickFieldInput
                  property={property}
                  value={value}
                  onChange={(newValue) => onUpdate({ [fieldId]: newValue })}
                />
                {property.description && (
                  <p className="text-xs text-gray-500">{property.description}</p>
                )}
              </div>
            </PopoverContent>
          </Popover>
        )
      }).filter(Boolean)}
    </div>
  )
}

// Helper component for individual quick setting fields
function QuickSettingField({ 
  fieldId, 
  block, 
  blockType, 
  onUpdate 
}: { 
  fieldId: string
  block: any
  blockType: any
  onUpdate: (updates: any) => void
}) {
  const schema = blockType.configSchema
  const property = schema?.properties?.[fieldId]
  
  if (!property) return null

  const value = block.configuration?.[fieldId]
  
  return (
    <div className="space-y-1">
      <label className="text-xs font-medium text-gray-700">
        {property.title || fieldId}
      </label>
      {/* Render appropriate input based on property type */}
      {property.type === 'boolean' ? (
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={value || false}
            onChange={(e) => onUpdate({ [fieldId]: e.target.checked })}
            className="rounded border-gray-300"
          />
          <span className="text-xs text-gray-600">{property.description}</span>
        </div>
      ) : property.enum ? (
        <select
          value={value || property.default || ''}
          onChange={(e) => onUpdate({ [fieldId]: e.target.value })}
          className="w-full px-2 py-1 text-xs border rounded"
        >
          {property.enum.map((option: string) => (
            <option key={option} value={option}>
              {option.charAt(0).toUpperCase() + option.slice(1)}
            </option>
          ))}
        </select>
      ) : (
        <input
          type={property.type === 'number' ? 'number' : 'text'}
          value={value || ''}
          onChange={(e) => onUpdate({ 
            [fieldId]: property.type === 'number' ? Number(e.target.value) : e.target.value 
          })}
          placeholder={property.placeholder}
          className="w-full px-2 py-1 text-xs border rounded"
        />
      )}
    </div>
  )
}

// Quick field input component
function QuickFieldInput({
  property,
  value,
  onChange
}: {
  property: any
  value: any
  onChange: (value: any) => void
}) {
  if (property.type === 'boolean') {
    return (
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          checked={value || false}
          onChange={(e) => onChange(e.target.checked)}
          className="rounded border-gray-300"
        />
        <span className="text-xs text-gray-600">{property.description}</span>
      </div>
    )
  }

  if (property.enum) {
    return (
      <select
        value={value || property.default || ''}
        onChange={(e) => onChange(e.target.value)}
        className="w-full px-2 py-1 text-xs border rounded"
      >
        {property.enum.map((option: string) => (
          <option key={option} value={option}>
            {option.charAt(0).toUpperCase() + option.slice(1)}
          </option>
        ))}
      </select>
    )
  }

  if (property['x-component'] === 'color-picker') {
    return (
      <div className="flex gap-2">
        <input
          type="color"
          value={value || '#000000'}
          onChange={(e) => onChange(e.target.value)}
          className="h-8 w-16 rounded border"
        />
        <input
          type="text"
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder="#000000"
          className="flex-1 px-2 py-1 text-xs border rounded"
        />
      </div>
    )
  }

  if (property['x-component'] === 'textarea') {
    return (
      <textarea
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        placeholder={property.placeholder}
        rows={3}
        className="w-full px-2 py-1 text-xs border rounded resize-none"
      />
    )
  }

  return (
    <input
      type={property.type === 'number' ? 'number' : 'text'}
      value={value || ''}
      onChange={(e) => onChange(
        property.type === 'number' ? Number(e.target.value) : e.target.value
      )}
      placeholder={property.placeholder}
      className="w-full px-2 py-1 text-xs border rounded"
    />
  )
}

// Helper function to get quick access fields
function getQuickAccessFields(properties: any, maxFields: number): string[] {
  const priorityFields = [
    'content', 'text', 'title', 'subtitle', 'description',
    'backgroundColor', 'textColor', 'color',
    'variant', 'size', 'alignment',
    'width', 'height'
  ]

  const availableFields = Object.keys(properties)

  return priorityFields
    .filter(field => availableFields.includes(field))
    .slice(0, maxFields)
}
