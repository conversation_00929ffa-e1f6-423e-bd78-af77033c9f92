'use client'

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Link,
  Palette,
  Type,
  ChevronDown,
  MoreHorizontal
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface BlockFormatToolbarProps {
  block: any
  blockType: any
  onUpdate: (updates: any) => void
  className?: string
}

export function BlockFormatToolbar({
  block,
  blockType,
  onUpdate,
  className
}: BlockFormatToolbarProps) {
  const [activePopover, setActivePopover] = useState<string | null>(null)

  // Safety checks
  if (!block || !blockType || !onUpdate) {
    return null
  }

  // Get current formatting values
  const config = block.configuration || {}

  // Check if block supports text formatting
  const supportsTextFormatting = blockType.name === 'text' ||
    blockType.configSchema?.properties?.content?.['x-component'] === 'rich-text'

  // Check if block supports alignment
  const supportsAlignment = blockType.configSchema?.properties?.textAlign ||
    blockType.configSchema?.properties?.alignment

  // Check if block supports colors
  const supportsColors = blockType.configSchema?.properties?.textColor ||
    blockType.configSchema?.properties?.backgroundColor

  if (!supportsTextFormatting && !supportsAlignment && !supportsColors) {
    return null
  }

  return (
    <div className={cn(
      'flex items-center gap-1 p-1 bg-white border border-gray-200 rounded-lg shadow-sm',
      className
    )}>
      {/* Text Formatting */}
      {supportsTextFormatting && (
        <>
          <Button
            variant={config.bold ? "default" : "ghost"}
            size="sm"
            onClick={() => onUpdate({ bold: !config.bold })}
            className="h-8 w-8 p-0"
          >
            <Bold className="h-4 w-4" />
          </Button>

          <Button
            variant={config.italic ? "default" : "ghost"}
            size="sm"
            onClick={() => onUpdate({ italic: !config.italic })}
            className="h-8 w-8 p-0"
          >
            <Italic className="h-4 w-4" />
          </Button>

          <Button
            variant={config.underline ? "default" : "ghost"}
            size="sm"
            onClick={() => onUpdate({ underline: !config.underline })}
            className="h-8 w-8 p-0"
          >
            <Underline className="h-4 w-4" />
          </Button>

          <div className="w-px h-6 bg-gray-200 mx-1" />
        </>
      )}

      {/* Text Alignment */}
      {supportsAlignment && (
        <>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2">
                {config.textAlign === 'center' ? <AlignCenter className="h-4 w-4" /> :
                 config.textAlign === 'right' ? <AlignRight className="h-4 w-4" /> :
                 config.textAlign === 'justify' ? <AlignJustify className="h-4 w-4" /> :
                 <AlignLeft className="h-4 w-4" />}
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuItem onClick={() => onUpdate({ textAlign: 'left' })}>
                <AlignLeft className="h-4 w-4 mr-2" />
                Align left
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onUpdate({ textAlign: 'center' })}>
                <AlignCenter className="h-4 w-4 mr-2" />
                Align center
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onUpdate({ textAlign: 'right' })}>
                <AlignRight className="h-4 w-4 mr-2" />
                Align right
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onUpdate({ textAlign: 'justify' })}>
                <AlignJustify className="h-4 w-4 mr-2" />
                Justify
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <div className="w-px h-6 bg-gray-200 mx-1" />
        </>
      )}

      {/* Typography */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 px-2">
            <Type className="h-4 w-4" />
            <ChevronDown className="h-3 w-3 ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-48">
          <DropdownMenuItem onClick={() => onUpdate({ fontSize: 'small' })}>
            <span className="text-sm">Small text</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onUpdate({ fontSize: 'medium' })}>
            <span className="text-base">Normal text</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onUpdate({ fontSize: 'large' })}>
            <span className="text-lg">Large text</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onUpdate({ fontSize: 'xl' })}>
            <span className="text-xl">Extra large</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Colors */}
      {supportsColors && (
        <>
          <div className="w-px h-6 bg-gray-200 mx-1" />
          
          <Popover 
            open={activePopover === 'colors'} 
            onOpenChange={(open) => setActivePopover(open ? 'colors' : null)}
          >
            <PopoverTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2">
                <Palette className="h-4 w-4" />
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64 p-3" align="start">
              <div className="space-y-3">
                <h4 className="font-medium text-sm">Colors</h4>
                
                {blockType.configSchema?.properties?.textColor && (
                  <div className="space-y-2">
                    <label className="text-xs font-medium">Text Color</label>
                    <div className="flex gap-2">
                      <input
                        type="color"
                        value={config.textColor || '#000000'}
                        onChange={(e) => onUpdate({ textColor: e.target.value })}
                        className="h-8 w-16 rounded border"
                      />
                      <input
                        type="text"
                        value={config.textColor || ''}
                        onChange={(e) => onUpdate({ textColor: e.target.value })}
                        placeholder="#000000"
                        className="flex-1 px-2 py-1 text-xs border rounded"
                      />
                    </div>
                  </div>
                )}

                {blockType.configSchema?.properties?.backgroundColor && (
                  <div className="space-y-2">
                    <label className="text-xs font-medium">Background Color</label>
                    <div className="flex gap-2">
                      <input
                        type="color"
                        value={config.backgroundColor || '#ffffff'}
                        onChange={(e) => onUpdate({ backgroundColor: e.target.value })}
                        className="h-8 w-16 rounded border"
                      />
                      <input
                        type="text"
                        value={config.backgroundColor || ''}
                        onChange={(e) => onUpdate({ backgroundColor: e.target.value })}
                        placeholder="transparent"
                        className="flex-1 px-2 py-1 text-xs border rounded"
                      />
                    </div>
                  </div>
                )}

                {/* Color Presets */}
                <div className="space-y-2">
                  <label className="text-xs font-medium">Presets</label>
                  <div className="grid grid-cols-6 gap-1">
                    {COLOR_PRESETS.map((color) => (
                      <button
                        key={color}
                        onClick={() => onUpdate({ textColor: color })}
                        className="h-6 w-6 rounded border border-gray-200 hover:scale-110 transition-transform"
                        style={{ backgroundColor: color }}
                        title={color}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </>
      )}

      {/* Link */}
      {supportsTextFormatting && (
        <>
          <div className="w-px h-6 bg-gray-200 mx-1" />
          
          <Popover 
            open={activePopover === 'link'} 
            onOpenChange={(open) => setActivePopover(open ? 'link' : null)}
          >
            <PopoverTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8">
                <Link className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-3" align="start">
              <div className="space-y-3">
                <h4 className="font-medium text-sm">Add Link</h4>
                
                <div className="space-y-2">
                  <label className="text-xs font-medium">URL</label>
                  <input
                    type="url"
                    placeholder="https://example.com"
                    className="w-full px-2 py-1 text-xs border rounded"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-xs font-medium">Link Text</label>
                  <input
                    type="text"
                    placeholder="Click here"
                    className="w-full px-2 py-1 text-xs border rounded"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" />
                  <label className="text-xs">Open in new tab</label>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" className="flex-1">Apply</Button>
                  <Button variant="outline" size="sm">Remove</Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </>
      )}

      {/* More Options */}
      <div className="w-px h-6 bg-gray-200 mx-1" />
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem>
            Clear formatting
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            Copy styles
          </DropdownMenuItem>
          <DropdownMenuItem>
            Paste styles
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

// Color presets for quick access
const COLOR_PRESETS = [
  '#000000', '#ffffff', '#f3f4f6', '#374151', '#ef4444', '#f97316',
  '#eab308', '#22c55e', '#3b82f6', '#8b5cf6', '#ec4899', '#06b6d4'
]

// Inline format toolbar that appears on text selection
interface InlineFormatToolbarProps {
  onFormat: (format: string, value?: any) => void
  position: { x: number; y: number }
  visible: boolean
}

export function InlineFormatToolbar({ 
  onFormat, 
  position, 
  visible 
}: InlineFormatToolbarProps) {
  if (!visible) return null

  return (
    <div
      className="fixed z-50 flex items-center gap-1 p-1 bg-gray-900 text-white rounded-lg shadow-lg"
      style={{
        left: position.x,
        top: position.y - 50,
        transform: 'translateX(-50%)'
      }}
    >
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 text-white hover:bg-gray-700"
        onClick={() => onFormat('bold')}
      >
        <Bold className="h-4 w-4" />
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 text-white hover:bg-gray-700"
        onClick={() => onFormat('italic')}
      >
        <Italic className="h-4 w-4" />
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 text-white hover:bg-gray-700"
        onClick={() => onFormat('underline')}
      >
        <Underline className="h-4 w-4" />
      </Button>

      <div className="w-px h-6 bg-gray-600 mx-1" />
      
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 text-white hover:bg-gray-700"
        onClick={() => onFormat('link')}
      >
        <Link className="h-4 w-4" />
      </Button>
    </div>
  )
}
