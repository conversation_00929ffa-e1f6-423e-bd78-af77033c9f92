'use client'

import React, { useState, useCallback } from 'react'
import { useChat } from 'ai/react'
import { usePageBuilder } from '../context'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import {
  Wand2,
  Sparkles,
  Layout,
  Grid,
  Smartphone,
  Tablet,
  Monitor,
  Tv,
  Zap,
  Target,
  Palette,
  Type,
  Image,
  ShoppingCart,
  Users,
  TrendingUp,
  Send,
  Loader2,
  <PERSON>fresh<PERSON><PERSON>,
  Download,
  Eye
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

interface AILayoutGeneratorProps {
  className?: string
  onLayoutGenerated?: (layout: any) => void
}

interface LayoutTemplate {
  id: string
  name: string
  description: string
  category: string
  preview: string
  blocks: string[]
  gridLayout: any[]
}

export function AILayoutGenerator({ className, onLayoutGenerated }: AILayoutGeneratorProps) {
  const { addBlock, clearBlocks, state } = usePageBuilder()
  const [activeTab, setActiveTab] = useState<'prompt' | 'templates' | 'customize'>('prompt')
  const [isGenerating, setIsGenerating] = useState(false)
  
  // Form state
  const [prompt, setPrompt] = useState('')
  const [pageType, setPageType] = useState('landing')
  const [industry, setIndustry] = useState('kids-clothing')
  const [style, setStyle] = useState('modern')
  const [targetDevice, setTargetDevice] = useState('desktop')
  const [includeBlocks, setIncludeBlocks] = useState<string[]>(['hero', 'features', 'cta'])
  const [colorScheme, setColorScheme] = useState('brand')
  const [layoutDensity, setLayoutDensity] = useState('balanced')

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
    setMessages
  } = useChat({
    api: '/api/ai-layout-generator',
    onFinish: (message) => {
      setIsGenerating(false)
      try {
        const response = JSON.parse(message.content)
        if (response.layout) {
          handleGeneratedLayout(response.layout)
          toast.success('AI layout generated successfully!')
        }
      } catch (error) {
        console.error('Error parsing AI response:', error)
        toast.error('Failed to parse AI response')
      }
    },
    onError: (error) => {
      setIsGenerating(false)
      toast.error('AI layout generation failed: ' + error.message)
    }
  })

  // Predefined layout templates
  const layoutTemplates: LayoutTemplate[] = [
    {
      id: 'hero-features-cta',
      name: 'Hero + Features + CTA',
      description: 'Classic landing page layout with hero section, feature highlights, and call-to-action',
      category: 'Landing Page',
      preview: '/templates/hero-features-cta.jpg',
      blocks: ['hero', 'features', 'cta'],
      gridLayout: [
        { i: 'hero', x: 0, y: 0, w: 12, h: 4 },
        { i: 'features', x: 0, y: 4, w: 12, h: 3 },
        { i: 'cta', x: 0, y: 7, w: 12, h: 2 }
      ]
    },
    {
      id: 'product-showcase',
      name: 'Product Showcase',
      description: 'E-commerce layout with product gallery, details, and related items',
      category: 'E-commerce',
      preview: '/templates/product-showcase.jpg',
      blocks: ['product-gallery', 'product-details', 'related-products', 'reviews'],
      gridLayout: [
        { i: 'product-gallery', x: 0, y: 0, w: 6, h: 4 },
        { i: 'product-details', x: 6, y: 0, w: 6, h: 4 },
        { i: 'related-products', x: 0, y: 4, w: 12, h: 3 },
        { i: 'reviews', x: 0, y: 7, w: 12, h: 3 }
      ]
    },
    {
      id: 'blog-layout',
      name: 'Blog Layout',
      description: 'Content-focused layout with article content, sidebar, and related posts',
      category: 'Blog',
      preview: '/templates/blog-layout.jpg',
      blocks: ['article-header', 'article-content', 'sidebar', 'related-posts'],
      gridLayout: [
        { i: 'article-header', x: 0, y: 0, w: 12, h: 2 },
        { i: 'article-content', x: 0, y: 2, w: 8, h: 6 },
        { i: 'sidebar', x: 8, y: 2, w: 4, h: 6 },
        { i: 'related-posts', x: 0, y: 8, w: 12, h: 3 }
      ]
    },
    {
      id: 'dashboard-layout',
      name: 'Dashboard Layout',
      description: 'Analytics dashboard with metrics, charts, and data tables',
      category: 'Dashboard',
      preview: '/templates/dashboard-layout.jpg',
      blocks: ['metrics-overview', 'charts', 'data-table', 'recent-activity'],
      gridLayout: [
        { i: 'metrics-overview', x: 0, y: 0, w: 12, h: 2 },
        { i: 'charts', x: 0, y: 2, w: 8, h: 4 },
        { i: 'recent-activity', x: 8, y: 2, w: 4, h: 4 },
        { i: 'data-table', x: 0, y: 6, w: 12, h: 4 }
      ]
    }
  ]

  // Available block types
  const blockTypes = [
    { id: 'hero', label: 'Hero Section', icon: Target },
    { id: 'features', label: 'Features', icon: Zap },
    { id: 'cta', label: 'Call to Action', icon: TrendingUp },
    { id: 'gallery', label: 'Image Gallery', icon: Image },
    { id: 'testimonials', label: 'Testimonials', icon: Users },
    { id: 'pricing', label: 'Pricing Table', icon: ShoppingCart },
    { id: 'contact', label: 'Contact Form', icon: Users },
    { id: 'text', label: 'Text Content', icon: Type },
    { id: 'video', label: 'Video Player', icon: Monitor },
    { id: 'social', label: 'Social Media', icon: Users }
  ]

  // Handle AI prompt generation
  const handleAIGeneration = useCallback(async (customPrompt?: string) => {
    setIsGenerating(true)
    
    const generationPrompt = customPrompt || prompt || `Create a ${pageType} page layout for a ${industry} business with ${style} design style. Include ${includeBlocks.join(', ')} sections. Target device: ${targetDevice}. Layout density: ${layoutDensity}. Color scheme: ${colorScheme}.`
    
    try {
      await handleSubmit({
        preventDefault: () => {},
        target: {
          prompt: { value: generationPrompt },
          pageType: { value: pageType },
          industry: { value: industry },
          style: { value: style },
          targetDevice: { value: targetDevice },
          includeBlocks: { value: includeBlocks },
          colorScheme: { value: colorScheme },
          layoutDensity: { value: layoutDensity }
        }
      } as any)
    } catch (error) {
      setIsGenerating(false)
      console.error('AI generation error:', error)
      toast.error('Failed to generate layout')
    }
  }, [prompt, pageType, industry, style, targetDevice, includeBlocks, colorScheme, layoutDensity, handleSubmit])

  // Handle generated layout
  const handleGeneratedLayout = useCallback((layout: any) => {
    if (layout.blocks && Array.isArray(layout.blocks)) {
      // Clear existing blocks
      clearBlocks()
      
      // Add new blocks with grid layout
      layout.blocks.forEach((block: any, index: number) => {
        const gridLayout = layout.gridLayout?.[index] || {
          x: (index % 4) * 3,
          y: Math.floor(index / 4) * 2,
          w: 3,
          h: 2
        }
        
        addBlock({
          id: block.id || `block-${Date.now()}-${index}`,
          type: block.type,
          content: block.content || {},
          config: block.config || {},
          gridLayout
        })
      })
      
      onLayoutGenerated?.(layout)
    }
  }, [clearBlocks, addBlock, onLayoutGenerated])

  // Apply template
  const handleApplyTemplate = useCallback((template: LayoutTemplate) => {
    clearBlocks()
    
    template.blocks.forEach((blockType, index) => {
      const gridLayout = template.gridLayout[index] || {
        x: (index % 4) * 3,
        y: Math.floor(index / 4) * 2,
        w: 3,
        h: 2
      }
      
      addBlock({
        id: `${blockType}-${Date.now()}-${index}`,
        type: blockType,
        content: {},
        config: {},
        gridLayout
      })
    })
    
    toast.success(`Applied ${template.name} template`)
  }, [clearBlocks, addBlock])

  // Toggle block inclusion
  const toggleBlockInclusion = useCallback((blockId: string) => {
    setIncludeBlocks(prev => 
      prev.includes(blockId) 
        ? prev.filter(id => id !== blockId)
        : [...prev, blockId]
    )
  }, [])

  return (
    <div className={cn('ai-layout-generator', className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Wand2 className="h-5 w-5" />
            <span>AI Layout Generator</span>
            <Badge variant="secondary" className="ml-auto">
              <Sparkles className="h-3 w-3 mr-1" />
              AI Powered
            </Badge>
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab as any}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="prompt">AI Prompt</TabsTrigger>
              <TabsTrigger value="templates">Templates</TabsTrigger>
              <TabsTrigger value="customize">Customize</TabsTrigger>
            </TabsList>

            {/* AI Prompt Tab */}
            <TabsContent value="prompt" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="prompt">Describe your layout</Label>
                  <Textarea
                    id="prompt"
                    placeholder="Create a modern landing page for a kids clothing store with hero section, product features, and call-to-action..."
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="pageType">Page Type</Label>
                    <Select value={pageType} onValueChange={setPageType}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="landing">Landing Page</SelectItem>
                        <SelectItem value="product">Product Page</SelectItem>
                        <SelectItem value="category">Category Page</SelectItem>
                        <SelectItem value="blog">Blog Post</SelectItem>
                        <SelectItem value="about">About Page</SelectItem>
                        <SelectItem value="contact">Contact Page</SelectItem>
                        <SelectItem value="dashboard">Dashboard</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="style">Design Style</Label>
                    <Select value={style} onValueChange={setStyle}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="modern">Modern</SelectItem>
                        <SelectItem value="minimal">Minimal</SelectItem>
                        <SelectItem value="bold">Bold</SelectItem>
                        <SelectItem value="elegant">Elegant</SelectItem>
                        <SelectItem value="playful">Playful</SelectItem>
                        <SelectItem value="professional">Professional</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Button 
                  onClick={() => handleAIGeneration()}
                  disabled={isGenerating || isLoading}
                  className="w-full"
                >
                  {isGenerating || isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating Layout...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Generate Layout with AI
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>

            {/* Templates Tab */}
            <TabsContent value="templates" className="space-y-4">
              <ScrollArea className="h-[400px]">
                <div className="grid grid-cols-1 gap-4">
                  {layoutTemplates.map((template) => (
                    <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h4 className="font-medium">{template.name}</h4>
                              <Badge variant="outline">{template.category}</Badge>
                            </div>
                            <p className="text-sm text-muted-foreground mb-3">
                              {template.description}
                            </p>
                            <div className="flex flex-wrap gap-1">
                              {template.blocks.map((block) => (
                                <Badge key={block} variant="secondary" className="text-xs">
                                  {block}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <Button
                            onClick={() => handleApplyTemplate(template)}
                            size="sm"
                            className="ml-4"
                          >
                            <Download className="h-3 w-3 mr-1" />
                            Apply
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Customize Tab */}
            <TabsContent value="customize" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label>Include Blocks</Label>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    {blockTypes.map((blockType) => (
                      <div key={blockType.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={blockType.id}
                          checked={includeBlocks.includes(blockType.id)}
                          onCheckedChange={() => toggleBlockInclusion(blockType.id)}
                        />
                        <Label htmlFor={blockType.id} className="text-sm">
                          {blockType.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="targetDevice">Target Device</Label>
                    <Select value={targetDevice} onValueChange={setTargetDevice}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mobile">Mobile</SelectItem>
                        <SelectItem value="tablet">Tablet</SelectItem>
                        <SelectItem value="desktop">Desktop</SelectItem>
                        <SelectItem value="large">Large Screen</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="layoutDensity">Layout Density</Label>
                    <Select value={layoutDensity} onValueChange={setLayoutDensity}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="compact">Compact</SelectItem>
                        <SelectItem value="balanced">Balanced</SelectItem>
                        <SelectItem value="spacious">Spacious</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Button 
                  onClick={() => handleAIGeneration()}
                  disabled={isGenerating || isLoading}
                  className="w-full"
                >
                  {isGenerating || isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating Custom Layout...
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-4 w-4 mr-2" />
                      Generate Custom Layout
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>
          </Tabs>

          {/* Current Layout Info */}
          {state.page.blocks.length > 0 && (
            <div className="mt-6 p-4 bg-muted rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Current Layout</h4>
                  <p className="text-sm text-muted-foreground">
                    {state.page.blocks.length} blocks in grid layout
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">
                    <Eye className="h-3 w-3 mr-1" />
                    Preview
                  </Button>
                  <Button variant="outline" size="sm">
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Reset
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
