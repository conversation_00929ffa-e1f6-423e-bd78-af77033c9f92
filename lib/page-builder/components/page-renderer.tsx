'use client'

import React from 'react'
import { PageData } from '../types'
import { BlockRenderer } from './block-renderer'
import { blockAnimationStyles } from '../blocks/base-block'
import { PageErrorBoundary, AsyncErrorBoundary } from './page-error-boundary'
import { optimizePageData, prefetchPageResources } from '../utils/performance'

interface PageRendererProps {
  page: PageData
  className?: string
}

export function PageRenderer({ page, className }: PageRendererProps) {
  // Sort blocks by position
  const sortedBlocks = [...page.blocks].sort((a, b) => a.position - b.position)

  // Filter visible blocks
  const visibleBlocks = sortedBlocks.filter(block => block.isVisible)

  return (
    <PageErrorBoundary>
      <AsyncErrorBoundary>
        <div className={className}>
          {/* Include block animation styles */}
          <style dangerouslySetInnerHTML={{ __html: blockAnimationStyles }} />

          {/* Custom page CSS */}
          {page.settings?.customCss && (
            <style dangerouslySetInnerHTML={{ __html: page.settings.customCss }} />
          )}

          {/* Render blocks without main wrapper to avoid padding constraints */}
          {visibleBlocks.length === 0 ? (
            <div className="min-h-screen flex items-center justify-center">
              <div className="text-center">
                <h1 className="text-2xl font-light text-gray-600 mb-2">
                  {page.title}
                </h1>
                <p className="text-gray-500">
                  This page has no content to display.
                </p>
              </div>
            </div>
          ) : (
            visibleBlocks.map((block, index) => (
              <BlockRenderer
                key={block.id}
                block={block}
                index={index}
                isEditing={false}
              />
            ))
          )}

          {/* Custom page JavaScript */}
          {page.settings?.customJs && (
            <script dangerouslySetInnerHTML={{ __html: page.settings.customJs }} />
          )}
        </div>
      </AsyncErrorBoundary>
    </PageErrorBoundary>
  )
}

// Static page renderer for server-side rendering
export function StaticPageRenderer({ page }: PageRendererProps) {
  // Optimize page data for performance
  const optimizedPage = React.useMemo(() => optimizePageData(page), [page])

  const sortedBlocks = [...optimizedPage.blocks].sort((a, b) => a.position - b.position)
  const visibleBlocks = sortedBlocks.filter(block => block.isVisible)

  // Prefetch critical resources
  React.useEffect(() => {
    prefetchPageResources(optimizedPage)
  }, [optimizedPage])

  return (
    <PageErrorBoundary>
      <AsyncErrorBoundary>
        <div>
          {/* Include block animation styles */}
          <style dangerouslySetInnerHTML={{ __html: blockAnimationStyles }} />

          {/* Custom page CSS */}
          {optimizedPage.settings?.customCss && (
            <style dangerouslySetInnerHTML={{ __html: optimizedPage.settings.customCss }} />
          )}

          {/* Render blocks without main wrapper to avoid padding constraints */}
          {visibleBlocks.length === 0 ? (
            <div className="min-h-screen flex items-center justify-center">
              <div className="text-center">
                <h1 className="text-2xl font-light text-gray-600 mb-2">
                  {optimizedPage.title}
                </h1>
                <p className="text-gray-500">
                  This page has no content to display.
                </p>
              </div>
            </div>
          ) : (
            visibleBlocks.map((block, index) => (
              <BlockRenderer
                key={block.id}
                block={block}
                index={index}
                isEditing={false}
              />
            ))
          )}

          {/* Custom page JavaScript */}
          {optimizedPage.settings?.customJs && (
            <script dangerouslySetInnerHTML={{ __html: optimizedPage.settings.customJs }} />
          )}
        </div>
      </AsyncErrorBoundary>
    </PageErrorBoundary>
  )
}

// Page renderer with layout wrapper
interface PageRendererWithLayoutProps extends PageRendererProps {
  showHeader?: boolean
  showFooter?: boolean
}

export function PageRendererWithLayout({ 
  page, 
  showHeader = true, 
  showFooter = true,
  className 
}: PageRendererWithLayoutProps) {
  return (
    <div className={className}>
      {showHeader && (
        <header className="bg-white border-b">
          <div className="container mx-auto px-4 py-4">
            <h1 className="text-xl font-semibold">{page.title}</h1>
          </div>
        </header>
      )}
      
      <PageRenderer page={page} />
      
      {showFooter && (
        <footer className="bg-gray-50 border-t mt-auto">
          <div className="container mx-auto px-4 py-8 text-center text-sm text-gray-600">
            <p>Built with Page Builder</p>
          </div>
        </footer>
      )}
    </div>
  )
}

// Page renderer for preview mode
export function PreviewPageRenderer({ page }: PageRendererProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-blue-600 text-white px-4 py-2 text-sm text-center">
        Preview Mode - {page.title}
      </div>
      <PageRenderer page={page} />
    </div>
  )
}

// Export default
export default PageRenderer
