'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Monitor, Tablet, Smartphone, Eye, EyeOff } from 'lucide-react'

interface ResponsiveVisibilityControlsProps {
  responsive: any
  onUpdate: (updates: any) => void
}

export function ResponsiveVisibilityControls({ responsive, onUpdate }: ResponsiveVisibilityControlsProps) {
  const getVisibilityStatus = () => {
    const hidden = []
    if (responsive.hideOnMobile) hidden.push('Mobile')
    if (responsive.hideOnTablet) hidden.push('Tablet')
    if (responsive.hideOnDesktop) hidden.push('Desktop')
    return hidden
  }

  const hiddenDevices = getVisibilityStatus()

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-sm">Visibility Controls</CardTitle>
            <CardDescription className="text-xs">
              Control when this block is visible on different devices
            </CardDescription>
          </div>
          {hiddenDevices.length > 0 && (
            <Badge variant="outline" className="text-xs">
              <EyeOff className="h-3 w-3 mr-1" />
              Hidden on {hiddenDevices.length} device{hiddenDevices.length > 1 ? 's' : ''}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Mobile Visibility */}
        <div className="flex items-center justify-between p-3 border rounded-lg">
          <div className="flex items-center space-x-3">
            <Smartphone className="h-4 w-4 text-muted-foreground" />
            <div>
              <Label className="text-xs font-medium">Mobile (≤ 768px)</Label>
              <p className="text-xs text-muted-foreground">Phones and small devices</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {responsive.hideOnMobile ? (
              <EyeOff className="h-4 w-4 text-muted-foreground" />
            ) : (
              <Eye className="h-4 w-4 text-green-600" />
            )}
            <Switch
              checked={!responsive.hideOnMobile}
              onCheckedChange={(checked) => onUpdate({ hideOnMobile: !checked })}
            />
          </div>
        </div>

        {/* Tablet Visibility */}
        <div className="flex items-center justify-between p-3 border rounded-lg">
          <div className="flex items-center space-x-3">
            <Tablet className="h-4 w-4 text-muted-foreground" />
            <div>
              <Label className="text-xs font-medium">Tablet (769px - 1024px)</Label>
              <p className="text-xs text-muted-foreground">Tablets and medium devices</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {responsive.hideOnTablet ? (
              <EyeOff className="h-4 w-4 text-muted-foreground" />
            ) : (
              <Eye className="h-4 w-4 text-green-600" />
            )}
            <Switch
              checked={!responsive.hideOnTablet}
              onCheckedChange={(checked) => onUpdate({ hideOnTablet: !checked })}
            />
          </div>
        </div>

        {/* Desktop Visibility */}
        <div className="flex items-center justify-between p-3 border rounded-lg">
          <div className="flex items-center space-x-3">
            <Monitor className="h-4 w-4 text-muted-foreground" />
            <div>
              <Label className="text-xs font-medium">Desktop (≥ 1025px)</Label>
              <p className="text-xs text-muted-foreground">Laptops and large screens</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {responsive.hideOnDesktop ? (
              <EyeOff className="h-4 w-4 text-muted-foreground" />
            ) : (
              <Eye className="h-4 w-4 text-green-600" />
            )}
            <Switch
              checked={!responsive.hideOnDesktop}
              onCheckedChange={(checked) => onUpdate({ hideOnDesktop: !checked })}
            />
          </div>
        </div>

        {/* Summary */}
        {hiddenDevices.length > 0 && (
          <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <EyeOff className="h-4 w-4 text-orange-600" />
              <span className="text-xs font-medium text-orange-800">
                This block is hidden on: {hiddenDevices.join(', ')}
              </span>
            </div>
            <p className="text-xs text-orange-700 mt-1">
              Users on these devices won't see this block.
            </p>
          </div>
        )}

        {hiddenDevices.length === 0 && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <Eye className="h-4 w-4 text-green-600" />
              <span className="text-xs font-medium text-green-800">
                This block is visible on all devices
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
