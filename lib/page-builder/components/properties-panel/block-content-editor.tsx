'use client'

import React from 'react'
import { HeroBlockConfig } from '../../blocks/hero-block'
import { ProductGridBlockConfig } from '../../blocks/product-grid-block'
import { CartWidgetBlockConfig } from '../../blocks/cart-widget-block'
import { ProductShowcaseBlockConfig } from '../../blocks/product-showcase-block'
import { LayoutContainerBlockConfig } from '../../blocks/layout-container-block'
import { CodeBlockConfig } from '../../blocks/code-block'
import { DynamicBlockEditor } from './dynamic-block-editor'
import { blockRegistry } from '../../blocks/registry'

interface BlockContentEditorProps {
  block: any
  blockType: any
  onUpdate: (updates: any) => void
}

export function BlockContentEditor({ block, blockType, onUpdate }: BlockContentEditorProps) {
  // Get block definition from registry
  const blockDefinition = blockRegistry.getBlockType(block.type)

  // Check if block has a custom schema for dynamic rendering
  if (blockDefinition?.configSchema) {
    return (
      <DynamicBlockEditor
        block={block}
        blockType={blockDefinition}
        schema={blockDefinition.configSchema}
        onUpdate={onUpdate}
      />
    )
  }

  // Fallback to specific block editors for legacy blocks
  switch (block.type) {
    case 'hero':
      return (
        <HeroBlockConfig
          config={block.configuration}
          onChange={onUpdate}
        />
      )

    case 'product-grid':
      return (
        <ProductGridBlockConfig
          config={block.configuration}
          onChange={onUpdate}
        />
      )

    case 'cart-widget':
      return (
        <CartWidgetBlockConfig
          config={block.configuration}
          onChange={onUpdate}
        />
      )

    case 'product-showcase':
      return (
        <ProductShowcaseBlockConfig
          config={block.configuration}
          onChange={onUpdate}
        />
      )

    case 'layout-container':
      return (
        <LayoutContainerBlockConfig
          config={block.configuration}
          onChange={onUpdate}
        />
      )

    case 'code':
      return (
        <CodeBlockConfig
          config={block.configuration}
          onChange={onUpdate}
        />
      )

    // Use dynamic editor for basic blocks
    case 'text':
    case 'image':
    case 'button':
    case 'spacer':
      return (
        <DynamicBlockEditor
          block={block}
          blockType={blockDefinition}
          schema={getBasicBlockSchema(block.type)}
          onUpdate={onUpdate}
        />
      )

    default:
      // Use dynamic editor with auto-generated schema
      return (
        <DynamicBlockEditor
          block={block}
          blockType={blockDefinition}
          schema={generateSchemaFromConfig(block.configuration)}
          onUpdate={onUpdate}
        />
      )
  }
}

// Helper function to get schema for basic blocks
function getBasicBlockSchema(blockType: string) {
  const schemas = {
    text: {
      type: 'object',
      properties: {
        content: {
          type: 'string',
          title: 'Text Content',
          description: 'The text content to display',
          'x-component': 'textarea',
          'x-component-props': { rows: 4 }
        },
        tag: {
          type: 'string',
          title: 'HTML Tag',
          description: 'The HTML tag to use for the text',
          enum: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'span', 'div'],
          default: 'p'
        }
      }
    },
    image: {
      type: 'object',
      properties: {
        src: {
          type: 'string',
          title: 'Image URL',
          description: 'The URL of the image',
          'x-component': 'image-upload'
        },
        alt: {
          type: 'string',
          title: 'Alt Text',
          description: 'Alternative text for accessibility'
        },
        width: {
          type: 'string',
          title: 'Width',
          description: 'Image width (e.g., 100%, 300px)',
          default: 'auto'
        },
        height: {
          type: 'string',
          title: 'Height',
          description: 'Image height (e.g., auto, 200px)',
          default: 'auto'
        },
        objectFit: {
          type: 'string',
          title: 'Object Fit',
          enum: ['cover', 'contain', 'fill', 'none', 'scale-down'],
          default: 'cover'
        },
        lazy: {
          type: 'boolean',
          title: 'Lazy Loading',
          description: 'Enable lazy loading for better performance',
          default: true
        }
      }
    },
    button: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          title: 'Button Text',
          description: 'The text displayed on the button'
        },
        href: {
          type: 'string',
          title: 'Link URL',
          description: 'The URL the button links to'
        },
        variant: {
          type: 'string',
          title: 'Button Style',
          enum: ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'],
          default: 'default'
        },
        size: {
          type: 'string',
          title: 'Button Size',
          enum: ['default', 'sm', 'lg', 'icon'],
          default: 'default'
        },
        fullWidth: {
          type: 'boolean',
          title: 'Full Width',
          description: 'Make button span full width',
          default: false
        },
        openInNewTab: {
          type: 'boolean',
          title: 'Open in New Tab',
          description: 'Open link in a new tab',
          default: false
        }
      }
    },
    spacer: {
      type: 'object',
      properties: {
        height: {
          type: 'string',
          title: 'Height',
          description: 'Spacer height (e.g., 50px, 2rem, 10vh)',
          default: '50px'
        },
        backgroundColor: {
          type: 'string',
          title: 'Background Color',
          description: 'Background color for the spacer',
          'x-component': 'color-picker',
          default: 'transparent'
        }
      }
    }
  }

  return schemas[blockType as keyof typeof schemas] || null
}

// Helper function to generate schema from existing configuration
function generateSchemaFromConfig(config: any) {
  if (!config || typeof config !== 'object') {
    return {
      type: 'object',
      properties: {
        configuration: {
          type: 'string',
          title: 'Configuration (JSON)',
          'x-component': 'json-editor'
        }
      }
    }
  }

  const properties: any = {}

  Object.keys(config).forEach(key => {
    const value = config[key]
    const type = typeof value

    if (type === 'string') {
      properties[key] = {
        type: 'string',
        title: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
        default: value
      }
    } else if (type === 'number') {
      properties[key] = {
        type: 'number',
        title: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
        default: value
      }
    } else if (type === 'boolean') {
      properties[key] = {
        type: 'boolean',
        title: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
        default: value
      }
    } else if (Array.isArray(value)) {
      properties[key] = {
        type: 'array',
        title: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
        items: { type: 'string' },
        'x-component': 'array-field'
      }
    } else if (type === 'object' && value !== null) {
      properties[key] = {
        type: 'object',
        title: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
        'x-component': 'object-field'
      }
    }
  })

  return {
    type: 'object',
    properties
  }
}


