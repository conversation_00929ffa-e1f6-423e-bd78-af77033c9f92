'use client'

import { FieldConfig, FieldValue } from './types'

// Get default value for a field based on its type and configuration
export function getFieldDefaultValue(config: FieldConfig): FieldValue {
  // Return explicit default value if provided
  if (config.defaultValue !== undefined) {
    return config.defaultValue
  }

  // Return type-specific default values
  switch (config.type) {
    case 'text':
    case 'textarea':
    case 'richtext':
    case 'email':
    case 'url':
    case 'password':
      return ''

    case 'number':
    case 'range':
      return 0

    case 'boolean':
      return false

    case 'color':
      return '#000000'

    case 'date':
      return new Date().toISOString().split('T')[0]

    case 'time':
      return '00:00'

    case 'datetime':
      return new Date().toISOString()

    case 'select':
      if (config.options && config.options.length > 0) {
        return config.options[0].value
      }
      return null

    case 'multi-select':
      return []

    case 'file':
    case 'image':
      return null

    case 'array':
    case 'repeater':
      return []

    case 'object':
      return {}

    case 'spacing':
      return {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
        unit: 'px'
      }

    case 'border':
      return {
        width: 0,
        style: 'solid',
        color: '#000000'
      }

    case 'shadow':
      return {
        x: 0,
        y: 0,
        blur: 0,
        spread: 0,
        color: '#000000',
        inset: false
      }

    case 'gradient':
      return {
        type: 'linear',
        angle: 0,
        stops: [
          { color: '#000000', position: 0 },
          { color: '#ffffff', position: 100 }
        ]
      }

    case 'animation':
      return {
        type: 'none',
        duration: 300,
        delay: 0,
        easing: 'ease',
        iteration: 1,
        direction: 'normal',
        fill: 'none'
      }

    case 'link':
      return {
        url: '',
        text: '',
        target: '_self',
        rel: ''
      }

    case 'font':
      return {
        family: 'inherit',
        weight: 'normal',
        style: 'normal',
        size: '16px',
        lineHeight: '1.5'
      }

    case 'icon':
      return {
        name: '',
        size: 24,
        color: '#000000'
      }

    case 'media':
      return {
        type: 'image',
        url: '',
        alt: '',
        caption: ''
      }

    default:
      return null
  }
}

// Get default values for multiple fields
export function getFieldsDefaultValues(configs: FieldConfig[]): Record<string, FieldValue> {
  const defaults: Record<string, FieldValue> = {}

  configs.forEach(config => {
    defaults[config.id] = getFieldDefaultValue(config)
  })

  return defaults
}

// Merge default values with existing values
export function mergeWithDefaults(
  configs: FieldConfig[],
  values: Record<string, FieldValue>
): Record<string, FieldValue> {
  const defaults = getFieldsDefaultValues(configs)
  
  return {
    ...defaults,
    ...values
  }
}

// Common default configurations for field types
export const fieldDefaults = {
  // Text field defaults
  text: {
    placeholder: 'Enter text...',
    maxLength: 255
  },

  // Number field defaults
  number: {
    min: 0,
    max: 999999,
    step: 1
  },

  // Range field defaults
  range: {
    min: 0,
    max: 100,
    step: 1,
    showValue: true
  },

  // Color field defaults
  color: {
    format: 'hex' as const,
    alpha: false,
    presets: [
      '#000000', '#ffffff', '#ff0000', '#00ff00', '#0000ff',
      '#ffff00', '#ff00ff', '#00ffff', '#808080', '#ffa500'
    ]
  },

  // Select field defaults
  select: {
    searchable: false,
    clearable: false,
    placeholder: 'Select an option...'
  },

  // Multi-select field defaults
  multiSelect: {
    searchable: true,
    clearable: true,
    placeholder: 'Select options...'
  },

  // Textarea field defaults
  textarea: {
    rows: 4,
    placeholder: 'Enter text...',
    maxLength: 1000
  },

  // Rich text field defaults
  richText: {
    toolbar: [
      'bold', 'italic', 'underline', 'strikethrough',
      'link', 'bulletList', 'orderedList',
      'blockquote', 'codeBlock'
    ],
    height: 200
  },

  // Code field defaults
  code: {
    language: 'javascript',
    theme: 'vs-dark',
    lineNumbers: true,
    wordWrap: true,
    height: 200
  },

  // Image field defaults
  image: {
    accept: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    maxSize: 5 * 1024 * 1024, // 5MB
    preview: true
  },

  // File field defaults
  file: {
    maxSize: 10 * 1024 * 1024, // 10MB
    preview: false
  },

  // Date field defaults
  date: {
    format: 'YYYY-MM-DD'
  },

  // Time field defaults
  time: {
    format: '24h' as const,
    step: 15 // 15 minutes
  },

  // DateTime field defaults
  dateTime: {
    dateFormat: 'YYYY-MM-DD',
    timeFormat: '24h' as const
  },

  // Spacing field defaults
  spacing: {
    sides: ['top', 'right', 'bottom', 'left'] as const,
    linked: false,
    unit: 'px' as const,
    min: 0,
    max: 200
  },

  // Border field defaults
  border: {
    sides: ['top', 'right', 'bottom', 'left'] as const,
    properties: ['width', 'style', 'color'] as const
  },

  // Animation field defaults
  animation: {
    properties: [
      'duration', 'delay', 'easing', 'iteration', 'direction', 'fill'
    ] as const,
    presets: [
      { label: 'Fade In', value: { type: 'fadeIn', duration: 300 } },
      { label: 'Slide Up', value: { type: 'slideUp', duration: 400 } },
      { label: 'Scale', value: { type: 'scale', duration: 200 } },
      { label: 'Bounce', value: { type: 'bounce', duration: 600 } }
    ]
  },

  // Link field defaults
  link: {
    allowExternal: true,
    allowInternal: true,
    allowEmail: true,
    allowPhone: true,
    allowAnchor: true
  },

  // Icon field defaults
  icon: {
    iconSet: 'lucide' as const,
    size: 24,
    searchable: true
  },

  // Font field defaults
  font: {
    families: [
      'Inter', 'P22 Underground', 'Arial', 'Helvetica', 'Times New Roman',
      'Georgia', 'Verdana', 'Courier New', 'monospace'
    ],
    weights: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
    styles: ['normal', 'italic', 'oblique'],
    preview: true
  },

  // Repeater field defaults
  repeater: {
    minItems: 0,
    maxItems: 10,
    addLabel: 'Add Item',
    removeLabel: 'Remove',
    sortable: true
  },

  // Object field defaults
  object: {
    layout: 'vertical' as const,
    columns: 1
  }
}

// Helper function to create field config with defaults
export function createFieldConfig(
  id: string,
  type: string,
  label: string,
  overrides?: Partial<FieldConfig>
): FieldConfig {
  const baseConfig: FieldConfig = {
    id,
    type,
    label,
    defaultValue: getFieldDefaultValue({ id, type, label } as FieldConfig)
  }

  // Apply type-specific defaults
  const typeDefaults = (fieldDefaults as any)[type] || {}

  return {
    ...baseConfig,
    ...typeDefaults,
    ...overrides
  }
}
