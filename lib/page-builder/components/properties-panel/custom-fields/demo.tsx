'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Palette, 
  Sliders, 
  Type, 
  Box, 
  Sparkles, 
  Settings,
  Eye,
  Code2
} from 'lucide-react'

import { CustomFieldRenderer } from './field-renderer'
import { FieldGroup } from './field-group'
import { FieldSection } from './field-section'
import { createFieldConfig } from './defaults'
import { commonFieldSchemas } from './schema'
import { FieldValue } from './types'

export function CustomFieldsDemo() {
  const [values, setValues] = useState<Record<string, FieldValue>>({
    textField: 'Hello World',
    numberField: 42,
    booleanField: true,
    colorField: '#3b82f6',
    rangeField: 75,
    selectField: 'option2',
    multiSelectField: ['option1', 'option3'],
    spacingField: {
      top: 16,
      right: 24,
      bottom: 16,
      left: 24,
      unit: 'px'
    },
    gradientField: {
      type: 'linear',
      angle: 45,
      stops: [
        { color: '#3b82f6', position: 0 },
        { color: '#8b5cf6', position: 100 }
      ]
    },
    fontField: {
      family: 'Inter',
      weight: '500',
      style: 'normal',
      size: '18px',
      lineHeight: '1.6',
      letterSpacing: '0px'
    },
    iconField: {
      name: 'Heart',
      size: 32,
      color: '#ec4899',
      strokeWidth: 2
    },
    borderField: {
      top: { width: 2, style: 'solid', color: '#3b82f6' },
      right: { width: 2, style: 'solid', color: '#3b82f6' },
      bottom: { width: 2, style: 'solid', color: '#3b82f6' },
      left: { width: 2, style: 'solid', color: '#3b82f6' },
      linked: true,
      radius: { topLeft: 8, topRight: 8, bottomRight: 8, bottomLeft: 8 }
    },
    shadowField: {
      layers: [
        {
          id: '1',
          x: 0,
          y: 4,
          blur: 8,
          spread: 0,
          color: '#00000040',
          inset: false,
          opacity: 0.25
        }
      ],
      enabled: true
    },
    animationField: {
      name: 'fadeIn',
      duration: 1000,
      delay: 0,
      iterationCount: 1,
      direction: 'normal',
      fillMode: 'forwards',
      timingFunction: 'ease',
      playState: 'paused'
    },
    linkField: {
      url: 'https://example.com',
      text: 'Visit Example',
      target: '_blank',
      rel: ['noopener'],
      title: 'Example website',
      type: 'url'
    },
    richTextField: {
      content: '# Welcome\n\nThis is **rich text** with *formatting*.',
      format: 'markdown',
      wordCount: 7,
      characterCount: 45
    },
    imageField: {
      url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
      alt: 'Sample landscape image',
      width: 400,
      height: 300
    },
    repeaterField: {
      items: [
        { id: '1', data: { title: 'Item 1', description: 'First item' }, collapsed: false },
        { id: '2', data: { title: 'Item 2', description: 'Second item' }, collapsed: true }
      ]
    },
    mediaField: {
      files: [
        {
          $id: 'media1',
          name: 'hero-image.jpg',
          mimeType: 'image/jpeg',
          sizeOriginal: 1024000,
          $createdAt: new Date().toISOString(),
          $updatedAt: new Date().toISOString(),
          url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=800',
          preview: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
          metadata: { width: 800, height: 600, alt: 'Hero landscape' }
        }
      ],
      multiple: false
    },
    fileField: {
      files: [
        {
          $id: 'file1',
          name: 'document.pdf',
          mimeType: 'application/pdf',
          sizeOriginal: 512000,
          $createdAt: new Date().toISOString(),
          $updatedAt: new Date().toISOString(),
          url: '#',
          metadata: { title: 'Sample Document' }
        }
      ],
      multiple: false
    },
    codeField: {
      code: 'function hello() {\n  console.log("Hello, World!");\n}',
      language: 'javascript',
      theme: 'light',
      lineNumbers: true,
      wordWrap: true,
      tabSize: 2
    },
    objectField: {
      properties: [
        { key: 'title', value: 'Sample Title', type: 'string', collapsed: false },
        { key: 'count', value: 42, type: 'number', collapsed: false },
        { key: 'enabled', value: true, type: 'boolean', collapsed: false }
      ],
      mode: 'visual'
    },
    conditionalField: {
      rules: [
        {
          id: '1',
          field: 'showAdvanced',
          operator: 'equals',
          value: 'true',
          logic: 'and'
        }
      ],
      showWhen: 'all',
      fields: [
        { id: 'advanced1', type: 'text', label: 'Advanced Setting' }
      ],
      isVisible: true
    },
    tabsField: {
      tabs: [
        {
          id: 'tab1',
          label: 'General',
          fields: [
            { id: 'name', type: 'text', label: 'Name' },
            { id: 'description', type: 'textarea', label: 'Description' }
          ],
          disabled: false
        },
        {
          id: 'tab2',
          label: 'Advanced',
          fields: [
            { id: 'settings', type: 'select', label: 'Settings' }
          ],
          disabled: false
        }
      ],
      activeTab: 'tab1',
      orientation: 'horizontal',
      variant: 'default'
    },
    accordionField: {
      items: [
        {
          id: 'item1',
          title: 'Basic Settings',
          content: 'Configure basic application settings here.',
          disabled: false,
          defaultOpen: true,
          fields: [
            { id: 'setting1', type: 'text', label: 'Setting 1' }
          ]
        },
        {
          id: 'item2',
          title: 'Advanced Options',
          content: 'Advanced configuration options for power users.',
          disabled: false,
          defaultOpen: false,
          fields: []
        }
      ],
      type: 'single',
      collapsible: true,
      variant: 'default',
      openItems: ['item1']
    }
  })

  const [validationErrors, setValidationErrors] = useState<Record<string, string[]>>({})

  const handleFieldChange = (fieldId: string, value: FieldValue) => {
    setValues(prev => ({ ...prev, [fieldId]: value }))
  }

  const handleValidation = (fieldId: string, isValid: boolean, message?: string) => {
    setValidationErrors(prev => ({
      ...prev,
      [fieldId]: isValid ? [] : [message || 'Invalid value']
    }))
  }

  // Demo field configurations
  const demoFields = [
    createFieldConfig('textField', 'text', 'Text Input', {
      description: 'A creative text input with validation and actions',
      placeholder: 'Enter some text...',
      validation: { required: true, minLength: 3, maxLength: 50 }
    }),
    
    createFieldConfig('numberField', 'number', 'Number Input', {
      description: 'Interactive number field with increment/decrement',
      min: 0,
      max: 100,
      step: 1,
      unit: 'px',
      quickValues: [0, 10, 20, 50, 100]
    }),
    
    createFieldConfig('booleanField', 'boolean', 'Boolean Toggle', {
      description: 'Multiple boolean input variants',
      variant: 'switch',
      trueLabel: 'Enabled',
      falseLabel: 'Disabled'
    }),
    
    createFieldConfig('colorField', 'color', 'Color Picker', {
      description: 'Advanced color picker with gradients and presets',
      format: 'hex',
      alpha: true,
      presets: ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981']
    }),
    
    createFieldConfig('rangeField', 'range', 'Range Slider', {
      description: 'Custom SVG range slider with visual feedback',
      min: 0,
      max: 100,
      step: 5,
      unit: '%',
      showValue: true,
      marks: [
        { value: 0, label: 'Min' },
        { value: 50, label: 'Mid' },
        { value: 100, label: 'Max' }
      ]
    }),
    
    createFieldConfig('selectField', 'select', 'Select Dropdown', {
      description: 'Searchable select with icons and descriptions',
      searchable: true,
      clearable: true,
      options: [
        { label: 'Option 1', value: 'option1', description: 'First option' },
        { label: 'Option 2', value: 'option2', description: 'Second option' },
        { label: 'Option 3', value: 'option3', description: 'Third option' }
      ]
    }),
    
    createFieldConfig('multiSelectField', 'multi-select', 'Multi-Select', {
      description: 'Visual multi-select with progress tracking',
      searchable: true,
      options: [
        { label: 'Feature A', value: 'option1', description: 'Enable feature A' },
        { label: 'Feature B', value: 'option2', description: 'Enable feature B' },
        { label: 'Feature C', value: 'option3', description: 'Enable feature C' },
        { label: 'Feature D', value: 'option4', description: 'Enable feature D' }
      ]
    }),
    
    createFieldConfig('spacingField', 'spacing', 'Spacing Control', {
      description: 'Visual spacing editor with live preview',
      sides: ['top', 'right', 'bottom', 'left'],
      linked: false,
      unit: 'px',
      min: 0,
      max: 100
    }),
    
    createFieldConfig('gradientField', 'gradient', 'Gradient Builder', {
      description: 'Interactive gradient builder with presets',
      presets: [
        {
          label: 'Ocean Blue',
          value: {
            type: 'linear',
            angle: 180,
            stops: [
              { color: '#667eea', position: 0 },
              { color: '#764ba2', position: 100 }
            ]
          }
        }
      ]
    }),

    createFieldConfig('fontField', 'font', 'Font Selector', {
      description: 'Advanced font picker with live preview and typography controls',
      families: [
        { name: 'Inter', category: 'Sans Serif', popular: true },
        { name: 'P22 Underground', category: 'Sans Serif', popular: true },
        { name: 'Playfair Display', category: 'Serif', popular: true },
        { name: 'Fira Code', category: 'Monospace', popular: true }
      ],
      preview: true
    }),

    createFieldConfig('iconField', 'icon', 'Icon Picker', {
      description: 'Searchable icon library with size and color controls',
      iconSet: 'lucide',
      size: 24,
      searchable: true
    }),

    createFieldConfig('borderField', 'border', 'Border Editor', {
      description: 'Advanced border editor with individual side controls and radius',
      linked: true,
      showRadius: true
    }),

    createFieldConfig('shadowField', 'shadow', 'Shadow Builder', {
      description: 'Multi-layer shadow editor with live preview',
      maxLayers: 5,
      presets: [
        { name: 'Subtle', value: { x: 0, y: 1, blur: 3, spread: 0, color: '#00000020' } },
        { name: 'Medium', value: { x: 0, y: 4, blur: 8, spread: 0, color: '#00000040' } },
        { name: 'Strong', value: { x: 0, y: 8, blur: 16, spread: 0, color: '#00000060' } }
      ]
    }),

    createFieldConfig('animationField', 'animation', 'Animation Controller', {
      description: 'CSS animation builder with presets and live preview',
      presets: ['fadeIn', 'slideIn', 'bounce', 'pulse'],
      showTimeline: true
    }),

    createFieldConfig('linkField', 'link', 'Link Builder', {
      description: 'Advanced link editor with validation and preview',
      types: ['url', 'email', 'phone', 'anchor'],
      validation: { required: false }
    }),

    createFieldConfig('richTextField', 'rich-text', 'Rich Text Editor', {
      description: 'Advanced text editor with formatting toolbar and live preview',
      formats: ['html', 'markdown', 'text'],
      showWordCount: true
    }),

    createFieldConfig('imageField', 'image', 'Image Upload', {
      description: 'Drag & drop image upload with preview and URL support',
      accept: ['image/*'],
      maxSize: '10MB'
    }),

    createFieldConfig('repeaterField', 'repeater', 'Repeater Field', {
      description: 'Dynamic list of items with drag & drop reordering',
      maxItems: 10,
      minItems: 0,
      fields: [
        { id: 'title', type: 'text', label: 'Title' },
        { id: 'description', type: 'textarea', label: 'Description' }
      ]
    }),

    createFieldConfig('mediaField', 'media', 'Media Library', {
      description: 'Appwrite-powered media library with upload and organization',
      multiple: false,
      accept: ['image/*', 'video/*'],
      maxFiles: 1
    }),

    createFieldConfig('fileField', 'file', 'File Manager', {
      description: 'File upload and management with Appwrite storage',
      multiple: false,
      accept: ['*/*'],
      maxFiles: 1
    })
  ]

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Advanced Custom Fields Demo
          </CardTitle>
          <CardDescription>
            Interactive showcase of creative field components with SVG visualizations and GUI-like interfaces
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="fields" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="fields" className="text-xs">
                <Settings className="h-3 w-3 mr-1" />
                Fields
              </TabsTrigger>
              <TabsTrigger value="groups" className="text-xs">
                <Box className="h-3 w-3 mr-1" />
                Groups
              </TabsTrigger>
              <TabsTrigger value="preview" className="text-xs">
                <Eye className="h-3 w-3 mr-1" />
                Preview
              </TabsTrigger>
              <TabsTrigger value="code" className="text-xs">
                <Code2 className="h-3 w-3 mr-1" />
                Code
              </TabsTrigger>
            </TabsList>

            <TabsContent value="fields" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {demoFields.map((field) => (
                  <Card key={field.id} className="p-4">
                    <CustomFieldRenderer
                      config={field}
                      value={values[field.id]}
                      onChange={(value) => handleFieldChange(field.id, value)}
                      onValidate={(isValid, message) => handleValidation(field.id, isValid, message)}
                      errors={validationErrors[field.id]}
                    />
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="groups" className="space-y-4">
              <FieldGroup
                config={{
                  id: 'demo-group',
                  label: 'Demo Field Group',
                  description: 'Organized field collection with collapsible interface',
                  fields: demoFields.map(f => f.id),
                  collapsible: true,
                  collapsed: false
                }}
                fields={demoFields}
                values={values}
                onChange={handleFieldChange}
                onValidate={handleValidation}
                errors={validationErrors}
              />
            </TabsContent>

            <TabsContent value="preview" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Current Values</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {Object.entries(values).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between text-xs">
                        <Badge variant="outline">{key}</Badge>
                        <code className="bg-muted px-2 py-1 rounded text-xs max-w-xs truncate">
                          {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                        </code>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Validation Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {Object.entries(validationErrors).map(([key, errors]) => (
                      <div key={key} className="flex items-center justify-between text-xs">
                        <Badge variant="outline">{key}</Badge>
                        <Badge variant={errors.length > 0 ? 'destructive' : 'default'}>
                          {errors.length > 0 ? 'Invalid' : 'Valid'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="code" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Usage Example</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-muted p-4 rounded-lg text-xs overflow-auto">
{`import { CustomFieldRenderer } from './custom-fields'
import { createFieldConfig } from './custom-fields/defaults'

const fieldConfig = createFieldConfig('myField', 'range', 'My Range Field', {
  min: 0,
  max: 100,
  step: 5,
  unit: '%',
  description: 'A custom range slider'
})

function MyComponent() {
  const [value, setValue] = useState(50)
  
  return (
    <CustomFieldRenderer
      config={fieldConfig}
      value={value}
      onChange={setValue}
    />
  )
}`}
                  </pre>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
