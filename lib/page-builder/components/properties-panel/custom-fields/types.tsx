'use client'

import { ReactNode } from 'react'

// Base field value types
export type FieldValue = 
  | string 
  | number 
  | boolean 
  | string[] 
  | number[] 
  | Record<string, any> 
  | null 
  | undefined

// Field validation configuration
export interface FieldValidation {
  required?: boolean
  min?: number
  max?: number
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: FieldValue) => string | null
  message?: string
}

// Base field configuration
export interface FieldConfig {
  id: string
  type: string
  label: string
  description?: string
  placeholder?: string
  defaultValue?: FieldValue
  validation?: FieldValidation
  disabled?: boolean
  hidden?: boolean
  className?: string
  
  // Field-specific options
  options?: Array<{ label: string; value: any; icon?: ReactNode }>
  multiple?: boolean
  searchable?: boolean
  clearable?: boolean
  
  // Conditional display
  showWhen?: {
    field: string
    operator: 'equals' | 'not-equals' | 'contains' | 'not-contains' | 'greater' | 'less'
    value: FieldValue
  }
  
  // Advanced options
  [key: string]: any
}

// Field schema for dynamic rendering
export interface FieldSchema {
  fields: FieldConfig[]
  groups?: FieldGroupConfig[]
  sections?: FieldSectionConfig[]
}

// Field group configuration
export interface FieldGroupConfig {
  id: string
  label: string
  description?: string
  fields: string[]
  collapsible?: boolean
  collapsed?: boolean
  className?: string
}

// Field section configuration
export interface FieldSectionConfig {
  id: string
  label: string
  description?: string
  icon?: ReactNode
  groups: string[]
  collapsible?: boolean
  collapsed?: boolean
  className?: string
}

// Custom field component props
export interface CustomFieldProps {
  config: FieldConfig
  value: FieldValue
  onChange: (value: FieldValue) => void
  onValidate?: (isValid: boolean, message?: string) => void
  errors?: string[]
  disabled?: boolean
  className?: string
}

// Specific field type configurations
export interface ColorFieldConfig extends FieldConfig {
  type: 'color'
  format?: 'hex' | 'rgb' | 'hsl'
  alpha?: boolean
  presets?: string[]
  gradient?: boolean
}

export interface RangeFieldConfig extends FieldConfig {
  type: 'range'
  min: number
  max: number
  step?: number
  unit?: string
  showValue?: boolean
  marks?: Array<{ value: number; label: string }>
}

export interface SelectFieldConfig extends FieldConfig {
  type: 'select'
  options: Array<{ label: string; value: any; icon?: ReactNode; description?: string }>
  multiple?: boolean
  searchable?: boolean
  clearable?: boolean
  creatable?: boolean
}

export interface RepeaterFieldConfig extends FieldConfig {
  type: 'repeater'
  itemSchema: FieldConfig[]
  minItems?: number
  maxItems?: number
  addLabel?: string
  removeLabel?: string
  sortable?: boolean
}

export interface ObjectFieldConfig extends FieldConfig {
  type: 'object'
  properties: Record<string, FieldConfig>
  layout?: 'vertical' | 'horizontal' | 'grid'
  columns?: number
}

export interface ConditionalFieldConfig extends FieldConfig {
  type: 'conditional'
  conditions: Array<{
    field: string
    operator: 'equals' | 'not-equals' | 'contains' | 'not-contains' | 'greater' | 'less'
    value: FieldValue
    fields: FieldConfig[]
  }>
  defaultFields?: FieldConfig[]
}

// Media field types
export interface ImageFieldConfig extends FieldConfig {
  type: 'image'
  accept?: string[]
  maxSize?: number
  dimensions?: { width?: number; height?: number; aspectRatio?: string }
  crop?: boolean
  preview?: boolean
}

export interface FileFieldConfig extends FieldConfig {
  type: 'file'
  accept?: string[]
  maxSize?: number
  multiple?: boolean
  preview?: boolean
}

// Date/Time field types
export interface DateFieldConfig extends FieldConfig {
  type: 'date'
  format?: string
  minDate?: Date
  maxDate?: Date
  disabledDates?: Date[]
}

export interface TimeFieldConfig extends FieldConfig {
  type: 'time'
  format?: '12h' | '24h'
  step?: number
}

export interface DateTimeFieldConfig extends FieldConfig {
  type: 'datetime'
  dateFormat?: string
  timeFormat?: '12h' | '24h'
  minDateTime?: Date
  maxDateTime?: Date
}

// Specialized field types
export interface SpacingFieldConfig extends FieldConfig {
  type: 'spacing'
  sides?: ('top' | 'right' | 'bottom' | 'left')[]
  linked?: boolean
  unit?: 'px' | 'rem' | 'em' | '%'
  min?: number
  max?: number
}

export interface BorderFieldConfig extends FieldConfig {
  type: 'border'
  sides?: ('top' | 'right' | 'bottom' | 'left')[]
  properties?: ('width' | 'style' | 'color')[]
}

export interface ShadowFieldConfig extends FieldConfig {
  type: 'shadow'
  multiple?: boolean
  presets?: Array<{ label: string; value: string }>
}

export interface GradientFieldConfig extends FieldConfig {
  type: 'gradient'
  direction?: boolean
  stops?: boolean
  presets?: Array<{ label: string; value: string }>
}

export interface AnimationFieldConfig extends FieldConfig {
  type: 'animation'
  properties?: ('duration' | 'delay' | 'easing' | 'iteration' | 'direction' | 'fill')[]
  presets?: Array<{ label: string; value: Record<string, any> }>
}

export interface LinkFieldConfig extends FieldConfig {
  type: 'link'
  allowExternal?: boolean
  allowInternal?: boolean
  allowEmail?: boolean
  allowPhone?: boolean
  allowAnchor?: boolean
}

export interface IconFieldConfig extends FieldConfig {
  type: 'icon'
  iconSet?: 'lucide' | 'heroicons' | 'feather' | 'custom'
  size?: number
  searchable?: boolean
  categories?: string[]
}

export interface FontFieldConfig extends FieldConfig {
  type: 'font'
  families?: string[]
  weights?: string[]
  styles?: string[]
  preview?: boolean
}

// Rich text field configuration
export interface RichTextFieldConfig extends FieldConfig {
  type: 'richtext'
  toolbar?: string[]
  plugins?: string[]
  formats?: string[]
  height?: number
  maxLength?: number
}

// Code field configuration
export interface CodeFieldConfig extends FieldConfig {
  type: 'code'
  language?: string
  theme?: string
  lineNumbers?: boolean
  wordWrap?: boolean
  height?: number
  readOnly?: boolean
}

// Tabs field configuration
export interface TabsFieldConfig extends FieldConfig {
  type: 'tabs'
  tabs: Array<{
    id: string
    label: string
    icon?: ReactNode
    fields: FieldConfig[]
  }>
  defaultTab?: string
}

// Accordion field configuration
export interface AccordionFieldConfig extends FieldConfig {
  type: 'accordion'
  sections: Array<{
    id: string
    label: string
    icon?: ReactNode
    fields: FieldConfig[]
    collapsed?: boolean
  }>
  multiple?: boolean
}
