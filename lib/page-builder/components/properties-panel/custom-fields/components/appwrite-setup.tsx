'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Database, 
  HardDrive, 
  Settings,
  RefreshCw,
  ExternalLink,
  AlertTriangle,
  Info
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface SetupStatus {
  setup: boolean
  bucket?: {
    $id: string
    name: string
    enabled: boolean
    maximumFileSize: number
    allowedFileExtensions: number
    fileSecurity: boolean
    compression: string
    encryption: boolean
    antivirus: boolean
  }
  database?: {
    $id: string
    name: string
    enabled: boolean
  }
  collection?: {
    $id: string
    name: string
    enabled: boolean
  }
  error?: string
  details?: string
  missingVars?: string[]
}

export function AppwriteSetup() {
  const [status, setStatus] = useState<SetupStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [setupLoading, setSetupLoading] = useState(false)

  const checkStatus = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/appwrite/setup')
      const data = await response.json()
      setStatus(data)
    } catch (error) {
      setStatus({
        setup: false,
        error: 'Failed to check setup status',
        details: 'Could not connect to the setup API'
      })
    } finally {
      setLoading(false)
    }
  }

  const runSetup = async () => {
    setSetupLoading(true)
    try {
      const response = await fetch('/api/appwrite/setup', {
        method: 'POST'
      })
      const data = await response.json()
      setStatus(data)
      
      if (data.success) {
        // Refresh status after successful setup
        setTimeout(checkStatus, 1000)
      }
    } catch (error) {
      setStatus({
        setup: false,
        error: 'Failed to run setup',
        details: 'Could not connect to the setup API'
      })
    } finally {
      setSetupLoading(false)
    }
  }

  useEffect(() => {
    checkStatus()
  }, [])

  const formatFileSize = (bytes: number): string => {
    return `${Math.round(bytes / 1024 / 1024)}MB`
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Checking Appwrite setup status...</span>
        </CardContent>
      </Card>
    )
  }

  if (!status) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Failed to load setup status
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Appwrite Media Library Setup
            {status.setup ? (
              <Badge variant="default" className="ml-auto">
                <CheckCircle className="h-3 w-3 mr-1" />
                Ready
              </Badge>
            ) : (
              <Badge variant="destructive" className="ml-auto">
                <XCircle className="h-3 w-3 mr-1" />
                Not Setup
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            Configure Appwrite storage buckets and database collections for the media library
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Error Display */}
          {status.error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p className="font-medium">{status.error}</p>
                  {status.details && (
                    <p className="text-sm opacity-90">{status.details}</p>
                  )}
                  {status.missingVars && (
                    <div className="text-sm">
                      <p className="font-medium">Missing environment variables:</p>
                      <ul className="list-disc list-inside mt-1">
                        {status.missingVars.map(varName => (
                          <li key={varName} className="font-mono">{varName}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Setup Actions */}
          <div className="flex items-center gap-3">
            {!status.setup ? (
              <Button 
                onClick={runSetup} 
                disabled={setupLoading}
                className="flex items-center gap-2"
              >
                {setupLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Settings className="h-4 w-4" />
                )}
                {setupLoading ? 'Setting up...' : 'Run Setup'}
              </Button>
            ) : (
              <Button 
                variant="outline"
                onClick={runSetup} 
                disabled={setupLoading}
                className="flex items-center gap-2"
              >
                {setupLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
                {setupLoading ? 'Updating...' : 'Update Setup'}
              </Button>
            )}
            
            <Button 
              variant="outline" 
              onClick={checkStatus}
              disabled={loading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={cn('h-4 w-4', loading && 'animate-spin')} />
              Refresh
            </Button>

            <Button 
              variant="outline" 
              onClick={() => window.open('https://cloud.appwrite.io', '_blank')}
              className="flex items-center gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              Appwrite Console
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Infrastructure Status */}
      {status.setup && (
        <div className="grid gap-4 md:grid-cols-3">
          {/* Storage Bucket */}
          {status.bucket && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <HardDrive className="h-4 w-4" />
                  Storage Bucket
                  <Badge variant={status.bucket.enabled ? 'default' : 'secondary'} className="ml-auto text-xs">
                    {status.bucket.enabled ? 'Enabled' : 'Disabled'}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">ID:</span>
                    <span className="font-mono text-xs">{status.bucket.$id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Name:</span>
                    <span>{status.bucket.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Max Size:</span>
                    <span>{formatFileSize(status.bucket.maximumFileSize)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">File Types:</span>
                    <span>{status.bucket.allowedFileExtensions} types</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2 pt-2 border-t">
                  <div className="flex items-center gap-1 text-xs">
                    {status.bucket.fileSecurity ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : (
                      <XCircle className="h-3 w-3 text-red-500" />
                    )}
                    <span>Security</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs">
                    {status.bucket.encryption ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : (
                      <XCircle className="h-3 w-3 text-red-500" />
                    )}
                    <span>Encryption</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs">
                    {status.bucket.antivirus ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : (
                      <XCircle className="h-3 w-3 text-red-500" />
                    )}
                    <span>Antivirus</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs">
                    <Info className="h-3 w-3 text-blue-500" />
                    <span>{status.bucket.compression}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Database */}
          {status.database && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  Database
                  <Badge variant={status.database.enabled ? 'default' : 'secondary'} className="ml-auto text-xs">
                    {status.database.enabled ? 'Enabled' : 'Disabled'}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">ID:</span>
                    <span className="font-mono text-xs">{status.database.$id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Name:</span>
                    <span>{status.database.name}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Collection */}
          {status.collection && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  Collection
                  <Badge variant={status.collection.enabled ? 'default' : 'secondary'} className="ml-auto text-xs">
                    {status.collection.enabled ? 'Enabled' : 'Disabled'}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">ID:</span>
                    <span className="font-mono text-xs">{status.collection.$id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Name:</span>
                    <span>{status.collection.name}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Setup Instructions */}
      {!status.setup && !status.error && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Setup Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 text-sm">
            <div className="space-y-2">
              <p className="font-medium">Before running setup, ensure you have:</p>
              <ul className="list-disc list-inside space-y-1 text-muted-foreground ml-4">
                <li>Created an Appwrite project</li>
                <li>Generated an API key with required permissions</li>
                <li>Set environment variables in your .env.local file</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <p className="font-medium">Required API key permissions:</p>
              <ul className="list-disc list-inside space-y-1 text-muted-foreground ml-4">
                <li>files.read, files.write</li>
                <li>buckets.read, buckets.write</li>
                <li>databases.read, databases.write</li>
                <li>collections.read, collections.write</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
