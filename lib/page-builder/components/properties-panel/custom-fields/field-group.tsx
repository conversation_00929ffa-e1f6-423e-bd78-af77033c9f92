'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { ChevronDown, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'

import { FieldGroupConfig, FieldConfig, FieldValue } from './types'
import { CustomFieldRenderer, getVisibleFields } from './field-renderer'

interface FieldGroupProps {
  config: FieldGroupConfig
  fields: FieldConfig[]
  values: Record<string, FieldValue>
  onChange: (fieldId: string, value: FieldValue) => void
  onValidate?: (fieldId: string, isValid: boolean, message?: string) => void
  errors?: Record<string, string[]>
  disabled?: boolean
  className?: string
}

export function FieldGroup({
  config,
  fields,
  values,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: FieldGroupProps) {
  const [isOpen, setIsOpen] = useState(!config.collapsed)

  // Filter fields that belong to this group and are visible
  const groupFields = fields.filter(field => config.fields.includes(field.id))
  const visibleFields = getVisibleFields(groupFields, values)

  if (visibleFields.length === 0) {
    return null
  }

  const content = (
    <div className="space-y-4">
      {visibleFields.map((field) => (
        <CustomFieldRenderer
          key={field.id}
          config={field}
          value={values[field.id]}
          onChange={(value) => onChange(field.id, value)}
          onValidate={onValidate ? (isValid, message) => onValidate(field.id, isValid, message) : undefined}
          errors={errors?.[field.id]}
          disabled={disabled}
        />
      ))}
    </div>
  )

  if (!config.collapsible) {
    return (
      <Card className={cn('', className, config.className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">{config.label}</CardTitle>
          {config.description && (
            <CardDescription className="text-xs">{config.description}</CardDescription>
          )}
        </CardHeader>
        <CardContent className="pt-0">
          {content}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn('', className, config.className)}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <CardHeader className="pb-3 cursor-pointer hover:bg-muted/50 transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-sm font-medium">{config.label}</CardTitle>
                {config.description && (
                  <CardDescription className="text-xs">{config.description}</CardDescription>
                )}
              </div>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                {isOpen ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )}
              </Button>
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <CardContent className="pt-0">
            {content}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}

// Helper component for rendering multiple field groups
interface FieldGroupsRendererProps {
  groups: FieldGroupConfig[]
  fields: FieldConfig[]
  values: Record<string, FieldValue>
  onChange: (fieldId: string, value: FieldValue) => void
  onValidate?: (fieldId: string, isValid: boolean, message?: string) => void
  errors?: Record<string, string[]>
  disabled?: boolean
  className?: string
}

export function FieldGroupsRenderer({
  groups,
  fields,
  values,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: FieldGroupsRendererProps) {
  return (
    <div className={cn('space-y-4', className)}>
      {groups.map((group) => (
        <FieldGroup
          key={group.id}
          config={group}
          fields={fields}
          values={values}
          onChange={onChange}
          onValidate={onValidate}
          errors={errors}
          disabled={disabled}
        />
      ))}
    </div>
  )
}

// Helper function to create a simple field group
export function createFieldGroup(
  id: string,
  label: string,
  fieldIds: string[],
  options?: {
    description?: string
    collapsible?: boolean
    collapsed?: boolean
    className?: string
  }
): FieldGroupConfig {
  return {
    id,
    label,
    description: options?.description,
    fields: fieldIds,
    collapsible: options?.collapsible ?? false,
    collapsed: options?.collapsed ?? false,
    className: options?.className
  }
}

// Predefined field groups for common use cases
export const commonFieldGroups = {
  // Typography group
  typography: createFieldGroup(
    'typography',
    'Typography',
    ['fontFamily', 'fontSize', 'fontWeight', 'lineHeight', 'letterSpacing', 'textAlign', 'textTransform'],
    {
      description: 'Text styling and formatting options',
      collapsible: true
    }
  ),

  // Colors group
  colors: createFieldGroup(
    'colors',
    'Colors',
    ['textColor', 'backgroundColor', 'borderColor', 'accentColor'],
    {
      description: 'Color scheme and theming',
      collapsible: true
    }
  ),

  // Spacing group
  spacing: createFieldGroup(
    'spacing',
    'Spacing',
    ['margin', 'padding', 'gap'],
    {
      description: 'Margins, padding, and spacing controls',
      collapsible: true
    }
  ),

  // Layout group
  layout: createFieldGroup(
    'layout',
    'Layout',
    ['display', 'position', 'width', 'height', 'flexDirection', 'justifyContent', 'alignItems'],
    {
      description: 'Layout and positioning options',
      collapsible: true
    }
  ),

  // Effects group
  effects: createFieldGroup(
    'effects',
    'Effects',
    ['boxShadow', 'borderRadius', 'opacity', 'transform', 'filter'],
    {
      description: 'Visual effects and transformations',
      collapsible: true
    }
  ),

  // Animation group
  animation: createFieldGroup(
    'animation',
    'Animation',
    ['animationType', 'animationDuration', 'animationDelay', 'animationEasing'],
    {
      description: 'Animation and transition settings',
      collapsible: true
    }
  ),

  // Accessibility group
  accessibility: createFieldGroup(
    'accessibility',
    'Accessibility',
    ['ariaLabel', 'ariaDescription', 'tabIndex', 'role'],
    {
      description: 'Accessibility and ARIA attributes',
      collapsible: true
    }
  ),

  // SEO group
  seo: createFieldGroup(
    'seo',
    'SEO',
    ['title', 'description', 'keywords', 'canonical'],
    {
      description: 'Search engine optimization settings',
      collapsible: true
    }
  )
}
