'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import {
  File,
  RotateCcw,
  AlertTriangle,
  Plus,
  X,
  Eye,
  Download,
  Upload,
  FileText,
  Image,
  Video,
  Music
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'
import { MediaFile, MediaLibrary } from '@/lib/appwrite/media'
import { MediaLibraryModal } from '../components/media-library-modal'

interface FileValue {
  files: MediaFile[]
  multiple: boolean
}

export function FileField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<FileValue>({
    ...(value as FileValue || {}),
    files: (value as FileValue)?.files || (value as MediaFile ? [value as MediaFile] : []),
    multiple: (value as FileValue)?.multiple || false
  })
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)

  useEffect(() => {
    setLocalValue({
      ...(value as FileValue || {}),
      files: (value as FileValue)?.files || (value as MediaFile ? [value as MediaFile] : []),
      multiple: (value as FileValue)?.multiple || false
    })
  }, [value])

  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const updateFiles = (updates: Partial<FileValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(updated)
  }

  const handleReset = () => {
    const defaultValue: FileValue = {
      ...(config.defaultValue as FileValue || {}),
      files: (config.defaultValue as FileValue)?.files || [],
      multiple: (config.defaultValue as FileValue)?.multiple || false
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  const handleFileSelect = (selectedFiles: MediaFile | MediaFile[]) => {
    const files = Array.isArray(selectedFiles) ? selectedFiles : [selectedFiles]
    updateFiles({ files })
    setIsModalOpen(false)
  }

  const removeFile = (fileId: string) => {
    const newFiles = localValue.files.filter(file => file.$id !== fileId)
    updateFiles({ files: newFiles })
  }

  const openFileLibrary = () => {
    setIsModalOpen(true)
  }

  // Get configuration options
  const multiple = (config as any).multiple || false
  const accept = (config as any).accept || []
  const maxFiles = (config as any).maxFiles || (multiple ? 10 : 1)

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])
  const hasFiles = localValue.files.length > 0

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return <Image className="h-6 w-6" />
    if (mimeType.startsWith('video/')) return <Video className="h-6 w-6" />
    if (mimeType.startsWith('audio/')) return <Music className="h-6 w-6" />
    return <FileText className="h-6 w-6" />
  }

  // Generate file preview
  const generateFilePreview = () => {
    if (!hasFiles) {
      return (
        <div className="border-2 border-dashed rounded-lg p-8 text-center">
          <div className="space-y-4">
            <div className="w-16 h-16 mx-auto bg-muted rounded-lg flex items-center justify-center">
              <File className="h-8 w-8 text-muted-foreground" />
            </div>
            <div>
              <p className="font-medium mb-1">No files selected</p>
              <p className="text-sm text-muted-foreground mb-4">
                Choose files from your library
              </p>
              <Button
                onClick={openFileLibrary}
                disabled={disabled}
                size="sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                Select Files
              </Button>
            </div>
          </div>
        </div>
      )
    }

    return (
      <div className="space-y-3">
        {/* File list */}
        <div className="space-y-2">
          {localValue.files.map((file) => (
            <div
              key={file.$id}
              className="flex items-center gap-3 p-3 border rounded-lg bg-background"
            >
              {/* File icon */}
              <div className="flex-shrink-0 text-muted-foreground">
                {getFileIcon(file.mimeType)}
              </div>

              {/* File info */}
              <div className="flex-1 min-w-0">
                <p className="font-medium text-sm truncate">{file.name}</p>
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span>{MediaLibrary.formatFileSize(file.sizeOriginal)}</span>
                  <span>{file.mimeType}</span>
                  <span>{new Date(file.$createdAt).toLocaleDateString()}</span>
                </div>
              </div>

              {/* File actions */}
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => window.open(file.url, '_blank')}
                  disabled={disabled}
                  className="h-6 w-6 p-0"
                  title="View file"
                >
                  <Eye className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    if (file.url) {
                      const a = document.createElement('a')
                      a.href = file.url
                      a.download = file.name
                      a.click()
                    }
                  }}
                  disabled={disabled}
                  className="h-6 w-6 p-0"
                  title="Download file"
                >
                  <Download className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFile(file.$id)}
                  disabled={disabled}
                  className="h-6 w-6 p-0"
                  title="Remove file"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Add more files button */}
        {multiple && localValue.files.length < maxFiles && (
          <Button
            variant="outline"
            size="sm"
            onClick={openFileLibrary}
            disabled={disabled}
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add More Files
          </Button>
        )}

        {/* Replace file button for single file */}
        {!multiple && (
          <Button
            variant="outline"
            size="sm"
            onClick={openFileLibrary}
            disabled={disabled}
            className="w-full"
          >
            <Upload className="h-4 w-4 mr-2" />
            Replace File
          </Button>
        )}
      </div>
    )
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <File className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        <div className="flex items-center gap-1">
          {hasFiles && (
            <Badge variant="outline" className="text-xs h-4 px-1">
              {localValue.files.length} file{localValue.files.length !== 1 ? 's' : ''}
            </Badge>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* File preview */}
      {generateFilePreview()}

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}

      {/* File Library Modal */}
      <MediaLibraryModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSelect={handleFileSelect}
        multiple={multiple}
        accept={accept}
        title="Select Files"
      />
    </div>
  )
}
