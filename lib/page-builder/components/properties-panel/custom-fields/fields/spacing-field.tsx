'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  RotateCcw,
  AlertTriangle,
  Link,
  Unlink,
  Box,
  Move,
  Maximize2
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

interface SpacingValue {
  top: number
  right: number
  bottom: number
  left: number
  unit: string
}

export function SpacingField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<SpacingValue>({
    ...(value as SpacingValue || {}),
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    unit: 'px'
  })
  const [isLinked, setIsLinked] = useState(config.linked || false)
  const [activeInput, setActiveInput] = useState<string | null>(null)
  const [validationError, setValidationError] = useState<string | null>(null)

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue({
      ...(value as SpacingValue || {}),
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
      unit: 'px'
    })
  }, [value])

  // Validate field on value change
  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const handleValueChange = (side: keyof SpacingValue, newValue: number | string) => {
    if (side === 'unit') {
      const updated = { ...localValue, unit: newValue as string }
      setLocalValue(updated)
      onChange(updated)
      return
    }

    const numValue = Number(newValue) || 0
    let updated: SpacingValue

    if (isLinked) {
      // Update all sides when linked
      updated = {
        ...localValue,
        top: numValue,
        right: numValue,
        bottom: numValue,
        left: numValue
      }
    } else {
      // Update only the specific side
      updated = {
        ...localValue,
        [side]: numValue
      }
    }

    setLocalValue(updated)
    onChange(updated)
  }

  const handleReset = () => {
    const defaultValue: SpacingValue = {
      ...(config.defaultValue as SpacingValue || {}),
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
      unit: 'px'
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  const sides = config.sides || ['top', 'right', 'bottom', 'left']
  const units = ['px', 'rem', 'em', '%', 'vh', 'vw']
  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  // Generate visual spacing representation
  const generateSpacingVisualization = () => {
    const maxValue = Math.max(localValue.top, localValue.right, localValue.bottom, localValue.left)
    const scale = maxValue > 0 ? 60 / maxValue : 1

    return (
      <svg width="120" height="120" className="mx-auto">
        <defs>
          <pattern id={`grid-${config.id}`} width="10" height="10" patternUnits="userSpaceOnUse">
            <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#e2e8f0" strokeWidth="0.5"/>
          </pattern>
          
          <linearGradient id={`spacing-gradient-${config.id}`} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.3" />
            <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0.3" />
          </linearGradient>
        </defs>

        {/* Grid background */}
        <rect width="120" height="120" fill={`url(#grid-${config.id})`} />

        {/* Center element */}
        <rect
          x={30 + (localValue.left * scale)}
          y={30 + (localValue.top * scale)}
          width={60 - ((localValue.left + localValue.right) * scale)}
          height={60 - ((localValue.top + localValue.bottom) * scale)}
          fill="#1f2937"
          stroke="#374151"
          strokeWidth="1"
          rx="4"
        />

        {/* Spacing areas */}
        {sides.includes('top') && localValue.top > 0 && (
          <rect
            x={30 + (localValue.left * scale)}
            y="30"
            width={60 - ((localValue.left + localValue.right) * scale)}
            height={localValue.top * scale}
            fill={`url(#spacing-gradient-${config.id})`}
            stroke="#3b82f6"
            strokeWidth="1"
            strokeDasharray="2,2"
            className={cn(
              'transition-all duration-200',
              activeInput === 'top' && 'stroke-2'
            )}
          />
        )}

        {sides.includes('right') && localValue.right > 0 && (
          <rect
            x={90 - (localValue.right * scale)}
            y={30 + (localValue.top * scale)}
            width={localValue.right * scale}
            height={60 - ((localValue.top + localValue.bottom) * scale)}
            fill={`url(#spacing-gradient-${config.id})`}
            stroke="#3b82f6"
            strokeWidth="1"
            strokeDasharray="2,2"
            className={cn(
              'transition-all duration-200',
              activeInput === 'right' && 'stroke-2'
            )}
          />
        )}

        {sides.includes('bottom') && localValue.bottom > 0 && (
          <rect
            x={30 + (localValue.left * scale)}
            y={90 - (localValue.bottom * scale)}
            width={60 - ((localValue.left + localValue.right) * scale)}
            height={localValue.bottom * scale}
            fill={`url(#spacing-gradient-${config.id})`}
            stroke="#3b82f6"
            strokeWidth="1"
            strokeDasharray="2,2"
            className={cn(
              'transition-all duration-200',
              activeInput === 'bottom' && 'stroke-2'
            )}
          />
        )}

        {sides.includes('left') && localValue.left > 0 && (
          <rect
            x="30"
            y={30 + (localValue.top * scale)}
            width={localValue.left * scale}
            height={60 - ((localValue.top + localValue.bottom) * scale)}
            fill={`url(#spacing-gradient-${config.id})`}
            stroke="#3b82f6"
            strokeWidth="1"
            strokeDasharray="2,2"
            className={cn(
              'transition-all duration-200',
              activeInput === 'left' && 'stroke-2'
            )}
          />
        )}

        {/* Labels */}
        {sides.includes('top') && (
          <text x="60" y="20" textAnchor="middle" className="text-xs fill-muted-foreground">
            {localValue.top}{localValue.unit}
          </text>
        )}
        {sides.includes('right') && (
          <text x="105" y="65" textAnchor="middle" className="text-xs fill-muted-foreground">
            {localValue.right}{localValue.unit}
          </text>
        )}
        {sides.includes('bottom') && (
          <text x="60" y="110" textAnchor="middle" className="text-xs fill-muted-foreground">
            {localValue.bottom}{localValue.unit}
          </text>
        )}
        {sides.includes('left') && (
          <text x="15" y="65" textAnchor="middle" className="text-xs fill-muted-foreground">
            {localValue.left}{localValue.unit}
          </text>
        )}
      </svg>
    )
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Box className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>
        
        {/* Controls */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsLinked(!isLinked)}
            className={cn(
              'h-5 w-5 p-0',
              isLinked && 'text-primary'
            )}
            title={isLinked ? 'Unlink values' : 'Link values'}
          >
            {isLinked ? <Link className="h-3 w-3" /> : <Unlink className="h-3 w-3" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      <Tabs defaultValue="visual" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="visual" className="text-xs">
            <Maximize2 className="h-3 w-3 mr-1" />
            Visual
          </TabsTrigger>
          <TabsTrigger value="inputs" className="text-xs">
            <Move className="h-3 w-3 mr-1" />
            Inputs
          </TabsTrigger>
        </TabsList>

        <TabsContent value="visual" className="space-y-3">
          {/* Visual representation */}
          <div className="border rounded-lg p-4 bg-muted/20">
            {generateSpacingVisualization()}
          </div>

          {/* Unit selector */}
          <div className="flex items-center gap-2">
            <Label className="text-xs">Unit:</Label>
            <div className="flex gap-1">
              {units.map((unit) => (
                <Button
                  key={unit}
                  variant={localValue.unit === unit ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleValueChange('unit', unit)}
                  className="h-6 px-2 text-xs"
                >
                  {unit}
                </Button>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="inputs" className="space-y-3">
          {/* Input controls */}
          <div className="grid grid-cols-2 gap-2">
            {sides.map((side: keyof SpacingValue) => (
              <div key={side} className="space-y-1">
                <Label className="text-xs capitalize">{side}</Label>
                <div className="flex items-center gap-1">
                  <Input
                    type="number"
                    value={localValue[side]}
                    onChange={(e) => handleValueChange(side, e.target.value)}
                    onFocus={() => setActiveInput(side)}
                    onBlur={() => setActiveInput(null)}
                    min={config.min || 0}
                    max={config.max || 200}
                    disabled={disabled || config.disabled}
                    className="text-xs h-6"
                  />
                  <Badge variant="outline" className="text-xs h-4 px-1">
                    {localValue.unit}
                  </Badge>
                </div>
              </div>
            ))}
          </div>

          {/* Linked input */}
          {isLinked && (
            <div className="space-y-1">
              <Label className="text-xs">All Sides</Label>
              <div className="flex items-center gap-1">
                <Input
                  type="number"
                  value={localValue.top}
                  onChange={(e) => {
                    const value = Number(e.target.value) || 0
                    const updated = {
                      ...localValue,
                      top: value,
                      right: value,
                      bottom: value,
                      left: value
                    }
                    setLocalValue(updated)
                    onChange(updated)
                  }}
                  min={config.min || 0}
                  max={config.max || 200}
                  disabled={disabled || config.disabled}
                  className="text-xs h-6"
                />
                <Badge variant="outline" className="text-xs h-4 px-1">
                  {localValue.unit}
                </Badge>
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
