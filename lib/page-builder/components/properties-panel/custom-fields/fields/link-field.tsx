'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Link,
  RotateCcw,
  AlertTriangle,
  ExternalLink,
  Mail,
  Phone,
  Globe,
  Hash,
  Settings,
  Eye
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

interface LinkValue {
  url: string
  text: string
  target: '_self' | '_blank' | '_parent' | '_top'
  rel: string[]
  title: string
  type: 'url' | 'email' | 'phone' | 'anchor'
}

export function LinkField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<LinkValue>({
    url: '',
    text: '',
    target: '_self',
    rel: [],
    title: '',
    type: 'url',
    ...(value as LinkValue || {})
  })
  const [validationError, setValidationError] = useState<string | null>(null)

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue({
      url: '',
      text: '',
      target: '_self',
      rel: [],
      title: '',
      type: 'url',
      ...(value as LinkValue || {})
    })
  }, [value])

  // Validate field on value change
  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const updateLink = (updates: Partial<LinkValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(updated)
  }

  const handleReset = () => {
    const defaultValue: LinkValue = {
      url: '',
      text: '',
      target: '_self',
      rel: [],
      title: '',
      type: 'url',
      ...(config.defaultValue as LinkValue || {})
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  const toggleRel = (relValue: string) => {
    const newRel = localValue.rel.includes(relValue)
      ? localValue.rel.filter(r => r !== relValue)
      : [...localValue.rel, relValue]
    updateLink({ rel: newRel })
  }

  const linkTypes = [
    { value: 'url', label: 'URL', icon: Globe, prefix: 'https://' },
    { value: 'email', label: 'Email', icon: Mail, prefix: 'mailto:' },
    { value: 'phone', label: 'Phone', icon: Phone, prefix: 'tel:' },
    { value: 'anchor', label: 'Anchor', icon: Hash, prefix: '#' }
  ]

  const targets = [
    { value: '_self', label: 'Same Window' },
    { value: '_blank', label: 'New Window' },
    { value: '_parent', label: 'Parent Frame' },
    { value: '_top', label: 'Top Frame' }
  ]

  const relOptions = [
    { value: 'nofollow', label: 'No Follow' },
    { value: 'noopener', label: 'No Opener' },
    { value: 'noreferrer', label: 'No Referrer' },
    { value: 'sponsored', label: 'Sponsored' },
    { value: 'ugc', label: 'User Generated' }
  ]

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  const selectedType = linkTypes.find(type => type.value === localValue.type)
  const TypeIcon = selectedType?.icon || Globe

  // Generate link preview
  const generateLinkPreview = () => {
    const fullUrl = localValue.type === 'url' && !localValue.url.startsWith('http')
      ? `https://${localValue.url}`
      : localValue.url

    return (
      <div className="border rounded-lg p-3 bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="flex items-start gap-3">
          <div className="w-8 h-8 rounded border bg-white flex items-center justify-center">
            <TypeIcon className="h-4 w-4 text-primary" />
          </div>

          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium text-primary truncate">
              {localValue.text || 'Link Text'}
            </div>
            <div className="text-xs text-muted-foreground truncate">
              {fullUrl || 'No URL specified'}
            </div>
            {localValue.title && (
              <div className="text-xs text-muted-foreground mt-1">
                {localValue.title}
              </div>
            )}

            <div className="flex items-center gap-2 mt-2">
              {localValue.target === '_blank' && (
                <Badge variant="outline" className="text-xs h-4 px-1">
                  <ExternalLink className="h-2 w-2 mr-1" />
                  New Window
                </Badge>
              )}
              {localValue.rel.length > 0 && (
                <Badge variant="outline" className="text-xs h-4 px-1">
                  {localValue.rel.join(', ')}
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Link className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleReset}
          className="h-5 w-5 p-0"
          title="Reset to default"
        >
          <RotateCcw className="h-3 w-3" />
        </Button>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Link preview */}
      {generateLinkPreview()}

      {/* Link controls */}
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic" className="text-xs">
            <Link className="h-3 w-3 mr-1" />
            Basic
          </TabsTrigger>
          <TabsTrigger value="advanced" className="text-xs">
            <Settings className="h-3 w-3 mr-1" />
            Advanced
          </TabsTrigger>
          <TabsTrigger value="preview" className="text-xs">
            <Eye className="h-3 w-3 mr-1" />
            Preview
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-3">
          {/* Link type */}
          <div className="space-y-2">
            <Label className="text-xs">Link Type</Label>
            <div className="grid grid-cols-2 gap-1">
              {linkTypes.map((type) => {
                const Icon = type.icon
                return (
                  <Button
                    key={type.value}
                    variant={localValue.type === type.value ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => updateLink({ type: type.value as any })}
                    className="text-xs h-8 justify-start"
                    disabled={disabled}
                  >
                    <Icon className="h-3 w-3 mr-1" />
                    {type.label}
                  </Button>
                )
              })}
            </div>
          </div>

          {/* URL input */}
          <div className="space-y-2">
            <Label className="text-xs">
              {localValue.type === 'email' ? 'Email Address' :
               localValue.type === 'phone' ? 'Phone Number' :
               localValue.type === 'anchor' ? 'Anchor ID' : 'URL'}
            </Label>
            <div className="flex">
              {selectedType?.prefix && (
                <div className="flex items-center px-2 bg-muted border border-r-0 rounded-l text-xs text-muted-foreground">
                  {selectedType.prefix}
                </div>
              )}
              <Input
                type={localValue.type === 'email' ? 'email' : localValue.type === 'phone' ? 'tel' : 'text'}
                value={localValue.url}
                onChange={(e) => updateLink({ url: e.target.value })}
                placeholder={
                  localValue.type === 'email' ? '<EMAIL>' :
                  localValue.type === 'phone' ? '+1234567890' :
                  localValue.type === 'anchor' ? 'section-id' :
                  'www.example.com'
                }
                className={cn(
                  'text-xs',
                  selectedType?.prefix && 'rounded-l-none'
                )}
                disabled={disabled}
              />
            </div>
          </div>

          {/* Link text */}
          <div className="space-y-2">
            <Label className="text-xs">Link Text</Label>
            <Input
              type="text"
              value={localValue.text}
              onChange={(e) => updateLink({ text: e.target.value })}
              placeholder="Click here"
              className="text-xs"
              disabled={disabled}
            />
          </div>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-3">
          {/* Target */}
          <div className="space-y-2">
            <Label className="text-xs">Target</Label>
            <div className="grid grid-cols-2 gap-1">
              {targets.map((target) => (
                <Button
                  key={target.value}
                  variant={localValue.target === target.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateLink({ target: target.value as any })}
                  className="text-xs h-6"
                  disabled={disabled}
                >
                  {target.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Title */}
          <div className="space-y-2">
            <Label className="text-xs">Title (Tooltip)</Label>
            <Input
              type="text"
              value={localValue.title}
              onChange={(e) => updateLink({ title: e.target.value })}
              placeholder="Additional information"
              className="text-xs"
              disabled={disabled}
            />
          </div>

          {/* Rel attributes */}
          <div className="space-y-2">
            <Label className="text-xs">Rel Attributes</Label>
            <div className="grid grid-cols-2 gap-1">
              {relOptions.map((rel) => (
                <Button
                  key={rel.value}
                  variant={localValue.rel.includes(rel.value) ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => toggleRel(rel.value)}
                  className="text-xs h-6"
                  disabled={disabled}
                >
                  {rel.label}
                </Button>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="preview" className="space-y-3">
          {/* HTML output */}
          <div className="space-y-1">
            <Label className="text-xs">HTML Output</Label>
            <div className="bg-muted p-2 rounded text-xs font-mono break-all">
              &lt;a href="{localValue.url}"
              {localValue.target !== '_self' && ` target="${localValue.target}"`}
              {localValue.rel.length > 0 && ` rel="${localValue.rel.join(' ')}"`}
              {localValue.title && ` title="${localValue.title}"`}
              &gt;{localValue.text || 'Link Text'}&lt;/a&gt;
            </div>
          </div>

          {/* Test link */}
          {localValue.url && (
            <div className="space-y-1">
              <Label className="text-xs">Test Link</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const url = localValue.type === 'url' && !localValue.url.startsWith('http')
                    ? `https://${localValue.url}`
                    : localValue.url
                  window.open(url, localValue.target)
                }}
                className="w-full text-xs h-8"
                disabled={disabled}
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                Open Link
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
