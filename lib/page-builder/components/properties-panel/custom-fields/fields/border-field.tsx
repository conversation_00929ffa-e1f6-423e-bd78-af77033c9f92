'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Slider } from '@/components/ui/slider'
import {
  Square,
  RotateCcw,
  AlertTriangle,
  Link,
  Unlink,
  Layers
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

interface BorderValue {
  top: { width: number; style: string; color: string }
  right: { width: number; style: string; color: string }
  bottom: { width: number; style: string; color: string }
  left: { width: number; style: string; color: string }
  linked: boolean
  radius: { topLeft: number; topRight: number; bottomRight: number; bottomLeft: number }
}

export function BorderField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<BorderValue>({
    ...(value as BorderValue || {}),
    top: (value as BorderValue)?.top || { width: 1, style: 'solid', color: '#e2e8f0' },
    right: (value as BorderValue)?.right || { width: 1, style: 'solid', color: '#e2e8f0' },
    bottom: (value as BorderValue)?.bottom || { width: 1, style: 'solid', color: '#e2e8f0' },
    left: (value as BorderValue)?.left || { width: 1, style: 'solid', color: '#e2e8f0' },
    linked: (value as BorderValue)?.linked ?? true,
    radius: (value as BorderValue)?.radius || { topLeft: 0, topRight: 0, bottomRight: 0, bottomLeft: 0 }
  })
  const [validationError, setValidationError] = useState<string | null>(null)

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue({
      ...(value as BorderValue || {}),
      top: (value as BorderValue)?.top || { width: 1, style: 'solid', color: '#e2e8f0' },
      right: (value as BorderValue)?.right || { width: 1, style: 'solid', color: '#e2e8f0' },
      bottom: (value as BorderValue)?.bottom || { width: 1, style: 'solid', color: '#e2e8f0' },
      left: (value as BorderValue)?.left || { width: 1, style: 'solid', color: '#e2e8f0' },
      linked: (value as BorderValue)?.linked ?? true,
      radius: (value as BorderValue)?.radius || { topLeft: 0, topRight: 0, bottomRight: 0, bottomLeft: 0 }
    })
  }, [value])

  // Validate field on value change
  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const updateBorder = (updates: Partial<BorderValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(updated)
  }

  const updateSide = (side: keyof Omit<BorderValue, 'linked' | 'radius'>, property: string, value: any) => {
    if (localValue.linked) {
      // Update all sides when linked
      const newBorder = {
        ...localValue,
        top: { ...localValue.top, [property]: value },
        right: { ...localValue.right, [property]: value },
        bottom: { ...localValue.bottom, [property]: value },
        left: { ...localValue.left, [property]: value }
      }
      setLocalValue(newBorder)
      onChange(newBorder)
    } else {
      // Update only the specific side
      const newBorder = {
        ...localValue,
        [side]: { ...localValue[side], [property]: value }
      }
      setLocalValue(newBorder)
      onChange(newBorder)
    }
  }

  const updateRadius = (corner: keyof BorderValue['radius'], value: number) => {
    const newBorder = {
      ...localValue,
      radius: { ...localValue.radius, [corner]: value }
    }
    setLocalValue(newBorder)
    onChange(newBorder)
  }

  const handleReset = () => {
    const defaultValue: BorderValue = {
      ...(config.defaultValue as BorderValue || {}),
      top: { width: 1, style: 'solid', color: '#e2e8f0' },
      right: { width: 1, style: 'solid', color: '#e2e8f0' },
      bottom: { width: 1, style: 'solid', color: '#e2e8f0' },
      left: { width: 1, style: 'solid', color: '#e2e8f0' },
      linked: true,
      radius: { topLeft: 0, topRight: 0, bottomRight: 0, bottomLeft: 0 },
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  const borderStyles = [
    { value: 'solid', label: 'Solid' },
    { value: 'dashed', label: 'Dashed' },
    { value: 'dotted', label: 'Dotted' },
    { value: 'double', label: 'Double' },
    { value: 'groove', label: 'Groove' },
    { value: 'ridge', label: 'Ridge' },
    { value: 'inset', label: 'Inset' },
    { value: 'outset', label: 'Outset' }
  ]

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  // Generate border preview SVG
  const generateBorderPreview = () => {
    const { top, right, bottom, left, radius } = localValue

    return (
      <svg width="120" height="80" className="mx-auto">
        <defs>
          <pattern id={`grid-${config.id}`} width="10" height="10" patternUnits="userSpaceOnUse">
            <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#f1f5f9" strokeWidth="0.5"/>
          </pattern>
        </defs>

        {/* Grid background */}
        <rect width="120" height="80" fill={`url(#grid-${config.id})`} />

        {/* Border preview box */}
        <rect
          x="20"
          y="15"
          width="80"
          height="50"
          fill="#f8fafc"
          stroke="none"
          rx={Math.max(radius.topLeft, radius.topRight, radius.bottomLeft, radius.bottomRight)}
        />

        {/* Top border */}
        <line
          x1="20"
          y1="15"
          x2="100"
          y2="15"
          stroke={top.color}
          strokeWidth={top.width}
          strokeDasharray={top.style === 'dashed' ? '4,2' : top.style === 'dotted' ? '1,1' : 'none'}
          className="transition-all duration-200"
        />

        {/* Right border */}
        <line
          x1="100"
          y1="15"
          x2="100"
          y2="65"
          stroke={right.color}
          strokeWidth={right.width}
          strokeDasharray={right.style === 'dashed' ? '4,2' : right.style === 'dotted' ? '1,1' : 'none'}
          className="transition-all duration-200"
        />

        {/* Bottom border */}
        <line
          x1="100"
          y1="65"
          x2="20"
          y2="65"
          stroke={bottom.color}
          strokeWidth={bottom.width}
          strokeDasharray={bottom.style === 'dashed' ? '4,2' : bottom.style === 'dotted' ? '1,1' : 'none'}
          className="transition-all duration-200"
        />

        {/* Left border */}
        <line
          x1="20"
          y1="65"
          x2="20"
          y2="15"
          stroke={left.color}
          strokeWidth={left.width}
          strokeDasharray={left.style === 'dashed' ? '4,2' : left.style === 'dotted' ? '1,1' : 'none'}
          className="transition-all duration-200"
        />

        {/* Corner radius indicators */}
        {radius.topLeft > 0 && (
          <circle cx="25" cy="20" r="2" fill="#3b82f6" opacity="0.6" />
        )}
        {radius.topRight > 0 && (
          <circle cx="95" cy="20" r="2" fill="#3b82f6" opacity="0.6" />
        )}
        {radius.bottomRight > 0 && (
          <circle cx="95" cy="60" r="2" fill="#3b82f6" opacity="0.6" />
        )}
        {radius.bottomLeft > 0 && (
          <circle cx="25" cy="60" r="2" fill="#3b82f6" opacity="0.6" />
        )}

        {/* Side labels */}
        <text x="60" y="10" textAnchor="middle" className="text-xs fill-muted-foreground">
          {top.width}px
        </text>
        <text x="110" y="42" textAnchor="middle" className="text-xs fill-muted-foreground">
          {right.width}px
        </text>
        <text x="60" y="75" textAnchor="middle" className="text-xs fill-muted-foreground">
          {bottom.width}px
        </text>
        <text x="10" y="42" textAnchor="middle" className="text-xs fill-muted-foreground">
          {left.width}px
        </text>
      </svg>
    )
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Square className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        {/* Controls */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => updateBorder({ linked: !localValue.linked })}
            className={cn(
              'h-5 w-5 p-0',
              localValue.linked && 'text-primary'
            )}
            title={localValue.linked ? 'Unlink sides' : 'Link sides'}
          >
            {localValue.linked ? <Link className="h-3 w-3" /> : <Unlink className="h-3 w-3" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Border preview */}
      <div className="border rounded-lg p-4 bg-muted/20">
        {generateBorderPreview()}
      </div>

      {/* Border controls */}
      <Tabs defaultValue="borders" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="borders" className="text-xs">
            <Square className="h-3 w-3 mr-1" />
            Borders
          </TabsTrigger>
          <TabsTrigger value="radius" className="text-xs">
            <Layers className="h-3 w-3 mr-1" />
            Radius
          </TabsTrigger>
        </TabsList>

        <TabsContent value="borders" className="space-y-3">
          {/* Border width control */}
          <div className="space-y-2">
            <Label className="text-xs">Width</Label>
            <div className="flex items-center gap-2">
              <Slider
                value={[localValue.top.width]}
                onValueChange={([width]) => updateSide('top', 'width', width)}
                min={0}
                max={10}
                step={1}
                className="flex-1"
                disabled={disabled}
              />
              <Badge variant="outline" className="text-xs h-4 px-1 min-w-[3rem]">
                {localValue.top.width}px
              </Badge>
            </div>
          </div>

          {/* Border style control */}
          <div className="space-y-2">
            <Label className="text-xs">Style</Label>
            <div className="grid grid-cols-2 gap-1">
              {borderStyles.slice(0, 4).map((style) => (
                <Button
                  key={style.value}
                  variant={localValue.top.style === style.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateSide('top', 'style', style.value)}
                  className="text-xs h-6"
                  disabled={disabled}
                >
                  {style.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Border color control */}
          <div className="space-y-2">
            <Label className="text-xs">Color</Label>
            <div className="flex gap-1">
              <Input
                type="color"
                value={localValue.top.color}
                onChange={(e) => updateSide('top', 'color', e.target.value)}
                className="w-8 h-6 p-0 border-0"
                disabled={disabled}
              />
              <Input
                type="text"
                value={localValue.top.color}
                onChange={(e) => updateSide('top', 'color', e.target.value)}
                className="text-xs h-6 flex-1"
                disabled={disabled}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="radius" className="space-y-3">
          {/* Corner radius controls */}
          <div className="grid grid-cols-2 gap-2">
            <div className="space-y-1">
              <Label className="text-xs">Top Left</Label>
              <Input
                type="number"
                value={localValue.radius.topLeft}
                onChange={(e) => updateRadius('topLeft', Number(e.target.value))}
                min={0}
                max={50}
                className="text-xs h-6"
                disabled={disabled}
              />
            </div>
            <div className="space-y-1">
              <Label className="text-xs">Top Right</Label>
              <Input
                type="number"
                value={localValue.radius.topRight}
                onChange={(e) => updateRadius('topRight', Number(e.target.value))}
                min={0}
                max={50}
                className="text-xs h-6"
                disabled={disabled}
              />
            </div>
            <div className="space-y-1">
              <Label className="text-xs">Bottom Left</Label>
              <Input
                type="number"
                value={localValue.radius.bottomLeft}
                onChange={(e) => updateRadius('bottomLeft', Number(e.target.value))}
                min={0}
                max={50}
                className="text-xs h-6"
                disabled={disabled}
              />
            </div>
            <div className="space-y-1">
              <Label className="text-xs">Bottom Right</Label>
              <Input
                type="number"
                value={localValue.radius.bottomRight}
                onChange={(e) => updateRadius('bottomRight', Number(e.target.value))}
                min={0}
                max={50}
                className="text-xs h-6"
                disabled={disabled}
              />
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
