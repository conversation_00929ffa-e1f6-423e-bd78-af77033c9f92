'use client'

import React, { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Minus, Plus, RotateCcw, AlertTriangle } from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

export function NumberField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState(value as number || 0)
  const [inputValue, setInputValue] = useState(String(value || 0))
  const [validationError, setValidationError] = useState<string | null>(null)

  // Update local value when prop value changes
  useEffect(() => {
    const numValue = Number(value) || 0
    setLocalValue(numValue)
    setInputValue(String(numValue))
  }, [value])

  // Validate field on value change
  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const stringValue = e.target.value
    setInputValue(stringValue)

    // Parse number value
    const numValue = parseFloat(stringValue)
    if (!isNaN(numValue)) {
      setLocalValue(numValue)
      onChange(numValue)
    } else if (stringValue === '') {
      setLocalValue(0)
      onChange(0)
    }
  }

  const handleIncrement = () => {
    const step = config.step || 1
    const newValue = localValue + step
    const maxValue = config.validation?.max || config.max
    
    if (maxValue === undefined || newValue <= maxValue) {
      setLocalValue(newValue)
      setInputValue(String(newValue))
      onChange(newValue)
    }
  }

  const handleDecrement = () => {
    const step = config.step || 1
    const newValue = localValue - step
    const minValue = config.validation?.min || config.min
    
    if (minValue === undefined || newValue >= minValue) {
      setLocalValue(newValue)
      setInputValue(String(newValue))
      onChange(newValue)
    }
  }

  const handleReset = () => {
    const defaultValue = config.defaultValue as number || 0
    setLocalValue(defaultValue)
    setInputValue(String(defaultValue))
    onChange(defaultValue)
  }

  const minValue = config.validation?.min || config.min
  const maxValue = config.validation?.max || config.max
  const step = config.step || 1
  const unit = config.unit || ''
  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  const canDecrement = minValue === undefined || localValue > minValue
  const canIncrement = maxValue === undefined || localValue < maxValue

  return (
    <div className={cn('space-y-2', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium">
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
          {unit && (
            <Badge variant="outline" className="ml-2 text-xs h-4 px-1">
              {unit}
            </Badge>
          )}
        </Label>
        
        {/* Reset button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleReset}
          className="h-5 w-5 p-0"
          title="Reset to default"
        >
          <RotateCcw className="h-3 w-3" />
        </Button>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Number input with controls */}
      <div className="flex items-center gap-1">
        {/* Decrement button */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleDecrement}
          disabled={disabled || config.disabled || !canDecrement}
          className="h-8 w-8 p-0 shrink-0"
        >
          <Minus className="h-3 w-3" />
        </Button>

        {/* Input field */}
        <div className="relative flex-1">
          <Input
            id={config.id}
            type="number"
            value={inputValue}
            onChange={handleInputChange}
            placeholder={config.placeholder}
            disabled={disabled || config.disabled}
            className={cn(
              'text-xs text-center',
              hasError && 'border-destructive focus-visible:ring-destructive',
              config.className
            )}
            min={minValue}
            max={maxValue}
            step={step}
            required={config.validation?.required}
          />
          
          {/* Unit display */}
          {unit && (
            <div className="absolute right-2 top-1/2 -translate-y-1/2 pointer-events-none">
              <span className="text-xs text-muted-foreground">{unit}</span>
            </div>
          )}
        </div>

        {/* Increment button */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleIncrement}
          disabled={disabled || config.disabled || !canIncrement}
          className="h-8 w-8 p-0 shrink-0"
        >
          <Plus className="h-3 w-3" />
        </Button>
      </div>

      {/* Range indicator */}
      {(minValue !== undefined || maxValue !== undefined) && (
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>Min: {minValue ?? '∞'}</span>
          <span>Max: {maxValue ?? '∞'}</span>
        </div>
      )}

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}

      {/* Quick value buttons */}
      {config.quickValues && (
        <div className="flex flex-wrap gap-1">
          {config.quickValues.map((quickValue: number) => (
            <Button
              key={quickValue}
              variant="outline"
              size="sm"
              onClick={() => {
                setLocalValue(quickValue)
                setInputValue(String(quickValue))
                onChange(quickValue)
              }}
              className="h-6 px-2 text-xs"
            >
              {quickValue}{unit}
            </Button>
          ))}
        </div>
      )}
    </div>
  )
}

// Specialized number field variants
export function IntegerField(props: CustomFieldProps) {
  return (
    <NumberField
      {...props}
      config={{
        ...props.config,
        step: 1
      }}
    />
  )
}

export function DecimalField(props: CustomFieldProps) {
  return (
    <NumberField
      {...props}
      config={{
        ...props.config,
        step: 0.1
      }}
    />
  )
}

export function PercentageField(props: CustomFieldProps) {
  return (
    <NumberField
      {...props}
      config={{
        ...props.config,
        min: 0,
        max: 100,
        step: 1,
        unit: '%'
      }}
    />
  )
}

export function CurrencyField(props: CustomFieldProps) {
  return (
    <NumberField
      {...props}
      config={{
        ...props.config,
        min: 0,
        step: 0.01,
        unit: props.config.currency || '$'
      }}
    />
  )
}
