'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Slider } from '@/components/ui/slider'
import {
  Star,
  RotateCcw,
  AlertTriangle,
  Search,
  Grid3X3,
  Heart,
  Home,
  User,
  Settings,
  Mail,
  Phone,
  Calendar,
  Camera,
  Music,
  Video,
  Download,
  Upload,
  Edit,
  Trash2,
  Plus,
  Minus,
  Check,
  X,
  ArrowRight,
  ChevronRight,
  Play,
  Pause,
  Volume2,
  Wifi,
  Battery,
  Zap,
  Sun,
  Moon,
  Cloud
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

interface IconValue {
  name: string
  size: number
  color: string
  strokeWidth?: number
}

export function IconField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<IconValue>({
    ...(value as IconValue || {}),
    name: (value as IconValue)?.name || 'Star',
    size: (value as IconValue)?.size || 24,
    color: (value as IconValue)?.color || '#000000',
    strokeWidth: (value as IconValue)?.strokeWidth || 2
  })
  const [isOpen, setIsOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [validationError, setValidationError] = useState<string | null>(null)

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue({
      ...(value as IconValue || {}),
      name: (value as IconValue)?.name || 'Star',
      size: (value as IconValue)?.size || 24,
      color: (value as IconValue)?.color || '#000000',
      strokeWidth: (value as IconValue)?.strokeWidth || 2
    })
  }, [value])

  // Validate field on value change
  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const updateIcon = (updates: Partial<IconValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(updated)
  }

  const handleReset = () => {
    const defaultValue: IconValue = {
      ...(config.defaultValue as IconValue || {}),
      name: (config.defaultValue as IconValue)?.name || 'Star',
      size: (config.defaultValue as IconValue)?.size || 24,
      color: (config.defaultValue as IconValue)?.color || '#000000',
      strokeWidth: (config.defaultValue as IconValue)?.strokeWidth || 2
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  // Icon library with categories
  const iconLibrary = [
    { name: 'Star', component: Star, category: 'general', popular: true },
    { name: 'Heart', component: Heart, category: 'general', popular: true },
    { name: 'Home', component: Home, category: 'general', popular: true },
    { name: 'User', component: User, category: 'general', popular: true },
    { name: 'Settings', component: Settings, category: 'general', popular: false },
    { name: 'Mail', component: Mail, category: 'communication', popular: true },
    { name: 'Phone', component: Phone, category: 'communication', popular: true },
    { name: 'Calendar', component: Calendar, category: 'general', popular: false },
    { name: 'Camera', component: Camera, category: 'media', popular: true },
    { name: 'Music', component: Music, category: 'media', popular: false },
    { name: 'Video', component: Video, category: 'media', popular: false },
    { name: 'Download', component: Download, category: 'actions', popular: true },
    { name: 'Upload', component: Upload, category: 'actions', popular: true },
    { name: 'Edit', component: Edit, category: 'actions', popular: true },
    { name: 'Trash2', component: Trash2, category: 'actions', popular: false },
    { name: 'Plus', component: Plus, category: 'actions', popular: true },
    { name: 'Minus', component: Minus, category: 'actions', popular: false },
    { name: 'Check', component: Check, category: 'actions', popular: true },
    { name: 'X', component: X, category: 'actions', popular: true },
    { name: 'ArrowRight', component: ArrowRight, category: 'arrows', popular: true },
    { name: 'ChevronRight', component: ChevronRight, category: 'arrows', popular: true },
    { name: 'Play', component: Play, category: 'media', popular: true },
    { name: 'Pause', component: Pause, category: 'media', popular: false },
    { name: 'Volume2', component: Volume2, category: 'media', popular: false },
    { name: 'Wifi', component: Wifi, category: 'tech', popular: false },
    { name: 'Battery', component: Battery, category: 'tech', popular: false },
    { name: 'Zap', component: Zap, category: 'general', popular: false },
    { name: 'Sun', component: Sun, category: 'weather', popular: false },
    { name: 'Moon', component: Moon, category: 'weather', popular: false },
    { name: 'Cloud', component: Cloud, category: 'weather', popular: false }
  ]

  const categories = [
    { id: 'all', label: 'All', count: iconLibrary.length },
    { id: 'popular', label: 'Popular', count: iconLibrary.filter(icon => icon.popular).length },
    { id: 'general', label: 'General', count: iconLibrary.filter(icon => icon.category === 'general').length },
    { id: 'actions', label: 'Actions', count: iconLibrary.filter(icon => icon.category === 'actions').length }
  ]

  const filteredIcons = iconLibrary.filter(icon => {
    const matchesSearch = !searchValue || icon.name.toLowerCase().includes(searchValue.toLowerCase())
    const matchesCategory = selectedCategory === 'all' ||
                           (selectedCategory === 'popular' && icon.popular) ||
                           icon.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const selectedIcon = iconLibrary.find(icon => icon.name === localValue.name)
  const IconComponent = selectedIcon?.component || Star

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Star className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleReset}
          className="h-5 w-5 p-0"
          title="Reset to default"
        >
          <RotateCcw className="h-3 w-3" />
        </Button>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Icon preview */}
      <div className="space-y-2">
        <div className="w-full p-6 border rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
          <div className="relative">
            {/* Icon with glow effect */}
            <div
              className="relative"
              style={{
                filter: `drop-shadow(0 0 ${localValue.size / 4}px ${localValue.color}40)`
              }}
            >
              <IconComponent
                size={localValue.size}
                color={localValue.color}
                strokeWidth={localValue.strokeWidth}
                className="transition-all duration-200"
              />
            </div>

            {/* Size indicator */}
            <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2">
              <Badge variant="outline" className="text-xs h-4 px-1">
                {localValue.size}px
              </Badge>
            </div>
          </div>
        </div>

        {/* Icon info */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>{localValue.name}</span>
          <span>{localValue.color}</span>
        </div>
      </div>

      {/* Icon controls */}
      <Tabs defaultValue="picker" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="picker" className="text-xs">
            <Grid3X3 className="h-3 w-3 mr-1" />
            Picker
          </TabsTrigger>
          <TabsTrigger value="settings" className="text-xs">
            <Settings className="h-3 w-3 mr-1" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="picker" className="space-y-3">
          {/* Icon selector */}
          <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  'w-full justify-between text-xs h-8',
                  hasError && 'border-destructive'
                )}
                disabled={disabled || config.disabled}
              >
                <div className="flex items-center gap-2">
                  <IconComponent size={16} />
                  <span>{localValue.name}</span>
                </div>
                <Search className="h-3 w-3" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-0" align="start">
              <div className="p-3 border-b">
                <div className="flex items-center gap-2">
                  <Search className="h-3 w-3 text-muted-foreground" />
                  <Input
                    placeholder="Search icons..."
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    className="text-xs h-6 border-0 focus-visible:ring-0"
                  />
                </div>
              </div>

              {/* Category tabs */}
              <div className="p-2 border-b">
                <div className="flex flex-wrap gap-1">
                  {categories.map((category) => (
                    <Button
                      key={category.id}
                      variant={selectedCategory === category.id ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setSelectedCategory(category.id)}
                      className="text-xs h-6"
                    >
                      {category.label} ({category.count})
                    </Button>
                  ))}
                </div>
              </div>

              {/* Icon grid */}
              <div className="max-h-64 overflow-auto p-2">
                <div className="grid grid-cols-6 gap-1">
                  {filteredIcons.map((icon) => {
                    const IconComp = icon.component
                    return (
                      <Button
                        key={icon.name}
                        variant={localValue.name === icon.name ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => {
                          updateIcon({ name: icon.name })
                          setIsOpen(false)
                        }}
                        className="h-8 w-8 p-0 relative"
                        title={icon.name}
                      >
                        <IconComp size={16} />
                        {icon.popular && (
                          <div className="absolute -top-1 -right-1">
                            <Star className="h-2 w-2 text-yellow-500 fill-current" />
                          </div>
                        )}
                      </Button>
                    )
                  })}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </TabsContent>

        <TabsContent value="settings" className="space-y-3">
          {/* Icon size */}
          <div className="space-y-2">
            <Label className="text-xs">Size</Label>
            <div className="flex items-center gap-2">
              <Slider
                value={[localValue.size]}
                onValueChange={([size]) => updateIcon({ size })}
                min={12}
                max={64}
                step={2}
                className="flex-1"
              />
              <Badge variant="outline" className="text-xs h-4 px-1 min-w-[3rem]">
                {localValue.size}px
              </Badge>
            </div>
          </div>

          {/* Icon color */}
          <div className="space-y-2">
            <Label className="text-xs">Color</Label>
            <div className="flex gap-1">
              <Input
                type="color"
                value={localValue.color}
                onChange={(e) => updateIcon({ color: e.target.value })}
                className="w-8 h-6 p-0 border-0"
              />
              <Input
                type="text"
                value={localValue.color}
                onChange={(e) => updateIcon({ color: e.target.value })}
                className="text-xs h-6 flex-1"
              />
            </div>
          </div>

          {/* Stroke width */}
          <div className="space-y-2">
            <Label className="text-xs">Stroke Width</Label>
            <div className="flex items-center gap-2">
              <Slider
                value={[localValue.strokeWidth || 2]}
                onValueChange={([strokeWidth]) => updateIcon({ strokeWidth })}
                min={0.5}
                max={4}
                step={0.5}
                className="flex-1"
              />
              <Badge variant="outline" className="text-xs h-4 px-1 min-w-[2rem]">
                {localValue.strokeWidth}
              </Badge>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}