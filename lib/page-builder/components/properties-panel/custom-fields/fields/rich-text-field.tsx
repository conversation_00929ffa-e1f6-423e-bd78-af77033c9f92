'use client'

import React, { useEffect, useState, useRef } from 'react'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import {
  Type,
  RotateCcw,
  AlertTriangle,
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Link,
  Quote,
  Code,
  Eye,
  Edit3,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Heading1,
  Heading2,
  Heading3
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

interface RichTextValue {
  content: string
  format: 'html' | 'markdown' | 'text'
  wordCount: number
  characterCount: number
}

export function RichTextField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<RichTextValue>({
    ...(value as RichTextValue || {}),
    format: 'html',
    wordCount: 0,
    characterCount: 0,
    content: (value as RichTextValue)?.content || (value as string) || ''
  })
  const [activeTab, setActiveTab] = useState<'editor' | 'preview'>('editor')
  const [validationError, setValidationError] = useState<string | null>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    const content = (value as RichTextValue)?.content || (value as string) || ''
    const updated = {
      content,
      format: (value as RichTextValue)?.format || 'html',
      wordCount: countWords(content),
      characterCount: content.length
    }
    setLocalValue(updated)
  }, [value])

  useEffect(() => {
    const result = validateField(config, localValue.content)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const countWords = (text: string): number => {
    return text.trim() === '' ? 0 : text.trim().split(/\s+/).length
  }

  const updateContent = (content: string) => {
    const updated = {
      ...localValue,
      content,
      wordCount: countWords(content),
      characterCount: content.length
    }
    setLocalValue(updated)
    onChange(updated)
  }

  const handleReset = () => {
    const defaultContent = (config.defaultValue as RichTextValue)?.content ||
                          (config.defaultValue as string) || ''
    const defaultValue: RichTextValue = {
      content: defaultContent,
      format: 'html',
      wordCount: countWords(defaultContent),
      characterCount: defaultContent.length
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  const insertFormatting = (before: string, after: string = '') => {
    const textarea = textareaRef.current
    if (!textarea) return

    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = localValue.content.substring(start, end)
    const newContent =
      localValue.content.substring(0, start) +
      before + selectedText + after +
      localValue.content.substring(end)

    updateContent(newContent)

    // Restore cursor position
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(
        start + before.length,
        start + before.length + selectedText.length
      )
    }, 0)
  }

  const formatButtons = [
    { icon: Bold, label: 'Bold', before: '**', after: '**', shortcut: 'Ctrl+B' },
    { icon: Italic, label: 'Italic', before: '*', after: '*', shortcut: 'Ctrl+I' },
    { icon: Underline, label: 'Underline', before: '<u>', after: '</u>' },
    { icon: Code, label: 'Code', before: '`', after: '`' },
    { icon: Quote, label: 'Quote', before: '> ', after: '' },
    { icon: List, label: 'Bullet List', before: '- ', after: '' },
    { icon: ListOrdered, label: 'Numbered List', before: '1. ', after: '' },
    { icon: Link, label: 'Link', before: '[', after: '](url)' }
  ]

  const headingButtons = [
    { icon: Heading1, label: 'Heading 1', before: '# ', after: '' },
    { icon: Heading2, label: 'Heading 2', before: '## ', after: '' },
    { icon: Heading3, label: 'Heading 3', before: '### ', after: '' }
  ]

  const alignButtons = [
    { icon: AlignLeft, label: 'Align Left', before: '<div style="text-align: left;">', after: '</div>' },
    { icon: AlignCenter, label: 'Align Center', before: '<div style="text-align: center;">', after: '</div>' },
    { icon: AlignRight, label: 'Align Right', before: '<div style="text-align: right;">', after: '</div>' }
  ]

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  // Generate content preview
  const generatePreview = () => {
    if (localValue.format === 'html') {
      return (
        <div
          className="prose prose-sm max-w-none"
          dangerouslySetInnerHTML={{ __html: localValue.content }}
        />
      )
    } else if (localValue.format === 'markdown') {
      // Simple markdown preview (you can integrate a proper markdown parser)
      const htmlContent = localValue.content
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
        .replace(/\*(.*)\*/gim, '<em>$1</em>')
        .replace(/`(.*)`/gim, '<code>$1</code>')
        .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')
        .replace(/\n/gim, '<br>')

      return (
        <div
          className="prose prose-sm max-w-none"
          dangerouslySetInnerHTML={{ __html: htmlContent }}
        />
      )
    } else {
      return (
        <div className="whitespace-pre-wrap font-mono text-sm">
          {localValue.content}
        </div>
      )
    }
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Type className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        <div className="flex items-center gap-1">
          <Badge variant="outline" className="text-xs h-4 px-1">
            {localValue.wordCount} words
          </Badge>
          <Badge variant="outline" className="text-xs h-4 px-1">
            {localValue.characterCount} chars
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Rich text editor */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="editor" className="text-xs">
            <Edit3 className="h-3 w-3 mr-1" />
            Editor
          </TabsTrigger>
          <TabsTrigger value="preview" className="text-xs">
            <Eye className="h-3 w-3 mr-1" />
            Preview
          </TabsTrigger>
        </TabsList>

        <TabsContent value="editor" className="space-y-2">
          {/* Formatting toolbar */}
          <div className="border rounded-lg p-2 bg-muted/20">
            {/* Headings */}
            <div className="flex items-center gap-1 mb-2 pb-2 border-b">
              <span className="text-xs text-muted-foreground mr-2">Headings:</span>
              {headingButtons.map((button) => {
                const Icon = button.icon
                return (
                  <Button
                    key={button.label}
                    variant="ghost"
                    size="sm"
                    onClick={() => insertFormatting(button.before, button.after)}
                    className="h-6 w-6 p-0"
                    title={button.label}
                    disabled={disabled}
                  >
                    <Icon className="h-3 w-3" />
                  </Button>
                )
              })}
            </div>

            {/* Text formatting */}
            <div className="flex items-center gap-1 mb-2 pb-2 border-b">
              <span className="text-xs text-muted-foreground mr-2">Format:</span>
              {formatButtons.map((button) => {
                const Icon = button.icon
                return (
                  <Button
                    key={button.label}
                    variant="ghost"
                    size="sm"
                    onClick={() => insertFormatting(button.before, button.after)}
                    className="h-6 w-6 p-0"
                    title={`${button.label}${button.shortcut ? ` (${button.shortcut})` : ''}`}
                    disabled={disabled}
                  >
                    <Icon className="h-3 w-3" />
                  </Button>
                )
              })}
            </div>

            {/* Alignment */}
            <div className="flex items-center gap-1">
              <span className="text-xs text-muted-foreground mr-2">Align:</span>
              {alignButtons.map((button) => {
                const Icon = button.icon
                return (
                  <Button
                    key={button.label}
                    variant="ghost"
                    size="sm"
                    onClick={() => insertFormatting(button.before, button.after)}
                    className="h-6 w-6 p-0"
                    title={button.label}
                    disabled={disabled}
                  >
                    <Icon className="h-3 w-3" />
                  </Button>
                )
              })}
            </div>
          </div>

          {/* Text editor */}
          <Textarea
            ref={textareaRef}
            value={localValue.content}
            onChange={(e) => updateContent(e.target.value)}
            placeholder="Start typing your content..."
            className={cn(
              'min-h-[200px] font-mono text-sm resize-none',
              hasError && 'border-destructive'
            )}
            disabled={disabled}
          />
        </TabsContent>

        <TabsContent value="preview" className="space-y-2">
          <div className="border rounded-lg p-4 min-h-[200px] bg-background">
            {localValue.content ? (
              generatePreview()
            ) : (
              <p className="text-muted-foreground text-sm italic">
                No content to preview
              </p>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
