'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  GitBranch,
  RotateCcw,
  AlertTriangle,
  Plus,
  Trash2,
  Eye,
  EyeOff,
  Settings,
  ChevronDown,
  ChevronRight,
  Zap
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'
import { CustomFieldRenderer } from '../field-renderer'

interface ConditionalRule {
  id: string
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'is_empty' | 'is_not_empty'
  value: any
  logic: 'and' | 'or'
}

interface ConditionalValue {
  rules: ConditionalRule[]
  showWhen: 'all' | 'any'
  fields: any[]
  isVisible: boolean
}

const OPERATORS = [
  { value: 'equals', label: 'Equals', needsValue: true },
  { value: 'not_equals', label: 'Not Equals', needsValue: true },
  { value: 'contains', label: 'Contains', needsValue: true },
  { value: 'not_contains', label: 'Not Contains', needsValue: true },
  { value: 'greater_than', label: 'Greater Than', needsValue: true },
  { value: 'less_than', label: 'Less Than', needsValue: true },
  { value: 'is_empty', label: 'Is Empty', needsValue: false },
  { value: 'is_not_empty', label: 'Is Not Empty', needsValue: false }
]

export function ConditionalField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<ConditionalValue>({
    rules: [],
    showWhen: 'all',
    fields: [],
    isVisible: true,
    ...(value as ConditionalValue || {})
  })
  const [validationError, setValidationError] = useState<string | null>(null)
  const [previewMode, setPreviewMode] = useState(false)

  useEffect(() => {
    setLocalValue({
      rules: [],
      showWhen: 'all',
      fields: [],
      isVisible: true,
      ...(value as ConditionalValue || {})
    })
  }, [value])

  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const updateConditional = (updates: Partial<ConditionalValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(updated)
  }

  const handleReset = () => {
    const defaultValue: ConditionalValue = {
      rules: [],
      showWhen: 'all',
      fields: [],
      isVisible: true,
      ...(config.defaultValue as ConditionalValue || {})
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  const addRule = () => {
    const newRule: ConditionalRule = {
      id: Date.now().toString(),
      field: '',
      operator: 'equals',
      value: '',
      logic: 'and'
    }
    updateConditional({
      rules: [...localValue.rules, newRule]
    })
  }

  const removeRule = (ruleId: string) => {
    const newRules = localValue.rules.filter(rule => rule.id !== ruleId)
    updateConditional({ rules: newRules })
  }

  const updateRule = (ruleId: string, updates: Partial<ConditionalRule>) => {
    const newRules = localValue.rules.map(rule =>
      rule.id === ruleId ? { ...rule, ...updates } : rule
    )
    updateConditional({ rules: newRules })
  }

  const addField = () => {
    const newField = {
      id: `field_${Date.now()}`,
      type: 'text',
      label: 'New Field',
      value: ''
    }
    updateConditional({
      fields: [...localValue.fields, newField]
    })
  }

  const removeField = (index: number) => {
    const newFields = localValue.fields.filter((_, i) => i !== index)
    updateConditional({ fields: newFields })
  }

  const updateField = (index: number, updates: any) => {
    const newFields = [...localValue.fields]
    newFields[index] = { ...newFields[index], ...updates }
    updateConditional({ fields: newFields })
  }

  // Simulate condition evaluation for preview
  const evaluateConditions = (): boolean => {
    if (localValue.rules.length === 0) return true

    const results = localValue.rules.map(rule => {
      // This is a simplified evaluation - in a real implementation,
      // you'd evaluate against actual form data
      switch (rule.operator) {
        case 'equals':
          return rule.value === 'true' // Simplified for demo
        case 'not_equals':
          return rule.value !== 'true'
        case 'is_empty':
          return !rule.value || rule.value === ''
        case 'is_not_empty':
          return rule.value && rule.value !== ''
        default:
          return true
      }
    })

    return localValue.showWhen === 'all'
      ? results.every(Boolean)
      : results.some(Boolean)
  }

  const isConditionMet = evaluateConditions()
  const shouldShowFields = previewMode ? isConditionMet : true

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <GitBranch className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        <div className="flex items-center gap-1">
          <Badge variant={isConditionMet ? 'default' : 'secondary'} className="text-xs h-4 px-1">
            {isConditionMet ? 'Visible' : 'Hidden'}
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setPreviewMode(!previewMode)}
            className="h-5 w-5 p-0"
            title={previewMode ? 'Exit preview' : 'Preview conditions'}
          >
            {previewMode ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Conditional logic builder */}
      <div className="space-y-4">
        {/* Show/Hide Logic */}
        <div className="border rounded-lg p-3 bg-muted/20">
          <div className="flex items-center justify-between mb-3">
            <Label className="text-xs font-medium">Visibility Conditions</Label>
            <div className="flex items-center gap-2">
              <span className="text-xs text-muted-foreground">Show when:</span>
              <Select
                value={localValue.showWhen}
                onValueChange={(value) => updateConditional({ showWhen: value as 'all' | 'any' })}
                disabled={disabled}
              >
                <SelectTrigger className="w-20 h-6 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all" className="text-xs">All</SelectItem>
                  <SelectItem value="any" className="text-xs">Any</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-xs text-muted-foreground">conditions are met</span>
            </div>
          </div>

          {/* Rules */}
          <div className="space-y-2">
            {localValue.rules.length === 0 ? (
              <div className="text-center py-4">
                <Zap className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm font-medium mb-1">No conditions</p>
                <p className="text-xs text-muted-foreground mb-3">
                  Add conditions to control field visibility
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={addRule}
                  disabled={disabled}
                  className="text-xs"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add First Condition
                </Button>
              </div>
            ) : (
              localValue.rules.map((rule, index) => (
                <div key={rule.id} className="border rounded-lg p-3 bg-background space-y-2">
                  <div className="flex items-center gap-2">
                    {index > 0 && (
                      <Badge variant="outline" className="text-xs h-4 px-1">
                        {localValue.showWhen.toUpperCase()}
                      </Badge>
                    )}

                    {/* Field selector */}
                    <Input
                      value={rule.field}
                      onChange={(e) => updateRule(rule.id, { field: e.target.value })}
                      placeholder="Field name"
                      className="text-xs h-6 flex-1"
                      disabled={disabled}
                    />

                    {/* Operator selector */}
                    <Select
                      value={rule.operator}
                      onValueChange={(value) => updateRule(rule.id, { operator: value as any })}
                      disabled={disabled}
                    >
                      <SelectTrigger className="w-32 h-6 text-xs">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {OPERATORS.map((op) => (
                          <SelectItem key={op.value} value={op.value} className="text-xs">
                            {op.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    {/* Value input */}
                    {OPERATORS.find(op => op.value === rule.operator)?.needsValue && (
                      <Input
                        value={rule.value}
                        onChange={(e) => updateRule(rule.id, { value: e.target.value })}
                        placeholder="Value"
                        className="text-xs h-6 w-24"
                        disabled={disabled}
                      />
                    )}

                    {/* Remove button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeRule(rule.id)}
                      disabled={disabled}
                      className="h-6 w-6 p-0"
                      title="Remove condition"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))
            )}

            {/* Add rule button */}
            {localValue.rules.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={addRule}
                disabled={disabled}
                className="text-xs h-6 w-full"
              >
                <Plus className="h-3 w-3 mr-1" />
                Add Condition
              </Button>
            )}
          </div>
        </div>

        {/* Conditional Fields */}
        <div className="border rounded-lg p-3 bg-background">
          <div className="flex items-center justify-between mb-3">
            <Label className="text-xs font-medium">Conditional Fields</Label>
            <Button
              variant="outline"
              size="sm"
              onClick={addField}
              disabled={disabled}
              className="text-xs h-6"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add Field
            </Button>
          </div>

          {/* Fields that will be shown/hidden */}
          <div className={cn(
            'space-y-3 transition-all duration-200',
            !shouldShowFields && 'opacity-50 pointer-events-none'
          )}>
            {localValue.fields.length === 0 ? (
              <div className="text-center py-4 border-2 border-dashed rounded-lg">
                <Settings className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm font-medium mb-1">No conditional fields</p>
                <p className="text-xs text-muted-foreground mb-3">
                  Add fields that will be shown/hidden based on conditions
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={addField}
                  disabled={disabled}
                  className="text-xs"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add Field
                </Button>
              </div>
            ) : (
              localValue.fields.map((field, index) => (
                <div key={field.id || index} className="border rounded-lg p-3 space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 flex-1">
                      <Input
                        value={field.label || ''}
                        onChange={(e) => updateField(index, { label: e.target.value })}
                        placeholder="Field label"
                        className="text-xs h-6 flex-1"
                        disabled={disabled}
                      />

                      <Select
                        value={field.type || 'text'}
                        onValueChange={(value) => updateField(index, { type: value })}
                        disabled={disabled}
                      >
                        <SelectTrigger className="w-24 h-6 text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="text" className="text-xs">Text</SelectItem>
                          <SelectItem value="number" className="text-xs">Number</SelectItem>
                          <SelectItem value="email" className="text-xs">Email</SelectItem>
                          <SelectItem value="select" className="text-xs">Select</SelectItem>
                          <SelectItem value="checkbox" className="text-xs">Checkbox</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeField(index)}
                      disabled={disabled}
                      className="h-6 w-6 p-0"
                      title="Remove field"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>

                  {/* Field preview */}
                  <div className="pl-4 border-l-2 border-muted">
                    <div className="text-xs text-muted-foreground mb-1">Preview:</div>
                    <div className="space-y-1">
                      <Label className="text-xs">{field.label || 'Field Label'}</Label>
                      {field.type === 'text' && (
                        <Input
                          placeholder="Text input"
                          className="text-xs h-6"
                          disabled
                        />
                      )}
                      {field.type === 'number' && (
                        <Input
                          type="number"
                          placeholder="Number input"
                          className="text-xs h-6"
                          disabled
                        />
                      )}
                      {field.type === 'email' && (
                        <Input
                          type="email"
                          placeholder="Email input"
                          className="text-xs h-6"
                          disabled
                        />
                      )}
                      {field.type === 'select' && (
                        <Select disabled>
                          <SelectTrigger className="h-6 text-xs">
                            <SelectValue placeholder="Select option" />
                          </SelectTrigger>
                        </Select>
                      )}
                      {field.type === 'checkbox' && (
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" disabled className="rounded" />
                          <span className="text-xs">Checkbox option</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Preview mode indicator */}
          {previewMode && (
            <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2">
                <Eye className="h-4 w-4 text-blue-600" />
                <span className="text-xs font-medium text-blue-800">
                  Preview Mode: Fields are {isConditionMet ? 'visible' : 'hidden'} based on conditions
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
