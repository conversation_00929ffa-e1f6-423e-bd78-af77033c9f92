'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Slider } from '@/components/ui/slider'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  RotateCcw, 
  AlertTriangle,
  Plus,
  Trash2,
  RotateCw,
  Palette,
  Zap,
  Circle,
  Square
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

interface GradientStop {
  color: string
  position: number
}

interface GradientValue {
  type: 'linear' | 'radial'
  angle: number
  stops: GradientStop[]
}

export function GradientField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<GradientValue>({
    ...(value as GradientValue || {}),
    type: (value as GradientValue)?.type || 'linear',
    angle: (value as GradientValue)?.angle || 0,
    stops: (value as GradientValue)?.stops || [
      { color: '#000000', position: 0 },
      { color: '#ffffff', position: 100 }
    ]
  })
  const [selectedStop, setSelectedStop] = useState<number>(0)
  const [isDragging, setIsDragging] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)
  const gradientRef = useRef<HTMLDivElement>(null)

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue({
      ...(value as GradientValue || {}),
      type: (value as GradientValue)?.type || 'linear',
      angle: (value as GradientValue)?.angle || 0,
      stops: (value as GradientValue)?.stops || [
        { color: '#000000', position: 0 },
        { color: '#ffffff', position: 100 }
      ]
    })
  }, [value])

  // Validate field on value change
  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const updateGradient = (updates: Partial<GradientValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(updated)
  }

  const addStop = () => {
    const newPosition = 50 // Add in the middle
    const newStop: GradientStop = {
      color: '#808080',
      position: newPosition
    }
    
    const newStops = [...localValue.stops, newStop].sort((a, b) => a.position - b.position)
    updateGradient({ stops: newStops })
    setSelectedStop(newStops.findIndex(stop => stop === newStop))
  }

  const removeStop = (index: number) => {
    if (localValue.stops.length <= 2) return // Keep at least 2 stops
    
    const newStops = localValue.stops.filter((_, i) => i !== index)
    updateGradient({ stops: newStops })
    setSelectedStop(Math.max(0, Math.min(selectedStop, newStops.length - 1)))
  }

  const updateStop = (index: number, updates: Partial<GradientStop>) => {
    const newStops = localValue.stops.map((stop, i) => 
      i === index ? { ...stop, ...updates } : stop
    )
    updateGradient({ stops: newStops })
  }

  const handleStopDrag = (e: React.MouseEvent, index: number) => {
    if (!gradientRef.current) return
    
    const rect = gradientRef.current.getBoundingClientRect()
    const x = e.clientX - rect.left
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))
    
    updateStop(index, { position: percentage })
  }

  const generateGradientCSS = () => {
    const stops = localValue.stops
      .sort((a, b) => a.position - b.position)
      .map(stop => `${stop.color} ${stop.position}%`)
      .join(', ')

    if (localValue.type === 'linear') {
      return `linear-gradient(${localValue.angle}deg, ${stops})`
    } else {
      return `radial-gradient(circle, ${stops})`
    }
  }

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  // Predefined gradient presets
  const presets = config.presets || [
    {
      label: 'Sunset',
      value: {
        type: 'linear',
        angle: 45,
        stops: [
          { color: '#ff7e5f', position: 0 },
          { color: '#feb47b', position: 100 }
        ]
      }
    },
    {
      label: 'Ocean',
      value: {
        type: 'linear',
        angle: 180,
        stops: [
          { color: '#667eea', position: 0 },
          { color: '#764ba2', position: 100 }
        ]
      }
    },
    {
      label: 'Forest',
      value: {
        type: 'radial',
        angle: 0,
        stops: [
          { color: '#134e5e', position: 0 },
          { color: '#71b280', position: 100 }
        ]
      }
    }
  ]

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Palette className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => updateGradient({
            type: 'linear',
            angle: 0,
            stops: [
              { color: '#000000', position: 0 },
              { color: '#ffffff', position: 100 }
            ]
          })}
          className="h-5 w-5 p-0"
          title="Reset to default"
        >
          <RotateCcw className="h-3 w-3" />
        </Button>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Gradient preview */}
      <div className="space-y-2">
        <div
          className="w-full h-16 rounded-lg border-2 border-muted relative overflow-hidden"
          style={{ background: generateGradientCSS() }}
        >
          {/* Gradient overlay pattern for transparency */}
          <div 
            className="absolute inset-0 opacity-20"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='%23000' fill-opacity='0.1'%3e%3cpath d='M0 0h10v10H0zM10 10h10v10H10z'/%3e%3c/g%3e%3c/svg%3e")`
            }}
          />
          
          {/* CSS output */}
          <div className="absolute bottom-1 left-1 right-1">
            <div className="bg-black/80 text-white text-xs p-1 rounded font-mono truncate">
              {generateGradientCSS()}
            </div>
          </div>
        </div>

        {/* Gradient stops editor */}
        <div
          ref={gradientRef}
          className="relative h-8 bg-gradient-to-r from-gray-200 to-gray-300 rounded cursor-pointer"
          style={{ background: generateGradientCSS() }}
          onMouseMove={(e) => isDragging && handleStopDrag(e, selectedStop)}
          onMouseUp={() => setIsDragging(false)}
        >
          {localValue.stops.map((stop, index) => (
            <div
              key={index}
              className={cn(
                'absolute top-0 w-4 h-8 cursor-grab active:cursor-grabbing transform -translate-x-1/2',
                selectedStop === index && 'z-10'
              )}
              style={{ left: `${stop.position}%` }}
              onMouseDown={(e) => {
                setSelectedStop(index)
                setIsDragging(true)
                handleStopDrag(e, index)
              }}
            >
              <div
                className={cn(
                  'w-full h-full rounded border-2 shadow-md transition-all',
                  selectedStop === index 
                    ? 'border-white scale-110' 
                    : 'border-gray-400 hover:border-white'
                )}
                style={{ backgroundColor: stop.color }}
              />
              
              {/* Position indicator */}
              <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground">
                {Math.round(stop.position)}%
              </div>
            </div>
          ))}
        </div>
      </div>

      <Tabs defaultValue="stops" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="stops" className="text-xs">
            <Circle className="h-3 w-3 mr-1" />
            Stops
          </TabsTrigger>
          <TabsTrigger value="settings" className="text-xs">
            <Zap className="h-3 w-3 mr-1" />
            Settings
          </TabsTrigger>
          <TabsTrigger value="presets" className="text-xs">
            <Square className="h-3 w-3 mr-1" />
            Presets
          </TabsTrigger>
        </TabsList>

        <TabsContent value="stops" className="space-y-3">
          {/* Selected stop controls */}
          {localValue.stops[selectedStop] && (
            <div className="space-y-2 p-3 border rounded-lg">
              <div className="flex items-center justify-between">
                <Label className="text-xs">Stop {selectedStop + 1}</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeStop(selectedStop)}
                  disabled={localValue.stops.length <= 2}
                  className="h-5 w-5 p-0 text-destructive"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Color</Label>
                  <div className="flex gap-1">
                    <Input
                      type="color"
                      value={localValue.stops[selectedStop].color}
                      onChange={(e) => updateStop(selectedStop, { color: e.target.value })}
                      className="w-8 h-6 p-0 border-0"
                    />
                    <Input
                      type="text"
                      value={localValue.stops[selectedStop].color}
                      onChange={(e) => updateStop(selectedStop, { color: e.target.value })}
                      className="text-xs h-6 flex-1"
                    />
                  </div>
                </div>
                
                <div>
                  <Label className="text-xs">Position (%)</Label>
                  <Input
                    type="number"
                    value={localValue.stops[selectedStop].position}
                    onChange={(e) => updateStop(selectedStop, { position: Number(e.target.value) })}
                    min={0}
                    max={100}
                    className="text-xs h-6"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Add stop button */}
          <Button
            variant="outline"
            size="sm"
            onClick={addStop}
            className="w-full text-xs"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Color Stop
          </Button>
        </TabsContent>

        <TabsContent value="settings" className="space-y-3">
          {/* Gradient type */}
          <div className="space-y-2">
            <Label className="text-xs">Type</Label>
            <div className="flex gap-2">
              <Button
                variant={localValue.type === 'linear' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateGradient({ type: 'linear' })}
                className="flex-1 text-xs"
              >
                Linear
              </Button>
              <Button
                variant={localValue.type === 'radial' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateGradient({ type: 'radial' })}
                className="flex-1 text-xs"
              >
                Radial
              </Button>
            </div>
          </div>

          {/* Angle (for linear gradients) */}
          {localValue.type === 'linear' && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-xs">Angle</Label>
                <div className="flex items-center gap-1">
                  <span className="text-xs text-muted-foreground">{localValue.angle}°</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => updateGradient({ angle: (localValue.angle + 45) % 360 })}
                    className="h-5 w-5 p-0"
                  >
                    <RotateCw className="h-3 w-3" />
                  </Button>
                </div>
              </div>
              <Slider
                value={[localValue.angle]}
                onValueChange={([angle]) => updateGradient({ angle })}
                min={0}
                max={360}
                step={1}
                className="w-full"
              />
            </div>
          )}
        </TabsContent>

        <TabsContent value="presets" className="space-y-3">
          <div className="grid grid-cols-1 gap-2">
            {presets.map((preset: any, index: number) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => updateGradient(preset.value)}
                className="h-12 p-2 text-xs justify-start"
              >
                <div
                  className="w-8 h-8 rounded border mr-2"
                  style={{
                    background: preset.value.type === 'linear'
                      ? `linear-gradient(${preset.value.angle}deg, ${preset.value.stops.map((s: any) => `${s.color} ${s.position}%`).join(', ')})`
                      : `radial-gradient(circle, ${preset.value.stops.map((s: any) => `${s.color} ${s.position}%`).join(', ')})`
                  }}
                />
                {preset.label}
              </Button>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
