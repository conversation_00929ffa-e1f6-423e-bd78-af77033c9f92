'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Slider } from '@/components/ui/slider'
import {
  Type,
  RotateCcw,
  AlertTriangle,
  Search,
  Star,
  Palette,
  Settings
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

interface FontValue {
  family: string
  weight: string
  style: string
  size: string
  lineHeight: string
  letterSpacing: string
}

export function FontField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<FontValue>({
    ...(value as FontValue || {}),
    family: (value as FontValue)?.family || 'Inter',
    weight: (value as FontValue)?.weight || '400',
    style: (value as FontValue)?.style || 'normal',
    size: (value as FontValue)?.size || '16px',
    lineHeight: (value as FontValue)?.lineHeight || '1.5',
    letterSpacing: (value as FontValue)?.letterSpacing || '0px'
  })
  const [isOpen, setIsOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [previewText, setPreviewText] = useState('The quick brown fox jumps over the lazy dog')
  const [validationError, setValidationError] = useState<string | null>(null)

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue({
      ...(value as FontValue || {}),
      family: (value as FontValue)?.family || 'Inter',
      weight: (value as FontValue)?.weight || '400',
      style: (value as FontValue)?.style || 'normal',
      size: (value as FontValue)?.size || '16px',
      lineHeight: (value as FontValue)?.lineHeight || '1.5',
      letterSpacing: (value as FontValue)?.letterSpacing || '0px'
    })
  }, [value])

  // Validate field on value change
  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const updateFont = (updates: Partial<FontValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(updated)
  }

  const handleReset = () => {
    const defaultValue: FontValue = {
      ...(config.defaultValue as FontValue || {}),
      family: (config.defaultValue as FontValue)?.family || 'Inter',
      weight: (config.defaultValue as FontValue)?.weight || '400',
      style: (config.defaultValue as FontValue)?.style || 'normal',
      size: (config.defaultValue as FontValue)?.size || '16px',
      lineHeight: (config.defaultValue as FontValue)?.lineHeight || '1.5',
      letterSpacing: (config.defaultValue as FontValue)?.letterSpacing || '0px'
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  // Font families with categories
  const fontFamilies = config.families || [
    { name: 'Inter', category: 'Sans Serif', popular: true },
    { name: 'P22 Underground', category: 'Sans Serif', popular: true },
    { name: 'Roboto', category: 'Sans Serif', popular: true },
    { name: 'Open Sans', category: 'Sans Serif', popular: false },
    { name: 'Lato', category: 'Sans Serif', popular: false },
    { name: 'Montserrat', category: 'Sans Serif', popular: false },
    { name: 'Playfair Display', category: 'Serif', popular: true },
    { name: 'Merriweather', category: 'Serif', popular: false },
    { name: 'Georgia', category: 'Serif', popular: false },
    { name: 'Times New Roman', category: 'Serif', popular: false },
    { name: 'Fira Code', category: 'Monospace', popular: true },
    { name: 'Source Code Pro', category: 'Monospace', popular: false },
    { name: 'Monaco', category: 'Monospace', popular: false },
    { name: 'Courier New', category: 'Monospace', popular: false }
  ]

  const fontWeights = config.weights || [
    { value: '100', label: 'Thin' },
    { value: '200', label: 'Extra Light' },
    { value: '300', label: 'Light' },
    { value: '400', label: 'Regular' },
    { value: '500', label: 'Medium' },
    { value: '600', label: 'Semi Bold' },
    { value: '700', label: 'Bold' },
    { value: '800', label: 'Extra Bold' },
    { value: '900', label: 'Black' }
  ]

  const fontStyles = config.styles || [
    { value: 'normal', label: 'Normal' },
    { value: 'italic', label: 'Italic' },
    { value: 'oblique', label: 'Oblique' }
  ]

  const filteredFonts = searchValue
    ? fontFamilies.filter((font: any) =>
        font.name.toLowerCase().includes(searchValue.toLowerCase()) ||
        font.category.toLowerCase().includes(searchValue.toLowerCase())
      )
    : fontFamilies

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  // Generate font preview SVG
  const generateFontPreview = (fontFamily: string) => {
    return (
      <svg width="120" height="40" className="border rounded">
        <defs>
          <linearGradient id={`text-gradient-${fontFamily.replace(/\s+/g, '')}`} x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#3b82f6" />
            <stop offset="100%" stopColor="#8b5cf6" />
          </linearGradient>
        </defs>

        {/* Background */}
        <rect width="120" height="40" fill="#f8fafc" stroke="#e2e8f0" strokeWidth="1" rx="4" />

        {/* Sample text */}
        <text
          x="60"
          y="25"
          textAnchor="middle"
          fontFamily={fontFamily}
          fontSize="14"
          fill={`url(#text-gradient-${fontFamily.replace(/\s+/g, '')})`}
          fontWeight={localValue.weight}
          fontStyle={localValue.style}
        >
          Aa
        </text>

        {/* Font name */}
        <text
          x="60"
          y="35"
          textAnchor="middle"
          fontSize="8"
          fill="#64748b"
          fontFamily="system-ui"
        >
          {fontFamily.length > 12 ? fontFamily.substring(0, 12) + '...' : fontFamily}
        </text>
      </svg>
    )
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Type className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleReset}
          className="h-5 w-5 p-0"
          title="Reset to default"
        >
          <RotateCcw className="h-3 w-3" />
        </Button>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Font preview */}
      <div className="space-y-2">
        <div
          className="w-full p-4 border rounded-lg bg-gradient-to-br from-slate-50 to-slate-100"
          style={{
            fontFamily: localValue.family,
            fontWeight: localValue.weight,
            fontStyle: localValue.style,
            fontSize: localValue.size,
            lineHeight: localValue.lineHeight,
            letterSpacing: localValue.letterSpacing
          }}
        >
          <div className="text-center">
            <div className="text-lg mb-2">{previewText}</div>
            <div className="text-xs text-muted-foreground">
              {localValue.family} • {localValue.weight} • {localValue.size}
            </div>
          </div>
        </div>

        {/* Preview text input */}
        <Input
          type="text"
          value={previewText}
          onChange={(e) => setPreviewText(e.target.value)}
          placeholder="Enter preview text..."
          className="text-xs"
        />
      </div>

      {/* Font controls */}
      <Tabs defaultValue="family" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="family" className="text-xs">
            <Type className="h-3 w-3 mr-1" />
            Family
          </TabsTrigger>
          <TabsTrigger value="style" className="text-xs">
            <Palette className="h-3 w-3 mr-1" />
            Style
          </TabsTrigger>
          <TabsTrigger value="spacing" className="text-xs">
            <Settings className="h-3 w-3 mr-1" />
            Spacing
          </TabsTrigger>
        </TabsList>

        <TabsContent value="family" className="space-y-3">
          {/* Font family selector */}
          <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  'w-full justify-between text-xs h-8',
                  hasError && 'border-destructive'
                )}
                disabled={disabled || config.disabled}
              >
                <div className="flex items-center gap-2">
                  <Type className="h-3 w-3" />
                  <span>{localValue.family}</span>
                </div>
                <Search className="h-3 w-3" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-0" align="start">
              <div className="p-3 border-b">
                <div className="flex items-center gap-2">
                  <Search className="h-3 w-3 text-muted-foreground" />
                  <Input
                    placeholder="Search fonts..."
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    className="text-xs h-6 border-0 focus-visible:ring-0"
                  />
                </div>
              </div>

              <div className="max-h-64 overflow-auto p-2">
                {/* Popular fonts */}
                {!searchValue && (
                  <div className="mb-4">
                    <div className="flex items-center gap-1 mb-2">
                      <Star className="h-3 w-3 text-yellow-500" />
                      <span className="text-xs font-medium">Popular</span>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {fontFamilies.filter((font: any) => font.popular).map((font: any) => (
                        <Button
                          key={font.name}
                          variant={localValue.family === font.name ? 'default' : 'ghost'}
                          size="sm"
                          onClick={() => {
                            updateFont({ family: font.name })
                            setIsOpen(false)
                          }}
                          className="h-12 p-2 flex flex-col items-center justify-center"
                        >
                          {generateFontPreview(font.name)}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}

                {/* All fonts */}
                <div className="space-y-1">
                  {filteredFonts.map((font: any) => (
                    <Button
                      key={font.name}
                      variant={localValue.family === font.name ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => {
                        updateFont({ family: font.name })
                        setIsOpen(false)
                      }}
                      className="w-full justify-start text-xs h-8"
                    >
                      <div className="flex items-center gap-2 w-full">
                        <div className="w-8 h-6 border rounded text-xs flex items-center justify-center" style={{ fontFamily: font.name }}>
                          Aa
                        </div>
                        <div className="flex-1 text-left">
                          <div className="font-medium">{font.name}</div>
                          <div className="text-xs text-muted-foreground">{font.category}</div>
                        </div>
                        {font.popular && <Star className="h-3 w-3 text-yellow-500" />}
                      </div>
                    </Button>
                  ))}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </TabsContent>

        <TabsContent value="style" className="space-y-3">
          {/* Font weight */}
          <div className="space-y-2">
            <Label className="text-xs">Weight</Label>
            <div className="grid grid-cols-3 gap-1">
              {fontWeights.map((weight: any) => (
                <Button
                  key={weight.value}
                  variant={localValue.weight === weight.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateFont({ weight: weight.value })}
                  className="text-xs h-6"
                >
                  {weight.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Font style */}
          <div className="space-y-2">
            <Label className="text-xs">Style</Label>
            <div className="flex gap-1">
              {fontStyles.map((style: any) => (
                <Button
                  key={style.value}
                  variant={localValue.style === style.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateFont({ style: style.value })}
                  className="text-xs h-6 flex-1"
                >
                  {style.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Font size */}
          <div className="space-y-2">
            <Label className="text-xs">Size</Label>
            <Input
              type="text"
              value={localValue.size}
              onChange={(e) => updateFont({ size: e.target.value })}
              placeholder="16px, 1rem"
              className="text-xs h-6"
            />
          </div>
        </TabsContent>

        <TabsContent value="spacing" className="space-y-3">
          {/* Line height */}
          <div className="space-y-2">
            <Label className="text-xs">Line Height</Label>
            <div className="flex items-center gap-2">
              <Slider
                value={[parseFloat(localValue.lineHeight) || 1.5]}
                onValueChange={([value]) => updateFont({ lineHeight: value.toString() })}
                min={1}
                max={3}
                step={0.1}
                className="flex-1"
              />
              <Badge variant="outline" className="text-xs h-4 px-1 min-w-[3rem]">
                {localValue.lineHeight}
              </Badge>
            </div>
          </div>

          {/* Letter spacing */}
          <div className="space-y-2">
            <Label className="text-xs">Letter Spacing</Label>
            <div className="flex items-center gap-2">
              <Slider
                value={[parseFloat(localValue.letterSpacing) || 0]}
                onValueChange={([value]) => updateFont({ letterSpacing: `${value}px` })}
                min={-2}
                max={5}
                step={0.1}
                className="flex-1"
              />
              <Badge variant="outline" className="text-xs h-4 px-1 min-w-[3rem]">
                {localValue.letterSpacing}
              </Badge>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
