'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Settings,
  Type,
  Palette,
  Layout,
  Zap,
  ChevronRight,
  X
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { CustomFieldRenderer } from './custom-fields'
import { FieldConfig, FieldValue } from './custom-fields/types'

interface CompactBlockSettingsProps {
  block: any
  blockType: any
  onUpdate: (updates: any) => void
  trigger?: React.ReactNode
  align?: 'start' | 'center' | 'end'
  side?: 'top' | 'right' | 'bottom' | 'left'
  className?: string
}

export function CompactBlockSettings({
  block,
  blockType,
  onUpdate,
  trigger,
  align = 'start',
  side = 'bottom',
  className
}: CompactBlockSettingsProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('content')
  const [fieldErrors, setFieldErrors] = useState<Record<string, string[]>>({})

  // Safety checks
  if (!block || !blockType || !onUpdate || !blockType.configSchema) {
    return null
  }

  // Convert schema to field configurations
  const fieldConfigs = convertSchemaToFieldConfigs(blockType.configSchema)
  
  // Organize fields into sections
  const sections = organizeFieldsIntoSections(fieldConfigs)

  // Handle field value changes
  const handleFieldChange = (fieldId: string, value: FieldValue) => {
    if (onUpdate) {
      onUpdate({ [fieldId]: value })
    }
  }

  // Handle field validation
  const handleFieldValidation = (fieldId: string, isValid: boolean, message?: string) => {
    setFieldErrors(prev => ({
      ...prev,
      [fieldId]: isValid ? [] : [message || 'Invalid value']
    }))
  }

  const defaultTrigger = (
    <Button
      variant="ghost"
      size="sm"
      className="h-8 w-8 p-0 hover:bg-blue-50"
    >
      <Settings className="h-4 w-4" />
    </Button>
  )

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        {trigger || defaultTrigger}
      </PopoverTrigger>
      <PopoverContent 
        className={cn("w-80 p-0", className)}
        align={align}
        side={side}
        sideOffset={8}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-gray-50">
          <div className="flex items-center gap-2">
            <span className="text-lg">{blockType.icon}</span>
            <div>
              <h3 className="font-semibold text-sm">{blockType.displayName}</h3>
              {blockType.description && (
                <p className="text-xs text-muted-foreground">
                  {blockType.description}
                </p>
              )}
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(false)}
            className="h-6 w-6 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Settings Content */}
        {sections.length > 1 ? (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 m-2">
              {sections.map((section) => (
                <TabsTrigger 
                  key={section.id} 
                  value={section.id} 
                  className="text-xs px-2"
                >
                  <section.icon className="h-3 w-3 mr-1" />
                  {section.title}
                </TabsTrigger>
              ))}
            </TabsList>
            
            <ScrollArea className="h-80">
              {sections.map((section) => (
                <TabsContent key={section.id} value={section.id} className="p-4 pt-0">
                  <CompactFieldSection
                    section={section}
                    values={block.configuration || {}}
                    onChange={handleFieldChange}
                    onValidate={handleFieldValidation}
                    errors={fieldErrors}
                  />
                </TabsContent>
              ))}
            </ScrollArea>
          </Tabs>
        ) : sections.length === 1 ? (
          <ScrollArea className="h-80">
            <div className="p-4">
              <CompactFieldSection
                section={sections[0]}
                values={block.configuration || {}}
                onChange={handleFieldChange}
                onValidate={handleFieldValidation}
                errors={fieldErrors}
              />
            </div>
          </ScrollArea>
        ) : (
          <div className="p-4 text-center text-sm text-muted-foreground">
            No settings available for this block.
          </div>
        )}
      </PopoverContent>
    </Popover>
  )
}

// Compact field section component
interface CompactFieldSectionProps {
  section: any
  values: any
  onChange: (fieldId: string, value: FieldValue) => void
  onValidate: (fieldId: string, isValid: boolean, message?: string) => void
  errors: Record<string, string[]>
}

function CompactFieldSection({ 
  section, 
  values, 
  onChange, 
  onValidate, 
  errors 
}: CompactFieldSectionProps) {
  return (
    <div className="space-y-3">
      {section.description && (
        <p className="text-xs text-muted-foreground">{section.description}</p>
      )}
      
      {section.fields.map((fieldConfig: FieldConfig) => (
        <div key={fieldConfig.id} className="space-y-1">
          <label className="text-xs font-medium text-gray-700 block">
            {fieldConfig.label}
            {fieldConfig.required && <span className="text-red-500 ml-1">*</span>}
          </label>
          
          {fieldConfig.description && (
            <p className="text-xs text-gray-500">{fieldConfig.description}</p>
          )}
          
          <CustomFieldRenderer
            config={fieldConfig}
            value={values[fieldConfig.id]}
            onChange={(value) => onChange(fieldConfig.id, value)}
            onValidate={(isValid, message) => onValidate(fieldConfig.id, isValid, message)}
            errors={errors[fieldConfig.id]}
            className="w-full"
          />
          
          {errors[fieldConfig.id] && errors[fieldConfig.id].length > 0 && (
            <p className="text-xs text-red-500">{errors[fieldConfig.id][0]}</p>
          )}
        </div>
      ))}
    </div>
  )
}

// Inline Quick Settings for toolbar
interface InlineQuickSettingsProps {
  block: any
  blockType: any
  onUpdate: (updates: any) => void
  maxFields?: number
}

export function InlineQuickSettings({ 
  block, 
  blockType, 
  onUpdate, 
  maxFields = 3 
}: InlineQuickSettingsProps) {
  if (!blockType?.configSchema) {
    return null
  }

  const schema = blockType.configSchema
  const properties = schema.properties || {}
  
  // Get the most important fields for quick access
  const quickFields = getQuickAccessFields(properties, maxFields)

  if (quickFields.length === 0) {
    return null
  }

  return (
    <div className="flex items-center gap-2">
      {quickFields.map((fieldId) => {
        const property = properties[fieldId]
        const value = block.configuration?.[fieldId]
        
        return (
          <Popover key={fieldId}>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-xs hover:bg-blue-50"
              >
                {property.title || fieldId}
                <ChevronRight className="h-3 w-3 ml-1" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64 p-3" align="start">
              <div className="space-y-2">
                <label className="text-xs font-medium block">
                  {property.title || fieldId}
                </label>
                <QuickFieldInput
                  property={property}
                  value={value}
                  onChange={(newValue) => onUpdate({ [fieldId]: newValue })}
                />
              </div>
            </PopoverContent>
          </Popover>
        )
      })}
    </div>
  )
}

// Quick field input component
function QuickFieldInput({ 
  property, 
  value, 
  onChange 
}: { 
  property: any
  value: any
  onChange: (value: any) => void
}) {
  if (property.type === 'boolean') {
    return (
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          checked={value || false}
          onChange={(e) => onChange(e.target.checked)}
          className="rounded border-gray-300"
        />
        <span className="text-xs text-gray-600">{property.description}</span>
      </div>
    )
  }
  
  if (property.enum) {
    return (
      <select
        value={value || property.default || ''}
        onChange={(e) => onChange(e.target.value)}
        className="w-full px-2 py-1 text-xs border rounded"
      >
        {property.enum.map((option: string) => (
          <option key={option} value={option}>
            {option.charAt(0).toUpperCase() + option.slice(1)}
          </option>
        ))}
      </select>
    )
  }
  
  if (property['x-component'] === 'color-picker') {
    return (
      <div className="flex gap-2">
        <input
          type="color"
          value={value || '#000000'}
          onChange={(e) => onChange(e.target.value)}
          className="h-8 w-16 rounded border"
        />
        <input
          type="text"
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder="#000000"
          className="flex-1 px-2 py-1 text-xs border rounded"
        />
      </div>
    )
  }
  
  return (
    <input
      type={property.type === 'number' ? 'number' : 'text'}
      value={value || ''}
      onChange={(e) => onChange(
        property.type === 'number' ? Number(e.target.value) : e.target.value
      )}
      placeholder={property.placeholder}
      className="w-full px-2 py-1 text-xs border rounded"
    />
  )
}

// Helper functions (simplified versions from dynamic-block-editor)
function convertSchemaToFieldConfigs(schema: any): FieldConfig[] {
  // Implementation similar to dynamic-block-editor
  const properties = schema.properties || {}
  const fieldConfigs: FieldConfig[] = []

  Object.entries(properties).forEach(([key, property]: [string, any]) => {
    fieldConfigs.push({
      id: key,
      type: getFieldType(property),
      label: property.title || key,
      description: property.description,
      required: property.required || false,
      defaultValue: property.default
    } as FieldConfig)
  })

  return fieldConfigs
}

function organizeFieldsIntoSections(fieldConfigs: FieldConfig[]) {
  // Implementation similar to dynamic-block-editor
  const sections = [
    { id: 'content', title: 'Content', icon: Type, fields: [] as FieldConfig[] },
    { id: 'style', title: 'Style', icon: Palette, fields: [] as FieldConfig[] },
    { id: 'layout', title: 'Layout', icon: Layout, fields: [] as FieldConfig[] },
    { id: 'behavior', title: 'Behavior', icon: Zap, fields: [] as FieldConfig[] }
  ]

  fieldConfigs.forEach(field => {
    // Simple categorization logic
    const label = field.label?.toLowerCase() || ''
    if (label.includes('color') || label.includes('style')) {
      sections[1].fields.push(field)
    } else if (label.includes('width') || label.includes('height') || label.includes('spacing')) {
      sections[2].fields.push(field)
    } else if (label.includes('animation') || label.includes('hover')) {
      sections[3].fields.push(field)
    } else {
      sections[0].fields.push(field)
    }
  })

  return sections.filter(section => section.fields.length > 0)
}

function getFieldType(property: any): string {
  if (property['x-component']) {
    const componentMap: Record<string, string> = {
      'color-picker': 'color',
      'rich-text': 'richtext',
      'media-field': 'media'
    }
    return componentMap[property['x-component']] || 'text'
  }
  
  if (property.enum) return 'select'
  if (property.type === 'boolean') return 'boolean'
  if (property.type === 'number') return 'number'
  return 'text'
}

function getQuickAccessFields(properties: any, maxFields: number): string[] {
  const priorityFields = ['content', 'text', 'title', 'color', 'backgroundColor', 'size', 'variant']
  const availableFields = Object.keys(properties)
  
  return priorityFields
    .filter(field => availableFields.includes(field))
    .slice(0, maxFields)
}
