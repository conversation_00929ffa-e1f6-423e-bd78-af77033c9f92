'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Play, Pause, RotateCcw } from 'lucide-react'

interface AnimationEditorProps {
  animation: any
  onUpdate: (updates: any) => void
}

export function AnimationEditor({ animation, onUpdate }: AnimationEditorProps) {
  const [isPreviewPlaying, setIsPreviewPlaying] = React.useState(false)

  const previewAnimation = () => {
    setIsPreviewPlaying(true)
    // Simulate animation preview
    setTimeout(() => setIsPreviewPlaying(false), animation.duration || 600)
  }

  const resetAnimation = () => {
    onUpdate({
      enabled: false,
      type: 'fade',
      direction: 'up',
      trigger: 'scroll',
      duration: 600,
      delay: 0,
      easing: 'ease-out',
      repeat: false
    })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-sm">Animation & Effects</CardTitle>
            <CardDescription className="text-xs">
              Add entrance animations and scroll effects
            </CardDescription>
          </div>
          {animation.enabled && (
            <Badge variant="secondary" className="text-xs">
              {animation.type} - {animation.trigger}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Enable Animation */}
        <div className="flex items-center justify-between">
          <div>
            <Label className="text-xs font-medium">Enable Animation</Label>
            <p className="text-xs text-muted-foreground">Add motion effects to this block</p>
          </div>
          <Switch
            checked={animation.enabled || false}
            onCheckedChange={(checked) => onUpdate({ enabled: checked })}
          />
        </div>

        {animation.enabled && (
          <div className="space-y-4 pt-4 border-t">
            {/* Animation Type */}
            <div>
              <Label className="text-xs">Animation Type</Label>
              <select
                value={animation.type || 'fade'}
                onChange={(e) => onUpdate({ type: e.target.value })}
                className="w-full mt-1 px-2 py-1 text-xs border rounded"
              >
                <option value="fade">Fade In</option>
                <option value="slide">Slide In</option>
                <option value="scale">Scale In</option>
                <option value="bounce">Bounce In</option>
                <option value="flip">Flip In</option>
                <option value="zoom">Zoom In</option>
                <option value="rotate">Rotate In</option>
                <option value="elastic">Elastic In</option>
              </select>
            </div>

            {/* Animation Direction (for slide) */}
            {animation.type === 'slide' && (
              <div>
                <Label className="text-xs">Direction</Label>
                <select
                  value={animation.direction || 'up'}
                  onChange={(e) => onUpdate({ direction: e.target.value })}
                  className="w-full mt-1 px-2 py-1 text-xs border rounded"
                >
                  <option value="up">From Bottom</option>
                  <option value="down">From Top</option>
                  <option value="left">From Right</option>
                  <option value="right">From Left</option>
                </select>
              </div>
            )}

            {/* Animation Trigger */}
            <div>
              <Label className="text-xs">Trigger</Label>
              <select
                value={animation.trigger || 'scroll'}
                onChange={(e) => onUpdate({ trigger: e.target.value })}
                className="w-full mt-1 px-2 py-1 text-xs border rounded"
              >
                <option value="scroll">On Scroll Into View</option>
                <option value="load">On Page Load</option>
                <option value="hover">On Hover</option>
                <option value="click">On Click</option>
                <option value="focus">On Focus</option>
              </select>
            </div>

            {/* Animation Settings */}
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">Duration (ms)</Label>
                <Input
                  type="number"
                  value={animation.duration || 600}
                  onChange={(e) => onUpdate({ duration: parseInt(e.target.value) })}
                  min="100"
                  max="3000"
                  step="100"
                  className="text-xs mt-1"
                />
              </div>
              <div>
                <Label className="text-xs">Delay (ms)</Label>
                <Input
                  type="number"
                  value={animation.delay || 0}
                  onChange={(e) => onUpdate({ delay: parseInt(e.target.value) })}
                  min="0"
                  max="2000"
                  step="100"
                  className="text-xs mt-1"
                />
              </div>
            </div>

            {/* Easing */}
            <div>
              <Label className="text-xs">Easing</Label>
              <select
                value={animation.easing || 'ease-out'}
                onChange={(e) => onUpdate({ easing: e.target.value })}
                className="w-full mt-1 px-2 py-1 text-xs border rounded"
              >
                <option value="ease">Ease</option>
                <option value="ease-in">Ease In</option>
                <option value="ease-out">Ease Out</option>
                <option value="ease-in-out">Ease In Out</option>
                <option value="linear">Linear</option>
                <option value="cubic-bezier(0.68, -0.55, 0.265, 1.55)">Bounce</option>
                <option value="cubic-bezier(0.175, 0.885, 0.32, 1.275)">Back</option>
              </select>
            </div>

            {/* Advanced Options */}
            <div className="space-y-3 pt-3 border-t">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-xs">Repeat Animation</Label>
                  <p className="text-xs text-muted-foreground">Play animation multiple times</p>
                </div>
                <Switch
                  checked={animation.repeat || false}
                  onCheckedChange={(checked) => onUpdate({ repeat: checked })}
                />
              </div>

              {animation.repeat && (
                <div>
                  <Label className="text-xs">Repeat Count</Label>
                  <select
                    value={animation.repeatCount || 'infinite'}
                    onChange={(e) => onUpdate({ repeatCount: e.target.value })}
                    className="w-full mt-1 px-2 py-1 text-xs border rounded"
                  >
                    <option value="infinite">Infinite</option>
                    <option value="1">1 time</option>
                    <option value="2">2 times</option>
                    <option value="3">3 times</option>
                    <option value="5">5 times</option>
                  </select>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-xs">Alternate Direction</Label>
                  <p className="text-xs text-muted-foreground">Reverse animation on alternate cycles</p>
                </div>
                <Switch
                  checked={animation.alternate || false}
                  onCheckedChange={(checked) => onUpdate({ alternate: checked })}
                />
              </div>
            </div>

            {/* Scroll Animation Options */}
            {animation.trigger === 'scroll' && (
              <div className="space-y-3 pt-3 border-t">
                <Label className="text-xs font-medium">Scroll Options</Label>
                
                <div>
                  <Label className="text-xs">Trigger Offset</Label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Input
                      type="number"
                      value={animation.scrollOffset || 0}
                      onChange={(e) => onUpdate({ scrollOffset: parseInt(e.target.value) })}
                      min="0"
                      max="100"
                      className="text-xs flex-1"
                    />
                    <span className="text-xs text-muted-foreground">% from bottom</span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-xs">Once Only</Label>
                    <p className="text-xs text-muted-foreground">Animate only once when scrolled into view</p>
                  </div>
                  <Switch
                    checked={animation.once || true}
                    onCheckedChange={(checked) => onUpdate({ once: checked })}
                  />
                </div>
              </div>
            )}

            {/* Preview Controls */}
            <div className="flex items-center space-x-2 pt-3 border-t">
              <Button
                variant="outline"
                size="sm"
                onClick={previewAnimation}
                disabled={isPreviewPlaying}
                className="text-xs"
              >
                {isPreviewPlaying ? (
                  <Pause className="h-3 w-3 mr-1" />
                ) : (
                  <Play className="h-3 w-3 mr-1" />
                )}
                {isPreviewPlaying ? 'Playing...' : 'Preview'}
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={resetAnimation}
                className="text-xs"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Reset
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
