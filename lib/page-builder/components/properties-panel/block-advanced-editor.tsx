'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'

import { AnimationEditor } from './advanced-editors/animation-editor'
import { AccessibilityEditor } from './advanced-editors/accessibility-editor'
import { DisplayConditionsEditor } from './advanced-editors/display-conditions-editor'
import { CustomCSSEditor } from './advanced-editors/custom-css-editor'
import { CustomAttributesEditor } from './advanced-editors/custom-attributes-editor'

interface BlockAdvancedEditorProps {
  block: any
  onUpdate: (updates: any) => void
}

export function BlockAdvancedEditor({ block, onUpdate }: BlockAdvancedEditorProps) {
  const animation = block.animation || {}
  const conditions = block.conditions || {}

  const updateAnimation = (updates: any) => {
    onUpdate({
      animation: { ...animation, ...updates }
    })
  }

  const updateConditions = (updates: any) => {
    onUpdate({
      conditions: { ...conditions, ...updates }
    })
  }

  return (
    <div className="space-y-6">
      {/* Block Info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Block Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-xs">
          <div className="flex justify-between">
            <span className="text-muted-foreground">ID:</span>
            <span className="font-mono">{block.id}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Type:</span>
            <span>{block.type}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Position:</span>
            <span>{block.position + 1}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Created:</span>
            <span>{new Date(block.createdAt || Date.now()).toLocaleDateString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Updated:</span>
            <span>{new Date(block.updatedAt || Date.now()).toLocaleDateString()}</span>
          </div>
        </CardContent>
      </Card>

      {/* Animation Controls */}
      <AnimationEditor
        animation={animation}
        onUpdate={updateAnimation}
      />

      {/* Accessibility */}
      <AccessibilityEditor
        block={block}
        onUpdate={onUpdate}
      />

      {/* Display Conditions */}
      <DisplayConditionsEditor
        conditions={conditions}
        onUpdate={updateConditions}
      />

      {/* Custom CSS */}
      <CustomCSSEditor
        block={block}
        onUpdate={onUpdate}
      />

      {/* Custom Attributes */}
      <CustomAttributesEditor
        block={block}
        onUpdate={onUpdate}
      />
    </div>
  )
}
