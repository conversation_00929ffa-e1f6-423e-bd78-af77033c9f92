'use client'

import React, { useState, useCallback } from 'react'
import { usePageBuilder } from '../context'
import { GridLayoutBuilder } from './grid-layout-builder'
import { AILayoutGenerator } from './ai-layout-generator'
import { BlockLibrary } from './block-library'
import { PageBuilderCanvas } from './page-builder-canvas'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Save, 
  Eye, 
  EyeOff, 
  Undo, 
  Redo, 
  Settings, 
  Grid, 
  Layers, 
  Wand2, 
  Smartphone, 
  Tablet, 
  Monitor, 
  Tv,
  ArrowLeft,
  Download,
  Share,
  MoreHorizontal
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

interface EnhancedPageBuilderEditorProps {
  initialPage?: any
  onSave?: () => Promise<void>
  onPreview?: () => void
  onBack?: () => void
  className?: string
}

type EditorMode = 'visual' | 'grid' | 'code'
type SidebarTab = 'blocks' | 'ai' | 'settings' | 'layers'

export function EnhancedPageBuilderEditor({
  initialPage,
  onSave,
  onPreview,
  onBack,
  className
}: EnhancedPageBuilderEditorProps) {
  const {
    state,
    setPreviewMode,
    setDevicePreview,
    undo,
    redo,
    canUndo,
    canRedo,
    addBlock,
    updatePage
  } = usePageBuilder()

  const [editorMode, setEditorMode] = useState<EditorMode>('grid')
  const [sidebarTab, setSidebarTab] = useState<SidebarTab>('ai')
  const [isSidebarOpen, setIsSidebarOpen] = useState(true)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [devicePreview, setDevicePreview] = useState<'mobile' | 'tablet' | 'desktop' | 'large'>('desktop')
  const [isSaving, setIsSaving] = useState(false)

  // Handle save
  const handleSave = useCallback(async () => {
    if (!onSave) return
    
    setIsSaving(true)
    try {
      await onSave()
      toast.success('Page saved successfully!')
    } catch (error) {
      toast.error('Failed to save page')
      console.error('Save error:', error)
    } finally {
      setIsSaving(false)
    }
  }, [onSave])

  // Handle preview toggle
  const handlePreviewToggle = useCallback(() => {
    const newPreviewMode = !isPreviewMode
    setIsPreviewMode(newPreviewMode)
    setPreviewMode(newPreviewMode)
    
    if (newPreviewMode) {
      toast.success('Preview mode enabled')
    } else {
      toast.success('Edit mode enabled')
    }
  }, [isPreviewMode, setPreviewMode])

  // Handle device preview change
  const handleDeviceChange = useCallback((device: typeof devicePreview) => {
    setDevicePreview(device)
    setDevicePreview(device)
    toast.success(`Switched to ${device} preview`)
  }, [setDevicePreview])

  // Handle AI layout generation
  const handleLayoutGenerated = useCallback((layout: any) => {
    updatePage({
      ...state.page,
      blocks: layout.blocks || [],
      metadata: {
        ...state.page.metadata,
        ...layout.metadata
      }
    })
    toast.success('AI layout applied successfully!')
  }, [state.page, updatePage])

  // Get device icon
  const getDeviceIcon = (device: string) => {
    switch (device) {
      case 'mobile': return Smartphone
      case 'tablet': return Tablet
      case 'desktop': return Monitor
      case 'large': return Tv
      default: return Monitor
    }
  }

  return (
    <div className={cn('enhanced-page-builder-editor h-screen flex flex-col', className)}>
      {/* Header Toolbar */}
      <div className="flex items-center justify-between p-4 border-b bg-background">
        <div className="flex items-center space-x-4">
          {/* Back Button */}
          {onBack && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          )}

          {/* Page Title */}
          <div>
            <h1 className="text-lg font-semibold">{state.page.title || 'Untitled Page'}</h1>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <span>{state.page.blocks.length} blocks</span>
              <Separator orientation="vertical" className="h-4" />
              <Badge variant="outline">{editorMode} mode</Badge>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Editor Mode Toggle */}
          <div className="flex items-center space-x-1 border rounded-md p-1">
            <Button
              variant={editorMode === 'visual' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setEditorMode('visual')}
              className="h-8 px-3"
            >
              <Layers className="h-4 w-4 mr-1" />
              Visual
            </Button>
            <Button
              variant={editorMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setEditorMode('grid')}
              className="h-8 px-3"
            >
              <Grid className="h-4 w-4 mr-1" />
              Grid
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Device Preview */}
          <div className="flex items-center space-x-1 border rounded-md p-1">
            {(['mobile', 'tablet', 'desktop', 'large'] as const).map((device) => {
              const Icon = getDeviceIcon(device)
              return (
                <Button
                  key={device}
                  variant={devicePreview === device ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleDeviceChange(device)}
                  className="h-8 w-8 p-0"
                >
                  <Icon className="h-4 w-4" />
                </Button>
              )
            })}
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Undo/Redo */}
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={undo}
              disabled={!canUndo}
              className="h-8 w-8 p-0"
            >
              <Undo className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={redo}
              disabled={!canRedo}
              className="h-8 w-8 p-0"
            >
              <Redo className="h-4 w-4" />
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Preview Toggle */}
          <Button
            variant={isPreviewMode ? 'default' : 'ghost'}
            size="sm"
            onClick={handlePreviewToggle}
          >
            {isPreviewMode ? (
              <>
                <EyeOff className="h-4 w-4 mr-2" />
                Exit Preview
              </>
            ) : (
              <>
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </>
            )}
          </Button>

          {/* Save Button */}
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save
              </>
            )}
          </Button>

          {/* More Actions */}
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        {isSidebarOpen && !isPreviewMode && (
          <div className="w-80 border-r bg-background flex flex-col">
            <Tabs value={sidebarTab} onValueChange={setSidebarTab as any} className="flex-1 flex flex-col">
              <TabsList className="grid w-full grid-cols-4 m-4 mb-0">
                <TabsTrigger value="ai" className="flex items-center space-x-1">
                  <Wand2 className="h-3 w-3" />
                  <span className="hidden sm:inline">AI</span>
                </TabsTrigger>
                <TabsTrigger value="blocks" className="flex items-center space-x-1">
                  <Grid className="h-3 w-3" />
                  <span className="hidden sm:inline">Blocks</span>
                </TabsTrigger>
                <TabsTrigger value="layers" className="flex items-center space-x-1">
                  <Layers className="h-3 w-3" />
                  <span className="hidden sm:inline">Layers</span>
                </TabsTrigger>
                <TabsTrigger value="settings" className="flex items-center space-x-1">
                  <Settings className="h-3 w-3" />
                  <span className="hidden sm:inline">Settings</span>
                </TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-hidden">
                <TabsContent value="ai" className="h-full m-0 p-4">
                  <ScrollArea className="h-full">
                    <AILayoutGenerator onLayoutGenerated={handleLayoutGenerated} />
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="blocks" className="h-full m-0 p-4">
                  <ScrollArea className="h-full">
                    <BlockLibrary />
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="layers" className="h-full m-0 p-4">
                  <ScrollArea className="h-full">
                    <div className="space-y-2">
                      <h3 className="font-medium">Page Layers</h3>
                      {state.page.blocks.map((block, index) => (
                        <Card key={block.id} className="p-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-primary rounded-full" />
                              <span className="text-sm font-medium">{block.type}</span>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {index + 1}
                            </Badge>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="settings" className="h-full m-0 p-4">
                  <ScrollArea className="h-full">
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-medium mb-2">Page Settings</h3>
                        <div className="space-y-2">
                          <div>
                            <label className="text-sm font-medium">Page Title</label>
                            <input
                              type="text"
                              value={state.page.title || ''}
                              onChange={(e) => updatePage({ ...state.page, title: e.target.value })}
                              className="w-full mt-1 px-3 py-2 border rounded-md text-sm"
                              placeholder="Enter page title"
                            />
                          </div>
                          <div>
                            <label className="text-sm font-medium">Page Description</label>
                            <textarea
                              value={state.page.description || ''}
                              onChange={(e) => updatePage({ ...state.page, description: e.target.value })}
                              className="w-full mt-1 px-3 py-2 border rounded-md text-sm"
                              rows={3}
                              placeholder="Enter page description"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </ScrollArea>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        )}

        {/* Canvas Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {editorMode === 'grid' ? (
            <GridLayoutBuilder
              isPreviewMode={isPreviewMode}
              devicePreview={devicePreview}
              className="flex-1"
            />
          ) : (
            <PageBuilderCanvas
              devicePreview={devicePreview}
              isPreviewMode={isPreviewMode}
            />
          )}
        </div>
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-4 py-2 border-t bg-muted/50 text-sm text-muted-foreground">
        <div className="flex items-center space-x-4">
          <span>{state.page.blocks.length} blocks</span>
          <span>•</span>
          <span>{editorMode} mode</span>
          <span>•</span>
          <span>{devicePreview} preview</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <span>Last saved: {state.page.updatedAt ? new Date(state.page.updatedAt).toLocaleTimeString() : 'Never'}</span>
        </div>
      </div>
    </div>
  )
}
