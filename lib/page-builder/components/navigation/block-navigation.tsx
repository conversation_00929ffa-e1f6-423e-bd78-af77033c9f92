'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from '@/components/ui/sheet'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  List,
  ChevronRight,
  ChevronDown,
  Eye,
  EyeOff,
  MoreVertical,
  Copy,
  Trash2,
  MoveUp,
  MoveDown,
  Grip
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { blockRegistry } from '../../blocks/registry'

interface BlockNavigationProps {
  blocks: any[]
  selectedBlockId?: string
  onSelectBlock: (blockId: string) => void
  onUpdateBlock: (blockId: string, updates: any) => void
  onDuplicateBlock: (blockId: string) => void
  onDeleteBlock: (blockId: string) => void
  onMoveBlock: (blockId: string, direction: 'up' | 'down') => void
  trigger?: React.ReactNode
}

export function BlockNavigation({
  blocks,
  selectedBlockId,
  onSelectBlock,
  onUpdateBlock,
  onDuplicateBlock,
  onDeleteBlock,
  onMoveBlock,
  trigger
}: BlockNavigationProps) {
  const [expandedBlocks, setExpandedBlocks] = useState<Set<string>>(new Set())

  const toggleExpanded = (blockId: string) => {
    setExpandedBlocks(prev => {
      const newSet = new Set(prev)
      if (newSet.has(blockId)) {
        newSet.delete(blockId)
      } else {
        newSet.add(blockId)
      }
      return newSet
    })
  }

  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <List className="h-4 w-4 mr-2" />
      Block Navigation
    </Button>
  )

  return (
    <Sheet>
      <SheetTrigger asChild>
        {trigger || defaultTrigger}
      </SheetTrigger>
      <SheetContent side="left" className="w-80">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <List className="h-5 w-5" />
            Block Navigation
          </SheetTitle>
        </SheetHeader>
        
        <div className="mt-6">
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm text-muted-foreground">
              {blocks.length} block{blocks.length !== 1 ? 's' : ''}
            </span>
            <Button variant="ghost" size="sm" className="text-xs">
              Collapse all
            </Button>
          </div>

          <ScrollArea className="h-[calc(100vh-200px)]">
            <div className="space-y-1">
              {blocks.map((block, index) => (
                <BlockNavigationItem
                  key={block.id}
                  block={block}
                  index={index}
                  isSelected={selectedBlockId === block.id}
                  isExpanded={expandedBlocks.has(block.id)}
                  onSelect={() => onSelectBlock(block.id)}
                  onToggleExpanded={() => toggleExpanded(block.id)}
                  onUpdate={(updates) => onUpdateBlock(block.id, updates)}
                  onDuplicate={() => onDuplicateBlock(block.id)}
                  onDelete={() => onDeleteBlock(block.id)}
                  onMoveUp={() => onMoveBlock(block.id, 'up')}
                  onMoveDown={() => onMoveBlock(block.id, 'down')}
                  canMoveUp={index > 0}
                  canMoveDown={index < blocks.length - 1}
                />
              ))}
            </div>
          </ScrollArea>
        </div>
      </SheetContent>
    </Sheet>
  )
}

interface BlockNavigationItemProps {
  block: any
  index: number
  isSelected: boolean
  isExpanded: boolean
  onSelect: () => void
  onToggleExpanded: () => void
  onUpdate: (updates: any) => void
  onDuplicate: () => void
  onDelete: () => void
  onMoveUp: () => void
  onMoveDown: () => void
  canMoveUp: boolean
  canMoveDown: boolean
}

function BlockNavigationItem({
  block,
  index,
  isSelected,
  isExpanded,
  onSelect,
  onToggleExpanded,
  onUpdate,
  onDuplicate,
  onDelete,
  onMoveUp,
  onMoveDown,
  canMoveUp,
  canMoveDown
}: BlockNavigationItemProps) {
  const blockType = blockRegistry.getBlockType(block.type)
  
  if (!blockType) {
    return null
  }

  const hasNestedBlocks = block.children && block.children.length > 0
  const blockTitle = getBlockTitle(block, blockType)

  return (
    <div className={cn(
      'group rounded-lg border transition-colors',
      isSelected ? 'bg-blue-50 border-blue-200' : 'border-transparent hover:bg-gray-50'
    )}>
      <div className="flex items-center gap-2 p-2">
        {/* Drag Handle */}
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 cursor-grab opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <Grip className="h-3 w-3 text-gray-400" />
        </Button>

        {/* Expand/Collapse */}
        {hasNestedBlocks ? (
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleExpanded}
            className="h-6 w-6 p-0"
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </Button>
        ) : (
          <div className="w-6" />
        )}

        {/* Block Info */}
        <button
          onClick={onSelect}
          className="flex items-center gap-2 flex-1 text-left min-w-0"
        >
          <span className="text-sm">{blockType.icon}</span>
          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium truncate">
                {blockTitle}
              </span>
              {block.hidden && (
                <EyeOff className="h-3 w-3 text-gray-400 flex-shrink-0" />
              )}
            </div>
            <div className="flex items-center gap-1 mt-1">
              <Badge variant="secondary" className="text-xs px-1 py-0">
                {blockType.displayName}
              </Badge>
              <span className="text-xs text-muted-foreground">
                #{index + 1}
              </span>
            </div>
          </div>
        </button>

        {/* Actions */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <MoreVertical className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={onDuplicate}>
              <Copy className="h-4 w-4 mr-2" />
              Duplicate
            </DropdownMenuItem>
            
            <DropdownMenuItem 
              onClick={() => onUpdate({ hidden: !block.hidden })}
            >
              {block.hidden ? (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Show
                </>
              ) : (
                <>
                  <EyeOff className="h-4 w-4 mr-2" />
                  Hide
                </>
              )}
            </DropdownMenuItem>

            {canMoveUp && (
              <DropdownMenuItem onClick={onMoveUp}>
                <MoveUp className="h-4 w-4 mr-2" />
                Move up
              </DropdownMenuItem>
            )}

            {canMoveDown && (
              <DropdownMenuItem onClick={onMoveDown}>
                <MoveDown className="h-4 w-4 mr-2" />
                Move down
              </DropdownMenuItem>
            )}

            <DropdownMenuItem 
              onClick={onDelete}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Nested Blocks */}
      {hasNestedBlocks && isExpanded && (
        <div className="pl-8 pb-2">
          {block.children.map((childBlock: any, childIndex: number) => (
            <BlockNavigationItem
              key={childBlock.id}
              block={childBlock}
              index={childIndex}
              isSelected={false}
              isExpanded={false}
              onSelect={() => {}}
              onToggleExpanded={() => {}}
              onUpdate={() => {}}
              onDuplicate={() => {}}
              onDelete={() => {}}
              onMoveUp={() => {}}
              onMoveDown={() => {}}
              canMoveUp={childIndex > 0}
              canMoveDown={childIndex < block.children.length - 1}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Helper function to get a meaningful title for the block
function getBlockTitle(block: any, blockType: any): string {
  const config = block.configuration || {}
  
  // Try to extract meaningful content for the title
  if (config.title) return config.title
  if (config.text) return config.text
  if (config.content) {
    // Strip HTML tags and truncate
    const textContent = config.content.replace(/<[^>]*>/g, '').trim()
    return textContent.length > 30 ? textContent.substring(0, 30) + '...' : textContent
  }
  if (config.src && blockType.name === 'image') {
    const filename = config.src.split('/').pop()
    return filename || 'Image'
  }
  if (config.url && blockType.name === 'button') {
    return config.text || 'Button'
  }
  
  // Fallback to block type name
  return blockType.displayName
}

// Compact block navigation for toolbar
interface CompactBlockNavigationProps {
  blocks: any[]
  selectedBlockId?: string
  onSelectBlock: (blockId: string) => void
}

export function CompactBlockNavigation({
  blocks,
  selectedBlockId,
  onSelectBlock
}: CompactBlockNavigationProps) {
  const selectedBlock = blocks.find(b => b.id === selectedBlockId)
  const selectedIndex = blocks.findIndex(b => b.id === selectedBlockId)
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 px-2">
          <List className="h-4 w-4 mr-1" />
          {selectedBlock ? (
            <span className="text-xs">
              {getBlockTitle(selectedBlock, blockRegistry.getBlockType(selectedBlock.type))}
            </span>
          ) : (
            <span className="text-xs">Navigate</span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-64 max-h-80 overflow-y-auto">
        {blocks.map((block, index) => {
          const blockType = blockRegistry.getBlockType(block.type)
          if (!blockType) return null
          
          return (
            <DropdownMenuItem
              key={block.id}
              onClick={() => onSelectBlock(block.id)}
              className={cn(
                'flex items-center gap-2',
                selectedBlockId === block.id && 'bg-blue-50'
              )}
            >
              <span>{blockType.icon}</span>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium truncate">
                  {getBlockTitle(block, blockType)}
                </div>
                <div className="text-xs text-muted-foreground">
                  {blockType.displayName} • #{index + 1}
                </div>
              </div>
              {block.hidden && (
                <EyeOff className="h-3 w-3 text-gray-400" />
              )}
            </DropdownMenuItem>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
