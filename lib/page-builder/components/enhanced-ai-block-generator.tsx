'use client'

import React, { useState, useEffect } from 'react'
import { usePageBuilder } from '../context'
import { useAIBlockGenerator } from '@/lib/ai-block-generator/hooks/useAIBlockGenerator'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Sparkles,
  Wand2,
  Brain,
  Plus,
  Loader2,
  Check,
  X,
  RefreshCw,
  Target,
  Palette,
  Layout,
  Type,
  Image,
  ShoppingCart,
  Star,
  ChevronDown,
  ChevronUp,
  Lightbulb
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

interface EnhancedAIBlockGeneratorProps {
  className?: string
  onBlockGenerated?: (block: any) => void
}

export function EnhancedAIBlockGenerator({ className, onBlockGenerated }: EnhancedAIBlockGeneratorProps) {
  const { state, addBlock } = usePageBuilder()
  const [isExpanded, setIsExpanded] = useState(false)
  const [activeMode, setActiveMode] = useState<'simple' | 'advanced'>('simple')
  
  // Simple mode state
  const [simplePrompt, setSimplePrompt] = useState('')
  
  // Advanced mode state
  const [blockType, setBlockType] = useState('')
  const [requirements, setRequirements] = useState('')
  const [context, setContext] = useState('')
  const [style, setStyle] = useState('')
  
  const {
    generateBlock,
    generateLayout,
    isGenerating,
    generatedBlocks,
    addGeneratedBlock,
    error
  } = useAIBlockGenerator()

  // Smart block suggestions based on page content
  const [smartSuggestions, setSmartSuggestions] = useState<any[]>([])

  // Block type options
  const blockTypes = [
    { value: 'hero', label: 'Hero Section', icon: Layout, description: 'Main banner with headline and CTA' },
    { value: 'features', label: 'Features', icon: Star, description: 'Highlight key features or benefits' },
    { value: 'products', label: 'Products', icon: ShoppingCart, description: 'Product showcase or grid' },
    { value: 'testimonials', label: 'Testimonials', icon: Type, description: 'Customer reviews and feedback' },
    { value: 'gallery', label: 'Gallery', icon: Image, description: 'Image or media gallery' },
    { value: 'content', label: 'Content', icon: Type, description: 'Text content or article' },
    { value: 'cta', label: 'Call to Action', icon: Target, description: 'Action-focused section' },
    { value: 'contact', label: 'Contact', icon: Type, description: 'Contact form or information' }
  ]

  // Style options
  const styleOptions = [
    { value: 'modern', label: 'Modern', description: 'Clean, contemporary design' },
    { value: 'minimal', label: 'Minimal', description: 'Simple, uncluttered layout' },
    { value: 'bold', label: 'Bold', description: 'Strong, impactful design' },
    { value: 'elegant', label: 'Elegant', description: 'Sophisticated, refined look' },
    { value: 'playful', label: 'Playful', description: 'Fun, colorful design' },
    { value: 'professional', label: 'Professional', description: 'Business-focused appearance' }
  ]

  // Generate smart suggestions based on current page
  useEffect(() => {
    const generateSmartSuggestions = () => {
      const currentBlocks = state.page?.blocks || []
      const suggestions = []

      // Analyze current page structure
      const hasHero = currentBlocks.some((block: any) => block.type === 'hero')
      const hasProducts = currentBlocks.some((block: any) => block.type === 'products')
      const hasTestimonials = currentBlocks.some((block: any) => block.type === 'testimonials')
      const hasFeatures = currentBlocks.some((block: any) => block.type === 'features')
      const hasCTA = currentBlocks.some((block: any) => block.type === 'cta')

      // Suggest missing essential blocks
      if (!hasHero) {
        suggestions.push({
          type: 'hero',
          title: 'Add Hero Section',
          description: 'Create an engaging hero section to capture attention',
          prompt: 'Create a modern hero section with compelling headline, description, and call-to-action button for a kids clothing store',
          priority: 'high'
        })
      }

      if (!hasProducts && currentBlocks.length > 0) {
        suggestions.push({
          type: 'products',
          title: 'Showcase Products',
          description: 'Display your featured products',
          prompt: 'Add a product showcase section featuring our best-selling kids clothing items with images and prices',
          priority: 'high'
        })
      }

      if (!hasTestimonials && currentBlocks.length > 1) {
        suggestions.push({
          type: 'testimonials',
          title: 'Customer Reviews',
          description: 'Build trust with customer testimonials',
          prompt: 'Create a testimonials section with customer reviews for our kids clothing brand',
          priority: 'medium'
        })
      }

      if (!hasFeatures && hasProducts) {
        suggestions.push({
          type: 'features',
          title: 'Highlight Features',
          description: 'Showcase key benefits and features',
          prompt: 'Add a features section highlighting the quality, comfort, and style of our kids clothing',
          priority: 'medium'
        })
      }

      if (!hasCTA && currentBlocks.length > 2) {
        suggestions.push({
          type: 'cta',
          title: 'Call to Action',
          description: 'Encourage visitors to take action',
          prompt: 'Create a compelling call-to-action section encouraging visitors to shop our kids clothing collection',
          priority: 'high'
        })
      }

      // Content enhancement suggestions
      if (currentBlocks.length > 0) {
        suggestions.push({
          type: 'gallery',
          title: 'Image Gallery',
          description: 'Add visual appeal with an image gallery',
          prompt: 'Create a responsive image gallery showcasing our kids clothing in lifestyle settings',
          priority: 'low'
        })
      }

      setSmartSuggestions(suggestions.slice(0, 4)) // Limit to 4 suggestions
    }

    generateSmartSuggestions()
  }, [state.page?.blocks])

  const handleSimpleGenerate = async () => {
    if (!simplePrompt.trim()) return

    try {
      const result = await generateBlock('auto', simplePrompt, 'kids clothing store', 'modern')
      if (result && onBlockGenerated) {
        onBlockGenerated(result)
      }
      setSimplePrompt('')
      toast.success('Block generated successfully!')
    } catch (error) {
      console.error('Simple generation error:', error)
      toast.error('Failed to generate block')
    }
  }

  const handleAdvancedGenerate = async () => {
    if (!blockType || !requirements) return

    try {
      const result = await generateBlock(blockType, requirements, context, style)
      if (result && onBlockGenerated) {
        onBlockGenerated(result)
      }
      // Reset form
      setBlockType('')
      setRequirements('')
      setContext('')
      setStyle('')
      toast.success('Block generated successfully!')
    } catch (error) {
      console.error('Advanced generation error:', error)
      toast.error('Failed to generate block')
    }
  }

  const handleSuggestionGenerate = async (suggestion: any) => {
    try {
      const result = await generateBlock(suggestion.type, suggestion.prompt, 'kids clothing store', 'modern')
      if (result && onBlockGenerated) {
        onBlockGenerated(result)
      }
      toast.success(`${suggestion.title} generated successfully!`)
    } catch (error) {
      console.error('Suggestion generation error:', error)
      toast.error('Failed to generate suggested block')
    }
  }

  return (
    <Card className={cn('h-full flex flex-col', className)}>
      {/* Header */}
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Wand2 className="h-5 w-5 text-blue-600" />
            AI Block Generator
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>
        <p className="text-sm text-muted-foreground">
          Generate blocks with AI assistance
        </p>
      </CardHeader>

      {isExpanded && (
        <CardContent className="flex-1 flex flex-col p-4 space-y-4">
          {/* Mode Toggle */}
          <div className="flex rounded-lg bg-muted p-1">
            <Button
              variant={activeMode === 'simple' ? 'default' : 'ghost'}
              size="sm"
              className="flex-1 h-8"
              onClick={() => setActiveMode('simple')}
            >
              <Sparkles className="mr-2 h-3 w-3" />
              Simple
            </Button>
            <Button
              variant={activeMode === 'advanced' ? 'default' : 'ghost'}
              size="sm"
              className="flex-1 h-8"
              onClick={() => setActiveMode('advanced')}
            >
              <Brain className="mr-2 h-3 w-3" />
              Advanced
            </Button>
          </div>

          <ScrollArea className="flex-1">
            {activeMode === 'simple' ? (
              <div className="space-y-4">
                {/* Simple Mode */}
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      Describe what you want to create
                    </label>
                    <Textarea
                      value={simplePrompt}
                      onChange={(e) => setSimplePrompt(e.target.value)}
                      placeholder="e.g., Create a hero section with a headline about kids fashion and a shop now button"
                      className="min-h-[80px]"
                      disabled={isGenerating}
                    />
                  </div>
                  
                  <Button
                    onClick={handleSimpleGenerate}
                    disabled={isGenerating || !simplePrompt.trim()}
                    className="w-full"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Sparkles className="mr-2 h-4 w-4" />
                        Generate Block
                      </>
                    )}
                  </Button>
                </div>

                {/* Smart Suggestions */}
                {smartSuggestions.length > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Lightbulb className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm font-medium">Smart Suggestions</span>
                    </div>
                    
                    <div className="space-y-2">
                      {smartSuggestions.map((suggestion, index) => (
                        <Card key={index} className="p-3">
                          <div className="flex items-start justify-between gap-3">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium text-sm">{suggestion.title}</h4>
                                <Badge 
                                  variant={suggestion.priority === 'high' ? 'default' : 'secondary'}
                                  className="text-xs"
                                >
                                  {suggestion.priority}
                                </Badge>
                              </div>
                              <p className="text-xs text-muted-foreground">{suggestion.description}</p>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleSuggestionGenerate(suggestion)}
                              disabled={isGenerating}
                              className="text-xs h-7 px-2"
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {/* Advanced Mode */}
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Block Type</label>
                    <Select value={blockType} onValueChange={setBlockType} disabled={isGenerating}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose block type" />
                      </SelectTrigger>
                      <SelectContent>
                        {blockTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            <div className="flex items-center gap-2">
                              <type.icon className="h-4 w-4" />
                              <div>
                                <div className="font-medium">{type.label}</div>
                                <div className="text-xs text-muted-foreground">{type.description}</div>
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Requirements</label>
                    <Textarea
                      value={requirements}
                      onChange={(e) => setRequirements(e.target.value)}
                      placeholder="Describe specific requirements for this block..."
                      className="min-h-[60px]"
                      disabled={isGenerating}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Context (Optional)</label>
                    <Input
                      value={context}
                      onChange={(e) => setContext(e.target.value)}
                      placeholder="Additional context or page information"
                      disabled={isGenerating}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Style</label>
                    <Select value={style} onValueChange={setStyle} disabled={isGenerating}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose style" />
                      </SelectTrigger>
                      <SelectContent>
                        {styleOptions.map((styleOption) => (
                          <SelectItem key={styleOption.value} value={styleOption.value}>
                            <div>
                              <div className="font-medium">{styleOption.label}</div>
                              <div className="text-xs text-muted-foreground">{styleOption.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Button
                    onClick={handleAdvancedGenerate}
                    disabled={isGenerating || !blockType || !requirements}
                    className="w-full"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Brain className="mr-2 h-4 w-4" />
                        Generate Block
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )}
          </ScrollArea>

          {/* Error Display */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2 text-red-700">
                <X className="h-4 w-4" />
                <span className="text-sm font-medium">Generation Error</span>
              </div>
              <p className="text-sm text-red-600 mt-1">{error.message}</p>
            </div>
          )}

          {/* Generated Blocks Preview */}
          {generatedBlocks.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">Recently Generated</span>
              </div>
              <div className="space-y-2">
                {generatedBlocks.slice(-2).map((block, index) => (
                  <Card key={index} className="p-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {block.type}
                        </Badge>
                        <span className="text-sm">{block.displayName || block.type}</span>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => addGeneratedBlock(block)}
                        className="text-xs h-6 px-2"
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  )
}
