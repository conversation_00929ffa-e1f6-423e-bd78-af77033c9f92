# Enhanced Page and Layout Builder with AI Integration

A comprehensive, AI-powered page and layout builder using react-grid-layout and the Vercel AI SDK for Next.js 15 applications.

## 🌟 Features

### Grid Layout System
- **React Grid Layout Integration** - Drag-and-drop grid-based layout system
- **Responsive Design** - Mobile, tablet, desktop, and large screen support
- **Real-time Editing** - Live preview with instant updates
- **Flexible Positioning** - Precise control over block placement and sizing

### AI-Powered Generation
- **AI Layout Generator** - Generate complete layouts from natural language prompts
- **Smart Templates** - Pre-built layout templates for common use cases
- **Content Generation** - AI-powered content creation for blocks
- **Layout Optimization** - AI suggestions for improving layout performance and UX

### Advanced Builder Features
- **Hybrid Mode** - Combine page building with layout design
- **Multi-Device Preview** - Test layouts across different screen sizes
- **Undo/Redo System** - Complete history management
- **Block Library** - Extensive collection of pre-built components
- **Custom Styling** - Advanced styling options for all elements

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pnpm add react-grid-layout @types/react-grid-layout react-resizable @types/react-resizable
```

### 2. Basic Usage

```tsx
import { AIGridPageBuilder } from '@/lib/page-builder/components/ai-grid-page-builder'

function MyPageBuilder() {
  return (
    <AIGridPageBuilder
      initialPage={{
        title: 'My Page',
        blocks: []
      }}
      onSave={async (page, layout) => {
        // Save page and layout
        console.log('Saving:', { page, layout })
      }}
    />
  )
}
```

### 3. Grid Layout Builder

```tsx
import { GridLayoutBuilder } from '@/lib/page-builder/components/grid-layout-builder'
import { PageBuilderProvider } from '@/lib/page-builder/context'

function GridBuilder() {
  return (
    <PageBuilderProvider>
      <GridLayoutBuilder
        isPreviewMode={false}
        devicePreview="desktop"
      />
    </PageBuilderProvider>
  )
}
```

### 4. AI Layout Generation

```tsx
import { AILayoutGenerator } from '@/lib/page-builder/components/ai-layout-generator'

function AIGenerator() {
  return (
    <AILayoutGenerator
      onLayoutGenerated={(layout) => {
        console.log('Generated layout:', layout)
      }}
    />
  )
}
```

## 🎯 Core Components

### AIGridPageBuilder

The main component that combines all builder features:

```tsx
<AIGridPageBuilder
  initialPage={page}
  initialLayout={layout}
  onSave={async (page, layout) => {
    await saveToDatabase(page, layout)
  }}
  onBack={() => router.back()}
/>
```

**Props:**
- `initialPage` - Initial page data
- `initialLayout` - Initial layout configuration
- `onSave` - Save callback function
- `onBack` - Back navigation callback

### GridLayoutBuilder

Grid-based layout editor with drag-and-drop:

```tsx
<GridLayoutBuilder
  isPreviewMode={false}
  devicePreview="desktop"
  className="custom-grid-builder"
/>
```

**Features:**
- Drag-and-drop block positioning
- Responsive grid system (12-column)
- Real-time layout updates
- Block resizing and constraints
- Device-specific layouts

### AILayoutGenerator

AI-powered layout generation:

```tsx
<AILayoutGenerator
  onLayoutGenerated={(layout) => {
    applyLayoutToBuilder(layout)
  }}
/>
```

**Capabilities:**
- Natural language layout generation
- Template-based layouts
- Custom block selection
- Style and device optimization
- Layout suggestions and improvements

## 🔧 Configuration

### Grid Layout Settings

```tsx
const gridConfig = {
  breakpoints: {
    lg: 1200,
    md: 996,
    sm: 768,
    xs: 480,
    xxs: 0
  },
  cols: {
    lg: 12,
    md: 10,
    sm: 6,
    xs: 4,
    xxs: 2
  },
  rowHeight: 100,
  margin: [16, 16],
  containerPadding: [0, 0]
}
```

### AI Generation Options

```tsx
const aiOptions = {
  pageType: 'landing',
  industry: 'kids-clothing',
  style: 'modern',
  targetDevice: 'desktop',
  includeBlocks: ['hero', 'features', 'cta'],
  colorScheme: 'brand',
  layoutDensity: 'balanced'
}
```

## 🎨 Block Types

### Content Blocks
- **Text** - Rich text content with formatting
- **Image** - Single images with captions
- **Gallery** - Image galleries and carousels
- **Video** - Video players and embeds

### Layout Blocks
- **Hero** - Large header sections with CTAs
- **Features** - Feature highlights and benefits
- **Testimonials** - Customer testimonials
- **CTA** - Call-to-action sections

### E-commerce Blocks
- **Product Gallery** - Product image displays
- **Product Details** - Product information
- **Related Products** - Product recommendations
- **Reviews** - Product reviews and ratings

### Data Blocks
- **Charts** - Data visualization
- **Metrics** - KPI displays
- **Tables** - Data tables
- **Forms** - Contact and lead forms

## 🤖 AI Integration

### Layout Generation API

The AI system uses the Vercel AI SDK with OpenAI GPT-4:

```typescript
// API Route: /api/ai-layout-generator
export async function POST(req: Request) {
  const { messages } = await req.json()
  
  const result = await streamText({
    model: openai('gpt-4-turbo'),
    messages,
    tools: {
      generateLayout: tool({
        description: 'Generate responsive grid layout',
        parameters: layoutSchema,
        execute: async (params) => {
          return generateSmartLayout(params)
        }
      })
    }
  })

  return result.toDataStreamResponse()
}
```

### AI Prompts

Example prompts for layout generation:

```typescript
const prompts = {
  ecommerce: "Create a modern e-commerce layout with hero section, product grid, and footer",
  blog: "Design a clean blog layout with article content, sidebar, and related posts",
  landing: "Generate a conversion-focused landing page with hero, features, and CTA",
  dashboard: "Build a dashboard layout with metrics, charts, and data tables"
}
```

## 📱 Responsive Design

### Breakpoint System

```typescript
const breakpoints = {
  mobile: '480px',    // xs
  tablet: '768px',    // sm
  desktop: '1024px',  // md
  large: '1280px'     // lg
}
```

### Device-Specific Layouts

```typescript
const layouts = {
  lg: [{ i: 'block1', x: 0, y: 0, w: 6, h: 4 }],
  md: [{ i: 'block1', x: 0, y: 0, w: 8, h: 4 }],
  sm: [{ i: 'block1', x: 0, y: 0, w: 6, h: 3 }],
  xs: [{ i: 'block1', x: 0, y: 0, w: 4, h: 3 }]
}
```

## 🔌 Extensibility

### Custom Block Types

```tsx
// Register custom block
ComponentRegistry.registerBlockComponent('custom-block', ({ content, config }) => {
  return (
    <div className="custom-block">
      <h3>{content.title}</h3>
      <p>{content.description}</p>
    </div>
  )
})
```

### Custom Templates

```tsx
// Register layout template
const customTemplate = {
  id: 'custom-layout',
  name: 'Custom Layout',
  blocks: ['hero', 'content', 'footer'],
  gridLayout: [
    { i: 'hero', x: 0, y: 0, w: 12, h: 4 },
    { i: 'content', x: 0, y: 4, w: 12, h: 6 },
    { i: 'footer', x: 0, y: 10, w: 12, h: 2 }
  ]
}
```

### AI Tools Integration

```tsx
// Custom AI tool
const customTool = tool({
  description: 'Generate custom layout elements',
  parameters: z.object({
    elementType: z.string(),
    style: z.string()
  }),
  execute: async ({ elementType, style }) => {
    return generateCustomElement(elementType, style)
  }
})
```

## 🎯 Use Cases

### E-commerce Store
- Product catalog pages
- Product detail pages
- Category pages
- Landing pages

### Blog/Content Site
- Article layouts
- Category pages
- Author pages
- Homepage designs

### Corporate Website
- About pages
- Service pages
- Contact pages
- Team pages

### Dashboard/Admin
- Analytics dashboards
- Data visualization
- Admin interfaces
- Reporting pages

## 🔧 Advanced Features

### Performance Optimization
- Lazy loading for large layouts
- Virtual scrolling for block libraries
- Optimized re-rendering
- Memory management

### Accessibility
- ARIA labels and roles
- Keyboard navigation
- Screen reader support
- Focus management

### SEO Integration
- Semantic HTML structure
- Meta tag management
- Schema markup
- Performance optimization

## 📚 Examples

Check the `/examples` directory for complete implementations:

- E-commerce product page
- Blog article layout
- Landing page design
- Dashboard interface
- Custom block development

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests and documentation
5. Submit a pull request

## 📄 License

This enhanced page builder is part of the Coco Milk Kids project and follows the same licensing terms.

---

**Built with ❤️ using React Grid Layout and AI technology**
