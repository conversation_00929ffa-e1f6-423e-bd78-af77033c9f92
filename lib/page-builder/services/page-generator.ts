import { PrismaClient } from '@prisma/client'
import { PageData, PageBlock } from '../types'
import { generateId } from '../utils'

const prisma = new PrismaClient()

export interface PageGenerationOptions {
  title: string
  slug: string
  description?: string
  type?: 'custom' | 'product' | 'category' | 'home' | 'landing'
  template?: string
  status?: 'draft' | 'published' | 'archived'
  seoTitle?: string
  seoDescription?: string
  seoKeywords?: string[]
  ogImage?: string
  requiresAuth?: boolean
  allowComments?: boolean
  customCss?: string
  customJs?: string
  blocks?: PageBlock[]
  publishedAt?: Date
  scheduledAt?: Date
  expiresAt?: Date
  isHomePage?: boolean
  isLandingPage?: boolean
  metadata?: Record<string, any>
  createdBy?: string
  updatedBy?: string
}

export class PageGenerator {
  /**
   * Generate a new page with default blocks
   */
  static async generatePage(options: PageGenerationOptions): Promise<PageData> {
    const {
      title,
      slug,
      description,
      type = 'custom',
      template,
      status = 'draft',
      seoTitle,
      seoDescription,
      seoKeywords = [],
      ogImage,
      requiresAuth = false,
      allowComments = false,
      customCss,
      customJs,
      blocks = [],
      publishedAt,
      scheduledAt,
      expiresAt,
      isHomePage = false,
      isLandingPage = false,
      metadata = {},
      createdBy,
      updatedBy
    } = options

    // Validate slug uniqueness
    const existingPage = await prisma.page.findUnique({
      where: { slug }
    })

    if (existingPage) {
      throw new Error(`Page with slug "${slug}" already exists`)
    }

    // Generate default blocks if none provided
    const pageBlocks = blocks.length > 0 ? blocks : this.generateDefaultBlocks(type)

    // Create page in database
    const page = await prisma.page.create({
      data: {
        title,
        slug,
        description,
        type,
        template,
        status,
        seoTitle,
        seoDescription,
        seoKeywords,
        ogImage,
        requiresAuth,
        allowComments,
        customCss,
        customJs,
        publishedAt: status === 'published' ? publishedAt || new Date() : publishedAt,
        scheduledAt,
        expiresAt,
        isHomePage,
        isLandingPage,
        metadata,
        createdBy,
        updatedBy,
        blocks: {
          create: pageBlocks.map((block, index) => ({
            id: block.id || generateId(),
            blockType: block.type,
            position: block.position || index,
            isVisible: block.isVisible !== false,
            configuration: block.configuration || {},
            content: block.content || {},
            styling: block.styling || {},
            responsive: block.responsive || {},
            animation: block.animation || {},
            conditions: block.conditions || {},
          }))
        }
      },
      include: {
        blocks: {
          orderBy: { position: 'asc' }
        }
      }
    })

    // Transform to PageData format
    return this.transformDatabasePageToPageData(page)
  }

  /**
   * Generate default blocks based on page type
   */
  static generateDefaultBlocks(type: string): PageBlock[] {
    const blocks: PageBlock[] = []

    switch (type) {
      case 'home':
        blocks.push(
          {
            id: generateId(),
            type: 'hero',
            position: 0,
            isVisible: true,
            configuration: {
              title: 'Welcome to Coco Milk Kids',
              subtitle: 'Premium children\'s clothing designed for comfort and style',
              backgroundImage: '/images/hero-bg.jpg',
              ctaText: 'Shop Now',
              ctaLink: '/products'
            },
            content: {},
            styling: {
              backgroundColor: '#f8f9fa',
              textColor: '#333',
              padding: '80px 0'
            },
            responsive: {},
            animation: {},
            conditions: {}
          },
          {
            id: generateId(),
            type: 'featured-products',
            position: 1,
            isVisible: true,
            configuration: {
              title: 'Featured Products',
              limit: 8,
              showPrice: true,
              showAddToCart: true
            },
            content: {},
            styling: {
              padding: '60px 0'
            },
            responsive: {},
            animation: {},
            conditions: {}
          },
          {
            id: generateId(),
            type: 'newsletter',
            position: 2,
            isVisible: true,
            configuration: {
              title: 'Stay Updated',
              description: 'Get the latest news and exclusive offers',
              placeholder: 'Enter your email address'
            },
            content: {},
            styling: {
              backgroundColor: '#f1f5f9',
              padding: '60px 0'
            },
            responsive: {},
            animation: {},
            conditions: {}
          }
        )
        break

      case 'landing':
        blocks.push(
          {
            id: generateId(),
            type: 'hero',
            position: 0,
            isVisible: true,
            configuration: {
              title: 'Special Offer',
              subtitle: 'Limited time deal on premium kids clothing',
              ctaText: 'Get Offer',
              ctaLink: '/products'
            },
            content: {},
            styling: {},
            responsive: {},
            animation: {},
            conditions: {}
          },
          {
            id: generateId(),
            type: 'testimonials',
            position: 1,
            isVisible: true,
            configuration: {
              title: 'What Parents Say',
              showRatings: true
            },
            content: {},
            styling: {},
            responsive: {},
            animation: {},
            conditions: {}
          }
        )
        break

      case 'product':
        blocks.push(
          {
            id: generateId(),
            type: 'product-details',
            position: 0,
            isVisible: true,
            configuration: {
              showGallery: true,
              showVariants: true,
              showReviews: true,
              showRelated: true
            },
            content: {},
            styling: {},
            responsive: {},
            animation: {},
            conditions: {}
          }
        )
        break

      default:
        blocks.push(
          {
            id: generateId(),
            type: 'heading',
            position: 0,
            isVisible: true,
            configuration: {
              text: 'Welcome to Our Page',
              level: 1,
              alignment: 'center'
            },
            content: {},
            styling: {
              padding: '40px 0'
            },
            responsive: {},
            animation: {},
            conditions: {}
          },
          {
            id: generateId(),
            type: 'text',
            position: 1,
            isVisible: true,
            configuration: {
              content: '<p>This is a new page created with our page builder. You can edit this content and add more blocks to customize your page.</p>'
            },
            content: {},
            styling: {
              padding: '20px 0'
            },
            responsive: {},
            animation: {},
            conditions: {}
          }
        )
    }

    return blocks
  }

  /**
   * Transform database page to PageData format
   */
  static transformDatabasePageToPageData(page: any): PageData {
    return {
      id: page.id,
      title: page.title,
      slug: page.slug,
      description: page.description || undefined,
      status: page.status as 'draft' | 'published' | 'archived',
      type: page.type as 'custom' | 'product' | 'category' | 'home' | 'landing',
      template: page.template || undefined,
      publishedAt: page.publishedAt || undefined,
      createdAt: page.createdAt,
      updatedAt: page.updatedAt,
      blocks: page.blocks.map((block: any) => ({
        id: block.id,
        type: block.blockType,
        position: block.position,
        isVisible: block.isVisible,
        configuration: block.configuration || {},
        content: block.content || {},
        styling: block.styling || {},
        responsive: block.responsive || {},
        animation: block.animation || {},
        conditions: block.conditions || {},
      })),
      settings: {
        title: page.title,
        description: page.description || undefined,
        seoTitle: page.seoTitle || undefined,
        seoDescription: page.seoDescription || undefined,
        seoKeywords: page.seoKeywords || [],
        ogImage: page.ogImage || undefined,
        customCss: page.customCss || undefined,
        customJs: page.customJs || undefined,
        requiresAuth: page.requiresAuth || false,
        allowComments: page.allowComments || false,
      }
    }
  }

  /**
   * Duplicate an existing page
   */
  static async duplicatePage(pageId: string, newSlug: string, newTitle?: string): Promise<PageData> {
    const originalPage = await prisma.page.findUnique({
      where: { id: pageId },
      include: {
        blocks: {
          orderBy: { position: 'asc' }
        }
      }
    })

    if (!originalPage) {
      throw new Error('Original page not found')
    }

    const duplicatedPage = await this.generatePage({
      title: newTitle || `${originalPage.title} (Copy)`,
      slug: newSlug,
      description: originalPage.description || undefined,
      type: originalPage.type as any,
      template: originalPage.template || undefined,
      status: 'draft',
      seoTitle: originalPage.seoTitle || undefined,
      seoDescription: originalPage.seoDescription || undefined,
      seoKeywords: originalPage.seoKeywords || [],
      ogImage: originalPage.ogImage || undefined,
      requiresAuth: originalPage.requiresAuth,
      allowComments: originalPage.allowComments,
      customCss: originalPage.customCss || undefined,
      customJs: originalPage.customJs || undefined,
      blocks: originalPage.blocks.map(block => ({
        id: generateId(), // Generate new IDs for duplicated blocks
        type: block.blockType,
        position: block.position,
        isVisible: block.isVisible,
        configuration: block.configuration || {},
        content: block.content || {},
        styling: block.styling || {},
        responsive: block.responsive || {},
        animation: block.animation || {},
        conditions: block.conditions || {},
      })),
      metadata: originalPage.metadata || {},
    })

    return duplicatedPage
  }
}
