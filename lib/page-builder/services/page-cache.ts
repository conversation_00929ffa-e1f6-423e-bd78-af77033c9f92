import { PageData } from '../types'

interface CacheEntry {
  data: PageData
  timestamp: number
  etag: string
  hits: number
}

interface CacheStats {
  totalEntries: number
  totalHits: number
  totalMisses: number
  hitRate: number
  memoryUsage: number
}

export class PageCache {
  private static cache = new Map<string, CacheEntry>()
  private static readonly TTL = 5 * 60 * 1000 // 5 minutes
  private static readonly MAX_ENTRIES = 1000
  private static stats = {
    hits: 0,
    misses: 0
  }

  /**
   * Get page from cache
   */
  static get(key: string): PageData | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      this.stats.misses++
      return null
    }

    // Check if entry is expired
    if (Date.now() - entry.timestamp > this.TTL) {
      this.cache.delete(key)
      this.stats.misses++
      return null
    }

    // Update hit count and stats
    entry.hits++
    this.stats.hits++
    
    return entry.data
  }

  /**
   * Set page in cache
   */
  static set(key: string, data: PageData): void {
    // Clean up old entries if cache is full
    if (this.cache.size >= this.MAX_ENTRIES) {
      this.cleanup()
    }

    const etag = this.generateETag(data)
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      etag,
      hits: 0
    })
  }

  /**
   * Delete page from cache
   */
  static delete(key: string): boolean {
    return this.cache.delete(key)
  }

  /**
   * Clear all cache entries
   */
  static clear(): void {
    this.cache.clear()
    this.stats.hits = 0
    this.stats.misses = 0
  }

  /**
   * Get cache statistics
   */
  static getStats(): CacheStats {
    const totalRequests = this.stats.hits + this.stats.misses
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0
    
    // Calculate approximate memory usage
    let memoryUsage = 0
    for (const entry of this.cache.values()) {
      memoryUsage += JSON.stringify(entry.data).length * 2 // Rough estimate
    }

    return {
      totalEntries: this.cache.size,
      totalHits: this.stats.hits,
      totalMisses: this.stats.misses,
      hitRate: Math.round(hitRate * 100) / 100,
      memoryUsage: Math.round(memoryUsage / 1024) // KB
    }
  }

  /**
   * Generate ETag for cache validation
   */
  static generateETag(data: PageData): string {
    const content = JSON.stringify({
      id: data.id,
      updatedAt: data.updatedAt,
      blocks: data.blocks.map(b => ({ id: b.id, type: b.type, updatedAt: b.updatedAt }))
    })
    
    // Simple hash function
    let hash = 0
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36)
  }

  /**
   * Check if cached data is still valid
   */
  static isValid(key: string, etag?: string): boolean {
    const entry = this.cache.get(key)
    
    if (!entry) {
      return false
    }

    // Check expiration
    if (Date.now() - entry.timestamp > this.TTL) {
      return false
    }

    // Check ETag if provided
    if (etag && entry.etag !== etag) {
      return false
    }

    return true
  }

  /**
   * Cleanup old and least used entries
   */
  private static cleanup(): void {
    const entries = Array.from(this.cache.entries())
    
    // Sort by timestamp (oldest first) and hits (least used first)
    entries.sort((a, b) => {
      const ageA = Date.now() - a[1].timestamp
      const ageB = Date.now() - b[1].timestamp
      
      // Prioritize old entries
      if (ageA > this.TTL && ageB <= this.TTL) return -1
      if (ageB > this.TTL && ageA <= this.TTL) return 1
      
      // Then by hits (ascending)
      return a[1].hits - b[1].hits
    })

    // Remove oldest/least used entries (25% of cache)
    const toRemove = Math.floor(this.MAX_ENTRIES * 0.25)
    for (let i = 0; i < toRemove && i < entries.length; i++) {
      this.cache.delete(entries[i][0])
    }
  }

  /**
   * Invalidate cache for specific page
   */
  static invalidatePage(pageId: string): void {
    const keysToDelete: string[] = []
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.data.id === pageId) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key))
  }

  /**
   * Invalidate cache for pages with specific block type
   */
  static invalidateByBlockType(blockType: string): void {
    const keysToDelete: string[] = []
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.data.blocks.some(block => block.type === blockType)) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key))
  }

  /**
   * Preload pages into cache
   */
  static async preload(pages: PageData[]): Promise<void> {
    for (const page of pages) {
      const key = `page:${page.slug}`
      this.set(key, page)
    }
  }

  /**
   * Get cache key for page
   */
  static getPageKey(slug: string, variant?: string): string {
    return variant ? `page:${slug}:${variant}` : `page:${slug}`
  }

  /**
   * Warm up cache with popular pages
   */
  static async warmup(popularSlugs: string[]): Promise<void> {
    // This would typically fetch pages from database and cache them
    // Implementation depends on your data access layer
    console.log(`Warming up cache for ${popularSlugs.length} popular pages`)
  }
}

// Cache middleware for Next.js API routes
export function withPageCache(handler: Function) {
  return async (req: any, res: any) => {
    const { slug } = req.query
    const cacheKey = PageCache.getPageKey(slug)
    
    // Try to get from cache first
    const cachedPage = PageCache.get(cacheKey)
    if (cachedPage) {
      // Set cache headers
      res.setHeader('Cache-Control', 'public, s-maxage=300, stale-while-revalidate=600')
      res.setHeader('ETag', PageCache.generateETag(cachedPage))
      res.setHeader('X-Cache', 'HIT')
      
      return res.json({
        success: true,
        data: cachedPage,
        cached: true
      })
    }

    // Call original handler
    const result = await handler(req, res)
    
    // Cache the result if it's a successful page response
    if (result && result.success && result.data) {
      PageCache.set(cacheKey, result.data)
      res.setHeader('X-Cache', 'MISS')
    }
    
    return result
  }
}

// Automatic cache invalidation on page updates
export function invalidatePageCache(pageId: string, slug?: string): void {
  PageCache.invalidatePage(pageId)
  
  if (slug) {
    PageCache.delete(PageCache.getPageKey(slug))
  }
}

// Export cache instance for external use
export { PageCache as default }
