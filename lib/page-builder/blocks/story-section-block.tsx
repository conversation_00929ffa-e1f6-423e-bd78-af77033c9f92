'use client'

import React from 'react'
import Image from 'next/image'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { cn } from '@/lib/utils'

interface StorySectionConfig {
  title: string
  content: string[]
  image: {
    src: string
    alt: string
  }
  layout: 'image-left' | 'image-right'
  imageAspect: 'square' | 'portrait' | 'landscape'
  spacing: 'compact' | 'normal' | 'spacious'
  backgroundColor: string
  textColor: string
}

interface StorySectionBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function StorySectionBlock({ block, isEditing = false }: StorySectionBlockProps) {
  const config = block.configuration as StorySectionConfig

  const {
    title,
    content,
    image,
    layout = 'image-right',
    imageAspect = 'square',
    spacing = 'normal',
    backgroundColor = 'transparent',
    textColor = 'inherit'
  } = config

  const getSpacingClasses = () => {
    switch (spacing) {
      case 'compact':
        return 'gap-8 mb-12'
      case 'spacious':
        return 'gap-16 mb-24'
      default:
        return 'gap-12 mb-20'
    }
  }

  const getImageAspectClasses = () => {
    switch (imageAspect) {
      case 'portrait':
        return 'aspect-[3/4]'
      case 'landscape':
        return 'aspect-[4/3]'
      default:
        return 'aspect-square'
    }
  }

  const isImageLeft = layout === 'image-left'

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div 
        className="container px-4 md:px-6 py-6 md:py-10"
        style={{ 
          backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : undefined,
          color: textColor !== 'inherit' ? textColor : undefined
        }}
      >
        <div className={cn(
          'grid md:grid-cols-2 items-center',
          getSpacingClasses()
        )}>
          {/* Content Section */}
          <div className={cn(
            'space-y-4',
            isImageLeft ? 'md:order-2' : 'md:order-1'
          )}>
            <h2 className="text-2xl font-bold font-montserrat mb-6">
              {title}
            </h2>
            <div className="space-y-4 text-muted-foreground font-light">
              {content.map((paragraph, index) => (
                <p key={index} className="leading-relaxed">
                  {paragraph}
                </p>
              ))}
            </div>
          </div>

          {/* Image Section */}
          <div className={cn(
            'relative rounded-lg overflow-hidden',
            getImageAspectClasses(),
            isImageLeft ? 'md:order-1' : 'md:order-2'
          )}>
            {image.src ? (
              <Image
                src={image.src}
                alt={image.alt || title}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
              />
            ) : (
              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                <span className="text-gray-500 text-sm">No image selected</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </BaseBlock>
  )
}

// Default configuration for the story section block
export const storySectionBlockConfig = {
  title: 'Our Story',
  content: [
    'Founded in the heart of Johannesburg with a simple belief that South African children deserve clothing that\'s as comfortable as it is stylish, Coco Milk Kids was born from the idea that kids should be free to play, explore, and express themselves without compromise.',
    'Our journey began when we noticed a gap in the South African market for high-quality, oversized children\'s clothing that could withstand our diverse climate while prioritizing both comfort and contemporary design.',
    'Today, we\'re proud to be a proudly South African brand offering a carefully curated collection of premium children\'s clothing that celebrates individuality while ensuring every little South African feels comfortable and confident.'
  ],
  image: {
    src: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?q=80&w=800&h=800&auto=format&fit=crop',
    alt: 'Children playing in comfortable clothing'
  },
  layout: 'image-right',
  imageAspect: 'square',
  spacing: 'normal',
  backgroundColor: 'transparent',
  textColor: 'inherit'
}

// Configuration schema for the Page Builder
export const storySectionBlockSchema = {
  type: 'object',
  properties: {
    title: {
      type: 'string',
      title: 'Section Title',
      default: 'Our Story'
    },
    content: {
      type: 'array',
      title: 'Content Paragraphs',
      items: {
        type: 'string',
        title: 'Paragraph'
      },
      default: storySectionBlockConfig.content
    },
    image: {
      type: 'object',
      title: 'Image',
      properties: {
        src: {
          type: 'string',
          title: 'Image URL',
          format: 'uri'
        },
        alt: {
          type: 'string',
          title: 'Alt Text'
        }
      }
    },
    layout: {
      type: 'string',
      title: 'Layout',
      enum: ['image-left', 'image-right'],
      enumNames: ['Image Left', 'Image Right'],
      default: 'image-right'
    },
    imageAspect: {
      type: 'string',
      title: 'Image Aspect Ratio',
      enum: ['square', 'portrait', 'landscape'],
      enumNames: ['Square (1:1)', 'Portrait (3:4)', 'Landscape (4:3)'],
      default: 'square'
    },
    spacing: {
      type: 'string',
      title: 'Spacing',
      enum: ['compact', 'normal', 'spacious'],
      enumNames: ['Compact', 'Normal', 'Spacious'],
      default: 'normal'
    },
    backgroundColor: {
      type: 'string',
      title: 'Background Color',
      format: 'color',
      default: 'transparent'
    },
    textColor: {
      type: 'string',
      title: 'Text Color',
      format: 'color',
      default: 'inherit'
    }
  },
  required: ['title', 'content']
}
