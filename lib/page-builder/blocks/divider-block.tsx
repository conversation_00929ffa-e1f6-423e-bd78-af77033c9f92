'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { cn } from '@/lib/utils'

interface DividerBlockConfig {
  style: 'solid' | 'dashed' | 'dotted' | 'double' | 'gradient' | 'decorative'
  width: number // percentage
  thickness: number // pixels
  color: string
  gradientColors?: {
    start: string
    end: string
  }
  alignment: 'left' | 'center' | 'right'
  spacing: {
    top: string
    bottom: string
  }
  decorativeStyle?: 'dots' | 'stars' | 'waves' | 'zigzag'
  text?: {
    content: string
    position: 'center' | 'left' | 'right'
    backgroundColor: string
    textColor: string
    fontSize: 'sm' | 'md' | 'lg'
  }
  shadow: {
    enabled: boolean
    color: string
    blur: number
    offset: number
  }
}

interface DividerBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function DividerBlock({ block, isEditing = false }: DividerBlockProps) {
  const config = block.configuration as DividerBlockConfig
  
  const {
    style,
    width,
    thickness,
    color,
    gradientColors,
    alignment,
    spacing,
    decorativeStyle,
    text,
    shadow,
  } = config

  const getAlignmentClass = () => {
    const alignmentClasses = {
      left: 'justify-start',
      center: 'justify-center',
      right: 'justify-end'
    }
    return alignmentClasses[alignment]
  }

  const getDividerStyles = () => {
    const baseStyles: React.CSSProperties = {
      width: `${width}%`,
      height: `${thickness}px`,
      marginTop: spacing.top,
      marginBottom: spacing.bottom,
    }

    if (shadow.enabled) {
      baseStyles.boxShadow = `${shadow.offset}px ${shadow.offset}px ${shadow.blur}px ${shadow.color}`
    }

    switch (style) {
      case 'solid':
        return {
          ...baseStyles,
          backgroundColor: color,
          border: 'none'
        }
      
      case 'dashed':
        return {
          ...baseStyles,
          border: 'none',
          borderTop: `${thickness}px dashed ${color}`,
          height: '0px'
        }
      
      case 'dotted':
        return {
          ...baseStyles,
          border: 'none',
          borderTop: `${thickness}px dotted ${color}`,
          height: '0px'
        }
      
      case 'double':
        return {
          ...baseStyles,
          border: 'none',
          borderTop: `${Math.max(1, Math.floor(thickness / 3))}px solid ${color}`,
          borderBottom: `${Math.max(1, Math.floor(thickness / 3))}px solid ${color}`,
          height: `${Math.max(2, thickness - 2)}px`
        }
      
      case 'gradient':
        return {
          ...baseStyles,
          background: `linear-gradient(to right, ${gradientColors?.start || color}, ${gradientColors?.end || color})`,
          border: 'none'
        }
      
      default:
        return baseStyles
    }
  }

  const renderDecorativeDivider = () => {
    const decorativeElements = {
      dots: '• • • • • • • • • •',
      stars: '★ ★ ★ ★ ★ ★ ★ ★ ★ ★',
      waves: '〜〜〜〜〜〜〜〜〜〜',
      zigzag: '⟨⟩⟨⟩⟨⟩⟨⟩⟨⟩⟨⟩⟨⟩⟨⟩⟨⟩⟨⟩'
    }

    return (
      <div 
        className="text-center select-none"
        style={{
          color: color,
          fontSize: `${thickness * 0.8}px`,
          marginTop: spacing.top,
          marginBottom: spacing.bottom,
          width: `${width}%`,
          overflow: 'hidden',
          whiteSpace: 'nowrap'
        }}
      >
        {decorativeElements[decorativeStyle || 'dots']}
      </div>
    )
  }

  const renderTextDivider = () => {
    if (!text?.content) return null

    const textSizeClasses = {
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg'
    }

    const textPositionClasses = {
      left: 'justify-start',
      center: 'justify-center',
      right: 'justify-end'
    }

    return (
      <div 
        className={cn('flex items-center w-full', textPositionClasses[text.position])}
        style={{
          marginTop: spacing.top,
          marginBottom: spacing.bottom,
        }}
      >
        {text.position !== 'left' && (
          <div 
            className="flex-1 h-px"
            style={{ backgroundColor: color }}
          />
        )}
        
        <span 
          className={cn(
            'px-4 py-1 whitespace-nowrap',
            textSizeClasses[text.fontSize]
          )}
          style={{
            backgroundColor: text.backgroundColor,
            color: text.textColor,
          }}
        >
          {text.content}
        </span>
        
        {text.position !== 'right' && (
          <div 
            className="flex-1 h-px"
            style={{ backgroundColor: color }}
          />
        )}
      </div>
    )
  }

  const containerStyles = {
    width: '100%',
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: alignment === 'left' ? 'flex-start' : alignment === 'right' ? 'flex-end' : 'center'
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div style={containerStyles}>
        {text?.content ? (
          renderTextDivider()
        ) : style === 'decorative' ? (
          renderDecorativeDivider()
        ) : (
          <div style={getDividerStyles()} />
        )}
        
        {isEditing && (
          <div className="mt-2 p-2 bg-gray-50 border border-gray-200 rounded text-xs text-gray-600 w-full text-center">
            <strong>Divider Block:</strong> {style} • {width}% width • {thickness}px thick
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Divider Block Configuration Component
interface DividerBlockConfigProps {
  config: DividerBlockConfig
  onChange: (config: DividerBlockConfig) => void
}

export function DividerBlockConfig({ config, onChange }: DividerBlockConfigProps) {
  const updateConfig = (updates: Partial<DividerBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const updateSpacing = (side: keyof DividerBlockConfig['spacing'], value: string) => {
    updateConfig({
      spacing: {
        ...config.spacing,
        [side]: value
      }
    })
  }

  const updateText = (updates: Partial<DividerBlockConfig['text']>) => {
    updateConfig({
      text: {
        ...config.text,
        ...updates
      } as DividerBlockConfig['text']
    })
  }

  const updateGradientColors = (updates: Partial<DividerBlockConfig['gradientColors']>) => {
    updateConfig({
      gradientColors: {
        ...config.gradientColors,
        ...updates
      } as DividerBlockConfig['gradientColors']
    })
  }

  const updateShadow = (updates: Partial<DividerBlockConfig['shadow']>) => {
    updateConfig({
      shadow: {
        ...config.shadow,
        ...updates
      }
    })
  }

  return (
    <div className="space-y-6">
      {/* Divider Style */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Divider Style</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Style</Label>
            <Select
              value={config.style}
              onValueChange={(value) => updateConfig({ style: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="solid">Solid</SelectItem>
                <SelectItem value="dashed">Dashed</SelectItem>
                <SelectItem value="dotted">Dotted</SelectItem>
                <SelectItem value="double">Double</SelectItem>
                <SelectItem value="gradient">Gradient</SelectItem>
                <SelectItem value="decorative">Decorative</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {config.style === 'decorative' && (
            <div>
              <Label className="text-xs">Decorative Style</Label>
              <Select
                value={config.decorativeStyle || 'dots'}
                onValueChange={(value) => updateConfig({ decorativeStyle: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="dots">Dots</SelectItem>
                  <SelectItem value="stars">Stars</SelectItem>
                  <SelectItem value="waves">Waves</SelectItem>
                  <SelectItem value="zigzag">Zigzag</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <div>
            <Label className="text-xs">Width (%)</Label>
            <div className="mt-2">
              <Slider
                value={[config.width]}
                onValueChange={(value) => updateConfig({ width: value[0] })}
                max={100}
                min={10}
                step={5}
                className="w-full"
              />
              <div className="text-xs text-muted-foreground mt-1">{config.width}%</div>
            </div>
          </div>

          <div>
            <Label className="text-xs">Thickness (px)</Label>
            <div className="mt-2">
              <Slider
                value={[config.thickness]}
                onValueChange={(value) => updateConfig({ thickness: value[0] })}
                max={20}
                min={1}
                step={1}
                className="w-full"
              />
              <div className="text-xs text-muted-foreground mt-1">{config.thickness}px</div>
            </div>
          </div>

          <div>
            <Label className="text-xs">Alignment</Label>
            <Select
              value={config.alignment}
              onValueChange={(value) => updateConfig({ alignment: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="left">Left</SelectItem>
                <SelectItem value="center">Center</SelectItem>
                <SelectItem value="right">Right</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Colors */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Colors</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Primary Color</Label>
            <Input
              type="color"
              value={config.color}
              onChange={(e) => updateConfig({ color: e.target.value })}
              className="mt-1 h-10"
            />
          </div>

          {config.style === 'gradient' && (
            <>
              <div>
                <Label className="text-xs">Gradient Start</Label>
                <Input
                  type="color"
                  value={config.gradientColors?.start || config.color}
                  onChange={(e) => updateGradientColors({ start: e.target.value })}
                  className="mt-1 h-10"
                />
              </div>
              <div>
                <Label className="text-xs">Gradient End</Label>
                <Input
                  type="color"
                  value={config.gradientColors?.end || config.color}
                  onChange={(e) => updateGradientColors({ end: e.target.value })}
                  className="mt-1 h-10"
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Text Divider */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Text Divider</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Text Content</Label>
            <Input
              value={config.text?.content || ''}
              onChange={(e) => updateText({ content: e.target.value })}
              placeholder="Optional text in divider"
              className="mt-1"
            />
          </div>

          {config.text?.content && (
            <>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Text Position</Label>
                  <Select
                    value={config.text.position}
                    onValueChange={(value) => updateText({ position: value as any })}
                  >
                    <SelectTrigger className="w-full mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="left">Left</SelectItem>
                      <SelectItem value="center">Center</SelectItem>
                      <SelectItem value="right">Right</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-xs">Text Size</Label>
                  <Select
                    value={config.text.fontSize}
                    onValueChange={(value) => updateText({ fontSize: value as any })}
                  >
                    <SelectTrigger className="w-full mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sm">Small</SelectItem>
                      <SelectItem value="md">Medium</SelectItem>
                      <SelectItem value="lg">Large</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Text Color</Label>
                  <Input
                    type="color"
                    value={config.text.textColor}
                    onChange={(e) => updateText({ textColor: e.target.value })}
                    className="mt-1 h-10"
                  />
                </div>
                <div>
                  <Label className="text-xs">Background Color</Label>
                  <Input
                    type="color"
                    value={config.text.backgroundColor}
                    onChange={(e) => updateText({ backgroundColor: e.target.value })}
                    className="mt-1 h-10"
                  />
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
