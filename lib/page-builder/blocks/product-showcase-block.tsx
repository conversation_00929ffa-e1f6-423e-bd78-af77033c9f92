'use client'

import React, { useState, useEffect } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { 
  ChevronLeft, 
  ChevronRight, 
  Star, 
  ShoppingCart,
  Heart,
  Eye
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ProductShowcaseBlockConfig {
  layout: 'carousel' | 'grid' | 'single'
  featuredProductIds: string[]
  autoplay: boolean
  autoplaySpeed: number
  showDots: boolean
  showArrows: boolean
  itemsPerView: {
    desktop: number
    tablet: number
    mobile: number
  }
  showPrice: boolean
  showDescription: boolean
  showAddToCart: boolean
  showBadges: boolean
  highlightStyle: 'border' | 'shadow' | 'background'
  title: string
  subtitle: string
}

interface Product {
  id: string
  title: string
  slug: string
  price: number
  compareAtPrice?: number
  images: { src: string; alt: string }[]
  description?: string
  inStock: boolean
  onSale: boolean
  featured: boolean
  rating?: number
  reviewCount?: number
  badges?: string[]
}

interface ProductShowcaseBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function ProductShowcaseBlock({ block, isEditing = false }: ProductShowcaseBlockProps) {
  const config = block.configuration as ProductShowcaseBlockConfig
  const [products, setProducts] = useState<Product[]>([])
  const [currentSlide, setCurrentSlide] = useState(0)
  const [loading, setLoading] = useState(true)

  const {
    layout,
    featuredProductIds,
    autoplay,
    autoplaySpeed,
    showDots,
    showArrows,
    itemsPerView,
    showPrice,
    showDescription,
    showAddToCart,
    showBadges,
    highlightStyle,
    title,
    subtitle,
  } = config

  // Fetch real products from API
  useEffect(() => {
    fetchFeaturedProducts()
  }, [featuredProductIds])

  // Auto-play carousel
  useEffect(() => {
    if (layout === 'carousel' && autoplay && products.length > 0) {
      const interval = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % Math.ceil(products.length / itemsPerView.desktop))
      }, autoplaySpeed)
      
      return () => clearInterval(interval)
    }
  }, [layout, autoplay, autoplaySpeed, products.length, itemsPerView.desktop])

  const fetchFeaturedProducts = async () => {
    try {
      setLoading(true)

      // If in editing mode and no specific products selected, show demo products
      if (isEditing && featuredProductIds.length === 0) {
        setProducts([
          {
            id: 'demo-1',
            title: 'Premium Kids Cotton T-Shirt',
            slug: 'premium-kids-cotton-tshirt',
            price: 399,
            compareAtPrice: 499,
            images: [{
              src: '/assets/images/cocomilk_kids-20220927_125643-1359487094.jpg',
              alt: 'Premium Kids Cotton T-Shirt'
            }],
            description: 'Soft, comfortable cotton t-shirt perfect for everyday wear.',
            inStock: true,
            onSale: true,
            featured: true,
            rating: 4.8,
            reviewCount: 24,
            badges: ['Best Seller', 'Organic']
          },
          {
            id: 'demo-2',
            title: 'Denim Overalls for Kids',
            slug: 'denim-overalls-kids',
            price: 799,
            images: [{
              src: '/assets/images/cocomilk_kids-20210912_114630-3065525289.jpg',
              alt: 'Denim Overalls for Kids'
            }],
            description: 'Durable denim overalls with adjustable straps.',
            inStock: true,
            onSale: false,
            featured: true,
            rating: 4.6,
            reviewCount: 18,
            badges: ['New Arrival']
          }
        ])
        setLoading(false)
        return
      }

      // Fetch real products from API
      const params = new URLSearchParams({
        featured: 'true',
        limit: '6',
        status: 'active',
        ...(featuredProductIds.length > 0 && { ids: featuredProductIds.join(',') })
      })

      const response = await fetch(`/api/e-commerce/products?${params}`)
      const data = await response.json()

      if (data.success) {
        // Transform API data to match component interface
        const transformedProducts = (data.data || []).map((product: any) => ({
          id: product.id,
          title: product.title,
          slug: product.handle || product.slug,
          price: product.price?.amount || 0,
          compareAtPrice: product.compareAtPrice?.amount,
          images: product.images?.map((img: any) => ({
            src: img.url || img.src,
            alt: img.alt || product.title
          })) || [],
          description: product.description,
          inStock: product.inventoryQuantity > 0 || !product.trackQuantity,
          onSale: !!product.compareAtPrice,
          featured: product.featured || false,
          rating: product.rating,
          reviewCount: product.reviewCount,
          badges: product.tags?.slice(0, 2) || []
        }))

        setProducts(transformedProducts)
      } else {
        console.error('Failed to fetch products:', data.error)
        setProducts([])
      }
    } catch (error) {
      console.error('Error fetching featured products:', error)
      setProducts([])
    } finally {
      setLoading(false)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(price / 100)
  }

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % Math.ceil(products.length / itemsPerView.desktop))
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => 
      prev === 0 ? Math.ceil(products.length / itemsPerView.desktop) - 1 : prev - 1
    )
  }

  const getHighlightClasses = () => {
    switch (highlightStyle) {
      case 'shadow':
        return 'shadow-lg hover:shadow-xl'
      case 'background':
        return 'bg-gradient-to-br from-primary/5 to-primary/10'
      default:
        return 'border-2 border-primary/20 hover:border-primary/40'
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          'h-3 w-3',
          i < Math.floor(rating) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
        )}
      />
    ))
  }

  const ProductCard = ({ product }: { product: Product }) => (
    <Card className={cn('group cursor-pointer transition-all duration-300', getHighlightClasses())}>
      <CardContent className="p-0">
        <div className="relative overflow-hidden">
          <img
            src={product.images[0]?.src}
            alt={product.images[0]?.alt}
            className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-105"
          />
          
          {/* Badges */}
          {showBadges && product.badges && product.badges.length > 0 && (
            <div className="absolute top-2 left-2 space-y-1">
              {product.badges.map((badge, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {badge}
                </Badge>
              ))}
            </div>
          )}

          {/* Sale Badge */}
          {product.onSale && (
            <Badge className="absolute top-2 right-2 bg-red-500 hover:bg-red-600">
              Sale
            </Badge>
          )}

          {/* Hover Actions */}
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-2">
            <Button size="sm" variant="secondary">
              <Eye className="h-4 w-4" />
            </Button>
            <Button size="sm" variant="secondary">
              <Heart className="h-4 w-4" />
            </Button>
            {showAddToCart && (
              <Button size="sm">
                <ShoppingCart className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        <div className="p-4">
          <h3 className="font-medium text-sm mb-1 line-clamp-2">{product.title}</h3>
          
          {product.rating && (
            <div className="flex items-center space-x-1 mb-2">
              <div className="flex">{renderStars(product.rating)}</div>
              <span className="text-xs text-muted-foreground">({product.reviewCount})</span>
            </div>
          )}

          {showDescription && product.description && (
            <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
              {product.description}
            </p>
          )}

          {showPrice && (
            <div className="flex items-center space-x-2">
              <span className="font-bold text-sm">{formatPrice(product.price)}</span>
              {product.compareAtPrice && (
                <span className="text-xs text-muted-foreground line-through">
                  {formatPrice(product.compareAtPrice)}
                </span>
              )}
            </div>
          )}

          {showAddToCart && (
            <Button className="w-full mt-3" size="sm">
              <ShoppingCart className="h-4 w-4 mr-2" />
              Add to Cart
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )

  if (loading) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="space-y-3">
                  <div className="h-64 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </BaseBlock>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <section className="py-12">
        <div className="container mx-auto px-4 md:px-6">
          {/* Header */}
          {(title || subtitle) && (
            <div className="text-center mb-8">
              {title && (
                <h2 className="text-3xl font-light font-p22 tracking-wide mb-2">
                  {title}
                </h2>
              )}
              {subtitle && (
                <p className="text-lg text-muted-foreground font-light">
                  {subtitle}
                </p>
              )}
            </div>
          )}

          {/* Products Display */}
          {layout === 'carousel' ? (
            <div className="relative">
              <div className="overflow-hidden">
                <div 
                  className="flex transition-transform duration-300 ease-in-out"
                  style={{ 
                    transform: `translateX(-${currentSlide * (100 / itemsPerView.desktop)}%)`,
                    width: `${Math.ceil(products.length / itemsPerView.desktop) * 100}%`
                  }}
                >
                  {products.map((product) => (
                    <div 
                      key={product.id} 
                      className="px-3"
                      style={{ width: `${100 / products.length}%` }}
                    >
                      <ProductCard product={product} />
                    </div>
                  ))}
                </div>
              </div>

              {/* Navigation Arrows */}
              {showArrows && products.length > itemsPerView.desktop && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 z-10"
                    onClick={prevSlide}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 z-10"
                    onClick={nextSlide}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </>
              )}

              {/* Dots Indicator */}
              {showDots && products.length > itemsPerView.desktop && (
                <div className="flex justify-center space-x-2 mt-6">
                  {Array.from({ length: Math.ceil(products.length / itemsPerView.desktop) }).map((_, i) => (
                    <button
                      key={i}
                      className={cn(
                        'w-2 h-2 rounded-full transition-colors',
                        i === currentSlide ? 'bg-primary' : 'bg-gray-300'
                      )}
                      onClick={() => setCurrentSlide(i)}
                    />
                  ))}
                </div>
              )}
            </div>
          ) : layout === 'grid' ? (
            <div className={cn(
              'grid gap-6',
              `grid-cols-1 md:grid-cols-${itemsPerView.tablet} lg:grid-cols-${itemsPerView.desktop}`
            )}>
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            // Single product layout
            products.length > 0 && (
              <div className="max-w-md mx-auto">
                <ProductCard product={products[0]} />
              </div>
            )
          )}

          {products.length === 0 && !loading && (
            <div className="text-center py-12">
              <p className="text-muted-foreground">No featured products found.</p>
            </div>
          )}
        </div>
      </section>
    </BaseBlock>
  )
}

// Product Showcase Block Configuration Component
interface ProductShowcaseBlockConfigProps {
  config: ProductShowcaseBlockConfig
  onChange: (config: ProductShowcaseBlockConfig) => void
}

export function ProductShowcaseBlockConfig({ config, onChange }: ProductShowcaseBlockConfigProps) {
  const updateConfig = (updates: Partial<ProductShowcaseBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  return (
    <div className="space-y-6">
      {/* Content Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Content Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Section Title</Label>
            <Input
              type="text"
              value={config.title}
              onChange={(e) => updateConfig({ title: e.target.value })}
              placeholder="Featured Products"
              className="text-xs mt-1"
            />
          </div>
          <div>
            <Label className="text-xs">Section Subtitle</Label>
            <Input
              type="text"
              value={config.subtitle}
              onChange={(e) => updateConfig({ subtitle: e.target.value })}
              placeholder="Discover our handpicked selection"
              className="text-xs mt-1"
            />
          </div>
        </CardContent>
      </Card>

      {/* Layout Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Layout Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Layout Style</Label>
            <select
              value={config.layout}
              onChange={(e) => updateConfig({ layout: e.target.value as any })}
              className="w-full mt-1 px-2 py-1 text-xs border rounded"
            >
              <option value="carousel">Carousel</option>
              <option value="grid">Grid</option>
              <option value="single">Single Product</option>
            </select>
          </div>

          <div>
            <Label className="text-xs">Highlight Style</Label>
            <select
              value={config.highlightStyle}
              onChange={(e) => updateConfig({ highlightStyle: e.target.value as any })}
              className="w-full mt-1 px-2 py-1 text-xs border rounded"
            >
              <option value="border">Border Highlight</option>
              <option value="shadow">Shadow Effect</option>
              <option value="background">Background Gradient</option>
            </select>
          </div>

          {config.layout !== 'single' && (
            <div>
              <Label className="text-xs mb-2 block">Items Per View</Label>
              <div className="grid grid-cols-3 gap-2">
                <div>
                  <Label className="text-xs">Desktop</Label>
                  <Input
                    type="number"
                    value={config.itemsPerView.desktop}
                    onChange={(e) => updateConfig({
                      itemsPerView: { ...config.itemsPerView, desktop: parseInt(e.target.value) }
                    })}
                    min="1"
                    max="6"
                    className="text-xs mt-1"
                  />
                </div>
                <div>
                  <Label className="text-xs">Tablet</Label>
                  <Input
                    type="number"
                    value={config.itemsPerView.tablet}
                    onChange={(e) => updateConfig({
                      itemsPerView: { ...config.itemsPerView, tablet: parseInt(e.target.value) }
                    })}
                    min="1"
                    max="4"
                    className="text-xs mt-1"
                  />
                </div>
                <div>
                  <Label className="text-xs">Mobile</Label>
                  <Input
                    type="number"
                    value={config.itemsPerView.mobile}
                    onChange={(e) => updateConfig({
                      itemsPerView: { ...config.itemsPerView, mobile: parseInt(e.target.value) }
                    })}
                    min="1"
                    max="2"
                    className="text-xs mt-1"
                  />
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Carousel Settings */}
      {config.layout === 'carousel' && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Carousel Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-xs">Autoplay</Label>
                <Switch
                  checked={config.autoplay}
                  onCheckedChange={(checked) => updateConfig({ autoplay: checked })}
                />
              </div>

              {config.autoplay && (
                <div>
                  <Label className="text-xs">Autoplay Speed (ms)</Label>
                  <Input
                    type="number"
                    value={config.autoplaySpeed}
                    onChange={(e) => updateConfig({ autoplaySpeed: parseInt(e.target.value) })}
                    min="1000"
                    max="10000"
                    step="500"
                    className="text-xs mt-1"
                  />
                </div>
              )}

              <div className="flex items-center justify-between">
                <Label className="text-xs">Show Navigation Arrows</Label>
                <Switch
                  checked={config.showArrows}
                  onCheckedChange={(checked) => updateConfig({ showArrows: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-xs">Show Dots Indicator</Label>
                <Switch
                  checked={config.showDots}
                  onCheckedChange={(checked) => updateConfig({ showDots: checked })}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Display Options */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Display Options</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-xs">Show Price</Label>
            <Switch
              checked={config.showPrice}
              onCheckedChange={(checked) => updateConfig({ showPrice: checked })}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label className="text-xs">Show Description</Label>
            <Switch
              checked={config.showDescription}
              onCheckedChange={(checked) => updateConfig({ showDescription: checked })}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label className="text-xs">Show Add to Cart Button</Label>
            <Switch
              checked={config.showAddToCart}
              onCheckedChange={(checked) => updateConfig({ showAddToCart: checked })}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label className="text-xs">Show Product Badges</Label>
            <Switch
              checked={config.showBadges}
              onCheckedChange={(checked) => updateConfig({ showBadges: checked })}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
