'use client'

import React, { useState, useEffect } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  AlertCircle, 
  CheckCircle, 
  Info, 
  AlertTriangle, 
  X, 
  Bell,
  Megaphone,
  Gift,
  Zap
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface AlertNotificationWidgetBlockConfig {
  type: 'info' | 'success' | 'warning' | 'error' | 'announcement' | 'promotion'
  title?: string
  message: string
  showIcon: boolean
  dismissible: boolean
  autoHide: boolean
  autoHideDelay: number
  position: 'static' | 'fixed' | 'sticky'
  fixedPosition: 'top' | 'bottom' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  style: 'default' | 'minimal' | 'bordered' | 'filled'
  size: 'sm' | 'md' | 'lg'
  animation: 'none' | 'slideIn' | 'fadeIn' | 'bounce'
  ctaButton?: {
    text: string
    url: string
    style: 'primary' | 'secondary' | 'outline'
  }
  backgroundColor?: string
  textColor?: string
  borderColor?: string
}

interface AlertNotificationWidgetBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function AlertNotificationWidgetBlock({ block, isEditing = false }: AlertNotificationWidgetBlockProps) {
  const config = block.configuration as AlertNotificationWidgetBlockConfig
  const [isVisible, setIsVisible] = useState(true)
  const [isAnimating, setIsAnimating] = useState(false)

  const {
    type,
    title,
    message,
    showIcon,
    dismissible,
    autoHide,
    autoHideDelay,
    position,
    fixedPosition,
    style,
    size,
    animation,
    ctaButton,
    backgroundColor,
    textColor,
    borderColor,
  } = config

  // Auto-hide functionality
  useEffect(() => {
    if (autoHide && !isEditing && isVisible) {
      const timer = setTimeout(() => {
        handleDismiss()
      }, autoHideDelay * 1000)

      return () => clearTimeout(timer)
    }
  }, [autoHide, autoHideDelay, isEditing, isVisible])

  // Animation on mount
  useEffect(() => {
    if (animation !== 'none' && !isEditing) {
      setIsAnimating(true)
      const timer = setTimeout(() => setIsAnimating(false), 500)
      return () => clearTimeout(timer)
    }
  }, [animation, isEditing])

  const getAlertIcon = () => {
    const icons = {
      info: <Info className="h-5 w-5" />,
      success: <CheckCircle className="h-5 w-5" />,
      warning: <AlertTriangle className="h-5 w-5" />,
      error: <AlertCircle className="h-5 w-5" />,
      announcement: <Megaphone className="h-5 w-5" />,
      promotion: <Gift className="h-5 w-5" />
    }
    return icons[type]
  }

  const getAlertColors = () => {
    const colors = {
      info: {
        bg: backgroundColor || '#EBF8FF',
        text: textColor || '#1E40AF',
        border: borderColor || '#3B82F6',
        icon: '#3B82F6'
      },
      success: {
        bg: backgroundColor || '#F0FDF4',
        text: textColor || '#166534',
        border: borderColor || '#22C55E',
        icon: '#22C55E'
      },
      warning: {
        bg: backgroundColor || '#FFFBEB',
        text: textColor || '#92400E',
        border: borderColor || '#F59E0B',
        icon: '#F59E0B'
      },
      error: {
        bg: backgroundColor || '#FEF2F2',
        text: textColor || '#991B1B',
        border: borderColor || '#EF4444',
        icon: '#EF4444'
      },
      announcement: {
        bg: backgroundColor || '#F8FAFC',
        text: textColor || '#475569',
        border: borderColor || '#64748B',
        icon: '#64748B'
      },
      promotion: {
        bg: backgroundColor || '#FDF4FF',
        text: textColor || '#86198F',
        border: borderColor || '#C026D3',
        icon: '#C026D3'
      }
    }
    return colors[type]
  }

  const getSizeClasses = () => {
    const sizeClasses = {
      sm: 'text-sm p-3',
      md: 'text-base p-4',
      lg: 'text-lg p-6'
    }
    return sizeClasses[size]
  }

  const getStyleClasses = () => {
    const colors = getAlertColors()
    
    const styleClasses = {
      default: `border-l-4`,
      minimal: 'border-0',
      bordered: 'border-2 rounded-lg',
      filled: 'border-0 rounded-lg'
    }

    return {
      className: styleClasses[style],
      styles: {
        backgroundColor: style === 'filled' ? colors.bg : style === 'minimal' ? 'transparent' : colors.bg,
        color: colors.text,
        borderColor: colors.border,
        borderLeftColor: style === 'default' ? colors.border : undefined,
      }
    }
  }

  const getPositionClasses = () => {
    if (position === 'fixed') {
      const positionClasses = {
        top: 'fixed top-4 left-4 right-4 z-50',
        bottom: 'fixed bottom-4 left-4 right-4 z-50',
        'top-left': 'fixed top-4 left-4 z-50 max-w-md',
        'top-right': 'fixed top-4 right-4 z-50 max-w-md',
        'bottom-left': 'fixed bottom-4 left-4 z-50 max-w-md',
        'bottom-right': 'fixed bottom-4 right-4 z-50 max-w-md'
      }
      return positionClasses[fixedPosition]
    }
    
    if (position === 'sticky') {
      return 'sticky top-0 z-40'
    }
    
    return ''
  }

  const getAnimationClass = () => {
    if (!isAnimating || isEditing) return ''

    const animationClasses = {
      none: '',
      slideIn: 'animate-slide-in-down',
      fadeIn: 'animate-fade-in',
      bounce: 'animate-bounce'
    }
    return animationClasses[animation]
  }

  const handleDismiss = () => {
    if (isEditing) return
    setIsVisible(false)
  }

  const handleCtaClick = () => {
    if (isEditing || !ctaButton?.url) return
    window.open(ctaButton.url, '_blank', 'noopener,noreferrer')
  }

  if (!isVisible && !isEditing) {
    return null
  }

  const colors = getAlertColors()
  const styleConfig = getStyleClasses()

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div className={cn(
        getPositionClasses(),
        isEditing && 'relative top-0 left-0 right-0 bottom-0 max-w-none'
      )}>
        <div
          className={cn(
            'flex items-start space-x-3 transition-all duration-300',
            getSizeClasses(),
            styleConfig.className,
            getAnimationClass(),
            isEditing && 'relative'
          )}
          style={styleConfig.styles}
        >
          {/* Icon */}
          {showIcon && (
            <div className="flex-shrink-0 mt-0.5" style={{ color: colors.icon }}>
              {getAlertIcon()}
            </div>
          )}

          {/* Content */}
          <div className="flex-1 min-w-0">
            {title && (
              <h4 className="font-semibold mb-1">{title}</h4>
            )}
            <p className="text-sm leading-relaxed">{message}</p>
            
            {ctaButton && (
              <Button
                variant={ctaButton.style as any}
                size="sm"
                className="mt-3"
                onClick={handleCtaClick}
                disabled={isEditing}
              >
                {ctaButton.text}
              </Button>
            )}
          </div>

          {/* Dismiss Button */}
          {dismissible && (
            <Button
              variant="ghost"
              size="sm"
              className="flex-shrink-0 h-6 w-6 p-0 hover:bg-black/10"
              onClick={handleDismiss}
              disabled={isEditing}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        {isEditing && (
          <div className="mt-2 p-2 bg-gray-100 border border-gray-200 rounded text-xs text-gray-600">
            <strong>Alert Widget:</strong> {type} • {style} style • {position} position
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Alert Notification Widget Block Configuration Component
interface AlertNotificationWidgetBlockConfigProps {
  config: AlertNotificationWidgetBlockConfig
  onChange: (config: AlertNotificationWidgetBlockConfig) => void
}

export function AlertNotificationWidgetBlockConfig({ config, onChange }: AlertNotificationWidgetBlockConfigProps) {
  const updateConfig = (updates: Partial<AlertNotificationWidgetBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const updateCtaButton = (updates: Partial<AlertNotificationWidgetBlockConfig['ctaButton']>) => {
    updateConfig({
      ctaButton: {
        ...config.ctaButton,
        ...updates
      } as AlertNotificationWidgetBlockConfig['ctaButton']
    })
  }

  return (
    <div className="space-y-6">
      {/* Alert Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Alert Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Alert Type</Label>
            <Select
              value={config.type}
              onValueChange={(value) => updateConfig({ type: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="info">Info</SelectItem>
                <SelectItem value="success">Success</SelectItem>
                <SelectItem value="warning">Warning</SelectItem>
                <SelectItem value="error">Error</SelectItem>
                <SelectItem value="announcement">Announcement</SelectItem>
                <SelectItem value="promotion">Promotion</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-xs">Title</Label>
            <Input
              value={config.title || ''}
              onChange={(e) => updateConfig({ title: e.target.value })}
              placeholder="Alert title (optional)"
              className="mt-1"
            />
          </div>

          <div>
            <Label className="text-xs">Message</Label>
            <Textarea
              value={config.message}
              onChange={(e) => updateConfig({ message: e.target.value })}
              placeholder="Alert message"
              className="mt-1"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Style</Label>
              <Select
                value={config.style}
                onValueChange={(value) => updateConfig({ style: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">Default</SelectItem>
                  <SelectItem value="minimal">Minimal</SelectItem>
                  <SelectItem value="bordered">Bordered</SelectItem>
                  <SelectItem value="filled">Filled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Size</Label>
              <Select
                value={config.size}
                onValueChange={(value) => updateConfig({ size: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sm">Small</SelectItem>
                  <SelectItem value="md">Medium</SelectItem>
                  <SelectItem value="lg">Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Icon</Label>
              <Switch
                checked={config.showIcon}
                onCheckedChange={(checked) => updateConfig({ showIcon: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Dismissible</Label>
              <Switch
                checked={config.dismissible}
                onCheckedChange={(checked) => updateConfig({ dismissible: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Auto Hide</Label>
              <Switch
                checked={config.autoHide}
                onCheckedChange={(checked) => updateConfig({ autoHide: checked })}
              />
            </div>
          </div>

          {config.autoHide && (
            <div>
              <Label className="text-xs">Auto Hide Delay (seconds)</Label>
              <Input
                type="number"
                min="1"
                max="60"
                value={config.autoHideDelay}
                onChange={(e) => updateConfig({ autoHideDelay: parseInt(e.target.value) || 5 })}
                className="mt-1"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Position Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Position Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Position Type</Label>
            <Select
              value={config.position}
              onValueChange={(value) => updateConfig({ position: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="static">Static</SelectItem>
                <SelectItem value="fixed">Fixed</SelectItem>
                <SelectItem value="sticky">Sticky</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {config.position === 'fixed' && (
            <div>
              <Label className="text-xs">Fixed Position</Label>
              <Select
                value={config.fixedPosition}
                onValueChange={(value) => updateConfig({ fixedPosition: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="top">Top</SelectItem>
                  <SelectItem value="bottom">Bottom</SelectItem>
                  <SelectItem value="top-left">Top Left</SelectItem>
                  <SelectItem value="top-right">Top Right</SelectItem>
                  <SelectItem value="bottom-left">Bottom Left</SelectItem>
                  <SelectItem value="bottom-right">Bottom Right</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <div>
            <Label className="text-xs">Animation</Label>
            <Select
              value={config.animation}
              onValueChange={(value) => updateConfig({ animation: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                <SelectItem value="slideIn">Slide In</SelectItem>
                <SelectItem value="fadeIn">Fade In</SelectItem>
                <SelectItem value="bounce">Bounce</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Call to Action */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Call to Action</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Button Text</Label>
            <Input
              value={config.ctaButton?.text || ''}
              onChange={(e) => updateCtaButton({ text: e.target.value })}
              placeholder="Learn More"
              className="mt-1"
            />
          </div>

          <div>
            <Label className="text-xs">Button URL</Label>
            <Input
              value={config.ctaButton?.url || ''}
              onChange={(e) => updateCtaButton({ url: e.target.value })}
              placeholder="https://..."
              className="mt-1"
            />
          </div>

          <div>
            <Label className="text-xs">Button Style</Label>
            <Select
              value={config.ctaButton?.style || 'primary'}
              onValueChange={(value) => updateCtaButton({ style: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="primary">Primary</SelectItem>
                <SelectItem value="secondary">Secondary</SelectItem>
                <SelectItem value="outline">Outline</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
