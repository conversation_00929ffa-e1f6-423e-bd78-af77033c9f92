'use client'

import React, { useState, useEffect, useRef } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Plus, Trash2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ProgressBarWidgetBlockConfig {
  title?: string
  progressBars: ProgressBarConfig[]
  layout: 'vertical' | 'horizontal'
  showLabels: boolean
  showPercentage: boolean
  showValues: boolean
  animateOnView: boolean
  animationDuration: number
  spacing: 'none' | 'sm' | 'md' | 'lg'
  backgroundColor: string
  borderRadius: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
}

interface ProgressBarConfig {
  id: string
  label: string
  value: number
  maxValue: number
  color: string
  backgroundColor?: string
  height: 'sm' | 'md' | 'lg' | 'xl'
  style: 'default' | 'rounded' | 'striped' | 'gradient'
  showIcon?: boolean
  icon?: string
  description?: string
}

interface ProgressBarWidgetBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function ProgressBarWidgetBlock({ block, isEditing = false }: ProgressBarWidgetBlockProps) {
  const config = block.configuration as ProgressBarWidgetBlockConfig
  const [animatedValues, setAnimatedValues] = useState<Record<string, number>>({})
  const [isVisible, setIsVisible] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  const {
    title,
    progressBars,
    layout,
    showLabels,
    showPercentage,
    showValues,
    animateOnView,
    animationDuration,
    spacing,
    backgroundColor,
    borderRadius,
    padding,
  } = config

  // Intersection Observer for animation trigger
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => observer.disconnect()
  }, [isVisible])

  // Progress animation
  useEffect(() => {
    if (!isVisible || !animateOnView || isEditing) return

    const duration = animationDuration * 1000
    const startTime = Date.now()

    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)

      const newValues: Record<string, number> = {}
      progressBars.forEach(bar => {
        const easeOutQuart = 1 - Math.pow(1 - progress, 4)
        newValues[bar.id] = bar.value * easeOutQuart
      })

      setAnimatedValues(newValues)

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }

    animate()
  }, [isVisible, progressBars, animateOnView, animationDuration, isEditing])

  const getProgressValue = (bar: ProgressBarConfig) => {
    if (animateOnView && isVisible && !isEditing) {
      return animatedValues[bar.id] ?? 0
    }
    return bar.value
  }

  const getProgressPercentage = (bar: ProgressBarConfig) => {
    const value = getProgressValue(bar)
    return Math.min((value / bar.maxValue) * 100, 100)
  }

  const getHeightClass = (height: string) => {
    const heightClasses = {
      sm: 'h-2',
      md: 'h-3',
      lg: 'h-4',
      xl: 'h-6'
    }
    return heightClasses[height as keyof typeof heightClasses]
  }

  const getSpacingClass = () => {
    const spacingClasses = {
      none: 'space-y-0',
      sm: 'space-y-2',
      md: 'space-y-4',
      lg: 'space-y-6'
    }
    return spacingClasses[spacing]
  }

  const getProgressBarStyles = (bar: ProgressBarConfig) => {
    const percentage = getProgressPercentage(bar)
    
    let backgroundStyle = bar.color
    
    if (bar.style === 'gradient') {
      backgroundStyle = `linear-gradient(90deg, ${bar.color}, ${bar.color}80)`
    }

    return {
      width: `${percentage}%`,
      background: backgroundStyle,
      transition: animateOnView ? `width ${animationDuration}s ease-out` : 'none'
    }
  }

  const getProgressBarClasses = (bar: ProgressBarConfig) => {
    return cn(
      getHeightClass(bar.height),
      'transition-all duration-300',
      bar.style === 'rounded' && 'rounded-full',
      bar.style === 'striped' && 'bg-stripes'
    )
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
  }

  // Demo progress bars for editing mode
  const demoProgressBars = [
    {
      id: 'demo-1',
      label: 'HTML/CSS',
      value: 90,
      maxValue: 100,
      color: '#3B82F6',
      height: 'md' as const,
      style: 'rounded' as const,
      description: 'Frontend development skills'
    },
    {
      id: 'demo-2',
      label: 'JavaScript',
      value: 85,
      maxValue: 100,
      color: '#10B981',
      height: 'md' as const,
      style: 'rounded' as const,
      description: 'Programming language proficiency'
    },
    {
      id: 'demo-3',
      label: 'React',
      value: 80,
      maxValue: 100,
      color: '#F59E0B',
      height: 'md' as const,
      style: 'rounded' as const,
      description: 'Framework expertise'
    },
    {
      id: 'demo-4',
      label: 'Node.js',
      value: 75,
      maxValue: 100,
      color: '#EF4444',
      height: 'md' as const,
      style: 'rounded' as const,
      description: 'Backend development'
    }
  ]

  const barsToRender = isEditing ? demoProgressBars : progressBars

  const renderProgressBar = (bar: ProgressBarConfig) => {
    const percentage = getProgressPercentage(bar)
    const currentValue = getProgressValue(bar)

    return (
      <div key={bar.id} className="w-full">
        {/* Label and Value */}
        {(showLabels || showPercentage || showValues) && (
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              {bar.showIcon && bar.icon && (
                <span className="text-sm">{bar.icon}</span>
              )}
              {showLabels && (
                <span className="text-sm font-medium">{bar.label}</span>
              )}
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              {showValues && (
                <span>{Math.round(currentValue)}/{bar.maxValue}</span>
              )}
              {showPercentage && (
                <span>{Math.round(percentage)}%</span>
              )}
            </div>
          </div>
        )}

        {/* Progress Bar */}
        <div 
          className={cn(
            'w-full bg-gray-200 overflow-hidden',
            getHeightClass(bar.height),
            bar.style === 'rounded' && 'rounded-full'
          )}
          style={{ backgroundColor: bar.backgroundColor || '#E5E7EB' }}
        >
          <div
            className={getProgressBarClasses(bar)}
            style={getProgressBarStyles(bar)}
          />
        </div>

        {/* Description */}
        {bar.description && (
          <p className="text-xs text-gray-500 mt-1">{bar.description}</p>
        )}
      </div>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div ref={containerRef} style={containerStyles}>
        {title && (
          <h3 className="font-semibold mb-4">{title}</h3>
        )}
        
        <div className={cn(
          layout === 'horizontal' ? 'grid grid-cols-1 md:grid-cols-2 gap-6' : 'flex flex-col',
          layout === 'vertical' && getSpacingClass()
        )}>
          {barsToRender.map(renderProgressBar)}
        </div>
        
        {isEditing && (
          <div className="mt-4 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700">
            <strong>Progress Bar Widget:</strong> {barsToRender.length} bars • {layout} layout • {animateOnView ? 'animated' : 'static'}
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Progress Bar Widget Block Configuration Component
interface ProgressBarWidgetBlockConfigProps {
  config: ProgressBarWidgetBlockConfig
  onChange: (config: ProgressBarWidgetBlockConfig) => void
}

export function ProgressBarWidgetBlockConfig({ config, onChange }: ProgressBarWidgetBlockConfigProps) {
  const updateConfig = (updates: Partial<ProgressBarWidgetBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const addProgressBar = () => {
    const newBar: ProgressBarConfig = {
      id: `bar-${Date.now()}`,
      label: 'New Progress Bar',
      value: 50,
      maxValue: 100,
      color: '#3B82F6',
      height: 'md',
      style: 'rounded'
    }
    
    updateConfig({
      progressBars: [...config.progressBars, newBar]
    })
  }

  const updateProgressBar = (index: number, updates: Partial<ProgressBarConfig>) => {
    const updatedBars = [...config.progressBars]
    updatedBars[index] = { ...updatedBars[index], ...updates }
    updateConfig({ progressBars: updatedBars })
  }

  const removeProgressBar = (index: number) => {
    const updatedBars = config.progressBars.filter((_, i) => i !== index)
    updateConfig({ progressBars: updatedBars })
  }

  return (
    <div className="space-y-6">
      {/* Widget Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Widget Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Title</Label>
            <Input
              value={config.title || ''}
              onChange={(e) => updateConfig({ title: e.target.value })}
              placeholder="Skills & Progress"
              className="mt-1"
            />
          </div>

          <div>
            <Label className="text-xs">Layout</Label>
            <Select
              value={config.layout}
              onValueChange={(value) => updateConfig({ layout: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="vertical">Vertical</SelectItem>
                <SelectItem value="horizontal">Horizontal</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Labels</Label>
              <Switch
                checked={config.showLabels}
                onCheckedChange={(checked) => updateConfig({ showLabels: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Percentage</Label>
              <Switch
                checked={config.showPercentage}
                onCheckedChange={(checked) => updateConfig({ showPercentage: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Values</Label>
              <Switch
                checked={config.showValues}
                onCheckedChange={(checked) => updateConfig({ showValues: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Animate on View</Label>
              <Switch
                checked={config.animateOnView}
                onCheckedChange={(checked) => updateConfig({ animateOnView: checked })}
              />
            </div>
          </div>

          {config.animateOnView && (
            <div>
              <Label className="text-xs">Animation Duration (seconds)</Label>
              <Input
                type="number"
                min="0.5"
                max="5"
                step="0.1"
                value={config.animationDuration}
                onChange={(e) => updateConfig({ animationDuration: parseFloat(e.target.value) || 1 })}
                className="mt-1"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Progress Bars */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm">Progress Bars</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={addProgressBar}
            className="h-8 px-2"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Bar
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.progressBars.map((bar, index) => (
            <div key={bar.id} className="border rounded-lg p-3 space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Progress Bar {index + 1}</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeProgressBar(index)}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
              
              <div>
                <Label className="text-xs">Label</Label>
                <Input
                  value={bar.label}
                  onChange={(e) => updateProgressBar(index, { label: e.target.value })}
                  className="mt-1"
                  placeholder="Progress bar label"
                />
              </div>
              
              <div className="grid grid-cols-3 gap-2">
                <div>
                  <Label className="text-xs">Value</Label>
                  <Input
                    type="number"
                    min="0"
                    value={bar.value}
                    onChange={(e) => updateProgressBar(index, { value: parseInt(e.target.value) || 0 })}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="text-xs">Max Value</Label>
                  <Input
                    type="number"
                    min="1"
                    value={bar.maxValue}
                    onChange={(e) => updateProgressBar(index, { maxValue: parseInt(e.target.value) || 100 })}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="text-xs">Color</Label>
                  <Input
                    type="color"
                    value={bar.color}
                    onChange={(e) => updateProgressBar(index, { color: e.target.value })}
                    className="mt-1 h-8"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Height</Label>
                  <Select
                    value={bar.height}
                    onValueChange={(value) => updateProgressBar(index, { height: value as any })}
                  >
                    <SelectTrigger className="w-full mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sm">Small</SelectItem>
                      <SelectItem value="md">Medium</SelectItem>
                      <SelectItem value="lg">Large</SelectItem>
                      <SelectItem value="xl">Extra Large</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-xs">Style</Label>
                  <Select
                    value={bar.style}
                    onValueChange={(value) => updateProgressBar(index, { style: value as any })}
                  >
                    <SelectTrigger className="w-full mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">Default</SelectItem>
                      <SelectItem value="rounded">Rounded</SelectItem>
                      <SelectItem value="striped">Striped</SelectItem>
                      <SelectItem value="gradient">Gradient</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          ))}
          
          {config.progressBars.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              <p className="text-sm">No progress bars yet.</p>
              <p className="text-xs">Click "Add Bar" to get started.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
