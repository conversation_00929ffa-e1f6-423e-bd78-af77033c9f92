'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { cn } from '@/lib/utils'

interface MissionStatementConfig {
  title: string
  content: string
  alignment: 'left' | 'center' | 'right'
  backgroundStyle: 'none' | 'light' | 'dark' | 'gradient' | 'custom'
  backgroundColor: string
  textColor: string
  padding: 'compact' | 'normal' | 'spacious'
  borderRadius: 'none' | 'small' | 'medium' | 'large'
  maxWidth: 'full' | 'container' | 'narrow' | 'wide'
}

interface MissionStatementBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function MissionStatementBlock({ block, isEditing = false }: MissionStatementBlockProps) {
  const config = block.configuration as MissionStatementConfig

  const {
    title,
    content,
    alignment = 'center',
    backgroundStyle = 'light',
    backgroundColor = '#f9fafb',
    textColor = 'inherit',
    padding = 'normal',
    borderRadius = 'medium',
    maxWidth = 'container'
  } = config

  const getAlignmentClasses = () => {
    switch (alignment) {
      case 'left':
        return 'text-left'
      case 'right':
        return 'text-right'
      default:
        return 'text-center'
    }
  }

  const getPaddingClasses = () => {
    switch (padding) {
      case 'compact':
        return 'p-6 md:p-8'
      case 'spacious':
        return 'p-10 md:p-16'
      default:
        return 'p-8 md:p-12'
    }
  }

  const getBorderRadiusClasses = () => {
    switch (borderRadius) {
      case 'none':
        return 'rounded-none'
      case 'small':
        return 'rounded-sm'
      case 'large':
        return 'rounded-xl'
      default:
        return 'rounded-lg'
    }
  }

  const getMaxWidthClasses = () => {
    switch (maxWidth) {
      case 'full':
        return 'max-w-full'
      case 'narrow':
        return 'max-w-2xl mx-auto'
      case 'wide':
        return 'max-w-5xl mx-auto'
      default:
        return 'max-w-3xl mx-auto'
    }
  }

  const getBackgroundStyle = () => {
    switch (backgroundStyle) {
      case 'none':
        return {}
      case 'dark':
        return { 
          backgroundColor: '#1f2937',
          color: '#ffffff'
        }
      case 'gradient':
        return {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: '#ffffff'
        }
      case 'custom':
        return {
          backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : undefined,
          color: textColor !== 'inherit' ? textColor : undefined
        }
      default: // light
        return {
          backgroundColor: '#f9fafb',
          color: textColor !== 'inherit' ? textColor : undefined
        }
    }
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div className="container px-4 md:px-6 py-6 md:py-10">
        <div className={getMaxWidthClasses()}>
          <div 
            className={cn(
              getPaddingClasses(),
              getBorderRadiusClasses(),
              getAlignmentClasses()
            )}
            style={getBackgroundStyle()}
          >
            {title && (
              <h2 className="text-2xl font-bold font-montserrat mb-4">
                {title}
              </h2>
            )}
            <p className="text-lg font-light leading-relaxed">
              {content}
            </p>
          </div>
        </div>
      </div>
    </BaseBlock>
  )
}

// Default configuration for the mission statement block
export const missionStatementBlockConfig = {
  title: 'Our Mission',
  content: 'To create clothing that empowers children to be themselves while providing parents with the peace of mind that comes from knowing their kids are wearing high-quality, comfortable, and stylish pieces that will last through all of life\'s adventures.',
  alignment: 'center',
  backgroundStyle: 'light',
  backgroundColor: '#f9fafb',
  textColor: 'inherit',
  padding: 'normal',
  borderRadius: 'medium',
  maxWidth: 'container'
}

// Configuration schema for the Page Builder
export const missionStatementBlockSchema = {
  type: 'object',
  properties: {
    title: {
      type: 'string',
      title: 'Title',
      default: 'Our Mission'
    },
    content: {
      type: 'string',
      title: 'Mission Statement',
      format: 'textarea',
      default: missionStatementBlockConfig.content
    },
    alignment: {
      type: 'string',
      title: 'Text Alignment',
      enum: ['left', 'center', 'right'],
      enumNames: ['Left', 'Center', 'Right'],
      default: 'center'
    },
    backgroundStyle: {
      type: 'string',
      title: 'Background Style',
      enum: ['none', 'light', 'dark', 'gradient', 'custom'],
      enumNames: ['None', 'Light Gray', 'Dark', 'Gradient', 'Custom Color'],
      default: 'light'
    },
    backgroundColor: {
      type: 'string',
      title: 'Custom Background Color',
      format: 'color',
      default: '#f9fafb',
      description: 'Only used when Background Style is set to Custom'
    },
    textColor: {
      type: 'string',
      title: 'Text Color',
      format: 'color',
      default: 'inherit'
    },
    padding: {
      type: 'string',
      title: 'Padding',
      enum: ['compact', 'normal', 'spacious'],
      enumNames: ['Compact', 'Normal', 'Spacious'],
      default: 'normal'
    },
    borderRadius: {
      type: 'string',
      title: 'Border Radius',
      enum: ['none', 'small', 'medium', 'large'],
      enumNames: ['None', 'Small', 'Medium', 'Large'],
      default: 'medium'
    },
    maxWidth: {
      type: 'string',
      title: 'Maximum Width',
      enum: ['full', 'container', 'narrow', 'wide'],
      enumNames: ['Full Width', 'Container', 'Narrow', 'Wide'],
      default: 'container'
    }
  },
  required: ['title', 'content']
}
