'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { 
  ShoppingBag, 
  Truck, 
  RotateCcw, 
  CreditCard, 
  Users, 
  Settings, 
  HelpCircle, 
  MessageCircle,
  Shield,
  Star,
  Gift,
  Phone
} from 'lucide-react'

interface HelpTopic {
  id: string
  title: string
  description: string
  icon: string
  link: string
  isPopular?: boolean
  articleCount?: number
  category?: string
}

interface HelpTopicsGridConfig {
  title: string
  subtitle?: string
  description?: string
  topics: HelpTopic[]
  columns: 2 | 3 | 4
  showPopularBadge: boolean
  showArticleCount: boolean
  cardStyle: 'default' | 'minimal' | 'bordered' | 'shadow'
  spacing: 'compact' | 'normal' | 'spacious'
  backgroundColor: string
  textColor: string
  iconColor: string
  searchSection: {
    enabled: boolean
    title: string
    description: string
    placeholder: string
  }
}

interface HelpTopicsGridBlockProps {
  block: PageBlock
  isEditing?: boolean
}

// Icon mapping for help topics
const topicIcons = {
  'shopping-bag': ShoppingBag,
  'truck': Truck,
  'rotate-ccw': RotateCcw,
  'credit-card': CreditCard,
  'users': Users,
  'settings': Settings,
  'help-circle': HelpCircle,
  'message-circle': MessageCircle,
  'shield': Shield,
  'star': Star,
  'gift': Gift,
  'phone': Phone
}

export function HelpTopicsGridBlock({ block, isEditing = false }: HelpTopicsGridBlockProps) {
  const config = block.configuration as HelpTopicsGridConfig

  const {
    title,
    subtitle,
    description,
    topics = [],
    columns = 3,
    showPopularBadge = true,
    showArticleCount = true,
    cardStyle = 'default',
    spacing = 'normal',
    backgroundColor = 'transparent',
    textColor = 'inherit',
    iconColor = '#012169',
    searchSection = {
      enabled: true,
      title: 'Can\'t find what you\'re looking for?',
      description: 'Search our knowledge base or contact our support team.',
      placeholder: 'Search help articles...'
    }
  } = config

  const getSpacingClasses = () => {
    switch (spacing) {
      case 'compact':
        return 'py-6 gap-6'
      case 'spacious':
        return 'py-16 gap-16'
      default:
        return 'py-12 gap-12'
    }
  }

  const getColumnClasses = () => {
    switch (columns) {
      case 2:
        return 'grid-cols-1 md:grid-cols-2'
      case 4:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
      default:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
    }
  }

  const getCardClasses = () => {
    switch (cardStyle) {
      case 'minimal':
        return 'hover:bg-gray-50 transition-colors'
      case 'bordered':
        return 'border-2 hover:border-[#012169] transition-colors'
      case 'shadow':
        return 'shadow-lg hover:shadow-xl transition-shadow'
      default:
        return 'hover:shadow-md transition-shadow'
    }
  }

  const renderTopicIcon = (iconName: string) => {
    const IconComponent = topicIcons[iconName as keyof typeof topicIcons] || HelpCircle
    return (
      <IconComponent 
        className="h-8 w-8" 
        style={{ color: iconColor }}
      />
    )
  }

  const renderTopicCard = (topic: HelpTopic) => {
    const CardComponent = cardStyle === 'minimal' ? 'div' : Card
    const ContentComponent = cardStyle === 'minimal' ? 'div' : CardContent

    const cardContent = (
      <ContentComponent className={cn(
        cardStyle === 'minimal' ? 'p-6' : 'p-6',
        'h-full flex flex-col'
      )}>
        <div className="flex items-start gap-4 mb-4">
          <div className="flex-shrink-0">
            {renderTopicIcon(topic.icon)}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2 mb-2">
              <h3 className="font-medium text-lg leading-tight">
                {topic.title}
              </h3>
              {showPopularBadge && topic.isPopular && (
                <Badge variant="secondary" className="text-xs">
                  Popular
                </Badge>
              )}
            </div>
            <p className="text-sm text-muted-foreground leading-relaxed">
              {topic.description}
            </p>
          </div>
        </div>
        
        <div className="mt-auto">
          {showArticleCount && topic.articleCount && (
            <p className="text-xs text-muted-foreground">
              {topic.articleCount} article{topic.articleCount !== 1 ? 's' : ''}
            </p>
          )}
        </div>
      </ContentComponent>
    )

    if (isEditing) {
      return (
        <div key={topic.id} className={cn(getCardClasses(), 'cursor-pointer')}>
          {cardContent}
        </div>
      )
    }

    return (
      <a
        key={topic.id}
        href={topic.link}
        className={cn(
          'block',
          getCardClasses(),
          'cursor-pointer'
        )}
      >
        {cardContent}
      </a>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div 
        className="container px-4 md:px-6"
        style={{ 
          backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : undefined,
          color: textColor !== 'inherit' ? textColor : undefined
        }}
      >
        <div className={getSpacingClasses()}>
          {/* Header */}
          <div className="text-center mb-12">
            {title && (
              <h1 className="text-3xl md:text-4xl font-bold font-montserrat mb-4">
                {title}
              </h1>
            )}
            {subtitle && (
              <h2 className="text-xl text-muted-foreground mb-4">
                {subtitle}
              </h2>
            )}
            {description && (
              <p className="text-lg text-muted-foreground font-light max-w-2xl mx-auto">
                {description}
              </p>
            )}
          </div>

          {/* Topics Grid */}
          <div className={cn('grid gap-6 mb-12', getColumnClasses())}>
            {topics.map((topic) => renderTopicCard(topic))}
          </div>

          {/* Search Section */}
          {searchSection.enabled && (
            <div className="text-center bg-gray-50 rounded-lg p-8">
              <h2 className="text-xl font-bold font-montserrat mb-4">
                {searchSection.title}
              </h2>
              <p className="text-muted-foreground mb-6">
                {searchSection.description}
              </p>
              <div className="max-w-md mx-auto">
                <div className="relative">
                  <input
                    type="text"
                    placeholder={searchSection.placeholder}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#012169] focus:border-transparent"
                    disabled={isEditing}
                  />
                  <button
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-[#012169] text-white px-4 py-1 rounded-md text-sm hover:bg-[#012169]/90 transition-colors"
                    disabled={isEditing}
                  >
                    Search
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </BaseBlock>
  )
}

// Default configuration for the help topics grid block
export const helpTopicsGridBlockConfig = {
  title: 'How Can We Help?',
  subtitle: 'Find answers to common questions',
  description: 'Browse our help topics below or search for specific information.',
  topics: [
    {
      id: 'orders',
      title: 'Orders & Checkout',
      description: 'Help with placing orders, payment methods, and checkout issues.',
      icon: 'shopping-bag',
      link: '/help/orders',
      isPopular: true,
      articleCount: 12,
      category: 'Shopping'
    },
    {
      id: 'shipping',
      title: 'Shipping & Delivery',
      description: 'Information about shipping options, delivery times, and tracking.',
      icon: 'truck',
      link: '/help/shipping',
      isPopular: true,
      articleCount: 8,
      category: 'Shipping'
    },
    {
      id: 'returns',
      title: 'Returns & Exchanges',
      description: 'How to return or exchange items, refund policies, and procedures.',
      icon: 'rotate-ccw',
      link: '/help/returns',
      isPopular: true,
      articleCount: 6,
      category: 'Returns'
    },
    {
      id: 'payments',
      title: 'Payment & Billing',
      description: 'Payment methods, billing issues, and transaction problems.',
      icon: 'credit-card',
      link: '/help/payments',
      articleCount: 5,
      category: 'Payments'
    },
    {
      id: 'account',
      title: 'Account & Profile',
      description: 'Managing your account, profile settings, and login issues.',
      icon: 'users',
      link: '/help/account',
      articleCount: 7,
      category: 'Account'
    },
    {
      id: 'products',
      title: 'Product Information',
      description: 'Size guides, product care, materials, and specifications.',
      icon: 'star',
      link: '/help/products',
      articleCount: 15,
      category: 'Products'
    }
  ],
  columns: 3,
  showPopularBadge: true,
  showArticleCount: true,
  cardStyle: 'default',
  spacing: 'normal',
  backgroundColor: 'transparent',
  textColor: 'inherit',
  iconColor: '#012169',
  searchSection: {
    enabled: true,
    title: 'Can\'t find what you\'re looking for?',
    description: 'Search our knowledge base or contact our support team.',
    placeholder: 'Search help articles...'
  }
}

// Configuration schema for the Page Builder
export const helpTopicsGridBlockSchema = {
  type: 'object',
  properties: {
    title: {
      type: 'string',
      title: 'Title',
      default: 'How Can We Help?'
    },
    subtitle: {
      type: 'string',
      title: 'Subtitle (Optional)',
      default: 'Find answers to common questions'
    },
    description: {
      type: 'string',
      title: 'Description (Optional)',
      format: 'textarea',
      default: helpTopicsGridBlockConfig.description
    },
    topics: {
      type: 'array',
      title: 'Help Topics',
      items: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            title: 'Topic ID',
            description: 'Unique identifier for the topic'
          },
          title: {
            type: 'string',
            title: 'Topic Title'
          },
          description: {
            type: 'string',
            title: 'Description'
          },
          icon: {
            type: 'string',
            title: 'Icon',
            enum: Object.keys(topicIcons),
            default: 'help-circle'
          },
          link: {
            type: 'string',
            title: 'Link URL'
          },
          isPopular: {
            type: 'boolean',
            title: 'Mark as Popular',
            default: false
          },
          articleCount: {
            type: 'number',
            title: 'Article Count (Optional)',
            minimum: 0
          },
          category: {
            type: 'string',
            title: 'Category (Optional)'
          }
        },
        required: ['id', 'title', 'description', 'icon', 'link']
      },
      default: helpTopicsGridBlockConfig.topics
    },
    columns: {
      type: 'number',
      title: 'Columns',
      enum: [2, 3, 4],
      default: 3
    },
    showPopularBadge: {
      type: 'boolean',
      title: 'Show Popular Badge',
      default: true
    },
    showArticleCount: {
      type: 'boolean',
      title: 'Show Article Count',
      default: true
    },
    cardStyle: {
      type: 'string',
      title: 'Card Style',
      enum: ['default', 'minimal', 'bordered', 'shadow'],
      enumNames: ['Default', 'Minimal', 'Bordered', 'Shadow'],
      default: 'default'
    },
    spacing: {
      type: 'string',
      title: 'Spacing',
      enum: ['compact', 'normal', 'spacious'],
      enumNames: ['Compact', 'Normal', 'Spacious'],
      default: 'normal'
    },
    iconColor: {
      type: 'string',
      title: 'Icon Color',
      format: 'color',
      default: '#012169'
    },
    searchSection: {
      type: 'object',
      title: 'Search Section',
      properties: {
        enabled: {
          type: 'boolean',
          title: 'Show Search Section',
          default: true
        },
        title: {
          type: 'string',
          title: 'Search Section Title',
          default: 'Can\'t find what you\'re looking for?'
        },
        description: {
          type: 'string',
          title: 'Search Section Description',
          default: helpTopicsGridBlockConfig.searchSection.description
        },
        placeholder: {
          type: 'string',
          title: 'Search Placeholder',
          default: 'Search help articles...'
        }
      }
    },
    backgroundColor: {
      type: 'string',
      title: 'Background Color',
      format: 'color',
      default: 'transparent'
    },
    textColor: {
      type: 'string',
      title: 'Text Color',
      format: 'color',
      default: 'inherit'
    }
  },
  required: ['title', 'topics']
}
