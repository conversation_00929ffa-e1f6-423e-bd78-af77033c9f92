'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { 
  Truck, 
  Clock, 
  MapPin, 
  Package, 
  Shield, 
  CreditCard,
  Plane,
  Home,
  Building,
  Globe
} from 'lucide-react'

interface ShippingOption {
  id: string
  name: string
  description: string
  icon: string
  price: string
  deliveryTime: string
  features: string[]
  isPopular?: boolean
  isFree?: boolean
  restrictions?: string[]
}

interface ShippingInfoCardsConfig {
  title: string
  subtitle?: string
  description?: string
  options: ShippingOption[]
  layout: 'cards' | 'list' | 'grid'
  columns: 2 | 3 | 4
  showPricing: boolean
  showFeatures: boolean
  showRestrictions: boolean
  cardStyle: 'default' | 'minimal' | 'bordered' | 'elevated'
  spacing: 'compact' | 'normal' | 'spacious'
  backgroundColor: string
  textColor: string
  iconColor: string
  freeShippingThreshold: {
    enabled: boolean
    amount: string
    currency: string
    message: string
  }
}

interface ShippingInfoCardsBlockProps {
  block: PageBlock
  isEditing?: boolean
}

// Icon mapping for shipping options
const shippingIcons = {
  truck: Truck,
  clock: Clock,
  'map-pin': MapPin,
  package: Package,
  shield: Shield,
  'credit-card': CreditCard,
  plane: Plane,
  home: Home,
  building: Building,
  globe: Globe
}

export function ShippingInfoCardsBlock({ block, isEditing = false }: ShippingInfoCardsBlockProps) {
  const config = block.configuration as ShippingInfoCardsConfig

  const {
    title,
    subtitle,
    description,
    options = [],
    layout = 'cards',
    columns = 3,
    showPricing = true,
    showFeatures = true,
    showRestrictions = false,
    cardStyle = 'default',
    spacing = 'normal',
    backgroundColor = 'transparent',
    textColor = 'inherit',
    iconColor = '#012169',
    freeShippingThreshold = {
      enabled: true,
      amount: 'R1,500',
      currency: 'ZAR',
      message: 'Free standard shipping on orders over R1,500'
    }
  } = config

  const getSpacingClasses = () => {
    switch (spacing) {
      case 'compact':
        return 'py-6 gap-4'
      case 'spacious':
        return 'py-16 gap-8'
      default:
        return 'py-12 gap-6'
    }
  }

  const getColumnClasses = () => {
    switch (columns) {
      case 2:
        return 'grid-cols-1 md:grid-cols-2'
      case 4:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
      default:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
    }
  }

  const getCardClasses = () => {
    switch (cardStyle) {
      case 'minimal':
        return 'border-0 shadow-none bg-transparent'
      case 'bordered':
        return 'border-2 shadow-none'
      case 'elevated':
        return 'shadow-lg hover:shadow-xl transition-shadow'
      default:
        return 'shadow-sm hover:shadow-md transition-shadow'
    }
  }

  const renderShippingIcon = (iconName: string) => {
    const IconComponent = shippingIcons[iconName as keyof typeof shippingIcons] || Truck
    return (
      <IconComponent 
        className="h-8 w-8" 
        style={{ color: iconColor }}
      />
    )
  }

  const renderShippingCard = (option: ShippingOption) => (
    <Card key={option.id} className={cn(getCardClasses(), 'h-full')}>
      <CardContent className="p-6 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-start gap-4 mb-4">
          <div className="flex-shrink-0">
            {renderShippingIcon(option.icon)}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2 mb-2">
              <h3 className="font-semibold text-lg leading-tight">
                {option.name}
              </h3>
              <div className="flex gap-1">
                {option.isPopular && (
                  <Badge variant="default" className="text-xs">
                    Popular
                  </Badge>
                )}
                {option.isFree && (
                  <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                    Free
                  </Badge>
                )}
              </div>
            </div>
            <p className="text-sm text-muted-foreground mb-3">
              {option.description}
            </p>
          </div>
        </div>

        {/* Pricing and Delivery Time */}
        {(showPricing || option.deliveryTime) && (
          <div className="flex justify-between items-center mb-4 text-sm">
            {showPricing && (
              <div className="font-medium">
                {option.isFree ? 'Free' : option.price}
              </div>
            )}
            {option.deliveryTime && (
              <div className="text-muted-foreground">
                {option.deliveryTime}
              </div>
            )}
          </div>
        )}

        {/* Features */}
        {showFeatures && option.features && option.features.length > 0 && (
          <div className="mb-4">
            <ul className="space-y-1 text-sm">
              {option.features.map((feature, index) => (
                <li key={index} className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-[#012169] rounded-full flex-shrink-0" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Restrictions */}
        {showRestrictions && option.restrictions && option.restrictions.length > 0 && (
          <div className="mt-auto pt-4 border-t">
            <p className="text-xs text-muted-foreground mb-2">Restrictions:</p>
            <ul className="space-y-1 text-xs text-muted-foreground">
              {option.restrictions.map((restriction, index) => (
                <li key={index}>• {restriction}</li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  )

  const renderShippingList = (option: ShippingOption) => (
    <div key={option.id} className="flex items-start gap-4 p-4 border rounded-lg">
      <div className="flex-shrink-0">
        {renderShippingIcon(option.icon)}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between gap-4 mb-2">
          <div>
            <h3 className="font-semibold">{option.name}</h3>
            <p className="text-sm text-muted-foreground">{option.description}</p>
          </div>
          <div className="text-right flex-shrink-0">
            {showPricing && (
              <div className="font-medium">
                {option.isFree ? 'Free' : option.price}
              </div>
            )}
            {option.deliveryTime && (
              <div className="text-sm text-muted-foreground">
                {option.deliveryTime}
              </div>
            )}
          </div>
        </div>
        {showFeatures && option.features && option.features.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-2">
            {option.features.map((feature, index) => (
              <span key={index} className="text-xs bg-gray-100 px-2 py-1 rounded">
                {feature}
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  )

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div 
        className="container px-4 md:px-6"
        style={{ 
          backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : undefined,
          color: textColor !== 'inherit' ? textColor : undefined
        }}
      >
        <div className={getSpacingClasses()}>
          {/* Header */}
          <div className="text-center mb-8">
            {title && (
              <h1 className="text-3xl font-bold font-montserrat mb-4">
                {title}
              </h1>
            )}
            {subtitle && (
              <h2 className="text-xl text-muted-foreground mb-4">
                {subtitle}
              </h2>
            )}
            {description && (
              <p className="text-lg text-muted-foreground font-light max-w-2xl mx-auto">
                {description}
              </p>
            )}
          </div>

          {/* Free Shipping Threshold */}
          {freeShippingThreshold.enabled && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-8 text-center">
              <p className="text-green-800 font-medium">
                {freeShippingThreshold.message}
              </p>
            </div>
          )}

          {/* Shipping Options */}
          {layout === 'list' ? (
            <div className="space-y-4 max-w-4xl mx-auto">
              {options.map((option) => renderShippingList(option))}
            </div>
          ) : (
            <div className={cn('grid gap-6', getColumnClasses())}>
              {options.map((option) => renderShippingCard(option))}
            </div>
          )}
        </div>
      </div>
    </BaseBlock>
  )
}

// Default configuration for the shipping info cards block
export const shippingInfoCardsBlockConfig = {
  title: 'Shipping Information',
  subtitle: 'Choose the delivery option that works best for you',
  description: 'We offer multiple shipping options to ensure your order arrives when you need it.',
  options: [
    {
      id: 'standard',
      name: 'Standard Shipping',
      description: 'Reliable delivery for everyday orders',
      icon: 'truck',
      price: 'R99',
      deliveryTime: '3-5 business days',
      features: [
        'Tracking included',
        'Signature on delivery',
        'Insurance up to R5,000'
      ],
      isPopular: true,
      isFree: false,
      restrictions: [
        'Not available to PO Boxes',
        'Rural areas may take longer'
      ]
    },
    {
      id: 'express',
      name: 'Express Shipping',
      description: 'Fast delivery when you need it quickly',
      icon: 'plane',
      price: 'R199',
      deliveryTime: '1-2 business days',
      features: [
        'Priority handling',
        'Real-time tracking',
        'Insurance up to R10,000',
        'SMS notifications'
      ],
      isPopular: false,
      isFree: false,
      restrictions: [
        'Order before 2 PM for next day delivery',
        'Major cities only'
      ]
    },
    {
      id: 'free',
      name: 'Free Standard Shipping',
      description: 'Free shipping on qualifying orders',
      icon: 'package',
      price: 'Free',
      deliveryTime: '3-5 business days',
      features: [
        'No additional cost',
        'Same reliable service',
        'Tracking included'
      ],
      isPopular: false,
      isFree: true,
      restrictions: [
        'Minimum order R1,500',
        'Standard delivery areas only'
      ]
    }
  ],
  layout: 'cards',
  columns: 3,
  showPricing: true,
  showFeatures: true,
  showRestrictions: false,
  cardStyle: 'default',
  spacing: 'normal',
  backgroundColor: 'transparent',
  textColor: 'inherit',
  iconColor: '#012169',
  freeShippingThreshold: {
    enabled: true,
    amount: 'R1,500',
    currency: 'ZAR',
    message: 'Free standard shipping on orders over R1,500'
  }
}

// Configuration schema for the Page Builder
export const shippingInfoCardsBlockSchema = {
  type: 'object',
  properties: {
    title: {
      type: 'string',
      title: 'Title',
      default: 'Shipping Information'
    },
    subtitle: {
      type: 'string',
      title: 'Subtitle (Optional)',
      default: 'Choose the delivery option that works best for you'
    },
    description: {
      type: 'string',
      title: 'Description (Optional)',
      format: 'textarea',
      default: shippingInfoCardsBlockConfig.description
    },
    options: {
      type: 'array',
      title: 'Shipping Options',
      items: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            title: 'Option ID',
            description: 'Unique identifier for the shipping option'
          },
          name: {
            type: 'string',
            title: 'Option Name'
          },
          description: {
            type: 'string',
            title: 'Description'
          },
          icon: {
            type: 'string',
            title: 'Icon',
            enum: Object.keys(shippingIcons),
            default: 'truck'
          },
          price: {
            type: 'string',
            title: 'Price'
          },
          deliveryTime: {
            type: 'string',
            title: 'Delivery Time'
          },
          features: {
            type: 'array',
            title: 'Features',
            items: {
              type: 'string'
            }
          },
          isPopular: {
            type: 'boolean',
            title: 'Mark as Popular',
            default: false
          },
          isFree: {
            type: 'boolean',
            title: 'Free Option',
            default: false
          },
          restrictions: {
            type: 'array',
            title: 'Restrictions (Optional)',
            items: {
              type: 'string'
            }
          }
        },
        required: ['id', 'name', 'description', 'icon', 'price', 'deliveryTime']
      },
      default: shippingInfoCardsBlockConfig.options
    },
    layout: {
      type: 'string',
      title: 'Layout',
      enum: ['cards', 'list', 'grid'],
      enumNames: ['Cards', 'List', 'Grid'],
      default: 'cards'
    },
    columns: {
      type: 'number',
      title: 'Columns (for cards/grid)',
      enum: [2, 3, 4],
      default: 3
    },
    showPricing: {
      type: 'boolean',
      title: 'Show Pricing',
      default: true
    },
    showFeatures: {
      type: 'boolean',
      title: 'Show Features',
      default: true
    },
    showRestrictions: {
      type: 'boolean',
      title: 'Show Restrictions',
      default: false
    },
    cardStyle: {
      type: 'string',
      title: 'Card Style',
      enum: ['default', 'minimal', 'bordered', 'elevated'],
      enumNames: ['Default', 'Minimal', 'Bordered', 'Elevated'],
      default: 'default'
    },
    spacing: {
      type: 'string',
      title: 'Spacing',
      enum: ['compact', 'normal', 'spacious'],
      enumNames: ['Compact', 'Normal', 'Spacious'],
      default: 'normal'
    },
    iconColor: {
      type: 'string',
      title: 'Icon Color',
      format: 'color',
      default: '#012169'
    },
    freeShippingThreshold: {
      type: 'object',
      title: 'Free Shipping Threshold',
      properties: {
        enabled: {
          type: 'boolean',
          title: 'Show Free Shipping Banner',
          default: true
        },
        amount: {
          type: 'string',
          title: 'Threshold Amount',
          default: 'R1,500'
        },
        currency: {
          type: 'string',
          title: 'Currency',
          default: 'ZAR'
        },
        message: {
          type: 'string',
          title: 'Banner Message',
          default: shippingInfoCardsBlockConfig.freeShippingThreshold.message
        }
      }
    },
    backgroundColor: {
      type: 'string',
      title: 'Background Color',
      format: 'color',
      default: 'transparent'
    },
    textColor: {
      type: 'string',
      title: 'Text Color',
      format: 'color',
      default: 'inherit'
    }
  },
  required: ['title', 'options']
}
