'use client'

import React, { useState } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Plus, Trash2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface TabsBlockConfig {
  tabs: TabItem[]
  defaultActiveTab: number
  orientation: 'horizontal' | 'vertical'
  style: 'default' | 'pills' | 'underline' | 'bordered'
  alignment: 'left' | 'center' | 'right' | 'justified'
  size: 'sm' | 'md' | 'lg'
  backgroundColor: string
  tabBackgroundColor: string
  activeTabColor: string
  borderRadius: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
}

interface TabItem {
  id: string
  label: string
  content: string
  icon?: string
  disabled?: boolean
  backgroundColor?: string
}

interface TabsBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function TabsBlock({ block, isEditing = false }: TabsBlockProps) {
  const config = block.configuration as TabsBlockConfig
  const [activeTab, setActiveTab] = useState(config.defaultActiveTab || 0)

  const {
    tabs,
    orientation,
    style,
    alignment,
    size,
    backgroundColor,
    tabBackgroundColor,
    activeTabColor,
    borderRadius,
    padding,
  } = config

  const getSizeClasses = () => {
    const sizeClasses = {
      sm: 'text-xs px-3 py-1.5',
      md: 'text-sm px-4 py-2',
      lg: 'text-base px-6 py-3'
    }
    return sizeClasses[size]
  }

  const getAlignmentClasses = () => {
    if (orientation === 'vertical') return ''
    
    const alignmentClasses = {
      left: 'justify-start',
      center: 'justify-center',
      right: 'justify-end',
      justified: 'justify-between'
    }
    return alignmentClasses[alignment]
  }

  const getTabClasses = (index: number, isActive: boolean) => {
    const baseClasses = cn(
      'transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500',
      getSizeClasses(),
      alignment === 'justified' && orientation === 'horizontal' && 'flex-1'
    )

    const styleClasses = {
      default: cn(
        'border-b-2 border-transparent hover:border-gray-300',
        isActive && 'border-blue-500 text-blue-600'
      ),
      pills: cn(
        'rounded-full hover:bg-gray-100',
        isActive && 'bg-blue-500 text-white hover:bg-blue-600'
      ),
      underline: cn(
        'border-b-2 border-transparent hover:border-gray-300 rounded-none',
        isActive && 'border-blue-500 text-blue-600'
      ),
      bordered: cn(
        'border border-gray-200 hover:bg-gray-50',
        isActive && 'bg-blue-500 text-white border-blue-500',
        orientation === 'horizontal' && index === 0 && 'rounded-l-lg',
        orientation === 'horizontal' && index === tabs.length - 1 && 'rounded-r-lg',
        orientation === 'vertical' && index === 0 && 'rounded-t-lg',
        orientation === 'vertical' && index === tabs.length - 1 && 'rounded-b-lg'
      )
    }

    return cn(baseClasses, styleClasses[style])
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
  }

  // Demo tabs for editing mode
  const demoTabs = [
    {
      id: 'demo-1',
      label: 'Overview',
      content: '<h3>Product Overview</h3><p>This is the overview tab content. Here you can provide a general description of your product or service.</p>'
    },
    {
      id: 'demo-2',
      label: 'Features',
      content: '<h3>Key Features</h3><ul><li>Feature 1: Advanced functionality</li><li>Feature 2: User-friendly interface</li><li>Feature 3: Mobile responsive</li></ul>'
    },
    {
      id: 'demo-3',
      label: 'Specifications',
      content: '<h3>Technical Specifications</h3><p>Detailed technical information about the product specifications and requirements.</p>'
    }
  ]

  const tabsToRender = isEditing ? demoTabs : tabs
  const activeTabContent = tabsToRender[activeTab]

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div style={containerStyles}>
        <div className={cn(
          'flex',
          orientation === 'vertical' ? 'flex-row space-x-4' : 'flex-col'
        )}>
          {/* Tab Navigation */}
          <div className={cn(
            'flex',
            orientation === 'horizontal' ? `flex-row ${getAlignmentClasses()}` : 'flex-col space-y-1',
            orientation === 'vertical' && 'w-48 flex-shrink-0'
          )}>
            {tabsToRender.map((tab, index) => (
              <button
                key={tab.id}
                className={getTabClasses(index, activeTab === index)}
                onClick={() => !isEditing && setActiveTab(index)}
                disabled={tab.disabled || isEditing}
                style={{
                  backgroundColor: activeTab === index ? activeTabColor : tabBackgroundColor,
                }}
              >
                {tab.icon && <span className="mr-2">{tab.icon}</span>}
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className={cn(
            'flex-1',
            orientation === 'horizontal' ? 'mt-4' : 'mt-0'
          )}>
            {activeTabContent && (
              <div 
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{ __html: activeTabContent.content }}
              />
            )}
          </div>
        </div>
        
        {isEditing && (
          <div className="mt-4 p-2 bg-cyan-50 border border-cyan-200 rounded text-xs text-cyan-700">
            <strong>Tabs Block:</strong> {tabsToRender.length} tabs • {orientation} orientation • {style} style
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Tabs Block Configuration Component
interface TabsBlockConfigProps {
  config: TabsBlockConfig
  onChange: (config: TabsBlockConfig) => void
}

export function TabsBlockConfig({ config, onChange }: TabsBlockConfigProps) {
  const updateConfig = (updates: Partial<TabsBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const addTab = () => {
    const newTab: TabItem = {
      id: `tab-${Date.now()}`,
      label: 'New Tab',
      content: 'Add your tab content here...'
    }
    
    updateConfig({
      tabs: [...config.tabs, newTab]
    })
  }

  const updateTab = (index: number, updates: Partial<TabItem>) => {
    const updatedTabs = [...config.tabs]
    updatedTabs[index] = { ...updatedTabs[index], ...updates }
    updateConfig({ tabs: updatedTabs })
  }

  const removeTab = (index: number) => {
    const updatedTabs = config.tabs.filter((_, i) => i !== index)
    updateConfig({ 
      tabs: updatedTabs,
      defaultActiveTab: Math.min(config.defaultActiveTab, updatedTabs.length - 1)
    })
  }

  return (
    <div className="space-y-6">
      {/* Tab Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Tab Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Orientation</Label>
              <Select
                value={config.orientation}
                onValueChange={(value) => updateConfig({ orientation: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="horizontal">Horizontal</SelectItem>
                  <SelectItem value="vertical">Vertical</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Style</Label>
              <Select
                value={config.style}
                onValueChange={(value) => updateConfig({ style: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">Default</SelectItem>
                  <SelectItem value="pills">Pills</SelectItem>
                  <SelectItem value="underline">Underline</SelectItem>
                  <SelectItem value="bordered">Bordered</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Alignment</Label>
              <Select
                value={config.alignment}
                onValueChange={(value) => updateConfig({ alignment: value as any })}
                disabled={config.orientation === 'vertical'}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="left">Left</SelectItem>
                  <SelectItem value="center">Center</SelectItem>
                  <SelectItem value="right">Right</SelectItem>
                  <SelectItem value="justified">Justified</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Size</Label>
              <Select
                value={config.size}
                onValueChange={(value) => updateConfig({ size: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sm">Small</SelectItem>
                  <SelectItem value="md">Medium</SelectItem>
                  <SelectItem value="lg">Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label className="text-xs">Default Active Tab</Label>
            <Select
              value={config.defaultActiveTab.toString()}
              onValueChange={(value) => updateConfig({ defaultActiveTab: parseInt(value) })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {config.tabs.map((tab, index) => (
                  <SelectItem key={tab.id} value={index.toString()}>
                    Tab {index + 1}: {tab.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tab Items */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm">Tab Items</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={addTab}
            className="h-8 px-2"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Tab
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.tabs.map((tab, index) => (
            <div key={tab.id} className="border rounded-lg p-3 space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Tab {index + 1}</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeTab(index)}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
              
              <div>
                <Label className="text-xs">Label</Label>
                <Input
                  value={tab.label}
                  onChange={(e) => updateTab(index, { label: e.target.value })}
                  className="mt-1"
                  placeholder="Tab label"
                />
              </div>
              
              <div>
                <Label className="text-xs">Content</Label>
                <Textarea
                  value={tab.content}
                  onChange={(e) => updateTab(index, { content: e.target.value })}
                  className="mt-1"
                  rows={3}
                  placeholder="Tab content (HTML supported)"
                />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-xs">Disabled</Label>
                <Switch
                  checked={tab.disabled || false}
                  onCheckedChange={(checked) => updateTab(index, { disabled: checked })}
                />
              </div>
            </div>
          ))}
          
          {config.tabs.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              <p className="text-sm">No tabs yet.</p>
              <p className="text-xs">Click "Add Tab" to get started.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
