'use client'

import React from 'react'
import { PageBlock, TestimonialBlockConfig, Testimonial } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Star, Quote, Plus, Trash2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface TestimonialsBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function TestimonialsBlock({ block, isEditing = false }: TestimonialsBlockProps) {
  const config = block.configuration as TestimonialBlockConfig

  const {
    testimonials,
    layout,
    showRating,
    showAvatar,
    showCompany,
    autoplay,
    autoplaySpeed,
  } = config

  if (!testimonials || testimonials.length === 0) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <section className="py-12">
          <div className="container mx-auto px-4 md:px-6">
            <div className="text-center py-12 text-muted-foreground">
              <Quote className="h-12 w-12 mx-auto mb-4" />
              <p>No testimonials to display</p>
            </div>
          </div>
        </section>
      </BaseBlock>
    )
  }

  const renderTestimonial = (testimonial: Testimonial, index: number) => (
    <Card key={testimonial.id} className="h-full">
      <CardContent className="p-6">
        {/* Quote Icon */}
        <Quote className="h-8 w-8 text-muted-foreground mb-4" />
        
        {/* Content */}
        <blockquote className="text-lg mb-6 leading-relaxed">
          "{testimonial.content}"
        </blockquote>

        {/* Rating */}
        {showRating && testimonial.rating && (
          <div className="flex items-center mb-4">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={cn(
                  'h-4 w-4',
                  i < testimonial.rating! 
                    ? 'text-yellow-400 fill-current' 
                    : 'text-gray-300'
                )}
              />
            ))}
          </div>
        )}

        {/* Author */}
        <div className="flex items-center">
          {showAvatar && testimonial.avatar && (
            <img
              src={testimonial.avatar}
              alt={testimonial.name}
              className="w-12 h-12 rounded-full mr-4 object-cover"
            />
          )}
          <div>
            <div className="font-medium text-gray-900">{testimonial.name}</div>
            {showCompany && testimonial.company && (
              <div className="text-sm text-muted-foreground">{testimonial.company}</div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <section className="py-12">
        <div className="container mx-auto px-4 md:px-6">
          {layout === 'single' ? (
            <div className="max-w-2xl mx-auto">
              {renderTestimonial(testimonials[0], 0)}
            </div>
          ) : layout === 'carousel' ? (
            <div className="relative">
              {/* Simple carousel implementation */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {testimonials.slice(0, 3).map((testimonial, index) => 
                  renderTestimonial(testimonial, index)
                )}
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {testimonials.map((testimonial, index) => 
                renderTestimonial(testimonial, index)
              )}
            </div>
          )}
        </div>
      </section>
    </BaseBlock>
  )
}

// Testimonials Block Configuration Component
interface TestimonialsBlockConfigProps {
  config: TestimonialBlockConfig
  onChange: (config: TestimonialBlockConfig) => void
}

export function TestimonialsBlockConfig({ config, onChange }: TestimonialsBlockConfigProps) {
  const updateConfig = (updates: Partial<TestimonialBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const addTestimonial = () => {
    const newTestimonial: Testimonial = {
      id: Date.now().toString(),
      name: 'Customer Name',
      company: 'Company Name',
      avatar: '',
      rating: 5,
      content: 'This is a great testimonial about our products and services.',
    }

    updateConfig({
      testimonials: [...(config.testimonials || []), newTestimonial]
    })
  }

  const updateTestimonial = (index: number, updates: Partial<Testimonial>) => {
    const updatedTestimonials = [...(config.testimonials || [])]
    updatedTestimonials[index] = { ...updatedTestimonials[index], ...updates }
    updateConfig({ testimonials: updatedTestimonials })
  }

  const removeTestimonial = (index: number) => {
    const updatedTestimonials = [...(config.testimonials || [])]
    updatedTestimonials.splice(index, 1)
    updateConfig({ testimonials: updatedTestimonials })
  }

  return (
    <div className="space-y-6">
      {/* Layout Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Layout</h3>
        
        <div>
          <Label className="block text-sm font-medium mb-2">Layout Style</Label>
          <Select
            value={config.layout}
            onValueChange={(value) => updateConfig({ layout: value as any })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="grid">Grid</SelectItem>
              <SelectItem value="carousel">Carousel</SelectItem>
              <SelectItem value="single">Single</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Display Options */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Display Options</h3>
        
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Show Rating</Label>
            <Switch
              checked={config.showRating}
              onCheckedChange={(checked) => updateConfig({ showRating: checked })}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Show Avatar</Label>
            <Switch
              checked={config.showAvatar}
              onCheckedChange={(checked) => updateConfig({ showAvatar: checked })}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Show Company</Label>
            <Switch
              checked={config.showCompany}
              onCheckedChange={(checked) => updateConfig({ showCompany: checked })}
            />
          </div>
        </div>
      </div>

      {/* Carousel Settings */}
      {config.layout === 'carousel' && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Carousel Settings</h3>
          
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Autoplay</Label>
            <Switch
              checked={config.autoplay}
              onCheckedChange={(checked) => updateConfig({ autoplay: checked })}
            />
          </div>

          {config.autoplay && (
            <div>
              <Label className="block text-sm font-medium mb-2">Autoplay Speed (ms)</Label>
              <Input
                type="number"
                min="1000"
                step="500"
                value={config.autoplaySpeed || 5000}
                onChange={(e) => updateConfig({ autoplaySpeed: parseInt(e.target.value) })}
              />
            </div>
          )}
        </div>
      )}

      {/* Testimonials */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Testimonials</h3>
          <Button
            size="sm"
            onClick={addTestimonial}
            className="flex items-center space-x-1"
          >
            <Plus className="h-4 w-4" />
            <span>Add</span>
          </Button>
        </div>

        <div className="space-y-4">
          {(config.testimonials || []).map((testimonial, index) => (
            <Card key={testimonial.id} className="p-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Testimonial {index + 1}</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeTestimonial(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>

                <div>
                  <Label className="text-xs">Customer Name</Label>
                  <Input
                    value={testimonial.name}
                    onChange={(e) => updateTestimonial(index, { name: e.target.value })}
                    placeholder="Customer Name"
                  />
                </div>

                <div>
                  <Label className="text-xs">Company (Optional)</Label>
                  <Input
                    value={testimonial.company || ''}
                    onChange={(e) => updateTestimonial(index, { company: e.target.value })}
                    placeholder="Company Name"
                  />
                </div>

                <div>
                  <Label className="text-xs">Avatar URL (Optional)</Label>
                  <Input
                    value={testimonial.avatar || ''}
                    onChange={(e) => updateTestimonial(index, { avatar: e.target.value })}
                    placeholder="https://example.com/avatar.jpg"
                  />
                </div>

                <div>
                  <Label className="text-xs">Rating</Label>
                  <Select
                    value={testimonial.rating?.toString() || '5'}
                    onValueChange={(value) => updateTestimonial(index, { rating: parseInt(value) })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 Star</SelectItem>
                      <SelectItem value="2">2 Stars</SelectItem>
                      <SelectItem value="3">3 Stars</SelectItem>
                      <SelectItem value="4">4 Stars</SelectItem>
                      <SelectItem value="5">5 Stars</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-xs">Testimonial Content</Label>
                  <Textarea
                    value={testimonial.content}
                    onChange={(e) => updateTestimonial(index, { content: e.target.value })}
                    placeholder="Write the testimonial content here..."
                    rows={3}
                  />
                </div>
              </div>
            </Card>
          ))}
        </div>

        {(!config.testimonials || config.testimonials.length === 0) && (
          <div className="text-center py-8 text-muted-foreground">
            <Quote className="h-8 w-8 mx-auto mb-2" />
            <p className="text-sm">No testimonials added yet</p>
            <Button
              variant="outline"
              size="sm"
              onClick={addTestimonial}
              className="mt-2"
            >
              Add First Testimonial
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
