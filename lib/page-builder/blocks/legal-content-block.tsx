'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'

interface LegalSection {
  id: string
  title: string
  content: string[]
  subsections?: {
    title: string
    content: string[]
  }[]
}

interface LegalContentConfig {
  title: string
  subtitle?: string
  lastUpdated: string
  effectiveDate: string
  sections: LegalSection[]
  tableOfContents: boolean
  contactInfo: {
    enabled: boolean
    title: string
    description: string
    email: string
    address: string
    phone: string
  }
  style: 'default' | 'minimal' | 'formal'
  spacing: 'compact' | 'normal' | 'spacious'
  backgroundColor: string
  textColor: string
}

interface LegalContentBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function LegalContentBlock({ block, isEditing = false }: LegalContentBlockProps) {
  const config = block.configuration as LegalContentConfig

  const {
    title,
    subtitle,
    lastUpdated,
    effectiveDate,
    sections = [],
    tableOfContents = true,
    contactInfo = {
      enabled: true,
      title: 'Contact Us',
      description: 'If you have any questions about this policy, please contact us:',
      email: '<EMAIL>',
      address: '123 Sandton Drive, Sandton, Johannesburg 2196, South Africa',
      phone: '+27 11 123 4567'
    },
    style = 'default',
    spacing = 'normal',
    backgroundColor = 'transparent',
    textColor = 'inherit'
  } = config

  const getSpacingClasses = () => {
    switch (spacing) {
      case 'compact':
        return 'py-6 gap-6'
      case 'spacious':
        return 'py-16 gap-16'
      default:
        return 'py-12 gap-12'
    }
  }

  const getStyleClasses = () => {
    switch (style) {
      case 'minimal':
        return 'text-sm leading-relaxed'
      case 'formal':
        return 'text-sm leading-loose font-serif'
      default:
        return 'text-sm leading-relaxed'
    }
  }

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const renderTableOfContents = () => (
    <Card className="mb-8">
      <CardContent className="p-6">
        <h2 className="text-lg font-semibold mb-4">Table of Contents</h2>
        <nav className="space-y-2">
          {sections.map((section, index) => (
            <div key={section.id}>
              <button
                onClick={() => scrollToSection(section.id)}
                className="text-left text-[#012169] hover:underline block"
              >
                {index + 1}. {section.title}
              </button>
              {section.subsections && section.subsections.length > 0 && (
                <div className="ml-4 mt-1 space-y-1">
                  {section.subsections.map((subsection, subIndex) => (
                    <button
                      key={subIndex}
                      onClick={() => scrollToSection(`${section.id}-${subIndex}`)}
                      className="text-left text-muted-foreground hover:text-[#012169] hover:underline block text-sm"
                    >
                      {index + 1}.{subIndex + 1} {subsection.title}
                    </button>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>
      </CardContent>
    </Card>
  )

  const renderSection = (section: LegalSection, index: number) => (
    <section key={section.id} id={section.id} className="scroll-mt-8">
      <h2 className="text-xl font-semibold mb-4">
        {index + 1}. {section.title}
      </h2>
      
      <div className="space-y-4 mb-8">
        {section.content.map((paragraph, pIndex) => (
          <p key={pIndex} className={getStyleClasses()}>
            {paragraph}
          </p>
        ))}
      </div>

      {section.subsections && section.subsections.length > 0 && (
        <div className="space-y-6 ml-4">
          {section.subsections.map((subsection, subIndex) => (
            <div key={subIndex} id={`${section.id}-${subIndex}`} className="scroll-mt-8">
              <h3 className="text-lg font-medium mb-3">
                {index + 1}.{subIndex + 1} {subsection.title}
              </h3>
              <div className="space-y-3">
                {subsection.content.map((paragraph, pIndex) => (
                  <p key={pIndex} className={getStyleClasses()}>
                    {paragraph}
                  </p>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </section>
  )

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div 
        className="container px-4 md:px-6"
        style={{ 
          backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : undefined,
          color: textColor !== 'inherit' ? textColor : undefined
        }}
      >
        <div className={getSpacingClasses()}>
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold font-montserrat mb-4">
              {title}
            </h1>
            {subtitle && (
              <p className="text-lg text-muted-foreground mb-4">
                {subtitle}
              </p>
            )}
            <div className="flex flex-col sm:flex-row gap-4 text-sm text-muted-foreground">
              {effectiveDate && (
                <div>
                  <strong>Effective Date:</strong> {effectiveDate}
                </div>
              )}
              {lastUpdated && (
                <div>
                  <strong>Last Updated:</strong> {lastUpdated}
                </div>
              )}
            </div>
          </div>

          <div className="max-w-4xl mx-auto">
            {/* Table of Contents */}
            {tableOfContents && sections.length > 0 && renderTableOfContents()}

            {/* Content Sections */}
            <div className="space-y-12">
              {sections.map((section, index) => renderSection(section, index))}
            </div>

            {/* Contact Information */}
            {contactInfo.enabled && (
              <Card className="mt-12">
                <CardContent className="p-6">
                  <h2 className="text-lg font-semibold mb-4">
                    {contactInfo.title}
                  </h2>
                  <p className="text-sm text-muted-foreground mb-4">
                    {contactInfo.description}
                  </p>
                  <div className="space-y-2 text-sm">
                    <div>
                      <strong>Email:</strong>{' '}
                      <a 
                        href={`mailto:${contactInfo.email}`}
                        className="text-[#012169] hover:underline"
                      >
                        {contactInfo.email}
                      </a>
                    </div>
                    {contactInfo.phone && (
                      <div>
                        <strong>Phone:</strong>{' '}
                        <a 
                          href={`tel:${contactInfo.phone.replace(/\s/g, '')}`}
                          className="text-[#012169] hover:underline"
                        >
                          {contactInfo.phone}
                        </a>
                      </div>
                    )}
                    {contactInfo.address && (
                      <div>
                        <strong>Address:</strong> {contactInfo.address}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </BaseBlock>
  )
}

// Default configuration for the legal content block
export const legalContentBlockConfig = {
  title: 'Privacy Policy',
  subtitle: 'How we collect, use, and protect your information',
  lastUpdated: 'December 1, 2024',
  effectiveDate: 'December 1, 2024',
  sections: [
    {
      id: 'information-collection',
      title: 'Information We Collect',
      content: [
        'We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support.',
        'We also automatically collect certain information about your device and how you interact with our services.'
      ],
      subsections: [
        {
          title: 'Personal Information',
          content: [
            'This includes your name, email address, phone number, shipping address, and payment information.',
            'We only collect information that is necessary to provide our services to you.'
          ]
        },
        {
          title: 'Usage Information',
          content: [
            'We collect information about how you use our website, including pages visited, time spent, and actions taken.',
            'This helps us improve our services and provide a better user experience.'
          ]
        }
      ]
    },
    {
      id: 'information-use',
      title: 'How We Use Your Information',
      content: [
        'We use the information we collect to provide, maintain, and improve our services.',
        'This includes processing orders, sending communications, and personalizing your experience.'
      ]
    },
    {
      id: 'information-sharing',
      title: 'Information Sharing',
      content: [
        'We do not sell, trade, or otherwise transfer your personal information to third parties without your consent.',
        'We may share information with trusted service providers who assist us in operating our website and conducting our business.'
      ]
    },
    {
      id: 'data-security',
      title: 'Data Security',
      content: [
        'We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.',
        'However, no method of transmission over the internet is 100% secure.'
      ]
    }
  ],
  tableOfContents: true,
  contactInfo: {
    enabled: true,
    title: 'Contact Us',
    description: 'If you have any questions about this Privacy Policy, please contact us:',
    email: '<EMAIL>',
    address: '123 Sandton Drive, Sandton, Johannesburg 2196, South Africa',
    phone: '+27 11 123 4567'
  },
  style: 'default',
  spacing: 'normal',
  backgroundColor: 'transparent',
  textColor: 'inherit'
}

// Configuration schema for the Page Builder
export const legalContentBlockSchema = {
  type: 'object',
  properties: {
    title: {
      type: 'string',
      title: 'Title',
      default: 'Privacy Policy'
    },
    subtitle: {
      type: 'string',
      title: 'Subtitle (Optional)',
      default: 'How we collect, use, and protect your information'
    },
    lastUpdated: {
      type: 'string',
      title: 'Last Updated Date',
      default: 'December 1, 2024'
    },
    effectiveDate: {
      type: 'string',
      title: 'Effective Date',
      default: 'December 1, 2024'
    },
    sections: {
      type: 'array',
      title: 'Content Sections',
      items: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            title: 'Section ID',
            description: 'Unique identifier for the section'
          },
          title: {
            type: 'string',
            title: 'Section Title'
          },
          content: {
            type: 'array',
            title: 'Content Paragraphs',
            items: {
              type: 'string',
              title: 'Paragraph'
            }
          },
          subsections: {
            type: 'array',
            title: 'Subsections (Optional)',
            items: {
              type: 'object',
              properties: {
                title: {
                  type: 'string',
                  title: 'Subsection Title'
                },
                content: {
                  type: 'array',
                  title: 'Subsection Content',
                  items: {
                    type: 'string',
                    title: 'Paragraph'
                  }
                }
              },
              required: ['title', 'content']
            }
          }
        },
        required: ['id', 'title', 'content']
      },
      default: legalContentBlockConfig.sections
    },
    tableOfContents: {
      type: 'boolean',
      title: 'Show Table of Contents',
      default: true
    },
    contactInfo: {
      type: 'object',
      title: 'Contact Information',
      properties: {
        enabled: {
          type: 'boolean',
          title: 'Show Contact Section',
          default: true
        },
        title: {
          type: 'string',
          title: 'Contact Section Title',
          default: 'Contact Us'
        },
        description: {
          type: 'string',
          title: 'Contact Description',
          default: legalContentBlockConfig.contactInfo.description
        },
        email: {
          type: 'string',
          title: 'Contact Email',
          format: 'email',
          default: '<EMAIL>'
        },
        address: {
          type: 'string',
          title: 'Address (Optional)',
          default: legalContentBlockConfig.contactInfo.address
        },
        phone: {
          type: 'string',
          title: 'Phone (Optional)',
          default: '+27 11 123 4567'
        }
      }
    },
    style: {
      type: 'string',
      title: 'Content Style',
      enum: ['default', 'minimal', 'formal'],
      enumNames: ['Default', 'Minimal', 'Formal'],
      default: 'default'
    },
    spacing: {
      type: 'string',
      title: 'Spacing',
      enum: ['compact', 'normal', 'spacious'],
      enumNames: ['Compact', 'Normal', 'Spacious'],
      default: 'normal'
    },
    backgroundColor: {
      type: 'string',
      title: 'Background Color',
      format: 'color',
      default: 'transparent'
    },
    textColor: {
      type: 'string',
      title: 'Text Color',
      format: 'color',
      default: 'inherit'
    }
  },
  required: ['title', 'sections']
}
