'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Plus, Trash2, Check, Star, Heart, Zap, Shield, Award } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FeatureListBlockConfig {
  title?: string
  subtitle?: string
  features: FeatureItem[]
  layout: 'list' | 'grid' | 'cards'
  columns: {
    desktop: number
    tablet: number
    mobile: number
  }
  iconStyle: 'emoji' | 'lucide' | 'custom' | 'none'
  iconPosition: 'left' | 'top'
  iconSize: 'sm' | 'md' | 'lg'
  spacing: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  alignment: 'left' | 'center' | 'right'
  showNumbers: boolean
  backgroundColor: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
  borderRadius: string
}

interface FeatureItem {
  id: string
  title: string
  description: string
  icon?: string
  iconType?: 'emoji' | 'lucide' | 'custom'
  iconColor?: string
  backgroundColor?: string
  borderColor?: string
}

interface FeatureListBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function FeatureListBlock({ block, isEditing = false }: FeatureListBlockProps) {
  const config = block.configuration as FeatureListBlockConfig
  
  const {
    title,
    subtitle,
    features,
    layout,
    columns,
    iconStyle,
    iconPosition,
    iconSize,
    spacing,
    alignment,
    showNumbers,
    backgroundColor,
    padding,
    borderRadius,
  } = config

  const getSpacingClass = () => {
    const spacingClasses = {
      none: 'gap-0',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8'
    }
    return spacingClasses[spacing]
  }

  const getLayoutClasses = () => {
    switch (layout) {
      case 'grid':
        return cn(
          'grid',
          `grid-cols-${columns.mobile}`,
          `md:grid-cols-${columns.tablet}`,
          `lg:grid-cols-${columns.desktop}`,
          getSpacingClass()
        )
      case 'cards':
        return cn(
          'grid',
          `grid-cols-${Math.min(columns.mobile, 2)}`,
          `md:grid-cols-${Math.min(columns.tablet, 3)}`,
          `lg:grid-cols-${columns.desktop}`,
          getSpacingClass()
        )
      default:
        return cn('space-y-4', spacing !== 'none' && `space-y-${spacing === 'sm' ? '2' : spacing === 'md' ? '4' : spacing === 'lg' ? '6' : '8'}`)
    }
  }

  const getIconSizeClass = () => {
    const sizeClasses = {
      sm: 'w-4 h-4',
      md: 'w-6 h-6',
      lg: 'w-8 h-8'
    }
    return sizeClasses[iconSize]
  }

  const renderIcon = (feature: FeatureItem, index: number) => {
    if (iconStyle === 'none') return null

    if (showNumbers) {
      return (
        <div className={cn(
          'flex items-center justify-center rounded-full bg-blue-500 text-white font-bold',
          iconSize === 'sm' ? 'w-6 h-6 text-xs' : iconSize === 'md' ? 'w-8 h-8 text-sm' : 'w-10 h-10 text-base'
        )}>
          {index + 1}
        </div>
      )
    }

    if (feature.icon) {
      if (feature.iconType === 'lucide' || iconStyle === 'lucide') {
        const iconMap: { [key: string]: React.ComponentType<any> } = {
          check: Check,
          star: Star,
          heart: Heart,
          zap: Zap,
          shield: Shield,
          award: Award,
        }
        const IconComponent = iconMap[feature.icon.toLowerCase()] || Check
        return (
          <IconComponent 
            className={cn(getIconSizeClass(), 'text-blue-500')}
            style={{ color: feature.iconColor }}
          />
        )
      }
      
      // Emoji or custom icon
      return (
        <span 
          className={cn(
            'flex items-center justify-center',
            iconSize === 'sm' ? 'text-lg' : iconSize === 'md' ? 'text-xl' : 'text-2xl'
          )}
          style={{ color: feature.iconColor }}
        >
          {feature.icon}
        </span>
      )
    }

    // Default icon
    return <Check className={cn(getIconSizeClass(), 'text-green-500')} />
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
  }

  // Demo features for editing mode
  const demoFeatures = [
    {
      id: 'demo-1',
      title: 'Fast & Reliable',
      description: 'Lightning-fast performance with 99.9% uptime guarantee.',
      icon: '⚡',
      iconType: 'emoji' as const
    },
    {
      id: 'demo-2',
      title: 'Secure & Safe',
      description: 'Enterprise-grade security with end-to-end encryption.',
      icon: '🔒',
      iconType: 'emoji' as const
    },
    {
      id: 'demo-3',
      title: '24/7 Support',
      description: 'Round-the-clock customer support whenever you need help.',
      icon: '🎧',
      iconType: 'emoji' as const
    },
    {
      id: 'demo-4',
      title: 'Easy Integration',
      description: 'Simple setup process with comprehensive documentation.',
      icon: '🔧',
      iconType: 'emoji' as const
    }
  ]

  const featuresToRender = isEditing ? demoFeatures : features

  const renderFeature = (feature: FeatureItem, index: number) => {
    const featureContent = (
      <div className={cn(
        'flex',
        iconPosition === 'top' ? 'flex-col items-center text-center space-y-3' : 'items-start space-x-3',
        alignment === 'center' && iconPosition === 'left' && 'text-center',
        alignment === 'right' && iconPosition === 'left' && 'text-right'
      )}>
        {renderIcon(feature, index)}
        <div className="flex-1">
          <h3 className={cn(
            'font-semibold mb-1',
            iconSize === 'sm' ? 'text-sm' : iconSize === 'md' ? 'text-base' : 'text-lg'
          )}>
            {feature.title}
          </h3>
          <p className={cn(
            'text-muted-foreground',
            iconSize === 'sm' ? 'text-xs' : 'text-sm'
          )}>
            {feature.description}
          </p>
        </div>
      </div>
    )

    if (layout === 'cards') {
      return (
        <div
          key={feature.id}
          className="p-4 rounded-lg border bg-card hover:shadow-md transition-shadow"
          style={{
            backgroundColor: feature.backgroundColor,
            borderColor: feature.borderColor,
          }}
        >
          {featureContent}
        </div>
      )
    }

    return (
      <div key={feature.id} className="w-full">
        {featureContent}
      </div>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div style={containerStyles}>
        {/* Header */}
        {(title || subtitle) && (
          <div className={cn('mb-8', `text-${alignment}`)}>
            {title && (
              <h2 className="text-2xl font-bold mb-2">{title}</h2>
            )}
            {subtitle && (
              <p className="text-muted-foreground">{subtitle}</p>
            )}
          </div>
        )}

        {/* Features */}
        <div className={getLayoutClasses()}>
          {featuresToRender.map((feature, index) => renderFeature(feature, index))}
        </div>
        
        {isEditing && (
          <div className="mt-4 p-2 bg-emerald-50 border border-emerald-200 rounded text-xs text-emerald-700">
            <strong>Feature List:</strong> {featuresToRender.length} features • {layout} layout • {iconStyle} icons
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Feature List Block Configuration Component
interface FeatureListBlockConfigProps {
  config: FeatureListBlockConfig
  onChange: (config: FeatureListBlockConfig) => void
}

export function FeatureListBlockConfig({ config, onChange }: FeatureListBlockConfigProps) {
  const updateConfig = (updates: Partial<FeatureListBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const updateColumns = (device: keyof FeatureListBlockConfig['columns'], value: number) => {
    updateConfig({
      columns: {
        ...config.columns,
        [device]: value
      }
    })
  }

  const addFeature = () => {
    const newFeature: FeatureItem = {
      id: `feature-${Date.now()}`,
      title: 'New Feature',
      description: 'Feature description goes here...',
      icon: '✨',
      iconType: 'emoji'
    }
    
    updateConfig({
      features: [...config.features, newFeature]
    })
  }

  const updateFeature = (index: number, updates: Partial<FeatureItem>) => {
    const updatedFeatures = [...config.features]
    updatedFeatures[index] = { ...updatedFeatures[index], ...updates }
    updateConfig({ features: updatedFeatures })
  }

  const removeFeature = (index: number) => {
    const updatedFeatures = config.features.filter((_, i) => i !== index)
    updateConfig({ features: updatedFeatures })
  }

  return (
    <div className="space-y-6">
      {/* Header Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Header</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Title</Label>
            <Input
              value={config.title || ''}
              onChange={(e) => updateConfig({ title: e.target.value })}
              placeholder="Optional section title"
              className="mt-1"
            />
          </div>

          <div>
            <Label className="text-xs">Subtitle</Label>
            <Input
              value={config.subtitle || ''}
              onChange={(e) => updateConfig({ subtitle: e.target.value })}
              placeholder="Optional subtitle"
              className="mt-1"
            />
          </div>
        </CardContent>
      </Card>

      {/* Layout Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Layout Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Layout</Label>
              <Select
                value={config.layout}
                onValueChange={(value) => updateConfig({ layout: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="list">List</SelectItem>
                  <SelectItem value="grid">Grid</SelectItem>
                  <SelectItem value="cards">Cards</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Alignment</Label>
              <Select
                value={config.alignment}
                onValueChange={(value) => updateConfig({ alignment: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="left">Left</SelectItem>
                  <SelectItem value="center">Center</SelectItem>
                  <SelectItem value="right">Right</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {(config.layout === 'grid' || config.layout === 'cards') && (
            <div className="grid grid-cols-3 gap-2">
              <div>
                <Label className="text-xs">Desktop</Label>
                <Input
                  type="number"
                  min="1"
                  max="6"
                  value={config.columns.desktop}
                  onChange={(e) => updateColumns('desktop', parseInt(e.target.value))}
                  className="mt-1"
                />
              </div>
              <div>
                <Label className="text-xs">Tablet</Label>
                <Input
                  type="number"
                  min="1"
                  max="4"
                  value={config.columns.tablet}
                  onChange={(e) => updateColumns('tablet', parseInt(e.target.value))}
                  className="mt-1"
                />
              </div>
              <div>
                <Label className="text-xs">Mobile</Label>
                <Input
                  type="number"
                  min="1"
                  max="2"
                  value={config.columns.mobile}
                  onChange={(e) => updateColumns('mobile', parseInt(e.target.value))}
                  className="mt-1"
                />
              </div>
            </div>
          )}

          <div>
            <Label className="text-xs">Spacing</Label>
            <Select
              value={config.spacing}
              onValueChange={(value) => updateConfig({ spacing: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                <SelectItem value="sm">Small</SelectItem>
                <SelectItem value="md">Medium</SelectItem>
                <SelectItem value="lg">Large</SelectItem>
                <SelectItem value="xl">Extra Large</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Icon Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Icon Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Icon Style</Label>
              <Select
                value={config.iconStyle}
                onValueChange={(value) => updateConfig({ iconStyle: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="emoji">Emoji</SelectItem>
                  <SelectItem value="lucide">Lucide Icons</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                  <SelectItem value="none">None</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Icon Position</Label>
              <Select
                value={config.iconPosition}
                onValueChange={(value) => updateConfig({ iconPosition: value as any })}
                disabled={config.iconStyle === 'none'}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="left">Left</SelectItem>
                  <SelectItem value="top">Top</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Icon Size</Label>
              <Select
                value={config.iconSize}
                onValueChange={(value) => updateConfig({ iconSize: value as any })}
                disabled={config.iconStyle === 'none'}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sm">Small</SelectItem>
                  <SelectItem value="md">Medium</SelectItem>
                  <SelectItem value="lg">Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center justify-between pt-6">
              <Label className="text-xs">Show Numbers</Label>
              <Switch
                checked={config.showNumbers}
                onCheckedChange={(checked) => updateConfig({ showNumbers: checked })}
                disabled={config.iconStyle === 'none'}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Feature Items */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm">Features</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={addFeature}
            className="h-8 px-2"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Feature
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.features.map((feature, index) => (
            <div key={feature.id} className="border rounded-lg p-3 space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Feature {index + 1}</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFeature(index)}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
              
              <div>
                <Label className="text-xs">Title</Label>
                <Input
                  value={feature.title}
                  onChange={(e) => updateFeature(index, { title: e.target.value })}
                  className="mt-1"
                  placeholder="Feature title"
                />
              </div>
              
              <div>
                <Label className="text-xs">Description</Label>
                <Textarea
                  value={feature.description}
                  onChange={(e) => updateFeature(index, { description: e.target.value })}
                  className="mt-1"
                  rows={2}
                  placeholder="Feature description"
                />
              </div>

              {config.iconStyle !== 'none' && !config.showNumbers && (
                <div>
                  <Label className="text-xs">Icon</Label>
                  <Input
                    value={feature.icon || ''}
                    onChange={(e) => updateFeature(index, { icon: e.target.value })}
                    className="mt-1"
                    placeholder={config.iconStyle === 'emoji' ? 'Emoji (e.g., ✨)' : config.iconStyle === 'lucide' ? 'Icon name (e.g., check)' : 'Custom icon'}
                  />
                </div>
              )}
            </div>
          ))}
          
          {config.features.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              <p className="text-sm">No features yet.</p>
              <p className="text-xs">Click "Add Feature" to get started.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
