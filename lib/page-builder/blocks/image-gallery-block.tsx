'use client'

import React, { useState, useEffect } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  ChevronLeft, 
  ChevronRight, 
  X, 
  ZoomIn, 
  Download,
  Plus,
  Trash2
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ImageGalleryBlockConfig {
  title?: string
  subtitle?: string
  images: GalleryImage[]
  layout: 'grid' | 'masonry' | 'carousel' | 'justified'
  columns: {
    desktop: number
    tablet: number
    mobile: number
  }
  aspectRatio: 'auto' | 'square' | '16:9' | '4:3' | '3:2'
  spacing: 'none' | 'sm' | 'md' | 'lg'
  showCaptions: boolean
  showOverlay: boolean
  enableLightbox: boolean
  enableDownload: boolean
  enableFiltering: boolean
  filterTags: string[]
  autoplay: boolean
  autoplaySpeed: number
  showThumbnails: boolean
  thumbnailPosition: 'bottom' | 'right'
  backgroundColor: string
  borderRadius: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
  maxWidth: string
  alignment: 'left' | 'center' | 'right'
}

interface GalleryImage {
  id: string
  src: string
  alt: string
  caption?: string
  title?: string
  tags?: string[]
  width?: number
  height?: number
  thumbnail?: string
}

interface ImageGalleryBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function ImageGalleryBlock({ block, isEditing = false }: ImageGalleryBlockProps) {
  const config = block.configuration as ImageGalleryBlockConfig
  const [filteredImages, setFilteredImages] = useState<GalleryImage[]>([])
  const [activeFilter, setActiveFilter] = useState<string>('all')
  const [lightboxOpen, setLightboxOpen] = useState(false)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [currentSlide, setCurrentSlide] = useState(0)

  const {
    title,
    subtitle,
    images,
    layout,
    columns,
    aspectRatio,
    spacing,
    showCaptions,
    showOverlay,
    enableLightbox,
    enableDownload,
    enableFiltering,
    filterTags,
    autoplay,
    autoplaySpeed,
    showThumbnails,
    thumbnailPosition,
    backgroundColor,
    borderRadius,
    padding,
    maxWidth,
    alignment,
  } = config

  // Filter images based on active filter
  useEffect(() => {
    if (activeFilter === 'all') {
      setFilteredImages(images)
    } else {
      setFilteredImages(images.filter(img => img.tags?.includes(activeFilter)))
    }
  }, [images, activeFilter])

  // Auto-play carousel
  useEffect(() => {
    if (layout === 'carousel' && autoplay && filteredImages.length > 0 && !isEditing) {
      const interval = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % filteredImages.length)
      }, autoplaySpeed)
      
      return () => clearInterval(interval)
    }
  }, [layout, autoplay, autoplaySpeed, filteredImages.length, isEditing])

  const getSpacingClass = () => {
    const spacingClasses = {
      none: 'gap-0',
      sm: 'gap-1',
      md: 'gap-4',
      lg: 'gap-6'
    }
    return spacingClasses[spacing]
  }

  const getAspectRatioClass = () => {
    const ratioClasses = {
      auto: '',
      square: 'aspect-square',
      '16:9': 'aspect-video',
      '4:3': 'aspect-[4/3]',
      '3:2': 'aspect-[3/2]'
    }
    return ratioClasses[aspectRatio]
  }

  const getAlignmentClass = () => {
    const alignmentClasses = {
      left: 'mr-auto',
      center: 'mx-auto',
      right: 'ml-auto'
    }
    return alignmentClasses[alignment]
  }

  const openLightbox = (index: number) => {
    if (enableLightbox && !isEditing) {
      setCurrentImageIndex(index)
      setLightboxOpen(true)
    }
  }

  const closeLightbox = () => {
    setLightboxOpen(false)
  }

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % filteredImages.length)
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? filteredImages.length - 1 : prev - 1
    )
  }

  const downloadImage = (image: GalleryImage) => {
    if (!enableDownload || isEditing) return
    
    const link = document.createElement('a')
    link.href = image.src
    link.download = image.title || image.alt || 'image'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
    maxWidth: maxWidth || '100%',
  }

  // Demo images for editing mode
  const demoImages: GalleryImage[] = [
    {
      id: 'demo-1',
      src: 'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=600&h=400&fit=crop',
      alt: 'Kids Fashion 1',
      caption: 'Colorful summer collection for kids',
      title: 'Summer Collection',
      tags: ['summer', 'colorful']
    },
    {
      id: 'demo-2',
      src: 'https://images.unsplash.com/photo-1519238263530-99bdd11df2ea?w=600&h=400&fit=crop',
      alt: 'Kids Fashion 2',
      caption: 'Comfortable everyday wear',
      title: 'Everyday Comfort',
      tags: ['casual', 'comfort']
    },
    {
      id: 'demo-3',
      src: 'https://images.unsplash.com/photo-1518831959646-742c3a14ebf7?w=600&h=400&fit=crop',
      alt: 'Kids Fashion 3',
      caption: 'Special occasion dresses',
      title: 'Special Occasions',
      tags: ['formal', 'special']
    },
    {
      id: 'demo-4',
      src: 'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=600&h=600&fit=crop',
      alt: 'Kids Fashion 4',
      caption: 'Trendy winter collection',
      title: 'Winter Trends',
      tags: ['winter', 'trendy']
    },
    {
      id: 'demo-5',
      src: 'https://images.unsplash.com/photo-1519238263530-99bdd11df2ea?w=600&h=600&fit=crop',
      alt: 'Kids Fashion 5',
      caption: 'Playful designs for active kids',
      title: 'Active Play',
      tags: ['active', 'playful']
    },
    {
      id: 'demo-6',
      src: 'https://images.unsplash.com/photo-1518831959646-742c3a14ebf7?w=600&h=600&fit=crop',
      alt: 'Kids Fashion 6',
      caption: 'Sustainable and eco-friendly options',
      title: 'Eco Collection',
      tags: ['eco', 'sustainable']
    }
  ]

  const imagesToRender = isEditing ? demoImages : images
  const displayImages = isEditing ? demoImages : filteredImages

  const renderImageCard = (image: GalleryImage, index: number) => (
    <div
      key={image.id}
      className={cn(
        'group relative overflow-hidden cursor-pointer transition-all duration-300',
        getAspectRatioClass(),
        'hover:scale-105 hover:shadow-lg'
      )}
      onClick={() => openLightbox(index)}
    >
      <img
        src={image.src}
        alt={image.alt}
        className="w-full h-full object-cover"
        loading="lazy"
      />
      
      {/* Overlay */}
      {showOverlay && (
        <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <div className="flex space-x-2">
            {enableLightbox && (
              <Button
                variant="secondary"
                size="sm"
                className="bg-white/90 hover:bg-white"
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            )}
            {enableDownload && (
              <Button
                variant="secondary"
                size="sm"
                className="bg-white/90 hover:bg-white"
                onClick={(e) => {
                  e.stopPropagation()
                  downloadImage(image)
                }}
              >
                <Download className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Caption */}
      {showCaptions && image.caption && (
        <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white p-2">
          <p className="text-sm truncate">{image.caption}</p>
        </div>
      )}
    </div>
  )

  const renderGrid = () => (
    <div className={cn(
      'grid',
      `grid-cols-${columns.mobile}`,
      `md:grid-cols-${columns.tablet}`,
      `lg:grid-cols-${columns.desktop}`,
      getSpacingClass()
    )}>
      {displayImages.map((image, index) => renderImageCard(image, index))}
    </div>
  )

  const renderMasonry = () => (
    <div className={cn(
      'columns-1 md:columns-2 lg:columns-3 xl:columns-4',
      getSpacingClass()
    )}>
      {displayImages.map((image, index) => (
        <div key={image.id} className="break-inside-avoid mb-4">
          {renderImageCard(image, index)}
        </div>
      ))}
    </div>
  )

  const renderCarousel = () => (
    <div className="relative">
      <div className="overflow-hidden rounded-lg">
        <div 
          className="flex transition-transform duration-300 ease-in-out"
          style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        >
          {displayImages.map((image, index) => (
            <div key={image.id} className="w-full flex-shrink-0">
              {renderImageCard(image, index)}
            </div>
          ))}
        </div>
      </div>

      {/* Navigation */}
      {displayImages.length > 1 && (
        <>
          <Button
            variant="outline"
            size="sm"
            className="absolute left-2 top-1/2 -translate-y-1/2 z-10"
            onClick={() => setCurrentSlide(prev => prev === 0 ? displayImages.length - 1 : prev - 1)}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="absolute right-2 top-1/2 -translate-y-1/2 z-10"
            onClick={() => setCurrentSlide(prev => (prev + 1) % displayImages.length)}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </>
      )}

      {/* Thumbnails */}
      {showThumbnails && displayImages.length > 1 && (
        <div className={cn(
          'flex mt-4 space-x-2 overflow-x-auto',
          thumbnailPosition === 'right' && 'flex-col space-x-0 space-y-2 w-20'
        )}>
          {displayImages.map((image, index) => (
            <button
              key={image.id}
              className={cn(
                'flex-shrink-0 w-16 h-12 rounded overflow-hidden border-2 transition-colors',
                index === currentSlide ? 'border-blue-500' : 'border-gray-300'
              )}
              onClick={() => setCurrentSlide(index)}
            >
              <img
                src={image.thumbnail || image.src}
                alt={image.alt}
                className="w-full h-full object-cover"
              />
            </button>
          ))}
        </div>
      )}
    </div>
  )

  const renderLightbox = () => {
    if (!lightboxOpen || displayImages.length === 0) return null

    const currentImage = displayImages[currentImageIndex]

    return (
      <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
        <div className="relative max-w-4xl max-h-full">
          <img
            src={currentImage.src}
            alt={currentImage.alt}
            className="max-w-full max-h-full object-contain"
          />
          
          {/* Close Button */}
          <Button
            variant="outline"
            size="sm"
            className="absolute top-4 right-4 bg-white/90 hover:bg-white"
            onClick={closeLightbox}
          >
            <X className="h-4 w-4" />
          </Button>

          {/* Navigation */}
          {displayImages.length > 1 && (
            <>
              <Button
                variant="outline"
                size="sm"
                className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white"
                onClick={prevImage}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white"
                onClick={nextImage}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </>
          )}

          {/* Caption */}
          {currentImage.caption && (
            <div className="absolute bottom-4 left-4 right-4 bg-black/70 text-white p-3 rounded">
              <p className="text-center">{currentImage.caption}</p>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div style={containerStyles}>
        <div className={cn('w-full', getAlignmentClass())}>
          {/* Header */}
          {(title || subtitle) && (
            <div className="text-center mb-8">
              {title && (
                <h2 className="text-3xl font-light font-p22 tracking-wide mb-2">
                  {title}
                </h2>
              )}
              {subtitle && (
                <p className="text-lg text-muted-foreground font-light">
                  {subtitle}
                </p>
              )}
            </div>
          )}

          {/* Filter Tags */}
          {enableFiltering && filterTags.length > 0 && (
            <div className="flex flex-wrap justify-center gap-2 mb-6">
              <Button
                variant={activeFilter === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveFilter('all')}
              >
                All
              </Button>
              {filterTags.map((tag) => (
                <Button
                  key={tag}
                  variant={activeFilter === tag ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setActiveFilter(tag)}
                >
                  {tag}
                </Button>
              ))}
            </div>
          )}

          {/* Gallery */}
          {layout === 'grid' && renderGrid()}
          {layout === 'masonry' && renderMasonry()}
          {layout === 'carousel' && renderCarousel()}
          {layout === 'justified' && renderGrid()}

          {displayImages.length === 0 && !isEditing && (
            <div className="text-center py-12">
              <p className="text-muted-foreground">No images found.</p>
            </div>
          )}
        </div>
        
        {isEditing && (
          <div className="mt-4 p-2 bg-purple-50 border border-purple-200 rounded text-xs text-purple-700">
            <strong>Image Gallery Block:</strong> {imagesToRender.length} images • {layout} layout • {columns.desktop} columns
          </div>
        )}
      </div>

      {/* Lightbox */}
      {renderLightbox()}
    </BaseBlock>
  )
}

// Image Gallery Block Configuration Component
interface ImageGalleryBlockConfigProps {
  config: ImageGalleryBlockConfig
  onChange: (config: ImageGalleryBlockConfig) => void
}

export function ImageGalleryBlockConfig({ config, onChange }: ImageGalleryBlockConfigProps) {
  const updateConfig = (updates: Partial<ImageGalleryBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const addImage = () => {
    const newImage: GalleryImage = {
      id: `image-${Date.now()}`,
      src: '',
      alt: 'New Image',
      caption: '',
      tags: []
    }
    
    updateConfig({
      images: [...config.images, newImage]
    })
  }

  const updateImage = (index: number, updates: Partial<GalleryImage>) => {
    const updatedImages = [...config.images]
    updatedImages[index] = { ...updatedImages[index], ...updates }
    updateConfig({ images: updatedImages })
  }

  const removeImage = (index: number) => {
    const updatedImages = config.images.filter((_, i) => i !== index)
    updateConfig({ images: updatedImages })
  }

  return (
    <div className="space-y-6">
      {/* Gallery Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Gallery Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Title</Label>
            <Input
              value={config.title || ''}
              onChange={(e) => updateConfig({ title: e.target.value })}
              placeholder="Gallery Title"
              className="mt-1"
            />
          </div>

          <div>
            <Label className="text-xs">Subtitle</Label>
            <Input
              value={config.subtitle || ''}
              onChange={(e) => updateConfig({ subtitle: e.target.value })}
              placeholder="Gallery subtitle"
              className="mt-1"
            />
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Layout</Label>
              <Select
                value={config.layout}
                onValueChange={(value) => updateConfig({ layout: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="grid">Grid</SelectItem>
                  <SelectItem value="masonry">Masonry</SelectItem>
                  <SelectItem value="carousel">Carousel</SelectItem>
                  <SelectItem value="justified">Justified</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Aspect Ratio</Label>
              <Select
                value={config.aspectRatio}
                onValueChange={(value) => updateConfig({ aspectRatio: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="auto">Auto</SelectItem>
                  <SelectItem value="square">Square</SelectItem>
                  <SelectItem value="16:9">16:9</SelectItem>
                  <SelectItem value="4:3">4:3</SelectItem>
                  <SelectItem value="3:2">3:2</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {(config.layout === 'grid' || config.layout === 'justified') && (
            <div className="grid grid-cols-3 gap-2">
              <div>
                <Label className="text-xs">Desktop</Label>
                <Input
                  type="number"
                  min="1"
                  max="6"
                  value={config.columns.desktop}
                  onChange={(e) => updateConfig({
                    columns: { ...config.columns, desktop: parseInt(e.target.value) }
                  })}
                  className="mt-1"
                />
              </div>
              <div>
                <Label className="text-xs">Tablet</Label>
                <Input
                  type="number"
                  min="1"
                  max="4"
                  value={config.columns.tablet}
                  onChange={(e) => updateConfig({
                    columns: { ...config.columns, tablet: parseInt(e.target.value) }
                  })}
                  className="mt-1"
                />
              </div>
              <div>
                <Label className="text-xs">Mobile</Label>
                <Input
                  type="number"
                  min="1"
                  max="2"
                  value={config.columns.mobile}
                  onChange={(e) => updateConfig({
                    columns: { ...config.columns, mobile: parseInt(e.target.value) }
                  })}
                  className="mt-1"
                />
              </div>
            </div>
          )}

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Captions</Label>
              <Switch
                checked={config.showCaptions}
                onCheckedChange={(checked) => updateConfig({ showCaptions: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Overlay</Label>
              <Switch
                checked={config.showOverlay}
                onCheckedChange={(checked) => updateConfig({ showOverlay: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Enable Lightbox</Label>
              <Switch
                checked={config.enableLightbox}
                onCheckedChange={(checked) => updateConfig({ enableLightbox: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Enable Download</Label>
              <Switch
                checked={config.enableDownload}
                onCheckedChange={(checked) => updateConfig({ enableDownload: checked })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Images */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm">Images</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={addImage}
            className="h-8 px-2"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Image
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.images.map((image, index) => (
            <div key={image.id} className="border rounded-lg p-3 space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Image {index + 1}</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeImage(index)}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
              
              <div>
                <Label className="text-xs">Image URL</Label>
                <Input
                  value={image.src}
                  onChange={(e) => updateImage(index, { src: e.target.value })}
                  className="mt-1"
                  placeholder="https://..."
                />
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Alt Text</Label>
                  <Input
                    value={image.alt}
                    onChange={(e) => updateImage(index, { alt: e.target.value })}
                    className="mt-1"
                    placeholder="Image description"
                  />
                </div>
                <div>
                  <Label className="text-xs">Caption</Label>
                  <Input
                    value={image.caption || ''}
                    onChange={(e) => updateImage(index, { caption: e.target.value })}
                    className="mt-1"
                    placeholder="Image caption"
                  />
                </div>
              </div>
            </div>
          ))}
          
          {config.images.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              <p className="text-sm">No images yet.</p>
              <p className="text-xs">Click "Add Image" to get started.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
