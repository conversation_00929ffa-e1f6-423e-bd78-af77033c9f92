'use client'

import React, { useState, useEffect, useRef } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Plus, Trash2, TrendingUp, Users, ShoppingBag, Star } from 'lucide-react'
import { cn } from '@/lib/utils'

interface StatsCounterWidgetBlockConfig {
  title?: string
  stats: StatConfig[]
  layout: 'horizontal' | 'vertical' | 'grid'
  style: 'simple' | 'cards' | 'highlighted'
  animationType: 'none' | 'countUp' | 'slideIn' | 'fadeIn'
  animationDuration: number
  showIcons: boolean
  showPrefix: boolean
  showSuffix: boolean
  alignment: 'left' | 'center' | 'right'
  spacing: 'none' | 'sm' | 'md' | 'lg'
  backgroundColor: string
  borderRadius: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
}

interface StatConfig {
  id: string
  label: string
  value: number
  prefix?: string
  suffix?: string
  icon?: string
  color?: string
  description?: string
  trend?: {
    direction: 'up' | 'down' | 'neutral'
    percentage: number
    period: string
  }
}

interface StatsCounterWidgetBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function StatsCounterWidgetBlock({ block, isEditing = false }: StatsCounterWidgetBlockProps) {
  const config = block.configuration as StatsCounterWidgetBlockConfig
  const [animatedValues, setAnimatedValues] = useState<Record<string, number>>({})
  const [isVisible, setIsVisible] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  const {
    title,
    stats,
    layout,
    style,
    animationType,
    animationDuration,
    showIcons,
    showPrefix,
    showSuffix,
    alignment,
    spacing,
    backgroundColor,
    borderRadius,
    padding,
  } = config

  // Intersection Observer for animation trigger
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => observer.disconnect()
  }, [isVisible])

  // Count up animation
  useEffect(() => {
    if (!isVisible || animationType !== 'countUp' || isEditing) return

    const duration = animationDuration * 1000
    const startTime = Date.now()

    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)

      const newValues: Record<string, number> = {}
      stats.forEach(stat => {
        const easeOutQuart = 1 - Math.pow(1 - progress, 4)
        newValues[stat.id] = Math.floor(stat.value * easeOutQuart)
      })

      setAnimatedValues(newValues)

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }

    animate()
  }, [isVisible, stats, animationType, animationDuration, isEditing])

  const getStatIcon = (iconName?: string) => {
    if (!iconName) return null

    const icons = {
      users: <Users className="h-5 w-5" />,
      trending: <TrendingUp className="h-5 w-5" />,
      shopping: <ShoppingBag className="h-5 w-5" />,
      star: <Star className="h-5 w-5" />
    }

    return icons[iconName as keyof typeof icons] || <TrendingUp className="h-5 w-5" />
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toLocaleString()
  }

  const getDisplayValue = (stat: StatConfig) => {
    if (animationType === 'countUp' && isVisible && !isEditing) {
      return animatedValues[stat.id] ?? 0
    }
    return stat.value
  }

  const getSpacingClass = () => {
    const spacingClasses = {
      none: 'gap-0',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6'
    }
    return spacingClasses[spacing]
  }

  const getLayoutClasses = () => {
    switch (layout) {
      case 'vertical':
        return 'flex flex-col'
      case 'grid':
        return `grid grid-cols-1 md:grid-cols-2 lg:grid-cols-${Math.min(stats.length, 4)}`
      default:
        return 'flex flex-row flex-wrap'
    }
  }

  const getAlignmentClass = () => {
    const alignmentClasses = {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right'
    }
    return alignmentClasses[alignment]
  }

  const getAnimationClass = () => {
    if (!isVisible || isEditing) return ''

    const animationClasses = {
      none: '',
      countUp: '',
      slideIn: 'animate-slide-in-up',
      fadeIn: 'animate-fade-in'
    }
    return animationClasses[animationType]
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
  }

  // Demo stats for editing mode
  const demoStats = [
    {
      id: 'demo-1',
      label: 'Happy Customers',
      value: 12500,
      suffix: '+',
      icon: 'users',
      color: '#3B82F6',
      description: 'Satisfied customers worldwide',
      trend: {
        direction: 'up' as const,
        percentage: 12,
        period: 'this month'
      }
    },
    {
      id: 'demo-2',
      label: 'Products Sold',
      value: 45000,
      suffix: '+',
      icon: 'shopping',
      color: '#10B981',
      description: 'Items delivered successfully'
    },
    {
      id: 'demo-3',
      label: 'Years Experience',
      value: 15,
      suffix: '+',
      icon: 'star',
      color: '#F59E0B',
      description: 'Years in business'
    },
    {
      id: 'demo-4',
      label: 'Growth Rate',
      value: 98,
      suffix: '%',
      icon: 'trending',
      color: '#EF4444',
      description: 'Annual growth rate',
      trend: {
        direction: 'up' as const,
        percentage: 5,
        period: 'vs last year'
      }
    }
  ]

  const statsToRender = isEditing ? demoStats : stats

  const renderStat = (stat: StatConfig) => {
    const displayValue = getDisplayValue(stat)

    if (style === 'cards') {
      return (
        <Card
          key={stat.id}
          className={cn(
            'transition-all duration-300',
            getAnimationClass()
          )}
        >
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-2">
              {showIcons && stat.icon && (
                <div style={{ color: stat.color }}>
                  {getStatIcon(stat.icon)}
                </div>
              )}
              {stat.trend && (
                <div className={cn(
                  'flex items-center text-xs',
                  stat.trend.direction === 'up' ? 'text-green-600' : 
                  stat.trend.direction === 'down' ? 'text-red-600' : 'text-gray-600'
                )}>
                  <TrendingUp className={cn(
                    'h-3 w-3 mr-1',
                    stat.trend.direction === 'down' && 'rotate-180'
                  )} />
                  {stat.trend.percentage}%
                </div>
              )}
            </div>
            <div className={getAlignmentClass()}>
              <div className="text-2xl font-bold mb-1" style={{ color: stat.color }}>
                {showPrefix && stat.prefix}
                {formatNumber(displayValue)}
                {showSuffix && stat.suffix}
              </div>
              <div className="text-sm font-medium text-gray-900">{stat.label}</div>
              {stat.description && (
                <div className="text-xs text-gray-500 mt-1">{stat.description}</div>
              )}
              {stat.trend && (
                <div className="text-xs text-gray-500 mt-1">{stat.trend.period}</div>
              )}
            </div>
          </CardContent>
        </Card>
      )
    }

    if (style === 'highlighted') {
      return (
        <div
          key={stat.id}
          className={cn(
            'p-4 rounded-lg border-l-4 bg-gray-50 transition-all duration-300',
            getAnimationClass(),
            getAlignmentClass()
          )}
          style={{ borderLeftColor: stat.color }}
        >
          <div className="flex items-center space-x-3">
            {showIcons && stat.icon && (
              <div style={{ color: stat.color }}>
                {getStatIcon(stat.icon)}
              </div>
            )}
            <div className="flex-1">
              <div className="text-xl font-bold" style={{ color: stat.color }}>
                {showPrefix && stat.prefix}
                {formatNumber(displayValue)}
                {showSuffix && stat.suffix}
              </div>
              <div className="text-sm font-medium text-gray-900">{stat.label}</div>
              {stat.description && (
                <div className="text-xs text-gray-500">{stat.description}</div>
              )}
            </div>
          </div>
        </div>
      )
    }

    // Default: simple style
    return (
      <div
        key={stat.id}
        className={cn(
          'transition-all duration-300',
          getAnimationClass(),
          getAlignmentClass()
        )}
      >
        <div className="flex items-center space-x-2 mb-1">
          {showIcons && stat.icon && (
            <div style={{ color: stat.color }}>
              {getStatIcon(stat.icon)}
            </div>
          )}
          <div className="text-lg font-bold" style={{ color: stat.color }}>
            {showPrefix && stat.prefix}
            {formatNumber(displayValue)}
            {showSuffix && stat.suffix}
          </div>
        </div>
        <div className="text-sm font-medium text-gray-900">{stat.label}</div>
        {stat.description && (
          <div className="text-xs text-gray-500">{stat.description}</div>
        )}
      </div>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div ref={containerRef} style={containerStyles}>
        {title && (
          <h3 className={cn('font-semibold mb-6', `text-${alignment}`)}>
            {title}
          </h3>
        )}
        
        <div className={cn(
          getLayoutClasses(),
          getSpacingClass()
        )}>
          {statsToRender.map(renderStat)}
        </div>
        
        {isEditing && (
          <div className="mt-4 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-700">
            <strong>Stats Counter Widget:</strong> {statsToRender.length} stats • {style} style • {animationType} animation
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Stats Counter Widget Block Configuration Component
interface StatsCounterWidgetBlockConfigProps {
  config: StatsCounterWidgetBlockConfig
  onChange: (config: StatsCounterWidgetBlockConfig) => void
}

export function StatsCounterWidgetBlockConfig({ config, onChange }: StatsCounterWidgetBlockConfigProps) {
  const updateConfig = (updates: Partial<StatsCounterWidgetBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const addStat = () => {
    const newStat: StatConfig = {
      id: `stat-${Date.now()}`,
      label: 'New Stat',
      value: 100,
      icon: 'trending',
      color: '#3B82F6'
    }
    
    updateConfig({
      stats: [...config.stats, newStat]
    })
  }

  const updateStat = (index: number, updates: Partial<StatConfig>) => {
    const updatedStats = [...config.stats]
    updatedStats[index] = { ...updatedStats[index], ...updates }
    updateConfig({ stats: updatedStats })
  }

  const removeStat = (index: number) => {
    const updatedStats = config.stats.filter((_, i) => i !== index)
    updateConfig({ stats: updatedStats })
  }

  return (
    <div className="space-y-6">
      {/* Widget Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Widget Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Title</Label>
            <Input
              value={config.title || ''}
              onChange={(e) => updateConfig({ title: e.target.value })}
              placeholder="Our Statistics"
              className="mt-1"
            />
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Layout</Label>
              <Select
                value={config.layout}
                onValueChange={(value) => updateConfig({ layout: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="horizontal">Horizontal</SelectItem>
                  <SelectItem value="vertical">Vertical</SelectItem>
                  <SelectItem value="grid">Grid</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Style</Label>
              <Select
                value={config.style}
                onValueChange={(value) => updateConfig({ style: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="simple">Simple</SelectItem>
                  <SelectItem value="cards">Cards</SelectItem>
                  <SelectItem value="highlighted">Highlighted</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label className="text-xs">Animation Type</Label>
            <Select
              value={config.animationType}
              onValueChange={(value) => updateConfig({ animationType: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                <SelectItem value="countUp">Count Up</SelectItem>
                <SelectItem value="slideIn">Slide In</SelectItem>
                <SelectItem value="fadeIn">Fade In</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Icons</Label>
              <Switch
                checked={config.showIcons}
                onCheckedChange={(checked) => updateConfig({ showIcons: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Prefix</Label>
              <Switch
                checked={config.showPrefix}
                onCheckedChange={(checked) => updateConfig({ showPrefix: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Suffix</Label>
              <Switch
                checked={config.showSuffix}
                onCheckedChange={(checked) => updateConfig({ showSuffix: checked })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Items */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm">Statistics</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={addStat}
            className="h-8 px-2"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Stat
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.stats.map((stat, index) => (
            <div key={stat.id} className="border rounded-lg p-3 space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Stat {index + 1}</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeStat(index)}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Label</Label>
                  <Input
                    value={stat.label}
                    onChange={(e) => updateStat(index, { label: e.target.value })}
                    className="mt-1"
                    placeholder="Stat label"
                  />
                </div>
                <div>
                  <Label className="text-xs">Value</Label>
                  <Input
                    type="number"
                    value={stat.value}
                    onChange={(e) => updateStat(index, { value: parseInt(e.target.value) || 0 })}
                    className="mt-1"
                    placeholder="0"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-2">
                <div>
                  <Label className="text-xs">Prefix</Label>
                  <Input
                    value={stat.prefix || ''}
                    onChange={(e) => updateStat(index, { prefix: e.target.value })}
                    className="mt-1"
                    placeholder="$"
                  />
                </div>
                <div>
                  <Label className="text-xs">Suffix</Label>
                  <Input
                    value={stat.suffix || ''}
                    onChange={(e) => updateStat(index, { suffix: e.target.value })}
                    className="mt-1"
                    placeholder="+"
                  />
                </div>
                <div>
                  <Label className="text-xs">Color</Label>
                  <Input
                    type="color"
                    value={stat.color || '#3B82F6'}
                    onChange={(e) => updateStat(index, { color: e.target.value })}
                    className="mt-1 h-8"
                  />
                </div>
              </div>
            </div>
          ))}
          
          {config.stats.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              <p className="text-sm">No statistics yet.</p>
              <p className="text-xs">Click "Add Stat" to get started.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
