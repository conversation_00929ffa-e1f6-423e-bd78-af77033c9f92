'use client'

import React, { useState } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Search, Filter, X, MapPin, Calendar, DollarSign } from 'lucide-react'
import { cn } from '@/lib/utils'

interface SearchWidgetBlockConfig {
  placeholder: string
  showFilters: boolean
  filters: FilterConfig[]
  searchType: 'products' | 'content' | 'locations' | 'events'
  layout: 'horizontal' | 'vertical' | 'compact'
  showSearchButton: boolean
  showClearButton: boolean
  autoSearch: boolean
  searchDelay: number
  backgroundColor: string
  borderRadius: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
  position: 'static' | 'fixed' | 'sticky'
  fixedPosition: 'top' | 'bottom'
}

interface FilterConfig {
  id: string
  label: string
  type: 'select' | 'range' | 'checkbox' | 'date'
  options?: string[]
  min?: number
  max?: number
  defaultValue?: any
}

interface SearchWidgetBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function SearchWidgetBlock({ block, isEditing = false }: SearchWidgetBlockProps) {
  const config = block.configuration as SearchWidgetBlockConfig
  const [searchQuery, setSearchQuery] = useState('')
  const [activeFilters, setActiveFilters] = useState<Record<string, any>>({})
  const [showFilters, setShowFilters] = useState(false)

  const {
    placeholder,
    showFilters: enableFilters,
    filters,
    searchType,
    layout,
    showSearchButton,
    showClearButton,
    autoSearch,
    searchDelay,
    backgroundColor,
    borderRadius,
    padding,
    position,
    fixedPosition,
  } = config

  const handleSearch = () => {
    if (isEditing) return
    
    console.log('Searching for:', searchQuery, 'with filters:', activeFilters)
    // Implement actual search logic here
  }

  const handleClear = () => {
    setSearchQuery('')
    setActiveFilters({})
  }

  const updateFilter = (filterId: string, value: any) => {
    setActiveFilters(prev => ({
      ...prev,
      [filterId]: value
    }))
  }

  const getSearchIcon = () => {
    switch (searchType) {
      case 'locations':
        return <MapPin className="h-4 w-4" />
      case 'events':
        return <Calendar className="h-4 w-4" />
      case 'products':
        return <DollarSign className="h-4 w-4" />
      default:
        return <Search className="h-4 w-4" />
    }
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
  }

  const getPositionClasses = () => {
    if (position === 'fixed') {
      return cn(
        'fixed left-0 right-0 z-40',
        fixedPosition === 'bottom' ? 'bottom-0' : 'top-0'
      )
    }
    if (position === 'sticky') {
      return 'sticky top-0 z-30'
    }
    return ''
  }

  const renderFilters = () => {
    if (!enableFilters || !showFilters) return null

    return (
      <div className="mt-4 p-4 bg-gray-50 rounded-lg space-y-3">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium">Filters</h4>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowFilters(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        <div className={cn(
          'grid gap-3',
          layout === 'horizontal' ? 'grid-cols-2 md:grid-cols-4' : 'grid-cols-1'
        )}>
          {filters.map((filter) => (
            <div key={filter.id}>
              <Label className="text-xs">{filter.label}</Label>
              {filter.type === 'select' && (
                <Select
                  value={activeFilters[filter.id] || ''}
                  onValueChange={(value) => updateFilter(filter.id, value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select..." />
                  </SelectTrigger>
                  <SelectContent>
                    {filter.options?.map((option) => (
                      <SelectItem key={option} value={option}>
                        {option}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
              {filter.type === 'range' && (
                <div className="flex space-x-2 mt-1">
                  <Input
                    type="number"
                    placeholder="Min"
                    min={filter.min}
                    max={filter.max}
                    value={activeFilters[filter.id]?.min || ''}
                    onChange={(e) => updateFilter(filter.id, {
                      ...activeFilters[filter.id],
                      min: e.target.value
                    })}
                  />
                  <Input
                    type="number"
                    placeholder="Max"
                    min={filter.min}
                    max={filter.max}
                    value={activeFilters[filter.id]?.max || ''}
                    onChange={(e) => updateFilter(filter.id, {
                      ...activeFilters[filter.id],
                      max: e.target.value
                    })}
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div className={cn(getPositionClasses())} style={containerStyles}>
        <div className={cn(
          'flex',
          layout === 'vertical' ? 'flex-col space-y-3' : 'items-center space-x-2',
          layout === 'compact' && 'max-w-md mx-auto'
        )}>
          {/* Search Input */}
          <div className="flex-1 relative">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              {getSearchIcon()}
            </div>
            <Input
              type="text"
              placeholder={placeholder}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4"
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !isEditing) {
                  handleSearch()
                }
              }}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            {enableFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className={cn(showFilters && 'bg-blue-50 border-blue-200')}
              >
                <Filter className="h-4 w-4" />
              </Button>
            )}
            
            {showSearchButton && (
              <Button
                size="sm"
                onClick={handleSearch}
                disabled={isEditing}
              >
                Search
              </Button>
            )}
            
            {showClearButton && (searchQuery || Object.keys(activeFilters).length > 0) && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleClear}
              >
                Clear
              </Button>
            )}
          </div>
        </div>

        {renderFilters()}
        
        {isEditing && (
          <div className="mt-4 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700">
            <strong>Search Widget:</strong> {searchType} search • {layout} layout
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Search Widget Block Configuration Component
interface SearchWidgetBlockConfigProps {
  config: SearchWidgetBlockConfig
  onChange: (config: SearchWidgetBlockConfig) => void
}

export function SearchWidgetBlockConfig({ config, onChange }: SearchWidgetBlockConfigProps) {
  const updateConfig = (updates: Partial<SearchWidgetBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const addFilter = () => {
    const newFilter: FilterConfig = {
      id: `filter-${Date.now()}`,
      label: 'New Filter',
      type: 'select',
      options: ['Option 1', 'Option 2']
    }
    
    updateConfig({
      filters: [...config.filters, newFilter]
    })
  }

  return (
    <div className="space-y-6">
      {/* Search Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Search Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Placeholder Text</Label>
            <Input
              value={config.placeholder}
              onChange={(e) => updateConfig({ placeholder: e.target.value })}
              placeholder="Search placeholder..."
              className="mt-1"
            />
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Search Type</Label>
              <Select
                value={config.searchType}
                onValueChange={(value) => updateConfig({ searchType: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="products">Products</SelectItem>
                  <SelectItem value="content">Content</SelectItem>
                  <SelectItem value="locations">Locations</SelectItem>
                  <SelectItem value="events">Events</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Layout</Label>
              <Select
                value={config.layout}
                onValueChange={(value) => updateConfig({ layout: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="horizontal">Horizontal</SelectItem>
                  <SelectItem value="vertical">Vertical</SelectItem>
                  <SelectItem value="compact">Compact</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Filters</Label>
              <Switch
                checked={config.showFilters}
                onCheckedChange={(checked) => updateConfig({ showFilters: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Search Button</Label>
              <Switch
                checked={config.showSearchButton}
                onCheckedChange={(checked) => updateConfig({ showSearchButton: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Clear Button</Label>
              <Switch
                checked={config.showClearButton}
                onCheckedChange={(checked) => updateConfig({ showClearButton: checked })}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
