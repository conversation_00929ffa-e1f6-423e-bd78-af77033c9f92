'use client'

import React, { useState, useEffect } from 'react'
import { PageBlock } from '../../types'
import { BaseBlock } from '../base-block'
import { useWishlist } from '@/components/wishlist-provider'
import { ProductCard } from '@/components/storefront/products/product-card'
import { Button } from '@/components/ui/button'
import { Heart } from 'lucide-react'
import Link from 'next/link'
import { getProductsByIds } from '@/lib/products'

interface WishlistBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function WishlistBlock({ block, isEditing = false }: WishlistBlockProps) {
  const { items: wishlistItems } = useWishlist()
  const [products, setProducts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  // Fetch wishlist products using real e-commerce system
  useEffect(() => {
    const fetchWishlistProducts = async () => {
      if (wishlistItems.length === 0) {
        setProducts([])
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        const productData = await getProductsByIds(wishlistItems)
        setProducts(productData)
      } catch (error) {
        console.error('Failed to load wishlist products:', error)
        setProducts([])
      } finally {
        setLoading(false)
      }
    }

    fetchWishlistProducts()
  }, [wishlistItems])

  // Empty wishlist state - exactly like hardcoded
  if (products.length === 0 && !loading) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <div className="container px-4 md:px-6 py-16">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <Heart className="h-16 w-16 text-muted-foreground" />
            <h1 className="text-2xl font-bold font-montserrat">Your wishlist is empty</h1>
            <p className="text-muted-foreground">Save items you love to your wishlist and shop them later.</p>
            <Button asChild>
              <Link href="/products">Start Shopping</Link>
            </Button>
          </div>
        </div>
      </BaseBlock>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      {/* Exact copy of hardcoded wishlist page structure */}
      <div className="container px-4 md:px-6 py-6 md:py-10">
        <h1 className="text-2xl md:text-3xl font-bold font-montserrat mb-6">My Wishlist</h1>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
          {products.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      </div>
    </BaseBlock>
  )
}
