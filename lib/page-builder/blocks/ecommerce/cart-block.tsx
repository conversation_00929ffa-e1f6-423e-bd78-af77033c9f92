'use client'

import React from 'react'
import { PageBlock, CartBlockConfig } from '../../types'
import { BaseBlock } from '../base-block'
import { useCart } from '@/hooks/use-cart'
import { CartItem } from '@/components/storefront/cart/cart-item'
import { CartSummary } from '@/components/storefront/cart/cart-summary'
import { Button } from '@/components/ui/button'
import { ShoppingBag } from 'lucide-react'
import Link from 'next/link'

interface CartBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function CartBlock({ block, isEditing = false }: CartBlockProps) {
  const { items, isEmpty } = useCart()

  // Use real cart data - no mock data needed
  const displayItems = items
  const isCartEmpty = isEmpty

  // Empty cart state - exactly like hardcoded
  if (isCartEmpty) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <div className="container px-4 md:px-6 py-16">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <ShoppingBag className="h-16 w-16 text-muted-foreground" />
            <h1 className="text-2xl font-bold font-montserrat">Your cart is empty</h1>
            <p className="text-muted-foreground">Looks like you haven't added anything to your cart yet.</p>
            <Button asChild>
              <Link href="/products">Continue Shopping</Link>
            </Button>
          </div>
        </div>
      </BaseBlock>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      {/* Exact copy of hardcoded cart page structure */}
      <div className="container px-4 md:px-6 py-6 md:py-10">
        <h1 className="text-2xl md:text-3xl font-bold font-montserrat mb-6">Shopping Cart</h1>
        <div className="grid lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <div className="space-y-4">
              {displayItems.map((item) => (
                <CartItem key={`${item.id}-${item.size}`} item={item} />
              ))}
            </div>
          </div>
          <div>
            <CartSummary />
          </div>
        </div>
      </div>
    </BaseBlock>
  )
}
