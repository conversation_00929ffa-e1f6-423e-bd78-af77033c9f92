'use client'

import React from 'react'
import { PageBlock, ProductDetailsBlockConfig } from '../../types'
import { BaseBlock } from '../base-block'
import { ProductInfo } from '@/components/storefront/products/product-info'
import { RelatedProducts } from '@/components/related-products'
import { SizeRecommendation } from '@/components/size-recommendation'
import { AIProductRecommendations } from '@/components/storefront/products/ai-product-recommendations'
import { useProduct } from '@/lib/ecommerce/hooks/use-products'

interface ProductDetailsBlockProps {
  block: PageBlock
  isEditing?: boolean
  productSlug?: string // For dynamic product pages
}

export function ProductDetailsBlock({ block, isEditing = false, productSlug }: ProductDetailsBlockProps) {
  // Use the real e-commerce hook instead of manual state management
  const { product, loading, error } = useProduct({
    slug: productSlug,
    autoFetch: !isEditing && !!productSlug
  })

  // Handle loading and error states
  if (loading) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <div className="container px-4 md:px-6 py-8">
          <div>Loading product...</div>
        </div>
      </BaseBlock>
    )
  }

  if (error || !product) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <div className="container px-4 md:px-6 py-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Product Not Found</h2>
            <p className="text-muted-foreground">
              {error || 'The product you are looking for does not exist.'}
            </p>
          </div>
        </div>
      </BaseBlock>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      {/* Exact copy of hardcoded product page structure */}
      <div>
        {/* Product Info - exactly like hardcoded */}
        <ProductInfo product={product} />

        {/* Size Recommendation - exactly like hardcoded */}
        <SizeRecommendation product={product} />

        {/* Related Products - exactly like hardcoded */}
        <RelatedProducts product={product} />

        {/* AI Product Recommendations - exactly like hardcoded */}
        <AIProductRecommendations product={product} />
      </div>
    </BaseBlock>
  )
}
