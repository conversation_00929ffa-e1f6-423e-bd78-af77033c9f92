# E-commerce & Landing Page Builder Blocks

This directory contains page builder blocks that integrate with the real e-commerce library and replicate the exact functionality and styling of the hardcoded components. These blocks use the actual e-commerce API routes, services, and hooks - **no mock or simulated data**.

## Block Categories

### E-commerce Blocks
Complete e-commerce functionality with real data integration.

### Landing Page Blocks
Landing page components with real e-commerce integration and Zara-style design.

## Available Blocks

### 1. Product Listing Block (`product-listing`)
**Purpose**: Complete product listing page with filters, sorting, and grid layout
**Replicates**: `/app/products/page.tsx`

**Features**:
- Exact same layout as hardcoded products page
- Desktop filters sidebar (hidden on mobile)
- Mobile filter button with sheet overlay
- Product sorting dropdown
- Responsive product grid
- Search parameter handling

**Usage**:
```typescript
{
  type: 'product-listing',
  configuration: {
    title: 'All Products',
    showFilters: true,
    showSort: true,
    showMobileFilters: true,
    filtersPosition: 'left'
  }
}
```

### 2. Cart Block (`cart`)
**Purpose**: Shopping cart page with items and summary
**Replicates**: `/app/frontend/cart/page.tsx`

**Features**:
- Exact same layout as hardcoded cart page
- Empty cart state with continue shopping button
- Cart items list with quantity controls
- Cart summary with totals
- Responsive grid layout

**Usage**:
```typescript
{
  type: 'cart',
  configuration: {}
}
```

### 3. Checkout Block (`checkout`)
**Purpose**: Checkout page with forms and order summary
**Replicates**: `/app/frontend/checkout/page.tsx`

**Features**:
- Exact same layout as hardcoded checkout page
- Empty cart redirect
- Checkout form integration
- Order summary sidebar
- Order confirmation handling

**Usage**:
```typescript
{
  type: 'checkout',
  configuration: {}
}
```

### 4. Product Details Block (`product-details`)
**Purpose**: Individual product page with gallery and info
**Replicates**: `/app/products/[slug]/page.tsx`

**Features**:
- Exact same layout as hardcoded product page
- Product info component integration
- Size recommendation component
- Related products section
- AI product recommendations

**Usage**:
```typescript
{
  type: 'product-details',
  configuration: {}
}
```

### 5. Wishlist Block (`wishlist`)
**Purpose**: Wishlist page with saved products
**Replicates**: `/app/frontend/wishlist/page.tsx`

**Features**:
- Exact same layout as hardcoded wishlist page
- Empty wishlist state
- Product grid display
- Wishlist functionality integration

**Usage**:
```typescript
{
  type: 'wishlist',
  configuration: {}
}
```

### 6. Collection Header Block (`collection-header`)
**Purpose**: Collection page header with background, title, and product count
**Integrates with**: Real product count from e-commerce API

**Features**:
- Dynamic product count from real data
- Background image/video support
- Breadcrumb navigation
- Customizable layout and styling

**Usage**:
```typescript
{
  type: 'collection-header',
  configuration: {
    title: 'Summer Collection',
    showProductCount: true,
    showBreadcrumbs: true,
    height: 'medium',
    alignment: 'center'
  }
}
```

### 7. Account Dashboard Block (`account-dashboard`)
**Purpose**: User account dashboard with orders, profile, and account management
**Integrates with**: Real order data and user authentication

**Features**:
- Real order history from e-commerce API
- User profile information
- Account management links
- Recent activity tracking

**Usage**:
```typescript
{
  type: 'account-dashboard',
  configuration: {
    showProfile: true,
    showOrders: true,
    showWishlist: true,
    recentOrdersLimit: 5
  }
}
```

### 8. Product Comparison Block (`product-comparison`)
**Purpose**: Side-by-side product comparison with features and pricing
**Integrates with**: Real product data from e-commerce API

**Features**:
- Real product data fetching
- Comparison list persistence
- Feature-by-feature comparison
- Add to cart functionality

**Usage**:
```typescript
{
  type: 'product-comparison',
  configuration: {
    maxProducts: 4,
    showFeatures: ['price', 'colors', 'sizes', 'description'],
    showPricing: true,
    showAddToCart: true
  }
}
```

## Landing Page Blocks

### 9. Hero Section Block (`hero-section`)
**Purpose**: Full-screen hero section with background image/video and call-to-action
**Replicates**: Hardcoded hero section on landing page

**Features**:
- Background image/video support
- Zara-style minimal content overlay
- Customizable animations and positioning
- Real brand images from assets directory

**Usage**:
```typescript
{
  type: 'hero-section',
  configuration: {
    title: 'KIDS COLLECTION',
    backgroundImage: '/assets/images/cocomilk_kids-20210912_114630-3065525289.jpg',
    ctaButton: { text: 'SHOP NOW', url: '/products', style: 'minimal' },
    style: 'zara'
  }
}
```

### 10. New Arrivals Block (`new-arrivals`)
**Purpose**: New arrivals product section with real product data
**Integrates with**: Real product API filtering by `isNew: true`

**Features**:
- Real new arrivals from e-commerce API
- Zara-style minimal grid layout
- Configurable product limits and columns
- View all link integration

**Usage**:
```typescript
{
  type: 'new-arrivals',
  configuration: {
    title: 'NEW IN',
    limit: 6,
    showViewAllLink: true,
    style: 'zara'
  }
}
```

### 11. Editorial Section Block (`editorial-section`)
**Purpose**: Zara-style asymmetric editorial grid with images and content
**Replicates**: Hardcoded editorial section on landing page

**Features**:
- Asymmetric layout like Zara website
- Real brand images from assets directory
- Hover animations and transitions
- Configurable content items

**Usage**:
```typescript
{
  type: 'editorial-section',
  configuration: {
    layout: 'asymmetric',
    style: 'zara',
    items: [
      {
        title: 'SPRING ESSENTIALS',
        image: '/assets/images/cocomilk_kids-20220829_111232-3780725228.jpg',
        link: '/collections/editorial',
        size: 'large'
      }
    ]
  }
}
```

### 12. Newsletter Signup Block (`newsletter-signup`)
**Purpose**: Newsletter subscription form with real email integration
**Integrates with**: Real newsletter API and database

**Features**:
- Real newsletter subscription via API
- Database persistence with Prisma
- Zara-style minimal form design
- Email validation and error handling

**Usage**:
```typescript
{
  type: 'newsletter-signup',
  configuration: {
    title: 'Stay Updated',
    style: 'zara',
    buttonText: 'SUBSCRIBE'
  }
}
```

## Implementation Details

### Exact Fidelity
All blocks are designed to render **exactly** the same as their hardcoded counterparts:
- Same HTML structure
- Same CSS classes
- Same component integrations
- Same responsive behavior
- Same functionality

### Real E-commerce Integration
The blocks integrate with the actual e-commerce library:
- **API Routes**: Use `/api/e-commerce/*` endpoints
- **Services**: Integrate with `ProductService`, `CartService`, `OrderService`, etc.
- **Hooks**: Use `useProducts`, `useCart`, `useOrders`, etc.
- **Database**: Connect to real Prisma database
- **No Mock Data**: All data comes from the real e-commerce system

### Component Reuse
The blocks reuse the existing components that are already connected to the real system:
- `ProductGrid` - Connected to real product API
- `ProductFilters` - Real filtering functionality
- `ProductSort` - Real sorting functionality
- `MobileFilters` - Real mobile filter experience
- `CartItem` - Real cart operations
- `CartSummary` - Real cart calculations
- `CheckoutForm` - Real checkout processing
- `ProductInfo` - Real product data display

### Data Flow
1. **Products**: Fetched from `/api/e-commerce/products` using `ProductService`
2. **Cart**: Managed through `CartService` and persisted in database/localStorage
3. **Orders**: Created and managed through `OrderService`
4. **Authentication**: Integrated with real auth system
5. **Wishlist**: Persisted and synchronized across sessions

### Search Parameters
The Product Listing Block properly handles search parameters for filtering and sorting, maintaining the same URL structure as the hardcoded implementation.

## Example Usage

### Converting Hardcoded Pages
See the example dynamic pages in `/app/frontend/*-dynamic/` for how to convert hardcoded pages to use the page builder system:

- `/app/products-dynamic/page.tsx` - Product listing
- `/app/frontend/cart-dynamic/page.tsx` - Shopping cart
- `/app/frontend/checkout-dynamic/page.tsx` - Checkout
- `/app/frontend/wishlist-dynamic/page.tsx` - Wishlist

### Creating Dynamic E-commerce Pages
You can now create e-commerce pages entirely through the page builder interface without writing any code:

1. Go to the page builder admin
2. Create a new page
3. Add e-commerce blocks
4. Configure as needed
5. Publish

The resulting pages will function exactly like the hardcoded versions but can be modified through the visual interface.

## Benefits

1. **No Code Changes**: Create new e-commerce pages without touching code
2. **Exact Functionality**: All original features and styling preserved
3. **Visual Editing**: Modify pages through the page builder interface
4. **Consistent Experience**: Users see no difference from hardcoded pages
5. **Flexible Layouts**: Combine e-commerce blocks with other content blocks
6. **SEO Friendly**: Full control over meta tags and page structure

## Migration Strategy

1. **Phase 1**: Use dynamic pages alongside hardcoded pages for testing
2. **Phase 2**: Gradually replace hardcoded routes with dynamic equivalents
3. **Phase 3**: Remove hardcoded pages once dynamic versions are verified
4. **Phase 4**: Create new e-commerce pages using only the page builder

This approach ensures zero downtime and allows for thorough testing before full migration.
