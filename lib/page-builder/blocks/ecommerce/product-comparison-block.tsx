'use client'

import React, { useState, useEffect } from 'react'
import { PageBlock, ProductComparisonBlockConfig } from '../../types'
import { BaseBlock } from '../base-block'
import { getProductsByIds } from '@/lib/products'
import { useCart } from '@/hooks/use-cart'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Scale, X, ShoppingBag } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { toast } from '@/components/ui/use-toast'
import { formatPrice } from '@/lib/utils'

interface ProductComparisonBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function ProductComparisonBlock({ block, isEditing = false }: ProductComparisonBlockProps) {
  const config = block.configuration as ProductComparisonBlockConfig
  const [compareList, setCompareList] = useState<string[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const { addItem } = useCart()

  const {
    maxProducts = 4,
    showFeatures = ['price', 'colors', 'sizes', 'description'],
    showPricing = true,
    showRatings = true,
    showImages = true,
    showAddToCart = true,
    emptyComparisonMessage = 'No products to compare',
    emptyComparisonCta = { text: 'Browse Products', url: '/products' }
  } = config

  // Load comparison list from localStorage
  useEffect(() => {
    if (!isEditing) {
      const stored = localStorage.getItem('compareList')
      if (stored) {
        try {
          setCompareList(JSON.parse(stored))
        } catch (error) {
          console.error('Failed to parse compare list:', error)
          setCompareList([])
        }
      }
    }
    setLoading(false)
  }, [isEditing])

  // Fetch products when compare list changes
  useEffect(() => {
    const loadProducts = async () => {
      if (compareList.length > 0) {
        try {
          setLoading(true)
          const productData = await getProductsByIds(compareList)
          setProducts(productData)
        } catch (error) {
          console.error('Failed to load comparison products:', error)
          setProducts([])
        } finally {
          setLoading(false)
        }
      } else {
        setProducts([])
        setLoading(false)
      }
    }

    loadProducts()
  }, [compareList])

  const removeFromComparison = (productId: string) => {
    if (isEditing) return

    const newCompareList = compareList.filter(id => id !== productId)
    setCompareList(newCompareList)
    localStorage.setItem('compareList', JSON.stringify(newCompareList))
    
    toast({
      title: 'Removed from comparison',
      description: 'Product has been removed from comparison.'
    })
  }

  const addToCart = (product: any) => {
    if (!showAddToCart) return

    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      color: product.colors?.[0]?.value || '',
      size: product.sizes?.[0] || '',
      quantity: 1,
      image: product.images?.[0] || ''
    })

    toast({
      title: 'Added to cart',
      description: `${product.name} has been added to your cart.`
    })
  }

  const clearComparison = () => {
    if (isEditing) return

    setCompareList([])
    localStorage.removeItem('compareList')
    
    toast({
      title: 'Comparison cleared',
      description: 'All products have been removed from comparison.'
    })
  }

  // Empty state
  if (products.length === 0 && !loading) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <div className="container px-4 md:px-6 py-16">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <Scale className="h-16 w-16 text-muted-foreground" />
            <h1 className="text-2xl font-bold font-montserrat">{emptyComparisonMessage}</h1>
            <p className="text-muted-foreground">
              Add products to your comparison list to see them side by side.
            </p>
            <Button asChild>
              <Link href={emptyComparisonCta.url}>{emptyComparisonCta.text}</Link>
            </Button>
          </div>
        </div>
      </BaseBlock>
    )
  }

  // Loading state
  if (loading) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <div className="container px-4 md:px-6 py-8">
          <div className="text-center">Loading comparison...</div>
        </div>
      </BaseBlock>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div className="container px-4 md:px-6 py-6 md:py-10">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold font-montserrat mb-2">Product Comparison</h1>
            <p className="text-muted-foreground">
              Compare {products.length} {products.length === 1 ? 'product' : 'products'}
            </p>
          </div>
          {products.length > 0 && (
            <Button variant="outline" onClick={clearComparison}>
              Clear All
            </Button>
          )}
        </div>

        {/* Comparison Table */}
        <div className="overflow-x-auto">
          <div className="min-w-full">
            <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${Math.min(products.length, maxProducts)}, 1fr)` }}>
              {products.slice(0, maxProducts).map((product) => (
                <div key={product.id} className="border rounded-lg p-4 space-y-4">
                  {/* Remove Button */}
                  <div className="flex justify-end">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFromComparison(product.id)}
                      className="h-8 w-8 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Product Image */}
                  {showImages && (
                    <div className="aspect-square relative">
                      <Image
                        src={product.images?.[0] || '/placeholder.jpg'}
                        alt={product.name}
                        fill
                        className="object-cover rounded-md"
                      />
                      {product.isNew && (
                        <Badge className="absolute top-2 left-2">New</Badge>
                      )}
                      {product.isSale && (
                        <Badge variant="destructive" className="absolute top-2 right-2">Sale</Badge>
                      )}
                    </div>
                  )}

                  {/* Product Name */}
                  <h3 className="font-semibold text-lg">{product.name}</h3>

                  {/* Price */}
                  {showPricing && (
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-bold">
                          {formatPrice(product.price)}
                        </span>
                        {product.compareAtPrice && (
                          <span className="text-sm text-muted-foreground line-through">
                            {formatPrice(product.compareAtPrice)}
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Features */}
                  <div className="space-y-3">
                    {showFeatures.includes('colors') && product.colors && (
                      <div>
                        <p className="text-sm font-medium mb-1">Colors:</p>
                        <div className="flex gap-1">
                          {product.colors.slice(0, 3).map((color: any) => (
                            <div
                              key={color.name}
                              className="w-6 h-6 rounded-full border border-gray-300"
                              style={{ backgroundColor: color.value }}
                              title={color.name}
                            />
                          ))}
                          {product.colors.length > 3 && (
                            <span className="text-xs text-muted-foreground">
                              +{product.colors.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {showFeatures.includes('sizes') && product.sizes && (
                      <div>
                        <p className="text-sm font-medium mb-1">Sizes:</p>
                        <p className="text-sm text-muted-foreground">
                          {product.sizes.join(', ')}
                        </p>
                      </div>
                    )}

                    {showFeatures.includes('description') && (
                      <div>
                        <p className="text-sm font-medium mb-1">Description:</p>
                        <p className="text-sm text-muted-foreground line-clamp-3">
                          {product.description}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Add to Cart */}
                  {showAddToCart && (
                    <Button 
                      className="w-full" 
                      onClick={() => addToCart(product)}
                    >
                      <ShoppingBag className="mr-2 h-4 w-4" />
                      Add to Cart
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </BaseBlock>
  )
}
