'use client'

import React from 'react'
import { PageBlock, AccountDashboardBlockConfig } from '../../types'
import { BaseBlock } from '../base-block'
import { useOrders } from '@/lib/ecommerce/hooks/use-dashboard'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { User, Package, Heart, MapPin, CreditCard, Clock } from 'lucide-react'
import Link from 'next/link'
import { formatPrice } from '@/lib/utils'

interface AccountDashboardBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function AccountDashboardBlock({ block, isEditing = false }: AccountDashboardBlockProps) {
  const config = block.configuration as AccountDashboardBlockConfig
  // For now, we'll use a mock user - this should be replaced with real auth
  const user = isEditing ? { firstName: 'John', email: '<EMAIL>' } : null
  const { orders, loading: ordersLoading } = useOrders({
    limit: config.recentOrdersLimit || 5,
    autoFetch: !isEditing && !!user
  })

  const {
    showProfile = true,
    showOrders = true,
    showWishlist = true,
    showAddresses = true,
    showPaymentMethods = true,
    showRecentActivity = true,
    recentOrdersLimit = 5,
    recentActivityLimit = 10
  } = config

  // Redirect to login if not authenticated (except in editing mode)
  if (!isEditing && !user) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <div className="container px-4 md:px-6 py-16">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <User className="h-16 w-16 text-muted-foreground" />
            <h1 className="text-2xl font-bold font-montserrat">Please log in</h1>
            <p className="text-muted-foreground">You need to be logged in to view your account dashboard.</p>
            <Button asChild>
              <Link href="/account">Log In</Link>
            </Button>
          </div>
        </div>
      </BaseBlock>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div className="container px-4 md:px-6 py-6 md:py-10">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl md:text-3xl font-bold font-montserrat mb-2">
            Welcome back{user?.firstName ? `, ${user.firstName}` : ''}!
          </h1>
          <p className="text-muted-foreground">Manage your account and view your order history.</p>
        </div>

        {/* Dashboard Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Profile Card */}
          {showProfile && (
            <Card>
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Profile</CardTitle>
                <User className="h-4 w-4 ml-auto text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    {user?.email || '<EMAIL>'}
                  </p>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/account/profile">Edit Profile</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recent Orders */}
          {showOrders && (
            <Card className="md:col-span-2">
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Recent Orders</CardTitle>
                <Package className="h-4 w-4 ml-auto text-muted-foreground" />
              </CardHeader>
              <CardContent>
                {ordersLoading ? (
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                    <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse" />
                  </div>
                ) : orders.length > 0 ? (
                  <div className="space-y-3">
                    {orders.slice(0, recentOrdersLimit).map((order) => (
                      <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="space-y-1">
                          <p className="text-sm font-medium">Order #{order.orderNumber}</p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(order.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="text-right space-y-1">
                          <p className="text-sm font-medium">
                            {formatPrice(order.total?.amount || 0)}
                          </p>
                          <Badge variant={order.status === 'completed' ? 'default' : 'secondary'}>
                            {order.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                    <Button variant="outline" size="sm" asChild className="w-full">
                      <Link href="/account/orders">View All Orders</Link>
                    </Button>
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-sm text-muted-foreground">No orders yet</p>
                    <Button variant="outline" size="sm" asChild className="mt-2">
                      <Link href="/products">Start Shopping</Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Wishlist */}
          {showWishlist && (
            <Card>
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Wishlist</CardTitle>
                <Heart className="h-4 w-4 ml-auto text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="text-2xl font-bold">0</p>
                  <p className="text-xs text-muted-foreground">Saved items</p>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/wishlist">View Wishlist</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Addresses */}
          {showAddresses && (
            <Card>
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Addresses</CardTitle>
                <MapPin className="h-4 w-4 ml-auto text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Manage your addresses</p>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/account/addresses">Manage Addresses</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Payment Methods */}
          {showPaymentMethods && (
            <Card>
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Payment Methods</CardTitle>
                <CreditCard className="h-4 w-4 ml-auto text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Manage payment methods</p>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/account/payment-methods">Manage Cards</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recent Activity */}
          {showRecentActivity && (
            <Card className="md:col-span-2 lg:col-span-3">
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
                <Clock className="h-4 w-4 ml-auto text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="text-center py-4">
                    <p className="text-sm text-muted-foreground">No recent activity</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </BaseBlock>
  )
}
