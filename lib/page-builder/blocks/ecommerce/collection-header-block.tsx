'use client'

import React from 'react'
import { <PERSON><PERSON>lock, CollectionHeaderBlockConfig } from '../../types'
import { BaseBlock } from '../base-block'
import { Button } from '@/components/ui/button'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import Image from 'next/image'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { useProducts } from '@/lib/ecommerce/hooks/use-products'

interface CollectionHeaderBlockProps {
  block: PageBlock
  isEditing?: boolean
  productCount?: number
  collectionSlug?: string
}

export function CollectionHeaderBlock({
  block,
  isEditing = false,
  productCount = 0,
  collectionSlug
}: CollectionHeaderBlockProps) {
  const config = block.configuration as CollectionHeaderBlockConfig

  // Get real product count for the collection
  const { pagination } = useProducts({
    initialParams: collectionSlug ? { category: collectionSlug } : {},
    autoFetch: !isEditing && !!collectionSlug
  })

  // Use real product count if available, otherwise fall back to prop
  const realProductCount = pagination?.total || productCount

  const {
    title,
    description,
    backgroundImage,
    backgroundVideo,
    overlay,
    showProductCount,
    showBreadcrumbs,
    ctaButton,
    height,
    customHeight,
    alignment
  } = config

  const getHeightClass = () => {
    switch (height) {
      case 'small':
        return 'h-48 md:h-64'
      case 'medium':
        return 'h-64 md:h-80'
      case 'large':
        return 'h-80 md:h-96'
      case 'custom':
        return ''
      case 'auto':
      default:
        return 'py-16 md:py-24'
    }
  }

  const getAlignmentClass = () => {
    switch (alignment) {
      case 'left':
        return 'text-left items-start'
      case 'right':
        return 'text-right items-end'
      case 'center':
      default:
        return 'text-center items-center'
    }
  }

  const getCustomStyle = () => {
    if (height === 'custom' && customHeight) {
      return { height: customHeight }
    }
    return {}
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <section 
        className={cn(
          'relative overflow-hidden',
          getHeightClass()
        )}
        style={getCustomStyle()}
      >
        {/* Background Media */}
        {backgroundVideo ? (
          <video
            autoPlay
            muted
            loop
            playsInline
            className="absolute inset-0 w-full h-full object-cover"
          >
            <source src={backgroundVideo} type="video/mp4" />
          </video>
        ) : backgroundImage ? (
          <Image
            src={backgroundImage}
            alt={title}
            fill
            className="object-cover"
            priority
          />
        ) : (
          <div className="absolute inset-0 bg-gradient-to-r from-gray-100 to-gray-200" />
        )}

        {/* Overlay */}
        {overlay?.enabled && (
          <div
            className="absolute inset-0"
            style={{
              backgroundColor: overlay.color,
              opacity: overlay.opacity
            }}
          />
        )}

        {/* Content */}
        <div className="relative z-10 h-full">
          <div className="container px-4 md:px-6 h-full">
            {/* Breadcrumbs */}
            {showBreadcrumbs && (
              <div className="pt-6 mb-8">
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbLink href="/" className="text-white/80 hover:text-white">
                        Home
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator className="text-white/60" />
                    <BreadcrumbItem>
                      <BreadcrumbLink href="/collections" className="text-white/80 hover:text-white">
                        Collections
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator className="text-white/60" />
                    <BreadcrumbItem>
                      <BreadcrumbPage className="text-white">
                        {title}
                      </BreadcrumbPage>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            )}

            {/* Main Content */}
            <div className={cn(
              'flex flex-col justify-center h-full space-y-6',
              getAlignmentClass(),
              showBreadcrumbs ? 'pt-0' : 'pt-6'
            )}>
              <div className="space-y-4 max-w-3xl">
                {/* Title */}
                <h1 className="text-4xl md:text-6xl font-bold text-white tracking-tight">
                  {title}
                </h1>

                {/* Description */}
                {description && (
                  <p className="text-lg md:text-xl text-white/90 leading-relaxed">
                    {description}
                  </p>
                )}

                {/* Product Count */}
                {showProductCount && (
                  <p className="text-white/80 font-medium">
                    {realProductCount} {realProductCount === 1 ? 'product' : 'products'}
                  </p>
                )}
              </div>

              {/* CTA Button */}
              {ctaButton && (
                <div className="pt-4">
                  <Button
                    asChild
                    size="lg"
                    variant={ctaButton.style === 'primary' ? 'default' : 
                            ctaButton.style === 'secondary' ? 'secondary' : 'outline'}
                    className={cn(
                      ctaButton.style === 'outline' && 'border-white text-white hover:bg-white hover:text-black'
                    )}
                  >
                    <Link href={ctaButton.url}>
                      {ctaButton.text}
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent" />
      </section>
    </BaseBlock>
  )
}
