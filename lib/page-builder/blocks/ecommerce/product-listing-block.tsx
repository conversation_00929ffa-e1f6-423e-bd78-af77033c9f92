'use client'

import React, { Suspense } from 'react'
import { PageBlock, ProductListingBlockConfig } from '../../types'
import { BaseBlock } from '../base-block'
import { ProductGrid } from '@/components/storefront/products/product-grid'
import { ProductFilters } from '@/components/storefront/products/product-filters'
import { ProductSort } from '@/components/storefront/products/product-sort'
import { MobileFilters } from '@/components/mobile-filters'
import { Skeleton } from '@/components/ui/skeleton'

interface ProductListingBlockProps {
  block: PageBlock
  isEditing?: boolean
  searchParams?: { [key: string]: string | string[] | undefined }
}

// Exact copy of ProductGridSkeleton from the hardcoded page
function ProductGridSkeleton() {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
      {Array.from({ length: 8 }).map((_, i) => (
        <div key={i} className="space-y-3">
          <Skeleton className="aspect-[3/4] w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      ))}
    </div>
  )
}

export function ProductListingBlock({
  block,
  isEditing = false,
  searchParams = {}
}: ProductListingBlockProps) {
  const config = block.configuration as ProductListingBlockConfig

  // Extract search params exactly like the hardcoded page
  // Support both direct searchParams and nested in configuration
  const actualSearchParams = config.searchParams || searchParams
  const { sort, category, color, size } = actualSearchParams

  const {
    title = "All Products",
    showFilters = true,
    showSort = true,
    showMobileFilters = true,
    filtersPosition = 'left'
  } = config

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      {/* Exact copy of the hardcoded products page structure */}
      <div className="container px-4 md:px-6 py-6 md:py-10">
        <div className="flex flex-col md:flex-row gap-8">
          {/* Filters Sidebar - exactly like hardcoded */}
          {showFilters && filtersPosition === 'left' && (
            <div className="w-full md:w-1/4 lg:w-1/5 hidden md:block">
              <ProductFilters
                selectedCategory={category as string}
                selectedColor={color as string}
                selectedSize={size as string}
              />
            </div>
          )}

          <div className="w-full md:w-3/4 lg:w-4/5">
            {/* Mobile Header with Filter Button - exactly like hardcoded */}
            <div className="flex justify-between items-center mb-6 pb-4 border-b border-gray-100">
              <h1 className="text-2xl font-normal">{title}</h1>
              <div className="flex items-center gap-2">
                {/* Mobile Filter Button - exactly like hardcoded */}
                {showMobileFilters && showFilters && (
                  <div className="md:hidden">
                    <MobileFilters
                      selectedCategory={category as string}
                      selectedColor={color as string}
                      selectedSize={size as string}
                    />
                  </div>
                )}
                {showSort && <ProductSort selectedSort={sort as string} />}
              </div>
            </div>

            {/* Product Grid - exactly like hardcoded */}
            <Suspense fallback={<ProductGridSkeleton />}>
              <ProductGrid
                sort={sort as string}
                category={category as string}
                color={color as string}
                size={size as string}
              />
            </Suspense>
          </div>

          {/* Filters Sidebar - right position */}
          {showFilters && filtersPosition === 'right' && (
            <div className="w-full md:w-1/4 lg:w-1/5 hidden md:block">
              <ProductFilters
                selectedCategory={category as string}
                selectedColor={color as string}
                selectedSize={size as string}
              />
            </div>
          )}
        </div>
      </div>
    </BaseBlock>
  )
}
