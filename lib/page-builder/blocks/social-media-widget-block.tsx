'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Plus, Trash2, ExternalLink } from 'lucide-react'
import { cn } from '@/lib/utils'

interface SocialMediaWidgetBlockConfig {
  title?: string
  socialLinks: SocialLinkConfig[]
  layout: 'horizontal' | 'vertical' | 'grid'
  style: 'icons' | 'buttons' | 'cards'
  size: 'sm' | 'md' | 'lg'
  showLabels: boolean
  showFollowerCount: boolean
  openInNewTab: boolean
  alignment: 'left' | 'center' | 'right'
  spacing: 'none' | 'sm' | 'md' | 'lg'
  backgroundColor: string
  borderRadius: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
  hoverEffect: 'none' | 'scale' | 'glow' | 'lift'
}

interface SocialLinkConfig {
  id: string
  platform: 'facebook' | 'twitter' | 'instagram' | 'linkedin' | 'youtube' | 'tiktok' | 'whatsapp' | 'email' | 'custom'
  url: string
  label?: string
  followerCount?: number
  customIcon?: string
  color?: string
}

interface SocialMediaWidgetBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function SocialMediaWidgetBlock({ block, isEditing = false }: SocialMediaWidgetBlockProps) {
  const config = block.configuration as SocialMediaWidgetBlockConfig
  
  const {
    title,
    socialLinks,
    layout,
    style,
    size,
    showLabels,
    showFollowerCount,
    openInNewTab,
    alignment,
    spacing,
    backgroundColor,
    borderRadius,
    padding,
    hoverEffect,
  } = config

  const getSocialIcon = (platform: string) => {
    const icons = {
      facebook: '📘',
      twitter: '🐦',
      instagram: '📷',
      linkedin: '💼',
      youtube: '📺',
      tiktok: '🎵',
      whatsapp: '💬',
      email: '📧',
      custom: '🔗'
    }
    return icons[platform as keyof typeof icons] || '🔗'
  }

  const getSocialColor = (platform: string) => {
    const colors = {
      facebook: '#1877F2',
      twitter: '#1DA1F2',
      instagram: '#E4405F',
      linkedin: '#0A66C2',
      youtube: '#FF0000',
      tiktok: '#000000',
      whatsapp: '#25D366',
      email: '#6B7280',
      custom: '#6B7280'
    }
    return colors[platform as keyof typeof colors] || '#6B7280'
  }

  const getSpacingClass = () => {
    const spacingClasses = {
      none: 'gap-0',
      sm: 'gap-1',
      md: 'gap-2',
      lg: 'gap-4'
    }
    return spacingClasses[spacing]
  }

  const getSizeClasses = () => {
    const sizeClasses = {
      sm: 'w-8 h-8 text-sm',
      md: 'w-10 h-10 text-base',
      lg: 'w-12 h-12 text-lg'
    }
    return sizeClasses[size]
  }

  const getHoverEffectClass = () => {
    const hoverClasses = {
      none: '',
      scale: 'hover:scale-110',
      glow: 'hover:shadow-lg',
      lift: 'hover:-translate-y-1 hover:shadow-md'
    }
    return hoverClasses[hoverEffect]
  }

  const getLayoutClasses = () => {
    switch (layout) {
      case 'vertical':
        return 'flex flex-col'
      case 'grid':
        return `grid grid-cols-2 md:grid-cols-3 lg:grid-cols-${Math.min(socialLinks.length, 6)}`
      default:
        return 'flex flex-row flex-wrap'
    }
  }

  const getAlignmentClass = () => {
    const alignmentClasses = {
      left: 'justify-start',
      center: 'justify-center',
      right: 'justify-end'
    }
    return alignmentClasses[alignment]
  }

  const formatFollowerCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`
    }
    return count.toString()
  }

  const handleSocialClick = (link: SocialLinkConfig) => {
    if (isEditing) return
    
    if (link.url) {
      if (openInNewTab) {
        window.open(link.url, '_blank', 'noopener,noreferrer')
      } else {
        window.location.href = link.url
      }
    }
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
  }

  // Demo social links for editing mode
  const demoSocialLinks = [
    {
      id: 'demo-1',
      platform: 'facebook' as const,
      url: 'https://facebook.com',
      label: 'Facebook',
      followerCount: 12500
    },
    {
      id: 'demo-2',
      platform: 'instagram' as const,
      url: 'https://instagram.com',
      label: 'Instagram',
      followerCount: 8300
    },
    {
      id: 'demo-3',
      platform: 'twitter' as const,
      url: 'https://twitter.com',
      label: 'Twitter',
      followerCount: 5600
    }
  ]

  const linksToRender = isEditing ? demoSocialLinks : socialLinks

  const renderSocialLink = (link: SocialLinkConfig) => {
    const linkColor = link.color || getSocialColor(link.platform)
    const icon = link.customIcon || getSocialIcon(link.platform)

    if (style === 'cards') {
      return (
        <div
          key={link.id}
          className={cn(
            'p-3 rounded-lg border bg-white cursor-pointer transition-all duration-200',
            getHoverEffectClass(),
            isEditing && 'pointer-events-none'
          )}
          onClick={() => handleSocialClick(link)}
          style={{ borderColor: linkColor }}
        >
          <div className="flex items-center space-x-2">
            <span className="text-xl">{icon}</span>
            {showLabels && (
              <div>
                <p className="text-sm font-medium">{link.label || link.platform}</p>
                {showFollowerCount && link.followerCount && (
                  <p className="text-xs text-gray-500">
                    {formatFollowerCount(link.followerCount)} followers
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      )
    }

    if (style === 'buttons') {
      return (
        <Button
          key={link.id}
          variant="outline"
          size="sm"
          className={cn(
            'transition-all duration-200',
            getHoverEffectClass(),
            isEditing && 'pointer-events-none'
          )}
          onClick={() => handleSocialClick(link)}
          style={{ borderColor: linkColor, color: linkColor }}
        >
          <span className="mr-2">{icon}</span>
          {showLabels && (link.label || link.platform)}
          {openInNewTab && <ExternalLink className="ml-2 h-3 w-3" />}
        </Button>
      )
    }

    // Default: icons style
    return (
      <div
        key={link.id}
        className={cn(
          'flex flex-col items-center cursor-pointer transition-all duration-200',
          getSizeClasses(),
          getHoverEffectClass(),
          isEditing && 'pointer-events-none'
        )}
        onClick={() => handleSocialClick(link)}
        style={{ color: linkColor }}
        title={link.label || link.platform}
      >
        <div className={cn(
          'flex items-center justify-center rounded-full',
          getSizeClasses()
        )}>
          <span>{icon}</span>
        </div>
        {showLabels && (
          <span className="text-xs mt-1 text-center">
            {link.label || link.platform}
          </span>
        )}
        {showFollowerCount && link.followerCount && (
          <span className="text-xs text-gray-500">
            {formatFollowerCount(link.followerCount)}
          </span>
        )}
      </div>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div style={containerStyles}>
        {title && (
          <h3 className={cn('font-semibold mb-4', `text-${alignment}`)}>
            {title}
          </h3>
        )}
        
        <div className={cn(
          getLayoutClasses(),
          getSpacingClass(),
          getAlignmentClass()
        )}>
          {linksToRender.map(renderSocialLink)}
        </div>
        
        {isEditing && (
          <div className="mt-4 p-2 bg-purple-50 border border-purple-200 rounded text-xs text-purple-700">
            <strong>Social Media Widget:</strong> {linksToRender.length} links • {style} style • {layout} layout
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Social Media Widget Block Configuration Component
interface SocialMediaWidgetBlockConfigProps {
  config: SocialMediaWidgetBlockConfig
  onChange: (config: SocialMediaWidgetBlockConfig) => void
}

export function SocialMediaWidgetBlockConfig({ config, onChange }: SocialMediaWidgetBlockConfigProps) {
  const updateConfig = (updates: Partial<SocialMediaWidgetBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const addSocialLink = () => {
    const newLink: SocialLinkConfig = {
      id: `social-${Date.now()}`,
      platform: 'facebook',
      url: '',
      label: 'Facebook'
    }
    
    updateConfig({
      socialLinks: [...config.socialLinks, newLink]
    })
  }

  const updateSocialLink = (index: number, updates: Partial<SocialLinkConfig>) => {
    const updatedLinks = [...config.socialLinks]
    updatedLinks[index] = { ...updatedLinks[index], ...updates }
    updateConfig({ socialLinks: updatedLinks })
  }

  const removeSocialLink = (index: number) => {
    const updatedLinks = config.socialLinks.filter((_, i) => i !== index)
    updateConfig({ socialLinks: updatedLinks })
  }

  return (
    <div className="space-y-6">
      {/* Widget Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Widget Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Title</Label>
            <Input
              value={config.title || ''}
              onChange={(e) => updateConfig({ title: e.target.value })}
              placeholder="Follow Us"
              className="mt-1"
            />
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Layout</Label>
              <Select
                value={config.layout}
                onValueChange={(value) => updateConfig({ layout: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="horizontal">Horizontal</SelectItem>
                  <SelectItem value="vertical">Vertical</SelectItem>
                  <SelectItem value="grid">Grid</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Style</Label>
              <Select
                value={config.style}
                onValueChange={(value) => updateConfig({ style: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="icons">Icons</SelectItem>
                  <SelectItem value="buttons">Buttons</SelectItem>
                  <SelectItem value="cards">Cards</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Labels</Label>
              <Switch
                checked={config.showLabels}
                onCheckedChange={(checked) => updateConfig({ showLabels: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Follower Count</Label>
              <Switch
                checked={config.showFollowerCount}
                onCheckedChange={(checked) => updateConfig({ showFollowerCount: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Open in New Tab</Label>
              <Switch
                checked={config.openInNewTab}
                onCheckedChange={(checked) => updateConfig({ openInNewTab: checked })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Social Links */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm">Social Links</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={addSocialLink}
            className="h-8 px-2"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Link
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.socialLinks.map((link, index) => (
            <div key={link.id} className="border rounded-lg p-3 space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Link {index + 1}</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeSocialLink(index)}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Platform</Label>
                  <Select
                    value={link.platform}
                    onValueChange={(value) => updateSocialLink(index, { platform: value as any })}
                  >
                    <SelectTrigger className="w-full mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="facebook">Facebook</SelectItem>
                      <SelectItem value="twitter">Twitter</SelectItem>
                      <SelectItem value="instagram">Instagram</SelectItem>
                      <SelectItem value="linkedin">LinkedIn</SelectItem>
                      <SelectItem value="youtube">YouTube</SelectItem>
                      <SelectItem value="tiktok">TikTok</SelectItem>
                      <SelectItem value="whatsapp">WhatsApp</SelectItem>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="custom">Custom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-xs">Label</Label>
                  <Input
                    value={link.label || ''}
                    onChange={(e) => updateSocialLink(index, { label: e.target.value })}
                    className="mt-1"
                    placeholder="Platform name"
                  />
                </div>
              </div>
              
              <div>
                <Label className="text-xs">URL</Label>
                <Input
                  value={link.url}
                  onChange={(e) => updateSocialLink(index, { url: e.target.value })}
                  className="mt-1"
                  placeholder="https://..."
                />
              </div>
            </div>
          ))}
          
          {config.socialLinks.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              <p className="text-sm">No social links yet.</p>
              <p className="text-xs">Click "Add Link" to get started.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
