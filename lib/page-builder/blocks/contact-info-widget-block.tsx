'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock, 
  Globe, 
  MessageCircle,
  Plus,
  Trash2,
  ExternalLink
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ContactInfoWidgetBlockConfig {
  title?: string
  contactItems: ContactItemConfig[]
  layout: 'vertical' | 'horizontal' | 'grid' | 'compact'
  style: 'simple' | 'cards' | 'icons'
  showIcons: boolean
  clickableItems: boolean
  alignment: 'left' | 'center' | 'right'
  spacing: 'none' | 'sm' | 'md' | 'lg'
  backgroundColor: string
  borderRadius: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
}

interface ContactItemConfig {
  id: string
  type: 'phone' | 'email' | 'address' | 'hours' | 'website' | 'social' | 'custom'
  label: string
  value: string
  link?: string
  icon?: string
  color?: string
  description?: string
}

interface ContactInfoWidgetBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function ContactInfoWidgetBlock({ block, isEditing = false }: ContactInfoWidgetBlockProps) {
  const config = block.configuration as ContactInfoWidgetBlockConfig
  
  const {
    title,
    contactItems,
    layout,
    style,
    showIcons,
    clickableItems,
    alignment,
    spacing,
    backgroundColor,
    borderRadius,
    padding,
  } = config

  const getContactIcon = (type: string, customIcon?: string) => {
    if (customIcon) return customIcon

    const icons = {
      phone: <Phone className="h-4 w-4" />,
      email: <Mail className="h-4 w-4" />,
      address: <MapPin className="h-4 w-4" />,
      hours: <Clock className="h-4 w-4" />,
      website: <Globe className="h-4 w-4" />,
      social: <MessageCircle className="h-4 w-4" />,
      custom: <MessageCircle className="h-4 w-4" />
    }
    return icons[type as keyof typeof icons] || icons.custom
  }

  const getContactLink = (item: ContactItemConfig) => {
    if (item.link) return item.link

    switch (item.type) {
      case 'phone':
        return `tel:${item.value.replace(/\s/g, '')}`
      case 'email':
        return `mailto:${item.value}`
      case 'address':
        return `https://maps.google.com/?q=${encodeURIComponent(item.value)}`
      case 'website':
        return item.value.startsWith('http') ? item.value : `https://${item.value}`
      default:
        return item.link || '#'
    }
  }

  const getSpacingClass = () => {
    const spacingClasses = {
      none: 'gap-0',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6'
    }
    return spacingClasses[spacing]
  }

  const getLayoutClasses = () => {
    switch (layout) {
      case 'horizontal':
        return 'flex flex-row flex-wrap'
      case 'grid':
        return `grid grid-cols-1 md:grid-cols-2 lg:grid-cols-${Math.min(contactItems.length, 3)}`
      case 'compact':
        return 'flex flex-col space-y-1'
      default:
        return 'flex flex-col'
    }
  }

  const getAlignmentClass = () => {
    const alignmentClasses = {
      left: 'text-left items-start',
      center: 'text-center items-center',
      right: 'text-right items-end'
    }
    return alignmentClasses[alignment]
  }

  const handleContactClick = (item: ContactItemConfig) => {
    if (isEditing || !clickableItems) return
    
    const link = getContactLink(item)
    if (link && link !== '#') {
      if (item.type === 'website' || item.type === 'social') {
        window.open(link, '_blank', 'noopener,noreferrer')
      } else {
        window.location.href = link
      }
    }
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
  }

  // Demo contact items for editing mode
  const demoContactItems = [
    {
      id: 'demo-1',
      type: 'phone' as const,
      label: 'Phone',
      value: '+27 11 123 4567',
      description: 'Call us during business hours'
    },
    {
      id: 'demo-2',
      type: 'email' as const,
      label: 'Email',
      value: '<EMAIL>',
      description: 'We reply within 24 hours'
    },
    {
      id: 'demo-3',
      type: 'address' as const,
      label: 'Address',
      value: '123 Main Street, Cape Town, South Africa',
      description: 'Visit our store'
    },
    {
      id: 'demo-4',
      type: 'hours' as const,
      label: 'Hours',
      value: 'Mon-Fri: 9AM-6PM, Sat: 9AM-4PM',
      description: 'Store opening hours'
    }
  ]

  const itemsToRender = isEditing ? demoContactItems : contactItems

  const renderContactItem = (item: ContactItemConfig) => {
    const isClickable = clickableItems && !isEditing && getContactLink(item) !== '#'

    if (style === 'cards') {
      return (
        <Card
          key={item.id}
          className={cn(
            'transition-all duration-200',
            isClickable && 'cursor-pointer hover:shadow-md hover:scale-105'
          )}
          onClick={() => handleContactClick(item)}
        >
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              {showIcons && (
                <div className="flex-shrink-0 mt-1" style={{ color: item.color }}>
                  {getContactIcon(item.type, item.icon)}
                </div>
              )}
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-sm">{item.label}</h4>
                <p className="text-sm text-gray-600 break-words">{item.value}</p>
                {item.description && (
                  <p className="text-xs text-gray-500 mt-1">{item.description}</p>
                )}
              </div>
              {isClickable && (
                <ExternalLink className="h-3 w-3 text-gray-400 flex-shrink-0" />
              )}
            </div>
          </CardContent>
        </Card>
      )
    }

    if (style === 'icons') {
      return (
        <div
          key={item.id}
          className={cn(
            'flex items-center space-x-3 transition-all duration-200',
            isClickable && 'cursor-pointer hover:bg-gray-50 rounded-lg p-2 -m-2',
            getAlignmentClass()
          )}
          onClick={() => handleContactClick(item)}
        >
          {showIcons && (
            <div className="flex-shrink-0" style={{ color: item.color }}>
              {getContactIcon(item.type, item.icon)}
            </div>
          )}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <span className="font-medium text-sm">{item.label}:</span>
              <span className="text-sm text-gray-600 break-words">{item.value}</span>
              {isClickable && (
                <ExternalLink className="h-3 w-3 text-gray-400 flex-shrink-0" />
              )}
            </div>
            {item.description && (
              <p className="text-xs text-gray-500 mt-1">{item.description}</p>
            )}
          </div>
        </div>
      )
    }

    // Default: simple style
    return (
      <div
        key={item.id}
        className={cn(
          'transition-all duration-200',
          isClickable && 'cursor-pointer hover:text-blue-600',
          getAlignmentClass()
        )}
        onClick={() => handleContactClick(item)}
      >
        <div className="flex items-center space-x-2">
          {showIcons && (
            <span style={{ color: item.color }}>
              {getContactIcon(item.type, item.icon)}
            </span>
          )}
          <span className="font-medium text-sm">{item.label}:</span>
          <span className="text-sm break-words">{item.value}</span>
          {isClickable && (
            <ExternalLink className="h-3 w-3 text-gray-400" />
          )}
        </div>
        {item.description && (
          <p className="text-xs text-gray-500 mt-1 ml-6">{item.description}</p>
        )}
      </div>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div style={containerStyles}>
        {title && (
          <h3 className={cn('font-semibold mb-4', `text-${alignment}`)}>
            {title}
          </h3>
        )}
        
        <div className={cn(
          getLayoutClasses(),
          getSpacingClass(),
          getAlignmentClass()
        )}>
          {itemsToRender.map(renderContactItem)}
        </div>
        
        {isEditing && (
          <div className="mt-4 p-2 bg-green-50 border border-green-200 rounded text-xs text-green-700">
            <strong>Contact Info Widget:</strong> {itemsToRender.length} items • {style} style • {layout} layout
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Contact Info Widget Block Configuration Component
interface ContactInfoWidgetBlockConfigProps {
  config: ContactInfoWidgetBlockConfig
  onChange: (config: ContactInfoWidgetBlockConfig) => void
}

export function ContactInfoWidgetBlockConfig({ config, onChange }: ContactInfoWidgetBlockConfigProps) {
  const updateConfig = (updates: Partial<ContactInfoWidgetBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const addContactItem = () => {
    const newItem: ContactItemConfig = {
      id: `contact-${Date.now()}`,
      type: 'phone',
      label: 'Phone',
      value: ''
    }
    
    updateConfig({
      contactItems: [...config.contactItems, newItem]
    })
  }

  const updateContactItem = (index: number, updates: Partial<ContactItemConfig>) => {
    const updatedItems = [...config.contactItems]
    updatedItems[index] = { ...updatedItems[index], ...updates }
    updateConfig({ contactItems: updatedItems })
  }

  const removeContactItem = (index: number) => {
    const updatedItems = config.contactItems.filter((_, i) => i !== index)
    updateConfig({ contactItems: updatedItems })
  }

  return (
    <div className="space-y-6">
      {/* Widget Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Widget Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Title</Label>
            <Input
              value={config.title || ''}
              onChange={(e) => updateConfig({ title: e.target.value })}
              placeholder="Contact Information"
              className="mt-1"
            />
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Layout</Label>
              <Select
                value={config.layout}
                onValueChange={(value) => updateConfig({ layout: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="vertical">Vertical</SelectItem>
                  <SelectItem value="horizontal">Horizontal</SelectItem>
                  <SelectItem value="grid">Grid</SelectItem>
                  <SelectItem value="compact">Compact</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Style</Label>
              <Select
                value={config.style}
                onValueChange={(value) => updateConfig({ style: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="simple">Simple</SelectItem>
                  <SelectItem value="cards">Cards</SelectItem>
                  <SelectItem value="icons">Icons</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Icons</Label>
              <Switch
                checked={config.showIcons}
                onCheckedChange={(checked) => updateConfig({ showIcons: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Clickable Items</Label>
              <Switch
                checked={config.clickableItems}
                onCheckedChange={(checked) => updateConfig({ clickableItems: checked })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Items */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm">Contact Items</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={addContactItem}
            className="h-8 px-2"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Item
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.contactItems.map((item, index) => (
            <div key={item.id} className="border rounded-lg p-3 space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Item {index + 1}</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeContactItem(index)}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Type</Label>
                  <Select
                    value={item.type}
                    onValueChange={(value) => updateContactItem(index, { type: value as any })}
                  >
                    <SelectTrigger className="w-full mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="phone">Phone</SelectItem>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="address">Address</SelectItem>
                      <SelectItem value="hours">Hours</SelectItem>
                      <SelectItem value="website">Website</SelectItem>
                      <SelectItem value="social">Social</SelectItem>
                      <SelectItem value="custom">Custom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-xs">Label</Label>
                  <Input
                    value={item.label}
                    onChange={(e) => updateContactItem(index, { label: e.target.value })}
                    className="mt-1"
                    placeholder="Label"
                  />
                </div>
              </div>
              
              <div>
                <Label className="text-xs">Value</Label>
                <Input
                  value={item.value}
                  onChange={(e) => updateContactItem(index, { value: e.target.value })}
                  className="mt-1"
                  placeholder="Contact information"
                />
              </div>
            </div>
          ))}
          
          {config.contactItems.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              <p className="text-sm">No contact items yet.</p>
              <p className="text-xs">Click "Add Item" to get started.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
