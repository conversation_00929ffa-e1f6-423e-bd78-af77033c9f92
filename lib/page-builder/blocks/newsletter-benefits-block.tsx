'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { 
  Mail, 
  Gift, 
  Percent, 
  Star, 
  Bell, 
  Crown,
  Zap,
  Heart,
  ShoppingBag,
  Calendar,
  Users,
  Sparkles
} from 'lucide-react'

interface NewsletterBenefit {
  id: string
  title: string
  description: string
  icon: string
  iconColor?: string
  isHighlighted?: boolean
}

interface NewsletterBenefitsConfig {
  title: string
  subtitle?: string
  description?: string
  benefits: NewsletterBenefit[]
  layout: 'grid' | 'list' | 'carousel'
  columns: 2 | 3 | 4
  cardStyle: 'default' | 'minimal' | 'bordered' | 'gradient'
  iconStyle: 'circle' | 'square' | 'none'
  iconSize: 'small' | 'medium' | 'large'
  spacing: 'compact' | 'normal' | 'spacious'
  backgroundColor: string
  textColor: string
  accentColor: string
  showNumbers: boolean
}

interface NewsletterBenefitsBlockProps {
  block: PageBlock
  isEditing?: boolean
}

// Icon mapping for newsletter benefits
const benefitIcons = {
  mail: Mail,
  gift: Gift,
  percent: Percent,
  star: Star,
  bell: Bell,
  crown: Crown,
  zap: Zap,
  heart: Heart,
  'shopping-bag': ShoppingBag,
  calendar: Calendar,
  users: Users,
  sparkles: Sparkles
}

export function NewsletterBenefitsBlock({ block, isEditing = false }: NewsletterBenefitsBlockProps) {
  const config = block.configuration as NewsletterBenefitsConfig

  const {
    title,
    subtitle,
    description,
    benefits = [],
    layout = 'grid',
    columns = 3,
    cardStyle = 'default',
    iconStyle = 'circle',
    iconSize = 'medium',
    spacing = 'normal',
    backgroundColor = 'transparent',
    textColor = 'inherit',
    accentColor = '#012169',
    showNumbers = false
  } = config

  const getSpacingClasses = () => {
    switch (spacing) {
      case 'compact':
        return 'py-6 gap-4'
      case 'spacious':
        return 'py-16 gap-8'
      default:
        return 'py-12 gap-6'
    }
  }

  const getColumnClasses = () => {
    switch (columns) {
      case 2:
        return 'grid-cols-1 md:grid-cols-2'
      case 4:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
      default:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
    }
  }

  const getIconSizeClasses = () => {
    switch (iconSize) {
      case 'small':
        return 'h-5 w-5'
      case 'large':
        return 'h-10 w-10'
      default:
        return 'h-6 w-6'
    }
  }

  const getIconContainerClasses = () => {
    const baseClasses = 'flex items-center justify-center mb-4'
    const sizeClasses = iconSize === 'small' ? 'w-10 h-10' : iconSize === 'large' ? 'w-16 h-16' : 'w-12 h-12'
    
    switch (iconStyle) {
      case 'square':
        return `${baseClasses} ${sizeClasses} rounded-md`
      case 'none':
        return `${baseClasses} ${sizeClasses}`
      default:
        return `${baseClasses} ${sizeClasses} rounded-full`
    }
  }

  const getCardClasses = () => {
    switch (cardStyle) {
      case 'minimal':
        return 'border-0 shadow-none bg-transparent'
      case 'bordered':
        return 'border-2 shadow-none hover:border-opacity-60 transition-colors'
      case 'gradient':
        return 'bg-gradient-to-br from-white to-gray-50 shadow-md hover:shadow-lg transition-shadow'
      default:
        return 'shadow-sm hover:shadow-md transition-shadow'
    }
  }

  const renderBenefitIcon = (iconName: string, iconColor?: string) => {
    const IconComponent = benefitIcons[iconName as keyof typeof benefitIcons] || Gift
    return (
      <IconComponent 
        className={getIconSizeClasses()}
        style={{ color: iconColor || accentColor }}
      />
    )
  }

  const renderBenefitCard = (benefit: NewsletterBenefit, index: number) => (
    <Card 
      key={benefit.id} 
      className={cn(
        getCardClasses(),
        'h-full',
        benefit.isHighlighted ? 'ring-2 ring-opacity-20' : ''
      )}
      style={benefit.isHighlighted ? { ringColor: accentColor } : undefined}
    >
      <CardContent className="p-6 text-center h-full flex flex-col">
        {/* Number */}
        {showNumbers && (
          <div className="text-xs font-medium text-muted-foreground mb-2">
            {String(index + 1).padStart(2, '0')}
          </div>
        )}

        {/* Icon */}
        <div 
          className={getIconContainerClasses()}
          style={iconStyle !== 'none' ? { backgroundColor: `${accentColor}15` } : undefined}
        >
          {renderBenefitIcon(benefit.icon, benefit.iconColor)}
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col">
          <h3 className="font-semibold text-lg mb-3 leading-tight">
            {benefit.title}
          </h3>
          <p className="text-sm text-muted-foreground leading-relaxed">
            {benefit.description}
          </p>
        </div>

        {/* Highlight indicator */}
        {benefit.isHighlighted && (
          <div className="mt-4">
            <div 
              className="w-8 h-1 rounded-full mx-auto"
              style={{ backgroundColor: accentColor }}
            />
          </div>
        )}
      </CardContent>
    </Card>
  )

  const renderBenefitList = (benefit: NewsletterBenefit, index: number) => (
    <div key={benefit.id} className="flex items-start gap-4 p-4">
      {/* Number */}
      {showNumbers && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-sm font-medium">
          {index + 1}
        </div>
      )}

      {/* Icon */}
      <div 
        className={cn(
          'flex-shrink-0',
          getIconContainerClasses().replace('mb-4', '')
        )}
        style={iconStyle !== 'none' ? { backgroundColor: `${accentColor}15` } : undefined}
      >
        {renderBenefitIcon(benefit.icon, benefit.iconColor)}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <h3 className="font-semibold mb-2">{benefit.title}</h3>
        <p className="text-sm text-muted-foreground">{benefit.description}</p>
      </div>
    </div>
  )

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div 
        className="container px-4 md:px-6"
        style={{ 
          backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : undefined,
          color: textColor !== 'inherit' ? textColor : undefined
        }}
      >
        <div className={getSpacingClasses()}>
          {/* Header */}
          <div className="text-center mb-12">
            {title && (
              <h2 className="text-3xl font-bold font-montserrat mb-4">
                {title}
              </h2>
            )}
            {subtitle && (
              <h3 className="text-xl text-muted-foreground mb-4">
                {subtitle}
              </h3>
            )}
            {description && (
              <p className="text-lg text-muted-foreground font-light max-w-2xl mx-auto">
                {description}
              </p>
            )}
          </div>

          {/* Benefits */}
          {layout === 'list' ? (
            <div className="max-w-3xl mx-auto space-y-6">
              {benefits.map((benefit, index) => renderBenefitList(benefit, index))}
            </div>
          ) : (
            <div className={cn('grid gap-6', getColumnClasses())}>
              {benefits.map((benefit, index) => renderBenefitCard(benefit, index))}
            </div>
          )}
        </div>
      </div>
    </BaseBlock>
  )
}

// Default configuration for the newsletter benefits block
export const newsletterBenefitsBlockConfig = {
  title: 'Why Subscribe?',
  subtitle: 'Join thousands of parents who love our newsletter',
  description: 'Get exclusive access to deals, new arrivals, and parenting tips delivered straight to your inbox.',
  benefits: [
    {
      id: 'exclusive-deals',
      title: 'Exclusive Deals',
      description: 'Be the first to know about sales, special offers, and subscriber-only discounts.',
      icon: 'percent',
      isHighlighted: true
    },
    {
      id: 'early-access',
      title: 'Early Access',
      description: 'Shop new collections before they go live to the public and secure your favorites.',
      icon: 'crown'
    },
    {
      id: 'style-tips',
      title: 'Style Tips',
      description: 'Get expert advice on styling your little ones and seasonal fashion inspiration.',
      icon: 'sparkles'
    },
    {
      id: 'size-guides',
      title: 'Size Guides',
      description: 'Receive helpful sizing tips and growth charts to ensure the perfect fit.',
      icon: 'star'
    },
    {
      id: 'birthday-treats',
      title: 'Birthday Treats',
      description: 'Special birthday discounts and surprises for your little ones throughout the year.',
      icon: 'gift'
    },
    {
      id: 'community',
      title: 'Parent Community',
      description: 'Connect with other parents and share experiences in our exclusive subscriber community.',
      icon: 'users'
    }
  ],
  layout: 'grid',
  columns: 3,
  cardStyle: 'default',
  iconStyle: 'circle',
  iconSize: 'medium',
  spacing: 'normal',
  backgroundColor: 'transparent',
  textColor: 'inherit',
  accentColor: '#012169',
  showNumbers: false
}

// Configuration schema for the Page Builder
export const newsletterBenefitsBlockSchema = {
  type: 'object',
  properties: {
    title: {
      type: 'string',
      title: 'Title',
      default: 'Why Subscribe?'
    },
    subtitle: {
      type: 'string',
      title: 'Subtitle (Optional)',
      default: 'Join thousands of parents who love our newsletter'
    },
    description: {
      type: 'string',
      title: 'Description (Optional)',
      format: 'textarea',
      default: newsletterBenefitsBlockConfig.description
    },
    benefits: {
      type: 'array',
      title: 'Benefits',
      items: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            title: 'Benefit ID',
            description: 'Unique identifier for the benefit'
          },
          title: {
            type: 'string',
            title: 'Benefit Title'
          },
          description: {
            type: 'string',
            title: 'Description'
          },
          icon: {
            type: 'string',
            title: 'Icon',
            enum: Object.keys(benefitIcons),
            default: 'gift'
          },
          iconColor: {
            type: 'string',
            title: 'Custom Icon Color (Optional)',
            format: 'color'
          },
          isHighlighted: {
            type: 'boolean',
            title: 'Highlight This Benefit',
            default: false
          }
        },
        required: ['id', 'title', 'description', 'icon']
      },
      default: newsletterBenefitsBlockConfig.benefits
    },
    layout: {
      type: 'string',
      title: 'Layout',
      enum: ['grid', 'list', 'carousel'],
      enumNames: ['Grid', 'List', 'Carousel'],
      default: 'grid'
    },
    columns: {
      type: 'number',
      title: 'Columns (for grid layout)',
      enum: [2, 3, 4],
      default: 3
    },
    cardStyle: {
      type: 'string',
      title: 'Card Style',
      enum: ['default', 'minimal', 'bordered', 'gradient'],
      enumNames: ['Default', 'Minimal', 'Bordered', 'Gradient'],
      default: 'default'
    },
    iconStyle: {
      type: 'string',
      title: 'Icon Style',
      enum: ['circle', 'square', 'none'],
      enumNames: ['Circle Background', 'Square Background', 'No Background'],
      default: 'circle'
    },
    iconSize: {
      type: 'string',
      title: 'Icon Size',
      enum: ['small', 'medium', 'large'],
      enumNames: ['Small', 'Medium', 'Large'],
      default: 'medium'
    },
    showNumbers: {
      type: 'boolean',
      title: 'Show Numbers',
      description: 'Display numbered sequence for benefits',
      default: false
    },
    spacing: {
      type: 'string',
      title: 'Spacing',
      enum: ['compact', 'normal', 'spacious'],
      enumNames: ['Compact', 'Normal', 'Spacious'],
      default: 'normal'
    },
    accentColor: {
      type: 'string',
      title: 'Accent Color',
      format: 'color',
      default: '#012169'
    },
    backgroundColor: {
      type: 'string',
      title: 'Background Color',
      format: 'color',
      default: 'transparent'
    },
    textColor: {
      type: 'string',
      title: 'Text Color',
      format: 'color',
      default: 'inherit'
    }
  },
  required: ['title', 'benefits']
}
