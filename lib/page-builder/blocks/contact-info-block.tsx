'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { cn } from '@/lib/utils'
import { Mail, Phone, MapPin, Clock, Globe, MessageCircle } from 'lucide-react'

interface ContactItem {
  type: 'email' | 'phone' | 'address' | 'hours' | 'website' | 'social'
  label: string
  value: string
  link?: string
  icon?: string
}

interface ContactInfoConfig {
  title: string
  subtitle?: string
  description?: string
  contacts: ContactItem[]
  layout: 'vertical' | 'horizontal' | 'grid'
  cardStyle: boolean
  spacing: 'compact' | 'normal' | 'spacious'
  backgroundColor: string
  textColor: string
  iconColor: string
}

interface ContactInfoBlockProps {
  block: PageBlock
  isEditing?: boolean
}

// Icon mapping for contact types
const contactIcons = {
  email: Mail,
  phone: Phone,
  address: MapPin,
  hours: Clock,
  website: Globe,
  social: MessageCircle
}

export function ContactInfoBlock({ block, isEditing = false }: ContactInfoBlockProps) {
  const config = block.configuration as ContactInfoConfig

  const {
    title,
    subtitle,
    description,
    contacts = [],
    layout = 'vertical',
    cardStyle = true,
    spacing = 'normal',
    backgroundColor = 'transparent',
    textColor = 'inherit',
    iconColor = '#012169'
  } = config

  const getSpacingClasses = () => {
    switch (spacing) {
      case 'compact':
        return 'py-6 gap-4'
      case 'spacious':
        return 'py-12 gap-8'
      default:
        return 'py-8 gap-6'
    }
  }

  const getLayoutClasses = () => {
    switch (layout) {
      case 'horizontal':
        return 'flex flex-wrap gap-6'
      case 'grid':
        return 'grid grid-cols-1 md:grid-cols-2 gap-6'
      default:
        return 'space-y-4'
    }
  }

  const renderContactIcon = (type: string) => {
    const IconComponent = contactIcons[type as keyof typeof contactIcons] || Mail
    return (
      <IconComponent 
        className="h-5 w-5" 
        style={{ color: iconColor }}
      />
    )
  }

  const renderContactItem = (contact: ContactItem, index: number) => {
    const content = (
      <div className="flex items-center space-x-3">
        {renderContactIcon(contact.type)}
        <div>
          <p className="font-medium">{contact.label}</p>
          <p className="text-sm text-muted-foreground">{contact.value}</p>
        </div>
      </div>
    )

    if (contact.link) {
      const isEmail = contact.type === 'email'
      const isPhone = contact.type === 'phone'
      const href = isEmail ? `mailto:${contact.link}` : 
                   isPhone ? `tel:${contact.link}` : 
                   contact.link

      return (
        <a
          key={index}
          href={href}
          className="block hover:opacity-80 transition-opacity"
          target={contact.type === 'website' ? '_blank' : undefined}
          rel={contact.type === 'website' ? 'noopener noreferrer' : undefined}
        >
          {content}
        </a>
      )
    }

    return (
      <div key={index}>
        {content}
      </div>
    )
  }

  const contactsContent = (
    <div className={getLayoutClasses()}>
      {contacts.map((contact, index) => renderContactItem(contact, index))}
    </div>
  )

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div 
        className="container px-4 md:px-6"
        style={{ 
          backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : undefined,
          color: textColor !== 'inherit' ? textColor : undefined
        }}
      >
        <div className={getSpacingClasses()}>
          {/* Header */}
          {(title || subtitle || description) && (
            <div className="mb-6">
              {title && (
                <h2 className="text-lg font-montserrat font-semibold mb-2">
                  {title}
                </h2>
              )}
              {subtitle && (
                <h3 className="text-base text-muted-foreground mb-2">
                  {subtitle}
                </h3>
              )}
              {description && (
                <p className="text-sm text-muted-foreground">
                  {description}
                </p>
              )}
            </div>
          )}

          {/* Contact Items */}
          {cardStyle ? (
            <div className="bg-white border rounded-lg p-6 shadow-sm">
              {contactsContent}
            </div>
          ) : (
            contactsContent
          )}
        </div>
      </div>
    </BaseBlock>
  )
}

// Default configuration for the contact info block
export const contactInfoBlockConfig = {
  title: 'Get in Touch',
  subtitle: '',
  description: 'We\'d love to hear from you. Send us a message and we\'ll respond as soon as possible.',
  contacts: [
    {
      type: 'email',
      label: 'Email',
      value: '<EMAIL>',
      link: '<EMAIL>'
    },
    {
      type: 'phone',
      label: 'Phone',
      value: '+27 11 123 4567',
      link: '+***********'
    },
    {
      type: 'address',
      label: 'Address',
      value: '123 Sandton Drive, Sandton, Johannesburg 2196, South Africa',
      link: ''
    },
    {
      type: 'hours',
      label: 'Business Hours',
      value: 'Monday - Friday: 9:00 AM - 5:00 PM',
      link: ''
    }
  ],
  layout: 'vertical',
  cardStyle: true,
  spacing: 'normal',
  backgroundColor: 'transparent',
  textColor: 'inherit',
  iconColor: '#012169'
}

// Configuration schema for the Page Builder
export const contactInfoBlockSchema = {
  type: 'object',
  properties: {
    title: {
      type: 'string',
      title: 'Title',
      default: 'Get in Touch'
    },
    subtitle: {
      type: 'string',
      title: 'Subtitle (Optional)',
      default: ''
    },
    description: {
      type: 'string',
      title: 'Description (Optional)',
      format: 'textarea',
      default: contactInfoBlockConfig.description
    },
    contacts: {
      type: 'array',
      title: 'Contact Information',
      items: {
        type: 'object',
        properties: {
          type: {
            type: 'string',
            title: 'Type',
            enum: ['email', 'phone', 'address', 'hours', 'website', 'social'],
            enumNames: ['Email', 'Phone', 'Address', 'Business Hours', 'Website', 'Social Media'],
            default: 'email'
          },
          label: {
            type: 'string',
            title: 'Label'
          },
          value: {
            type: 'string',
            title: 'Display Value'
          },
          link: {
            type: 'string',
            title: 'Link (Optional)',
            description: 'For email: email address, for phone: phone number, for others: URL'
          }
        },
        required: ['type', 'label', 'value']
      },
      default: contactInfoBlockConfig.contacts
    },
    layout: {
      type: 'string',
      title: 'Layout',
      enum: ['vertical', 'horizontal', 'grid'],
      enumNames: ['Vertical Stack', 'Horizontal Flow', 'Grid Layout'],
      default: 'vertical'
    },
    cardStyle: {
      type: 'boolean',
      title: 'Card Style',
      description: 'Wrap content in a card with border and shadow',
      default: true
    },
    spacing: {
      type: 'string',
      title: 'Spacing',
      enum: ['compact', 'normal', 'spacious'],
      enumNames: ['Compact', 'Normal', 'Spacious'],
      default: 'normal'
    },
    iconColor: {
      type: 'string',
      title: 'Icon Color',
      format: 'color',
      default: '#012169'
    },
    backgroundColor: {
      type: 'string',
      title: 'Background Color',
      format: 'color',
      default: 'transparent'
    },
    textColor: {
      type: 'string',
      title: 'Text Color',
      format: 'color',
      default: 'inherit'
    }
  },
  required: ['title', 'contacts']
}
