'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Ruler } from 'lucide-react'

interface SpacerBlockConfig {
  height: string
  backgroundColor: string
}

interface SpacerBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function SpacerBlock({ block, isEditing = false }: SpacerBlockProps) {
  const config = block.configuration as SpacerBlockConfig

  const {
    height = '50px',
    backgroundColor = 'transparent',
  } = config

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div
        style={{
          height,
          backgroundColor,
          minHeight: isEditing ? '20px' : undefined,
        }}
        className={isEditing ? 'relative border-2 border-dashed border-gray-300' : ''}
      >
        {isEditing && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="bg-white px-2 py-1 rounded text-xs text-gray-500 flex items-center space-x-1">
              <Ruler className="h-3 w-3" />
              <span>Spacer ({height})</span>
            </div>
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Spacer Block Configuration Component
interface SpacerBlockConfigProps {
  config: SpacerBlockConfig
  onChange: (config: SpacerBlockConfig) => void
}

export function SpacerBlockConfig({ config, onChange }: SpacerBlockConfigProps) {
  const updateConfig = (updates: Partial<SpacerBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  return (
    <div className="space-y-6">
      {/* Spacing */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Spacing</h3>
        
        <div>
          <Label className="block text-sm font-medium mb-2">Height</Label>
          <Input
            value={config.height}
            onChange={(e) => updateConfig({ height: e.target.value })}
            placeholder="50px"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Use CSS units like px, rem, vh, etc.
          </p>
        </div>

        <div>
          <Label className="block text-sm font-medium mb-2">Background Color</Label>
          <div className="flex space-x-2">
            <Input
              type="color"
              value={config.backgroundColor === 'transparent' ? '#ffffff' : config.backgroundColor}
              onChange={(e) => updateConfig({ backgroundColor: e.target.value })}
              className="w-16 h-10 p-1"
            />
            <Input
              value={config.backgroundColor}
              onChange={(e) => updateConfig({ backgroundColor: e.target.value })}
              placeholder="transparent"
              className="flex-1"
            />
          </div>
        </div>
      </div>

      {/* Quick Presets */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Quick Presets</h3>
        
        <div className="grid grid-cols-2 gap-2">
          {[
            { label: 'Small', height: '20px' },
            { label: 'Medium', height: '50px' },
            { label: 'Large', height: '100px' },
            { label: 'XL', height: '150px' },
          ].map((preset) => (
            <button
              key={preset.label}
              onClick={() => updateConfig({ height: preset.height })}
              className="px-3 py-2 text-sm border rounded hover:bg-gray-50 transition-colors"
            >
              {preset.label} ({preset.height})
            </button>
          ))}
        </div>
      </div>

      {/* Preview */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Preview</h3>
        <div className="border rounded-lg p-4 bg-gray-50">
          <div className="text-center text-xs text-gray-500 mb-2">
            Above content
          </div>
          <div
            style={{
              height: config.height,
              backgroundColor: config.backgroundColor,
              border: '1px dashed #ccc',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '12px',
              color: '#666',
            }}
          >
            Spacer ({config.height})
          </div>
          <div className="text-center text-xs text-gray-500 mt-2">
            Below content
          </div>
        </div>
      </div>
    </div>
  )
}
