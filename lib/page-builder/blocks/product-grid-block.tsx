'use client'

import React, { useState, useEffect } from 'react'
import { PageBlock, ProductGridBlockConfig } from '../types'
import { BaseBlock } from './base-block'
import { ProductCard } from '@/components/storefront/products/product-card'
import { Button } from '@/components/ui/button'
import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface Product {
  id: string
  title: string
  slug: string
  price: number
  compareAtPrice?: number
  images: { src: string; alt: string }[]
  description?: string
  inStock: boolean
  onSale: boolean
  featured: boolean
}

interface ProductGridBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function ProductGridBlock({ block, isEditing = false }: ProductGridBlockProps) {
  const config = block.configuration as ProductGridBlockConfig
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const {
    productIds,
    categoryIds,
    collectionIds,
    limit,
    columns,
    showPrice,
    showDescription,
    showAddToCart,
    sortBy,
    sortOrder,
    filterBy,
  } = config

  // Fetch products
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true)
        setError(null)

        const params = new URLSearchParams({
          limit: limit.toString(),
          sortBy,
          sortOrder,
        })

        // Add filters
        if (productIds?.length) {
          params.append('productIds', productIds.join(','))
        }
        if (categoryIds?.length) {
          params.append('categoryIds', categoryIds.join(','))
        }
        if (collectionIds?.length) {
          params.append('collectionIds', collectionIds.join(','))
        }
        if (filterBy?.inStock) {
          params.append('inStock', 'true')
        }
        if (filterBy?.onSale) {
          params.append('onSale', 'true')
        }
        if (filterBy?.featured) {
          params.append('featured', 'true')
        }

        const response = await fetch(`/api/e-commerce/products?${params}`)
        const data = await response.json()

        if (data.success) {
          setProducts(data.data.products || [])
        } else {
          setError(data.error || 'Failed to fetch products')
        }
      } catch (err) {
        setError('Failed to fetch products')
        console.error('Error fetching products:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [productIds, categoryIds, collectionIds, limit, sortBy, sortOrder, filterBy])

  // Get grid classes based on column configuration
  const getGridClasses = () => {
    return cn(
      'grid gap-6',
      `grid-cols-${columns.mobile}`,
      `md:grid-cols-${columns.tablet}`,
      `lg:grid-cols-${columns.desktop}`
    )
  }

  if (loading) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <div className="container mx-auto px-4 md:px-6 py-12">
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">Loading products...</span>
          </div>
        </div>
      </BaseBlock>
    )
  }

  if (error) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <div className="container mx-auto px-4 md:px-6 py-12">
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">{error}</p>
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>
          </div>
        </div>
      </BaseBlock>
    )
  }

  if (products.length === 0) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <div className="container mx-auto px-4 md:px-6 py-12">
          <div className="text-center py-12">
            <p className="text-muted-foreground">No products found matching the criteria.</p>
          </div>
        </div>
      </BaseBlock>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <section className="py-12">
        <div className="container mx-auto px-4 md:px-6">
          <div className={getGridClasses()}>
            {products.map((product) => (
              <ProductCard
                key={product.id}
                product={{
                  id: product.id,
                  title: product.title,
                  slug: product.slug,
                  price: product.price,
                  compareAtPrice: product.compareAtPrice,
                  images: product.images,
                  description: showDescription ? product.description : undefined,
                  inStock: product.inStock,
                  onSale: product.onSale,
                  featured: product.featured,
                }}
                showPrice={showPrice}
                showAddToCart={showAddToCart}
                isEditing={isEditing}
              />
            ))}
          </div>
        </div>
      </section>
    </BaseBlock>
  )
}

// Product Grid Block Configuration Component
interface ProductGridBlockConfigProps {
  config: ProductGridBlockConfig
  onChange: (config: ProductGridBlockConfig) => void
}

export function ProductGridBlockConfig({ config, onChange }: ProductGridBlockConfigProps) {
  const [categories, setCategories] = useState<any[]>([])
  const [collections, setCollections] = useState<any[]>([])

  // Fetch categories and collections for selection
  useEffect(() => {
    const fetchOptions = async () => {
      try {
        const [categoriesRes, collectionsRes] = await Promise.all([
          fetch('/api/e-commerce/categories'),
          fetch('/api/e-commerce/collections'),
        ])

        const categoriesData = await categoriesRes.json()
        const collectionsData = await collectionsRes.json()

        if (categoriesData.success) {
          setCategories(categoriesData.data || [])
        }
        if (collectionsData.success) {
          setCollections(collectionsData.data || [])
        }
      } catch (error) {
        console.error('Error fetching options:', error)
      }
    }

    fetchOptions()
  }, [])

  const updateConfig = (updates: Partial<ProductGridBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const updateColumns = (device: keyof ProductGridBlockConfig['columns'], value: number) => {
    updateConfig({
      columns: { ...config.columns, [device]: value }
    })
  }

  const updateFilterBy = (filterUpdates: Partial<ProductGridBlockConfig['filterBy']>) => {
    updateConfig({
      filterBy: { ...config.filterBy, ...filterUpdates }
    })
  }

  return (
    <div className="space-y-6">
      {/* Product Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Product Selection</h3>
        
        <div>
          <label className="block text-sm font-medium mb-2">Number of Products</label>
          <input
            type="number"
            min="1"
            max="50"
            value={config.limit}
            onChange={(e) => updateConfig({ limit: parseInt(e.target.value) || 1 })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Categories</label>
          <select
            multiple
            value={config.categoryIds || []}
            onChange={(e) => {
              const values = Array.from(e.target.selectedOptions, option => option.value)
              updateConfig({ categoryIds: values })
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            size={4}
          >
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
          <p className="text-xs text-gray-500 mt-1">Hold Ctrl/Cmd to select multiple</p>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Collections</label>
          <select
            multiple
            value={config.collectionIds || []}
            onChange={(e) => {
              const values = Array.from(e.target.selectedOptions, option => option.value)
              updateConfig({ collectionIds: values })
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            size={4}
          >
            {collections.map((collection) => (
              <option key={collection.id} value={collection.id}>
                {collection.title}
              </option>
            ))}
          </select>
          <p className="text-xs text-gray-500 mt-1">Hold Ctrl/Cmd to select multiple</p>
        </div>
      </div>

      {/* Layout Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Layout</h3>
        
        <div className="grid grid-cols-3 gap-3">
          <div>
            <label className="block text-sm font-medium mb-2">Mobile Columns</label>
            <select
              value={config.columns.mobile}
              onChange={(e) => updateColumns('mobile', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={1}>1</option>
              <option value={2}>2</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Tablet Columns</label>
            <select
              value={config.columns.tablet}
              onChange={(e) => updateColumns('tablet', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={2}>2</option>
              <option value={3}>3</option>
              <option value={4}>4</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Desktop Columns</label>
            <select
              value={config.columns.desktop}
              onChange={(e) => updateColumns('desktop', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={3}>3</option>
              <option value={4}>4</option>
              <option value={5}>5</option>
              <option value={6}>6</option>
            </select>
          </div>
        </div>
      </div>

      {/* Display Options */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Display Options</h3>
        
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="show-price"
              checked={config.showPrice}
              onChange={(e) => updateConfig({ showPrice: e.target.checked })}
              className="rounded"
            />
            <label htmlFor="show-price" className="text-sm font-medium">
              Show Price
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="show-description"
              checked={config.showDescription}
              onChange={(e) => updateConfig({ showDescription: e.target.checked })}
              className="rounded"
            />
            <label htmlFor="show-description" className="text-sm font-medium">
              Show Description
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="show-add-to-cart"
              checked={config.showAddToCart}
              onChange={(e) => updateConfig({ showAddToCart: e.target.checked })}
              className="rounded"
            />
            <label htmlFor="show-add-to-cart" className="text-sm font-medium">
              Show Add to Cart Button
            </label>
          </div>
        </div>
      </div>

      {/* Sorting */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Sorting</h3>
        
        <div className="grid grid-cols-2 gap-3">
          <div>
            <label className="block text-sm font-medium mb-2">Sort By</label>
            <select
              value={config.sortBy}
              onChange={(e) => updateConfig({ sortBy: e.target.value as any })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="created">Date Created</option>
              <option value="price">Price</option>
              <option value="title">Title</option>
              <option value="popularity">Popularity</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Sort Order</label>
            <select
              value={config.sortOrder}
              onChange={(e) => updateConfig({ sortOrder: e.target.value as any })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="asc">Ascending</option>
              <option value="desc">Descending</option>
            </select>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Filters</h3>
        
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="filter-in-stock"
              checked={config.filterBy?.inStock || false}
              onChange={(e) => updateFilterBy({ inStock: e.target.checked })}
              className="rounded"
            />
            <label htmlFor="filter-in-stock" className="text-sm font-medium">
              Only In Stock Products
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="filter-on-sale"
              checked={config.filterBy?.onSale || false}
              onChange={(e) => updateFilterBy({ onSale: e.target.checked })}
              className="rounded"
            />
            <label htmlFor="filter-on-sale" className="text-sm font-medium">
              Only Sale Products
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="filter-featured"
              checked={config.filterBy?.featured || false}
              onChange={(e) => updateFilterBy({ featured: e.target.checked })}
              className="rounded"
            />
            <label htmlFor="filter-featured" className="text-sm font-medium">
              Only Featured Products
            </label>
          </div>
        </div>
      </div>
    </div>
  )
}
