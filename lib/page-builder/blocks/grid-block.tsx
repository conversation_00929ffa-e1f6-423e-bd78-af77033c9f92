'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { cn } from '@/lib/utils'

interface GridBlockConfig {
  columns: {
    desktop: number
    tablet: number
    mobile: number
  }
  rows: 'auto' | number
  gap: {
    column: 'none' | 'sm' | 'md' | 'lg' | 'xl'
    row: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  }
  autoFit: boolean
  minItemWidth: string
  maxItemWidth: string
  aspectRatio: 'auto' | 'square' | '16/9' | '4/3' | '3/2'
  alignment: 'start' | 'center' | 'end' | 'stretch'
  justifyItems: 'start' | 'center' | 'end' | 'stretch'
  backgroundColor: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
  borderRadius: string
  items: GridItemConfig[]
}

interface GridItemConfig {
  id: string
  content: string
  columnSpan?: number
  rowSpan?: number
  backgroundColor?: string
  padding?: {
    top: string
    right: string
    bottom: string
    left: string
  }
  alignment?: 'left' | 'center' | 'right'
}

interface GridBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function GridBlock({ block, isEditing = false }: GridBlockProps) {
  const config = block.configuration as GridBlockConfig
  
  const {
    columns,
    rows,
    gap,
    autoFit,
    minItemWidth,
    maxItemWidth,
    aspectRatio,
    alignment,
    justifyItems,
    backgroundColor,
    padding,
    borderRadius,
    items,
  } = config

  const getGapClasses = () => {
    const gapClasses = {
      none: '0',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem'
    }
    return {
      columnGap: gapClasses[gap.column],
      rowGap: gapClasses[gap.row]
    }
  }

  const getGridTemplateColumns = () => {
    if (autoFit) {
      return `repeat(auto-fit, minmax(${minItemWidth || '200px'}, ${maxItemWidth || '1fr'}))`
    }
    return {
      gridTemplateColumns: `repeat(${columns.desktop}, 1fr)`,
    }
  }

  const getAspectRatioClass = () => {
    const ratioClasses = {
      auto: '',
      square: 'aspect-square',
      '16/9': 'aspect-video',
      '4/3': 'aspect-[4/3]',
      '3/2': 'aspect-[3/2]'
    }
    return ratioClasses[aspectRatio]
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
  }

  const gridStyles = {
    display: 'grid',
    ...getGridTemplateColumns(),
    ...getGapClasses(),
    gridTemplateRows: rows === 'auto' ? 'auto' : `repeat(${rows}, 1fr)`,
    alignItems: alignment,
    justifyItems: justifyItems,
  }

  // Generate demo content for editing mode
  const getDemoContent = (index: number) => {
    const demoContents = [
      '<div class="text-center"><h4>Grid Item 1</h4><p>Featured content</p></div>',
      '<div class="text-center"><h4>Grid Item 2</h4><p>Product showcase</p></div>',
      '<div class="text-center"><h4>Grid Item 3</h4><p>Service highlight</p></div>',
      '<div class="text-center"><h4>Grid Item 4</h4><p>Team member</p></div>',
      '<div class="text-center"><h4>Grid Item 5</h4><p>Testimonial</p></div>',
      '<div class="text-center"><h4>Grid Item 6</h4><p>Portfolio item</p></div>',
      '<div class="text-center"><h4>Grid Item 7</h4><p>Blog post</p></div>',
      '<div class="text-center"><h4>Grid Item 8</h4><p>Gallery image</p></div>'
    ]
    return demoContents[index] || `<div class="text-center"><h4>Grid Item ${index + 1}</h4><p>Content goes here</p></div>`
  }

  const renderGridItems = () => {
    const itemsToRender = isEditing 
      ? Array.from({ length: columns.desktop * (typeof rows === 'number' ? rows : 2) }, (_, i) => ({
          id: `item-${i}`,
          content: getDemoContent(i),
          alignment: 'center' as const,
        }))
      : items

    return itemsToRender.map((item, index) => {
      const itemStyles = {
        backgroundColor: item.backgroundColor || 'transparent',
        paddingTop: item.padding?.top || '1rem',
        paddingRight: item.padding?.right || '1rem',
        paddingBottom: item.padding?.bottom || '1rem',
        paddingLeft: item.padding?.left || '1rem',
        gridColumn: item.columnSpan ? `span ${item.columnSpan}` : 'auto',
        gridRow: item.rowSpan ? `span ${item.rowSpan}` : 'auto',
      }

      return (
        <div
          key={item.id}
          className={cn(
            getAspectRatioClass(),
            `text-${item.alignment || 'center'}`,
            isEditing && 'border border-dashed border-gray-300 rounded min-h-[100px] flex items-center justify-center'
          )}
          style={itemStyles}
        >
          {isEditing ? (
            <div 
              className="prose prose-sm max-w-none w-full"
              dangerouslySetInnerHTML={{ __html: item.content }}
            />
          ) : (
            <div 
              className="prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{ __html: item.content }}
            />
          )}
        </div>
      )
    })
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div style={containerStyles}>
        <div 
          className={cn(
            'grid',
            `md:grid-cols-${columns.tablet}`,
            `lg:grid-cols-${columns.desktop}`,
            `grid-cols-${columns.mobile}`
          )}
          style={gridStyles}
        >
          {renderGridItems()}
        </div>
        
        {isEditing && (
          <div className="mt-4 p-2 bg-green-50 border border-green-200 rounded text-xs text-green-700">
            <strong>Grid Block:</strong> {columns.desktop} columns × {rows === 'auto' ? 'auto' : rows} rows
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Grid Block Configuration Component
interface GridBlockConfigProps {
  config: GridBlockConfig
  onChange: (config: GridBlockConfig) => void
}

export function GridBlockConfig({ config, onChange }: GridBlockConfigProps) {
  const updateConfig = (updates: Partial<GridBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const updateColumns = (device: keyof GridBlockConfig['columns'], value: number) => {
    updateConfig({
      columns: {
        ...config.columns,
        [device]: value
      }
    })
  }

  const updateGap = (type: keyof GridBlockConfig['gap'], value: string) => {
    updateConfig({
      gap: {
        ...config.gap,
        [type]: value as any
      }
    })
  }

  return (
    <div className="space-y-6">
      {/* Grid Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Grid Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-3 gap-2">
            <div>
              <Label className="text-xs">Desktop</Label>
              <Input
                type="number"
                min="1"
                max="12"
                value={config.columns.desktop}
                onChange={(e) => updateColumns('desktop', parseInt(e.target.value))}
                className="mt-1"
              />
            </div>
            <div>
              <Label className="text-xs">Tablet</Label>
              <Input
                type="number"
                min="1"
                max="8"
                value={config.columns.tablet}
                onChange={(e) => updateColumns('tablet', parseInt(e.target.value))}
                className="mt-1"
              />
            </div>
            <div>
              <Label className="text-xs">Mobile</Label>
              <Input
                type="number"
                min="1"
                max="4"
                value={config.columns.mobile}
                onChange={(e) => updateColumns('mobile', parseInt(e.target.value))}
                className="mt-1"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Column Gap</Label>
              <Select
                value={config.gap.column}
                onValueChange={(value) => updateGap('column', value)}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="sm">Small</SelectItem>
                  <SelectItem value="md">Medium</SelectItem>
                  <SelectItem value="lg">Large</SelectItem>
                  <SelectItem value="xl">Extra Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Row Gap</Label>
              <Select
                value={config.gap.row}
                onValueChange={(value) => updateGap('row', value)}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="sm">Small</SelectItem>
                  <SelectItem value="md">Medium</SelectItem>
                  <SelectItem value="lg">Large</SelectItem>
                  <SelectItem value="xl">Extra Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label className="text-xs">Aspect Ratio</Label>
            <Select
              value={config.aspectRatio}
              onValueChange={(value) => updateConfig({ aspectRatio: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="auto">Auto</SelectItem>
                <SelectItem value="square">Square (1:1)</SelectItem>
                <SelectItem value="16/9">Video (16:9)</SelectItem>
                <SelectItem value="4/3">Classic (4:3)</SelectItem>
                <SelectItem value="3/2">Photo (3:2)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <Label className="text-xs">Auto Fit</Label>
            <Switch
              checked={config.autoFit}
              onCheckedChange={(checked) => updateConfig({ autoFit: checked })}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
