'use client'

import React, { useState, useCallback } from 'react'
import { CodeMirrorEditor, CodeLanguage } from '../components/code-editor/codemirror-editor'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Play, 
  Copy, 
  Check, 
  Eye, 
  EyeOff, 
  Settings,
  Code,
  FileCode,
  Palette,
  Globe
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { BlockConfiguration } from '../types'

export interface CodeBlockConfiguration extends BlockConfiguration {
  code: {
    html: string
    css: string
    javascript: string
  }
  activeLanguage: CodeLanguage
  showPreview: boolean
  enabledLanguages: CodeLanguage[]
  theme: 'light' | 'dark'
  height: string
  readOnly: boolean
  showLineNumbers: boolean
  enableSearch: boolean
  enableAutocompletion: boolean
}

interface CodeBlockProps {
  configuration: CodeBlockConfiguration
  isSelected?: boolean
  isEditing?: boolean
  onUpdate?: (updates: Partial<CodeBlockConfiguration>) => void
  className?: string
}

export function CodeBlock({
  configuration,
  isSelected = false,
  isEditing = false,
  onUpdate,
  className
}: CodeBlockProps) {
  const [copied, setCopied] = useState(false)
  const [activeTab, setActiveTab] = useState<'editor' | 'preview'>('editor')

  const {
    code = { html: '', css: '', javascript: '' },
    activeLanguage = 'html',
    showPreview = true,
    enabledLanguages = ['html', 'css', 'javascript'],
    theme = 'light',
    height = '400px',
    readOnly = false,
    showLineNumbers = true,
    enableSearch = true,
    enableAutocompletion = true
  } = configuration

  // Handle code changes
  const handleCodeChange = useCallback((language: CodeLanguage, value: string) => {
    if (!onUpdate) return
    
    onUpdate({
      code: {
        ...code,
        [language]: value
      }
    })
  }, [code, onUpdate])

  // Handle language change
  const handleLanguageChange = useCallback((language: CodeLanguage) => {
    if (!onUpdate) return
    onUpdate({ activeLanguage: language })
  }, [onUpdate])

  // Copy code to clipboard
  const copyCode = useCallback(async () => {
    try {
      const currentCode = code[activeLanguage] || ''
      await navigator.clipboard.writeText(currentCode)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy code:', error)
    }
  }, [code, activeLanguage])

  // Execute code (for preview)
  const executeCode = useCallback(() => {
    if (!showPreview) return

    // Create a preview with the current code
    const previewContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <style>${code.css}</style>
        </head>
        <body>
          ${code.html}
          <script>${code.javascript}</script>
        </body>
      </html>
    `

    // Open in new window or update preview iframe
    const previewWindow = window.open('', '_blank')
    if (previewWindow) {
      previewWindow.document.write(previewContent)
      previewWindow.document.close()
    }
  }, [code, showPreview])

  // Language configuration
  const languageConfig = {
    html: { label: 'HTML', icon: Globe, color: 'bg-orange-100 text-orange-800' },
    css: { label: 'CSS', icon: Palette, color: 'bg-blue-100 text-blue-800' },
    javascript: { label: 'JavaScript', icon: FileCode, color: 'bg-yellow-100 text-yellow-800' },
    typescript: { label: 'TypeScript', icon: FileCode, color: 'bg-blue-100 text-blue-800' },
    jsx: { label: 'JSX', icon: FileCode, color: 'bg-cyan-100 text-cyan-800' },
    tsx: { label: 'TSX', icon: FileCode, color: 'bg-purple-100 text-purple-800' },
    json: { label: 'JSON', icon: Code, color: 'bg-green-100 text-green-800' }
  }

  // Render view mode (non-editing)
  if (!isEditing) {
    return (
      <div className={cn('relative group', className)}>
        <Card className={cn(
          'transition-all duration-200',
          isSelected && 'ring-2 ring-blue-500 ring-offset-2'
        )}>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Code className="h-5 w-5 text-blue-600" />
                <CardTitle className="text-lg">Code Block</CardTitle>
                <Badge 
                  variant="secondary" 
                  className={languageConfig[activeLanguage]?.color}
                >
                  {languageConfig[activeLanguage]?.label || activeLanguage}
                </Badge>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={copyCode}
                  title="Copy code"
                >
                  {copied ? (
                    <Check className="h-4 w-4 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
                
                {showPreview && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={executeCode}
                    title="Run code"
                  >
                    <Play className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          
          <CardContent>
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="editor">Code</TabsTrigger>
                {showPreview && <TabsTrigger value="preview">Preview</TabsTrigger>}
              </TabsList>
              
              <TabsContent value="editor" className="mt-4">
                <CodeMirrorEditor
                  value={code[activeLanguage] || ''}
                  onChange={(value) => handleCodeChange(activeLanguage, value)}
                  language={activeLanguage}
                  theme={theme}
                  height={height}
                  readOnly={readOnly}
                  lineNumbers={showLineNumbers}
                  searchEnabled={enableSearch}
                  autocompletion={enableAutocompletion}
                  placeholder={`Enter ${activeLanguage} code here...`}
                />
              </TabsContent>
              
              {showPreview && (
                <TabsContent value="preview" className="mt-4">
                  <div className="border border-gray-200 rounded-md overflow-hidden">
                    <iframe
                      srcDoc={`
                        <!DOCTYPE html>
                        <html>
                          <head>
                            <style>${code.css}</style>
                          </head>
                          <body>
                            ${code.html}
                            <script>${code.javascript}</script>
                          </body>
                        </html>
                      `}
                      className="w-full border-0"
                      style={{ height }}
                      title="Code Preview"
                    />
                  </div>
                </TabsContent>
              )}
            </Tabs>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Render editing mode
  return (
    <div className={cn('relative', className)}>
      <Card className="border-2 border-blue-500">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Code className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-lg">Code Block</CardTitle>
            </div>
            
            <div className="flex items-center space-x-2">
              {/* Language Selector */}
              <div className="flex items-center space-x-1">
                {enabledLanguages.map((lang) => {
                  const config = languageConfig[lang]
                  if (!config) return null
                  
                  return (
                    <Button
                      key={lang}
                      variant={activeLanguage === lang ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => handleLanguageChange(lang)}
                      className="h-8"
                    >
                      <config.icon className="h-3 w-3 mr-1" />
                      {config.label}
                    </Button>
                  )
                })}
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={copyCode}
                title="Copy code"
              >
                {copied ? (
                  <Check className="h-4 w-4 text-green-600" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="editor">Code Editor</TabsTrigger>
              {showPreview && <TabsTrigger value="preview">Live Preview</TabsTrigger>}
            </TabsList>
            
            <TabsContent value="editor" className="mt-4">
              <CodeMirrorEditor
                value={code[activeLanguage] || ''}
                onChange={(value) => handleCodeChange(activeLanguage, value)}
                language={activeLanguage}
                theme={theme}
                height={height}
                readOnly={false}
                lineNumbers={showLineNumbers}
                searchEnabled={enableSearch}
                autocompletion={enableAutocompletion}
                placeholder={`Enter ${activeLanguage} code here...`}
              />
            </TabsContent>
            
            {showPreview && (
              <TabsContent value="preview" className="mt-4">
                <div className="border border-gray-200 rounded-md overflow-hidden">
                  <iframe
                    srcDoc={`
                      <!DOCTYPE html>
                      <html>
                        <head>
                          <style>${code.css}</style>
                        </head>
                        <body>
                          ${code.html}
                          <script>${code.javascript}</script>
                        </body>
                      </html>
                    `}
                    className="w-full border-0"
                    style={{ height }}
                    title="Code Preview"
                  />
                </div>
              </TabsContent>
            )}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

// Configuration component for the properties panel
export function CodeBlockConfig({
  config,
  onChange
}: {
  config: CodeBlockConfiguration
  onChange: (updates: Partial<CodeBlockConfiguration>) => void
}) {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Code Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Language Selection */}
          <div>
            <label className="text-xs font-medium">Active Language</label>
            <select
              value={config.activeLanguage || 'html'}
              onChange={(e) => onChange({ activeLanguage: e.target.value as CodeLanguage })}
              className="w-full mt-1 px-2 py-1 text-xs border rounded"
            >
              <option value="html">HTML</option>
              <option value="css">CSS</option>
              <option value="javascript">JavaScript</option>
              <option value="typescript">TypeScript</option>
              <option value="jsx">JSX</option>
              <option value="tsx">TSX</option>
              <option value="json">JSON</option>
            </select>
          </div>

          {/* Theme */}
          <div>
            <label className="text-xs font-medium">Theme</label>
            <select
              value={config.theme || 'light'}
              onChange={(e) => onChange({ theme: e.target.value as 'light' | 'dark' })}
              className="w-full mt-1 px-2 py-1 text-xs border rounded"
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
            </select>
          </div>

          {/* Height */}
          <div>
            <label className="text-xs font-medium">Height</label>
            <input
              type="text"
              value={config.height || '400px'}
              onChange={(e) => onChange({ height: e.target.value })}
              placeholder="400px"
              className="w-full mt-1 px-2 py-1 text-xs border rounded"
            />
          </div>

          {/* Options */}
          <div className="space-y-2">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.showPreview !== false}
                onChange={(e) => onChange({ showPreview: e.target.checked })}
                className="rounded"
              />
              <span className="text-xs">Show Preview</span>
            </label>
            
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.showLineNumbers !== false}
                onChange={(e) => onChange({ showLineNumbers: e.target.checked })}
                className="rounded"
              />
              <span className="text-xs">Line Numbers</span>
            </label>
            
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.enableSearch !== false}
                onChange={(e) => onChange({ enableSearch: e.target.checked })}
                className="rounded"
              />
              <span className="text-xs">Enable Search</span>
            </label>
            
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.enableAutocompletion !== false}
                onChange={(e) => onChange({ enableAutocompletion: e.target.checked })}
                className="rounded"
              />
              <span className="text-xs">Auto-completion</span>
            </label>
            
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.readOnly === true}
                onChange={(e) => onChange({ readOnly: e.target.checked })}
                className="rounded"
              />
              <span className="text-xs">Read Only</span>
            </label>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
