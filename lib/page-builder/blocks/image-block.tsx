'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import { ImageIcon } from 'lucide-react'
import Link from 'next/link'

interface ImageBlockConfig {
  src: string
  alt: string
  caption?: string
  link?: string
  width: string
  height: string
  objectFit: 'cover' | 'contain' | 'fill' | 'none'
  borderRadius: string
}

interface ImageBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function ImageBlock({ block, isEditing = false }: ImageBlockProps) {
  const config = block.configuration as ImageBlockConfig

  const {
    src,
    alt,
    caption,
    link,
    width = '100%',
    height = 'auto',
    objectFit = 'cover',
    borderRadius = '0px',
  } = config

  if (!src) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <section className="py-8">
          <div className="container mx-auto px-4 md:px-6">
            <Card className="border-dashed">
              <CardContent className="flex flex-col items-center justify-center py-12">
                <ImageIcon className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">No image selected</p>
              </CardContent>
            </Card>
          </div>
        </section>
      </BaseBlock>
    )
  }

  const imageElement = (
    <img
      src={src}
      alt={alt}
      style={{
        width,
        height,
        objectFit,
        borderRadius,
      }}
      className="max-w-full h-auto"
    />
  )

  const content = (
    <div className="text-center">
      {link && !isEditing ? (
        <Link href={link} className="inline-block">
          {imageElement}
        </Link>
      ) : (
        imageElement
      )}
      
      {caption && (
        <p className="mt-4 text-sm text-muted-foreground italic">
          {caption}
        </p>
      )}
    </div>
  )

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <section className="py-8">
        <div className="container mx-auto px-4 md:px-6">
          {content}
        </div>
      </section>
    </BaseBlock>
  )
}

// Image Block Configuration Component
interface ImageBlockConfigProps {
  config: ImageBlockConfig
  onChange: (config: ImageBlockConfig) => void
}

export function ImageBlockConfig({ config, onChange }: ImageBlockConfigProps) {
  const updateConfig = (updates: Partial<ImageBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  return (
    <div className="space-y-6">
      {/* Image Source */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Image</h3>
        
        <div>
          <Label className="block text-sm font-medium mb-2">Image URL</Label>
          <Input
            value={config.src}
            onChange={(e) => updateConfig({ src: e.target.value })}
            placeholder="https://example.com/image.jpg"
            type="url"
          />
        </div>

        <div>
          <Label className="block text-sm font-medium mb-2">Alt Text</Label>
          <Input
            value={config.alt}
            onChange={(e) => updateConfig({ alt: e.target.value })}
            placeholder="Describe the image for accessibility"
          />
        </div>

        <div>
          <Label className="block text-sm font-medium mb-2">Caption (Optional)</Label>
          <Textarea
            value={config.caption || ''}
            onChange={(e) => updateConfig({ caption: e.target.value })}
            placeholder="Image caption or description"
            rows={2}
          />
        </div>

        <div>
          <Label className="block text-sm font-medium mb-2">Link URL (Optional)</Label>
          <Input
            value={config.link || ''}
            onChange={(e) => updateConfig({ link: e.target.value })}
            placeholder="https://example.com"
            type="url"
          />
        </div>
      </div>

      {/* Styling */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Styling</h3>
        
        <div className="grid grid-cols-2 gap-3">
          <div>
            <Label className="block text-sm font-medium mb-2">Width</Label>
            <Input
              value={config.width}
              onChange={(e) => updateConfig({ width: e.target.value })}
              placeholder="100%"
            />
          </div>
          <div>
            <Label className="block text-sm font-medium mb-2">Height</Label>
            <Input
              value={config.height}
              onChange={(e) => updateConfig({ height: e.target.value })}
              placeholder="auto"
            />
          </div>
        </div>

        <div>
          <Label className="block text-sm font-medium mb-2">Object Fit</Label>
          <Select
            value={config.objectFit}
            onValueChange={(value) => updateConfig({ objectFit: value as any })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="cover">Cover</SelectItem>
              <SelectItem value="contain">Contain</SelectItem>
              <SelectItem value="fill">Fill</SelectItem>
              <SelectItem value="none">None</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label className="block text-sm font-medium mb-2">Border Radius</Label>
          <Input
            value={config.borderRadius}
            onChange={(e) => updateConfig({ borderRadius: e.target.value })}
            placeholder="0px"
          />
        </div>
      </div>

      {/* Preview */}
      {config.src && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Preview</h3>
          <div className="border rounded-lg p-4 bg-gray-50">
            <div className="text-center">
              <img
                src={config.src}
                alt={config.alt}
                style={{
                  width: config.width,
                  height: config.height,
                  objectFit: config.objectFit,
                  borderRadius: config.borderRadius,
                  maxWidth: '100%',
                  maxHeight: '200px',
                }}
                onError={(e) => {
                  e.currentTarget.style.display = 'none'
                }}
              />
              {config.caption && (
                <p className="mt-2 text-sm text-muted-foreground italic">
                  {config.caption}
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
