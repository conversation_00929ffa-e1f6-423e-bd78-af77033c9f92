'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Plus, Trash2, ExternalLink } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ButtonGroupBlockConfig {
  buttons: ButtonConfig[]
  layout: 'horizontal' | 'vertical' | 'grid'
  alignment: 'left' | 'center' | 'right' | 'justified'
  spacing: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  size: 'sm' | 'md' | 'lg'
  fullWidth: boolean
  stackOnMobile: boolean
  gridColumns: number
  backgroundColor: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
  borderRadius: string
}

interface ButtonConfig {
  id: string
  text: string
  url: string
  variant: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'sm' | 'md' | 'lg'
  icon?: string
  iconPosition: 'left' | 'right'
  openInNewTab: boolean
  disabled: boolean
  customColor?: string
  customBackgroundColor?: string
}

interface ButtonGroupBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function ButtonGroupBlock({ block, isEditing = false }: ButtonGroupBlockProps) {
  const config = block.configuration as ButtonGroupBlockConfig
  
  const {
    buttons,
    layout,
    alignment,
    spacing,
    size,
    fullWidth,
    stackOnMobile,
    gridColumns,
    backgroundColor,
    padding,
    borderRadius,
  } = config

  const getSpacingClass = () => {
    const spacingClasses = {
      none: 'gap-0',
      sm: 'gap-1',
      md: 'gap-2',
      lg: 'gap-4',
      xl: 'gap-6'
    }
    return spacingClasses[spacing]
  }

  const getAlignmentClass = () => {
    if (layout === 'grid') return 'justify-items-center'
    
    const alignmentClasses = {
      left: 'justify-start',
      center: 'justify-center',
      right: 'justify-end',
      justified: 'justify-between'
    }
    return alignmentClasses[alignment]
  }

  const getLayoutClasses = () => {
    switch (layout) {
      case 'vertical':
        return 'flex flex-col'
      case 'grid':
        return `grid grid-cols-${Math.min(gridColumns, buttons.length)}`
      default:
        return cn(
          'flex',
          stackOnMobile ? 'flex-col sm:flex-row' : 'flex-row flex-wrap'
        )
    }
  }

  const handleButtonClick = (button: ButtonConfig) => {
    if (isEditing || button.disabled) return
    
    if (button.url) {
      if (button.openInNewTab) {
        window.open(button.url, '_blank', 'noopener,noreferrer')
      } else {
        window.location.href = button.url
      }
    }
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
  }

  // Demo buttons for editing mode
  const demoButtons = [
    {
      id: 'demo-1',
      text: 'Primary Action',
      url: '#',
      variant: 'default' as const,
      iconPosition: 'left' as const,
      openInNewTab: false,
      disabled: false
    },
    {
      id: 'demo-2',
      text: 'Secondary Action',
      url: '#',
      variant: 'outline' as const,
      iconPosition: 'left' as const,
      openInNewTab: false,
      disabled: false
    },
    {
      id: 'demo-3',
      text: 'Learn More',
      url: '#',
      variant: 'ghost' as const,
      icon: '📖',
      iconPosition: 'left' as const,
      openInNewTab: true,
      disabled: false
    }
  ]

  const buttonsToRender = isEditing ? demoButtons : buttons

  const renderButton = (button: ButtonConfig) => {
    const buttonStyles = {
      color: button.customColor,
      backgroundColor: button.customBackgroundColor,
    }

    const getButtonSize = () => {
      const buttonSize = button.size || size
      return buttonSize === 'md' ? 'default' : buttonSize
    }

    return (
      <Button
        key={button.id}
        variant={button.variant}
        size={getButtonSize() as any}
        disabled={button.disabled || isEditing}
        onClick={() => handleButtonClick(button)}
        className={cn(
          fullWidth && 'w-full',
          alignment === 'justified' && layout === 'horizontal' && 'flex-1'
        )}
        style={buttonStyles}
      >
        {button.icon && button.iconPosition === 'left' && (
          <span className="mr-2">{button.icon}</span>
        )}
        {button.text}
        {button.icon && button.iconPosition === 'right' && (
          <span className="ml-2">{button.icon}</span>
        )}
        {button.openInNewTab && !button.icon && (
          <ExternalLink className="ml-2 h-3 w-3" />
        )}
      </Button>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div style={containerStyles}>
        <div className={cn(
          getLayoutClasses(),
          getSpacingClass(),
          getAlignmentClass()
        )}>
          {buttonsToRender.map((button) => renderButton(button))}
        </div>
        
        {isEditing && (
          <div className="mt-4 p-2 bg-orange-50 border border-orange-200 rounded text-xs text-orange-700">
            <strong>Button Group:</strong> {buttonsToRender.length} buttons • {layout} layout • {alignment} alignment
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Button Group Block Configuration Component
interface ButtonGroupBlockConfigProps {
  config: ButtonGroupBlockConfig
  onChange: (config: ButtonGroupBlockConfig) => void
}

export function ButtonGroupBlockConfig({ config, onChange }: ButtonGroupBlockConfigProps) {
  const updateConfig = (updates: Partial<ButtonGroupBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const addButton = () => {
    const newButton: ButtonConfig = {
      id: `button-${Date.now()}`,
      text: 'New Button',
      url: '#',
      variant: 'default',
      iconPosition: 'left',
      openInNewTab: false,
      disabled: false
    }
    
    updateConfig({
      buttons: [...config.buttons, newButton]
    })
  }

  const updateButton = (index: number, updates: Partial<ButtonConfig>) => {
    const updatedButtons = [...config.buttons]
    updatedButtons[index] = { ...updatedButtons[index], ...updates }
    updateConfig({ buttons: updatedButtons })
  }

  const removeButton = (index: number) => {
    const updatedButtons = config.buttons.filter((_, i) => i !== index)
    updateConfig({ buttons: updatedButtons })
  }

  return (
    <div className="space-y-6">
      {/* Layout Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Layout Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Layout</Label>
              <Select
                value={config.layout}
                onValueChange={(value) => updateConfig({ layout: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="horizontal">Horizontal</SelectItem>
                  <SelectItem value="vertical">Vertical</SelectItem>
                  <SelectItem value="grid">Grid</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Alignment</Label>
              <Select
                value={config.alignment}
                onValueChange={(value) => updateConfig({ alignment: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="left">Left</SelectItem>
                  <SelectItem value="center">Center</SelectItem>
                  <SelectItem value="right">Right</SelectItem>
                  <SelectItem value="justified">Justified</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Spacing</Label>
              <Select
                value={config.spacing}
                onValueChange={(value) => updateConfig({ spacing: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="sm">Small</SelectItem>
                  <SelectItem value="md">Medium</SelectItem>
                  <SelectItem value="lg">Large</SelectItem>
                  <SelectItem value="xl">Extra Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Button Size</Label>
              <Select
                value={config.size}
                onValueChange={(value) => updateConfig({ size: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sm">Small</SelectItem>
                  <SelectItem value="md">Medium</SelectItem>
                  <SelectItem value="lg">Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {config.layout === 'grid' && (
            <div>
              <Label className="text-xs">Grid Columns</Label>
              <Input
                type="number"
                min="1"
                max="6"
                value={config.gridColumns}
                onChange={(e) => updateConfig({ gridColumns: parseInt(e.target.value) || 2 })}
                className="mt-1"
              />
            </div>
          )}

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Full Width Buttons</Label>
              <Switch
                checked={config.fullWidth}
                onCheckedChange={(checked) => updateConfig({ fullWidth: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Stack on Mobile</Label>
              <Switch
                checked={config.stackOnMobile}
                onCheckedChange={(checked) => updateConfig({ stackOnMobile: checked })}
                disabled={config.layout !== 'horizontal'}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Button Items */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm">Buttons</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={addButton}
            className="h-8 px-2"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Button
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.buttons.map((button, index) => (
            <div key={button.id} className="border rounded-lg p-3 space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Button {index + 1}</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeButton(index)}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Text</Label>
                  <Input
                    value={button.text}
                    onChange={(e) => updateButton(index, { text: e.target.value })}
                    className="mt-1"
                    placeholder="Button text"
                  />
                </div>
                <div>
                  <Label className="text-xs">URL</Label>
                  <Input
                    value={button.url}
                    onChange={(e) => updateButton(index, { url: e.target.value })}
                    className="mt-1"
                    placeholder="Button URL"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Variant</Label>
                  <Select
                    value={button.variant}
                    onValueChange={(value) => updateButton(index, { variant: value as any })}
                  >
                    <SelectTrigger className="w-full mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">Default</SelectItem>
                      <SelectItem value="destructive">Destructive</SelectItem>
                      <SelectItem value="outline">Outline</SelectItem>
                      <SelectItem value="secondary">Secondary</SelectItem>
                      <SelectItem value="ghost">Ghost</SelectItem>
                      <SelectItem value="link">Link</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-xs">Icon</Label>
                  <Input
                    value={button.icon || ''}
                    onChange={(e) => updateButton(index, { icon: e.target.value })}
                    className="mt-1"
                    placeholder="Emoji or icon"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={button.openInNewTab}
                      onCheckedChange={(checked) => updateButton(index, { openInNewTab: checked })}
                    />
                    <Label className="text-xs">New Tab</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={button.disabled}
                      onCheckedChange={(checked) => updateButton(index, { disabled: checked })}
                    />
                    <Label className="text-xs">Disabled</Label>
                  </div>
                </div>
              </div>
            </div>
          ))}
          
          {config.buttons.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              <p className="text-sm">No buttons yet.</p>
              <p className="text-xs">Click "Add Button" to get started.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
