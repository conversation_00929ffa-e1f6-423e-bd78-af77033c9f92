'use client'

import React, { useState, useEffect } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Clock, Timer, Calendar, MapPin } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ClockTimerWidgetBlockConfig {
  type: 'clock' | 'countdown' | 'stopwatch' | 'timezone'
  title?: string
  format: '12h' | '24h'
  showSeconds: boolean
  showDate: boolean
  showTimezone: boolean
  timezone: string
  countdownTarget?: string // ISO date string
  countdownMessage?: string
  style: 'digital' | 'analog' | 'minimal' | 'card'
  size: 'sm' | 'md' | 'lg' | 'xl'
  color: string
  backgroundColor: string
  borderRadius: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
  alignment: 'left' | 'center' | 'right'
  showIcon: boolean
}

interface ClockTimerWidgetBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function ClockTimerWidgetBlock({ block, isEditing = false }: ClockTimerWidgetBlockProps) {
  const config = block.configuration as ClockTimerWidgetBlockConfig
  const [currentTime, setCurrentTime] = useState(new Date())
  const [countdownTime, setCountdownTime] = useState({ days: 0, hours: 0, minutes: 0, seconds: 0 })
  const [stopwatchTime, setStopwatchTime] = useState(0)
  const [isStopwatchRunning, setIsStopwatchRunning] = useState(false)

  const {
    type,
    title,
    format,
    showSeconds,
    showDate,
    showTimezone,
    timezone,
    countdownTarget,
    countdownMessage,
    style,
    size,
    color,
    backgroundColor,
    borderRadius,
    padding,
    alignment,
    showIcon,
  } = config

  // Update current time
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Calculate countdown
  useEffect(() => {
    if (type === 'countdown' && countdownTarget) {
      const timer = setInterval(() => {
        const target = new Date(countdownTarget)
        const now = new Date()
        const difference = target.getTime() - now.getTime()

        if (difference > 0) {
          const days = Math.floor(difference / (1000 * 60 * 60 * 24))
          const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
          const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
          const seconds = Math.floor((difference % (1000 * 60)) / 1000)

          setCountdownTime({ days, hours, minutes, seconds })
        } else {
          setCountdownTime({ days: 0, hours: 0, minutes: 0, seconds: 0 })
        }
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [type, countdownTarget])

  // Stopwatch functionality
  useEffect(() => {
    let timer: NodeJS.Timeout
    if (type === 'stopwatch' && isStopwatchRunning && !isEditing) {
      timer = setInterval(() => {
        setStopwatchTime(prev => prev + 1)
      }, 1000)
    }
    return () => clearInterval(timer)
  }, [type, isStopwatchRunning, isEditing])

  const formatTime = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
      ...(showSeconds && { second: '2-digit' }),
      hour12: format === '12h',
      ...(timezone && { timeZone: timezone })
    }

    return date.toLocaleTimeString('en-US', options)
  }

  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      ...(timezone && { timeZone: timezone })
    }

    return date.toLocaleDateString('en-US', options)
  }

  const formatStopwatch = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const getSizeClasses = () => {
    const sizeClasses = {
      sm: 'text-lg',
      md: 'text-2xl',
      lg: 'text-4xl',
      xl: 'text-6xl'
    }
    return sizeClasses[size]
  }

  const getIcon = () => {
    const icons = {
      clock: <Clock className="h-5 w-5" />,
      countdown: <Timer className="h-5 w-5" />,
      stopwatch: <Timer className="h-5 w-5" />,
      timezone: <MapPin className="h-5 w-5" />
    }
    return icons[type]
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
    color: color,
  }

  const renderClock = () => {
    return (
      <div className={cn('font-mono', getSizeClasses())}>
        {formatTime(currentTime)}
      </div>
    )
  }

  const renderCountdown = () => {
    const { days, hours, minutes, seconds } = countdownTime
    const isExpired = days === 0 && hours === 0 && minutes === 0 && seconds === 0

    if (isExpired && countdownMessage) {
      return (
        <div className={cn('font-semibold', getSizeClasses())}>
          {countdownMessage}
        </div>
      )
    }

    return (
      <div className="space-y-2">
        <div className={cn('font-mono grid grid-cols-4 gap-2 text-center', getSizeClasses())}>
          <div>
            <div className="font-bold">{days.toString().padStart(2, '0')}</div>
            <div className="text-xs text-gray-500">DAYS</div>
          </div>
          <div>
            <div className="font-bold">{hours.toString().padStart(2, '0')}</div>
            <div className="text-xs text-gray-500">HOURS</div>
          </div>
          <div>
            <div className="font-bold">{minutes.toString().padStart(2, '0')}</div>
            <div className="text-xs text-gray-500">MINS</div>
          </div>
          <div>
            <div className="font-bold">{seconds.toString().padStart(2, '0')}</div>
            <div className="text-xs text-gray-500">SECS</div>
          </div>
        </div>
      </div>
    )
  }

  const renderStopwatch = () => {
    return (
      <div className="space-y-4">
        <div className={cn('font-mono', getSizeClasses())}>
          {formatStopwatch(stopwatchTime)}
        </div>
        {!isEditing && (
          <div className="flex justify-center space-x-2">
            <button
              onClick={() => setIsStopwatchRunning(!isStopwatchRunning)}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              {isStopwatchRunning ? 'Pause' : 'Start'}
            </button>
            <button
              onClick={() => {
                setStopwatchTime(0)
                setIsStopwatchRunning(false)
              }}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            >
              Reset
            </button>
          </div>
        )}
      </div>
    )
  }

  const renderTimezone = () => {
    const timezoneTime = new Date().toLocaleString('en-US', {
      timeZone: timezone,
      hour: '2-digit',
      minute: '2-digit',
      ...(showSeconds && { second: '2-digit' }),
      hour12: format === '12h'
    })

    return (
      <div className="space-y-2">
        <div className={cn('font-mono', getSizeClasses())}>
          {timezoneTime}
        </div>
        <div className="text-sm text-gray-500">
          {timezone.replace('_', ' ')}
        </div>
      </div>
    )
  }

  const renderContent = () => {
    switch (type) {
      case 'countdown':
        return renderCountdown()
      case 'stopwatch':
        return renderStopwatch()
      case 'timezone':
        return renderTimezone()
      default:
        return renderClock()
    }
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div style={containerStyles}>
        <div className={cn(
          'flex flex-col space-y-3',
          `text-${alignment}`,
          style === 'card' && 'p-6 bg-white rounded-lg shadow-md',
          style === 'minimal' && 'p-2'
        )}>
          {/* Header */}
          {(title || showIcon) && (
            <div className="flex items-center justify-center space-x-2">
              {showIcon && (
                <div style={{ color: color }}>
                  {getIcon()}
                </div>
              )}
              {title && (
                <h3 className="font-semibold">{title}</h3>
              )}
            </div>
          )}

          {/* Main Content */}
          <div className="flex-1">
            {renderContent()}
          </div>

          {/* Date and Timezone */}
          {(showDate || showTimezone) && type === 'clock' && (
            <div className="text-sm text-gray-600 space-y-1">
              {showDate && (
                <div>{formatDate(currentTime)}</div>
              )}
              {showTimezone && timezone && (
                <div className="flex items-center justify-center space-x-1">
                  <MapPin className="h-3 w-3" />
                  <span>{timezone.replace('_', ' ')}</span>
                </div>
              )}
            </div>
          )}
        </div>
        
        {isEditing && (
          <div className="mt-4 p-2 bg-indigo-50 border border-indigo-200 rounded text-xs text-indigo-700">
            <strong>Clock/Timer Widget:</strong> {type} • {format} format • {style} style
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Clock Timer Widget Block Configuration Component
interface ClockTimerWidgetBlockConfigProps {
  config: ClockTimerWidgetBlockConfig
  onChange: (config: ClockTimerWidgetBlockConfig) => void
}

export function ClockTimerWidgetBlockConfig({ config, onChange }: ClockTimerWidgetBlockConfigProps) {
  const updateConfig = (updates: Partial<ClockTimerWidgetBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const commonTimezones = [
    'UTC',
    'America/New_York',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Australia/Sydney',
    'Africa/Johannesburg',
    'America/Sao_Paulo'
  ]

  return (
    <div className="space-y-6">
      {/* Widget Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Widget Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Type</Label>
            <Select
              value={config.type}
              onValueChange={(value) => updateConfig({ type: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="clock">Clock</SelectItem>
                <SelectItem value="countdown">Countdown Timer</SelectItem>
                <SelectItem value="stopwatch">Stopwatch</SelectItem>
                <SelectItem value="timezone">Timezone Clock</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-xs">Title</Label>
            <Input
              value={config.title || ''}
              onChange={(e) => updateConfig({ title: e.target.value })}
              placeholder="Current Time"
              className="mt-1"
            />
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Format</Label>
              <Select
                value={config.format}
                onValueChange={(value) => updateConfig({ format: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="12h">12 Hour</SelectItem>
                  <SelectItem value="24h">24 Hour</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Style</Label>
              <Select
                value={config.style}
                onValueChange={(value) => updateConfig({ style: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="digital">Digital</SelectItem>
                  <SelectItem value="analog">Analog</SelectItem>
                  <SelectItem value="minimal">Minimal</SelectItem>
                  <SelectItem value="card">Card</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label className="text-xs">Size</Label>
            <Select
              value={config.size}
              onValueChange={(value) => updateConfig({ size: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sm">Small</SelectItem>
                <SelectItem value="md">Medium</SelectItem>
                <SelectItem value="lg">Large</SelectItem>
                <SelectItem value="xl">Extra Large</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Icon</Label>
              <Switch
                checked={config.showIcon}
                onCheckedChange={(checked) => updateConfig({ showIcon: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Seconds</Label>
              <Switch
                checked={config.showSeconds}
                onCheckedChange={(checked) => updateConfig({ showSeconds: checked })}
              />
            </div>
            {config.type === 'clock' && (
              <>
                <div className="flex items-center justify-between">
                  <Label className="text-xs">Show Date</Label>
                  <Switch
                    checked={config.showDate}
                    onCheckedChange={(checked) => updateConfig({ showDate: checked })}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label className="text-xs">Show Timezone</Label>
                  <Switch
                    checked={config.showTimezone}
                    onCheckedChange={(checked) => updateConfig({ showTimezone: checked })}
                  />
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Type-specific Settings */}
      {(config.type === 'timezone' || (config.type === 'clock' && config.showTimezone)) && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Timezone Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-xs">Timezone</Label>
              <Select
                value={config.timezone}
                onValueChange={(value) => updateConfig({ timezone: value })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {commonTimezones.map(tz => (
                    <SelectItem key={tz} value={tz}>
                      {tz.replace('_', ' ')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      )}

      {config.type === 'countdown' && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Countdown Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-xs">Target Date & Time</Label>
              <Input
                type="datetime-local"
                value={config.countdownTarget || ''}
                onChange={(e) => updateConfig({ countdownTarget: e.target.value })}
                className="mt-1"
              />
            </div>
            <div>
              <Label className="text-xs">Completion Message</Label>
              <Input
                value={config.countdownMessage || ''}
                onChange={(e) => updateConfig({ countdownMessage: e.target.value })}
                placeholder="Time's up!"
                className="mt-1"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Styling */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Styling</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Text Color</Label>
            <Input
              type="color"
              value={config.color}
              onChange={(e) => updateConfig({ color: e.target.value })}
              className="mt-1 h-10"
            />
          </div>
          <div>
            <Label className="text-xs">Background Color</Label>
            <Input
              type="color"
              value={config.backgroundColor}
              onChange={(e) => updateConfig({ backgroundColor: e.target.value })}
              className="mt-1 h-10"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
