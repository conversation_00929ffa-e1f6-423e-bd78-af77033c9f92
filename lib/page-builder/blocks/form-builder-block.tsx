'use client'

import React, { useState } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Plus, Trash2, Send, CheckCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FormBuilderBlockConfig {
  title?: string
  description?: string
  fields: FormField[]
  submitButtonText: string
  successMessage: string
  errorMessage: string
  redirectUrl?: string
  emailNotification: {
    enabled: boolean
    recipient: string
    subject: string
  }
  layout: 'single-column' | 'two-column'
  fieldSpacing: 'compact' | 'normal' | 'relaxed'
  showRequiredIndicator: boolean
  backgroundColor: string
  borderRadius: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
  maxWidth: string
  alignment: 'left' | 'center' | 'right'
}

interface FormField {
  id: string
  type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file' | 'date' | 'number'
  label: string
  placeholder?: string
  required: boolean
  validation?: {
    minLength?: number
    maxLength?: number
    pattern?: string
    min?: number
    max?: number
  }
  options?: string[] // For select, radio, checkbox
  defaultValue?: string
  helpText?: string
  width: 'full' | 'half'
  order: number
}

interface FormBuilderBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function FormBuilderBlock({ block, isEditing = false }: FormBuilderBlockProps) {
  const config = block.configuration as FormBuilderBlockConfig
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const {
    title,
    description,
    fields,
    submitButtonText,
    successMessage,
    errorMessage,
    redirectUrl,
    emailNotification,
    layout,
    fieldSpacing,
    showRequiredIndicator,
    backgroundColor,
    borderRadius,
    padding,
    maxWidth,
    alignment,
  } = config

  const getSpacingClass = () => {
    const spacingClasses = {
      compact: 'space-y-3',
      normal: 'space-y-4',
      relaxed: 'space-y-6'
    }
    return spacingClasses[fieldSpacing]
  }

  const getAlignmentClass = () => {
    const alignmentClasses = {
      left: 'mr-auto',
      center: 'mx-auto',
      right: 'ml-auto'
    }
    return alignmentClasses[alignment]
  }

  const validateField = (field: FormField, value: any): string | null => {
    if (field.required && (!value || value.toString().trim() === '')) {
      return `${field.label} is required`
    }

    if (field.validation) {
      const { minLength, maxLength, pattern, min, max } = field.validation
      const stringValue = value?.toString() || ''

      if (minLength && stringValue.length < minLength) {
        return `${field.label} must be at least ${minLength} characters`
      }

      if (maxLength && stringValue.length > maxLength) {
        return `${field.label} must be no more than ${maxLength} characters`
      }

      if (pattern && !new RegExp(pattern).test(stringValue)) {
        return `${field.label} format is invalid`
      }

      if (field.type === 'number') {
        const numValue = parseFloat(value)
        if (min !== undefined && numValue < min) {
          return `${field.label} must be at least ${min}`
        }
        if (max !== undefined && numValue > max) {
          return `${field.label} must be no more than ${max}`
        }
      }
    }

    if (field.type === 'email' && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(value)) {
        return 'Please enter a valid email address'
      }
    }

    return null
  }

  const handleFieldChange = (fieldId: string, value: any) => {
    setFormData(prev => ({ ...prev, [fieldId]: value }))
    
    // Clear error when user starts typing
    if (errors[fieldId]) {
      setErrors(prev => ({ ...prev, [fieldId]: '' }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (isEditing) return

    // Validate all fields
    const newErrors: Record<string, string> = {}
    fields.forEach(field => {
      const error = validateField(field, formData[field.id])
      if (error) {
        newErrors[field.id] = error
      }
    })

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }

    setIsSubmitting(true)

    try {
      // Submit form data
      const response = await fetch('/api/forms/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          formData,
          emailNotification,
          blockId: block.id
        }),
      })

      if (response.ok) {
        setIsSubmitted(true)
        setFormData({})
        
        if (redirectUrl) {
          setTimeout(() => {
            window.location.href = redirectUrl
          }, 2000)
        }
      } else {
        throw new Error('Form submission failed')
      }
    } catch (error) {
      console.error('Form submission error:', error)
      setErrors({ general: errorMessage || 'An error occurred. Please try again.' })
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderField = (field: FormField) => {
    const fieldValue = formData[field.id] || field.defaultValue || ''
    const fieldError = errors[field.id]

    const fieldProps = {
      id: field.id,
      name: field.id,
      placeholder: field.placeholder,
      required: field.required,
      className: cn('w-full', fieldError && 'border-red-500'),
      value: fieldValue,
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => 
        handleFieldChange(field.id, e.target.value)
    }

    const renderFieldInput = () => {
      switch (field.type) {
        case 'textarea':
          return (
            <Textarea
              {...fieldProps}
              rows={4}
            />
          )

        case 'select':
          return (
            <Select
              value={fieldValue}
              onValueChange={(value) => handleFieldChange(field.id, value)}
            >
              <SelectTrigger className={cn('w-full', fieldError && 'border-red-500')}>
                <SelectValue placeholder={field.placeholder || 'Select an option'} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )

        case 'checkbox':
          return (
            <div className="space-y-2">
              {field.options?.map((option) => (
                <div key={option} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${field.id}-${option}`}
                    checked={Array.isArray(fieldValue) ? fieldValue.includes(option) : false}
                    onCheckedChange={(checked) => {
                      const currentValues = Array.isArray(fieldValue) ? fieldValue : []
                      const newValues = checked
                        ? [...currentValues, option]
                        : currentValues.filter(v => v !== option)
                      handleFieldChange(field.id, newValues)
                    }}
                  />
                  <Label htmlFor={`${field.id}-${option}`} className="text-sm">
                    {option}
                  </Label>
                </div>
              ))}
            </div>
          )

        case 'radio':
          return (
            <RadioGroup
              value={fieldValue}
              onValueChange={(value) => handleFieldChange(field.id, value)}
            >
              {field.options?.map((option) => (
                <div key={option} className="flex items-center space-x-2">
                  <RadioGroupItem value={option} id={`${field.id}-${option}`} />
                  <Label htmlFor={`${field.id}-${option}`} className="text-sm">
                    {option}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          )

        case 'file':
          return (
            <Input
              type="file"
              id={field.id}
              name={field.id}
              className={cn('w-full', fieldError && 'border-red-500')}
              onChange={(e) => handleFieldChange(field.id, e.target.files?.[0])}
            />
          )

        default:
          return (
            <Input
              {...fieldProps}
              type={field.type}
            />
          )
      }
    }

    return (
      <div
        key={field.id}
        className={cn(
          field.width === 'half' && layout === 'two-column' ? 'col-span-1' : 'col-span-2'
        )}
      >
        <Label htmlFor={field.id} className="text-sm font-medium">
          {field.label}
          {field.required && showRequiredIndicator && (
            <span className="text-red-500 ml-1">*</span>
          )}
        </Label>
        
        <div className="mt-1">
          {renderFieldInput()}
        </div>

        {field.helpText && (
          <p className="text-xs text-gray-500 mt-1">{field.helpText}</p>
        )}

        {fieldError && (
          <p className="text-xs text-red-500 mt-1">{fieldError}</p>
        )}
      </div>
    )
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
    maxWidth: maxWidth || '100%',
  }

  // Demo fields for editing mode
  const demoFields: FormField[] = [
    {
      id: 'demo-name',
      type: 'text',
      label: 'Full Name',
      placeholder: 'Enter your full name',
      required: true,
      width: 'full',
      order: 1
    },
    {
      id: 'demo-email',
      type: 'email',
      label: 'Email Address',
      placeholder: '<EMAIL>',
      required: true,
      width: 'half',
      order: 2
    },
    {
      id: 'demo-phone',
      type: 'tel',
      label: 'Phone Number',
      placeholder: '+27 11 123 4567',
      required: false,
      width: 'half',
      order: 3
    },
    {
      id: 'demo-message',
      type: 'textarea',
      label: 'Message',
      placeholder: 'Tell us how we can help you...',
      required: true,
      width: 'full',
      order: 4
    }
  ]

  const fieldsToRender = isEditing ? demoFields : fields.sort((a, b) => a.order - b.order)

  if (isSubmitted && !isEditing) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <div style={containerStyles}>
          <div className={cn('w-full', getAlignmentClass())}>
            <div className="text-center py-8">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Thank You!</h3>
              <p className="text-gray-600">{successMessage}</p>
              {redirectUrl && (
                <p className="text-sm text-gray-500 mt-2">
                  Redirecting you shortly...
                </p>
              )}
            </div>
          </div>
        </div>
      </BaseBlock>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div style={containerStyles}>
        <div className={cn('w-full', getAlignmentClass())}>
          {/* Form Header */}
          {(title || description) && (
            <div className="mb-6">
              {title && (
                <h2 className="text-2xl font-semibold mb-2">{title}</h2>
              )}
              {description && (
                <p className="text-gray-600">{description}</p>
              )}
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* General Error */}
            {errors.general && (
              <div className="p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                {errors.general}
              </div>
            )}

            {/* Form Fields */}
            <div className={cn(
              'grid gap-4',
              layout === 'two-column' ? 'grid-cols-2' : 'grid-cols-1',
              getSpacingClass()
            )}>
              {fieldsToRender.map(renderField)}
            </div>

            {/* Submit Button */}
            <div className="pt-4">
              <Button
                type="submit"
                disabled={isSubmitting || isEditing}
                className="w-full sm:w-auto"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    {submitButtonText}
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
        
        {isEditing && (
          <div className="mt-4 p-2 bg-green-50 border border-green-200 rounded text-xs text-green-700">
            <strong>Form Builder Block:</strong> {fieldsToRender.length} fields • {layout} layout
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Form Builder Block Configuration Component
interface FormBuilderBlockConfigProps {
  config: FormBuilderBlockConfig
  onChange: (config: FormBuilderBlockConfig) => void
}

export function FormBuilderBlockConfig({ config, onChange }: FormBuilderBlockConfigProps) {
  const updateConfig = (updates: Partial<FormBuilderBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const addField = () => {
    const newField: FormField = {
      id: `field-${Date.now()}`,
      type: 'text',
      label: 'New Field',
      required: false,
      width: 'full',
      order: config.fields.length + 1
    }
    
    updateConfig({
      fields: [...config.fields, newField]
    })
  }

  const updateField = (index: number, updates: Partial<FormField>) => {
    const updatedFields = [...config.fields]
    updatedFields[index] = { ...updatedFields[index], ...updates }
    updateConfig({ fields: updatedFields })
  }

  const removeField = (index: number) => {
    const updatedFields = config.fields.filter((_, i) => i !== index)
    updateConfig({ fields: updatedFields })
  }

  return (
    <div className="space-y-6">
      {/* Form Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Form Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Form Title</Label>
            <Input
              value={config.title || ''}
              onChange={(e) => updateConfig({ title: e.target.value })}
              placeholder="Contact Us"
              className="mt-1"
            />
          </div>

          <div>
            <Label className="text-xs">Description</Label>
            <Textarea
              value={config.description || ''}
              onChange={(e) => updateConfig({ description: e.target.value })}
              placeholder="Form description"
              className="mt-1"
              rows={2}
            />
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Layout</Label>
              <Select
                value={config.layout}
                onValueChange={(value) => updateConfig({ layout: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="single-column">Single Column</SelectItem>
                  <SelectItem value="two-column">Two Column</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Field Spacing</Label>
              <Select
                value={config.fieldSpacing}
                onValueChange={(value) => updateConfig({ fieldSpacing: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="compact">Compact</SelectItem>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="relaxed">Relaxed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label className="text-xs">Submit Button Text</Label>
            <Input
              value={config.submitButtonText}
              onChange={(e) => updateConfig({ submitButtonText: e.target.value })}
              placeholder="Send Message"
              className="mt-1"
            />
          </div>
        </CardContent>
      </Card>

      {/* Form Fields */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm">Form Fields</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={addField}
            className="h-8 px-2"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Field
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.fields.map((field, index) => (
            <div key={field.id} className="border rounded-lg p-3 space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Field {index + 1}</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeField(index)}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Field Type</Label>
                  <Select
                    value={field.type}
                    onValueChange={(value) => updateField(index, { type: value as any })}
                  >
                    <SelectTrigger className="w-full mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="text">Text</SelectItem>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="tel">Phone</SelectItem>
                      <SelectItem value="textarea">Textarea</SelectItem>
                      <SelectItem value="select">Select</SelectItem>
                      <SelectItem value="checkbox">Checkbox</SelectItem>
                      <SelectItem value="radio">Radio</SelectItem>
                      <SelectItem value="file">File</SelectItem>
                      <SelectItem value="date">Date</SelectItem>
                      <SelectItem value="number">Number</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-xs">Width</Label>
                  <Select
                    value={field.width}
                    onValueChange={(value) => updateField(index, { width: value as any })}
                  >
                    <SelectTrigger className="w-full mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="full">Full Width</SelectItem>
                      <SelectItem value="half">Half Width</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <Label className="text-xs">Label</Label>
                <Input
                  value={field.label}
                  onChange={(e) => updateField(index, { label: e.target.value })}
                  className="mt-1"
                  placeholder="Field label"
                />
              </div>

              <div>
                <Label className="text-xs">Placeholder</Label>
                <Input
                  value={field.placeholder || ''}
                  onChange={(e) => updateField(index, { placeholder: e.target.value })}
                  className="mt-1"
                  placeholder="Field placeholder"
                />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-xs">Required</Label>
                <Switch
                  checked={field.required}
                  onCheckedChange={(checked) => updateField(index, { required: checked })}
                />
              </div>
            </div>
          ))}
          
          {config.fields.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              <p className="text-sm">No fields yet.</p>
              <p className="text-xs">Click "Add Field" to get started.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
