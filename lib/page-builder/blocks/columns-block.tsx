'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { cn } from '@/lib/utils'

interface ColumnsBlockConfig {
  columnCount: 2 | 3 | 4 | 6
  gap: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  verticalAlignment: 'top' | 'center' | 'bottom' | 'stretch'
  equalHeight: boolean
  stackOnMobile: boolean
  reverseOnMobile: boolean
  backgroundColor: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
  borderRadius: string
  columns: ColumnConfig[]
}

interface ColumnConfig {
  id: string
  width?: string // Custom width override
  backgroundColor?: string
  padding?: {
    top: string
    right: string
    bottom: string
    left: string
  }
  content: string
  alignment: 'left' | 'center' | 'right'
}

interface ColumnsBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function ColumnsBlock({ block, isEditing = false }: ColumnsBlockProps) {
  const config = block.configuration as ColumnsBlockConfig
  
  const {
    columnCount,
    gap,
    verticalAlignment,
    equalHeight,
    stackOnMobile,
    reverseOnMobile,
    backgroundColor,
    padding,
    borderRadius,
    columns,
  } = config

  const getGapClass = () => {
    const gapClasses = {
      none: 'gap-0',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8'
    }
    return gapClasses[gap]
  }

  const getAlignmentClass = () => {
    const alignmentClasses = {
      top: 'items-start',
      center: 'items-center',
      bottom: 'items-end',
      stretch: 'items-stretch'
    }
    return alignmentClasses[verticalAlignment]
  }

  const getColumnClasses = () => {
    const baseClasses = 'flex-1'
    const responsiveClasses = {
      2: 'md:w-1/2',
      3: 'md:w-1/3',
      4: 'md:w-1/4 lg:w-1/4',
      6: 'md:w-1/2 lg:w-1/3 xl:w-1/6'
    }
    
    return cn(
      baseClasses,
      responsiveClasses[columnCount],
      stackOnMobile && 'w-full',
      equalHeight && 'h-full'
    )
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
  }

  // Generate demo content for editing mode
  const getDemoContent = (index: number) => {
    const demoContents = [
      '<h3>Column 1</h3><p>This is the first column with some sample content. You can add any content here including text, images, and other elements.</p>',
      '<h3>Column 2</h3><p>This is the second column. Columns automatically adjust their width based on the number of columns selected.</p>',
      '<h3>Column 3</h3><p>This is the third column. The layout is fully responsive and will stack on mobile devices.</p>',
      '<h3>Column 4</h3><p>This is the fourth column. You can customize spacing, alignment, and styling for each column.</p>',
      '<h3>Column 5</h3><p>This is the fifth column with additional content for demonstration purposes.</p>',
      '<h3>Column 6</h3><p>This is the sixth column, perfect for grid layouts and feature showcases.</p>'
    ]
    return demoContents[index] || `<h3>Column ${index + 1}</h3><p>Column content goes here.</p>`
  }

  const renderColumns = () => {
    const columnsToRender = isEditing 
      ? Array.from({ length: columnCount }, (_, i) => ({
          id: `col-${i}`,
          content: getDemoContent(i),
          alignment: 'left' as const,
        }))
      : columns.slice(0, columnCount)

    return columnsToRender.map((column, index) => {
      const columnStyles = {
        backgroundColor: column.backgroundColor || 'transparent',
        paddingTop: column.padding?.top || '0',
        paddingRight: column.padding?.right || '0',
        paddingBottom: column.padding?.bottom || '0',
        paddingLeft: column.padding?.left || '0',
        width: column.width || 'auto',
      }

      return (
        <div
          key={column.id}
          className={cn(
            getColumnClasses(),
            `text-${column.alignment}`,
            isEditing && 'min-h-[120px] border border-dashed border-gray-300 rounded'
          )}
          style={columnStyles}
        >
          {isEditing ? (
            <div className="p-4">
              <div 
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{ __html: column.content }}
              />
            </div>
          ) : (
            <div 
              className="prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{ __html: column.content }}
            />
          )}
        </div>
      )
    })
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div style={containerStyles}>
        <div className={cn(
          'flex',
          getGapClass(),
          getAlignmentClass(),
          stackOnMobile && 'flex-col md:flex-row',
          reverseOnMobile && 'flex-col-reverse md:flex-row',
          !stackOnMobile && 'flex-wrap'
        )}>
          {renderColumns()}
        </div>
        
        {isEditing && (
          <div className="mt-4 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700">
            <strong>Columns Block:</strong> {columnCount} columns with {gap} gap spacing
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Columns Block Configuration Component
interface ColumnsBlockConfigProps {
  config: ColumnsBlockConfig
  onChange: (config: ColumnsBlockConfig) => void
}

export function ColumnsBlockConfig({ config, onChange }: ColumnsBlockConfigProps) {
  const updateConfig = (updates: Partial<ColumnsBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const updatePadding = (side: keyof ColumnsBlockConfig['padding'], value: string) => {
    updateConfig({
      padding: {
        ...config.padding,
        [side]: value
      }
    })
  }

  return (
    <div className="space-y-6">
      {/* Layout Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Layout Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Number of Columns</Label>
            <Select
              value={config.columnCount.toString()}
              onValueChange={(value) => updateConfig({ columnCount: parseInt(value) as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2">2 Columns</SelectItem>
                <SelectItem value="3">3 Columns</SelectItem>
                <SelectItem value="4">4 Columns</SelectItem>
                <SelectItem value="6">6 Columns</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-xs">Gap Size</Label>
            <Select
              value={config.gap}
              onValueChange={(value) => updateConfig({ gap: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">No Gap</SelectItem>
                <SelectItem value="sm">Small</SelectItem>
                <SelectItem value="md">Medium</SelectItem>
                <SelectItem value="lg">Large</SelectItem>
                <SelectItem value="xl">Extra Large</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-xs">Vertical Alignment</Label>
            <Select
              value={config.verticalAlignment}
              onValueChange={(value) => updateConfig({ verticalAlignment: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="top">Top</SelectItem>
                <SelectItem value="center">Center</SelectItem>
                <SelectItem value="bottom">Bottom</SelectItem>
                <SelectItem value="stretch">Stretch</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Equal Height</Label>
              <Switch
                checked={config.equalHeight}
                onCheckedChange={(checked) => updateConfig({ equalHeight: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Stack on Mobile</Label>
              <Switch
                checked={config.stackOnMobile}
                onCheckedChange={(checked) => updateConfig({ stackOnMobile: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Reverse on Mobile</Label>
              <Switch
                checked={config.reverseOnMobile}
                onCheckedChange={(checked) => updateConfig({ reverseOnMobile: checked })}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
