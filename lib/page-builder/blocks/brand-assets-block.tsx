'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Download, Copy, Check } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Logo } from '@/components/logo'

interface BrandAsset {
  id: string
  name: string
  type: 'logo' | 'color' | 'typography' | 'icon' | 'pattern'
  variants?: {
    name: string
    description: string
    preview: React.ReactNode | string
    downloadUrl?: string
    copyValue?: string
  }[]
  colors?: {
    name: string
    hex: string
    rgb: string
    usage: string
  }[]
  fonts?: {
    name: string
    weights: string[]
    usage: string
    preview: string
  }[]
}

interface BrandAssetsConfig {
  title: string
  subtitle?: string
  description?: string
  assets: BrandAsset[]
  layout: 'grid' | 'sections'
  columns: 2 | 3 | 4
  showDownloadButtons: boolean
  showCopyButtons: boolean
  spacing: 'compact' | 'normal' | 'spacious'
  backgroundColor: string
  textColor: string
}

interface BrandAssetsBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function BrandAssetsBlock({ block, isEditing = false }: BrandAssetsBlockProps) {
  const config = block.configuration as BrandAssetsConfig

  const {
    title,
    subtitle,
    description,
    assets = [],
    layout = 'sections',
    columns = 3,
    showDownloadButtons = true,
    showCopyButtons = true,
    spacing = 'normal',
    backgroundColor = 'transparent',
    textColor = 'inherit'
  } = config

  const [copiedValue, setCopiedValue] = React.useState<string | null>(null)

  const getSpacingClasses = () => {
    switch (spacing) {
      case 'compact':
        return 'py-6 gap-6'
      case 'spacious':
        return 'py-16 gap-16'
      default:
        return 'py-12 gap-12'
    }
  }

  const getColumnClasses = () => {
    switch (columns) {
      case 2:
        return 'grid-cols-1 md:grid-cols-2'
      case 4:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
      default:
        return 'grid-cols-1 md:grid-cols-3'
    }
  }

  const handleCopy = async (value: string) => {
    try {
      await navigator.clipboard.writeText(value)
      setCopiedValue(value)
      setTimeout(() => setCopiedValue(null), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const renderLogoVariants = (asset: BrandAsset) => (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8 p-8 border rounded-lg">
      {asset.variants?.map((variant, index) => (
        <div key={index} className="flex flex-col items-center gap-4">
          <div className="bg-white p-8 rounded-lg shadow-sm border min-h-[120px] flex items-center justify-center">
            {typeof variant.preview === 'string' ? (
              <img src={variant.preview} alt={variant.name} className="max-h-16" />
            ) : (
              variant.preview
            )}
          </div>
          <div className="text-center">
            <span className="text-sm font-light">{variant.name}</span>
            {variant.description && (
              <p className="text-xs text-muted-foreground mt-1">{variant.description}</p>
            )}
          </div>
          {showDownloadButtons && variant.downloadUrl && (
            <Button variant="outline" size="sm" asChild>
              <a href={variant.downloadUrl} download>
                <Download className="h-3 w-3 mr-1" />
                Download
              </a>
            </Button>
          )}
        </div>
      ))}
    </div>
  )

  const renderColorPalette = (asset: BrandAsset) => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {asset.colors?.map((color, index) => (
        <Card key={index}>
          <CardContent className="p-4">
            <div 
              className="w-full h-20 rounded-lg mb-3 border"
              style={{ backgroundColor: color.hex }}
            />
            <div className="space-y-2">
              <h4 className="font-medium">{color.name}</h4>
              <div className="space-y-1 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">HEX:</span>
                  <div className="flex items-center gap-2">
                    <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                      {color.hex}
                    </code>
                    {showCopyButtons && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCopy(color.hex)}
                        className="h-6 w-6 p-0"
                      >
                        {copiedValue === color.hex ? (
                          <Check className="h-3 w-3 text-green-600" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </Button>
                    )}
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">RGB:</span>
                  <div className="flex items-center gap-2">
                    <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                      {color.rgb}
                    </code>
                    {showCopyButtons && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCopy(color.rgb)}
                        className="h-6 w-6 p-0"
                      >
                        {copiedValue === color.rgb ? (
                          <Check className="h-3 w-3 text-green-600" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
              <p className="text-xs text-muted-foreground">{color.usage}</p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )

  const renderTypography = (asset: BrandAsset) => (
    <div className="space-y-6">
      {asset.fonts?.map((font, index) => (
        <Card key={index}>
          <CardContent className="p-6">
            <div className="grid md:grid-cols-2 gap-6 items-center">
              <div>
                <h4 className="font-medium mb-2">{font.name}</h4>
                <p className="text-sm text-muted-foreground mb-3">{font.usage}</p>
                <div className="flex flex-wrap gap-2">
                  {font.weights.map((weight) => (
                    <span
                      key={weight}
                      className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
                    >
                      {weight}
                    </span>
                  ))}
                </div>
              </div>
              <div className="text-right">
                <div 
                  className="text-2xl mb-2"
                  style={{ fontFamily: font.name }}
                >
                  {font.preview}
                </div>
                <div className="text-sm text-muted-foreground">
                  The quick brown fox jumps over the lazy dog
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )

  const renderAsset = (asset: BrandAsset) => {
    switch (asset.type) {
      case 'logo':
        return renderLogoVariants(asset)
      case 'color':
        return renderColorPalette(asset)
      case 'typography':
        return renderTypography(asset)
      default:
        return (
          <div className="p-8 border rounded-lg text-center">
            <p className="text-muted-foreground">Asset type not supported</p>
          </div>
        )
    }
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div 
        className="container px-4 md:px-6"
        style={{ 
          backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : undefined,
          color: textColor !== 'inherit' ? textColor : undefined
        }}
      >
        <div className={getSpacingClasses()}>
          {/* Header */}
          <div className="mb-8">
            {title && (
              <h1 className="text-3xl font-light mb-4 tracking-wide">
                {title}
              </h1>
            )}
            {subtitle && (
              <h2 className="text-xl text-muted-foreground mb-4">
                {subtitle}
              </h2>
            )}
            {description && (
              <p className="text-muted-foreground max-w-2xl">
                {description}
              </p>
            )}
          </div>

          {/* Assets */}
          {layout === 'grid' ? (
            <div className={cn('grid gap-8', getColumnClasses())}>
              {assets.map((asset) => (
                <div key={asset.id}>
                  <h3 className="text-xl font-light mb-4 tracking-wide">
                    {asset.name}
                  </h3>
                  {renderAsset(asset)}
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-12">
              {assets.map((asset) => (
                <section key={asset.id}>
                  <h2 className="text-xl font-light mb-6 tracking-wide">
                    {asset.name}
                  </h2>
                  {renderAsset(asset)}
                </section>
              ))}
            </div>
          )}
        </div>
      </div>
    </BaseBlock>
  )
}

// Default configuration for the brand assets block
export const brandAssetsBlockConfig = {
  title: 'Brand Assets',
  subtitle: 'Download and use our brand assets',
  description: 'Here you can find our logo variations, color palette, and typography guidelines. Please follow our brand guidelines when using these assets.',
  assets: [
    {
      id: 'logo',
      name: 'Logo Variations',
      type: 'logo',
      variants: [
        {
          name: 'Primary Logo',
          description: 'Use on light backgrounds',
          preview: <Logo className="h-8" />,
          downloadUrl: '/brand/logo-primary.svg'
        },
        {
          name: 'White Logo',
          description: 'Use on dark backgrounds',
          preview: <Logo className="h-8 text-white" />,
          downloadUrl: '/brand/logo-white.svg'
        },
        {
          name: 'Icon Only',
          description: 'Use when space is limited',
          preview: <Logo className="h-8" iconOnly />,
          downloadUrl: '/brand/logo-icon.svg'
        }
      ]
    },
    {
      id: 'colors',
      name: 'Color Palette',
      type: 'color',
      colors: [
        {
          name: 'Primary Blue',
          hex: '#012169',
          rgb: 'rgb(1, 33, 105)',
          usage: 'Primary brand color for headers, buttons, and key elements'
        },
        {
          name: 'Soft Pink',
          hex: '#FFB6C1',
          rgb: 'rgb(255, 182, 193)',
          usage: 'Accent color for highlights and decorative elements'
        },
        {
          name: 'Warm Gray',
          hex: '#6B7280',
          rgb: 'rgb(107, 114, 128)',
          usage: 'Secondary text and subtle elements'
        },
        {
          name: 'Light Gray',
          hex: '#F9FAFB',
          rgb: 'rgb(249, 250, 251)',
          usage: 'Background color for sections and cards'
        }
      ]
    },
    {
      id: 'typography',
      name: 'Typography',
      type: 'typography',
      fonts: [
        {
          name: 'Montserrat',
          weights: ['300 Light', '400 Regular', '500 Medium', '600 SemiBold', '700 Bold'],
          usage: 'Primary font for headings and important text',
          preview: 'Coco Milk Kids'
        },
        {
          name: 'Inter',
          weights: ['300 Light', '400 Regular', '500 Medium'],
          usage: 'Secondary font for body text and descriptions',
          preview: 'The quick brown fox'
        }
      ]
    }
  ],
  layout: 'sections',
  columns: 3,
  showDownloadButtons: true,
  showCopyButtons: true,
  spacing: 'normal',
  backgroundColor: 'transparent',
  textColor: 'inherit'
}

// Configuration schema for the Page Builder
export const brandAssetsBlockSchema = {
  type: 'object',
  properties: {
    title: {
      type: 'string',
      title: 'Title',
      default: 'Brand Assets'
    },
    subtitle: {
      type: 'string',
      title: 'Subtitle (Optional)',
      default: 'Download and use our brand assets'
    },
    description: {
      type: 'string',
      title: 'Description (Optional)',
      format: 'textarea',
      default: brandAssetsBlockConfig.description
    },
    layout: {
      type: 'string',
      title: 'Layout',
      enum: ['grid', 'sections'],
      enumNames: ['Grid Layout', 'Section Layout'],
      default: 'sections'
    },
    columns: {
      type: 'number',
      title: 'Columns (for grid layout)',
      enum: [2, 3, 4],
      default: 3
    },
    showDownloadButtons: {
      type: 'boolean',
      title: 'Show Download Buttons',
      default: true
    },
    showCopyButtons: {
      type: 'boolean',
      title: 'Show Copy Buttons',
      default: true
    },
    spacing: {
      type: 'string',
      title: 'Spacing',
      enum: ['compact', 'normal', 'spacious'],
      enumNames: ['Compact', 'Normal', 'Spacious'],
      default: 'normal'
    },
    backgroundColor: {
      type: 'string',
      title: 'Background Color',
      format: 'color',
      default: 'transparent'
    },
    textColor: {
      type: 'string',
      title: 'Text Color',
      format: 'color',
      default: 'inherit'
    }
  },
  required: ['title']
}
