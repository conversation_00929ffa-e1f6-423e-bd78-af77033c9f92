'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { cn } from '@/lib/utils'
import { 
  Heart, 
  Shield, 
  Leaf, 
  Users, 
  Star, 
  Award, 
  Target, 
  Zap,
  CheckCircle,
  Globe,
  Truck,
  Clock
} from 'lucide-react'

interface ValueItem {
  icon: string
  title: string
  description: string
  iconColor?: string
}

interface ValuesGridConfig {
  title: string
  subtitle?: string
  values: ValueItem[]
  columns: 2 | 3 | 4
  spacing: 'compact' | 'normal' | 'spacious'
  backgroundColor: string
  textColor: string
  iconStyle: 'circle' | 'square' | 'none'
  iconSize: 'small' | 'medium' | 'large'
}

interface ValuesGridBlockProps {
  block: PageBlock
  isEditing?: boolean
}

// Icon mapping for the configuration
const iconMap = {
  heart: Heart,
  shield: Shield,
  leaf: Leaf,
  users: Users,
  star: Star,
  award: Award,
  target: Target,
  zap: Zap,
  'check-circle': CheckCircle,
  globe: Globe,
  truck: Truck,
  clock: Clock
}

export function ValuesGridBlock({ block, isEditing = false }: ValuesGridBlockProps) {
  const config = block.configuration as ValuesGridConfig

  const {
    title,
    subtitle,
    values = [],
    columns = 4,
    spacing = 'normal',
    backgroundColor = 'transparent',
    textColor = 'inherit',
    iconStyle = 'circle',
    iconSize = 'medium'
  } = config

  const getSpacingClasses = () => {
    switch (spacing) {
      case 'compact':
        return 'py-8 gap-6'
      case 'spacious':
        return 'py-16 gap-12'
      default:
        return 'py-12 gap-8'
    }
  }

  const getColumnClasses = () => {
    switch (columns) {
      case 2:
        return 'grid-cols-1 md:grid-cols-2'
      case 3:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
      case 4:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
      default:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
    }
  }

  const getIconSizeClasses = () => {
    switch (iconSize) {
      case 'small':
        return 'h-6 w-6'
      case 'large':
        return 'h-10 w-10'
      default:
        return 'h-8 w-8'
    }
  }

  const getIconContainerClasses = () => {
    const baseClasses = 'flex items-center justify-center mx-auto mb-4'
    const sizeClasses = iconSize === 'small' ? 'w-12 h-12' : iconSize === 'large' ? 'w-20 h-20' : 'w-16 h-16'
    
    switch (iconStyle) {
      case 'square':
        return `${baseClasses} ${sizeClasses} bg-[#012169]/10 rounded-md`
      case 'none':
        return `${baseClasses} ${sizeClasses}`
      default:
        return `${baseClasses} ${sizeClasses} bg-[#012169]/10 rounded-full`
    }
  }

  const renderIcon = (iconName: string, iconColor?: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap]
    if (!IconComponent) return null

    return (
      <IconComponent 
        className={cn(
          getIconSizeClasses(),
          iconColor ? '' : 'text-[#012169]'
        )}
        style={iconColor ? { color: iconColor } : undefined}
      />
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div 
        className="container px-4 md:px-6"
        style={{ 
          backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : undefined,
          color: textColor !== 'inherit' ? textColor : undefined
        }}
      >
        <div className={getSpacingClasses()}>
          {/* Header */}
          {(title || subtitle) && (
            <div className="text-center mb-12">
              {title && (
                <h2 className="text-2xl font-bold font-montserrat mb-4">
                  {title}
                </h2>
              )}
              {subtitle && (
                <p className="text-lg text-muted-foreground font-light max-w-2xl mx-auto">
                  {subtitle}
                </p>
              )}
            </div>
          )}

          {/* Values Grid */}
          <div className={cn('grid', getColumnClasses(), getSpacingClasses())}>
            {values.map((value, index) => (
              <div key={index} className="text-center">
                <div className={getIconContainerClasses()}>
                  {renderIcon(value.icon, value.iconColor)}
                </div>
                <h3 className="font-medium mb-2">{value.title}</h3>
                <p className="text-sm text-muted-foreground font-light">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </BaseBlock>
  )
}

// Default configuration for the values grid block
export const valuesGridBlockConfig = {
  title: 'Our Values',
  subtitle: '',
  values: [
    {
      icon: 'heart',
      title: 'Comfort First',
      description: 'Every piece is designed with your child\'s comfort in mind, using soft, breathable fabrics.',
      iconColor: ''
    },
    {
      icon: 'shield',
      title: 'Quality Promise',
      description: 'Premium materials and expert craftsmanship ensure our clothes last through all adventures.',
      iconColor: ''
    },
    {
      icon: 'leaf',
      title: 'Sustainable Choice',
      description: 'Environmentally conscious production methods and materials for a better tomorrow.',
      iconColor: ''
    },
    {
      icon: 'users',
      title: 'Family Focused',
      description: 'Designed by parents, for parents who want the best for their children.',
      iconColor: ''
    }
  ],
  columns: 4,
  spacing: 'normal',
  backgroundColor: 'transparent',
  textColor: 'inherit',
  iconStyle: 'circle',
  iconSize: 'medium'
}

// Configuration schema for the Page Builder
export const valuesGridBlockSchema = {
  type: 'object',
  properties: {
    title: {
      type: 'string',
      title: 'Section Title',
      default: 'Our Values'
    },
    subtitle: {
      type: 'string',
      title: 'Subtitle (Optional)',
      default: ''
    },
    values: {
      type: 'array',
      title: 'Values',
      items: {
        type: 'object',
        properties: {
          icon: {
            type: 'string',
            title: 'Icon',
            enum: Object.keys(iconMap),
            default: 'heart'
          },
          title: {
            type: 'string',
            title: 'Title'
          },
          description: {
            type: 'string',
            title: 'Description'
          },
          iconColor: {
            type: 'string',
            title: 'Icon Color (Optional)',
            format: 'color'
          }
        },
        required: ['icon', 'title', 'description']
      },
      default: valuesGridBlockConfig.values
    },
    columns: {
      type: 'number',
      title: 'Columns',
      enum: [2, 3, 4],
      default: 4
    },
    spacing: {
      type: 'string',
      title: 'Spacing',
      enum: ['compact', 'normal', 'spacious'],
      enumNames: ['Compact', 'Normal', 'Spacious'],
      default: 'normal'
    },
    iconStyle: {
      type: 'string',
      title: 'Icon Style',
      enum: ['circle', 'square', 'none'],
      enumNames: ['Circle Background', 'Square Background', 'No Background'],
      default: 'circle'
    },
    iconSize: {
      type: 'string',
      title: 'Icon Size',
      enum: ['small', 'medium', 'large'],
      enumNames: ['Small', 'Medium', 'Large'],
      default: 'medium'
    },
    backgroundColor: {
      type: 'string',
      title: 'Background Color',
      format: 'color',
      default: 'transparent'
    },
    textColor: {
      type: 'string',
      title: 'Text Color',
      format: 'color',
      default: 'inherit'
    }
  },
  required: ['title', 'values']
}
