'use client'

import React, { useState } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ChevronDown, ChevronRight, Plus, Trash2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface AccordionBlockConfig {
  items: AccordionItem[]
  allowMultiple: boolean
  defaultOpen: number[] // Indices of items that should be open by default
  style: 'default' | 'bordered' | 'filled' | 'minimal'
  iconPosition: 'left' | 'right'
  iconStyle: 'chevron' | 'plus' | 'arrow'
  spacing: 'none' | 'sm' | 'md' | 'lg'
  backgroundColor: string
  borderRadius: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
}

interface AccordionItem {
  id: string
  title: string
  content: string
  isOpen?: boolean
  backgroundColor?: string
  borderColor?: string
}

interface AccordionBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function AccordionBlock({ block, isEditing = false }: AccordionBlockProps) {
  const config = block.configuration as AccordionBlockConfig
  const [openItems, setOpenItems] = useState<Set<string>>(
    new Set(config.defaultOpen.map(index => config.items[index]?.id).filter(Boolean))
  )

  const {
    items,
    allowMultiple,
    style,
    iconPosition,
    iconStyle,
    spacing,
    backgroundColor,
    borderRadius,
    padding,
  } = config

  const toggleItem = (itemId: string) => {
    if (isEditing) return

    setOpenItems(prev => {
      const newOpenItems = new Set(prev)
      
      if (newOpenItems.has(itemId)) {
        newOpenItems.delete(itemId)
      } else {
        if (!allowMultiple) {
          newOpenItems.clear()
        }
        newOpenItems.add(itemId)
      }
      
      return newOpenItems
    })
  }

  const getSpacingClass = () => {
    const spacingClasses = {
      none: 'space-y-0',
      sm: 'space-y-1',
      md: 'space-y-2',
      lg: 'space-y-4'
    }
    return spacingClasses[spacing]
  }

  const getStyleClasses = (isOpen: boolean) => {
    const styleClasses = {
      default: 'border border-gray-200 bg-white',
      bordered: 'border-2 border-gray-300 bg-white',
      filled: 'bg-gray-50 border border-gray-200',
      minimal: 'border-b border-gray-200 bg-transparent'
    }
    
    return cn(
      styleClasses[style],
      isOpen && style !== 'minimal' && 'shadow-sm',
      style === 'minimal' ? 'rounded-none' : 'rounded-lg'
    )
  }

  const renderIcon = (isOpen: boolean) => {
    const iconProps = {
      className: cn(
        'transition-transform duration-200',
        isOpen && iconStyle === 'chevron' && 'rotate-180',
        isOpen && iconStyle === 'arrow' && 'rotate-90'
      ),
      size: 16
    }

    switch (iconStyle) {
      case 'plus':
        return isOpen ? <Trash2 {...iconProps} /> : <Plus {...iconProps} />
      case 'arrow':
        return <ChevronRight {...iconProps} />
      default:
        return <ChevronDown {...iconProps} />
    }
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
  }

  // Demo items for editing mode
  const demoItems = [
    {
      id: 'demo-1',
      title: 'What is your return policy?',
      content: 'We offer a 30-day return policy for all items in original condition. Returns are free and easy - just contact our customer service team to get started.'
    },
    {
      id: 'demo-2',
      title: 'How long does shipping take?',
      content: 'Standard shipping takes 3-5 business days within South Africa. Express shipping is available for next-day delivery in major cities.'
    },
    {
      id: 'demo-3',
      title: 'Do you offer international shipping?',
      content: 'Yes, we ship internationally to most countries. International shipping typically takes 7-14 business days depending on the destination.'
    }
  ]

  const itemsToRender = isEditing ? demoItems : items

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div style={containerStyles}>
        <div className={getSpacingClass()}>
          {itemsToRender.map((item, index) => {
            const isOpen = isEditing ? index === 0 : openItems.has(item.id)
            
            return (
              <div
                key={item.id}
                className={getStyleClasses(isOpen)}
                style={{
                  backgroundColor: item.backgroundColor,
                  borderColor: item.borderColor,
                }}
              >
                <button
                  className={cn(
                    'w-full px-4 py-3 text-left flex items-center justify-between',
                    'hover:bg-gray-50 transition-colors duration-200',
                    'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset',
                    iconPosition === 'left' && 'flex-row-reverse',
                    style === 'minimal' && 'hover:bg-transparent'
                  )}
                  onClick={() => toggleItem(item.id)}
                  disabled={isEditing}
                >
                  <span className="font-medium text-sm">{item.title}</span>
                  <span className={cn(
                    iconPosition === 'left' ? 'mr-auto ml-0' : 'ml-auto mr-0'
                  )}>
                    {renderIcon(isOpen)}
                  </span>
                </button>
                
                {isOpen && (
                  <div className={cn(
                    'px-4 pb-3',
                    style === 'minimal' && 'border-t border-gray-100 pt-3'
                  )}>
                    <div 
                      className="prose prose-sm max-w-none text-gray-600"
                      dangerouslySetInnerHTML={{ __html: item.content }}
                    />
                  </div>
                )}
              </div>
            )
          })}
        </div>
        
        {isEditing && (
          <div className="mt-4 p-2 bg-indigo-50 border border-indigo-200 rounded text-xs text-indigo-700">
            <strong>Accordion Block:</strong> {itemsToRender.length} items • {allowMultiple ? 'Multiple' : 'Single'} open
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Accordion Block Configuration Component
interface AccordionBlockConfigProps {
  config: AccordionBlockConfig
  onChange: (config: AccordionBlockConfig) => void
}

export function AccordionBlockConfig({ config, onChange }: AccordionBlockConfigProps) {
  const updateConfig = (updates: Partial<AccordionBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const addItem = () => {
    const newItem: AccordionItem = {
      id: `item-${Date.now()}`,
      title: 'New Accordion Item',
      content: 'Add your content here...'
    }
    
    updateConfig({
      items: [...config.items, newItem]
    })
  }

  const updateItem = (index: number, updates: Partial<AccordionItem>) => {
    const updatedItems = [...config.items]
    updatedItems[index] = { ...updatedItems[index], ...updates }
    updateConfig({ items: updatedItems })
  }

  const removeItem = (index: number) => {
    const updatedItems = config.items.filter((_, i) => i !== index)
    updateConfig({ items: updatedItems })
  }

  return (
    <div className="space-y-6">
      {/* Accordion Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Accordion Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Style</Label>
            <Select
              value={config.style}
              onValueChange={(value) => updateConfig({ style: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="default">Default</SelectItem>
                <SelectItem value="bordered">Bordered</SelectItem>
                <SelectItem value="filled">Filled</SelectItem>
                <SelectItem value="minimal">Minimal</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Icon Position</Label>
              <Select
                value={config.iconPosition}
                onValueChange={(value) => updateConfig({ iconPosition: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="right">Right</SelectItem>
                  <SelectItem value="left">Left</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Icon Style</Label>
              <Select
                value={config.iconStyle}
                onValueChange={(value) => updateConfig({ iconStyle: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="chevron">Chevron</SelectItem>
                  <SelectItem value="plus">Plus/Minus</SelectItem>
                  <SelectItem value="arrow">Arrow</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label className="text-xs">Spacing</Label>
            <Select
              value={config.spacing}
              onValueChange={(value) => updateConfig({ spacing: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                <SelectItem value="sm">Small</SelectItem>
                <SelectItem value="md">Medium</SelectItem>
                <SelectItem value="lg">Large</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <Label className="text-xs">Allow Multiple Open</Label>
            <Switch
              checked={config.allowMultiple}
              onCheckedChange={(checked) => updateConfig({ allowMultiple: checked })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Accordion Items */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm">Accordion Items</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={addItem}
            className="h-8 px-2"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Item
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.items.map((item, index) => (
            <div key={item.id} className="border rounded-lg p-3 space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Item {index + 1}</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeItem(index)}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
              
              <div>
                <Label className="text-xs">Title</Label>
                <Input
                  value={item.title}
                  onChange={(e) => updateItem(index, { title: e.target.value })}
                  className="mt-1"
                  placeholder="Accordion item title"
                />
              </div>
              
              <div>
                <Label className="text-xs">Content</Label>
                <Textarea
                  value={item.content}
                  onChange={(e) => updateItem(index, { content: e.target.value })}
                  className="mt-1"
                  rows={2}
                  placeholder="Accordion item content (HTML supported)"
                />
              </div>
            </div>
          ))}
          
          {config.items.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              <p className="text-sm">No accordion items yet.</p>
              <p className="text-xs">Click "Add Item" to get started.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
