'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface CardBlockConfig {
  title: string
  subtitle?: string
  content: string
  image?: {
    src: string
    alt: string
    position: 'top' | 'left' | 'right' | 'background'
    aspectRatio: 'auto' | 'square' | '16/9' | '4/3' | '3/2'
  }
  button?: {
    text: string
    url: string
    style: 'primary' | 'secondary' | 'outline' | 'ghost'
    size: 'sm' | 'md' | 'lg'
  }
  layout: 'vertical' | 'horizontal'
  alignment: 'left' | 'center' | 'right'
  shadow: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  border: {
    enabled: boolean
    width: string
    style: 'solid' | 'dashed' | 'dotted'
    color: string
    radius: string
  }
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
  backgroundColor: string
  hoverEffect: 'none' | 'lift' | 'scale' | 'glow'
  clickable: boolean
  clickUrl?: string
}

interface CardBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function CardBlock({ block, isEditing = false }: CardBlockProps) {
  const config = block.configuration as CardBlockConfig
  
  const {
    title,
    subtitle,
    content,
    image,
    button,
    layout,
    alignment,
    shadow,
    border,
    padding,
    backgroundColor,
    hoverEffect,
    clickable,
    clickUrl,
  } = config

  const getShadowClass = () => {
    const shadowClasses = {
      none: '',
      sm: 'shadow-sm',
      md: 'shadow-md',
      lg: 'shadow-lg',
      xl: 'shadow-xl'
    }
    return shadowClasses[shadow]
  }

  const getHoverEffectClass = () => {
    const hoverClasses = {
      none: '',
      lift: 'hover:shadow-lg hover:-translate-y-1',
      scale: 'hover:scale-105',
      glow: 'hover:shadow-2xl hover:shadow-blue-500/25'
    }
    return hoverClasses[hoverEffect]
  }

  const getImageAspectRatioClass = () => {
    if (!image) return ''
    const ratioClasses = {
      auto: '',
      square: 'aspect-square',
      '16/9': 'aspect-video',
      '4/3': 'aspect-[4/3]',
      '3/2': 'aspect-[3/2]'
    }
    return ratioClasses[image.aspectRatio]
  }

  const cardStyles = {
    backgroundColor: backgroundColor || 'white',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderWidth: border.enabled ? border.width : '0',
    borderStyle: border.enabled ? border.style : 'none',
    borderColor: border.enabled ? border.color : 'transparent',
    borderRadius: border.radius,
  }

  const handleCardClick = () => {
    if (clickable && clickUrl && !isEditing) {
      window.open(clickUrl, '_blank')
    }
  }

  const renderImage = () => {
    if (!image?.src) return null

    const imageElement = (
      <img
        src={image.src}
        alt={image.alt || title}
        className={cn(
          'object-cover w-full',
          getImageAspectRatioClass(),
          image.position === 'top' && 'rounded-t-lg',
          image.position === 'left' && 'rounded-l-lg',
          image.position === 'right' && 'rounded-r-lg'
        )}
      />
    )

    if (image.position === 'background') {
      return (
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat rounded-lg"
          style={{ backgroundImage: `url(${image.src})` }}
        >
          <div className="absolute inset-0 bg-black/40 rounded-lg" />
        </div>
      )
    }

    return imageElement
  }

  const renderContent = () => (
    <div className={cn(
      'relative z-10',
      `text-${alignment}`,
      image?.position === 'background' && 'text-white'
    )}>
      {title && (
        <h3 className={cn(
          'font-semibold mb-2',
          subtitle ? 'text-lg' : 'text-xl'
        )}>
          {title}
        </h3>
      )}
      
      {subtitle && (
        <p className="text-sm text-muted-foreground mb-3">
          {subtitle}
        </p>
      )}
      
      {content && (
        <div 
          className="prose prose-sm max-w-none mb-4"
          dangerouslySetInnerHTML={{ __html: content }}
        />
      )}
      
      {button && (
        <Button
          variant={button.style as any}
          size={button.size}
          className="mt-auto"
          onClick={(e) => {
            if (!isEditing && button.url) {
              e.stopPropagation()
              window.open(button.url, '_blank')
            }
          }}
        >
          {button.text}
        </Button>
      )}
    </div>
  )

  const cardContent = () => {
    if (layout === 'horizontal' && image && image.position !== 'background') {
      return (
        <div className="flex items-start space-x-4">
          {image.position === 'left' && (
            <div className="flex-shrink-0 w-1/3">
              {renderImage()}
            </div>
          )}
          <div className="flex-1">
            {renderContent()}
          </div>
          {image.position === 'right' && (
            <div className="flex-shrink-0 w-1/3">
              {renderImage()}
            </div>
          )}
        </div>
      )
    }

    return (
      <div className="space-y-4">
        {image && image.position === 'top' && renderImage()}
        {renderContent()}
      </div>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div
        className={cn(
          'relative overflow-hidden transition-all duration-300',
          getShadowClass(),
          getHoverEffectClass(),
          clickable && !isEditing && 'cursor-pointer',
          isEditing && 'min-h-[200px]'
        )}
        style={cardStyles}
        onClick={handleCardClick}
      >
        {image?.position === 'background' && renderImage()}
        
        <div className="relative z-10">
          {cardContent()}
        </div>
        
        {isEditing && (
          <div className="absolute top-2 right-2 bg-purple-500 text-white text-xs px-2 py-1 rounded">
            Card Block
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Card Block Configuration Component
interface CardBlockConfigProps {
  config: CardBlockConfig
  onChange: (config: CardBlockConfig) => void
}

export function CardBlockConfig({ config, onChange }: CardBlockConfigProps) {
  const updateConfig = (updates: Partial<CardBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const updateImage = (updates: Partial<CardBlockConfig['image']>) => {
    updateConfig({
      image: {
        ...config.image,
        ...updates
      } as CardBlockConfig['image']
    })
  }

  const updateButton = (updates: Partial<CardBlockConfig['button']>) => {
    updateConfig({
      button: {
        ...config.button,
        ...updates
      } as CardBlockConfig['button']
    })
  }

  const updateBorder = (updates: Partial<CardBlockConfig['border']>) => {
    updateConfig({
      border: {
        ...config.border,
        ...updates
      }
    })
  }

  return (
    <div className="space-y-6">
      {/* Content Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Content</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Title</Label>
            <Input
              value={config.title}
              onChange={(e) => updateConfig({ title: e.target.value })}
              placeholder="Card title"
              className="mt-1"
            />
          </div>

          <div>
            <Label className="text-xs">Subtitle</Label>
            <Input
              value={config.subtitle || ''}
              onChange={(e) => updateConfig({ subtitle: e.target.value })}
              placeholder="Optional subtitle"
              className="mt-1"
            />
          </div>

          <div>
            <Label className="text-xs">Content</Label>
            <Textarea
              value={config.content}
              onChange={(e) => updateConfig({ content: e.target.value })}
              placeholder="Card content (HTML supported)"
              className="mt-1"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Layout Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Layout</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Layout</Label>
            <Select
              value={config.layout}
              onValueChange={(value) => updateConfig({ layout: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="vertical">Vertical</SelectItem>
                <SelectItem value="horizontal">Horizontal</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-xs">Text Alignment</Label>
            <Select
              value={config.alignment}
              onValueChange={(value) => updateConfig({ alignment: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="left">Left</SelectItem>
                <SelectItem value="center">Center</SelectItem>
                <SelectItem value="right">Right</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-xs">Shadow</Label>
            <Select
              value={config.shadow}
              onValueChange={(value) => updateConfig({ shadow: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                <SelectItem value="sm">Small</SelectItem>
                <SelectItem value="md">Medium</SelectItem>
                <SelectItem value="lg">Large</SelectItem>
                <SelectItem value="xl">Extra Large</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-xs">Hover Effect</Label>
            <Select
              value={config.hoverEffect}
              onValueChange={(value) => updateConfig({ hoverEffect: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                <SelectItem value="lift">Lift</SelectItem>
                <SelectItem value="scale">Scale</SelectItem>
                <SelectItem value="glow">Glow</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
