'use client'

import React, { useState } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { Search, ChevronDown, ChevronUp } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FAQItem {
  id: string
  question: string
  answer: string
  category?: string
  tags?: string[]
}

interface FAQAccordionConfig {
  title: string
  subtitle?: string
  description?: string
  faqs: FAQItem[]
  enableSearch: boolean
  searchPlaceholder: string
  categories: string[]
  showCategories: boolean
  defaultOpenItems: string[]
  allowMultiple: boolean
  style: 'default' | 'bordered' | 'minimal' | 'cards'
  spacing: 'compact' | 'normal' | 'spacious'
  backgroundColor: string
  textColor: string
  contactSection: {
    enabled: boolean
    title: string
    description: string
    email: string
    phone: string
  }
}

interface FAQAccordionBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function FAQAccordionBlock({ block, isEditing = false }: FAQAccordionBlockProps) {
  const config = block.configuration as FAQAccordionConfig

  const {
    title,
    subtitle,
    description,
    faqs = [],
    enableSearch = true,
    searchPlaceholder = 'Search FAQs...',
    categories = [],
    showCategories = false,
    defaultOpenItems = [],
    allowMultiple = false,
    style = 'default',
    spacing = 'normal',
    backgroundColor = 'transparent',
    textColor = 'inherit',
    contactSection = {
      enabled: true,
      title: 'Still Have Questions?',
      description: 'Can\'t find what you\'re looking for? Our customer service team is here to help.',
      email: '<EMAIL>',
      phone: '+27 11 123 4567'
    }
  } = config

  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [openItems, setOpenItems] = useState<string[]>(defaultOpenItems)

  const getSpacingClasses = () => {
    switch (spacing) {
      case 'compact':
        return 'py-6 gap-4'
      case 'spacious':
        return 'py-12 gap-8'
      default:
        return 'py-8 gap-6'
    }
  }

  const getStyleClasses = () => {
    switch (style) {
      case 'bordered':
        return 'border rounded-lg'
      case 'minimal':
        return ''
      case 'cards':
        return 'space-y-4'
      default:
        return 'border rounded-lg'
    }
  }

  // Filter FAQs based on search and category
  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = !searchTerm || 
      faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory

    return matchesSearch && matchesCategory
  })

  const handleValueChange = (value: string[]) => {
    if (allowMultiple) {
      setOpenItems(value)
    } else {
      setOpenItems(value.slice(-1)) // Keep only the last opened item
    }
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div 
        className="container px-4 md:px-6"
        style={{ 
          backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : undefined,
          color: textColor !== 'inherit' ? textColor : undefined
        }}
      >
        <div className={getSpacingClasses()}>
          {/* Header */}
          <div className="text-center mb-8">
            {title && (
              <h1 className="text-3xl md:text-4xl font-bold font-montserrat mb-4">
                {title}
              </h1>
            )}
            {subtitle && (
              <h2 className="text-xl text-muted-foreground mb-4">
                {subtitle}
              </h2>
            )}
            {description && (
              <p className="text-lg text-muted-foreground font-light max-w-2xl mx-auto">
                {description}
              </p>
            )}
          </div>

          {/* Search and Categories */}
          {(enableSearch || showCategories) && (
            <div className="mb-8 space-y-4">
              {/* Search */}
              {enableSearch && (
                <div className="max-w-md mx-auto relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    type="text"
                    placeholder={searchPlaceholder}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              )}

              {/* Categories */}
              {showCategories && categories.length > 0 && (
                <div className="flex flex-wrap justify-center gap-2">
                  <Button
                    variant={selectedCategory === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory('all')}
                  >
                    All
                  </Button>
                  {categories.map((category) => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedCategory(category)}
                    >
                      {category}
                    </Button>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* FAQ Accordion */}
          <div className="max-w-4xl mx-auto">
            {filteredFAQs.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-muted-foreground">
                  {searchTerm ? 'No FAQs found matching your search.' : 'No FAQs available.'}
                </p>
              </div>
            ) : (
              <Accordion
                type={allowMultiple ? 'multiple' : 'single'}
                value={openItems}
                onValueChange={handleValueChange}
                className={getStyleClasses()}
              >
                {filteredFAQs.map((faq, index) => (
                  <AccordionItem
                    key={faq.id}
                    value={faq.id}
                    className={cn(
                      style === 'cards' ? 'border rounded-lg p-4 bg-white shadow-sm' : ''
                    )}
                  >
                    <AccordionTrigger className="text-left hover:no-underline">
                      <span className="font-medium">{faq.question}</span>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="text-muted-foreground leading-relaxed">
                        {faq.answer}
                      </div>
                      {faq.tags && faq.tags.length > 0 && (
                        <div className="mt-3 flex flex-wrap gap-1">
                          {faq.tags.map((tag) => (
                            <span
                              key={tag}
                              className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            )}
          </div>

          {/* Contact Section */}
          {contactSection.enabled && (
            <div className="mt-12 text-center bg-gray-50 rounded-lg p-8">
              <h2 className="text-xl font-bold font-montserrat mb-4">
                {contactSection.title}
              </h2>
              <p className="text-muted-foreground mb-4">
                {contactSection.description}
              </p>
              <p className="text-sm">
                Email us at{' '}
                <a 
                  href={`mailto:${contactSection.email}`} 
                  className="text-[#012169] hover:underline"
                >
                  {contactSection.email}
                </a>{' '}
                or call{' '}
                <a 
                  href={`tel:${contactSection.phone.replace(/\s/g, '')}`} 
                  className="text-[#012169] hover:underline"
                >
                  {contactSection.phone}
                </a>
              </p>
            </div>
          )}
        </div>
      </div>
    </BaseBlock>
  )
}

// Default configuration for the FAQ accordion block
export const faqAccordionBlockConfig = {
  title: 'Frequently Asked Questions',
  subtitle: '',
  description: 'Find answers to the most common questions about our products and services.',
  faqs: [
    {
      id: '1',
      question: 'What sizes do you offer?',
      answer: 'We offer sizes from 0-3 months up to 14 years. Our size guide provides detailed measurements to help you find the perfect fit for your child.',
      category: 'Products',
      tags: ['sizing', 'fit', 'measurements']
    },
    {
      id: '2',
      question: 'What is your return policy?',
      answer: 'We offer a 30-day return policy for unworn items with original tags. Returns are free for defective items, and we provide a prepaid return label for your convenience.',
      category: 'Returns',
      tags: ['returns', 'policy', 'refunds']
    },
    {
      id: '3',
      question: 'How long does shipping take?',
      answer: 'Standard shipping takes 3-5 business days, while express shipping takes 1-2 business days. Free standard shipping is available on orders over R1,500.',
      category: 'Shipping',
      tags: ['shipping', 'delivery', 'timing']
    },
    {
      id: '4',
      question: 'Are your clothes made from organic materials?',
      answer: 'Yes, we prioritize sustainable and organic materials. Most of our collection is made from organic cotton and other eco-friendly fabrics that are gentle on your child\'s skin.',
      category: 'Materials',
      tags: ['organic', 'sustainable', 'materials']
    }
  ],
  enableSearch: true,
  searchPlaceholder: 'Search FAQs...',
  categories: ['Products', 'Returns', 'Shipping', 'Materials'],
  showCategories: true,
  defaultOpenItems: [],
  allowMultiple: false,
  style: 'default',
  spacing: 'normal',
  backgroundColor: 'transparent',
  textColor: 'inherit',
  contactSection: {
    enabled: true,
    title: 'Still Have Questions?',
    description: 'Can\'t find what you\'re looking for? Our customer service team is here to help.',
    email: '<EMAIL>',
    phone: '+27 11 123 4567'
  }
}

// Configuration schema for the Page Builder
export const faqAccordionBlockSchema = {
  type: 'object',
  properties: {
    title: {
      type: 'string',
      title: 'Title',
      default: 'Frequently Asked Questions'
    },
    subtitle: {
      type: 'string',
      title: 'Subtitle (Optional)',
      default: ''
    },
    description: {
      type: 'string',
      title: 'Description (Optional)',
      format: 'textarea',
      default: faqAccordionBlockConfig.description
    },
    faqs: {
      type: 'array',
      title: 'FAQ Items',
      items: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            title: 'ID',
            description: 'Unique identifier for the FAQ item'
          },
          question: {
            type: 'string',
            title: 'Question'
          },
          answer: {
            type: 'string',
            title: 'Answer',
            format: 'textarea'
          },
          category: {
            type: 'string',
            title: 'Category (Optional)'
          },
          tags: {
            type: 'array',
            title: 'Tags (Optional)',
            items: {
              type: 'string'
            }
          }
        },
        required: ['id', 'question', 'answer']
      },
      default: faqAccordionBlockConfig.faqs
    },
    enableSearch: {
      type: 'boolean',
      title: 'Enable Search',
      default: true
    },
    searchPlaceholder: {
      type: 'string',
      title: 'Search Placeholder',
      default: 'Search FAQs...'
    },
    categories: {
      type: 'array',
      title: 'Categories',
      items: {
        type: 'string'
      },
      default: faqAccordionBlockConfig.categories
    },
    showCategories: {
      type: 'boolean',
      title: 'Show Category Filter',
      default: true
    },
    allowMultiple: {
      type: 'boolean',
      title: 'Allow Multiple Open Items',
      default: false
    },
    style: {
      type: 'string',
      title: 'Style',
      enum: ['default', 'bordered', 'minimal', 'cards'],
      enumNames: ['Default', 'Bordered', 'Minimal', 'Cards'],
      default: 'default'
    },
    spacing: {
      type: 'string',
      title: 'Spacing',
      enum: ['compact', 'normal', 'spacious'],
      enumNames: ['Compact', 'Normal', 'Spacious'],
      default: 'normal'
    },
    contactSection: {
      type: 'object',
      title: 'Contact Section',
      properties: {
        enabled: {
          type: 'boolean',
          title: 'Show Contact Section',
          default: true
        },
        title: {
          type: 'string',
          title: 'Contact Section Title',
          default: 'Still Have Questions?'
        },
        description: {
          type: 'string',
          title: 'Contact Section Description',
          default: faqAccordionBlockConfig.contactSection.description
        },
        email: {
          type: 'string',
          title: 'Contact Email',
          format: 'email',
          default: '<EMAIL>'
        },
        phone: {
          type: 'string',
          title: 'Contact Phone',
          default: '+27 11 123 4567'
        }
      }
    },
    backgroundColor: {
      type: 'string',
      title: 'Background Color',
      format: 'color',
      default: 'transparent'
    },
    textColor: {
      type: 'string',
      title: 'Text Color',
      format: 'color',
      default: 'inherit'
    }
  },
  required: ['title', 'faqs']
}
