'use client'

import React, { useState, useRef, useEffect } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Maximize, 
  SkipBack, 
  SkipForward,
  Settings,
  ExternalLink
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface VideoPlayerBlockConfig {
  videoType: 'youtube' | 'vimeo' | 'custom' | 'playlist'
  videoUrl: string
  videoId?: string
  title?: string
  description?: string
  thumbnail?: string
  autoplay: boolean
  muted: boolean
  loop: boolean
  controls: boolean
  showTitle: boolean
  showDescription: boolean
  aspectRatio: '16:9' | '4:3' | '1:1' | '21:9' | 'custom'
  customAspectRatio?: {
    width: number
    height: number
  }
  maxWidth: string
  alignment: 'left' | 'center' | 'right'
  playlist?: PlaylistItem[]
  currentVideoIndex: number
  showPlaylist: boolean
  playlistPosition: 'right' | 'bottom'
  backgroundColor: string
  borderRadius: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
}

interface PlaylistItem {
  id: string
  title: string
  url: string
  thumbnail?: string
  duration?: string
  description?: string
}

interface VideoPlayerBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function VideoPlayerBlock({ block, isEditing = false }: VideoPlayerBlockProps) {
  const config = block.configuration as VideoPlayerBlockConfig
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(config.muted)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const {
    videoType,
    videoUrl,
    videoId,
    title,
    description,
    thumbnail,
    autoplay,
    muted,
    loop,
    controls,
    showTitle,
    showDescription,
    aspectRatio,
    customAspectRatio,
    maxWidth,
    alignment,
    playlist,
    currentVideoIndex,
    showPlaylist,
    playlistPosition,
    backgroundColor,
    borderRadius,
    padding,
  } = config

  const getAspectRatioClass = () => {
    const ratioClasses = {
      '16:9': 'aspect-video',
      '4:3': 'aspect-[4/3]',
      '1:1': 'aspect-square',
      '21:9': 'aspect-[21/9]',
      'custom': customAspectRatio ? `aspect-[${customAspectRatio.width}/${customAspectRatio.height}]` : 'aspect-video'
    }
    return ratioClasses[aspectRatio]
  }

  const getAlignmentClass = () => {
    const alignmentClasses = {
      left: 'mr-auto',
      center: 'mx-auto',
      right: 'ml-auto'
    }
    return alignmentClasses[alignment]
  }

  const getEmbedUrl = () => {
    if (videoType === 'youtube' && videoId) {
      const params = new URLSearchParams({
        autoplay: autoplay ? '1' : '0',
        mute: muted ? '1' : '0',
        loop: loop ? '1' : '0',
        controls: controls ? '1' : '0',
        rel: '0',
        modestbranding: '1'
      })
      return `https://www.youtube.com/embed/${videoId}?${params}`
    }
    
    if (videoType === 'vimeo' && videoId) {
      const params = new URLSearchParams({
        autoplay: autoplay ? '1' : '0',
        muted: muted ? '1' : '0',
        loop: loop ? '1' : '0',
        controls: controls ? '1' : '0',
        title: showTitle ? '1' : '0',
        byline: '0',
        portrait: '0'
      })
      return `https://player.vimeo.com/video/${videoId}?${params}`
    }
    
    return videoUrl
  }

  const extractVideoId = (url: string) => {
    // YouTube
    const youtubeMatch = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/)
    if (youtubeMatch) {
      return { type: 'youtube', id: youtubeMatch[1] }
    }
    
    // Vimeo
    const vimeoMatch = url.match(/(?:vimeo\.com\/)([0-9]+)/)
    if (vimeoMatch) {
      return { type: 'vimeo', id: vimeoMatch[1] }
    }
    
    return { type: 'custom', id: null }
  }

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted
      setIsMuted(!isMuted)
    }
  }

  const toggleFullscreen = () => {
    if (containerRef.current) {
      if (!isFullscreen) {
        containerRef.current.requestFullscreen()
      } else {
        document.exitFullscreen()
      }
      setIsFullscreen(!isFullscreen)
    }
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
    maxWidth: maxWidth || '100%',
  }

  const renderCustomVideoPlayer = () => (
    <div className="relative group">
      <video
        ref={videoRef}
        src={videoUrl}
        poster={thumbnail}
        autoPlay={autoplay && !isEditing}
        muted={muted}
        loop={loop}
        controls={controls}
        className="w-full h-full object-cover"
        onTimeUpdate={(e) => setCurrentTime(e.currentTarget.currentTime)}
        onLoadedMetadata={(e) => setDuration(e.currentTarget.duration)}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
      />
      
      {!controls && (
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <div className="flex items-center space-x-4">
            <Button
              variant="secondary"
              size="sm"
              onClick={togglePlay}
              className="bg-white/90 hover:bg-white"
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={toggleMute}
              className="bg-white/90 hover:bg-white"
            >
              {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={toggleFullscreen}
              className="bg-white/90 hover:bg-white"
            >
              <Maximize className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )

  const renderEmbedPlayer = () => (
    <iframe
      src={getEmbedUrl()}
      title={title || 'Video Player'}
      className="w-full h-full"
      frameBorder="0"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      allowFullScreen
    />
  )

  const renderPlaylist = () => {
    if (!showPlaylist || !playlist || playlist.length === 0) return null

    return (
      <div className={cn(
        'bg-gray-50 rounded-lg p-4',
        playlistPosition === 'right' ? 'w-80' : 'w-full mt-4'
      )}>
        <h4 className="font-semibold mb-3 text-sm">Playlist</h4>
        <div className="space-y-2 max-h-64 overflow-y-auto">
          {playlist.map((item, index) => (
            <div
              key={item.id}
              className={cn(
                'flex items-center space-x-3 p-2 rounded cursor-pointer transition-colors',
                index === currentVideoIndex ? 'bg-blue-100 border border-blue-200' : 'hover:bg-gray-100'
              )}
            >
              {item.thumbnail && (
                <img
                  src={item.thumbnail}
                  alt={item.title}
                  className="w-16 h-12 object-cover rounded"
                />
              )}
              <div className="flex-1 min-w-0">
                <h5 className="text-sm font-medium truncate">{item.title}</h5>
                {item.duration && (
                  <p className="text-xs text-gray-500">{item.duration}</p>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div style={containerStyles}>
        <div className={cn('w-full', getAlignmentClass())}>
          {/* Video Header */}
          {(showTitle && title) || (showDescription && description) && (
            <div className="mb-4">
              {showTitle && title && (
                <h3 className="text-xl font-semibold mb-2">{title}</h3>
              )}
              {showDescription && description && (
                <p className="text-gray-600 text-sm">{description}</p>
              )}
            </div>
          )}

          {/* Video Player Container */}
          <div className={cn(
            'flex',
            playlistPosition === 'right' ? 'space-x-4' : 'flex-col'
          )}>
            <div className="flex-1">
              <div
                ref={containerRef}
                className={cn(
                  'relative overflow-hidden bg-black rounded-lg',
                  getAspectRatioClass()
                )}
              >
                {videoType === 'custom' ? renderCustomVideoPlayer() : renderEmbedPlayer()}
                
                {isEditing && (
                  <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                    <div className="text-white text-center">
                      <Play className="h-12 w-12 mx-auto mb-2" />
                      <p className="text-sm">Video Player</p>
                      <p className="text-xs opacity-75">{videoType.toUpperCase()}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Playlist */}
            {playlistPosition === 'right' && renderPlaylist()}
          </div>

          {/* Bottom Playlist */}
          {playlistPosition === 'bottom' && renderPlaylist()}
        </div>
        
        {isEditing && (
          <div className="mt-4 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700">
            <strong>Video Player Block:</strong> {videoType} • {aspectRatio} aspect ratio
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Video Player Block Configuration Component
interface VideoPlayerBlockConfigProps {
  config: VideoPlayerBlockConfig
  onChange: (config: VideoPlayerBlockConfig) => void
}

export function VideoPlayerBlockConfig({ config, onChange }: VideoPlayerBlockConfigProps) {
  const updateConfig = (updates: Partial<VideoPlayerBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const handleUrlChange = (url: string) => {
    const { type, id } = extractVideoId(url)
    updateConfig({
      videoUrl: url,
      videoType: type as any,
      videoId: id || undefined
    })
  }

  const extractVideoId = (url: string) => {
    // YouTube
    const youtubeMatch = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/)
    if (youtubeMatch) {
      return { type: 'youtube', id: youtubeMatch[1] }
    }
    
    // Vimeo
    const vimeoMatch = url.match(/(?:vimeo\.com\/)([0-9]+)/)
    if (vimeoMatch) {
      return { type: 'vimeo', id: vimeoMatch[1] }
    }
    
    return { type: 'custom', id: null }
  }

  return (
    <div className="space-y-6">
      {/* Video Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Video Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Video URL</Label>
            <Input
              value={config.videoUrl}
              onChange={(e) => handleUrlChange(e.target.value)}
              placeholder="https://www.youtube.com/watch?v=... or direct video URL"
              className="mt-1"
            />
            <p className="text-xs text-gray-500 mt-1">
              Supports YouTube, Vimeo, and direct video URLs
            </p>
          </div>

          <div>
            <Label className="text-xs">Video Type</Label>
            <Select
              value={config.videoType}
              onValueChange={(value) => updateConfig({ videoType: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="youtube">YouTube</SelectItem>
                <SelectItem value="vimeo">Vimeo</SelectItem>
                <SelectItem value="custom">Custom/Direct URL</SelectItem>
                <SelectItem value="playlist">Playlist</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-xs">Title</Label>
            <Input
              value={config.title || ''}
              onChange={(e) => updateConfig({ title: e.target.value })}
              placeholder="Video title"
              className="mt-1"
            />
          </div>

          <div>
            <Label className="text-xs">Description</Label>
            <Textarea
              value={config.description || ''}
              onChange={(e) => updateConfig({ description: e.target.value })}
              placeholder="Video description"
              className="mt-1"
              rows={2}
            />
          </div>
        </CardContent>
      </Card>

      {/* Display Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Display Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Aspect Ratio</Label>
            <Select
              value={config.aspectRatio}
              onValueChange={(value) => updateConfig({ aspectRatio: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="16:9">16:9 (Widescreen)</SelectItem>
                <SelectItem value="4:3">4:3 (Standard)</SelectItem>
                <SelectItem value="1:1">1:1 (Square)</SelectItem>
                <SelectItem value="21:9">21:9 (Ultrawide)</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-xs">Alignment</Label>
            <Select
              value={config.alignment}
              onValueChange={(value) => updateConfig({ alignment: value as any })}
            >
              <SelectTrigger className="w-full mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="left">Left</SelectItem>
                <SelectItem value="center">Center</SelectItem>
                <SelectItem value="right">Right</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-xs">Max Width</Label>
            <Input
              value={config.maxWidth}
              onChange={(e) => updateConfig({ maxWidth: e.target.value })}
              placeholder="100% or 800px"
              className="mt-1"
            />
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Title</Label>
              <Switch
                checked={config.showTitle}
                onCheckedChange={(checked) => updateConfig({ showTitle: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Description</Label>
              <Switch
                checked={config.showDescription}
                onCheckedChange={(checked) => updateConfig({ showDescription: checked })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Player Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Player Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-xs">Autoplay</Label>
            <Switch
              checked={config.autoplay}
              onCheckedChange={(checked) => updateConfig({ autoplay: checked })}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label className="text-xs">Muted</Label>
            <Switch
              checked={config.muted}
              onCheckedChange={(checked) => updateConfig({ muted: checked })}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label className="text-xs">Loop</Label>
            <Switch
              checked={config.loop}
              onCheckedChange={(checked) => updateConfig({ loop: checked })}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label className="text-xs">Show Controls</Label>
            <Switch
              checked={config.controls}
              onCheckedChange={(checked) => updateConfig({ controls: checked })}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
