'use client'

import React, { useState } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'

interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'tel' | 'textarea' | 'select'
  required: boolean
  placeholder?: string
  options?: string[]
  width: 'full' | 'half'
}

interface ContactFormConfig {
  title: string
  subtitle?: string
  description?: string
  fields: FormField[]
  submitButtonText: string
  successMessage: string
  errorMessage: string
  emailEndpoint: string
  cardStyle: boolean
  spacing: 'compact' | 'normal' | 'spacious'
  backgroundColor: string
  textColor: string
}

interface ContactFormBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function ContactFormBlock({ block, isEditing = false }: ContactFormBlockProps) {
  const config = block.configuration as ContactFormConfig

  const {
    title,
    subtitle,
    description,
    fields = [],
    submitButtonText = 'Send Message',
    successMessage = 'Thank you! Your message has been sent successfully.',
    errorMessage = 'Sorry, there was an error sending your message. Please try again.',
    emailEndpoint = '/api/contact',
    cardStyle = true,
    spacing = 'normal',
    backgroundColor = 'transparent',
    textColor = 'inherit'
  } = config

  const [formData, setFormData] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const getSpacingClasses = () => {
    switch (spacing) {
      case 'compact':
        return 'py-6'
      case 'spacious':
        return 'py-12'
      default:
        return 'py-8'
    }
  }

  const handleInputChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (isEditing) {
      toast.info('Form submission is disabled in edit mode')
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch(emailEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        toast.success(successMessage)
        setFormData({})
      } else {
        throw new Error('Failed to send message')
      }
    } catch (error) {
      console.error('Error submitting form:', error)
      toast.error(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderField = (field: FormField, index: number) => {
    const value = formData[field.name] || ''

    const fieldElement = (() => {
      switch (field.type) {
        case 'textarea':
          return (
            <Textarea
              id={field.name}
              name={field.name}
              value={value}
              onChange={(e) => handleInputChange(field.name, e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
              rows={4}
            />
          )
        
        case 'select':
          return (
            <Select
              value={value}
              onValueChange={(value) => handleInputChange(field.name, value)}
              required={field.required}
            >
              <SelectTrigger>
                <SelectValue placeholder={field.placeholder || `Select ${field.label}`} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )
        
        default:
          return (
            <Input
              id={field.name}
              name={field.name}
              type={field.type}
              value={value}
              onChange={(e) => handleInputChange(field.name, e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
            />
          )
      }
    })()

    return (
      <div 
        key={index} 
        className={cn(
          'space-y-2',
          field.width === 'half' ? 'md:col-span-1' : 'md:col-span-2'
        )}
      >
        <Label htmlFor={field.name}>
          {field.label} {field.required && '*'}
        </Label>
        {fieldElement}
      </div>
    )
  }

  const formContent = (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Header */}
      {(title || subtitle || description) && (
        <div className="mb-6">
          {title && (
            <h2 className="text-lg font-montserrat font-semibold mb-2">
              {title}
            </h2>
          )}
          {subtitle && (
            <h3 className="text-base text-muted-foreground mb-2">
              {subtitle}
            </h3>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">
              {description}
            </p>
          )}
        </div>
      )}

      {/* Form Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {fields.map((field, index) => renderField(field, index))}
      </div>

      {/* Submit Button */}
      <Button
        type="submit"
        disabled={isSubmitting}
        className="w-full bg-[#012169] hover:bg-[#012169]/90"
      >
        {isSubmitting ? 'Sending...' : submitButtonText}
      </Button>
    </form>
  )

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div 
        className="container px-4 md:px-6"
        style={{ 
          backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : undefined,
          color: textColor !== 'inherit' ? textColor : undefined
        }}
      >
        <div className={getSpacingClasses()}>
          {cardStyle ? (
            <div className="bg-white border rounded-lg p-6 shadow-sm max-w-2xl mx-auto">
              {formContent}
            </div>
          ) : (
            <div className="max-w-2xl mx-auto">
              {formContent}
            </div>
          )}
        </div>
      </div>
    </BaseBlock>
  )
}

// Default configuration for the contact form block
export const contactFormBlockConfig = {
  title: 'Send us a Message',
  subtitle: '',
  description: 'Fill out the form below and we\'ll get back to you as soon as possible.',
  fields: [
    {
      name: 'name',
      label: 'Name',
      type: 'text',
      required: true,
      placeholder: 'Your full name',
      width: 'half'
    },
    {
      name: 'email',
      label: 'Email',
      type: 'email',
      required: true,
      placeholder: '<EMAIL>',
      width: 'half'
    },
    {
      name: 'subject',
      label: 'Subject',
      type: 'select',
      required: true,
      placeholder: 'Select a subject',
      options: [
        'General Inquiry',
        'Product Question',
        'Order Support',
        'Returns & Exchanges',
        'Wholesale Inquiry',
        'Press & Media',
        'Other'
      ],
      width: 'full'
    },
    {
      name: 'message',
      label: 'Message',
      type: 'textarea',
      required: true,
      placeholder: 'Tell us how we can help you...',
      width: 'full'
    }
  ],
  submitButtonText: 'Send Message',
  successMessage: 'Thank you! Your message has been sent successfully.',
  errorMessage: 'Sorry, there was an error sending your message. Please try again.',
  emailEndpoint: '/api/contact',
  cardStyle: true,
  spacing: 'normal',
  backgroundColor: 'transparent',
  textColor: 'inherit'
}

// Configuration schema for the Page Builder
export const contactFormBlockSchema = {
  type: 'object',
  properties: {
    title: {
      type: 'string',
      title: 'Title',
      default: 'Send us a Message'
    },
    subtitle: {
      type: 'string',
      title: 'Subtitle (Optional)',
      default: ''
    },
    description: {
      type: 'string',
      title: 'Description (Optional)',
      format: 'textarea',
      default: contactFormBlockConfig.description
    },
    fields: {
      type: 'array',
      title: 'Form Fields',
      items: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            title: 'Field Name',
            description: 'Unique identifier for the field'
          },
          label: {
            type: 'string',
            title: 'Label'
          },
          type: {
            type: 'string',
            title: 'Field Type',
            enum: ['text', 'email', 'tel', 'textarea', 'select'],
            enumNames: ['Text', 'Email', 'Phone', 'Textarea', 'Select Dropdown'],
            default: 'text'
          },
          required: {
            type: 'boolean',
            title: 'Required Field',
            default: false
          },
          placeholder: {
            type: 'string',
            title: 'Placeholder Text (Optional)'
          },
          options: {
            type: 'array',
            title: 'Select Options (for select type)',
            items: {
              type: 'string'
            }
          },
          width: {
            type: 'string',
            title: 'Field Width',
            enum: ['full', 'half'],
            enumNames: ['Full Width', 'Half Width'],
            default: 'full'
          }
        },
        required: ['name', 'label', 'type']
      },
      default: contactFormBlockConfig.fields
    },
    submitButtonText: {
      type: 'string',
      title: 'Submit Button Text',
      default: 'Send Message'
    },
    successMessage: {
      type: 'string',
      title: 'Success Message',
      default: contactFormBlockConfig.successMessage
    },
    errorMessage: {
      type: 'string',
      title: 'Error Message',
      default: contactFormBlockConfig.errorMessage
    },
    emailEndpoint: {
      type: 'string',
      title: 'Email Endpoint',
      description: 'API endpoint to handle form submissions',
      default: '/api/contact'
    },
    cardStyle: {
      type: 'boolean',
      title: 'Card Style',
      description: 'Wrap form in a card with border and shadow',
      default: true
    },
    spacing: {
      type: 'string',
      title: 'Spacing',
      enum: ['compact', 'normal', 'spacious'],
      enumNames: ['Compact', 'Normal', 'Spacious'],
      default: 'normal'
    },
    backgroundColor: {
      type: 'string',
      title: 'Background Color',
      format: 'color',
      default: 'transparent'
    },
    textColor: {
      type: 'string',
      title: 'Text Color',
      format: 'color',
      default: 'inherit'
    }
  },
  required: ['title', 'fields']
}
