'use client'

import React, { useState, useEffect, useRef } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  MapPin, 
  Navigation, 
  Phone, 
  Mail, 
  Clock,
  Plus,
  Trash2,
  ExternalLink
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface MapBlockConfig {
  title?: string
  description?: string
  mapType: 'google' | 'openstreetmap' | 'mapbox'
  apiKey?: string
  center: {
    lat: number
    lng: number
  }
  zoom: number
  height: string
  markers: MapMarker[]
  showControls: boolean
  showZoomControls: boolean
  showStreetView: boolean
  showFullscreenControl: boolean
  enableScrollWheel: boolean
  mapStyle: 'roadmap' | 'satellite' | 'hybrid' | 'terrain'
  customStyle?: string
  showDirections: boolean
  showContactInfo: boolean
  contactInfo: {
    address: string
    phone: string
    email: string
    hours: string
    website: string
  }
  backgroundColor: string
  borderRadius: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
  maxWidth: string
  alignment: 'left' | 'center' | 'right'
}

interface MapMarker {
  id: string
  lat: number
  lng: number
  title: string
  description?: string
  address?: string
  phone?: string
  email?: string
  website?: string
  icon?: string
  color?: string
}

interface MapBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function MapBlock({ block, isEditing = false }: MapBlockProps) {
  const config = block.configuration as MapBlockConfig
  const [mapLoaded, setMapLoaded] = useState(false)
  const [selectedMarker, setSelectedMarker] = useState<MapMarker | null>(null)
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<any>(null)

  const {
    title,
    description,
    mapType,
    apiKey,
    center,
    zoom,
    height,
    markers,
    showControls,
    showZoomControls,
    showStreetView,
    showFullscreenControl,
    enableScrollWheel,
    mapStyle,
    customStyle,
    showDirections,
    showContactInfo,
    contactInfo,
    backgroundColor,
    borderRadius,
    padding,
    maxWidth,
    alignment,
  } = config

  const getAlignmentClass = () => {
    const alignmentClasses = {
      left: 'mr-auto',
      center: 'mx-auto',
      right: 'ml-auto'
    }
    return alignmentClasses[alignment]
  }

  const loadGoogleMaps = () => {
    if (typeof window !== 'undefined' && window.google) {
      initializeMap()
      return
    }

    const script = document.createElement('script')
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`
    script.async = true
    script.defer = true
    script.onload = initializeMap
    document.head.appendChild(script)
  }

  const initializeMap = () => {
    if (!mapRef.current || !window.google) return

    const mapOptions = {
      center: { lat: center.lat, lng: center.lng },
      zoom: zoom,
      mapTypeId: mapStyle,
      disableDefaultUI: !showControls,
      zoomControl: showZoomControls,
      streetViewControl: showStreetView,
      fullscreenControl: showFullscreenControl,
      scrollwheel: enableScrollWheel,
      styles: customStyle ? JSON.parse(customStyle) : undefined
    }

    const map = new window.google.maps.Map(mapRef.current, mapOptions)
    mapInstanceRef.current = map

    // Add markers
    markers.forEach(marker => {
      const mapMarker = new window.google.maps.Marker({
        position: { lat: marker.lat, lng: marker.lng },
        map: map,
        title: marker.title,
        icon: marker.icon || undefined
      })

      // Info window
      const infoWindow = new window.google.maps.InfoWindow({
        content: `
          <div class="p-2">
            <h3 class="font-semibold text-sm mb-1">${marker.title}</h3>
            ${marker.description ? `<p class="text-xs text-gray-600 mb-2">${marker.description}</p>` : ''}
            ${marker.address ? `<p class="text-xs mb-1"><strong>Address:</strong> ${marker.address}</p>` : ''}
            ${marker.phone ? `<p class="text-xs mb-1"><strong>Phone:</strong> ${marker.phone}</p>` : ''}
            ${marker.email ? `<p class="text-xs mb-1"><strong>Email:</strong> ${marker.email}</p>` : ''}
            ${marker.website ? `<p class="text-xs"><a href="${marker.website}" target="_blank" class="text-blue-600 hover:underline">Visit Website</a></p>` : ''}
          </div>
        `
      })

      mapMarker.addListener('click', () => {
        infoWindow.open(map, mapMarker)
        setSelectedMarker(marker)
      })
    })

    setMapLoaded(true)
  }

  const renderOpenStreetMap = () => {
    // For demo purposes, show a placeholder
    return (
      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
        <div className="text-center">
          <MapPin className="h-12 w-12 mx-auto mb-2 text-gray-400" />
          <p className="text-gray-600">OpenStreetMap Integration</p>
          <p className="text-xs text-gray-500">Map would be rendered here</p>
        </div>
      </div>
    )
  }

  const renderMapboxMap = () => {
    // For demo purposes, show a placeholder
    return (
      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
        <div className="text-center">
          <MapPin className="h-12 w-12 mx-auto mb-2 text-gray-400" />
          <p className="text-gray-600">Mapbox Integration</p>
          <p className="text-xs text-gray-500">Map would be rendered here</p>
        </div>
      </div>
    )
  }

  const getDirectionsUrl = (marker: MapMarker) => {
    const destination = `${marker.lat},${marker.lng}`
    return `https://www.google.com/maps/dir/?api=1&destination=${destination}`
  }

  const containerStyles = {
    backgroundColor: backgroundColor || 'transparent',
    paddingTop: padding.top,
    paddingRight: padding.right,
    paddingBottom: padding.bottom,
    paddingLeft: padding.left,
    borderRadius: borderRadius,
    maxWidth: maxWidth || '100%',
  }

  // Demo marker for editing mode
  const demoMarker: MapMarker = {
    id: 'demo-1',
    lat: -26.2041,
    lng: 28.0473,
    title: 'Cocomilk Store',
    description: 'Our flagship store in Johannesburg',
    address: '123 Main Street, Johannesburg, South Africa',
    phone: '+27 11 123 4567',
    email: '<EMAIL>',
    website: 'https://cocomilk.co.za'
  }

  const markersToRender = isEditing ? [demoMarker] : markers

  useEffect(() => {
    if (mapType === 'google' && !isEditing) {
      loadGoogleMaps()
    }
  }, [mapType, apiKey, isEditing])

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div style={containerStyles}>
        <div className={cn('w-full', getAlignmentClass())}>
          {/* Header */}
          {(title || description) && (
            <div className="mb-6">
              {title && (
                <h2 className="text-2xl font-semibold mb-2">{title}</h2>
              )}
              {description && (
                <p className="text-gray-600">{description}</p>
              )}
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Map */}
            <div className={cn(
              'rounded-lg overflow-hidden border',
              showContactInfo ? 'lg:col-span-2' : 'lg:col-span-3'
            )}>
              <div
                ref={mapRef}
                className="w-full"
                style={{ height: height || '400px' }}
              >
                {isEditing && (
                  <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                    <div className="text-center">
                      <MapPin className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                      <p className="text-gray-600 font-medium">Interactive Map</p>
                      <p className="text-sm text-gray-500">{mapType.toUpperCase()} Map</p>
                      <p className="text-xs text-gray-500 mt-2">
                        {markersToRender.length} marker{markersToRender.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                )}
                {!isEditing && mapType === 'google' && !mapLoaded && (
                  <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                      <p className="text-gray-600">Loading map...</p>
                    </div>
                  </div>
                )}
                {!isEditing && mapType === 'openstreetmap' && renderOpenStreetMap()}
                {!isEditing && mapType === 'mapbox' && renderMapboxMap()}
              </div>
            </div>

            {/* Contact Information */}
            {showContactInfo && (
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <MapPin className="h-5 w-5 mr-2" />
                      Contact Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {contactInfo.address && (
                      <div className="flex items-start space-x-3">
                        <MapPin className="h-4 w-4 mt-1 text-gray-500" />
                        <div>
                          <p className="text-sm font-medium">Address</p>
                          <p className="text-sm text-gray-600">{contactInfo.address}</p>
                        </div>
                      </div>
                    )}

                    {contactInfo.phone && (
                      <div className="flex items-start space-x-3">
                        <Phone className="h-4 w-4 mt-1 text-gray-500" />
                        <div>
                          <p className="text-sm font-medium">Phone</p>
                          <a 
                            href={`tel:${contactInfo.phone}`}
                            className="text-sm text-blue-600 hover:underline"
                          >
                            {contactInfo.phone}
                          </a>
                        </div>
                      </div>
                    )}

                    {contactInfo.email && (
                      <div className="flex items-start space-x-3">
                        <Mail className="h-4 w-4 mt-1 text-gray-500" />
                        <div>
                          <p className="text-sm font-medium">Email</p>
                          <a 
                            href={`mailto:${contactInfo.email}`}
                            className="text-sm text-blue-600 hover:underline"
                          >
                            {contactInfo.email}
                          </a>
                        </div>
                      </div>
                    )}

                    {contactInfo.hours && (
                      <div className="flex items-start space-x-3">
                        <Clock className="h-4 w-4 mt-1 text-gray-500" />
                        <div>
                          <p className="text-sm font-medium">Hours</p>
                          <p className="text-sm text-gray-600">{contactInfo.hours}</p>
                        </div>
                      </div>
                    )}

                    {contactInfo.website && (
                      <div className="flex items-start space-x-3">
                        <ExternalLink className="h-4 w-4 mt-1 text-gray-500" />
                        <div>
                          <p className="text-sm font-medium">Website</p>
                          <a 
                            href={contactInfo.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:underline"
                          >
                            Visit Website
                          </a>
                        </div>
                      </div>
                    )}

                    {showDirections && markersToRender.length > 0 && (
                      <div className="pt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={() => window.open(getDirectionsUrl(markersToRender[0]), '_blank')}
                        >
                          <Navigation className="h-4 w-4 mr-2" />
                          Get Directions
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Markers List */}
                {markersToRender.length > 1 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Locations</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {markersToRender.map((marker) => (
                          <div
                            key={marker.id}
                            className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                            onClick={() => setSelectedMarker(marker)}
                          >
                            <h4 className="font-medium text-sm">{marker.title}</h4>
                            {marker.address && (
                              <p className="text-xs text-gray-600 mt-1">{marker.address}</p>
                            )}
                            {showDirections && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="mt-2 h-6 px-2 text-xs"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  window.open(getDirectionsUrl(marker), '_blank')
                                }}
                              >
                                <Navigation className="h-3 w-3 mr-1" />
                                Directions
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </div>
        </div>
        
        {isEditing && (
          <div className="mt-4 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700">
            <strong>Map Block:</strong> {mapType} • {markersToRender.length} marker{markersToRender.length !== 1 ? 's' : ''} • {height} height
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Map Block Configuration Component
interface MapBlockConfigProps {
  config: MapBlockConfig
  onChange: (config: MapBlockConfig) => void
}

export function MapBlockConfig({ config, onChange }: MapBlockConfigProps) {
  const updateConfig = (updates: Partial<MapBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const updateContactInfo = (updates: Partial<MapBlockConfig['contactInfo']>) => {
    updateConfig({
      contactInfo: { ...config.contactInfo, ...updates }
    })
  }

  const addMarker = () => {
    const newMarker: MapMarker = {
      id: `marker-${Date.now()}`,
      lat: config.center.lat,
      lng: config.center.lng,
      title: 'New Location',
      description: '',
      address: ''
    }
    
    updateConfig({
      markers: [...config.markers, newMarker]
    })
  }

  const updateMarker = (index: number, updates: Partial<MapMarker>) => {
    const updatedMarkers = [...config.markers]
    updatedMarkers[index] = { ...updatedMarkers[index], ...updates }
    updateConfig({ markers: updatedMarkers })
  }

  const removeMarker = (index: number) => {
    const updatedMarkers = config.markers.filter((_, i) => i !== index)
    updateConfig({ markers: updatedMarkers })
  }

  return (
    <div className="space-y-6">
      {/* Map Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Map Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Title</Label>
            <Input
              value={config.title || ''}
              onChange={(e) => updateConfig({ title: e.target.value })}
              placeholder="Find Us"
              className="mt-1"
            />
          </div>

          <div>
            <Label className="text-xs">Description</Label>
            <Textarea
              value={config.description || ''}
              onChange={(e) => updateConfig({ description: e.target.value })}
              placeholder="Visit our store locations"
              className="mt-1"
              rows={2}
            />
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Map Type</Label>
              <Select
                value={config.mapType}
                onValueChange={(value) => updateConfig({ mapType: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="google">Google Maps</SelectItem>
                  <SelectItem value="openstreetmap">OpenStreetMap</SelectItem>
                  <SelectItem value="mapbox">Mapbox</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Map Style</Label>
              <Select
                value={config.mapStyle}
                onValueChange={(value) => updateConfig({ mapStyle: value as any })}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="roadmap">Roadmap</SelectItem>
                  <SelectItem value="satellite">Satellite</SelectItem>
                  <SelectItem value="hybrid">Hybrid</SelectItem>
                  <SelectItem value="terrain">Terrain</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-2">
            <div>
              <Label className="text-xs">Latitude</Label>
              <Input
                type="number"
                step="any"
                value={config.center.lat}
                onChange={(e) => updateConfig({
                  center: { ...config.center, lat: parseFloat(e.target.value) }
                })}
                className="mt-1"
              />
            </div>
            <div>
              <Label className="text-xs">Longitude</Label>
              <Input
                type="number"
                step="any"
                value={config.center.lng}
                onChange={(e) => updateConfig({
                  center: { ...config.center, lng: parseFloat(e.target.value) }
                })}
                className="mt-1"
              />
            </div>
            <div>
              <Label className="text-xs">Zoom</Label>
              <Input
                type="number"
                min="1"
                max="20"
                value={config.zoom}
                onChange={(e) => updateConfig({ zoom: parseInt(e.target.value) })}
                className="mt-1"
              />
            </div>
          </div>

          <div>
            <Label className="text-xs">Height</Label>
            <Input
              value={config.height}
              onChange={(e) => updateConfig({ height: e.target.value })}
              placeholder="400px"
              className="mt-1"
            />
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Contact Info</Label>
              <Switch
                checked={config.showContactInfo}
                onCheckedChange={(checked) => updateConfig({ showContactInfo: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Directions</Label>
              <Switch
                checked={config.showDirections}
                onCheckedChange={(checked) => updateConfig({ showDirections: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Enable Scroll Wheel</Label>
              <Switch
                checked={config.enableScrollWheel}
                onCheckedChange={(checked) => updateConfig({ enableScrollWheel: checked })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      {config.showContactInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Contact Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-xs">Address</Label>
              <Textarea
                value={config.contactInfo.address}
                onChange={(e) => updateContactInfo({ address: e.target.value })}
                placeholder="123 Main Street, City, Country"
                className="mt-1"
                rows={2}
              />
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">Phone</Label>
                <Input
                  value={config.contactInfo.phone}
                  onChange={(e) => updateContactInfo({ phone: e.target.value })}
                  placeholder="+27 11 123 4567"
                  className="mt-1"
                />
              </div>
              <div>
                <Label className="text-xs">Email</Label>
                <Input
                  value={config.contactInfo.email}
                  onChange={(e) => updateContactInfo({ email: e.target.value })}
                  placeholder="<EMAIL>"
                  className="mt-1"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">Hours</Label>
                <Input
                  value={config.contactInfo.hours}
                  onChange={(e) => updateContactInfo({ hours: e.target.value })}
                  placeholder="Mon-Fri: 9AM-6PM"
                  className="mt-1"
                />
              </div>
              <div>
                <Label className="text-xs">Website</Label>
                <Input
                  value={config.contactInfo.website}
                  onChange={(e) => updateContactInfo({ website: e.target.value })}
                  placeholder="https://example.com"
                  className="mt-1"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Markers */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm">Map Markers</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={addMarker}
            className="h-8 px-2"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Marker
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.markers.map((marker, index) => (
            <div key={marker.id} className="border rounded-lg p-3 space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Marker {index + 1}</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeMarker(index)}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
              
              <div>
                <Label className="text-xs">Title</Label>
                <Input
                  value={marker.title}
                  onChange={(e) => updateMarker(index, { title: e.target.value })}
                  className="mt-1"
                  placeholder="Location name"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Latitude</Label>
                  <Input
                    type="number"
                    step="any"
                    value={marker.lat}
                    onChange={(e) => updateMarker(index, { lat: parseFloat(e.target.value) })}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="text-xs">Longitude</Label>
                  <Input
                    type="number"
                    step="any"
                    value={marker.lng}
                    onChange={(e) => updateMarker(index, { lng: parseFloat(e.target.value) })}
                    className="mt-1"
                  />
                </div>
              </div>

              <div>
                <Label className="text-xs">Address</Label>
                <Input
                  value={marker.address || ''}
                  onChange={(e) => updateMarker(index, { address: e.target.value })}
                  className="mt-1"
                  placeholder="Full address"
                />
              </div>
            </div>
          ))}
          
          {config.markers.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              <p className="text-sm">No markers yet.</p>
              <p className="text-xs">Click "Add Marker" to get started.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
