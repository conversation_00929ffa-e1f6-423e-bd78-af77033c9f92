'use client'

import React, { useState } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { 
  Search, 
  MapPin, 
  Phone, 
  Clock, 
  Navigation,
  Star,
  ExternalLink
} from 'lucide-react'

interface StoreLocation {
  id: string
  name: string
  address: string
  city: string
  province: string
  postalCode: string
  phone: string
  email?: string
  hours: {
    [key: string]: string
  }
  services: string[]
  rating?: number
  isNew?: boolean
  isFlagship?: boolean
  coordinates?: {
    lat: number
    lng: number
  }
  image?: string
  description?: string
}

interface StoreLocatorConfig {
  title: string
  subtitle?: string
  description?: string
  stores: StoreLocation[]
  enableSearch: boolean
  searchPlaceholder: string
  showMap: boolean
  mapHeight: string
  showRatings: boolean
  showServices: boolean
  showHours: boolean
  cardStyle: 'default' | 'minimal' | 'detailed'
  layout: 'grid' | 'list'
  columns: 2 | 3 | 4
  spacing: 'compact' | 'normal' | 'spacious'
  backgroundColor: string
  textColor: string
  defaultLocation: {
    city: string
    province: string
  }
}

interface StoreLocatorBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function StoreLocatorBlock({ block, isEditing = false }: StoreLocatorBlockProps) {
  const config = block.configuration as StoreLocatorConfig

  const {
    title,
    subtitle,
    description,
    stores = [],
    enableSearch = true,
    searchPlaceholder = 'Search by city or store name...',
    showMap = false,
    mapHeight = '400px',
    showRatings = true,
    showServices = true,
    showHours = false,
    cardStyle = 'default',
    layout = 'grid',
    columns = 3,
    spacing = 'normal',
    backgroundColor = 'transparent',
    textColor = 'inherit',
    defaultLocation = {
      city: 'Johannesburg',
      province: 'Gauteng'
    }
  } = config

  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStore, setSelectedStore] = useState<string | null>(null)

  const getSpacingClasses = () => {
    switch (spacing) {
      case 'compact':
        return 'py-6 gap-4'
      case 'spacious':
        return 'py-16 gap-8'
      default:
        return 'py-12 gap-6'
    }
  }

  const getColumnClasses = () => {
    switch (columns) {
      case 2:
        return 'grid-cols-1 md:grid-cols-2'
      case 4:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
      default:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
    }
  }

  // Filter stores based on search term
  const filteredStores = stores.filter(store => {
    if (!searchTerm) return true
    const searchLower = searchTerm.toLowerCase()
    return (
      store.name.toLowerCase().includes(searchLower) ||
      store.city.toLowerCase().includes(searchLower) ||
      store.province.toLowerCase().includes(searchLower) ||
      store.address.toLowerCase().includes(searchLower)
    )
  })

  const formatHours = (hours: { [key: string]: string }) => {
    const today = new Date().toLocaleLowerCase().slice(0, 3)
    const todayHours = hours[today] || hours['mon'] || 'Closed'
    return `Today: ${todayHours}`
  }

  const renderStoreCard = (store: StoreLocation) => (
    <Card 
      key={store.id} 
      className={cn(
        'h-full cursor-pointer transition-all duration-200',
        selectedStore === store.id ? 'ring-2 ring-[#012169]' : 'hover:shadow-md',
        cardStyle === 'minimal' ? 'shadow-none border-0' : ''
      )}
      onClick={() => setSelectedStore(selectedStore === store.id ? null : store.id)}
    >
      <CardContent className="p-6 h-full flex flex-col">
        {/* Store Image */}
        {store.image && cardStyle === 'detailed' && (
          <div className="mb-4 rounded-lg overflow-hidden">
            <img 
              src={store.image} 
              alt={store.name}
              className="w-full h-32 object-cover"
            />
          </div>
        )}

        {/* Header */}
        <div className="mb-4">
          <div className="flex items-start justify-between gap-2 mb-2">
            <h3 className="font-semibold text-lg leading-tight">
              {store.name}
            </h3>
            <div className="flex gap-1">
              {store.isNew && (
                <Badge variant="default" className="text-xs bg-green-600">
                  New
                </Badge>
              )}
              {store.isFlagship && (
                <Badge variant="secondary" className="text-xs">
                  Flagship
                </Badge>
              )}
            </div>
          </div>
          
          {/* Rating */}
          {showRatings && store.rating && (
            <div className="flex items-center gap-1 mb-2">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <span className="text-sm font-medium">{store.rating}</span>
            </div>
          )}

          {/* Description */}
          {store.description && cardStyle === 'detailed' && (
            <p className="text-sm text-muted-foreground mb-3">
              {store.description}
            </p>
          )}
        </div>

        {/* Address */}
        <div className="flex items-start gap-2 mb-3">
          <MapPin className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
          <div className="text-sm">
            <div>{store.address}</div>
            <div className="text-muted-foreground">
              {store.city}, {store.province} {store.postalCode}
            </div>
          </div>
        </div>

        {/* Contact */}
        <div className="flex items-center gap-2 mb-3">
          <Phone className="h-4 w-4 text-muted-foreground" />
          <a 
            href={`tel:${store.phone.replace(/\s/g, '')}`}
            className="text-sm text-[#012169] hover:underline"
            onClick={(e) => e.stopPropagation()}
          >
            {store.phone}
          </a>
        </div>

        {/* Hours */}
        {showHours && (
          <div className="flex items-center gap-2 mb-3">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{formatHours(store.hours)}</span>
          </div>
        )}

        {/* Services */}
        {showServices && store.services && store.services.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {store.services.slice(0, 3).map((service, index) => (
                <span
                  key={index}
                  className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
                >
                  {service}
                </span>
              ))}
              {store.services.length > 3 && (
                <span className="text-xs text-muted-foreground">
                  +{store.services.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="mt-auto pt-4 flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex-1"
            onClick={(e) => {
              e.stopPropagation()
              if (store.coordinates) {
                window.open(
                  `https://www.google.com/maps/dir/?api=1&destination=${store.coordinates.lat},${store.coordinates.lng}`,
                  '_blank'
                )
              }
            }}
            disabled={isEditing}
          >
            <Navigation className="h-3 w-3 mr-1" />
            Directions
          </Button>
          {store.email && (
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                window.location.href = `mailto:${store.email}`
              }}
              disabled={isEditing}
            >
              <ExternalLink className="h-3 w-3" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )

  const renderStoreList = (store: StoreLocation) => (
    <div key={store.id} className="border rounded-lg p-4 hover:shadow-sm transition-shadow">
      <div className="flex justify-between items-start gap-4">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="font-semibold">{store.name}</h3>
            {store.isNew && (
              <Badge variant="default" className="text-xs bg-green-600">
                New
              </Badge>
            )}
            {store.isFlagship && (
              <Badge variant="secondary" className="text-xs">
                Flagship
              </Badge>
            )}
          </div>
          <p className="text-sm text-muted-foreground mb-2">
            {store.address}, {store.city}, {store.province}
          </p>
          <p className="text-sm">{store.phone}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" disabled={isEditing}>
            <Navigation className="h-3 w-3 mr-1" />
            Directions
          </Button>
        </div>
      </div>
    </div>
  )

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div 
        className="container px-4 md:px-6"
        style={{ 
          backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : undefined,
          color: textColor !== 'inherit' ? textColor : undefined
        }}
      >
        <div className={getSpacingClasses()}>
          {/* Header */}
          <div className="text-center mb-8">
            {title && (
              <h1 className="text-3xl font-bold font-montserrat mb-4">
                {title}
              </h1>
            )}
            {subtitle && (
              <h2 className="text-xl text-muted-foreground mb-4">
                {subtitle}
              </h2>
            )}
            {description && (
              <p className="text-lg text-muted-foreground font-light max-w-2xl mx-auto">
                {description}
              </p>
            )}
          </div>

          {/* Search */}
          {enableSearch && (
            <div className="max-w-md mx-auto mb-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder={searchPlaceholder}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          )}

          {/* Map Placeholder */}
          {showMap && (
            <div 
              className="bg-gray-200 rounded-lg mb-8 flex items-center justify-center"
              style={{ height: mapHeight }}
            >
              <p className="text-gray-500">Map integration would go here</p>
            </div>
          )}

          {/* Store Results */}
          <div className="mb-4">
            <p className="text-sm text-muted-foreground">
              {filteredStores.length} store{filteredStores.length !== 1 ? 's' : ''} found
              {searchTerm && ` for "${searchTerm}"`}
            </p>
          </div>

          {/* Stores */}
          {filteredStores.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-muted-foreground">
                No stores found. Try adjusting your search.
              </p>
            </div>
          ) : layout === 'list' ? (
            <div className="space-y-4 max-w-4xl mx-auto">
              {filteredStores.map((store) => renderStoreList(store))}
            </div>
          ) : (
            <div className={cn('grid gap-6', getColumnClasses())}>
              {filteredStores.map((store) => renderStoreCard(store))}
            </div>
          )}
        </div>
      </div>
    </BaseBlock>
  )
}

// Default configuration for the store locator block
export const storeLocatorBlockConfig = {
  title: 'Find a Store',
  subtitle: 'Visit us at one of our locations',
  description: 'Discover our stores across South Africa and find the perfect location for your shopping needs.',
  stores: [
    {
      id: 'sandton-city',
      name: 'Sandton City',
      address: '83 Rivonia Road, Sandton City Shopping Centre',
      city: 'Sandton',
      province: 'Gauteng',
      postalCode: '2196',
      phone: '+27 11 784 7911',
      email: '<EMAIL>',
      hours: {
        'mon': '9:00 AM - 9:00 PM',
        'tue': '9:00 AM - 9:00 PM',
        'wed': '9:00 AM - 9:00 PM',
        'thu': '9:00 AM - 9:00 PM',
        'fri': '9:00 AM - 9:00 PM',
        'sat': '9:00 AM - 7:00 PM',
        'sun': '9:00 AM - 6:00 PM'
      },
      services: ['Personal Shopping', 'Gift Wrapping', 'Size Consultation', 'Returns'],
      rating: 4.8,
      isNew: false,
      isFlagship: true,
      coordinates: {
        lat: -26.1076,
        lng: 28.0567
      },
      description: 'Our flagship store featuring the complete collection and personal shopping services.'
    },
    {
      id: 'canal-walk',
      name: 'Canal Walk',
      address: 'Century Boulevard, Canal Walk Shopping Centre',
      city: 'Cape Town',
      province: 'Western Cape',
      postalCode: '7441',
      phone: '+27 21 555 2000',
      email: '<EMAIL>',
      hours: {
        'mon': '9:00 AM - 9:00 PM',
        'tue': '9:00 AM - 9:00 PM',
        'wed': '9:00 AM - 9:00 PM',
        'thu': '9:00 AM - 9:00 PM',
        'fri': '9:00 AM - 9:00 PM',
        'sat': '9:00 AM - 7:00 PM',
        'sun': '9:00 AM - 6:00 PM'
      },
      services: ['Gift Wrapping', 'Size Consultation', 'Returns'],
      rating: 4.6,
      isNew: false,
      isFlagship: false,
      coordinates: {
        lat: -33.8916,
        lng: 18.5103
      }
    },
    {
      id: 'gateway',
      name: 'Gateway Theatre of Shopping',
      address: '1 Palm Boulevard, Gateway Theatre of Shopping',
      city: 'Durban',
      province: 'KwaZulu-Natal',
      postalCode: '4319',
      phone: '+27 31 566 3000',
      email: '<EMAIL>',
      hours: {
        'mon': '9:00 AM - 9:00 PM',
        'tue': '9:00 AM - 9:00 PM',
        'wed': '9:00 AM - 9:00 PM',
        'thu': '9:00 AM - 9:00 PM',
        'fri': '9:00 AM - 9:00 PM',
        'sat': '9:00 AM - 7:00 PM',
        'sun': '9:00 AM - 6:00 PM'
      },
      services: ['Gift Wrapping', 'Returns'],
      rating: 4.5,
      isNew: true,
      isFlagship: false,
      coordinates: {
        lat: -29.7674,
        lng: 31.0669
      }
    }
  ],
  enableSearch: true,
  searchPlaceholder: 'Search by city or store name...',
  showMap: false,
  mapHeight: '400px',
  showRatings: true,
  showServices: true,
  showHours: false,
  cardStyle: 'default',
  layout: 'grid',
  columns: 3,
  spacing: 'normal',
  backgroundColor: 'transparent',
  textColor: 'inherit',
  defaultLocation: {
    city: 'Johannesburg',
    province: 'Gauteng'
  }
}

// Configuration schema for the Page Builder
export const storeLocatorBlockSchema = {
  type: 'object',
  properties: {
    title: {
      type: 'string',
      title: 'Title',
      default: 'Find a Store'
    },
    subtitle: {
      type: 'string',
      title: 'Subtitle (Optional)',
      default: 'Visit us at one of our locations'
    },
    description: {
      type: 'string',
      title: 'Description (Optional)',
      format: 'textarea',
      default: storeLocatorBlockConfig.description
    },
    enableSearch: {
      type: 'boolean',
      title: 'Enable Search',
      default: true
    },
    searchPlaceholder: {
      type: 'string',
      title: 'Search Placeholder',
      default: 'Search by city or store name...'
    },
    showMap: {
      type: 'boolean',
      title: 'Show Map',
      default: false
    },
    mapHeight: {
      type: 'string',
      title: 'Map Height',
      default: '400px'
    },
    showRatings: {
      type: 'boolean',
      title: 'Show Store Ratings',
      default: true
    },
    showServices: {
      type: 'boolean',
      title: 'Show Store Services',
      default: true
    },
    showHours: {
      type: 'boolean',
      title: 'Show Store Hours',
      default: false
    },
    cardStyle: {
      type: 'string',
      title: 'Card Style',
      enum: ['default', 'minimal', 'detailed'],
      enumNames: ['Default', 'Minimal', 'Detailed'],
      default: 'default'
    },
    layout: {
      type: 'string',
      title: 'Layout',
      enum: ['grid', 'list'],
      enumNames: ['Grid', 'List'],
      default: 'grid'
    },
    columns: {
      type: 'number',
      title: 'Columns (for grid layout)',
      enum: [2, 3, 4],
      default: 3
    },
    spacing: {
      type: 'string',
      title: 'Spacing',
      enum: ['compact', 'normal', 'spacious'],
      enumNames: ['Compact', 'Normal', 'Spacious'],
      default: 'normal'
    },
    defaultLocation: {
      type: 'object',
      title: 'Default Location',
      properties: {
        city: {
          type: 'string',
          title: 'Default City',
          default: 'Johannesburg'
        },
        province: {
          type: 'string',
          title: 'Default Province',
          default: 'Gauteng'
        }
      }
    },
    backgroundColor: {
      type: 'string',
      title: 'Background Color',
      format: 'color',
      default: 'transparent'
    },
    textColor: {
      type: 'string',
      title: 'Text Color',
      format: 'color',
      default: 'inherit'
    }
  },
  required: ['title']
}
