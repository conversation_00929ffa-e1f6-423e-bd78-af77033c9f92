'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'

interface LayoutContainerBlockConfig {
  containerType: 'container' | 'section' | 'div' | 'article' | 'aside'
  maxWidth: string
  padding: {
    top: string
    right: string
    bottom: string
    left: string
  }
  margin: {
    top: string
    right: string
    bottom: string
    left: string
  }
  backgroundColor: string
  backgroundImage?: string
  backgroundSize: 'cover' | 'contain' | 'auto'
  backgroundPosition: string
  backgroundRepeat: boolean
  centerContent: boolean
  fullHeight: boolean
  flexDirection: 'row' | 'column'
  justifyContent: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly'
  alignItems: 'start' | 'center' | 'end' | 'stretch'
  gap: string
  borderRadius: string
  border: {
    width: string
    style: 'none' | 'solid' | 'dashed' | 'dotted'
    color: string
  }
  shadow: {
    enabled: boolean
    size: 'sm' | 'md' | 'lg' | 'xl'
    color: string
  }
}

interface LayoutContainerBlockProps {
  block: PageBlock
  isEditing?: boolean
  children?: React.ReactNode
}

export function LayoutContainerBlock({ 
  block, 
  isEditing = false, 
  children 
}: LayoutContainerBlockProps) {
  const config = block.configuration as LayoutContainerBlockConfig

  const {
    containerType,
    maxWidth,
    padding,
    margin,
    backgroundColor,
    backgroundImage,
    backgroundSize,
    backgroundPosition,
    backgroundRepeat,
    centerContent,
    fullHeight,
    flexDirection,
    justifyContent,
    alignItems,
    gap,
    borderRadius,
    border,
    shadow,
  } = config

  const getContainerClasses = () => {
    const classes = []

    // Base container classes
    if (centerContent) {
      classes.push('mx-auto')
    }

    // Flex layout
    classes.push('flex')
    if (flexDirection === 'column') {
      classes.push('flex-col')
    }

    // Justify content
    switch (justifyContent) {
      case 'center':
        classes.push('justify-center')
        break
      case 'end':
        classes.push('justify-end')
        break
      case 'between':
        classes.push('justify-between')
        break
      case 'around':
        classes.push('justify-around')
        break
      case 'evenly':
        classes.push('justify-evenly')
        break
      default:
        classes.push('justify-start')
    }

    // Align items
    switch (alignItems) {
      case 'center':
        classes.push('items-center')
        break
      case 'end':
        classes.push('items-end')
        break
      case 'stretch':
        classes.push('items-stretch')
        break
      default:
        classes.push('items-start')
    }

    // Full height
    if (fullHeight) {
      classes.push('min-h-screen')
    }

    // Shadow
    if (shadow.enabled) {
      switch (shadow.size) {
        case 'sm':
          classes.push('shadow-sm')
          break
        case 'md':
          classes.push('shadow-md')
          break
        case 'lg':
          classes.push('shadow-lg')
          break
        case 'xl':
          classes.push('shadow-xl')
          break
      }
    }

    return classes.join(' ')
  }

  const getContainerStyles = () => {
    const styles: React.CSSProperties = {}

    // Max width
    if (maxWidth) {
      styles.maxWidth = maxWidth
    }

    // Padding
    if (padding) {
      styles.paddingTop = padding.top || '0'
      styles.paddingRight = padding.right || '0'
      styles.paddingBottom = padding.bottom || '0'
      styles.paddingLeft = padding.left || '0'
    }

    // Margin
    if (margin) {
      styles.marginTop = margin.top || '0'
      styles.marginRight = margin.right || '0'
      styles.marginBottom = margin.bottom || '0'
      styles.marginLeft = margin.left || '0'
    }

    // Background
    if (backgroundColor) {
      styles.backgroundColor = backgroundColor
    }

    if (backgroundImage) {
      styles.backgroundImage = `url(${backgroundImage})`
      styles.backgroundSize = backgroundSize
      styles.backgroundPosition = backgroundPosition
      styles.backgroundRepeat = backgroundRepeat ? 'repeat' : 'no-repeat'
    }

    // Gap
    if (gap) {
      styles.gap = gap
    }

    // Border radius
    if (borderRadius) {
      styles.borderRadius = borderRadius
    }

    // Border
    if (border && border.style !== 'none') {
      styles.borderWidth = border.width || '1px'
      styles.borderStyle = border.style
      styles.borderColor = border.color || '#000000'
    }

    return styles
  }

  const ContainerElement = containerType as keyof JSX.IntrinsicElements

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <ContainerElement
        className={cn(getContainerClasses(), isEditing && 'min-h-[100px] border-2 border-dashed border-gray-300')}
        style={getContainerStyles()}
      >
        {children || (
          isEditing && (
            <div className="flex items-center justify-center p-8 text-muted-foreground">
              <div className="text-center">
                <div className="text-4xl mb-2">📦</div>
                <p className="text-sm font-medium">Layout Container</p>
                <p className="text-xs">Drop blocks here or add content</p>
              </div>
            </div>
          )
        )}
      </ContainerElement>
    </BaseBlock>
  )
}

// Layout Container Block Configuration Component
interface LayoutContainerBlockConfigProps {
  config: LayoutContainerBlockConfig
  onChange: (config: LayoutContainerBlockConfig) => void
}

export function LayoutContainerBlockConfig({ config, onChange }: LayoutContainerBlockConfigProps) {
  const updateConfig = (updates: Partial<LayoutContainerBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  return (
    <div className="space-y-6">
      {/* Container Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Container Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Container Type</Label>
            <select
              value={config.containerType}
              onChange={(e) => updateConfig({ containerType: e.target.value as any })}
              className="w-full mt-1 px-2 py-1 text-xs border rounded"
            >
              <option value="div">Div</option>
              <option value="container">Container</option>
              <option value="section">Section</option>
              <option value="article">Article</option>
              <option value="aside">Aside</option>
            </select>
          </div>

          <div>
            <Label className="text-xs">Max Width</Label>
            <Input
              type="text"
              value={config.maxWidth}
              onChange={(e) => updateConfig({ maxWidth: e.target.value })}
              placeholder="1200px, 100%, none"
              className="text-xs mt-1"
            />
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Center Content</Label>
              <Switch
                checked={config.centerContent}
                onCheckedChange={(checked) => updateConfig({ centerContent: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Full Height</Label>
              <Switch
                checked={config.fullHeight}
                onCheckedChange={(checked) => updateConfig({ fullHeight: checked })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Layout Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Flexbox Layout</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Flex Direction</Label>
            <select
              value={config.flexDirection}
              onChange={(e) => updateConfig({ flexDirection: e.target.value as any })}
              className="w-full mt-1 px-2 py-1 text-xs border rounded"
            >
              <option value="row">Row (Horizontal)</option>
              <option value="column">Column (Vertical)</option>
            </select>
          </div>

          <div>
            <Label className="text-xs">Justify Content</Label>
            <select
              value={config.justifyContent}
              onChange={(e) => updateConfig({ justifyContent: e.target.value as any })}
              className="w-full mt-1 px-2 py-1 text-xs border rounded"
            >
              <option value="start">Start</option>
              <option value="center">Center</option>
              <option value="end">End</option>
              <option value="between">Space Between</option>
              <option value="around">Space Around</option>
              <option value="evenly">Space Evenly</option>
            </select>
          </div>

          <div>
            <Label className="text-xs">Align Items</Label>
            <select
              value={config.alignItems}
              onChange={(e) => updateConfig({ alignItems: e.target.value as any })}
              className="w-full mt-1 px-2 py-1 text-xs border rounded"
            >
              <option value="start">Start</option>
              <option value="center">Center</option>
              <option value="end">End</option>
              <option value="stretch">Stretch</option>
            </select>
          </div>

          <div>
            <Label className="text-xs">Gap</Label>
            <Input
              type="text"
              value={config.gap}
              onChange={(e) => updateConfig({ gap: e.target.value })}
              placeholder="1rem, 16px, 0"
              className="text-xs mt-1"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
