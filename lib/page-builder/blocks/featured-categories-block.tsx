'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { cn } from '@/lib/utils'
import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'

interface CategoryItem {
  id: string
  name: string
  description?: string
  image: string
  link: string
  textColor?: string
  textPosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center'
  size: 'large' | 'small'
  overlay?: {
    enabled: boolean
    color: string
    opacity: number
  }
}

interface FeaturedCategoriesConfig {
  title?: string
  subtitle?: string
  categories: CategoryItem[]
  layout: '2x2' | '3x3' | 'asymmetric'
  aspectRatio: 'square' | 'portrait' | 'landscape'
  gap: 'none' | 'small' | 'medium' | 'large'
  borderRadius: 'none' | 'small' | 'medium' | 'large'
  hoverEffect: 'none' | 'zoom' | 'overlay' | 'lift'
  textStyle: 'minimal' | 'bold' | 'elegant'
  spacing: 'compact' | 'normal' | 'spacious'
  backgroundColor: string
  showDescriptions: boolean
}

interface FeaturedCategoriesBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function FeaturedCategoriesBlock({ block, isEditing = false }: FeaturedCategoriesBlockProps) {
  const config = block.configuration as FeaturedCategoriesConfig

  const {
    title,
    subtitle,
    categories = [],
    layout = '2x2',
    aspectRatio = 'square',
    gap = 'small',
    borderRadius = 'none',
    hoverEffect = 'zoom',
    textStyle = 'minimal',
    spacing = 'normal',
    backgroundColor = 'transparent',
    showDescriptions = false
  } = config

  // Framer Motion variants - exactly like hardcoded component
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  }

  const getSpacingClasses = () => {
    switch (spacing) {
      case 'compact':
        return 'py-6'
      case 'spacious':
        return 'py-16'
      default:
        return 'py-12'
    }
  }

  const getGapClasses = () => {
    switch (gap) {
      case 'none':
        return 'gap-0'
      case 'small':
        return 'gap-2'
      case 'large':
        return 'gap-8'
      default:
        return 'gap-4'
    }
  }

  const getBorderRadiusClasses = () => {
    switch (borderRadius) {
      case 'small':
        return 'rounded-sm'
      case 'medium':
        return 'rounded-md'
      case 'large':
        return 'rounded-lg'
      default:
        return 'rounded-none'
    }
  }

  const getAspectRatioClasses = () => {
    switch (aspectRatio) {
      case 'portrait':
        return 'aspect-[3/4]'
      case 'landscape':
        return 'aspect-[4/3]'
      default:
        return 'aspect-square'
    }
  }

  const getHoverEffectClasses = () => {
    switch (hoverEffect) {
      case 'zoom':
        return 'overflow-hidden group-hover:scale-105 transition-transform duration-500'
      case 'overlay':
        return 'group-hover:brightness-75 transition-all duration-300'
      case 'lift':
        return 'group-hover:shadow-xl group-hover:-translate-y-1 transition-all duration-300'
      default:
        return ''
    }
  }

  const getTextStyleClasses = () => {
    switch (textStyle) {
      case 'bold':
        return 'font-bold text-lg'
      case 'elegant':
        return 'font-light text-xl tracking-wide'
      default:
        return 'font-medium text-base'
    }
  }

  const getTextPositionClasses = (position: string) => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4'
      case 'top-right':
        return 'top-4 right-4'
      case 'bottom-left':
        return 'bottom-4 left-4'
      case 'bottom-right':
        return 'bottom-4 right-4'
      case 'center':
        return 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2'
      default:
        return 'bottom-4 left-4'
    }
  }

  const getLayoutClasses = () => {
    switch (layout) {
      case '3x3':
        return 'grid-cols-3 grid-rows-3'
      case 'asymmetric':
        return 'grid-cols-4 grid-rows-2'
      default: // 2x2
        return 'grid-cols-2 grid-rows-2'
    }
  }

  const getCategoryClasses = (category: CategoryItem, index: number) => {
    if (layout === 'asymmetric') {
      // Asymmetric Zara-style layout
      if (index === 0) return 'col-span-2 row-span-2' // Large left
      if (index === 1) return 'col-span-2 row-span-1' // Top right
      if (index === 2) return 'col-span-2 row-span-1' // Bottom right
      return 'col-span-1 row-span-1'
    }
    
    if (layout === '2x2' && category.size === 'large') {
      return 'col-span-2 row-span-2'
    }
    
    return 'col-span-1 row-span-1'
  }

  const renderCategory = (category: CategoryItem, index: number) => {
    const content = (
      <div className="group block">
        <div className="relative aspect-square overflow-hidden bg-gray-100">
          <Image
            src={category.image}
            alt={category.name}
            fill
            className="object-cover transition-transform duration-500 group-hover:scale-102"
          />

          {/* Zara-style overlay */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300" />

          {/* Zara-style text overlay - exactly like hardcoded */}
          <div className="absolute bottom-0 left-0 right-0 p-6 md:p-8">
            <div className="bg-white/95 backdrop-blur-sm p-4 md:p-6 transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
              <h3 className="text-xl md:text-2xl font-light tracking-wide text-black mb-1">
                {category.name}
              </h3>
              <p className="text-sm md:text-base text-gray-600 font-light">
                {category.subtitle || category.description}
              </p>
            </div>
          </div>
        </div>
      </div>
    )

    if (isEditing) {
      return (
        <div key={category.id}>
          {content}
        </div>
      )
    }

    return (
      <Link key={category.id} href={category.link}>
        {content}
      </Link>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      {/* Exact replica of the hardcoded FeaturedCategories component */}
      <section
        className="py-16 bg-white"
        style={{
          backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : '#ffffff'
        }}
      >
        <div className="container px-4 md:px-6 max-w-7xl">
          {/* Header */}
          {(title || subtitle) && (
            <div className="text-center mb-8">
              {title && (
                <h2 className="text-2xl font-light mb-4 tracking-wide">
                  {title}
                </h2>
              )}
              {subtitle && (
                <p className="text-muted-foreground font-light">
                  {subtitle}
                </p>
              )}
            </div>
          )}

          {/* Zara-style 2x2 Grid - exactly like hardcoded */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6"
          >
            {categories.slice(0, layout === '3x3' ? 9 : layout === 'asymmetric' ? 3 : 4).map((category, index) => (
              <motion.div key={`${category.name}-${index}`} variants={itemVariants}>
                {renderCategory(category, index)}
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>
    </BaseBlock>
  )
}

// Default configuration for the featured categories block
export const featuredCategoriesBlockConfig = {
  title: '',
  subtitle: '',
  categories: [
    {
      id: 'girls',
      name: 'Girls Collection',
      description: 'Stylish & Comfortable',
      image: '/assets/images/cocomilk_kids-20220819_100135-2187605151.jpg',
      link: '/products?category=girls',
      textPosition: 'bottom-left',
      size: 'small',
      overlay: {
        enabled: true,
        color: '#000000',
        opacity: 20
      }
    },
    {
      id: 'boys',
      name: 'Boys Collection',
      description: 'Adventure Ready',
      image: '/assets/images/cocomilk_kids-20220822_112525-1393039322.jpg',
      link: '/products?category=boys',
      textPosition: 'bottom-left',
      size: 'small',
      overlay: {
        enabled: true,
        color: '#000000',
        opacity: 20
      }
    },
    {
      id: 'new-arrivals',
      name: 'New Arrivals',
      description: 'Latest Trends',
      image: '/assets/images/cocomilk_kids-20221028_102959-387306553.jpg',
      link: '/products?category=new-arrivals',
      textPosition: 'bottom-left',
      size: 'small',
      overlay: {
        enabled: true,
        color: '#000000',
        opacity: 20
      }
    },
    {
      id: 'sale',
      name: 'Sale',
      description: 'Up to 50% Off',
      image: '/assets/images/cocomilk_kids-20220912_082247-3005247592.jpg',
      link: '/products?category=sale',
      textPosition: 'bottom-left',
      size: 'small',
      overlay: {
        enabled: true,
        color: '#000000',
        opacity: 20
      }
    }
  ],
  layout: '2x2',
  aspectRatio: 'square',
  gap: 'small',
  borderRadius: 'none',
  hoverEffect: 'zoom',
  textStyle: 'minimal',
  spacing: 'normal',
  backgroundColor: 'transparent',
  showDescriptions: false
}

// Configuration schema for the Page Builder
export const featuredCategoriesBlockSchema = {
  type: 'object',
  properties: {
    title: {
      type: 'string',
      title: 'Title (Optional)',
      default: ''
    },
    subtitle: {
      type: 'string',
      title: 'Subtitle (Optional)',
      default: ''
    },
    categories: {
      type: 'array',
      title: 'Categories',
      items: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            title: 'Category ID',
            description: 'Unique identifier for the category'
          },
          name: {
            type: 'string',
            title: 'Category Name'
          },
          description: {
            type: 'string',
            title: 'Description (Optional)'
          },
          image: {
            type: 'string',
            title: 'Image URL',
            format: 'uri'
          },
          link: {
            type: 'string',
            title: 'Link URL'
          },
          textColor: {
            type: 'string',
            title: 'Text Color (Optional)',
            format: 'color'
          },
          textPosition: {
            type: 'string',
            title: 'Text Position',
            enum: ['top-left', 'top-right', 'bottom-left', 'bottom-right', 'center'],
            enumNames: ['Top Left', 'Top Right', 'Bottom Left', 'Bottom Right', 'Center'],
            default: 'bottom-left'
          },
          size: {
            type: 'string',
            title: 'Size',
            enum: ['large', 'small'],
            enumNames: ['Large', 'Small'],
            default: 'small'
          },
          overlay: {
            type: 'object',
            title: 'Overlay Settings',
            properties: {
              enabled: {
                type: 'boolean',
                title: 'Enable Overlay',
                default: true
              },
              color: {
                type: 'string',
                title: 'Overlay Color',
                format: 'color',
                default: '#000000'
              },
              opacity: {
                type: 'number',
                title: 'Overlay Opacity (%)',
                minimum: 0,
                maximum: 100,
                default: 20
              }
            }
          }
        },
        required: ['id', 'name', 'image', 'link']
      },
      default: featuredCategoriesBlockConfig.categories
    },
    layout: {
      type: 'string',
      title: 'Layout',
      enum: ['2x2', '3x3', 'asymmetric'],
      enumNames: ['2x2 Grid', '3x3 Grid', 'Asymmetric (Zara Style)'],
      default: '2x2'
    },
    aspectRatio: {
      type: 'string',
      title: 'Aspect Ratio',
      enum: ['square', 'portrait', 'landscape'],
      enumNames: ['Square (1:1)', 'Portrait (3:4)', 'Landscape (4:3)'],
      default: 'square'
    },
    gap: {
      type: 'string',
      title: 'Gap Between Items',
      enum: ['none', 'small', 'medium', 'large'],
      enumNames: ['None', 'Small', 'Medium', 'Large'],
      default: 'small'
    },
    borderRadius: {
      type: 'string',
      title: 'Border Radius',
      enum: ['none', 'small', 'medium', 'large'],
      enumNames: ['None', 'Small', 'Medium', 'Large'],
      default: 'none'
    },
    hoverEffect: {
      type: 'string',
      title: 'Hover Effect',
      enum: ['none', 'zoom', 'overlay', 'lift'],
      enumNames: ['None', 'Zoom', 'Overlay', 'Lift'],
      default: 'zoom'
    },
    textStyle: {
      type: 'string',
      title: 'Text Style',
      enum: ['minimal', 'bold', 'elegant'],
      enumNames: ['Minimal', 'Bold', 'Elegant'],
      default: 'minimal'
    },
    showDescriptions: {
      type: 'boolean',
      title: 'Show Descriptions',
      default: false
    },
    spacing: {
      type: 'string',
      title: 'Spacing',
      enum: ['compact', 'normal', 'spacious'],
      enumNames: ['Compact', 'Normal', 'Spacious'],
      default: 'normal'
    },
    backgroundColor: {
      type: 'string',
      title: 'Background Color',
      format: 'color',
      default: 'transparent'
    }
  },
  required: ['categories']
}
