'use client'

import React, { useState, useEffect } from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  X, 
  ShoppingBag,
  CreditCard
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface CartWidgetBlockConfig {
  displayType: 'mini' | 'full' | 'sidebar'
  showItemCount: boolean
  showSubtotal: boolean
  showCheckoutButton: boolean
  showContinueShoppingButton: boolean
  emptyCartMessage: string
  checkoutButtonText: string
  continueShoppingText: string
  maxItemsToShow: number
  showProductImages: boolean
  showProductVariants: boolean
  enableQuantityEdit: boolean
  enableItemRemoval: boolean
  autoOpen: boolean
  position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
}

interface CartItem {
  id: string
  productId: string
  title: string
  price: number
  quantity: number
  image?: string
  variant?: string
  sku?: string
}

interface CartWidgetBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function CartWidgetBlock({ block, isEditing = false }: CartWidgetBlockProps) {
  const config = block.configuration as CartWidgetBlockConfig
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [loading, setLoading] = useState(true)

  const {
    displayType,
    showItemCount,
    showSubtotal,
    showCheckoutButton,
    showContinueShoppingButton,
    emptyCartMessage,
    checkoutButtonText,
    continueShoppingText,
    maxItemsToShow,
    showProductImages,
    showProductVariants,
    enableQuantityEdit,
    enableItemRemoval,
    autoOpen,
    position,
  } = config

  // Mock cart data for demo
  useEffect(() => {
    if (isEditing) {
      setCartItems([
        {
          id: '1',
          productId: 'prod-1',
          title: 'Kids Cotton T-Shirt',
          price: 299,
          quantity: 2,
          image: 'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=100&h=100&fit=crop',
          variant: 'Size: 4-5Y, Color: Blue',
          sku: 'KTS-001-BL-4Y'
        },
        {
          id: '2',
          productId: 'prod-2',
          title: 'Denim Overalls',
          price: 599,
          quantity: 1,
          image: 'https://images.unsplash.com/photo-1519238263530-99bdd11df2ea?w=100&h=100&fit=crop',
          variant: 'Size: 6-7Y',
          sku: 'DO-002-6Y'
        }
      ])
      setLoading(false)
    } else {
      // Fetch real cart data
      fetchCartData()
    }
  }, [isEditing])

  const fetchCartData = async () => {
    try {
      const response = await fetch('/api/e-commerce/cart')
      const data = await response.json()
      if (data.success) {
        setCartItems(data.data.items || [])
      }
    } catch (error) {
      console.error('Error fetching cart:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateQuantity = async (itemId: string, newQuantity: number) => {
    if (isEditing) {
      setCartItems(items => 
        items.map(item => 
          item.id === itemId ? { ...item, quantity: newQuantity } : item
        )
      )
      return
    }

    try {
      const response = await fetch('/api/e-commerce/cart/items', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ itemId, quantity: newQuantity })
      })
      
      if (response.ok) {
        fetchCartData()
      }
    } catch (error) {
      console.error('Error updating quantity:', error)
    }
  }

  const removeItem = async (itemId: string) => {
    if (isEditing) {
      setCartItems(items => items.filter(item => item.id !== itemId))
      return
    }

    try {
      const response = await fetch(`/api/e-commerce/cart/items/${itemId}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        fetchCartData()
      }
    } catch (error) {
      console.error('Error removing item:', error)
    }
  }

  const getTotalItems = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0)
  }

  const getSubtotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0)
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(price / 100)
  }

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4'
      case 'bottom-left':
        return 'bottom-4 left-4'
      case 'bottom-right':
        return 'bottom-4 right-4'
      default:
        return 'top-4 right-4'
    }
  }

  if (displayType === 'mini') {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <div className={cn(
          'fixed z-50',
          getPositionClasses(),
          isEditing && 'relative top-0 left-0 right-0 bottom-0'
        )}>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsOpen(!isOpen)}
            className="bg-white shadow-lg hover:shadow-xl transition-shadow"
          >
            <ShoppingCart className="h-4 w-4 mr-2" />
            {showItemCount && (
              <span className="mr-1">({getTotalItems()})</span>
            )}
            {showSubtotal && (
              <span>{formatPrice(getSubtotal())}</span>
            )}
          </Button>

          {isOpen && (
            <Card className="absolute top-full mt-2 w-80 max-h-96 overflow-hidden shadow-xl">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm">Shopping Cart</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                {cartItems.length === 0 ? (
                  <div className="p-4 text-center text-muted-foreground">
                    <ShoppingBag className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">{emptyCartMessage}</p>
                  </div>
                ) : (
                  <>
                    <div className="max-h-48 overflow-y-auto px-4">
                      {cartItems.slice(0, maxItemsToShow).map((item) => (
                        <div key={item.id} className="flex items-center space-x-3 py-3 border-b last:border-b-0">
                          {showProductImages && item.image && (
                            <img
                              src={item.image}
                              alt={item.title}
                              className="w-12 h-12 object-cover rounded"
                            />
                          )}
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium truncate">{item.title}</h4>
                            {showProductVariants && item.variant && (
                              <p className="text-xs text-muted-foreground">{item.variant}</p>
                            )}
                            <div className="flex items-center justify-between mt-1">
                              <span className="text-sm font-medium">{formatPrice(item.price)}</span>
                              {enableQuantityEdit && (
                                <div className="flex items-center space-x-1">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => updateQuantity(item.id, Math.max(1, item.quantity - 1))}
                                    className="h-6 w-6 p-0"
                                  >
                                    <Minus className="h-3 w-3" />
                                  </Button>
                                  <span className="text-xs w-6 text-center">{item.quantity}</span>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                    className="h-6 w-6 p-0"
                                  >
                                    <Plus className="h-3 w-3" />
                                  </Button>
                                </div>
                              )}
                              {enableItemRemoval && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeItem(item.id)}
                                  className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    <div className="p-4 border-t bg-muted/50">
                      {showSubtotal && (
                        <div className="flex justify-between items-center mb-3">
                          <span className="font-medium">Subtotal:</span>
                          <span className="font-bold">{formatPrice(getSubtotal())}</span>
                        </div>
                      )}
                      
                      <div className="space-y-2">
                        {showCheckoutButton && (
                          <Button className="w-full" size="sm">
                            <CreditCard className="h-4 w-4 mr-2" />
                            {checkoutButtonText}
                          </Button>
                        )}
                        {showContinueShoppingButton && (
                          <Button variant="outline" className="w-full" size="sm">
                            {continueShoppingText}
                          </Button>
                        )}
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </BaseBlock>
    )
  }

  // Full cart display (for cart page)
  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Shopping Cart</h1>
        
        {cartItems.length === 0 ? (
          <div className="text-center py-12">
            <ShoppingBag className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
            <h2 className="text-xl font-medium mb-2">Your cart is empty</h2>
            <p className="text-muted-foreground mb-6">{emptyCartMessage}</p>
            {showContinueShoppingButton && (
              <Button>
                {continueShoppingText}
              </Button>
            )}
          </div>
        ) : (
          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <div className="space-y-4">
                {cartItems.map((item) => (
                  <Card key={item.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-4">
                        {showProductImages && item.image && (
                          <img
                            src={item.image}
                            alt={item.title}
                            className="w-20 h-20 object-cover rounded"
                          />
                        )}
                        <div className="flex-1">
                          <h3 className="font-medium">{item.title}</h3>
                          {showProductVariants && item.variant && (
                            <p className="text-sm text-muted-foreground">{item.variant}</p>
                          )}
                          <p className="text-sm text-muted-foreground">SKU: {item.sku}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{formatPrice(item.price)}</p>
                          {enableQuantityEdit && (
                            <div className="flex items-center space-x-2 mt-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => updateQuantity(item.id, Math.max(1, item.quantity - 1))}
                              >
                                <Minus className="h-4 w-4" />
                              </Button>
                              <span className="w-12 text-center">{item.quantity}</span>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => updateQuantity(item.id, item.quantity + 1)}
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                          {enableItemRemoval && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeItem(item.id)}
                              className="mt-2 text-destructive hover:text-destructive"
                            >
                              Remove
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
            
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Order Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span>Items ({getTotalItems()})</span>
                    <span>{formatPrice(getSubtotal())}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-bold">
                    <span>Total</span>
                    <span>{formatPrice(getSubtotal())}</span>
                  </div>
                  
                  {showCheckoutButton && (
                    <Button className="w-full" size="lg">
                      <CreditCard className="h-4 w-4 mr-2" />
                      {checkoutButtonText}
                    </Button>
                  )}
                  
                  {showContinueShoppingButton && (
                    <Button variant="outline" className="w-full">
                      {continueShoppingText}
                    </Button>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </BaseBlock>
  )
}

// Cart Widget Block Configuration Component
interface CartWidgetBlockConfigProps {
  config: CartWidgetBlockConfig
  onChange: (config: CartWidgetBlockConfig) => void
}

export function CartWidgetBlockConfig({ config, onChange }: CartWidgetBlockConfigProps) {
  const updateConfig = (updates: Partial<CartWidgetBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  return (
    <div className="space-y-6">
      {/* Display Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Display Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-xs">Display Type</Label>
            <select
              value={config.displayType}
              onChange={(e) => updateConfig({ displayType: e.target.value as any })}
              className="w-full mt-1 px-2 py-1 text-xs border rounded"
            >
              <option value="mini">Mini Cart Widget</option>
              <option value="full">Full Cart Page</option>
              <option value="sidebar">Sidebar Cart</option>
            </select>
          </div>

          {config.displayType === 'mini' && (
            <div>
              <Label className="text-xs">Position</Label>
              <select
                value={config.position}
                onChange={(e) => updateConfig({ position: e.target.value as any })}
                className="w-full mt-1 px-2 py-1 text-xs border rounded"
              >
                <option value="top-right">Top Right</option>
                <option value="top-left">Top Left</option>
                <option value="bottom-right">Bottom Right</option>
                <option value="bottom-left">Bottom Left</option>
              </select>
            </div>
          )}

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Item Count</Label>
              <Switch
                checked={config.showItemCount}
                onCheckedChange={(checked) => updateConfig({ showItemCount: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Subtotal</Label>
              <Switch
                checked={config.showSubtotal}
                onCheckedChange={(checked) => updateConfig({ showSubtotal: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Product Images</Label>
              <Switch
                checked={config.showProductImages}
                onCheckedChange={(checked) => updateConfig({ showProductImages: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Show Product Variants</Label>
              <Switch
                checked={config.showProductVariants}
                onCheckedChange={(checked) => updateConfig({ showProductVariants: checked })}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
