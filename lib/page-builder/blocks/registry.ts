// Block Registry System
// Manages all available block types and their configurations

import { BlockTypeDefinition, BlockCategory } from '../types'
import { storySectionBlockConfig, storySectionBlockSchema } from './story-section-block'
import { valuesGridBlockConfig, valuesGridBlockSchema } from './values-grid-block'
import { missionStatementBlockConfig, missionStatementBlockSchema } from './mission-statement-block'
import { contactInfoBlockConfig, contactInfoBlockSchema } from './contact-info-block'
import { contactFormBlockConfig, contactFormBlockSchema } from './contact-form-block'
import { faqAccordionBlockConfig, faqAccordionBlockSchema } from './faq-accordion-block'
import { brandAssetsBlockConfig, brandAssetsBlockSchema } from './brand-assets-block'
import { legalContentBlockConfig, legalContentBlockSchema } from './legal-content-block'
import { helpTopicsGridBlockConfig, helpTopicsGridBlockSchema } from './help-topics-grid-block'
import { shippingInfoCardsBlockConfig, shippingInfoCardsBlockSchema } from './shipping-info-cards-block'
import { storeLocatorBlockConfig, storeLocatorBlockSchema } from './store-locator-block'
import { newsletterBenefitsBlockConfig, newsletterBenefitsBlockSchema } from './newsletter-benefits-block'
import { featuredCategoriesBlockConfig, featuredCategoriesBlockSchema } from './featured-categories-block'
// Removed deleted blocks: editorial-grid-block, special-offers-banner-block

class BlockRegistry {
  private blocks: Map<string, BlockTypeDefinition> = new Map()
  private categories: Map<BlockCategory, BlockTypeDefinition[]> = new Map()

  /**
   * Register a new block type
   */
  register(blockType: BlockTypeDefinition): void {
    this.blocks.set(blockType.name, blockType)
    
    // Add to category
    if (!this.categories.has(blockType.category)) {
      this.categories.set(blockType.category, [])
    }
    this.categories.get(blockType.category)!.push(blockType)
  }

  /**
   * Get a block type by name
   */
  getBlockType(name: string): BlockTypeDefinition | undefined {
    return this.blocks.get(name)
  }

  /**
   * Get all block types
   */
  getAllBlockTypes(): BlockTypeDefinition[] {
    return Array.from(this.blocks.values()).filter(block => block.isActive)
  }

  /**
   * Get block types by category
   */
  getBlocksByCategory(category: BlockCategory): BlockTypeDefinition[] {
    return this.categories.get(category) || []
  }

  /**
   * Get all categories
   */
  getCategories(): BlockCategory[] {
    return Array.from(this.categories.keys())
  }

  /**
   * Search blocks by name or tags
   */
  searchBlocks(query: string): BlockTypeDefinition[] {
    const searchTerm = query.toLowerCase()
    return this.getAllBlockTypes().filter(block => 
      block.displayName.toLowerCase().includes(searchTerm) ||
      block.description?.toLowerCase().includes(searchTerm) ||
      block.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
    )
  }

  /**
   * Check if a block type exists
   */
  hasBlockType(name: string): boolean {
    return this.blocks.has(name)
  }

  /**
   * Unregister a block type
   */
  unregister(name: string): boolean {
    const blockType = this.blocks.get(name)
    if (!blockType) return false

    this.blocks.delete(name)
    
    // Remove from category
    const categoryBlocks = this.categories.get(blockType.category)
    if (categoryBlocks) {
      const index = categoryBlocks.findIndex(block => block.name === name)
      if (index > -1) {
        categoryBlocks.splice(index, 1)
      }
    }

    return true
  }
}

// Create singleton instance
export const blockRegistry = new BlockRegistry()

// Register default block types
export function registerDefaultBlocks(): void {
  // Hero Block
  blockRegistry.register({
    id: 'hero',
    name: 'hero',
    displayName: 'Hero Section',
    description: 'Large banner section with title, subtitle, and call-to-action button',
    category: 'content',
    icon: '🎯',
    defaultConfig: {
      title: 'Welcome to Our Store',
      subtitle: 'Discover amazing products',
      description: 'Find the perfect items for your needs with our curated collection.',
      backgroundImage: '',
      backgroundVideo: '',
      overlay: {
        enabled: false,
        color: '#000000',
        opacity: 0.5,
      },
      ctaButton: {
        text: 'Shop Now',
        url: '/products',
        style: 'primary',
      },
      alignment: 'center',
      height: 'viewport',
      customHeight: '500px',
    },
    configSchema: {
      type: 'object',
      properties: {
        title: { type: 'string', title: 'Title' },
        subtitle: { type: 'string', title: 'Subtitle' },
        description: { type: 'string', title: 'Description' },
        backgroundImage: { type: 'string', title: 'Background Image URL' },
        backgroundVideo: { type: 'string', title: 'Background Video URL' },
        alignment: { 
          type: 'string', 
          enum: ['left', 'center', 'right'],
          title: 'Text Alignment'
        },
        height: {
          type: 'string',
          enum: ['auto', 'viewport', 'custom'],
          title: 'Section Height'
        },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['banner', 'header', 'cta'],
  })

  // Product Grid Block
  blockRegistry.register({
    id: 'product-grid',
    name: 'product-grid',
    displayName: 'Product Grid',
    description: 'Display products in a responsive grid layout',
    category: 'ecommerce',
    icon: '🛍️',
    defaultConfig: {
      productIds: [],
      categoryIds: [],
      collectionIds: [],
      limit: 8,
      columns: {
        desktop: 4,
        tablet: 3,
        mobile: 2,
      },
      showPrice: true,
      showDescription: false,
      showAddToCart: true,
      sortBy: 'created',
      sortOrder: 'desc',
      filterBy: {
        inStock: true,
        onSale: false,
        featured: false,
      },
    },
    configSchema: {
      type: 'object',
      properties: {
        limit: { type: 'number', title: 'Number of Products', minimum: 1, maximum: 50 },
        showPrice: { type: 'boolean', title: 'Show Price' },
        showDescription: { type: 'boolean', title: 'Show Description' },
        showAddToCart: { type: 'boolean', title: 'Show Add to Cart Button' },
        sortBy: {
          type: 'string',
          enum: ['created', 'price', 'title', 'popularity'],
          title: 'Sort By'
        },
        sortOrder: {
          type: 'string',
          enum: ['asc', 'desc'],
          title: 'Sort Order'
        },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['products', 'grid', 'ecommerce'],
  })

  // Testimonials Block
  blockRegistry.register({
    id: 'testimonials',
    name: 'testimonials',
    displayName: 'Testimonials',
    description: 'Customer testimonials and reviews',
    category: 'marketing',
    icon: '💬',
    defaultConfig: {
      testimonials: [
        {
          id: '1',
          name: 'Sarah Johnson',
          company: 'Happy Customer',
          avatar: '',
          rating: 5,
          content: 'Amazing quality and fast delivery! My kids love their new clothes.',
        },
        {
          id: '2',
          name: 'Mike Chen',
          company: 'Satisfied Parent',
          avatar: '',
          rating: 5,
          content: 'Great selection and excellent customer service. Highly recommended!',
        },
      ],
      layout: 'carousel',
      showRating: true,
      showAvatar: true,
      showCompany: true,
      autoplay: true,
      autoplaySpeed: 5000,
    },
    configSchema: {
      type: 'object',
      properties: {
        layout: {
          type: 'string',
          enum: ['carousel', 'grid', 'single'],
          title: 'Layout'
        },
        showRating: { type: 'boolean', title: 'Show Rating' },
        showAvatar: { type: 'boolean', title: 'Show Avatar' },
        showCompany: { type: 'boolean', title: 'Show Company' },
        autoplay: { type: 'boolean', title: 'Autoplay Carousel' },
        autoplaySpeed: { type: 'number', title: 'Autoplay Speed (ms)', minimum: 1000 },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['testimonials', 'reviews', 'social-proof'],
  })

  // Newsletter Signup Block
  blockRegistry.register({
    id: 'newsletter',
    name: 'newsletter',
    displayName: 'Newsletter Signup',
    description: 'Email newsletter subscription form',
    category: 'marketing',
    icon: '📧',
    defaultConfig: {
      title: 'Stay Updated',
      description: 'Subscribe to our newsletter for the latest updates and exclusive offers.',
      placeholder: 'Enter your email address',
      buttonText: 'Subscribe',
      successMessage: 'Thank you for subscribing!',
      errorMessage: 'Please enter a valid email address.',
      provider: 'custom',
      listId: '',
      tags: [],
    },
    configSchema: {
      type: 'object',
      properties: {
        title: { type: 'string', title: 'Title' },
        description: { type: 'string', title: 'Description' },
        placeholder: { type: 'string', title: 'Input Placeholder' },
        buttonText: { type: 'string', title: 'Button Text' },
        successMessage: { type: 'string', title: 'Success Message' },
        errorMessage: { type: 'string', title: 'Error Message' },
        provider: {
          type: 'string',
          enum: ['mailchimp', 'klaviyo', 'custom'],
          title: 'Email Provider'
        },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['newsletter', 'email', 'subscription'],
  })

  // Text Block
  blockRegistry.register({
    id: 'text',
    name: 'text',
    displayName: 'Text Content',
    description: 'Rich text content with formatting options',
    category: 'content',
    icon: '📝',
    defaultConfig: {
      content: '<p>Add your text content here. You can use <strong>bold</strong>, <em>italic</em>, and other formatting options.</p>',
      textAlign: 'left',
      fontSize: 'medium',
      lineHeight: 'normal',
    },
    configSchema: {
      type: 'object',
      properties: {
        content: {
          type: 'string',
          title: 'Content',
          description: 'Rich text content with formatting options',
          'x-component': 'rich-text',
          'x-component-props': {
            toolbar: ['bold', 'italic', 'underline', 'link', 'list', 'heading'],
            placeholder: 'Enter your text content...'
          }
        },
        textAlign: {
          type: 'string',
          enum: ['left', 'center', 'right', 'justify'],
          title: 'Text Alignment',
          description: 'How the text should be aligned',
          default: 'left'
        },
        fontSize: {
          type: 'string',
          enum: ['small', 'medium', 'large', 'xl'],
          title: 'Font Size',
          description: 'Size of the text',
          default: 'medium'
        },
        lineHeight: {
          type: 'string',
          enum: ['tight', 'normal', 'relaxed'],
          title: 'Line Height',
          description: 'Spacing between lines',
          default: 'normal'
        },
        textColor: {
          type: 'string',
          title: 'Text Color',
          description: 'Color of the text',
          'x-component': 'color-picker',
          default: '#000000'
        },
        backgroundColor: {
          type: 'string',
          title: 'Background Color',
          description: 'Background color of the text block',
          'x-component': 'color-picker',
          default: 'transparent'
        },
        padding: {
          type: 'object',
          title: 'Padding',
          description: 'Internal spacing around the text',
          'x-component': 'spacing-field',
          default: { top: 16, right: 16, bottom: 16, left: 16 }
        },
        margin: {
          type: 'object',
          title: 'Margin',
          description: 'External spacing around the text block',
          'x-component': 'spacing-field',
          default: { top: 0, right: 0, bottom: 0, left: 0 }
        }
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['text', 'content', 'paragraph'],
  })

  // Image Block
  blockRegistry.register({
    id: 'image',
    name: 'image',
    displayName: 'Image',
    description: 'Single image with optional caption and link',
    category: 'media',
    icon: '🖼️',
    defaultConfig: {
      src: '',
      alt: '',
      caption: '',
      link: '',
      width: '100%',
      height: 'auto',
      objectFit: 'cover',
      borderRadius: '0px',
    },
    configSchema: {
      type: 'object',
      properties: {
        src: {
          type: 'string',
          title: 'Image',
          description: 'Select or upload an image',
          'x-component': 'media-field',
          'x-component-props': {
            accept: 'image/*',
            maxSize: '5MB'
          }
        },
        alt: {
          type: 'string',
          title: 'Alt Text',
          description: 'Alternative text for accessibility and SEO',
          placeholder: 'Describe the image...'
        },
        caption: {
          type: 'string',
          title: 'Caption',
          description: 'Optional caption displayed below the image',
          'x-component': 'textarea',
          'x-component-props': { rows: 2 }
        },
        link: {
          type: 'object',
          title: 'Link',
          description: 'Make the image clickable',
          'x-component': 'link-field',
          default: { url: '', text: '', target: '_self' }
        },
        width: {
          type: 'string',
          title: 'Width',
          description: 'Image width (e.g., 100%, 300px, auto)',
          default: '100%'
        },
        height: {
          type: 'string',
          title: 'Height',
          description: 'Image height (e.g., auto, 200px)',
          default: 'auto'
        },
        objectFit: {
          type: 'string',
          enum: ['cover', 'contain', 'fill', 'none', 'scale-down'],
          title: 'Object Fit',
          description: 'How the image should be resized to fit its container',
          default: 'cover'
        },
        borderRadius: {
          type: 'string',
          title: 'Border Radius',
          description: 'Rounded corners (e.g., 0px, 8px, 50%)',
          default: '0px'
        },
        shadow: {
          type: 'object',
          title: 'Shadow',
          description: 'Drop shadow effect',
          'x-component': 'shadow-field',
          default: { enabled: false, x: 0, y: 4, blur: 6, spread: 0, color: '#00000025' }
        },
        lazy: {
          type: 'boolean',
          title: 'Lazy Loading',
          description: 'Load image only when it comes into view',
          default: true
        }
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['image', 'media', 'visual'],
  })

  // Columns Block
  blockRegistry.register({
    id: 'columns',
    name: 'columns',
    displayName: 'Columns',
    description: 'Multi-column layout with responsive behavior',
    category: 'layout',
    icon: '📐',
    defaultConfig: {
      columnCount: 2,
      gap: 'md',
      verticalAlignment: 'top',
      equalHeight: false,
      stackOnMobile: true,
      reverseOnMobile: false,
      backgroundColor: '',
      padding: {
        top: '0px',
        right: '0px',
        bottom: '0px',
        left: '0px',
      },
      borderRadius: '0px',
      columns: [],
    },
    configSchema: {
      type: 'object',
      properties: {
        columnCount: {
          type: 'number',
          enum: [2, 3, 4, 6],
          title: 'Number of Columns'
        },
        gap: {
          type: 'string',
          enum: ['none', 'sm', 'md', 'lg', 'xl'],
          title: 'Gap Size'
        },
        verticalAlignment: {
          type: 'string',
          enum: ['top', 'center', 'bottom', 'stretch'],
          title: 'Vertical Alignment'
        },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['layout', 'columns', 'responsive'],
  })

  // Button Block
  blockRegistry.register({
    id: 'button',
    name: 'button',
    displayName: 'Button',
    description: 'Call-to-action button with customizable styling',
    category: 'content',
    icon: '🔘',
    defaultConfig: {
      text: 'Click Me',
      url: '',
      target: '_self',
      variant: 'primary',
      size: 'medium',
      fullWidth: false,
      disabled: false,
      icon: '',
      iconPosition: 'left',
      animation: 'none',
      backgroundColor: '#007bff',
      textColor: '#ffffff',
      borderColor: '#007bff',
      borderWidth: '1px',
      borderRadius: '6px',
      padding: { top: 12, right: 24, bottom: 12, left: 24 },
      margin: { top: 0, right: 0, bottom: 0, left: 0 },
      shadow: { enabled: false, x: 0, y: 2, blur: 4, spread: 0, color: '#00000025' },
      hover: {
        backgroundColor: '#0056b3',
        textColor: '#ffffff',
        borderColor: '#0056b3',
        transform: 'none',
        shadow: { enabled: false, x: 0, y: 4, blur: 8, spread: 0, color: '#00000040' }
      }
    },
    configSchema: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          title: 'Button Text',
          description: 'The text displayed on the button',
          placeholder: 'Enter button text...'
        },
        url: {
          type: 'object',
          title: 'Link',
          description: 'Where the button should link to',
          'x-component': 'link-field',
          default: { url: '', text: '', target: '_self' }
        },
        variant: {
          type: 'string',
          enum: ['primary', 'secondary', 'outline', 'ghost', 'destructive'],
          title: 'Button Style',
          description: 'Pre-defined button style',
          default: 'primary'
        },
        size: {
          type: 'string',
          enum: ['small', 'medium', 'large'],
          title: 'Button Size',
          description: 'Size of the button',
          default: 'medium'
        },
        fullWidth: {
          type: 'boolean',
          title: 'Full Width',
          description: 'Make button span the full width of its container',
          default: false
        },
        disabled: {
          type: 'boolean',
          title: 'Disabled',
          description: 'Disable the button interaction',
          default: false
        },
        icon: {
          type: 'string',
          title: 'Icon',
          description: 'Optional icon to display with the button',
          'x-component': 'icon-field'
        },
        iconPosition: {
          type: 'string',
          enum: ['left', 'right'],
          title: 'Icon Position',
          description: 'Position of the icon relative to text',
          default: 'left'
        },
        backgroundColor: {
          type: 'string',
          title: 'Background Color',
          description: 'Button background color',
          'x-component': 'color-picker',
          default: '#007bff'
        },
        textColor: {
          type: 'string',
          title: 'Text Color',
          description: 'Button text color',
          'x-component': 'color-picker',
          default: '#ffffff'
        },
        borderColor: {
          type: 'string',
          title: 'Border Color',
          description: 'Button border color',
          'x-component': 'color-picker',
          default: '#007bff'
        },
        borderRadius: {
          type: 'string',
          title: 'Border Radius',
          description: 'Rounded corners (e.g., 6px, 50%)',
          default: '6px'
        },
        padding: {
          type: 'object',
          title: 'Padding',
          description: 'Internal spacing inside the button',
          'x-component': 'spacing-field',
          default: { top: 12, right: 24, bottom: 12, left: 24 }
        },
        shadow: {
          type: 'object',
          title: 'Shadow',
          description: 'Drop shadow effect',
          'x-component': 'shadow-field',
          default: { enabled: false, x: 0, y: 2, blur: 4, spread: 0, color: '#00000025' }
        },
        animation: {
          type: 'string',
          enum: ['none', 'bounce', 'pulse', 'shake', 'fade'],
          title: 'Animation',
          description: 'Animation effect on hover/click',
          default: 'none'
        }
      }
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['button', 'cta', 'action', 'link']
  })

  // Grid Block
  blockRegistry.register({
    id: 'grid',
    name: 'grid',
    displayName: 'Grid',
    description: 'CSS Grid-based layouts for complex arrangements',
    category: 'layout',
    icon: '⚏',
    defaultConfig: {
      columns: {
        desktop: 3,
        tablet: 2,
        mobile: 1,
      },
      rows: 'auto',
      gap: {
        column: 'md',
        row: 'md',
      },
      autoFit: false,
      minItemWidth: '200px',
      maxItemWidth: '1fr',
      aspectRatio: 'auto',
      alignment: 'stretch',
      justifyItems: 'stretch',
      backgroundColor: '',
      padding: {
        top: '0px',
        right: '0px',
        bottom: '0px',
        left: '0px',
      },
      borderRadius: '0px',
      items: [],
    },
    configSchema: {
      type: 'object',
      properties: {
        columns: {
          type: 'object',
          properties: {
            desktop: { type: 'number', title: 'Desktop Columns' },
            tablet: { type: 'number', title: 'Tablet Columns' },
            mobile: { type: 'number', title: 'Mobile Columns' },
          },
        },
        aspectRatio: {
          type: 'string',
          enum: ['auto', 'square', '16/9', '4/3', '3/2'],
          title: 'Aspect Ratio'
        },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['layout', 'grid', 'responsive'],
  })

  // Card Block
  blockRegistry.register({
    id: 'card',
    name: 'card',
    displayName: 'Card',
    description: 'Reusable card component for content organization',
    category: 'layout',
    icon: '🃏',
    defaultConfig: {
      title: 'Card Title',
      subtitle: '',
      content: 'Card content goes here...',
      image: {
        src: '',
        alt: '',
        position: 'top',
        aspectRatio: 'auto',
      },
      button: {
        text: 'Learn More',
        url: '#',
        style: 'primary',
        size: 'md',
      },
      layout: 'vertical',
      alignment: 'left',
      shadow: 'md',
      border: {
        enabled: true,
        width: '1px',
        style: 'solid',
        color: '#e5e7eb',
        radius: '8px',
      },
      padding: {
        top: '16px',
        right: '16px',
        bottom: '16px',
        left: '16px',
      },
      backgroundColor: '#ffffff',
      hoverEffect: 'lift',
      clickable: false,
      clickUrl: '',
    },
    configSchema: {
      type: 'object',
      properties: {
        title: { type: 'string', title: 'Title' },
        content: { type: 'string', title: 'Content' },
        layout: {
          type: 'string',
          enum: ['vertical', 'horizontal'],
          title: 'Layout'
        },
        shadow: {
          type: 'string',
          enum: ['none', 'sm', 'md', 'lg', 'xl'],
          title: 'Shadow'
        },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['layout', 'card', 'content'],
  })

  // Spacer Block
  blockRegistry.register({
    id: 'spacer',
    name: 'spacer',
    displayName: 'Spacer',
    description: 'Add vertical spacing between sections',
    category: 'layout',
    icon: '📏',
    defaultConfig: {
      height: '50px',
      backgroundColor: 'transparent',
    },
    configSchema: {
      type: 'object',
      properties: {
        height: { type: 'string', title: 'Height' },
        backgroundColor: { type: 'string', title: 'Background Color' },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['spacer', 'layout', 'spacing'],
  })

  // Accordion Block
  blockRegistry.register({
    id: 'accordion',
    name: 'accordion',
    displayName: 'Accordion',
    description: 'Collapsible content sections',
    category: 'layout',
    icon: '📋',
    defaultConfig: {
      items: [],
      allowMultiple: false,
      defaultOpen: [],
      style: 'default',
      iconPosition: 'right',
      iconStyle: 'chevron',
      spacing: 'sm',
      backgroundColor: '',
      borderRadius: '8px',
      padding: {
        top: '0px',
        right: '0px',
        bottom: '0px',
        left: '0px',
      },
    },
    configSchema: {
      type: 'object',
      properties: {
        allowMultiple: { type: 'boolean', title: 'Allow Multiple Open' },
        style: {
          type: 'string',
          enum: ['default', 'bordered', 'filled', 'minimal'],
          title: 'Style'
        },
        iconStyle: {
          type: 'string',
          enum: ['chevron', 'plus', 'arrow'],
          title: 'Icon Style'
        },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['layout', 'accordion', 'collapsible'],
  })

  // Tabs Block
  blockRegistry.register({
    id: 'tabs',
    name: 'tabs',
    displayName: 'Tabs',
    description: 'Tabbed content organization',
    category: 'layout',
    icon: '📑',
    defaultConfig: {
      tabs: [],
      defaultActiveTab: 0,
      orientation: 'horizontal',
      style: 'default',
      alignment: 'left',
      size: 'md',
      backgroundColor: '',
      tabBackgroundColor: '',
      activeTabColor: '',
      borderRadius: '8px',
      padding: {
        top: '0px',
        right: '0px',
        bottom: '0px',
        left: '0px',
      },
    },
    configSchema: {
      type: 'object',
      properties: {
        orientation: {
          type: 'string',
          enum: ['horizontal', 'vertical'],
          title: 'Orientation'
        },
        style: {
          type: 'string',
          enum: ['default', 'pills', 'underline', 'bordered'],
          title: 'Style'
        },
        alignment: {
          type: 'string',
          enum: ['left', 'center', 'right', 'justified'],
          title: 'Alignment'
        },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['layout', 'tabs', 'navigation'],
  })

  // Divider Block
  blockRegistry.register({
    id: 'divider',
    name: 'divider',
    displayName: 'Divider',
    description: 'Visual separators with various styles',
    category: 'layout',
    icon: '➖',
    defaultConfig: {
      style: 'solid',
      width: 100,
      thickness: 1,
      color: '#e5e7eb',
      gradientColors: {
        start: '#e5e7eb',
        end: '#e5e7eb',
      },
      alignment: 'center',
      spacing: {
        top: '16px',
        bottom: '16px',
      },
      decorativeStyle: 'dots',
      text: {
        content: '',
        position: 'center',
        backgroundColor: '#ffffff',
        textColor: '#374151',
        fontSize: 'md',
      },
      shadow: {
        enabled: false,
        color: '#00000020',
        blur: 4,
        offset: 2,
      },
    },
    configSchema: {
      type: 'object',
      properties: {
        style: {
          type: 'string',
          enum: ['solid', 'dashed', 'dotted', 'double', 'gradient', 'decorative'],
          title: 'Style'
        },
        width: { type: 'number', title: 'Width (%)' },
        thickness: { type: 'number', title: 'Thickness (px)' },
        alignment: {
          type: 'string',
          enum: ['left', 'center', 'right'],
          title: 'Alignment'
        },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['layout', 'divider', 'separator'],
  })

  // Cart Widget Block
  blockRegistry.register({
    id: 'cart-widget',
    name: 'cart-widget',
    displayName: 'Shopping Cart Widget',
    description: 'Interactive shopping cart widget with mini cart and full cart views',
    category: 'ecommerce',
    icon: '🛒',
    defaultConfig: {
      displayType: 'mini',
      showItemCount: true,
      showSubtotal: true,
      showCheckoutButton: true,
      showContinueShoppingButton: true,
      emptyCartMessage: 'Your cart is empty. Start shopping to add items!',
      checkoutButtonText: 'Proceed to Checkout',
      continueShoppingText: 'Continue Shopping',
      maxItemsToShow: 5,
      showProductImages: true,
      showProductVariants: true,
      enableQuantityEdit: true,
      enableItemRemoval: true,
      autoOpen: false,
      position: 'top-right',
    },
    configSchema: {
      type: 'object',
      properties: {
        displayType: {
          type: 'string',
          enum: ['mini', 'full', 'sidebar'],
          title: 'Display Type'
        },
        showItemCount: { type: 'boolean', title: 'Show Item Count' },
        showSubtotal: { type: 'boolean', title: 'Show Subtotal' },
        showCheckoutButton: { type: 'boolean', title: 'Show Checkout Button' },
        showContinueShoppingButton: { type: 'boolean', title: 'Show Continue Shopping Button' },
        emptyCartMessage: { type: 'string', title: 'Empty Cart Message' },
        checkoutButtonText: { type: 'string', title: 'Checkout Button Text' },
        continueShoppingText: { type: 'string', title: 'Continue Shopping Text' },
        maxItemsToShow: { type: 'number', title: 'Max Items to Show', minimum: 1, maximum: 20 },
        showProductImages: { type: 'boolean', title: 'Show Product Images' },
        showProductVariants: { type: 'boolean', title: 'Show Product Variants' },
        enableQuantityEdit: { type: 'boolean', title: 'Enable Quantity Edit' },
        enableItemRemoval: { type: 'boolean', title: 'Enable Item Removal' },
        position: {
          type: 'string',
          enum: ['top-right', 'top-left', 'bottom-right', 'bottom-left'],
          title: 'Widget Position'
        },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['cart', 'ecommerce', 'shopping', 'widget'],
  })

  // Product Showcase Block
  blockRegistry.register({
    id: 'product-showcase',
    name: 'product-showcase',
    displayName: 'Product Showcase',
    description: 'Featured product display with carousel and highlight options',
    category: 'ecommerce',
    icon: '⭐',
    defaultConfig: {
      layout: 'carousel',
      featuredProductIds: [],
      autoplay: true,
      autoplaySpeed: 5000,
      showDots: true,
      showArrows: true,
      itemsPerView: {
        desktop: 3,
        tablet: 2,
        mobile: 1,
      },
      showPrice: true,
      showDescription: true,
      showAddToCart: true,
      showBadges: true,
      highlightStyle: 'border',
      title: 'Featured Products',
      subtitle: 'Discover our handpicked selection',
    },
    configSchema: {
      type: 'object',
      properties: {
        layout: {
          type: 'string',
          enum: ['carousel', 'grid', 'single'],
          title: 'Layout Style'
        },
        title: { type: 'string', title: 'Section Title' },
        subtitle: { type: 'string', title: 'Section Subtitle' },
        autoplay: { type: 'boolean', title: 'Autoplay Carousel' },
        autoplaySpeed: { type: 'number', title: 'Autoplay Speed (ms)', minimum: 1000 },
        showPrice: { type: 'boolean', title: 'Show Price' },
        showDescription: { type: 'boolean', title: 'Show Description' },
        showAddToCart: { type: 'boolean', title: 'Show Add to Cart Button' },
        showBadges: { type: 'boolean', title: 'Show Product Badges' },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['products', 'featured', 'carousel', 'showcase'],
  })

  // Category Navigation Block
  blockRegistry.register({
    id: 'category-nav',
    name: 'category-nav',
    displayName: 'Category Navigation',
    description: 'Product category navigation with grid or list layout',
    category: 'ecommerce',
    icon: '📂',
    defaultConfig: {
      layout: 'grid',
      columns: {
        desktop: 4,
        tablet: 3,
        mobile: 2,
      },
      showCategoryImages: true,
      showProductCount: true,
      showDescription: false,
      categoryIds: [],
      title: 'Shop by Category',
      subtitle: 'Browse our product categories',
      imageStyle: 'rounded',
      hoverEffect: 'scale',
    },
    configSchema: {
      type: 'object',
      properties: {
        layout: {
          type: 'string',
          enum: ['grid', 'list', 'carousel'],
          title: 'Layout Style'
        },
        title: { type: 'string', title: 'Section Title' },
        subtitle: { type: 'string', title: 'Section Subtitle' },
        showCategoryImages: { type: 'boolean', title: 'Show Category Images' },
        showProductCount: { type: 'boolean', title: 'Show Product Count' },
        showDescription: { type: 'boolean', title: 'Show Category Description' },
        imageStyle: {
          type: 'string',
          enum: ['rounded', 'square', 'circle'],
          title: 'Image Style'
        },
        hoverEffect: {
          type: 'string',
          enum: ['none', 'scale', 'fade', 'lift'],
          title: 'Hover Effect'
        },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['categories', 'navigation', 'ecommerce'],
  })

  // Layout Container Block
  blockRegistry.register({
    id: 'layout-container',
    name: 'layout-container',
    displayName: 'Layout Container',
    description: 'Flexible container for organizing and styling content with advanced layout options',
    category: 'layout',
    icon: '📦',
    defaultConfig: {
      containerType: 'div',
      maxWidth: '1200px',
      padding: {
        top: '2rem',
        right: '1rem',
        bottom: '2rem',
        left: '1rem',
      },
      margin: {
        top: '0',
        right: '0',
        bottom: '0',
        left: '0',
      },
      backgroundColor: 'transparent',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: false,
      centerContent: true,
      fullHeight: false,
      flexDirection: 'column',
      justifyContent: 'start',
      alignItems: 'stretch',
      gap: '1rem',
      borderRadius: '0',
      border: {
        width: '0',
        style: 'none',
        color: '#000000',
      },
      shadow: {
        enabled: false,
        size: 'md',
        color: '#000000',
      },
    },
    configSchema: {
      type: 'object',
      properties: {
        containerType: {
          type: 'string',
          enum: ['container', 'section', 'div', 'article', 'aside'],
          title: 'Container Type'
        },
        maxWidth: { type: 'string', title: 'Max Width' },
        centerContent: { type: 'boolean', title: 'Center Content' },
        fullHeight: { type: 'boolean', title: 'Full Height' },
        flexDirection: {
          type: 'string',
          enum: ['row', 'column'],
          title: 'Flex Direction'
        },
        justifyContent: {
          type: 'string',
          enum: ['start', 'center', 'end', 'between', 'around', 'evenly'],
          title: 'Justify Content'
        },
        alignItems: {
          type: 'string',
          enum: ['start', 'center', 'end', 'stretch'],
          title: 'Align Items'
        },
        gap: { type: 'string', title: 'Gap' },
        backgroundColor: { type: 'string', title: 'Background Color' },
        backgroundImage: { type: 'string', title: 'Background Image URL' },
        borderRadius: { type: 'string', title: 'Border Radius' },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['layout', 'container', 'flexbox', 'structure'],
  })

  // Button Group Block
  blockRegistry.register({
    id: 'button-group',
    name: 'button-group',
    displayName: 'Button Group',
    description: 'Multiple buttons with various layouts',
    category: 'layout',
    icon: '🔘',
    defaultConfig: {
      buttons: [],
      layout: 'horizontal',
      alignment: 'center',
      spacing: 'md',
      size: 'md',
      fullWidth: false,
      stackOnMobile: true,
      gridColumns: 2,
      backgroundColor: '',
      padding: {
        top: '0px',
        right: '0px',
        bottom: '0px',
        left: '0px',
      },
      borderRadius: '0px',
    },
    configSchema: {
      type: 'object',
      properties: {
        layout: {
          type: 'string',
          enum: ['horizontal', 'vertical', 'grid'],
          title: 'Layout'
        },
        alignment: {
          type: 'string',
          enum: ['left', 'center', 'right', 'justified'],
          title: 'Alignment'
        },
        spacing: {
          type: 'string',
          enum: ['none', 'sm', 'md', 'lg', 'xl'],
          title: 'Spacing'
        },
        size: {
          type: 'string',
          enum: ['sm', 'md', 'lg'],
          title: 'Button Size'
        },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['layout', 'buttons', 'actions'],
  })

  // Feature List Block
  blockRegistry.register({
    id: 'feature-list',
    name: 'feature-list',
    displayName: 'Feature List',
    description: 'Structured list of features/benefits',
    category: 'content',
    icon: '✨',
    defaultConfig: {
      title: '',
      subtitle: '',
      features: [],
      layout: 'list',
      columns: {
        desktop: 3,
        tablet: 2,
        mobile: 1,
      },
      iconStyle: 'emoji',
      iconPosition: 'left',
      iconSize: 'md',
      spacing: 'md',
      alignment: 'left',
      showNumbers: false,
      backgroundColor: '',
      padding: {
        top: '0px',
        right: '0px',
        bottom: '0px',
        left: '0px',
      },
      borderRadius: '0px',
    },
    configSchema: {
      type: 'object',
      properties: {
        layout: {
          type: 'string',
          enum: ['list', 'grid', 'cards'],
          title: 'Layout'
        },
        iconStyle: {
          type: 'string',
          enum: ['emoji', 'lucide', 'custom', 'none'],
          title: 'Icon Style'
        },
        iconPosition: {
          type: 'string',
          enum: ['left', 'top'],
          title: 'Icon Position'
        },
        alignment: {
          type: 'string',
          enum: ['left', 'center', 'right'],
          title: 'Alignment'
        },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['content', 'features', 'list'],
  })

  // Search Widget Block
  blockRegistry.register({
    id: 'search-widget',
    name: 'search-widget',
    displayName: 'Search Widget',
    description: 'Search functionality with filters and customizable layout',
    category: 'marketing',
    icon: '🔍',
    defaultConfig: {
      placeholder: 'Search...',
      showFilters: true,
      filters: [],
      searchType: 'products',
      layout: 'horizontal',
      showSearchButton: true,
      showClearButton: true,
      autoSearch: false,
      searchDelay: 500,
      backgroundColor: '',
      borderRadius: '8px',
      padding: {
        top: '16px',
        right: '16px',
        bottom: '16px',
        left: '16px',
      },
      position: 'static',
      fixedPosition: 'top',
    },
    configSchema: {
      type: 'object',
      properties: {
        placeholder: { type: 'string', title: 'Placeholder Text' },
        searchType: {
          type: 'string',
          enum: ['products', 'content', 'locations', 'events'],
          title: 'Search Type'
        },
        layout: {
          type: 'string',
          enum: ['horizontal', 'vertical', 'compact'],
          title: 'Layout'
        },
        showFilters: { type: 'boolean', title: 'Show Filters' },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['widget', 'search', 'filter'],
  })

  // Social Media Widget Block
  blockRegistry.register({
    id: 'social-media-widget',
    name: 'social-media-widget',
    displayName: 'Social Media Widget',
    description: 'Social media links and feeds with customizable styling',
    category: 'marketing',
    icon: '📱',
    defaultConfig: {
      title: 'Follow Us',
      socialLinks: [],
      layout: 'horizontal',
      style: 'icons',
      size: 'md',
      showLabels: true,
      showFollowerCount: false,
      openInNewTab: true,
      alignment: 'center',
      spacing: 'md',
      backgroundColor: '',
      borderRadius: '8px',
      padding: {
        top: '16px',
        right: '16px',
        bottom: '16px',
        left: '16px',
      },
      hoverEffect: 'scale',
    },
    configSchema: {
      type: 'object',
      properties: {
        layout: {
          type: 'string',
          enum: ['horizontal', 'vertical', 'grid'],
          title: 'Layout'
        },
        style: {
          type: 'string',
          enum: ['icons', 'buttons', 'cards'],
          title: 'Style'
        },
        size: {
          type: 'string',
          enum: ['sm', 'md', 'lg'],
          title: 'Size'
        },
        showLabels: { type: 'boolean', title: 'Show Labels' },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['widget', 'social', 'links'],
  })

  // Contact Info Widget Block
  blockRegistry.register({
    id: 'contact-info-widget',
    name: 'contact-info-widget',
    displayName: 'Contact Info Widget',
    description: 'Contact information display with clickable items',
    category: 'marketing',
    icon: '📞',
    defaultConfig: {
      title: 'Contact Information',
      contactItems: [],
      layout: 'vertical',
      style: 'simple',
      showIcons: true,
      clickableItems: true,
      alignment: 'left',
      spacing: 'md',
      backgroundColor: '',
      borderRadius: '8px',
      padding: {
        top: '16px',
        right: '16px',
        bottom: '16px',
        left: '16px',
      },
    },
    configSchema: {
      type: 'object',
      properties: {
        layout: {
          type: 'string',
          enum: ['vertical', 'horizontal', 'grid', 'compact'],
          title: 'Layout'
        },
        style: {
          type: 'string',
          enum: ['simple', 'cards', 'icons'],
          title: 'Style'
        },
        showIcons: { type: 'boolean', title: 'Show Icons' },
        clickableItems: { type: 'boolean', title: 'Clickable Items' },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['widget', 'contact', 'info'],
  })

  // Stats Counter Widget Block
  blockRegistry.register({
    id: 'stats-counter-widget',
    name: 'stats-counter-widget',
    displayName: 'Stats Counter Widget',
    description: 'Animated counters and statistics display',
    category: 'marketing',
    icon: '📊',
    defaultConfig: {
      title: 'Our Statistics',
      stats: [],
      layout: 'horizontal',
      style: 'simple',
      animationType: 'countUp',
      animationDuration: 2,
      showIcons: true,
      showPrefix: true,
      showSuffix: true,
      alignment: 'center',
      spacing: 'md',
      backgroundColor: '',
      borderRadius: '8px',
      padding: {
        top: '16px',
        right: '16px',
        bottom: '16px',
        left: '16px',
      },
    },
    configSchema: {
      type: 'object',
      properties: {
        layout: {
          type: 'string',
          enum: ['horizontal', 'vertical', 'grid'],
          title: 'Layout'
        },
        style: {
          type: 'string',
          enum: ['simple', 'cards', 'highlighted'],
          title: 'Style'
        },
        animationType: {
          type: 'string',
          enum: ['none', 'countUp', 'slideIn', 'fadeIn'],
          title: 'Animation Type'
        },
        showIcons: { type: 'boolean', title: 'Show Icons' },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['widget', 'stats', 'counter'],
  })

  // Progress Bar Widget Block
  blockRegistry.register({
    id: 'progress-bar-widget',
    name: 'progress-bar-widget',
    displayName: 'Progress Bar Widget',
    description: 'Progress indicators and skill bars',
    category: 'content',
    icon: '📈',
    defaultConfig: {
      title: 'Skills & Progress',
      progressBars: [],
      layout: 'vertical',
      showLabels: true,
      showPercentage: true,
      showValues: false,
      animateOnView: true,
      animationDuration: 2,
      spacing: 'md',
      backgroundColor: '',
      borderRadius: '8px',
      padding: {
        top: '16px',
        right: '16px',
        bottom: '16px',
        left: '16px',
      },
    },
    configSchema: {
      type: 'object',
      properties: {
        layout: {
          type: 'string',
          enum: ['vertical', 'horizontal'],
          title: 'Layout'
        },
        showLabels: { type: 'boolean', title: 'Show Labels' },
        showPercentage: { type: 'boolean', title: 'Show Percentage' },
        showValues: { type: 'boolean', title: 'Show Values' },
        animateOnView: { type: 'boolean', title: 'Animate on View' },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['widget', 'progress', 'skills'],
  })

  // Alert Notification Widget Block
  blockRegistry.register({
    id: 'alert-notification-widget',
    name: 'alert-notification-widget',
    displayName: 'Alert Notification Widget',
    description: 'Alerts, announcements, and notifications',
    category: 'marketing',
    icon: '🔔',
    defaultConfig: {
      type: 'info',
      title: '',
      message: 'This is an important notification.',
      showIcon: true,
      dismissible: true,
      autoHide: false,
      autoHideDelay: 5,
      position: 'static',
      fixedPosition: 'top',
      style: 'default',
      size: 'md',
      animation: 'slideIn',
      ctaButton: {
        text: 'Learn More',
        url: '',
        style: 'primary',
      },
      backgroundColor: '',
      textColor: '',
      borderColor: '',
    },
    configSchema: {
      type: 'object',
      properties: {
        type: {
          type: 'string',
          enum: ['info', 'success', 'warning', 'error', 'announcement', 'promotion'],
          title: 'Alert Type'
        },
        style: {
          type: 'string',
          enum: ['default', 'minimal', 'bordered', 'filled'],
          title: 'Style'
        },
        position: {
          type: 'string',
          enum: ['static', 'fixed', 'sticky'],
          title: 'Position'
        },
        showIcon: { type: 'boolean', title: 'Show Icon' },
        dismissible: { type: 'boolean', title: 'Dismissible' },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['widget', 'alert', 'notification'],
  })

  // Clock Timer Widget Block
  blockRegistry.register({
    id: 'clock-timer-widget',
    name: 'clock-timer-widget',
    displayName: 'Clock Timer Widget',
    description: 'Time display and countdown timers',
    category: 'content',
    icon: '🕐',
    defaultConfig: {
      type: 'clock',
      title: 'Current Time',
      format: '12h',
      showSeconds: true,
      showDate: false,
      showTimezone: false,
      timezone: 'UTC',
      countdownTarget: '',
      countdownMessage: "Time's up!",
      style: 'digital',
      size: 'md',
      color: '#000000',
      backgroundColor: '',
      borderRadius: '8px',
      padding: {
        top: '16px',
        right: '16px',
        bottom: '16px',
        left: '16px',
      },
      alignment: 'center',
      showIcon: true,
    },
    configSchema: {
      type: 'object',
      properties: {
        type: {
          type: 'string',
          enum: ['clock', 'countdown', 'stopwatch', 'timezone'],
          title: 'Type'
        },
        format: {
          type: 'string',
          enum: ['12h', '24h'],
          title: 'Format'
        },
        style: {
          type: 'string',
          enum: ['digital', 'analog', 'minimal', 'card'],
          title: 'Style'
        },
        showSeconds: { type: 'boolean', title: 'Show Seconds' },
        showDate: { type: 'boolean', title: 'Show Date' },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['widget', 'clock', 'timer'],
  })

  // Video Player Block
  blockRegistry.register({
    id: 'video-player',
    name: 'video-player',
    displayName: 'Video Player',
    description: 'Video player with YouTube, Vimeo, and custom video support',
    category: 'media',
    icon: '🎥',
    defaultConfig: {
      videoType: 'youtube',
      videoUrl: '',
      videoId: '',
      title: '',
      description: '',
      thumbnail: '',
      autoplay: false,
      muted: false,
      loop: false,
      controls: true,
      showTitle: true,
      showDescription: true,
      aspectRatio: '16:9',
      customAspectRatio: {
        width: 16,
        height: 9
      },
      maxWidth: '100%',
      alignment: 'center',
      playlist: [],
      currentVideoIndex: 0,
      showPlaylist: false,
      playlistPosition: 'bottom',
      backgroundColor: '',
      borderRadius: '8px',
      padding: {
        top: '0px',
        right: '0px',
        bottom: '0px',
        left: '0px',
      },
    },
    configSchema: {
      type: 'object',
      properties: {
        videoType: {
          type: 'string',
          enum: ['youtube', 'vimeo', 'custom', 'playlist'],
          title: 'Video Type'
        },
        videoUrl: { type: 'string', title: 'Video URL' },
        aspectRatio: {
          type: 'string',
          enum: ['16:9', '4:3', '1:1', '21:9', 'custom'],
          title: 'Aspect Ratio'
        },
        autoplay: { type: 'boolean', title: 'Autoplay' },
        controls: { type: 'boolean', title: 'Show Controls' },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['media', 'video', 'player'],
  })

  // Form Builder Block
  blockRegistry.register({
    id: 'form-builder',
    name: 'form-builder',
    displayName: 'Form Builder',
    description: 'Custom forms with validation and email notifications',
    category: 'content',
    icon: '📝',
    defaultConfig: {
      title: 'Contact Us',
      description: 'Get in touch with us',
      fields: [],
      submitButtonText: 'Send Message',
      successMessage: 'Thank you for your message! We\'ll get back to you soon.',
      errorMessage: 'An error occurred. Please try again.',
      redirectUrl: '',
      emailNotification: {
        enabled: true,
        recipient: '',
        subject: 'New Form Submission'
      },
      layout: 'single-column',
      fieldSpacing: 'normal',
      showRequiredIndicator: true,
      backgroundColor: '',
      borderRadius: '8px',
      padding: {
        top: '24px',
        right: '24px',
        bottom: '24px',
        left: '24px',
      },
      maxWidth: '600px',
      alignment: 'center',
    },
    configSchema: {
      type: 'object',
      properties: {
        title: { type: 'string', title: 'Form Title' },
        layout: {
          type: 'string',
          enum: ['single-column', 'two-column'],
          title: 'Layout'
        },
        fieldSpacing: {
          type: 'string',
          enum: ['compact', 'normal', 'relaxed'],
          title: 'Field Spacing'
        },
        showRequiredIndicator: { type: 'boolean', title: 'Show Required Indicator' },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['content', 'form', 'contact'],
  })

  // Image Gallery Block
  blockRegistry.register({
    id: 'image-gallery',
    name: 'image-gallery',
    displayName: 'Image Gallery',
    description: 'Image galleries with lightbox and filtering',
    category: 'media',
    icon: '🖼️',
    defaultConfig: {
      title: 'Gallery',
      subtitle: '',
      images: [],
      layout: 'grid',
      columns: {
        desktop: 3,
        tablet: 2,
        mobile: 1,
      },
      aspectRatio: 'auto',
      spacing: 'md',
      showCaptions: true,
      showOverlay: true,
      enableLightbox: true,
      enableDownload: false,
      enableFiltering: false,
      filterTags: [],
      autoplay: false,
      autoplaySpeed: 3000,
      showThumbnails: false,
      thumbnailPosition: 'bottom',
      backgroundColor: '',
      borderRadius: '8px',
      padding: {
        top: '0px',
        right: '0px',
        bottom: '0px',
        left: '0px',
      },
      maxWidth: '100%',
      alignment: 'center',
    },
    configSchema: {
      type: 'object',
      properties: {
        layout: {
          type: 'string',
          enum: ['grid', 'masonry', 'carousel', 'justified'],
          title: 'Layout'
        },
        aspectRatio: {
          type: 'string',
          enum: ['auto', 'square', '16:9', '4:3', '3:2'],
          title: 'Aspect Ratio'
        },
        showCaptions: { type: 'boolean', title: 'Show Captions' },
        enableLightbox: { type: 'boolean', title: 'Enable Lightbox' },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['media', 'gallery', 'images'],
  })

  // Code Block
  blockRegistry.register({
    id: 'code',
    name: 'code',
    displayName: 'Code Editor',
    description: 'Advanced code editor with syntax highlighting and live preview',
    category: 'content',
    icon: '💻',
    defaultConfig: {
      code: {
        html: '<div class="example">\n  <h2>Hello World!</h2>\n  <p>This is an example HTML snippet.</p>\n</div>',
        css: '.example {\n  padding: 20px;\n  background: #f5f5f5;\n  border-radius: 8px;\n  text-align: center;\n}\n\n.example h2 {\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.example p {\n  color: #666;\n  margin: 0;\n}',
        javascript: '// Interactive functionality\ndocument.addEventListener("DOMContentLoaded", function() {\n  console.log("Code block loaded!");\n  \n  // Add click handler to example\n  const example = document.querySelector(".example");\n  if (example) {\n    example.addEventListener("click", function() {\n      alert("Hello from the code block!");\n    });\n  }\n});'
      },
      activeLanguage: 'html',
      showPreview: true,
      enabledLanguages: ['html', 'css', 'javascript'],
      theme: 'light',
      height: '400px',
      readOnly: false,
      showLineNumbers: true,
      enableSearch: true,
      enableAutocompletion: true
    },
    configSchema: {
      type: 'object',
      properties: {
        activeLanguage: {
          type: 'string',
          enum: ['html', 'css', 'javascript', 'typescript', 'jsx', 'tsx', 'json'],
          title: 'Active Language'
        },
        theme: {
          type: 'string',
          enum: ['light', 'dark'],
          title: 'Editor Theme'
        },
        height: { type: 'string', title: 'Editor Height' },
        showPreview: { type: 'boolean', title: 'Show Preview' },
        readOnly: { type: 'boolean', title: 'Read Only' },
        showLineNumbers: { type: 'boolean', title: 'Show Line Numbers' },
        enableSearch: { type: 'boolean', title: 'Enable Search' },
        enableAutocompletion: { type: 'boolean', title: 'Enable Auto-completion' }
      }
    },
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['code', 'editor', 'development', 'syntax', 'programming'],
  })

  // Map Block
  blockRegistry.register({
    id: 'map',
    name: 'map',
    displayName: 'Map',
    description: 'Interactive maps with markers and contact information',
    category: 'content',
    icon: '🗺️',
    defaultConfig: {
      title: 'Find Us',
      description: 'Visit our locations',
      mapType: 'google',
      apiKey: '',
      center: {
        lat: -26.2041,
        lng: 28.0473
      },
      zoom: 12,
      height: '400px',
      markers: [],
      showControls: true,
      showZoomControls: true,
      showStreetView: false,
      showFullscreenControl: true,
      enableScrollWheel: true,
      mapStyle: 'roadmap',
      customStyle: '',
      showDirections: true,
      showContactInfo: true,
      contactInfo: {
        address: '',
        phone: '',
        email: '',
        hours: '',
        website: ''
      },
      backgroundColor: '',
      borderRadius: '8px',
      padding: {
        top: '0px',
        right: '0px',
        bottom: '0px',
        left: '0px',
      },
      maxWidth: '100%',
      alignment: 'center',
    },
    configSchema: {
      type: 'object',
      properties: {
        mapType: {
          type: 'string',
          enum: ['google', 'openstreetmap', 'mapbox'],
          title: 'Map Type'
        },
        mapStyle: {
          type: 'string',
          enum: ['roadmap', 'satellite', 'hybrid', 'terrain'],
          title: 'Map Style'
        },
        showContactInfo: { type: 'boolean', title: 'Show Contact Info' },
        showDirections: { type: 'boolean', title: 'Show Directions' },
      },
    },
    isActive: true,
    isSystem: true,
    version: '1.0.0',
    tags: ['content', 'map', 'location'],
  })

  // Story Section Block
  blockRegistry.register({
    id: 'story-section',
    name: 'story-section',
    displayName: 'Story Section',
    description: 'Content section with text and image for storytelling',
    category: 'content',
    icon: '📖',
    defaultConfig: storySectionBlockConfig,
    configSchema: storySectionBlockSchema,
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['story', 'content', 'image', 'text'],
  })

  // Values Grid Block
  blockRegistry.register({
    id: 'values-grid',
    name: 'values-grid',
    displayName: 'Values Grid',
    description: 'Display company values with icons in a grid layout',
    category: 'content',
    icon: '⭐',
    defaultConfig: valuesGridBlockConfig,
    configSchema: valuesGridBlockSchema,
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['values', 'grid', 'icons', 'company'],
  })

  // Mission Statement Block
  blockRegistry.register({
    id: 'mission-statement',
    name: 'mission-statement',
    displayName: 'Mission Statement',
    description: 'Highlighted mission or vision statement with background styling',
    category: 'content',
    icon: '🎯',
    defaultConfig: missionStatementBlockConfig,
    configSchema: missionStatementBlockSchema,
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['mission', 'statement', 'vision', 'company'],
  })

  // Contact Info Block
  blockRegistry.register({
    id: 'contact-info',
    name: 'contact-info',
    displayName: 'Contact Information',
    description: 'Display contact details with icons and links',
    category: 'content',
    icon: '📞',
    defaultConfig: contactInfoBlockConfig,
    configSchema: contactInfoBlockSchema,
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['contact', 'info', 'details', 'communication'],
  })

  // Contact Form Block
  blockRegistry.register({
    id: 'contact-form',
    name: 'contact-form',
    displayName: 'Contact Form',
    description: 'Customizable contact form with validation and submission',
    category: 'content',
    icon: '📝',
    defaultConfig: contactFormBlockConfig,
    configSchema: contactFormBlockSchema,
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['contact', 'form', 'submission', 'validation'],
  })

  // FAQ Accordion Block
  blockRegistry.register({
    id: 'faq-accordion',
    name: 'faq-accordion',
    displayName: 'FAQ Accordion',
    description: 'Expandable FAQ section with search and categories',
    category: 'content',
    icon: '❓',
    defaultConfig: faqAccordionBlockConfig,
    configSchema: faqAccordionBlockSchema,
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['faq', 'accordion', 'questions', 'help'],
  })

  // Brand Assets Block
  blockRegistry.register({
    id: 'brand-assets',
    name: 'brand-assets',
    displayName: 'Brand Assets',
    description: 'Showcase brand assets including logos, colors, and typography',
    category: 'content',
    icon: '🎨',
    defaultConfig: brandAssetsBlockConfig,
    configSchema: brandAssetsBlockSchema,
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['brand', 'assets', 'logo', 'colors', 'typography'],
  })

  // Legal Content Block
  blockRegistry.register({
    id: 'legal-content',
    name: 'legal-content',
    displayName: 'Legal Content',
    description: 'Structured legal content with table of contents and sections',
    category: 'content',
    icon: '📄',
    defaultConfig: legalContentBlockConfig,
    configSchema: legalContentBlockSchema,
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['legal', 'privacy', 'terms', 'policy'],
  })

  // Help Topics Grid Block
  blockRegistry.register({
    id: 'help-topics-grid',
    name: 'help-topics-grid',
    displayName: 'Help Topics Grid',
    description: 'Grid of help topics with icons and search functionality',
    category: 'content',
    icon: '🆘',
    defaultConfig: helpTopicsGridBlockConfig,
    configSchema: helpTopicsGridBlockSchema,
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['help', 'support', 'topics', 'grid'],
  })

  // Shipping Info Cards Block
  blockRegistry.register({
    id: 'shipping-info-cards',
    name: 'shipping-info-cards',
    displayName: 'Shipping Info Cards',
    description: 'Display shipping options with pricing and features in card format',
    category: 'content',
    icon: '🚚',
    defaultConfig: shippingInfoCardsBlockConfig,
    configSchema: shippingInfoCardsBlockSchema,
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['shipping', 'delivery', 'cards', 'pricing'],
  })

  // Store Locator Block
  blockRegistry.register({
    id: 'store-locator',
    name: 'store-locator',
    displayName: 'Store Locator',
    description: 'Interactive store finder with search and location details',
    category: 'content',
    icon: '📍',
    defaultConfig: storeLocatorBlockConfig,
    configSchema: storeLocatorBlockSchema,
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['stores', 'locations', 'map', 'search'],
  })

  // Newsletter Benefits Block
  blockRegistry.register({
    id: 'newsletter-benefits',
    name: 'newsletter-benefits',
    displayName: 'Newsletter Benefits',
    description: 'Showcase newsletter subscription benefits with icons and descriptions',
    category: 'content',
    icon: '📧',
    defaultConfig: newsletterBenefitsBlockConfig,
    configSchema: newsletterBenefitsBlockSchema,
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['newsletter', 'benefits', 'subscription', 'marketing'],
  })

  // Featured Categories Block
  blockRegistry.register({
    id: 'featured-categories',
    name: 'featured-categories',
    displayName: 'Featured Categories',
    description: 'Zara-style category grid with image overlays and hover effects',
    category: 'content',
    icon: '🏷️',
    defaultConfig: featuredCategoriesBlockConfig,
    configSchema: featuredCategoriesBlockSchema,
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['categories', 'grid', 'zara', 'fashion', 'navigation'],
  })

  // Editorial Grid Block (redirects to editorial-section)
  blockRegistry.register({
    id: 'editorial-grid',
    name: 'editorial-grid',
    displayName: 'Editorial Grid',
    description: 'Zara-style asymmetric editorial content grid with overlays and effects',
    category: 'content',
    icon: '🖼️',
    defaultConfig: {
      items: [
        {
          id: 'spring-essentials',
          title: 'SPRING ESSENTIALS',
          description: 'Discover the season\'s must-have pieces',
          image: '/assets/images/cocomilk_kids-20220829_111232-3780725228.jpg',
          link: '/collections/editorial',
          size: 'large'
        }
      ],
      layout: 'asymmetric',
      style: 'zara',
      spacing: 'normal',
      animation: { enabled: true, type: 'slide', stagger: 0.1 },
      backgroundColor: '#ffffff'
    },
    configSchema: {
      type: 'object',
      properties: {
        layout: {
          type: 'string',
          enum: ['asymmetric', 'grid'],
          title: 'Layout Style'
        },
        style: {
          type: 'string',
          enum: ['zara', 'minimal', 'modern'],
          title: 'Visual Style'
        }
      }
    },
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['editorial', 'grid', 'zara', 'magazine', 'asymmetric'],
  })

  // Special Offers Banner Block (redirects to landing special offers)
  blockRegistry.register({
    id: 'special-offers-banner',
    name: 'special-offers-banner',
    displayName: 'Special Offers Banner',
    description: 'Promotional banner with offers, CTAs, and urgency messaging',
    category: 'content',
    icon: '🎯',
    defaultConfig: {
      title: 'SPECIAL OFFER',
      subtitle: 'Limited Time Only',
      description: 'Get 20% off on all items',
      ctaText: 'SHOP NOW',
      ctaUrl: '/products',
      backgroundColor: '#000000',
      textColor: '#ffffff',
      urgency: true,
      countdown: false
    },
    configSchema: {
      type: 'object',
      properties: {
        title: { type: 'string', title: 'Title' },
        subtitle: { type: 'string', title: 'Subtitle' },
        description: { type: 'string', title: 'Description' },
        ctaText: { type: 'string', title: 'CTA Button Text' },
        ctaUrl: { type: 'string', title: 'CTA Button URL' },
        urgency: { type: 'boolean', title: 'Show Urgency' },
        countdown: { type: 'boolean', title: 'Show Countdown' }
      }
    },
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['offers', 'banner', 'promotion', 'cta', 'marketing'],
  })

  // E-commerce Blocks

  // Product Listing Block
  blockRegistry.register({
    id: 'product-listing',
    name: 'product-listing',
    displayName: 'Product Listing',
    description: 'Complete product listing page with filters, sorting, and grid - exactly like hardcoded products page',
    category: 'ecommerce',
    icon: '🛍️',
    defaultConfig: {
      title: 'All Products',
      showFilters: true,
      showSort: true,
      showMobileFilters: true,
      filtersPosition: 'left'
    },
    configSchema: {},
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['products', 'listing', 'filters', 'ecommerce'],
  })

  // Cart Block
  blockRegistry.register({
    id: 'cart',
    name: 'cart',
    displayName: 'Shopping Cart',
    description: 'Shopping cart page with items, summary, and checkout - exactly like hardcoded cart page',
    category: 'ecommerce',
    icon: '🛒',
    defaultConfig: {},
    configSchema: {},
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['cart', 'shopping', 'checkout', 'ecommerce'],
  })

  // Checkout Block
  blockRegistry.register({
    id: 'checkout',
    name: 'checkout',
    displayName: 'Checkout',
    description: 'Checkout page with forms and order summary - exactly like hardcoded checkout page',
    category: 'ecommerce',
    icon: '💳',
    defaultConfig: {},
    configSchema: {},
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['checkout', 'payment', 'order', 'ecommerce'],
  })

  // Product Details Block
  blockRegistry.register({
    id: 'product-details',
    name: 'product-details',
    displayName: 'Product Details',
    description: 'Product detail page with gallery, info, and recommendations - exactly like hardcoded product page',
    category: 'ecommerce',
    icon: '📦',
    defaultConfig: {},
    configSchema: {},
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['product', 'details', 'gallery', 'ecommerce'],
  })

  // Wishlist Block
  blockRegistry.register({
    id: 'wishlist',
    name: 'wishlist',
    displayName: 'Wishlist',
    description: 'Wishlist page with saved products - exactly like hardcoded wishlist page',
    category: 'ecommerce',
    icon: '❤️',
    defaultConfig: {},
    configSchema: {},
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['wishlist', 'favorites', 'saved', 'ecommerce'],
  })

  // Collection Header Block
  blockRegistry.register({
    id: 'collection-header',
    name: 'collection-header',
    displayName: 'Collection Header',
    description: 'Collection page header with background, title, and product count',
    category: 'ecommerce',
    icon: '🏷️',
    defaultConfig: {
      title: 'Collection Name',
      showProductCount: true,
      showBreadcrumbs: true,
      height: 'medium',
      alignment: 'center'
    },
    configSchema: {},
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['collection', 'header', 'banner', 'ecommerce'],
  })

  // Account Dashboard Block
  blockRegistry.register({
    id: 'account-dashboard',
    name: 'account-dashboard',
    displayName: 'Account Dashboard',
    description: 'User account dashboard with orders, profile, and account management',
    category: 'ecommerce',
    icon: '👤',
    defaultConfig: {
      showProfile: true,
      showOrders: true,
      showWishlist: true,
      showAddresses: true,
      showPaymentMethods: true,
      showRecentActivity: true,
      recentOrdersLimit: 5
    },
    configSchema: {},
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['account', 'dashboard', 'profile', 'orders', 'ecommerce'],
  })

  // Product Comparison Block
  blockRegistry.register({
    id: 'product-comparison',
    name: 'product-comparison',
    displayName: 'Product Comparison',
    description: 'Side-by-side product comparison with features and pricing',
    category: 'ecommerce',
    icon: '⚖️',
    defaultConfig: {
      maxProducts: 4,
      showFeatures: ['price', 'colors', 'sizes', 'description'],
      showPricing: true,
      showImages: true,
      showAddToCart: true
    },
    configSchema: {},
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['comparison', 'products', 'features', 'ecommerce'],
  })

  // Landing Page Blocks

  // Hero Section Block
  blockRegistry.register({
    id: 'hero-section',
    name: 'hero-section',
    displayName: 'Hero Section',
    description: 'Full-screen hero section with background image/video and call-to-action - exactly like hardcoded hero',
    category: 'content',
    icon: '🎬',
    defaultConfig: {
      title: 'KIDS COLLECTION',
      backgroundImage: '/assets/images/cocomilk_kids-20210912_114630-3065525289.jpg',
      overlay: { enabled: true, color: '#000000', opacity: 0.2 },
      ctaButton: { text: 'SHOP NOW', url: '/products', style: 'minimal' },
      contentPosition: 'bottom-left',
      height: 'viewport',
      textColor: '#ffffff',
      animation: { enabled: true, type: 'fade', duration: 1, delay: 0 },
      style: 'zara'
    },
    configSchema: {},
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['hero', 'banner', 'landing', 'cta'],
  })

  // New Arrivals Block
  blockRegistry.register({
    id: 'new-arrivals',
    name: 'new-arrivals',
    displayName: 'New Arrivals',
    description: 'New arrivals product section with real product data - exactly like hardcoded component',
    category: 'ecommerce',
    icon: '✨',
    defaultConfig: {
      title: 'NEW IN',
      limit: 6,
      showViewAllLink: true,
      viewAllText: 'VIEW ALL NEW IN',
      viewAllUrl: '/collections/new-arrivals',
      layout: 'grid',
      columns: { desktop: 3, tablet: 2, mobile: 2 },
      spacing: 'normal',
      style: 'zara'
    },
    configSchema: {},
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['products', 'new-arrivals', 'landing', 'ecommerce'],
  })

  // Editorial Section Block
  blockRegistry.register({
    id: 'editorial-section',
    name: 'editorial-section',
    displayName: 'Editorial Section',
    description: 'Zara-style asymmetric editorial grid with images and content - exactly like hardcoded component',
    category: 'content',
    icon: '📰',
    defaultConfig: {
      items: [
        {
          id: 'spring-essentials',
          title: 'SPRING ESSENTIALS',
          description: 'Discover the season\'s must-have pieces',
          image: '/assets/images/cocomilk_kids-20220829_111232-3780725228.jpg',
          link: '/collections/editorial',
          size: 'large'
        }
      ],
      layout: 'asymmetric',
      style: 'zara',
      spacing: 'normal',
      animation: { enabled: true, type: 'slide', stagger: 0.1 },
      backgroundColor: '#ffffff'
    },
    configSchema: {},
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['editorial', 'content', 'landing', 'zara'],
  })

  // Newsletter Signup Block
  blockRegistry.register({
    id: 'newsletter-signup',
    name: 'newsletter-signup',
    displayName: 'Newsletter Signup',
    description: 'Newsletter subscription form with real email integration - exactly like hardcoded component',
    category: 'marketing',
    icon: '📧',
    defaultConfig: {
      title: 'Stay Updated',
      description: 'Subscribe to our newsletter for the latest updates and exclusive offers.',
      placeholder: 'Email address',
      buttonText: 'SUBSCRIBE',
      successMessage: 'Thank you for subscribing',
      style: 'zara',
      layout: 'vertical',
      backgroundColor: 'transparent',
      textColor: '#000000',
      showIcon: true,
      animation: { enabled: true, type: 'fade' }
    },
    configSchema: {},
    isActive: true,
    isSystem: false,
    version: '1.0.0',
    tags: ['newsletter', 'signup', 'marketing', 'email'],
  })
}

// Initialize default blocks
registerDefaultBlocks()
