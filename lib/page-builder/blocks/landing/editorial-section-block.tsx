'use client'

import React from 'react'
import { PageBlock } from '../../types'
import { BaseBlock } from '../base-block'
import { EditorialSection } from '@/components/editorial-section'

interface EditorialSectionBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function EditorialSectionBlock({ block, isEditing = false }: EditorialSectionBlockProps) {
  // Simply render the existing EditorialSection component
  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <EditorialSection />
    </BaseBlock>
  )
}
