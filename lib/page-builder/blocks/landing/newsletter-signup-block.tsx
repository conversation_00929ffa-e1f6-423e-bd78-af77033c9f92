'use client'

import React, { useState } from 'react'
import { PageBlock } from '../../types'
import { BaseBlock } from '../base-block'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { toast } from '@/components/ui/use-toast'
import { Mail, Check } from 'lucide-react'
import { motion } from 'framer-motion'
// Newsletter API integration

interface NewsletterSignupBlockConfig {
  title: string
  subtitle?: string
  description?: string
  placeholder: string
  buttonText: string
  successMessage: string
  style: 'minimal' | 'zara' | 'modern' | 'card'
  layout: 'horizontal' | 'vertical' | 'inline'
  backgroundColor: string
  textColor: string
  showIcon: boolean
  animation: {
    enabled: boolean
    type: 'fade' | 'slide' | 'scale'
  }
  privacy: {
    enabled: boolean
    text: string
    linkText: string
    linkUrl: string
  }
}

interface NewsletterSignupBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function NewsletterSignupBlock({ block, isEditing = false }: NewsletterSignupBlockProps) {
  const config = block.configuration as NewsletterSignupBlockConfig
  const [email, setEmail] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isSubscribed, setIsSubscribed] = useState(false)

  const {
    title = 'Stay Updated',
    subtitle,
    description = 'Subscribe to our newsletter for the latest updates and exclusive offers.',
    placeholder = 'Email address',
    buttonText = 'SUBSCRIBE',
    successMessage = 'Thank you for subscribing',
    style = 'zara',
    layout = 'vertical',
    backgroundColor = 'transparent',
    textColor = '#000000',
    showIcon = true,
    animation = { enabled: true, type: 'fade' },
    privacy = {
      enabled: true,
      text: 'By subscribing, you agree to our',
      linkText: 'Privacy Policy',
      linkUrl: '/privacy-policy'
    }
  } = config

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email || isLoading || isEditing) return

    setIsLoading(true)

    try {
      // Use real newsletter API
      const response = await fetch('/api/e-commerce/newsletter/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email })
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: 'Successfully subscribed!',
          description: 'Thank you for subscribing to our newsletter.',
        })

        setIsSubscribed(true)
        setEmail('')
      } else {
        toast({
          title: 'Subscription failed',
          description: result.message || 'Please try again later.',
          variant: 'destructive'
        })
      }
    } catch (error) {
      toast({
        title: 'Subscription failed',
        description: 'Please try again later.',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getAnimationProps = () => {
    if (!animation.enabled) return {}

    switch (animation.type) {
      case 'fade':
        return {
          initial: { opacity: 0 },
          whileInView: { opacity: 1 },
          transition: { duration: 0.6 },
          viewport: { once: true }
        }
      case 'slide':
        return {
          initial: { opacity: 0, y: 20 },
          whileInView: { opacity: 1, y: 0 },
          transition: { duration: 0.6 },
          viewport: { once: true }
        }
      case 'scale':
        return {
          initial: { opacity: 0, scale: 0.95 },
          whileInView: { opacity: 1, scale: 1 },
          transition: { duration: 0.6 },
          viewport: { once: true }
        }
      default:
        return {}
    }
  }

  const renderZaraStyle = () => (
    <section className="py-16 md:py-24" style={{ backgroundColor, color: textColor }}>
      <div className="container px-4 md:px-6 max-w-2xl">
        <motion.div {...getAnimationProps()} className="text-center space-y-8">
          {/* Header - exactly like hardcoded */}
          <div className="space-y-4">
            {showIcon && (
              <div className="flex justify-center">
                <Mail className="h-8 w-8" style={{ color: textColor }} />
              </div>
            )}
            <h2 className="text-2xl md:text-3xl font-light tracking-wide">
              {title}
            </h2>
            {subtitle && (
              <h3 className="text-lg md:text-xl font-light opacity-80">
                {subtitle}
              </h3>
            )}
            {description && (
              <p className="text-base font-light opacity-70">
                {description}
              </p>
            )}
          </div>

          {/* Form - exactly like hardcoded */}
          {isSubscribed ? (
            <div className="flex items-center justify-center space-x-2 text-lg">
              <Check className="h-5 w-5 text-green-600" />
              <span>{successMessage}</span>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="max-w-md mx-auto">
              <div className="flex border-b border-current">
                <Input
                  type="email"
                  placeholder={placeholder}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled={isEditing}
                  className="border-0 bg-transparent placeholder:opacity-50 focus-visible:ring-0 focus-visible:ring-offset-0 px-0"
                  style={{ color: textColor }}
                />
                <button
                  type="submit"
                  disabled={isLoading || isEditing}
                  className="text-sm font-medium tracking-wider hover:opacity-60 transition-opacity duration-200 px-4 py-2"
                  style={{ color: textColor }}
                >
                  {isLoading ? '...' : buttonText}
                </button>
              </div>
              
              {/* Privacy notice */}
              {privacy.enabled && (
                <p className="text-xs opacity-60 mt-4">
                  {privacy.text}{' '}
                  <a 
                    href={privacy.linkUrl} 
                    className="underline hover:no-underline"
                    style={{ color: textColor }}
                  >
                    {privacy.linkText}
                  </a>
                </p>
              )}
            </form>
          )}
        </motion.div>
      </div>
    </section>
  )

  const renderModernStyle = () => (
    <section className="py-16 md:py-24" style={{ backgroundColor }}>
      <div className="container px-4 md:px-6 max-w-4xl">
        <motion.div {...getAnimationProps()} className="text-center">
          <div className="bg-white rounded-lg shadow-lg p-8 md:p-12">
            <div className="space-y-6">
              {showIcon && (
                <div className="flex justify-center">
                  <div className="bg-primary/10 p-4 rounded-full">
                    <Mail className="h-8 w-8 text-primary" />
                  </div>
                </div>
              )}
              
              <div className="space-y-2">
                <h2 className="text-2xl md:text-3xl font-bold">{title}</h2>
                {subtitle && <h3 className="text-lg text-muted-foreground">{subtitle}</h3>}
                {description && <p className="text-muted-foreground">{description}</p>}
              </div>

              {isSubscribed ? (
                <div className="flex items-center justify-center space-x-2 text-lg text-green-600">
                  <Check className="h-5 w-5" />
                  <span>{successMessage}</span>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="max-w-md mx-auto">
                  <div className="flex gap-2">
                    <Input
                      type="email"
                      placeholder={placeholder}
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      disabled={isEditing}
                      className="flex-1"
                    />
                    <Button 
                      type="submit" 
                      disabled={isLoading || isEditing}
                      className="px-6"
                    >
                      {isLoading ? '...' : buttonText}
                    </Button>
                  </div>
                  
                  {privacy.enabled && (
                    <p className="text-xs text-muted-foreground mt-3">
                      {privacy.text}{' '}
                      <a href={privacy.linkUrl} className="underline hover:no-underline">
                        {privacy.linkText}
                      </a>
                    </p>
                  )}
                </form>
              )}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      {style === 'zara' ? renderZaraStyle() : renderModernStyle()}
    </BaseBlock>
  )
}
