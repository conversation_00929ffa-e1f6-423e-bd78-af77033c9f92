'use client'

import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { PageBlock } from '../../types'
import { BaseBlock } from '../base-block'

interface SpecialOffersBannerBlockConfig {
  title: string
  subtitle?: string
  description?: string
  buttonText: string
  buttonLink: string
  image: {
    url: string
    alt: string
  }
  layout: 'split' | 'overlay' | 'centered'
  style: 'zara' | 'minimal' | 'modern'
  backgroundColor: string
  textColor: string
  spacing: 'compact' | 'normal' | 'spacious'
  animation: {
    enabled: boolean
    type: 'fade' | 'slide' | 'scale'
    stagger: number
  }
}

interface SpecialOffersBannerBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function SpecialOffersBannerBlock({ block, isEditing = false }: SpecialOffersBannerBlockProps) {
  const config = block.configuration as SpecialOffersBannerBlockConfig
  
  const {
    title = 'SALE',
    subtitle,
    description = 'Up to 50% off selected items',
    buttonText = 'SHOP SALE',
    buttonLink = '/collections/sale',
    image = {
      url: '/assets/images/cocomilk_kids-20220906_082318-3283935526.jpg',
      alt: 'Sale collection'
    },
    layout = 'split',
    style = 'zara',
    backgroundColor = '#000000',
    textColor = '#ffffff',
    spacing = 'normal',
    animation = { enabled: true, type: 'slide', stagger: 0.1 }
  } = config

  const getSpacingClass = () => {
    switch (spacing) {
      case 'compact': return 'py-12 md:py-16'
      case 'spacious': return 'py-20 md:py-32'
      default: return 'py-16 md:py-24'
    }
  }

  const getGapClass = () => {
    switch (spacing) {
      case 'compact': return 'gap-6 md:gap-12'
      case 'spacious': return 'gap-12 md:gap-20'
      default: return 'gap-8 md:gap-16'
    }
  }

  const getAnimationProps = (index: number) => {
    if (!animation.enabled) return {}

    const delay = index * animation.stagger

    switch (animation.type) {
      case 'fade':
        return {
          initial: { opacity: 0 },
          whileInView: { opacity: 1 },
          transition: { duration: 0.6, delay },
          viewport: { once: true }
        }
      case 'slide':
        return {
          initial: { opacity: 0, y: 20 },
          whileInView: { opacity: 1, y: 0 },
          transition: { duration: 0.6, delay },
          viewport: { once: true }
        }
      case 'scale':
        return {
          initial: { opacity: 0, scale: 0.95 },
          whileInView: { opacity: 1, scale: 1 },
          transition: { duration: 0.6, delay },
          viewport: { once: true }
        }
      default:
        return {
          initial: { opacity: 0, y: 20 },
          whileInView: { opacity: 1, y: 0 },
          transition: { duration: 0.6, delay },
          viewport: { once: true }
        }
    }
  }

  const renderSplitLayout = () => (
    <div className={`grid grid-cols-1 lg:grid-cols-2 ${getGapClass()} items-center`}>
      {/* Zara-style minimal text - exactly like hardcoded */}
      <motion.div
        {...getAnimationProps(0)}
        className="space-y-6 md:space-y-8"
      >
        <div className="space-y-4">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-light leading-tight">
            {title}
          </h2>
          {subtitle && (
            <h3 className="text-2xl md:text-3xl font-light" style={{ color: textColor }}>
              {subtitle}
            </h3>
          )}
          {description && (
            <p className="text-lg md:text-xl font-light text-gray-300">
              {description}
            </p>
          )}
        </div>
        <Link
          href={buttonLink}
          className="inline-block bg-white text-black px-8 py-3 text-sm font-medium tracking-wider hover:bg-gray-100 transition-colors duration-200"
        >
          {buttonText}
        </Link>
      </motion.div>

      {/* Zara-style image - exactly like hardcoded */}
      <motion.div
        {...getAnimationProps(1)}
        className="relative aspect-[4/5] overflow-hidden"
      >
        <Image
          src={image.url}
          alt={image.alt}
          fill
          className="object-cover"
        />
      </motion.div>
    </div>
  )

  const renderOverlayLayout = () => (
    <div className="relative aspect-[16/9] overflow-hidden">
      <Image
        src={image.url}
        alt={image.alt}
        fill
        className="object-cover"
      />
      <div className="absolute inset-0 bg-black/50" />
      <div className="absolute inset-0 flex items-center justify-center">
        <motion.div
          {...getAnimationProps(0)}
          className="text-center space-y-6 md:space-y-8 max-w-2xl px-4"
        >
          <div className="space-y-4">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-light leading-tight text-white">
              {title}
            </h2>
            {subtitle && (
              <h3 className="text-2xl md:text-3xl font-light text-white">
                {subtitle}
              </h3>
            )}
            {description && (
              <p className="text-lg md:text-xl font-light text-gray-200">
                {description}
              </p>
            )}
          </div>
          <Link
            href={buttonLink}
            className="inline-block bg-white text-black px-8 py-3 text-sm font-medium tracking-wider hover:bg-gray-100 transition-colors duration-200"
          >
            {buttonText}
          </Link>
        </motion.div>
      </div>
    </div>
  )

  const renderCenteredLayout = () => (
    <div className="text-center space-y-8 md:space-y-12">
      <motion.div
        {...getAnimationProps(0)}
        className="space-y-6"
      >
        <div className="space-y-4">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-light leading-tight">
            {title}
          </h2>
          {subtitle && (
            <h3 className="text-2xl md:text-3xl font-light" style={{ color: textColor }}>
              {subtitle}
            </h3>
          )}
          {description && (
            <p className="text-lg md:text-xl font-light text-gray-300 max-w-2xl mx-auto">
              {description}
            </p>
          )}
        </div>
        <Link
          href={buttonLink}
          className="inline-block bg-white text-black px-8 py-3 text-sm font-medium tracking-wider hover:bg-gray-100 transition-colors duration-200"
        >
          {buttonText}
        </Link>
      </motion.div>

      <motion.div
        {...getAnimationProps(1)}
        className="relative aspect-[16/9] md:aspect-[21/9] overflow-hidden max-w-4xl mx-auto"
      >
        <Image
          src={image.url}
          alt={image.alt}
          fill
          className="object-cover"
        />
      </motion.div>
    </div>
  )

  const renderLayout = () => {
    switch (layout) {
      case 'overlay': return renderOverlayLayout()
      case 'centered': return renderCenteredLayout()
      default: return renderSplitLayout()
    }
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <section 
        className={`${getSpacingClass()}`}
        style={{ backgroundColor, color: textColor }}
      >
        <div className="container px-4 md:px-6 max-w-7xl">
          {renderLayout()}
        </div>
      </section>
    </BaseBlock>
  )
}
