'use client'

import React from 'react'
import { PageBlock } from '../../types'
import { BaseBlock } from '../base-block'
import { ProductCard } from '@/components/storefront/products/product-card'
import { useProducts } from '@/lib/ecommerce/hooks/use-products'
import Link from 'next/link'
import { Skeleton } from '@/components/ui/skeleton'

interface NewArrivalsBlockConfig {
  title: string
  subtitle?: string
  limit: number
  showViewAllLink: boolean
  viewAllText: string
  viewAllUrl: string
  layout: 'grid' | 'carousel'
  columns: {
    desktop: number
    tablet: number
    mobile: number
  }
  spacing: 'compact' | 'normal' | 'spacious'
  style: 'minimal' | 'zara' | 'modern'
}

interface NewArrivalsBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function NewArrivalsBlock({ block, isEditing = false }: NewArrivalsBlockProps) {
  const config = block.configuration as NewArrivalsBlockConfig
  
  const {
    title = 'NEW IN',
    subtitle,
    limit = 6,
    showViewAllLink = true,
    viewAllText = 'VIEW ALL NEW IN',
    viewAllUrl = '/collections/new-arrivals',
    layout = 'grid',
    columns = { desktop: 3, tablet: 2, mobile: 2 },
    spacing = 'normal',
    style = 'zara'
  } = config

  // Fetch new arrivals using real e-commerce API
  const { products, loading, error } = useProducts({
    initialParams: {
      limit,
      sort: {
        field: 'createdAt',
        direction: 'desc'
      }
    },
    autoFetch: !isEditing
  })

  // Transform e-commerce products to ProductCard format
  const transformedProducts = products.map(product => ({
    id: product.id,
    name: product.title, // Transform title to name
    slug: product.slug,
    price: typeof product.price === 'number' ? product.price : product.price?.amount || 0,
    compareAtPrice: product.compareAtPrice ?
      (typeof product.compareAtPrice === 'number' ? product.compareAtPrice : product.compareAtPrice?.amount)
      : undefined,
    images: product.images?.map(img => img.url) || [],
    description: product.description,
    isNew: true, // Since this is the new arrivals block
    isSale: product.compareAtPrice ?
      (typeof product.compareAtPrice === 'number' ? product.compareAtPrice > (typeof product.price === 'number' ? product.price : product.price?.amount || 0) :
       (product.compareAtPrice?.amount || 0) > (typeof product.price === 'number' ? product.price : product.price?.amount || 0))
      : false
  }))

  // For editing mode, show skeleton
  if (isEditing || loading) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <section className="py-16 md:py-24 bg-white">
          <div className="container px-4 md:px-6 max-w-7xl">
            {/* Header */}
            <div className="mb-12 md:mb-16">
              <h2 className="text-2xl md:text-3xl font-light tracking-wide mb-8">
                {title}
              </h2>
              {subtitle && (
                <p className="text-gray-600 font-light">{subtitle}</p>
              )}
            </div>

            {/* Loading Grid */}
            <div className={`grid grid-cols-${columns.mobile} md:grid-cols-${columns.tablet} lg:grid-cols-${columns.desktop} gap-4 md:gap-6`}>
              {Array.from({ length: limit }).map((_, index) => (
                <div key={index} className="space-y-3">
                  <Skeleton className="aspect-[3/4] w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              ))}
            </div>

            {/* View All Link */}
            {showViewAllLink && (
              <div className="mt-12 md:mt-16 text-center">
                <div className="inline-block text-sm font-medium tracking-wider border-b border-black pb-1">
                  {viewAllText}
                </div>
              </div>
            )}
          </div>
        </section>
      </BaseBlock>
    )
  }

  // Error state
  if (error) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <section className="py-16 md:py-24 bg-white">
          <div className="container px-4 md:px-6 max-w-7xl">
            <div className="text-center">
              <p className="text-red-600">Failed to load new arrivals: {error}</p>
            </div>
          </div>
        </section>
      </BaseBlock>
    )
  }

  // No products state
  if (!products || products.length === 0) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <section className="py-16 md:py-24 bg-white">
          <div className="container px-4 md:px-6 max-w-7xl">
            <div className="text-center">
              <h2 className="text-2xl md:text-3xl font-light tracking-wide mb-4">{title}</h2>
              <p className="text-gray-600">No new arrivals available at the moment.</p>
            </div>
          </div>
        </section>
      </BaseBlock>
    )
  }

  const getSpacingClass = () => {
    switch (spacing) {
      case 'compact': return 'gap-2 md:gap-3'
      case 'spacious': return 'gap-6 md:gap-8'
      default: return 'gap-4 md:gap-6'
    }
  }

  const getGridClass = () => {
    return `grid grid-cols-${columns.mobile} md:grid-cols-${columns.tablet} lg:grid-cols-${columns.desktop} ${getSpacingClass()}`
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <section className="py-16 md:py-24 bg-white">
        <div className="container px-4 md:px-6 max-w-7xl">
          {/* Zara-style minimal header */}
          <div className="mb-12 md:mb-16">
            <h2 className="text-2xl md:text-3xl font-light tracking-wide mb-8">
              {title}
            </h2>
            {subtitle && (
              <p className="text-gray-600 font-light">{subtitle}</p>
            )}
          </div>

          {/* Product grid - exactly like hardcoded component */}
          <div className={getGridClass()}>
            {transformedProducts.slice(0, limit).map((product) => (
              <div key={product.id} className="group">
                <ProductCard product={product} />
              </div>
            ))}
          </div>

          {/* Zara-style view all link - exactly like hardcoded component */}
          {showViewAllLink && (
            <div className="mt-12 md:mt-16 text-center">
              <Link
                href={viewAllUrl}
                className="inline-block text-sm font-medium tracking-wider border-b border-black pb-1 hover:border-gray-400 transition-colors duration-200"
              >
                {viewAllText}
              </Link>
            </div>
          )}
        </div>
      </section>
    </BaseBlock>
  )
}
