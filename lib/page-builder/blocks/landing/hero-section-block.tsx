'use client'

import React from 'react'
import { PageBlock } from '../../types'
import { BaseBlock } from '../base-block'
import { Button } from '@/components/ui/button'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'

interface HeroSectionBlockConfig {
  title: string
  subtitle?: string
  description?: string
  backgroundImage: string
  backgroundVideo?: string
  overlay: {
    enabled: boolean
    color: string
    opacity: number
  }
  ctaButton: {
    text: string
    url: string
    style: 'primary' | 'secondary' | 'outline' | 'minimal'
  }
  secondaryButton?: {
    text: string
    url: string
    style: 'primary' | 'secondary' | 'outline' | 'minimal'
  }
  contentPosition: 'left' | 'center' | 'right' | 'bottom-left' | 'bottom-center' | 'bottom-right'
  height: 'viewport' | 'large' | 'medium' | 'small' | 'custom'
  customHeight?: string
  textColor: string
  animation: {
    enabled: boolean
    type: 'fade' | 'slide' | 'scale'
    duration: number
    delay: number
  }
  style: 'minimal' | 'zara' | 'modern' | 'classic'
}

interface HeroSectionBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function HeroSectionBlock({ block, isEditing = false }: HeroSectionBlockProps) {
  const config = block.configuration as HeroSectionBlockConfig

  const {
    title = 'KIDS COLLECTION',
    subtitle,
    description,
    backgroundImage = '/assets/images/cocomilk_kids-20210912_114630-3065525289.jpg',
    backgroundVideo,
    overlay = { enabled: true, color: '#000000', opacity: 0.2 },
    ctaButton = { text: 'SHOP NOW', url: '/products', style: 'minimal' },
    secondaryButton,
    contentPosition = 'bottom-left',
    height = 'viewport',
    customHeight,
    textColor = '#ffffff',
    animation = { enabled: true, type: 'fade', duration: 1, delay: 0 },
    style = 'zara'
  } = config

  const getHeightClass = () => {
    switch (height) {
      case 'viewport': return 'h-screen'
      case 'large': return 'h-[80vh]'
      case 'medium': return 'h-[60vh]'
      case 'small': return 'h-[40vh]'
      case 'custom': return ''
      default: return 'h-screen'
    }
  }

  const getContentPositionClass = () => {
    switch (contentPosition) {
      case 'left': return 'items-center justify-start'
      case 'center': return 'items-center justify-center'
      case 'right': return 'items-center justify-end'
      case 'bottom-left': return 'items-end justify-start'
      case 'bottom-center': return 'items-end justify-center'
      case 'bottom-right': return 'items-end justify-end'
      default: return 'items-end justify-start'
    }
  }

  const getButtonClass = (buttonStyle: string) => {
    switch (buttonStyle) {
      case 'minimal':
        return 'inline-block bg-white text-black px-8 py-3 text-sm font-medium tracking-wider hover:bg-gray-100 transition-colors duration-200'
      case 'outline':
        return 'inline-block border border-white text-white px-8 py-3 text-sm font-medium tracking-wider hover:bg-white hover:text-black transition-colors duration-200'
      case 'primary':
        return 'inline-block bg-primary text-primary-foreground px-8 py-3 text-sm font-medium tracking-wider hover:bg-primary/90 transition-colors duration-200'
      case 'secondary':
        return 'inline-block bg-secondary text-secondary-foreground px-8 py-3 text-sm font-medium tracking-wider hover:bg-secondary/90 transition-colors duration-200'
      default:
        return 'inline-block bg-white text-black px-8 py-3 text-sm font-medium tracking-wider hover:bg-gray-100 transition-colors duration-200'
    }
  }

  const getAnimationProps = () => {
    if (!animation.enabled) return {}

    const baseProps = {
      transition: { duration: animation.duration, ease: "easeOut", delay: animation.delay }
    }

    switch (animation.type) {
      case 'fade':
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          ...baseProps
        }
      case 'slide':
        return {
          initial: { opacity: 0, y: 30 },
          animate: { opacity: 1, y: 0 },
          ...baseProps
        }
      case 'scale':
        return {
          initial: { opacity: 0, scale: 0.95 },
          animate: { opacity: 1, scale: 1 },
          ...baseProps
        }
      default:
        return {
          initial: { opacity: 0, y: 30 },
          animate: { opacity: 1, y: 0 },
          ...baseProps
        }
    }
  }

  const customStyle = height === 'custom' && customHeight ? { height: customHeight } : {}

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      {/* Exact replica of the hardcoded hero section */}
      <section 
        className={`relative overflow-hidden ${getHeightClass()}`}
        style={customStyle}
      >
        {/* Background Media */}
        <div className="absolute inset-0">
          {backgroundVideo ? (
            <video
              autoPlay
              muted
              loop
              playsInline
              className="w-full h-full object-cover"
            >
              <source src={backgroundVideo} type="video/mp4" />
            </video>
          ) : (
            <Image
              src={backgroundImage}
              alt={title}
              fill
              priority
              className="object-cover"
            />
          )}
          
          {/* Overlay - exactly like hardcoded */}
          {overlay.enabled && (
            <div 
              className="absolute inset-0"
              style={{
                backgroundColor: overlay.color,
                opacity: overlay.opacity
              }}
            />
          )}
        </div>

        {/* Content - exactly like hardcoded Zara-style minimal content */}
        <div className={`absolute inset-0 flex ${getContentPositionClass()}`}>
          <div className="w-full p-8 md:p-16">
            <motion.div
              {...getAnimationProps()}
              className="max-w-md"
              style={{ color: textColor }}
            >
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-light leading-tight mb-6">
                {title.split(' ').map((word, index) => (
                  <React.Fragment key={index}>
                    {word}
                    {index < title.split(' ').length - 1 && <br />}
                  </React.Fragment>
                ))}
              </h1>
              
              {subtitle && (
                <h2 className="text-lg md:text-xl font-light mb-4 opacity-90">
                  {subtitle}
                </h2>
              )}
              
              {description && (
                <p className="text-base md:text-lg font-light mb-6 opacity-80">
                  {description}
                </p>
              )}
              
              <div className="space-y-4">
                <Link
                  href={ctaButton.url}
                  className={getButtonClass(ctaButton.style)}
                >
                  {ctaButton.text}
                </Link>
                
                {secondaryButton && (
                  <div className="ml-4 inline-block">
                    <Link
                      href={secondaryButton.url}
                      className={getButtonClass(secondaryButton.style)}
                    >
                      {secondaryButton.text}
                    </Link>
                  </div>
                )}
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </BaseBlock>
  )
}
