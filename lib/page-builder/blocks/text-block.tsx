'use client'

import React from 'react'
import { PageBlock } from '../types'
import { BaseBlock } from './base-block'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { cn } from '@/lib/utils'

interface TextBlockConfig {
  content: string
  textAlign: 'left' | 'center' | 'right' | 'justify'
  fontSize: 'small' | 'medium' | 'large' | 'xl'
  lineHeight: 'tight' | 'normal' | 'relaxed'
}

interface TextBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function TextBlock({ block, isEditing = false }: TextBlockProps) {
  const config = block.configuration as TextBlockConfig

  const {
    content = '<p>Add your text content here.</p>',
    textAlign = 'left',
    fontSize = 'medium',
    lineHeight = 'normal',
  } = config

  // Get CSS classes based on configuration
  const getTextClasses = () => {
    const alignmentClasses = {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
      justify: 'text-justify',
    }

    const sizeClasses = {
      small: 'text-sm',
      medium: 'text-base',
      large: 'text-lg',
      xl: 'text-xl',
    }

    const lineHeightClasses = {
      tight: 'leading-tight',
      normal: 'leading-normal',
      relaxed: 'leading-relaxed',
    }

    return cn(
      alignmentClasses[textAlign],
      sizeClasses[fontSize],
      lineHeightClasses[lineHeight],
      'prose prose-gray max-w-none'
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <section className="py-8">
        <div className="container mx-auto px-4 md:px-6">
          <div
            className={getTextClasses()}
            dangerouslySetInnerHTML={{ __html: content }}
          />
        </div>
      </section>
    </BaseBlock>
  )
}

// Text Block Configuration Component
interface TextBlockConfigProps {
  config: TextBlockConfig
  onChange: (config: TextBlockConfig) => void
}

export function TextBlockConfig({ config, onChange }: TextBlockConfigProps) {
  const updateConfig = (updates: Partial<TextBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  return (
    <div className="space-y-6">
      {/* Content */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Content</h3>
        
        <div>
          <Label className="block text-sm font-medium mb-2">Text Content</Label>
          <Textarea
            value={config.content}
            onChange={(e) => updateConfig({ content: e.target.value })}
            placeholder="Enter your text content here..."
            rows={8}
            className="font-mono text-sm"
          />
          <p className="text-xs text-muted-foreground mt-1">
            You can use HTML tags for formatting
          </p>
        </div>
      </div>

      {/* Typography */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Typography</h3>
        
        <div>
          <Label className="block text-sm font-medium mb-2">Text Alignment</Label>
          <Select
            value={config.textAlign}
            onValueChange={(value) => updateConfig({ textAlign: value as any })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="left">Left</SelectItem>
              <SelectItem value="center">Center</SelectItem>
              <SelectItem value="right">Right</SelectItem>
              <SelectItem value="justify">Justify</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label className="block text-sm font-medium mb-2">Font Size</Label>
          <Select
            value={config.fontSize}
            onValueChange={(value) => updateConfig({ fontSize: value as any })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="small">Small</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="large">Large</SelectItem>
              <SelectItem value="xl">Extra Large</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label className="block text-sm font-medium mb-2">Line Height</Label>
          <Select
            value={config.lineHeight}
            onValueChange={(value) => updateConfig({ lineHeight: value as any })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="tight">Tight</SelectItem>
              <SelectItem value="normal">Normal</SelectItem>
              <SelectItem value="relaxed">Relaxed</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Preview */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Preview</h3>
        <div className="border rounded-lg p-4 bg-gray-50">
          <div
            className={cn(
              'prose prose-gray max-w-none',
              {
                'text-left': config.textAlign === 'left',
                'text-center': config.textAlign === 'center',
                'text-right': config.textAlign === 'right',
                'text-justify': config.textAlign === 'justify',
                'text-sm': config.fontSize === 'small',
                'text-base': config.fontSize === 'medium',
                'text-lg': config.fontSize === 'large',
                'text-xl': config.fontSize === 'xl',
                'leading-tight': config.lineHeight === 'tight',
                'leading-normal': config.lineHeight === 'normal',
                'leading-relaxed': config.lineHeight === 'relaxed',
              }
            )}
            dangerouslySetInnerHTML={{ __html: config.content }}
          />
        </div>
      </div>
    </div>
  )
}
