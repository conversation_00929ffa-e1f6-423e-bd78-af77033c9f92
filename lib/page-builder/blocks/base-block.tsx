'use client'

import React from 'react'
import { PageBlock } from '../types'
import { stylingToCss, getResponsiveClasses } from '../utils'
import { usePageBuilder } from '../context'
import { cn } from '@/lib/utils'

interface BaseBlockProps {
  block: PageBlock
  children: React.ReactNode
  className?: string
  isEditing?: boolean
  onClick?: () => void
}

export function BaseBlock({
  block,
  children,
  className,
  isEditing = false,
  onClick
}: BaseBlockProps) {
  // Only use PageBuilder context when editing
  let state: any = null
  let selectBlock: any = null

  try {
    if (isEditing) {
      const pageBuilderContext = usePageBuilder()
      state = pageBuilderContext.state
      selectBlock = pageBuilderContext.selectBlock
    }
  } catch (error) {
    // Context not available, continue without it
    console.warn('PageBuilder context not available, rendering in static mode')
  }

  const selectedBlockId = state?.selectedBlockId
  const devicePreview = state?.devicePreview || 'desktop'
  const isPreviewMode = state?.isPreviewMode || false

  const isSelected = isEditing && selectedBlockId === block.id && !isPreviewMode
  const isVisible = block.isVisible

  // Generate CSS from styling configuration
  const customStyles = block.styling ? stylingToCss(block.styling) : ''

  // Get responsive classes
  const responsiveClasses = block.responsive
    ? getResponsiveClasses(block.responsive, devicePreview)
    : ''

  // Handle block selection
  const handleClick = (e: React.MouseEvent) => {
    if (isEditing && !isPreviewMode && selectBlock) {
      e.stopPropagation()
      selectBlock(block.id)
      onClick?.()
    }
  }

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isEditing && !isPreviewMode && e.key === 'Enter') {
      selectBlock(block.id)
      onClick?.()
    }
  }

  if (!isVisible && isPreviewMode) {
    return null
  }

  return (
    <div
      className={cn(
        'relative transition-all duration-200',
        {
          'ring-2 ring-blue-500 ring-offset-2': isSelected,
          'opacity-50': !isVisible && isEditing,
          'cursor-pointer': isEditing && !isPreviewMode,
          'hover:ring-1 hover:ring-blue-300': isEditing && !isPreviewMode && !isSelected,
        },
        responsiveClasses,
        className
      )}
      style={customStyles ? { cssText: customStyles } : undefined}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={isEditing && !isPreviewMode ? 0 : undefined}
      role={isEditing ? 'button' : undefined}
      aria-label={isEditing ? `Edit ${block.type} block` : undefined}
      data-block-id={block.id}
      data-block-type={block.type}
    >
      {/* Block overlay for editing mode */}
      {isEditing && !isPreviewMode && (
        <div className="absolute inset-0 z-10 pointer-events-none">
          {isSelected && (
            <div className="absolute top-0 left-0 bg-blue-500 text-white text-xs px-2 py-1 rounded-br-md font-medium">
              {block.type}
            </div>
          )}
        </div>
      )}

      {/* Block content */}
      <div className={cn('relative', { 'z-0': isEditing })}>
        {children}
      </div>

      {/* Animation wrapper */}
      {block.animation && block.animation.type !== 'none' && (
        <style jsx>{`
          [data-block-id="${block.id}"] {
            animation: ${getAnimationCss(block.animation)};
          }
        `}</style>
      )}
    </div>
  )
}

// Helper function to generate animation CSS
function getAnimationCss(animation: any): string {
  const { type, duration = 1000, delay = 0, direction = 'up' } = animation

  const animations = {
    fade: `fadeIn ${duration}ms ease-in-out ${delay}ms both`,
    slide: `slideIn${direction.charAt(0).toUpperCase() + direction.slice(1)} ${duration}ms ease-out ${delay}ms both`,
    scale: `scaleIn ${duration}ms ease-out ${delay}ms both`,
    bounce: `bounceIn ${duration}ms ease-out ${delay}ms both`,
  }

  return animations[type as keyof typeof animations] || 'none'
}

// Block wrapper for drag and drop
interface DraggableBlockProps extends BaseBlockProps {
  index: number
  isDragging?: boolean
  dragRef?: React.RefObject<HTMLDivElement>
}

export function DraggableBlock({
  block,
  children,
  index,
  isDragging = false,
  dragRef,
  ...props
}: DraggableBlockProps) {
  return (
    <div
      ref={dragRef}
      className={cn(
        'transition-all duration-200',
        {
          'opacity-50 scale-95': isDragging,
          'transform-gpu': isDragging,
        }
      )}
      data-index={index}
    >
      <BaseBlock block={block} {...props}>
        {children}
      </BaseBlock>
    </div>
  )
}

// Block error boundary
interface BlockErrorBoundaryProps {
  children: React.ReactNode
  blockType: string
  blockId: string
}

interface BlockErrorBoundaryState {
  hasError: boolean
  error?: Error
}

export class BlockErrorBoundary extends React.Component<
  BlockErrorBoundaryProps,
  BlockErrorBoundaryState
> {
  constructor(props: BlockErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): BlockErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error(`Error in block ${this.props.blockType} (${this.props.blockId}):`, error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="border-2 border-red-300 bg-red-50 p-4 rounded-lg">
          <div className="flex items-center space-x-2 text-red-700 mb-2">
            <span className="text-lg">⚠️</span>
            <span className="font-medium">Block Error</span>
          </div>
          <p className="text-sm text-red-600 mb-2">
            There was an error rendering this {this.props.blockType} block.
          </p>
          <details className="text-xs text-red-500">
            <summary className="cursor-pointer">Error Details</summary>
            <pre className="mt-2 whitespace-pre-wrap">
              {this.state.error?.message}
            </pre>
          </details>
        </div>
      )
    }

    return this.props.children
  }
}

// Block loading placeholder
export function BlockLoadingPlaceholder({ blockType }: { blockType: string }) {
  return (
    <div className="animate-pulse bg-gray-200 rounded-lg p-8">
      <div className="flex items-center justify-center space-x-2 text-gray-500">
        <div className="w-4 h-4 bg-gray-300 rounded animate-spin"></div>
        <span>Loading {blockType} block...</span>
      </div>
    </div>
  )
}

// Block not found placeholder
export function BlockNotFound({ blockType }: { blockType: string }) {
  return (
    <div className="border-2 border-yellow-300 bg-yellow-50 p-4 rounded-lg">
      <div className="flex items-center space-x-2 text-yellow-700 mb-2">
        <span className="text-lg">❓</span>
        <span className="font-medium">Unknown Block Type</span>
      </div>
      <p className="text-sm text-yellow-600">
        Block type "{blockType}" is not registered or available.
      </p>
    </div>
  )
}

// CSS animations (to be included in global styles)
export const blockAnimationStyles = `
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from { 
    opacity: 0;
    transform: translateY(30px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from { 
    opacity: 0;
    transform: translateY(-30px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from { 
    opacity: 0;
    transform: translateX(-30px);
  }
  to { 
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from { 
    opacity: 0;
    transform: translateX(30px);
  }
  to { 
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.9);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
`
