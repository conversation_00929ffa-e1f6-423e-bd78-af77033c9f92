'use client'

import React from 'react'
import Link from 'next/link'
import { <PERSON><PERSON>lock, HeroBlockConfig } from '../types'
import { BaseBlock } from './base-block'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface HeroBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function HeroBlock({ block, isEditing = false }: HeroBlockProps) {
  const config = block.configuration as HeroBlockConfig

  const {
    title,
    subtitle,
    description,
    backgroundImage,
    backgroundVideo,
    overlay,
    ctaButton,
    alignment,
    height,
    customHeight,
  } = config

  // Determine section height
  const getSectionHeight = () => {
    switch (height) {
      case 'viewport':
        return 'min-h-screen'
      case 'custom':
        return ''
      default:
        return 'min-h-[400px]'
    }
  }

  // Get text alignment classes
  const getAlignmentClasses = () => {
    switch (alignment) {
      case 'left':
        return 'text-left items-start'
      case 'right':
        return 'text-right items-end'
      default:
        return 'text-center items-center'
    }
  }

  // Get button variant
  const getButtonVariant = () => {
    switch (ctaButton?.style) {
      case 'secondary':
        return 'secondary'
      case 'outline':
        return 'outline'
      default:
        return 'default'
    }
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <section
        className={cn(
          'relative flex flex-col justify-center',
          getSectionHeight(),
          getAlignmentClasses()
        )}
        style={{
          height: height === 'custom' ? customHeight : undefined,
        }}
      >
        {/* Background Image */}
        {backgroundImage && (
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: `url(${backgroundImage})`,
            }}
          />
        )}

        {/* Background Video */}
        {backgroundVideo && (
          <video
            className="absolute inset-0 w-full h-full object-cover"
            autoPlay
            muted
            loop
            playsInline
          >
            <source src={backgroundVideo} type="video/mp4" />
          </video>
        )}

        {/* Overlay */}
        {overlay?.enabled && (
          <div
            className="absolute inset-0"
            style={{
              backgroundColor: overlay.color,
              opacity: overlay.opacity,
            }}
          />
        )}

        {/* Content */}
        <div className="relative z-10 container mx-auto px-4 md:px-6">
          <div className={cn(
            'max-w-4xl',
            alignment === 'left' ? 'mr-auto' : 
            alignment === 'right' ? 'ml-auto' : 'mx-auto'
          )}>
            {/* Subtitle */}
            {subtitle && (
              <p className="text-sm md:text-base font-medium text-muted-foreground mb-4 tracking-wide uppercase">
                {subtitle}
              </p>
            )}

            {/* Title */}
            {title && (
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-light font-p22 tracking-wide mb-6 leading-tight">
                {title}
              </h1>
            )}

            {/* Description */}
            {description && (
              <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-2xl font-light leading-relaxed">
                {description}
              </p>
            )}

            {/* CTA Button */}
            {ctaButton?.text && ctaButton?.url && (
              <div className="flex justify-start">
                {isEditing ? (
                  <Button
                    variant={getButtonVariant()}
                    size="lg"
                    className="px-8 py-3 text-base"
                    onClick={(e) => e.preventDefault()}
                  >
                    {ctaButton.text}
                  </Button>
                ) : (
                  <Button
                    variant={getButtonVariant()}
                    size="lg"
                    className="px-8 py-3 text-base"
                    asChild
                  >
                    <Link href={ctaButton.url}>
                      {ctaButton.text}
                    </Link>
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Scroll indicator (only for viewport height) */}
        {height === 'viewport' && !isEditing && (
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div className="w-6 h-10 border-2 border-current rounded-full flex justify-center">
              <div className="w-1 h-3 bg-current rounded-full mt-2 animate-pulse"></div>
            </div>
          </div>
        )}
      </section>
    </BaseBlock>
  )
}

// Hero Block Configuration Component
interface HeroBlockConfigProps {
  config: HeroBlockConfig
  onChange: (config: HeroBlockConfig) => void
}

export function HeroBlockConfig({ config, onChange }: HeroBlockConfigProps) {
  const updateConfig = (updates: Partial<HeroBlockConfig>) => {
    onChange({ ...config, ...updates })
  }

  const updateOverlay = (overlayUpdates: Partial<HeroBlockConfig['overlay']>) => {
    updateConfig({
      overlay: { ...config.overlay, ...overlayUpdates }
    })
  }

  const updateCtaButton = (buttonUpdates: Partial<HeroBlockConfig['ctaButton']>) => {
    updateConfig({
      ctaButton: { ...config.ctaButton, ...buttonUpdates }
    })
  }

  return (
    <div className="space-y-6">
      {/* Content Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Content</h3>
        
        <div>
          <label className="block text-sm font-medium mb-2">Title</label>
          <input
            type="text"
            value={config.title}
            onChange={(e) => updateConfig({ title: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter hero title"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Subtitle</label>
          <input
            type="text"
            value={config.subtitle || ''}
            onChange={(e) => updateConfig({ subtitle: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter subtitle (optional)"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Description</label>
          <textarea
            value={config.description || ''}
            onChange={(e) => updateConfig({ description: e.target.value })}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter description (optional)"
          />
        </div>
      </div>

      {/* Background Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Background</h3>
        
        <div>
          <label className="block text-sm font-medium mb-2">Background Image URL</label>
          <input
            type="url"
            value={config.backgroundImage || ''}
            onChange={(e) => updateConfig({ backgroundImage: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="https://example.com/image.jpg"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Background Video URL</label>
          <input
            type="url"
            value={config.backgroundVideo || ''}
            onChange={(e) => updateConfig({ backgroundVideo: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="https://example.com/video.mp4"
          />
        </div>

        {/* Overlay Settings */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="overlay-enabled"
              checked={config.overlay?.enabled || false}
              onChange={(e) => updateOverlay({ enabled: e.target.checked })}
              className="rounded"
            />
            <label htmlFor="overlay-enabled" className="text-sm font-medium">
              Enable Overlay
            </label>
          </div>

          {config.overlay?.enabled && (
            <div className="grid grid-cols-2 gap-3 ml-6">
              <div>
                <label className="block text-sm font-medium mb-1">Color</label>
                <input
                  type="color"
                  value={config.overlay.color}
                  onChange={(e) => updateOverlay({ color: e.target.value })}
                  className="w-full h-10 rounded border border-gray-300"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Opacity</label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={config.overlay.opacity}
                  onChange={(e) => updateOverlay({ opacity: parseFloat(e.target.value) })}
                  className="w-full"
                />
                <span className="text-xs text-gray-500">{config.overlay.opacity}</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Layout Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Layout</h3>
        
        <div>
          <label className="block text-sm font-medium mb-2">Text Alignment</label>
          <select
            value={config.alignment}
            onChange={(e) => updateConfig({ alignment: e.target.value as any })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="left">Left</option>
            <option value="center">Center</option>
            <option value="right">Right</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Section Height</label>
          <select
            value={config.height}
            onChange={(e) => updateConfig({ height: e.target.value as any })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="auto">Auto</option>
            <option value="viewport">Full Viewport</option>
            <option value="custom">Custom</option>
          </select>
        </div>

        {config.height === 'custom' && (
          <div>
            <label className="block text-sm font-medium mb-2">Custom Height</label>
            <input
              type="text"
              value={config.customHeight || ''}
              onChange={(e) => updateConfig({ customHeight: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., 500px, 50vh"
            />
          </div>
        )}
      </div>

      {/* CTA Button Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Call to Action</h3>
        
        <div>
          <label className="block text-sm font-medium mb-2">Button Text</label>
          <input
            type="text"
            value={config.ctaButton?.text || ''}
            onChange={(e) => updateCtaButton({ text: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Shop Now"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Button URL</label>
          <input
            type="url"
            value={config.ctaButton?.url || ''}
            onChange={(e) => updateCtaButton({ url: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="/products"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Button Style</label>
          <select
            value={config.ctaButton?.style || 'primary'}
            onChange={(e) => updateCtaButton({ style: e.target.value as any })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="primary">Primary</option>
            <option value="secondary">Secondary</option>
            <option value="outline">Outline</option>
          </select>
        </div>
      </div>
    </div>
  )
}
