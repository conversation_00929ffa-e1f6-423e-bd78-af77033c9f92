"use client"

import { useCallback, useState } from 'react'
import { useChat } from '@ai-sdk/react'
import { usePageBuilder } from '../context'
import { toast } from 'sonner'

export interface AIPageDesignerOptions {
  onPageUpdated?: (pageData: any) => void
  onBlockAdded?: (blockData: any) => void
  onBlockUpdated?: (blockId: string, blockData: any) => void
  onSettingsUpdated?: (settings: any) => void
  onError?: (error: Error) => void
}

export function useAIPageDesigner(options: AIPageDesignerOptions = {}) {
  const { state, dispatch } = usePageBuilder()
  const [isDesigning, setIsDesigning] = useState(false)

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
    stop,
    setMessages,
    append,
  } = useChat({
    api: '/api/ai-page-designer',
    maxSteps: 5,
    onFinish: (message) => {
      setIsDesigning(false)
      
      // Process tool results to apply page changes
      const toolResults = message.parts
        ?.filter(part => part.type === 'tool-invocation' && part.toolInvocation.state === 'result')
        ?.map(part => part.toolInvocation.result) || []

      toolResults.forEach(result => {
        if (result.success) {
          switch (result.action) {
            case 'updatePage':
              dispatch({ type: 'SET_PAGE', payload: result.pageData })
              options.onPageUpdated?.(result.pageData)
              toast.success('Page updated by AI!')
              break
            case 'addBlock':
              dispatch({ type: 'ADD_BLOCK', payload: result.blockData })
              options.onBlockAdded?.(result.blockData)
              toast.success('Block added by AI!')
              break
            case 'updateBlock':
              dispatch({ type: 'UPDATE_BLOCK', payload: { id: result.blockId, updates: result.blockData } })
              options.onBlockUpdated?.(result.blockId, result.blockData)
              toast.success('Block updated by AI!')
              break
            case 'updatePageSettings':
              dispatch({ type: 'UPDATE_PAGE_SETTINGS', payload: result.settings })
              options.onSettingsUpdated?.(result.settings)
              toast.success('Page settings updated by AI!')
              break
          }
        }
      })
    },
    onError: (error) => {
      setIsDesigning(false)
      options.onError?.(error)
      toast.error('AI Designer error: ' + error.message)
    },
  })

  // Design a complete page from scratch
  const designPage = useCallback(async (
    prompt: string,
    pageType: 'landing' | 'product' | 'category' | 'about' | 'contact' | 'custom' = 'custom'
  ) => {
    setIsDesigning(true)
    
    const designPrompt = `Create a ${pageType} page for a kids clothing store. ${prompt}`
    
    await append({
      role: 'user',
      content: designPrompt,
    })
  }, [append])

  // Add specific content blocks
  const addContentBlock = useCallback(async (
    blockType: string,
    requirements: string,
    position?: number
  ) => {
    setIsDesigning(true)
    
    const prompt = `Add a ${blockType} block to the page. Requirements: ${requirements}${
      position !== undefined ? ` Position it at index ${position}.` : ''
    }`
    
    await append({
      role: 'user',
      content: prompt,
    })
  }, [append])

  // Modify existing content
  const modifyContent = useCallback(async (
    blockId: string,
    modifications: string
  ) => {
    setIsDesigning(true)
    
    const prompt = `Modify the block with ID "${blockId}". Changes needed: ${modifications}`
    
    await append({
      role: 'user',
      content: prompt,
    })
  }, [append])

  // Apply design theme
  const applyTheme = useCallback(async (
    themeName: string,
    customizations?: string
  ) => {
    setIsDesigning(true)
    
    const prompt = `Apply a "${themeName}" theme to the page${
      customizations ? ` with these customizations: ${customizations}` : ''
    }`
    
    await append({
      role: 'user',
      content: prompt,
    })
  }, [append])

  // Optimize for specific goals
  const optimizeFor = useCallback(async (
    goal: 'conversions' | 'mobile' | 'accessibility' | 'performance' | 'seo',
    specificRequirements?: string
  ) => {
    setIsDesigning(true)
    
    const prompt = `Optimize this page for ${goal}${
      specificRequirements ? `. Specific requirements: ${specificRequirements}` : ''
    }`
    
    await append({
      role: 'user',
      content: prompt,
    })
  }, [append])

  // Generate content for existing blocks
  const generateContent = useCallback(async (
    contentType: 'headlines' | 'descriptions' | 'cta-text' | 'product-copy' | 'testimonials',
    context: string
  ) => {
    setIsDesigning(true)
    
    const prompt = `Generate ${contentType} for the page. Context: ${context}`
    
    await append({
      role: 'user',
      content: prompt,
    })
  }, [append])

  // Ask for design advice
  const getDesignAdvice = useCallback(async (question: string) => {
    await append({
      role: 'user',
      content: `Design advice needed: ${question}`,
    })
  }, [append])

  // Clear conversation
  const clearConversation = useCallback(() => {
    setMessages([])
    setIsDesigning(false)
  }, [setMessages])

  // Quick design templates
  const applyTemplate = useCallback(async (
    template: 'hero-products-testimonials' | 'product-showcase' | 'brand-story' | 'contact-page' | 'landing-page'
  ) => {
    setIsDesigning(true)
    
    const templatePrompts = {
      'hero-products-testimonials': 'Create a landing page with a hero section, featured products grid, and customer testimonials',
      'product-showcase': 'Create a product showcase page with product grids, filters, and detailed product information',
      'brand-story': 'Create an about page that tells the brand story with images, text, and company values',
      'contact-page': 'Create a contact page with contact form, store information, and location details',
      'landing-page': 'Create a conversion-focused landing page with clear value proposition and strong CTAs'
    }
    
    await append({
      role: 'user',
      content: templatePrompts[template],
    })
  }, [append])

  return {
    // Chat interface
    messages,
    input,
    handleInputChange,
    handleSubmit: (e: React.FormEvent) => {
      e.preventDefault()
      setIsDesigning(true)
      handleSubmit(e, {
        body: {
          pageData: state.page,
          action: 'designPage'
        }
      })
    },
    isLoading: isLoading || isDesigning,
    error,
    stop,
    setMessages,
    append,
    
    // Design functions
    designPage,
    addContentBlock,
    modifyContent,
    applyTheme,
    optimizeFor,
    generateContent,
    getDesignAdvice,
    applyTemplate,
    
    // Utility functions
    clearConversation,
    
    // Status
    isDesigning,
    hasMessages: messages.length > 0,
    lastMessage: messages[messages.length - 1],
    currentPage: state.page,
  }
}
