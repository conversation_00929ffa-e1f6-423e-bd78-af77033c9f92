'use client'

import { useState, useEffect, useCallback } from 'react'
import { PageData } from '../types'

interface UseDynamicPageOptions {
  slug: string
  preview?: boolean
  previewToken?: string
  enableCache?: boolean
  onError?: (error: Error) => void
  onLoading?: (loading: boolean) => void
}

interface UseDynamicPageReturn {
  page: PageData | null
  loading: boolean
  error: Error | null
  refetch: () => Promise<void>
  isPreview: boolean
  cacheStatus: 'hit' | 'miss' | 'none'
}

export function useDynamicPage(options: UseDynamicPageOptions): UseDynamicPageReturn {
  const {
    slug,
    preview = false,
    previewToken,
    enableCache = true,
    onError,
    onLoading
  } = options

  const [page, setPage] = useState<PageData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const [cacheStatus, setCacheStatus] = useState<'hit' | 'miss' | 'none'>('none')

  const fetchPage = useCallback(async () => {
    if (!slug) return

    try {
      setLoading(true)
      setError(null)
      onLoading?.(true)

      // Build API URL
      const baseUrl = preview ? `/api/e-commerce/pages/preview/${slug}` : `/api/e-commerce/pages/slug/${slug}`
      const url = new URL(baseUrl, window.location.origin)
      
      if (preview && previewToken) {
        url.searchParams.set('token', previewToken)
      }

      if (!enableCache) {
        url.searchParams.set('nocache', 'true')
      }

      const response = await fetch(url.toString(), {
        headers: {
          'Content-Type': 'application/json',
        },
        // Add cache control headers
        ...(enableCache && {
          cache: 'force-cache',
          next: { revalidate: 300 } // 5 minutes
        })
      })

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Page not found')
        }
        throw new Error(`Failed to fetch page: ${response.statusText}`)
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to load page')
      }

      // Check cache status from headers
      const cacheHeader = response.headers.get('X-Cache')
      setCacheStatus(cacheHeader?.toLowerCase() as 'hit' | 'miss' || 'none')

      setPage(result.data)

    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      onError?.(error)
      console.error('Error fetching dynamic page:', error)
    } finally {
      setLoading(false)
      onLoading?.(false)
    }
  }, [slug, preview, previewToken, enableCache, onError, onLoading])

  // Initial fetch
  useEffect(() => {
    fetchPage()
  }, [slug, preview, previewToken, enableCache]) // Use primitive dependencies instead of fetchPage function

  // Refetch function
  const refetch = useCallback(async () => {
    await fetchPage()
  }, [fetchPage])

  return {
    page,
    loading,
    error,
    refetch,
    isPreview: preview,
    cacheStatus
  }
}

// Hook for fetching multiple pages (for archives, sitemaps, etc.)
export function useDynamicPages(options: {
  type?: string
  status?: string
  limit?: number
  page?: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}) {
  const [pages, setPages] = useState<PageData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const [pagination, setPagination] = useState({
    total: 0,
    pages: 0,
    current: 1,
    hasNext: false,
    hasPrev: false
  })

  const fetchPages = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const url = new URL('/api/e-commerce/pages', window.location.origin)
      
      // Add query parameters
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
          url.searchParams.set(key, value.toString())
        }
      })

      const response = await fetch(url.toString())

      if (!response.ok) {
        throw new Error(`Failed to fetch pages: ${response.statusText}`)
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to load pages')
      }

      setPages(result.data.pages)
      setPagination(result.data.pagination)

    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      console.error('Error fetching dynamic pages:', error)
    } finally {
      setLoading(false)
    }
  }, [options])

  useEffect(() => {
    fetchPages()
  }, [fetchPages])

  return {
    pages,
    loading,
    error,
    pagination,
    refetch: fetchPages
  }
}

// Hook for page generation
export function usePageGeneration() {
  const [generating, setGenerating] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const generatePage = useCallback(async (options: {
    title: string
    slug: string
    type?: string
    template?: string
    description?: string
    blocks?: any[]
  }) => {
    try {
      setGenerating(true)
      setError(null)

      const response = await fetch('/api/page-builder/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(options)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate page')
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to generate page')
      }

      return result.data

    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      throw error
    } finally {
      setGenerating(false)
    }
  }, [])

  const duplicatePage = useCallback(async (pageId: string, newSlug: string, newTitle?: string) => {
    try {
      setGenerating(true)
      setError(null)

      const response = await fetch('/api/page-builder/generate', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ pageId, newSlug, newTitle })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to duplicate page')
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to duplicate page')
      }

      return result.data

    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      throw error
    } finally {
      setGenerating(false)
    }
  }, [])

  return {
    generatePage,
    duplicatePage,
    generating,
    error
  }
}

// Hook for cache management
export function usePageCache() {
  const [stats, setStats] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const getStats = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/page-builder/generate?action=cache-stats')
      const result = await response.json()
      
      if (result.success) {
        setStats(result.data)
      }
    } catch (error) {
      console.error('Error fetching cache stats:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  const clearCache = useCallback(async () => {
    try {
      const response = await fetch('/api/page-builder/generate?action=clear-cache', {
        method: 'DELETE'
      })
      const result = await response.json()
      
      if (result.success) {
        await getStats() // Refresh stats
      }
      
      return result.success
    } catch (error) {
      console.error('Error clearing cache:', error)
      return false
    }
  }, [getStats])

  const invalidatePage = useCallback(async (pageId: string, slug?: string) => {
    try {
      const url = new URL('/api/page-builder/generate', window.location.origin)
      url.searchParams.set('action', 'invalidate-page')
      url.searchParams.set('pageId', pageId)
      if (slug) url.searchParams.set('slug', slug)

      const response = await fetch(url.toString(), { method: 'DELETE' })
      const result = await response.json()
      
      if (result.success) {
        await getStats() // Refresh stats
      }
      
      return result.success
    } catch (error) {
      console.error('Error invalidating page cache:', error)
      return false
    }
  }, [getStats])

  useEffect(() => {
    getStats()
  }, [getStats])

  return {
    stats,
    loading,
    getStats,
    clearCache,
    invalidatePage
  }
}
