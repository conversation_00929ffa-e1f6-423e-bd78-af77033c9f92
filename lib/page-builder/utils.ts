// Page Builder Utility Functions

import { PageBlock, BlockStyling, ResponsiveSettings } from './types'

/**
 * Generate a unique ID for blocks
 */
export function generateId(): string {
  return `block_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Generate a URL-friendly slug from a title
 */
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim()
}

/**
 * Validate if a slug is unique
 */
export async function validateSlug(slug: string, excludeId?: string): Promise<boolean> {
  try {
    const response = await fetch(`/api/e-commerce/pages/validate-slug?slug=${slug}&excludeId=${excludeId || ''}`)
    const data = await response.json()
    return data.isUnique
  } catch (error) {
    console.error('Error validating slug:', error)
    return false
  }
}

/**
 * Convert styling object to CSS string
 */
export function stylingToCss(styling: BlockStyling): string {
  const cssRules: string[] = []

  if (styling.backgroundColor) {
    cssRules.push(`background-color: ${styling.backgroundColor}`)
  }

  if (styling.textColor) {
    cssRules.push(`color: ${styling.textColor}`)
  }

  if (styling.padding) {
    const { top, right, bottom, left } = styling.padding
    if (top || right || bottom || left) {
      cssRules.push(`padding: ${top || '0'} ${right || '0'} ${bottom || '0'} ${left || '0'}`)
    }
  }

  if (styling.margin) {
    const { top, right, bottom, left } = styling.margin
    if (top || right || bottom || left) {
      cssRules.push(`margin: ${top || '0'} ${right || '0'} ${bottom || '0'} ${left || '0'}`)
    }
  }

  if (styling.borderRadius) {
    cssRules.push(`border-radius: ${styling.borderRadius}`)
  }

  if (styling.border) {
    const { width, style, color } = styling.border
    if (width && style && color) {
      cssRules.push(`border: ${width} ${style} ${color}`)
    }
  }

  if (styling.shadow) {
    cssRules.push(`box-shadow: ${styling.shadow}`)
  }

  if (styling.customCss) {
    cssRules.push(styling.customCss)
  }

  return cssRules.join('; ')
}

/**
 * Get responsive CSS classes based on device
 */
export function getResponsiveClasses(
  responsive: ResponsiveSettings,
  device: 'desktop' | 'tablet' | 'mobile'
): string {
  const classes: string[] = []

  // Hide on specific devices
  if (responsive.hideOnDesktop && device === 'desktop') {
    classes.push('hidden')
  }
  if (responsive.hideOnTablet && device === 'tablet') {
    classes.push('hidden')
  }
  if (responsive.hideOnMobile && device === 'mobile') {
    classes.push('hidden')
  }

  return classes.join(' ')
}

/**
 * Deep clone an object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T
  }

  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }

  return obj
}

/**
 * Reorder array items
 */
export function reorderArray<T>(array: T[], startIndex: number, endIndex: number): T[] {
  const result = Array.from(array)
  const [removed] = result.splice(startIndex, 1)
  result.splice(endIndex, 0, removed)
  return result
}

/**
 * Format currency for ZAR
 */
export function formatZAR(amount: number): string {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2,
  }).format(amount)
}

/**
 * Debounce function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * Throttle function
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * Check if device is mobile
 */
export function isMobile(): boolean {
  if (typeof window === 'undefined') return false
  return window.innerWidth < 768
}

/**
 * Check if device is tablet
 */
export function isTablet(): boolean {
  if (typeof window === 'undefined') return false
  return window.innerWidth >= 768 && window.innerWidth < 1024
}

/**
 * Check if device is desktop
 */
export function isDesktop(): boolean {
  if (typeof window === 'undefined') return false
  return window.innerWidth >= 1024
}

/**
 * Get current device type
 */
export function getCurrentDevice(): 'mobile' | 'tablet' | 'desktop' {
  if (isMobile()) return 'mobile'
  if (isTablet()) return 'tablet'
  return 'desktop'
}

/**
 * Sanitize HTML content
 */
export function sanitizeHtml(html: string): string {
  // Basic HTML sanitization - in production, use a proper library like DOMPurify
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
}

/**
 * Validate email address
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validate URL
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * Get image dimensions
 */
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      resolve({ width: img.width, height: img.height })
    }
    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}

/**
 * Convert file to base64
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

/**
 * Download JSON as file
 */
export function downloadJson(data: any, filename: string): void {
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

/**
 * Copy text to clipboard
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    return false
  }
}

/**
 * Generate CSS custom properties from theme
 */
export function generateCssVariables(theme: Record<string, string>): string {
  return Object.entries(theme)
    .map(([key, value]) => `--${key}: ${value};`)
    .join('\n')
}

/**
 * Parse CSS custom properties
 */
export function parseCssVariables(css: string): Record<string, string> {
  const variables: Record<string, string> = {}
  const matches = css.match(/--[\w-]+:\s*[^;]+/g)
  
  if (matches) {
    matches.forEach(match => {
      const [property, value] = match.split(':').map(s => s.trim())
      if (property && value) {
        variables[property.replace('--', '')] = value
      }
    })
  }
  
  return variables
}

/**
 * Merge block configurations
 */
export function mergeBlockConfigurations(
  base: Record<string, any>,
  override: Record<string, any>
): Record<string, any> {
  const result = { ...base }
  
  for (const key in override) {
    if (override[key] !== null && override[key] !== undefined) {
      if (typeof override[key] === 'object' && !Array.isArray(override[key])) {
        result[key] = mergeBlockConfigurations(result[key] || {}, override[key])
      } else {
        result[key] = override[key]
      }
    }
  }
  
  return result
}
