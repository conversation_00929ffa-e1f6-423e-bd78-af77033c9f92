import { PageData, PageBlock } from '../types'

/**
 * Performance optimization utilities for the Page Builder
 */

// Cache for block components to avoid re-imports
const blockComponentCache = new Map<string, any>()

// Cache for processed page data
const pageDataCache = new Map<string, { data: PageData; timestamp: number }>()
const CACHE_TTL = 5 * 60 * 1000 // 5 minutes

/**
 * Preload block components for better performance
 */
export async function preloadBlockComponents(blockTypes: string[]) {
  const loadPromises = blockTypes.map(async (blockType) => {
    if (blockComponentCache.has(blockType)) {
      return blockComponentCache.get(blockType)
    }

    try {
      let component
      switch (blockType) {
        case 'hero':
          component = await import('../blocks/hero-block')
          break
        case 'product-grid':
          component = await import('../blocks/product-grid-block')
          break
        case 'testimonials':
          component = await import('../blocks/testimonials-block')
          break
        case 'newsletter':
          component = await import('../blocks/landing/newsletter-signup-block')
          break
        case 'text':
          component = await import('../blocks/text-block')
          break
        case 'story-section':
          component = await import('../blocks/story-section-block')
          break
        case 'values-grid':
          component = await import('../blocks/values-grid-block')
          break
        case 'mission-statement':
          component = await import('../blocks/mission-statement-block')
          break
        case 'contact-info':
          component = await import('../blocks/contact-info-block')
          break
        case 'contact-form':
          component = await import('../blocks/contact-form-block')
          break
        case 'editorial-grid':
          component = await import('../blocks/landing/editorial-section-block')
          break
        case 'special-offers-banner':
          component = await import('../blocks/landing/special-offers-banner-block')
          break
        case 'featured-categories':
          component = await import('../blocks/featured-categories-block')
          break
        default:
          console.warn(`Unknown block type for preloading: ${blockType}`)
          return null
      }

      if (component) {
        blockComponentCache.set(blockType, component)
        return component
      }
    } catch (error) {
      console.error(`Failed to preload block component: ${blockType}`, error)
    }

    return null
  })

  await Promise.allSettled(loadPromises)
}

/**
 * Get cached block component
 */
export function getCachedBlockComponent(blockType: string) {
  return blockComponentCache.get(blockType)
}

/**
 * Optimize page data for rendering
 */
export function optimizePageData(page: PageData): PageData {
  const cacheKey = `${page.id}-${page.updatedAt?.getTime() || Date.now()}`
  
  // Check cache first
  const cached = pageDataCache.get(cacheKey)
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data
  }

  // Optimize blocks
  const optimizedBlocks = page.blocks
    .filter(block => block.isVisible) // Remove invisible blocks
    .sort((a, b) => a.position - b.position) // Sort by position
    .map(optimizeBlock) // Optimize individual blocks

  const optimizedPage: PageData = {
    ...page,
    blocks: optimizedBlocks,
    settings: {
      ...page.settings,
      // Minify custom CSS if present
      customCss: page.settings?.customCss ? minifyCSS(page.settings.customCss) : undefined,
    }
  }

  // Cache the optimized data
  pageDataCache.set(cacheKey, {
    data: optimizedPage,
    timestamp: Date.now()
  })

  return optimizedPage
}

/**
 * Optimize individual block data
 */
function optimizeBlock(block: PageBlock): PageBlock {
  // Remove empty or undefined configuration properties
  const cleanConfiguration = Object.fromEntries(
    Object.entries(block.configuration || {}).filter(([_, value]) => 
      value !== undefined && value !== null && value !== ''
    )
  )

  return {
    ...block,
    configuration: cleanConfiguration,
    // Remove unused properties in production
    ...(process.env.NODE_ENV === 'production' && {
      content: undefined, // Remove if not used
      conditions: block.conditions && Object.keys(block.conditions).length > 0 ? block.conditions : undefined,
    })
  }
}

/**
 * Simple CSS minification
 */
function minifyCSS(css: string): string {
  return css
    .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/;\s*}/g, '}') // Remove semicolon before closing brace
    .replace(/\s*{\s*/g, '{') // Remove spaces around opening brace
    .replace(/;\s*/g, ';') // Remove spaces after semicolon
    .trim()
}

/**
 * Lazy load images in blocks
 */
export function optimizeImages(blocks: PageBlock[]): PageBlock[] {
  return blocks.map(block => {
    if (block.configuration && typeof block.configuration === 'object') {
      const config = { ...block.configuration }
      
      // Add lazy loading to image configurations
      if (config.image && typeof config.image === 'string') {
        // For single image
        config.loading = 'lazy'
      } else if (config.images && Array.isArray(config.images)) {
        // For multiple images
        config.images = config.images.map((img: any) => ({
          ...img,
          loading: 'lazy'
        }))
      } else if (config.backgroundImage) {
        // For background images, we can't use lazy loading directly
        // but we can mark them for intersection observer
        config.lazyBackground = true
      }

      return {
        ...block,
        configuration: config
      }
    }
    
    return block
  })
}

/**
 * Calculate page performance metrics
 */
export interface PagePerformanceMetrics {
  blockCount: number
  visibleBlockCount: number
  estimatedLoadTime: number
  hasHeavyBlocks: boolean
  optimizationSuggestions: string[]
}

export function calculatePageMetrics(page: PageData): PagePerformanceMetrics {
  const visibleBlocks = page.blocks.filter(block => block.isVisible)
  const heavyBlockTypes = ['product-grid', 'testimonials', 'gallery', 'video']
  const hasHeavyBlocks = visibleBlocks.some(block => heavyBlockTypes.includes(block.type))
  
  // Estimate load time based on block count and types
  let estimatedLoadTime = 500 // Base load time in ms
  estimatedLoadTime += visibleBlocks.length * 100 // 100ms per block
  
  if (hasHeavyBlocks) {
    estimatedLoadTime += 1000 // Additional time for heavy blocks
  }

  const suggestions: string[] = []
  
  if (visibleBlocks.length > 10) {
    suggestions.push('Consider reducing the number of blocks for better performance')
  }
  
  if (hasHeavyBlocks) {
    suggestions.push('Consider lazy loading for heavy blocks like product grids')
  }
  
  if (page.settings?.customCss && page.settings.customCss.length > 5000) {
    suggestions.push('Custom CSS is quite large, consider optimizing')
  }

  return {
    blockCount: page.blocks.length,
    visibleBlockCount: visibleBlocks.length,
    estimatedLoadTime,
    hasHeavyBlocks,
    optimizationSuggestions: suggestions
  }
}

/**
 * Prefetch critical resources for a page
 */
export function prefetchPageResources(page: PageData) {
  // Prefetch critical images
  const criticalImages = extractCriticalImages(page.blocks.slice(0, 3)) // First 3 blocks
  
  criticalImages.forEach(imageUrl => {
    if (typeof window !== 'undefined') {
      const link = document.createElement('link')
      link.rel = 'prefetch'
      link.href = imageUrl
      document.head.appendChild(link)
    }
  })
}

/**
 * Extract critical images from blocks
 */
function extractCriticalImages(blocks: PageBlock[]): string[] {
  const images: string[] = []
  
  blocks.forEach(block => {
    const config = block.configuration
    
    if (config?.image && typeof config.image === 'string') {
      images.push(config.image)
    } else if (config?.backgroundImage) {
      images.push(config.backgroundImage)
    } else if (config?.images && Array.isArray(config.images)) {
      config.images.forEach((img: any) => {
        if (typeof img === 'string') {
          images.push(img)
        } else if (img?.src) {
          images.push(img.src)
        }
      })
    }
  })
  
  return images
}

/**
 * Clear performance caches
 */
export function clearPerformanceCaches() {
  blockComponentCache.clear()
  pageDataCache.clear()
}

/**
 * Get cache statistics
 */
export function getCacheStats() {
  return {
    blockComponents: blockComponentCache.size,
    pageData: pageDataCache.size,
    totalMemoryUsage: JSON.stringify([...blockComponentCache.values(), ...pageDataCache.values()]).length
  }
}
