// Database Schema Generator Service
// Visual database design and schema generation system

import { PrismaClient } from '@prisma/client'
import { 
  DatabaseSchema,
  DatabaseTable,
  DatabaseField,
  DatabaseRelationship,
  SchemaGenerationOptions,
  SchemaValidationResult,
  ValidationError,
  ValidationWarning,
  SchemaMigration,
  DatabaseFieldType
} from '../posts/types'
import { generateId } from '../page-builder/utils'

export class SchemaGeneratorService {
  private db: PrismaClient

  constructor() {
    this.db = new PrismaClient()
  }

  /**
   * Create a new database schema
   */
  async createSchema(schemaData: Omit<DatabaseSchema, 'id' | 'createdAt' | 'updatedAt'>): Promise<DatabaseSchema> {
    try {
      const schema = await this.db.databaseSchema.create({
        data: {
          ...schemaData,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      })

      return schema as DatabaseSchema
    } catch (error) {
      console.error('Error creating schema:', error)
      throw new Error('Failed to create schema')
    }
  }

  /**
   * Update existing schema
   */
  async updateSchema(schemaId: string, updates: Partial<DatabaseSchema>): Promise<DatabaseSchema> {
    try {
      const schema = await this.db.databaseSchema.update({
        where: { id: schemaId },
        data: {
          ...updates,
          updatedAt: new Date(),
        }
      })

      return schema as DatabaseSchema
    } catch (error) {
      console.error('Error updating schema:', error)
      throw new Error('Failed to update schema')
    }
  }

  /**
   * Add table to schema
   */
  async addTable(schemaId: string, tableData: Omit<DatabaseTable, 'id' | 'createdAt' | 'updatedAt'>): Promise<DatabaseTable> {
    try {
      // Validate table name
      this.validateTableName(tableData.name)

      // Get current schema
      const schema = await this.getSchema(schemaId)
      if (!schema) {
        throw new Error('Schema not found')
      }

      // Check for duplicate table names
      const existingTable = schema.tables.find(table => table.name === tableData.name)
      if (existingTable) {
        throw new Error(`Table '${tableData.name}' already exists`)
      }

      // Create the table
      const table: DatabaseTable = {
        ...tableData,
        id: generateId(),
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      // Update schema with new table
      const updatedTables = [...schema.tables, table]
      await this.updateSchema(schemaId, { tables: updatedTables })

      return table
    } catch (error) {
      console.error('Error adding table:', error)
      throw error
    }
  }

  /**
   * Update table in schema
   */
  async updateTable(schemaId: string, tableId: string, updates: Partial<DatabaseTable>): Promise<DatabaseTable> {
    try {
      const schema = await this.getSchema(schemaId)
      if (!schema) {
        throw new Error('Schema not found')
      }

      const tableIndex = schema.tables.findIndex(table => table.id === tableId)
      if (tableIndex === -1) {
        throw new Error('Table not found')
      }

      // Update the table
      const updatedTable = {
        ...schema.tables[tableIndex],
        ...updates,
        updatedAt: new Date(),
      }

      const updatedTables = [...schema.tables]
      updatedTables[tableIndex] = updatedTable

      await this.updateSchema(schemaId, { tables: updatedTables })

      return updatedTable
    } catch (error) {
      console.error('Error updating table:', error)
      throw error
    }
  }

  /**
   * Remove table from schema
   */
  async removeTable(schemaId: string, tableId: string): Promise<void> {
    try {
      const schema = await this.getSchema(schemaId)
      if (!schema) {
        throw new Error('Schema not found')
      }

      // Remove relationships that reference this table
      const updatedTables = schema.tables.map(table => ({
        ...table,
        relationships: table.relationships.filter(rel => 
          rel.fromTable !== tableId && rel.toTable !== tableId
        )
      })).filter(table => table.id !== tableId)

      await this.updateSchema(schemaId, { tables: updatedTables })
    } catch (error) {
      console.error('Error removing table:', error)
      throw error
    }
  }

  /**
   * Add field to table
   */
  async addField(schemaId: string, tableId: string, fieldData: Omit<DatabaseField, 'id'>): Promise<DatabaseField> {
    try {
      // Validate field name
      this.validateFieldName(fieldData.name)

      const schema = await this.getSchema(schemaId)
      if (!schema) {
        throw new Error('Schema not found')
      }

      const tableIndex = schema.tables.findIndex(table => table.id === tableId)
      if (tableIndex === -1) {
        throw new Error('Table not found')
      }

      const table = schema.tables[tableIndex]

      // Check for duplicate field names
      const existingField = table.fields.find(field => field.name === fieldData.name)
      if (existingField) {
        throw new Error(`Field '${fieldData.name}' already exists in table '${table.name}'`)
      }

      // Create the field
      const field: DatabaseField = {
        ...fieldData,
        id: generateId(),
        position: table.fields.length,
      }

      // Update table with new field
      const updatedFields = [...table.fields, field]
      await this.updateTable(schemaId, tableId, { fields: updatedFields })

      return field
    } catch (error) {
      console.error('Error adding field:', error)
      throw error
    }
  }

  /**
   * Update field in table
   */
  async updateField(schemaId: string, tableId: string, fieldId: string, updates: Partial<DatabaseField>): Promise<DatabaseField> {
    try {
      const schema = await this.getSchema(schemaId)
      if (!schema) {
        throw new Error('Schema not found')
      }

      const table = schema.tables.find(table => table.id === tableId)
      if (!table) {
        throw new Error('Table not found')
      }

      const fieldIndex = table.fields.findIndex(field => field.id === fieldId)
      if (fieldIndex === -1) {
        throw new Error('Field not found')
      }

      // Update the field
      const updatedField = {
        ...table.fields[fieldIndex],
        ...updates,
      }

      const updatedFields = [...table.fields]
      updatedFields[fieldIndex] = updatedField

      await this.updateTable(schemaId, tableId, { fields: updatedFields })

      return updatedField
    } catch (error) {
      console.error('Error updating field:', error)
      throw error
    }
  }

  /**
   * Remove field from table
   */
  async removeField(schemaId: string, tableId: string, fieldId: string): Promise<void> {
    try {
      const schema = await this.getSchema(schemaId)
      if (!schema) {
        throw new Error('Schema not found')
      }

      const table = schema.tables.find(table => table.id === tableId)
      if (!table) {
        throw new Error('Table not found')
      }

      // Remove relationships that reference this field
      const updatedTables = schema.tables.map(t => ({
        ...t,
        relationships: t.relationships.filter(rel => 
          !(rel.fromTable === tableId && rel.fromField === fieldId) &&
          !(rel.toTable === tableId && rel.toField === fieldId)
        )
      }))

      // Remove the field from the table
      const tableIndex = updatedTables.findIndex(t => t.id === tableId)
      updatedTables[tableIndex].fields = updatedTables[tableIndex].fields.filter(field => field.id !== fieldId)

      await this.updateSchema(schemaId, { tables: updatedTables })
    } catch (error) {
      console.error('Error removing field:', error)
      throw error
    }
  }

  /**
   * Add relationship between tables
   */
  async addRelationship(schemaId: string, relationshipData: Omit<DatabaseRelationship, 'id'>): Promise<DatabaseRelationship> {
    try {
      const schema = await this.getSchema(schemaId)
      if (!schema) {
        throw new Error('Schema not found')
      }

      // Validate relationship
      this.validateRelationship(schema, relationshipData)

      // Create the relationship
      const relationship: DatabaseRelationship = {
        ...relationshipData,
        id: generateId(),
      }

      // Add relationship to the from table
      const fromTableIndex = schema.tables.findIndex(table => table.id === relationshipData.fromTable)
      if (fromTableIndex !== -1) {
        const updatedTables = [...schema.tables]
        updatedTables[fromTableIndex].relationships.push(relationship)
        await this.updateSchema(schemaId, { tables: updatedTables })
      }

      return relationship
    } catch (error) {
      console.error('Error adding relationship:', error)
      throw error
    }
  }

  /**
   * Get schema by ID
   */
  async getSchema(schemaId: string): Promise<DatabaseSchema | null> {
    try {
      const schema = await this.db.databaseSchema.findUnique({
        where: { id: schemaId }
      })

      return schema as DatabaseSchema | null
    } catch (error) {
      console.error('Error fetching schema:', error)
      return null
    }
  }

  /**
   * Get all schemas
   */
  async getAllSchemas(): Promise<DatabaseSchema[]> {
    try {
      const schemas = await this.db.databaseSchema.findMany({
        orderBy: { updatedAt: 'desc' }
      })

      return schemas as DatabaseSchema[]
    } catch (error) {
      console.error('Error fetching schemas:', error)
      return []
    }
  }

  /**
   * Validate schema
   */
  validateSchema(schema: DatabaseSchema): SchemaValidationResult {
    const errors: ValidationError[] = []
    const warnings: ValidationWarning[] = []

    // Validate tables
    schema.tables.forEach(table => {
      // Check for primary key
      const hasPrimaryKey = table.fields.some(field => field.isPrimaryKey)
      if (!hasPrimaryKey) {
        errors.push({
          type: 'missing_primary_key',
          message: `Table '${table.name}' has no primary key`,
          table: table.name,
          severity: 'error'
        })
      }

      // Check for duplicate field names
      const fieldNames = table.fields.map(field => field.name)
      const duplicateFields = fieldNames.filter((name, index) => fieldNames.indexOf(name) !== index)
      duplicateFields.forEach(fieldName => {
        errors.push({
          type: 'duplicate_field',
          message: `Duplicate field name '${fieldName}' in table '${table.name}'`,
          table: table.name,
          field: fieldName,
          severity: 'error'
        })
      })

      // Validate relationships
      table.relationships.forEach(relationship => {
        const toTable = schema.tables.find(t => t.id === relationship.toTable)
        if (!toTable) {
          errors.push({
            type: 'invalid_relationship',
            message: `Relationship '${relationship.name}' references non-existent table`,
            table: table.name,
            relationship: relationship.name,
            severity: 'error'
          })
        }
      })
    })

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions: []
    }
  }

  /**
   * Generate schema code
   */
  generateSchemaCode(schema: DatabaseSchema, options: SchemaGenerationOptions): string {
    switch (options.target) {
      case 'prisma':
        return this.generatePrismaSchema(schema, options)
      case 'sql':
        return this.generateSQLSchema(schema, options)
      case 'typescript':
        return this.generateTypeScriptTypes(schema, options)
      case 'graphql':
        return this.generateGraphQLSchema(schema, options)
      default:
        throw new Error(`Unsupported target: ${options.target}`)
    }
  }

  /**
   * Generate Prisma schema
   */
  private generatePrismaSchema(schema: DatabaseSchema, options: SchemaGenerationOptions): string {
    let output = ''

    if (options.includeComments) {
      output += `// Generated Prisma Schema for ${schema.name}\n`
      output += `// Version: ${schema.version}\n`
      if (schema.description) {
        output += `// ${schema.description}\n`
      }
      output += '\n'
    }

    // Generate models
    schema.tables.forEach(table => {
      if (options.includeComments && table.description) {
        output += `// ${table.description}\n`
      }

      output += `model ${this.toPascalCase(table.name)} {\n`

      // Generate fields
      table.fields.forEach(field => {
        output += `  ${field.name.padEnd(20)} ${this.mapToPrismaType(field.type)}`
        
        if (field.isPrimaryKey) output += ' @id'
        if (field.isUnique) output += ' @unique'
        if (field.defaultValue !== undefined) {
          output += ` @default(${this.formatDefaultValue(field.defaultValue, field.type)})`
        }
        
        if (options.includeComments && field.description) {
          output += ` // ${field.description}`
        }
        
        output += '\n'
      })

      // Generate relationships
      table.relationships.forEach(relationship => {
        const toTable = schema.tables.find(t => t.id === relationship.toTable)
        if (toTable) {
          output += `  ${relationship.name.padEnd(20)} ${this.toPascalCase(toTable.name)}`
          if (relationship.type === 'one-to-many') output += '[]'
          output += '\n'
        }
      })

      output += `\n  @@map("${table.name}")\n`
      output += '}\n\n'
    })

    return output
  }

  /**
   * Validate table name
   */
  private validateTableName(name: string): void {
    if (!name || name.trim().length === 0) {
      throw new Error('Table name cannot be empty')
    }

    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name)) {
      throw new Error('Table name must be a valid identifier')
    }

    // Check for reserved words
    const reservedWords = ['user', 'order', 'group', 'table', 'index', 'key', 'value']
    if (reservedWords.includes(name.toLowerCase())) {
      throw new Error(`'${name}' is a reserved word and cannot be used as a table name`)
    }
  }

  /**
   * Validate field name
   */
  private validateFieldName(name: string): void {
    if (!name || name.trim().length === 0) {
      throw new Error('Field name cannot be empty')
    }

    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name)) {
      throw new Error('Field name must be a valid identifier')
    }
  }

  /**
   * Validate relationship
   */
  private validateRelationship(schema: DatabaseSchema, relationship: Omit<DatabaseRelationship, 'id'>): void {
    const fromTable = schema.tables.find(table => table.id === relationship.fromTable)
    const toTable = schema.tables.find(table => table.id === relationship.toTable)

    if (!fromTable) {
      throw new Error('From table not found')
    }

    if (!toTable) {
      throw new Error('To table not found')
    }

    const fromField = fromTable.fields.find(field => field.id === relationship.fromField)
    const toField = toTable.fields.find(field => field.id === relationship.toField)

    if (!fromField) {
      throw new Error('From field not found')
    }

    if (!toField) {
      throw new Error('To field not found')
    }

    // Check type compatibility
    if (fromField.type !== toField.type) {
      throw new Error('Field types must match for relationships')
    }
  }

  /**
   * Helper methods
   */
  private toPascalCase(str: string): string {
    return str.replace(/(^\w|_\w)/g, match => match.replace('_', '').toUpperCase())
  }

  private mapToPrismaType(type: DatabaseFieldType): string {
    const typeMap: Record<DatabaseFieldType, string> = {
      'string': 'String',
      'text': 'String',
      'integer': 'Int',
      'bigint': 'BigInt',
      'decimal': 'Decimal',
      'float': 'Float',
      'boolean': 'Boolean',
      'date': 'DateTime',
      'datetime': 'DateTime',
      'timestamp': 'DateTime',
      'time': 'DateTime',
      'json': 'Json',
      'uuid': 'String',
      'enum': 'String',
      'binary': 'Bytes',
      'array': 'String[]',
    }

    return typeMap[type] || 'String'
  }

  private formatDefaultValue(value: any, type: DatabaseFieldType): string {
    if (value === null) return 'null'
    if (typeof value === 'string') return `"${value}"`
    if (typeof value === 'boolean') return value.toString()
    if (typeof value === 'number') return value.toString()
    if (type === 'datetime' && value === 'now') return 'now()'
    return `"${value}"`
  }

  private generateSQLSchema(schema: DatabaseSchema, options: SchemaGenerationOptions): string {
    // SQL generation implementation
    return '-- SQL schema generation not implemented yet'
  }

  private generateTypeScriptTypes(schema: DatabaseSchema, options: SchemaGenerationOptions): string {
    // TypeScript generation implementation
    return '// TypeScript types generation not implemented yet'
  }

  private generateGraphQLSchema(schema: DatabaseSchema, options: SchemaGenerationOptions): string {
    // GraphQL generation implementation
    return '# GraphQL schema generation not implemented yet'
  }
}

export const schemaGeneratorService = new SchemaGeneratorService()
