'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Progress } from '@/components/ui/progress'
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Info, 
  RefreshCw,
  FileText,
  Database,
  Link,
  Key
} from 'lucide-react'
import { DatabaseSchema, SchemaValidationResult, ValidationError, ValidationWarning } from '../../posts/types'
import { schemaGeneratorService } from '../schema-generator-service'

interface SchemaValidatorProps {
  schema: DatabaseSchema
}

export function SchemaValidator({ schema }: SchemaValidatorProps) {
  const [validationResult, setValidationResult] = useState<SchemaValidationResult | null>(null)
  const [isValidating, setIsValidating] = useState(false)

  const validateSchema = async () => {
    setIsValidating(true)
    try {
      // Simulate validation delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const result = schemaGeneratorService.validateSchema(schema)
      setValidationResult(result)
    } catch (error) {
      console.error('Validation error:', error)
    } finally {
      setIsValidating(false)
    }
  }

  useEffect(() => {
    validateSchema()
  }, [schema])

  const getValidationScore = () => {
    if (!validationResult) return 0
    
    const totalIssues = validationResult.errors.length + validationResult.warnings.length
    const maxIssues = schema.tables.length * 3 // Rough estimate
    
    return Math.max(0, Math.min(100, 100 - (totalIssues / maxIssues) * 100))
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent'
    if (score >= 60) return 'Good'
    if (score >= 40) return 'Fair'
    return 'Poor'
  }

  const groupErrorsByType = (errors: ValidationError[]) => {
    return errors.reduce((groups, error) => {
      const type = error.type
      if (!groups[type]) {
        groups[type] = []
      }
      groups[type].push(error)
      return groups
    }, {} as Record<string, ValidationError[]>)
  }

  const getErrorIcon = (severity: string) => {
    switch (severity) {
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default:
        return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  const getErrorTypeIcon = (type: string) => {
    switch (type) {
      case 'missing_primary_key':
        return <Key className="h-4 w-4" />
      case 'duplicate_field':
        return <FileText className="h-4 w-4" />
      case 'invalid_relationship':
        return <Link className="h-4 w-4" />
      default:
        return <Database className="h-4 w-4" />
    }
  }

  const score = getValidationScore()

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Schema Validation</h3>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={validateSchema}
            disabled={isValidating}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isValidating ? 'animate-spin' : ''}`} />
            Validate
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {/* Validation Score */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Schema Health Score</CardTitle>
            </CardHeader>
            <CardContent>
              {isValidating ? (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    <span className="text-sm">Validating schema...</span>
                  </div>
                  <Progress value={50} className="w-full" />
                </div>
              ) : validationResult ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className={`text-2xl font-bold ${getScoreColor(score)}`}>
                      {Math.round(score)}%
                    </span>
                    <Badge 
                      variant={score >= 80 ? 'default' : score >= 60 ? 'secondary' : 'destructive'}
                    >
                      {getScoreLabel(score)}
                    </Badge>
                  </div>
                  <Progress value={score} className="w-full" />
                  <div className="grid grid-cols-3 gap-2 text-sm">
                    <div className="text-center">
                      <div className="font-medium text-red-600">{validationResult.errors.length}</div>
                      <div className="text-gray-500">Errors</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-yellow-600">{validationResult.warnings.length}</div>
                      <div className="text-gray-500">Warnings</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-green-600">{schema.tables.length}</div>
                      <div className="text-gray-500">Tables</div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <Database className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">Click validate to check schema</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Validation Results */}
          {validationResult && !isValidating && (
            <>
              {/* Errors */}
              {validationResult.errors.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center space-x-2">
                      <XCircle className="h-4 w-4 text-red-500" />
                      <span>Errors ({validationResult.errors.length})</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {Object.entries(groupErrorsByType(validationResult.errors)).map(([type, errors]) => (
                        <div key={type} className="space-y-2">
                          <div className="flex items-center space-x-2 text-sm font-medium">
                            {getErrorTypeIcon(type)}
                            <span className="capitalize">{type.replace(/_/g, ' ')}</span>
                            <Badge variant="destructive" className="text-xs">
                              {errors.length}
                            </Badge>
                          </div>
                          {errors.map((error, index) => (
                            <div key={index} className="ml-6 p-2 bg-red-50 border border-red-200 rounded text-sm">
                              <div className="flex items-start space-x-2">
                                {getErrorIcon(error.severity)}
                                <div className="flex-1">
                                  <p className="text-red-800">{error.message}</p>
                                  {error.table && (
                                    <p className="text-red-600 text-xs mt-1">
                                      Table: {error.table}
                                      {error.field && ` → Field: ${error.field}`}
                                      {error.relationship && ` → Relationship: ${error.relationship}`}
                                    </p>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Warnings */}
              {validationResult.warnings.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                      <span>Warnings ({validationResult.warnings.length})</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {validationResult.warnings.map((warning, index) => (
                        <div key={index} className="p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
                          <div className="flex items-start space-x-2">
                            <AlertTriangle className="h-4 w-4 text-yellow-500 flex-shrink-0 mt-0.5" />
                            <div className="flex-1">
                              <p className="text-yellow-800">{warning.message}</p>
                              {warning.table && (
                                <p className="text-yellow-600 text-xs mt-1">
                                  Table: {warning.table}
                                  {warning.field && ` → Field: ${warning.field}`}
                                </p>
                              )}
                              {warning.suggestion && (
                                <p className="text-yellow-700 text-xs mt-1 font-medium">
                                  Suggestion: {warning.suggestion}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Suggestions */}
              {validationResult.suggestions.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center space-x-2">
                      <Info className="h-4 w-4 text-blue-500" />
                      <span>Suggestions ({validationResult.suggestions.length})</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {validationResult.suggestions.map((suggestion, index) => (
                        <div key={index} className="p-2 bg-blue-50 border border-blue-200 rounded text-sm">
                          <div className="flex items-start space-x-2">
                            <Info className="h-4 w-4 text-blue-500 flex-shrink-0 mt-0.5" />
                            <div className="flex-1">
                              <p className="text-blue-800">{suggestion.message}</p>
                              <div className="flex items-center justify-between mt-1">
                                <p className="text-blue-600 text-xs">
                                  Action: {suggestion.action}
                                </p>
                                <Badge 
                                  variant={
                                    suggestion.impact === 'high' ? 'destructive' :
                                    suggestion.impact === 'medium' ? 'secondary' : 'outline'
                                  }
                                  className="text-xs"
                                >
                                  {suggestion.impact} impact
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Success State */}
              {validationResult.isValid && validationResult.errors.length === 0 && validationResult.warnings.length === 0 && (
                <Card>
                  <CardContent className="p-6">
                    <div className="text-center">
                      <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                      <h3 className="text-lg font-medium text-green-800 mb-2">Schema is Valid!</h3>
                      <p className="text-green-600 text-sm">
                        Your database schema has no errors or warnings. It's ready for generation.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}

          {/* Schema Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Schema Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="font-medium">Tables</div>
                  <div className="text-gray-500">{schema.tables.length}</div>
                </div>
                <div>
                  <div className="font-medium">Total Fields</div>
                  <div className="text-gray-500">
                    {schema.tables.reduce((total, table) => total + table.fields.length, 0)}
                  </div>
                </div>
                <div>
                  <div className="font-medium">Relationships</div>
                  <div className="text-gray-500">
                    {schema.tables.reduce((total, table) => total + table.relationships.length, 0)}
                  </div>
                </div>
                <div>
                  <div className="font-medium">Primary Keys</div>
                  <div className="text-gray-500">
                    {schema.tables.reduce((total, table) => 
                      total + table.fields.filter(field => field.isPrimaryKey).length, 0
                    )}
                  </div>
                </div>
                <div>
                  <div className="font-medium">Unique Fields</div>
                  <div className="text-gray-500">
                    {schema.tables.reduce((total, table) => 
                      total + table.fields.filter(field => field.isUnique).length, 0
                    )}
                  </div>
                </div>
                <div>
                  <div className="font-medium">Indexed Fields</div>
                  <div className="text-gray-500">
                    {schema.tables.reduce((total, table) => 
                      total + table.fields.filter(field => field.isIndexed).length, 0
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </ScrollArea>
    </div>
  )
}
