'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { 
  Download, 
  Copy, 
  Code, 
  FileText, 
  Database, 
  Layers,
  CheckCircle,
  RefreshCw
} from 'lucide-react'
import { DatabaseSchema, SchemaGenerationOptions } from '../../posts/types'
import { schemaGeneratorService } from '../schema-generator-service'
import { toast } from 'sonner'

interface CodeGeneratorProps {
  schema: DatabaseSchema
  onExport: (schema: DatabaseSchema, format: string) => void
}

export function CodeGenerator({ schema, onExport }: CodeGeneratorProps) {
  const [generatedCode, setGeneratedCode] = useState<Record<string, string>>({})
  const [isGenerating, setIsGenerating] = useState(false)
  const [activeTab, setActiveTab] = useState<'prisma' | 'sql' | 'typescript' | 'graphql'>('prisma')
  const [options, setOptions] = useState<SchemaGenerationOptions>({
    target: 'prisma',
    includeComments: true,
    includeIndexes: true,
    includeConstraints: true,
    includeSeeds: false,
    formatOutput: true,
  })

  const generateCode = async (target: string) => {
    setIsGenerating(true)
    try {
      const generationOptions = { ...options, target: target as any }
      const code = schemaGeneratorService.generateSchemaCode(schema, generationOptions)
      
      setGeneratedCode(prev => ({
        ...prev,
        [target]: code
      }))
    } catch (error) {
      console.error('Code generation error:', error)
      toast.error(`Failed to generate ${target.toUpperCase()} code`)
    } finally {
      setIsGenerating(false)
    }
  }

  const generateAllCode = async () => {
    setIsGenerating(true)
    const targets = ['prisma', 'sql', 'typescript', 'graphql']
    
    for (const target of targets) {
      try {
        const generationOptions = { ...options, target: target as any }
        const code = schemaGeneratorService.generateSchemaCode(schema, generationOptions)
        
        setGeneratedCode(prev => ({
          ...prev,
          [target]: code
        }))
      } catch (error) {
        console.error(`Error generating ${target}:`, error)
      }
    }
    
    setIsGenerating(false)
    toast.success('All code generated successfully')
  }

  const copyToClipboard = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code)
      toast.success('Code copied to clipboard')
    } catch (error) {
      toast.error('Failed to copy code')
    }
  }

  const downloadCode = (code: string, filename: string) => {
    const blob = new Blob([code], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success(`Downloaded ${filename}`)
  }

  const getFileExtension = (target: string) => {
    const extensions = {
      prisma: 'prisma',
      sql: 'sql',
      typescript: 'ts',
      graphql: 'graphql'
    }
    return extensions[target as keyof typeof extensions] || 'txt'
  }

  const getLanguageLabel = (target: string) => {
    const labels = {
      prisma: 'Prisma Schema',
      sql: 'SQL DDL',
      typescript: 'TypeScript Types',
      graphql: 'GraphQL Schema'
    }
    return labels[target as keyof typeof labels] || target
  }

  const getLanguageIcon = (target: string) => {
    const icons = {
      prisma: <Database className="h-4 w-4" />,
      sql: <FileText className="h-4 w-4" />,
      typescript: <Code className="h-4 w-4" />,
      graphql: <Layers className="h-4 w-4" />
    }
    return icons[target as keyof typeof icons] || <Code className="h-4 w-4" />
  }

  useEffect(() => {
    generateCode(activeTab)
  }, [schema, options, activeTab])

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Code Generator</h3>
          <Button 
            size="sm" 
            onClick={generateAllCode}
            disabled={isGenerating}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isGenerating ? 'animate-spin' : ''}`} />
            Generate All
          </Button>
        </div>
      </div>

      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Generation Options */}
        <div className="p-4 border-b bg-gray-50">
          <h4 className="font-medium mb-3">Generation Options</h4>
          <div className="grid grid-cols-2 gap-3">
            <div className="flex items-center space-x-2">
              <Switch
                id="include-comments"
                checked={options.includeComments}
                onCheckedChange={(checked) => setOptions(prev => ({ ...prev, includeComments: checked }))}
              />
              <Label htmlFor="include-comments" className="text-sm">Include Comments</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="include-indexes"
                checked={options.includeIndexes}
                onCheckedChange={(checked) => setOptions(prev => ({ ...prev, includeIndexes: checked }))}
              />
              <Label htmlFor="include-indexes" className="text-sm">Include Indexes</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="include-constraints"
                checked={options.includeConstraints}
                onCheckedChange={(checked) => setOptions(prev => ({ ...prev, includeConstraints: checked }))}
              />
              <Label htmlFor="include-constraints" className="text-sm">Include Constraints</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="format-output"
                checked={options.formatOutput}
                onCheckedChange={(checked) => setOptions(prev => ({ ...prev, formatOutput: checked }))}
              />
              <Label htmlFor="format-output" className="text-sm">Format Output</Label>
            </div>
          </div>
        </div>

        {/* Code Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="flex-1 flex flex-col">
          <div className="border-b bg-white px-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="prisma" className="flex items-center space-x-2">
                <Database className="h-4 w-4" />
                <span>Prisma</span>
              </TabsTrigger>
              <TabsTrigger value="sql" className="flex items-center space-x-2">
                <FileText className="h-4 w-4" />
                <span>SQL</span>
              </TabsTrigger>
              <TabsTrigger value="typescript" className="flex items-center space-x-2">
                <Code className="h-4 w-4" />
                <span>TypeScript</span>
              </TabsTrigger>
              <TabsTrigger value="graphql" className="flex items-center space-x-2">
                <Layers className="h-4 w-4" />
                <span>GraphQL</span>
              </TabsTrigger>
            </TabsList>
          </div>

          {(['prisma', 'sql', 'typescript', 'graphql'] as const).map((target) => (
            <TabsContent key={target} value={target} className="flex-1 flex flex-col mt-0">
              <div className="p-4 border-b bg-white">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getLanguageIcon(target)}
                    <span className="font-medium">{getLanguageLabel(target)}</span>
                    {generatedCode[target] && (
                      <Badge variant="secondary" className="text-xs">
                        {generatedCode[target].split('\n').length} lines
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    {generatedCode[target] && (
                      <>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(generatedCode[target])}
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copy
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => downloadCode(
                            generatedCode[target], 
                            `${schema.name}.${getFileExtension(target)}`
                          )}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => onExport(schema, target)}
                        >
                          Export
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex-1 overflow-hidden">
                {isGenerating && activeTab === target ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <RefreshCw className="h-8 w-8 mx-auto mb-4 animate-spin text-blue-500" />
                      <p className="text-sm text-gray-500">Generating {getLanguageLabel(target)}...</p>
                    </div>
                  </div>
                ) : generatedCode[target] ? (
                  <ScrollArea className="h-full">
                    <pre className="p-4 text-sm font-mono bg-gray-50 h-full overflow-auto">
                      <code>{generatedCode[target]}</code>
                    </pre>
                  </ScrollArea>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <Code className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Code Generated</h3>
                      <p className="text-gray-500 mb-4">
                        Click generate to create {getLanguageLabel(target)} code
                      </p>
                      <Button onClick={() => generateCode(target)}>
                        Generate {getLanguageLabel(target)}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>

      {/* Schema Info */}
      <div className="p-4 border-t bg-gray-50">
        <div className="grid grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <div className="font-medium">{schema.tables.length}</div>
            <div className="text-gray-500">Tables</div>
          </div>
          <div className="text-center">
            <div className="font-medium">
              {schema.tables.reduce((total, table) => total + table.fields.length, 0)}
            </div>
            <div className="text-gray-500">Fields</div>
          </div>
          <div className="text-center">
            <div className="font-medium">
              {schema.tables.reduce((total, table) => total + table.relationships.length, 0)}
            </div>
            <div className="text-gray-500">Relations</div>
          </div>
          <div className="text-center">
            <div className="font-medium">v{schema.version}</div>
            <div className="text-gray-500">Version</div>
          </div>
        </div>
      </div>
    </div>
  )
}
