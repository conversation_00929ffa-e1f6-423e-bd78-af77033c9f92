'use client'

import React, { useState, useCallback } from 'react'
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent } from '@dnd-kit/core'
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { 
  Plus, 
  Save, 
  X, 
  Trash2, 
  <PERSON>,
  GripVertical,
  Key,
  Link,
  Database,
  Shield
} from 'lucide-react'
import { DatabaseTable, DatabaseField, DatabaseSchema, DatabaseFieldType } from '../../posts/types'
import { FieldEditor } from './field-editor'
import { generateId } from '../../page-builder/utils'

interface TableDesignerProps {
  table: DatabaseTable
  schema: DatabaseSchema
  onSave: (updates: Partial<DatabaseTable>) => void
  onCancel: () => void
}

export function TableDesigner({ table, schema, onSave, onCancel }: TableDesignerProps) {
  const [localTable, setLocalTable] = useState<DatabaseTable>(table)
  const [selectedField, setSelectedField] = useState<DatabaseField | null>(null)
  const [isFieldEditorOpen, setIsFieldEditorOpen] = useState(false)
  const [activeTab, setActiveTab] = useState<'fields' | 'indexes' | 'constraints' | 'settings'>('fields')
  const [draggedField, setDraggedField] = useState<DatabaseField | null>(null)

  const handleSave = useCallback(() => {
    onSave(localTable)
  }, [localTable, onSave])

  const handleTableUpdate = useCallback((updates: Partial<DatabaseTable>) => {
    setLocalTable(prev => ({ ...prev, ...updates }))
  }, [])

  const handleAddField = useCallback(() => {
    const newField: DatabaseField = {
      id: generateId(),
      name: `field_${localTable.fields.length + 1}`,
      displayName: `Field ${localTable.fields.length + 1}`,
      type: 'string',
      isRequired: false,
      isPrimaryKey: false,
      isUnique: false,
      isIndexed: false,
      validation: [],
      position: localTable.fields.length,
      metadata: {
        category: 'user',
        tags: [],
      }
    }

    setLocalTable(prev => ({
      ...prev,
      fields: [...prev.fields, newField]
    }))

    setSelectedField(newField)
    setIsFieldEditorOpen(true)
  }, [localTable.fields])

  const handleUpdateField = useCallback((fieldId: string, updates: Partial<DatabaseField>) => {
    setLocalTable(prev => ({
      ...prev,
      fields: prev.fields.map(field => 
        field.id === fieldId ? { ...field, ...updates } : field
      )
    }))

    if (selectedField?.id === fieldId) {
      setSelectedField(prev => prev ? { ...prev, ...updates } : null)
    }
  }, [selectedField])

  const handleDeleteField = useCallback((fieldId: string) => {
    if (!confirm('Are you sure you want to delete this field?')) {
      return
    }

    setLocalTable(prev => ({
      ...prev,
      fields: prev.fields.filter(field => field.id !== fieldId)
    }))

    if (selectedField?.id === fieldId) {
      setSelectedField(null)
      setIsFieldEditorOpen(false)
    }
  }, [selectedField])

  const handleDragStart = (event: DragStartEvent) => {
    const field = localTable.fields.find(f => f.id === event.active.id)
    setDraggedField(field || null)
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    
    if (!over || active.id === over.id) {
      setDraggedField(null)
      return
    }

    const fields = localTable.fields
    const oldIndex = fields.findIndex(field => field.id === active.id)
    const newIndex = fields.findIndex(field => field.id === over.id)

    if (oldIndex !== -1 && newIndex !== -1) {
      const newFields = [...fields]
      const [movedField] = newFields.splice(oldIndex, 1)
      newFields.splice(newIndex, 0, movedField)

      // Update positions
      const updatedFields = newFields.map((field, index) => ({
        ...field,
        position: index
      }))

      setLocalTable(prev => ({
        ...prev,
        fields: updatedFields
      }))
    }

    setDraggedField(null)
  }

  const getFieldTypeIcon = (type: DatabaseFieldType) => {
    const icons = {
      'string': '📝',
      'text': '📄',
      'integer': '🔢',
      'bigint': '🔢',
      'decimal': '💰',
      'float': '📊',
      'boolean': '✅',
      'date': '📅',
      'datetime': '🕐',
      'timestamp': '⏰',
      'time': '🕐',
      'json': '📋',
      'uuid': '🔑',
      'enum': '📝',
      'binary': '💾',
      'array': '📚',
    }
    return icons[type] || '📝'
  }

  if (isFieldEditorOpen && selectedField) {
    return (
      <FieldEditor
        field={selectedField}
        table={localTable}
        schema={schema}
        onSave={(updates) => {
          handleUpdateField(selectedField.id, updates)
          setIsFieldEditorOpen(false)
        }}
        onCancel={() => setIsFieldEditorOpen(false)}
      />
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b bg-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Table Designer</h1>
            <p className="text-gray-600">
              Editing: {localTable.displayName} ({localTable.name})
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={onCancel}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleSave}>
              <Save className="h-4 w-4 mr-2" />
              Save Table
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Main Editor */}
        <div className="flex-1 flex flex-col">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="flex-1 flex flex-col">
            <div className="border-b bg-white px-4">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="fields">Fields</TabsTrigger>
                <TabsTrigger value="indexes">Indexes</TabsTrigger>
                <TabsTrigger value="constraints">Constraints</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="fields" className="flex-1 p-4 overflow-hidden">
              <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
                <div className="h-full flex flex-col">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">Table Fields</h3>
                    <Button onClick={handleAddField}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Field
                    </Button>
                  </div>

                  <ScrollArea className="flex-1">
                    <SortableContext 
                      items={localTable.fields.map(f => f.id)}
                      strategy={verticalListSortingStrategy}
                    >
                      <div className="space-y-2">
                        {localTable.fields.map((field) => (
                          <FieldRow
                            key={field.id}
                            field={field}
                            isSelected={selectedField?.id === field.id}
                            onSelect={() => {
                              setSelectedField(field)
                              setIsFieldEditorOpen(true)
                            }}
                            onDelete={() => handleDeleteField(field.id)}
                          />
                        ))}
                      </div>
                    </SortableContext>

                    {localTable.fields.length === 0 && (
                      <div className="text-center py-12 text-gray-500">
                        <Key className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p className="text-lg font-medium mb-2">No fields yet</p>
                        <p className="text-sm">Add fields to define your table structure</p>
                      </div>
                    )}
                  </ScrollArea>
                </div>

                <DragOverlay>
                  {draggedField && (
                    <div className="bg-white border rounded-lg p-3 shadow-lg">
                      <div className="flex items-center space-x-2">
                        <GripVertical className="h-4 w-4 text-gray-400" />
                        <span className="text-lg">{getFieldTypeIcon(draggedField.type)}</span>
                        <span className="font-medium">{draggedField.displayName}</span>
                        <Badge variant="secondary">{draggedField.type}</Badge>
                      </div>
                    </div>
                  )}
                </DragOverlay>
              </DndContext>
            </TabsContent>

            <TabsContent value="indexes" className="flex-1 p-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Indexes</h3>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Index
                  </Button>
                </div>

                <div className="text-center py-12 text-gray-500">
                  <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">No indexes yet</p>
                  <p className="text-sm">Add indexes to improve query performance</p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="constraints" className="flex-1 p-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Constraints</h3>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Constraint
                  </Button>
                </div>

                <div className="text-center py-12 text-gray-500">
                  <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">No constraints yet</p>
                  <p className="text-sm">Add constraints to enforce data integrity</p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="flex-1 p-4">
              <ScrollArea className="h-full">
                <div className="space-y-6 max-w-2xl">
                  <Card>
                    <CardHeader>
                      <CardTitle>Basic Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="table-name">Table Name</Label>
                          <Input
                            id="table-name"
                            value={localTable.name}
                            onChange={(e) => handleTableUpdate({ name: e.target.value })}
                            placeholder="table_name"
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            Database table name (lowercase, underscores)
                          </p>
                        </div>

                        <div>
                          <Label htmlFor="display-name">Display Name</Label>
                          <Input
                            id="display-name"
                            value={localTable.displayName}
                            onChange={(e) => handleTableUpdate({ displayName: e.target.value })}
                            placeholder="Table Display Name"
                          />
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="description">Description</Label>
                        <Textarea
                          id="description"
                          value={localTable.description || ''}
                          onChange={(e) => handleTableUpdate({ description: e.target.value })}
                          placeholder="Describe what this table stores..."
                          rows={3}
                        />
                      </div>

                      <div>
                        <Label htmlFor="schema">Schema</Label>
                        <Select
                          value={localTable.schema}
                          onValueChange={(value) => handleTableUpdate({ schema: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="public">public</SelectItem>
                            <SelectItem value="auth">auth</SelectItem>
                            <SelectItem value="admin">admin</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Table Options</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Active</Label>
                          <p className="text-sm text-gray-500">Enable this table in the schema</p>
                        </div>
                        <Switch
                          checked={localTable.isActive}
                          onCheckedChange={(checked) => handleTableUpdate({ isActive: checked })}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Timestamps</Label>
                          <p className="text-sm text-gray-500">Add created_at and updated_at fields</p>
                        </div>
                        <Switch
                          checked={localTable.metadata.timestamps}
                          onCheckedChange={(checked) => handleTableUpdate({ 
                            metadata: { ...localTable.metadata, timestamps: checked }
                          })}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Soft Delete</Label>
                          <p className="text-sm text-gray-500">Add deleted_at field for soft deletes</p>
                        </div>
                        <Switch
                          checked={localTable.metadata.softDelete}
                          onCheckedChange={(checked) => handleTableUpdate({ 
                            metadata: { ...localTable.metadata, softDelete: checked }
                          })}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Audit Trail</Label>
                          <p className="text-sm text-gray-500">Enable audit logging for this table</p>
                        </div>
                        <Switch
                          checked={localTable.metadata.auditEnabled}
                          onCheckedChange={(checked) => handleTableUpdate({ 
                            metadata: { ...localTable.metadata, auditEnabled: checked }
                          })}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}

// Field Row Component
interface FieldRowProps {
  field: DatabaseField
  isSelected: boolean
  onSelect: () => void
  onDelete: () => void
}

function FieldRow({ field, isSelected, onSelect, onDelete }: FieldRowProps) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ 
    id: field.id 
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const getFieldTypeIcon = (type: DatabaseFieldType) => {
    const icons = {
      'string': '📝',
      'text': '📄',
      'integer': '🔢',
      'bigint': '🔢',
      'decimal': '💰',
      'float': '📊',
      'boolean': '✅',
      'date': '📅',
      'datetime': '🕐',
      'timestamp': '⏰',
      'time': '🕐',
      'json': '📋',
      'uuid': '🔑',
      'enum': '📝',
      'binary': '💾',
      'array': '📚',
    }
    return icons[type] || '📝'
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        group border rounded-lg p-3 bg-white transition-all duration-200
        ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}
        ${isDragging ? 'opacity-50 shadow-lg' : ''}
      `}
    >
      <div className="flex items-center space-x-3">
        {/* Drag Handle */}
        <div
          {...attributes}
          {...listeners}
          className="flex-shrink-0 p-1 rounded cursor-grab hover:bg-gray-100 transition-colors"
        >
          <GripVertical className="h-4 w-4 text-gray-400" />
        </div>

        {/* Field Icon */}
        <div className="flex-shrink-0 text-lg">
          {getFieldTypeIcon(field.type)}
        </div>

        {/* Field Info */}
        <div className="flex-1 min-w-0 cursor-pointer" onClick={onSelect}>
          <div className="flex items-center justify-between mb-1">
            <h4 className="text-sm font-medium text-gray-900 truncate">
              {field.displayName}
            </h4>
            <div className="flex items-center space-x-1">
              {field.isPrimaryKey && (
                <Badge variant="destructive" className="text-xs">PK</Badge>
              )}
              {field.isUnique && (
                <Badge variant="outline" className="text-xs">Unique</Badge>
              )}
              {field.isRequired && (
                <Badge variant="secondary" className="text-xs">Required</Badge>
              )}
              <Badge variant="secondary" className="text-xs">
                {field.type}
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-500 font-mono">
              {field.name}
            </span>
            
            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation()
                  onSelect()
                }}
                className="h-6 w-6 p-0"
              >
                <Edit className="h-3 w-3" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete()
                }}
                className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Import missing dependencies
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
