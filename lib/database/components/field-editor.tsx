'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Save, 
  X, 
  Plus, 
  Trash2, 
  Key,
  Link,
  AlertCircle,
  Info
} from 'lucide-react'
import { DatabaseField, DatabaseTable, DatabaseSchema, DatabaseFieldType, FieldValidation } from '../../posts/types'

interface FieldEditorProps {
  field: DatabaseField
  table: DatabaseTable
  schema: DatabaseSchema
  onSave: (updates: Partial<DatabaseField>) => void
  onCancel: () => void
}

export function FieldEditor({ field, table, schema, onSave, onCancel }: FieldEditorProps) {
  const [localField, setLocalField] = useState<DatabaseField>(field)
  const [activeTab, setActiveTab] = useState<'general' | 'validation' | 'metadata'>('general')

  useEffect(() => {
    setLocalField(field)
  }, [field])

  const handleSave = () => {
    onSave(localField)
  }

  const handleFieldUpdate = (updates: Partial<DatabaseField>) => {
    setLocalField(prev => ({ ...prev, ...updates }))
  }

  const handleValidationAdd = () => {
    const newValidation: FieldValidation = {
      type: 'min',
      message: 'Validation failed',
    }
    handleFieldUpdate({ 
      validation: [...localField.validation, newValidation] 
    })
  }

  const handleValidationUpdate = (index: number, updates: Partial<FieldValidation>) => {
    const newValidation = localField.validation.map((rule, i) => 
      i === index ? { ...rule, ...updates } : rule
    )
    handleFieldUpdate({ validation: newValidation })
  }

  const handleValidationRemove = (index: number) => {
    const newValidation = localField.validation.filter((_, i) => i !== index)
    handleFieldUpdate({ validation: newValidation })
  }

  const fieldTypes: Array<{ value: DatabaseFieldType; label: string; description: string }> = [
    { value: 'string', label: 'String', description: 'Variable length text (VARCHAR)' },
    { value: 'text', label: 'Text', description: 'Long text content (TEXT)' },
    { value: 'integer', label: 'Integer', description: '32-bit signed integer' },
    { value: 'bigint', label: 'Big Integer', description: '64-bit signed integer' },
    { value: 'decimal', label: 'Decimal', description: 'Fixed-point decimal number' },
    { value: 'float', label: 'Float', description: 'Floating-point number' },
    { value: 'boolean', label: 'Boolean', description: 'True/false value' },
    { value: 'date', label: 'Date', description: 'Date without time' },
    { value: 'datetime', label: 'DateTime', description: 'Date and time' },
    { value: 'timestamp', label: 'Timestamp', description: 'Unix timestamp' },
    { value: 'time', label: 'Time', description: 'Time without date' },
    { value: 'json', label: 'JSON', description: 'JSON data structure' },
    { value: 'uuid', label: 'UUID', description: 'Universally unique identifier' },
    { value: 'enum', label: 'Enum', description: 'Enumerated values' },
    { value: 'binary', label: 'Binary', description: 'Binary data (BLOB)' },
    { value: 'array', label: 'Array', description: 'Array of values' },
  ]

  const validationTypes = [
    { value: 'min', label: 'Minimum', description: 'Minimum value or length' },
    { value: 'max', label: 'Maximum', description: 'Maximum value or length' },
    { value: 'pattern', label: 'Pattern', description: 'Regular expression pattern' },
    { value: 'custom', label: 'Custom', description: 'Custom validation function' },
    { value: 'range', label: 'Range', description: 'Value within range' },
    { value: 'length', label: 'Length', description: 'Exact length requirement' },
  ]

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b bg-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Field Editor</h1>
            <div className="flex items-center space-x-2 mt-1">
              <Badge variant="secondary">{localField.type}</Badge>
              <span className="text-sm text-gray-500">{localField.name}</span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={onCancel}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleSave}>
              <Save className="h-4 w-4 mr-2" />
              Save Field
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="h-full flex flex-col">
          <div className="border-b bg-white px-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="validation">Validation</TabsTrigger>
              <TabsTrigger value="metadata">Metadata</TabsTrigger>
            </TabsList>
          </div>

          <ScrollArea className="flex-1">
            <div className="p-4">
              <TabsContent value="general" className="space-y-6 mt-0">
                {/* Basic Properties */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Basic Properties</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="field-name">Field Name</Label>
                        <Input
                          id="field-name"
                          value={localField.name}
                          onChange={(e) => handleFieldUpdate({ name: e.target.value })}
                          placeholder="field_name"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          Database column name (lowercase, underscores)
                        </p>
                      </div>

                      <div>
                        <Label htmlFor="display-name">Display Name</Label>
                        <Input
                          id="display-name"
                          value={localField.displayName}
                          onChange={(e) => handleFieldUpdate({ displayName: e.target.value })}
                          placeholder="Field Display Name"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="field-description">Description</Label>
                      <Textarea
                        id="field-description"
                        value={localField.description || ''}
                        onChange={(e) => handleFieldUpdate({ description: e.target.value })}
                        placeholder="Optional description or help text"
                        rows={2}
                      />
                    </div>

                    <div>
                      <Label htmlFor="field-type">Data Type</Label>
                      <Select
                        value={localField.type}
                        onValueChange={(value) => handleFieldUpdate({ type: value as DatabaseFieldType })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {fieldTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              <div>
                                <div className="font-medium">{type.label}</div>
                                <div className="text-xs text-gray-500">{type.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>

                {/* Field Options */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Field Options</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="field-required"
                          checked={localField.isRequired}
                          onCheckedChange={(checked) => handleFieldUpdate({ isRequired: checked })}
                        />
                        <Label htmlFor="field-required">Required</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="field-unique"
                          checked={localField.isUnique}
                          onCheckedChange={(checked) => handleFieldUpdate({ isUnique: checked })}
                        />
                        <Label htmlFor="field-unique">Unique</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="field-primary"
                          checked={localField.isPrimaryKey}
                          onCheckedChange={(checked) => handleFieldUpdate({ isPrimaryKey: checked })}
                        />
                        <Label htmlFor="field-primary">Primary Key</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="field-indexed"
                          checked={localField.isIndexed}
                          onCheckedChange={(checked) => handleFieldUpdate({ isIndexed: checked })}
                        />
                        <Label htmlFor="field-indexed">Indexed</Label>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Type-specific Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Type-specific Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {(localField.type === 'string' || localField.type === 'text') && (
                      <div>
                        <Label htmlFor="field-length">Max Length</Label>
                        <Input
                          id="field-length"
                          type="number"
                          value={localField.length || ''}
                          onChange={(e) => handleFieldUpdate({ length: parseInt(e.target.value) || undefined })}
                          placeholder="255"
                        />
                      </div>
                    )}

                    {(localField.type === 'decimal' || localField.type === 'float') && (
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="field-precision">Precision</Label>
                          <Input
                            id="field-precision"
                            type="number"
                            value={localField.precision || ''}
                            onChange={(e) => handleFieldUpdate({ precision: parseInt(e.target.value) || undefined })}
                            placeholder="10"
                          />
                        </div>
                        <div>
                          <Label htmlFor="field-scale">Scale</Label>
                          <Input
                            id="field-scale"
                            type="number"
                            value={localField.scale || ''}
                            onChange={(e) => handleFieldUpdate({ scale: parseInt(e.target.value) || undefined })}
                            placeholder="2"
                          />
                        </div>
                      </div>
                    )}

                    {localField.type === 'enum' && (
                      <div>
                        <Label>Enum Values</Label>
                        <div className="space-y-2">
                          {(localField.enumValues || []).map((value, index) => (
                            <div key={index} className="flex items-center space-x-2">
                              <Input
                                value={value}
                                onChange={(e) => {
                                  const newValues = [...(localField.enumValues || [])]
                                  newValues[index] = e.target.value
                                  handleFieldUpdate({ enumValues: newValues })
                                }}
                                placeholder="enum_value"
                              />
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => {
                                  const newValues = (localField.enumValues || []).filter((_, i) => i !== index)
                                  handleFieldUpdate({ enumValues: newValues })
                                }}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              const newValues = [...(localField.enumValues || []), '']
                              handleFieldUpdate({ enumValues: newValues })
                            }}
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            Add Value
                          </Button>
                        </div>
                      </div>
                    )}

                    <div>
                      <Label htmlFor="field-default">Default Value</Label>
                      <Input
                        id="field-default"
                        value={localField.defaultValue || ''}
                        onChange={(e) => handleFieldUpdate({ defaultValue: e.target.value })}
                        placeholder="Default value..."
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="validation" className="space-y-6 mt-0">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm">Validation Rules</CardTitle>
                      <Button size="sm" variant="outline" onClick={handleValidationAdd}>
                        <Plus className="h-3 w-3 mr-1" />
                        Add Rule
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {localField.validation.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <AlertCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No validation rules</p>
                        <p className="text-xs">Add rules to validate field data</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {localField.validation.map((rule, index) => (
                          <div key={index} className="p-3 border rounded space-y-2">
                            <div className="flex items-center justify-between">
                              <Select
                                value={rule.type}
                                onValueChange={(value) => handleValidationUpdate(index, { type: value as any })}
                              >
                                <SelectTrigger className="w-40">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {validationTypes.map((type) => (
                                    <SelectItem key={type.value} value={type.value}>
                                      {type.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleValidationRemove(index)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>

                            {(rule.type === 'min' || rule.type === 'max' || rule.type === 'length' || rule.type === 'pattern') && (
                              <Input
                                placeholder={rule.type === 'pattern' ? 'Regular expression' : 'Value'}
                                value={rule.value || ''}
                                onChange={(e) => handleValidationUpdate(index, { value: e.target.value })}
                              />
                            )}

                            <Input
                              placeholder="Error message"
                              value={rule.message}
                              onChange={(e) => handleValidationUpdate(index, { message: e.target.value })}
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="metadata" className="space-y-6 mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Field Metadata</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="field-category">Category</Label>
                      <Select
                        value={localField.metadata.category}
                        onValueChange={(value) => handleFieldUpdate({ 
                          metadata: { ...localField.metadata, category: value }
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="system">System</SelectItem>
                          <SelectItem value="user">User</SelectItem>
                          <SelectItem value="business">Business</SelectItem>
                          <SelectItem value="audit">Audit</SelectItem>
                          <SelectItem value="metadata">Metadata</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Tags</Label>
                      <Input
                        value={(localField.metadata.tags || []).join(', ')}
                        onChange={(e) => handleFieldUpdate({ 
                          metadata: { 
                            ...localField.metadata, 
                            tags: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
                          }
                        })}
                        placeholder="tag1, tag2, tag3"
                      />
                    </div>

                    <div>
                      <Label htmlFor="field-documentation">Documentation</Label>
                      <Textarea
                        id="field-documentation"
                        value={localField.metadata.documentation || ''}
                        onChange={(e) => handleFieldUpdate({ 
                          metadata: { ...localField.metadata, documentation: e.target.value }
                        })}
                        placeholder="Detailed field documentation..."
                        rows={4}
                      />
                    </div>

                    <Separator />

                    <div className="space-y-3">
                      <h4 className="font-medium">Display Options</h4>
                      
                      <div className="grid grid-cols-3 gap-4">
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={localField.metadata.searchable}
                            onCheckedChange={(checked) => handleFieldUpdate({ 
                              metadata: { ...localField.metadata, searchable: checked }
                            })}
                          />
                          <Label>Searchable</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={localField.metadata.sortable}
                            onCheckedChange={(checked) => handleFieldUpdate({ 
                              metadata: { ...localField.metadata, sortable: checked }
                            })}
                          />
                          <Label>Sortable</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={localField.metadata.filterable}
                            onCheckedChange={(checked) => handleFieldUpdate({ 
                              metadata: { ...localField.metadata, filterable: checked }
                            })}
                          />
                          <Label>Filterable</Label>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </div>
          </ScrollArea>
        </Tabs>
      </div>
    </div>
  )
}
