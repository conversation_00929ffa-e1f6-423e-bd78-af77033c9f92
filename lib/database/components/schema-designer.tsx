'use client'

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Plus, 
  Save, 
  Download, 
  Eye, 
  Settings, 
  Database, 
  Table, 
  Link2,
  Trash2,
  Edit,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Play,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'
import { DatabaseSchema, DatabaseTable, DatabaseField, DatabaseRelationship } from '../../posts/types'
import { TableDesigner } from './table-designer'
import { RelationshipDesigner } from './relationship-designer'
import { SchemaValidator } from './schema-validator'
import { CodeGenerator } from './code-generator'
import { generateId } from '../../page-builder/utils'
import { toast } from 'sonner'

interface SchemaDesignerProps {
  initialSchema?: DatabaseSchema
  onSave?: (schema: DatabaseSchema) => void
  onExport?: (schema: DatabaseSchema, format: string) => void
}

export function SchemaDesigner({ initialSchema, onSave, onExport }: SchemaDesignerProps) {
  const [schema, setSchema] = useState<DatabaseSchema>(
    initialSchema || {
      id: generateId(),
      name: 'New Schema',
      version: '1.0.0',
      description: '',
      tables: [],
      views: [],
      functions: [],
      triggers: [],
      migrations: [],
      metadata: {
        version: '1.0.0',
        compatibility: [],
        features: [],
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  )

  const [selectedTable, setSelectedTable] = useState<DatabaseTable | null>(null)
  const [selectedRelationship, setSelectedRelationship] = useState<DatabaseRelationship | null>(null)
  const [activeTab, setActiveTab] = useState<'design' | 'validate' | 'generate' | 'settings'>('design')
  const [isTableDesignerOpen, setIsTableDesignerOpen] = useState(false)
  const [isRelationshipDesignerOpen, setIsRelationshipDesignerOpen] = useState(false)
  const [zoom, setZoom] = useState(1)
  const [pan, setPan] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })

  const canvasRef = useRef<HTMLDivElement>(null)

  const handleAddTable = useCallback(() => {
    const newTable: DatabaseTable = {
      id: generateId(),
      name: `table_${schema.tables.length + 1}`,
      displayName: `Table ${schema.tables.length + 1}`,
      description: '',
      schema: 'public',
      isSystem: false,
      isActive: true,
      position: { 
        x: 100 + (schema.tables.length * 50), 
        y: 100 + (schema.tables.length * 50) 
      },
      fields: [
        {
          id: generateId(),
          name: 'id',
          displayName: 'ID',
          type: 'uuid',
          isRequired: true,
          isPrimaryKey: true,
          isUnique: true,
          isIndexed: true,
          validation: [],
          position: 0,
          metadata: {
            category: 'system',
            tags: ['primary-key'],
          }
        }
      ],
      relationships: [],
      indexes: [],
      constraints: [],
      metadata: {
        category: 'user',
        tags: [],
        timestamps: true,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    setSchema(prev => ({
      ...prev,
      tables: [...prev.tables, newTable],
      updatedAt: new Date(),
    }))

    setSelectedTable(newTable)
    setIsTableDesignerOpen(true)
  }, [schema.tables])

  const handleUpdateTable = useCallback((tableId: string, updates: Partial<DatabaseTable>) => {
    setSchema(prev => ({
      ...prev,
      tables: prev.tables.map(table => 
        table.id === tableId ? { ...table, ...updates, updatedAt: new Date() } : table
      ),
      updatedAt: new Date(),
    }))

    if (selectedTable?.id === tableId) {
      setSelectedTable(prev => prev ? { ...prev, ...updates } : null)
    }
  }, [selectedTable])

  const handleDeleteTable = useCallback((tableId: string) => {
    if (!confirm('Are you sure you want to delete this table? This will also remove all related relationships.')) {
      return
    }

    setSchema(prev => ({
      ...prev,
      tables: prev.tables
        .filter(table => table.id !== tableId)
        .map(table => ({
          ...table,
          relationships: table.relationships.filter(rel => 
            rel.fromTable !== tableId && rel.toTable !== tableId
          )
        })),
      updatedAt: new Date(),
    }))

    if (selectedTable?.id === tableId) {
      setSelectedTable(null)
      setIsTableDesignerOpen(false)
    }
  }, [selectedTable])

  const handleTablePositionChange = useCallback((tableId: string, position: { x: number; y: number }) => {
    handleUpdateTable(tableId, { position })
  }, [handleUpdateTable])

  const handleAddRelationship = useCallback((fromTableId: string, toTableId: string) => {
    const fromTable = schema.tables.find(t => t.id === fromTableId)
    const toTable = schema.tables.find(t => t.id === toTableId)

    if (!fromTable || !toTable) return

    const newRelationship: DatabaseRelationship = {
      id: generateId(),
      name: `${fromTable.name}_${toTable.name}`,
      type: 'many-to-one',
      fromTable: fromTableId,
      fromField: fromTable.fields[0]?.id || '',
      toTable: toTableId,
      toField: toTable.fields[0]?.id || '',
      onDelete: 'cascade',
      onUpdate: 'cascade',
      isRequired: false,
      metadata: {}
    }

    setSchema(prev => ({
      ...prev,
      tables: prev.tables.map(table => 
        table.id === fromTableId 
          ? { ...table, relationships: [...table.relationships, newRelationship] }
          : table
      ),
      updatedAt: new Date(),
    }))

    setSelectedRelationship(newRelationship)
    setIsRelationshipDesignerOpen(true)
  }, [schema.tables])

  const handleSave = useCallback(() => {
    if (onSave) {
      onSave(schema)
      toast.success('Schema saved successfully')
    }
  }, [schema, onSave])

  const handleExport = useCallback((format: string) => {
    if (onExport) {
      onExport(schema, format)
      toast.success(`Schema exported as ${format.toUpperCase()}`)
    }
  }, [schema, onExport])

  const handleZoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev * 1.2, 3))
  }, [])

  const handleZoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev / 1.2, 0.3))
  }, [])

  const handleResetView = useCallback(() => {
    setZoom(1)
    setPan({ x: 0, y: 0 })
  }, [])

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      setIsDragging(true)
      setDragStart({ x: e.clientX - pan.x, y: e.clientY - pan.y })
    }
  }, [pan])

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging) {
      setPan({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      })
    }
  }, [isDragging, dragStart])

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  useEffect(() => {
    const handleGlobalMouseUp = () => setIsDragging(false)
    document.addEventListener('mouseup', handleGlobalMouseUp)
    return () => document.removeEventListener('mouseup', handleGlobalMouseUp)
  }, [])

  if (isTableDesignerOpen && selectedTable) {
    return (
      <TableDesigner
        table={selectedTable}
        schema={schema}
        onSave={(updates) => {
          handleUpdateTable(selectedTable.id, updates)
          setIsTableDesignerOpen(false)
        }}
        onCancel={() => setIsTableDesignerOpen(false)}
      />
    )
  }

  if (isRelationshipDesignerOpen && selectedRelationship) {
    return (
      <RelationshipDesigner
        relationship={selectedRelationship}
        schema={schema}
        onSave={(updates) => {
          // Update relationship logic here
          setIsRelationshipDesignerOpen(false)
        }}
        onCancel={() => setIsRelationshipDesignerOpen(false)}
      />
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b bg-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Database Schema Designer</h1>
            <p className="text-gray-600">
              Visual database design tool - {schema.name} v{schema.version}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={handleResetView}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset View
            </Button>
            <Button variant="outline" onClick={() => handleExport('prisma')}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button onClick={handleSave}>
              <Save className="h-4 w-4 mr-2" />
              Save Schema
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Tools */}
        <div className="w-80 border-r bg-gray-50 flex flex-col">
          <div className="p-4 border-b">
            <h3 className="font-semibold mb-3">Schema Tools</h3>
            <div className="space-y-2">
              <Button 
                onClick={handleAddTable} 
                className="w-full justify-start"
                variant="outline"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Table
              </Button>
              <Button 
                onClick={() => setActiveTab('validate')} 
                className="w-full justify-start"
                variant="outline"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Validate Schema
              </Button>
              <Button 
                onClick={() => setActiveTab('generate')} 
                className="w-full justify-start"
                variant="outline"
              >
                <Play className="h-4 w-4 mr-2" />
                Generate Code
              </Button>
            </div>
          </div>

          <ScrollArea className="flex-1">
            <div className="p-4">
              <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="design">Design</TabsTrigger>
                  <TabsTrigger value="settings">Settings</TabsTrigger>
                </TabsList>

                <TabsContent value="design" className="space-y-4 mt-4">
                  <div>
                    <h4 className="font-medium mb-2">Tables ({schema.tables.length})</h4>
                    <div className="space-y-2">
                      {schema.tables.map((table) => (
                        <Card 
                          key={table.id} 
                          className={`cursor-pointer transition-colors ${
                            selectedTable?.id === table.id ? 'ring-2 ring-blue-500' : ''
                          }`}
                          onClick={() => {
                            setSelectedTable(table)
                            setIsTableDesignerOpen(true)
                          }}
                        >
                          <CardContent className="p-3">
                            <div className="flex items-center justify-between">
                              <div>
                                <h5 className="font-medium">{table.displayName}</h5>
                                <p className="text-sm text-gray-500">{table.fields.length} fields</p>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    setSelectedTable(table)
                                    setIsTableDesignerOpen(true)
                                  }}
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleDeleteTable(table.id)
                                  }}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="settings" className="space-y-4 mt-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Schema Name</label>
                    <Input
                      value={schema.name}
                      onChange={(e) => setSchema(prev => ({ ...prev, name: e.target.value }))}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Version</label>
                    <Input
                      value={schema.version}
                      onChange={(e) => setSchema(prev => ({ ...prev, version: e.target.value }))}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Description</label>
                    <Input
                      value={schema.description || ''}
                      onChange={(e) => setSchema(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Schema description..."
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </ScrollArea>
        </div>

        {/* Center - Canvas */}
        <div className="flex-1 relative overflow-hidden bg-gray-100">
          {/* Canvas Controls */}
          <div className="absolute top-4 right-4 z-10 flex items-center space-x-2">
            <Button size="sm" variant="outline" onClick={handleZoomOut}>
              <ZoomOut className="h-4 w-4" />
            </Button>
            <span className="text-sm font-medium px-2">
              {Math.round(zoom * 100)}%
            </span>
            <Button size="sm" variant="outline" onClick={handleZoomIn}>
              <ZoomIn className="h-4 w-4" />
            </Button>
          </div>

          {/* Canvas */}
          <div
            ref={canvasRef}
            className="w-full h-full cursor-grab active:cursor-grabbing"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            style={{
              transform: `scale(${zoom}) translate(${pan.x}px, ${pan.y}px)`,
              transformOrigin: '0 0'
            }}
          >
            {/* Grid Background */}
            <div 
              className="absolute inset-0 opacity-20"
              style={{
                backgroundImage: `
                  linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                  linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
                `,
                backgroundSize: '20px 20px'
              }}
            />

            {/* Tables */}
            {schema.tables.map((table) => (
              <TableNode
                key={table.id}
                table={table}
                isSelected={selectedTable?.id === table.id}
                onSelect={() => setSelectedTable(table)}
                onEdit={() => {
                  setSelectedTable(table)
                  setIsTableDesignerOpen(true)
                }}
                onDelete={() => handleDeleteTable(table.id)}
                onPositionChange={(position) => handleTablePositionChange(table.id, position)}
              />
            ))}

            {/* Relationships */}
            {schema.tables.map(table =>
              table.relationships.map(relationship => {
                const toTable = schema.tables.find(t => t.id === relationship.toTable)
                if (!toTable) return null

                return (
                  <RelationshipLine
                    key={relationship.id}
                    relationship={relationship}
                    fromTable={table}
                    toTable={toTable}
                    onSelect={() => {
                      setSelectedRelationship(relationship)
                      setIsRelationshipDesignerOpen(true)
                    }}
                  />
                )
              })
            )}

            {/* Empty State */}
            {schema.tables.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <Database className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No tables yet</h3>
                  <p className="text-gray-500 mb-4">Create your first table to get started</p>
                  <Button onClick={handleAddTable}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Table
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Sidebar - Properties */}
        {(activeTab === 'validate' || activeTab === 'generate') && (
          <div className="w-96 border-l bg-white">
            {activeTab === 'validate' && (
              <SchemaValidator schema={schema} />
            )}
            {activeTab === 'generate' && (
              <CodeGenerator schema={schema} onExport={handleExport} />
            )}
          </div>
        )}
      </div>
    </div>
  )
}

// Table Node Component
interface TableNodeProps {
  table: DatabaseTable
  isSelected: boolean
  onSelect: () => void
  onEdit: () => void
  onDelete: () => void
  onPositionChange: (position: { x: number; y: number }) => void
}

function TableNode({ table, isSelected, onSelect, onEdit, onDelete, onPositionChange }: TableNodeProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })

  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsDragging(true)
    setDragStart({
      x: e.clientX - table.position.x,
      y: e.clientY - table.position.y
    })
    onSelect()
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      onPositionChange({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  return (
    <div
      className={`absolute bg-white border-2 rounded-lg shadow-lg min-w-48 cursor-move ${
        isSelected ? 'border-blue-500' : 'border-gray-200'
      }`}
      style={{
        left: table.position.x,
        top: table.position.y,
      }}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
    >
      {/* Table Header */}
      <div className="bg-gray-50 px-3 py-2 border-b rounded-t-lg">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium">{table.displayName}</h4>
            <p className="text-xs text-gray-500">{table.name}</p>
          </div>
          <div className="flex items-center space-x-1">
            <Button size="sm" variant="ghost" onClick={onEdit}>
              <Edit className="h-3 w-3" />
            </Button>
            {!table.isSystem && (
              <Button size="sm" variant="ghost" onClick={onDelete} className="text-red-600">
                <Trash2 className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Table Fields */}
      <div className="p-2">
        {table.fields.slice(0, 5).map((field) => (
          <div key={field.id} className="flex items-center justify-between py-1 text-sm">
            <div className="flex items-center space-x-2">
              {field.isPrimaryKey && <Badge variant="secondary" className="text-xs">PK</Badge>}
              <span className={field.isPrimaryKey ? 'font-medium' : ''}>{field.name}</span>
            </div>
            <span className="text-gray-500 text-xs">{field.type}</span>
          </div>
        ))}
        {table.fields.length > 5 && (
          <div className="text-xs text-gray-500 text-center py-1">
            +{table.fields.length - 5} more fields
          </div>
        )}
      </div>
    </div>
  )
}

// Relationship Line Component
interface RelationshipLineProps {
  relationship: DatabaseRelationship
  fromTable: DatabaseTable
  toTable: DatabaseTable
  onSelect: () => void
}

function RelationshipLine({ relationship, fromTable, toTable, onSelect }: RelationshipLineProps) {
  const fromX = fromTable.position.x + 192 // table width
  const fromY = fromTable.position.y + 40  // approximate center
  const toX = toTable.position.x
  const toY = toTable.position.y + 40

  return (
    <svg
      className="absolute inset-0 pointer-events-none"
      style={{ zIndex: 1 }}
    >
      <line
        x1={fromX}
        y1={fromY}
        x2={toX}
        y2={toY}
        stroke="#6b7280"
        strokeWidth="2"
        className="cursor-pointer pointer-events-auto"
        onClick={onSelect}
      />
      {/* Relationship type indicator */}
      <circle
        cx={(fromX + toX) / 2}
        cy={(fromY + toY) / 2}
        r="4"
        fill="#3b82f6"
        className="cursor-pointer pointer-events-auto"
        onClick={onSelect}
      />
    </svg>
  )
}
