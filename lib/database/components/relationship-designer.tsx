'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Link, 
  Unlink,
  ArrowRight,
  ArrowLeftRight,
  Plus,
  Trash2,
  Edit,
  Database,
  Table,
  Key,
  GitBranch
} from 'lucide-react'

interface DatabaseTable {
  id: string
  name: string
  fields: DatabaseField[]
  position: { x: number; y: number }
}

interface DatabaseField {
  id: string
  name: string
  type: string
  isPrimaryKey: boolean
  isForeignKey: boolean
  isRequired: boolean
  isUnique: boolean
}

interface Relationship {
  id: string
  name: string
  fromTable: string
  fromField: string
  toTable: string
  toField: string
  type: 'one-to-one' | 'one-to-many' | 'many-to-many'
  onDelete: 'cascade' | 'set-null' | 'restrict'
  onUpdate: 'cascade' | 'set-null' | 'restrict'
}

interface RelationshipDesignerProps {
  tables: DatabaseTable[]
  relationships: Relationship[]
  onCreateRelationship: (relationship: Omit<Relationship, 'id'>) => void
  onUpdateRelationship: (id: string, relationship: Partial<Relationship>) => void
  onDeleteRelationship: (id: string) => void
}

export function RelationshipDesigner({
  tables,
  relationships,
  onCreateRelationship,
  onUpdateRelationship,
  onDeleteRelationship
}: RelationshipDesignerProps) {
  const [selectedRelationship, setSelectedRelationship] = useState<string | null>(null)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newRelationship, setNewRelationship] = useState<Partial<Relationship>>({
    type: 'one-to-many',
    onDelete: 'cascade',
    onUpdate: 'cascade'
  })

  const getRelationshipIcon = (type: string) => {
    switch (type) {
      case 'one-to-one':
        return <ArrowRight className="h-4 w-4" />
      case 'one-to-many':
        return <GitBranch className="h-4 w-4" />
      case 'many-to-many':
        return <ArrowLeftRight className="h-4 w-4" />
      default:
        return <Link className="h-4 w-4" />
    }
  }

  const getRelationshipColor = (type: string) => {
    switch (type) {
      case 'one-to-one':
        return 'bg-blue-100 text-blue-800'
      case 'one-to-many':
        return 'bg-green-100 text-green-800'
      case 'many-to-many':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleCreateRelationship = () => {
    if (newRelationship.fromTable && newRelationship.fromField && 
        newRelationship.toTable && newRelationship.toField && 
        newRelationship.type) {
      onCreateRelationship({
        name: `${newRelationship.fromTable}_${newRelationship.toTable}`,
        fromTable: newRelationship.fromTable,
        fromField: newRelationship.fromField,
        toTable: newRelationship.toTable,
        toField: newRelationship.toField,
        type: newRelationship.type,
        onDelete: newRelationship.onDelete || 'cascade',
        onUpdate: newRelationship.onUpdate || 'cascade'
      })
      setNewRelationship({
        type: 'one-to-many',
        onDelete: 'cascade',
        onUpdate: 'cascade'
      })
      setShowCreateForm(false)
    }
  }

  const getTableFields = (tableId: string) => {
    const table = tables.find(t => t.id === tableId)
    return table?.fields || []
  }

  const getFieldsForRelationship = (tableId: string, isSource: boolean) => {
    const fields = getTableFields(tableId)
    return isSource 
      ? fields.filter(f => f.isPrimaryKey || f.isUnique)
      : fields
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Relationship Designer</h3>
          <p className="text-sm text-muted-foreground">
            Define relationships between database tables
          </p>
        </div>
        <Button onClick={() => setShowCreateForm(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Relationship
        </Button>
      </div>

      <Tabs defaultValue="list" className="w-full">
        <TabsList>
          <TabsTrigger value="list">Relationships List</TabsTrigger>
          <TabsTrigger value="diagram">Visual Diagram</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          {/* Relationships List */}
          <div className="grid gap-4">
            {relationships.map((relationship) => {
              const fromTable = tables.find(t => t.id === relationship.fromTable)
              const toTable = tables.find(t => t.id === relationship.toTable)
              
              return (
                <Card 
                  key={relationship.id}
                  className={`cursor-pointer transition-colors ${
                    selectedRelationship === relationship.id 
                      ? 'ring-2 ring-primary' 
                      : 'hover:bg-muted/50'
                  }`}
                  onClick={() => setSelectedRelationship(relationship.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <Badge className={getRelationshipColor(relationship.type)}>
                          {getRelationshipIcon(relationship.type)}
                          <span className="ml-1">{relationship.type}</span>
                        </Badge>
                        
                        <div className="flex items-center gap-2 text-sm">
                          <div className="flex items-center gap-1">
                            <Table className="h-3 w-3" />
                            <span className="font-medium">{fromTable?.name}</span>
                            <span className="text-muted-foreground">({relationship.fromField})</span>
                          </div>
                          
                          <ArrowRight className="h-3 w-3 text-muted-foreground" />
                          
                          <div className="flex items-center gap-1">
                            <Table className="h-3 w-3" />
                            <span className="font-medium">{toTable?.name}</span>
                            <span className="text-muted-foreground">({relationship.toField})</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          ON DELETE: {relationship.onDelete}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          ON UPDATE: {relationship.onUpdate}
                        </Badge>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            onDeleteRelationship(relationship.id)
                          }}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
            
            {relationships.length === 0 && (
              <Card>
                <CardContent className="p-8 text-center">
                  <Database className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-medium mb-2">No Relationships</h3>
                  <p className="text-muted-foreground mb-4">
                    Create relationships between your tables to define how data connects
                  </p>
                  <Button onClick={() => setShowCreateForm(true)}>
                    Create First Relationship
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="diagram">
          <Card>
            <CardContent className="p-8 text-center">
              <GitBranch className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">Visual Diagram</h3>
              <p className="text-muted-foreground">
                Interactive relationship diagram coming soon
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create Relationship Form */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-2xl">
            <CardHeader>
              <CardTitle>Create Relationship</CardTitle>
              <CardDescription>
                Define a relationship between two tables
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                {/* From Table */}
                <div className="space-y-4">
                  <h4 className="font-medium">From Table</h4>
                  
                  <div className="space-y-2">
                    <Label>Table</Label>
                    <Select
                      value={newRelationship.fromTable || ''}
                      onValueChange={(value) => setNewRelationship(prev => ({ 
                        ...prev, 
                        fromTable: value,
                        fromField: '' 
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select table" />
                      </SelectTrigger>
                      <SelectContent>
                        {tables.map(table => (
                          <SelectItem key={table.id} value={table.id}>
                            {table.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Field</Label>
                    <Select
                      value={newRelationship.fromField || ''}
                      onValueChange={(value) => setNewRelationship(prev => ({ 
                        ...prev, 
                        fromField: value 
                      }))}
                      disabled={!newRelationship.fromTable}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select field" />
                      </SelectTrigger>
                      <SelectContent>
                        {getFieldsForRelationship(newRelationship.fromTable || '', true).map(field => (
                          <SelectItem key={field.id} value={field.name}>
                            <div className="flex items-center gap-2">
                              {field.isPrimaryKey && <Key className="h-3 w-3 text-yellow-600" />}
                              {field.name}
                              <span className="text-xs text-muted-foreground">({field.type})</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* To Table */}
                <div className="space-y-4">
                  <h4 className="font-medium">To Table</h4>
                  
                  <div className="space-y-2">
                    <Label>Table</Label>
                    <Select
                      value={newRelationship.toTable || ''}
                      onValueChange={(value) => setNewRelationship(prev => ({ 
                        ...prev, 
                        toTable: value,
                        toField: '' 
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select table" />
                      </SelectTrigger>
                      <SelectContent>
                        {tables.filter(t => t.id !== newRelationship.fromTable).map(table => (
                          <SelectItem key={table.id} value={table.id}>
                            {table.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Field</Label>
                    <Select
                      value={newRelationship.toField || ''}
                      onValueChange={(value) => setNewRelationship(prev => ({ 
                        ...prev, 
                        toField: value 
                      }))}
                      disabled={!newRelationship.toTable}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select field" />
                      </SelectTrigger>
                      <SelectContent>
                        {getFieldsForRelationship(newRelationship.toTable || '', false).map(field => (
                          <SelectItem key={field.id} value={field.name}>
                            <div className="flex items-center gap-2">
                              {field.isPrimaryKey && <Key className="h-3 w-3 text-yellow-600" />}
                              {field.name}
                              <span className="text-xs text-muted-foreground">({field.type})</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Relationship Configuration */}
              <div className="space-y-4">
                <h4 className="font-medium">Relationship Configuration</h4>
                
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Relationship Type</Label>
                    <Select
                      value={newRelationship.type || ''}
                      onValueChange={(value) => setNewRelationship(prev => ({ 
                        ...prev, 
                        type: value as any 
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="one-to-one">One to One</SelectItem>
                        <SelectItem value="one-to-many">One to Many</SelectItem>
                        <SelectItem value="many-to-many">Many to Many</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>On Delete</Label>
                    <Select
                      value={newRelationship.onDelete || ''}
                      onValueChange={(value) => setNewRelationship(prev => ({ 
                        ...prev, 
                        onDelete: value as any 
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cascade">Cascade</SelectItem>
                        <SelectItem value="set-null">Set Null</SelectItem>
                        <SelectItem value="restrict">Restrict</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>On Update</Label>
                    <Select
                      value={newRelationship.onUpdate || ''}
                      onValueChange={(value) => setNewRelationship(prev => ({ 
                        ...prev, 
                        onUpdate: value as any 
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cascade">Cascade</SelectItem>
                        <SelectItem value="set-null">Set Null</SelectItem>
                        <SelectItem value="restrict">Restrict</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <div className="flex gap-2 pt-4">
                <Button onClick={handleCreateRelationship} className="flex-1">
                  Create Relationship
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setShowCreateForm(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
