// Payment Gateway Types and Interfaces

export interface PaymentAmount {
  amount: number
  currency: string
  formatted?: string
}

export interface PaymentCustomer {
  id?: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  address?: PaymentAddress
}

export interface PaymentAddress {
  line1: string
  line2?: string
  city: string
  state: string
  postalCode: string
  country: string
}

export interface PaymentItem {
  id: string
  name: string
  description?: string
  quantity: number
  unitPrice: number
  totalPrice: number
  sku?: string
  category?: string
}

export interface PaymentMetadata {
  orderId: string
  customerId?: string
  source: string
  [key: string]: any
}

export interface PaymentRequest {
  amount: PaymentAmount
  customer: PaymentCustomer
  items: PaymentItem[]
  metadata: PaymentMetadata
  returnUrl: string
  cancelUrl: string
  notifyUrl: string
  reference: string
  description: string
}

export interface PaymentResponse {
  success: boolean
  transactionId?: string
  paymentUrl?: string
  qrCode?: string
  reference?: string
  status: PaymentStatus
  message?: string
  error?: PaymentError
  gatewayResponse?: any
}

export interface PaymentError {
  code: string
  message: string
  details?: any
}

export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially_refunded',
  EXPIRED = 'expired'
}

export enum PaymentMethod {
  CARD = 'card',
  EFT = 'eft',
  QR_CODE = 'qr_code',
  INSTANT_EFT = 'instant_eft',
  BANK_TRANSFER = 'bank_transfer',
  MOBILE_MONEY = 'mobile_money',
  CRYPTO = 'crypto'
}

export enum PaymentGateway {
  PAYFAST = 'payfast',
  OZOW = 'ozow',
  SNAPSCAN = 'snapscan',
  YOCO = 'yoco',
  PAYU = 'payu'
}

// Webhook Types
export interface WebhookPayload {
  gateway: PaymentGateway
  event: WebhookEvent
  data: any
  signature?: string
  timestamp: string
}

export enum WebhookEvent {
  PAYMENT_COMPLETED = 'payment.completed',
  PAYMENT_FAILED = 'payment.failed',
  PAYMENT_CANCELLED = 'payment.cancelled',
  PAYMENT_REFUNDED = 'payment.refunded',
  PAYMENT_PENDING = 'payment.pending'
}

// Gateway-specific configurations
export interface PayFastConfig {
  merchantId: string
  merchantKey: string
  passphrase: string
  sandbox: boolean
}

export interface OzowConfig {
  apiKey: string
  privateKey: string
  siteCode: string
  sandbox: boolean
}

export interface SnapScanConfig {
  apiKey: string
  merchantId: string
  sandbox: boolean
}

export interface YocoConfig {
  secretKey: string
  publicKey: string
  sandbox: boolean
}

export interface PayUConfig {
  apiKey: string
  safeKey: string
  merchantId: string
  sandbox: boolean
}

export interface PaymentGatewayConfig {
  payfast?: PayFastConfig
  ozow?: OzowConfig
  snapscan?: SnapScanConfig
  yoco?: YocoConfig
  payu?: PayUConfig
}

// Payment Gateway Interface
export interface IPaymentGateway {
  name: PaymentGateway
  displayName: string
  supportedMethods: PaymentMethod[]
  supportedCurrencies: string[]
  
  // Core payment methods
  createPayment(request: PaymentRequest): Promise<PaymentResponse>
  getPaymentStatus(transactionId: string): Promise<PaymentStatus>
  refundPayment(transactionId: string, amount?: number): Promise<PaymentResponse>
  
  // Webhook handling
  verifyWebhook(payload: any, signature: string): boolean
  processWebhook(payload: WebhookPayload): Promise<void>
  
  // Validation
  validateConfig(): boolean
  validatePaymentRequest(request: PaymentRequest): PaymentValidationResult
}

export interface PaymentValidationResult {
  isValid: boolean
  errors: string[]
}

// Transaction logging
export interface PaymentTransaction {
  id: string
  orderId: string
  customerId?: string
  gateway: PaymentGateway
  method: PaymentMethod
  amount: PaymentAmount
  status: PaymentStatus
  reference: string
  transactionId?: string
  gatewayResponse?: any
  createdAt: Date
  updatedAt: Date
  metadata?: any
}

// Refund types
export interface RefundRequest {
  transactionId: string
  amount?: number
  reason: string
  metadata?: any
}

export interface RefundResponse {
  success: boolean
  refundId?: string
  amount?: number
  status: PaymentStatus
  message?: string
  error?: PaymentError
}

// Payment analytics
export interface PaymentAnalytics {
  totalTransactions: number
  totalAmount: number
  successRate: number
  averageTransactionValue: number
  topPaymentMethods: Array<{
    method: PaymentMethod
    count: number
    percentage: number
  }>
  gatewayPerformance: Array<{
    gateway: PaymentGateway
    successRate: number
    averageProcessingTime: number
  }>
}

// Security and compliance
export interface SecurityConfig {
  encryptionKey: string
  hashAlgorithm: string
  tokenExpiry: number
  maxRetries: number
  rateLimitWindow: number
  rateLimitMax: number
}

export interface ComplianceData {
  pciCompliant: boolean
  popiaCompliant: boolean
  vatNumber?: string
  businessRegistration?: string
  auditTrail: boolean
}

// Error codes
export enum PaymentErrorCode {
  INVALID_AMOUNT = 'INVALID_AMOUNT',
  INVALID_CURRENCY = 'INVALID_CURRENCY',
  INVALID_CUSTOMER = 'INVALID_CUSTOMER',
  GATEWAY_ERROR = 'GATEWAY_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  INSUFFICIENT_FUNDS = 'INSUFFICIENT_FUNDS',
  CARD_DECLINED = 'CARD_DECLINED',
  EXPIRED_CARD = 'EXPIRED_CARD',
  INVALID_CARD = 'INVALID_CARD',
  FRAUD_DETECTED = 'FRAUD_DETECTED',
  RATE_LIMITED = 'RATE_LIMITED',
  MAINTENANCE = 'MAINTENANCE',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// Rate limiting
export interface RateLimitConfig {
  windowMs: number
  max: number
  message: string
  standardHeaders: boolean
  legacyHeaders: boolean
}

// Logging
export interface PaymentLog {
  id: string
  level: 'info' | 'warn' | 'error' | 'debug'
  message: string
  gateway?: PaymentGateway
  transactionId?: string
  orderId?: string
  customerId?: string
  metadata?: any
  timestamp: Date
}

// All types and enums are already exported above
