import winston from 'winston'
// Temporarily use simple console logging to avoid build issues
// import DailyRotateFile from 'winston-daily-rotate-file'
import path from 'path'
import type { PaymentLog, PaymentGateway } from './types'
import { LOGGING_CONFIG } from './config'
import { maskSensitiveData } from './utils'

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs')

// Custom format for payment logs
const paymentLogFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const logEntry = {
      timestamp,
      level,
      message,
      ...maskSensitiveData(meta),
    }
    return JSON.stringify(logEntry)
  })
)

// Create Winston logger instance
const logger = winston.createLogger({
  level: LOGGING_CONFIG.level,
  format: paymentLogFormat,
  defaultMeta: { service: 'payment-gateway' },
  transports: [
    // Error log file
    new winston.transports.File({
      filename: path.join(logsDir, 'payment-errors.log'),
      level: 'error',
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      tailable: true,
    }),
    
    // Combined log file
    new winston.transports.File({
      filename: path.join(logsDir, 'payment-combined.log'),
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 10,
      tailable: true,
    }),
    
    // Audit trail file (temporarily using simple file transport)
    new winston.transports.File({
      filename: path.join(logsDir, 'payment-audit.log'),
      maxsize: 20 * 1024 * 1024, // 20MB
      maxFiles: 5,
      tailable: true,
    }),
  ],
})

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
      winston.format.printf(({ timestamp, level, message, ...meta }) => {
        const metaStr = Object.keys(meta).length ? JSON.stringify(maskSensitiveData(meta), null, 2) : ''
        return `${timestamp} [${level}]: ${message} ${metaStr}`
      })
    )
  }))
}

// Payment-specific logging functions
export const paymentLogger = {
  /**
   * Log payment initiation
   */
  logPaymentInitiated(data: {
    gateway: PaymentGateway
    reference: string
    amount: number
    currency: string
    customerId?: string
    orderId: string
  }) {
    logger.info('Payment initiated', {
      event: 'payment_initiated',
      ...data,
    })
  },

  /**
   * Log payment completion
   */
  logPaymentCompleted(data: {
    gateway: PaymentGateway
    reference: string
    transactionId: string
    amount: number
    currency: string
    customerId?: string
    orderId: string
    processingTime?: number
  }) {
    logger.info('Payment completed', {
      event: 'payment_completed',
      ...data,
    })
  },

  /**
   * Log payment failure
   */
  logPaymentFailed(data: {
    gateway: PaymentGateway
    reference: string
    transactionId?: string
    amount: number
    currency: string
    customerId?: string
    orderId: string
    errorCode: string
    errorMessage: string
    processingTime?: number
  }) {
    logger.error('Payment failed', {
      event: 'payment_failed',
      ...data,
    })
  },

  /**
   * Log payment cancellation
   */
  logPaymentCancelled(data: {
    gateway: PaymentGateway
    reference: string
    transactionId?: string
    amount: number
    currency: string
    customerId?: string
    orderId: string
  }) {
    logger.warn('Payment cancelled', {
      event: 'payment_cancelled',
      ...data,
    })
  },

  /**
   * Log refund initiation
   */
  logRefundInitiated(data: {
    gateway: PaymentGateway
    originalTransactionId: string
    refundAmount: number
    currency: string
    reason: string
    customerId?: string
    orderId: string
  }) {
    logger.info('Refund initiated', {
      event: 'refund_initiated',
      ...data,
    })
  },

  /**
   * Log refund completion
   */
  logRefundCompleted(data: {
    gateway: PaymentGateway
    originalTransactionId: string
    refundTransactionId: string
    refundAmount: number
    currency: string
    customerId?: string
    orderId: string
    processingTime?: number
  }) {
    logger.info('Refund completed', {
      event: 'refund_completed',
      ...data,
    })
  },

  /**
   * Log webhook received
   */
  logWebhookReceived(data: {
    gateway: PaymentGateway
    event: string
    transactionId?: string
    reference?: string
    verified: boolean
    payload?: any
  }) {
    logger.info('Webhook received', {
      event: 'webhook_received',
      ...data,
      payload: maskSensitiveData(data.payload),
    })
  },

  /**
   * Log webhook processing error
   */
  logWebhookError(data: {
    gateway: PaymentGateway
    event: string
    error: string
    payload?: any
  }) {
    logger.error('Webhook processing error', {
      event: 'webhook_error',
      ...data,
      payload: maskSensitiveData(data.payload),
    })
  },

  /**
   * Log gateway API error
   */
  logGatewayError(data: {
    gateway: PaymentGateway
    operation: string
    error: string
    statusCode?: number
    response?: any
    request?: any
  }) {
    logger.error('Gateway API error', {
      event: 'gateway_error',
      ...data,
      response: maskSensitiveData(data.response),
      request: maskSensitiveData(data.request),
    })
  },

  /**
   * Log security event
   */
  logSecurityEvent(data: {
    event: string
    severity: 'low' | 'medium' | 'high' | 'critical'
    description: string
    ipAddress?: string
    userAgent?: string
    customerId?: string
    metadata?: any
  }) {
    logger.warn('Security event', {
      event: 'security_event',
      ...data,
      metadata: maskSensitiveData(data.metadata),
    })
  },

  /**
   * Log fraud detection
   */
  logFraudDetection(data: {
    gateway: PaymentGateway
    reference: string
    transactionId?: string
    amount: number
    currency: string
    customerId?: string
    orderId: string
    riskScore?: number
    riskFactors?: string[]
    action: 'blocked' | 'flagged' | 'allowed'
  }) {
    logger.warn('Fraud detection', {
      event: 'fraud_detection',
      ...data,
    })
  },

  /**
   * Log rate limiting
   */
  logRateLimit(data: {
    ipAddress: string
    endpoint: string
    limit: number
    windowMs: number
    attempts: number
  }) {
    logger.warn('Rate limit exceeded', {
      event: 'rate_limit_exceeded',
      ...data,
    })
  },

  /**
   * Log configuration error
   */
  logConfigError(data: {
    gateway: PaymentGateway
    error: string
    missingFields?: string[]
  }) {
    logger.error('Configuration error', {
      event: 'config_error',
      ...data,
    })
  },

  /**
   * Log performance metrics
   */
  logPerformanceMetrics(data: {
    gateway: PaymentGateway
    operation: string
    duration: number
    success: boolean
    statusCode?: number
  }) {
    logger.info('Performance metrics', {
      event: 'performance_metrics',
      ...data,
    })
  },
}

// Audit trail functions
export const auditLogger = {
  /**
   * Log user action
   */
  logUserAction(data: {
    userId?: string
    action: string
    resource: string
    resourceId?: string
    ipAddress?: string
    userAgent?: string
    metadata?: any
  }) {
    logger.info('User action', {
      event: 'user_action',
      ...data,
      metadata: maskSensitiveData(data.metadata),
    })
  },

  /**
   * Log admin action
   */
  logAdminAction(data: {
    adminId: string
    action: string
    resource: string
    resourceId?: string
    changes?: any
    ipAddress?: string
    userAgent?: string
  }) {
    logger.info('Admin action', {
      event: 'admin_action',
      ...data,
      changes: maskSensitiveData(data.changes),
    })
  },

  /**
   * Log data access
   */
  logDataAccess(data: {
    userId?: string
    resource: string
    resourceId?: string
    operation: 'read' | 'write' | 'delete'
    ipAddress?: string
    userAgent?: string
  }) {
    logger.info('Data access', {
      event: 'data_access',
      ...data,
    })
  },
}

// Export the main logger for general use
export { logger }

// Export default logger instance
export default logger
