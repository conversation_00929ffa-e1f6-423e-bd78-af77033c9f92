import axios from 'axios'
import type {
  IPaymentGateway,
  PaymentRequest,
  PaymentResponse,
  PaymentStatus,
  PaymentMethod,
  PaymentGateway,
  PaymentValidationResult,
  WebhookPayload,
  PaymentErrorCode,
} from '../types'
import {
  paymentConfig,
  getGatewayUrl,
  TIMEOUT_CONFIG,
} from '../config'
import {
  validatePaymentRequest,
  generatePaymentReference,
  generateOzowHash,
  formatAmount,
  maskSensitiveData,
  retryWithBackoff,
  getPaymentErrorCode,
} from '../utils'
import { logger } from '../logger'

export class OzowGateway implements IPaymentGateway {
  name = PaymentGateway.OZOW
  displayName = 'Ozow'
  supportedMethods = [PaymentMethod.INSTANT_EFT, PaymentMethod.EFT]
  supportedCurrencies = ['ZAR']

  private config = paymentConfig.ozow!
  private baseUrl = getGatewayUrl(PaymentGateway.OZOW)
  private apiUrl = getGatewayUrl(PaymentGateway.OZOW, '')

  constructor() {
    if (!this.validateConfig()) {
      throw new Error('Ozow configuration is invalid')
    }
  }

  /**
   * Validate Ozow configuration
   */
  validateConfig(): boolean {
    return !!(
      this.config.apiKey &&
      this.config.privateKey &&
      this.config.siteCode
    )
  }

  /**
   * Validate payment request
   */
  validatePaymentRequest(request: PaymentRequest): PaymentValidationResult {
    const baseValidation = validatePaymentRequest(request)
    
    if (!baseValidation.isValid) {
      return baseValidation
    }

    const errors: string[] = []

    // Ozow specific validations
    if (request.amount.currency !== 'ZAR') {
      errors.push('Ozow only supports ZAR currency')
    }

    if (request.amount.amount < 1) {
      errors.push('Ozow minimum amount is R1.00')
    }

    if (request.amount.amount > 50000) {
      errors.push('Ozow maximum amount is R50,000.00')
    }

    return {
      isValid: errors.length === 0,
      errors: [...baseValidation.errors, ...errors],
    }
  }

  /**
   * Create payment with Ozow
   */
  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      logger.info('Creating Ozow payment', {
        gateway: this.name,
        amount: request.amount.amount,
        currency: request.amount.currency,
        reference: request.reference,
      })

      // Validate request
      const validation = this.validatePaymentRequest(request)
      if (!validation.isValid) {
        return {
          success: false,
          status: PaymentStatus.FAILED,
          error: {
            code: PaymentErrorCode.INVALID_AMOUNT,
            message: validation.errors.join(', '),
          },
        }
      }

      // Prepare Ozow payment data
      const paymentData = this.preparePaymentData(request)
      
      // Generate hash
      paymentData.HashCheck = generateOzowHash(paymentData, this.config.privateKey)

      // Make API request to Ozow
      const response = await retryWithBackoff(async () => {
        return await axios.post(this.baseUrl, paymentData, {
          timeout: TIMEOUT_CONFIG.paymentRequest,
          headers: {
            'Content-Type': 'application/json',
            'ApiKey': this.config.apiKey,
          },
        })
      })

      if (response.data.url) {
        logger.info('Ozow payment created successfully', {
          gateway: this.name,
          reference: request.reference,
          transactionId: response.data.transactionId,
        })

        return {
          success: true,
          paymentUrl: response.data.url,
          transactionId: response.data.transactionId,
          reference: request.reference,
          status: PaymentStatus.PENDING,
          gatewayResponse: maskSensitiveData(response.data),
        }
      } else {
        throw new Error(response.data.errorMessage || 'Failed to create payment')
      }

    } catch (error) {
      logger.error('Ozow payment creation failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error',
        reference: request.reference,
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: getPaymentErrorCode(error),
          message: error instanceof Error ? error.message : 'Payment creation failed',
        },
      }
    }
  }

  /**
   * Get payment status from Ozow
   */
  async getPaymentStatus(transactionId: string): Promise<PaymentStatus> {
    try {
      logger.info('Checking Ozow payment status', {
        gateway: this.name,
        transactionId,
      })

      const statusData = {
        SiteCode: this.config.siteCode,
        TransactionId: transactionId,
      }

      statusData.HashCheck = generateOzowHash(statusData, this.config.privateKey)

      const response = await axios.post(
        `${this.apiUrl}/GetTransaction`,
        statusData,
        {
          timeout: TIMEOUT_CONFIG.statusCheck,
          headers: {
            'Content-Type': 'application/json',
            'ApiKey': this.config.apiKey,
          },
        }
      )

      return this.mapOzowStatus(response.data.status)

    } catch (error) {
      logger.error('Ozow status check failed', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error',
      })

      return PaymentStatus.FAILED
    }
  }

  /**
   * Refund payment via Ozow
   */
  async refundPayment(transactionId: string, amount?: number): Promise<PaymentResponse> {
    try {
      logger.info('Processing Ozow refund', {
        gateway: this.name,
        transactionId,
        amount,
      })

      // Ozow refunds are typically processed through their portal
      // This is a placeholder for when API becomes available
      
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.GATEWAY_ERROR,
          message: 'Ozow refunds must be processed through the merchant portal',
        },
      }

    } catch (error) {
      logger.error('Ozow refund failed', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error',
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: getPaymentErrorCode(error),
          message: error instanceof Error ? error.message : 'Refund failed',
        },
      }
    }
  }

  /**
   * Verify Ozow webhook signature
   */
  verifyWebhook(payload: any, signature: string): boolean {
    try {
      const calculatedHash = generateOzowHash(payload, this.config.privateKey)
      return calculatedHash === signature
    } catch (error) {
      logger.error('Ozow webhook verification failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      return false
    }
  }

  /**
   * Process Ozow webhook
   */
  async processWebhook(payload: WebhookPayload): Promise<void> {
    try {
      logger.info('Processing Ozow webhook', {
        gateway: this.name,
        event: payload.event,
        data: maskSensitiveData(payload.data),
      })

      const data = payload.data

      // Verify the webhook signature
      if (!this.verifyWebhook(data, data.HashCheck)) {
        throw new Error('Invalid webhook signature')
      }

      // Process based on transaction status
      switch (data.Status) {
        case 'Complete':
          await this.handlePaymentCompleted(data)
          break
        case 'Error':
        case 'Abandoned':
          await this.handlePaymentFailed(data)
          break
        case 'Cancelled':
          await this.handlePaymentCancelled(data)
          break
        case 'PendingInvestigation':
          await this.handlePaymentPending(data)
          break
        default:
          logger.warn('Unknown Ozow transaction status', {
            gateway: this.name,
            status: data.Status,
            transactionId: data.TransactionId,
          })
      }

    } catch (error) {
      logger.error('Ozow webhook processing failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error',
        payload: maskSensitiveData(payload),
      })
      throw error
    }
  }

  /**
   * Prepare Ozow payment data
   */
  private preparePaymentData(request: PaymentRequest) {
    return {
      SiteCode: this.config.siteCode,
      CountryCode: 'ZA',
      CurrencyCode: 'ZAR',
      Amount: request.amount.amount,
      TransactionReference: request.reference,
      BankReference: request.reference,
      Customer: request.customer.email,
      
      // Optional fields
      IsTest: this.config.sandbox,
      NotifyUrl: request.notifyUrl,
      SuccessUrl: request.returnUrl,
      ErrorUrl: request.cancelUrl,
      CancelUrl: request.cancelUrl,
      
      // Additional metadata
      Optional1: request.metadata.orderId,
      Optional2: request.metadata.customerId || '',
      Optional3: request.metadata.source,
      Optional4: request.customer.firstName,
      Optional5: request.customer.lastName,
    }
  }

  /**
   * Map Ozow status to internal status
   */
  private mapOzowStatus(ozowStatus: string): PaymentStatus {
    switch (ozowStatus?.toLowerCase()) {
      case 'complete':
        return PaymentStatus.COMPLETED
      case 'pending':
      case 'pendinginvestigation':
        return PaymentStatus.PENDING
      case 'error':
      case 'abandoned':
        return PaymentStatus.FAILED
      case 'cancelled':
        return PaymentStatus.CANCELLED
      default:
        return PaymentStatus.PENDING
    }
  }

  /**
   * Handle completed payment
   */
  private async handlePaymentCompleted(data: any): Promise<void> {
    logger.info('Ozow payment completed', {
      gateway: this.name,
      transactionId: data.TransactionId,
      reference: data.TransactionReference,
      amount: data.Amount,
    })

    // Update order status in WooCommerce
    // This would be implemented based on your order management system
  }

  /**
   * Handle failed payment
   */
  private async handlePaymentFailed(data: any): Promise<void> {
    logger.warn('Ozow payment failed', {
      gateway: this.name,
      transactionId: data.TransactionId,
      reference: data.TransactionReference,
      status: data.Status,
      errorMessage: data.StatusMessage,
    })

    // Update order status and notify customer
  }

  /**
   * Handle cancelled payment
   */
  private async handlePaymentCancelled(data: any): Promise<void> {
    logger.info('Ozow payment cancelled', {
      gateway: this.name,
      transactionId: data.TransactionId,
      reference: data.TransactionReference,
    })

    // Update order status
  }

  /**
   * Handle pending payment
   */
  private async handlePaymentPending(data: any): Promise<void> {
    logger.info('Ozow payment pending investigation', {
      gateway: this.name,
      transactionId: data.TransactionId,
      reference: data.TransactionReference,
    })

    // Keep order in pending status
  }
}

export default OzowGateway
