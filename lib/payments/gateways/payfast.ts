import axios from 'axios'
import type {
  IPaymentGateway,
  PaymentRequest,
  PaymentResponse,
  PaymentStatus,
  PaymentMethod,
  PaymentGateway,
  PaymentValidationResult,
  WebhookPayload,
  PaymentErrorCode,
} from '../types'
import {
  paymentConfig,
  getGatewayUrl,
  TIMEOUT_CONFIG,
} from '../config'
import {
  validatePaymentRequest,
  generatePaymentReference,
  generatePayFastSignature,
  verifyPayFastSignature,
  formatAmount,
  maskSensitiveData,
  retryWithBackoff,
  getPaymentErrorCode,
} from '../utils'
import { logger } from '../logger'

export class PayFastGateway implements IPaymentGateway {
  name = PaymentGateway.PAYFAST
  displayName = 'PayFast'
  supportedMethods = [PaymentMethod.CARD, PaymentMethod.EFT]
  supportedCurrencies = ['ZAR']

  private config = paymentConfig.payfast!
  private baseUrl = getGatewayUrl(PaymentGateway.PAYFAST)
  private apiUrl = getGatewayUrl(PaymentGateway.PAYFAST, '')

  constructor() {
    if (!this.validateConfig()) {
      throw new Error('PayFast configuration is invalid')
    }
  }

  /**
   * Validate PayFast configuration
   */
  validateConfig(): boolean {
    return !!(
      this.config.merchantId &&
      this.config.merchantKey &&
      this.config.passphrase
    )
  }

  /**
   * Validate payment request
   */
  validatePaymentRequest(request: PaymentRequest): PaymentValidationResult {
    const baseValidation = validatePaymentRequest(request)
    
    if (!baseValidation.isValid) {
      return baseValidation
    }

    const errors: string[] = []

    // PayFast specific validations
    if (request.amount.currency !== 'ZAR') {
      errors.push('PayFast only supports ZAR currency')
    }

    if (request.amount.amount < 5) {
      errors.push('PayFast minimum amount is R5.00')
    }

    if (request.amount.amount > 1000000) {
      errors.push('PayFast maximum amount is R1,000,000.00')
    }

    return {
      isValid: errors.length === 0,
      errors: [...baseValidation.errors, ...errors],
    }
  }

  /**
   * Create payment with PayFast
   */
  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      logger.info('Creating PayFast payment', {
        gateway: this.name,
        amount: request.amount.amount,
        currency: request.amount.currency,
        reference: request.reference,
      })

      // Validate request
      const validation = this.validatePaymentRequest(request)
      if (!validation.isValid) {
        return {
          success: false,
          status: PaymentStatus.FAILED,
          error: {
            code: PaymentErrorCode.INVALID_AMOUNT,
            message: validation.errors.join(', '),
          },
        }
      }

      // Prepare PayFast data
      const paymentData = this.preparePaymentData(request)
      
      // Generate signature
      paymentData.signature = generatePayFastSignature(paymentData, this.config.passphrase)

      // Create payment URL
      const paymentUrl = this.createPaymentUrl(paymentData)

      logger.info('PayFast payment created successfully', {
        gateway: this.name,
        reference: request.reference,
        paymentUrl: paymentUrl.substring(0, 50) + '...',
      })

      return {
        success: true,
        paymentUrl,
        reference: request.reference,
        status: PaymentStatus.PENDING,
        gatewayResponse: maskSensitiveData(paymentData),
      }

    } catch (error) {
      logger.error('PayFast payment creation failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error',
        reference: request.reference,
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: getPaymentErrorCode(error),
          message: error instanceof Error ? error.message : 'Payment creation failed',
        },
      }
    }
  }

  /**
   * Get payment status from PayFast
   */
  async getPaymentStatus(transactionId: string): Promise<PaymentStatus> {
    try {
      logger.info('Checking PayFast payment status', {
        gateway: this.name,
        transactionId,
      })

      // PayFast doesn't have a direct status check API
      // Status is typically received via webhooks
      // This is a placeholder for custom implementation
      
      return PaymentStatus.PENDING

    } catch (error) {
      logger.error('PayFast status check failed', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error',
      })

      return PaymentStatus.FAILED
    }
  }

  /**
   * Refund payment via PayFast
   */
  async refundPayment(transactionId: string, amount?: number): Promise<PaymentResponse> {
    try {
      logger.info('Processing PayFast refund', {
        gateway: this.name,
        transactionId,
        amount,
      })

      // PayFast refunds are typically processed manually
      // This would integrate with their API when available
      
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.GATEWAY_ERROR,
          message: 'PayFast refunds must be processed manually through the merchant portal',
        },
      }

    } catch (error) {
      logger.error('PayFast refund failed', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error',
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: getPaymentErrorCode(error),
          message: error instanceof Error ? error.message : 'Refund failed',
        },
      }
    }
  }

  /**
   * Verify PayFast webhook signature
   */
  verifyWebhook(payload: any, signature: string): boolean {
    try {
      return verifyPayFastSignature(payload, signature, this.config.passphrase)
    } catch (error) {
      logger.error('PayFast webhook verification failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      return false
    }
  }

  /**
   * Process PayFast webhook
   */
  async processWebhook(payload: WebhookPayload): Promise<void> {
    try {
      logger.info('Processing PayFast webhook', {
        gateway: this.name,
        event: payload.event,
        data: maskSensitiveData(payload.data),
      })

      const data = payload.data

      // Verify the webhook signature
      if (!this.verifyWebhook(data, data.signature)) {
        throw new Error('Invalid webhook signature')
      }

      // Process based on payment status
      switch (data.payment_status) {
        case 'COMPLETE':
          await this.handlePaymentCompleted(data)
          break
        case 'FAILED':
          await this.handlePaymentFailed(data)
          break
        case 'CANCELLED':
          await this.handlePaymentCancelled(data)
          break
        default:
          logger.warn('Unknown PayFast payment status', {
            gateway: this.name,
            status: data.payment_status,
            reference: data.m_payment_id,
          })
      }

    } catch (error) {
      logger.error('PayFast webhook processing failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error',
        payload: maskSensitiveData(payload),
      })
      throw error
    }
  }

  /**
   * Prepare PayFast payment data
   */
  private preparePaymentData(request: PaymentRequest) {
    const itemName = request.items.length === 1 
      ? request.items[0].name 
      : `${request.items.length} items`

    return {
      // Merchant details
      merchant_id: this.config.merchantId,
      merchant_key: this.config.merchantKey,
      
      // Transaction details
      amount: request.amount.amount.toFixed(2),
      item_name: itemName,
      item_description: request.description,
      
      // Custom fields
      custom_str1: request.metadata.orderId,
      custom_str2: request.metadata.customerId || '',
      custom_str3: request.metadata.source,
      
      // URLs
      return_url: request.returnUrl,
      cancel_url: request.cancelUrl,
      notify_url: request.notifyUrl,
      
      // Customer details
      name_first: request.customer.firstName,
      name_last: request.customer.lastName,
      email_address: request.customer.email,
      cell_number: request.customer.phone || '',
      
      // Payment reference
      m_payment_id: request.reference,
    }
  }

  /**
   * Create PayFast payment URL
   */
  private createPaymentUrl(data: Record<string, any>): string {
    const params = new URLSearchParams()
    
    Object.entries(data).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        params.append(key, value.toString())
      }
    })

    return `${this.baseUrl}?${params.toString()}`
  }

  /**
   * Handle completed payment
   */
  private async handlePaymentCompleted(data: any): Promise<void> {
    logger.info('PayFast payment completed', {
      gateway: this.name,
      reference: data.m_payment_id,
      transactionId: data.pf_payment_id,
      amount: data.amount_gross,
    })

    // Update order status in WooCommerce
    // This would be implemented based on your order management system
  }

  /**
   * Handle failed payment
   */
  private async handlePaymentFailed(data: any): Promise<void> {
    logger.warn('PayFast payment failed', {
      gateway: this.name,
      reference: data.m_payment_id,
      reason: data.payment_status,
    })

    // Update order status and notify customer
  }

  /**
   * Handle cancelled payment
   */
  private async handlePaymentCancelled(data: any): Promise<void> {
    logger.info('PayFast payment cancelled', {
      gateway: this.name,
      reference: data.m_payment_id,
    })

    // Update order status
  }
}

export default PayFastGateway
