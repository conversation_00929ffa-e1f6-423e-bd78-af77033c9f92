// Export all payment types
export * from './types'

// Export configuration
export * from './config'

// Export utilities
export * from './utils'

// Export logger
export * from './logger'

// Export gateway factory and manager
export * from './gateway-factory'

// Export gateway implementations
export { default as PayFastGateway } from './gateways/payfast'
export { default as OzowGateway } from './gateways/ozow'

// Re-export main instances for convenience
export { paymentGatewayFactory, paymentManager } from './gateway-factory'
export { logger, paymentLogger, auditLogger } from './logger'

// Export validation functions
export { 
  validatePaymentRequest,
  isValidEmail,
  isValidSouthAfricanPhone,
  isValidUrl,
  formatAmount,
  calculateVAT 
} from './utils'

// Export configuration functions
export {
  validateEnvironmentVariables,
  getGatewayUrl,
  isGatewayEnabled,
  getEnabledGateways,
  calculateTransactionFee
} from './config'
