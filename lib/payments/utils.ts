import crypto from 'crypto'
import { v4 as uuidv4 } from 'uuid'
import type { 
  PaymentRequest, 
  PaymentAmount, 
  PaymentValidationResult,
  PaymentCustomer,
  PaymentItem,
  PaymentGateway,
  PaymentMethod,
  PaymentErrorCode 
} from './types'
import { 
  SUPPORTED_CURRENCIES, 
  TRANSACTION_LIMITS, 
  securityConfig,
  VAT_CONFIG 
} from './config'

/**
 * Generate a unique payment reference
 */
export function generatePaymentReference(prefix: string = 'PAY'): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substring(2, 8)
  return `${prefix}_${timestamp}_${random}`.toUpperCase()
}

/**
 * Generate a secure transaction ID
 */
export function generateTransactionId(): string {
  return uuidv4().replace(/-/g, '').toUpperCase()
}

/**
 * Create MD5 hash for PayFast signature
 */
export function createMD5Hash(data: string): string {
  return crypto.createHash('md5').update(data).digest('hex')
}

/**
 * Create SHA256 hash
 */
export function createSHA256Hash(data: string, secret?: string): string {
  if (secret) {
    return crypto.createHmac('sha256', secret).update(data).digest('hex')
  }
  return crypto.createHash('sha256').update(data).digest('hex')
}

/**
 * Create SHA512 hash
 */
export function createSHA512Hash(data: string, secret?: string): string {
  if (secret) {
    return crypto.createHmac('sha512', secret).update(data).digest('hex')
  }
  return crypto.createHash('sha512').update(data).digest('hex')
}

/**
 * Encrypt sensitive data
 */
export function encryptData(data: string): string {
  const algorithm = 'aes-256-gcm'
  const key = crypto.scryptSync(securityConfig.encryptionKey, 'salt', 32)
  const iv = crypto.randomBytes(16)
  
  const cipher = crypto.createCipher(algorithm, key)
  let encrypted = cipher.update(data, 'utf8', 'hex')
  encrypted += cipher.final('hex')
  
  return `${iv.toString('hex')}:${encrypted}`
}

/**
 * Decrypt sensitive data
 */
export function decryptData(encryptedData: string): string {
  const algorithm = 'aes-256-gcm'
  const key = crypto.scryptSync(securityConfig.encryptionKey, 'salt', 32)
  
  const [ivHex, encrypted] = encryptedData.split(':')
  const iv = Buffer.from(ivHex, 'hex')
  
  const decipher = crypto.createDecipher(algorithm, key)
  let decrypted = decipher.update(encrypted, 'hex', 'utf8')
  decrypted += decipher.final('utf8')
  
  return decrypted
}

/**
 * Validate payment request
 */
export function validatePaymentRequest(request: PaymentRequest): PaymentValidationResult {
  const errors: string[] = []

  // Validate amount
  if (!request.amount || typeof request.amount.amount !== 'number') {
    errors.push('Invalid amount')
  } else {
    if (request.amount.amount < TRANSACTION_LIMITS.min) {
      errors.push(`Amount must be at least R${TRANSACTION_LIMITS.min}`)
    }
    if (request.amount.amount > TRANSACTION_LIMITS.max) {
      errors.push(`Amount cannot exceed R${TRANSACTION_LIMITS.max}`)
    }
  }

  // Validate currency
  if (!request.amount?.currency || !SUPPORTED_CURRENCIES.includes(request.amount.currency as any)) {
    errors.push('Invalid or unsupported currency')
  }

  // Validate customer
  if (!request.customer) {
    errors.push('Customer information is required')
  } else {
    if (!request.customer.email || !isValidEmail(request.customer.email)) {
      errors.push('Valid email address is required')
    }
    if (!request.customer.firstName || request.customer.firstName.trim().length < 2) {
      errors.push('First name is required (minimum 2 characters)')
    }
    if (!request.customer.lastName || request.customer.lastName.trim().length < 2) {
      errors.push('Last name is required (minimum 2 characters)')
    }
    if (request.customer.phone && !isValidSouthAfricanPhone(request.customer.phone)) {
      errors.push('Invalid South African phone number format')
    }
  }

  // Validate items
  if (!request.items || !Array.isArray(request.items) || request.items.length === 0) {
    errors.push('At least one item is required')
  } else {
    request.items.forEach((item, index) => {
      if (!item.id || !item.name) {
        errors.push(`Item ${index + 1}: ID and name are required`)
      }
      if (typeof item.quantity !== 'number' || item.quantity <= 0) {
        errors.push(`Item ${index + 1}: Valid quantity is required`)
      }
      if (typeof item.unitPrice !== 'number' || item.unitPrice <= 0) {
        errors.push(`Item ${index + 1}: Valid unit price is required`)
      }
      if (typeof item.totalPrice !== 'number' || item.totalPrice <= 0) {
        errors.push(`Item ${index + 1}: Valid total price is required`)
      }
      // Validate that total price matches quantity * unit price
      if (Math.abs(item.totalPrice - (item.quantity * item.unitPrice)) > 0.01) {
        errors.push(`Item ${index + 1}: Total price doesn't match quantity × unit price`)
      }
    })
  }

  // Validate URLs
  if (!request.returnUrl || !isValidUrl(request.returnUrl)) {
    errors.push('Valid return URL is required')
  }
  if (!request.cancelUrl || !isValidUrl(request.cancelUrl)) {
    errors.push('Valid cancel URL is required')
  }
  if (!request.notifyUrl || !isValidUrl(request.notifyUrl)) {
    errors.push('Valid notify URL is required')
  }

  // Validate reference
  if (!request.reference || request.reference.trim().length < 3) {
    errors.push('Payment reference is required (minimum 3 characters)')
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

/**
 * Validate email address
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validate South African phone number
 */
export function isValidSouthAfricanPhone(phone: string): boolean {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '')
  
  // Check for valid SA phone number patterns
  const patterns = [
    /^27[0-9]{9}$/, // +27 format
    /^0[0-9]{9}$/, // 0 prefix format
    /^[0-9]{9}$/, // 9 digits without prefix
  ]
  
  return patterns.some(pattern => pattern.test(cleaned))
}

/**
 * Validate URL
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * Format amount for display
 */
export function formatAmount(amount: number, currency: string = 'ZAR'): string {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount)
}

/**
 * Calculate VAT amount
 */
export function calculateVAT(amount: number, inclusive: boolean = VAT_CONFIG.inclusive): {
  vatAmount: number
  netAmount: number
  grossAmount: number
} {
  if (inclusive) {
    // VAT is included in the amount
    const vatAmount = (amount * VAT_CONFIG.rate) / (1 + VAT_CONFIG.rate)
    const netAmount = amount - vatAmount
    return {
      vatAmount: Math.round(vatAmount * 100) / 100,
      netAmount: Math.round(netAmount * 100) / 100,
      grossAmount: amount,
    }
  } else {
    // VAT is added to the amount
    const vatAmount = amount * VAT_CONFIG.rate
    const grossAmount = amount + vatAmount
    return {
      vatAmount: Math.round(vatAmount * 100) / 100,
      netAmount: amount,
      grossAmount: Math.round(grossAmount * 100) / 100,
    }
  }
}

/**
 * Sanitize string for payment processing
 */
export function sanitizeString(str: string): string {
  return str
    .replace(/[^\w\s\-_.@]/g, '') // Remove special characters except allowed ones
    .trim()
    .substring(0, 255) // Limit length
}

/**
 * Generate PayFast signature
 */
export function generatePayFastSignature(data: Record<string, any>, passphrase: string): string {
  // Remove signature and empty values
  const filteredData = Object.entries(data)
    .filter(([key, value]) => key !== 'signature' && value !== '' && value !== null && value !== undefined)
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&')
  
  const stringToHash = `${filteredData}&passphrase=${encodeURIComponent(passphrase)}`
  return createMD5Hash(stringToHash)
}

/**
 * Verify PayFast signature
 */
export function verifyPayFastSignature(data: Record<string, any>, signature: string, passphrase: string): boolean {
  const calculatedSignature = generatePayFastSignature(data, passphrase)
  return calculatedSignature === signature
}

/**
 * Generate Ozow hash
 */
export function generateOzowHash(data: Record<string, any>, privateKey: string): string {
  const sortedData = Object.entries(data)
    .sort(([a], [b]) => a.toLowerCase().localeCompare(b.toLowerCase()))
    .map(([key, value]) => `${key}=${value}`)
    .join('')
  
  return createSHA512Hash(sortedData, privateKey).toLowerCase()
}

/**
 * Mask sensitive data for logging
 */
export function maskSensitiveData(data: any): any {
  if (typeof data !== 'object' || data === null) {
    return data
  }

  const sensitiveFields = [
    'password', 'passphrase', 'secret', 'key', 'token', 'signature',
    'cardNumber', 'cvv', 'pin', 'accountNumber', 'merchantKey'
  ]

  const masked = { ...data }
  
  for (const [key, value] of Object.entries(masked)) {
    const lowerKey = key.toLowerCase()
    if (sensitiveFields.some(field => lowerKey.includes(field))) {
      if (typeof value === 'string' && value.length > 4) {
        masked[key] = `${value.substring(0, 4)}${'*'.repeat(value.length - 4)}`
      } else {
        masked[key] = '***'
      }
    } else if (typeof value === 'object' && value !== null) {
      masked[key] = maskSensitiveData(value)
    }
  }

  return masked
}

/**
 * Generate webhook signature
 */
export function generateWebhookSignature(payload: string, secret: string): string {
  return createSHA256Hash(payload, secret)
}

/**
 * Verify webhook signature
 */
export function verifyWebhookSignature(payload: string, signature: string, secret: string): boolean {
  const calculatedSignature = generateWebhookSignature(payload, secret)
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(calculatedSignature, 'hex')
  )
}

/**
 * Convert cents to rands
 */
export function centsToRands(cents: number): number {
  return Math.round(cents) / 100
}

/**
 * Convert rands to cents
 */
export function randsToCents(rands: number): number {
  return Math.round(rands * 100)
}

/**
 * Retry function with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      
      if (attempt === maxAttempts) {
        throw lastError
      }

      const delay = baseDelay * Math.pow(2, attempt - 1)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  throw lastError!
}

/**
 * Check if error is retryable
 */
export function isRetryableError(error: any): boolean {
  const retryableErrors = [
    'NETWORK_ERROR',
    'TIMEOUT',
    'RATE_LIMITED',
    'MAINTENANCE',
    'GATEWAY_ERROR'
  ]

  if (error?.code) {
    return retryableErrors.includes(error.code)
  }

  if (error?.message) {
    const message = error.message.toLowerCase()
    return message.includes('timeout') || 
           message.includes('network') || 
           message.includes('connection') ||
           message.includes('rate limit')
  }

  return false
}

/**
 * Generate payment error code from error
 */
export function getPaymentErrorCode(error: any): PaymentErrorCode {
  if (!error) return PaymentErrorCode.UNKNOWN_ERROR

  const message = error.message?.toLowerCase() || ''
  
  if (message.includes('amount')) return PaymentErrorCode.INVALID_AMOUNT
  if (message.includes('currency')) return PaymentErrorCode.INVALID_CURRENCY
  if (message.includes('customer')) return PaymentErrorCode.INVALID_CUSTOMER
  if (message.includes('network')) return PaymentErrorCode.NETWORK_ERROR
  if (message.includes('authentication')) return PaymentErrorCode.AUTHENTICATION_ERROR
  if (message.includes('insufficient')) return PaymentErrorCode.INSUFFICIENT_FUNDS
  if (message.includes('declined')) return PaymentErrorCode.CARD_DECLINED
  if (message.includes('expired')) return PaymentErrorCode.EXPIRED_CARD
  if (message.includes('invalid card')) return PaymentErrorCode.INVALID_CARD
  if (message.includes('fraud')) return PaymentErrorCode.FRAUD_DETECTED
  if (message.includes('rate limit')) return PaymentErrorCode.RATE_LIMITED
  if (message.includes('maintenance')) return PaymentErrorCode.MAINTENANCE

  return PaymentErrorCode.UNKNOWN_ERROR
}

// All functions are already exported above
