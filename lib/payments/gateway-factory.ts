import type {
  IPaymentGateway,
  PaymentRequest,
  PaymentResponse,
  PaymentStatus,
  PaymentMethod,
  PaymentErrorCode,
} from './types'
import { PaymentGateway } from './types'
import { 
  GATEWAY_PRIORITY, 
  getEnabledGateways, 
  isGatewayEnabled,
  PAYMENT_METHOD_CONFIG 
} from './config'
// Use dedicated logger to avoid circular dependencies
const logger = {
  info: (message: string, meta?: any) => {
    console.log(`[Payment Gateway Factory] ${message}`, meta ? JSON.stringify(meta, null, 2) : '')
  },
  error: (message: string, meta?: any) => {
    console.error(`[Payment Gateway Factory Error] ${message}`, meta ? JSON.stringify(meta, null, 2) : '')
  },
  warn: (message: string, meta?: any) => {
    console.warn(`[Payment Gateway Factory Warning] ${message}`, meta ? JSON.stringify(meta, null, 2) : '')
  }
}

const paymentLogger = {
  logPaymentInitiated: (data: any) => {
    console.log('[Payment Initiated]', JSON.stringify(data, null, 2))
  },
  logPaymentFailed: (data: any) => {
    console.error('[Payment Failed]', JSON.stringify(data, null, 2))
  },
  logRefundInitiated: (data: any) => {
    console.log('[Refund Initiated]', JSON.stringify(data, null, 2))
  },
  logRefundCompleted: (data: any) => {
    console.log('[Refund Completed]', JSON.stringify(data, null, 2))
  },
  logPerformanceMetrics: (data: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug('[Performance Metrics]', JSON.stringify(data, null, 2))
    }
  }
}
import { retryWithBackoff, isRetryableError } from './utils'

// Import gateway implementations
import PayFastGateway from './gateways/payfast'
import OzowGateway from './gateways/ozow'
// import SnapScanGateway from './gateways/snapscan'
// import YocoGateway from './gateways/yoco'
// import PayUGateway from './gateways/payu'

/**
 * Payment Gateway Factory
 * Creates and manages payment gateway instances
 */
export class PaymentGatewayFactory {
  private static instance: PaymentGatewayFactory
  private gateways: Map<PaymentGateway, IPaymentGateway> = new Map()

  private constructor() {
    this.initializeGateways()
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): PaymentGatewayFactory {
    if (!PaymentGatewayFactory.instance) {
      PaymentGatewayFactory.instance = new PaymentGatewayFactory()
    }
    return PaymentGatewayFactory.instance
  }

  /**
   * Initialize available gateways
   */
  private initializeGateways(): void {
    try {
      // Initialize PayFast
      if (isGatewayEnabled(PaymentGateway.PAYFAST)) {
        this.gateways.set(PaymentGateway.PAYFAST, new PayFastGateway())
        logger.info('PayFast gateway initialized')
      }

      // Initialize Ozow
      if (isGatewayEnabled(PaymentGateway.OZOW)) {
        this.gateways.set(PaymentGateway.OZOW, new OzowGateway())
        logger.info('Ozow gateway initialized')
      }

      // TODO: Initialize other gateways when implemented
      // if (isGatewayEnabled(PaymentGateway.SNAPSCAN)) {
      //   this.gateways.set(PaymentGateway.SNAPSCAN, new SnapScanGateway())
      //   logger.info('SnapScan gateway initialized')
      // }

      logger.info(`Initialized ${this.gateways.size} payment gateways`, {
        gateways: Array.from(this.gateways.keys()),
      })

    } catch (error) {
      logger.error('Failed to initialize payment gateways', {
        error: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  /**
   * Get gateway instance by name
   */
  public getGateway(gateway: PaymentGateway): IPaymentGateway | null {
    return this.gateways.get(gateway) || null
  }

  /**
   * Get all available gateways
   */
  public getAvailableGateways(): PaymentGateway[] {
    return Array.from(this.gateways.keys())
  }

  /**
   * Get gateways that support a specific payment method
   */
  public getGatewaysForMethod(method: PaymentMethod): PaymentGateway[] {
    const supportedGateways: PaymentGateway[] = []

    for (const [gatewayName, gateway] of this.gateways) {
      if (gateway.supportedMethods.includes(method)) {
        supportedGateways.push(gatewayName)
      }
    }

    // Sort by priority
    return supportedGateways.sort((a, b) => {
      const priorityA = GATEWAY_PRIORITY.indexOf(a)
      const priorityB = GATEWAY_PRIORITY.indexOf(b)
      return priorityA - priorityB
    })
  }

  /**
   * Get best gateway for a payment request
   */
  public getBestGateway(request: PaymentRequest, preferredMethod?: PaymentMethod): IPaymentGateway | null {
    const currency = request.amount.currency
    const amount = request.amount.amount

    // Filter gateways by currency support
    const compatibleGateways = Array.from(this.gateways.entries()).filter(([_, gateway]) => 
      gateway.supportedCurrencies.includes(currency)
    )

    if (compatibleGateways.length === 0) {
      logger.warn('No gateways support the requested currency', { currency })
      return null
    }

    // If preferred method is specified, filter by method support
    if (preferredMethod) {
      const methodCompatibleGateways = compatibleGateways.filter(([_, gateway]) =>
        gateway.supportedMethods.includes(preferredMethod)
      )

      if (methodCompatibleGateways.length > 0) {
        // Return the highest priority gateway that supports the method
        for (const gatewayName of GATEWAY_PRIORITY) {
          const gateway = methodCompatibleGateways.find(([name]) => name === gatewayName)
          if (gateway) {
            return gateway[1]
          }
        }
      }
    }

    // Return the highest priority compatible gateway
    for (const gatewayName of GATEWAY_PRIORITY) {
      const gateway = compatibleGateways.find(([name]) => name === gatewayName)
      if (gateway) {
        return gateway[1]
      }
    }

    return null
  }

  /**
   * Validate gateway configuration
   */
  public validateGatewayConfig(gateway: PaymentGateway): boolean {
    const gatewayInstance = this.getGateway(gateway)
    return gatewayInstance ? gatewayInstance.validateConfig() : false
  }
}

/**
 * Payment Manager
 * High-level payment processing with fallback and retry logic
 */
export class PaymentManager {
  private factory: PaymentGatewayFactory

  constructor() {
    this.factory = PaymentGatewayFactory.getInstance()
  }

  /**
   * Process payment with automatic gateway selection and fallback
   */
  async processPayment(
    request: PaymentRequest,
    preferredGateway?: PaymentGateway,
    preferredMethod?: PaymentMethod
  ): Promise<PaymentResponse> {
    const startTime = Date.now()

    try {
      paymentLogger.logPaymentInitiated({
        gateway: preferredGateway || PaymentGateway.PAYFAST,
        reference: request.reference,
        amount: request.amount.amount,
        currency: request.amount.currency,
        customerId: request.metadata.customerId,
        orderId: request.metadata.orderId,
      })

      // Get primary gateway
      let gateway: IPaymentGateway | null = null

      if (preferredGateway) {
        gateway = this.factory.getGateway(preferredGateway)
        if (!gateway) {
          logger.warn('Preferred gateway not available', { gateway: preferredGateway })
        }
      }

      // Fallback to best available gateway
      if (!gateway) {
        gateway = this.factory.getBestGateway(request, preferredMethod)
      }

      if (!gateway) {
        const error = 'No suitable payment gateway available'
        paymentLogger.logPaymentFailed({
          gateway: preferredGateway || PaymentGateway.PAYFAST,
          reference: request.reference,
          amount: request.amount.amount,
          currency: request.amount.currency,
          customerId: request.metadata.customerId,
          orderId: request.metadata.orderId,
          errorCode: PaymentErrorCode.GATEWAY_ERROR,
          errorMessage: error,
          processingTime: Date.now() - startTime,
        })

        return {
          success: false,
          status: PaymentStatus.FAILED,
          error: {
            code: PaymentErrorCode.GATEWAY_ERROR,
            message: error,
          },
        }
      }

      // Process payment with retry logic
      const response = await retryWithBackoff(
        async () => await gateway!.createPayment(request),
        3,
        1000
      )

      const processingTime = Date.now() - startTime

      if (response.success) {
        paymentLogger.logPaymentInitiated({
          gateway: gateway.name,
          reference: request.reference,
          amount: request.amount.amount,
          currency: request.amount.currency,
          customerId: request.metadata.customerId,
          orderId: request.metadata.orderId,
        })

        paymentLogger.logPerformanceMetrics({
          gateway: gateway.name,
          operation: 'create_payment',
          duration: processingTime,
          success: true,
        })
      } else {
        paymentLogger.logPaymentFailed({
          gateway: gateway.name,
          reference: request.reference,
          amount: request.amount.amount,
          currency: request.amount.currency,
          customerId: request.metadata.customerId,
          orderId: request.metadata.orderId,
          errorCode: response.error?.code || PaymentErrorCode.UNKNOWN_ERROR,
          errorMessage: response.error?.message || 'Unknown error',
          processingTime,
        })
      }

      return response

    } catch (error) {
      const processingTime = Date.now() - startTime

      paymentLogger.logPaymentFailed({
        gateway: preferredGateway || PaymentGateway.PAYFAST,
        reference: request.reference,
        amount: request.amount.amount,
        currency: request.amount.currency,
        customerId: request.metadata.customerId,
        orderId: request.metadata.orderId,
        errorCode: PaymentErrorCode.UNKNOWN_ERROR,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        processingTime,
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.UNKNOWN_ERROR,
          message: error instanceof Error ? error.message : 'Payment processing failed',
        },
      }
    }
  }

  /**
   * Get payment status from gateway
   */
  async getPaymentStatus(
    transactionId: string,
    gateway: PaymentGateway
  ): Promise<PaymentStatus> {
    try {
      const gatewayInstance = this.factory.getGateway(gateway)
      if (!gatewayInstance) {
        throw new Error(`Gateway ${gateway} not available`)
      }

      return await gatewayInstance.getPaymentStatus(transactionId)

    } catch (error) {
      logger.error('Failed to get payment status', {
        gateway,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error',
      })

      return PaymentStatus.FAILED
    }
  }

  /**
   * Process refund
   */
  async processRefund(
    transactionId: string,
    gateway: PaymentGateway,
    amount?: number,
    reason?: string
  ): Promise<PaymentResponse> {
    try {
      const gatewayInstance = this.factory.getGateway(gateway)
      if (!gatewayInstance) {
        throw new Error(`Gateway ${gateway} not available`)
      }

      paymentLogger.logRefundInitiated({
        gateway,
        originalTransactionId: transactionId,
        refundAmount: amount || 0,
        currency: 'ZAR', // Default to ZAR
        reason: reason || 'Customer request',
        orderId: '', // Would be retrieved from transaction
      })

      const response = await gatewayInstance.refundPayment(transactionId, amount)

      if (response.success) {
        paymentLogger.logRefundCompleted({
          gateway,
          originalTransactionId: transactionId,
          refundTransactionId: response.transactionId || '',
          refundAmount: amount || 0,
          currency: 'ZAR',
          orderId: '',
        })
      }

      return response

    } catch (error) {
      logger.error('Failed to process refund', {
        gateway,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error',
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.UNKNOWN_ERROR,
          message: error instanceof Error ? error.message : 'Refund processing failed',
        },
      }
    }
  }

  /**
   * Get available payment methods
   */
  getAvailablePaymentMethods(): Array<{
    method: PaymentMethod
    displayName: string
    description: string
    gateways: PaymentGateway[]
  }> {
    const methods: Array<{
      method: PaymentMethod
      displayName: string
      description: string
      gateways: PaymentGateway[]
    }> = []

    for (const [method, config] of Object.entries(PAYMENT_METHOD_CONFIG)) {
      const supportedGateways = this.factory.getGatewaysForMethod(method as PaymentMethod)
      
      if (supportedGateways.length > 0) {
        methods.push({
          method: method as PaymentMethod,
          displayName: config.displayName,
          description: config.description,
          gateways: supportedGateways,
        })
      }
    }

    return methods
  }
}

// Export singleton instances
export const paymentGatewayFactory = PaymentGatewayFactory.getInstance()
export const paymentManager = new PaymentManager()

// All exports are already available above
