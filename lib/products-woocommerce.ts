import { wooCommerceClient, wooCommerceUtils } from './wordpress'

// Types for internal product representation
export interface Product {
  id: string
  name: string
  slug: string
  description: string
  price: number
  compareAtPrice?: number
  images: string[]
  colors: Array<{ name: string; value: string }>
  sizes: string[]
  categoryId: string
  isNew: boolean
  isSale: boolean
  inStock: boolean
  stockQuantity?: number
  sku: string
  rating: number
  reviewCount: number
  tags: string[]
  categories: Array<{ id: number; name: string; slug: string }>
  variations?: any[]
  attributes?: any[]
  meta_data?: any[]
  woocommerce?: any
}

export interface GetProductsOptions {
  category?: string
  color?: string
  size?: string
  minPrice?: number
  maxPrice?: number
  sortBy?: 'price' | 'name' | 'date' | 'popularity' | 'rating'
  sortOrder?: 'asc' | 'desc'
  page?: number
  per_page?: number
  search?: string
  featured?: boolean
  on_sale?: boolean
  stock_status?: 'instock' | 'outofstock' | 'onbackorder'
}

/**
 * Get products from WooCommerce with filtering and pagination
 */
export async function getProducts(options: GetProductsOptions = {}): Promise<Product[]> {
  try {
    // Map internal options to WooCommerce API parameters
    const wcOptions = {
      page: options.page || 1,
      per_page: options.per_page || 20,
      search: options.search,
      category: options.category,
      featured: options.featured,
      on_sale: options.on_sale,
      min_price: options.minPrice,
      max_price: options.maxPrice,
      stock_status: options.stock_status,
      orderby: mapSortBy(options.sortBy),
      order: options.sortOrder || 'desc',
    }

    // Fetch products from WooCommerce
    const wcProducts = await wooCommerceClient.getProducts(wcOptions)

    // Convert to internal format
    const products = wcProducts.map(wcProduct => {
      const baseProduct = wooCommerceUtils.convertToInternalProduct(wcProduct)
      const { colors, sizes } = wooCommerceUtils.extractProductAttributes(wcProduct)
      
      return {
        ...baseProduct,
        colors,
        sizes,
        rating: parseFloat(wcProduct.average_rating) || 0,
        reviewCount: wcProduct.rating_count || 0,
        tags: wcProduct.tags.map(tag => tag.name),
        categories: wcProduct.categories.map(cat => ({
          id: cat.id,
          name: cat.name,
          slug: cat.slug,
        })),
        variations: wcProduct.variations || [],
        attributes: wcProduct.attributes,
        meta_data: wcProduct.meta_data,
        woocommerce: {
          id: wcProduct.id,
          sku: wcProduct.sku,
          weight: wcProduct.weight,
          dimensions: wcProduct.dimensions,
          shipping_required: wcProduct.shipping_required,
          tax_status: wcProduct.tax_status,
          tax_class: wcProduct.tax_class,
          manage_stock: wcProduct.manage_stock,
          stock_quantity: wcProduct.stock_quantity,
          backorders: wcProduct.backorders,
          sold_individually: wcProduct.sold_individually,
          purchase_note: wcProduct.purchase_note,
          related_ids: wcProduct.related_ids,
          upsell_ids: wcProduct.upsell_ids,
          cross_sell_ids: wcProduct.cross_sell_ids,
        },
      }
    })

    // Apply additional client-side filters if needed
    let filteredProducts = products

    // Filter by color if specified
    if (options.color) {
      filteredProducts = filteredProducts.filter(product =>
        product.colors.some(color => 
          color.name.toLowerCase() === options.color?.toLowerCase()
        )
      )
    }

    // Filter by size if specified
    if (options.size) {
      filteredProducts = filteredProducts.filter(product =>
        product.sizes.some(size => 
          size.toLowerCase() === options.size?.toLowerCase()
        )
      )
    }

    return filteredProducts

  } catch (error) {
    console.error('Error fetching products from WooCommerce:', error)
    throw new Error('Failed to fetch products')
  }
}

/**
 * Get a single product by slug
 */
export async function getProduct(slug: string): Promise<Product | null> {
  try {
    // Try to get product by slug first
    let wcProduct = await wooCommerceClient.getProductBySlug(slug)
    
    // If not found by slug, try by ID (if slug is numeric)
    if (!wcProduct && /^\d+$/.test(slug)) {
      wcProduct = await wooCommerceClient.getProduct(parseInt(slug))
    }

    if (!wcProduct) {
      return null
    }

    // Convert to internal format
    const baseProduct = wooCommerceUtils.convertToInternalProduct(wcProduct)
    const { colors, sizes } = wooCommerceUtils.extractProductAttributes(wcProduct)
    
    return {
      ...baseProduct,
      colors,
      sizes,
      rating: parseFloat(wcProduct.average_rating) || 0,
      reviewCount: wcProduct.rating_count || 0,
      tags: wcProduct.tags.map(tag => tag.name),
      categories: wcProduct.categories.map(cat => ({
        id: cat.id,
        name: cat.name,
        slug: cat.slug,
      })),
      variations: wcProduct.variations || [],
      attributes: wcProduct.attributes,
      meta_data: wcProduct.meta_data,
      woocommerce: {
        id: wcProduct.id,
        sku: wcProduct.sku,
        weight: wcProduct.weight,
        dimensions: wcProduct.dimensions,
        shipping_required: wcProduct.shipping_required,
        tax_status: wcProduct.tax_status,
        tax_class: wcProduct.tax_class,
        manage_stock: wcProduct.manage_stock,
        stock_quantity: wcProduct.stock_quantity,
        backorders: wcProduct.backorders,
        sold_individually: wcProduct.sold_individually,
        purchase_note: wcProduct.purchase_note,
        related_ids: wcProduct.related_ids,
        upsell_ids: wcProduct.upsell_ids,
        cross_sell_ids: wcProduct.cross_sell_ids,
      },
    }

  } catch (error) {
    console.error(`Error fetching product ${slug} from WooCommerce:`, error)
    return null
  }
}

/**
 * Get related products for a given product
 */
export async function getRelatedProducts(categoryId: string, currentProductId: string): Promise<Product[]> {
  try {
    // Convert string IDs to numbers for WooCommerce
    const numericProductId = parseInt(currentProductId)
    
    if (isNaN(numericProductId)) {
      return []
    }

    const wcRelatedProducts = await wooCommerceClient.getRelatedProducts(numericProductId, 4)
    
    return wcRelatedProducts.map(wcProduct => {
      const baseProduct = wooCommerceUtils.convertToInternalProduct(wcProduct)
      const { colors, sizes } = wooCommerceUtils.extractProductAttributes(wcProduct)
      
      return {
        ...baseProduct,
        colors,
        sizes,
        rating: parseFloat(wcProduct.average_rating) || 0,
        reviewCount: wcProduct.rating_count || 0,
        tags: wcProduct.tags.map(tag => tag.name),
        categories: wcProduct.categories.map(cat => ({
          id: cat.id,
          name: cat.name,
          slug: cat.slug,
        })),
        variations: wcProduct.variations || [],
        attributes: wcProduct.attributes,
        meta_data: wcProduct.meta_data,
      }
    })

  } catch (error) {
    console.error(`Error fetching related products for ${currentProductId}:`, error)
    return []
  }
}

/**
 * Search products
 */
export async function searchProducts(query: string): Promise<Product[]> {
  try {
    const wcProducts = await wooCommerceClient.searchProducts(query, { per_page: 10 })
    
    return wcProducts.map(wcProduct => {
      const baseProduct = wooCommerceUtils.convertToInternalProduct(wcProduct)
      const { colors, sizes } = wooCommerceUtils.extractProductAttributes(wcProduct)
      
      return {
        ...baseProduct,
        colors,
        sizes,
        rating: parseFloat(wcProduct.average_rating) || 0,
        reviewCount: wcProduct.rating_count || 0,
        tags: wcProduct.tags.map(tag => tag.name),
        categories: wcProduct.categories.map(cat => ({
          id: cat.id,
          name: cat.name,
          slug: cat.slug,
        })),
      }
    })

  } catch (error) {
    console.error(`Error searching products for "${query}":`, error)
    return []
  }
}

/**
 * Get products by IDs
 */
export async function getProductsByIds(ids: string[]): Promise<Product[]> {
  try {
    const products: Product[] = []
    
    // Fetch each product individually (WooCommerce API doesn't support bulk fetch by IDs directly)
    for (const id of ids) {
      const product = await getProduct(id)
      if (product) {
        products.push(product)
      }
    }
    
    return products

  } catch (error) {
    console.error('Error fetching products by IDs:', error)
    return []
  }
}

/**
 * Get product categories
 */
export async function getCategories(): Promise<Array<{ id: string; name: string; slug: string }>> {
  try {
    const wcCategories = await wooCommerceClient.getCategories()
    
    return wcCategories.map(category => ({
      id: category.id.toString(),
      name: category.name,
      slug: category.slug,
    }))

  } catch (error) {
    console.error('Error fetching categories from WooCommerce:', error)
    return []
  }
}

/**
 * Helper function to map internal sort options to WooCommerce API parameters
 */
function mapSortBy(sortBy?: string): string {
  switch (sortBy) {
    case 'price':
      return 'price'
    case 'name':
      return 'title'
    case 'date':
      return 'date'
    case 'popularity':
      return 'popularity'
    case 'rating':
      return 'rating'
    default:
      return 'date'
  }
}

/**
 * Check if a product is in stock
 */
export function isProductInStock(product: Product): boolean {
  return product.inStock && (
    !product.woocommerce?.manage_stock || 
    (product.stockQuantity !== undefined && product.stockQuantity > 0)
  )
}

/**
 * Format price with South African Rand currency
 */
export function formatPrice(price: number): string {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
  }).format(price)
}
