import axios from 'axios';

const WOOCOMMERCE_BASE_URL = process.env.WOOCOMMERCE_BASE_URL || 'https://yourstore.com';
const WOOCOMMERCE_CONSUMER_KEY = process.env.WOOCOMMERCE_CONSUMER_KEY || 'your_consumer_key';
const WOOCOMMERCE_CONSUMER_SECRET = process.env.WOOCOMMERCE_CONSUMER_SECRET || 'your_consumer_secret';

export async function syncOrders() {
  try {
    const response = await axios.get(
      `${WOOCOMMERCE_BASE_URL}/wp-json/wc/v3/orders`,
      {
        auth: {
          username: WOOCOMMERCE_CONSUMER_KEY,
          password: WOOCOMMERCE_CONSUMER_SECRET
        }
      }
    );
    const orders = response.data;
    console.log('Orders synced successfully:', orders);
    return orders;
  } catch (error) {
    console.error('Error syncing orders:', error);
    throw error;
  }
}
