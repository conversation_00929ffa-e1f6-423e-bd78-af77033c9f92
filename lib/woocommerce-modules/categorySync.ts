import axios from 'axios';

const WOOCOMMERCE_BASE_URL = process.env.WOOCOMMERCE_BASE_URL || 'https://yourstore.com';
const WOOCOMMERCE_CONSUMER_KEY = process.env.WOOCOMMERCE_CONSUMER_KEY || 'your_consumer_key';
const WOOCOMMERCE_CONSUMER_SECRET = process.env.WOOCOMMERCE_CONSUMER_SECRET || 'your_consumer_secret';

export async function syncCategories() {
  try {
    const response = await axios.get(
      `${WOOCOMMERCE_BASE_URL}/wp-json/wc/v3/products/categories`,
      {
        auth: {
          username: WOOCOMMERCE_CONSUMER_KEY,
          password: WOOCOMMERCE_CONSUMER_SECRET
        }
      }
    );
    const categories = response.data;
    console.log('Categories synced successfully:', categories);
    return categories;
  } catch (error) {
    console.error('Error syncing categories:', error);
    throw error;
  }
}
