import axios from 'axios';

const WOOCOMMERCE_BASE_URL = process.env.WOOCOMMERCE_BASE_URL || 'https://yourstore.com';
const WOOCOMMERCE_CONSUMER_KEY = process.env.WOOCOMMERCE_CONSUMER_KEY || 'your_consumer_key';
const WOOCOMMERCE_CONSUMER_SECRET = process.env.WOOCOMMERCE_CONSUMER_SECRET || 'your_consumer_secret';

export async function syncReviews() {
  try {
    const response = await axios.get(
      `${WOOCOMMERCE_BASE_URL}/wp-json/wc/v3/products/reviews`,
      {
        auth: {
          username: WOOCOMMERCE_CONSUMER_KEY,
          password: WOOCOMMERCE_CONSUMER_SECRET
        }
      }
    );
    const reviews = response.data;
    console.log('Product reviews synced successfully:', reviews);
    return reviews;
  } catch (error) {
    console.error('Error syncing product reviews:', error);
    throw error;
  }
}
