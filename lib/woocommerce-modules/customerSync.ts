import axios from 'axios';

const WOOCOMMERCE_BASE_URL = process.env.WOOCOMMERCE_BASE_URL || 'https://yourstore.com';
const WOOCOMMERCE_CONSUMER_KEY = process.env.WOOCOMMERCE_CONSUMER_KEY || 'your_consumer_key';
const WOOCOMMERCE_CONSUMER_SECRET = process.env.WOOCOMMERCE_CONSUMER_SECRET || 'your_consumer_secret';

export async function syncCustomers() {
  try {
    const response = await axios.get(
      `${WOOCOMMERCE_BASE_URL}/wp-json/wc/v3/customers`,
      {
        auth: {
          username: WOOCOMMERCE_CONSUMER_KEY,
          password: WOOCOMMERCE_CONSUMER_SECRET
        }
      }
    );
    const customers = response.data;
    console.log('Customers synced successfully:', customers);
    return customers;
  } catch (error) {
    console.error('Error syncing customers:', error);
    throw error;
  }
}
