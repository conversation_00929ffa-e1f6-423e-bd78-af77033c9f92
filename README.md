# 🛍️ Coco Milk Kids - E-commerce Platform

A comprehensive, production-ready e-commerce platform built with Next.js 15, featuring advanced admin tools, AI integration, and South African payment gateways.

## ✨ Features

- 🛒 **Complete E-commerce Solution** - Product catalog, shopping cart, checkout, order management
- 🎨 **Visual Page Builder** - Drag-and-drop page creation with AI-powered blocks
- 🤖 **AI Integration** - Product recommendations, shopping assistant, content generation
- 💳 **Payment Gateways** - PayFast & Ozow integration for South African market
- 📊 **Advanced Analytics** - Sales reports, customer insights, inventory tracking
- 🔐 **Secure Admin Panel** - Role-based access control, comprehensive management tools
- 📱 **Responsive Design** - Mobile-first, Zara-inspired aesthetic
- 🌍 **WordPress Integration** - WooCommerce compatibility

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- PostgreSQL database
- pnpm package manager

### One-Command Setup

```bash
pnpm setup
```

This will:
- Install all dependencies
- Set up the database
- Seed with sample data
- Create admin user
- Build the project

### Manual Setup

1. **Install Dependencies**
   ```bash
   pnpm install
   ```

2. **Environment Configuration**
   Create a `.env` file with:
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/cocomilk"
   JWT_SECRET="your-jwt-secret-here"
   NEXTAUTH_SECRET="your-nextauth-secret-here"
   OPENAI_API_KEY="your-openai-api-key"
   APPWRITE_PROJECT_ID="your-appwrite-project-id"
   APPWRITE_API_KEY="your-appwrite-api-key"
   PAYFAST_MERCHANT_ID="your-payfast-merchant-id"
   PAYFAST_MERCHANT_KEY="your-payfast-merchant-key"
   OZOW_SITE_CODE="your-ozow-site-code"
   OZOW_PRIVATE_KEY="your-ozow-private-key"
   ```

3. **Database Setup**
   ```bash
   pnpm db:push
   pnpm seed
   pnpm create-admin
   ```

4. **Start Development**
   ```bash
   pnpm dev
   ```

## 🌐 Access Points

- **Frontend**: http://localhost:3090
- **Admin Panel**: http://localhost:3090/admin
- **API Documentation**: http://localhost:3090/api

### Admin Credentials

- **Email**: <EMAIL>
- **Password**: admin123

## 🏗️ Tech Stack

### Frontend
- **Next.js 15** - App Router, Server Components
- **React 19** - Latest React features
- **TypeScript** - Type safety
- **Tailwind CSS** - Utility-first styling
- **Shadcn/UI** - Component library

### Backend
- **Next.js API Routes** - Serverless functions
- **Prisma ORM** - Database management
- **PostgreSQL** - Primary database
- **JWT** - Authentication

### AI & Integrations
- **OpenAI GPT-4** - AI features
- **Vercel AI SDK** - AI integration
- **Appwrite** - Backend services
- **PayFast/Ozow** - Payment processing

## 📁 Project Structure

```
├── app/                    # Next.js App Router
│   ├── admin/             # Admin dashboard
│   ├── api/               # API routes
│   └── frontend/          # Customer-facing pages
├── components/            # Reusable components
│   ├── admin/            # Admin-specific components
│   ├── ui/               # UI components
│   └── page-builder/     # Page builder components
├── lib/                   # Utilities and services
│   ├── ecommerce/        # E-commerce logic
│   ├── ai/               # AI services
│   └── wordpress/        # WordPress integration
├── prisma/               # Database schema and migrations
└── scripts/              # Setup and utility scripts
```

## 🛠️ Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm setup` - Complete project setup
- `pnpm seed` - Seed database with sample data
- `pnpm create-admin` - Create admin user
- `pnpm db:push` - Push database schema
- `pnpm db:studio` - Open Prisma Studio

## 📊 Key Features

### E-commerce Core
- Product catalog with variants
- Shopping cart and checkout
- Order management
- Inventory tracking
- Customer accounts

### Admin Dashboard
- Product management
- Order processing
- Customer management
- Analytics and reports
- Inventory control

### Page Builder
- Visual drag-and-drop editor
- AI-powered block generation
- Responsive design tools
- Template system
- Custom components

### AI Features
- Product recommendations
- Shopping assistant chatbot
- Content generation
- Size recommendations
- Smart search

## 🔒 Security

- JWT-based authentication
- Rate limiting
- Input validation
- SQL injection prevention
- XSS protection
- CORS configuration

## 🌍 Localization

- South African Rand (ZAR) currency
- Local payment gateways
- South African address formats
- Timezone support

## 📈 Performance

- Next.js 15 optimizations
- Static generation
- Image optimization
- Code splitting
- Caching strategies

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation in `PROJECT_COMPLETION_SUMMARY.md`
- Review the code comments
- Open an issue on GitHub

---

**🎉 Ready to build amazing e-commerce experiences! 🎉**
