'use client'

import { useState, useEffect, useCallback } from 'react'
import { toast } from 'sonner'

export interface SettingsSection {
  id: string
  name: string
  data: Record<string, any>
  lastUpdated?: string
  status: 'loaded' | 'loading' | 'error' | 'saving'
}

export interface UseSettingsOptions {
  section?: string
  autoLoad?: boolean
}

export interface UseSettingsResult {
  settings: Record<string, SettingsSection>
  loading: boolean
  error: string | null
  loadSettings: (section?: string) => Promise<void>
  saveSettings: (section: string, data: Record<string, any>) => Promise<void>
  updateSetting: (section: string, key: string, value: any) => void
  getSetting: (section: string, key: string, defaultValue?: any) => any
  getSection: (section: string) => SettingsSection | null
  resetSection: (section: string) => Promise<void>
  exportSettings: () => Promise<string>
  importSettings: (data: string) => Promise<void>
}

const DEFAULT_SETTINGS: Record<string, Record<string, any>> = {
  general: {
    siteName: 'Coco Milk Kids',
    siteDescription: 'Premium children\'s clothing and accessories for South African families',
    siteUrl: 'https://cocomilkkids.co.za',
    adminEmail: '<EMAIL>',
    timezone: 'Africa/Johannesburg',
    dateFormat: 'Y-m-d',
    timeFormat: 'H:i',
    weekStartsOn: '1',
    language: 'en_ZA',
    maintenanceMode: false,
    registrationEnabled: true,
    defaultUserRole: 'customer'
  },
  store: {
    storeName: 'Coco Milk Kids',
    storeDescription: 'Premium children\'s clothing and accessories designed for comfort, style, and adventure.',
    storeTagline: 'Where Style Meets Adventure',
    email: '<EMAIL>',
    phone: '+27 21 123 4567',
    address: {
      street: '123 Main Street',
      city: 'Cape Town',
      state: 'Western Cape',
      postalCode: '8001',
      country: 'ZA'
    },
    currency: 'ZAR',
    currencyPosition: 'before',
    currencySymbol: 'R',
    decimalPlaces: 2
  },
  payments: {
    enablePayments: true,
    defaultGateway: 'payfast',
    testMode: true,
    gateways: {
      payfast: {
        enabled: true,
        merchantId: '',
        merchantKey: '',
        testMode: true
      },
      ozow: {
        enabled: true,
        siteCode: '',
        privateKey: '',
        testMode: true
      },
      cod: {
        enabled: true,
        title: 'Cash on Delivery',
        description: 'Pay when you receive your order'
      }
    }
  },
  shipping: {
    enableShipping: true,
    enableShippingCalculator: true,
    hideShippingUntilAddress: false,
    enableLocalPickup: true,
    weightUnit: 'kg',
    dimensionUnit: 'cm',
    defaultDimensions: {
      length: 20,
      width: 15,
      height: 5,
      weight: 0.5
    }
  },
  taxes: {
    enableTaxes: true,
    pricesIncludeTax: true,
    calculateTaxBased: 'shipping',
    roundingMode: 'round',
    displayPricesInShop: 'including',
    displayPricesInCart: 'including',
    displayTaxTotals: 'itemized'
  },
  security: {
    minPasswordLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
    passwordExpiry: 90,
    enable2FA: false,
    sessionTimeout: 60,
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    enableCaptcha: true,
    enableAuditLog: true
  }
}

export function useSettings(options: UseSettingsOptions = {}): UseSettingsResult {
  const { section, autoLoad = true } = options

  const [settings, setSettings] = useState<Record<string, SettingsSection>>({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadSettings = useCallback(async (targetSection?: string) => {
    try {
      setLoading(true)
      setError(null)

      const sectionsToLoad = targetSection ? [targetSection] : Object.keys(DEFAULT_SETTINGS)

      // Update status to loading
      setSettings(prev => {
        const updated = { ...prev }
        sectionsToLoad.forEach(sectionId => {
          updated[sectionId] = {
            ...updated[sectionId],
            id: sectionId,
            name: sectionId.charAt(0).toUpperCase() + sectionId.slice(1),
            data: updated[sectionId]?.data || {},
            status: 'loading'
          }
        })
        return updated
      })

      // Simulate API call - in production, this would fetch from your API
      await new Promise(resolve => setTimeout(resolve, 500))

      // Load settings (using defaults for now)
      setSettings(prev => {
        const updated = { ...prev }
        sectionsToLoad.forEach(sectionId => {
          updated[sectionId] = {
            id: sectionId,
            name: sectionId.charAt(0).toUpperCase() + sectionId.slice(1),
            data: { ...DEFAULT_SETTINGS[sectionId] },
            lastUpdated: new Date().toISOString(),
            status: 'loaded'
          }
        })
        return updated
      })

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load settings'
      setError(errorMessage)
      
      // Update status to error
      setSettings(prev => {
        const updated = { ...prev }
        const sectionsToLoad = targetSection ? [targetSection] : Object.keys(DEFAULT_SETTINGS)
        sectionsToLoad.forEach(sectionId => {
          if (updated[sectionId]) {
            updated[sectionId].status = 'error'
          }
        })
        return updated
      })
      
      toast.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  const saveSettings = useCallback(async (sectionId: string, data: Record<string, any>) => {
    try {
      // Update status to saving
      setSettings(prev => ({
        ...prev,
        [sectionId]: {
          ...prev[sectionId],
          status: 'saving'
        }
      }))

      // Simulate API call - in production, this would save to your API
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Update settings
      setSettings(prev => ({
        ...prev,
        [sectionId]: {
          ...prev[sectionId],
          data: { ...data },
          lastUpdated: new Date().toISOString(),
          status: 'loaded'
        }
      }))

      toast.success(`${sectionId.charAt(0).toUpperCase() + sectionId.slice(1)} settings saved successfully`)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save settings'
      
      // Update status to error
      setSettings(prev => ({
        ...prev,
        [sectionId]: {
          ...prev[sectionId],
          status: 'error'
        }
      }))
      
      toast.error(errorMessage)
      throw err
    }
  }, [])

  const updateSetting = useCallback((sectionId: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [sectionId]: {
        ...prev[sectionId],
        data: {
          ...prev[sectionId]?.data,
          [key]: value
        }
      }
    }))
  }, [])

  const getSetting = useCallback((sectionId: string, key: string, defaultValue?: any) => {
    const section = settings[sectionId]
    if (!section || !section.data) {
      return defaultValue
    }
    return section.data[key] ?? defaultValue
  }, [settings])

  const getSection = useCallback((sectionId: string): SettingsSection | null => {
    return settings[sectionId] || null
  }, [settings])

  const resetSection = useCallback(async (sectionId: string) => {
    try {
      const defaultData = DEFAULT_SETTINGS[sectionId]
      if (!defaultData) {
        throw new Error(`No default settings found for section: ${sectionId}`)
      }

      await saveSettings(sectionId, defaultData)
      toast.success(`${sectionId.charAt(0).toUpperCase() + sectionId.slice(1)} settings reset to defaults`)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reset settings'
      toast.error(errorMessage)
      throw err
    }
  }, [saveSettings])

  const exportSettings = useCallback(async (): Promise<string> => {
    try {
      const exportData = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        settings: Object.fromEntries(
          Object.entries(settings).map(([key, section]) => [key, section.data])
        )
      }
      
      return JSON.stringify(exportData, null, 2)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to export settings'
      toast.error(errorMessage)
      throw err
    }
  }, [settings])

  const importSettings = useCallback(async (data: string) => {
    try {
      const importData = JSON.parse(data)
      
      if (!importData.settings || typeof importData.settings !== 'object') {
        throw new Error('Invalid settings format')
      }

      // Save each section
      for (const [sectionId, sectionData] of Object.entries(importData.settings)) {
        if (typeof sectionData === 'object' && sectionData !== null) {
          await saveSettings(sectionId, sectionData as Record<string, any>)
        }
      }

      toast.success('Settings imported successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to import settings'
      toast.error(errorMessage)
      throw err
    }
  }, [saveSettings])

  // Auto-load settings on mount
  useEffect(() => {
    if (autoLoad) {
      loadSettings(section)
    }
  }, [loadSettings, section, autoLoad])

  return {
    settings,
    loading,
    error,
    loadSettings,
    saveSettings,
    updateSetting,
    getSetting,
    getSection,
    resetSection,
    exportSettings,
    importSettings
  }
}
