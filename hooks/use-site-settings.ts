'use client'

import { useState, useEffect } from 'react'

export interface SiteSettings {
  id: string
  homepageId?: string
  homepageSlug?: string
  siteName: string
  siteDescription: string
  siteUrl: string
  logoUrl?: string
  faviconUrl?: string
  socialMedia: {
    facebook?: string
    instagram?: string
    twitter?: string
    youtube?: string
  }
  seo: {
    defaultTitle: string
    defaultDescription: string
    defaultKeywords: string[]
    ogImage?: string
  }
  ecommerce: {
    currency: string
    taxRate: number
    freeShippingThreshold?: number
    defaultShippingCost: number
  }
  maintenance: {
    enabled: boolean
    message?: string
    allowedIps?: string[]
  }
  analytics: {
    googleAnalyticsId?: string
    facebookPixelId?: string
    hotjarId?: string
  }
}

export interface AvailablePage {
  id: string
  title: string
  slug: string
  type: string
}

export function useSiteSettings() {
  const [settings, setSettings] = useState<SiteSettings | null>(null)
  const [availablePages, setAvailablePages] = useState<AvailablePage[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchSettings = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/admin/site-settings')
      if (!response.ok) {
        throw new Error('Failed to fetch site settings')
      }
      
      const data = await response.json()
      setSettings(data.data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch site settings')
    } finally {
      setLoading(false)
    }
  }

  const fetchAvailablePages = async () => {
    try {
      const response = await fetch('/api/admin/site-settings/homepage')
      if (!response.ok) {
        throw new Error('Failed to fetch available pages')
      }
      
      const data = await response.json()
      setAvailablePages(data.data.availablePages || [])
    } catch (err) {
      console.error('Failed to fetch available pages:', err)
    }
  }

  const updateSettings = async (updatedSettings: Partial<SiteSettings>) => {
    try {
      const response = await fetch('/api/admin/site-settings', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updatedSettings)
      })

      if (!response.ok) {
        throw new Error('Failed to update site settings')
      }

      const data = await response.json()
      setSettings(data.data)
      return { success: true, data: data.data }
    } catch (err) {
      const error = err instanceof Error ? err.message : 'Failed to update site settings'
      return { success: false, error }
    }
  }

  const setHomepage = async (pageId: string) => {
    try {
      const response = await fetch('/api/admin/site-settings/homepage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ pageId })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to set homepage')
      }

      await fetchSettings() // Refresh settings
      return { success: true }
    } catch (err) {
      const error = err instanceof Error ? err.message : 'Failed to set homepage'
      return { success: false, error }
    }
  }

  const clearHomepage = async () => {
    try {
      const response = await fetch('/api/admin/site-settings/homepage', {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to clear homepage')
      }

      await fetchSettings() // Refresh settings
      return { success: true }
    } catch (err) {
      const error = err instanceof Error ? err.message : 'Failed to clear homepage'
      return { success: false, error }
    }
  }

  const enableMaintenanceMode = async (message?: string, allowedIps?: string[]) => {
    return updateSettings({
      maintenance: {
        enabled: true,
        message,
        allowedIps
      }
    })
  }

  const disableMaintenanceMode = async () => {
    return updateSettings({
      maintenance: {
        enabled: false
      }
    })
  }

  useEffect(() => {
    fetchSettings()
    fetchAvailablePages()
  }, [])

  return {
    settings,
    availablePages,
    loading,
    error,
    fetchSettings,
    fetchAvailablePages,
    updateSettings,
    setHomepage,
    clearHomepage,
    enableMaintenanceMode,
    disableMaintenanceMode
  }
}

// Hook for getting current homepage information (for public use)
export function useHomepage() {
  const [homepage, setHomepage] = useState<{ pageId: string; slug: string } | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchHomepage = async () => {
      try {
        const response = await fetch('/api/admin/site-settings/homepage')
        if (response.ok) {
          const data = await response.json()
          setHomepage(data.data.currentHomepage)
        }
      } catch (error) {
        console.error('Failed to fetch homepage:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchHomepage()
  }, [])

  return { homepage, loading }
}
