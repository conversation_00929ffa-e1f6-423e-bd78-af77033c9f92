"use client"

import { useState, useEffect } from "react"

interface Currency {
  code: string
  symbol: string
  rate: number
}

export function usePriceFormatter() {
  const [currency, setCurrency] = useState<Currency>({
    code: "ZAR",
    symbol: "R",
    rate: 1,
  })

  useEffect(() => {
    // Initialize from localStorage if available
    const storedCurrency = localStorage.getItem("selectedCurrency")
    if (storedCurrency) {
      try {
        const currencies = [
          { code: "ZAR", symbol: "R", rate: 1 },
          { code: "USD", symbol: "$", rate: 0.054 },
          { code: "EUR", symbol: "€", rate: 0.050 },
          { code: "GBP", symbol: "£", rate: 0.043 },
          { code: "BWP", symbol: "P", rate: 0.74 },
          { code: "NAD", symbol: "N$", rate: 1 },
        ]
        const found = currencies.find((c) => c.code === storedCurrency)
        if (found) {
          setCurrency(found)
        }
      } catch (error) {
        console.error("Failed to parse currency from localStorage", error)
      }
    }

    // Listen for currency changes
    const handleCurrencyChange = (event: CustomEvent) => {
      setCurrency(event.detail)
    }

    window.addEventListener("currencyChange", handleCurrencyChange as EventListener)

    return () => {
      window.removeEventListener("currencyChange", handleCurrencyChange as EventListener)
    }
  }, [])

  const formatPrice = (priceInZAR: number, options?: { showCode?: boolean; decimals?: number }) => {
    const { showCode = false, decimals = 2 } = options || {}
    const convertedPrice = priceInZAR * currency.rate

    const formattedPrice = new Intl.NumberFormat("en-ZA", {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(convertedPrice)

    return `${currency.symbol}${formattedPrice}${showCode ? ` ${currency.code}` : ""}`
  }

  return { formatPrice, currency }
}
