# WooCommerce Integration Setup Guide

This guide will help you set up the WooCommerce integration for your Coco Milk Kids e-commerce store.

## Prerequisites

1. **WordPress Site with WooCommerce**: You mentioned you already have this set up.
2. **WooCommerce REST API Access**: You'll need to generate API keys.
3. **WordPress JWT Authentication Plugin**: For user authentication.

## Step 1: WordPress/WooCommerce Configuration

### 1.1 Install Required WordPress Plugins

1. **JWT Authentication for WP REST API** (recommended plugin: `jwt-authentication-for-wp-rest-api`)
   ```
   https://wordpress.org/plugins/jwt-authentication-for-wp-rest-api/
   ```

2. **WooCommerce** (if not already installed)

### 1.2 Generate WooCommerce API Keys

1. Go to your WordPress admin dashboard
2. Navigate to **WooCommerce > Settings > Advanced > REST API**
3. Click **Add Key**
4. Fill in the details:
   - **Description**: "Coco Milk Kids Next.js App"
   - **User**: Select an administrator user
   - **Permissions**: Read/Write
5. Click **Generate API Key**
6. Copy the **Consumer Key** and **Consumer Secret** (you'll need these for environment variables)

### 1.3 Configure JWT Authentication

1. Add the following to your WordPress `wp-config.php` file:
   ```php
   define('JWT_AUTH_SECRET_KEY', 'your-super-secret-jwt-key-here');
   define('JWT_AUTH_CORS_ENABLE', true);
   ```

2. Add the following to your `.htaccess` file (if using Apache):
   ```apache
   RewriteEngine On
   RewriteCond %{HTTP:Authorization} ^(.*)
   RewriteRule ^(.*) - [E=HTTP_AUTHORIZATION:%1]
   ```

### 1.4 Enable CORS (if needed)

Add this to your WordPress theme's `functions.php` or a custom plugin:

```php
function add_cors_http_header(){
    header("Access-Control-Allow-Origin: http://localhost:3000");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, Authorization");
    header("Access-Control-Allow-Credentials: true");
}
add_action('init','add_cors_http_header');
```

## Step 2: Next.js Environment Configuration

### 2.1 Create Environment Variables

Create a `.env.local` file in your project root (copy from `.env.example`):

```env
# WordPress/WooCommerce Configuration
WORDPRESS_URL=https://your-wordpress-site.com
WOOCOMMERCE_CONSUMER_KEY=ck_your_consumer_key_here
WOOCOMMERCE_CONSUMER_SECRET=cs_your_consumer_secret_here

# WordPress Authentication
WORDPRESS_JWT_SECRET=your-super-secret-jwt-key-here
WORDPRESS_APPLICATION_PASSWORD_USERNAME=your_wp_username
WORDPRESS_APPLICATION_PASSWORD=your_application_password

# OpenAI Configuration (existing)
OPENAI_API_KEY=your_openai_api_key_here

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_here

# Currency Configuration
DEFAULT_CURRENCY=ZAR
CURRENCY_SYMBOL=R
```

### 2.2 Replace Values

- `WORDPRESS_URL`: Your WordPress site URL (e.g., `https://yoursite.com`)
- `WOOCOMMERCE_CONSUMER_KEY`: The consumer key from Step 1.2
- `WOOCOMMERCE_CONSUMER_SECRET`: The consumer secret from Step 1.2
- `WORDPRESS_JWT_SECRET`: The same secret you added to `wp-config.php`
- `WORDPRESS_APPLICATION_PASSWORD_USERNAME`: Your WordPress admin username
- `WORDPRESS_APPLICATION_PASSWORD`: Generate this in WordPress Users > Your Profile > Application Passwords

## Step 3: Test the Integration

### 3.1 Test API Endpoints

You can test the integration using these endpoints:

1. **Products**: `GET /api/woocommerce/products`
2. **Single Product**: `GET /api/woocommerce/products/[id]`
3. **Categories**: `GET /api/woocommerce/categories`
4. **Authentication**: `POST /api/woocommerce/auth`

### 3.2 Test Authentication

```bash
curl -X POST http://localhost:3000/api/woocommerce/auth \
  -H "Content-Type: application/json" \
  -d '{
    "action": "login",
    "username": "your_username",
    "password": "your_password"
  }'
```

### 3.3 Test Products

```bash
curl http://localhost:3000/api/woocommerce/products
```

## Step 4: Update Existing Components

### 4.1 Switch to WooCommerce Products

The integration includes a new `lib/products-woocommerce.ts` file that replaces the mock data. To use it:

1. Update imports in your components from:
   ```typescript
   import { getProducts } from '@/lib/products'
   ```
   
   To:
   ```typescript
   import { getProducts } from '@/lib/products-woocommerce'
   ```

2. The API is compatible, so no other changes should be needed.

### 4.2 Use Authentication

Components can now use the `useAuth` hook:

```typescript
import { useAuth } from '@/components/auth-provider'

function MyComponent() {
  const { user, isAuthenticated, login, logout } = useAuth()
  
  // Use authentication state and methods
}
```

## Step 5: WordPress Product Setup

### 5.1 Product Attributes

For the best integration, set up these product attributes in WooCommerce:

1. **Color/Colour**: For product color variations
2. **Size**: For size variations (e.g., 2T, 3T, 4T, etc.)

### 5.2 Product Categories

Create categories that match your site structure:
- Tops
- Bottoms  
- Dresses
- Outerwear
- Accessories

### 5.3 Product Images

- Use high-quality images
- Set featured images for all products
- Add multiple product gallery images

## Step 6: South African Specific Configuration

### 6.1 Currency

The integration is pre-configured for South African Rand (ZAR).

### 6.2 Tax Configuration

In WooCommerce:
1. Go to **WooCommerce > Settings > Tax**
2. Enable tax calculation
3. Set up VAT at 15% for South Africa

### 6.3 Shipping

Configure shipping zones for South African provinces in **WooCommerce > Settings > Shipping**.

## Troubleshooting

### Common Issues

1. **CORS Errors**: Make sure CORS is properly configured in WordPress
2. **Authentication Fails**: Check JWT secret key matches in both WordPress and Next.js
3. **API Key Issues**: Verify WooCommerce API keys have correct permissions
4. **SSL Issues**: Ensure your WordPress site has a valid SSL certificate

### Debug Mode

Add this to your `.env.local` for debugging:
```env
NODE_ENV=development
DEBUG=true
```

### Logs

Check the browser console and Next.js server logs for detailed error messages.

## Security Considerations

1. **Never commit `.env.local`** to version control
2. **Use strong JWT secrets** (at least 32 characters)
3. **Regularly rotate API keys**
4. **Use HTTPS** in production
5. **Implement rate limiting** for API endpoints

## Production Deployment

1. Update environment variables for production
2. Ensure WordPress site is accessible from your production domain
3. Update CORS settings to include your production domain
4. Test all functionality in production environment

## Support

If you encounter issues:

1. Check WordPress and WooCommerce logs
2. Verify all environment variables are set correctly
3. Test API endpoints individually
4. Check network connectivity between Next.js and WordPress

## Next Steps

After setup is complete, you can:

1. Customize product display components
2. Add more WooCommerce features (coupons, reviews, etc.)
3. Implement advanced filtering
4. Add payment gateway integration
5. Set up order management features

The integration provides a solid foundation for a full-featured e-commerce site with WordPress/WooCommerce as the backend and Next.js as the frontend.
